# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V276
43
44
60
63
94
95
124
125
126
127
172
173
177
178
215
216
247
248
976
979
981
982
1008
1010
1012
1015
1542
1545
8214
8215
8242
8245
8256
8257
8260
8261
8274
8275
8289
8293
8314
8319
8330
8335
8400
8413
8417
8418
8421
8423
8427
8432
8450
8451
8455
8456
8458
8468
8469
8470
8472
8478
8484
8485
8488
8490
8492
8494
8495
8498
8499
8505
8508
8522
8523
8524
8592
8616
8617
8623
8624
8626
8630
8632
8636
8668
8669
8670
8676
8678
8692
8960
8968
8972
8992
8994
9084
9085
9115
9142
9143
9144
9168
9169
9180
9187
9632
9634
9646
9656
9660
9666
9670
9672
9674
9676
9679
9684
9698
9699
9700
9701
9703
9709
9720
9728
9733
9735
9792
9793
9794
9795
9824
9828
9837
9840
10176
10240
10496
11008
11056
11077
11079
11085
64297
64298
65121
65127
65128
65129
65291
65292
65308
65311
65340
65341
65342
65343
65372
65373
65374
65375
65506
65507
65513
65517
119808
119893
119894
119965
119966
119968
119970
119971
119973
119975
119977
119981
119982
119994
119995
119996
119997
120004
120005
120070
120071
120075
120077
120085
120086
120093
120094
120122
120123
120127
120128
120133
120134
120135
120138
120145
120146
120486
120488
120780
120782
120832
126464
126468
126469
126496
126497
126499
126500
126501
126503
126504
126505
126515
126516
126520
126521
126522
126523
126524
126530
126531
126535
126536
126537
126538
126539
126540
126541
126544
126545
126547
126548
126549
126551
126552
126553
126554
126555
126556
126557
126558
126559
126560
126561
126563
126564
126565
126567
126571
126572
126579
126580
126584
126585
126589
126590
126591
126592
126602
126603
126620
126625
126628
126629
126634
126635
126652
126704
126706
END
