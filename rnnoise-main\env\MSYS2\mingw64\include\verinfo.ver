/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
VS_VERSION_INFO VERSIONINFO
FILEVERSION APPVERSION,APPREVISION,0,APPRELEASE
PRODUCTVERSION APPVERSION,APPREVISION,0,APPRELEASE
FILEFLAGSMASK VERSIONFILEFLAGSMASK
FILEFLAGS VERSIONFLAGS
FILEOS VOS_DOS_WINDOWS32
FILETYPE VERSIONTYPE
FILESUBTYPE VERSIONSUBTYPE
BEGIN
   BLOCK "StringFileInfo"
   BEGIN
       BLOCK "040904E4"
       BEGIN
           VALUE "CompanyName",VERSIONCOMPANYNAME
           VALUE "FileDescription",VERSIONDESCRIPTION
           VALUE "FileVersion",VERSIONSTR
           VALUE "InternalName",VERSIONNAME
           VALUE "LegalCopyright",VER<PERSON><PERSON>COPYRIGHT
           VALUE "OriginalFilename",VERSIONNAME
           VALUE "ProductName",VERSIONPRODUCTNAME
           VALUE "ProductVersion",VERSIONSTR
       END
       BLOCK "041104E4"
       BEGIN
           VALUE "CompanyName",VERSIONCOMPANYNAME
           VALUE "FileDescription",VERSIONDESCRIPTION
           VALUE "FileVersion",VERSIONSTR
           VALUE "InternalName",VERSIONNAME
           VALUE "LegalCopyright",VERSIONCOPYRIGHT
           VALUE "OriginalFilename",VERSIONNAME
           VALUE "ProductName",VERSIONPRODUCTNAME
           VALUE "ProductVersion",VERSIONSTR
       END
   END
   BLOCK "VarFileInfo"
   BEGIN

       VALUE "Translation",0x409,0x4E4,0x411,0x4E4
   END
END
