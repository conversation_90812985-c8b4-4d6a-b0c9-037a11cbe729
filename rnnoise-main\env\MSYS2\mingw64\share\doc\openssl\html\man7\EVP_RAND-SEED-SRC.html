<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_RAND-SEED-SRC</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Identity">Identity</a></li>
      <li><a href="#Supported-parameters">Supported parameters</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_RAND-SEED-SRC - The randomness seed source EVP_RAND implementation</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for deterministic random number generator seeding through the <b>EVP_RAND</b> API.</p>

<p>The seed sources used are specified at the time OpenSSL is configured for building using the <b>--with-rand-seed=</b> option. By default, operating system randomness sources are used.</p>

<h2 id="Identity">Identity</h2>

<p>&quot;SEED-SRC&quot; is the name for this implementation; it can be used with the EVP_RAND_fetch() function.</p>

<h2 id="Supported-parameters">Supported parameters</h2>

<p>The supported parameters are:</p>

<dl>

<dt id="state-OSSL_RAND_PARAM_STATE-integer">&quot;state&quot; (<b>OSSL_RAND_PARAM_STATE</b>) &lt;integer&gt;</dt>
<dd>

</dd>
<dt id="strength-OSSL_RAND_PARAM_STRENGTH-unsigned-integer">&quot;strength&quot; (<b>OSSL_RAND_PARAM_STRENGTH</b>) &lt;unsigned integer&gt;</dt>
<dd>

</dd>
<dt id="max_request-OSSL_RAND_PARAM_MAX_REQUEST-unsigned-integer">&quot;max_request&quot; (<b>OSSL_RAND_PARAM_MAX_REQUEST</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>These parameters work as described in <a href="../man3/EVP_RAND.html">&quot;PARAMETERS&quot; in EVP_RAND(3)</a>.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>A context for the seed source can be obtained by calling:</p>

<pre><code>EVP_RAND *rand = EVP_RAND_fetch(NULL, &quot;SEED-SRC&quot;, NULL);
EVP_RAND_CTX *rctx = EVP_RAND_CTX_new(rand, NULL);</code></pre>

<h1 id="EXAMPLES">EXAMPLES</h1>

<pre><code>EVP_RAND *rand;
EVP_RAND_CTX *seed, *rctx;
unsigned char bytes[100];
OSSL_PARAM params[2], *p = params;
unsigned int strength = 128;

/* Create and instantiate a seed source */
rand = EVP_RAND_fetch(NULL, &quot;SEED-SRC&quot;, NULL);
seed = EVP_RAND_CTX_new(rand, NULL);
EVP_RAND_instantiate(seed, strength, 0, NULL, 0, NULL);
EVP_RAND_free(rand);

/* Feed this into a DRBG */
rand = EVP_RAND_fetch(NULL, &quot;CTR-DRBG&quot;, NULL);
rctx = EVP_RAND_CTX_new(rand, seed);
EVP_RAND_free(rand);

/* Configure the DRBG */
*p++ = OSSL_PARAM_construct_utf8_string(OSSL_DRBG_PARAM_CIPHER,
                                        SN_aes_256_ctr, 0);
*p = OSSL_PARAM_construct_end();
EVP_RAND_instantiate(rctx, strength, 0, NULL, 0, params);

EVP_RAND_generate(rctx, bytes, sizeof(bytes), strength, 0, NULL, 0);

EVP_RAND_CTX_free(rctx);
EVP_RAND_CTX_free(seed);</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_RAND.html">EVP_RAND(3)</a>, <a href="../man3/EVP_RAND.html">&quot;PARAMETERS&quot; in EVP_RAND(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


