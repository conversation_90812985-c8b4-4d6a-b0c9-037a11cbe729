.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_server_name_set" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_server_name_set \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_server_name_set(gnutls_session_t " session ", gnutls_server_name_type_t " type ", const void * " name ", size_t " name_length ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_server_name_type_t type" 12
specifies the indicator type
.IP "const void * name" 12
is a string that contains the server name.
.IP "size_t name_length" 12
holds the length of name excluding the terminating null byte
.SH "DESCRIPTION"
This function is to be used by clients that want to inform (via a
TLS extension mechanism) the server of the name they connected to.
This should be used by clients that connect to servers that do
virtual hosting.

The value of  \fIname\fP depends on the  \fItype\fP type.  In case of
\fBGNUTLS_NAME_DNS\fP, a UTF\-8 null\-terminated domain name string,
without the trailing dot, is expected.

IPv4 or IPv6 addresses are not permitted to be set by this function.
If the function is called with a name of  \fIname_length\fP zero it will clear
all server names set.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
