------------------------------------------------------------------------
-- dqMax.decTest -- decQuad maxnum                                    --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- we assume that base comparison is tested in compare.decTest, so
-- these mainly cover special cases and rounding
extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- sanity checks
dqmax001 max  -2  -2  -> -2
dqmax002 max  -2  -1  -> -1
dqmax003 max  -2   0  ->  0
dqmax004 max  -2   1  ->  1
dqmax005 max  -2   2  ->  2
dqmax006 max  -1  -2  -> -1
dqmax007 max  -1  -1  -> -1
dqmax008 max  -1   0  ->  0
dqmax009 max  -1   1  ->  1
dqmax010 max  -1   2  ->  2
dqmax011 max   0  -2  ->  0
dqmax012 max   0  -1  ->  0
dqmax013 max   0   0  ->  0
dqmax014 max   0   1  ->  1
dqmax015 max   0   2  ->  2
dqmax016 max   1  -2  ->  1
dqmax017 max   1  -1  ->  1
dqmax018 max   1   0  ->  1
dqmax019 max   1   1  ->  1
dqmax020 max   1   2  ->  2
dqmax021 max   2  -2  ->  2
dqmax022 max   2  -1  ->  2
dqmax023 max   2   0  ->  2
dqmax025 max   2   1  ->  2
dqmax026 max   2   2  ->  2

-- extended zeros
dqmax030 max   0     0   ->  0
dqmax031 max   0    -0   ->  0
dqmax032 max   0    -0.0 ->  0
dqmax033 max   0     0.0 ->  0
dqmax034 max  -0     0   ->  0    -- note: -0 = 0, but 0 chosen
dqmax035 max  -0    -0   -> -0
dqmax036 max  -0    -0.0 -> -0.0
dqmax037 max  -0     0.0 ->  0.0
dqmax038 max   0.0   0   ->  0
dqmax039 max   0.0  -0   ->  0.0
dqmax040 max   0.0  -0.0 ->  0.0
dqmax041 max   0.0   0.0 ->  0.0
dqmax042 max  -0.0   0   ->  0
dqmax043 max  -0.0  -0   -> -0.0
dqmax044 max  -0.0  -0.0 -> -0.0
dqmax045 max  -0.0   0.0 ->  0.0

dqmax050 max  -0E1   0E1 ->  0E+1
dqmax051 max  -0E2   0E2 ->  0E+2
dqmax052 max  -0E2   0E1 ->  0E+1
dqmax053 max  -0E1   0E2 ->  0E+2
dqmax054 max   0E1  -0E1 ->  0E+1
dqmax055 max   0E2  -0E2 ->  0E+2
dqmax056 max   0E2  -0E1 ->  0E+2
dqmax057 max   0E1  -0E2 ->  0E+1

dqmax058 max   0E1   0E1 ->  0E+1
dqmax059 max   0E2   0E2 ->  0E+2
dqmax060 max   0E2   0E1 ->  0E+2
dqmax061 max   0E1   0E2 ->  0E+2
dqmax062 max  -0E1  -0E1 -> -0E+1
dqmax063 max  -0E2  -0E2 -> -0E+2
dqmax064 max  -0E2  -0E1 -> -0E+1
dqmax065 max  -0E1  -0E2 -> -0E+1

-- Specials
dqmax090 max  Inf  -Inf   ->  Infinity
dqmax091 max  Inf  -1000  ->  Infinity
dqmax092 max  Inf  -1     ->  Infinity
dqmax093 max  Inf  -0     ->  Infinity
dqmax094 max  Inf   0     ->  Infinity
dqmax095 max  Inf   1     ->  Infinity
dqmax096 max  Inf   1000  ->  Infinity
dqmax097 max  Inf   Inf   ->  Infinity
dqmax098 max -1000  Inf   ->  Infinity
dqmax099 max -Inf   Inf   ->  Infinity
dqmax100 max -1     Inf   ->  Infinity
dqmax101 max -0     Inf   ->  Infinity
dqmax102 max  0     Inf   ->  Infinity
dqmax103 max  1     Inf   ->  Infinity
dqmax104 max  1000  Inf   ->  Infinity
dqmax105 max  Inf   Inf   ->  Infinity

dqmax120 max -Inf  -Inf   -> -Infinity
dqmax121 max -Inf  -1000  -> -1000
dqmax122 max -Inf  -1     -> -1
dqmax123 max -Inf  -0     -> -0
dqmax124 max -Inf   0     ->  0
dqmax125 max -Inf   1     ->  1
dqmax126 max -Inf   1000  ->  1000
dqmax127 max -Inf   Inf   ->  Infinity
dqmax128 max -Inf  -Inf   ->  -Infinity
dqmax129 max -1000 -Inf   ->  -1000
dqmax130 max -1    -Inf   ->  -1
dqmax131 max -0    -Inf   ->  -0
dqmax132 max  0    -Inf   ->  0
dqmax133 max  1    -Inf   ->  1
dqmax134 max  1000 -Inf   ->  1000
dqmax135 max  Inf  -Inf   ->  Infinity

-- 2004.08.02 754r chooses number over NaN in mixed cases
dqmax141 max  NaN -Inf    -> -Infinity
dqmax142 max  NaN -1000   -> -1000
dqmax143 max  NaN -1      -> -1
dqmax144 max  NaN -0      -> -0
dqmax145 max  NaN  0      ->  0
dqmax146 max  NaN  1      ->  1
dqmax147 max  NaN  1000   ->  1000
dqmax148 max  NaN  Inf    ->  Infinity
dqmax149 max  NaN  NaN    ->  NaN
dqmax150 max -Inf  NaN    -> -Infinity
dqmax151 max -1000 NaN    -> -1000
dqmax152 max -1    NaN    -> -1
dqmax153 max -0    NaN    -> -0
dqmax154 max  0    NaN    ->  0
dqmax155 max  1    NaN    ->  1
dqmax156 max  1000 NaN    ->  1000
dqmax157 max  Inf  NaN    ->  Infinity

dqmax161 max  sNaN -Inf   ->  NaN  Invalid_operation
dqmax162 max  sNaN -1000  ->  NaN  Invalid_operation
dqmax163 max  sNaN -1     ->  NaN  Invalid_operation
dqmax164 max  sNaN -0     ->  NaN  Invalid_operation
dqmax165 max  sNaN  0     ->  NaN  Invalid_operation
dqmax166 max  sNaN  1     ->  NaN  Invalid_operation
dqmax167 max  sNaN  1000  ->  NaN  Invalid_operation
dqmax168 max  sNaN  NaN   ->  NaN  Invalid_operation
dqmax169 max  sNaN sNaN   ->  NaN  Invalid_operation
dqmax170 max  NaN  sNaN   ->  NaN  Invalid_operation
dqmax171 max -Inf  sNaN   ->  NaN  Invalid_operation
dqmax172 max -1000 sNaN   ->  NaN  Invalid_operation
dqmax173 max -1    sNaN   ->  NaN  Invalid_operation
dqmax174 max -0    sNaN   ->  NaN  Invalid_operation
dqmax175 max  0    sNaN   ->  NaN  Invalid_operation
dqmax176 max  1    sNaN   ->  NaN  Invalid_operation
dqmax177 max  1000 sNaN   ->  NaN  Invalid_operation
dqmax178 max  Inf  sNaN   ->  NaN  Invalid_operation
dqmax179 max  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
dqmax181 max  NaN9  -Inf   -> -Infinity
dqmax182 max  NaN8     9   ->  9
dqmax183 max -NaN7   Inf   ->  Infinity

dqmax184 max -NaN1   NaN11 -> -NaN1
dqmax185 max  NaN2   NaN12 ->  NaN2
dqmax186 max -NaN13 -NaN7  -> -NaN13
dqmax187 max  NaN14 -NaN5  ->  NaN14

dqmax188 max -Inf    NaN4  -> -Infinity
dqmax189 max -9     -NaN3  -> -9
dqmax190 max  Inf    NaN2  ->  Infinity

dqmax191 max  sNaN99 -Inf    ->  NaN99 Invalid_operation
dqmax192 max  sNaN98 -1      ->  NaN98 Invalid_operation
dqmax193 max -sNaN97  NaN    -> -NaN97 Invalid_operation
dqmax194 max  sNaN96 sNaN94  ->  NaN96 Invalid_operation
dqmax195 max  NaN95  sNaN93  ->  NaN93 Invalid_operation
dqmax196 max -Inf    sNaN92  ->  NaN92 Invalid_operation
dqmax197 max  0      sNaN91  ->  NaN91 Invalid_operation
dqmax198 max  Inf   -sNaN90  -> -NaN90 Invalid_operation
dqmax199 max  NaN    sNaN89  ->  NaN89 Invalid_operation

-- old rounding checks
dqmax221 max 12345678000 1  -> 12345678000
dqmax222 max 1 12345678000  -> 12345678000
dqmax223 max 1234567800  1  -> 1234567800
dqmax224 max 1 1234567800   -> 1234567800
dqmax225 max 1234567890  1  -> 1234567890
dqmax226 max 1 1234567890   -> 1234567890
dqmax227 max 1234567891  1  -> 1234567891
dqmax228 max 1 1234567891   -> 1234567891
dqmax229 max 12345678901 1  -> 12345678901
dqmax230 max 1 12345678901  -> 12345678901
dqmax231 max 1234567896  1  -> 1234567896
dqmax232 max 1 1234567896   -> 1234567896
dqmax233 max -1234567891  1 -> 1
dqmax234 max 1 -1234567891  -> 1
dqmax235 max -12345678901 1 -> 1
dqmax236 max 1 -12345678901 -> 1
dqmax237 max -1234567896  1 -> 1
dqmax238 max 1 -1234567896  -> 1

-- from examples
dqmax280 max '3'   '2'  ->  '3'
dqmax281 max '-10' '3'  ->  '3'
dqmax282 max '1.0' '1'  ->  '1'
dqmax283 max '1' '1.0'  ->  '1'
dqmax284 max '7' 'NaN'  ->  '7'

-- expanded list from min/max 754r purple prose
-- [explicit tests for exponent ordering]
dqmax401 max  Inf    1.1     ->  Infinity
dqmax402 max  1.1    1       ->  1.1
dqmax403 max  1      1.0     ->  1
dqmax404 max  1.0    0.1     ->  1.0
dqmax405 max  0.1    0.10    ->  0.1
dqmax406 max  0.10   0.100   ->  0.10
dqmax407 max  0.10   0       ->  0.10
dqmax408 max  0      0.0     ->  0
dqmax409 max  0.0   -0       ->  0.0
dqmax410 max  0.0   -0.0     ->  0.0
dqmax411 max  0.00  -0.0     ->  0.00
dqmax412 max  0.0   -0.00    ->  0.0
dqmax413 max  0     -0.0     ->  0
dqmax414 max  0     -0       ->  0
dqmax415 max -0.0   -0       -> -0.0
dqmax416 max -0     -0.100   -> -0
dqmax417 max -0.100 -0.10    -> -0.100
dqmax418 max -0.10  -0.1     -> -0.10
dqmax419 max -0.1   -1.0     -> -0.1
dqmax420 max -1.0   -1       -> -1.0
dqmax421 max -1     -1.1     -> -1
dqmax423 max -1.1   -Inf     -> -1.1
-- same with operands reversed
dqmax431 max  1.1    Inf     ->  Infinity
dqmax432 max  1      1.1     ->  1.1
dqmax433 max  1.0    1       ->  1
dqmax434 max  0.1    1.0     ->  1.0
dqmax435 max  0.10   0.1     ->  0.1
dqmax436 max  0.100  0.10    ->  0.10
dqmax437 max  0      0.10    ->  0.10
dqmax438 max  0.0    0       ->  0
dqmax439 max -0      0.0     ->  0.0
dqmax440 max -0.0    0.0     ->  0.0
dqmax441 max -0.0    0.00    ->  0.00
dqmax442 max -0.00   0.0     ->  0.0
dqmax443 max -0.0    0       ->  0
dqmax444 max -0      0       ->  0
dqmax445 max -0     -0.0     -> -0.0
dqmax446 max -0.100 -0       -> -0
dqmax447 max -0.10  -0.100   -> -0.100
dqmax448 max -0.1   -0.10    -> -0.10
dqmax449 max -1.0   -0.1     -> -0.1
dqmax450 max -1     -1.0     -> -1.0
dqmax451 max -1.1   -1       -> -1
dqmax453 max -Inf   -1.1     -> -1.1
-- largies
dqmax460 max  1000   1E+3    ->  1E+3
dqmax461 max  1E+3   1000    ->  1E+3
dqmax462 max  1000  -1E+3    ->  1000
dqmax463 max  1E+3  -1000    ->  1E+3
dqmax464 max -1000   1E+3    ->  1E+3
dqmax465 max -1E+3   1000    ->  1000
dqmax466 max -1000  -1E+3    -> -1000
dqmax467 max -1E+3  -1000    -> -1000

-- misalignment traps for little-endian
dqmax471 max      1.0       0.1  -> 1.0
dqmax472 max      0.1       1.0  -> 1.0
dqmax473 max     10.0       0.1  -> 10.0
dqmax474 max      0.1      10.0  -> 10.0
dqmax475 max      100       1.0  -> 100
dqmax476 max      1.0       100  -> 100
dqmax477 max     1000      10.0  -> 1000
dqmax478 max     10.0      1000  -> 1000
dqmax479 max    10000     100.0  -> 10000
dqmax480 max    100.0     10000  -> 10000
dqmax481 max   100000    1000.0  -> 100000
dqmax482 max   1000.0    100000  -> 100000
dqmax483 max  1000000   10000.0  -> 1000000
dqmax484 max  10000.0   1000000  -> 1000000

-- subnormals
dqmax510 max  1.00E-6143       0  ->   1.00E-6143
dqmax511 max  0.1E-6143        0  ->   1E-6144    Subnormal
dqmax512 max  0.10E-6143       0  ->   1.0E-6144  Subnormal
dqmax513 max  0.100E-6143      0  ->   1.00E-6144 Subnormal
dqmax514 max  0.01E-6143       0  ->   1E-6145    Subnormal
dqmax515 max  0.999E-6143      0  ->   9.99E-6144 Subnormal
dqmax516 max  0.099E-6143      0  ->   9.9E-6145  Subnormal
dqmax517 max  0.009E-6143      0  ->   9E-6146    Subnormal
dqmax518 max  0.001E-6143      0  ->   1E-6146    Subnormal
dqmax519 max  0.0009E-6143     0  ->   9E-6147    Subnormal
dqmax520 max  0.0001E-6143     0  ->   1E-6147    Subnormal

dqmax530 max -1.00E-6143       0  ->   0
dqmax531 max -0.1E-6143        0  ->   0
dqmax532 max -0.10E-6143       0  ->   0
dqmax533 max -0.100E-6143      0  ->   0
dqmax534 max -0.01E-6143       0  ->   0
dqmax535 max -0.999E-6143      0  ->   0
dqmax536 max -0.099E-6143      0  ->   0
dqmax537 max -0.009E-6143      0  ->   0
dqmax538 max -0.001E-6143      0  ->   0
dqmax539 max -0.0009E-6143     0  ->   0
dqmax540 max -0.0001E-6143     0  ->   0

-- Null tests
dqmax900 max 10  #  -> NaN Invalid_operation
dqmax901 max  # 10  -> NaN Invalid_operation



