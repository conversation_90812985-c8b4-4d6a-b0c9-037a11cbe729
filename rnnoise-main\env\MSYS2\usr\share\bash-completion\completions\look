_look_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-t'|'--terminate')
			COMPREPLY=( $(compgen -W "char" -- $cur) )
			return 0
			;;
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="--alternative --alphanum --ignore-case --terminate --version --help"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	case $COMP_CWORD in
		1)
			COMPREPLY=( $(compgen -W "$(look "$cur")" -- $cur) )
			;;
		2)
			local IFS=$'\n'
			compopt -o filenames
			COMPREPLY=( $(compgen -f -- $cur) )
			;;
	esac
	return 0
}
complete -F _look_module look
