## Syntax highlighting for OCaml.

syntax ocaml "\.mli?$"
magic "OCaml"
comment "(*|*)"

# Uid:
color red "\<[[:upper:]][[:lower:][:digit:]_]{2,}\>"
# Declarations:
color green "\<(let|val|method|in|and|rec|private|virtual|constraint)\>"
# Structure items:
color red "\<(type|open|class|module|exception|external)\>"
# Patterns:
color blue "\<(fun|function|functor|match|try|with)\>"
# Pattern modifiers:
color yellow "\<(as|when|of)\>"
# Conditions:
color cyan "\<(if|then|else)\>"
# Blocks:
color magenta "\<(begin|end|object|struct|sig|for|while|do|done|to|downto)\>"
# Constants:
color green "\<(true|false)\>"
# Modules/classes:
color green "\<(include|inherit|initializer)\>"
# Expression modifiers:
color yellow "\<(new|ref|mutable|lazy|assert|raise)\>"
# Comments:
color white start="\(\*" end="\*\)"
# Strings:
color brightblack ""[^"\]*""
