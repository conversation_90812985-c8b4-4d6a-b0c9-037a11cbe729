.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_verify_stored_pubkey" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_verify_stored_pubkey \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_verify_stored_pubkey(const char * " db_name ", gnutls_tdb_t " tdb ", const char * " host ", const char * " service ", gnutls_certificate_type_t " cert_type ", const gnutls_datum_t * " cert ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const char * db_name" 12
A file specifying the stored keys (use NULL for the default)
.IP "gnutls_tdb_t tdb" 12
A storage structure or NULL to use the default
.IP "const char * host" 12
The peer's name
.IP "const char * service" 12
non\-NULL if this key is specific to a service (e.g. http)
.IP "gnutls_certificate_type_t cert_type" 12
The type of the certificate
.IP "const gnutls_datum_t * cert" 12
The raw (der) data of the certificate
.IP "unsigned int flags" 12
should be 0.
.SH "DESCRIPTION"
This function will try to verify a raw public\-key or a public\-key provided via
a raw (DER\-encoded) certificate using a list of stored public keys.
The  \fIservice\fP field if non\-NULL should be a port number.

The  \fIdb_name\fP variable if non\-null specifies a custom backend for
the retrieval of entries. If it is NULL then the
default file backend will be used. In POSIX\-like systems the
file backend uses the $HOME/.gnutls/known_hosts file.

Note that if the custom storage backend is provided the
retrieval function should return \fBGNUTLS_E_CERTIFICATE_KEY_MISMATCH\fP
if the host/service pair is found but key doesn't match,
\fBGNUTLS_E_NO_CERTIFICATE_FOUND\fP if no such host/service with
the given key is found, and 0 if it was found. The storage
function should return 0 on success.

As of GnuTLS 3.6.6 this function also verifies raw public keys.
.SH "RETURNS"
If no associated public key is found
then \fBGNUTLS_E_NO_CERTIFICATE_FOUND\fP will be returned. If a key
is found but does not match \fBGNUTLS_E_CERTIFICATE_KEY_MISMATCH\fP
is returned. On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
or a negative error value on other errors.
.SH "SINCE"
3.0.13
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
