This is gnutls.info, produced by makeinfo version 7.1 from gnutls.texi.

This manual is last updated 28 January 2025 for version 3.8.9 of GnuTLS.

Copyright © 2001-2025 Free Software Foundation, Inc.\\ Copyright ©
2001-2025 <PERSON><PERSON>

     Permission is granted to copy, distribute and/or modify this
     document under the terms of the GNU Free Documentation License,
     Version 1.3 or any later version published by the Free Software
     Foundation; with no Invariant Sections, no Front-Cover Texts, and
     no Back-Cover Texts.  A copy of the license is included in the
     section entitled "GNU Free Documentation License".
INFO-DIR-SECTION Software libraries
START-INFO-DIR-ENTRY
* GnuTLS: (gnutls).		GNU Transport Layer Security Library.
END-INFO-DIR-ENTRY

INFO-DIR-SECTION System Administration
START-INFO-DIR-ENTRY
* certtool: (gnutls)certtool Invocation.	Manipulate certificates and keys.
* gnutls-serv: (gnutls)gnutls-serv Invocation.	GnuTLS test server.
* gnutls-cli: (gnutls)gnutls-cli Invocation.	GnuTLS test client.
* gnutls-cli-debug: (gnutls)gnutls-cli-debug Invocation.	GnuTLS debug client.
* psktool: (gnutls)psktool Invocation.	Simple TLS-Pre-Shared-Keys manager.
* srptool: (gnutls)srptool Invocation.	Simple SRP password tool.
END-INFO-DIR-ENTRY


File: gnutls.info,  Node: PKCS 7 API,  Next: OCSP API,  Prev: X509 certificate API,  Up: API reference

E.4 PKCS 7 API
==============

The following functions are to be used for PKCS 7 structures handling.
Their prototypes lie in ‘gnutls/pkcs7.h’.

gnutls_pkcs7_add_attr
---------------------

 -- Function: int gnutls_pkcs7_add_attr (gnutls_pkcs7_attrs_t * LIST,
          const char * OID, gnutls_datum_t * DATA, unsigned FLAGS)
     LIST: A list of existing attributes or pointer to ‘NULL’ for the
     first one

     OID: the OID of the attribute to be set

     DATA: the raw (DER-encoded) data of the attribute to be set

     FLAGS: zero or ‘GNUTLS_PKCS7_ATTR_ENCODE_OCTET_STRING’

     This function will set a PKCS ‘7’ attribute in the provided list.
     If this function fails, the previous list would be deallocated.

     Note that any attributes set with this function must either be DER
     or BER encoded, unless a special flag is present.

     *Returns:* On success, the new list head, otherwise ‘NULL’ .

     *Since:* 3.4.2

gnutls_pkcs7_attrs_deinit
-------------------------

 -- Function: void gnutls_pkcs7_attrs_deinit (gnutls_pkcs7_attrs_t LIST)
     LIST: A list of existing attributes

     This function will clear a PKCS ‘7’ attribute list.

     *Since:* 3.4.2

gnutls_pkcs7_deinit
-------------------

 -- Function: void gnutls_pkcs7_deinit (gnutls_pkcs7_t PKCS7)
     PKCS7: the type to be deinitialized

     This function will deinitialize a PKCS7 type.

gnutls_pkcs7_delete_crl
-----------------------

 -- Function: int gnutls_pkcs7_delete_crl (gnutls_pkcs7_t PKCS7, int
          INDX)
     PKCS7: The pkcs7 type

     INDX: the index of the crl to delete

     This function will delete a crl from a PKCS7 or RFC2630 crl set.
     Index starts from 0.  Returns 0 on success.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs7_delete_crt
-----------------------

 -- Function: int gnutls_pkcs7_delete_crt (gnutls_pkcs7_t PKCS7, int
          INDX)
     PKCS7: The pkcs7 type

     INDX: the index of the certificate to delete

     This function will delete a certificate from a PKCS7 or RFC2630
     certificate set.  Index starts from 0.  Returns 0 on success.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs7_export
-------------------

 -- Function: int gnutls_pkcs7_export (gnutls_pkcs7_t PKCS7,
          gnutls_x509_crt_fmt_t FORMAT, void * OUTPUT_DATA, size_t *
          OUTPUT_DATA_SIZE)
     PKCS7: The pkcs7 type

     FORMAT: the format of output params.  One of PEM or DER.

     OUTPUT_DATA: will contain a structure PEM or DER encoded

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will export the pkcs7 structure to DER or PEM format.

     If the buffer provided is not long enough to hold the output, then
     * ‘output_data_size’ is updated and ‘GNUTLS_E_SHORT_MEMORY_BUFFER’
     will be returned.

     If the structure is PEM encoded, it will have a header of "BEGIN
     PKCS7".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs7_export2
--------------------

 -- Function: int gnutls_pkcs7_export2 (gnutls_pkcs7_t PKCS7,
          gnutls_x509_crt_fmt_t FORMAT, gnutls_datum_t * OUT)
     PKCS7: The pkcs7 type

     FORMAT: the format of output params.  One of PEM or DER.

     OUT: will contain a structure PEM or DER encoded

     This function will export the pkcs7 structure to DER or PEM format.

     The output buffer is allocated using ‘gnutls_malloc()’ .

     If the structure is PEM encoded, it will have a header of "BEGIN
     PKCS7".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.3

gnutls_pkcs7_get_attr
---------------------

 -- Function: int gnutls_pkcs7_get_attr (gnutls_pkcs7_attrs_t LIST,
          unsigned IDX, char ** OID, gnutls_datum_t * DATA, unsigned
          FLAGS)
     LIST: A list of existing attributes or ‘NULL’ for the first one

     IDX: the index of the attribute to get

     OID: the OID of the attribute (read-only)

     DATA: the raw data of the attribute

     FLAGS: zero or ‘GNUTLS_PKCS7_ATTR_ENCODE_OCTET_STRING’

     This function will get a PKCS ‘7’ attribute from the provided list.
     The OID is a constant string, but data will be allocated and must
     be deinitialized by the caller.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned if there are no
     data in the current index.

     *Since:* 3.4.2

gnutls_pkcs7_get_crl_count
--------------------------

 -- Function: int gnutls_pkcs7_get_crl_count (gnutls_pkcs7_t PKCS7)
     PKCS7: The pkcs7 type

     This function will return the number of certificates in the PKCS7
     or RFC2630 crl set.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs7_get_crl_raw
------------------------

 -- Function: int gnutls_pkcs7_get_crl_raw (gnutls_pkcs7_t PKCS7,
          unsigned INDX, void * CRL, size_t * CRL_SIZE)
     PKCS7: The pkcs7 type

     INDX: contains the index of the crl to extract

     CRL: the contents of the crl will be copied there (may be null)

     CRL_SIZE: should hold the size of the crl

     This function will return a crl of the PKCS7 or RFC2630 crl set.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  If the provided buffer is not
     long enough, then ‘crl_size’ is updated and
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ is returned.  After the last crl has
     been read ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

gnutls_pkcs7_get_crl_raw2
-------------------------

 -- Function: int gnutls_pkcs7_get_crl_raw2 (gnutls_pkcs7_t PKCS7,
          unsigned INDX, gnutls_datum_t * CRL)
     PKCS7: The pkcs7 type

     INDX: contains the index of the crl to extract

     CRL: will contain the contents of the CRL in an allocated buffer

     This function will return a DER encoded CRL of the PKCS7 or RFC2630
     crl set.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  After the last crl has been read
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Since:* 3.4.2

gnutls_pkcs7_get_crt_count
--------------------------

 -- Function: int gnutls_pkcs7_get_crt_count (gnutls_pkcs7_t PKCS7)
     PKCS7: should contain a ‘gnutls_pkcs7_t’ type

     This function will return the number of certificates in the PKCS7
     or RFC2630 certificate set.

     *Returns:* On success, a positive number is returned, otherwise a
     negative error value.

gnutls_pkcs7_get_crt_raw
------------------------

 -- Function: int gnutls_pkcs7_get_crt_raw (gnutls_pkcs7_t PKCS7,
          unsigned INDX, void * CERTIFICATE, size_t * CERTIFICATE_SIZE)
     PKCS7: should contain a gnutls_pkcs7_t type

     INDX: contains the index of the certificate to extract

     CERTIFICATE: the contents of the certificate will be copied there
     (may be null)

     CERTIFICATE_SIZE: should hold the size of the certificate

     This function will return a certificate of the PKCS7 or RFC2630
     certificate set.

     After the last certificate has been read
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  If the provided buffer is not
     long enough, then ‘certificate_size’ is updated and
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ is returned.

gnutls_pkcs7_get_crt_raw2
-------------------------

 -- Function: int gnutls_pkcs7_get_crt_raw2 (gnutls_pkcs7_t PKCS7,
          unsigned INDX, gnutls_datum_t * CERT)
     PKCS7: should contain a gnutls_pkcs7_t type

     INDX: contains the index of the certificate to extract

     CERT: will hold the contents of the certificate; must be
     deallocated with ‘gnutls_free()’

     This function will return a certificate of the PKCS7 or RFC2630
     certificate set.

     After the last certificate has been read
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  If the provided buffer is not
     long enough, then ‘certificate_size’ is updated and
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ is returned.

     *Since:* 3.4.2

gnutls_pkcs7_get_embedded_data
------------------------------

 -- Function: int gnutls_pkcs7_get_embedded_data (gnutls_pkcs7_t PKCS7,
          unsigned FLAGS, gnutls_datum_t * DATA)
     PKCS7: should contain a gnutls_pkcs7_t type

     FLAGS: must be zero or ‘GNUTLS_PKCS7_EDATA_GET_RAW’

     DATA: will hold the embedded data in the provided structure

     This function will return the data embedded in the signature of the
     PKCS7 structure.  If no data are available then
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     The returned data must be de-allocated using ‘gnutls_free()’ .

     Note, that this function returns the exact same data that are
     authenticated.  If the ‘GNUTLS_PKCS7_EDATA_GET_RAW’ flag is
     provided, the returned data will be including the wrapping
     tag/value as they are encoded in the structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.8

gnutls_pkcs7_get_embedded_data_oid
----------------------------------

 -- Function: const char * gnutls_pkcs7_get_embedded_data_oid
          (gnutls_pkcs7_t PKCS7)
     PKCS7: should contain a gnutls_pkcs7_t type

     This function will return the OID of the data embedded in the
     signature of the PKCS7 structure.  If no data are available then
     ‘NULL’ will be returned.  The returned value will be valid during
     the lifetime of the ‘pkcs7’ structure.

     *Returns:* On success, a pointer to an OID string, ‘NULL’ on error.

     *Since:* 3.5.5

gnutls_pkcs7_get_signature_count
--------------------------------

 -- Function: int gnutls_pkcs7_get_signature_count (gnutls_pkcs7_t
          PKCS7)
     PKCS7: should contain a ‘gnutls_pkcs7_t’ type

     This function will return the number of signatures in the PKCS7
     structure.

     *Returns:* On success, a positive number is returned, otherwise a
     negative error value.

     *Since:* 3.4.3

gnutls_pkcs7_get_signature_info
-------------------------------

 -- Function: int gnutls_pkcs7_get_signature_info (gnutls_pkcs7_t PKCS7,
          unsigned IDX, gnutls_pkcs7_signature_info_st * INFO)
     PKCS7: should contain a ‘gnutls_pkcs7_t’ type

     IDX: the index of the signature info to check

     INFO: will contain the output signature

     This function will return information about the signature
     identified by idx in the provided PKCS ‘7’ structure.  The
     information should be deinitialized using
     ‘gnutls_pkcs7_signature_info_deinit()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.2

gnutls_pkcs7_import
-------------------

 -- Function: int gnutls_pkcs7_import (gnutls_pkcs7_t PKCS7, const
          gnutls_datum_t * DATA, gnutls_x509_crt_fmt_t FORMAT)
     PKCS7: The data to store the parsed PKCS7.

     DATA: The DER or PEM encoded PKCS7.

     FORMAT: One of DER or PEM

     This function will convert the given DER or PEM encoded PKCS7 to
     the native ‘gnutls_pkcs7_t’ format.  The output will be stored in
     ‘pkcs7’ .  Any signed data that may be present inside the ‘pkcs7’
     structure, like certificates set by ‘gnutls_pkcs7_set_crt()’ , will
     be freed and overwritten by this function.

     If the PKCS7 is PEM encoded it should have a header of "PKCS7".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs7_init
-----------------

 -- Function: int gnutls_pkcs7_init (gnutls_pkcs7_t * PKCS7)
     PKCS7: A pointer to the type to be initialized

     This function will initialize a PKCS7 structure.  PKCS7 structures
     usually contain lists of X.509 Certificates and X.509 Certificate
     revocation lists.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs7_print
------------------

 -- Function: int gnutls_pkcs7_print (gnutls_pkcs7_t PKCS7,
          gnutls_certificate_print_formats_t FORMAT, gnutls_datum_t *
          OUT)
     PKCS7: The PKCS7 struct to be printed

     FORMAT: Indicate the format to use

     OUT: Newly allocated datum with null terminated string.

     This function will pretty print a signed PKCS ‘7’ structure,
     suitable for display to a human.

     Currently the supported formats are ‘GNUTLS_CRT_PRINT_FULL’ and
     ‘GNUTLS_CRT_PRINT_COMPACT’ .

     The output ‘out’ needs to be deallocated using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs7_print_signature_info
---------------------------------

 -- Function: int gnutls_pkcs7_print_signature_info
          (gnutls_pkcs7_signature_info_st * INFO,
          gnutls_certificate_print_formats_t FORMAT, gnutls_datum_t *
          OUT)
     INFO: The PKCS7 signature info struct to be printed

     FORMAT: Indicate the format to use

     OUT: Newly allocated datum with null terminated string.

     This function will pretty print a PKCS ‘7’ signature info
     structure, suitable for display to a human.

     Currently the supported formats are ‘GNUTLS_CRT_PRINT_FULL’ and
     ‘GNUTLS_CRT_PRINT_COMPACT’ .

     The output ‘out’ needs to be deallocated using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.14

gnutls_pkcs7_set_crl
--------------------

 -- Function: int gnutls_pkcs7_set_crl (gnutls_pkcs7_t PKCS7,
          gnutls_x509_crl_t CRL)
     PKCS7: The pkcs7 type

     CRL: the DER encoded crl to be added

     This function will add a parsed CRL to the PKCS7 or RFC2630 crl
     set.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs7_set_crl_raw
------------------------

 -- Function: int gnutls_pkcs7_set_crl_raw (gnutls_pkcs7_t PKCS7, const
          gnutls_datum_t * CRL)
     PKCS7: The pkcs7 type

     CRL: the DER encoded crl to be added

     This function will add a crl to the PKCS7 or RFC2630 crl set.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs7_set_crt
--------------------

 -- Function: int gnutls_pkcs7_set_crt (gnutls_pkcs7_t PKCS7,
          gnutls_x509_crt_t CRT)
     PKCS7: The pkcs7 type

     CRT: the certificate to be copied.

     This function will add a parsed certificate to the PKCS7 or RFC2630
     certificate set.  This is a wrapper function over
     ‘gnutls_pkcs7_set_crt_raw()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs7_set_crt_raw
------------------------

 -- Function: int gnutls_pkcs7_set_crt_raw (gnutls_pkcs7_t PKCS7, const
          gnutls_datum_t * CRT)
     PKCS7: The pkcs7 type

     CRT: the DER encoded certificate to be added

     This function will add a certificate to the PKCS7 or RFC2630
     certificate set.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs7_sign
-----------------

 -- Function: int gnutls_pkcs7_sign (gnutls_pkcs7_t PKCS7,
          gnutls_x509_crt_t SIGNER, gnutls_privkey_t SIGNER_KEY, const
          gnutls_datum_t * DATA, gnutls_pkcs7_attrs_t SIGNED_ATTRS,
          gnutls_pkcs7_attrs_t UNSIGNED_ATTRS, gnutls_digest_algorithm_t
          DIG, unsigned FLAGS)
     PKCS7: should contain a ‘gnutls_pkcs7_t’ type

     SIGNER: the certificate to sign the structure

     SIGNER_KEY: the key to sign the structure

     DATA: The data to be signed or ‘NULL’ if the data are already
     embedded

     SIGNED_ATTRS: Any additional attributes to be included in the
     signed ones (or ‘NULL’ )

     UNSIGNED_ATTRS: Any additional attributes to be included in the
     unsigned ones (or ‘NULL’ )

     DIG: The digest algorithm to use for signing

     FLAGS: Should be zero or one of ‘GNUTLS_PKCS7’ flags

     This function will add a signature in the provided PKCS ‘7’
     structure for the provided data.  Multiple signatures can be made
     with different signers.

     The available flags are: ‘GNUTLS_PKCS7_EMBED_DATA’ ,
     ‘GNUTLS_PKCS7_INCLUDE_TIME’ , ‘GNUTLS_PKCS7_INCLUDE_CERT’ , and
     ‘GNUTLS_PKCS7_WRITE_SPKI’ .  They are explained in the
     ‘gnutls_pkcs7_sign_flags’ definition.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.2

gnutls_pkcs7_signature_info_deinit
----------------------------------

 -- Function: void gnutls_pkcs7_signature_info_deinit
          (gnutls_pkcs7_signature_info_st * INFO)
     INFO: should point to a ‘gnutls_pkcs7_signature_info_st’ structure

     This function will deinitialize any allocated value in the provided
     ‘gnutls_pkcs7_signature_info_st’ .

     *Since:* 3.4.2

gnutls_pkcs7_verify
-------------------

 -- Function: int gnutls_pkcs7_verify (gnutls_pkcs7_t PKCS7,
          gnutls_x509_trust_list_t TL, gnutls_typed_vdata_st * VDATA,
          unsigned int VDATA_SIZE, unsigned IDX, const gnutls_datum_t *
          DATA, unsigned FLAGS)
     PKCS7: should contain a ‘gnutls_pkcs7_t’ type

     TL: A list of trusted certificates

     VDATA: an array of typed data

     VDATA_SIZE: the number of data elements

     IDX: the index of the signature info to check

     DATA: The data to be verified or ‘NULL’

     FLAGS: Zero or an OR list of ‘gnutls_certificate_verify_flags’

     This function will verify the provided data against the signature
     present in the SignedData of the PKCS ‘7’ structure.  If the data
     provided are NULL then the data in the encapsulatedContent field
     will be used instead.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  A verification error results to
     a ‘GNUTLS_E_PK_SIG_VERIFY_FAILED’ and the lack of encapsulated data
     to verify to a ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ .

     *Since:* 3.4.2

gnutls_pkcs7_verify_direct
--------------------------

 -- Function: int gnutls_pkcs7_verify_direct (gnutls_pkcs7_t PKCS7,
          gnutls_x509_crt_t SIGNER, unsigned IDX, const gnutls_datum_t *
          DATA, unsigned FLAGS)
     PKCS7: should contain a ‘gnutls_pkcs7_t’ type

     SIGNER: the certificate believed to have signed the structure

     IDX: the index of the signature info to check

     DATA: The data to be verified or ‘NULL’

     FLAGS: Zero or an OR list of ‘gnutls_certificate_verify_flags’

     This function will verify the provided data against the signature
     present in the SignedData of the PKCS ‘7’ structure.  If the data
     provided are NULL then the data in the encapsulatedContent field
     will be used instead.

     Note that, unlike ‘gnutls_pkcs7_verify()’ this function does not
     verify the key purpose of the signer.  It is expected for the
     caller to verify the intended purpose of the ‘signer’ -e.g., via
     ‘gnutls_x509_crt_get_key_purpose_oid()’ , or
     ‘gnutls_x509_crt_check_key_purpose()’ .

     Note also, that since GnuTLS 3.5.6 this function introduces checks
     in the end certificate ( ‘signer’ ), including time checks and key
     usage checks.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  A verification error results to
     a ‘GNUTLS_E_PK_SIG_VERIFY_FAILED’ and the lack of encapsulated data
     to verify to a ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ .

     *Since:* 3.4.2

