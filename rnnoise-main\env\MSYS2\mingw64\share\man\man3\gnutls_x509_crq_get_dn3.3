.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_dn3" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_dn3 \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_dn3(gnutls_x509_crq_t " crq ", gnutls_datum_t * " dn ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "gnutls_datum_t * dn" 12
a pointer to a structure to hold the name; must be freed using \fBgnutls_free()\fP
.IP "unsigned flags" 12
zero or \fBGNUTLS_X509_DN_FLAG_COMPAT\fP
.SH "DESCRIPTION"
This function will allocate buffer and copy the name of the Certificate 
request. The name will be in the form "C=xxxx,O=yyyy,CN=zzzz" as
described in RFC4514. The output string will be ASCII or UTF\-8
encoded, depending on the certificate data.

When the flag \fBGNUTLS_X509_DN_FLAG_COMPAT\fP is specified, the output
format will match the format output by previous to 3.5.6 versions of GnuTLS
which was not not fully RFC4514\-compliant.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value. and a negative error code on error.
.SH "SINCE"
3.5.7
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
