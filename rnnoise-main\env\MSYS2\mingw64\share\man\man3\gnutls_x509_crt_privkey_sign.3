.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_privkey_sign" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_privkey_sign \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_x509_crt_privkey_sign(gnutls_x509_crt_t " crt ", gnutls_x509_crt_t " issuer ", gnutls_privkey_t " issuer_key ", gnutls_digest_algorithm_t " dig ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
a certificate of type \fBgnutls_x509_crt_t\fP
.IP "gnutls_x509_crt_t issuer" 12
is the certificate of the certificate issuer
.IP "gnutls_privkey_t issuer_key" 12
holds the issuer's private key
.IP "gnutls_digest_algorithm_t dig" 12
The message digest to use, \fBGNUTLS_DIG_SHA256\fP is a safe choice
.IP "unsigned int flags" 12
must be 0
.SH "DESCRIPTION"
This function will sign the certificate with the issuer's private key, and
will copy the issuer's information into the certificate.

This must be the last step in a certificate generation since all
the previously set parameters are now signed.

A known limitation of this function is, that a newly\-signed certificate will not
be fully functional (e.g., for signature verification), until it
is exported an re\-imported.

After GnuTLS 3.6.1 the value of  \fIdig\fP may be \fBGNUTLS_DIG_UNKNOWN\fP,
and in that case, a suitable but reasonable for the key algorithm will be selected.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
