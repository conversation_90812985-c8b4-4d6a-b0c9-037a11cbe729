.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ecc_curve_set_enabled" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ecc_curve_set_enabled \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_ecc_curve_set_enabled(gnutls_ecc_curve_t " curve ", unsigned int " enabled ");"
.SH ARGUMENTS
.IP "gnutls_ecc_curve_t curve" 12
is an ECC curve
.IP "unsigned int enabled" 12
whether to enable the curve
.SH "DESCRIPTION"
Modify the previous system wide setting that marked  \fIcurve\fP as
enabled or disabled.  Calling this function is allowed
only if allowlisting mode is set in the configuration file,
and only if the system\-wide TLS priority string
has not been initialized yet.
The intended usage is to provide applications with a way
to expressly deviate from the distribution or site defaults
inherited from the configuration file.
The modification is composable with further modifications
performed through the priority string mechanism.

This function is not thread\-safe and is intended to be called
in the main thread at the beginning of the process execution.
.SH "RETURNS"
0 on success or negative error code otherwise.
.SH "SINCE"
3.7.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
