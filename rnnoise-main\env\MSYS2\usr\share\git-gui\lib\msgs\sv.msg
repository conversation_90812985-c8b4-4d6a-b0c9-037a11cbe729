set ::msgcat::header "Project-Id-Version: git-gui 0.21.0\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2023-10-26 21:23+0100\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: Swedish <<EMAIL>>\nLanguage: sv\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Gtranslator 3.38.0\n"
::msgcat::mcset sv "Invalid font specified in %s:" "Ogiltigt teckensnitt angivet i %s:"
::msgcat::mcset sv "Main Font" "Huvudteckensnitt"
::msgcat::mcset sv "Diff/Console Font" "Diff/konsolteckensnitt"
::msgcat::mcset sv "git-gui: fatal error" "git-gui: \u00f6desdigert fel"
::msgcat::mcset sv "Cannot find git in PATH." "Hittar inte git i PATH."
::msgcat::mcset sv "Cannot parse Git version string:" "Kan inte tolka versionsstr\u00e4ng fr\u00e5n Git:"
::msgcat::mcset sv "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "Kan inte avg\u00f6ra Gits version.\n\n%s s\u00e4ger att dess version \u00e4r \u201d%s\u201d.\n\n%s kr\u00e4ver minst Git 1.5.0 eller senare.\n\nAnta att \u201d%s\u201d \u00e4r version 1.5.0?\n"
::msgcat::mcset sv "Git directory not found:" "Git-katalogen hittades inte:"
::msgcat::mcset sv "Cannot move to top of working directory:" "Kan inte g\u00e5 till b\u00f6rjan p\u00e5 arbetskatalogen:"
::msgcat::mcset sv "Cannot use bare repository:" "Kan inte anv\u00e4nda naket arkiv:"
::msgcat::mcset sv "No working directory" "Ingen arbetskatalog"
::msgcat::mcset sv "Refreshing file status..." "Uppdaterar filstatus..."
::msgcat::mcset sv "Scanning for modified files ..." "S\u00f6ker efter \u00e4ndrade filer..."
::msgcat::mcset sv "Calling prepare-commit-msg hook..." "Anropar kroken f\u00f6r f\u00f6rberedelse av incheckningsmeddelande (prepare-commit-msg)..."
::msgcat::mcset sv "Commit declined by prepare-commit-msg hook." "Incheckningen avvisades av kroken f\u00f6r f\u00f6rberedelse av incheckningsmeddelande (prepare-commit-msg)."
::msgcat::mcset sv "Ready." "Klar."
::msgcat::mcset sv "Display limit (gui.maxfilesdisplayed = %s) reached, not showing all %s files." "Visningsgr\u00e4ns (gui.maxfilesdisplayed = %s) n\u00e5dd, visare inte samtliga %s filer."
::msgcat::mcset sv "Unmodified" "Of\u00f6r\u00e4ndrade"
::msgcat::mcset sv "Modified, not staged" "F\u00f6r\u00e4ndrade, ej k\u00f6ade"
::msgcat::mcset sv "Staged for commit" "K\u00f6ade f\u00f6r incheckning"
::msgcat::mcset sv "Portions staged for commit" "Delar k\u00f6ade f\u00f6r incheckning"
::msgcat::mcset sv "Staged for commit, missing" "K\u00f6ade f\u00f6r incheckning, saknade"
::msgcat::mcset sv "File type changed, not staged" "Filtyp \u00e4ndrad, ej k\u00f6ade"
::msgcat::mcset sv "File type changed, old type staged for commit" "Filtyp \u00e4ndrad, gammal typ k\u00f6ade f\u00f6r incheckning"
::msgcat::mcset sv "File type changed, staged" "Filtyp \u00e4ndrad, k\u00f6ade"
::msgcat::mcset sv "File type change staged, modification not staged" "Filtyps\u00e4ndringar k\u00f6ade, inneh\u00e5lls\u00e4ndringar ej k\u00f6ade"
::msgcat::mcset sv "File type change staged, file missing" "Filtyps\u00e4ndringar k\u00f6ade, fil saknas"
::msgcat::mcset sv "Untracked, not staged" "Ej sp\u00e5rade, ej k\u00f6ade"
::msgcat::mcset sv "Missing" "Saknade"
::msgcat::mcset sv "Staged for removal" "K\u00f6ade f\u00f6r borttagning"
::msgcat::mcset sv "Staged for removal, still present" "K\u00f6ade f\u00f6r borttagning, fortfarande n\u00e4rvarande"
::msgcat::mcset sv "Requires merge resolution" "Kr\u00e4ver konflikthantering efter sammanslagning"
::msgcat::mcset sv "Couldn't find gitk in PATH" "Hittade inte gitk i PATH."
::msgcat::mcset sv "Starting %s... please wait..." "Startar %s... v\u00e4nta..."
::msgcat::mcset sv "Couldn't find git gui in PATH" "Hittade inte git gui i PATH."
::msgcat::mcset sv "Repository" "Arkiv"
::msgcat::mcset sv "Edit" "Redigera"
::msgcat::mcset sv "Branch" "Gren"
::msgcat::mcset sv "Commit@@noun" "Incheckning"
::msgcat::mcset sv "Merge" "Sl\u00e5 ihop"
::msgcat::mcset sv "Remote" "Fj\u00e4rrarkiv"
::msgcat::mcset sv "Tools" "Verktyg"
::msgcat::mcset sv "Explore Working Copy" "Utforska arbetskopia"
::msgcat::mcset sv "Git Bash" "Git Bash"
::msgcat::mcset sv "Browse Current Branch's Files" "Bl\u00e4ddra i grenens filer"
::msgcat::mcset sv "Browse Branch Files..." "Bl\u00e4ddra filer p\u00e5 gren..."
::msgcat::mcset sv "Visualize Current Branch's History" "Visualisera grenens historik"
::msgcat::mcset sv "Visualize All Branch History" "Visualisera alla grenars historik"
::msgcat::mcset sv "Browse %s's Files" "Bl\u00e4ddra i filer f\u00f6r %s"
::msgcat::mcset sv "Visualize %s's History" "Visualisera historik f\u00f6r %s"
::msgcat::mcset sv "Database Statistics" "Databasstatistik"
::msgcat::mcset sv "Compress Database" "Komprimera databas"
::msgcat::mcset sv "Verify Database" "Verifiera databas"
::msgcat::mcset sv "Create Desktop Icon" "Skapa skrivbordsikon"
::msgcat::mcset sv "Quit" "Avsluta"
::msgcat::mcset sv "Undo" "\u00c5ngra"
::msgcat::mcset sv "Redo" "G\u00f6r om"
::msgcat::mcset sv "Cut" "Klipp ut"
::msgcat::mcset sv "Copy" "Kopiera"
::msgcat::mcset sv "Paste" "Klistra in"
::msgcat::mcset sv "Delete" "Ta bort"
::msgcat::mcset sv "Select All" "Markera alla"
::msgcat::mcset sv "Create..." "Skapa..."
::msgcat::mcset sv "Checkout..." "Checka ut..."
::msgcat::mcset sv "Rename..." "Byt namn..."
::msgcat::mcset sv "Delete..." "Ta bort..."
::msgcat::mcset sv "Reset..." "\u00c5terst\u00e4ll..."
::msgcat::mcset sv "Done" "F\u00e4rdig"
::msgcat::mcset sv "Commit@@verb" "Checka in"
::msgcat::mcset sv "Amend Last Commit" "L\u00e4gg till f\u00f6reg\u00e5ende incheckning"
::msgcat::mcset sv "Rescan" "S\u00f6k p\u00e5 nytt"
::msgcat::mcset sv "Stage To Commit" "K\u00f6a f\u00f6r incheckning"
::msgcat::mcset sv "Stage Changed Files To Commit" "K\u00f6a \u00e4ndrade filer f\u00f6r incheckning"
::msgcat::mcset sv "Unstage From Commit" "Ta bort fr\u00e5n incheckningsk\u00f6"
::msgcat::mcset sv "Revert Changes" "\u00c5terst\u00e4ll \u00e4ndringar"
::msgcat::mcset sv "Show Less Context" "Visa mindre sammanhang"
::msgcat::mcset sv "Show More Context" "Visa mer sammanhang"
::msgcat::mcset sv "Sign Off" "Skriv under"
::msgcat::mcset sv "Local Merge..." "Lokal sammanslagning..."
::msgcat::mcset sv "Abort Merge..." "Avbryt sammanslagning..."
::msgcat::mcset sv "Add..." "L\u00e4gg till..."
::msgcat::mcset sv "Push..." "S\u00e4nd..."
::msgcat::mcset sv "Delete Branch..." "Ta bort gren..."
::msgcat::mcset sv "Options..." "Alternativ..."
::msgcat::mcset sv "Remove..." "Ta bort..."
::msgcat::mcset sv "Help" "Hj\u00e4lp"
::msgcat::mcset sv "About %s" "Om %s"
::msgcat::mcset sv "Online Documentation" "Webbdokumentation"
::msgcat::mcset sv "Show SSH Key" "Visa SSH-nyckel"
::msgcat::mcset sv "usage:" "anv\u00e4ndning:"
::msgcat::mcset sv "Usage" "Anv\u00e4ndning"
::msgcat::mcset sv "Error" "Fel"
::msgcat::mcset sv "fatal: cannot stat path %s: No such file or directory" "\u00f6desdigert: kunde inte ta status p\u00e5 s\u00f6kv\u00e4gen %s: Fil eller katalog saknas"
::msgcat::mcset sv "Current Branch:" "Aktuell gren:"
::msgcat::mcset sv "Unstaged Changes" "Ok\u00f6ade \u00e4ndringar"
::msgcat::mcset sv "Staged Changes (Will Commit)" "K\u00f6ade \u00e4ndringar (kommer att checkas in)"
::msgcat::mcset sv "Stage Changed" "K\u00f6a \u00e4ndrade"
::msgcat::mcset sv "Push" "S\u00e4nd"
::msgcat::mcset sv "Initial Commit Message:" "Inledande incheckningsmeddelande:"
::msgcat::mcset sv "Amended Commit Message:" "Ut\u00f6kat incheckningsmeddelande:"
::msgcat::mcset sv "Amended Initial Commit Message:" "Ut\u00f6kat inledande incheckningsmeddelande:"
::msgcat::mcset sv "Amended Merge Commit Message:" "Ut\u00f6kat incheckningsmeddelande f\u00f6r sammanslagning:"
::msgcat::mcset sv "Merge Commit Message:" "Incheckningsmeddelande f\u00f6r sammanslagning:"
::msgcat::mcset sv "Commit Message:" "Incheckningsmeddelande:"
::msgcat::mcset sv "Copy All" "Kopiera alla"
::msgcat::mcset sv "File:" "Fil:"
::msgcat::mcset sv "Open" "\u00d6ppna"
::msgcat::mcset sv "Refresh" "Uppdatera"
::msgcat::mcset sv "Decrease Font Size" "Minska teckensnittsstorlek"
::msgcat::mcset sv "Increase Font Size" "\u00d6ka teckensnittsstorlek"
::msgcat::mcset sv "Encoding" "Teckenkodning"
::msgcat::mcset sv "Apply/Reverse Hunk" "Anv\u00e4nd/\u00e5terst\u00e4ll del"
::msgcat::mcset sv "Apply/Reverse Line" "Anv\u00e4nd/\u00e5terst\u00e4ll rad"
::msgcat::mcset sv "Revert Hunk" "\u00c5terst\u00e4ll del"
::msgcat::mcset sv "Revert Line" "\u00c5terst\u00e4ll rad"
::msgcat::mcset sv "Undo Last Revert" "\u00c5ngra senaste \u00e5terst\u00e4llning"
::msgcat::mcset sv "Run Merge Tool" "Starta verktyg f\u00f6r sammanslagning"
::msgcat::mcset sv "Use Remote Version" "Anv\u00e4nd versionen fr\u00e5n fj\u00e4rrarkivet"
::msgcat::mcset sv "Use Local Version" "Anv\u00e4nd lokala versionen"
::msgcat::mcset sv "Revert To Base" "\u00c5terst\u00e4ll till basversionen"
::msgcat::mcset sv "Visualize These Changes In The Submodule" "Visualisera \u00e4ndringarna i undermodulen"
::msgcat::mcset sv "Visualize Current Branch History In The Submodule" "Visualisera grenens historik i undermodulen"
::msgcat::mcset sv "Visualize All Branch History In The Submodule" "Visualisera alla grenars historik i undermodulen"
::msgcat::mcset sv "Start git gui In The Submodule" "Starta git gui i undermodulen"
::msgcat::mcset sv "Unstage Hunk From Commit" "Ta bort del ur incheckningsk\u00f6"
::msgcat::mcset sv "Unstage Lines From Commit" "Ta bort rader ur incheckningsk\u00f6"
::msgcat::mcset sv "Revert Lines" "\u00c5terst\u00e4ll rader"
::msgcat::mcset sv "Unstage Line From Commit" "Ta bort rad ur incheckningsk\u00f6"
::msgcat::mcset sv "Stage Hunk For Commit" "St\u00e4ll del i incheckningsk\u00f6"
::msgcat::mcset sv "Stage Lines For Commit" "St\u00e4ll rader i incheckningsk\u00f6"
::msgcat::mcset sv "Stage Line For Commit" "St\u00e4ll rad i incheckningsk\u00f6"
::msgcat::mcset sv "Initializing..." "Initierar..."
::msgcat::mcset sv "git-gui - a graphical user interface for Git." "git-gui - ett grafiskt anv\u00e4ndargr\u00e4nssnitt f\u00f6r Git."
::msgcat::mcset sv "%s (%s): File Viewer" "%s (%s): Filvisare"
::msgcat::mcset sv "Commit:" "Incheckning:"
::msgcat::mcset sv "Copy Commit" "Kopiera incheckning"
::msgcat::mcset sv "Find Text..." "S\u00f6k text..."
::msgcat::mcset sv "Goto Line..." "G\u00e5 till rad..."
::msgcat::mcset sv "Do Full Copy Detection" "G\u00f6r full kopieringsigenk\u00e4nning"
::msgcat::mcset sv "Show History Context" "Visa historiksammanhang"
::msgcat::mcset sv "Blame Parent Commit" "Klandra f\u00f6r\u00e4ldraincheckning"
::msgcat::mcset sv "Reading %s..." "L\u00e4ser %s..."
::msgcat::mcset sv "Loading copy/move tracking annotations..." "L\u00e4ser annoteringar f\u00f6r kopiering/flyttning..."
::msgcat::mcset sv "lines annotated" "rader annoterade"
::msgcat::mcset sv "Loading original location annotations..." "L\u00e4ser in annotering av originalplacering..."
::msgcat::mcset sv "Annotation complete." "Annotering fullbordad."
::msgcat::mcset sv "Busy" "Upptagen"
::msgcat::mcset sv "Annotation process is already running." "Annoteringsprocess k\u00f6rs redan."
::msgcat::mcset sv "Running thorough copy detection..." "K\u00f6r grundlig kopieringsigenk\u00e4nning..."
::msgcat::mcset sv "Loading annotation..." "L\u00e4ser in annotering..."
::msgcat::mcset sv "Author:" "F\u00f6rfattare:"
::msgcat::mcset sv "Committer:" "Incheckare:"
::msgcat::mcset sv "Original File:" "Ursprunglig fil:"
::msgcat::mcset sv "Cannot find HEAD commit:" "Hittar inte incheckning f\u00f6r HEAD:"
::msgcat::mcset sv "Cannot find parent commit:" "Hittar inte f\u00f6r\u00e4ldraincheckning:"
::msgcat::mcset sv "Unable to display parent" "Kan inte visa f\u00f6r\u00e4lder"
::msgcat::mcset sv "Error loading diff:" "Fel vid inl\u00e4sning av differens:"
::msgcat::mcset sv "Originally By:" "Ursprungligen av:"
::msgcat::mcset sv "In File:" "I filen:"
::msgcat::mcset sv "Copied Or Moved Here By:" "Kopierad eller flyttad hit av:"
::msgcat::mcset sv "%s (%s): Checkout Branch" "%s (%s): Checka ut gren"
::msgcat::mcset sv "Checkout Branch" "Checka ut gren"
::msgcat::mcset sv "Checkout" "Checka ut"
::msgcat::mcset sv "Cancel" "Avbryt"
::msgcat::mcset sv "Revision" "Revision"
::msgcat::mcset sv "Options" "Alternativ"
::msgcat::mcset sv "Fetch Tracking Branch" "H\u00e4mta sp\u00e5rande gren"
::msgcat::mcset sv "Detach From Local Branch" "Koppla bort fr\u00e5n lokal gren"
::msgcat::mcset sv "%s (%s): Create Branch" "%s (%s): Skapa gren"
::msgcat::mcset sv "Create New Branch" "Skapa ny gren"
::msgcat::mcset sv "Create" "Skapa"
::msgcat::mcset sv "Branch Name" "Namn p\u00e5 gren"
::msgcat::mcset sv "Name:" "Namn:"
::msgcat::mcset sv "Match Tracking Branch Name" "Anv\u00e4nd namn p\u00e5 sp\u00e5rad gren"
::msgcat::mcset sv "Starting Revision" "Inledande revision"
::msgcat::mcset sv "Update Existing Branch:" "Uppdatera befintlig gren:"
::msgcat::mcset sv "No" "Nej"
::msgcat::mcset sv "Fast Forward Only" "Endast snabbspolning"
::msgcat::mcset sv "Reset" "\u00c5terst\u00e4ll"
::msgcat::mcset sv "Checkout After Creation" "Checka ut n\u00e4r skapad"
::msgcat::mcset sv "Please select a tracking branch." "V\u00e4lj en gren att sp\u00e5ra."
::msgcat::mcset sv "Tracking branch %s is not a branch in the remote repository." "Den sp\u00e5rade grenen %s \u00e4r inte en gren i fj\u00e4rrarkivet."
::msgcat::mcset sv "Please supply a branch name." "Ange ett namn f\u00f6r grenen."
::msgcat::mcset sv "'%s' is not an acceptable branch name." "\u201d%s\u201d kan inte anv\u00e4ndas som namn p\u00e5 grenen."
::msgcat::mcset sv "%s (%s): Delete Branch" "%s (%s): Ta bort gren"
::msgcat::mcset sv "Delete Local Branch" "Ta bort lokal gren"
::msgcat::mcset sv "Local Branches" "Lokala grenar"
::msgcat::mcset sv "Delete Only If Merged Into" "Ta bara bort om sammanslagen med"
::msgcat::mcset sv "Always (Do not perform merge checks)" "Alltid (utf\u00f6r inte sammanslagningstest)"
::msgcat::mcset sv "The following branches are not completely merged into %s:" "F\u00f6ljande grenar \u00e4r inte till fullo sammanslagna med %s:"
::msgcat::mcset sv "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "Det kan vara sv\u00e5rt att \u00e5terst\u00e4lla borttagna grenar.\n\nTa bort de valda grenarna?"
::msgcat::mcset sv " - %s:" " - %s:"
::msgcat::mcset sv "Failed to delete branches:\n%s" "Kunde inte ta bort grenar:\n%s"
::msgcat::mcset sv "%s (%s): Rename Branch" "%s (%s): Byt namn p\u00e5 gren"
::msgcat::mcset sv "Rename Branch" "Byt namn p\u00e5 gren"
::msgcat::mcset sv "Rename" "Byt namn"
::msgcat::mcset sv "Branch:" "Gren:"
::msgcat::mcset sv "New Name:" "Nytt namn:"
::msgcat::mcset sv "Please select a branch to rename." "V\u00e4lj en gren att byta namn p\u00e5."
::msgcat::mcset sv "Branch '%s' already exists." "Grenen \u201d%s\u201d finns redan."
::msgcat::mcset sv "Failed to rename '%s'." "Kunde inte byta namn p\u00e5 \u201d%s\u201d."
::msgcat::mcset sv "Starting..." "Startar..."
::msgcat::mcset sv "%s (%s): File Browser" "%s (%s): Filbl\u00e4ddrare"
::msgcat::mcset sv "Loading %s..." "L\u00e4ser %s..."
::msgcat::mcset sv "\[Up To Parent\]" "\[Upp till f\u00f6r\u00e4lder\]"
::msgcat::mcset sv "%s (%s): Browse Branch Files" "%s (%s): Bl\u00e4ddra filer p\u00e5 grenen"
::msgcat::mcset sv "Browse Branch Files" "Bl\u00e4ddra filer p\u00e5 grenen"
::msgcat::mcset sv "Browse" "Bl\u00e4ddra"
::msgcat::mcset sv "Fetching %s from %s" "H\u00e4mtar %s fr\u00e5n %s"
::msgcat::mcset sv "fatal: Cannot resolve %s" "\u00f6desdigert: Kunde inte sl\u00e5 upp %s"
::msgcat::mcset sv "Close" "St\u00e4ng"
::msgcat::mcset sv "Branch '%s' does not exist." "Grenen \u201d%s\u201d finns inte."
::msgcat::mcset sv "Failed to configure simplified git-pull for '%s'." "Kunde inte konfigurera f\u00f6renklad git-pull f\u00f6r '%s'."
::msgcat::mcset sv "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "Grenen \u201d%s\u201d finns redan.\n\nDen kan inte snabbspolas till %s.\nEn sammanslagning kr\u00e4vs."
::msgcat::mcset sv "Merge strategy '%s' not supported." "Sammanslagningsstrategin \u201d%s\u201d st\u00f6ds inte."
::msgcat::mcset sv "Failed to update '%s'." "Misslyckades med att uppdatera \u201d%s\u201d."
::msgcat::mcset sv "Staging area (index) is already locked." "K\u00f6omr\u00e5det (index) \u00e4r redan l\u00e5st."
::msgcat::mcset sv "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before the current branch can be changed.\n\nThe rescan will be automatically started now.\n" "Det senaste inl\u00e4sta tillst\u00e5ndet motsvarar inte tillst\u00e5ndet i arkivet.\n\nEtt annat Git-program har \u00e4ndrat arkivet sedan senaste avs\u00f6kningen. Du m\u00e5ste utf\u00f6ra en ny s\u00f6kning innan den aktuella grenen kan \u00e4ndras.\n\nS\u00f6kningen kommer att startas automatiskt nu.\n"
::msgcat::mcset sv "Updating working directory to '%s'..." "Uppdaterar arbetskatalogen till \u201d%s\u201d..."
::msgcat::mcset sv "files checked out" "filer utcheckade"
::msgcat::mcset sv "Aborted checkout of '%s' (file level merging is required)." "Avbryter utcheckning av \u201d%s\u201d (sammanslagning p\u00e5 filniv\u00e5 kr\u00e4vs)."
::msgcat::mcset sv "File level merge required." "Sammanslagning p\u00e5 filniv\u00e5 kr\u00e4vs."
::msgcat::mcset sv "Staying on branch '%s'." "Stannar p\u00e5 grenen \u201d%s\u201d."
::msgcat::mcset sv "You are no longer on a local branch.\n\nIf you wanted to be on a branch, create one now starting from 'This Detached Checkout'." "Du \u00e4r inte l\u00e4ngre p\u00e5 en lokal gren.\n\nOm du ville vara p\u00e5 en gren skapar du en nu, baserad p\u00e5 \u201dDenna fr\u00e5nkopplade utcheckning\u201d."
::msgcat::mcset sv "Checked out '%s'." "Checkade ut \u201d%s\u201d."
::msgcat::mcset sv "Resetting '%s' to '%s' will lose the following commits:" "Om du \u00e5terst\u00e4ller \u201d%s\u201d till \u201d%s\u201d g\u00e5r f\u00f6ljande incheckningar f\u00f6rlorade:"
::msgcat::mcset sv "Recovering lost commits may not be easy." "Det kanske inte \u00e4r s\u00e5 enkelt att \u00e5terskapa f\u00f6rlorade incheckningar."
::msgcat::mcset sv "Reset '%s'?" "\u00c5terst\u00e4lla \u201d%s\u201d?"
::msgcat::mcset sv "Visualize" "Visualisera"
::msgcat::mcset sv "Failed to set current branch.\n\nThis working directory is only partially switched.  We successfully updated your files, but failed to update an internal Git file.\n\nThis should not have occurred.  %s will now close and give up." "Kunde inte st\u00e4lla in aktuell gren.\n\nArbetskatalogen har bara v\u00e4xlats delvis. Vi uppdaterade filerna utan problem, men kunde inte uppdatera en intern fil i Git.\n\nDetta skulle inte ha h\u00e4nt. %s kommer nu st\u00e4ngas och ge upp."
::msgcat::mcset sv "Select" "V\u00e4lj"
::msgcat::mcset sv "Font Family" "Teckensnittsfamilj"
::msgcat::mcset sv "Font Size" "Storlek"
::msgcat::mcset sv "Font Example" "Exempel"
::msgcat::mcset sv "This is example text.\nIf you like this text, it can be your font." "Detta \u00e4r en exempeltext.\nOm du tycker om den h\u00e4r texten kan den vara ditt teckensnitt."
::msgcat::mcset sv "Git Gui" "Git Gui"
::msgcat::mcset sv "Create New Repository" "Skapa nytt arkiv"
::msgcat::mcset sv "New..." "Nytt..."
::msgcat::mcset sv "Clone Existing Repository" "Klona befintligt arkiv"
::msgcat::mcset sv "Clone..." "Klona..."
::msgcat::mcset sv "Open Existing Repository" "\u00d6ppna befintligt arkiv"
::msgcat::mcset sv "Open..." "\u00d6ppna..."
::msgcat::mcset sv "Recent Repositories" "Senaste arkiven"
::msgcat::mcset sv "Open Recent Repository:" "\u00d6ppna tidigare arkiv:"
::msgcat::mcset sv "Failed to create repository %s:" "Kunde inte skapa arkivet %s:"
::msgcat::mcset sv "Directory:" "Katalog:"
::msgcat::mcset sv "Git Repository" "Gitarkiv"
::msgcat::mcset sv "Directory %s already exists." "Katalogen %s finns redan."
::msgcat::mcset sv "File %s already exists." "Filen %s finns redan."
::msgcat::mcset sv "Clone" "Klona"
::msgcat::mcset sv "Source Location:" "Plats f\u00f6r k\u00e4llkod:"
::msgcat::mcset sv "Target Directory:" "M\u00e5lkatalog:"
::msgcat::mcset sv "Clone Type:" "Typ av klon:"
::msgcat::mcset sv "Standard (Fast, Semi-Redundant, Hardlinks)" "Standard (snabb, semiredundant, h\u00e5rda l\u00e4nkar)"
::msgcat::mcset sv "Full Copy (Slower, Redundant Backup)" "Full kopia (l\u00e5ngsammare, redundant s\u00e4kerhetskopia)"
::msgcat::mcset sv "Shared (Fastest, Not Recommended, No Backup)" "Delad (snabbast, rekommenderas ej, ingen s\u00e4kerhetskopia)"
::msgcat::mcset sv "Recursively clone submodules too" "Klona \u00e4ven rekursivt undermoduler"
::msgcat::mcset sv "Not a Git repository: %s" "Inte ett Gitarkiv: %s"
::msgcat::mcset sv "Standard only available for local repository." "Standard \u00e4r endast tillg\u00e4ngligt f\u00f6r lokala arkiv."
::msgcat::mcset sv "Shared only available for local repository." "Delat \u00e4r endast tillg\u00e4ngligt f\u00f6r lokala arkiv."
::msgcat::mcset sv "Location %s already exists." "Platsen %s finns redan."
::msgcat::mcset sv "Failed to configure origin" "Kunde inte konfigurera ursprung"
::msgcat::mcset sv "Counting objects" "R\u00e4knar objekt"
::msgcat::mcset sv "buckets" "hinkar"
::msgcat::mcset sv "Unable to copy objects/info/alternates: %s" "Kunde inte kopiera objekt/info/alternativ: %s"
::msgcat::mcset sv "Nothing to clone from %s." "Ingenting att klona fr\u00e5n %s."
::msgcat::mcset sv "The 'master' branch has not been initialized." "Grenen \u201dmaster\u201d har inte initierats."
::msgcat::mcset sv "Hardlinks are unavailable.  Falling back to copying." "H\u00e5rda l\u00e4nkar \u00e4r inte tillg\u00e4ngliga. Faller tillbaka p\u00e5 kopiering."
::msgcat::mcset sv "Cloning from %s" "Klonar fr\u00e5n %s"
::msgcat::mcset sv "Copying objects" "Kopierar objekt"
::msgcat::mcset sv "KiB" "KiB"
::msgcat::mcset sv "Unable to copy object: %s" "Kunde inte kopiera objekt: %s"
::msgcat::mcset sv "Linking objects" "L\u00e4nkar objekt"
::msgcat::mcset sv "objects" "objekt"
::msgcat::mcset sv "Unable to hardlink object: %s" "Kunde inte h\u00e5rdl\u00e4nka objekt: %s"
::msgcat::mcset sv "Cannot fetch branches and objects.  See console output for details." "Kunde inte h\u00e4mta grenar och objekt. Se konsolutdata f\u00f6r detaljer."
::msgcat::mcset sv "Cannot fetch tags.  See console output for details." "Kunde inte h\u00e4mta taggar. Se konsolutdata f\u00f6r detaljer."
::msgcat::mcset sv "Cannot determine HEAD.  See console output for details." "Kunde inte avg\u00f6ra HEAD. Se konsolutdata f\u00f6r detaljer."
::msgcat::mcset sv "Unable to cleanup %s" "Kunde inte st\u00e4da upp %s"
::msgcat::mcset sv "Clone failed." "Kloning misslyckades."
::msgcat::mcset sv "No default branch obtained." "H\u00e4mtade ingen standardgren."
::msgcat::mcset sv "Cannot resolve %s as a commit." "Kunde inte sl\u00e5 upp %s till n\u00e5gon incheckning."
::msgcat::mcset sv "Creating working directory" "Skapar arbetskatalog"
::msgcat::mcset sv "files" "filer"
::msgcat::mcset sv "Initial file checkout failed." "Inledande filutcheckning misslyckades."
::msgcat::mcset sv "Cloning submodules" "Klonar undermoduler"
::msgcat::mcset sv "Cannot clone submodules." "Kan inte klona undermoduler."
::msgcat::mcset sv "Repository:" "Arkiv:"
::msgcat::mcset sv "Failed to open repository %s:" "Kunde inte \u00f6ppna arkivet %s:"
::msgcat::mcset sv "This Detached Checkout" "Denna fr\u00e5nkopplade utcheckning"
::msgcat::mcset sv "Revision Expression:" "Revisionsuttryck:"
::msgcat::mcset sv "Local Branch" "Lokal gren"
::msgcat::mcset sv "Tracking Branch" "Sp\u00e5rande gren"
::msgcat::mcset sv "Tag" "Tagg"
::msgcat::mcset sv "Invalid revision: %s" "Ogiltig revision: %s"
::msgcat::mcset sv "No revision selected." "Ingen revision vald."
::msgcat::mcset sv "Revision expression is empty." "Revisionsuttrycket \u00e4r tomt."
::msgcat::mcset sv "Updated" "Uppdaterad"
::msgcat::mcset sv "URL" "Webbadress"
::msgcat::mcset sv "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "Det finns ingenting att ut\u00f6ka.\n\nDu h\u00e5ller p\u00e5 att skapa den inledande incheckningen. Det finns ingen tidigare incheckning att ut\u00f6ka.\n"
::msgcat::mcset sv "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "Kan inte ut\u00f6ka vid sammanslagning.\n\nDu \u00e4r i mitten av en sammanslagning som inte \u00e4r fullbordad. Du kan inte ut\u00f6ka tidigare incheckningar om du inte f\u00f6rst avbryter den p\u00e5g\u00e5ende sammanslagningen.\n"
::msgcat::mcset sv "Error loading commit data for amend:" "Fel vid inl\u00e4sning av incheckningsdata f\u00f6r ut\u00f6kning:"
::msgcat::mcset sv "Unable to obtain your identity:" "Kunde inte h\u00e4mta din identitet:"
::msgcat::mcset sv "Invalid GIT_COMMITTER_IDENT:" "Felaktig GIT_COMMITTER_IDENT:"
::msgcat::mcset sv "warning: Tcl does not support encoding '%s'." "varning: Tcl st\u00f6der inte teckenkodningen \u201d%s\u201d."
::msgcat::mcset sv "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before another commit can be created.\n\nThe rescan will be automatically started now.\n" "Det senaste inl\u00e4sta tillst\u00e5ndet motsvarar inte tillst\u00e5ndet i arkivet.\n\nEtt annat Git-program har \u00e4ndrat arkivet sedan senaste avs\u00f6kningen. Du m\u00e5ste utf\u00f6ra en ny s\u00f6kning innan du kan g\u00f6ra en ny incheckning.\n\nS\u00f6kningen kommer att startas automatiskt nu.\n"
::msgcat::mcset sv "Unmerged files cannot be committed.\n\nFile %s has merge conflicts.  You must resolve them and stage the file before committing.\n" "Osammanslagna filer kan inte checkas in.\n\nFilen %s har sammanslagningskonflikter. Du m\u00e5ste l\u00f6sa dem och k\u00f6a filen innan du checkar in den.\n"
::msgcat::mcset sv "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "Ok\u00e4nd filstatus %s uppt\u00e4ckt.\n\nFilen %s kan inte checkas in av programmet.\n"
::msgcat::mcset sv "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "Inga \u00e4ndringar att checka in.\n\nDu m\u00e5ste k\u00f6a \u00e5tminstone en fil innan du kan checka in.\n"
::msgcat::mcset sv "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "Ange ett incheckningsmeddelande.\n\nEtt bra incheckningsmeddelande har f\u00f6ljande format:\n\n- F\u00f6rsta raden: Beskriv i en mening vad du gjorde.\n- Andra raden: Tom\n- F\u00f6ljande rader: Beskriv varf\u00f6r det h\u00e4r \u00e4r en bra \u00e4ndring.\n"
::msgcat::mcset sv "Calling pre-commit hook..." "Anropar kroken f\u00f6re incheckning (pre-commit)..."
::msgcat::mcset sv "Commit declined by pre-commit hook." "Incheckningen avvisades av kroken f\u00f6re incheckning (pre-commit)."
::msgcat::mcset sv "You are about to commit on a detached head. This is a potentially dangerous thing to do because if you switch to another branch you will lose your changes and it can be difficult to retrieve them later from the reflog. You should probably cancel this commit and create a new branch to continue.\n \n Do you really want to proceed with your Commit?" "Du \u00e4r p\u00e5 v\u00e4g att checka in p\u00e5 ett fr\u00e5nkopplat huvud. Det kan potentiellt vara farligt, eftersom du kommer f\u00f6rlora dina \u00e4ndringar om du v\u00e4xlar till en annan gren och det kan vara sv\u00e5rt att h\u00e4mta dem senare fr\u00e5n ref-loggen. Du b\u00f6r troligen avbryta incheckningen och skapa en ny gren f\u00f6r att forts\u00e4tta.\n \n Vill du verkligen forts\u00e4tta checka in?"
::msgcat::mcset sv "Calling commit-msg hook..." "Anropar kroken f\u00f6r incheckningsmeddelande (commit-msg)..."
::msgcat::mcset sv "Commit declined by commit-msg hook." "Incheckning avvisad av kroken f\u00f6r incheckningsmeddelande (commit-msg)."
::msgcat::mcset sv "Committing changes..." "Checkar in \u00e4ndringar..."
::msgcat::mcset sv "write-tree failed:" "write-tree misslyckades:"
::msgcat::mcset sv "Commit failed." "Incheckningen misslyckades."
::msgcat::mcset sv "Commit %s appears to be corrupt" "Incheckningen %s verkar vara trasig"
::msgcat::mcset sv "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "Inga \u00e4ndringar att checka in.\n\nInga filer \u00e4ndrades av incheckningen och det var inte en sammanslagning.\n\nEn s\u00f6kning kommer att startas automatiskt nu.\n"
::msgcat::mcset sv "No changes to commit." "Inga \u00e4ndringar att checka in."
::msgcat::mcset sv "commit-tree failed:" "commit-tree misslyckades:"
::msgcat::mcset sv "update-ref failed:" "update-ref misslyckades:"
::msgcat::mcset sv "Created commit %s: %s" "Skapade incheckningen %s: %s"
::msgcat::mcset sv "Working... please wait..." "Arbetar... v\u00e4nta..."
::msgcat::mcset sv "Success" "Lyckades"
::msgcat::mcset sv "Error: Command Failed" "Fel: Kommando misslyckades"
::msgcat::mcset sv "Number of loose objects" "Antal l\u00f6sa objekt"
::msgcat::mcset sv "Disk space used by loose objects" "Diskutrymme anv\u00e4nt av l\u00f6sa objekt"
::msgcat::mcset sv "Number of packed objects" "Antal packade objekt"
::msgcat::mcset sv "Number of packs" "Antal paket"
::msgcat::mcset sv "Disk space used by packed objects" "Diskutrymme anv\u00e4nt av packade objekt"
::msgcat::mcset sv "Packed objects waiting for pruning" "Packade objekt som v\u00e4ntar p\u00e5 st\u00e4dning"
::msgcat::mcset sv "Garbage files" "Skr\u00e4pfiler"
::msgcat::mcset sv "%s:" "%s:"
::msgcat::mcset sv "%s (%s): Database Statistics" "%s (%s): Databasstatistik"
::msgcat::mcset sv "Compressing the object database" "Komprimerar objektdatabasen"
::msgcat::mcset sv "Verifying the object database with fsck-objects" "Verifierar objektdatabasen med fsck-objects"
::msgcat::mcset sv "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database.\n\nCompress the database now?" "Arkivet har f\u00f6r n\u00e4rvarande omkring %i l\u00f6sa objekt.\n\nF\u00f6r att bibeh\u00e5lla optimal prestanda rekommenderas det \u00e5 det best\u00e4mdaste att du komprimerar databasen.\n\nKomprimera databasen nu?"
::msgcat::mcset sv "Invalid date from Git: %s" "Ogiltigt datum fr\u00e5n Git: %s"
::msgcat::mcset sv "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "Hittade inga skillnader.\n\n%s inneh\u00e5ller inga \u00e4ndringar.\n\nModifieringsdatum f\u00f6r filen uppdaterades av ett annat program, men inneh\u00e5llet i filen har inte \u00e4ndrats.\n\nEn s\u00f6kning kommer automatiskt att startas f\u00f6r att hitta andra filer som kan vara i samma tillst\u00e5nd."
::msgcat::mcset sv "Loading diff of %s..." "L\u00e4ser differens f\u00f6r %s..."
::msgcat::mcset sv "LOCAL: deleted\nREMOTE:\n" "LOKAL: borttagen\nFJ\u00c4RR:\n"
::msgcat::mcset sv "REMOTE: deleted\nLOCAL:\n" "FJ\u00c4RR: borttagen\nLOKAL:\n"
::msgcat::mcset sv "LOCAL:\n" "LOKAL:\n"
::msgcat::mcset sv "REMOTE:\n" "FJ\u00c4RR:\n"
::msgcat::mcset sv "Unable to display %s" "Kan inte visa %s"
::msgcat::mcset sv "Error loading file:" "Fel vid l\u00e4sning av fil:"
::msgcat::mcset sv "Git Repository (subproject)" "Gitarkiv (underprojekt)"
::msgcat::mcset sv "* Binary file (not showing content)." "* Bin\u00e4rfil (visar inte inneh\u00e5llet)."
::msgcat::mcset sv "* Untracked file is %d bytes.\n* Showing only first %d bytes.\n" "* Den osp\u00e5rade filen \u00e4r %d byte.\n* Visar endast inledande %d byte.\n"
::msgcat::mcset sv "\n* Untracked file clipped here by %s.\n* To see the entire file, use an external editor.\n" "\n* Den osp\u00e5rade filen klipptes h\u00e4r av %s.\n* F\u00f6r att se hela filen, anv\u00e4nd ett externt redigeringsprogram.\n"
::msgcat::mcset sv "Failed to unstage selected hunk." "Kunde inte ta bort den valda delen fr\u00e5n k\u00f6n."
::msgcat::mcset sv "Failed to revert selected hunk." "Kunde inte \u00e5terst\u00e4lla den valda delen."
::msgcat::mcset sv "Failed to stage selected hunk." "Kunde inte l\u00e4gga till den valda delen till k\u00f6n."
::msgcat::mcset sv "Failed to unstage selected line." "Kunde inte ta bort den valda raden fr\u00e5n k\u00f6n."
::msgcat::mcset sv "Failed to revert selected line." "Kunde inte \u00e5terst\u00e4lla den valda raden."
::msgcat::mcset sv "Failed to stage selected line." "Kunde inte l\u00e4gga till den valda raden till k\u00f6n."
::msgcat::mcset sv "Failed to undo last revert." "Kunde inte \u00e5ngra den senaste \u00e5terst\u00e4llningen."
::msgcat::mcset sv "Default" "Standard"
::msgcat::mcset sv "System (%s)" "Systemets (%s)"
::msgcat::mcset sv "Other" "Annan"
::msgcat::mcset sv "%s: error" "%s: fel"
::msgcat::mcset sv "%s: warning" "%s: varning"
::msgcat::mcset sv "%s hook failed:" "%s-krok misslyckades:"
::msgcat::mcset sv "You must correct the above errors before committing." "Du m\u00e5ste r\u00e4tta till felen ovan innan du checkar in."
::msgcat::mcset sv "%s (%s): error" "%s (%s): fel"
::msgcat::mcset sv "Unable to unlock the index." "Kunde inte l\u00e5sa upp indexet."
::msgcat::mcset sv "Index Error" "Indexfel"
::msgcat::mcset sv "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "Misslyckades med att uppdatera Gitindexet. En oms\u00f6kning kommer att startas automatiskt f\u00f6r att synkronisera om git-gui."
::msgcat::mcset sv "Continue" "Forts\u00e4tt"
::msgcat::mcset sv "Unlock Index" "L\u00e5s upp index"
::msgcat::mcset sv "Unstaging selected files from commit" "Tar bort valda filer fr\u00e5n incheckningsk\u00f6n"
::msgcat::mcset sv "Unstaging %s from commit" "Tar bort %s fr\u00e5n incheckningsk\u00f6n"
::msgcat::mcset sv "Ready to commit." "Redo att checka in."
::msgcat::mcset sv "Adding selected files" "L\u00e4gger till valda filer"
::msgcat::mcset sv "Adding %s" "L\u00e4gger till %s"
::msgcat::mcset sv "Stage %d untracked files?" "K\u00f6a %d osp\u00e5rade filer?"
::msgcat::mcset sv "Adding all changed files" "L\u00e4gger till alla \u00e4ndrade filer"
::msgcat::mcset sv "Revert changes in file %s?" "\u00c5terst\u00e4ll \u00e4ndringarna i filen %s?"
::msgcat::mcset sv "Revert changes in these %i files?" "\u00c5terst\u00e4ll \u00e4ndringarna i dessa %i filer?"
::msgcat::mcset sv "Any unstaged changes will be permanently lost by the revert." "Alla ok\u00f6ade \u00e4ndringar kommer permanent g\u00e5 f\u00f6rlorade vid \u00e5terst\u00e4llningen."
::msgcat::mcset sv "Do Nothing" "G\u00f6r ingenting"
::msgcat::mcset sv "Delete untracked file %s?" "Ta bort den osp\u00e5rade filen %s?"
::msgcat::mcset sv "Delete these %i untracked files?" "Ta bort dessa %i osp\u00e5rade filer?"
::msgcat::mcset sv "Files will be permanently deleted." "Filerna kommer tas bort permanent."
::msgcat::mcset sv "Delete Files" "Ta bort filer"
::msgcat::mcset sv "Deleting" "Tar bort"
::msgcat::mcset sv "Encountered errors deleting files:\n" "Fel uppstod vid borttagning av filer:\n"
::msgcat::mcset sv "None of the %d selected files could be deleted." "Ingen av de %d valda filerna kunde tas bort."
::msgcat::mcset sv "%d of the %d selected files could not be deleted." "%d av de %d valda filerna kunde inte tas bort."
::msgcat::mcset sv "Reverting selected files" "\u00c5terst\u00e4ller valda filer"
::msgcat::mcset sv "Reverting %s" "\u00c5terst\u00e4ller %s"
::msgcat::mcset sv "Goto Line:" "G\u00e5 till rad:"
::msgcat::mcset sv "Go" "G\u00e5"
::msgcat::mcset sv "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "Kan inte sl\u00e5 ihop vid ut\u00f6kning.\n\nDu m\u00e5ste g\u00f6ra f\u00e4rdig ut\u00f6kningen av incheckningen innan du p\u00e5b\u00f6rjar n\u00e5gon slags sammanslagning.\n"
::msgcat::mcset sv "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before a merge can be performed.\n\nThe rescan will be automatically started now.\n" "Det senaste inl\u00e4sta tillst\u00e5ndet motsvarar inte tillst\u00e5ndet i arkivet.\n\nEtt annat Git-program har \u00e4ndrat arkivet sedan senaste avs\u00f6kningen. Du m\u00e5ste utf\u00f6ra en ny s\u00f6kning innan du kan utf\u00f6ra en sammanslagning.\n\nS\u00f6kningen kommer att startas automatiskt nu.\n"
::msgcat::mcset sv "You are in the middle of a conflicted merge.\n\nFile %s has merge conflicts.\n\nYou must resolve them, stage the file, and commit to complete the current merge.  Only then can you begin another merge.\n" "Du \u00e4r mitt i en sammanslagning med konflikter.\n\nFilen %s har sammanslagningskonflikter.\n\nDu m\u00e5ste l\u00f6sa dem, k\u00f6a filen och checka in f\u00f6r att fullborda den aktuella sammanslagningen. N\u00e4r du gjort det kan du p\u00e5b\u00f6rja en ny sammanslagning.\n"
::msgcat::mcset sv "You are in the middle of a change.\n\nFile %s is modified.\n\nYou should complete the current commit before starting a merge.  Doing so will help you abort a failed merge, should the need arise.\n" "Du \u00e4r mitt i en \u00e4ndring.\n\nFilen %s har \u00e4ndringar.\n\nDu b\u00f6r fullborda den aktuella incheckningen innan du p\u00e5b\u00f6rjar en sammanslagning. Om du g\u00f6r det blir det enklare att avbryta en misslyckad sammanslagning, om det skulle vara n\u00f6dv\u00e4ndigt.\n"
::msgcat::mcset sv "%s of %s" "%s av %s"
::msgcat::mcset sv "Merging %s and %s..." "Sl\u00e5r ihop %s och %s..."
::msgcat::mcset sv "Merge completed successfully." "Sammanslagningen avslutades framg\u00e5ngsrikt."
::msgcat::mcset sv "Merge failed.  Conflict resolution is required." "Sammanslagningen misslyckades. Du m\u00e5ste l\u00f6sa konflikterna."
::msgcat::mcset sv "%s (%s): Merge" "%s (%s): Sammanslagning"
::msgcat::mcset sv "Merge Into %s" "Sl\u00e5 ihop i %s"
::msgcat::mcset sv "Revision To Merge" "Revisioner att sl\u00e5 ihop"
::msgcat::mcset sv "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "Kan inte avbryta vid ut\u00f6kning.\n\nDu m\u00e5ste g\u00f6ra dig f\u00e4rdig med att ut\u00f6ka incheckningen.\n"
::msgcat::mcset sv "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "Avbryt sammanslagning?\n\nOm du avbryter sammanslagningen kommer *ALLA* ej incheckade \u00e4ndringar att g\u00e5 f\u00f6rlorade.\n\nG\u00e5 vidare med att avbryta den aktuella sammanslagningen?"
::msgcat::mcset sv "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "\u00c5terst\u00e4ll \u00e4ndringar?\n\nOm du \u00e5terst\u00e4ller \u00e4ndringarna kommer *ALLA* ej incheckade \u00e4ndringar att g\u00e5 f\u00f6rlorade.\n\nG\u00e5 vidare med att \u00e5terst\u00e4lla de aktuella \u00e4ndringarna?"
::msgcat::mcset sv "Aborting" "Avbryter"
::msgcat::mcset sv "files reset" "filer \u00e5terst\u00e4llda"
::msgcat::mcset sv "Abort failed." "Misslyckades avbryta."
::msgcat::mcset sv "Abort completed.  Ready." "Avbrytning fullbordad. Redo."
::msgcat::mcset sv "Force resolution to the base version?" "Tvinga l\u00f6sning att anv\u00e4nda basversionen?"
::msgcat::mcset sv "Force resolution to this branch?" "Tvinga l\u00f6sning att anv\u00e4nda den aktuella grenen?"
::msgcat::mcset sv "Force resolution to the other branch?" "Tvinga l\u00f6sning att anv\u00e4nda den andra grenen?"
::msgcat::mcset sv "Note that the diff shows only conflicting changes.\n\n%s will be overwritten.\n\nThis operation can be undone only by restarting the merge." "Observera att diffen endast visar de \u00e4ndringar som st\u00e5r i konflikt.\n\n%s kommer att skrivas \u00f6ver.\n\nDu m\u00e5ste starta om sammanslagningen f\u00f6r att g\u00f6ra den h\u00e4r operationen ogjord."
::msgcat::mcset sv "File %s seems to have unresolved conflicts, still stage?" "Filen %s verkar inneh\u00e5lla ol\u00f6sta konflikter. Vill du k\u00f6a \u00e4nd\u00e5?"
::msgcat::mcset sv "Adding resolution for %s" "L\u00e4gger till l\u00f6sning f\u00f6r %s"
::msgcat::mcset sv "Cannot resolve deletion or link conflicts using a tool" "Kan inte l\u00f6sa borttagnings- eller l\u00e4nkkonflikter med ett verktyg"
::msgcat::mcset sv "Conflict file does not exist" "Konfliktfil existerar inte"
::msgcat::mcset sv "Not a GUI merge tool: '%s'" "Inte ett grafiskt verktyg f\u00f6r sammanslagning: %s"
::msgcat::mcset sv "Unsupported merge tool '%s'" "Verktyget \u201d%s\u201d f\u00f6r sammanslagning st\u00f6ds inte"
::msgcat::mcset sv "Merge tool is already running, terminate it?" "Verktyget f\u00f6r sammanslagning k\u00f6rs redan. Vill du avsluta det?"
::msgcat::mcset sv "Error retrieving versions:\n%s" "Fel vid h\u00e4mtning av versioner:\n%s"
::msgcat::mcset sv "Could not start the merge tool:\n\n%s" "Kunde inte starta verktyg f\u00f6r sammanslagning:\n\n%s"
::msgcat::mcset sv "Running merge tool..." "K\u00f6r verktyg f\u00f6r sammanslagning..."
::msgcat::mcset sv "Merge tool failed." "Verktyget f\u00f6r sammanslagning misslyckades."
::msgcat::mcset sv "Invalid global encoding '%s'" "Den globala teckenkodningen \u201d%s\u201d \u00e4r ogiltig"
::msgcat::mcset sv "Invalid repo encoding '%s'" "Arkivets teckenkodning \u201d%s\u201d \u00e4r ogiltig"
::msgcat::mcset sv "Restore Defaults" "\u00c5terst\u00e4ll standardv\u00e4rden"
::msgcat::mcset sv "Save" "Spara"
::msgcat::mcset sv "%s Repository" "Arkivet %s"
::msgcat::mcset sv "Global (All Repositories)" "Globalt (alla arkiv)"
::msgcat::mcset sv "User Name" "Anv\u00e4ndarnamn"
::msgcat::mcset sv "Email Address" "E-postadress"
::msgcat::mcset sv "Summarize Merge Commits" "Summera sammanslagningsincheckningar"
::msgcat::mcset sv "Merge Verbosity" "Pratsamhet f\u00f6r sammanslagningar"
::msgcat::mcset sv "Show Diffstat After Merge" "Visa diffstatistik efter sammanslagning"
::msgcat::mcset sv "Use Merge Tool" "Anv\u00e4nd verktyg f\u00f6r sammanslagning"
::msgcat::mcset sv "Trust File Modification Timestamps" "Lita p\u00e5 fil\u00e4ndringstidsst\u00e4mplar"
::msgcat::mcset sv "Prune Tracking Branches During Fetch" "St\u00e4da sp\u00e5rade grenar vid h\u00e4mtning"
::msgcat::mcset sv "Match Tracking Branches" "Matcha sp\u00e5rade grenar"
::msgcat::mcset sv "Use Textconv For Diffs and Blames" "Anv\u00e4nd Textconv f\u00f6r diff och klandring"
::msgcat::mcset sv "Blame Copy Only On Changed Files" "Klandra kopiering bara i \u00e4ndrade filer"
::msgcat::mcset sv "Maximum Length of Recent Repositories List" "Max l\u00e4ngd f\u00f6r lista \u00f6ver tidigare arkiv"
::msgcat::mcset sv "Minimum Letters To Blame Copy On" "Minsta antal tecken att klandra kopiering f\u00f6r"
::msgcat::mcset sv "Blame History Context Radius (days)" "Historikradie f\u00f6r klandring (dagar)"
::msgcat::mcset sv "Number of Diff Context Lines" "Antal rader sammanhang i differenser"
::msgcat::mcset sv "Additional Diff Parameters" "Ytterligare diff-parametrar"
::msgcat::mcset sv "Commit Message Text Width" "Textbredd f\u00f6r incheckningsmeddelande"
::msgcat::mcset sv "New Branch Name Template" "Mall f\u00f6r namn p\u00e5 nya grenar"
::msgcat::mcset sv "Default File Contents Encoding" "Standardteckenkodning f\u00f6r filinneh\u00e5ll"
::msgcat::mcset sv "Warn before committing to a detached head" "Varna f\u00f6r incheckning p\u00e5 fr\u00e5nkopplat huvud"
::msgcat::mcset sv "Staging of untracked files" "K\u00f6a osp\u00e5rade filer"
::msgcat::mcset sv "Show untracked files" "Visa osp\u00e5rade filer"
::msgcat::mcset sv "Tab spacing" "Blanksteg f\u00f6r tabulatortecken"
::msgcat::mcset sv "Change" "\u00c4ndra"
::msgcat::mcset sv "Spelling Dictionary:" "Stavningsordlista:"
::msgcat::mcset sv "Change Font" "Byt teckensnitt"
::msgcat::mcset sv "Choose %s" "V\u00e4lj %s"
::msgcat::mcset sv "pt." "p."
::msgcat::mcset sv "Preferences" "Inst\u00e4llningar"
::msgcat::mcset sv "Failed to completely save options:" "Misslyckades med att helt spara alternativ:"
::msgcat::mcset sv "%s (%s): Add Remote" "%s (%s): L\u00e4gg till fj\u00e4rrarkiv"
::msgcat::mcset sv "Add New Remote" "L\u00e4gg till nytt fj\u00e4rrarkiv"
::msgcat::mcset sv "Add" "L\u00e4gg till"
::msgcat::mcset sv "Remote Details" "Detaljer f\u00f6r fj\u00e4rrarkiv"
::msgcat::mcset sv "Location:" "Plats:"
::msgcat::mcset sv "Further Action" "Ytterligare \u00e5tg\u00e4rd"
::msgcat::mcset sv "Fetch Immediately" "H\u00e4mta omedelbart"
::msgcat::mcset sv "Initialize Remote Repository and Push" "Initiera fj\u00e4rrarkiv och s\u00e4nd till"
::msgcat::mcset sv "Do Nothing Else Now" "G\u00f6r ingent mer nu"
::msgcat::mcset sv "Please supply a remote name." "Ange ett namn f\u00f6r fj\u00e4rrarkivet."
::msgcat::mcset sv "'%s' is not an acceptable remote name." "\u201d%s\u201d kan inte anv\u00e4ndas som namn p\u00e5 fj\u00e4rrarkivet."
::msgcat::mcset sv "Failed to add remote '%s' of location '%s'." "Kunde inte l\u00e4gga till fj\u00e4rrarkivet \u201d%s\u201d p\u00e5 platsen \u201d%s\u201d."
::msgcat::mcset sv "fetch %s" "h\u00e4mta %s"
::msgcat::mcset sv "Fetching the %s" "H\u00e4mtar %s"
::msgcat::mcset sv "Do not know how to initialize repository at location '%s'." "Vet inte hur arkivet p\u00e5 platsen \u201d%s\u201d skall initieras."
::msgcat::mcset sv "push %s" "s\u00e4nd %s"
::msgcat::mcset sv "Setting up the %s (at %s)" "Konfigurerar %s (p\u00e5 %s)"
::msgcat::mcset sv "%s (%s): Delete Branch Remotely" "%s (%s): Ta bort gren fr\u00e5n fj\u00e4rrarkiv"
::msgcat::mcset sv "Delete Branch Remotely" "Ta bort gren fr\u00e5n fj\u00e4rrarkiv"
::msgcat::mcset sv "From Repository" "Fr\u00e5n arkiv"
::msgcat::mcset sv "Remote:" "Fj\u00e4rrarkiv:"
::msgcat::mcset sv "Arbitrary Location:" "Godtycklig plats:"
::msgcat::mcset sv "Branches" "Grenar"
::msgcat::mcset sv "Delete Only If" "Ta endast bort om"
::msgcat::mcset sv "Merged Into:" "Sammanslagen i:"
::msgcat::mcset sv "A branch is required for 'Merged Into'." "En gren kr\u00e4vs f\u00f6r \u201dSammanslagen i\u201d."
::msgcat::mcset sv "The following branches are not completely merged into %s:\n\n - %s" "F\u00f6ljande grenar har inte helt slagits samman i %s:\n\n - %s"
::msgcat::mcset sv "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "En eller flera av sammanslagningstesterna misslyckades eftersom du inte har h\u00e4mtat de n\u00f6dv\u00e4ndiga incheckningarna. F\u00f6rs\u00f6k h\u00e4mta fr\u00e5n %s f\u00f6rst."
::msgcat::mcset sv "Please select one or more branches to delete." "V\u00e4lj en eller flera grenar att ta bort."
::msgcat::mcset sv "Deleting branches from %s" "Tar bort grenar fr\u00e5n %s"
::msgcat::mcset sv "No repository selected." "Inget arkiv markerat."
::msgcat::mcset sv "Scanning %s..." "S\u00f6ker %s..."
::msgcat::mcset sv "Push to" "S\u00e4nd till"
::msgcat::mcset sv "Remove Remote" "Ta bort fj\u00e4rrarkiv"
::msgcat::mcset sv "Prune from" "Ta bort fr\u00e5n"
::msgcat::mcset sv "Fetch from" "H\u00e4mta fr\u00e5n"
::msgcat::mcset sv "All" "Alla"
::msgcat::mcset sv "Find:" "S\u00f6k:"
::msgcat::mcset sv "Next" "N\u00e4sta"
::msgcat::mcset sv "Prev" "F\u00f6reg"
::msgcat::mcset sv "RegExp" "Reg.uttr."
::msgcat::mcset sv "Case" "Skiftl\u00e4ge"
::msgcat::mcset sv "%s (%s): Create Desktop Icon" "%s (%s): Skapa skrivbordsikon"
::msgcat::mcset sv "Cannot write shortcut:" "Kan inte skriva genv\u00e4g:"
::msgcat::mcset sv "Cannot write icon:" "Kan inte skriva ikon:"
::msgcat::mcset sv "Unsupported spell checker" "Stavningskontrollprogrammet st\u00f6ds inte"
::msgcat::mcset sv "Spell checking is unavailable" "Stavningskontroll \u00e4r ej tillg\u00e4nglig"
::msgcat::mcset sv "Invalid spell checking configuration" "Ogiltig inst\u00e4llning f\u00f6r stavningskontroll"
::msgcat::mcset sv "Reverting dictionary to %s." "\u00c5terst\u00e4ller ordlistan till %s."
::msgcat::mcset sv "Spell checker silently failed on startup" "Stavningskontroll misslyckades tyst vid start"
::msgcat::mcset sv "Unrecognized spell checker" "Stavningskontrollprogrammet k\u00e4nns inte igen"
::msgcat::mcset sv "No Suggestions" "Inga f\u00f6rslag"
::msgcat::mcset sv "Unexpected EOF from spell checker" "Ov\u00e4ntat filslut fr\u00e5n stavningskontroll"
::msgcat::mcset sv "Spell Checker Failed" "Stavningskontroll misslyckades"
::msgcat::mcset sv "No keys found." "Inga nycklar hittades."
::msgcat::mcset sv "Found a public key in: %s" "Hittade \u00f6ppen nyckel i: %s"
::msgcat::mcset sv "Generate Key" "Skapa nyckel"
::msgcat::mcset sv "Copy To Clipboard" "Kopiera till Urklipp"
::msgcat::mcset sv "Your OpenSSH Public Key" "Din \u00f6ppna OpenSSH-nyckel"
::msgcat::mcset sv "Generating..." "Skapar..."
::msgcat::mcset sv "Could not start ssh-keygen:\n\n%s" "Kunde inte starta ssh-keygen:\n\n%s"
::msgcat::mcset sv "Generation failed." "Misslyckades med att skapa."
::msgcat::mcset sv "Generation succeeded, but no keys found." "Lyckades skapa nyckeln, men hittar inte n\u00e5gon nyckel."
::msgcat::mcset sv "Your key is in: %s" "Din nyckel finns i: %s"
::msgcat::mcset sv "%s ... %*i of %*i %s (%3i%%)" "%s... %*i av %*i %s (%3i%%)"
::msgcat::mcset sv "%s (%s): Add Tool" "%s (%s): L\u00e4gg till verktyg"
::msgcat::mcset sv "Add New Tool Command" "L\u00e4gg till nytt verktygskommando"
::msgcat::mcset sv "Add globally" "L\u00e4gg till globalt"
::msgcat::mcset sv "Tool Details" "Detaljer f\u00f6r verktyg"
::msgcat::mcset sv "Use '/' separators to create a submenu tree:" "Anv\u00e4nd \u201d/\u201d-avdelare f\u00f6r att skapa ett undermenytr\u00e4d:"
::msgcat::mcset sv "Command:" "Kommando:"
::msgcat::mcset sv "Show a dialog before running" "Visa dialog innan programmet startas"
::msgcat::mcset sv "Ask the user to select a revision (sets \$REVISION)" "Be anv\u00e4ndaren v\u00e4lja en version (s\u00e4tter \$REVISION)"
::msgcat::mcset sv "Ask the user for additional arguments (sets \$ARGS)" "Be anv\u00e4ndaren om ytterligare parametrar (s\u00e4tter \$ARGS)"
::msgcat::mcset sv "Don't show the command output window" "Visa inte kommandots utdataf\u00f6nster"
::msgcat::mcset sv "Run only if a diff is selected (\$FILENAME not empty)" "K\u00f6r endast om en diff har markerats (\$FILENAME \u00e4r inte tomt)"
::msgcat::mcset sv "Please supply a name for the tool." "Ange ett namn f\u00f6r verktyget."
::msgcat::mcset sv "Tool '%s' already exists." "Verktyget \u201d%s\u201d finns redan."
::msgcat::mcset sv "Could not add tool:\n%s" "Kunde inte l\u00e4gga till verktyget:\n%s"
::msgcat::mcset sv "%s (%s): Remove Tool" "%s (%s): Ta bort verktyg"
::msgcat::mcset sv "Remove Tool Commands" "Ta bort verktygskommandon"
::msgcat::mcset sv "Remove" "Ta bort"
::msgcat::mcset sv "(Blue denotes repository-local tools)" "(Bl\u00e5tt anger verktyg lokala f\u00f6r arkivet)"
::msgcat::mcset sv "%s (%s):" "%s (%s):"
::msgcat::mcset sv "Run Command: %s" "K\u00f6r kommandot: %s"
::msgcat::mcset sv "Arguments" "Argument"
::msgcat::mcset sv "OK" "OK"
::msgcat::mcset sv "Running %s requires a selected file." "F\u00f6r att starta %s m\u00e5ste du v\u00e4lja en fil."
::msgcat::mcset sv "Are you sure you want to run %1\$s on file \"%2\$s\"?" "\u00c4r du s\u00e4ker p\u00e5 att du vill starta %1\$s med filen \u201d%2\$s\u201d?"
::msgcat::mcset sv "Are you sure you want to run %s?" "\u00c4r du s\u00e4ker p\u00e5 att du vill starta %s?"
::msgcat::mcset sv "Tool: %s" "Verktyg: %s"
::msgcat::mcset sv "Running: %s" "Exekverar: %s"
::msgcat::mcset sv "Tool completed successfully: %s" "Verktyget avslutades framg\u00e5ngsrikt: %s"
::msgcat::mcset sv "Tool failed: %s" "Verktyget misslyckades: %s"
::msgcat::mcset sv "Fetching new changes from %s" "H\u00e4mtar nya \u00e4ndringar fr\u00e5n %s"
::msgcat::mcset sv "remote prune %s" "fj\u00e4rrborttagning %s"
::msgcat::mcset sv "Pruning tracking branches deleted from %s" "Tar bort sp\u00e5rande grenar som tagits bort fr\u00e5n %s"
::msgcat::mcset sv "fetch all remotes" "h\u00e4mta alla fj\u00e4rrarkiv"
::msgcat::mcset sv "Fetching new changes from all remotes" "H\u00e4mtar nya \u00e4ndringar fr\u00e5n alla fj\u00e4rrarkiv"
::msgcat::mcset sv "remote prune all remotes" "rensa alla fj\u00e4rrarkiv"
::msgcat::mcset sv "Pruning tracking branches deleted from all remotes" "Rensar sp\u00e5rande grenar som tagits bort, fr\u00e5n alla fj\u00e4rrarkiv"
::msgcat::mcset sv "Pushing changes to %s" "S\u00e4nder \u00e4ndringar till %s"
::msgcat::mcset sv "Mirroring to %s" "Speglar till %s"
::msgcat::mcset sv "Pushing %s %s to %s" "S\u00e4nder %s %s till %s"
::msgcat::mcset sv "Push Branches" "S\u00e4nd grenar"
::msgcat::mcset sv "Source Branches" "K\u00e4llgrenar"
::msgcat::mcset sv "Destination Repository" "Destinationsarkiv"
::msgcat::mcset sv "Transfer Options" "\u00d6verf\u00f6ringsalternativ"
::msgcat::mcset sv "Force overwrite existing branch (may discard changes)" "Tvinga \u00f6verskrivning av befintlig gren (kan kasta bort \u00e4ndringar)"
::msgcat::mcset sv "Use thin pack (for slow network connections)" "Anv\u00e4nd tunt paket (f\u00f6r l\u00e5ngsamma n\u00e4tverksanslutningar)"
::msgcat::mcset sv "Include tags" "Ta med taggar"
::msgcat::mcset sv "%s (%s): Push" "%s (%s): S\u00e4nd"
