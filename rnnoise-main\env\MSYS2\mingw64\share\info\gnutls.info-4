This is gnutls.info, produced by makeinfo version 7.1 from gnutls.texi.

This manual is last updated 28 January 2025 for version 3.8.9 of GnuTLS.

Copyright © 2001-2025 Free Software Foundation, Inc.\\ Copyright ©
2001-2025 <PERSON><PERSON>

     Permission is granted to copy, distribute and/or modify this
     document under the terms of the GNU Free Documentation License,
     Version 1.3 or any later version published by the Free Software
     Foundation; with no Invariant Sections, no Front-Cover Texts, and
     no Back-Cover Texts.  A copy of the license is included in the
     section entitled "GNU Free Documentation License".
INFO-DIR-SECTION Software libraries
START-INFO-DIR-ENTRY
* GnuTLS: (gnutls).		GNU Transport Layer Security Library.
END-INFO-DIR-ENTRY

INFO-DIR-SECTION System Administration
START-INFO-DIR-ENTRY
* certtool: (gnutls)certtool Invocation.	Manipulate certificates and keys.
* gnutls-serv: (gnutls)gnutls-serv Invocation.	GnuTLS test server.
* gnutls-cli: (gnutls)gnutls-cli Invocation.	GnuTLS test client.
* gnutls-cli-debug: (gnutls)gnutls-cli-debug Invocation.	GnuTLS debug client.
* psktool: (gnutls)psktool Invocation.	Simple TLS-Pre-Shared-Keys manager.
* srptool: (gnutls)srptool Invocation.	Simple SRP password tool.
END-INFO-DIR-ENTRY


File: gnutls.info,  Node: Datagram TLS API,  Next: X509 certificate API,  Prev: Core TLS API,  Up: API reference

E.2 Datagram TLS API
====================

The prototypes for the following functions lie in ‘gnutls/dtls.h’.

gnutls_dtls_cookie_send
-----------------------

 -- Function: int gnutls_dtls_cookie_send (gnutls_datum_t * KEY, void *
          CLIENT_DATA, size_t CLIENT_DATA_SIZE, gnutls_dtls_prestate_st
          * PRESTATE, gnutls_transport_ptr_t PTR, gnutls_push_func
          PUSH_FUNC)
     KEY: is a random key to be used at cookie generation

     CLIENT_DATA: contains data identifying the client (i.e.  address)

     CLIENT_DATA_SIZE: The size of client's data

     PRESTATE: The previous cookie returned by
     ‘gnutls_dtls_cookie_verify()’

     PTR: A transport pointer to be used by ‘push_func’

     PUSH_FUNC: A function that will be used to reply

     This function can be used to prevent denial of service attacks to a
     DTLS server by requiring the client to reply using a cookie sent by
     this function.  That way it can be ensured that a client we
     allocated resources for (i.e.  ‘gnutls_session_t’ ) is the one that
     the original incoming packet was originated from.

     This function must be called at the first incoming packet, prior to
     allocating any resources and must be succeeded by
     ‘gnutls_dtls_cookie_verify()’ .

     *Returns:* the number of bytes sent, or a negative error code.

     *Since:* 3.0

gnutls_dtls_cookie_verify
-------------------------

 -- Function: int gnutls_dtls_cookie_verify (gnutls_datum_t * KEY, void
          * CLIENT_DATA, size_t CLIENT_DATA_SIZE, void * _MSG, size_t
          MSG_SIZE, gnutls_dtls_prestate_st * PRESTATE)
     KEY: is a random key to be used at cookie generation

     CLIENT_DATA: contains data identifying the client (i.e.  address)

     CLIENT_DATA_SIZE: The size of client's data

     _MSG: An incoming message that initiates a connection.

     MSG_SIZE: The size of the message.

     PRESTATE: The cookie of this client.

     This function will verify the received message for a valid cookie.
     If a valid cookie is returned then it should be associated with the
     session using ‘gnutls_dtls_prestate_set()’ ;

     This function must be called after ‘gnutls_dtls_cookie_send()’ .

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success, or a negative error
     code.

     *Since:* 3.0

gnutls_dtls_get_data_mtu
------------------------

 -- Function: unsigned int gnutls_dtls_get_data_mtu (gnutls_session_t
          SESSION)
     SESSION: is a ‘gnutls_session_t’ type.

     This function will return the actual maximum transfer unit for
     application data.  I.e.  DTLS headers are subtracted from the
     actual MTU which is set using ‘gnutls_dtls_set_mtu()’ .

     *Returns:* the maximum allowed transfer unit.

     *Since:* 3.0

gnutls_dtls_get_mtu
-------------------

 -- Function: unsigned int gnutls_dtls_get_mtu (gnutls_session_t
          SESSION)
     SESSION: is a ‘gnutls_session_t’ type.

     This function will return the MTU size as set with
     ‘gnutls_dtls_set_mtu()’ .  This is not the actual MTU of data you
     can transmit.  Use ‘gnutls_dtls_get_data_mtu()’ for that reason.

     *Returns:* the set maximum transfer unit.

     *Since:* 3.0

gnutls_dtls_get_timeout
-----------------------

 -- Function: unsigned int gnutls_dtls_get_timeout (gnutls_session_t
          SESSION)
     SESSION: is a ‘gnutls_session_t’ type.

     This function will return the milliseconds remaining for a
     retransmission of the previously sent handshake message.  This
     function is useful when DTLS is used in non-blocking mode, to
     estimate when to call ‘gnutls_handshake()’ if no packets have been
     received.

     *Returns:* the remaining time in milliseconds.

     *Since:* 3.0

gnutls_dtls_prestate_set
------------------------

 -- Function: void gnutls_dtls_prestate_set (gnutls_session_t SESSION,
          gnutls_dtls_prestate_st * PRESTATE)
     SESSION: a new session

     PRESTATE: contains the client's prestate

     This function will associate the prestate acquired by the cookie
     authentication with the client, with the newly established session.

     This functions must be called after a successful
     ‘gnutls_dtls_cookie_verify()’ and should be succeeded by the actual
     DTLS handshake using ‘gnutls_handshake()’ .

     *Since:* 3.0

gnutls_dtls_set_data_mtu
------------------------

 -- Function: int gnutls_dtls_set_data_mtu (gnutls_session_t SESSION,
          unsigned int MTU)
     SESSION: is a ‘gnutls_session_t’ type.

     MTU: The maximum unencrypted transfer unit of the session

     This function will set the maximum size of the *unencrypted*
     records which will be sent over a DTLS session.  It is equivalent
     to calculating the DTLS packet overhead with the current encryption
     parameters, and calling ‘gnutls_dtls_set_mtu()’ with that value.
     In particular, this means that you may need to call this function
     again after any negotiation or renegotiation, in order to ensure
     that the MTU is still sufficient to account for the new protocol
     overhead.

     In most cases you only need to call ‘gnutls_dtls_set_mtu()’ with
     the maximum MTU of your transport layer.

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success, or a negative error
     code.

     *Since:* 3.1

gnutls_dtls_set_mtu
-------------------

 -- Function: void gnutls_dtls_set_mtu (gnutls_session_t SESSION,
          unsigned int MTU)
     SESSION: is a ‘gnutls_session_t’ type.

     MTU: The maximum transfer unit of the transport

     This function will set the maximum transfer unit of the transport
     that DTLS packets are sent over.  Note that this should exclude the
     IP (or IPv6) and UDP headers.  So for DTLS over IPv6 on an Ethernet
     device with MTU 1500, the DTLS MTU set with this function would be
     1500 - 40 (IPV6 header) - 8 (UDP header) = 1452.

     *Since:* 3.0

gnutls_dtls_set_timeouts
------------------------

 -- Function: void gnutls_dtls_set_timeouts (gnutls_session_t SESSION,
          unsigned int RETRANS_TIMEOUT, unsigned int TOTAL_TIMEOUT)
     SESSION: is a ‘gnutls_session_t’ type.

     RETRANS_TIMEOUT: The time at which a retransmission will occur in
     milliseconds

     TOTAL_TIMEOUT: The time at which the connection will be aborted, in
     milliseconds.

     This function will set the timeouts required for the DTLS handshake
     protocol.  The retransmission timeout is the time after which a
     message from the peer is not received, the previous messages will
     be retransmitted.  The total timeout is the time after which the
     handshake will be aborted with ‘GNUTLS_E_TIMEDOUT’ .

     The DTLS protocol recommends the values of 1 sec and 60 seconds
     respectively, and these are the default values.

     To disable retransmissions set a ‘retrans_timeout’ larger than the
     ‘total_timeout’ .

     *Since:* 3.0

gnutls_record_get_discarded
---------------------------

 -- Function: unsigned int gnutls_record_get_discarded (gnutls_session_t
          SESSION)
     SESSION: is a ‘gnutls_session_t’ type.

     Returns the number of discarded packets in a DTLS connection.

     *Returns:* The number of discarded packets.

     *Since:* 3.0


File: gnutls.info,  Node: X509 certificate API,  Next: PKCS 7 API,  Prev: Datagram TLS API,  Up: API reference

E.3 X.509 certificate API
=========================

The following functions are to be used for X.509 certificate handling.
Their prototypes lie in ‘gnutls/x509.h’.

gnutls_certificate_get_trust_list
---------------------------------

 -- Function: void gnutls_certificate_get_trust_list
          (gnutls_certificate_credentials_t RES,
          gnutls_x509_trust_list_t * TLIST)
     RES: is a ‘gnutls_certificate_credentials_t’ type.

     TLIST: Location where to store the trust list.

     Obtains the list of trusted certificates stored in ‘res’ and writes
     a pointer to it to the location ‘tlist’ .  The pointer will point
     to memory internal to ‘res’ , and must not be deinitialized.  It
     will be automatically deallocated when the ‘res’ structure is
     deinitialized.

     *Since:* 3.4.0

gnutls_certificate_set_trust_list
---------------------------------

 -- Function: void gnutls_certificate_set_trust_list
          (gnutls_certificate_credentials_t RES,
          gnutls_x509_trust_list_t TLIST, unsigned FLAGS)
     RES: is a ‘gnutls_certificate_credentials_t’ type.

     TLIST: is a ‘gnutls_x509_trust_list_t’ type

     FLAGS: must be zero

     This function sets a trust list in the
     gnutls_certificate_credentials_t type.

     Note that the ‘tlist’ will become part of the credentials structure
     and must not be deallocated.  It will be automatically deallocated
     when the ‘res’ structure is deinitialized.

     *Since:* 3.2.2

gnutls_certificate_verification_profile_get_id
----------------------------------------------

 -- Function: gnutls_certificate_verification_profiles_t
          gnutls_certificate_verification_profile_get_id (const char *
          NAME)
     NAME: is a profile name

     Convert a string to a ‘gnutls_certificate_verification_profiles_t’
     value.  The names are compared in a case insensitive way.

     *Returns:* a ‘gnutls_certificate_verification_profiles_t’ id of the
     specified profile, or ‘GNUTLS_PROFILE_UNKNOWN’ on failure.

gnutls_certificate_verification_profile_get_name
------------------------------------------------

 -- Function: const char *
          gnutls_certificate_verification_profile_get_name
          (gnutls_certificate_verification_profiles_t ID)
     ID: is a profile ID

     Convert a ‘gnutls_certificate_verification_profiles_t’ value to a
     string.

     *Returns:* a string that contains the name of the specified profile
     or ‘NULL’ .

gnutls_pkcs8_info
-----------------

 -- Function: int gnutls_pkcs8_info (const gnutls_datum_t * DATA,
          gnutls_x509_crt_fmt_t FORMAT, unsigned int * SCHEMA, unsigned
          int * CIPHER, void * SALT, unsigned int * SALT_SIZE, unsigned
          int * ITER_COUNT, char ** OID)
     DATA: Holds the PKCS ‘8’ data

     FORMAT: the format of the PKCS ‘8’ data

     SCHEMA: indicate the schema as one of ‘gnutls_pkcs_encrypt_flags_t’

     CIPHER: the cipher used as ‘gnutls_cipher_algorithm_t’

     SALT: PBKDF2 salt (if non-NULL then ‘salt_size’ initially holds its
     size)

     SALT_SIZE: PBKDF2 salt size

     ITER_COUNT: PBKDF2 iteration count

     OID: if non-NULL it will contain an allocated null-terminated
     variable with the OID

     This function will provide information on the algorithms used in a
     particular PKCS ‘8’ structure.  If the structure algorithms are
     unknown the code ‘GNUTLS_E_UNKNOWN_CIPHER_TYPE’ will be returned,
     and only ‘oid’ , will be set.  That is, ‘oid’ will be set on
     encrypted PKCS ‘8’ structures whether supported or not.  It must be
     deinitialized using ‘gnutls_free()’ .  The other variables are only
     set on supported structures.

     *Returns:* ‘GNUTLS_E_INVALID_REQUEST’ if the provided structure
     isn't an encrypted key, ‘GNUTLS_E_UNKNOWN_CIPHER_TYPE’ if the
     structure's encryption isn't supported, or another negative error
     code in case of a failure.  Zero on success.

     *Since:* 3.4.0

gnutls_pkcs_schema_get_name
---------------------------

 -- Function: const char * gnutls_pkcs_schema_get_name (unsigned int
          SCHEMA)
     SCHEMA: Holds the PKCS ‘12’ or PBES2 schema
     (‘gnutls_pkcs_encrypt_flags_t’ )

     This function will return a human readable description of the
     PKCS12 or PBES2 schema.

     *Returns:* a constraint string or ‘NULL’ on error.

     *Since:* 3.4.0

gnutls_pkcs_schema_get_oid
--------------------------

 -- Function: const char * gnutls_pkcs_schema_get_oid (unsigned int
          SCHEMA)
     SCHEMA: Holds the PKCS ‘12’ or PBES2 schema
     (‘gnutls_pkcs_encrypt_flags_t’ )

     This function will return the object identifier of the PKCS12 or
     PBES2 schema.

     *Returns:* a constraint string or ‘NULL’ on error.

     *Since:* 3.4.0

gnutls_session_set_verify_output_function
-----------------------------------------

 -- Function: void gnutls_session_set_verify_output_function
          (gnutls_session_t SESSION, gnutls_verify_output_function *
          FUNC)
     SESSION: is a ‘gnutls_x509_trust_list_t’ type.

     FUNC: is the callback function

     This function sets a callback to be called when the peer's
     certificate chain has to be verified and full path to the trusted
     root has to be printed.

     The callback's function prototype is defined in 'x509.h': int
     (*callback)( gnutls_x509_crt_t cert, gnutls_x509_crt_t issuer,
     gnutls_x509_crl_t crl, unsigned int verification_output);

     If the callback function is provided then gnutls will call it, in
     the certificate verification procedure.  To verify the certificate
     chain and print its path uptp the trusted root, functions such as
     ‘gnutls_certificate_verify_peers()’ ,
     ‘gnutls_x509_trust_list_verify_crt()’ , and
     ‘gnutls_x509_trust_list_verify_crt2()’ can be used.  The callback
     is set in ‘_gnutls_verify_crt_status()’ and
     ‘_gnutls_pkcs11_verify_crt_status()’ .

     *Since:* 3.7.0

gnutls_subject_alt_names_deinit
-------------------------------

 -- Function: void gnutls_subject_alt_names_deinit
          (gnutls_subject_alt_names_t SANS)
     SANS: The alternative names

     This function will deinitialize an alternative names structure.

     *Since:* 3.3.0

gnutls_subject_alt_names_get
----------------------------

 -- Function: int gnutls_subject_alt_names_get
          (gnutls_subject_alt_names_t SANS, unsigned int SEQ, unsigned
          int * SAN_TYPE, gnutls_datum_t * SAN, gnutls_datum_t *
          OTHERNAME_OID)
     SANS: The alternative names

     SEQ: The index of the name to get

     SAN_TYPE: Will hold the type of the name (of
     ‘gnutls_subject_alt_names_t’ )

     SAN: The alternative name data (should be treated as constant)

     OTHERNAME_OID: The object identifier if ‘san_type’ is
     ‘GNUTLS_SAN_OTHERNAME’ (should be treated as constant)

     This function will return a specific alternative name as stored in
     the ‘sans’ type.  The returned values should be treated as constant
     and valid for the lifetime of ‘sans’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the index is out of
     bounds, otherwise a negative error value.

     *Since:* 3.3.0

gnutls_subject_alt_names_init
-----------------------------

 -- Function: int gnutls_subject_alt_names_init
          (gnutls_subject_alt_names_t * SANS)
     SANS: The alternative names

     This function will initialize an alternative names structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_subject_alt_names_set
----------------------------

 -- Function: int gnutls_subject_alt_names_set
          (gnutls_subject_alt_names_t SANS, unsigned int SAN_TYPE, const
          gnutls_datum_t * SAN, const char * OTHERNAME_OID)
     SANS: The alternative names

     SAN_TYPE: The type of the name (of ‘gnutls_subject_alt_names_t’ )

     SAN: The alternative name data

     OTHERNAME_OID: The object identifier if ‘san_type’ is
     ‘GNUTLS_SAN_OTHERNAME’

     This function will store the specified alternative name in the
     ‘sans’ .

     Since version 3.5.7 the ‘GNUTLS_SAN_RFC822NAME’ ,
     ‘GNUTLS_SAN_DNSNAME’ , and ‘GNUTLS_SAN_OTHERNAME_XMPP’ are
     converted to ACE format when necessary.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0), otherwise a negative
     error value.

     *Since:* 3.3.0

gnutls_x509_aia_deinit
----------------------

 -- Function: void gnutls_x509_aia_deinit (gnutls_x509_aia_t AIA)
     AIA: The authority info access

     This function will deinitialize an authority info access type.

     *Since:* 3.3.0

gnutls_x509_aia_get
-------------------

 -- Function: int gnutls_x509_aia_get (gnutls_x509_aia_t AIA, unsigned
          int SEQ, gnutls_datum_t * OID, unsigned * SAN_TYPE,
          gnutls_datum_t * SAN)
     AIA: The authority info access

     SEQ: specifies the sequence number of the access descriptor (0 for
     the first one, 1 for the second etc.)

     OID: the type of available data; to be treated as constant.

     SAN_TYPE: Will hold the type of the name of
     ‘gnutls_subject_alt_names_t’ (may be null).

     SAN: the access location name; to be treated as constant (may be
     null).

     This function reads from the Authority Information Access type.

     The ‘seq’ input parameter is used to indicate which member of the
     sequence the caller is interested in.  The first member is 0, the
     second member 1 and so on.  When the ‘seq’ value is out of bounds,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.

     Typically ‘oid’ is ‘GNUTLS_OID_AD_CAISSUERS’ or
     ‘GNUTLS_OID_AD_OCSP’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_aia_init
--------------------

 -- Function: int gnutls_x509_aia_init (gnutls_x509_aia_t * AIA)
     AIA: The authority info access

     This function will initialize an authority info access type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_aia_set
-------------------

 -- Function: int gnutls_x509_aia_set (gnutls_x509_aia_t AIA, const char
          * OID, unsigned SAN_TYPE, const gnutls_datum_t * SAN)
     AIA: The authority info access

     OID: the type of data.

     SAN_TYPE: The type of the name (of ‘gnutls_subject_alt_names_t’ )

     SAN: The alternative name data

     This function will store the specified alternative name in the
     ‘aia’ type.

     Typically the value for ‘oid’ should be ‘GNUTLS_OID_AD_OCSP’ , or
     ‘GNUTLS_OID_AD_CAISSUERS’ .

     Since version 3.5.7 the ‘GNUTLS_SAN_RFC822NAME’ , and
     ‘GNUTLS_SAN_DNSNAME’ , are converted to ACE format when necessary.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0), otherwise a negative
     error value.

     *Since:* 3.3.0

gnutls_x509_aki_deinit
----------------------

 -- Function: void gnutls_x509_aki_deinit (gnutls_x509_aki_t AKI)
     AKI: The authority key identifier type

     This function will deinitialize an authority key identifier.

     *Since:* 3.3.0

gnutls_x509_aki_get_cert_issuer
-------------------------------

 -- Function: int gnutls_x509_aki_get_cert_issuer (gnutls_x509_aki_t
          AKI, unsigned int SEQ, unsigned int * SAN_TYPE, gnutls_datum_t
          * SAN, gnutls_datum_t * OTHERNAME_OID, gnutls_datum_t *
          SERIAL)
     AKI: The authority key ID

     SEQ: The index of the name to get

     SAN_TYPE: Will hold the type of the name (of
     ‘gnutls_subject_alt_names_t’ )

     SAN: The alternative name data

     OTHERNAME_OID: The object identifier if ‘san_type’ is
     ‘GNUTLS_SAN_OTHERNAME’

     SERIAL: The authorityCertSerialNumber number

     This function will return a specific authorityCertIssuer name as
     stored in the ‘aki’ type, as well as the authorityCertSerialNumber.
     All the returned values should be treated as constant, and may be
     set to ‘NULL’ when are not required.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the index is out of
     bounds, otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_aki_get_id
----------------------

 -- Function: int gnutls_x509_aki_get_id (gnutls_x509_aki_t AKI,
          gnutls_datum_t * ID)
     AKI: The authority key ID

     ID: Will hold the identifier

     This function will return the key identifier as stored in the ‘aki’
     type.  The identifier should be treated as constant.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the index is out of
     bounds, otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_aki_init
--------------------

 -- Function: int gnutls_x509_aki_init (gnutls_x509_aki_t * AKI)
     AKI: The authority key ID type

     This function will initialize an authority key ID.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_aki_set_cert_issuer
-------------------------------

 -- Function: int gnutls_x509_aki_set_cert_issuer (gnutls_x509_aki_t
          AKI, unsigned int SAN_TYPE, const gnutls_datum_t * SAN, const
          char * OTHERNAME_OID, const gnutls_datum_t * SERIAL)
     AKI: The authority key ID

     SAN_TYPE: the type of the name (of ‘gnutls_subject_alt_names_t’ ),
     may be null

     SAN: The alternative name data

     OTHERNAME_OID: The object identifier if ‘san_type’ is
     ‘GNUTLS_SAN_OTHERNAME’

     SERIAL: The authorityCertSerialNumber number (may be null)

     This function will set the authorityCertIssuer name and the
     authorityCertSerialNumber to be stored in the ‘aki’ type.  When
     storing multiple names, the serial should be set on the first call,
     and subsequent calls should use a ‘NULL’ serial.

     Since version 3.5.7 the ‘GNUTLS_SAN_RFC822NAME’ ,
     ‘GNUTLS_SAN_DNSNAME’ , and ‘GNUTLS_SAN_OTHERNAME_XMPP’ are
     converted to ACE format when necessary.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_aki_set_id
----------------------

 -- Function: int gnutls_x509_aki_set_id (gnutls_x509_aki_t AKI, const
          gnutls_datum_t * ID)
     AKI: The authority key ID

     ID: the key identifier

     This function will set the keyIdentifier to be stored in the ‘aki’
     type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_cidr_to_rfc5280
---------------------------

 -- Function: int gnutls_x509_cidr_to_rfc5280 (const char * CIDR,
          gnutls_datum_t * CIDR_RFC5280)
     CIDR: CIDR in RFC4632 format (IP/prefix), null-terminated

     CIDR_RFC5280: CIDR range converted to RFC5280 format

     This function will convert text CIDR range with prefix (such as
     '10.0.0.0/8') to RFC5280 (IP address in network byte order followed
     by its network mask).  Works for both IPv4 and IPv6.

     The resulting object is directly usable for IP name constraints
     usage, for example in functions
     ‘gnutls_x509_name_constraints_add_permitted’ or
     ‘gnutls_x509_name_constraints_add_excluded’ .

     The data in datum needs to be deallocated using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.4

gnutls_x509_crl_check_issuer
----------------------------

 -- Function: unsigned gnutls_x509_crl_check_issuer (gnutls_x509_crl_t
          CRL, gnutls_x509_crt_t ISSUER)
     CRL: is the CRL to be checked

     ISSUER: is the certificate of a possible issuer

     This function will check if the given CRL was issued by the given
     issuer certificate.

     *Returns:* true (1) if the given CRL was issued by the given
     issuer, and false (0) if not.

gnutls_x509_crl_deinit
----------------------

 -- Function: void gnutls_x509_crl_deinit (gnutls_x509_crl_t CRL)
     CRL: The data to be deinitialized

     This function will deinitialize a CRL structure.

gnutls_x509_crl_dist_points_deinit
----------------------------------

 -- Function: void gnutls_x509_crl_dist_points_deinit
          (gnutls_x509_crl_dist_points_t CDP)
     CDP: The CRL distribution points

     This function will deinitialize a CRL distribution points type.

     *Since:* 3.3.0

gnutls_x509_crl_dist_points_get
-------------------------------

 -- Function: int gnutls_x509_crl_dist_points_get
          (gnutls_x509_crl_dist_points_t CDP, unsigned int SEQ, unsigned
          int * TYPE, gnutls_datum_t * SAN, unsigned int * REASONS)
     CDP: The CRL distribution points

     SEQ: specifies the sequence number of the distribution point (0 for
     the first one, 1 for the second etc.)

     TYPE: The name type of the corresponding name
     (gnutls_x509_subject_alt_name_t)

     SAN: The distribution point names (to be treated as constant)

     REASONS: Revocation reasons.  An ORed sequence of flags from
     ‘gnutls_x509_crl_reason_flags_t’ .

     This function retrieves the individual CRL distribution points
     (*********), contained in provided type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the index is out of
     bounds, otherwise a negative error value.

gnutls_x509_crl_dist_points_init
--------------------------------

 -- Function: int gnutls_x509_crl_dist_points_init
          (gnutls_x509_crl_dist_points_t * CDP)
     CDP: The CRL distribution points

     This function will initialize a CRL distribution points type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_crl_dist_points_set
-------------------------------

 -- Function: int gnutls_x509_crl_dist_points_set
          (gnutls_x509_crl_dist_points_t CDP,
          gnutls_x509_subject_alt_name_t TYPE, const gnutls_datum_t *
          SAN, unsigned int REASONS)
     CDP: The CRL distribution points

     TYPE: The type of the name (of ‘gnutls_subject_alt_names_t’ )

     SAN: The point name data

     REASONS: Revocation reasons.  An ORed sequence of flags from
     ‘gnutls_x509_crl_reason_flags_t’ .

     This function will store the specified CRL distribution point value
     the ‘cdp’ type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0), otherwise a negative
     error value.

     *Since:* 3.3.0

gnutls_x509_crl_export
----------------------

 -- Function: int gnutls_x509_crl_export (gnutls_x509_crl_t CRL,
          gnutls_x509_crt_fmt_t FORMAT, void * OUTPUT_DATA, size_t *
          OUTPUT_DATA_SIZE)
     CRL: Holds the revocation list

     FORMAT: the format of output params.  One of PEM or DER.

     OUTPUT_DATA: will contain a private key PEM or DER encoded

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will export the revocation list to DER or PEM format.

     If the buffer provided is not long enough to hold the output, then
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ will be returned.

     If the structure is PEM encoded, it will have a header of "BEGIN
     X509 CRL".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_export2
-----------------------

 -- Function: int gnutls_x509_crl_export2 (gnutls_x509_crl_t CRL,
          gnutls_x509_crt_fmt_t FORMAT, gnutls_datum_t * OUT)
     CRL: Holds the revocation list

     FORMAT: the format of output params.  One of PEM or DER.

     OUT: will contain a private key PEM or DER encoded

     This function will export the revocation list to DER or PEM format.

     The output buffer is allocated using ‘gnutls_malloc()’ .

     If the structure is PEM encoded, it will have a header of "BEGIN
     X509 CRL".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     Since 3.1.3

gnutls_x509_crl_get_authority_key_gn_serial
-------------------------------------------

 -- Function: int gnutls_x509_crl_get_authority_key_gn_serial
          (gnutls_x509_crl_t CRL, unsigned int SEQ, void * ALT, size_t *
          ALT_SIZE, unsigned int * ALT_TYPE, void * SERIAL, size_t *
          SERIAL_SIZE, unsigned int * CRITICAL)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     SEQ: specifies the sequence number of the alt name (0 for the first
     one, 1 for the second etc.)

     ALT: is the place where the alternative name will be copied to

     ALT_SIZE: holds the size of alt.

     ALT_TYPE: holds the type of the alternative name (one of
     gnutls_x509_subject_alt_name_t).

     SERIAL: buffer to store the serial number (may be null)

     SERIAL_SIZE: Holds the size of the serial field (may be null)

     CRITICAL: will be non-zero if the extension is marked as critical
     (may be null)

     This function will return the X.509 authority key identifier when
     stored as a general name (authorityCertIssuer) and serial number.

     Because more than one general names might be stored ‘seq’ can be
     used as a counter to request them all until
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.

     *Returns:* Returns 0 on success, or an error code.

     *Since:* 3.0

gnutls_x509_crl_get_authority_key_id
------------------------------------

 -- Function: int gnutls_x509_crl_get_authority_key_id
          (gnutls_x509_crl_t CRL, void * ID, size_t * ID_SIZE, unsigned
          int * CRITICAL)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     ID: The place where the identifier will be copied

     ID_SIZE: Holds the size of the result field.

     CRITICAL: will be non-zero if the extension is marked as critical
     (may be null)

     This function will return the CRL authority's key identifier.  This
     is obtained by the X.509 Authority Key identifier extension field
     (2.5.29.35).  Note that this function only returns the
     keyIdentifier field of the extension and
     ‘GNUTLS_E_X509_UNSUPPORTED_EXTENSION’ , if the extension contains
     the name and serial number of the certificate.  In that case
     ‘gnutls_x509_crl_get_authority_key_gn_serial()’ may be used.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code in case of an error.

     *Since:* 2.8.0

gnutls_x509_crl_get_crt_count
-----------------------------

 -- Function: int gnutls_x509_crl_get_crt_count (gnutls_x509_crl_t CRL)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     This function will return the number of revoked certificates in the
     given CRL.

     *Returns:* number of certificates, a negative error code on
     failure.

gnutls_x509_crl_get_crt_serial
------------------------------

 -- Function: int gnutls_x509_crl_get_crt_serial (gnutls_x509_crl_t CRL,
          unsigned INDX, unsigned char * SERIAL, size_t * SERIAL_SIZE,
          time_t * T)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     INDX: the index of the certificate to extract (starting from 0)

     SERIAL: where the serial number will be copied

     SERIAL_SIZE: initially holds the size of serial

     T: if non null, will hold the time this certificate was revoked

     This function will retrieve the serial number of the specified, by
     the index, revoked certificate.

     Note that this function will have performance issues in large
     sequences of revoked certificates.  In that case use
     ‘gnutls_x509_crl_iter_crt_serial()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_get_dn_oid
--------------------------

 -- Function: int gnutls_x509_crl_get_dn_oid (gnutls_x509_crl_t CRL,
          unsigned INDX, void * OID, size_t * SIZEOF_OID)
     CRL: should contain a gnutls_x509_crl_t type

     INDX: Specifies which DN OID to send.  Use (0) to get the first
     one.

     OID: a pointer to store the OID (may be null)

     SIZEOF_OID: initially holds the size of 'oid'

     This function will extract the requested OID of the name of the CRL
     issuer, specified by the given index.

     If oid is null then only the size will be filled.

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the sizeof_oid will be updated
     with the required size.  On success 0 is returned.

gnutls_x509_crl_get_extension_data
----------------------------------

 -- Function: int gnutls_x509_crl_get_extension_data (gnutls_x509_crl_t
          CRL, unsigned INDX, void * DATA, size_t * SIZEOF_DATA)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     INDX: Specifies which extension OID to send.  Use (0) to get the
     first one.

     DATA: a pointer to a structure to hold the data (may be null)

     SIZEOF_DATA: initially holds the size of ‘oid’

     This function will return the requested extension data in the CRL.
     The extension data will be stored as a string in the provided
     buffer.

     Use ‘gnutls_x509_crl_get_extension_info()’ to extract the OID and
     critical flag.  Use ‘gnutls_x509_crl_get_extension_info()’ instead,
     if you want to get data indexed by the extension OID rather than
     sequence.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code in case of an error.  If your have
     reached the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Since:* 2.8.0

gnutls_x509_crl_get_extension_data2
-----------------------------------

 -- Function: int gnutls_x509_crl_get_extension_data2 (gnutls_x509_crl_t
          CRL, unsigned INDX, gnutls_datum_t * DATA)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     INDX: Specifies which extension OID to read.  Use (0) to get the
     first one.

     DATA: will contain the extension DER-encoded data

     This function will return the requested by the index extension data
     in the certificate revocation list.  The extension data will be
     allocated using ‘gnutls_malloc()’ .

     Use ‘gnutls_x509_crt_get_extension_info()’ to extract the OID.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  If you have reached
     the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

gnutls_x509_crl_get_extension_info
----------------------------------

 -- Function: int gnutls_x509_crl_get_extension_info (gnutls_x509_crl_t
          CRL, unsigned INDX, void * OID, size_t * SIZEOF_OID, unsigned
          int * CRITICAL)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     INDX: Specifies which extension OID to send, use (0) to get the
     first one.

     OID: a pointer to store the OID

     SIZEOF_OID: initially holds the maximum size of ‘oid’ , on return
     holds actual size of ‘oid’ .

     CRITICAL: output variable with critical flag, may be NULL.

     This function will return the requested extension OID in the CRL,
     and the critical flag for it.  The extension OID will be stored as
     a string in the provided buffer.  Use
     ‘gnutls_x509_crl_get_extension_data()’ to extract the data.

     If the buffer provided is not long enough to hold the output, then
     * ‘sizeof_oid’ is updated and ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ will
     be returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code in case of an error.  If your have
     reached the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Since:* 2.8.0

gnutls_x509_crl_get_extension_oid
---------------------------------

 -- Function: int gnutls_x509_crl_get_extension_oid (gnutls_x509_crl_t
          CRL, unsigned INDX, void * OID, size_t * SIZEOF_OID)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     INDX: Specifies which extension OID to send, use (0) to get the
     first one.

     OID: a pointer to store the OID (may be null)

     SIZEOF_OID: initially holds the size of ‘oid’

     This function will return the requested extension OID in the CRL.
     The extension OID will be stored as a string in the provided
     buffer.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code in case of an error.  If your have
     reached the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Since:* 2.8.0

gnutls_x509_crl_get_issuer_dn
-----------------------------

 -- Function: int gnutls_x509_crl_get_issuer_dn (gnutls_x509_crl_t CRL,
          char * BUF, size_t * SIZEOF_BUF)
     CRL: should contain a gnutls_x509_crl_t type

     BUF: a pointer to a structure to hold the peer's name (may be null)

     SIZEOF_BUF: initially holds the size of ‘buf’

     This function will copy the name of the CRL issuer in the provided
     buffer.  The name will be in the form "C=xxxx,O=yyyy,CN=zzzz" as
     described in RFC4514.  The output string will be ASCII or UTF-8
     encoded, depending on the certificate data.

     If buf is ‘NULL’ then only the size will be filled.

     This function does not output a fully RFC4514 compliant string, if
     that is required see ‘gnutls_x509_crl_get_issuer_dn3()’ .

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the sizeof_buf will be updated
     with the required size, and 0 on success.

gnutls_x509_crl_get_issuer_dn2
------------------------------

 -- Function: int gnutls_x509_crl_get_issuer_dn2 (gnutls_x509_crl_t CRL,
          gnutls_datum_t * DN)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     DN: a pointer to a structure to hold the name; must be freed using
     ‘gnutls_free()’

     This function will allocate buffer and copy the name of the CRL
     issuer.  The name will be in the form "C=xxxx,O=yyyy,CN=zzzz" as
     described in RFC4514.  The output string will be ASCII or UTF-8
     encoded, depending on the certificate data.

     This function does not output a fully RFC4514 compliant string, if
     that is required see ‘gnutls_x509_crl_get_issuer_dn3()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.10

gnutls_x509_crl_get_issuer_dn3
------------------------------

 -- Function: int gnutls_x509_crl_get_issuer_dn3 (gnutls_x509_crl_t CRL,
          gnutls_datum_t * DN, unsigned FLAGS)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     DN: a pointer to a structure to hold the name; must be freed using
     ‘gnutls_free()’

     FLAGS: zero or ‘GNUTLS_X509_DN_FLAG_COMPAT’

     This function will allocate buffer and copy the name of the CRL
     issuer.  The name will be in the form "C=xxxx,O=yyyy,CN=zzzz" as
     described in RFC4514.  The output string will be ASCII or UTF-8
     encoded, depending on the certificate data.

     When the flag ‘GNUTLS_X509_DN_FLAG_COMPAT’ is specified, the output
     format will match the format output by previous to 3.5.6 versions
     of GnuTLS which was not not fully RFC4514-compliant.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.7

gnutls_x509_crl_get_issuer_dn_by_oid
------------------------------------

 -- Function: int gnutls_x509_crl_get_issuer_dn_by_oid
          (gnutls_x509_crl_t CRL, const char * OID, unsigned INDX,
          unsigned int RAW_FLAG, void * BUF, size_t * SIZEOF_BUF)
     CRL: should contain a gnutls_x509_crl_t type

     OID: holds an Object Identified in null terminated string

     INDX: In case multiple same OIDs exist in the RDN, this specifies
     which to send.  Use (0) to get the first one.

     RAW_FLAG: If non-zero returns the raw DER data of the DN part.

     BUF: a pointer to a structure to hold the peer's name (may be null)

     SIZEOF_BUF: initially holds the size of ‘buf’

     This function will extract the part of the name of the CRL issuer
     specified by the given OID. The output will be encoded as described
     in RFC4514.  The output string will be ASCII or UTF-8 encoded,
     depending on the certificate data.

     Some helper macros with popular OIDs can be found in gnutls/x509.h
     If raw flag is (0), this function will only return known OIDs as
     text.  Other OIDs will be DER encoded, as described in RFC4514 - in
     hex format with a '#' prefix.  You can check about known OIDs using
     ‘gnutls_x509_dn_oid_known()’ .

     If buf is null then only the size will be filled.

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the sizeof_buf will be updated
     with the required size, and 0 on success.

gnutls_x509_crl_get_next_update
-------------------------------

 -- Function: time_t gnutls_x509_crl_get_next_update (gnutls_x509_crl_t
          CRL)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     This function will return the time the next CRL will be issued.
     This field is optional in a CRL so it might be normal to get an
     error instead.

     *Returns:* when the next CRL will be issued, or (time_t)-1 on
     error.

gnutls_x509_crl_get_number
--------------------------

 -- Function: int gnutls_x509_crl_get_number (gnutls_x509_crl_t CRL,
          void * RET, size_t * RET_SIZE, unsigned int * CRITICAL)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     RET: The place where the number will be copied

     RET_SIZE: Holds the size of the result field.

     CRITICAL: will be non-zero if the extension is marked as critical
     (may be null)

     This function will return the CRL number extension.  This is
     obtained by the CRL Number extension field (2.5.29.20).

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code in case of an error.

     *Since:* 2.8.0

gnutls_x509_crl_get_raw_issuer_dn
---------------------------------

 -- Function: int gnutls_x509_crl_get_raw_issuer_dn (gnutls_x509_crl_t
          CRL, gnutls_datum_t * DN)
     CRL: should contain a gnutls_x509_crl_t type

     DN: will hold the starting point of the DN

     This function will return a pointer to the DER encoded DN structure
     and the length.

     *Returns:* a negative error code on error, and (0) on success.

     *Since:* 2.12.0

gnutls_x509_crl_get_signature
-----------------------------

 -- Function: int gnutls_x509_crl_get_signature (gnutls_x509_crl_t CRL,
          char * SIG, size_t * SIZEOF_SIG)
     CRL: should contain a gnutls_x509_crl_t type

     SIG: a pointer where the signature part will be copied (may be
     null).

     SIZEOF_SIG: initially holds the size of ‘sig’

     This function will extract the signature field of a CRL.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_get_signature_algorithm
---------------------------------------

 -- Function: int gnutls_x509_crl_get_signature_algorithm
          (gnutls_x509_crl_t CRL)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     This function will return a value of the ‘gnutls_sign_algorithm_t’
     enumeration that is the signature algorithm.

     Since 3.6.0 this function never returns a negative error code.
     Error cases and unknown/unsupported signature algorithms are mapped
     to ‘GNUTLS_SIGN_UNKNOWN’ .

     *Returns:* a ‘gnutls_sign_algorithm_t’ value

gnutls_x509_crl_get_signature_oid
---------------------------------

 -- Function: int gnutls_x509_crl_get_signature_oid (gnutls_x509_crl_t
          CRL, char * OID, size_t * OID_SIZE)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     OID: a pointer to a buffer to hold the OID (may be null)

     OID_SIZE: initially holds the size of ‘oid’

     This function will return the OID of the signature algorithm that
     has been used to sign this CRL. This is function is useful in the
     case ‘gnutls_x509_crl_get_signature_algorithm()’ returned
     ‘GNUTLS_SIGN_UNKNOWN’ .

     *Returns:* zero or a negative error code on error.

     *Since:* 3.5.0

gnutls_x509_crl_get_this_update
-------------------------------

 -- Function: time_t gnutls_x509_crl_get_this_update (gnutls_x509_crl_t
          CRL)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     This function will return the time this CRL was issued.

     *Returns:* when the CRL was issued, or (time_t)-1 on error.

gnutls_x509_crl_get_version
---------------------------

 -- Function: int gnutls_x509_crl_get_version (gnutls_x509_crl_t CRL)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     This function will return the version of the specified CRL.

     *Returns:* The version number, or a negative error code on error.

gnutls_x509_crl_import
----------------------

 -- Function: int gnutls_x509_crl_import (gnutls_x509_crl_t CRL, const
          gnutls_datum_t * DATA, gnutls_x509_crt_fmt_t FORMAT)
     CRL: The data to store the parsed CRL.

     DATA: The DER or PEM encoded CRL.

     FORMAT: One of DER or PEM

     This function will convert the given DER or PEM encoded CRL to the
     native ‘gnutls_x509_crl_t’ format.  The output will be stored in
     'crl'.

     If the CRL is PEM encoded it should have a header of "X509 CRL".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_init
--------------------

 -- Function: int gnutls_x509_crl_init (gnutls_x509_crl_t * CRL)
     CRL: A pointer to the type to be initialized

     This function will initialize a CRL structure.  CRL stands for
     Certificate Revocation List.  A revocation list usually contains
     lists of certificate serial numbers that have been revoked by an
     Authority.  The revocation lists are always signed with the
     authority's private key.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_iter_crt_serial
-------------------------------

 -- Function: int gnutls_x509_crl_iter_crt_serial (gnutls_x509_crl_t
          CRL, gnutls_x509_crl_iter_t * ITER, unsigned char * SERIAL,
          size_t * SERIAL_SIZE, time_t * T)
     CRL: should contain a ‘gnutls_x509_crl_t’ type

     ITER: A pointer to an iterator (initially the iterator should be
     ‘NULL’ )

     SERIAL: where the serial number will be copied

     SERIAL_SIZE: initially holds the size of serial

     T: if non null, will hold the time this certificate was revoked

     This function performs the same as
     ‘gnutls_x509_crl_get_crt_serial()’ , but reads sequentially and
     keeps state in the iterator between calls.  That allows it to
     provide better performance in sequences with many elements
     (50000+).

     When past the last element is accessed
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned and the
     iterator is reset.

     After use, the iterator must be deinitialized using
     ‘gnutls_x509_crl_iter_deinit()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_iter_deinit
---------------------------

 -- Function: void gnutls_x509_crl_iter_deinit (gnutls_x509_crl_iter_t
          ITER)
     ITER: The iterator to be deinitialized

     This function will deinitialize an iterator type.

gnutls_x509_crl_list_import
---------------------------

 -- Function: int gnutls_x509_crl_list_import (gnutls_x509_crl_t * CRLS,
          unsigned int * CRL_MAX, const gnutls_datum_t * DATA,
          gnutls_x509_crt_fmt_t FORMAT, unsigned int FLAGS)
     CRLS: Indicates where the parsed CRLs will be copied to.  Must not
     be initialized.

     CRL_MAX: Initially must hold the maximum number of crls.  It will
     be updated with the number of crls available.

     DATA: The CRL data

     FORMAT: One of DER or PEM.

     FLAGS: must be (0) or an OR'd sequence of
     gnutls_certificate_import_flags.

     This function will convert the given CRL list to the native
     gnutls_x509_crl_t format.  The output will be stored in ‘crls’ .
     They will be automatically initialized.

     If the Certificate is PEM encoded it should have a header of "X509
     CRL".

     *Returns:* the number of certificates read or a negative error
     value.

     *Since:* 3.0

gnutls_x509_crl_list_import2
----------------------------

 -- Function: int gnutls_x509_crl_list_import2 (gnutls_x509_crl_t **
          CRLS, unsigned int * SIZE, const gnutls_datum_t * DATA,
          gnutls_x509_crt_fmt_t FORMAT, unsigned int FLAGS)
     CRLS: Will contain the parsed crl list.

     SIZE: It will contain the size of the list.

     DATA: The CRL data.

     FORMAT: One of DER or PEM.

     FLAGS: must be (0) or an OR'd sequence of
     gnutls_certificate_import_flags.

     This function will convert the given CRL list to the native
     gnutls_x509_crl_t format.  The output will be stored in ‘crls’ .
     They will be automatically initialized.

     If the Certificate is PEM encoded it should have a header of "X509
     CRL".

     *Returns:* the number of certificates read or a negative error
     value.

     *Since:* 3.0

gnutls_x509_crl_print
---------------------

 -- Function: int gnutls_x509_crl_print (gnutls_x509_crl_t CRL,
          gnutls_certificate_print_formats_t FORMAT, gnutls_datum_t *
          OUT)
     CRL: The data to be printed

     FORMAT: Indicate the format to use

     OUT: Newly allocated datum with null terminated string.

     This function will pretty print a X.509 certificate revocation
     list, suitable for display to a human.

     The output ‘out’ needs to be deallocated using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_set_authority_key_id
------------------------------------

 -- Function: int gnutls_x509_crl_set_authority_key_id
          (gnutls_x509_crl_t CRL, const void * ID, size_t ID_SIZE)
     CRL: a CRL of type ‘gnutls_x509_crl_t’

     ID: The key ID

     ID_SIZE: Holds the size of the serial field.

     This function will set the CRL's authority key ID extension.  Only
     the keyIdentifier field can be set with this function.  This may be
     used by an authority that holds multiple private keys, to
     distinguish the used key.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.8.0

gnutls_x509_crl_set_crt
-----------------------

 -- Function: int gnutls_x509_crl_set_crt (gnutls_x509_crl_t CRL,
          gnutls_x509_crt_t CRT, time_t REVOCATION_TIME)
     CRL: should contain a gnutls_x509_crl_t type

     CRT: a certificate of type ‘gnutls_x509_crt_t’ with the revoked
     certificate

     REVOCATION_TIME: The time this certificate was revoked

     This function will set a revoked certificate's serial number to the
     CRL.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_set_crt_serial
------------------------------

 -- Function: int gnutls_x509_crl_set_crt_serial (gnutls_x509_crl_t CRL,
          const void * SERIAL, size_t SERIAL_SIZE, time_t
          REVOCATION_TIME)
     CRL: should contain a gnutls_x509_crl_t type

     SERIAL: The revoked certificate's serial number

     SERIAL_SIZE: Holds the size of the serial field.

     REVOCATION_TIME: The time this certificate was revoked

     This function will set a revoked certificate's serial number to the
     CRL.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_set_next_update
-------------------------------

 -- Function: int gnutls_x509_crl_set_next_update (gnutls_x509_crl_t
          CRL, time_t EXP_TIME)
     CRL: should contain a gnutls_x509_crl_t type

     EXP_TIME: The actual time

     This function will set the time this CRL will be updated.  This is
     an optional value to be set on a CRL and this call can be omitted
     when generating a CRL.

     Prior to GnuTLS 3.5.7, setting a nextUpdate field was required in
     order to generate a CRL.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_set_number
--------------------------

 -- Function: int gnutls_x509_crl_set_number (gnutls_x509_crl_t CRL,
          const void * NR, size_t NR_SIZE)
     CRL: a CRL of type ‘gnutls_x509_crl_t’

     NR: The CRL number

     NR_SIZE: Holds the size of the nr field.

     This function will set the CRL's number extension.  This is to be
     used as a unique and monotonic number assigned to the CRL by the
     authority.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.8.0

gnutls_x509_crl_set_this_update
-------------------------------

 -- Function: int gnutls_x509_crl_set_this_update (gnutls_x509_crl_t
          CRL, time_t ACT_TIME)
     CRL: should contain a gnutls_x509_crl_t type

     ACT_TIME: The actual time

     This function will set the time this CRL was issued.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_set_version
---------------------------

 -- Function: int gnutls_x509_crl_set_version (gnutls_x509_crl_t CRL,
          unsigned int VERSION)
     CRL: should contain a gnutls_x509_crl_t type

     VERSION: holds the version number.  For CRLv1 crls must be 1.

     This function will set the version of the CRL. This must be one for
     CRL version 1, and so on.  The CRLs generated by gnutls should have
     a version number of 2.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_sign
--------------------

 -- Function: int gnutls_x509_crl_sign (gnutls_x509_crl_t CRL,
          gnutls_x509_crt_t ISSUER, gnutls_x509_privkey_t ISSUER_KEY)
     CRL: should contain a gnutls_x509_crl_t type

     ISSUER: is the certificate of the certificate issuer

     ISSUER_KEY: holds the issuer's private key

     This function is the same a ‘gnutls_x509_crl_sign2()’ with no
     flags, and an appropriate hash algorithm.  The hash algorithm used
     may vary between versions of GnuTLS, and it is tied to the security
     level of the issuer's public key.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_sign2
---------------------

 -- Function: int gnutls_x509_crl_sign2 (gnutls_x509_crl_t CRL,
          gnutls_x509_crt_t ISSUER, gnutls_x509_privkey_t ISSUER_KEY,
          gnutls_digest_algorithm_t DIG, unsigned int FLAGS)
     CRL: should contain a gnutls_x509_crl_t type

     ISSUER: is the certificate of the certificate issuer

     ISSUER_KEY: holds the issuer's private key

     DIG: The message digest to use.  GNUTLS_DIG_SHA256 is the safe
     choice unless you know what you're doing.

     FLAGS: must be 0

     This function will sign the CRL with the issuer's private key, and
     will copy the issuer's information into the CRL.

     This must be the last step in a certificate CRL since all the
     previously set parameters are now signed.

     A known limitation of this function is, that a newly-signed CRL
     will not be fully functional (e.g., for signature verification),
     until it is exported an re-imported.

     After GnuTLS 3.6.1 the value of ‘dig’ may be ‘GNUTLS_DIG_UNKNOWN’ ,
     and in that case, a suitable but reasonable for the key algorithm
     will be selected.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crl_verify
----------------------

 -- Function: int gnutls_x509_crl_verify (gnutls_x509_crl_t CRL, const
          gnutls_x509_crt_t * TRUSTED_CAS, unsigned TCAS_SIZE, unsigned
          int FLAGS, unsigned int * VERIFY)
     CRL: is the crl to be verified

     TRUSTED_CAS: is a certificate list that is considered to be trusted
     one

     TCAS_SIZE: holds the number of CA certificates in CA_list

     FLAGS: Flags that may be used to change the verification algorithm.
     Use OR of the gnutls_certificate_verify_flags enumerations.

     VERIFY: will hold the crl verification output.

     This function will try to verify the given crl and return its
     verification status.  See ‘gnutls_x509_crt_list_verify()’ for a
     detailed description of return values.  Note that since GnuTLS
     3.1.4 this function includes the time checks.

     Note that value in ‘verify’ is set only when the return value of
     this function is success (i.e, failure to trust a CRL a certificate
     does not imply a negative return value).

     Before GnuTLS 3.5.7 this function would return zero or a positive
     number on success.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0), otherwise a negative
     error value.

gnutls_x509_crq_deinit
----------------------

 -- Function: void gnutls_x509_crq_deinit (gnutls_x509_crq_t CRQ)
     CRQ: the type to be deinitialized

     This function will deinitialize a PKCS‘10’ certificate request
     structure.

gnutls_x509_crq_export
----------------------

 -- Function: int gnutls_x509_crq_export (gnutls_x509_crq_t CRQ,
          gnutls_x509_crt_fmt_t FORMAT, void * OUTPUT_DATA, size_t *
          OUTPUT_DATA_SIZE)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     FORMAT: the format of output params.  One of PEM or DER.

     OUTPUT_DATA: will contain a certificate request PEM or DER encoded

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will export the certificate request to a PEM or DER
     encoded PKCS10 structure.

     If the buffer provided is not long enough to hold the output, then
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ will be returned and *
     ‘output_data_size’ will be updated.

     If the structure is PEM encoded, it will have a header of "BEGIN
     NEW CERTIFICATE REQUEST".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_export2
-----------------------

 -- Function: int gnutls_x509_crq_export2 (gnutls_x509_crq_t CRQ,
          gnutls_x509_crt_fmt_t FORMAT, gnutls_datum_t * OUT)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     FORMAT: the format of output params.  One of PEM or DER.

     OUT: will contain a certificate request PEM or DER encoded

     This function will export the certificate request to a PEM or DER
     encoded PKCS10 structure.

     The output buffer is allocated using ‘gnutls_malloc()’ .

     If the structure is PEM encoded, it will have a header of "BEGIN
     NEW CERTIFICATE REQUEST".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     Since 3.1.3

gnutls_x509_crq_get_attribute_by_oid
------------------------------------

 -- Function: int gnutls_x509_crq_get_attribute_by_oid
          (gnutls_x509_crq_t CRQ, const char * OID, unsigned INDX, void
          * BUF, size_t * BUF_SIZE)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     OID: holds an Object Identifier in null-terminated string

     INDX: In case multiple same OIDs exist in the attribute list, this
     specifies which to get, use (0) to get the first one

     BUF: a pointer to a structure to hold the attribute data (may be
     ‘NULL’ )

     BUF_SIZE: initially holds the size of ‘buf’

     This function will return the attribute in the certificate request
     specified by the given Object ID. The attribute will be DER
     encoded.

     Attributes in a certificate request is an optional set of data
     appended to the request.  Their interpretation depends on the CA
     policy.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_get_attribute_data
----------------------------------

 -- Function: int gnutls_x509_crq_get_attribute_data (gnutls_x509_crq_t
          CRQ, unsigned INDX, void * DATA, size_t * SIZEOF_DATA)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     INDX: Specifies which attribute number to get.  Use (0) to get the
     first one.

     DATA: a pointer to a structure to hold the data (may be null)

     SIZEOF_DATA: initially holds the size of ‘oid’

     This function will return the requested attribute data in the
     certificate request.  The attribute data will be stored as a string
     in the provided buffer.

     Use ‘gnutls_x509_crq_get_attribute_info()’ to extract the OID. Use
     ‘gnutls_x509_crq_get_attribute_by_oid()’ instead, if you want to
     get data indexed by the attribute OID rather than sequence.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code in case of an error.  If your have
     reached the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Since:* 2.8.0

gnutls_x509_crq_get_attribute_info
----------------------------------

 -- Function: int gnutls_x509_crq_get_attribute_info (gnutls_x509_crq_t
          CRQ, unsigned INDX, void * OID, size_t * SIZEOF_OID)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     INDX: Specifies which attribute number to get.  Use (0) to get the
     first one.

     OID: a pointer to a structure to hold the OID

     SIZEOF_OID: initially holds the maximum size of ‘oid’ , on return
     holds actual size of ‘oid’ .

     This function will return the requested attribute OID in the
     certificate, and the critical flag for it.  The attribute OID will
     be stored as a string in the provided buffer.  Use
     ‘gnutls_x509_crq_get_attribute_data()’ to extract the data.

     If the buffer provided is not long enough to hold the output, then
     * ‘sizeof_oid’ is updated and ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ will
     be returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code in case of an error.  If your have
     reached the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Since:* 2.8.0

gnutls_x509_crq_get_basic_constraints
-------------------------------------

 -- Function: int gnutls_x509_crq_get_basic_constraints
          (gnutls_x509_crq_t CRQ, unsigned int * CRITICAL, unsigned int
          * CA, int * PATHLEN)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     CRITICAL: will be non-zero if the extension is marked as critical

     CA: pointer to output integer indicating CA status, may be NULL,
     value is 1 if the certificate CA flag is set, 0 otherwise.

     PATHLEN: pointer to output integer indicating path length (may be
     NULL), non-negative error codes indicate a present
     pathLenConstraint field and the actual value, -1 indicate that the
     field is absent.

     This function will read the certificate's basic constraints, and
     return the certificates CA status.  It reads the basicConstraints
     X.509 extension (2.5.29.19).

     *Returns:* If the certificate is a CA a positive value will be
     returned, or (0) if the certificate does not have CA flag set.  A
     negative error code may be returned in case of errors.  If the
     certificate does not contain the basicConstraints extension
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Since:* 2.8.0

gnutls_x509_crq_get_challenge_password
--------------------------------------

 -- Function: int gnutls_x509_crq_get_challenge_password
          (gnutls_x509_crq_t CRQ, char * PASS, size_t * PASS_SIZE)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     PASS: will hold a (0)-terminated password string

     PASS_SIZE: Initially holds the size of ‘pass’ .

     This function will return the challenge password in the request.
     The challenge password is intended to be used for requesting a
     revocation of the certificate.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_get_dn
----------------------

 -- Function: int gnutls_x509_crq_get_dn (gnutls_x509_crq_t CRQ, char *
          BUF, size_t * BUF_SIZE)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     BUF: a pointer to a structure to hold the name (may be ‘NULL’ )

     BUF_SIZE: initially holds the size of ‘buf’

     This function will copy the name of the Certificate request subject
     to the provided buffer.  The name will be in the form
     "C=xxxx,O=yyyy,CN=zzzz" as described in RFC 2253.  The output
     string ‘buf’ will be ASCII or UTF-8 encoded, depending on the
     certificate data.

     This function does not output a fully RFC4514 compliant string, if
     that is required see ‘gnutls_x509_crq_get_dn3()’ .

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the * ‘buf_size’ will be updated
     with the required size.  On success 0 is returned.

gnutls_x509_crq_get_dn2
-----------------------

 -- Function: int gnutls_x509_crq_get_dn2 (gnutls_x509_crq_t CRQ,
          gnutls_datum_t * DN)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     DN: a pointer to a structure to hold the name; must be freed using
     ‘gnutls_free()’

     This function will allocate buffer and copy the name of the
     Certificate request.  The name will be in the form
     "C=xxxx,O=yyyy,CN=zzzz" as described in RFC4514.  The output string
     will be ASCII or UTF-8 encoded, depending on the certificate data.

     This function does not output a fully RFC4514 compliant string, if
     that is required see ‘gnutls_x509_crq_get_dn3()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  and a negative error code on
     error.

     *Since:* 3.1.10

gnutls_x509_crq_get_dn3
-----------------------

 -- Function: int gnutls_x509_crq_get_dn3 (gnutls_x509_crq_t CRQ,
          gnutls_datum_t * DN, unsigned FLAGS)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     DN: a pointer to a structure to hold the name; must be freed using
     ‘gnutls_free()’

     FLAGS: zero or ‘GNUTLS_X509_DN_FLAG_COMPAT’

     This function will allocate buffer and copy the name of the
     Certificate request.  The name will be in the form
     "C=xxxx,O=yyyy,CN=zzzz" as described in RFC4514.  The output string
     will be ASCII or UTF-8 encoded, depending on the certificate data.

     When the flag ‘GNUTLS_X509_DN_FLAG_COMPAT’ is specified, the output
     format will match the format output by previous to 3.5.6 versions
     of GnuTLS which was not not fully RFC4514-compliant.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  and a negative error code on
     error.

     *Since:* 3.5.7

gnutls_x509_crq_get_dn_by_oid
-----------------------------

 -- Function: int gnutls_x509_crq_get_dn_by_oid (gnutls_x509_crq_t CRQ,
          const char * OID, unsigned INDX, unsigned int RAW_FLAG, void *
          BUF, size_t * BUF_SIZE)
     CRQ: should contain a gnutls_x509_crq_t type

     OID: holds an Object Identifier in a null terminated string

     INDX: In case multiple same OIDs exist in the RDN, this specifies
     which to get.  Use (0) to get the first one.

     RAW_FLAG: If non-zero returns the raw DER data of the DN part.

     BUF: a pointer to a structure to hold the name (may be ‘NULL’ )

     BUF_SIZE: initially holds the size of ‘buf’

     This function will extract the part of the name of the Certificate
     request subject, specified by the given OID. The output will be
     encoded as described in RFC2253.  The output string will be ASCII
     or UTF-8 encoded, depending on the certificate data.

     Some helper macros with popular OIDs can be found in gnutls/x509.h
     If raw flag is (0), this function will only return known OIDs as
     text.  Other OIDs will be DER encoded, as described in RFC2253 - in
     hex format with a '\#' prefix.  You can check about known OIDs
     using ‘gnutls_x509_dn_oid_known()’ .

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the * ‘buf_size’ will be updated
     with the required size.  On success 0 is returned.

gnutls_x509_crq_get_dn_oid
--------------------------

 -- Function: int gnutls_x509_crq_get_dn_oid (gnutls_x509_crq_t CRQ,
          unsigned INDX, void * OID, size_t * SIZEOF_OID)
     CRQ: should contain a gnutls_x509_crq_t type

     INDX: Specifies which DN OID to get.  Use (0) to get the first one.

     OID: a pointer to a structure to hold the name (may be ‘NULL’ )

     SIZEOF_OID: initially holds the size of ‘oid’

     This function will extract the requested OID of the name of the
     certificate request subject, specified by the given index.

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the * ‘sizeof_oid’ will be
     updated with the required size.  On success 0 is returned.

gnutls_x509_crq_get_extension_by_oid
------------------------------------

 -- Function: int gnutls_x509_crq_get_extension_by_oid
          (gnutls_x509_crq_t CRQ, const char * OID, unsigned INDX, void
          * BUF, size_t * BUF_SIZE, unsigned int * CRITICAL)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     OID: holds an Object Identifier in a null terminated string

     INDX: In case multiple same OIDs exist in the extensions, this
     specifies which to get.  Use (0) to get the first one.

     BUF: a pointer to a structure to hold the name (may be null)

     BUF_SIZE: initially holds the size of ‘buf’

     CRITICAL: will be non-zero if the extension is marked as critical

     This function will return the extension specified by the OID in the
     certificate.  The extensions will be returned as binary data DER
     encoded, in the provided buffer.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code in case of an error.  If the
     certificate does not contain the specified extension
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Since:* 2.8.0

gnutls_x509_crq_get_extension_by_oid2
-------------------------------------

 -- Function: int gnutls_x509_crq_get_extension_by_oid2
          (gnutls_x509_crq_t CRQ, const char * OID, unsigned INDX,
          gnutls_datum_t * OUTPUT, unsigned int * CRITICAL)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     OID: holds an Object Identifier in a null terminated string

     INDX: In case multiple same OIDs exist in the extensions, this
     specifies which to get.  Use (0) to get the first one.

     OUTPUT: will hold the allocated extension data

     CRITICAL: will be non-zero if the extension is marked as critical

     This function will return the extension specified by the OID in the
     certificate.  The extensions will be returned as binary data DER
     encoded, in the provided buffer.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code in case of an error.  If the
     certificate does not contain the specified extension
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Since:* 3.3.8

gnutls_x509_crq_get_extension_data
----------------------------------

 -- Function: int gnutls_x509_crq_get_extension_data (gnutls_x509_crq_t
          CRQ, unsigned INDX, void * DATA, size_t * SIZEOF_DATA)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     INDX: Specifies which extension number to get.  Use (0) to get the
     first one.

     DATA: a pointer to a structure to hold the data (may be null)

     SIZEOF_DATA: initially holds the size of ‘oid’

     This function will return the requested extension data in the
     certificate.  The extension data will be stored as a string in the
     provided buffer.

     Use ‘gnutls_x509_crq_get_extension_info()’ to extract the OID and
     critical flag.  Use ‘gnutls_x509_crq_get_extension_by_oid()’
     instead, if you want to get data indexed by the extension OID
     rather than sequence.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code in case of an error.  If your have
     reached the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Since:* 2.8.0

gnutls_x509_crq_get_extension_data2
-----------------------------------

 -- Function: int gnutls_x509_crq_get_extension_data2 (gnutls_x509_crq_t
          CRQ, unsigned INDX, gnutls_datum_t * DATA)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     INDX: Specifies which extension OID to read.  Use (0) to get the
     first one.

     DATA: will contain the extension DER-encoded data

     This function will return the requested extension data in the
     certificate request.  The extension data will be allocated using
     ‘gnutls_malloc()’ .

     Use ‘gnutls_x509_crq_get_extension_info()’ to extract the OID.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  If you have reached
     the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Since:* 3.3.0

gnutls_x509_crq_get_extension_info
----------------------------------

 -- Function: int gnutls_x509_crq_get_extension_info (gnutls_x509_crq_t
          CRQ, unsigned INDX, void * OID, size_t * SIZEOF_OID, unsigned
          int * CRITICAL)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     INDX: Specifies which extension number to get.  Use (0) to get the
     first one.

     OID: a pointer to store the OID

     SIZEOF_OID: initially holds the maximum size of ‘oid’ , on return
     holds actual size of ‘oid’ .

     CRITICAL: output variable with critical flag, may be NULL.

     This function will return the requested extension OID in the
     certificate, and the critical flag for it.  The extension OID will
     be stored as a string in the provided buffer.  Use
     ‘gnutls_x509_crq_get_extension_data()’ to extract the data.

     If the buffer provided is not long enough to hold the output, then
     * ‘sizeof_oid’ is updated and ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ will
     be returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code in case of an error.  If your have
     reached the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Since:* 2.8.0

gnutls_x509_crq_get_key_id
--------------------------

 -- Function: int gnutls_x509_crq_get_key_id (gnutls_x509_crq_t CRQ,
          unsigned int FLAGS, unsigned char * OUTPUT_DATA, size_t *
          OUTPUT_DATA_SIZE)
     CRQ: a certificate of type ‘gnutls_x509_crq_t’

     FLAGS: should be one of the flags from ‘gnutls_keyid_flags_t’

     OUTPUT_DATA: will contain the key ID

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will return a unique ID that depends on the public
     key parameters.  This ID can be used in checking whether a
     certificate corresponds to the given private key.

     If the buffer provided is not long enough to hold the output, then
     * ‘output_data_size’ is updated and GNUTLS_E_SHORT_MEMORY_BUFFER
     will be returned.  The output will normally be a SHA-1 hash output,
     which is 20 bytes.

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

     *Since:* 2.8.0

gnutls_x509_crq_get_key_purpose_oid
-----------------------------------

 -- Function: int gnutls_x509_crq_get_key_purpose_oid (gnutls_x509_crq_t
          CRQ, unsigned INDX, void * OID, size_t * SIZEOF_OID, unsigned
          int * CRITICAL)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     INDX: This specifies which OID to return, use (0) to get the first
     one

     OID: a pointer to store the OID (may be ‘NULL’ )

     SIZEOF_OID: initially holds the size of ‘oid’

     CRITICAL: output variable with critical flag, may be ‘NULL’ .

     This function will extract the key purpose OIDs of the Certificate
     specified by the given index.  These are stored in the Extended Key
     Usage extension (*********).  See the GNUTLS_KP_* definitions for
     human readable names.

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the * ‘sizeof_oid’ will be
     updated with the required size.  On success 0 is returned.

     *Since:* 2.8.0

gnutls_x509_crq_get_key_rsa_raw
-------------------------------

 -- Function: int gnutls_x509_crq_get_key_rsa_raw (gnutls_x509_crq_t
          CRQ, gnutls_datum_t * M, gnutls_datum_t * E)
     CRQ: Holds the certificate

     M: will hold the modulus

     E: will hold the public exponent

     This function will export the RSA public key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.8.0

gnutls_x509_crq_get_key_usage
-----------------------------

 -- Function: int gnutls_x509_crq_get_key_usage (gnutls_x509_crq_t CRQ,
          unsigned int * KEY_USAGE, unsigned int * CRITICAL)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     KEY_USAGE: where the key usage bits will be stored

     CRITICAL: will be non-zero if the extension is marked as critical

     This function will return certificate's key usage, by reading the
     keyUsage X.509 extension (*********).  The key usage value will
     ORed values of the: ‘GNUTLS_KEY_DIGITAL_SIGNATURE’ ,
     ‘GNUTLS_KEY_NON_REPUDIATION’ , ‘GNUTLS_KEY_KEY_ENCIPHERMENT’ ,
     ‘GNUTLS_KEY_DATA_ENCIPHERMENT’ , ‘GNUTLS_KEY_KEY_AGREEMENT’ ,
     ‘GNUTLS_KEY_KEY_CERT_SIGN’ , ‘GNUTLS_KEY_CRL_SIGN’ ,
     ‘GNUTLS_KEY_ENCIPHER_ONLY’ , ‘GNUTLS_KEY_DECIPHER_ONLY’ .

     *Returns:* the certificate key usage, or a negative error code in
     case of parsing error.  If the certificate does not contain the
     keyUsage extension ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be
     returned.

     *Since:* 2.8.0

gnutls_x509_crq_get_pk_algorithm
--------------------------------

 -- Function: int gnutls_x509_crq_get_pk_algorithm (gnutls_x509_crq_t
          CRQ, unsigned int * BITS)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     BITS: if bits is non-‘NULL’ it will hold the size of the
     parameters' in bits

     This function will return the public key algorithm of a PKCS‘10’
     certificate request.

     If bits is non-‘NULL’ , it should have enough size to hold the
     parameters size in bits.  For RSA the bits returned is the modulus.
     For DSA the bits returned are of the public exponent.

     *Returns:* a member of the ‘gnutls_pk_algorithm_t’ enumeration on
     success, or a negative error code on error.

gnutls_x509_crq_get_pk_oid
--------------------------

 -- Function: int gnutls_x509_crq_get_pk_oid (gnutls_x509_crq_t CRQ,
          char * OID, size_t * OID_SIZE)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     OID: a pointer to a buffer to hold the OID (may be null)

     OID_SIZE: initially holds the size of ‘oid’

     This function will return the OID of the public key algorithm on
     that certificate request.  This function is useful in the case
     ‘gnutls_x509_crq_get_pk_algorithm()’ returned ‘GNUTLS_PK_UNKNOWN’ .

     *Returns:* zero or a negative error code on error.

     *Since:* 3.5.0

gnutls_x509_crq_get_private_key_usage_period
--------------------------------------------

 -- Function: int gnutls_x509_crq_get_private_key_usage_period
          (gnutls_x509_crq_t CRQ, time_t * ACTIVATION, time_t *
          EXPIRATION, unsigned int * CRITICAL)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     ACTIVATION: The activation time

     EXPIRATION: The expiration time

     CRITICAL: the extension status

     This function will return the expiration and activation times of
     the private key of the certificate.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the extension is not
     present, otherwise a negative error value.

gnutls_x509_crq_get_signature_algorithm
---------------------------------------

 -- Function: int gnutls_x509_crq_get_signature_algorithm
          (gnutls_x509_crq_t CRQ)
     CRQ: should contain a ‘gnutls_x509_cr_t’ type

     This function will return a value of the ‘gnutls_sign_algorithm_t’
     enumeration that is the signature algorithm that has been used to
     sign this certificate request.

     Since 3.6.0 this function never returns a negative error code.
     Error cases and unknown/unsupported signature algorithms are mapped
     to ‘GNUTLS_SIGN_UNKNOWN’ .

     *Returns:* a ‘gnutls_sign_algorithm_t’ value

     *Since:* 3.4.0

gnutls_x509_crq_get_signature_oid
---------------------------------

 -- Function: int gnutls_x509_crq_get_signature_oid (gnutls_x509_crq_t
          CRQ, char * OID, size_t * OID_SIZE)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     OID: a pointer to a buffer to hold the OID (may be null)

     OID_SIZE: initially holds the size of ‘oid’

     This function will return the OID of the signature algorithm that
     has been used to sign this certificate request.  This function is
     useful in the case ‘gnutls_x509_crq_get_signature_algorithm()’
     returned ‘GNUTLS_SIGN_UNKNOWN’ .

     *Returns:* zero or a negative error code on error.

     *Since:* 3.5.0

gnutls_x509_crq_get_spki
------------------------

 -- Function: int gnutls_x509_crq_get_spki (gnutls_x509_crq_t CRQ,
          gnutls_x509_spki_t SPKI, unsigned int FLAGS)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     SPKI: a SubjectPublicKeyInfo structure of type ‘gnutls_x509_spki_t’

     FLAGS: must be zero

     This function will return the public key information of a PKCS‘10’
     certificate request.  The provided ‘spki’ must be initialized.

     *Returns:* Zero on success, or a negative error code on error.

gnutls_x509_crq_get_subject_alt_name
------------------------------------

 -- Function: int gnutls_x509_crq_get_subject_alt_name
          (gnutls_x509_crq_t CRQ, unsigned int SEQ, void * RET, size_t *
          RET_SIZE, unsigned int * RET_TYPE, unsigned int * CRITICAL)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     SEQ: specifies the sequence number of the alt name, 0 for the first
     one, 1 for the second etc.

     RET: is the place where the alternative name will be copied to

     RET_SIZE: holds the size of ret.

     RET_TYPE: holds the ‘gnutls_x509_subject_alt_name_t’ name type

     CRITICAL: will be non-zero if the extension is marked as critical
     (may be null)

     This function will return the alternative names, contained in the
     given certificate.  It is the same as
     ‘gnutls_x509_crq_get_subject_alt_name()’ except for the fact that
     it will return the type of the alternative name in ‘ret_type’ even
     if the function fails for some reason (i.e.  the buffer provided is
     not enough).

     *Returns:* the alternative subject name type on success, one of the
     enumerated ‘gnutls_x509_subject_alt_name_t’ .  It will return
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if ‘ret_size’ is not large enough to
     hold the value.  In that case ‘ret_size’ will be updated with the
     required size.  If the certificate request does not have an
     Alternative name with the specified sequence number then
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.

     *Since:* 2.8.0

gnutls_x509_crq_get_subject_alt_othername_oid
---------------------------------------------

 -- Function: int gnutls_x509_crq_get_subject_alt_othername_oid
          (gnutls_x509_crq_t CRQ, unsigned int SEQ, void * RET, size_t *
          RET_SIZE)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     SEQ: specifies the sequence number of the alt name (0 for the first
     one, 1 for the second etc.)

     RET: is the place where the otherName OID will be copied to

     RET_SIZE: holds the size of ret.

     This function will extract the type OID of an otherName Subject
     Alternative Name, contained in the given certificate, and return
     the type as an enumerated element.

     This function is only useful if
     ‘gnutls_x509_crq_get_subject_alt_name()’ returned
     ‘GNUTLS_SAN_OTHERNAME’ .

     *Returns:* the alternative subject name type on success, one of the
     enumerated gnutls_x509_subject_alt_name_t.  For supported OIDs, it
     will return one of the virtual (GNUTLS_SAN_OTHERNAME_*) types, e.g.
     ‘GNUTLS_SAN_OTHERNAME_XMPP’ , and ‘GNUTLS_SAN_OTHERNAME’ for
     unknown OIDs.  It will return ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if
     ‘ret_size’ is not large enough to hold the value.  In that case
     ‘ret_size’ will be updated with the required size.  If the
     certificate does not have an Alternative name with the specified
     sequence number and with the otherName type then
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.

     *Since:* 2.8.0

gnutls_x509_crq_get_tlsfeatures
-------------------------------

 -- Function: int gnutls_x509_crq_get_tlsfeatures (gnutls_x509_crq_t
          CRQ, gnutls_x509_tlsfeatures_t FEATURES, unsigned int FLAGS,
          unsigned int * CRITICAL)
     CRQ: An X.509 certificate request

     FEATURES: If the function succeeds, the features will be stored in
     this variable.

     FLAGS: zero or ‘GNUTLS_EXT_FLAG_APPEND’

     CRITICAL: the extension status

     This function will get the X.509 TLS features extension structure
     from the certificate request.  The returned structure needs to be
     freed using ‘gnutls_x509_tlsfeatures_deinit()’ .

     When the ‘flags’ is set to ‘GNUTLS_EXT_FLAG_APPEND’ , then if the
     ‘features’ structure is empty this function will behave identically
     as if the flag was not set.  Otherwise if there are elements in the
     ‘features’ structure then they will be merged with.

     Note that ‘features’ must be initialized prior to calling this
     function.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.1

gnutls_x509_crq_get_version
---------------------------

 -- Function: int gnutls_x509_crq_get_version (gnutls_x509_crq_t CRQ)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     This function will return the version of the specified Certificate
     request.

     *Returns:* version of certificate request, or a negative error code
     on error.

gnutls_x509_crq_import
----------------------

 -- Function: int gnutls_x509_crq_import (gnutls_x509_crq_t CRQ, const
          gnutls_datum_t * DATA, gnutls_x509_crt_fmt_t FORMAT)
     CRQ: The data to store the parsed certificate request.

     DATA: The DER or PEM encoded certificate.

     FORMAT: One of DER or PEM

     This function will convert the given DER or PEM encoded certificate
     request to a ‘gnutls_x509_crq_t’ type.  The output will be stored
     in ‘crq’ .

     If the Certificate is PEM encoded it should have a header of "NEW
     CERTIFICATE REQUEST".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_init
--------------------

 -- Function: int gnutls_x509_crq_init (gnutls_x509_crq_t * CRQ)
     CRQ: A pointer to the type to be initialized

     This function will initialize a PKCS‘10’ certificate request
     structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_print
---------------------

 -- Function: int gnutls_x509_crq_print (gnutls_x509_crq_t CRQ,
          gnutls_certificate_print_formats_t FORMAT, gnutls_datum_t *
          OUT)
     CRQ: The data to be printed

     FORMAT: Indicate the format to use

     OUT: Newly allocated datum with null terminated string.

     This function will pretty print a certificate request, suitable for
     display to a human.

     The output ‘out’ needs to be deallocated using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.8.0

gnutls_x509_crq_set_attribute_by_oid
------------------------------------

 -- Function: int gnutls_x509_crq_set_attribute_by_oid
          (gnutls_x509_crq_t CRQ, const char * OID, void * BUF, size_t
          BUF_SIZE)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     OID: holds an Object Identifier in a null-terminated string

     BUF: a pointer to a structure that holds the attribute data

     BUF_SIZE: holds the size of ‘buf’

     This function will set the attribute in the certificate request
     specified by the given Object ID. The provided attribute must be be
     DER encoded.

     Attributes in a certificate request is an optional set of data
     appended to the request.  Their interpretation depends on the CA
     policy.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_set_basic_constraints
-------------------------------------

 -- Function: int gnutls_x509_crq_set_basic_constraints
          (gnutls_x509_crq_t CRQ, unsigned int CA, int
          PATHLENCONSTRAINT)
     CRQ: a certificate request of type ‘gnutls_x509_crq_t’

     CA: true(1) or false(0) depending on the Certificate authority
     status.

     PATHLENCONSTRAINT: non-negative error codes indicate maximum length
     of path, and negative error codes indicate that the
     pathLenConstraints field should not be present.

     This function will set the basicConstraints certificate extension.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.8.0

gnutls_x509_crq_set_challenge_password
--------------------------------------

 -- Function: int gnutls_x509_crq_set_challenge_password
          (gnutls_x509_crq_t CRQ, const char * PASS)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     PASS: holds a (0)-terminated password

     This function will set a challenge password to be used when
     revoking the request.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_set_dn
----------------------

 -- Function: int gnutls_x509_crq_set_dn (gnutls_x509_crq_t CRQ, const
          char * DN, const char ** ERR)
     CRQ: a certificate of type ‘gnutls_x509_crq_t’

     DN: a comma separated DN string (RFC4514)

     ERR: indicates the error position (if any)

     This function will set the DN on the provided certificate.  The
     input string should be plain ASCII or UTF-8 encoded.  On DN parsing
     error ‘GNUTLS_E_PARSING_ERROR’ is returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_set_dn_by_oid
-----------------------------

 -- Function: int gnutls_x509_crq_set_dn_by_oid (gnutls_x509_crq_t CRQ,
          const char * OID, unsigned int RAW_FLAG, const void * DATA,
          unsigned int SIZEOF_DATA)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     OID: holds an Object Identifier in a (0)-terminated string

     RAW_FLAG: must be 0, or 1 if the data are DER encoded

     DATA: a pointer to the input data

     SIZEOF_DATA: holds the size of ‘data’

     This function will set the part of the name of the Certificate
     request subject, specified by the given OID. The input string
     should be ASCII or UTF-8 encoded.

     Some helper macros with popular OIDs can be found in gnutls/x509.h
     With this function you can only set the known OIDs.  You can test
     for known OIDs using ‘gnutls_x509_dn_oid_known()’ .  For OIDs that
     are not known (by gnutls) you should properly DER encode your data,
     and call this function with raw_flag set.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_set_extension_by_oid
------------------------------------

 -- Function: int gnutls_x509_crq_set_extension_by_oid
          (gnutls_x509_crq_t CRQ, const char * OID, const void * BUF,
          size_t SIZEOF_BUF, unsigned int CRITICAL)
     CRQ: a certificate of type ‘gnutls_x509_crq_t’

     OID: holds an Object Identifier in null terminated string

     BUF: a pointer to a DER encoded data

     SIZEOF_BUF: holds the size of ‘buf’

     CRITICAL: should be non-zero if the extension is to be marked as
     critical

     This function will set an the extension, by the specified OID, in
     the certificate request.  The extension data should be binary data
     DER encoded.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_set_key
-----------------------

 -- Function: int gnutls_x509_crq_set_key (gnutls_x509_crq_t CRQ,
          gnutls_x509_privkey_t KEY)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     KEY: holds a private key

     This function will set the public parameters from the given private
     key to the request.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_set_key_purpose_oid
-----------------------------------

 -- Function: int gnutls_x509_crq_set_key_purpose_oid (gnutls_x509_crq_t
          CRQ, const void * OID, unsigned int CRITICAL)
     CRQ: a certificate of type ‘gnutls_x509_crq_t’

     OID: a pointer to a null-terminated string that holds the OID

     CRITICAL: Whether this extension will be critical or not

     This function will set the key purpose OIDs of the Certificate.
     These are stored in the Extended Key Usage extension (*********)
     See the GNUTLS_KP_* definitions for human readable names.

     Subsequent calls to this function will append OIDs to the OID list.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.8.0

gnutls_x509_crq_set_key_rsa_raw
-------------------------------

 -- Function: int gnutls_x509_crq_set_key_rsa_raw (gnutls_x509_crq_t
          CRQ, const gnutls_datum_t * M, const gnutls_datum_t * E)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     M: holds the modulus

     E: holds the public exponent

     This function will set the public parameters from the given private
     key to the request.  Only RSA keys are currently supported.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.6.0

gnutls_x509_crq_set_key_usage
-----------------------------

 -- Function: int gnutls_x509_crq_set_key_usage (gnutls_x509_crq_t CRQ,
          unsigned int USAGE)
     CRQ: a certificate request of type ‘gnutls_x509_crq_t’

     USAGE: an ORed sequence of the GNUTLS_KEY_* elements.

     This function will set the keyUsage certificate extension.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.8.0

gnutls_x509_crq_set_private_key_usage_period
--------------------------------------------

 -- Function: int gnutls_x509_crq_set_private_key_usage_period
          (gnutls_x509_crq_t CRQ, time_t ACTIVATION, time_t EXPIRATION)
     CRQ: a certificate of type ‘gnutls_x509_crq_t’

     ACTIVATION: The activation time

     EXPIRATION: The expiration time

     This function will set the private key usage period extension
     (*********).

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_set_spki
------------------------

 -- Function: int gnutls_x509_crq_set_spki (gnutls_x509_crq_t CRQ, const
          gnutls_x509_spki_t SPKI, unsigned int FLAGS)
     CRQ: a certificate request of type ‘gnutls_x509_crq_t’

     SPKI: a SubjectPublicKeyInfo structure of type ‘gnutls_x509_spki_t’

     FLAGS: must be zero

     This function will set the certificate request's subject public key
     information explicitly.  This is intended to be used in the cases
     where a single public key (e.g., RSA) can be used for multiple
     signature algorithms (RSA PKCS1-1.5, and RSA-PSS).

     To export the public key (i.e., the SubjectPublicKeyInfo part),
     check ‘gnutls_pubkey_import_x509()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.0

gnutls_x509_crq_set_subject_alt_name
------------------------------------

 -- Function: int gnutls_x509_crq_set_subject_alt_name
          (gnutls_x509_crq_t CRQ, gnutls_x509_subject_alt_name_t NT,
          const void * DATA, unsigned int DATA_SIZE, unsigned int FLAGS)
     CRQ: a certificate request of type ‘gnutls_x509_crq_t’

     NT: is one of the ‘gnutls_x509_subject_alt_name_t’ enumerations

     DATA: The data to be set

     DATA_SIZE: The size of data to be set

     FLAGS: ‘GNUTLS_FSAN_SET’ to clear previous data or
     ‘GNUTLS_FSAN_APPEND’ to append.

     This function will set the subject alternative name certificate
     extension.  It can set the following types:

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.8.0

gnutls_x509_crq_set_subject_alt_othername
-----------------------------------------

 -- Function: int gnutls_x509_crq_set_subject_alt_othername
          (gnutls_x509_crq_t CRQ, const char * OID, const void * DATA,
          unsigned int DATA_SIZE, unsigned int FLAGS)
     CRQ: a certificate request of type ‘gnutls_x509_crq_t’

     OID: is the othername OID

     DATA: The data to be set

     DATA_SIZE: The size of data to be set

     FLAGS: ‘GNUTLS_FSAN_SET’ to clear previous data or
     ‘GNUTLS_FSAN_APPEND’ to append.

     This function will set the subject alternative name certificate
     extension.  It can set the following types:

     The values set must be binary values and must be properly DER
     encoded.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.0

gnutls_x509_crq_set_tlsfeatures
-------------------------------

 -- Function: int gnutls_x509_crq_set_tlsfeatures (gnutls_x509_crq_t
          CRQ, gnutls_x509_tlsfeatures_t FEATURES)
     CRQ: An X.509 certificate request

     FEATURES: If the function succeeds, the features will be added to
     the certificate request.

     This function will set the certificate request's X.509 TLS
     extension from the given structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.1

gnutls_x509_crq_set_version
---------------------------

 -- Function: int gnutls_x509_crq_set_version (gnutls_x509_crq_t CRQ,
          unsigned int VERSION)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     VERSION: holds the version number, for v1 Requests must be 1

     This function will set the version of the certificate request.  For
     version 1 requests this must be one.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_sign
--------------------

 -- Function: int gnutls_x509_crq_sign (gnutls_x509_crq_t CRQ,
          gnutls_x509_privkey_t KEY)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     KEY: holds a private key

     This function is the same a ‘gnutls_x509_crq_sign2()’ with no
     flags, and an appropriate hash algorithm.  The hash algorithm used
     may vary between versions of GnuTLS, and it is tied to the security
     level of the issuer's public key.

     A known limitation of this function is, that a newly-signed request
     will not be fully functional (e.g., for signature verification),
     until it is exported an re-imported.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crq_sign2
---------------------

 -- Function: int gnutls_x509_crq_sign2 (gnutls_x509_crq_t CRQ,
          gnutls_x509_privkey_t KEY, gnutls_digest_algorithm_t DIG,
          unsigned int FLAGS)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     KEY: holds a private key

     DIG: The message digest to use, i.e., ‘GNUTLS_DIG_SHA256’

     FLAGS: must be 0

     This function will sign the certificate request with a private key.
     This must be the same key as the one used in
     ‘gnutls_x509_crt_set_key()’ since a certificate request is self
     signed.

     This must be the last step in a certificate request generation
     since all the previously set parameters are now signed.

     A known limitation of this function is, that a newly-signed request
     will not be fully functional (e.g., for signature verification),
     until it is exported an re-imported.

     After GnuTLS 3.6.1 the value of ‘dig’ may be ‘GNUTLS_DIG_UNKNOWN’ ,
     and in that case, a suitable but reasonable for the key algorithm
     will be selected.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.  ‘GNUTLS_E_ASN1_VALUE_NOT_FOUND’ is returned if you
     didn't set all information in the certificate request (e.g., the
     version using ‘gnutls_x509_crq_set_version()’ ).

gnutls_x509_crq_verify
----------------------

 -- Function: int gnutls_x509_crq_verify (gnutls_x509_crq_t CRQ,
          unsigned int FLAGS)
     CRQ: is the crq to be verified

     FLAGS: Flags that may be used to change the verification algorithm.
     Use OR of the gnutls_certificate_verify_flags enumerations.

     This function will verify self signature in the certificate request
     and return its status.

     *Returns:* In case of a verification failure
     ‘GNUTLS_E_PK_SIG_VERIFY_FAILED’ is returned, and zero or positive
     code on success.

     Since 2.12.0

gnutls_x509_crt_check_email
---------------------------

 -- Function: unsigned gnutls_x509_crt_check_email (gnutls_x509_crt_t
          CERT, const char * EMAIL, unsigned int FLAGS)
     CERT: should contain an gnutls_x509_crt_t type

     EMAIL: A null terminated string that contains an email address
     (RFC822)

     FLAGS: should be zero

     This function will check if the given certificate's subject matches
     the given email address.

     *Returns:* non-zero for a successful match, and zero on failure.

gnutls_x509_crt_check_hostname
------------------------------

 -- Function: unsigned gnutls_x509_crt_check_hostname (gnutls_x509_crt_t
          CERT, const char * HOSTNAME)
     CERT: should contain an gnutls_x509_crt_t type

     HOSTNAME: A null terminated string that contains a DNS name

     This function will check if the given certificate's subject matches
     the given hostname.  This is a basic implementation of the matching
     described in RFC6125, and takes into account wildcards, and the
     DNSName/IPAddress subject alternative name PKIX extension.

     For details see also ‘gnutls_x509_crt_check_hostname2()’ .

     *Returns:* non-zero for a successful match, and zero on failure.

gnutls_x509_crt_check_hostname2
-------------------------------

 -- Function: unsigned gnutls_x509_crt_check_hostname2
          (gnutls_x509_crt_t CERT, const char * HOSTNAME, unsigned int
          FLAGS)
     CERT: should contain an gnutls_x509_crt_t type

     HOSTNAME: A null terminated string that contains a DNS name

     FLAGS: gnutls_certificate_verify_flags

     This function will check if the given certificate's subject matches
     the given hostname.  This is a basic implementation of the matching
     described in RFC6125, and takes into account wildcards, and the
     DNSName/IPAddress subject alternative name PKIX extension.

     IPv4 addresses are accepted by this function in the dotted-decimal
     format (e.g, ddd.ddd.ddd.ddd), and IPv6 addresses in the
     hexadecimal x:x:x:x:x:x:x:x format.  For them the IPAddress subject
     alternative name extension is consulted.  Previous versions to
     3.6.0 of GnuTLS in case of a non-match would consult (in a
     non-standard extension) the DNSname and CN fields.  This is no
     longer the case.

     When the flag ‘GNUTLS_VERIFY_DO_NOT_ALLOW_WILDCARDS’ is specified
     no wildcards are considered.  Otherwise they are only considered if
     the domain name consists of three components or more, and the
     wildcard starts at the leftmost position.  When the flag
     ‘GNUTLS_VERIFY_DO_NOT_ALLOW_IP_MATCHES’ is specified, the input
     will be treated as a DNS name, and matching of textual IP addresses
     against the IPAddress part of the alternative name will not be
     allowed.

     The function ‘gnutls_x509_crt_check_ip()’ is available for matching
     IP addresses.

     *Returns:* non-zero for a successful match, and zero on failure.

     *Since:* 3.3.0

gnutls_x509_crt_check_ip
------------------------

 -- Function: unsigned gnutls_x509_crt_check_ip (gnutls_x509_crt_t CERT,
          const unsigned char * IP, unsigned int IP_SIZE, unsigned int
          FLAGS)
     CERT: should contain an gnutls_x509_crt_t type

     IP: A pointer to the raw IP address

     IP_SIZE: the number of bytes in ip (4 or 16)

     FLAGS: should be zero

     This function will check if the IP allowed IP addresses in the
     certificate's subject alternative name match the provided IP
     address.

     *Returns:* non-zero for a successful match, and zero on failure.

gnutls_x509_crt_check_issuer
----------------------------

 -- Function: unsigned gnutls_x509_crt_check_issuer (gnutls_x509_crt_t
          CERT, gnutls_x509_crt_t ISSUER)
     CERT: is the certificate to be checked

     ISSUER: is the certificate of a possible issuer

     This function will check if the given certificate was issued by the
     given issuer.  It checks the DN fields and the authority key
     identifier and subject key identifier fields match.

     If the same certificate is provided at the ‘cert’ and ‘issuer’
     fields, it will check whether the certificate is self-signed.

     *Returns:* It will return true (1) if the given certificate is
     issued by the given issuer, and false (0) if not.

gnutls_x509_crt_check_key_purpose
---------------------------------

 -- Function: unsigned gnutls_x509_crt_check_key_purpose
          (gnutls_x509_crt_t CERT, const char * PURPOSE, unsigned FLAGS)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     PURPOSE: a key purpose OID (e.g., ‘GNUTLS_KP_CODE_SIGNING’ )

     FLAGS: zero or ‘GNUTLS_KP_FLAG_DISALLOW_ANY’

     This function will check whether the given certificate matches the
     provided key purpose.  If ‘flags’ contains
     ‘GNUTLS_KP_FLAG_ALLOW_ANY’ then it a certificate marked for any
     purpose will not match.

     *Returns:* zero if the key purpose doesn't match, and non-zero
     otherwise.

     *Since:* 3.5.6

gnutls_x509_crt_check_revocation
--------------------------------

 -- Function: int gnutls_x509_crt_check_revocation (gnutls_x509_crt_t
          CERT, const gnutls_x509_crl_t * CRL_LIST, unsigned
          CRL_LIST_LENGTH)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     CRL_LIST: should contain a list of gnutls_x509_crl_t types

     CRL_LIST_LENGTH: the length of the crl_list

     This function will check if the given certificate is revoked.  It
     is assumed that the CRLs have been verified before.

     *Returns:* 0 if the certificate is NOT revoked, and 1 if it is.  A
     negative error code is returned on error.

gnutls_x509_crt_cpy_crl_dist_points
-----------------------------------

 -- Function: int gnutls_x509_crt_cpy_crl_dist_points (gnutls_x509_crt_t
          DST, gnutls_x509_crt_t SRC)
     DST: a certificate of type ‘gnutls_x509_crt_t’

     SRC: the certificate where the dist points will be copied from

     This function will copy the CRL distribution points certificate
     extension, from the source to the destination certificate.  This
     may be useful to copy from a CA certificate to issued ones.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_deinit
----------------------

 -- Function: void gnutls_x509_crt_deinit (gnutls_x509_crt_t CERT)
     CERT: The data to be deinitialized

     This function will deinitialize a certificate structure.

gnutls_x509_crt_equals
----------------------

 -- Function: unsigned gnutls_x509_crt_equals (gnutls_x509_crt_t CERT1,
          gnutls_x509_crt_t CERT2)
     CERT1: The first certificate

     CERT2: The second certificate

     This function will compare two X.509 certificate structures.

     *Returns:* On equality non-zero is returned, otherwise zero.

     *Since:* 3.5.0

gnutls_x509_crt_equals2
-----------------------

 -- Function: unsigned gnutls_x509_crt_equals2 (gnutls_x509_crt_t CERT1,
          const gnutls_datum_t * DER)
     CERT1: The first certificate

     DER: A DER encoded certificate

     This function will compare an X.509 certificate structures, with
     DER encoded certificate data.

     *Returns:* On equality non-zero is returned, otherwise zero.

     *Since:* 3.5.0

gnutls_x509_crt_export
----------------------

 -- Function: int gnutls_x509_crt_export (gnutls_x509_crt_t CERT,
          gnutls_x509_crt_fmt_t FORMAT, void * OUTPUT_DATA, size_t *
          OUTPUT_DATA_SIZE)
     CERT: Holds the certificate

     FORMAT: the format of output params.  One of PEM or DER.

     OUTPUT_DATA: will contain a certificate PEM or DER encoded

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will export the certificate to DER or PEM format.

     If the buffer provided is not long enough to hold the output, then
     *output_data_size is updated and GNUTLS_E_SHORT_MEMORY_BUFFER will
     be returned.

     If the structure is PEM encoded, it will have a header of "BEGIN
     CERTIFICATE".

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

gnutls_x509_crt_export2
-----------------------

 -- Function: int gnutls_x509_crt_export2 (gnutls_x509_crt_t CERT,
          gnutls_x509_crt_fmt_t FORMAT, gnutls_datum_t * OUT)
     CERT: Holds the certificate

     FORMAT: the format of output params.  One of PEM or DER.

     OUT: will contain a certificate PEM or DER encoded

     This function will export the certificate to DER or PEM format.
     The output buffer is allocated using ‘gnutls_malloc()’ .

     If the structure is PEM encoded, it will have a header of "BEGIN
     CERTIFICATE".

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

     *Since:* 3.1.3

gnutls_x509_crt_get_activation_time
-----------------------------------

 -- Function: time_t gnutls_x509_crt_get_activation_time
          (gnutls_x509_crt_t CERT)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     This function will return the time this Certificate was or will be
     activated.

     *Returns:* activation time, or (time_t)-1 on error.

gnutls_x509_crt_get_authority_info_access
-----------------------------------------

 -- Function: int gnutls_x509_crt_get_authority_info_access
          (gnutls_x509_crt_t CRT, unsigned int SEQ, int WHAT,
          gnutls_datum_t * DATA, unsigned int * CRITICAL)
     CRT: Holds the certificate

     SEQ: specifies the sequence number of the access descriptor (0 for
     the first one, 1 for the second etc.)

     WHAT: what data to get, a ‘gnutls_info_access_what_t’ type.

     DATA: output data to be freed with ‘gnutls_free()’ .

     CRITICAL: pointer to output integer that is set to non-zero if the
     extension is marked as critical (may be ‘NULL’ )

     Note that a simpler API to access the authority info data is
     provided by ‘gnutls_x509_aia_get()’ and
     ‘gnutls_x509_ext_import_aia()’ .

     This function extracts the Authority Information Access (AIA)
     extension, see RFC 5280 section ******* for more information.  The
     AIA extension holds a sequence of AccessDescription (AD) data.

     The ‘seq’ input parameter is used to indicate which member of the
     sequence the caller is interested in.  The first member is 0, the
     second member 1 and so on.  When the ‘seq’ value is out of bounds,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.

     The type of data returned in ‘data’ is specified via ‘what’ which
     should be ‘gnutls_info_access_what_t’ values.

     If ‘what’ is ‘GNUTLS_IA_ACCESSMETHOD_OID’ then ‘data’ will hold the
     accessMethod OID (e.g., "*******.********.1").

     If ‘what’ is ‘GNUTLS_IA_ACCESSLOCATION_GENERALNAME_TYPE’ , ‘data’
     will hold the accessLocation GeneralName type (e.g.,
     "uniformResourceIdentifier").

     If ‘what’ is ‘GNUTLS_IA_URI’ , ‘data’ will hold the accessLocation
     URI data.  Requesting this ‘what’ value leads to an error if the
     accessLocation is not of the "uniformResourceIdentifier" type.

     If ‘what’ is ‘GNUTLS_IA_OCSP_URI’ , ‘data’ will hold the OCSP URI.
     Requesting this ‘what’ value leads to an error if the accessMethod
     is not *******.********.1 aka OCSP, or if accessLocation is not of
     the "uniformResourceIdentifier" type.  In that case
     ‘GNUTLS_E_UNKNOWN_ALGORITHM’ will be returned, and ‘seq’ should be
     increased and this function called again.

     If ‘what’ is ‘GNUTLS_IA_CAISSUERS_URI’ , ‘data’ will hold the
     caIssuers URI. Requesting this ‘what’ value leads to an error if
     the accessMethod is not *******.********.2 aka caIssuers, or if
     accessLocation is not of the "uniformResourceIdentifier" type.  In
     that case handle as in ‘GNUTLS_IA_OCSP_URI’ .

     More ‘what’ values may be allocated in the future as needed.

     If ‘data’ is NULL, the function does the same without storing the
     output data, that is, it will set ‘critical’ and do error checking
     as usual.

     The value of the critical flag is returned in * ‘critical’ .
     Supply a NULL ‘critical’ if you want the function to make sure the
     extension is non-critical, as required by RFC 5280.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success,
     ‘GNUTLS_E_INVALID_REQUEST’ on invalid ‘crt’ ,
     ‘GNUTLS_E_CONSTRAINT_ERROR’ if the extension is incorrectly marked
     as critical (use a non-NULL ‘critical’ to override),
     ‘GNUTLS_E_UNKNOWN_ALGORITHM’ if the requested OID does not match
     (e.g., when using ‘GNUTLS_IA_OCSP_URI’ ), otherwise a negative
     error code.

     *Since:* 3.0

gnutls_x509_crt_get_authority_key_gn_serial
-------------------------------------------

 -- Function: int gnutls_x509_crt_get_authority_key_gn_serial
          (gnutls_x509_crt_t CERT, unsigned int SEQ, void * ALT, size_t
          * ALT_SIZE, unsigned int * ALT_TYPE, void * SERIAL, size_t *
          SERIAL_SIZE, unsigned int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     SEQ: specifies the sequence number of the alt name (0 for the first
     one, 1 for the second etc.)

     ALT: is the place where the alternative name will be copied to

     ALT_SIZE: holds the size of alt.

     ALT_TYPE: holds the type of the alternative name (one of
     gnutls_x509_subject_alt_name_t).

     SERIAL: buffer to store the serial number (may be null)

     SERIAL_SIZE: Holds the size of the serial field (may be null)

     CRITICAL: will be non-zero if the extension is marked as critical
     (may be null)

     This function will return the X.509 authority key identifier when
     stored as a general name (authorityCertIssuer) and serial number.

     Because more than one general names might be stored ‘seq’ can be
     used as a counter to request them all until
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the extension is not
     present, otherwise a negative error value.

     *Since:* 3.0

gnutls_x509_crt_get_authority_key_id
------------------------------------

 -- Function: int gnutls_x509_crt_get_authority_key_id
          (gnutls_x509_crt_t CERT, void * ID, size_t * ID_SIZE, unsigned
          int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     ID: The place where the identifier will be copied

     ID_SIZE: Holds the size of the id field.

     CRITICAL: will be non-zero if the extension is marked as critical
     (may be null)

     This function will return the X.509v3 certificate authority's key
     identifier.  This is obtained by the X.509 Authority Key identifier
     extension field (2.5.29.35).  Note that this function only returns
     the keyIdentifier field of the extension and
     ‘GNUTLS_E_X509_UNSUPPORTED_EXTENSION’ , if the extension contains
     the name and serial number of the certificate.  In that case
     ‘gnutls_x509_crt_get_authority_key_gn_serial()’ may be used.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the extension is not
     present, otherwise a negative error value.

gnutls_x509_crt_get_basic_constraints
-------------------------------------

 -- Function: int gnutls_x509_crt_get_basic_constraints
          (gnutls_x509_crt_t CERT, unsigned int * CRITICAL, unsigned int
          * CA, int * PATHLEN)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     CRITICAL: will be non-zero if the extension is marked as critical

     CA: pointer to output integer indicating CA status, may be NULL,
     value is 1 if the certificate CA flag is set, 0 otherwise.

     PATHLEN: pointer to output integer indicating path length (may be
     NULL), non-negative error codes indicate a present
     pathLenConstraint field and the actual value, -1 indicate that the
     field is absent.

     This function will read the certificate's basic constraints, and
     return the certificates CA status.  It reads the basicConstraints
     X.509 extension (2.5.29.19).

     *Returns:* If the certificate is a CA a positive value will be
     returned, or (0) if the certificate does not have CA flag set.  A
     negative error code may be returned in case of errors.  If the
     certificate does not contain the basicConstraints extension
     GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE will be returned.

gnutls_x509_crt_get_ca_status
-----------------------------

 -- Function: int gnutls_x509_crt_get_ca_status (gnutls_x509_crt_t CERT,
          unsigned int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     CRITICAL: will be non-zero if the extension is marked as critical

     This function will return certificates CA status, by reading the
     basicConstraints X.509 extension (2.5.29.19).  If the certificate
     is a CA a positive value will be returned, or (0) if the
     certificate does not have CA flag set.

     Use ‘gnutls_x509_crt_get_basic_constraints()’ if you want to read
     the pathLenConstraint field too.

     *Returns:* If the certificate is a CA a positive value will be
     returned, or (0) if the certificate does not have CA flag set.  A
     negative error code may be returned in case of errors.  If the
     certificate does not contain the basicConstraints extension
     GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE will be returned.

gnutls_x509_crt_get_crl_dist_points
-----------------------------------

 -- Function: int gnutls_x509_crt_get_crl_dist_points (gnutls_x509_crt_t
          CERT, unsigned int SEQ, void * SAN, size_t * SAN_SIZE,
          unsigned int * REASON_FLAGS, unsigned int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     SEQ: specifies the sequence number of the distribution point (0 for
     the first one, 1 for the second etc.)

     SAN: is the place where the distribution point will be copied to

     SAN_SIZE: holds the size of ret.

     REASON_FLAGS: Revocation reasons.  An ORed sequence of flags from
     ‘gnutls_x509_crl_reason_flags_t’ .

     CRITICAL: will be non-zero if the extension is marked as critical
     (may be null)

     This function retrieves the CRL distribution points (*********),
     contained in the given certificate in the X509v3 Certificate
     Extensions.

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ and updates ‘ret_size’ if
     ‘ret_size’ is not enough to hold the distribution point, or the
     type of the distribution point if everything was ok.  The type is
     one of the enumerated ‘gnutls_x509_subject_alt_name_t’ .  If the
     certificate does not have an Alternative name with the specified
     sequence number then ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is
     returned.

gnutls_x509_crt_get_dn
----------------------

 -- Function: int gnutls_x509_crt_get_dn (gnutls_x509_crt_t CERT, char *
          BUF, size_t * BUF_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     BUF: a pointer to a structure to hold the name (may be null)

     BUF_SIZE: initially holds the size of ‘buf’

     This function will copy the name of the Certificate in the provided
     buffer.  The name will be in the form "C=xxxx,O=yyyy,CN=zzzz" as
     described in RFC4514.  The output string will be ASCII or UTF-8
     encoded, depending on the certificate data.

     The ‘buf’ returned will be null terminated and the ‘buf_size’ will
     account for the trailing null.  If ‘buf’ is null then only the size
     will be filled.

     This function does not output a fully RFC4514 compliant string, if
     that is required see ‘gnutls_x509_crt_get_dn3()’ .

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the ‘buf_size’ will be updated
     with the required size.  ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if
     the DN does not exist, or another error value on error.  On success
     0 is returned.

gnutls_x509_crt_get_dn2
-----------------------

 -- Function: int gnutls_x509_crt_get_dn2 (gnutls_x509_crt_t CERT,
          gnutls_datum_t * DN)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     DN: a pointer to a structure to hold the name; must be freed using
     ‘gnutls_free()’

     This function will allocate buffer and copy the name of the
     Certificate.  The name will be in the form "C=xxxx,O=yyyy,CN=zzzz"
     as described in RFC4514.  The output string will be ASCII or UTF-8
     encoded, depending on the certificate data.

     This function does not output a fully RFC4514 compliant string, if
     that is required see ‘gnutls_x509_crt_get_dn3()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.10

gnutls_x509_crt_get_dn3
-----------------------

 -- Function: int gnutls_x509_crt_get_dn3 (gnutls_x509_crt_t CERT,
          gnutls_datum_t * DN, unsigned FLAGS)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     DN: a pointer to a structure to hold the name; must be freed using
     ‘gnutls_free()’

     FLAGS: zero or ‘GNUTLS_X509_DN_FLAG_COMPAT’

     This function will allocate buffer and copy the name of the
     Certificate.  The name will be in the form "C=xxxx,O=yyyy,CN=zzzz"
     as described in RFC4514.  The output string will be ASCII or UTF-8
     encoded, depending on the certificate data.

     When the flag ‘GNUTLS_X509_DN_FLAG_COMPAT’ is specified, the output
     format will match the format output by previous to 3.5.6 versions
     of GnuTLS which was not not fully RFC4514-compliant.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.7

gnutls_x509_crt_get_dn_by_oid
-----------------------------

 -- Function: int gnutls_x509_crt_get_dn_by_oid (gnutls_x509_crt_t CERT,
          const char * OID, unsigned INDX, unsigned int RAW_FLAG, void *
          BUF, size_t * BUF_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     OID: holds an Object Identified in null terminated string

     INDX: In case multiple same OIDs exist in the RDN, this specifies
     which to send.  Use (0) to get the first one.

     RAW_FLAG: If non-zero returns the raw DER data of the DN part.

     BUF: a pointer where the DN part will be copied (may be null).

     BUF_SIZE: initially holds the size of ‘buf’

     This function will extract the part of the name of the Certificate
     subject specified by the given OID. The output, if the raw flag is
     not used, will be encoded as described in RFC4514.  Thus a string
     that is ASCII or UTF-8 encoded, depending on the certificate data.

     Some helper macros with popular OIDs can be found in gnutls/x509.h
     If raw flag is (0), this function will only return known OIDs as
     text.  Other OIDs will be DER encoded, as described in RFC4514 - in
     hex format with a '#' prefix.  You can check about known OIDs using
     ‘gnutls_x509_dn_oid_known()’ .

     If ‘buf’ is null then only the size will be filled.  If the
     ‘raw_flag’ is not specified the output is always null terminated,
     although the ‘buf_size’ will not include the null character.

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the ‘buf_size’ will be updated
     with the required size.  ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if
     there are no data in the current index.  On success 0 is returned.

gnutls_x509_crt_get_dn_oid
--------------------------

 -- Function: int gnutls_x509_crt_get_dn_oid (gnutls_x509_crt_t CERT,
          unsigned INDX, void * OID, size_t * OID_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     INDX: This specifies which OID to return.  Use (0) to get the first
     one.

     OID: a pointer to a buffer to hold the OID (may be null)

     OID_SIZE: initially holds the size of ‘oid’

     This function will extract the OIDs of the name of the Certificate
     subject specified by the given index.

     If ‘oid’ is null then only the size will be filled.  The ‘oid’
     returned will be null terminated, although ‘oid_size’ will not
     account for the trailing null.

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the ‘buf_size’ will be updated
     with the required size.  ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if
     there are no data in the current index.  On success 0 is returned.

gnutls_x509_crt_get_expiration_time
-----------------------------------

 -- Function: time_t gnutls_x509_crt_get_expiration_time
          (gnutls_x509_crt_t CERT)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     This function will return the time this certificate was or will be
     expired.

     *Returns:* expiration time, or (time_t)-1 on error.

gnutls_x509_crt_get_extension_by_oid
------------------------------------

 -- Function: int gnutls_x509_crt_get_extension_by_oid
          (gnutls_x509_crt_t CERT, const char * OID, unsigned INDX, void
          * BUF, size_t * BUF_SIZE, unsigned int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     OID: holds an Object Identified in null terminated string

     INDX: In case multiple same OIDs exist in the extensions, this
     specifies which to send.  Use (0) to get the first one.

     BUF: a pointer to a structure to hold the name (may be null)

     BUF_SIZE: initially holds the size of ‘buf’

     CRITICAL: will be non-zero if the extension is marked as critical

     This function will return the extension specified by the OID in the
     certificate.  The extensions will be returned as binary data DER
     encoded, in the provided buffer.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  If the certificate
     does not contain the specified extension
     GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE will be returned.

gnutls_x509_crt_get_extension_by_oid2
-------------------------------------

 -- Function: int gnutls_x509_crt_get_extension_by_oid2
          (gnutls_x509_crt_t CERT, const char * OID, unsigned INDX,
          gnutls_datum_t * OUTPUT, unsigned int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     OID: holds an Object Identified in null terminated string

     INDX: In case multiple same OIDs exist in the extensions, this
     specifies which to send.  Use (0) to get the first one.

     OUTPUT: will hold the allocated extension data

     CRITICAL: will be non-zero if the extension is marked as critical

     This function will return the extension specified by the OID in the
     certificate.  The extensions will be returned as binary data DER
     encoded, in the provided buffer.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  If the certificate
     does not contain the specified extension
     GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE will be returned.

     *Since:* 3.3.8

gnutls_x509_crt_get_extension_data
----------------------------------

 -- Function: int gnutls_x509_crt_get_extension_data (gnutls_x509_crt_t
          CERT, unsigned INDX, void * DATA, size_t * SIZEOF_DATA)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     INDX: Specifies which extension OID to send.  Use (0) to get the
     first one.

     DATA: a pointer to a structure to hold the data (may be null)

     SIZEOF_DATA: initially holds the size of ‘data’

     This function will return the requested extension data in the
     certificate.  The extension data will be stored in the provided
     buffer.

     Use ‘gnutls_x509_crt_get_extension_info()’ to extract the OID and
     critical flag.  Use ‘gnutls_x509_crt_get_extension_by_oid()’
     instead, if you want to get data indexed by the extension OID
     rather than sequence.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  If you have reached
     the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

gnutls_x509_crt_get_extension_data2
-----------------------------------

 -- Function: int gnutls_x509_crt_get_extension_data2 (gnutls_x509_crt_t
          CERT, unsigned INDX, gnutls_datum_t * DATA)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     INDX: Specifies which extension OID to read.  Use (0) to get the
     first one.

     DATA: will contain the extension DER-encoded data

     This function will return the requested by the index extension data
     in the certificate.  The extension data will be allocated using
     ‘gnutls_malloc()’ .

     Use ‘gnutls_x509_crt_get_extension_info()’ to extract the OID.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  If you have reached
     the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

gnutls_x509_crt_get_extension_info
----------------------------------

 -- Function: int gnutls_x509_crt_get_extension_info (gnutls_x509_crt_t
          CERT, unsigned INDX, void * OID, size_t * OID_SIZE, unsigned
          int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     INDX: Specifies which extension OID to send.  Use (0) to get the
     first one.

     OID: a pointer to a structure to hold the OID

     OID_SIZE: initially holds the maximum size of ‘oid’ , on return
     holds actual size of ‘oid’ .

     CRITICAL: output variable with critical flag, may be NULL.

     This function will return the requested extension OID in the
     certificate, and the critical flag for it.  The extension OID will
     be stored as a string in the provided buffer.  Use
     ‘gnutls_x509_crt_get_extension()’ to extract the data.

     If the buffer provided is not long enough to hold the output, then
     ‘oid_size’ is updated and ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ will be
     returned.  The ‘oid’ returned will be null terminated, although
     ‘oid_size’ will not account for the trailing null (the latter is
     not true for GnuTLS prior to 3.6.0).

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  If you have reached
     the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

gnutls_x509_crt_get_extension_oid
---------------------------------

 -- Function: int gnutls_x509_crt_get_extension_oid (gnutls_x509_crt_t
          CERT, unsigned INDX, void * OID, size_t * OID_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     INDX: Specifies which extension OID to send.  Use (0) to get the
     first one.

     OID: a pointer to a structure to hold the OID (may be null)

     OID_SIZE: initially holds the size of ‘oid’

     This function will return the requested extension OID in the
     certificate.  The extension OID will be stored as a string in the
     provided buffer.

     The ‘oid’ returned will be null terminated, although ‘oid_size’
     will not account for the trailing null.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  If you have reached
     the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

gnutls_x509_crt_get_fingerprint
-------------------------------

 -- Function: int gnutls_x509_crt_get_fingerprint (gnutls_x509_crt_t
          CERT, gnutls_digest_algorithm_t ALGO, void * BUF, size_t *
          BUF_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     ALGO: is a digest algorithm

     BUF: a pointer to a structure to hold the fingerprint (may be null)

     BUF_SIZE: initially holds the size of ‘buf’

     This function will calculate and copy the certificate's fingerprint
     in the provided buffer.  The fingerprint is a hash of the
     DER-encoded data of the certificate.

     If the buffer is null then only the size will be filled.

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the *buf_size will be updated
     with the required size.  On success 0 is returned.

gnutls_x509_crt_get_inhibit_anypolicy
-------------------------------------

 -- Function: int gnutls_x509_crt_get_inhibit_anypolicy
          (gnutls_x509_crt_t CERT, unsigned int * SKIPCERTS, unsigned
          int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     SKIPCERTS: will hold the number of certificates after which
     anypolicy is no longer acceptable.

     CRITICAL: will be non-zero if the extension is marked as critical

     This function will return certificate's value of the SkipCerts,
     i.e., the Inhibit anyPolicy X.509 extension (*********).

     The returned value is the number of additional certificates that
     may appear in the path before the anyPolicy is no longer
     acceptable.

     *Returns:* zero on success, or a negative error code in case of
     parsing error.  If the certificate does not contain the Inhibit
     anyPolicy extension ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be
     returned.

     *Since:* 3.6.0

gnutls_x509_crt_get_issuer
--------------------------

 -- Function: int gnutls_x509_crt_get_issuer (gnutls_x509_crt_t CERT,
          gnutls_x509_dn_t * DN)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     DN: output variable with pointer to uint8_t DN

     Return the Certificate's Issuer DN as a ‘gnutls_x509_dn_t’ data
     type, that can be decoded using ‘gnutls_x509_dn_get_rdn_ava()’ .

     Note that ‘dn’ should be treated as constant.  Because it points
     into the ‘cert’ object, you should not use ‘dn’ after ‘cert’ is
     deallocated.

     *Returns:* Returns 0 on success, or an error code.

gnutls_x509_crt_get_issuer_alt_name
-----------------------------------

 -- Function: int gnutls_x509_crt_get_issuer_alt_name (gnutls_x509_crt_t
          CERT, unsigned int SEQ, void * IAN, size_t * IAN_SIZE,
          unsigned int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     SEQ: specifies the sequence number of the alt name (0 for the first
     one, 1 for the second etc.)

     IAN: is the place where the alternative name will be copied to

     IAN_SIZE: holds the size of ian.

     CRITICAL: will be non-zero if the extension is marked as critical
     (may be null)

     This function retrieves the Issuer Alternative Name (2.5.29.18),
     contained in the given certificate in the X509v3 Certificate
     Extensions.

     When the SAN type is otherName, it will extract the data in the
     otherName's value field, and ‘GNUTLS_SAN_OTHERNAME’ is returned.
     You may use ‘gnutls_x509_crt_get_subject_alt_othername_oid()’ to
     get the corresponding OID and the "virtual" SAN types (e.g.,
     ‘GNUTLS_SAN_OTHERNAME_XMPP’ ).

     If an otherName OID is known, the data will be decoded.  Otherwise
     the returned data will be DER encoded, and you will have to decode
     it yourself.  Currently, only the RFC 3920 id-on-xmppAddr Issuer
     AltName is recognized.

     *Returns:* the alternative issuer name type on success, one of the
     enumerated ‘gnutls_x509_subject_alt_name_t’ .  It will return
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if ‘ian_size’ is not large enough to
     hold the value.  In that case ‘ian_size’ will be updated with the
     required size.  If the certificate does not have an Alternative
     name with the specified sequence number then
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.

     *Since:* 2.10.0

gnutls_x509_crt_get_issuer_alt_name2
------------------------------------

 -- Function: int gnutls_x509_crt_get_issuer_alt_name2
          (gnutls_x509_crt_t CERT, unsigned int SEQ, void * IAN, size_t
          * IAN_SIZE, unsigned int * IAN_TYPE, unsigned int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     SEQ: specifies the sequence number of the alt name (0 for the first
     one, 1 for the second etc.)

     IAN: is the place where the alternative name will be copied to

     IAN_SIZE: holds the size of ret.

     IAN_TYPE: holds the type of the alternative name (one of
     gnutls_x509_subject_alt_name_t).

     CRITICAL: will be non-zero if the extension is marked as critical
     (may be null)

     This function will return the alternative names, contained in the
     given certificate.  It is the same as
     ‘gnutls_x509_crt_get_issuer_alt_name()’ except for the fact that it
     will return the type of the alternative name in ‘ian_type’ even if
     the function fails for some reason (i.e.  the buffer provided is
     not enough).

     *Returns:* the alternative issuer name type on success, one of the
     enumerated ‘gnutls_x509_subject_alt_name_t’ .  It will return
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if ‘ian_size’ is not large enough to
     hold the value.  In that case ‘ian_size’ will be updated with the
     required size.  If the certificate does not have an Alternative
     name with the specified sequence number then
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.

     *Since:* 2.10.0

gnutls_x509_crt_get_issuer_alt_othername_oid
--------------------------------------------

 -- Function: int gnutls_x509_crt_get_issuer_alt_othername_oid
          (gnutls_x509_crt_t CERT, unsigned int SEQ, void * RET, size_t
          * RET_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     SEQ: specifies the sequence number of the alt name (0 for the first
     one, 1 for the second etc.)

     RET: is the place where the otherName OID will be copied to

     RET_SIZE: holds the size of ret.

     This function will extract the type OID of an otherName Subject
     Alternative Name, contained in the given certificate, and return
     the type as an enumerated element.

     If ‘oid’ is null then only the size will be filled.  The ‘oid’
     returned will be null terminated, although ‘oid_size’ will not
     account for the trailing null.

     This function is only useful if
     ‘gnutls_x509_crt_get_issuer_alt_name()’ returned
     ‘GNUTLS_SAN_OTHERNAME’ .

     *Returns:* the alternative issuer name type on success, one of the
     enumerated gnutls_x509_subject_alt_name_t.  For supported OIDs, it
     will return one of the virtual (GNUTLS_SAN_OTHERNAME_*) types, e.g.
     ‘GNUTLS_SAN_OTHERNAME_XMPP’ , and ‘GNUTLS_SAN_OTHERNAME’ for
     unknown OIDs.  It will return ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if
     ‘ret_size’ is not large enough to hold the value.  In that case
     ‘ret_size’ will be updated with the required size.  If the
     certificate does not have an Alternative name with the specified
     sequence number and with the otherName type then
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.

     *Since:* 2.10.0

gnutls_x509_crt_get_issuer_dn
-----------------------------

 -- Function: int gnutls_x509_crt_get_issuer_dn (gnutls_x509_crt_t CERT,
          char * BUF, size_t * BUF_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     BUF: a pointer to a structure to hold the name (may be null)

     BUF_SIZE: initially holds the size of ‘buf’

     This function will copy the name of the Certificate issuer in the
     provided buffer.  The name will be in the form
     "C=xxxx,O=yyyy,CN=zzzz" as described in RFC4514.  The output string
     will be ASCII or UTF-8 encoded, depending on the certificate data.

     If ‘buf’ is null then only the size will be filled.

     This function does not output a fully RFC4514 compliant string, if
     that is required see ‘gnutls_x509_crt_get_issuer_dn3()’ .

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the ‘buf_size’ will be updated
     with the required size.  ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if
     the DN does not exist, or another error value on error.  On success
     0 is returned.

gnutls_x509_crt_get_issuer_dn2
------------------------------

 -- Function: int gnutls_x509_crt_get_issuer_dn2 (gnutls_x509_crt_t
          CERT, gnutls_datum_t * DN)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     DN: a pointer to a structure to hold the name; must be freed using
     ‘gnutls_free()’

     This function will allocate buffer and copy the name of issuer of
     the Certificate.  The name will be in the form
     "C=xxxx,O=yyyy,CN=zzzz" as described in RFC4514.  The output string
     will be ASCII or UTF-8 encoded, depending on the certificate data.

     This function does not output a fully RFC4514 compliant string, if
     that is required see ‘gnutls_x509_crt_get_issuer_dn3()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.10

gnutls_x509_crt_get_issuer_dn3
------------------------------

 -- Function: int gnutls_x509_crt_get_issuer_dn3 (gnutls_x509_crt_t
          CERT, gnutls_datum_t * DN, unsigned FLAGS)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     DN: a pointer to a structure to hold the name; must be freed using
     ‘gnutls_free()’

     FLAGS: zero or ‘GNUTLS_X509_DN_FLAG_COMPAT’

     This function will allocate buffer and copy the name of issuer of
     the Certificate.  The name will be in the form
     "C=xxxx,O=yyyy,CN=zzzz" as described in RFC4514.  The output string
     will be ASCII or UTF-8 encoded, depending on the certificate data.

     When the flag ‘GNUTLS_X509_DN_FLAG_COMPAT’ is specified, the output
     format will match the format output by previous to 3.5.6 versions
     of GnuTLS which was not not fully RFC4514-compliant.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.7

gnutls_x509_crt_get_issuer_dn_by_oid
------------------------------------

 -- Function: int gnutls_x509_crt_get_issuer_dn_by_oid
          (gnutls_x509_crt_t CERT, const char * OID, unsigned INDX,
          unsigned int RAW_FLAG, void * BUF, size_t * BUF_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     OID: holds an Object Identified in null terminated string

     INDX: In case multiple same OIDs exist in the RDN, this specifies
     which to send.  Use (0) to get the first one.

     RAW_FLAG: If non-zero returns the raw DER data of the DN part.

     BUF: a pointer to a structure to hold the name (may be null)

     BUF_SIZE: initially holds the size of ‘buf’

     This function will extract the part of the name of the Certificate
     issuer specified by the given OID. The output, if the raw flag is
     not used, will be encoded as described in RFC4514.  Thus a string
     that is ASCII or UTF-8 encoded, depending on the certificate data.

     Some helper macros with popular OIDs can be found in gnutls/x509.h
     If raw flag is (0), this function will only return known OIDs as
     text.  Other OIDs will be DER encoded, as described in RFC4514 - in
     hex format with a '#' prefix.  You can check about known OIDs using
     ‘gnutls_x509_dn_oid_known()’ .

     If ‘buf’ is null then only the size will be filled.  If the
     ‘raw_flag’ is not specified the output is always null terminated,
     although the ‘buf_size’ will not include the null character.

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the ‘buf_size’ will be updated
     with the required size.  ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if
     there are no data in the current index.  On success 0 is returned.

gnutls_x509_crt_get_issuer_dn_oid
---------------------------------

 -- Function: int gnutls_x509_crt_get_issuer_dn_oid (gnutls_x509_crt_t
          CERT, unsigned INDX, void * OID, size_t * OID_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     INDX: This specifies which OID to return.  Use (0) to get the first
     one.

     OID: a pointer to a buffer to hold the OID (may be null)

     OID_SIZE: initially holds the size of ‘oid’

     This function will extract the OIDs of the name of the Certificate
     issuer specified by the given index.

     If ‘oid’ is null then only the size will be filled.  The ‘oid’
     returned will be null terminated, although ‘oid_size’ will not
     account for the trailing null.

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the ‘buf_size’ will be updated
     with the required size.  ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if
     there are no data in the current index.  On success 0 is returned.

gnutls_x509_crt_get_issuer_unique_id
------------------------------------

 -- Function: int gnutls_x509_crt_get_issuer_unique_id
          (gnutls_x509_crt_t CRT, char * BUF, size_t * BUF_SIZE)
     CRT: Holds the certificate

     BUF: user allocated memory buffer, will hold the unique id

     BUF_SIZE: size of user allocated memory buffer (on input), will
     hold actual size of the unique ID on return.

     This function will extract the issuerUniqueID value (if present)
     for the given certificate.

     If the user allocated memory buffer is not large enough to hold the
     full subjectUniqueID, then a GNUTLS_E_SHORT_MEMORY_BUFFER error
     will be returned, and buf_size will be set to the actual length.

     This function had a bug prior to 3.4.8 that prevented the setting
     of ‘NULL’ ‘buf’ to discover the ‘buf_size’ .  To use this function
     safely with the older versions the ‘buf’ must be a valid buffer
     that can hold at least a single byte if ‘buf_size’ is zero.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 2.12.0

gnutls_x509_crt_get_key_id
--------------------------

 -- Function: int gnutls_x509_crt_get_key_id (gnutls_x509_crt_t CRT,
          unsigned int FLAGS, unsigned char * OUTPUT_DATA, size_t *
          OUTPUT_DATA_SIZE)
     CRT: Holds the certificate

     FLAGS: should be one of the flags from ‘gnutls_keyid_flags_t’

     OUTPUT_DATA: will contain the key ID

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will return a unique ID that depends on the public
     key parameters.  This ID can be used in checking whether a
     certificate corresponds to the given private key.

     If the buffer provided is not long enough to hold the output, then
     *output_data_size is updated and GNUTLS_E_SHORT_MEMORY_BUFFER will
     be returned.  The output will normally be a SHA-1 hash output,
     which is 20 bytes.

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

gnutls_x509_crt_get_key_purpose_oid
-----------------------------------

 -- Function: int gnutls_x509_crt_get_key_purpose_oid (gnutls_x509_crt_t
          CERT, unsigned INDX, void * OID, size_t * OID_SIZE, unsigned
          int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     INDX: This specifies which OID to return.  Use (0) to get the first
     one.

     OID: a pointer to a buffer to hold the OID (may be null)

     OID_SIZE: initially holds the size of ‘oid’

     CRITICAL: output flag to indicate criticality of extension

     This function will extract the key purpose OIDs of the Certificate
     specified by the given index.  These are stored in the Extended Key
     Usage extension (*********) See the GNUTLS_KP_* definitions for
     human readable names.

     If ‘oid’ is null then only the size will be filled.  The ‘oid’
     returned will be null terminated, although ‘oid_size’ will not
     account for the trailing null.

     *Returns:* ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if the provided buffer is
     not long enough, and in that case the *oid_size will be updated
     with the required size.  On success 0 is returned.

gnutls_x509_crt_get_key_usage
-----------------------------

 -- Function: int gnutls_x509_crt_get_key_usage (gnutls_x509_crt_t CERT,
          unsigned int * KEY_USAGE, unsigned int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     KEY_USAGE: where the key usage bits will be stored

     CRITICAL: will be non-zero if the extension is marked as critical

     This function will return certificate's key usage, by reading the
     keyUsage X.509 extension (*********).  The key usage value will
     ORed values of the: ‘GNUTLS_KEY_DIGITAL_SIGNATURE’ ,
     ‘GNUTLS_KEY_NON_REPUDIATION’ , ‘GNUTLS_KEY_KEY_ENCIPHERMENT’ ,
     ‘GNUTLS_KEY_DATA_ENCIPHERMENT’ , ‘GNUTLS_KEY_KEY_AGREEMENT’ ,
     ‘GNUTLS_KEY_KEY_CERT_SIGN’ , ‘GNUTLS_KEY_CRL_SIGN’ ,
     ‘GNUTLS_KEY_ENCIPHER_ONLY’ , ‘GNUTLS_KEY_DECIPHER_ONLY’ .

     *Returns:* zero on success, or a negative error code in case of
     parsing error.  If the certificate does not contain the keyUsage
     extension ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

gnutls_x509_crt_get_name_constraints
------------------------------------

 -- Function: int gnutls_x509_crt_get_name_constraints
          (gnutls_x509_crt_t CRT, gnutls_x509_name_constraints_t NC,
          unsigned int FLAGS, unsigned int * CRITICAL)
     CRT: should contain a ‘gnutls_x509_crt_t’ type

     NC: The nameconstraints intermediate type

     FLAGS: zero or ‘GNUTLS_EXT_FLAG_APPEND’

     CRITICAL: the extension status

     This function will return an intermediate type containing the name
     constraints of the provided CA certificate.  That structure can be
     used in combination with ‘gnutls_x509_name_constraints_check()’ to
     verify whether a server's name is in accordance with the
     constraints.

     When the ‘flags’ is set to ‘GNUTLS_EXT_FLAG_APPEND’ , then if the
     ‘nc’ structure is empty this function will behave identically as if
     the flag was not set.  Otherwise if there are elements in the ‘nc’
     structure then the constraints will be merged with the existing
     constraints following RFC5280 p6.1.4 (excluded constraints will be
     appended, permitted will be intersected).

     Note that ‘nc’ must be initialized prior to calling this function.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the extension is not
     present, otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_crt_get_pk_algorithm
--------------------------------

 -- Function: int gnutls_x509_crt_get_pk_algorithm (gnutls_x509_crt_t
          CERT, unsigned int * BITS)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     BITS: if bits is non null it will hold the size of the parameters'
     in bits

     This function will return the public key algorithm of an X.509
     certificate.

     If bits is non null, it should have enough size to hold the
     parameters size in bits.  For RSA the bits returned is the modulus.
     For DSA the bits returned are of the public exponent.

     Unknown/unsupported algorithms are mapped to ‘GNUTLS_PK_UNKNOWN’ .

     *Returns:* a member of the ‘gnutls_pk_algorithm_t’ enumeration on
     success, or a negative error code on error.

gnutls_x509_crt_get_pk_dsa_raw
------------------------------

 -- Function: int gnutls_x509_crt_get_pk_dsa_raw (gnutls_x509_crt_t CRT,
          gnutls_datum_t * P, gnutls_datum_t * Q, gnutls_datum_t * G,
          gnutls_datum_t * Y)
     CRT: Holds the certificate

     P: will hold the p

     Q: will hold the q

     G: will hold the g

     Y: will hold the y

     This function will export the DSA public key's parameters found in
     the given certificate.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

gnutls_x509_crt_get_pk_ecc_raw
------------------------------

 -- Function: int gnutls_x509_crt_get_pk_ecc_raw (gnutls_x509_crt_t CRT,
          gnutls_ecc_curve_t * CURVE, gnutls_datum_t * X, gnutls_datum_t
          * Y)
     CRT: Holds the certificate

     CURVE: will hold the curve

     X: will hold the x-coordinate

     Y: will hold the y-coordinate

     This function will export the ECC public key's parameters found in
     the given certificate.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     In EdDSA curves the ‘y’ parameter will be ‘NULL’ and the other
     parameters will be in the native format for the curve.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.4.1

gnutls_x509_crt_get_pk_gost_raw
-------------------------------

 -- Function: int gnutls_x509_crt_get_pk_gost_raw (gnutls_x509_crt_t
          CRT, gnutls_ecc_curve_t * CURVE, gnutls_digest_algorithm_t *
          DIGEST, gnutls_gost_paramset_t * PARAMSET, gnutls_datum_t * X,
          gnutls_datum_t * Y)
     CRT: Holds the certificate

     CURVE: will hold the curve

     DIGEST: will hold the digest

     PARAMSET: will hold the GOST parameter set ID

     X: will hold the x-coordinate

     Y: will hold the y-coordinate

     This function will export the GOST public key's parameters found in
     the given certificate.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.6.3

gnutls_x509_crt_get_pk_oid
--------------------------

 -- Function: int gnutls_x509_crt_get_pk_oid (gnutls_x509_crt_t CERT,
          char * OID, size_t * OID_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     OID: a pointer to a buffer to hold the OID (may be null)

     OID_SIZE: initially holds the size of ‘oid’

     This function will return the OID of the public key algorithm on
     that certificate.  This is function is useful in the case
     ‘gnutls_x509_crt_get_pk_algorithm()’ returned ‘GNUTLS_PK_UNKNOWN’ .

     *Returns:* zero or a negative error code on error.

     *Since:* 3.5.0

gnutls_x509_crt_get_pk_rsa_raw
------------------------------

 -- Function: int gnutls_x509_crt_get_pk_rsa_raw (gnutls_x509_crt_t CRT,
          gnutls_datum_t * M, gnutls_datum_t * E)
     CRT: Holds the certificate

     M: will hold the modulus

     E: will hold the public exponent

     This function will export the RSA public key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

gnutls_x509_crt_get_policy
--------------------------

 -- Function: int gnutls_x509_crt_get_policy (gnutls_x509_crt_t CRT,
          unsigned INDX, struct gnutls_x509_policy_st * POLICY, unsigned
          int * CRITICAL)
     CRT: should contain a ‘gnutls_x509_crt_t’ type

     INDX: This specifies which policy to return.  Use (0) to get the
     first one.

     POLICY: A pointer to a policy structure.

     CRITICAL: will be non-zero if the extension is marked as critical

     This function will extract the certificate policy (extension
     *********) specified by the given index.

     The policy returned by this function must be deinitialized by using
     ‘gnutls_x509_policy_release()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the extension is not
     present, otherwise a negative error value.

     *Since:* 3.1.5

gnutls_x509_crt_get_private_key_usage_period
--------------------------------------------

 -- Function: int gnutls_x509_crt_get_private_key_usage_period
          (gnutls_x509_crt_t CERT, time_t * ACTIVATION, time_t *
          EXPIRATION, unsigned int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     ACTIVATION: The activation time

     EXPIRATION: The expiration time

     CRITICAL: the extension status

     This function will return the expiration and activation times of
     the private key of the certificate.  It relies on the PKIX
     extension ********* being present.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the extension is not
     present, otherwise a negative error value.

gnutls_x509_crt_get_proxy
-------------------------

 -- Function: int gnutls_x509_crt_get_proxy (gnutls_x509_crt_t CERT,
          unsigned int * CRITICAL, int * PATHLEN, char **
          POLICYLANGUAGE, char ** POLICY, size_t * SIZEOF_POLICY)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     CRITICAL: will be non-zero if the extension is marked as critical

     PATHLEN: pointer to output integer indicating path length (may be
     NULL), non-negative error codes indicate a present
     pCPathLenConstraint field and the actual value, -1 indicate that
     the field is absent.

     POLICYLANGUAGE: output variable with OID of policy language

     POLICY: output variable with policy data

     SIZEOF_POLICY: output variable size of policy data

     This function will get information from a proxy certificate.  It
     reads the ProxyCertInfo X.509 extension (*******.5.5.7.1.14).

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.

gnutls_x509_crt_get_raw_dn
--------------------------

 -- Function: int gnutls_x509_crt_get_raw_dn (gnutls_x509_crt_t CERT,
          gnutls_datum_t * DN)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     DN: will hold the starting point of the DN

     This function will return a pointer to the DER encoded DN structure
     and the length.  This points to allocated data that must be free'd
     using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  or a negative error code on
     error.

gnutls_x509_crt_get_raw_issuer_dn
---------------------------------

 -- Function: int gnutls_x509_crt_get_raw_issuer_dn (gnutls_x509_crt_t
          CERT, gnutls_datum_t * DN)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     DN: will hold the starting point of the DN

     This function will return a pointer to the DER encoded DN structure
     and the length.  This points to allocated data that must be free'd
     using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.or a negative error code on error.

gnutls_x509_crt_get_serial
--------------------------

 -- Function: int gnutls_x509_crt_get_serial (gnutls_x509_crt_t CERT,
          void * RESULT, size_t * RESULT_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     RESULT: The place where the serial number will be copied

     RESULT_SIZE: Holds the size of the result field.

     This function will return the X.509 certificate's serial number.
     This is obtained by the X509 Certificate serialNumber field.
     Serial is not always a 32 or 64bit number.  Some CAs use large
     serial numbers, thus it may be wise to handle it as something
     uint8_t.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_get_signature
-----------------------------

 -- Function: int gnutls_x509_crt_get_signature (gnutls_x509_crt_t CERT,
          char * SIG, size_t * SIG_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     SIG: a pointer where the signature part will be copied (may be
     null).

     SIG_SIZE: initially holds the size of ‘sig’

     This function will extract the signature field of a certificate.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_get_signature_algorithm
---------------------------------------

 -- Function: int gnutls_x509_crt_get_signature_algorithm
          (gnutls_x509_crt_t CERT)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     This function will return a value of the ‘gnutls_sign_algorithm_t’
     enumeration that is the signature algorithm that has been used to
     sign this certificate.

     Since 3.6.0 this function never returns a negative error code.
     Error cases and unknown/unsupported signature algorithms are mapped
     to ‘GNUTLS_SIGN_UNKNOWN’ .

     *Returns:* a ‘gnutls_sign_algorithm_t’ value

gnutls_x509_crt_get_signature_oid
---------------------------------

 -- Function: int gnutls_x509_crt_get_signature_oid (gnutls_x509_crt_t
          CERT, char * OID, size_t * OID_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     OID: a pointer to a buffer to hold the OID (may be null)

     OID_SIZE: initially holds the size of ‘oid’

     This function will return the OID of the signature algorithm that
     has been used to sign this certificate.  This is function is useful
     in the case ‘gnutls_x509_crt_get_signature_algorithm()’ returned
     ‘GNUTLS_SIGN_UNKNOWN’ .

     *Returns:* zero or a negative error code on error.

     *Since:* 3.5.0

gnutls_x509_crt_get_spki
------------------------

 -- Function: int gnutls_x509_crt_get_spki (gnutls_x509_crt_t CERT,
          gnutls_x509_spki_t SPKI, unsigned int FLAGS)
     CERT: a certificate of type ‘gnutls_x509_crt_t’

     SPKI: a SubjectPublicKeyInfo structure of type ‘gnutls_x509_spki_t’

     FLAGS: must be zero

     This function will return the public key information of an X.509
     certificate.  The provided ‘spki’ must be initialized.

     *Since:* 3.6.0

gnutls_x509_crt_get_subject
---------------------------

 -- Function: int gnutls_x509_crt_get_subject (gnutls_x509_crt_t CERT,
          gnutls_x509_dn_t * DN)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     DN: output variable with pointer to uint8_t DN.

     Return the Certificate's Subject DN as a ‘gnutls_x509_dn_t’ data
     type, that can be decoded using ‘gnutls_x509_dn_get_rdn_ava()’ .

     Note that ‘dn’ should be treated as constant.  Because it points
     into the ‘cert’ object, you should not use ‘dn’ after ‘cert’ is
     deallocated.

     *Returns:* Returns 0 on success, or an error code.

gnutls_x509_crt_get_subject_alt_name
------------------------------------

 -- Function: int gnutls_x509_crt_get_subject_alt_name
          (gnutls_x509_crt_t CERT, unsigned int SEQ, void * SAN, size_t
          * SAN_SIZE, unsigned int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     SEQ: specifies the sequence number of the alt name (0 for the first
     one, 1 for the second etc.)

     SAN: is the place where the alternative name will be copied to

     SAN_SIZE: holds the size of san.

     CRITICAL: will be non-zero if the extension is marked as critical
     (may be null)

     This function retrieves the Alternative Name (2.5.29.17), contained
     in the given certificate in the X509v3 Certificate Extensions.

     When the SAN type is otherName, it will extract the data in the
     otherName's value field, and ‘GNUTLS_SAN_OTHERNAME’ is returned.
     You may use ‘gnutls_x509_crt_get_subject_alt_othername_oid()’ to
     get the corresponding OID and the "virtual" SAN types (e.g.,
     ‘GNUTLS_SAN_OTHERNAME_XMPP’ ).

     If an otherName OID is known, the data will be decoded.  Otherwise
     the returned data will be DER encoded, and you will have to decode
     it yourself.  Currently, only the RFC 3920 id-on-xmppAddr SAN is
     recognized.

     *Returns:* the alternative subject name type on success, one of the
     enumerated ‘gnutls_x509_subject_alt_name_t’ .  It will return
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if ‘san_size’ is not large enough to
     hold the value.  In that case ‘san_size’ will be updated with the
     required size.  If the certificate does not have an Alternative
     name with the specified sequence number then
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.

gnutls_x509_crt_get_subject_alt_name2
-------------------------------------

 -- Function: int gnutls_x509_crt_get_subject_alt_name2
          (gnutls_x509_crt_t CERT, unsigned int SEQ, void * SAN, size_t
          * SAN_SIZE, unsigned int * SAN_TYPE, unsigned int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     SEQ: specifies the sequence number of the alt name (0 for the first
     one, 1 for the second etc.)

     SAN: is the place where the alternative name will be copied to

     SAN_SIZE: holds the size of ret.

     SAN_TYPE: holds the type of the alternative name (one of
     gnutls_x509_subject_alt_name_t).

     CRITICAL: will be non-zero if the extension is marked as critical
     (may be null)

     This function will return the alternative names, contained in the
     given certificate.  It is the same as
     ‘gnutls_x509_crt_get_subject_alt_name()’ except for the fact that
     it will return the type of the alternative name in ‘san_type’ even
     if the function fails for some reason (i.e.  the buffer provided is
     not enough).

     *Returns:* the alternative subject name type on success, one of the
     enumerated ‘gnutls_x509_subject_alt_name_t’ .  It will return
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if ‘san_size’ is not large enough to
     hold the value.  In that case ‘san_size’ will be updated with the
     required size.  If the certificate does not have an Alternative
     name with the specified sequence number then
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.

gnutls_x509_crt_get_subject_alt_othername_oid
---------------------------------------------

 -- Function: int gnutls_x509_crt_get_subject_alt_othername_oid
          (gnutls_x509_crt_t CERT, unsigned int SEQ, void * OID, size_t
          * OID_SIZE)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     SEQ: specifies the sequence number of the alt name (0 for the first
     one, 1 for the second etc.)

     OID: is the place where the otherName OID will be copied to

     OID_SIZE: holds the size of ret.

     This function will extract the type OID of an otherName Subject
     Alternative Name, contained in the given certificate, and return
     the type as an enumerated element.

     This function is only useful if
     ‘gnutls_x509_crt_get_subject_alt_name()’ returned
     ‘GNUTLS_SAN_OTHERNAME’ .

     If ‘oid’ is null then only the size will be filled.  The ‘oid’
     returned will be null terminated, although ‘oid_size’ will not
     account for the trailing null.

     *Returns:* the alternative subject name type on success, one of the
     enumerated gnutls_x509_subject_alt_name_t.  For supported OIDs, it
     will return one of the virtual (GNUTLS_SAN_OTHERNAME_*) types, e.g.
     ‘GNUTLS_SAN_OTHERNAME_XMPP’ , and ‘GNUTLS_SAN_OTHERNAME’ for
     unknown OIDs.  It will return ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ if
     ‘ian_size’ is not large enough to hold the value.  In that case
     ‘ian_size’ will be updated with the required size.  If the
     certificate does not have an Alternative name with the specified
     sequence number and with the otherName type then
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.

gnutls_x509_crt_get_subject_key_id
----------------------------------

 -- Function: int gnutls_x509_crt_get_subject_key_id (gnutls_x509_crt_t
          CERT, void * RET, size_t * RET_SIZE, unsigned int * CRITICAL)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     RET: The place where the identifier will be copied

     RET_SIZE: Holds the size of the result field.

     CRITICAL: will be non-zero if the extension is marked as critical
     (may be null)

     This function will return the X.509v3 certificate's subject key
     identifier.  This is obtained by the X.509 Subject Key identifier
     extension field (2.5.29.14).

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the extension is not
     present, otherwise a negative error value.

gnutls_x509_crt_get_subject_unique_id
-------------------------------------

 -- Function: int gnutls_x509_crt_get_subject_unique_id
          (gnutls_x509_crt_t CRT, char * BUF, size_t * BUF_SIZE)
     CRT: Holds the certificate

     BUF: user allocated memory buffer, will hold the unique id

     BUF_SIZE: size of user allocated memory buffer (on input), will
     hold actual size of the unique ID on return.

     This function will extract the subjectUniqueID value (if present)
     for the given certificate.

     If the user allocated memory buffer is not large enough to hold the
     full subjectUniqueID, then a GNUTLS_E_SHORT_MEMORY_BUFFER error
     will be returned, and buf_size will be set to the actual length.

     This function had a bug prior to 3.4.8 that prevented the setting
     of ‘NULL’ ‘buf’ to discover the ‘buf_size’ .  To use this function
     safely with the older versions the ‘buf’ must be a valid buffer
     that can hold at least a single byte if ‘buf_size’ is zero.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

gnutls_x509_crt_get_tlsfeatures
-------------------------------

 -- Function: int gnutls_x509_crt_get_tlsfeatures (gnutls_x509_crt_t
          CRT, gnutls_x509_tlsfeatures_t FEATURES, unsigned int FLAGS,
          unsigned int * CRITICAL)
     CRT: A X.509 certificate

     FEATURES: If the function succeeds, the features will be stored in
     this variable.

     FLAGS: zero or ‘GNUTLS_EXT_FLAG_APPEND’

     CRITICAL: the extension status

     This function will get the X.509 TLS features extension structure
     from the certificate.  The returned structure needs to be freed
     using ‘gnutls_x509_tlsfeatures_deinit()’ .

     When the ‘flags’ is set to ‘GNUTLS_EXT_FLAG_APPEND’ , then if the
     ‘features’ structure is empty this function will behave identically
     as if the flag was not set.  Otherwise if there are elements in the
     ‘features’ structure then they will be merged with.

     Note that ‘features’ must be initialized prior to calling this
     function.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.1

gnutls_x509_crt_get_version
---------------------------

 -- Function: int gnutls_x509_crt_get_version (gnutls_x509_crt_t CERT)
     CERT: should contain a ‘gnutls_x509_crt_t’ type

     This function will return the version of the specified Certificate.

     *Returns:* version of certificate, or a negative error code on
     error.

gnutls_x509_crt_import
----------------------

 -- Function: int gnutls_x509_crt_import (gnutls_x509_crt_t CERT, const
          gnutls_datum_t * DATA, gnutls_x509_crt_fmt_t FORMAT)
     CERT: The data to store the parsed certificate.

     DATA: The DER or PEM encoded certificate.

     FORMAT: One of DER or PEM

     This function will convert the given DER or PEM encoded Certificate
     to the native gnutls_x509_crt_t format.  The output will be stored
     in ‘cert’ .

     If the Certificate is PEM encoded it should have a header of "X509
     CERTIFICATE", or "CERTIFICATE".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_import_url
--------------------------

 -- Function: int gnutls_x509_crt_import_url (gnutls_x509_crt_t CRT,
          const char * URL, unsigned int FLAGS)
     CRT: A certificate of type ‘gnutls_x509_crt_t’

     URL: A PKCS 11 url

     FLAGS: One of GNUTLS_PKCS11_OBJ_* flags for PKCS‘11’ URLs or zero
     otherwise

     This function will import a certificate present in a PKCS‘11’ token
     or any type of back-end that supports URLs.

     In previous versions of gnutls this function was named
     gnutls_x509_crt_import_pkcs11_url, and the old name is an alias to
     this one.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_x509_crt_init
--------------------

 -- Function: int gnutls_x509_crt_init (gnutls_x509_crt_t * CERT)
     CERT: A pointer to the type to be initialized

     This function will initialize an X.509 certificate structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_list_import
---------------------------

 -- Function: int gnutls_x509_crt_list_import (gnutls_x509_crt_t *
          CERTS, unsigned int * CERT_MAX, const gnutls_datum_t * DATA,
          gnutls_x509_crt_fmt_t FORMAT, unsigned int FLAGS)
     CERTS: Indicates where the parsed list will be copied to.  Must not
     be initialized.

     CERT_MAX: Initially must hold the maximum number of certs.  It will
     be updated with the number of certs available.

     DATA: The PEM encoded certificate.

     FORMAT: One of DER or PEM.

     FLAGS: must be (0) or an OR'd sequence of
     gnutls_certificate_import_flags.

     This function will convert the given PEM encoded certificate list
     to the native gnutls_x509_crt_t format.  The output will be stored
     in ‘certs’ .  They will be automatically initialized.

     The flag ‘GNUTLS_X509_CRT_LIST_IMPORT_FAIL_IF_EXCEED’ will cause
     import to fail if the certificates in the provided buffer are more
     than the available structures.  The
     ‘GNUTLS_X509_CRT_LIST_FAIL_IF_UNSORTED’ flag will cause the
     function to fail if the provided list is not sorted from subject to
     issuer.

     If the Certificate is PEM encoded it should have a header of "X509
     CERTIFICATE", or "CERTIFICATE".

     *Returns:* the number of certificates read or a negative error
     value.

gnutls_x509_crt_list_import2
----------------------------

 -- Function: int gnutls_x509_crt_list_import2 (gnutls_x509_crt_t **
          CERTS, unsigned int * SIZE, const gnutls_datum_t * DATA,
          gnutls_x509_crt_fmt_t FORMAT, unsigned int FLAGS)
     CERTS: Will hold the parsed certificate list.

     SIZE: It will contain the size of the list.

     DATA: The PEM encoded certificate.

     FORMAT: One of DER or PEM.

     FLAGS: must be (0) or an OR'd sequence of
     gnutls_certificate_import_flags.

     This function will convert the given PEM encoded certificate list
     to the native gnutls_x509_crt_t format.  The output will be stored
     in ‘certs’ which will be allocated and initialized.

     If the Certificate is PEM encoded it should have a header of "X509
     CERTIFICATE", or "CERTIFICATE".

     To deinitialize ‘certs’ , you need to deinitialize each crt
     structure independently, and use ‘gnutls_free()’ at ‘certs’ .

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.0

gnutls_x509_crt_list_import_url
-------------------------------

 -- Function: int gnutls_x509_crt_list_import_url (gnutls_x509_crt_t **
          CERTS, unsigned int * SIZE, const char * URL,
          gnutls_pin_callback_t PIN_FN, void * PIN_FN_USERDATA, unsigned
          int FLAGS)
     CERTS: Will hold the allocated certificate list.

     SIZE: It will contain the size of the list.

     URL: A PKCS 11 url

     PIN_FN: a PIN callback if not globally set

     PIN_FN_USERDATA: parameter for the PIN callback

     FLAGS: One of GNUTLS_PKCS11_OBJ_* flags for PKCS‘11’ URLs or zero
     otherwise

     This function will import a certificate chain present in a PKCS‘11’
     token or any type of back-end that supports URLs.  The certificates
     must be deinitialized afterwards using ‘gnutls_x509_crt_deinit()’
     and the returned pointer must be freed using ‘gnutls_free()’ .

     The URI provided must be the first certificate in the chain;
     subsequent certificates will be retrieved using
     ‘gnutls_pkcs11_get_raw_issuer()’ or equivalent functionality for
     the supported URI.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.3

gnutls_x509_crt_list_verify
---------------------------

 -- Function: int gnutls_x509_crt_list_verify (const gnutls_x509_crt_t *
          CERT_LIST, unsigned CERT_LIST_LENGTH, const gnutls_x509_crt_t
          * CA_LIST, unsigned CA_LIST_LENGTH, const gnutls_x509_crl_t *
          CRL_LIST, unsigned CRL_LIST_LENGTH, unsigned int FLAGS,
          unsigned int * VERIFY)
     CERT_LIST: is the certificate list to be verified

     CERT_LIST_LENGTH: holds the number of certificate in cert_list

     CA_LIST: is the CA list which will be used in verification

     CA_LIST_LENGTH: holds the number of CA certificate in CA_list

     CRL_LIST: holds a list of CRLs.

     CRL_LIST_LENGTH: the length of CRL list.

     FLAGS: Flags that may be used to change the verification algorithm.
     Use OR of the gnutls_certificate_verify_flags enumerations.

     VERIFY: will hold the certificate verification output.

     This function will try to verify the given certificate list and
     return its status.  The details of the verification are the same as
     in ‘gnutls_x509_trust_list_verify_crt2()’ .

     You must check the peer's name in order to check if the verified
     certificate belongs to the actual peer.

     The certificate verification output will be put in ‘verify’ and
     will be one or more of the gnutls_certificate_status_t enumerated
     elements bitwise or'd.  For a more detailed verification status use
     ‘gnutls_x509_crt_verify()’ per list element.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_print
---------------------

 -- Function: int gnutls_x509_crt_print (gnutls_x509_crt_t CERT,
          gnutls_certificate_print_formats_t FORMAT, gnutls_datum_t *
          OUT)
     CERT: The data to be printed

     FORMAT: Indicate the format to use

     OUT: Newly allocated datum with null terminated string.

     This function will pretty print a X.509 certificate, suitable for
     display to a human.

     If the format is ‘GNUTLS_CRT_PRINT_FULL’ then all fields of the
     certificate will be output, on multiple lines.  The
     ‘GNUTLS_CRT_PRINT_ONELINE’ format will generate one line with some
     selected fields, which is useful for logging purposes.

     The output ‘out’ needs to be deallocated using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_activation_time
-----------------------------------

 -- Function: int gnutls_x509_crt_set_activation_time (gnutls_x509_crt_t
          CERT, time_t ACT_TIME)
     CERT: a certificate of type ‘gnutls_x509_crt_t’

     ACT_TIME: The actual time

     This function will set the time this certificate was or will be
     activated.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_authority_info_access
-----------------------------------------

 -- Function: int gnutls_x509_crt_set_authority_info_access
          (gnutls_x509_crt_t CRT, int WHAT, gnutls_datum_t * DATA)
     CRT: Holds the certificate

     WHAT: what data to get, a ‘gnutls_info_access_what_t’ type.

     DATA: output data to be freed with ‘gnutls_free()’ .

     This function sets the Authority Information Access (AIA)
     extension, see RFC 5280 section ******* for more information.

     The type of data stored in ‘data’ is specified via ‘what’ which
     should be ‘gnutls_info_access_what_t’ values.

     If ‘what’ is ‘GNUTLS_IA_OCSP_URI’ , ‘data’ will hold the OCSP URI.
     If ‘what’ is ‘GNUTLS_IA_CAISSUERS_URI’ , ‘data’ will hold the
     caIssuers URI.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_x509_crt_set_authority_key_id
------------------------------------

 -- Function: int gnutls_x509_crt_set_authority_key_id
          (gnutls_x509_crt_t CERT, const void * ID, size_t ID_SIZE)
     CERT: a certificate of type ‘gnutls_x509_crt_t’

     ID: The key ID

     ID_SIZE: Holds the size of the key ID field.

     This function will set the X.509 certificate's authority key ID
     extension.  Only the keyIdentifier field can be set with this
     function.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_basic_constraints
-------------------------------------

 -- Function: int gnutls_x509_crt_set_basic_constraints
          (gnutls_x509_crt_t CRT, unsigned int CA, int
          PATHLENCONSTRAINT)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     CA: true(1) or false(0).  Depending on the Certificate authority
     status.

     PATHLENCONSTRAINT: non-negative error codes indicate maximum length
     of path, and negative error codes indicate that the
     pathLenConstraints field should not be present.

     This function will set the basicConstraints certificate extension.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_ca_status
-----------------------------

 -- Function: int gnutls_x509_crt_set_ca_status (gnutls_x509_crt_t CRT,
          unsigned int CA)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     CA: true(1) or false(0).  Depending on the Certificate authority
     status.

     This function will set the basicConstraints certificate extension.
     Use ‘gnutls_x509_crt_set_basic_constraints()’ if you want to
     control the pathLenConstraint field too.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_crl_dist_points
-----------------------------------

 -- Function: int gnutls_x509_crt_set_crl_dist_points (gnutls_x509_crt_t
          CRT, gnutls_x509_subject_alt_name_t TYPE, const void *
          DATA_STRING, unsigned int REASON_FLAGS)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     TYPE: is one of the gnutls_x509_subject_alt_name_t enumerations

     DATA_STRING: The data to be set

     REASON_FLAGS: revocation reasons

     This function will set the CRL distribution points certificate
     extension.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_crl_dist_points2
------------------------------------

 -- Function: int gnutls_x509_crt_set_crl_dist_points2
          (gnutls_x509_crt_t CRT, gnutls_x509_subject_alt_name_t TYPE,
          const void * DATA, unsigned int DATA_SIZE, unsigned int
          REASON_FLAGS)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     TYPE: is one of the gnutls_x509_subject_alt_name_t enumerations

     DATA: The data to be set

     DATA_SIZE: The data size

     REASON_FLAGS: revocation reasons

     This function will set the CRL distribution points certificate
     extension.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.6.0

gnutls_x509_crt_set_crq
-----------------------

 -- Function: int gnutls_x509_crt_set_crq (gnutls_x509_crt_t CRT,
          gnutls_x509_crq_t CRQ)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     CRQ: holds a certificate request

     This function will set the name and public parameters as well as
     the extensions from the given certificate request to the
     certificate.  Only RSA keys are currently supported.

     Note that this function will only set the ‘crq’ if it is self
     signed and the signature is correct.  See ‘gnutls_x509_crq_sign2()’
     .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_crq_extension_by_oid
----------------------------------------

 -- Function: int gnutls_x509_crt_set_crq_extension_by_oid
          (gnutls_x509_crt_t CRT, gnutls_x509_crq_t CRQ, const char *
          OID, unsigned FLAGS)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     CRQ: holds a certificate request

     OID: the object identifier of the OID to copy

     FLAGS: should be zero

     This function will set the extension specify by ‘oid’ from the
     given request to the certificate.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.1

gnutls_x509_crt_set_crq_extensions
----------------------------------

 -- Function: int gnutls_x509_crt_set_crq_extensions (gnutls_x509_crt_t
          CRT, gnutls_x509_crq_t CRQ)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     CRQ: holds a certificate request

     This function will set the extensions from the given request to the
     certificate.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.8.0

gnutls_x509_crt_set_dn
----------------------

 -- Function: int gnutls_x509_crt_set_dn (gnutls_x509_crt_t CRT, const
          char * DN, const char ** ERR)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     DN: a comma separated DN string (RFC4514)

     ERR: indicates the error position (if any)

     This function will set the DN on the provided certificate.  The
     input string should be plain ASCII or UTF-8 encoded.  On DN parsing
     error ‘GNUTLS_E_PARSING_ERROR’ is returned.

     Note that DNs are not expected to hold DNS information, and thus no
     automatic IDNA conversions are attempted when using this function.
     If that is required (e.g., store a domain in CN), process the
     corresponding input with ‘gnutls_idna_map()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_dn_by_oid
-----------------------------

 -- Function: int gnutls_x509_crt_set_dn_by_oid (gnutls_x509_crt_t CRT,
          const char * OID, unsigned int RAW_FLAG, const void * NAME,
          unsigned int SIZEOF_NAME)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     OID: holds an Object Identifier in a null terminated string

     RAW_FLAG: must be 0, or 1 if the data are DER encoded

     NAME: a pointer to the name

     SIZEOF_NAME: holds the size of ‘name’

     This function will set the part of the name of the Certificate
     subject, specified by the given OID. The input string should be
     ASCII or UTF-8 encoded.

     Some helper macros with popular OIDs can be found in gnutls/x509.h
     With this function you can only set the known OIDs.  You can test
     for known OIDs using ‘gnutls_x509_dn_oid_known()’ .  For OIDs that
     are not known (by gnutls) you should properly DER encode your data,
     and call this function with ‘raw_flag’ set.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_expiration_time
-----------------------------------

 -- Function: int gnutls_x509_crt_set_expiration_time (gnutls_x509_crt_t
          CERT, time_t EXP_TIME)
     CERT: a certificate of type ‘gnutls_x509_crt_t’

     EXP_TIME: The actual time

     This function will set the time this Certificate will expire.
     Setting an expiration time to (time_t)-1 will set to the no
     well-defined expiration date value.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_extension_by_oid
------------------------------------

 -- Function: int gnutls_x509_crt_set_extension_by_oid
          (gnutls_x509_crt_t CRT, const char * OID, const void * BUF,
          size_t SIZEOF_BUF, unsigned int CRITICAL)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     OID: holds an Object Identifier in null terminated string

     BUF: a pointer to a DER encoded data

     SIZEOF_BUF: holds the size of ‘buf’

     CRITICAL: should be non-zero if the extension is to be marked as
     critical

     This function will set an the extension, by the specified OID, in
     the certificate.  The extension data should be binary data DER
     encoded.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_flags
-------------------------

 -- Function: void gnutls_x509_crt_set_flags (gnutls_x509_crt_t CERT,
          unsigned int FLAGS)
     CERT: A type ‘gnutls_x509_crt_t’

     FLAGS: flags from the ‘gnutls_x509_crt_flags’

     This function will set flags for the specified certificate.
     Currently this is useful for the
     ‘GNUTLS_X509_CRT_FLAG_IGNORE_SANITY’ which allows importing
     certificates even if they have known issues.

     *Since:* 3.6.0

gnutls_x509_crt_set_inhibit_anypolicy
-------------------------------------

 -- Function: int gnutls_x509_crt_set_inhibit_anypolicy
          (gnutls_x509_crt_t CRT, unsigned int SKIPCERTS)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     SKIPCERTS: number of certificates after which anypolicy is no
     longer acceptable.

     This function will set the Inhibit anyPolicy certificate extension.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_issuer_alt_name
-----------------------------------

 -- Function: int gnutls_x509_crt_set_issuer_alt_name (gnutls_x509_crt_t
          CRT, gnutls_x509_subject_alt_name_t TYPE, const void * DATA,
          unsigned int DATA_SIZE, unsigned int FLAGS)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     TYPE: is one of the gnutls_x509_subject_alt_name_t enumerations

     DATA: The data to be set

     DATA_SIZE: The size of data to be set

     FLAGS: GNUTLS_FSAN_SET to clear previous data or GNUTLS_FSAN_APPEND
     to append.

     This function will set the issuer alternative name certificate
     extension.  It can set the same types as
     ‘gnutls_x509_crt_set_subject_alt_name()’ .

     Since version 3.5.7 the ‘GNUTLS_SAN_RFC822NAME’ ,
     ‘GNUTLS_SAN_DNSNAME’ , and ‘GNUTLS_SAN_OTHERNAME_XMPP’ are
     converted to ACE format when necessary.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_crt_set_issuer_alt_othername
----------------------------------------

 -- Function: int gnutls_x509_crt_set_issuer_alt_othername
          (gnutls_x509_crt_t CRT, const char * OID, const void * DATA,
          unsigned int DATA_SIZE, unsigned int FLAGS)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     OID: The other name OID

     DATA: The data to be set

     DATA_SIZE: The size of data to be set

     FLAGS: GNUTLS_FSAN_SET to clear previous data or GNUTLS_FSAN_APPEND
     to append.

     This function will set an "othername" to the issuer alternative
     name certificate extension.

     The values set are set as binary values and are expected to have
     the proper DER encoding.  For convenience the flags
     ‘GNUTLS_FSAN_ENCODE_OCTET_STRING’ and
     ‘GNUTLS_FSAN_ENCODE_UTF8_STRING’ can be used to encode the provided
     data.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.0

gnutls_x509_crt_set_issuer_dn
-----------------------------

 -- Function: int gnutls_x509_crt_set_issuer_dn (gnutls_x509_crt_t CRT,
          const char * DN, const char ** ERR)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     DN: a comma separated DN string (RFC4514)

     ERR: indicates the error position (if any)

     This function will set the DN on the provided certificate.  The
     input string should be plain ASCII or UTF-8 encoded.  On DN parsing
     error ‘GNUTLS_E_PARSING_ERROR’ is returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_issuer_dn_by_oid
------------------------------------

 -- Function: int gnutls_x509_crt_set_issuer_dn_by_oid
          (gnutls_x509_crt_t CRT, const char * OID, unsigned int
          RAW_FLAG, const void * NAME, unsigned int SIZEOF_NAME)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     OID: holds an Object Identifier in a null terminated string

     RAW_FLAG: must be 0, or 1 if the data are DER encoded

     NAME: a pointer to the name

     SIZEOF_NAME: holds the size of ‘name’

     This function will set the part of the name of the Certificate
     issuer, specified by the given OID. The input string should be
     ASCII or UTF-8 encoded.

     Some helper macros with popular OIDs can be found in gnutls/x509.h
     With this function you can only set the known OIDs.  You can test
     for known OIDs using ‘gnutls_x509_dn_oid_known()’ .  For OIDs that
     are not known (by gnutls) you should properly DER encode your data,
     and call this function with ‘raw_flag’ set.

     Normally you do not need to call this function, since the signing
     operation will copy the signer's name as the issuer of the
     certificate.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_issuer_unique_id
------------------------------------

 -- Function: int gnutls_x509_crt_set_issuer_unique_id
          (gnutls_x509_crt_t CERT, const void * ID, size_t ID_SIZE)
     CERT: a certificate of type ‘gnutls_x509_crt_t’

     ID: The unique ID

     ID_SIZE: Holds the size of the unique ID.

     This function will set the X.509 certificate's issuer unique ID
     field.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.7

gnutls_x509_crt_set_key
-----------------------

 -- Function: int gnutls_x509_crt_set_key (gnutls_x509_crt_t CRT,
          gnutls_x509_privkey_t KEY)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     KEY: holds a private key

     This function will set the public parameters from the given private
     key to the certificate.

     To export the public key (i.e., the SubjectPublicKeyInfo part),
     check ‘gnutls_pubkey_import_x509()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_key_purpose_oid
-----------------------------------

 -- Function: int gnutls_x509_crt_set_key_purpose_oid (gnutls_x509_crt_t
          CERT, const void * OID, unsigned int CRITICAL)
     CERT: a certificate of type ‘gnutls_x509_crt_t’

     OID: a pointer to a null terminated string that holds the OID

     CRITICAL: Whether this extension will be critical or not

     This function will set the key purpose OIDs of the Certificate.
     These are stored in the Extended Key Usage extension (*********)
     See the GNUTLS_KP_* definitions for human readable names.

     Subsequent calls to this function will append OIDs to the OID list.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.

gnutls_x509_crt_set_key_usage
-----------------------------

 -- Function: int gnutls_x509_crt_set_key_usage (gnutls_x509_crt_t CRT,
          unsigned int USAGE)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     USAGE: an ORed sequence of the GNUTLS_KEY_* elements.

     This function will set the keyUsage certificate extension.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_name_constraints
------------------------------------

 -- Function: int gnutls_x509_crt_set_name_constraints
          (gnutls_x509_crt_t CRT, gnutls_x509_name_constraints_t NC,
          unsigned int CRITICAL)
     CRT: The certificate

     NC: The nameconstraints structure

     CRITICAL: whether this extension will be critical

     This function will set the provided name constraints to the
     certificate extension list.  This extension is always marked as
     critical.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_crt_set_pin_function
--------------------------------

 -- Function: void gnutls_x509_crt_set_pin_function (gnutls_x509_crt_t
          CRT, gnutls_pin_callback_t FN, void * USERDATA)
     CRT: The certificate structure

     FN: the callback

     USERDATA: data associated with the callback

     This function will set a callback function to be used when it is
     required to access a protected object.  This function overrides the
     global function set using ‘gnutls_pkcs11_set_pin_function()’ .

     Note that this callback is currently used only during the import of
     a PKCS ‘11’ certificate with ‘gnutls_x509_crt_import_url()’ .

     *Since:* 3.1.0

gnutls_x509_crt_set_policy
--------------------------

 -- Function: int gnutls_x509_crt_set_policy (gnutls_x509_crt_t CRT,
          const struct gnutls_x509_policy_st * POLICY, unsigned int
          CRITICAL)
     CRT: should contain a ‘gnutls_x509_crt_t’ type

     POLICY: A pointer to a policy

     CRITICAL: use non-zero if the extension is marked as critical

     This function will set the certificate policy extension
     (*********).  Multiple calls to this function append a new policy.

     Note the maximum text size for the qualifier
     ‘GNUTLS_X509_QUALIFIER_NOTICE’ is 200 characters.  This function
     will fail with ‘GNUTLS_E_INVALID_REQUEST’ if this is exceeded.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.5

gnutls_x509_crt_set_private_key_usage_period
--------------------------------------------

 -- Function: int gnutls_x509_crt_set_private_key_usage_period
          (gnutls_x509_crt_t CRT, time_t ACTIVATION, time_t EXPIRATION)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     ACTIVATION: The activation time

     EXPIRATION: The expiration time

     This function will set the private key usage period extension
     (*********).

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_proxy
-------------------------

 -- Function: int gnutls_x509_crt_set_proxy (gnutls_x509_crt_t CRT, int
          PATHLENCONSTRAINT, const char * POLICYLANGUAGE, const char *
          POLICY, size_t SIZEOF_POLICY)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     PATHLENCONSTRAINT: non-negative error codes indicate maximum length
     of path, and negative error codes indicate that the
     pathLenConstraints field should not be present.

     POLICYLANGUAGE: OID describing the language of ‘policy’ .

     POLICY: uint8_t byte array with policy language, can be ‘NULL’

     SIZEOF_POLICY: size of ‘policy’ .

     This function will set the proxyCertInfo extension.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_proxy_dn
----------------------------

 -- Function: int gnutls_x509_crt_set_proxy_dn (gnutls_x509_crt_t CRT,
          gnutls_x509_crt_t EECRT, unsigned int RAW_FLAG, const void *
          NAME, unsigned int SIZEOF_NAME)
     CRT: a gnutls_x509_crt_t type with the new proxy cert

     EECRT: the end entity certificate that will be issuing the proxy

     RAW_FLAG: must be 0, or 1 if the CN is DER encoded

     NAME: a pointer to the CN name, may be NULL (but MUST then be added
     later)

     SIZEOF_NAME: holds the size of ‘name’

     This function will set the subject in ‘crt’ to the end entity's
     ‘eecrt’ subject name, and add a single Common Name component ‘name’
     of size ‘sizeof_name’ .  This corresponds to the required proxy
     certificate naming style.  Note that if ‘name’ is ‘NULL’ , you MUST
     set it later by using ‘gnutls_x509_crt_set_dn_by_oid()’ or similar.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_serial
--------------------------

 -- Function: int gnutls_x509_crt_set_serial (gnutls_x509_crt_t CERT,
          const void * SERIAL, size_t SERIAL_SIZE)
     CERT: a certificate of type ‘gnutls_x509_crt_t’

     SERIAL: The serial number

     SERIAL_SIZE: Holds the size of the serial field.

     This function will set the X.509 certificate's serial number.
     While the serial number is an integer, it is often handled as an
     opaque field by several CAs.  For this reason this function accepts
     any kind of data as a serial number.  To be consistent with the
     X.509/PKIX specifications the provided ‘serial’ should be a
     big-endian positive number (i.e.  its leftmost bit should be zero).

     The size of the serial is restricted to 20 bytes maximum by
     RFC5280.  This function allows writing more than 20 bytes but the
     generated certificates in that case may be rejected by other
     implementations.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_spki
------------------------

 -- Function: int gnutls_x509_crt_set_spki (gnutls_x509_crt_t CRT, const
          gnutls_x509_spki_t SPKI, unsigned int FLAGS)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     SPKI: a SubjectPublicKeyInfo structure of type ‘gnutls_x509_spki_t’

     FLAGS: must be zero

     This function will set the certificate's subject public key
     information explicitly.  This is intended to be used in the cases
     where a single public key (e.g., RSA) can be used for multiple
     signature algorithms (RSA PKCS1-1.5, and RSA-PSS).

     To export the public key (i.e., the SubjectPublicKeyInfo part),
     check ‘gnutls_pubkey_import_x509()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.0

gnutls_x509_crt_set_subject_alt_name
------------------------------------

 -- Function: int gnutls_x509_crt_set_subject_alt_name
          (gnutls_x509_crt_t CRT, gnutls_x509_subject_alt_name_t TYPE,
          const void * DATA, unsigned int DATA_SIZE, unsigned int FLAGS)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     TYPE: is one of the gnutls_x509_subject_alt_name_t enumerations

     DATA: The data to be set

     DATA_SIZE: The size of data to be set

     FLAGS: GNUTLS_FSAN_SET to clear previous data or GNUTLS_FSAN_APPEND
     to append.

     This function will set the subject alternative name certificate
     extension.  It can set the following types: ‘GNUTLS_SAN_DNSNAME’ as
     a text string, ‘GNUTLS_SAN_RFC822NAME’ as a text string,
     ‘GNUTLS_SAN_URI’ as a text string, ‘GNUTLS_SAN_IPADDRESS’ as a
     binary IP address (4 or 16 bytes), ‘GNUTLS_SAN_OTHERNAME_XMPP’ as a
     UTF8 string (since 3.5.0).

     Since version 3.5.7 the ‘GNUTLS_SAN_RFC822NAME’ ,
     ‘GNUTLS_SAN_DNSNAME’ , and ‘GNUTLS_SAN_OTHERNAME_XMPP’ are
     converted to ACE format when necessary.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.6.0

gnutls_x509_crt_set_subject_alt_othername
-----------------------------------------

 -- Function: int gnutls_x509_crt_set_subject_alt_othername
          (gnutls_x509_crt_t CRT, const char * OID, const void * DATA,
          unsigned int DATA_SIZE, unsigned int FLAGS)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     OID: The other name OID

     DATA: The data to be set

     DATA_SIZE: The size of data to be set

     FLAGS: GNUTLS_FSAN_SET to clear previous data or GNUTLS_FSAN_APPEND
     to append.

     This function will set an "othername" to the subject alternative
     name certificate extension.

     The values set are set as binary values and are expected to have
     the proper DER encoding.  For convenience the flags
     ‘GNUTLS_FSAN_ENCODE_OCTET_STRING’ and
     ‘GNUTLS_FSAN_ENCODE_UTF8_STRING’ can be used to encode the provided
     data.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.0

gnutls_x509_crt_set_subject_alternative_name
--------------------------------------------

 -- Function: int gnutls_x509_crt_set_subject_alternative_name
          (gnutls_x509_crt_t CRT, gnutls_x509_subject_alt_name_t TYPE,
          const char * DATA_STRING)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     TYPE: is one of the gnutls_x509_subject_alt_name_t enumerations

     DATA_STRING: The data to be set, a (0) terminated string

     This function will set the subject alternative name certificate
     extension.  This function assumes that data can be expressed as a
     null terminated string.

     The name of the function is unfortunate since it is inconsistent
     with ‘gnutls_x509_crt_get_subject_alt_name()’ .

     See ‘gnutls_x509_crt_set_subject_alt_name()’ for more information.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_subject_key_id
----------------------------------

 -- Function: int gnutls_x509_crt_set_subject_key_id (gnutls_x509_crt_t
          CERT, const void * ID, size_t ID_SIZE)
     CERT: a certificate of type ‘gnutls_x509_crt_t’

     ID: The key ID

     ID_SIZE: Holds the size of the subject key ID field.

     This function will set the X.509 certificate's subject key ID
     extension.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_subject_unique_id
-------------------------------------

 -- Function: int gnutls_x509_crt_set_subject_unique_id
          (gnutls_x509_crt_t CERT, const void * ID, size_t ID_SIZE)
     CERT: a certificate of type ‘gnutls_x509_crt_t’

     ID: The unique ID

     ID_SIZE: Holds the size of the unique ID.

     This function will set the X.509 certificate's subject unique ID
     field.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.7

gnutls_x509_crt_set_tlsfeatures
-------------------------------

 -- Function: int gnutls_x509_crt_set_tlsfeatures (gnutls_x509_crt_t
          CRT, gnutls_x509_tlsfeatures_t FEATURES)
     CRT: A X.509 certificate

     FEATURES: If the function succeeds, the features will be added to
     the certificate.

     This function will set the certificates X.509 TLS extension from
     the given structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.1

gnutls_x509_crt_set_version
---------------------------

 -- Function: int gnutls_x509_crt_set_version (gnutls_x509_crt_t CRT,
          unsigned int VERSION)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     VERSION: holds the version number.  For X.509v1 certificates must
     be 1.

     This function will set the version of the certificate.  This must
     be one for X.509 version 1, and so on.  Plain certificates without
     extensions must have version set to one.

     To create well-formed certificates, you must specify version 3 if
     you use any certificate extensions.  Extensions are created by
     functions such as ‘gnutls_x509_crt_set_subject_alt_name()’ or
     ‘gnutls_x509_crt_set_key_usage()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_sign
--------------------

 -- Function: int gnutls_x509_crt_sign (gnutls_x509_crt_t CRT,
          gnutls_x509_crt_t ISSUER, gnutls_x509_privkey_t ISSUER_KEY)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     ISSUER: is the certificate of the certificate issuer

     ISSUER_KEY: holds the issuer's private key

     This function is the same a ‘gnutls_x509_crt_sign2()’ with no
     flags, and an appropriate hash algorithm.  The hash algorithm used
     may vary between versions of GnuTLS, and it is tied to the security
     level of the issuer's public key.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_sign2
---------------------

 -- Function: int gnutls_x509_crt_sign2 (gnutls_x509_crt_t CRT,
          gnutls_x509_crt_t ISSUER, gnutls_x509_privkey_t ISSUER_KEY,
          gnutls_digest_algorithm_t DIG, unsigned int FLAGS)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     ISSUER: is the certificate of the certificate issuer

     ISSUER_KEY: holds the issuer's private key

     DIG: The message digest to use, ‘GNUTLS_DIG_SHA256’ is a safe
     choice

     FLAGS: must be 0

     This function will sign the certificate with the issuer's private
     key, and will copy the issuer's information into the certificate.

     This must be the last step in a certificate generation since all
     the previously set parameters are now signed.

     A known limitation of this function is, that a newly-signed
     certificate will not be fully functional (e.g., for signature
     verification), until it is exported an re-imported.

     After GnuTLS 3.6.1 the value of ‘dig’ may be ‘GNUTLS_DIG_UNKNOWN’ ,
     and in that case, a suitable but reasonable for the key algorithm
     will be selected.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_verify
----------------------

 -- Function: int gnutls_x509_crt_verify (gnutls_x509_crt_t CERT, const
          gnutls_x509_crt_t * CA_LIST, unsigned CA_LIST_LENGTH, unsigned
          int FLAGS, unsigned int * VERIFY)
     CERT: is the certificate to be verified

     CA_LIST: is one certificate that is considered to be trusted one

     CA_LIST_LENGTH: holds the number of CA certificate in CA_list

     FLAGS: Flags that may be used to change the verification algorithm.
     Use OR of the gnutls_certificate_verify_flags enumerations.

     VERIFY: will hold the certificate verification output.

     This function will try to verify the given certificate and return
     its status.  Note that a verification error does not imply a
     negative return status.  In that case the ‘verify’ status is set.

     The details of the verification are the same as in
     ‘gnutls_x509_trust_list_verify_crt2()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_verify_data2
----------------------------

 -- Function: int gnutls_x509_crt_verify_data2 (gnutls_x509_crt_t CRT,
          gnutls_sign_algorithm_t ALGO, unsigned int FLAGS, const
          gnutls_datum_t * DATA, const gnutls_datum_t * SIGNATURE)
     CRT: Holds the certificate to verify with

     ALGO: The signature algorithm used

     FLAGS: Zero or an OR list of ‘gnutls_certificate_verify_flags’

     DATA: holds the signed data

     SIGNATURE: contains the signature

     This function will verify the given signed data, using the
     parameters from the certificate.

     *Returns:* In case of a verification failure
     ‘GNUTLS_E_PK_SIG_VERIFY_FAILED’ is returned, ‘GNUTLS_E_EXPIRED’ or
     ‘GNUTLS_E_NOT_YET_ACTIVATED’ on expired or not yet activated
     certificate and zero or positive code on success.

     Note that since GnuTLS 3.5.6 this function introduces checks in the
     end certificate ( ‘crt’ ), including time checks and key usage
     checks.

     *Since:* 3.4.0

gnutls_x509_ct_sct_get
----------------------

 -- Function: int gnutls_x509_ct_sct_get (const gnutls_x509_ct_scts_t
          SCTS, unsigned IDX, time_t * TIMESTAMP, gnutls_datum_t *
          LOGID, gnutls_sign_algorithm_t * SIGALG, gnutls_datum_t *
          SIGNATURE)
     SCTS: A list of SCTs

     IDX: The index of the target SCT in the list

     TIMESTAMP: The timestamp of the SCT

     LOGID: The LogID field of the SCT; must be freed with
     ‘gnutls_free()’

     SIGALG: The signature algorithm

     SIGNATURE: The signature of the SCT; must be freed with
     ‘gnutls_free()’

     This function will return a specific SCT (Signed Certificate
     Timestamp) stored in the SCT list ‘scts’ .

     The datums holding the SCT's LogId and signature will be allocated
     using ‘gnutls_malloc()’ .

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) will be returned on success,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if ‘idx’ exceeds the number
     of SCTs in the list or a negative error value.

gnutls_x509_ct_sct_get_version
------------------------------

 -- Function: int gnutls_x509_ct_sct_get_version (gnutls_x509_ct_scts_t
          SCTS, unsigned IDX, unsigned int * VERSION_OUT)
     SCTS: A list of SCTs

     IDX: The index of the target SCT in the list

     VERSION_OUT: The version of the target SCT.

     This function obtains the version of the SCT at the given position
     in the SCT list.

     The version of that SCT will be placed on ‘version_out’ .

     Return : ‘GNUTLS_E_SUCCESS’ (0) is returned on success,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if ‘idx’ exceeds the number
     of SCTs in the list and ‘GNUTLS_E_INVALID_REQUEST’ if the SCT's
     version is different than 1, as that's currently the only defined
     version.

gnutls_x509_dn_deinit
---------------------

 -- Function: void gnutls_x509_dn_deinit (gnutls_x509_dn_t DN)
     DN: a DN uint8_t object pointer.

     This function deallocates the DN object as returned by
     ‘gnutls_x509_dn_import()’ .

     *Since:* 2.4.0

gnutls_x509_dn_export
---------------------

 -- Function: int gnutls_x509_dn_export (gnutls_x509_dn_t DN,
          gnutls_x509_crt_fmt_t FORMAT, void * OUTPUT_DATA, size_t *
          OUTPUT_DATA_SIZE)
     DN: Holds the uint8_t DN object

     FORMAT: the format of output params.  One of PEM or DER.

     OUTPUT_DATA: will contain a DN PEM or DER encoded

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will export the DN to DER or PEM format.

     If the buffer provided is not long enough to hold the output, then
     * ‘output_data_size’ is updated and ‘GNUTLS_E_SHORT_MEMORY_BUFFER’
     will be returned.

     If the structure is PEM encoded, it will have a header of "BEGIN
     NAME".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_dn_export2
----------------------

 -- Function: int gnutls_x509_dn_export2 (gnutls_x509_dn_t DN,
          gnutls_x509_crt_fmt_t FORMAT, gnutls_datum_t * OUT)
     DN: Holds the uint8_t DN object

     FORMAT: the format of output params.  One of PEM or DER.

     OUT: will contain a DN PEM or DER encoded

     This function will export the DN to DER or PEM format.

     The output buffer is allocated using ‘gnutls_malloc()’ .

     If the structure is PEM encoded, it will have a header of "BEGIN
     NAME".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.3

gnutls_x509_dn_get_rdn_ava
--------------------------

 -- Function: int gnutls_x509_dn_get_rdn_ava (gnutls_x509_dn_t DN, int
          IRDN, int IAVA, gnutls_x509_ava_st * AVA)
     DN: a pointer to DN

     IRDN: index of RDN

     IAVA: index of AVA.

     AVA: Pointer to structure which will hold output information.

     Get pointers to data within the DN. The format of the ‘ava’
     structure is shown below.

     struct gnutls_x509_ava_st { gnutls_datum_t oid; gnutls_datum_t
     value; unsigned long value_tag; };

     The X.509 distinguished name is a sequence of sequences of strings
     and this is what the ‘irdn’ and ‘iava’ indexes model.

     Note that ‘ava’ will contain pointers into the ‘dn’ structure which
     in turns points to the original certificate.  Thus you should not
     modify any data or deallocate any of those.

     This is a low-level function that requires the caller to do the
     value conversions when necessary (e.g.  from UCS-2).

     *Returns:* Returns 0 on success, or an error code.

gnutls_x509_dn_get_str
----------------------

 -- Function: int gnutls_x509_dn_get_str (gnutls_x509_dn_t DN,
          gnutls_datum_t * STR)
     DN: a pointer to DN

     STR: a datum that will hold the name

     This function will allocate buffer and copy the name in the
     provided DN. The name will be in the form "C=xxxx,O=yyyy,CN=zzzz"
     as described in RFC4514.  The output string will be ASCII or UTF-8
     encoded, depending on the certificate data.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.2

gnutls_x509_dn_get_str2
-----------------------

 -- Function: int gnutls_x509_dn_get_str2 (gnutls_x509_dn_t DN,
          gnutls_datum_t * STR, unsigned FLAGS)
     DN: a pointer to DN

     STR: a datum that will hold the name

     FLAGS: zero or ‘GNUTLS_X509_DN_FLAG_COMPAT’

     This function will allocate buffer and copy the name in the
     provided DN. The name will be in the form "C=xxxx,O=yyyy,CN=zzzz"
     as described in RFC4514.  The output string will be ASCII or UTF-8
     encoded, depending on the certificate data.

     When the flag ‘GNUTLS_X509_DN_FLAG_COMPAT’ is specified, the output
     format will match the format output by previous to 3.5.6 versions
     of GnuTLS which was not not fully RFC4514-compliant.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.7

gnutls_x509_dn_import
---------------------

 -- Function: int gnutls_x509_dn_import (gnutls_x509_dn_t DN, const
          gnutls_datum_t * DATA)
     DN: the structure that will hold the imported DN

     DATA: should contain a DER encoded RDN sequence

     This function parses an RDN sequence and stores the result to a
     ‘gnutls_x509_dn_t’ type.  The data must have been initialized with
     ‘gnutls_x509_dn_init()’ .  You may use
     ‘gnutls_x509_dn_get_rdn_ava()’ to decode the DN.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.4.0

gnutls_x509_dn_init
-------------------

 -- Function: int gnutls_x509_dn_init (gnutls_x509_dn_t * DN)
     DN: the object to be initialized

     This function initializes a ‘gnutls_x509_dn_t’ type.

     The object returned must be deallocated using
     ‘gnutls_x509_dn_deinit()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.4.0

gnutls_x509_dn_oid_known
------------------------

 -- Function: int gnutls_x509_dn_oid_known (const char * OID)
     OID: holds an Object Identifier in a null terminated string

     This function will inform about known DN OIDs.  This is useful
     since functions like ‘gnutls_x509_crt_set_dn_by_oid()’ use the
     information on known OIDs to properly encode their input.  Object
     Identifiers that are not known are not encoded by these functions,
     and their input is stored directly into the ASN.1 structure.  In
     that case of unknown OIDs, you have the responsibility of DER
     encoding your data.

     *Returns:* 1 on known OIDs and 0 otherwise.

gnutls_x509_dn_oid_name
-----------------------

 -- Function: const char * gnutls_x509_dn_oid_name (const char * OID,
          unsigned int FLAGS)
     OID: holds an Object Identifier in a null terminated string

     FLAGS: 0 or GNUTLS_X509_DN_OID_*

     This function will return the name of a known DN OID. If
     ‘GNUTLS_X509_DN_OID_RETURN_OID’ is specified this function will
     return the given OID if no descriptive name has been found.

     *Returns:* A null terminated string or NULL otherwise.

     *Since:* 3.0

gnutls_x509_dn_set_str
----------------------

 -- Function: int gnutls_x509_dn_set_str (gnutls_x509_dn_t DN, const
          char * STR, const char ** ERR)
     DN: a pointer to DN

     STR: a comma separated DN string (RFC4514)

     ERR: indicates the error position (if any)

     This function will set the DN on the provided DN structure.  The
     input string should be plain ASCII or UTF-8 encoded.  On DN parsing
     error ‘GNUTLS_E_PARSING_ERROR’ is returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.3

gnutls_x509_ext_ct_export_scts
------------------------------

 -- Function: int gnutls_x509_ext_ct_export_scts (const
          gnutls_x509_ct_scts_t SCTS, gnutls_datum_t * EXT)
     SCTS: An initialized SCT list

     EXT: The DER-encoded extension data; must be freed with
     ‘gnutls_free()’

     This function will convert the provided list of SCTs to a
     DER-encoded SignedCertificateTimestampList extension
     (*******.4.1.11129.2.4.2).  The output data in ‘ext’ will be
     allocated using ‘gnutls_malloc()’ .

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success or a negative error
     value.

gnutls_x509_ext_ct_import_scts
------------------------------

 -- Function: int gnutls_x509_ext_ct_import_scts (const gnutls_datum_t *
          EXT, gnutls_x509_ct_scts_t SCTS, unsigned int FLAGS)
     EXT: a DER-encoded extension

     SCTS: The SCT list

     FLAGS: should be zero

     This function will read a SignedCertificateTimestampList structure
     from the DER data of the X.509 Certificate Transparency SCT
     extension (OID *******.4.1.11129.2.4.2).

     The list of SCTs (Signed Certificate Timestamps) is placed on
     ‘scts’ , which must be previously initialized with
     ‘gnutls_x509_ext_ct_scts_init()’ .

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success or a negative error
     value.

gnutls_x509_ext_ct_scts_deinit
------------------------------

 -- Function: void gnutls_x509_ext_ct_scts_deinit (gnutls_x509_ct_scts_t
          SCTS)
     SCTS: The SCT list

     This function will deinitialize a Certificate Transparency SCT
     list.

gnutls_x509_ext_ct_scts_init
----------------------------

 -- Function: int gnutls_x509_ext_ct_scts_init (gnutls_x509_ct_scts_t *
          SCTS)
     SCTS: The SCT list

     This function will initialize a Certificate Transparency SCT list.

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success, otherwise a negative
     error value.

gnutls_x509_ext_deinit
----------------------

 -- Function: void gnutls_x509_ext_deinit (gnutls_x509_ext_st * EXT)
     EXT: The extensions structure

     This function will deinitialize an extensions structure.

     *Since:* 3.3.8

gnutls_x509_ext_export_aia
--------------------------

 -- Function: int gnutls_x509_ext_export_aia (gnutls_x509_aia_t AIA,
          gnutls_datum_t * EXT)
     AIA: The authority info access

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will DER encode the Authority Information Access
     (AIA) extension; see RFC 5280 section ******* for more information
     on the extension.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_export_authority_key_id
---------------------------------------

 -- Function: int gnutls_x509_ext_export_authority_key_id
          (gnutls_x509_aki_t AKI, gnutls_datum_t * EXT)
     AKI: An initialized authority key identifier

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will convert the provided key identifier to a
     DER-encoded PKIX AuthorityKeyIdentifier extension.  The output data
     in ‘ext’ will be allocated using ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_export_basic_constraints
----------------------------------------

 -- Function: int gnutls_x509_ext_export_basic_constraints (unsigned int
          CA, int PATHLEN, gnutls_datum_t * EXT)
     CA: non-zero for a CA

     PATHLEN: The path length constraint (set to -1 for no constraint)

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will convert the parameters provided to a basic
     constraints DER encoded extension (2.5.29.19).  The ‘ext’ data will
     be allocated using ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_export_crl_dist_points
--------------------------------------

 -- Function: int gnutls_x509_ext_export_crl_dist_points
          (gnutls_x509_crl_dist_points_t CDP, gnutls_datum_t * EXT)
     CDP: A pointer to an initialized CRL distribution points.

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will convert the provided policies, to a certificate
     policy DER encoded extension (*********).

     The ‘ext’ data will be allocated using ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_export_inhibit_anypolicy
----------------------------------------

 -- Function: int gnutls_x509_ext_export_inhibit_anypolicy (unsigned int
          SKIPCERTS, gnutls_datum_t * EXT)
     SKIPCERTS: number of certificates after which anypolicy is no
     longer acceptable.

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will convert the ‘skipcerts’ value to a DER encoded
     Inhibit AnyPolicy PKIX extension.  The ‘ext’ data will be allocated
     using ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.0

gnutls_x509_ext_export_key_purposes
-----------------------------------

 -- Function: int gnutls_x509_ext_export_key_purposes
          (gnutls_x509_key_purposes_t P, gnutls_datum_t * EXT)
     P: The key purposes

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will convert the key purposes type to a DER-encoded
     PKIX ExtKeyUsageSyntax (*********) extension.  The output data in
     ‘ext’ will be allocated using ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_export_key_usage
--------------------------------

 -- Function: int gnutls_x509_ext_export_key_usage (unsigned int USAGE,
          gnutls_datum_t * EXT)
     USAGE: an ORed sequence of the GNUTLS_KEY_* elements.

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will convert the keyUsage bit string to a DER encoded
     PKIX extension.  The ‘ext’ data will be allocated using
     ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_export_name_constraints
---------------------------------------

 -- Function: int gnutls_x509_ext_export_name_constraints
          (gnutls_x509_name_constraints_t NC, gnutls_datum_t * EXT)
     NC: The nameconstraints

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will convert the provided name constraints type to a
     DER-encoded PKIX NameConstraints (2.5.29.30) extension.  The output
     data in ‘ext’ will be allocated using ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_export_policies
-------------------------------

 -- Function: int gnutls_x509_ext_export_policies
          (gnutls_x509_policies_t POLICIES, gnutls_datum_t * EXT)
     POLICIES: A pointer to an initialized policies.

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will convert the provided policies, to a certificate
     policy DER encoded extension (*********).

     The ‘ext’ data will be allocated using ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_export_private_key_usage_period
-----------------------------------------------

 -- Function: int gnutls_x509_ext_export_private_key_usage_period
          (time_t ACTIVATION, time_t EXPIRATION, gnutls_datum_t * EXT)
     ACTIVATION: The activation time

     EXPIRATION: The expiration time

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will convert the periods provided to a private key
     usage DER encoded extension (*********).  The ‘ext’ data will be
     allocated using ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_export_proxy
----------------------------

 -- Function: int gnutls_x509_ext_export_proxy (int PATHLENCONSTRAINT,
          const char * POLICYLANGUAGE, const char * POLICY, size_t
          SIZEOF_POLICY, gnutls_datum_t * EXT)
     PATHLENCONSTRAINT: A negative value will remove the path length
     constraint, while non-negative values will be set as the length of
     the pathLenConstraints field.

     POLICYLANGUAGE: OID describing the language of ‘policy’ .

     POLICY: uint8_t byte array with policy language, can be ‘NULL’

     SIZEOF_POLICY: size of ‘policy’ .

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will convert the parameters provided to a
     proxyCertInfo extension.

     The ‘ext’ data will be allocated using ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_export_subject_alt_names
----------------------------------------

 -- Function: int gnutls_x509_ext_export_subject_alt_names
          (gnutls_subject_alt_names_t SANS, gnutls_datum_t * EXT)
     SANS: The alternative names

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will convert the provided alternative names structure
     to a DER-encoded SubjectAltName PKIX extension.  The output data in
     ‘ext’ will be allocated using ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_export_subject_key_id
-------------------------------------

 -- Function: int gnutls_x509_ext_export_subject_key_id (const
          gnutls_datum_t * ID, gnutls_datum_t * EXT)
     ID: The key identifier

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will convert the provided key identifier to a
     DER-encoded PKIX SubjectKeyIdentifier extension.  The output data
     in ‘ext’ will be allocated using ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_export_tlsfeatures
----------------------------------

 -- Function: int gnutls_x509_ext_export_tlsfeatures
          (gnutls_x509_tlsfeatures_t F, gnutls_datum_t * EXT)
     F: The features structure

     EXT: The DER-encoded extension data; must be freed using
     ‘gnutls_free()’ .

     This function will convert the provided TLS features structure
     structure to a DER-encoded TLS features PKIX extension.  The output
     data in ‘ext’ will be allocated using ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.1

gnutls_x509_ext_import_aia
--------------------------

 -- Function: int gnutls_x509_ext_import_aia (const gnutls_datum_t *
          EXT, gnutls_x509_aia_t AIA, unsigned int FLAGS)
     EXT: The DER-encoded extension data

     AIA: The authority info access

     FLAGS: should be zero

     This function extracts the Authority Information Access (AIA)
     extension from the provided DER-encoded data; see RFC 5280 section
     ******* for more information on the extension.  The AIA extension
     holds a sequence of AccessDescription (AD) data.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_import_authority_key_id
---------------------------------------

 -- Function: int gnutls_x509_ext_import_authority_key_id (const
          gnutls_datum_t * EXT, gnutls_x509_aki_t AKI, unsigned int
          FLAGS)
     EXT: a DER encoded extension

     AKI: An initialized authority key identifier type

     FLAGS: should be zero

     This function will return the subject key ID stored in the provided
     AuthorityKeyIdentifier extension.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the extension is not
     present, otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_import_basic_constraints
----------------------------------------

 -- Function: int gnutls_x509_ext_import_basic_constraints (const
          gnutls_datum_t * EXT, unsigned int * CA, int * PATHLEN)
     EXT: the DER encoded extension data

     CA: will be non zero if the CA status is true

     PATHLEN: the path length constraint; will be set to -1 for no limit

     This function will return the CA status and path length constraint
     as written in the PKIX extension 2.5.29.19.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_import_crl_dist_points
--------------------------------------

 -- Function: int gnutls_x509_ext_import_crl_dist_points (const
          gnutls_datum_t * EXT, gnutls_x509_crl_dist_points_t CDP,
          unsigned int FLAGS)
     EXT: the DER encoded extension data

     CDP: A pointer to an initialized CRL distribution points.

     FLAGS: should be zero

     This function will extract the CRL distribution points extension
     (*********) and store it into the provided type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_import_inhibit_anypolicy
----------------------------------------

 -- Function: int gnutls_x509_ext_import_inhibit_anypolicy (const
          gnutls_datum_t * EXT, unsigned int * SKIPCERTS)
     EXT: the DER encoded extension data

     SKIPCERTS: will hold the number of certificates after which
     anypolicy is no longer acceptable.

     This function will return certificate's value of SkipCerts, by
     reading the DER data of the Inhibit anyPolicy X.509 extension
     (*********).

     The ‘skipcerts’ value is the number of additional certificates that
     may appear in the path before the anyPolicy
     (‘GNUTLS_X509_OID_POLICY_ANY’ ) is no longer acceptable.

     *Returns:* zero, or a negative error code in case of parsing error.
     If the certificate does not contain the Inhibit anyPolicy extension
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Since:* 3.6.0

gnutls_x509_ext_import_key_purposes
-----------------------------------

 -- Function: int gnutls_x509_ext_import_key_purposes (const
          gnutls_datum_t * EXT, gnutls_x509_key_purposes_t P, unsigned
          int FLAGS)
     EXT: The DER-encoded extension data

     P: The key purposes

     FLAGS: should be zero

     This function will extract the key purposes in the provided
     DER-encoded ExtKeyUsageSyntax PKIX extension, to a
     ‘gnutls_x509_key_purposes_t’ type.  The data must be initialized.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_import_key_usage
--------------------------------

 -- Function: int gnutls_x509_ext_import_key_usage (const gnutls_datum_t
          * EXT, unsigned int * KEY_USAGE)
     EXT: the DER encoded extension data

     KEY_USAGE: where the key usage bits will be stored

     This function will return certificate's key usage, by reading the
     DER data of the keyUsage X.509 extension (*********).  The key
     usage value will ORed values of the: ‘GNUTLS_KEY_DIGITAL_SIGNATURE’
     , ‘GNUTLS_KEY_NON_REPUDIATION’ , ‘GNUTLS_KEY_KEY_ENCIPHERMENT’ ,
     ‘GNUTLS_KEY_DATA_ENCIPHERMENT’ , ‘GNUTLS_KEY_KEY_AGREEMENT’ ,
     ‘GNUTLS_KEY_KEY_CERT_SIGN’ , ‘GNUTLS_KEY_CRL_SIGN’ ,
     ‘GNUTLS_KEY_ENCIPHER_ONLY’ , ‘GNUTLS_KEY_DECIPHER_ONLY’ .

     *Returns:* the certificate key usage, or a negative error code in
     case of parsing error.  If the certificate does not contain the
     keyUsage extension ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be
     returned.

     *Since:* 3.3.0

gnutls_x509_ext_import_name_constraints
---------------------------------------

 -- Function: int gnutls_x509_ext_import_name_constraints (const
          gnutls_datum_t * EXT, gnutls_x509_name_constraints_t NC,
          unsigned int FLAGS)
     EXT: a DER encoded extension

     NC: The nameconstraints

     FLAGS: zero or ‘GNUTLS_NAME_CONSTRAINTS_FLAG_APPEND’

     This function will return an intermediate type containing the name
     constraints of the provided NameConstraints extension.  That can be
     used in combination with ‘gnutls_x509_name_constraints_check()’ to
     verify whether a server's name is in accordance with the
     constraints.

     When the ‘flags’ is set to ‘GNUTLS_NAME_CONSTRAINTS_FLAG_APPEND’ ,
     then if the ‘nc’ type is empty this function will behave
     identically as if the flag was not set.  Otherwise if there are
     elements in the ‘nc’ structure then the constraints will be merged
     with the existing constraints following RFC5280 p6.1.4 (excluded
     constraints will be appended, permitted will be intersected).

     Note that ‘nc’ must be initialized prior to calling this function.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the extension is not
     present, otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_import_policies
-------------------------------

 -- Function: int gnutls_x509_ext_import_policies (const gnutls_datum_t
          * EXT, gnutls_x509_policies_t POLICIES, unsigned int FLAGS)
     EXT: the DER encoded extension data

     POLICIES: A pointer to an initialized policies.

     FLAGS: should be zero

     This function will extract the certificate policy extension
     (*********) and store it the provided policies.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_import_private_key_usage_period
-----------------------------------------------

 -- Function: int gnutls_x509_ext_import_private_key_usage_period (const
          gnutls_datum_t * EXT, time_t * ACTIVATION, time_t *
          EXPIRATION)
     EXT: the DER encoded extension data

     ACTIVATION: Will hold the activation time

     EXPIRATION: Will hold the expiration time

     This function will return the expiration and activation times of
     the private key as written in the PKIX extension *********.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_import_proxy
----------------------------

 -- Function: int gnutls_x509_ext_import_proxy (const gnutls_datum_t *
          EXT, int * PATHLEN, char ** POLICYLANGUAGE, char ** POLICY,
          size_t * SIZEOF_POLICY)
     EXT: the DER encoded extension data

     PATHLEN: pointer to output integer indicating path length (may be
     NULL), non-negative error codes indicate a present
     pCPathLenConstraint field and the actual value, -1 indicate that
     the field is absent.

     POLICYLANGUAGE: output variable with OID of policy language

     POLICY: output variable with policy data

     SIZEOF_POLICY: output variable with size of policy data

     This function will return the information from a proxy certificate
     extension.  It reads the ProxyCertInfo X.509 extension
     (*******.5.5.7.1.14).  The ‘policyLanguage’ and ‘policy’ values
     must be deinitialized using ‘gnutls_free()’ after use.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_import_subject_alt_names
----------------------------------------

 -- Function: int gnutls_x509_ext_import_subject_alt_names (const
          gnutls_datum_t * EXT, gnutls_subject_alt_names_t SANS,
          unsigned int FLAGS)
     EXT: The DER-encoded extension data

     SANS: The alternative names

     FLAGS: should be zero

     This function will export the alternative names in the provided
     DER-encoded SubjectAltName PKIX extension, to a
     ‘gnutls_subject_alt_names_t’ type.  ‘sans’ must be initialized.

     This function will succeed even if there no subject alternative
     names in the structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_import_subject_key_id
-------------------------------------

 -- Function: int gnutls_x509_ext_import_subject_key_id (const
          gnutls_datum_t * EXT, gnutls_datum_t * ID)
     EXT: a DER encoded extension

     ID: will contain the subject key ID

     This function will return the subject key ID stored in the provided
     SubjectKeyIdentifier extension.  The ID will be allocated using
     ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the extension is not
     present, otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_ext_import_tlsfeatures
----------------------------------

 -- Function: int gnutls_x509_ext_import_tlsfeatures (const
          gnutls_datum_t * EXT, gnutls_x509_tlsfeatures_t F, unsigned
          int FLAGS)
     EXT: The DER-encoded extension data

     F: The features structure

     FLAGS: zero or ‘GNUTLS_EXT_FLAG_APPEND’

     This function will export the features in the provided DER-encoded
     TLS Features PKIX extension, to a ‘gnutls_x509_tlsfeatures_t’ type.
     ‘f’ must be initialized.

     When the ‘flags’ is set to ‘GNUTLS_EXT_FLAG_APPEND’ , then if the
     ‘features’ structure is empty this function will behave identically
     as if the flag was not set.  Otherwise if there are elements in the
     ‘features’ structure then they will be merged with.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.1

gnutls_x509_ext_print
---------------------

 -- Function: int gnutls_x509_ext_print (gnutls_x509_ext_st * EXTS,
          unsigned int EXTS_SIZE, gnutls_certificate_print_formats_t
          FORMAT, gnutls_datum_t * OUT)
     EXTS: The data to be printed

     EXTS_SIZE: the number of available structures

     FORMAT: Indicate the format to use

     OUT: Newly allocated datum with null terminated string.

     This function will pretty print X.509 certificate extensions,
     suitable for display to a human.

     The output ‘out’ needs to be deallocated using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_key_purpose_deinit
------------------------------

 -- Function: void gnutls_x509_key_purpose_deinit
          (gnutls_x509_key_purposes_t P)
     P: The key purposes

     This function will deinitialize a key purposes type.

     *Since:* 3.3.0

gnutls_x509_key_purpose_get
---------------------------

 -- Function: int gnutls_x509_key_purpose_get
          (gnutls_x509_key_purposes_t P, unsigned IDX, gnutls_datum_t *
          OID)
     P: The key purposes

     IDX: The index of the key purpose to retrieve

     OID: Will hold the object identifier of the key purpose (to be
     treated as constant)

     This function will retrieve the specified by the index key purpose
     in the purposes type.  The object identifier will be a null
     terminated string.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the index is out of
     bounds, otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_key_purpose_init
----------------------------

 -- Function: int gnutls_x509_key_purpose_init
          (gnutls_x509_key_purposes_t * P)
     P: The key purposes

     This function will initialize an alternative names type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_key_purpose_set
---------------------------

 -- Function: int gnutls_x509_key_purpose_set
          (gnutls_x509_key_purposes_t P, const char * OID)
     P: The key purposes

     OID: The object identifier of the key purpose

     This function will store the specified key purpose in the purposes.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0), otherwise a negative
     error value.

     *Since:* 3.3.0

gnutls_x509_name_constraints_add_excluded
-----------------------------------------

 -- Function: int gnutls_x509_name_constraints_add_excluded
          (gnutls_x509_name_constraints_t NC,
          gnutls_x509_subject_alt_name_t TYPE, const gnutls_datum_t *
          NAME)
     NC: The nameconstraints

     TYPE: The type of the constraints

     NAME: The data of the constraints

     This function will add a name constraint to the list of excluded
     constraints.  The constraints ‘type’ can be any of the following
     types: ‘GNUTLS_SAN_DNSNAME’ , ‘GNUTLS_SAN_RFC822NAME’ ,
     ‘GNUTLS_SAN_DN’ , ‘GNUTLS_SAN_URI’ , ‘GNUTLS_SAN_IPADDRESS’ .  For
     the latter, an IP address in network byte order is expected,
     followed by its network mask (which is 4 bytes in IPv4 or 16-bytes
     in IPv6).

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_name_constraints_add_permitted
------------------------------------------

 -- Function: int gnutls_x509_name_constraints_add_permitted
          (gnutls_x509_name_constraints_t NC,
          gnutls_x509_subject_alt_name_t TYPE, const gnutls_datum_t *
          NAME)
     NC: The nameconstraints

     TYPE: The type of the constraints

     NAME: The data of the constraints

     This function will add a name constraint to the list of permitted
     constraints.  The constraints ‘type’ can be any of the following
     types: ‘GNUTLS_SAN_DNSNAME’ , ‘GNUTLS_SAN_RFC822NAME’ ,
     ‘GNUTLS_SAN_DN’ , ‘GNUTLS_SAN_URI’ , ‘GNUTLS_SAN_IPADDRESS’ .  For
     the latter, an IP address in network byte order is expected,
     followed by its network mask.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_name_constraints_check
----------------------------------

 -- Function: unsigned gnutls_x509_name_constraints_check
          (gnutls_x509_name_constraints_t NC,
          gnutls_x509_subject_alt_name_t TYPE, const gnutls_datum_t *
          NAME)
     NC: the extracted name constraints

     TYPE: the type of the constraint to check (of type
     gnutls_x509_subject_alt_name_t)

     NAME: the name to be checked

     This function will check the provided name against the constraints
     in ‘nc’ using the RFC5280 rules.  Currently this function is
     limited to DNS names, emails and IP addresses (of type
     ‘GNUTLS_SAN_DNSNAME’ , ‘GNUTLS_SAN_RFC822NAME’ and
     ‘GNUTLS_SAN_IPADDRESS’ ).

     *Returns:* zero if the provided name is not acceptable, and
     non-zero otherwise.

     *Since:* 3.3.0

gnutls_x509_name_constraints_check_crt
--------------------------------------

 -- Function: unsigned gnutls_x509_name_constraints_check_crt
          (gnutls_x509_name_constraints_t NC,
          gnutls_x509_subject_alt_name_t TYPE, gnutls_x509_crt_t CERT)
     NC: the extracted name constraints

     TYPE: the type of the constraint to check (of type
     gnutls_x509_subject_alt_name_t)

     CERT: the certificate to be checked

     This function will check the provided certificate names against the
     constraints in ‘nc’ using the RFC5280 rules.  It will traverse all
     the certificate's names and alternative names.

     Currently this function is limited to DNS names and emails (of type
     ‘GNUTLS_SAN_DNSNAME’ and ‘GNUTLS_SAN_RFC822NAME’ ).

     *Returns:* zero if the provided name is not acceptable, and
     non-zero otherwise.

     *Since:* 3.3.0

gnutls_x509_name_constraints_deinit
-----------------------------------

 -- Function: void gnutls_x509_name_constraints_deinit
          (gnutls_x509_name_constraints_t NC)
     NC: The nameconstraints

     This function will deinitialize a name constraints type.

     *Since:* 3.3.0

gnutls_x509_name_constraints_get_excluded
-----------------------------------------

 -- Function: int gnutls_x509_name_constraints_get_excluded
          (gnutls_x509_name_constraints_t NC, unsigned IDX, unsigned *
          TYPE, gnutls_datum_t * NAME)
     NC: the extracted name constraints

     IDX: the index of the constraint

     TYPE: the type of the constraint (of type
     gnutls_x509_subject_alt_name_t)

     NAME: the name in the constraint (of the specific type)

     This function will return an intermediate type containing the name
     constraints of the provided CA certificate.  That structure can be
     used in combination with ‘gnutls_x509_name_constraints_check()’ to
     verify whether a server's name is in accordance with the
     constraints.

     The name should be treated as constant and valid for the lifetime
     of ‘nc’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the extension is not
     present, otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_name_constraints_get_permitted
------------------------------------------

 -- Function: int gnutls_x509_name_constraints_get_permitted
          (gnutls_x509_name_constraints_t NC, unsigned IDX, unsigned *
          TYPE, gnutls_datum_t * NAME)
     NC: the extracted name constraints

     IDX: the index of the constraint

     TYPE: the type of the constraint (of type
     gnutls_x509_subject_alt_name_t)

     NAME: the name in the constraint (of the specific type)

     This function will return an intermediate type containing the name
     constraints of the provided CA certificate.  That structure can be
     used in combination with ‘gnutls_x509_name_constraints_check()’ to
     verify whether a server's name is in accordance with the
     constraints.

     The name should be treated as constant and valid for the lifetime
     of ‘nc’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the extension is not
     present, otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_name_constraints_init
---------------------------------

 -- Function: int gnutls_x509_name_constraints_init
          (gnutls_x509_name_constraints_t * NC)
     NC: The nameconstraints

     This function will initialize a name constraints type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_othername_to_virtual
--------------------------------

 -- Function: int gnutls_x509_othername_to_virtual (const char * OID,
          const gnutls_datum_t * OTHERNAME, unsigned int * VIRT_TYPE,
          gnutls_datum_t * VIRT)
     OID: The othername object identifier

     OTHERNAME: The othername data

     VIRT_TYPE: GNUTLS_SAN_OTHERNAME_XXX

     VIRT: allocated printable data

     This function will parse and convert the othername data to a
     virtual type supported by gnutls.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.8

gnutls_x509_policies_deinit
---------------------------

 -- Function: void gnutls_x509_policies_deinit (gnutls_x509_policies_t
          POLICIES)
     POLICIES: The authority key identifier

     This function will deinitialize an authority key identifier type.

     *Since:* 3.3.0

gnutls_x509_policies_get
------------------------

 -- Function: int gnutls_x509_policies_get (gnutls_x509_policies_t
          POLICIES, unsigned int SEQ, struct gnutls_x509_policy_st *
          POLICY)
     POLICIES: The policies

     SEQ: The index of the name to get

     POLICY: Will hold the policy

     This function will return a specific policy as stored in the
     ‘policies’ type.  The returned values should be treated as constant
     and valid for the lifetime of ‘policies’ .

     The any policy OID is available as the ‘GNUTLS_X509_OID_POLICY_ANY’
     macro.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the index is out of
     bounds, otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_policies_init
-------------------------

 -- Function: int gnutls_x509_policies_init (gnutls_x509_policies_t *
          POLICIES)
     POLICIES: The authority key ID

     This function will initialize an authority key ID type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_x509_policies_set
------------------------

 -- Function: int gnutls_x509_policies_set (gnutls_x509_policies_t
          POLICIES, const struct gnutls_x509_policy_st * POLICY)
     POLICIES: An initialized policies

     POLICY: Contains the policy to set

     This function will store the specified policy in the provided
     ‘policies’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0), otherwise a negative
     error value.

     *Since:* 3.3.0

gnutls_x509_policy_release
--------------------------

 -- Function: void gnutls_x509_policy_release (struct
          gnutls_x509_policy_st * POLICY)
     POLICY: a certificate policy

     This function will deinitialize all memory associated with the
     provided ‘policy’ .  The policy is allocated using
     ‘gnutls_x509_crt_get_policy()’ .

     *Since:* 3.1.5

gnutls_x509_privkey_cpy
-----------------------

 -- Function: int gnutls_x509_privkey_cpy (gnutls_x509_privkey_t DST,
          gnutls_x509_privkey_t SRC)
     DST: The destination key, which should be initialized.

     SRC: The source key

     This function will copy a private key from source to destination
     key.  Destination has to be initialized.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_deinit
--------------------------

 -- Function: void gnutls_x509_privkey_deinit (gnutls_x509_privkey_t
          KEY)
     KEY: The key to be deinitialized

     This function will deinitialize a private key structure.

gnutls_x509_privkey_export
--------------------------

 -- Function: int gnutls_x509_privkey_export (gnutls_x509_privkey_t KEY,
          gnutls_x509_crt_fmt_t FORMAT, void * OUTPUT_DATA, size_t *
          OUTPUT_DATA_SIZE)
     KEY: Holds the key

     FORMAT: the format of output params.  One of PEM or DER.

     OUTPUT_DATA: will contain a private key PEM or DER encoded

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will export the private key to a PKCS‘1’ structure
     for RSA or RSA-PSS keys, and integer sequence for DSA keys.  Other
     keys types will be exported in PKCS‘8’ form.

     If the structure is PEM encoded, it will have a header of "BEGIN
     RSA PRIVATE KEY".

     It is recommended to use ‘gnutls_x509_privkey_export_pkcs8()’
     instead of this function, when a consistent output format is
     required.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_export2
---------------------------

 -- Function: int gnutls_x509_privkey_export2 (gnutls_x509_privkey_t
          KEY, gnutls_x509_crt_fmt_t FORMAT, gnutls_datum_t * OUT)
     KEY: Holds the key

     FORMAT: the format of output params.  One of PEM or DER.

     OUT: will contain a private key PEM or DER encoded

     This function will export the private key to a PKCS‘1’ structure
     for RSA or RSA-PSS keys, and integer sequence for DSA keys.  Other
     keys types will be exported in PKCS‘8’ form.

     The output buffer is allocated using ‘gnutls_malloc()’ .

     It is recommended to use ‘gnutls_x509_privkey_export2_pkcs8()’
     instead of this function, when a consistent output format is
     required.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     Since 3.1.3

gnutls_x509_privkey_export2_pkcs8
---------------------------------

 -- Function: int gnutls_x509_privkey_export2_pkcs8
          (gnutls_x509_privkey_t KEY, gnutls_x509_crt_fmt_t FORMAT,
          const char * PASSWORD, unsigned int FLAGS, gnutls_datum_t *
          OUT)
     KEY: Holds the key

     FORMAT: the format of output params.  One of PEM or DER.

     PASSWORD: the password that will be used to encrypt the key.

     FLAGS: an ORed sequence of gnutls_pkcs_encrypt_flags_t

     OUT: will contain a private key PEM or DER encoded

     This function will export the private key to a PKCS8 structure.
     Both RSA and DSA keys can be exported.  For DSA keys we use PKCS
     ‘11’ definitions.  If the flags do not specify the encryption
     cipher, then the default 3DES (PBES2) will be used.

     The ‘password’ can be either ASCII or UTF-8 in the default PBES2
     encryption schemas, or ASCII for the PKCS12 schemas.

     The output buffer is allocated using ‘gnutls_malloc()’ .

     If the structure is PEM encoded, it will have a header of "BEGIN
     ENCRYPTED PRIVATE KEY" or "BEGIN PRIVATE KEY" if encryption is not
     used.

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

     Since 3.1.3

gnutls_x509_privkey_export_dsa_raw
----------------------------------

 -- Function: int gnutls_x509_privkey_export_dsa_raw
          (gnutls_x509_privkey_t KEY, gnutls_datum_t * P, gnutls_datum_t
          * Q, gnutls_datum_t * G, gnutls_datum_t * Y, gnutls_datum_t *
          X)
     KEY: a key

     P: will hold the p

     Q: will hold the q

     G: will hold the g

     Y: will hold the y

     X: will hold the x

     This function will export the DSA private key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_export_ecc_raw
----------------------------------

 -- Function: int gnutls_x509_privkey_export_ecc_raw
          (gnutls_x509_privkey_t KEY, gnutls_ecc_curve_t * CURVE,
          gnutls_datum_t * X, gnutls_datum_t * Y, gnutls_datum_t * K)
     KEY: a key

     CURVE: will hold the curve

     X: will hold the x-coordinate

     Y: will hold the y-coordinate

     K: will hold the private key

     This function will export the ECC private key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     In EdDSA curves the ‘y’ parameter will be ‘NULL’ and the other
     parameters will be in the native format for the curve.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_x509_privkey_export_gost_raw
-----------------------------------

 -- Function: int gnutls_x509_privkey_export_gost_raw
          (gnutls_x509_privkey_t KEY, gnutls_ecc_curve_t * CURVE,
          gnutls_digest_algorithm_t * DIGEST, gnutls_gost_paramset_t *
          PARAMSET, gnutls_datum_t * X, gnutls_datum_t * Y,
          gnutls_datum_t * K)
     KEY: a key

     CURVE: will hold the curve

     DIGEST: will hold the digest

     PARAMSET: will hold the GOST parameter set ID

     X: will hold the x-coordinate

     Y: will hold the y-coordinate

     K: will hold the private key

     This function will export the GOST private key's parameters found
     in the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Note:* parameters will be stored with least significant byte
     first.  On version 3.6.3 this was incorrectly returned in
     big-endian format.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.3

gnutls_x509_privkey_export_pkcs8
--------------------------------

 -- Function: int gnutls_x509_privkey_export_pkcs8
          (gnutls_x509_privkey_t KEY, gnutls_x509_crt_fmt_t FORMAT,
          const char * PASSWORD, unsigned int FLAGS, void * OUTPUT_DATA,
          size_t * OUTPUT_DATA_SIZE)
     KEY: Holds the key

     FORMAT: the format of output params.  One of PEM or DER.

     PASSWORD: the password that will be used to encrypt the key.

     FLAGS: an ORed sequence of gnutls_pkcs_encrypt_flags_t

     OUTPUT_DATA: will contain a private key PEM or DER encoded

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will export the private key to a PKCS8 structure.
     Both RSA and DSA keys can be exported.  For DSA keys we use PKCS
     ‘11’ definitions.  If the flags do not specify the encryption
     cipher, then the default 3DES (PBES2) will be used.

     The ‘password’ can be either ASCII or UTF-8 in the default PBES2
     encryption schemas, or ASCII for the PKCS12 schemas.

     If the buffer provided is not long enough to hold the output, then
     *output_data_size is updated and GNUTLS_E_SHORT_MEMORY_BUFFER will
     be returned.

     If the structure is PEM encoded, it will have a header of "BEGIN
     ENCRYPTED PRIVATE KEY" or "BEGIN PRIVATE KEY" if encryption is not
     used.

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

gnutls_x509_privkey_export_rsa_raw
----------------------------------

 -- Function: int gnutls_x509_privkey_export_rsa_raw
          (gnutls_x509_privkey_t KEY, gnutls_datum_t * M, gnutls_datum_t
          * E, gnutls_datum_t * D, gnutls_datum_t * P, gnutls_datum_t *
          Q, gnutls_datum_t * U)
     KEY: a key

     M: will hold the modulus

     E: will hold the public exponent

     D: will hold the private exponent

     P: will hold the first prime (p)

     Q: will hold the second prime (q)

     U: will hold the coefficient

     This function will export the RSA private key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_export_rsa_raw2
-----------------------------------

 -- Function: int gnutls_x509_privkey_export_rsa_raw2
          (gnutls_x509_privkey_t KEY, gnutls_datum_t * M, gnutls_datum_t
          * E, gnutls_datum_t * D, gnutls_datum_t * P, gnutls_datum_t *
          Q, gnutls_datum_t * U, gnutls_datum_t * E1, gnutls_datum_t *
          E2)
     KEY: a key

     M: will hold the modulus

     E: will hold the public exponent

     D: will hold the private exponent

     P: will hold the first prime (p)

     Q: will hold the second prime (q)

     U: will hold the coefficient

     E1: will hold e1 = d mod (p-1)

     E2: will hold e2 = d mod (q-1)

     This function will export the RSA private key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_x509_privkey_fix
-----------------------

 -- Function: int gnutls_x509_privkey_fix (gnutls_x509_privkey_t KEY)
     KEY: a key

     This function will recalculate the secondary parameters in a key.
     In RSA keys, this can be the coefficient and exponent1,2.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_generate
----------------------------

 -- Function: int gnutls_x509_privkey_generate (gnutls_x509_privkey_t
          KEY, gnutls_pk_algorithm_t ALGO, unsigned int BITS, unsigned
          int FLAGS)
     KEY: an initialized key

     ALGO: is one of the algorithms in ‘gnutls_pk_algorithm_t’ .

     BITS: the size of the parameters to generate

     FLAGS: Must be zero or flags from ‘gnutls_privkey_flags_t’ .

     This function will generate a random private key.  Note that this
     function must be called on an initialized private key.

     The flag ‘GNUTLS_PRIVKEY_FLAG_PROVABLE’ instructs the key
     generation process to use algorithms like Shawe-Taylor (from FIPS
     PUB186-4) which generate provable parameters out of a seed for RSA
     and DSA keys.  See ‘gnutls_x509_privkey_generate2()’ for more
     information.

     Note that when generating an elliptic curve key, the curve can be
     substituted in the place of the bits parameter using the
     ‘GNUTLS_CURVE_TO_BITS()’ macro.  The input to the macro is any
     curve from ‘gnutls_ecc_curve_t’ .

     For DSA keys, if the subgroup size needs to be specified check the
     ‘GNUTLS_SUBGROUP_TO_BITS()’ macro.

     It is recommended to do not set the number of ‘bits’ directly, use
     ‘gnutls_sec_param_to_pk_bits()’ instead .

     See also ‘gnutls_privkey_generate()’ ,
     ‘gnutls_x509_privkey_generate2()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_generate2
-----------------------------

 -- Function: int gnutls_x509_privkey_generate2 (gnutls_x509_privkey_t
          KEY, gnutls_pk_algorithm_t ALGO, unsigned int BITS, unsigned
          int FLAGS, const gnutls_keygen_data_st * DATA, unsigned
          DATA_SIZE)
     KEY: a key

     ALGO: is one of the algorithms in ‘gnutls_pk_algorithm_t’ .

     BITS: the size of the modulus

     FLAGS: Must be zero or flags from ‘gnutls_privkey_flags_t’ .

     DATA: Allow specifying ‘gnutls_keygen_data_st’ types such as the
     seed to be used.

     DATA_SIZE: The number of ‘data’ available.

     This function will generate a random private key.  Note that this
     function must be called on an initialized private key.

     The flag ‘GNUTLS_PRIVKEY_FLAG_PROVABLE’ instructs the key
     generation process to use algorithms like Shawe-Taylor (from FIPS
     PUB186-4) which generate provable parameters out of a seed for RSA
     and DSA keys.  On DSA keys the PQG parameters are generated using
     the seed, while on RSA the two primes.  To specify an explicit seed
     (by default a random seed is used), use the ‘data’ with a
     ‘GNUTLS_KEYGEN_SEED’ type.

     Note that when generating an elliptic curve key, the curve can be
     substituted in the place of the bits parameter using the
     ‘GNUTLS_CURVE_TO_BITS()’ macro.

     To export the generated keys in memory or in files it is
     recommended to use the PKCS‘8’ form as it can handle all key types,
     and can store additional parameters such as the seed, in case of
     provable RSA or DSA keys.  Generated keys can be exported in memory
     using ‘gnutls_privkey_export_x509()’ , and then with
     ‘gnutls_x509_privkey_export2_pkcs8()’ .

     If key generation is part of your application, avoid setting the
     number of bits directly, and instead use
     ‘gnutls_sec_param_to_pk_bits()’ .  That way the generated keys will
     adapt to the security levels of the underlying GnuTLS library.

     See also ‘gnutls_privkey_generate2()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_get_key_id
------------------------------

 -- Function: int gnutls_x509_privkey_get_key_id (gnutls_x509_privkey_t
          KEY, unsigned int FLAGS, unsigned char * OUTPUT_DATA, size_t *
          OUTPUT_DATA_SIZE)
     KEY: a key

     FLAGS: should be one of the flags from ‘gnutls_keyid_flags_t’

     OUTPUT_DATA: will contain the key ID

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will return a unique ID that depends on the public
     key parameters.  This ID can be used in checking whether a
     certificate corresponds to the given key.

     If the buffer provided is not long enough to hold the output, then
     * ‘output_data_size’ is updated and ‘GNUTLS_E_SHORT_MEMORY_BUFFER’
     will be returned.  The output will normally be a SHA-1 hash output,
     which is 20 bytes.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_get_pk_algorithm
------------------------------------

 -- Function: int gnutls_x509_privkey_get_pk_algorithm
          (gnutls_x509_privkey_t KEY)
     KEY: should contain a ‘gnutls_x509_privkey_t’ type

     This function will return the public key algorithm of a private
     key.

     *Returns:* a member of the ‘gnutls_pk_algorithm_t’ enumeration on
     success, or a negative error code on error.

gnutls_x509_privkey_get_pk_algorithm2
-------------------------------------

 -- Function: int gnutls_x509_privkey_get_pk_algorithm2
          (gnutls_x509_privkey_t KEY, unsigned int * BITS)
     KEY: should contain a ‘gnutls_x509_privkey_t’ type

     BITS: The number of bits in the public key algorithm

     This function will return the public key algorithm of a private
     key.

     *Returns:* a member of the ‘gnutls_pk_algorithm_t’ enumeration on
     success, or a negative error code on error.

gnutls_x509_privkey_get_seed
----------------------------

 -- Function: int gnutls_x509_privkey_get_seed (gnutls_x509_privkey_t
          KEY, gnutls_digest_algorithm_t * DIGEST, void * SEED, size_t *
          SEED_SIZE)
     KEY: should contain a ‘gnutls_x509_privkey_t’ type

     DIGEST: if non-NULL it will contain the digest algorithm used for
     key generation (if applicable)

     SEED: where seed will be copied to

     SEED_SIZE: originally holds the size of ‘seed’ , will be updated
     with actual size

     This function will return the seed that was used to generate the
     given private key.  That function will succeed only if the key was
     generated as a provable key.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.0

gnutls_x509_privkey_get_spki
----------------------------

 -- Function: int gnutls_x509_privkey_get_spki (gnutls_x509_privkey_t
          KEY, gnutls_x509_spki_t SPKI, unsigned int FLAGS)
     KEY: should contain a ‘gnutls_x509_privkey_t’ type

     SPKI: a SubjectPublicKeyInfo structure of type ‘gnutls_x509_spki_t’

     FLAGS: must be zero

     This function will return the public key information of a private
     key.  The provided ‘spki’ must be initialized.

     *Returns:* Zero on success, or a negative error code on error.

gnutls_x509_privkey_import
--------------------------

 -- Function: int gnutls_x509_privkey_import (gnutls_x509_privkey_t KEY,
          const gnutls_datum_t * DATA, gnutls_x509_crt_fmt_t FORMAT)
     KEY: The data to store the parsed key

     DATA: The DER or PEM encoded certificate.

     FORMAT: One of DER or PEM

     This function will convert the given DER or PEM encoded key to the
     native ‘gnutls_x509_privkey_t’ format.  The output will be stored
     in ‘key’ .

     If the key is PEM encoded it should have a header that contains
     "PRIVATE KEY". Note that this function falls back to PKCS ‘8’
     decoding without password, if the default format fails to import.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_import2
---------------------------

 -- Function: int gnutls_x509_privkey_import2 (gnutls_x509_privkey_t
          KEY, const gnutls_datum_t * DATA, gnutls_x509_crt_fmt_t
          FORMAT, const char * PASSWORD, unsigned int FLAGS)
     KEY: The data to store the parsed key

     DATA: The DER or PEM encoded key.

     FORMAT: One of DER or PEM

     PASSWORD: A password (optional)

     FLAGS: an ORed sequence of gnutls_pkcs_encrypt_flags_t

     This function will import the given DER or PEM encoded key, to the
     native ‘gnutls_x509_privkey_t’ format, irrespective of the input
     format.  The input format is auto-detected.

     The supported formats are basic unencrypted key, PKCS8, PKCS12, and
     the openssl format.

     If the provided key is encrypted but no password was given, then
     ‘GNUTLS_E_DECRYPTION_FAILED’ is returned.  Since GnuTLS 3.4.0 this
     function will utilize the PIN callbacks if any.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_import_dh_raw
---------------------------------

 -- Function: int gnutls_x509_privkey_import_dh_raw
          (gnutls_x509_privkey_t KEY, const gnutls_dh_params_t PARAMS,
          const gnutls_datum_t * Y, const gnutls_datum_t * X)
     KEY: The data to store the parsed key

     PARAMS: holds the ‘gnutls_dh_params_t’

     Y: holds the y (optional)

     X: holds the x

     This function will convert the given Diffie-Hellman raw parameters
     to the native ‘gnutls_x509_privkey_t’ format.  The output will be
     stored in ‘key’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_import_dsa_raw
----------------------------------

 -- Function: int gnutls_x509_privkey_import_dsa_raw
          (gnutls_x509_privkey_t KEY, const gnutls_datum_t * P, const
          gnutls_datum_t * Q, const gnutls_datum_t * G, const
          gnutls_datum_t * Y, const gnutls_datum_t * X)
     KEY: The data to store the parsed key

     P: holds the p

     Q: holds the q

     G: holds the g

     Y: holds the y (optional)

     X: holds the x

     This function will convert the given DSA raw parameters to the
     native ‘gnutls_x509_privkey_t’ format.  The output will be stored
     in ‘key’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_import_ecc_raw
----------------------------------

 -- Function: int gnutls_x509_privkey_import_ecc_raw
          (gnutls_x509_privkey_t KEY, gnutls_ecc_curve_t CURVE, const
          gnutls_datum_t * X, const gnutls_datum_t * Y, const
          gnutls_datum_t * K)
     KEY: The data to store the parsed key

     CURVE: holds the curve

     X: holds the x-coordinate

     Y: holds the y-coordinate

     K: holds the k

     This function will convert the given elliptic curve parameters to
     the native ‘gnutls_x509_privkey_t’ format.  The output will be
     stored in ‘key’ .  For EdDSA keys, the ‘x’ and ‘k’ values must be
     in the native to curve format.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_x509_privkey_import_gost_raw
-----------------------------------

 -- Function: int gnutls_x509_privkey_import_gost_raw
          (gnutls_x509_privkey_t KEY, gnutls_ecc_curve_t CURVE,
          gnutls_digest_algorithm_t DIGEST, gnutls_gost_paramset_t
          PARAMSET, const gnutls_datum_t * X, const gnutls_datum_t * Y,
          const gnutls_datum_t * K)
     KEY: The data to store the parsed key

     CURVE: holds the curve

     DIGEST: will hold the digest

     PARAMSET: will hold the GOST parameter set ID

     X: holds the x-coordinate

     Y: holds the y-coordinate

     K: holds the k (private key)

     This function will convert the given GOST private key's parameters
     to the native ‘gnutls_x509_privkey_t’ format.  The output will be
     stored in ‘key’ .  ‘digest’ should be one of GNUTLS_DIG_GOSR_94,
     GNUTLS_DIG_STREEBOG_256 or GNUTLS_DIG_STREEBOG_512.  If ‘paramset’
     is set to GNUTLS_GOST_PARAMSET_UNKNOWN default one will be selected
     depending on ‘digest’ .

     *Note:* parameters should be stored with least significant byte
     first.  On version 3.6.3 big-endian format was used incorrectly.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.3

gnutls_x509_privkey_import_openssl
----------------------------------

 -- Function: int gnutls_x509_privkey_import_openssl
          (gnutls_x509_privkey_t KEY, const gnutls_datum_t * DATA, const
          char * PASSWORD)
     KEY: The data to store the parsed key

     DATA: The DER or PEM encoded key.

     PASSWORD: the password to decrypt the key (if it is encrypted).

     This function will convert the given PEM encrypted to the native
     gnutls_x509_privkey_t format.  The output will be stored in ‘key’ .

     The ‘password’ should be in ASCII. If the password is not provided
     or wrong then ‘GNUTLS_E_DECRYPTION_FAILED’ will be returned.

     If the Certificate is PEM encoded it should have a header of
     "PRIVATE KEY" and the "DEK-Info" header.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_import_pkcs8
--------------------------------

 -- Function: int gnutls_x509_privkey_import_pkcs8
          (gnutls_x509_privkey_t KEY, const gnutls_datum_t * DATA,
          gnutls_x509_crt_fmt_t FORMAT, const char * PASSWORD, unsigned
          int FLAGS)
     KEY: The data to store the parsed key

     DATA: The DER or PEM encoded key.

     FORMAT: One of DER or PEM

     PASSWORD: the password to decrypt the key (if it is encrypted).

     FLAGS: 0 if encrypted or GNUTLS_PKCS_PLAIN if not encrypted.

     This function will convert the given DER or PEM encoded PKCS8 2.0
     encrypted key to the native gnutls_x509_privkey_t format.  The
     output will be stored in ‘key’ .  Both RSA and DSA keys can be
     imported, and flags can only be used to indicate an unencrypted
     key.

     The ‘password’ can be either ASCII or UTF-8 in the default PBES2
     encryption schemas, or ASCII for the PKCS12 schemas.

     If the Certificate is PEM encoded it should have a header of
     "ENCRYPTED PRIVATE KEY", or "PRIVATE KEY". You only need to specify
     the flags if the key is DER encoded, since in that case the
     encryption status cannot be auto-detected.

     If the ‘GNUTLS_PKCS_PLAIN’ flag is specified and the supplied data
     are encrypted then ‘GNUTLS_E_DECRYPTION_FAILED’ is returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_import_rsa_raw
----------------------------------

 -- Function: int gnutls_x509_privkey_import_rsa_raw
          (gnutls_x509_privkey_t KEY, const gnutls_datum_t * M, const
          gnutls_datum_t * E, const gnutls_datum_t * D, const
          gnutls_datum_t * P, const gnutls_datum_t * Q, const
          gnutls_datum_t * U)
     KEY: The data to store the parsed key

     M: holds the modulus

     E: holds the public exponent

     D: holds the private exponent

     P: holds the first prime (p)

     Q: holds the second prime (q)

     U: holds the coefficient

     This function will convert the given RSA raw parameters to the
     native ‘gnutls_x509_privkey_t’ format.  The output will be stored
     in ‘key’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_import_rsa_raw2
-----------------------------------

 -- Function: int gnutls_x509_privkey_import_rsa_raw2
          (gnutls_x509_privkey_t KEY, const gnutls_datum_t * M, const
          gnutls_datum_t * E, const gnutls_datum_t * D, const
          gnutls_datum_t * P, const gnutls_datum_t * Q, const
          gnutls_datum_t * U, const gnutls_datum_t * E1, const
          gnutls_datum_t * E2)
     KEY: The data to store the parsed key

     M: holds the modulus

     E: holds the public exponent

     D: holds the private exponent

     P: holds the first prime (p)

     Q: holds the second prime (q)

     U: holds the coefficient (optional)

     E1: holds e1 = d mod (p-1) (optional)

     E2: holds e2 = d mod (q-1) (optional)

     This function will convert the given RSA raw parameters to the
     native ‘gnutls_x509_privkey_t’ format.  The output will be stored
     in ‘key’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_init
------------------------

 -- Function: int gnutls_x509_privkey_init (gnutls_x509_privkey_t * KEY)
     KEY: A pointer to the type to be initialized

     This function will initialize a private key type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_sec_param
-----------------------------

 -- Function: gnutls_sec_param_t gnutls_x509_privkey_sec_param
          (gnutls_x509_privkey_t KEY)
     KEY: a key

     This function will return the security parameter appropriate with
     this private key.

     *Returns:* On success, a valid security parameter is returned
     otherwise ‘GNUTLS_SEC_PARAM_UNKNOWN’ is returned.

     *Since:* 2.12.0

gnutls_x509_privkey_set_flags
-----------------------------

 -- Function: void gnutls_x509_privkey_set_flags (gnutls_x509_privkey_t
          KEY, unsigned int FLAGS)
     KEY: A key of type ‘gnutls_x509_privkey_t’

     FLAGS: flags from the ‘gnutls_privkey_flags’

     This function will set flags for the specified private key, after
     it is generated.  Currently this is useful for the
     ‘GNUTLS_PRIVKEY_FLAG_EXPORT_COMPAT’ to allow exporting a "provable"
     private key in backwards compatible way.

     *Since:* 3.5.0

gnutls_x509_privkey_set_pin_function
------------------------------------

 -- Function: void gnutls_x509_privkey_set_pin_function
          (gnutls_x509_privkey_t PRIVKEY, gnutls_pin_callback_t FN, void
          * USERDATA)
     PRIVKEY: The certificate structure

     FN: the callback

     USERDATA: data associated with the callback

     This function will set a callback function to be used when it is
     required to access a protected object.  This function overrides the
     global function set using ‘gnutls_pkcs11_set_pin_function()’ .

     Note that this callback is used when decrypting a key.

     *Since:* 3.4.0

gnutls_x509_privkey_set_spki
----------------------------

 -- Function: int gnutls_x509_privkey_set_spki (gnutls_x509_privkey_t
          KEY, const gnutls_x509_spki_t SPKI, unsigned int FLAGS)
     KEY: should contain a ‘gnutls_x509_privkey_t’ type

     SPKI: a SubjectPublicKeyInfo structure of type ‘gnutls_x509_spki_t’

     FLAGS: must be zero

     This function will return the public key information of a private
     key.  The provided ‘spki’ must be initialized.

     *Returns:* Zero on success, or a negative error code on error.

gnutls_x509_privkey_sign_data
-----------------------------

 -- Function: int gnutls_x509_privkey_sign_data (gnutls_x509_privkey_t
          KEY, gnutls_digest_algorithm_t DIGEST, unsigned int FLAGS,
          const gnutls_datum_t * DATA, void * SIGNATURE, size_t *
          SIGNATURE_SIZE)
     KEY: a key

     DIGEST: should be a digest algorithm

     FLAGS: should be 0 for now

     DATA: holds the data to be signed

     SIGNATURE: will contain the signature

     SIGNATURE_SIZE: holds the size of signature (and will be replaced
     by the new size)

     This function will sign the given data using a signature algorithm
     supported by the private key.  Signature algorithms are always used
     together with a hash functions.  Different hash functions may be
     used for the RSA algorithm, but only SHA-1 for the DSA keys.

     If the buffer provided is not long enough to hold the output, then
     * ‘signature_size’ is updated and ‘GNUTLS_E_SHORT_MEMORY_BUFFER’
     will be returned.

     Use ‘gnutls_x509_crt_get_preferred_hash_algorithm()’ to determine
     the hash algorithm.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_verify_params
---------------------------------

 -- Function: int gnutls_x509_privkey_verify_params
          (gnutls_x509_privkey_t KEY)
     KEY: a key

     This function will verify the private key parameters.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_privkey_verify_seed
-------------------------------

 -- Function: int gnutls_x509_privkey_verify_seed (gnutls_x509_privkey_t
          KEY, gnutls_digest_algorithm_t DIGEST, const void * SEED,
          size_t SEED_SIZE)
     KEY: should contain a ‘gnutls_x509_privkey_t’ type

     DIGEST: it contains the digest algorithm used for key generation
     (if applicable)

     SEED: the seed of the key to be checked with

     SEED_SIZE: holds the size of ‘seed’

     This function will verify that the given private key was generated
     from the provided seed.  If ‘seed’ is ‘NULL’ then the seed stored
     in the ‘key’ 's structure will be used for verification.

     *Returns:* In case of a verification failure
     ‘GNUTLS_E_PRIVKEY_VERIFICATION_ERROR’ is returned, and zero or
     positive code on success.

     *Since:* 3.5.0

gnutls_x509_rdn_get
-------------------

 -- Function: int gnutls_x509_rdn_get (const gnutls_datum_t * IDN, char
          * BUF, size_t * BUF_SIZE)
     IDN: should contain a DER encoded RDN sequence

     BUF: a pointer to a structure to hold the peer's name

     BUF_SIZE: holds the size of ‘buf’

     This function will return the name of the given RDN sequence.  The
     name will be in the form "C=xxxx,O=yyyy,CN=zzzz" as described in
     RFC4514.

     This function does not output a fully RFC4514 compliant string, if
     that is required see ‘gnutls_x509_rdn_get2()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned, or
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ is returned and * ‘buf_size’ is
     updated if the provided buffer is not long enough, otherwise a
     negative error value.

gnutls_x509_rdn_get2
--------------------

 -- Function: int gnutls_x509_rdn_get2 (const gnutls_datum_t * IDN,
          gnutls_datum_t * STR, unsigned FLAGS)
     IDN: should contain a DER encoded RDN sequence

     STR: a datum that will hold the name

     FLAGS: zero of ‘GNUTLS_X509_DN_FLAG_COMPAT’

     This function will return the name of the given RDN sequence.  The
     name will be in the form "C=xxxx,O=yyyy,CN=zzzz" as described in
     RFC4514.

     When the flag ‘GNUTLS_X509_DN_FLAG_COMPAT’ is specified, the output
     format will match the format output by previous to 3.5.6 versions
     of GnuTLS which was not not fully RFC4514-compliant.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned, or
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ is returned and * ‘buf_size’ is
     updated if the provided buffer is not long enough, otherwise a
     negative error value.

gnutls_x509_rdn_get_by_oid
--------------------------

 -- Function: int gnutls_x509_rdn_get_by_oid (const gnutls_datum_t *
          IDN, const char * OID, unsigned INDX, unsigned int RAW_FLAG,
          void * BUF, size_t * BUF_SIZE)
     IDN: should contain a DER encoded RDN sequence

     OID: an Object Identifier

     INDX: In case multiple same OIDs exist in the RDN indicates which
     to send.  Use 0 for the first one.

     RAW_FLAG: If non-zero then the raw DER data are returned.

     BUF: a pointer to a structure to hold the peer's name

     BUF_SIZE: holds the size of ‘buf’

     This function will return the name of the given Object identifier,
     of the RDN sequence.  The name will be encoded using the rules from
     RFC4514.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned, or
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ is returned and * ‘buf_size’ is
     updated if the provided buffer is not long enough, otherwise a
     negative error value.

gnutls_x509_rdn_get_oid
-----------------------

 -- Function: int gnutls_x509_rdn_get_oid (const gnutls_datum_t * IDN,
          unsigned INDX, void * BUF, size_t * BUF_SIZE)
     IDN: should contain a DER encoded RDN sequence

     INDX: Indicates which OID to return.  Use 0 for the first one.

     BUF: a pointer to a structure to hold the peer's name OID

     BUF_SIZE: holds the size of ‘buf’

     This function will return the specified Object identifier, of the
     RDN sequence.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned, or
     ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ is returned and * ‘buf_size’ is
     updated if the provided buffer is not long enough, otherwise a
     negative error value.

     *Since:* 2.4.0

gnutls_x509_spki_deinit
-----------------------

 -- Function: void gnutls_x509_spki_deinit (gnutls_x509_spki_t SPKI)
     SPKI: the SubjectPublicKeyInfo structure

     This function will deinitialize a SubjectPublicKeyInfo structure.

     *Since:* 3.6.0

gnutls_x509_spki_get_rsa_oaep_params
------------------------------------

 -- Function: int gnutls_x509_spki_get_rsa_oaep_params
          (gnutls_x509_spki_t SPKI, gnutls_digest_algorithm_t * DIG,
          gnutls_datum_t * LABEL)
     SPKI: the SubjectPublicKeyInfo structure

     DIG: if non-NULL, it will hold the digest algorithm

     LABEL: if non-NULL, it will hold the pointer to label

     This function will get the public key algorithm parameters of
     RSA-OAEP type.

     *Returns:* zero if the parameters are present or a negative value
     on error.

     *Since:* 3.8.4

gnutls_x509_spki_get_rsa_pss_params
-----------------------------------

 -- Function: int gnutls_x509_spki_get_rsa_pss_params
          (gnutls_x509_spki_t SPKI, gnutls_digest_algorithm_t * DIG,
          unsigned int * SALT_SIZE)
     SPKI: the SubjectPublicKeyInfo structure

     DIG: if non-NULL, it will hold the digest algorithm

     SALT_SIZE: if non-NULL, it will hold the salt size

     This function will get the public key algorithm parameters of
     RSA-PSS type.

     *Returns:* zero if the parameters are present or a negative value
     on error.

     *Since:* 3.6.0

gnutls_x509_spki_init
---------------------

 -- Function: int gnutls_x509_spki_init (gnutls_x509_spki_t * SPKI)
     SPKI: A pointer to the type to be initialized

     This function will initialize a SubjectPublicKeyInfo structure used
     in PKIX. The structure is used to set additional parameters in the
     public key information field of a certificate.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.0

gnutls_x509_spki_set_rsa_oaep_params
------------------------------------

 -- Function: int gnutls_x509_spki_set_rsa_oaep_params
          (gnutls_x509_spki_t SPKI, gnutls_digest_algorithm_t DIG, const
          gnutls_datum_t * LABEL)
     SPKI: the SubjectPublicKeyInfo structure

     DIG: a digest algorithm of type ‘gnutls_digest_algorithm_t’

     LABEL: optional label

     This function will set the public key parameters for an RSA-OAEP
     algorithm, in the SubjectPublicKeyInfo structure.

     *Returns:* zero if the parameters are present or a negative value
     on error.

     *Since:* 3.8.4

gnutls_x509_spki_set_rsa_pss_params
-----------------------------------

 -- Function: void gnutls_x509_spki_set_rsa_pss_params
          (gnutls_x509_spki_t SPKI, gnutls_digest_algorithm_t DIG,
          unsigned int SALT_SIZE)
     SPKI: the SubjectPublicKeyInfo structure

     DIG: a digest algorithm of type ‘gnutls_digest_algorithm_t’

     SALT_SIZE: the size of salt string

     This function will set the public key parameters for an RSA-PSS
     algorithm, in the SubjectPublicKeyInfo structure.

     *Since:* 3.6.0

gnutls_x509_tlsfeatures_add
---------------------------

 -- Function: int gnutls_x509_tlsfeatures_add (gnutls_x509_tlsfeatures_t
          F, unsigned int FEATURE)
     F: The TLS features

     FEATURE: The feature to add

     This function will append a feature to the X.509 TLS features
     extension structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.1

gnutls_x509_tlsfeatures_check_crt
---------------------------------

 -- Function: unsigned gnutls_x509_tlsfeatures_check_crt
          (gnutls_x509_tlsfeatures_t FEAT, gnutls_x509_crt_t CERT)
     FEAT: a set of TLSFeatures

     CERT: the certificate to be checked

     This function will check the provided certificate against the
     TLSFeatures set in ‘feat’ using the RFC7633 p.4.2.2 rules.  It will
     check whether the certificate contains the features in ‘feat’ or a
     superset.

     *Returns:* non-zero if the provided certificate complies, and zero
     otherwise.

     *Since:* 3.5.1

gnutls_x509_tlsfeatures_deinit
------------------------------

 -- Function: void gnutls_x509_tlsfeatures_deinit
          (gnutls_x509_tlsfeatures_t F)
     F: The TLS features

     This function will deinitialize a X.509 TLS features extension
     structure

     *Since:* 3.5.1

gnutls_x509_tlsfeatures_get
---------------------------

 -- Function: int gnutls_x509_tlsfeatures_get (gnutls_x509_tlsfeatures_t
          F, unsigned IDX, unsigned int * FEATURE)
     F: The TLS features

     IDX: The index of the feature to get

     FEATURE: If the function succeeds, the feature will be stored in
     this variable

     This function will get a feature from the X.509 TLS features
     extension structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.1

gnutls_x509_tlsfeatures_init
----------------------------

 -- Function: int gnutls_x509_tlsfeatures_init
          (gnutls_x509_tlsfeatures_t * F)
     F: The TLS features

     This function will initialize a X.509 TLS features extension
     structure

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.1

gnutls_x509_trust_list_add_cas
------------------------------

 -- Function: int gnutls_x509_trust_list_add_cas
          (gnutls_x509_trust_list_t LIST, const gnutls_x509_crt_t *
          CLIST, unsigned CLIST_SIZE, unsigned int FLAGS)
     LIST: The list

     CLIST: A list of CAs

     CLIST_SIZE: The length of the CA list

     FLAGS: flags from ‘gnutls_trust_list_flags_t’

     This function will add the given certificate authorities to the
     trusted list.  The CAs in ‘clist’ must not be deinitialized during
     the lifetime of ‘list’ .

     If the flag ‘GNUTLS_TL_NO_DUPLICATES’ is specified, then this
     function will ensure that no duplicates will be present in the
     final trust list.

     If the flag ‘GNUTLS_TL_NO_DUPLICATE_KEY’ is specified, then this
     function will ensure that no certificates with the same key are
     present in the final trust list.

     If either ‘GNUTLS_TL_NO_DUPLICATE_KEY’ or ‘GNUTLS_TL_NO_DUPLICATES’
     are given, ‘gnutls_x509_trust_list_deinit()’ must be called with
     parameter ‘all’ being 1.

     *Returns:* The number of added elements is returned; that includes
     duplicate entries.

     *Since:* 3.0.0

gnutls_x509_trust_list_add_crls
-------------------------------

 -- Function: int gnutls_x509_trust_list_add_crls
          (gnutls_x509_trust_list_t LIST, const gnutls_x509_crl_t *
          CRL_LIST, unsigned CRL_SIZE, unsigned int FLAGS, unsigned int
          VERIFICATION_FLAGS)
     LIST: The list

     CRL_LIST: A list of CRLs

     CRL_SIZE: The length of the CRL list

     FLAGS: flags from ‘gnutls_trust_list_flags_t’

     VERIFICATION_FLAGS: gnutls_certificate_verify_flags if flags
     specifies GNUTLS_TL_VERIFY_CRL

     This function will add the given certificate revocation lists to
     the trusted list.  The CRLs in ‘crl_list’ must not be deinitialized
     during the lifetime of ‘list’ .

     This function must be called after
     ‘gnutls_x509_trust_list_add_cas()’ to allow verifying the CRLs for
     validity.  If the flag ‘GNUTLS_TL_NO_DUPLICATES’ is given, then the
     final CRL list will not contain duplicate entries.

     If the flag ‘GNUTLS_TL_NO_DUPLICATES’ is given,
     ‘gnutls_x509_trust_list_deinit()’ must be called with parameter
     ‘all’ being 1.

     If flag ‘GNUTLS_TL_VERIFY_CRL’ is given the CRLs will be verified
     before being added, and if verification fails, they will be
     skipped.

     *Returns:* The number of added elements is returned; that includes
     duplicate entries.

     *Since:* 3.0

gnutls_x509_trust_list_add_named_crt
------------------------------------

 -- Function: int gnutls_x509_trust_list_add_named_crt
          (gnutls_x509_trust_list_t LIST, gnutls_x509_crt_t CERT, const
          void * NAME, size_t NAME_SIZE, unsigned int FLAGS)
     LIST: The list

     CERT: A certificate

     NAME: An identifier for the certificate

     NAME_SIZE: The size of the identifier

     FLAGS: should be 0.

     This function will add the given certificate to the trusted list
     and associate it with a name.  The certificate will not be be used
     for verification with ‘gnutls_x509_trust_list_verify_crt()’ but
     with ‘gnutls_x509_trust_list_verify_named_crt()’ or
     ‘gnutls_x509_trust_list_verify_crt2()’ - the latter only since
     GnuTLS 3.4.0 and if a hostname is provided.

     In principle this function can be used to set individual "server"
     certificates that are trusted by the user for that specific server
     but for no other purposes.

     The certificate ‘cert’ must not be deinitialized during the
     lifetime of the ‘list’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0.0

gnutls_x509_trust_list_add_system_trust
---------------------------------------

 -- Function: int gnutls_x509_trust_list_add_system_trust
          (gnutls_x509_trust_list_t LIST, unsigned int TL_FLAGS,
          unsigned int TL_VFLAGS)
     LIST: The structure of the list

     TL_FLAGS: GNUTLS_TL_*

     TL_VFLAGS: gnutls_certificate_verify_flags if flags specifies
     GNUTLS_TL_VERIFY_CRL

     This function adds the system's default trusted certificate
     authorities to the trusted list.  Note that on unsupported systems
     this function returns ‘GNUTLS_E_UNIMPLEMENTED_FEATURE’ .

     This function implies the flag ‘GNUTLS_TL_NO_DUPLICATES’ .

     *Returns:* The number of added elements or a negative error code on
     error.

     *Since:* 3.1

gnutls_x509_trust_list_add_trust_dir
------------------------------------

 -- Function: int gnutls_x509_trust_list_add_trust_dir
          (gnutls_x509_trust_list_t LIST, const char * CA_DIR, const
          char * CRL_DIR, gnutls_x509_crt_fmt_t TYPE, unsigned int
          TL_FLAGS, unsigned int TL_VFLAGS)
     LIST: The list

     CA_DIR: A directory containing the CAs (optional)

     CRL_DIR: A directory containing a list of CRLs (optional)

     TYPE: The format of the certificates

     TL_FLAGS: flags from ‘gnutls_trust_list_flags_t’

     TL_VFLAGS: gnutls_certificate_verify_flags if flags specifies
     GNUTLS_TL_VERIFY_CRL

     This function will add the given certificate authorities to the
     trusted list.  Only directories are accepted by this function.

     *Returns:* The number of added elements is returned.

     *Since:* 3.3.6

gnutls_x509_trust_list_add_trust_file
-------------------------------------

 -- Function: int gnutls_x509_trust_list_add_trust_file
          (gnutls_x509_trust_list_t LIST, const char * CA_FILE, const
          char * CRL_FILE, gnutls_x509_crt_fmt_t TYPE, unsigned int
          TL_FLAGS, unsigned int TL_VFLAGS)
     LIST: The list

     CA_FILE: A file containing a list of CAs (optional)

     CRL_FILE: A file containing a list of CRLs (optional)

     TYPE: The format of the certificates

     TL_FLAGS: flags from ‘gnutls_trust_list_flags_t’

     TL_VFLAGS: gnutls_certificate_verify_flags if flags specifies
     GNUTLS_TL_VERIFY_CRL

     This function will add the given certificate authorities to the
     trusted list.  PKCS ‘11’ URLs are also accepted, instead of files,
     by this function.  A PKCS ‘11’ URL implies a trust database (a
     specially marked module in p11-kit); the URL "pkcs11:" implies all
     trust databases in the system.  Only a single URL specifying trust
     databases can be set; they cannot be stacked with multiple calls.

     *Returns:* The number of added elements is returned.

     *Since:* 3.1

gnutls_x509_trust_list_add_trust_mem
------------------------------------

 -- Function: int gnutls_x509_trust_list_add_trust_mem
          (gnutls_x509_trust_list_t LIST, const gnutls_datum_t * CAS,
          const gnutls_datum_t * CRLS, gnutls_x509_crt_fmt_t TYPE,
          unsigned int TL_FLAGS, unsigned int TL_VFLAGS)
     LIST: The list

     CAS: A buffer containing a list of CAs (optional)

     CRLS: A buffer containing a list of CRLs (optional)

     TYPE: The format of the certificates

     TL_FLAGS: flags from ‘gnutls_trust_list_flags_t’

     TL_VFLAGS: gnutls_certificate_verify_flags if flags specifies
     GNUTLS_TL_VERIFY_CRL

     This function will add the given certificate authorities to the
     trusted list.

     If this function is used ‘gnutls_x509_trust_list_deinit()’ must be
     called with parameter ‘all’ being 1.

     *Returns:* The number of added elements is returned.

     *Since:* 3.1

gnutls_x509_trust_list_deinit
-----------------------------

 -- Function: void gnutls_x509_trust_list_deinit
          (gnutls_x509_trust_list_t LIST, unsigned int ALL)
     LIST: The list to be deinitialized

     ALL: if non-zero it will deinitialize all the certificates and CRLs
     contained in the structure.

     This function will deinitialize a trust list.  Note that the ‘all’
     flag should be typically non-zero unless you have specified your
     certificates using ‘gnutls_x509_trust_list_add_cas()’ and you want
     to prevent them from being deinitialized by this function.

     *Since:* 3.0.0

gnutls_x509_trust_list_get_issuer
---------------------------------

 -- Function: int gnutls_x509_trust_list_get_issuer
          (gnutls_x509_trust_list_t LIST, gnutls_x509_crt_t CERT,
          gnutls_x509_crt_t * ISSUER, unsigned int FLAGS)
     LIST: The list

     CERT: is the certificate to find issuer for

     ISSUER: Will hold the issuer if any.  Should be treated as constant
     unless ‘GNUTLS_TL_GET_COPY’ is set in ‘flags’ .

     FLAGS: flags from ‘gnutls_trust_list_flags_t’ (‘GNUTLS_TL_GET_COPY’
     is applicable)

     This function will find the issuer of the given certificate.  If
     the flag ‘GNUTLS_TL_GET_COPY’ is specified a copy of the issuer
     will be returned which must be freed using
     ‘gnutls_x509_crt_deinit()’ .  In that case the provided ‘issuer’
     must not be initialized.

     Note that the flag ‘GNUTLS_TL_GET_COPY’ is required for this
     function to work with PKCS‘11’ trust lists in a thread-safe way.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_x509_trust_list_get_issuer_by_dn
---------------------------------------

 -- Function: int gnutls_x509_trust_list_get_issuer_by_dn
          (gnutls_x509_trust_list_t LIST, const gnutls_datum_t * DN,
          gnutls_x509_crt_t * ISSUER, unsigned int FLAGS)
     LIST: The list

     DN: is the issuer's DN

     ISSUER: Will hold the issuer if any.  Should be deallocated after
     use.

     FLAGS: Use zero

     This function will find the issuer with the given name, and return
     a copy of the issuer, which must be freed using
     ‘gnutls_x509_crt_deinit()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_x509_trust_list_get_issuer_by_subject_key_id
---------------------------------------------------

 -- Function: int gnutls_x509_trust_list_get_issuer_by_subject_key_id
          (gnutls_x509_trust_list_t LIST, const gnutls_datum_t * DN,
          const gnutls_datum_t * SPKI, gnutls_x509_crt_t * ISSUER,
          unsigned int FLAGS)
     LIST: The list

     DN: is the issuer's DN (may be ‘NULL’ )

     SPKI: is the subject key ID

     ISSUER: Will hold the issuer if any.  Should be deallocated after
     use.

     FLAGS: Use zero

     This function will find the issuer with the given name and subject
     key ID, and return a copy of the issuer, which must be freed using
     ‘gnutls_x509_crt_deinit()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.2

gnutls_x509_trust_list_get_ptr
------------------------------

 -- Function: void * gnutls_x509_trust_list_get_ptr
          (gnutls_x509_trust_list_t TLIST)
     TLIST: is a ‘gnutls_x509_trust_list_t’ type.

     Get user pointer for tlist.  Useful in callback function
     gnutls_x509_trust_list_set_getissuer_function.  This is the pointer
     set with ‘gnutls_x509_trust_list_set_ptr()’ .

     *Returns:* the user given pointer from the tlist structure, or
     ‘NULL’ if it was never set.

     *Since:* 3.7.0

gnutls_x509_trust_list_init
---------------------------

 -- Function: int gnutls_x509_trust_list_init (gnutls_x509_trust_list_t
          * LIST, unsigned int SIZE)
     LIST: A pointer to the type to be initialized

     SIZE: The size of the internal hash table.  Use (0) for default
     size.

     This function will initialize an X.509 trust list structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0.0

gnutls_x509_trust_list_iter_deinit
----------------------------------

 -- Function: void gnutls_x509_trust_list_iter_deinit
          (gnutls_x509_trust_list_iter_t ITER)
     ITER: The iterator structure to be deinitialized

     This function will deinitialize an iterator structure.

     *Since:* 3.4.0

gnutls_x509_trust_list_iter_get_ca
----------------------------------

 -- Function: int gnutls_x509_trust_list_iter_get_ca
          (gnutls_x509_trust_list_t LIST, gnutls_x509_trust_list_iter_t
          * ITER, gnutls_x509_crt_t * CRT)
     LIST: The list

     ITER: A pointer to an iterator (initially the iterator should be
     ‘NULL’ )

     CRT: where the certificate will be copied

     This function obtains a certificate in the trust list and advances
     the iterator to the next certificate.  The certificate returned in
     ‘crt’ must be deallocated with ‘gnutls_x509_crt_deinit()’ .

     When past the last element is accessed
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned and the
     iterator is reset.

     The iterator is deinitialized and reset to ‘NULL’ automatically by
     this function after iterating through all elements until
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.  If the
     iteration is aborted early, it must be manually deinitialized using
     ‘gnutls_x509_trust_list_iter_deinit()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_x509_trust_list_remove_cas
---------------------------------

 -- Function: int gnutls_x509_trust_list_remove_cas
          (gnutls_x509_trust_list_t LIST, const gnutls_x509_crt_t *
          CLIST, unsigned CLIST_SIZE)
     LIST: The list

     CLIST: A list of CAs

     CLIST_SIZE: The length of the CA list

     This function will remove the given certificate authorities from
     the trusted list.

     Note that this function can accept certificates and authorities not
     yet known.  In that case they will be kept in a separate block list
     that will be used during certificate verification.  Unlike
     ‘gnutls_x509_trust_list_add_cas()’ there is no deinitialization
     restriction for certificate list provided in this function.

     *Returns:* The number of removed elements is returned.

     *Since:* 3.1.10

gnutls_x509_trust_list_remove_trust_file
----------------------------------------

 -- Function: int gnutls_x509_trust_list_remove_trust_file
          (gnutls_x509_trust_list_t LIST, const char * CA_FILE,
          gnutls_x509_crt_fmt_t TYPE)
     LIST: The list

     CA_FILE: A file containing a list of CAs

     TYPE: The format of the certificates

     This function will remove the given certificate authorities from
     the trusted list, and add them into a block list when needed.  PKCS
     11 URLs are also accepted, instead of files, by this function.

     See also ‘gnutls_x509_trust_list_remove_cas()’ .

     *Returns:* The number of added elements is returned.

     *Since:* 3.1.10

gnutls_x509_trust_list_remove_trust_mem
---------------------------------------

 -- Function: int gnutls_x509_trust_list_remove_trust_mem
          (gnutls_x509_trust_list_t LIST, const gnutls_datum_t * CAS,
          gnutls_x509_crt_fmt_t TYPE)
     LIST: The list

     CAS: A buffer containing a list of CAs (optional)

     TYPE: The format of the certificates

     This function will remove the provided certificate authorities from
     the trusted list, and add them into a block list when needed.

     See also ‘gnutls_x509_trust_list_remove_cas()’ .

     *Returns:* The number of removed elements is returned.

     *Since:* 3.1.10

gnutls_x509_trust_list_set_getissuer_function
---------------------------------------------

 -- Function: void gnutls_x509_trust_list_set_getissuer_function
          (gnutls_x509_trust_list_t TLIST,
          gnutls_x509_trust_list_getissuer_function * FUNC)
     TLIST: is a ‘gnutls_x509_trust_list_t’ type.

     FUNC: is the callback function

     This function sets a callback to be called when the peer's
     certificate chain is incomplete due a missing intermediate
     certificate.  The callback may provide the missing certificate for
     use during verification.

     The callback's function prototype is defined in gnutls/x509.h as:

     int (*callback)(gnutls_x509_trust_list_t list, const
     gnutls_x509_crt_t cert, gnutls_x509_crt_t **issuers, unsigned int
     *issuers_size);

     If the callback function is provided then gnutls will call it
     during the certificate verification procedure.  The callback may
     wish to use ‘gnutls_x509_crt_get_authority_info_access()’ to get a
     URI from which to attempt to download the missing issuer
     certificate, if available.

     On a successful call, the callback shall set '*issuers' and
     '*issuers_size' even if the result is empty; in that case
     '*issuers' will point to ‘NULL’ and '*issuers_size' will be 0.
     Otherwise, the '*issuers' array shall be allocated using
     ‘gnutls_x509_crt_list_import2()’ .  The ownership of both the array
     and the elements is transferred to the caller and thus the
     application does not need to maintain the memory after the call.

     The callback function should return 0 if the attempt to retrieve
     the issuer certificates for 'crt' succeeded, or non-zero to
     indicate any error occurred during the attempt.  In the latter
     case, '*issuers' and '*issuers_size' are not set.

     *Since:* 3.7.0

gnutls_x509_trust_list_set_ptr
------------------------------

 -- Function: void gnutls_x509_trust_list_set_ptr
          (gnutls_x509_trust_list_t TLIST, void * PTR)
     TLIST: is a ‘gnutls_x509_trust_list_t’ type.

     PTR: is the user pointer

     This function will set (associate) the user given pointer ‘ptr’ to
     the tlist structure.  This pointer can be accessed with
     ‘gnutls_x509_trust_list_get_ptr()’ .  Useful in the callback
     function gnutls_x509_trust_list_set_getissuer_function.

     *Since:* 3.7.0

gnutls_x509_trust_list_verify_crt
---------------------------------

 -- Function: int gnutls_x509_trust_list_verify_crt
          (gnutls_x509_trust_list_t LIST, gnutls_x509_crt_t * CERT_LIST,
          unsigned int CERT_LIST_SIZE, unsigned int FLAGS, unsigned int
          * VOUTPUT, gnutls_verify_output_function FUNC)
     LIST: The list

     CERT_LIST: is the certificate list to be verified

     CERT_LIST_SIZE: is the certificate list size

     FLAGS: Flags that may be used to change the verification algorithm.
     Use OR of the gnutls_certificate_verify_flags enumerations.

     VOUTPUT: will hold the certificate verification output.

     FUNC: If non-null will be called on each chain element verification
     with the output.

     This function will try to verify the given certificate and return
     its status.  The ‘voutput’ parameter will hold an OR'ed sequence of
     ‘gnutls_certificate_status_t’ flags.

     The details of the verification are the same as in
     ‘gnutls_x509_trust_list_verify_crt2()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_x509_trust_list_verify_crt2
----------------------------------

 -- Function: int gnutls_x509_trust_list_verify_crt2
          (gnutls_x509_trust_list_t LIST, gnutls_x509_crt_t * CERT_LIST,
          unsigned int CERT_LIST_SIZE, gnutls_typed_vdata_st * DATA,
          unsigned int ELEMENTS, unsigned int FLAGS, unsigned int *
          VOUTPUT, gnutls_verify_output_function FUNC)
     LIST: The list

     CERT_LIST: is the certificate list to be verified

     CERT_LIST_SIZE: is the certificate list size

     DATA: an array of typed data

     ELEMENTS: the number of data elements

     FLAGS: Flags that may be used to change the verification algorithm.
     Use OR of the gnutls_certificate_verify_flags enumerations.

     VOUTPUT: will hold the certificate verification output.

     FUNC: If non-null will be called on each chain element verification
     with the output.

     This function will attempt to verify the given certificate chain
     and return its status.  The ‘voutput’ parameter will hold an OR'ed
     sequence of ‘gnutls_certificate_status_t’ flags.

     When a certificate chain of ‘cert_list_size’ with more than one
     certificates is provided, the verification status will apply to the
     first certificate in the chain that failed verification.  The
     verification process starts from the end of the chain (from CA to
     end certificate).  The first certificate in the chain must be the
     end-certificate while the rest of the members may be sorted or not.

     Additionally a certificate verification profile can be specified
     from the ones in ‘gnutls_certificate_verification_profiles_t’ by
     ORing the result of ‘GNUTLS_PROFILE_TO_VFLAGS()’ to the
     verification flags.

     Additional verification parameters are possible via the ‘data’
     types; the acceptable types are ‘GNUTLS_DT_DNS_HOSTNAME’ ,
     ‘GNUTLS_DT_IP_ADDRESS’ and ‘GNUTLS_DT_KEY_PURPOSE_OID’ .  The
     former accepts as data a null-terminated hostname, and the latter a
     null-terminated object identifier (e.g., ‘GNUTLS_KP_TLS_WWW_SERVER’
     ).  If a DNS hostname is provided then this function will compare
     the hostname in the end certificate against the given.  If names do
     not match the ‘GNUTLS_CERT_UNEXPECTED_OWNER’ status flag will be
     set.  In addition it will consider certificates provided with
     ‘gnutls_x509_trust_list_add_named_crt()’ .

     If a key purpose OID is provided and the end-certificate contains
     the extended key usage PKIX extension, it will be required to match
     the provided OID or be marked for any purpose, otherwise
     verification will fail with ‘GNUTLS_CERT_PURPOSE_MISMATCH’ status.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  Note that verification failure
     will not result to an error code, only ‘voutput’ will be updated.

     *Since:* 3.3.8

gnutls_x509_trust_list_verify_named_crt
---------------------------------------

 -- Function: int gnutls_x509_trust_list_verify_named_crt
          (gnutls_x509_trust_list_t LIST, gnutls_x509_crt_t CERT, const
          void * NAME, size_t NAME_SIZE, unsigned int FLAGS, unsigned
          int * VOUTPUT, gnutls_verify_output_function FUNC)
     LIST: The list

     CERT: is the certificate to be verified

     NAME: is the certificate's name

     NAME_SIZE: is the certificate's name size

     FLAGS: Flags that may be used to change the verification algorithm.
     Use OR of the gnutls_certificate_verify_flags enumerations.

     VOUTPUT: will hold the certificate verification output.

     FUNC: If non-null will be called on each chain element verification
     with the output.

     This function will try to find a certificate that is associated
     with the provided name -see
     ‘gnutls_x509_trust_list_add_named_crt()’ .  If a match is found the
     certificate is considered valid.  In addition to that this function
     will also check CRLs.  The ‘voutput’ parameter will hold an OR'ed
     sequence of ‘gnutls_certificate_status_t’ flags.

     Additionally a certificate verification profile can be specified
     from the ones in ‘gnutls_certificate_verification_profiles_t’ by
     ORing the result of ‘GNUTLS_PROFILE_TO_VFLAGS()’ to the
     verification flags.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0.0

