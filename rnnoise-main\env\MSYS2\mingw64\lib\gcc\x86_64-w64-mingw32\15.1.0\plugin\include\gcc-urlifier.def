/* Keep this file sorted.  */
DOC_URL ("#pragma GCC diagnostic", "gcc/Diagnostic-Pragmas.html"),
DOC_URL ("#pragma GCC diagnostic ignored_attributes", "gcc/Diagnostic-Pragmas.html"),
DOC_URL ("#pragma GCC ivdep", "gcc/Loop-Specific-Pragmas.html#index-pragma-GCC-ivdep"),
DOC_URL ("#pragma GCC novector", "gcc/Loop-Specific-Pragmas.html#index-pragma-GCC-novector"),
DOC_URL ("#pragma GCC optimize", "gcc/Function-Specific-Option-Pragmas.html#index-pragma-GCC-optimize"),
DOC_URL ("#pragma GCC pop_options", "gcc/Push_002fPop-Macro-Pragmas.html"),
DOC_URL ("#pragma GCC push_options", "gcc/Push_002fPop-Macro-Pragmas.html"),
DOC_URL ("#pragma GCC reset_options", "gcc/Function-Specific-Option-Pragmas.html#index-pragma-GCC-reset_005foptions"),
DOC_URL ("#pragma GCC target", "gcc/Function-Specific-Option-Pragmas.html#index-pragma-GCC-target"),
DOC_URL ("#pragma GCC unroll", "gcc/Loop-Specific-Pragmas.html#index-pragma-GCC-unroll-n"),
DOC_URL ("#pragma GCC visibility", "gcc/Visibility-Pragmas.html"),
DOC_URL ("#pragma GCC visibility pop", "gcc/Visibility-Pragmas.html"),
DOC_URL ("#pragma message", "gcc/Diagnostic-Pragmas.html"),
DOC_URL ("#pragma pack", "gcc/Structure-Layout-Pragmas.html"),
DOC_URL ("#pragma redefine_extname", "gcc/Symbol-Renaming-Pragmas.html"),
DOC_URL ("#pragma scalar_storage_order", "gcc/Structure-Layout-Pragmas.html"),
DOC_URL ("#pragma weak", "gcc/Weak-Pragmas.html"),
