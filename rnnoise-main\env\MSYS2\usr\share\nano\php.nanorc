## Syntax highlighting for PHP.

## Original author:  <PERSON>
## License:  GPL version 3 or newer

syntax php "\.(php[23457s~]?|phtml|ctp)$"
magic "PHP script"
comment "//"

# PHP markings.
color brightgreen "(<\?(php|=)?|\?>)"

# Function names.
color white "\<[[:alpha:]_][[:alnum:]_]*\("
# Variable names.
color cyan "\$[[:alpha:]_][[:alnum:]_]*"

# Types.
color green "\<(array|bool|callable|const|float|global|int|object|string|var)\>"

# Directives and structure.
color brightcyan "\<(abstract|as|class|clone|(end)?declare|extends|function|implements|include(_once)?|inst(ance|ead)of|interface|namespace|new|private|protected|public|require(_once)?|static|trait|use|yield)\>"
color brightcyan "\<(case|catch|default|do|echo|else(if)?|end(for(each)?|if|switch|while)|final(ly)?|for(each)?|if|print|switch|throw|try|while)\>"
# Operators.
color brightcyan "\<(and|or|xor)\>"

# Control flow.
color magenta "\<(break|continue|goto|return)\>"

# Strings.
color brightyellow ""([^"\]|\\.)*"|'([^'\]|\\.)*'"

# Comments.
color brightblue "(^|[[:blank:]]+)//.*"
color brightblue start="/\*" end="\*/"

# Trailing whitespace.
color ,green "[[:space:]]+$"
