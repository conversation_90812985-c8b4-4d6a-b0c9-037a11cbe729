.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_transport_set_push_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_transport_set_push_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_transport_set_push_function(gnutls_session_t " session ", gnutls_push_func " push_func ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_push_func push_func" 12
a callback function similar to \fBwrite()\fP
.SH "DESCRIPTION"
This is the function where you set a push function for gnutls to
use in order to send data.  If you are going to use berkeley style
sockets, you do not need to use this function since the default
send(2) will probably be ok.  Otherwise you should specify this
function for gnutls to be able to send data.
The callback should return a positive number indicating the
bytes sent, and \-1 on error.

 \fIpush_func\fP is of the form,
ssize_t (*gnutls_push_func)(gnutls_transport_ptr_t, const void*, size_t);
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
