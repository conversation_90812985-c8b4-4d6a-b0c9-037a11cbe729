.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_psk_set_server_credentials_hint" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_psk_set_server_credentials_hint \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_psk_set_server_credentials_hint(gnutls_psk_server_credentials_t " res ", const char * " hint ");"
.SH ARGUMENTS
.IP "gnutls_psk_server_credentials_t res" 12
is a \fBgnutls_psk_server_credentials_t\fP type.
.IP "const char * hint" 12
is the PSK identity hint string
.SH "DESCRIPTION"
This function sets the identity hint, in a
\fBgnutls_psk_server_credentials_t\fP type.  This hint is sent to
the client to help it chose a good PSK credential (i.e., username
and password).
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "SINCE"
2.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
