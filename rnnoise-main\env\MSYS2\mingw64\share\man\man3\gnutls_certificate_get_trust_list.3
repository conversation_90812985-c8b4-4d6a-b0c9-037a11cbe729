.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_get_trust_list" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_get_trust_list \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "void gnutls_certificate_get_trust_list(gnutls_certificate_credentials_t " res ", gnutls_x509_trust_list_t * " tlist ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t res" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "gnutls_x509_trust_list_t * tlist" 12
Location where to store the trust list.
.SH "DESCRIPTION"
Obtains the list of trusted certificates stored in  \fIres\fP and writes a
pointer to it to the location  \fItlist\fP . The pointer will point to memory
internal to  \fIres\fP , and must not be deinitialized. It will be automatically
deallocated when the  \fIres\fP structure is deinitialized.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
