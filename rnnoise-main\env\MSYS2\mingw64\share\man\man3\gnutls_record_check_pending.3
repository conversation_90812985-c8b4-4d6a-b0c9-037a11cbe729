.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_check_pending" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_check_pending \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "size_t gnutls_record_check_pending(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
This function checks if there are unread data
in the gnutls buffers. If the return value is
non\-zero the next call to \fBgnutls_record_recv()\fP
is guaranteed not to block.
.SH "RETURNS"
Returns the size of the data or zero.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
