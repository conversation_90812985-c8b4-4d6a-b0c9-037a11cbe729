.. cmake-manual-description: CMake Environment Variables Reference

cmake-env-variables(7)
**********************

.. only:: html

   .. contents::

This page lists environment variables that have special
meaning to CMake.

For general information on environment variables, see the
:ref:`Environment Variables <CMake Language Environment Variables>`
section in the cmake-language manual.

Environment Variables that Change Behavior
==========================================

.. toctree::
   :maxdepth: 1

   /envvar/CLICOLOR
   /envvar/CLICOLOR_FORCE
   /envvar/CMAKE_APPBUNDLE_PATH
   /envvar/CMAKE_FRAMEWORK_PATH
   /envvar/CMAKE_INCLUDE_PATH
   /envvar/CMAKE_LIBRARY_PATH
   /envvar/CMAKE_MAXIMUM_RECURSION_DEPTH
   /envvar/CMAKE_POLICY_VERSION_MINIMUM
   /envvar/CMAKE_PREFIX_PATH
   /envvar/CMAKE_PROGRAM_PATH
   /envvar/CMAKE_TLS_VERIFY
   /envvar/CMAKE_TLS_VERSION
   /envvar/SSL_CERT_DIR
   /envvar/SSL_CERT_FILE

Environment Variables that Control the Build
============================================

.. toctree::
   :maxdepth: 1

   /envvar/ADSP_ROOT
   /envvar/CMAKE_APPLE_SILICON_PROCESSOR
   /envvar/CMAKE_BUILD_PARALLEL_LEVEL
   /envvar/CMAKE_BUILD_TYPE
   /envvar/CMAKE_COLOR_DIAGNOSTICS
   /envvar/CMAKE_CONFIG_DIR
   /envvar/CMAKE_CONFIG_TYPE
   /envvar/CMAKE_CONFIGURATION_TYPES
   /envvar/CMAKE_CROSSCOMPILING_EMULATOR
   /envvar/CMAKE_EXPORT_BUILD_DATABASE
   /envvar/CMAKE_EXPORT_COMPILE_COMMANDS
   /envvar/CMAKE_GENERATOR
   /envvar/CMAKE_GENERATOR_INSTANCE
   /envvar/CMAKE_GENERATOR_PLATFORM
   /envvar/CMAKE_GENERATOR_TOOLSET
   /envvar/CMAKE_INSTALL_MODE
   /envvar/CMAKE_INSTALL_PARALLEL_LEVEL
   /envvar/CMAKE_INSTALL_PREFIX
   /envvar/CMAKE_LANG_COMPILER_LAUNCHER
   /envvar/CMAKE_LANG_IMPLICIT_LINK_DIRECTORIES_EXCLUDE
   /envvar/CMAKE_LANG_LINKER_LAUNCHER
   /envvar/CMAKE_MSVCIDE_RUN_PATH
   /envvar/CMAKE_NO_VERBOSE
   /envvar/CMAKE_OSX_ARCHITECTURES
   /envvar/CMAKE_TEST_LAUNCHER
   /envvar/CMAKE_TOOLCHAIN_FILE
   /envvar/DESTDIR
   /envvar/LDFLAGS
   /envvar/MACOSX_DEPLOYMENT_TARGET
   /envvar/PackageName_ROOT
   /envvar/VERBOSE

Environment Variables for Languages
===================================

.. toctree::
   :maxdepth: 1

   /envvar/ASM_DIALECT
   /envvar/ASM_DIALECTFLAGS
   /envvar/CC
   /envvar/CFLAGS
   /envvar/CSFLAGS
   /envvar/CUDAARCHS
   /envvar/CUDACXX
   /envvar/CUDAFLAGS
   /envvar/CUDAHOSTCXX
   /envvar/CXX
   /envvar/CXXFLAGS
   /envvar/FC
   /envvar/FFLAGS
   /envvar/HIPCXX
   /envvar/HIPFLAGS
   /envvar/HIPHOSTCXX
   /envvar/ISPC
   /envvar/ISPCFLAGS
   /envvar/OBJC
   /envvar/OBJCFLAGS
   /envvar/OBJCXX
   /envvar/OBJCXXFLAGS
   /envvar/RC
   /envvar/RCFLAGS
   /envvar/SWIFTC

Environment Variables for CTest
===============================

.. toctree::
   :maxdepth: 1

   /envvar/CMAKE_CONFIG_TYPE
   /envvar/CTEST_INTERACTIVE_DEBUG_MODE
   /envvar/CTEST_NO_TESTS_ACTION
   /envvar/CTEST_OUTPUT_ON_FAILURE
   /envvar/CTEST_PARALLEL_LEVEL
   /envvar/CTEST_PROGRESS_OUTPUT
   /envvar/CTEST_USE_INSTRUMENTATION
   /envvar/CTEST_USE_LAUNCHERS_DEFAULT
   /envvar/CTEST_USE_VERBOSE_INSTRUMENTATION
   /envvar/DASHBOARD_TEST_FROM_CTEST

Environment Variables for the CMake curses interface
====================================================

.. toctree::
   :maxdepth: 1

   /envvar/CCMAKE_COLORS
