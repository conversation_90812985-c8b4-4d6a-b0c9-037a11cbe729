<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>System interfaces compatible with Solaris or SunOS functions:</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="compatibility.html" title="Chapter&#160;1.&#160;Compatibility"><link rel="prev" href="std-gnu.html" title="System interfaces compatible with GNU or Linux extensions:"><link rel="next" href="std-iso.html" title="System interfaces not in POSIX but compatible with ISO C requirements:"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">System interfaces compatible with Solaris or SunOS functions:</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="std-gnu.html">Prev</a>&#160;</td><th width="60%" align="center">Chapter&#160;1.&#160;Compatibility</th><td width="20%" align="right">&#160;<a accesskey="n" href="std-iso.html">Next</a></td></tr></table><hr></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="std-solaris"></a>System interfaces compatible with Solaris or SunOS functions:</h2></div></div></div><pre class="screen">
    __fbufsize
    __flbf
    __fpending
    __fpurge
    __freadable
    __freading
    __fsetlocking
    __fwritable
    __fwriting
    __xdrrec_getrec		(available in external "libtirpc" library)
    __xdrrec_setnonblock	(available in external "libtirpc" library)
    acl				(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    aclcheck			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    aclfrommode			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    aclfrompbits		(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    aclfromtext			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    aclsort			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    acltomode			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    acltopbits			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    acltotext			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    endmntent
    facl			(see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    fegetprec
    fesetprec
    futimesat
    getmntent
    memalign
    setmntent
    xdr_array			(available in external "libtirpc" library)
    xdr_bool			(available in external "libtirpc" library)
    xdr_bytes			(available in external "libtirpc" library)
    xdr_char			(available in external "libtirpc" library)
    xdr_double			(available in external "libtirpc" library)
    xdr_enum			(available in external "libtirpc" library)
    xdr_float			(available in external "libtirpc" library)
    xdr_free			(available in external "libtirpc" library)
    xdr_hyper			(available in external "libtirpc" library)
    xdr_int			(available in external "libtirpc" library)
    xdr_int16_t			(available in external "libtirpc" library)
    xdr_int32_t			(available in external "libtirpc" library)
    xdr_int64_t			(available in external "libtirpc" library)
    xdr_int8_t			(available in external "libtirpc" library)
    xdr_long			(available in external "libtirpc" library)
    xdr_longlong_t		(available in external "libtirpc" library)
    xdr_netobj			(available in external "libtirpc" library)
    xdr_opaque			(available in external "libtirpc" library)
    xdr_pointer			(available in external "libtirpc" library)
    xdr_reference		(available in external "libtirpc" library)
    xdr_short			(available in external "libtirpc" library)
    xdr_sizeof			(available in external "libtirpc" library)
    xdr_string			(available in external "libtirpc" library)
    xdr_u_char			(available in external "libtirpc" library)
    xdr_u_hyper			(available in external "libtirpc" library)
    xdr_u_int			(available in external "libtirpc" library)
    xdr_u_int16_t		(available in external "libtirpc" library)
    xdr_u_int32_t		(available in external "libtirpc" library)
    xdr_u_int64_t		(available in external "libtirpc" library)
    xdr_u_int8_t		(available in external "libtirpc" library)
    xdr_u_long			(available in external "libtirpc" library)
    xdr_u_longlong_t		(available in external "libtirpc" library)
    xdr_u_short			(available in external "libtirpc" library)
    xdr_uint16_t		(available in external "libtirpc" library)
    xdr_uint32_t		(available in external "libtirpc" library)
    xdr_uint64_t		(available in external "libtirpc" library)
    xdr_uint8_t			(available in external "libtirpc" library)
    xdr_union			(available in external "libtirpc" library)
    xdr_vector			(available in external "libtirpc" library)
    xdr_void			(available in external "libtirpc" library)
    xdr_wrapstring		(available in external "libtirpc" library)
    xdrmem_create		(available in external "libtirpc" library)
    xdrrec_create		(available in external "libtirpc" library)
    xdrrec_endofrecord		(available in external "libtirpc" library)
    xdrrec_eof			(available in external "libtirpc" library)
    xdrrec_skiprecord		(available in external "libtirpc" library)
    xdrstdio_create		(available in external "libtirpc" library)
</pre></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="std-gnu.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="compatibility.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="std-iso.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">System interfaces compatible with GNU or Linux extensions:&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;System interfaces not in POSIX but compatible with ISO C requirements:</td></tr></table></div></body></html>
