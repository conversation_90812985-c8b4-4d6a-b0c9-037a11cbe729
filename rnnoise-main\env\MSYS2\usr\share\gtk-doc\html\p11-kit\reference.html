<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>API Reference: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="index.html" title="p11-kit">
<link rel="prev" href="trust.html" title="trust">
<link rel="next" href="p11-kit-Modules.html" title="Modules">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><img src="up-insensitive.png" width="16" height="16" border="0"></td>
<td><a accesskey="p" href="trust.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="p11-kit-Modules.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="chapter">
<div class="titlepage"><div><div><h1 class="title">
<a name="reference"></a>API Reference</h1></div></div></div>
<div class="toc"><dl class="toc">
<dt>
<span class="refentrytitle"><a href="p11-kit-Modules.html">Modules</a></span><span class="refpurpose"> — Module loading and initializing</span>
</dt>
<dt>
<span class="refentrytitle"><a href="p11-kit-URIs.html">URIs</a></span><span class="refpurpose"> — Parsing and formatting PKCS#11 URIs</span>
</dt>
<dt>
<span class="refentrytitle"><a href="p11-kit-PIN-Callbacks.html">PIN Callbacks</a></span><span class="refpurpose"> — PIN Callbacks</span>
</dt>
<dt>
<span class="refentrytitle"><a href="p11-kit-Utilities.html">Utilities</a></span><span class="refpurpose"> — PKCS#11 utilities</span>
</dt>
<dt>
<span class="refentrytitle"><a href="p11-kit-Future.html">Future</a></span><span class="refpurpose"> — Future Unstable API</span>
</dt>
<dt>
<span class="refentrytitle"><a href="p11-kit-Deprecated.html">Deprecated</a></span><span class="refpurpose"> — Deprecated functions</span>
</dt>
<dt><span class="index"><a href="reference.html#api-index-full">API Index</a></span></dt>
<dt><span class="glossary"><a href="reference.html#annotation-glossary">Annotation Glossary</a></span></dt>
</dl></div>
<div class="index">
<div class="titlepage"><div><div><h2 class="title">
<a name="api-index-full"></a>API Index</h2></div></div></div>
<a name="idx"></a><a name="idxB"></a><h3 class="title">B</h3>
<dt>
<a class="link" href="p11-kit-Utilities.html#p11-kit-be-loud" title="p11_kit_be_loud ()">p11_kit_be_loud</a>, function in <a class="link" href="p11-kit-Utilities.html" title="Utilities">Utilities</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Utilities.html#p11-kit-be-quiet" title="p11_kit_be_quiet ()">p11_kit_be_quiet</a>, function in <a class="link" href="p11-kit-Utilities.html" title="Utilities">Utilities</a>
</dt>
<dd></dd>
<a name="idxC"></a><h3 class="title">C</h3>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-config-option" title="p11_kit_config_option ()">p11_kit_config_option</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<a name="idxD"></a><h3 class="title">D</h3>
<dt>
<a class="link" href="p11-kit-Deprecated.html#P11-KIT-DEPRECATED-FOR:CAPS" title="P11_KIT_DEPRECATED_FOR()">P11_KIT_DEPRECATED_FOR</a>, macro in <a class="link" href="p11-kit-Deprecated.html" title="Deprecated">Deprecated</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-destroyer" title="p11_kit_destroyer ()">p11_kit_destroyer</a>, user_function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<a name="idxF"></a><h3 class="title">F</h3>
<dt>
<a class="link" href="p11-kit-Deprecated.html#p11-kit-finalize-module" title="p11_kit_finalize_module ()">p11_kit_finalize_module</a>, function in <a class="link" href="p11-kit-Deprecated.html" title="Deprecated">Deprecated</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Deprecated.html#p11-kit-finalize-registered" title="p11_kit_finalize_registered ()">p11_kit_finalize_registered</a>, function in <a class="link" href="p11-kit-Deprecated.html" title="Deprecated">Deprecated</a>
</dt>
<dd></dd>
<a name="idxI"></a><h3 class="title">I</h3>
<dt>
<a class="link" href="p11-kit-Deprecated.html#p11-kit-initialize-module" title="p11_kit_initialize_module ()">p11_kit_initialize_module</a>, function in <a class="link" href="p11-kit-Deprecated.html" title="Deprecated">Deprecated</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Deprecated.html#p11-kit-initialize-registered" title="p11_kit_initialize_registered ()">p11_kit_initialize_registered</a>, function in <a class="link" href="p11-kit-Deprecated.html" title="Deprecated">Deprecated</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter" title="p11_kit_iter">p11_kit_iter</a>, typedef in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-add-callback" title="p11_kit_iter_add_callback ()">p11_kit_iter_add_callback</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-add-filter" title="p11_kit_iter_add_filter ()">p11_kit_iter_add_filter</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-begin" title="p11_kit_iter_begin ()">p11_kit_iter_begin</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-begin-with" title="p11_kit_iter_begin_with ()">p11_kit_iter_begin_with</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-callback" title="p11_kit_iter_callback ()">p11_kit_iter_callback</a>, user_function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-destroy-object" title="p11_kit_iter_destroy_object ()">p11_kit_iter_destroy_object</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-free" title="p11_kit_iter_free ()">p11_kit_iter_free</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-attributes" title="p11_kit_iter_get_attributes ()">p11_kit_iter_get_attributes</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-kind" title="p11_kit_iter_get_kind ()">p11_kit_iter_get_kind</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-module" title="p11_kit_iter_get_module ()">p11_kit_iter_get_module</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-object" title="p11_kit_iter_get_object ()">p11_kit_iter_get_object</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-session" title="p11_kit_iter_get_session ()">p11_kit_iter_get_session</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-slot" title="p11_kit_iter_get_slot ()">p11_kit_iter_get_slot</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-slot-info" title="p11_kit_iter_get_slot_info ()">p11_kit_iter_get_slot_info</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-get-token" title="p11_kit_iter_get_token ()">p11_kit_iter_get_token</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-keep-session" title="p11_kit_iter_keep_session ()">p11_kit_iter_keep_session</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-load-attributes" title="p11_kit_iter_load_attributes ()">p11_kit_iter_load_attributes</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-new" title="p11_kit_iter_new ()">p11_kit_iter_new</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-next" title="p11_kit_iter_next ()">p11_kit_iter_next</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-iter-set-uri" title="p11_kit_iter_set_uri ()">p11_kit_iter_set_uri</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<a name="idxL"></a><h3 class="title">L</h3>
<dt>
<a class="link" href="p11-kit-Deprecated.html#p11-kit-load-initialize-module" title="p11_kit_load_initialize_module ()">p11_kit_load_initialize_module</a>, function in <a class="link" href="p11-kit-Deprecated.html" title="Deprecated">Deprecated</a>
</dt>
<dd></dd>
<a name="idxM"></a><h3 class="title">M</h3>
<dt>
<a class="link" href="p11-kit-Utilities.html#p11-kit-message" title="p11_kit_message ()">p11_kit_message</a>, function in <a class="link" href="p11-kit-Utilities.html" title="Utilities">Utilities</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-finalize" title="p11_kit_modules_finalize ()">p11_kit_modules_finalize</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-finalize-and-release" title="p11_kit_modules_finalize_and_release ()">p11_kit_modules_finalize_and_release</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-initialize" title="p11_kit_modules_initialize ()">p11_kit_modules_initialize</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-load" title="p11_kit_modules_load ()">p11_kit_modules_load</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-load-and-initialize" title="p11_kit_modules_load_and_initialize ()">p11_kit_modules_load_and_initialize</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-modules-release" title="p11_kit_modules_release ()">p11_kit_modules_release</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#P11-KIT-MODULE-CRITICAL:CAPS" title="P11_KIT_MODULE_CRITICAL">P11_KIT_MODULE_CRITICAL</a>, macro in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-module-finalize" title="p11_kit_module_finalize ()">p11_kit_module_finalize</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-module-for-name" title="p11_kit_module_for_name ()">p11_kit_module_for_name</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-module-get-filename" title="p11_kit_module_get_filename ()">p11_kit_module_get_filename</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-module-get-flags" title="p11_kit_module_get_flags ()">p11_kit_module_get_flags</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-module-get-name" title="p11_kit_module_get_name ()">p11_kit_module_get_name</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-module-initialize" title="p11_kit_module_initialize ()">p11_kit_module_initialize</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-module-load" title="p11_kit_module_load ()">p11_kit_module_load</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#p11-kit-module-release" title="p11_kit_module_release ()">p11_kit_module_release</a>, function in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Modules.html#P11-KIT-MODULE-UNMANAGED:CAPS" title="P11_KIT_MODULE_UNMANAGED">P11_KIT_MODULE_UNMANAGED</a>, macro in <a class="link" href="p11-kit-Modules.html" title="Modules">Modules</a>
</dt>
<dd></dd>
<a name="idxP"></a><h3 class="title">P</h3>
<dt>
<a class="link" href="p11-kit-Future.html#P11KitIter" title="P11KitIter">P11KitIter</a>, typedef in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#P11KitIterBehavior" title="enum P11KitIterBehavior">P11KitIterBehavior</a>, enum in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#P11KitIterKind" title="enum P11KitIterKind">P11KitIterKind</a>, enum in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPin" title="P11KitPin">P11KitPin</a>, typedef in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#P11KitPinFlags" title="enum P11KitPinFlags">P11KitPinFlags</a>, enum in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#P11KitUri" title="P11KitUri">P11KitUri</a>, typedef in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#P11KitUriResult" title="enum P11KitUriResult">P11KitUriResult</a>, enum in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#P11KitUriType" title="enum P11KitUriType">P11KitUriType</a>, enum in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-callback" title="p11_kit_pin_callback ()">p11_kit_pin_callback</a>, user_function in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-destroy-func" title="p11_kit_pin_destroy_func ()">p11_kit_pin_destroy_func</a>, user_function in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#P11-KIT-PIN-FALLBACK:CAPS" title="P11_KIT_PIN_FALLBACK">P11_KIT_PIN_FALLBACK</a>, macro in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-file-callback" title="p11_kit_pin_file_callback ()">p11_kit_pin_file_callback</a>, function in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-get-length" title="p11_kit_pin_get_length ()">p11_kit_pin_get_length</a>, function in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-get-value" title="p11_kit_pin_get_value ()">p11_kit_pin_get_value</a>, function in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-new" title="p11_kit_pin_new ()">p11_kit_pin_new</a>, function in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-new-for-buffer" title="p11_kit_pin_new_for_buffer ()">p11_kit_pin_new_for_buffer</a>, function in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-new-for-string" title="p11_kit_pin_new_for_string ()">p11_kit_pin_new_for_string</a>, function in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-ref" title="p11_kit_pin_ref ()">p11_kit_pin_ref</a>, function in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-register-callback" title="p11_kit_pin_register_callback ()">p11_kit_pin_register_callback</a>, function in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-request" title="p11_kit_pin_request ()">p11_kit_pin_request</a>, function in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-unref" title="p11_kit_pin_unref ()">p11_kit_pin_unref</a>, function in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-PIN-Callbacks.html#p11-kit-pin-unregister-callback" title="p11_kit_pin_unregister_callback ()">p11_kit_pin_unregister_callback</a>, function in <a class="link" href="p11-kit-PIN-Callbacks.html" title="PIN Callbacks">PIN Callbacks</a>
</dt>
<dd></dd>
<a name="idxR"></a><h3 class="title">R</h3>
<dt>
<a class="link" href="p11-kit-Deprecated.html#p11-kit-registered-modules" title="p11_kit_registered_modules ()">p11_kit_registered_modules</a>, function in <a class="link" href="p11-kit-Deprecated.html" title="Deprecated">Deprecated</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Deprecated.html#p11-kit-registered-module-to-name" title="p11_kit_registered_module_to_name ()">p11_kit_registered_module_to_name</a>, function in <a class="link" href="p11-kit-Deprecated.html" title="Deprecated">Deprecated</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Deprecated.html#p11-kit-registered-name-to-module" title="p11_kit_registered_name_to_module ()">p11_kit_registered_name_to_module</a>, function in <a class="link" href="p11-kit-Deprecated.html" title="Deprecated">Deprecated</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Deprecated.html#p11-kit-registered-option" title="p11_kit_registered_option ()">p11_kit_registered_option</a>, function in <a class="link" href="p11-kit-Deprecated.html" title="Deprecated">Deprecated</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-remote-serve-module" title="p11_kit_remote_serve_module ()">p11_kit_remote_serve_module</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-remote-serve-token" title="p11_kit_remote_serve_token ()">p11_kit_remote_serve_token</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-remote-serve-tokens" title="p11_kit_remote_serve_tokens ()">p11_kit_remote_serve_tokens</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<a name="idxS"></a><h3 class="title">S</h3>
<dt>
<a class="link" href="p11-kit-Future.html#p11-kit-set-progname" title="p11_kit_set_progname ()">p11_kit_set_progname</a>, function in <a class="link" href="p11-kit-Future.html" title="Future">Future</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Utilities.html#p11-kit-space-strdup" title="p11_kit_space_strdup ()">p11_kit_space_strdup</a>, function in <a class="link" href="p11-kit-Utilities.html" title="Utilities">Utilities</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Utilities.html#p11-kit-space-strlen" title="p11_kit_space_strlen ()">p11_kit_space_strlen</a>, function in <a class="link" href="p11-kit-Utilities.html" title="Utilities">Utilities</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-Utilities.html#p11-kit-strerror" title="p11_kit_strerror ()">p11_kit_strerror</a>, function in <a class="link" href="p11-kit-Utilities.html" title="Utilities">Utilities</a>
</dt>
<dd></dd>
<a name="idxU"></a><h3 class="title">U</h3>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri" title="p11_kit_uri">p11_kit_uri</a>, typedef in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-any-unrecognized" title="p11_kit_uri_any_unrecognized ()">p11_kit_uri_any_unrecognized</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-clear-attribute" title="p11_kit_uri_clear_attribute ()">p11_kit_uri_clear_attribute</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-clear-attributes" title="p11_kit_uri_clear_attributes ()">p11_kit_uri_clear_attributes</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-format" title="p11_kit_uri_format ()">p11_kit_uri_format</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-free" title="p11_kit_uri_free ()">p11_kit_uri_free</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-attribute" title="p11_kit_uri_get_attribute ()">p11_kit_uri_get_attribute</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-attributes" title="p11_kit_uri_get_attributes ()">p11_kit_uri_get_attributes</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-module-info" title="p11_kit_uri_get_module_info ()">p11_kit_uri_get_module_info</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-module-name" title="p11_kit_uri_get_module_name ()">p11_kit_uri_get_module_name</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-module-path" title="p11_kit_uri_get_module_path ()">p11_kit_uri_get_module_path</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-pinfile" title="p11_kit_uri_get_pinfile ()">p11_kit_uri_get_pinfile</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-pin-source" title="p11_kit_uri_get_pin_source ()">p11_kit_uri_get_pin_source</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-pin-value" title="p11_kit_uri_get_pin_value ()">p11_kit_uri_get_pin_value</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-slot-id" title="p11_kit_uri_get_slot_id ()">p11_kit_uri_get_slot_id</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-slot-info" title="p11_kit_uri_get_slot_info ()">p11_kit_uri_get_slot_info</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-token-info" title="p11_kit_uri_get_token_info ()">p11_kit_uri_get_token_info</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-get-vendor-query" title="p11_kit_uri_get_vendor_query ()">p11_kit_uri_get_vendor_query</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-match-attributes" title="p11_kit_uri_match_attributes ()">p11_kit_uri_match_attributes</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-match-module-info" title="p11_kit_uri_match_module_info ()">p11_kit_uri_match_module_info</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-match-slot-info" title="p11_kit_uri_match_slot_info ()">p11_kit_uri_match_slot_info</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-match-token-info" title="p11_kit_uri_match_token_info ()">p11_kit_uri_match_token_info</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-message" title="p11_kit_uri_message ()">p11_kit_uri_message</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-new" title="p11_kit_uri_new ()">p11_kit_uri_new</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#P11-KIT-URI-NO-MEMORY:CAPS" title="P11_KIT_URI_NO_MEMORY">P11_KIT_URI_NO_MEMORY</a>, macro in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-parse" title="p11_kit_uri_parse ()">p11_kit_uri_parse</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#P11-KIT-URI-SCHEME:CAPS" title="P11_KIT_URI_SCHEME">P11_KIT_URI_SCHEME</a>, macro in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#P11-KIT-URI-SCHEME-LEN:CAPS" title="P11_KIT_URI_SCHEME_LEN">P11_KIT_URI_SCHEME_LEN</a>, macro in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-attribute" title="p11_kit_uri_set_attribute ()">p11_kit_uri_set_attribute</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-attributes" title="p11_kit_uri_set_attributes ()">p11_kit_uri_set_attributes</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-module-name" title="p11_kit_uri_set_module_name ()">p11_kit_uri_set_module_name</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-module-path" title="p11_kit_uri_set_module_path ()">p11_kit_uri_set_module_path</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-pinfile" title="p11_kit_uri_set_pinfile ()">p11_kit_uri_set_pinfile</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-pin-source" title="p11_kit_uri_set_pin_source ()">p11_kit_uri_set_pin_source</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-pin-value" title="p11_kit_uri_set_pin_value ()">p11_kit_uri_set_pin_value</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-slot-id" title="p11_kit_uri_set_slot_id ()">p11_kit_uri_set_slot_id</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-unrecognized" title="p11_kit_uri_set_unrecognized ()">p11_kit_uri_set_unrecognized</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
<dt>
<a class="link" href="p11-kit-URIs.html#p11-kit-uri-set-vendor-query" title="p11_kit_uri_set_vendor_query ()">p11_kit_uri_set_vendor_query</a>, function in <a class="link" href="p11-kit-URIs.html" title="URIs">URIs</a>
</dt>
<dd></dd>
</div>
<div class="glossary">
<div class="titlepage"><div><div><h2 class="title">
<a name="annotation-glossary"></a>Annotation Glossary</h2></div></div></div>
<a name="glsA"></a><h3 class="title">A</h3>
<dt><span class="glossterm"><a name="annotation-glossterm-allow-none"></a>allow-none</span></dt>
<dd class="glossdef"><p>NULL is ok, both for passing and for returning.</p></dd>
<dt><span class="glossterm"><a name="annotation-glossterm-array"></a>array</span></dt>
<dd class="glossdef"><p>Parameter points to an array of items.</p></dd>
<a name="glsE"></a><h3 class="title">E</h3>
<dt><span class="glossterm"><a name="annotation-glossterm-element-type"></a>element-type</span></dt>
<dd class="glossdef"><p>Generics and defining elements of containers and arrays.</p></dd>
<a name="glsI"></a><h3 class="title">I</h3>
<dt><span class="glossterm"><a name="annotation-glossterm-inout"></a>inout</span></dt>
<dd class="glossdef"><p>Parameter for input and for returning results. Default is <acronym title="Free data after the code is done."><span class="acronym">transfer full</span></acronym>.</p></dd>
<a name="glsO"></a><h3 class="title">O</h3>
<dt><span class="glossterm"><a name="annotation-glossterm-out"></a>out</span></dt>
<dd class="glossdef"><p>Parameter for returning results. Default is <acronym title="Free data after the code is done."><span class="acronym">transfer full</span></acronym>.</p></dd>
<a name="glsT"></a><h3 class="title">T</h3>
<dt><span class="glossterm"><a name="annotation-glossterm-transfer%20full"></a>transfer full</span></dt>
<dd class="glossdef"><p>Free data after the code is done.</p></dd>
<dt><span class="glossterm"><a name="annotation-glossterm-type"></a>type</span></dt>
<dd class="glossdef"><p>Override the parsed C type with given type</p></dd>
<dt><span class="glossterm"><a name="annotation-glossterm-transfer%20none"></a>transfer none</span></dt>
<dd class="glossdef"><p>Don't free data after the code is done.</p></dd>
</div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>