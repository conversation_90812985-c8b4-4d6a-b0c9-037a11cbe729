# help..txt -  GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


.#gpg.edit_ownertrust.value
# fixme: Please translate and remove the hash mark from the key line.
It's up to you to assign a value here; this value will never be exported
to any 3rd party.  We need it to implement the web-of-trust; it has nothing
to do with the (implicitly created) web-of-certificates.
.

.#gpg.edit_ownertrust.set_ultimate.okay
# fixme: Please translate and remove the hash mark from the key line.
To build the Web-of-Trust, GnuPG needs to know which keys are
ultimately trusted - those are usually the keys for which you have
access to the secret key.  Answer "yes" to set this key to
ultimately trusted

.

.#gpg.untrusted_key.override
# fixme: Please translate and remove the hash mark from the key line.
If you want to use this untrusted key anyway, answer "yes".
.

.#gpg.pklist.user_id.enter
# fixme: Please translate and remove the hash mark from the key line.
Enter the user ID of the addressee to whom you want to send the message.
.

.#gpg.keygen.algo
# fixme: Please translate and remove the hash mark from the key line.
Select the algorithm to use.

DSA (aka DSS) is the Digital Signature Algorithm and can only be used
for signatures.

Elgamal is an encrypt-only algorithm.

RSA may be used for signatures or encryption.

The first (primary) key must always be a key which is capable of signing.
.

.#gpg.keygen.algo.rsa_se
# fixme: Please translate and remove the hash mark from the key line.
In general it is not a good idea to use the same key for signing and
encryption.  This algorithm should only be used in certain domains.
Please consult your security expert first.
.

.#gpg.keygen.size
# fixme: Please translate and remove the hash mark from the key line.
Enter the size of the key
.

.#gpg.keygen.size.huge.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" or "no"
.

.#gpg.keygen.size.large.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" or "no"
.

.#gpg.keygen.valid
# fixme: Please translate and remove the hash mark from the key line.
Enter the required value as shown in the prompt.
It is possible to enter a ISO date (YYYY-MM-DD) but you won't
get a good error response - instead the system tries to interpret
the given value as an interval.
.

.#gpg.keygen.valid.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" or "no"
.

.#gpg.keygen.name
# fixme: Please translate and remove the hash mark from the key line.
Enter the name of the key holder
.

.#gpg.keygen.email
# fixme: Please translate and remove the hash mark from the key line.
please enter an optional but highly suggested email address
.

.#gpg.keygen.comment
# fixme: Please translate and remove the hash mark from the key line.
Please enter an optional comment
.

.#gpg.keygen.userid.cmd
# fixme: Please translate and remove the hash mark from the key line.
N  to change the name.
C  to change the comment.
E  to change the email address.
O  to continue with key generation.
Q  to to quit the key generation.
.

.#gpg.keygen.sub.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" (or just "y") if it is okay to generate the sub key.
.

.#gpg.sign_uid.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" or "no"
.

.#gpg.sign_uid.class
# fixme: Please translate and remove the hash mark from the key line.
When you sign a user ID on a key, you should first verify that the key
belongs to the person named in the user ID.  It is useful for others to
know how carefully you verified this.

"0" means you make no particular claim as to how carefully you verified the
    key.

"1" means you believe the key is owned by the person who claims to own it
    but you could not, or did not verify the key at all.  This is useful for
    a "persona" verification, where you sign the key of a pseudonymous user.

"2" means you did casual verification of the key.  For example, this could
    mean that you verified the key fingerprint and checked the user ID on the
    key against a photo ID.

"3" means you did extensive verification of the key.  For example, this could
    mean that you verified the key fingerprint with the owner of the key in
    person, and that you checked, by means of a hard to forge document with a
    photo ID (such as a passport) that the name of the key owner matches the
    name in the user ID on the key, and finally that you verified (by exchange
    of email) that the email address on the key belongs to the key owner.

Note that the examples given above for levels 2 and 3 are *only* examples.
In the end, it is up to you to decide just what "casual" and "extensive"
mean to you when you sign other keys.

If you don't know what the right answer is, answer "0".
.

.#gpg.change_passwd.empty.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" or "no"
.

.#gpg.keyedit.save.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" or "no"
.

.#gpg.keyedit.cancel.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" or "no"
.

.#gpg.keyedit.sign_all.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" if you want to sign ALL the user IDs
.

.#gpg.keyedit.remove.uid.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" if you really want to delete this user ID.
All certificates are then also lost!
.

.#gpg.keyedit.remove.subkey.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" if it is okay to delete the subkey
.

.#gpg.keyedit.delsig.valid
# fixme: Please translate and remove the hash mark from the key line.
This is a valid signature on the key; you normally don't want
to delete this signature because it may be important to establish a
trust connection to the key or another key certified by this key.
.

.#gpg.keyedit.delsig.unknown
# fixme: Please translate and remove the hash mark from the key line.
This signature can't be checked because you don't have the
corresponding key.  You should postpone its deletion until you
know which key was used because this signing key might establish
a trust connection through another already certified key.
.

.#gpg.keyedit.delsig.invalid
# fixme: Please translate and remove the hash mark from the key line.
The signature is not valid.  It does make sense to remove it from
your keyring.
.

.#gpg.keyedit.delsig.selfsig
# fixme: Please translate and remove the hash mark from the key line.
This is a signature which binds the user ID to the key. It is
usually not a good idea to remove such a signature.  Actually
GnuPG might not be able to use this key anymore.  So do this
only if this self-signature is for some reason not valid and
a second one is available.
.

.#gpg.keyedit.updpref.okay
# fixme: Please translate and remove the hash mark from the key line.
Change the preferences of all user IDs (or just of the selected ones)
to the current list of preferences.  The timestamp of all affected
self-signatures will be advanced by one second.

.

.#gpg.passphrase.enter
# fixme: Please translate and remove the hash mark from the key line.
Please enter the passphrase; this is a secret sentence

.

.#gpg.passphrase.repeat
# fixme: Please translate and remove the hash mark from the key line.
Please repeat the last passphrase, so you are sure what you typed in.
.

.#gpg.detached_signature.filename
# fixme: Please translate and remove the hash mark from the key line.
Give the name of the file to which the signature applies
.

.#gpg.openfile.overwrite.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" if it is okay to overwrite the file
.

.#gpg.openfile.askoutname
# fixme: Please translate and remove the hash mark from the key line.
Please enter a new filename. If you just hit RETURN the default
file (which is shown in brackets) will be used.
.

.#gpg.ask_revocation_reason.code
# fixme: Please translate and remove the hash mark from the key line.
You should specify a reason for the certification.  Depending on the
context you have the ability to choose from this list:
  "Key has been compromised"
      Use this if you have a reason to believe that unauthorized persons
      got access to your secret key.
  "Key is superseded"
      Use this if you have replaced this key with a newer one.
  "Key is no longer used"
      Use this if you have retired this key.
  "User ID is no longer valid"
      Use this to state that the user ID should not longer be used;
      this is normally used to mark an email address invalid.

.

.#gpg.ask_revocation_reason.text
# fixme: Please translate and remove the hash mark from the key line.
If you like, you can enter a text describing why you issue this
revocation certificate.  Please keep this text concise.
An empty line ends the text.

.



# Local variables:
# mode: fundamental
# coding: utf-8
# End:
