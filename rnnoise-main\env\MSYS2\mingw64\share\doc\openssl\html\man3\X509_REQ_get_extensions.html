<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_REQ_get_extensions</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_REQ_get_extensions, X509_REQ_add_extensions, X509_REQ_add_extensions_nid - handle X.509 extension attributes of a CSR</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

STACK_OF(X509_EXTENSION) *X509_REQ_get_extensions(const X509_REQ *req);
int X509_REQ_add_extensions(X509_REQ *req, const STACK_OF(X509_EXTENSION) *exts);
int X509_REQ_add_extensions_nid(X509_REQ *req,
                                const STACK_OF(X509_EXTENSION) *exts, int nid);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>X509_REQ_get_extensions() returns the first list of X.509 extensions found in the attributes of <i>req</i>. The returned list is empty if there are no such extensions in <i>req</i>. The caller is responsible for freeing the list obtained.</p>

<p>X509_REQ_add_extensions_nid() adds to <i>req</i> a list of X.509 extensions <i>exts</i>, using <i>nid</i> to identify the extensions attribute. <i>req</i> is unchanged if <i>exts</i> is NULL or an empty list. This function may be called more than once on the same <i>req</i> and <i>nid</i>. In such case any previous extensions are augmented, where an extension to be added that has the same OID as a pre-existing one replaces this earlier one.</p>

<p>X509_REQ_add_extensions() is like X509_REQ_add_extensions_nid() except that the default <b>NID_ext_req</b> is used.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>X509_REQ_get_extensions() returns a pointer to <b>STACK_OF(X509_EXTENSION)</b> or NULL on error.</p>

<p>X509_REQ_add_extensions() and X509_REQ_add_extensions_nid() return 1 on success, 0 on error.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2022-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


