NAMN
    dos2unix - konverterare för textfilsformat från DOS/Mac till Unix och
    vice versa

SYNOPSIS
        dos2unix [flaggor] [FIL …] [-n INFIL UTFIL …]
        unix2dos [flaggor] [FIL …] [-n INFIL UTFIL …]

BESKRIVNING
    Paketet Dos2unix inkluderar verktygen "dos2unix" och "unix2dos" som
    konverterar oformaterade textfiler i DOS- eller Mac-format till
    Unix-format och vice versa.

    Textfiler i DOS/Windows har en radbrytning, också känd som nyrad, som är
    en kombination av två tecken: vagnretur (Carriage Return, CR) åtföljt av
    radmatning (Line Feed, LF). Textfiler i Unix har en radbrytning som är
    ett enda tecken: radmatning (Line Feed, LF). Textfiler för Mac, innan
    Mac OS X, hade en radbrytning som var en enda vagnretur (Carriage
    Return, CR). Numera använder Mac OS radbrytning i Unix-stil (LF).

    Förutom radbrytningar så kan Dos2unix också konvertera filers kodning.
    Några DOS-teckentabeller kan konverteras till Unix Latin-1. Och filer
    som använder Windows Unicode (UTF-16) kan konverteras till Unix Unicode
    (UTF-8).

    Binära filer hoppas över automatiskt, om inte konvertering tvingas.

    Kataloger och FIFOs och andra filer som inte är vanliga filer hoppas
    över automatiskt.

    Symboliska länkar och deras mål förblir oförändrade som standard.
    Symboliska länkar kan valfritt bli ersatta eller så kan utmatningen
    skrivas till målet för den symboliska länken. På Windows saknas stöd för
    att skriva till målet för en symbolisk länk.

    Dos2unix modellerades efter dos2unix från SunOS/Solaris. Det finns en
    viktig skillnad gentemot originalversionen för SunOS/Solaris. Denna
    versionen gör som standard konverteringen på plats (gammalfilsläge),
    medan originalversionen från SunOS/Solaris bara hade stöd för parad
    konvertering (nyfilsläge). Se vidare flaggorna "-o" och "-n". En annan
    skillnad är att SunOS/Solaris-versionen som standard använder
    *iso*-lägeskonvertering medan denna version som standard använder
    *ascii*-lägeskonvertering.

FLAGGOR
    --  Behandla alla efterföljande flaggor som filnamn. Använd denna flagga
        om du vill konvertera filer vars namn börjar med bindestreck. För
        att till exempel konvertera en fil med namnet “-foo“ kan du använda
        detta kommando:

            dos2unix -- -foo

        Eller i nyfilsläge:

            dos2unix -n -- -foo out.txt

    --allow-chown
        Tillåt ändring av ägarskap för fil i gammalt filläge.

        När denna flagga används, kommer konverteringen inte att avbrytas
        när användar- och/eller gruppägarskap för originalfilen inte kan
        bevaras i gammalt filläget. Konverteringen kommer att fortsätta och
        den konverterade filen kommer att få samma nya ägarskap som om den
        konverterades i nyfilsläge. Se också flaggorna "-o" och "-n". Denna
        flagga är endast tillgänglig om dos2unix har stöd för att bevara
        användar- och gruppägarskap för filer.

    -ascii
        Standardkonverteringsläge. Se vidare stycket KONVERTERINGSLÄGEN.

    -iso
        Konvertering mellan DOS- och ISO-8859-1-teckentabeller. Se vidare
        stycket KONVERTERINGSLÄGEN.

    -1252
        Använd Windows-teckentabell 1252 (Västeuropeisk).

    -437
        Använd DOS-teckentabell 437 (USA). Detta är standardteckentabellen
        som används för ISO-konvertering.

    -850
        Använd DOS-teckentabell 850 (Västeuropeisk).

    -860
        Använd DOS-teckentabell 860 (Portugisisk).

    -863
        Använd DOS-teckentabell 863 (Fransk-kanadensisk).

    -865
        Använd DOS-teckentabell 865 (Nordisk).

    -7  Konvertera 8-bitars tecken till 7-bitars blanksteg.

    -b, --keep-bom
        Behåll byteordningsmarkering (Byte Order Mark, BOM). Om infilen har
        en BOM, skriv en BOM i utfilen. Detta är standardbeteendet vid
        konvertering av DOS-radbrytningar. Se vidare flaggan "-r".

    -c, --convmode KONVERTERINGSLÄGE
        Ställer in konverteringsläge. Där KONVERTERINGSLÄGE är en av:
        *ascii*, *7bit*, *iso*, *mac* där ascii är standard.

    -D, --display-enc KODNING
        Ställ in kodning för visad text. Där KODNING är en av: *ansi*,
        *unicode*, *unicodebom*, *utf8*, *utf8bom* där ansi är
        standardvalet.

        Denna flagga finns bara tillgänglig i dos2unix för Windows med stöd
        för Unicode-filnamn. Denna flagga har ingen effekt på själva
        filnamnen som läses och skrivs, bara på hur de visas.

        Det finns flera metoder för att visa text i en Windows-konsol
        baserad på vilken kodning texten har. De har alla för- och
        nackdelar.

        ansi
            Dos2unix standardmetod är att använda ANSI-kodad text. Fördelen
            är att den är bakåtkompatibel. Det fungerar med raster- och
            TrueType-teckensnitt. I vissa regioner kan du behöva ändra den
            aktiva DOS OEM-teckentabellen till Windows-systemets
            ANSI-teckentabell genom att använda kommandot "chcp", eftersom
            dos2unix använder Windows-systemets teckentabell.

            Nackdelen med ansi är att internationella filnamn med tecken som
            inte finns i systemets standardteckentabell inte visas korrekt.
            Du kommer att se frågetecken, eller en felaktig symbol istället.
            När du inte arbetar med utländska filnamn är denna metoden OK.

        unicode, unicodebom
            Fördelen med unicode-kodning (Windows-namnet för UTF-16) är att
            text vanligtvis visas korrekt. Det finns inget behov av att
            ändra den aktiva teckentabellen. Du kan behöva ställa in
            konsolens teckensnitt till ett TrueType-teckensnitt för att få
            internationella tecken att visas korrekt. När ett tecken inte
            finns inkluderat i TrueType-teckensnittet kommer du vanligtvis
            att se en liten ruta, ibland med ett frågetecken inuti.

            När du använder ConEmu-konsolen kommer all text att visas
            korrekt eftersom ConEmu automatiskt väljer ett bra teckensnitt.

            Nackdelen med unicode är att den inte är kompatibel med ASCII.
            Utmatningen är inte lätt att hantera när du omdirigerar den till
            ett annat program eller en fil.

            När metod "unicodebom" används kommer Unicode-texten att föregås
            av en BOM (byteordningsmarkering, Byte Order Mark). En BOM krävs
            för korrekt omdirigering eller rörledning i PowerShell.

        utf8, utf8bom
            Fördelen med utf8 är att den är kompatibel med ASCII. Du måste
            ställa in konsolens teckensnitt till ett TrueType-teckensnitt.
            Med ett TrueType-teckensnitt kommer text att visas på liknande
            sätt som med "unicode"-kodningen.

            Nackdelen är att när du använder standardrasterteckensnittet
            kommer alla icke-ASCII tecken att visas fel. Inte enbart
            unicode-filnamn, utan också översatta meddelanden kommer att bli
            oläsbara. Under Windows som konfigurerats för Östasien kan man
            komma att se många blinkningar i konsolen när meddelanden visas.

            I ConEmu-konsolen fungerar utf8-kodningsmetoden väl.

            När metod "utf8bom" används kommer UTF-8-texten att föregås av
            en BOM (byteordningsmarkering, Byte Order Mark). En BOM krävs
            för korrekt omdirigering eller rörledning i PowerShell.

        Standardkodningen kan ändras via miljövariabeln DOS2UNIX_DISPLAY_ENC
        genom att sätta den till "unicode", "unicodebom", "utf8" or
        "utf8bom".

    -e, --add-eol
        Lägg till en radbrytning på sista raden om det inte finns någon.
        Detta fungerar för alla konverteringar.

        En fil konverterad från DOS- till Unix-format kan sakna en
        radbrytning på sista raden. Det finns textredigerare som skriver
        textfiler utan en radbrytning på den sista raden. Vissa Unix-program
        har problem med att behandla dessa filer, då POSIX-standarden
        definierar det som att varje rad i en textfil måste har ett
        avslutande nyradstecken. Att konkatenera filer kan till exempel ge
        oväntat resultat.

    -f, --force
        Tvinga konvertering av binära filer.

    -gb, --gb18030
        Under Windows konverteras UTF-16-filer som standard till UTF-8,
        oavsett vilken lokalinställning som är gjord. Använd denna flagga
        för att konvertera UTF-16-filer till GB18030. Denna flagga finns
        bara tillgänglig i Windows. Se vidare i avsnittet GB18030.

    -h, --help
        Visa hjälptext och avsluta.

    -i[FLAGGOR], --info[=FLAGGOR] FIL ...
        Visa filinformation. Ingen konvertering görs.

        Följande information skrivs ut, i denna ordningen: antal
        DOS-radbrytningar, antal Unix-radbrytningar, antal
        Mac-radbrytningar, byteordningsmarkeringen, text eller binär,
        filnamn.

        Exempelutmatning:

             6       0       0  no_bom    text    dos.txt
             0       6       0  no_bom    text    unix.txt
             0       0       6  no_bom    text    mac.txt
             6       6       6  no_bom    text    mixed.txt
            50       0       0  UTF-16LE  text    utf16le.txt
             0      50       0  no_bom    text    utf8unix.txt
            50       0       0  UTF-8     text    utf8dos.txt
             2     418     219  no_bom    binär   dos2unix.exe

        Notera att en binärfil ibland kan misstas för en textfil. Se vidare
        flaggan "-s".

        Om dessutom flaggan "-e" eller "--add-eol" används så kommer även
        den radbrytning som används på sista raden att skrivas ut, eller
        "noeol" om det inte finns någon.

        Exempelutmatning:

             6       0       0  no_bom    text   dos     dos.txt
             0       6       0  no_bom    text   unix    unix.txt
             0       0       6  no_bom    text   mac     mac.txt
             1       0       0  no_bom    text   noeol   noeol_dos.txt

        Extra flaggor kan användas valfritt för att ändra utmatningen. En
        eller fler flaggor kan läggas till.

        0   Skriv ut filinformationsraderna följt av ett null-tecken
            istället för ett nyradstecken. Detta möjliggör korrekt tolkning
            av filnamn med blanksteg eller citationstecken när c-flaggan
            används. Använd denna flagga i kombination med xargs(1):s flagga
            -0 eller "--null".

        d   Skriv ut antal DOS-radbrytningar.

        u   Skriv ut antal Unix-radbrytningar.

        m   Skriv ut antal Mac-radbrytningar.

        b   Skriv ut byteordningsmarkeringen.

        t   Skriv ut om filen är text eller binär.

        e   Skriv ut radbrytningstypen på sista raden, eller "noeol" om det
            inte finns någon.

        c   Skriv bara ut filerna som skulle ha konverterats.

            Med "c"-flaggan kommer dos2unix att skriva ut filerna som
            innehåller DOS-radbrytningar, unix2dos kommer bara att skriva ut
            filnamn som har Unix-radbrytningar.

            Om dessutom flaggan "-e" eller "--add-eol" används så kommer
            även filer som saknar en radbrytning på sista raden att skrivas
            ut.

        h   Skriv ut rubrik.

        p   Visa filnamn utan sökväg.

        Exempel:

        Visa information för alla *.txt-filer:

            dos2unix -i *.txt

        Visa bara antalet DOS-radbrytningar och Unix-radbrytningar:

            dos2unix -idu *.txt

        Visa bara byteordningsmarkeringen:

            dos2unix --info=b *.txt

        Lista filerna som har DOS-radbrytningar:

            dos2unix -ic *.txt

        Lista filerna som har Unix-radbrytningar:

            unix2dos -ic *.txt

        Lista filerna som har DOS-radbrytningar eller saknar en radbrytning
        på sista raden:

            dos2unix -e -ic *.txt

        Konvertera endast filer som har DOS-radbrytningar och lämna övriga
        filer orörda:

            dos2unix -ic0 *.txt | xargs -0 dos2unix

        Hitta textfiler som har DOS-radbrytningar:

            find -name '*.txt' -print0 | xargs -0 dos2unix -ic

    -k, --keepdate
        Behåll infilens datumstämpel för utfilen.

    -L, --license
        Visa programmets licens.

    -l, --newline
        Lägg till ytterligare nyrad.

        dos2unix: Endast DOS-radbrytningar ändras till två
        Unix-radbrytningar. I Mac-läge ändras endast Mac-radbrytningar till
        två Unix-radbrytningar.

        unix2dos: Endast Unix-radbrytningar ändras till två
        DOS-radbrytningar. I Mac-läge ändras Unix-radbrytningar till två
        Mac-radbrytningar.

    -m, --add-bom
        Skriv en byteordningsmarkering (Byte Order Mark, BOM) i utfilen. Som
        standard skrivs en UTF-8 BOM.

        När infilen är UTF-16, och flaggan "-u" används, kommer en UTF-16
        BOM att skrivas.

        Använd aldrig denna flagga när kodningen för utmatning är något
        annat än UTF-8, UTF-16 eller GB18030. Se vidare i avsnittet UNICODE.

    -n, --newfile INFIL UTFIL …
        Nyfilsläge. Konvertera filen INFIL och skriv utfilen UTFIL.
        Filnamnen måste ange i par och jokertecken i namnen ska *inte*
        användas annars *kommer* du att förlora filer.

        Användaren som påbörjar konverteringen i nyfilsläge (parat läge)
        kommer att bli ägaren till den konverterade filen.
        Läs-/skrivbehörigheter för den nya filen kommer att vara samma
        behörigheter som för originalfilen minus umask(1) för användaren som
        kör konverteringen.

    --no-allow-chown
        Tillåt inte ändring av ägarskap i gammalt filläge (standard)

        Avbryt konvertering när användar- och/eller gruppägarskap för
        originalfilen inte kan bevaras i gammalt filläge. Se också flaggorna
        "-o" och "-n". Denna flagga är endast tillgänglig om dos2unix har
        stöd för att bevara användar- och gruppägarskap för filer.

    --no-add-eol
        Lägg inte till en radbrytning på den sista raden om det inte finns
        någon.

    -O, --to-stdout
        Skriv till standard ut som ett Unix-filter. Använd flaggan "-o" för
        att återgå till det gamla (på-plats) filläget.

        Kombinerat med flaggan "-e" kan filer konkateneras korrekt. Inga
        sammanfogade sista och första rader, och inga Unicode
        byteordningsmarkeringar i mitten på den konkatenerade filen.
        Exempel:

            dos2unix -e -O fil1.txt fil2.txt > ut.txt

    -o, --oldfile FIL …
        Gammalfilsläge. Konvertera filen FIL och skriv över den med
        utmatningen. Programmet kör i detta läge som standard. Jokertecken i
        filnamn får användas.

        I gammalfilsläge (på-plats läge) kommer den konverterade filen att
        få samma ägare, grupp samt läs-/skrivbehörigheter som originalfilen.
        Även då filen konverteras av en annan användare som har
        skrivbehörighet för filen (t.ex. användaren root). Konverteringen
        kommer att avbrytas när det inte är möjligt att bevara
        originalvärdena. Byte av ägare skulle kunna innebära att
        originalägaren inte längre kan läsa filen. Byte av grupp skulle
        kunna vara en säkerhetsrisk, filen skulle kunna bli läsbar för
        användare som den inte är avsedd för. Stöd för bevarande av ägare,
        grupp och läs-/skrivbehörigheter finns bara i Unix.

        För att kontrollera om dos2unix har stöd för att bevara användar-
        och gruppägarskap för filer skriv "dosunix -V".

        Konvertering görs alltid via en temporärfil. När ett fel inträffar
        halvvägs i konverteringen tas den temporära filen bort och
        originalfilen finns kvar intakt. Om konverteringen är framgångsrik
        kommer originalfilen att ersättas med temporärfilen. Du kanske har
        skrivrättigheter till originalfilen men inte rättigheter att ställa
        in samma användar- och/eller grupprättighetsegenskaper på
        temporärfilen som originalfilen har. Detta innebär att du inte kan
        bevara användar- och/eller gruppägarskapet för originalfilen. I
        detta fall kan du använda flaggan "--allow-chown" för att fortsätta
        konverteringen:

            dos2unix --allow-chown foo.txt

        Ett annat alternativ är att använda nyfilsläge:

            dos2unix -n foo.txt foo.txt

        Fördelen med flaggan "--allow-chown" är att du kan använda
        jokertecken och att ägarskapsegenskaper om möjligt kommer att
        bevaras.

    -q, --quiet
        Tyst drift. Undertryck alla varningar och meddelanden. Returvärdet
        är noll. Utom när felaktiga kommandoradsflaggor används.

    -r, --remove-bom
        Ta bort byteordningsmarkering (Byte Order Mark, BOM). Skriv inte en
        BOM i utfilen. Detta är standardbeteende vid konvertering av
        Unix-radbrytningar. Se vidare flaggan "-b".

    -s, --safe
        Hoppa över binära filer (standard).

        Binärfiler hoppas över för att undvika oavsiktliga misstag. Var
        medveten om att detektering av binärfiler inte är 100% säker.
        Infiler genomsöks efter binära symboler som typiskt inte återfinns i
        textfiler. Det är möjligt att en binärfil enbart innehåller
        texttecken. En sådan binärfil kommer oavsiktligt att ses som en
        textfil.

    -u, --keep-utf16
        Behåll infilens original UTF-16-kodning. Utfilen kommer att skrivas
        med samma UTF-16-kodning som infilen, omvänd eller rak byteordning
        (little eller big endian). Detta förhindrar transformation till
        UTF-8. En UTF-16 BOM kommer att skrivas i enlighet med detta. Denna
        flagga kan inaktiveras med "-ascii"-flaggan.

    -ul, --assume-utf16le
        Antag att infilsformatet är UTF-16LE.

        När det finns en byteordningsmarkering (Byte Order Mark) i infilen
        så har BOM:en högre prioritet än denna flagga.

        När du har gjort fel antagande (infilen var inte i UTF-16LE-format)
        och konverteringens lyckas, kommer du att få en UTF-8 utfil med
        felaktig text. Du kan göra denna konvertering ogjord med iconv(1)
        genom att konvertera UTF-8 utfilen tillbaka till UTF-16LE. Detta
        kommer att återskapa originalfilen.

        Antagandet om UTF-16LE fungerar som ett *konverteringsläge*. Genom
        att växla till standard *ascii*-läget kommer UTF-16LE antagandet att
        stängas av.

    -ub, --assume-utf16be
        Antag att infilsformatet är UTF-16BE.

        Denna flagga fungerar på samma sätt som flaggan "-ul".

    -v, --verbose
        Visa utförliga meddelanden. Extra information visas om
        byteordningsmarkeringar och antalet konverterade radbrytningar.

    -F, --follow-symlink
        Följ symboliska länkar och konvertera målen.

    -R, --replace-symlink
        Ersätt symboliska länkar med konverterade filer (originalmålfilerna
        förblir oförändrade).

    -S, --skip-symlink
        Behåll symboliska länkar och mål oförändrade (standard).

    -V, --version
        Visa versionsinformation och avsluta.

MAC-LÄGE
    Som standard konverteras radbrytningar från DOS till Unix och vice
    versa. Mac-radbrytningar konverteras inte.

    I Mac-läge konverteras radbrytningar från Mac till Unix och vice versa.
    DOS-radbrytningar ändras ej.

    För att köra i Mac-läge använd kommandoradsflaggan "-c mac" eller använd
    kommandona "mac2unix" eller "unix2mac".

KONVERTERINGSLÄGEN
    ascii
        Detta är standardkonverteringsläget. Detta läge används för att
        konvertera ASCII och ASCII-kompatibla kodade filer, så som UTF-8.
        Att aktiveras ascii-läge inaktiverar 7bit- och iso-läge.

        Om dos2unix har UTF-16 stöd kommer UTF-16-kodade filer att
        konverteras till kodningen för den aktuella lokalen på POSIX-sytstem
        och till UTF-8 på Windows. Aktivering av ascii-läget inaktiverar
        flaggan för att behålla UTF-16-kodning ("-u") och flaggorna för att
        anta UTF-16 indata ("-ul" och "-ub"). För att se om dos2unix har
        UTF-16-stöd skriv "dox2unix -V", se också avsnittet UNICODE.

    7bit
        I detta läge konverteras alla 8-bitars icke-ASCII tecken (med värden
        från 128 till 255) till ett 7-bitars blanksteg.

    iso Tecken konverteras mellan DOS teckenuppsättning (teckentabell) och
        ISO teckenuppsättning ISO-8859-1 (Latin-1) på Unix. DOS tecken utan
        motsvarande ISO-8859-1 tecken, för vilka konvertering är omöjligt,
        kommer att ersättas med en punkt. Detsamma gäller för ISO-8859-1
        tecken utan motsvarighet i DOS.

        När enbart flaggan "-iso" används kommer dos2unix att försöka avgöra
        den aktiva teckentabellen. När detta inte är möjligt kommer dos2unix
        att använda standardteckentabellen CP437, vilken huvudsakligen
        används i USA. För att tvinga en specifik teckentabell använd
        flaggorna -437 (USA), -850 (Västeuropeisk), -860 (Portugisisk), -863
        (Fransk-kanadensisk) eller -865 (Nordisk). Det finns också stöd för
        Windows-teckentabell CP-1252 (Västeuropeisk) via flaggan -1252. För
        andra teckentabeller använd dos2unix i kombination med iconv(1).
        iconv kan konvertera mellan en lång lista av teckenkodningar.

        Använd aldrig ISO-konvertering på Unicode-textfiler. Det kommer att
        korrumpera UTF-8-kodade filer.

        Några exempel:

        Konvertera från DOS standardteckentabell till Unix Latin-1:

            dos2unix -iso -n in.txt ut.txt

        Konvertera från DOS CP850 till Unix Latin-1:

            dos2unix -850 -n in.txt ut.txt

        Konvertera från Windows CP1252 till Unix Latin-1:

            dos2unix -1252 -n in.txt ut.txt

        Konvertera från Windows CP1252 till Unix UTF-8 (Unicode):

            iconv -f CP1252 -t UTF-8 in.txt | dos2unix > ut.txt

        Konvertera från Unix Latin-1 till DOS-standardteckentabell:

            unix2dos -iso -n in.txt ut.txt

        Konvertera från Unix Latin-1 till DOS CP850:

            unix2dos -850 -n in.txt ut.txt

        Konvertera från Unix Latin-1 till Windows CP1252:

            unix2dos -1252 -n in.txt ut.txt

        Konvertera från Unix UTF-8 (Unicode) till Windows CP1252:

            unix2dos < in.txt | iconv -f UTF-8 -t CP1252 > ut.txt

        Se även <http://czyborra.com/charsets/codepages.html> och
        <http://czyborra.com/charsets/iso8859.html>.

UNICODE
  Kodningar
    Det finns flera olika Unicode kodningar. I Unix och Linux kodas filer
    vanligtvis med UTF-8-kodning. I Windows kan Unicode-textfiler kodas i
    UTF-8, UTF-16 eller UTF-16 rak byteordning (big endian), men kodas
    mestadels i UTF-16-format.

  Konvertering
    Unicode-textfiler kan ha DOS, Unix eller Mac-radbrytningar precis som
    ASCII-textfiler.

    Alla versioner av dos2unix och unix2dos kan konvertera UTF-8-kodade
    filer, eftersom UTF-8 designades för bakåtkompatibilitet med ASCII.

    Dos2unix och unix2dos med Unicode-UTF-16-stöd, kan läsa UTF-16-kodade
    textfiler i omvänd och rak byteordning (little och big endian). För att
    se om dos2unix byggts med UTF-16-stöd skriv "dos2unix -V".

    Under Unix/Linux kommer UTF-16-kodade filer att konverteras till
    lokalens teckenkodning. Använd kommandot locale(1) för att ta reda på
    vilken lokalens teckenkodning är. När konvertering inte är möjlig kommer
    ett konverteringsfel att inträffa och filen kommer att hoppas över.

    Under Windows konverteras UTF-16-filer som standard till UTF-8.
    UTF-8-formaterade textfiler har bra stöd både under Windows och
    Unix/Linux.

    UTF-16- och UTF-8-kodning är fullt kompatibla, ingen text kommer att gå
    förlorad i konverteringen. När ett UTF-16 till UTF-8-konverteringsfel
    uppstår, till exempel när infilen i UTF-16-format innehåller ett fel,
    kommer att filen att hoppas över.

    När flaggan "-u" används kommer utfilen att skrivas med samma
    UTF-16-kodning som infilen. Flaggan "-u" förhindrar konvertering till
    UTF-8.

    Dos2unix och unix2dos har ingen flagga för att konvertera UTF-8-filer
    till UTF-16.

    ISO- och 7-bitarslägeskonvertering fungerar inte på UTF-16-filer.

  Byteordningsmarkering (Byte Order Mark)
    I Windows har Unicode-textfiler typiskt en byteordningsmarkering (Byte
    Order Mark, BOM) eftersom många Windows-program (inklusive Notepad)
    lägger till BOM:ar som standard. Se även
    <https://en.wikipedia.org/wiki/Byte_order_mark>.

    I Unix har Unicode-textfiler typiskt ingen BOM. Filer antas vara kodade
    i den lokala teckenuppsättningen.

    Dos2Unix kan bara detektera om en fil är i UTF-16-format om filen har en
    BOM. När en UTF-16-fil inte har en BOM så kommer dos2unix att de filen
    som en binärfil.

    Använd flaggan "-ul" eller "-ub" för att konvertera en UTF-16-fil utan
    BOM.

    Dos2unix skriver som standard ingen BOM i utfilen. Med flaggan "-b"
    kommer Dos2unix att skriva en BOM när infilen har en BOM.

    Unix2dos skriver som standard en BOM i utfilen när infilen har en BOM.
    Använd flaggan "-r" för att ta bort BOM:en.

    Dos2unix och unix2dos skriver alltid en BOM när flaggan "-m" används.

  Unicode-filnamn under Windows
    Dos2unix har valfritt stöd för läsning och skrivning av Unicode-filnamn
    i Windows kommandoprompt. Detta innebär att dos2unix kan öppna filer som
    har tecken i sina namn som inte är en del av systemets atandard
    ANSI-teckentabell. För att se om dos2unix för Windows byggdes med stöd
    för Unicode-filnamn skriv "dos2unix -V".

    Det finns en del problem med att visa Unicode-filnamn i en
    Windows-konsol. Se vidare flaggan "-D", "--display-enc". Filnamnen kan
    visas felaktigt i konsolen, men filerna som skrivs kommer att ha de
    korrekta namnen.

  Unicode-exempel
    Konvertera från Windows UTF-16 (med BOM) till Unix UTF-8:

        dos2unix -n in.txt ut.txt

    Konvertera från Windows UTF-16LE (utan BOM) till Unix UTF-8:

        dos2unix -ul -n in.txt ut.txt

    Konvertera från Unix UTF-8 till Windows UTF-8 med BOM:

        unix2dos -m -n in.txt ut.txt

    Konvertera från Unix UTF-8 till Windows UTF-16:

        unix2dos < in.txt | iconv -f UTF-8 -t UTF-16 > ut.txt

GB18030
    GB18030 är en standard från Kinesiska regeringen. En obligatorisk
    delmängd av standarden GB18030 krävs officiellt för alla
    programvaruprodukter som säljs i Kina. Se vidare
    <https://en.wikipedia.org/wiki/GB_18030>.

    GB18030 är fullständigt kompatibel med Unicode och kan anses vara ett
    överföringsformat för unicode. Precis som UTF-8 är GB18030 kompatibel
    med ASCII. GB18030 är också kompatibel med Windows-teckentabell 936,
    också känd som GBK.

    Under Unix/Linux kommer UTF-16-filer att konverteras till GB18030 när
    lokalens teckenkodning är inställd på GB18030. Notera att detta endast
    kommer att fungera om lokalen har stöd i systemet. Använd kommandot
    "locale -a" för att få en lista över de lokaler som stöds.

    Under Windows måste du använda flaggan "-gb" för att konvertera
    UTF-16-filer till GB18030.

    GB18030-kodade filer kan ha en byteordningsmarkering, precis som
    Unicode-filer.

EXEMPEL
    Läsa inmatning från “stdin“ och skriv utmatning till “stdout“:

        dos2unix < a.txt
        cat a.txt | dos2unix

    Konvertera och ersätta a.txt. Konvertera och ersätt b.txt:

        dos2unix a.txt b.txt
        dos2unix -o a.txt b.txt

    Konvertera och ersätt a.txt i ascii-konverteringsläge:

        dos2unix a.txt

    Konvertera och ersätt a.txt i ascii-konverteringsläge, konvertera och
    ersätt b.txt i 7bit-konverteringsläge:

        dos2unix a.txt -c 7bit b.txt
        dos2unix -c ascii a.txt -c 7bit b.txt
        dos2unix -ascii a.txt -7 b.txt

    Konvertera a.txt från Mac- till Unix-format:

        dos2unix -c mac a.txt
        mac2unix a.txt

    Konvertera a.txt från Unix- till Mac-format:

        unix2dos -c mac a.txt
        unix2mac a.txt

    Konvertera och ersätt a.txt medan originalet tidsstämpel behålls:

        dos2unix -k a.txt
        dos2unix -k -o a.txt

    Konvertera a.txt och skriv till e.txt:

        dos2unix -n a.txt e.txt

    Konvertera a.txt och skriv till e.txt, låt e.txt behålla tidsstämpeln
    från a.txt:

        dos2unix -k -n a.txt e.txt

    Konvertera och ersätt a.txt, konvertera b.txt och skriv till e.txt:

        dos2unix a.txt -n b.txt e.txt
        dos2unix -o a.txt -n b.txt e.txt

    Konvertera c.txt och skriv till e.txt, konvertera och ersätt a.txt,
    konvertera och ersätt b.txt, konvertera d.txt och skriv till f.txt:

        dos2unix -n c.txt e.txt -o a.txt b.txt -n d.txt f.txt

REKURSIV KONVERTERING
    I ett Unix-skal kan kommandona find(1) och xargs(1) användas för att
    köra dos2unix rekursivt över alla textfiler i ett katalogträd. För att
    till exempel konvertera alla .txt-filer i katalogträdet under den
    aktuella katalogen skriv:

        find . -name '*.txt' -print0 |xargs -0 dos2unix

    Flaggan "-print0" till find(1) och motsvarande flagga -0 till xargs(1)
    behövs när det finns filer med mellanslag eller citationstecken i
    namnet. Annars kan dessa flaggor utelämnas. Ett annat alternativ är att
    användas find(1) med flaggan "-exec":

        find . -name '*.txt' -exec dos2unix {} \;

    I en Windows-kommandoprompt kan följande kommando användas:

        for /R %G in (*.txt) do dos2unix "%G"

    PowerShell-användare kan använda följande kommando i Windows PowerShell:

        get-childitem -path . -filter '*.txt' -recurse | foreach-object {dos2unix $_.Fullname}

LOKALISERING
    LANG
        Det primära språket väljs med miljövariabeln LANG. LANG-variabeln
        består av flera delas. Den första delen är språkkoden i gemener. Den
        andra delen är valfri och utgör landskoden i versaler, föregången av
        ett understreck. Det finns också en valfri tredje del:
        teckenkodning, föregången av en punkt. Ett par exempel för skal av
        POSIX-standard-typ:

            export LANG=nl               Nederländska
            export LANG=nl_NL            Nederländska, Nederländerna
            export LANG=nl_BE            Nederländska, Belgien
            export LANG=es_ES            Spanska, Spanien
            export LANG=es_MX            Spanska, Mexiko
            export LANG=en_US.iso88591   Engelska, USA, Latin-1-kodning
            export LANG=en_GB.UTF-8      Engelska, UK, UTF-8-kodning

        För en fullständig lista över språk och landskoder se vidare i
        gettext-manualen:
        <https://www.gnu.org/software/gettext/manual/html_node/Usual-Languag
        e-Codes.html>

        På Unix-system kan du använda kommando locale(1) för att få
        lokal-specifik information.

    LANGUAGE
        Med miljövariabeln LANGUAGE kan du ange en prioritetslista över
        språk, separerade med kolon. Dos2unix kommer att ge företräde till
        LANGAUGE över LANG. Exempelvis först nederländska och sedan tyska:
        "LANGUAGE=nl:de". Du måste först ha aktiverat lokalisering, genom
        att sätta LANG (eller LC_ALL) till ett värde annat än “C“, innan du
        kan använda en prioritetslista för språk via LANGUAGE-variabeln. Se
        vidare i gettext-manualen:
        <https://www.gnu.org/software/gettext/manual/html_node/The-LANGUAGE-
        variable.html>

        Om du väljer ett språk som inte är tillgänglig kommer du att få
        engelska meddelanden som standard.

    DOS2UNIX_LOCALEDIR
        Med miljövariabeln DOS2UNIX_LOCALEDIR kan LOCALEDIR som ställts in
        vid kompilering åsidosättas. LOCALEDIR används för att hitta
        språkfiler. Standardvärdet för GNU-program är
        "/usr/local/share/locale". Flaggan --version kommer att visa vilken
        LOCALEDIR som används.

        Exempel (POSIX-skal):

            export DOS2UNIX_LOCALEDIR=$HOME/share/locale

RETURVÄRDE
    Om allt går bra kommer noll att returneras. När ett systemfel uppstår
    kommer det senaste systemfelet att returneras. För andra fel kommer 1
    att returneras.

    Returvärdet är alltid noll i tyst läge, utom när felaktiga
    kommandoradsflaggor används.

STANDARDER
    <https://en.wikipedia.org/wiki/Text_file>

    <https://en.wikipedia.org/wiki/Carriage_return>

    <https://en.wikipedia.org/wiki/Newline>

    <https://en.wikipedia.org/wiki/Unicode>

FÖRFATTARE
    Benjamin Lin - <<EMAIL>>, Bernd Johannes Wuebben
    (mac2unix-läge) - <<EMAIL>>, Christian Wurll (lägg till en extra
    radbrytning) - <<EMAIL>>, Erwin Waterlander -
    <<EMAIL>> (upphovsman)

    Projektsida: <https://waterlan.home.xs4all.nl/dos2unix.html>

    SourceForge-sida: <https://sourceforge.net/projects/dos2unix/>

SE ÄVEN
    file(1) find(1) iconv(1) locale(1) xargs(1)

