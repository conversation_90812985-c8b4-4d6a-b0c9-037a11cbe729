.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_psk_client_get_hint" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_psk_client_get_hint \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const char * gnutls_psk_client_get_hint(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.SH "DESCRIPTION"
The PSK identity hint may give the client help in deciding which
username to use.  This should only be called in case of PSK
authentication and in case of a client.
.SH "NOTE"
there is no hint in TLS 1.3, so this function will return \fBNULL\fP
if TLS 1.3 has been negotiated.
.SH "RETURNS"
the identity hint of the peer, or \fBNULL\fP in case of an error or if TLS 1.3 is being used.
.SH "SINCE"
2.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
