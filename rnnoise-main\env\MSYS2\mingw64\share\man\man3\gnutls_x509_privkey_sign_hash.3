.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_sign_hash" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_sign_hash \- API function
.SH SYNOPSIS
.B #include <gnutls/compat.h>
.sp
.BI "int gnutls_x509_privkey_sign_hash(gnutls_x509_privkey_t " key ", const gnutls_datum_t * " hash ", gnutls_datum_t * " signature ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t key" 12
a key
.IP "const gnutls_datum_t * hash" 12
holds the data to be signed
.IP "gnutls_datum_t * signature" 12
will contain newly allocated signature
.SH "DESCRIPTION"
This function will sign the given hash using the private key. Do not
use this function directly unless you know what it is. Typical signing
requires the data to be hashed and stored in special formats 
(e.g. BER Digest\-Info for RSA).

This API is provided only for backwards compatibility, and thus
restricted to RSA, DSA and ECDSA key types. For other key types please
use \fBgnutls_privkey_sign_hash()\fP and \fBgnutls_privkey_sign_data()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.

Deprecated in: 2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
