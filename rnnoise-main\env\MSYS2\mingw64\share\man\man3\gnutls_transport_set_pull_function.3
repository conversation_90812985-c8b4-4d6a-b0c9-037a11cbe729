.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_transport_set_pull_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_transport_set_pull_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_transport_set_pull_function(gnutls_session_t " session ", gnutls_pull_func " pull_func ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_pull_func pull_func" 12
a callback function similar to \fBread()\fP
.SH "DESCRIPTION"
This is the function where you set a function for gnutls to receive
data.  Normally, if you use berkeley style sockets, do not need to
use this function since the default recv(2) will probably be ok.
The callback should return 0 on connection termination, a positive
number indicating the number of bytes received, and \-1 on error.

 \fIgnutls_pull_func\fP is of the form,
ssize_t (*gnutls_pull_func)(gnutls_transport_ptr_t, void*, size_t);
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
