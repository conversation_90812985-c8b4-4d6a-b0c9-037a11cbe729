.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_recv_packet" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_recv_packet \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "ssize_t gnutls_record_recv_packet(gnutls_session_t " session ", gnutls_packet_t * " packet ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_packet_t * packet" 12
the structure that will hold the packet data
.SH "DESCRIPTION"
This is a lower\-level function than \fBgnutls_record_recv()\fP and allows
to directly receive the whole decrypted packet. That avoids a
memory copy, and is intended to be used by applications seeking high
performance.

The received packet is accessed using \fBgnutls_packet_get()\fP and
must be deinitialized using \fBgnutls_packet_deinit()\fP. The returned
packet will be \fBNULL\fP if the return value is zero (EOF).
.SH "RETURNS"
The number of bytes received and zero on EOF (for stream
connections).  A negative error code is returned in case of an error.
.SH "SINCE"
3.3.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
