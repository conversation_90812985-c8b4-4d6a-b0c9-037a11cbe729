<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BIO_s_dgram_pair</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#THREADING">THREADING</a></li>
  <li><a href="#CAPABILITY-INDICATION">CAPABILITY INDICATION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BIO_s_dgram_pair, BIO_new_bio_dgram_pair, BIO_dgram_set_no_trunc, BIO_dgram_get_no_trunc, BIO_dgram_get_effective_caps, BIO_dgram_get_caps, BIO_dgram_set_caps, BIO_dgram_set_mtu, BIO_dgram_get_mtu, BIO_dgram_set0_local_addr - datagram pair BIO</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/bio.h&gt;

const BIO_METHOD *BIO_s_dgram_pair(void);

int BIO_new_bio_dgram_pair(BIO **bio1, size_t writebuf1,
                           BIO **bio2, size_t writebuf2);
int BIO_dgram_set_no_trunc(BIO *bio, int enable);
int BIO_dgram_get_no_trunc(BIO *bio);
uint32_t BIO_dgram_get_effective_caps(BIO *bio);
uint32_t BIO_dgram_get_caps(BIO *bio);
int BIO_dgram_set_caps(BIO *bio, uint32_t caps);
int BIO_dgram_set_mtu(BIO *bio, unsigned int mtu);
unsigned int BIO_dgram_get_mtu(BIO *bio);
int BIO_dgram_set0_local_addr(BIO *bio, BIO_ADDR *addr);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>BIO_s_dgram_pair() returns the method for a BIO datagram pair. A BIO datagram pair is similar to a BIO pair (see <a href="../man3/BIO_s_bio.html">BIO_s_bio(3)</a>) but has datagram semantics. Broadly, this means that the length of the buffer passed to a write call will match that retrieved by a read call. If the buffer passed to a read call is too short, the datagram is truncated or the read fails, depending on how the BIO is configured.</p>

<p>The BIO datagram pair attaches certain metadata to each write, such as source and destination addresses. This information may be retrieved on read.</p>

<p>A typical application of a BIO datagram pair is to allow an application to keep all datagram network I/O requested by libssl under application control.</p>

<p>The BIO datagram pair is designed to support multithreaded use where certain restrictions are observed; see THREADING.</p>

<p>The BIO datagram pair allows each half of a pair to signal to the other half whether they support certain capabilities; see CAPABILITY INDICATION.</p>

<p>BIO_new_bio_dgram_pair() combines the calls to <a href="../man3/BIO_new.html">BIO_new(3)</a>, <a href="../man3/BIO_make_bio_pair.html">BIO_make_bio_pair(3)</a> and <a href="../man3/BIO_set_write_buf_size.html">BIO_set_write_buf_size(3)</a> to create a connected pair of BIOs <b>bio1</b>, <b>bio2</b> with write buffer sizes <b>writebuf1</b> and <b>writebuf2</b>. If either size is zero then the default size is used.</p>

<p><a href="../man3/BIO_make_bio_pair.html">BIO_make_bio_pair(3)</a> may be used to join two datagram pair BIOs into a pair. The two BIOs must both use the method returned by BIO_s_dgram_pair() and neither of the BIOs may currently be associated in a pair.</p>

<p><a href="../man3/BIO_destroy_bio_pair.html">BIO_destroy_bio_pair(3)</a> destroys the association between two connected BIOs. Freeing either half of the pair will automatically destroy the association.</p>

<p><a href="../man3/BIO_reset.html">BIO_reset(3)</a> clears any data in the write buffer of the given BIO. This means that the opposite BIO in the pair will no longer have any data waiting to be read.</p>

<p>The BIO maintains a fixed size internal write buffer. When the buffer is full, further writes will fail until the buffer is drained via calls to <a href="../man3/BIO_read.html">BIO_read(3)</a>. The size of the buffer can be changed using <a href="../man3/BIO_set_write_buf_size.html">BIO_set_write_buf_size(3)</a> and queried using <a href="../man3/BIO_get_write_buf_size.html">BIO_get_write_buf_size(3)</a>.</p>

<p>Note that the write buffer is partially consumed by metadata stored internally which is attached to each datagram, such as source and destination addresses. The size of this overhead is undefined and may change between releases.</p>

<p>The standard <a href="../man3/BIO_ctrl_pending.html">BIO_ctrl_pending(3)</a> call has modified behaviour and returns the size of the next datagram waiting to be read in bytes. An application can use this function to ensure it provides an adequate buffer to a subsequent read call. If no datagram is waiting to be read, zero is returned.</p>

<p>This BIO does not support sending or receiving zero-length datagrams. Passing a zero-length buffer to BIO_write is treated as a no-op.</p>

<p><a href="../man3/BIO_eof.html">BIO_eof(3)</a> returns 1 only if the given BIO datagram pair BIO is not currently connected to a peer BIO.</p>

<p><a href="../man3/BIO_get_write_guarantee.html">BIO_get_write_guarantee(3)</a> and <a href="../man3/BIO_ctrl_get_write_guarantee.html">BIO_ctrl_get_write_guarantee(3)</a> return how large a datagram the next call to <a href="../man3/BIO_write.html">BIO_write(3)</a> can accept. If there is not enough space in the write buffer to accept another datagram equal in size to the configured MTU, zero is returned (see below). This is intended to avoid a situation where an application attempts to read a datagram from a network intending to write it to a BIO datagram pair, but where the received datagram ends up being too large to write to the BIO datagram pair.</p>

<p>BIO_dgram_set_no_trunc() and BIO_ctrl_get_no_trunc() set and retrieve the truncation mode for the given half of a BIO datagram pair. When no-truncate mode is enabled, BIO_read() will fail if the buffer provided is inadequate to hold the next datagram to be read. If no-truncate mode is disabled (the default), the datagram will be silently truncated. This default behaviour maintains compatibility with the semantics of the Berkeley sockets API.</p>

<p>BIO_dgram_set_mtu() and BIO_dgram_get_mtu() may be used to set an informational MTU value on the BIO datagram pair. If BIO_dgram_set_mtu() is used on a BIO which is currently part of a BIO datagram pair, the MTU value is set on both halves of the pair. The value does not affect the operation of the BIO datagram pair (except for BIO_get_write_guarantee(); see above) but may be used by other code to determine a requested MTU. When a BIO datagram pair BIO is created, the MTU is set to an unspecified but valid value.</p>

<p>BIO_dgram_set0_local_addr() can be used to set the local BIO_ADDR to be used when sending a datagram via a BIO datagram pair. This becomes the peer address when receiving on the other half of the pair. If the BIO is used in a call to <a href="../man3/BIO_sendmmsg.html">BIO_sendmmsg(3)</a> and a local address is explicitly specified, then the explicitly specified local address takes precedence. The reference to the BIO_ADDR is passed to the BIO by this call and will be freed automatically when the BIO is freed.</p>

<p><a href="../man3/BIO_flush.html">BIO_flush(3)</a> is a no-op.</p>

<h1 id="NOTES">NOTES</h1>

<p>The halves of a BIO datagram pair have independent lifetimes and must be separately freed.</p>

<h1 id="THREADING">THREADING</h1>

<p><a href="../man3/BIO_recvmmsg.html">BIO_recvmmsg(3)</a>, <a href="../man3/BIO_sendmmsg.html">BIO_sendmmsg(3)</a>, <a href="../man3/BIO_read.html">BIO_read(3)</a>, <a href="../man3/BIO_write.html">BIO_write(3)</a>, <a href="../man3/BIO_pending.html">BIO_pending(3)</a>, <a href="../man3/BIO_get_write_guarantee.html">BIO_get_write_guarantee(3)</a> and <a href="../man3/BIO_flush.html">BIO_flush(3)</a> may be used by multiple threads simultaneously on the same BIO datagram pair. Specific <a href="../man3/BIO_ctrl.html">BIO_ctrl(3)</a> operations (namely BIO_CTRL_PENDING, BIO_CTRL_FLUSH and BIO_C_GET_WRITE_GUARANTEE) may also be used. Invoking any other BIO call, or any other <a href="../man3/BIO_ctrl.html">BIO_ctrl(3)</a> operation, on either half of a BIO datagram pair while any other BIO call is also in progress to either half of the same BIO datagram pair results in undefined behaviour.</p>

<h1 id="CAPABILITY-INDICATION">CAPABILITY INDICATION</h1>

<p>The BIO datagram pair can be used to enqueue datagrams which have source and destination addresses attached. It is important that the component consuming one side of a BIO datagram pair understand whether the other side of the pair will honour any source and destination addresses it attaches to each datagram. For example, if datagrams are queued with destination addresses set but simply read by simple calls to <a href="../man3/BIO_read.html">BIO_read(3)</a>, the destination addresses will be discarded.</p>

<p>Each half of a BIO datagram pair can have capability flags set on it which indicate whether source and destination addresses will be honoured by the reader and whether they will be provided by the writer. These capability flags should be set via a call to BIO_dgram_set_caps(), and these capabilities will be reflected in the value returned by BIO_dgram_get_effective_caps() on the opposite BIO. If necessary, the capability value previously set can be retrieved using BIO_dgram_get_caps(). Note that BIO_dgram_set_caps() on a given BIO controls the capabilities advertised to the peer, and BIO_dgram_get_effective_caps() on a given BIO determines the capabilities advertised by the peer of that BIO.</p>

<p>The following capabilities are available:</p>

<dl>

<dt id="BIO_DGRAM_CAP_HANDLES_SRC_ADDR"><b>BIO_DGRAM_CAP_HANDLES_SRC_ADDR</b></dt>
<dd>

<p>The user of the datagram pair BIO promises to honour source addresses provided with datagrams written to the BIO pair.</p>

</dd>
<dt id="BIO_DGRAM_CAP_HANDLES_DST_ADDR"><b>BIO_DGRAM_CAP_HANDLES_DST_ADDR</b></dt>
<dd>

<p>The user of the datagram pair BIO promises to honour destination addresses provided with datagrams written to the BIO pair.</p>

</dd>
<dt id="BIO_DGRAM_CAP_PROVIDES_SRC_ADDR"><b>BIO_DGRAM_CAP_PROVIDES_SRC_ADDR</b></dt>
<dd>

<p>The user of the datagram pair BIO advertises the fact that it will provide source addressing information with future writes to the BIO pair, where available.</p>

</dd>
<dt id="BIO_DGRAM_CAP_PROVIDES_DST_ADDR"><b>BIO_DGRAM_CAP_PROVIDES_DST_ADDR</b></dt>
<dd>

<p>The user of the datagram pair BIO advertises the fact that it will provide destination addressing information with future writes to the BIO pair, where available.</p>

</dd>
</dl>

<p>If a caller attempts to specify a destination address (for example, using <a href="../man3/BIO_sendmmsg.html">BIO_sendmmsg(3)</a>) and the peer has not advertised the <b>BIO_DGRAM_CAP_HANDLES_DST_ADDR</b> capability, the operation fails. Thus, capability negotiation is mandatory.</p>

<p>If a caller attempts to specify a source address when writing, or requests a destination address when receiving, and local address support has not been enabled, the operation fails; see <a href="../man3/BIO_dgram_set_local_addr_enable.html">BIO_dgram_set_local_addr_enable(3)</a>.</p>

<p>If a caller attempts to enable local address support using <a href="../man3/BIO_dgram_set_local_addr_enable.html">BIO_dgram_set_local_addr_enable(3)</a> and <a href="../man3/BIO_dgram_get_local_addr_cap.html">BIO_dgram_get_local_addr_cap(3)</a> does not return 1 (meaning that the peer has not advertised both the <b>BIO_DGRAM_CAP_HANDLES_SRC_ADDR</b> and the <b>BIO_DGRAM_CAP_PROVIDES_DST_ADDR</b> capability), the operation fails.</p>

<p><b>BIO_DGRAM_CAP_PROVIDES_SRC_ADDR</b> and <b>BIO_DGRAM_CAP_PROVIDES_DST_ADDR</b> indicate that the application using that half of a BIO datagram pair promises to provide source and destination addresses respectively when writing datagrams to that half of the BIO datagram pair. However, these capability flags do not affect the behaviour of the BIO datagram pair.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BIO_new_bio_dgram_pair() returns 1 on success, with the new BIOs available in <b>bio1</b> and <b>bio2</b>, or 0 on failure, with NULL pointers stored into the locations for <b>bio1</b> and <b>bio2</b>. Check the error stack for more information.</p>

<p>BIO_dgram_set_no_trunc(), BIO_dgram_set_caps() and BIO_dgram_set_mtu() return 1 on success and 0 on failure.</p>

<p>BIO_dgram_get_no_trunc() returns 1 if no-truncate mode is enabled on a BIO, or 0 if no-truncate mode is not enabled or not supported on a given BIO.</p>

<p>BIO_dgram_get_effective_caps() and BIO_dgram_get_caps() return zero if no capabilities are supported.</p>

<p>BIO_dgram_get_mtu() returns the MTU value configured on the BIO, or zero if the operation is not supported.</p>

<p>BIO_dgram_set0_local_addr() returns 1 on success and &lt;= 0 otherwise.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/BIO_s_bio.html">BIO_s_bio(3)</a>, <a href="../man7/bio.html">bio(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>BIO_s_dgram_pair(), BIO_new_bio_dgram_pair() were added in OpenSSL 3.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2022-2025 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


