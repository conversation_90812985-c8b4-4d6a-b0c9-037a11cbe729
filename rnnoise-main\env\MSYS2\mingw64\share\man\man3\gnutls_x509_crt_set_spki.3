.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_spki" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_spki \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_spki(gnutls_x509_crt_t " crt ", const gnutls_x509_spki_t " spki ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
a certificate of type \fBgnutls_x509_crt_t\fP
.IP "const gnutls_x509_spki_t spki" 12
a SubjectPublicKeyInfo structure of type \fBgnutls_x509_spki_t\fP
.IP "unsigned int flags" 12
must be zero
.SH "DESCRIPTION"
This function will set the certificate's subject public key
information explicitly. This is intended to be used in the cases
where a single public key (e.g., RSA) can be used for multiple
signature algorithms (RSA PKCS1\-1.5, and RSA\-PSS).

To export the public key (i.e., the SubjectPublicKeyInfo part), check
\fBgnutls_pubkey_import_x509()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.6.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
