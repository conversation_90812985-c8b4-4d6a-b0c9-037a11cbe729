<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>Cygwin User's Guide</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><meta name="description" content="Cygwin User's Guide"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="next" href="overview.html" title="Chapter&#160;1.&#160;Cygwin Overview"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">Cygwin User's Guide</th></tr><tr><td width="20%" align="left">&#160;</td><th width="60%" align="center">&#160;</th><td width="20%" align="right">&#160;<a accesskey="n" href="overview.html">Next</a></td></tr></table><hr></div><div class="book"><div class="titlepage"><div><div><h1 class="title"><a name="cygwin-ug-net"></a>Cygwin User's Guide</h1></div><div><div class="legalnotice"><a name="legal"></a><p>Copyright &#169; Cygwin authors</p><p>Permission is granted to make and distribute verbatim copies of
this documentation provided the copyright notice and this permission
notice are preserved on all copies.</p><p>Permission is granted to copy and distribute modified versions
of this documentation under the conditions for verbatim copying,
provided that the entire resulting derived work is distributed under
the terms of a permission notice identical to this one.</p><p>Permission is granted to copy and distribute translations of
this documentation into another language, under the above conditions
for modified versions, except that this permission notice may be
stated in a translation approved by the Free Software
Foundation.</p></div></div><div><div class="abstract"><p class="title"><b>Abstract</b></p><p>Cygwin User's Guide</p></div></div></div><hr></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="chapter"><a href="overview.html">1. Cygwin Overview</a></span></dt><dd><dl><dt><span class="sect1"><a href="overview.html#what-is-it">What is it?</a></span></dt><dt><span class="sect1"><a href="ov-ex-win.html">Quick Start Guide for those more experienced with Windows</a></span></dt><dt><span class="sect1"><a href="ov-ex-unix.html">Quick Start Guide for those more experienced with UNIX</a></span></dt><dt><span class="sect1"><a href="are-free.html">Are the Cygwin tools free software?</a></span></dt><dt><span class="sect1"><a href="brief-history.html">A brief history of the Cygwin project</a></span></dt><dt><span class="sect1"><a href="highlights.html">Highlights of Cygwin Functionality</a></span></dt><dd><dl><dt><span class="sect2"><a href="highlights.html#ov-hi-intro">Introduction</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-perm">Permissions and Security</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-files">File Access</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-textvsbinary">Text Mode vs. Binary Mode</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-ansiclib">ANSI C Library</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-process">Process Creation</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-signals">Signals</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-sockets">Sockets</a></span></dt><dt><span class="sect2"><a href="highlights.html#ov-hi-select">Select</a></span></dt></dl></dd><dt><span class="sect1"><a href="ov-new.html">What's new and what changed in Cygwin</a></span></dt><dd><dl><dt><span class="sect2"><a href="ov-new.html#ov-new3.6">What's new and what changed in 3.6</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new3.5">What's new and what changed in 3.5</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new3.4">What's new and what changed in 3.4</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new3.3">What's new and what changed in 3.3</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new3.2">What's new and what changed in 3.2</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new3.1">What's new and what changed in 3.1</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new3.0">What's new and what changed in 3.0</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.11">What's new and what changed in 2.11</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.10">What's new and what changed in 2.10</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.9">What's new and what changed in 2.9</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.8">What's new and what changed in 2.8</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.7">What's new and what changed in 2.7</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.6">What's new and what changed in 2.6</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.5">What's new and what changed in 2.5</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.4">What's new and what changed in 2.4</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.3">What's new and what changed in 2.3</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.2">What's new and what changed in 2.2</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.1">What's new and what changed in 2.1</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new2.0">What's new and what changed in 2.0</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.35">What's new and what changed in 1.7.35</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.34">What's new and what changed in 1.7.34</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.33">What's new and what changed in 1.7.33</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.32">What's new and what changed in 1.7.32</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.31">What's new and what changed in 1.7.31</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.29">What's new and what changed in 1.7.29</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.28">What's new and what changed in 1.7.28</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.27">What's new and what changed in 1.7.27</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.26">What's new and what changed in 1.7.26</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.25">What's new and what changed in 1.7.25</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.24">What's new and what changed in 1.7.24</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.23">What's new and what changed in 1.7.23</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.22">What's new and what changed in 1.7.22</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.21">What's new and what changed in 1.7.21</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.19">What's new and what changed in 1.7.19</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.18">What's new and what changed in 1.7.18</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.17">What's new and what changed in 1.7.17</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.16">What's new and what changed in 1.7.16</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.15">What's new and what changed in 1.7.15</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.14">What's new and what changed in 1.7.14</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.13">What's new and what changed in 1.7.13</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.12">What's new and what changed in 1.7.12</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.11">What's new and what changed in 1.7.11</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.10">What's new and what changed in 1.7.10</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.9">What's new and what changed in 1.7.9</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.8">What's new and what changed in 1.7.8</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.7">What's new and what changed in 1.7.7</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.6">What's new and what changed in 1.7.6</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.5">What's new and what changed in 1.7.5</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.3">What's new and what changed in 1.7.3</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.2">What's new and what changed in 1.7.2</a></span></dt><dt><span class="sect2"><a href="ov-new.html#ov-new1.7.1">What's new and what changed from 1.5 to 1.7</a></span></dt><dd><dl><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-os">OS related changes</a></span></dt><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-file">File Access related changes</a></span></dt><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-net">Network related changes</a></span></dt><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-device">Device related changes</a></span></dt><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-posix">Other POSIX related changes</a></span></dt><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-sec">Security related changes</a></span></dt><dt><span class="sect3"><a href="ov-new.html#ov-new1.7-misc">Miscellaneous</a></span></dt></dl></dd></dl></dd></dl></dd><dt><span class="chapter"><a href="setup-net.html">2. Setting Up Cygwin</a></span></dt><dd><dl><dt><span class="sect1"><a href="setup-net.html#internet-setup">Internet Setup</a></span></dt><dd><dl><dt><span class="sect2"><a href="setup-net.html#setup-download">Download Source</a></span></dt><dt><span class="sect2"><a href="setup-net.html#setup-dir">Selecting an Install Directory</a></span></dt><dt><span class="sect2"><a href="setup-net.html#setup-localdir">Local Package Directory</a></span></dt><dt><span class="sect2"><a href="setup-net.html#setup-connection">Connection Method</a></span></dt><dt><span class="sect2"><a href="setup-net.html#setup-mirror">Choosing Mirrors</a></span></dt><dt><span class="sect2"><a href="setup-net.html#setup-packages">Choosing Packages</a></span></dt><dt><span class="sect2"><a href="setup-net.html#setup-progress">Download and Installation Progress</a></span></dt><dt><span class="sect2"><a href="setup-net.html#setup-icons">Shortcuts</a></span></dt><dt><span class="sect2"><a href="setup-net.html#setup-postinstall">Post-Install Scripts</a></span></dt><dt><span class="sect2"><a href="setup-net.html#setup-troubleshooting">Troubleshooting</a></span></dt></dl></dd><dt><span class="sect1"><a href="setup-env.html">Environment Variables</a></span></dt><dd><dl><dt><span class="sect2"><a href="setup-env.html#setup-env-ov">Overview</a></span></dt><dt><span class="sect2"><a href="setup-env.html#setup-env-win32">Restricted Win32 environment</a></span></dt></dl></dd><dt><span class="sect1"><a href="setup-maxmem.html">Changing Cygwin's Maximum Memory</a></span></dt><dt><span class="sect1"><a href="setup-locale.html">Internationalization</a></span></dt><dd><dl><dt><span class="sect2"><a href="setup-locale.html#setup-locale-ov">Overview</a></span></dt><dt><span class="sect2"><a href="setup-locale.html#setup-locale-how">How to set the locale</a></span></dt><dt><span class="sect2"><a href="setup-locale.html#setup-locale-console">The Windows Console character set</a></span></dt><dt><span class="sect2"><a href="setup-locale.html#setup-locale-problems">Potential Problems when using Locales</a></span></dt><dt><span class="sect2"><a href="setup-locale.html#setup-locale-charsetlist">List of supported character sets</a></span></dt></dl></dd><dt><span class="sect1"><a href="setup-files.html">Customizing bash</a></span></dt></dl></dd><dt><span class="chapter"><a href="using.html">3. Using Cygwin</a></span></dt><dd><dl><dt><span class="sect1"><a href="using.html#using-pathnames">Mapping path names</a></span></dt><dd><dl><dt><span class="sect2"><a href="using.html#pathnames-intro">Introduction</a></span></dt><dt><span class="sect2"><a href="using.html#mount-table">The Cygwin Mount Table</a></span></dt><dt><span class="sect2"><a href="using.html#unc-paths">UNC paths</a></span></dt><dt><span class="sect2"><a href="using.html#cygdrive">The cygdrive path prefix</a></span></dt><dt><span class="sect2"><a href="using.html#usertemp">The usertemp file system type</a></span></dt><dt><span class="sect2"><a href="using.html#pathnames-symlinks">Symbolic links</a></span></dt><dt><span class="sect2"><a href="using.html#pathnames-win32">Using native Win32 paths</a></span></dt><dt><span class="sect2"><a href="using.html#pathnames-win32-api">Using the Win32 file API in Cygwin applications</a></span></dt><dt><span class="sect2"><a href="using.html#pathnames-additional">Additional Path-related Information</a></span></dt></dl></dd><dt><span class="sect1"><a href="using-textbinary.html">Text and Binary modes</a></span></dt><dd><dl><dt><span class="sect2"><a href="using-textbinary.html#textbin-issue">The Issue</a></span></dt><dt><span class="sect2"><a href="using-textbinary.html#textbin-default">The default Cygwin behavior</a></span></dt><dt><span class="sect2"><a href="using-textbinary.html#textbin-question">Binary or text?</a></span></dt><dt><span class="sect2"><a href="using-textbinary.html#textbin-devel">Programming</a></span></dt></dl></dd><dt><span class="sect1"><a href="using-filemodes.html">File permissions</a></span></dt><dt><span class="sect1"><a href="using-specialnames.html">Special filenames</a></span></dt><dd><dl><dt><span class="sect2"><a href="using-specialnames.html#pathnames-etc">Special files in /etc</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-dosdevices">Invalid filenames</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-specialchars">Forbidden characters in filenames</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-unusual">Filenames with unusual (foreign) characters</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-casesensitive">Case sensitive filenames</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-casesensitivedirs">Case sensitive directories</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-posixdevices">POSIX devices</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-exe">The .exe extension</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-proc">The /proc filesystem</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-proc-registry">The /proc/registry filesystem</a></span></dt><dt><span class="sect2"><a href="using-specialnames.html#pathnames-at">The @pathnames</a></span></dt></dl></dd><dt><span class="sect1"><a href="using-cygwinenv.html">The <code class="envar">CYGWIN</code> environment
variable</a></span></dt><dd><dl><dt><span class="sect2"><a href="using-cygwinenv.html#cygwinenv-implemented-options">Implemented options</a></span></dt><dt><span class="sect2"><a href="using-cygwinenv.html#cygwinenv-removed-options">Obsolete options</a></span></dt></dl></dd><dt><span class="sect1"><a href="ntsec.html">POSIX accounts, permission, and security</a></span></dt><dd><dl><dt><span class="sect2"><a href="ntsec.html#ntsec-common">Brief overview of Windows security</a></span></dt><dt><span class="sect2"><a href="ntsec.html#ntsec-mapping">Mapping Windows accounts to POSIX accounts</a></span></dt><dd><dl><dt><span class="sect3"><a href="ntsec.html#ntsec-mapping-how">Mapping Windows SIDs to POSIX uid/gid values</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-mapping-caching">Caching account information</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-mapping-passwdinfo">Cygwin user names, home dirs, login shells</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-mapping-nsswitch">The <code class="filename">/etc/nsswitch.conf</code> file</a></span></dt><dd><dl><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-syntax">The <code class="filename">/etc/nsswitch.conf</code> syntax</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-pwdgrp">The <code class="literal">passwd:</code> and <code class="literal">group:</code> settings</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-enum">The <code class="literal">db_enum:</code> setting</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-passwd">Settings defining how to create the <code class="literal">passwd</code> entry</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-home">The <code class="literal">db_home</code> setting</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-shell">The <code class="literal">db_shell</code> setting</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-gecos">The <code class="literal">db_gecos</code> setting</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-cygwin">The <code class="literal">cygwin</code> schema</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-posix">The <code class="literal">unix</code> schema</a></span></dt><dt><span class="sect4"><a href="ntsec.html#ntsec-mapping-nsswitch-desc">The <code class="literal">desc</code> schema</a></span></dt></dl></dd><dt><span class="sect3"><a href="ntsec.html#ntsec-mapping-nfs">NFS account mapping</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-mapping-samba">Samba account mapping</a></span></dt></dl></dd><dt><span class="sect2"><a href="ntsec.html#ntsec-files">File permissions</a></span></dt><dt><span class="sect2"><a href="ntsec.html#ntsec-setuid-overview">Switching the user context</a></span></dt><dd><dl><dt><span class="sect3"><a href="ntsec.html#ntsec-logonuser">Switching the user context with password authentication</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-nopasswd1">Switching the user context without password, Method 1: Kerberos/MsV1_0 S4U authentication</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-nopasswd3">Switching the user context without password, Method 2: With password</a></span></dt><dt><span class="sect3"><a href="ntsec.html#ntsec-setuid-impl">Switching the user context, how does it all fit together?</a></span></dt></dl></dd></dl></dd><dt><span class="sect1"><a href="using-cygserver.html">Cygserver</a></span></dt><dd><dl><dt><span class="sect2"><a href="using-cygserver.html#what-is-cygserver">What is Cygserver?</a></span></dt><dt><span class="sect2"><a href="using-cygserver.html#cygserver-command-line">Cygserver command line options</a></span></dt><dt><span class="sect2"><a href="using-cygserver.html#install-cygserver">How to install Cygserver</a></span></dt><dt><span class="sect2"><a href="using-cygserver.html#start-cygserver">How to start Cygserver</a></span></dt><dt><span class="sect2"><a href="using-cygserver.html#cygserver-config">The Cygserver configuration file</a></span></dt></dl></dd><dt><span class="sect1"><a href="using-utils.html">Cygwin Utilities</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="chattr.html">chattr</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="cygcheck.html">cygcheck</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="cygpath.html">cygpath</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="dumper.html">dumper</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="getconf.html">getconf</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="getfacl.html">getfacl</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="gmondump.html">gmondump</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="kill.html">kill</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="ldd.html">ldd</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="locale.html">locale</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="lsattr.html">lsattr</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="minidumper.html">minidumper</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="mkgroup.html">mkgroup</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="mkpasswd.html">mkpasswd</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="mount.html">mount</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="newgrp.html">newgrp</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="passwd.html">passwd</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="pldd.html">pldd</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="profiler.html">profiler</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="ps.html">ps</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="regtool.html">regtool</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="setfacl.html">setfacl</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="setmetamode.html">setmetamode</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="ssp.html">ssp</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="strace.html">strace</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="tzset.html">tzset</a></span><span class="refpurpose"></span></dt><dt><span class="refentrytitle"><a href="umount.html">umount</a></span><span class="refpurpose"></span></dt></dl></dd><dt><span class="sect1"><a href="using-effectively.html">Using Cygwin effectively with Windows</a></span></dt><dd><dl><dt><span class="sect2"><a href="using-effectively.html#using-pathnames-effectively">Pathnames</a></span></dt><dt><span class="sect2"><a href="using-effectively.html#using-net">Cygwin and Windows Networking</a></span></dt><dt><span class="sect2"><a href="using-effectively.html#using-shortcuts">Creating shortcuts</a></span></dt><dt><span class="sect2"><a href="using-effectively.html#using-printing">Printing</a></span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="programming.html">4. Programming with Cygwin</a></span></dt><dd><dl><dt><span class="sect1"><a href="programming.html#gcc">Using GCC with Cygwin</a></span></dt><dd><dl><dt><span class="sect2"><a href="programming.html#gcc-default">Standard Usage</a></span></dt><dt><span class="sect2"><a href="programming.html#gcc-64">Building applications for 64 bit Cygwin</a></span></dt><dt><span class="sect2"><a href="programming.html#gcc-gui">GUI Mode Applications</a></span></dt></dl></dd><dt><span class="sect1"><a href="gdb.html">Debugging Cygwin Programs</a></span></dt><dt><span class="sect1"><a href="dll.html">Building and Using DLLs</a></span></dt><dd><dl><dt><span class="sect2"><a href="dll.html#dll-build">Building DLLs</a></span></dt><dt><span class="sect2"><a href="dll.html#dll-link">Linking Against DLLs</a></span></dt></dl></dd><dt><span class="sect1"><a href="windres.html">Defining Windows Resources</a></span></dt><dt><span class="sect1"><a href="gprof.html">Profiling Cygwin Programs</a></span></dt><dd><dl><dt><span class="sect2"><a href="gprof.html#gprof-intro">Introduction</a></span></dt><dt><span class="sect2"><a href="gprof.html#gprof-ex">Examples</a></span></dt><dt><span class="sect2"><a href="gprof.html#gprof-ss">Special situations</a></span></dt><dd><dl><dt><span class="sect3"><a href="gprof.html#gprof-mt">Profiling multi-threaded programs</a></span></dt><dt><span class="sect3"><a href="gprof.html#gprof-fork">Profiling programs that fork</a></span></dt><dt><span class="sect3"><a href="gprof.html#gprof-res">Getting better profiling resolution</a></span></dt><dt><span class="sect3"><a href="gprof.html#gprof-lib">Profiling programs with their libraries</a></span></dt><dt><span class="sect3"><a href="gprof.html#gprof-cyg">Profiling Cygwin itself</a></span></dt></dl></dd></dl></dd></dl></dd></dl></div><div class="list-of-examples"><p><b>List of Examples</b></p><dl><dt>3.1. <a href="using.html#pathnames-mount-ex">Displaying the current set of mount points</a></dt><dt>3.2. <a href="using.html#pathnames-mount-dir">Displaying Windows mount points as cygdrives</a></dt><dt>3.3. <a href="using-specialnames.html#pathnames-at-ex"> Using @pathname</a></dt><dt>3.4. <a href="cygcheck.html#utils-cygcheck-eiex">Example <span class="command">cygcheck</span> -e/-i
      usage</a></dt><dt>3.5. <a href="cygcheck.html#utils-cygcheck-ex">Example <span class="command">cygcheck</span> -f/-l
      usage</a></dt><dt>3.6. <a href="cygcheck.html#utils-search-ex">Searching all packages for a
      file</a></dt><dt>3.7. <a href="cygpath.html#utils-cygpath-ex">Example <span class="command">cygpath</span> usage</a></dt><dt>3.8. <a href="kill.html#utils-kill-ex">Using the kill command</a></dt><dt>3.9. <a href="mkgroup.html#utils-mkgroup-ex">Setting up group entry for current user with different
        domain/group separator</a></dt><dt>3.10. <a href="mkpasswd.html#utils-althome-ex">Using an alternate home root</a></dt><dt>3.11. <a href="mount.html#utils-mount-ex">Displaying the current set of mount points</a></dt><dt>3.12. <a href="mount.html#utils-mount-add-ex">Adding mount points</a></dt><dt>3.13. <a href="mount.html#utils-cygdrive-ex">Changing the default prefix</a></dt><dt>3.14. <a href="mount.html#utils-cygdrive-ex2">Changing the default prefix with specific mount options</a></dt><dt>4.1. <a href="programming.html#gcc-hello-world">Building Hello World with GCC</a></dt><dt>4.2. <a href="programming.html#gcc-64-ex1">64bit-programming, Using ReadFile, 1st try</a></dt><dt>4.3. <a href="programming.html#gcc-64-ex2">64bit-programming, Using ReadFile, 2nd try</a></dt><dt>4.4. <a href="gdb.html#gdb-g">Compiling with -g</a></dt><dt>4.5. <a href="gdb.html#gdb-break">"break" in gdb</a></dt><dt>4.6. <a href="gdb.html#gdb-cliargs">Debugging with command line arguments</a></dt><dt>4.7. <a href="gprof.html#gprof-flat">Flat profile</a></dt><dt>4.8. <a href="gprof.html#gprof-cg">Call graph</a></dt><dt>4.9. <a href="gprof.html#gprof-line">Source line profile</a></dt><dt>4.10. <a href="gprof.html#gprof-prefix"></a></dt></dl></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left">&#160;</td><td width="20%" align="center">&#160;</td><td width="40%" align="right">&#160;<a accesskey="n" href="overview.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">&#160;</td><td width="20%" align="center">&#160;</td><td width="40%" align="right" valign="top">&#160;Chapter&#160;1.&#160;Cygwin Overview</td></tr></table></div></body></html>
