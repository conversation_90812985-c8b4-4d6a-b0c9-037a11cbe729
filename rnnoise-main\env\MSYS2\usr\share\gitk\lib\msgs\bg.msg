set ::msgcat::header "Project-Id-Version: gitk master\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2024-12-24 11:05+0100\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: Bulgarian <<EMAIL>>\nLanguage: bg\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPlural-Forms: nplurals=2; plural=(n != 1);\n"
::msgcat::mcset bg "Couldn't get list of unmerged files:" "\u0421\u043f\u0438\u0441\u044a\u043a\u044a\u0442 \u0441 \u043d\u0435\u0441\u043b\u0435\u0442\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043f\u043e\u043b\u0443\u0447\u0438:"
::msgcat::mcset bg "Color words" "\u041e\u0446\u0432\u0435\u0442\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u0434\u0443\u043c\u0438\u0442\u0435"
::msgcat::mcset bg "Markup words" "\u041e\u0442\u0431\u0435\u043b\u044f\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u0434\u0443\u043c\u0438\u0442\u0435"
::msgcat::mcset bg "Error parsing revisions:" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0430\u043d\u0430\u043b\u0438\u0437 \u043d\u0430 \u0432\u0435\u0440\u0441\u0438\u0438\u0442\u0435:"
::msgcat::mcset bg "Error executing --argscmd command:" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0438\u0437\u043f\u044a\u043b\u043d\u0435\u043d\u0438\u0435 \u043d\u0430 \u043a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430 \u0441 \u201e--argscmd\u201c."
::msgcat::mcset bg "No files selected: --merge specified but no files are unmerged." "\u041d\u0435 \u0441\u0430 \u0438\u0437\u0431\u0440\u0430\u043d\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u0435 \u2014 \u0443\u043a\u0430\u0437\u0430\u043d\u0430 \u0435 \u043e\u043f\u0446\u0438\u044f\u0442\u0430 \u201e--merge\u201c, \u043d\u043e \u043d\u044f\u043c\u0430 \u043d\u0435\u0441\u043b\u0435\u0442\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u0435."
::msgcat::mcset bg "No files selected: --merge specified but no unmerged files are within file limit." "\u041d\u0435 \u0441\u0430 \u0438\u0437\u0431\u0440\u0430\u043d\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u0435 \u2014 \u0443\u043a\u0430\u0437\u0430\u043d\u0430 \u0435 \u043e\u043f\u0446\u0438\u044f\u0442\u0430 \u201e--merge\u201c, \u043d\u043e \u043d\u044f\u043c\u0430 \u043d\u0435\u0441\u043b\u0435\u0442\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u0435 \u0432 \u043e\u0433\u0440\u0430\u043d\u0438\u0447\u0435\u043d\u0438\u044f\u0442\u0430."
::msgcat::mcset bg "Error executing git log:" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0438\u0437\u043f\u044a\u043b\u043d\u0435\u043d\u0438\u0435 \u043d\u0430 \u201egit log\u201c:"
::msgcat::mcset bg "Reading" "\u041f\u0440\u043e\u0447\u0438\u0442\u0430\u043d\u0435"
::msgcat::mcset bg "Reading commits..." "\u041f\u0440\u043e\u0447\u0438\u0442\u0430\u043d\u0435 \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f\u0442\u0430\u2026"
::msgcat::mcset bg "No commits selected" "\u041d\u0435 \u0441\u0430 \u0438\u0437\u0431\u0440\u0430\u043d\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f"
::msgcat::mcset bg "Command line" "\u041a\u043e\u043c\u0430\u043d\u0434\u0435\u043d \u0440\u0435\u0434"
::msgcat::mcset bg "Can't parse git log output:" "\u0418\u0437\u0445\u043e\u0434\u044a\u0442 \u043e\u0442 \u201egit log\u201c \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0430\u043d\u0430\u043b\u0438\u0437\u0438\u0440\u0430:"
::msgcat::mcset bg "No commit information available" "\u041b\u0438\u043f\u0441\u0432\u0430 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f"
::msgcat::mcset bg "OK" "\u0414\u043e\u0431\u0440\u0435"
::msgcat::mcset bg "Cancel" "\u041e\u0442\u043a\u0430\u0437"
::msgcat::mcset bg "&Update" "&\u041e\u0431\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "&Reload" "&\u041f\u0440\u0435\u0437\u0430\u0440\u0435\u0436\u0434\u0430\u043d\u0435"
::msgcat::mcset bg "Reread re&ferences" "\u041f\u0440\u043e\u0447\u0438\u0442\u0430\u043d\u0435 &\u043d\u0430\u043d\u043e\u0432\u043e"
::msgcat::mcset bg "&List references" "&\u0418\u0437\u0431\u0440\u043e\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u0443\u043a\u0430\u0437\u0430\u0442\u0435\u043b\u0438\u0442\u0435"
::msgcat::mcset bg "Start git &gui" "&\u0421\u0442\u0430\u0440\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u201egit gui\u201c"
::msgcat::mcset bg "&Quit" "&\u0421\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430"
::msgcat::mcset bg "&File" "&\u0424\u0430\u0439\u043b"
::msgcat::mcset bg "&Preferences" "&\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438"
::msgcat::mcset bg "&Edit" "&\u0420\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u0430\u043d\u0435"
::msgcat::mcset bg "&New view..." "&\u041d\u043e\u0432 \u0438\u0437\u0433\u043b\u0435\u0434\u2026"
::msgcat::mcset bg "&Edit view..." "&\u0420\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0438\u0437\u0433\u043b\u0435\u0434\u0430\u2026"
::msgcat::mcset bg "&Delete view" "&\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u0438\u0437\u0433\u043b\u0435\u0434\u0430"
::msgcat::mcset bg "&All files" "&\u0412\u0441\u0438\u0447\u043a\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u0435"
::msgcat::mcset bg "&View" "&\u0418\u0437\u0433\u043b\u0435\u0434"
::msgcat::mcset bg "&About gitk" "&\u041e\u0442\u043d\u043e\u0441\u043d\u043e gitk"
::msgcat::mcset bg "&Key bindings" "&\u041a\u043b\u0430\u0432\u0438\u0448\u043d\u0438 \u043a\u043e\u043c\u0431\u0438\u043d\u0430\u0446\u0438\u0438"
::msgcat::mcset bg "&Help" "\u041f\u043e\u043c\u043e&\u0449"
::msgcat::mcset bg "Commit ID:" "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0435:"
::msgcat::mcset bg "Row" "\u0420\u0435\u0434"
::msgcat::mcset bg "Find" "\u0422\u044a\u0440\u0441\u0435\u043d\u0435"
::msgcat::mcset bg "commit" "\u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "containing:" "\u0441\u044a\u0434\u044a\u0440\u0436\u0430\u0449\u043e:"
::msgcat::mcset bg "touching paths:" "\u0432 \u043f\u044a\u0442\u0438\u0449\u0430\u0442\u0430:"
::msgcat::mcset bg "adding/removing string:" "\u0434\u043e\u0431\u0430\u0432\u044f\u0449\u043e/\u043f\u0440\u0435\u043c\u0430\u0445\u0432\u0430\u0449\u043e \u043d\u0438\u0437"
::msgcat::mcset bg "changing lines matching:" "\u043f\u0440\u043e\u043c\u0435\u043d\u044f\u0449\u043e \u0440\u0435\u0434\u043e\u0432\u0435 \u043d\u0430\u043f\u0430\u0441\u0432\u0430\u0449\u0438:"
::msgcat::mcset bg "Exact" "\u0422\u043e\u0447\u043d\u043e"
::msgcat::mcset bg "IgnCase" "\u0411\u0435\u0437 \u0440\u0435\u0433\u0438\u0441\u0442\u044a\u0440"
::msgcat::mcset bg "Regexp" "\u0420\u0435\u0433. \u0438\u0437\u0440\u0430\u0437"
::msgcat::mcset bg "All fields" "\u0412\u0441\u0438\u0447\u043a\u0438 \u043f\u043e\u043b\u0435\u0442\u0430"
::msgcat::mcset bg "Headline" "\u041f\u044a\u0440\u0432\u0438 \u0440\u0435\u0434"
::msgcat::mcset bg "Comments" "\u041a\u043e\u043c\u0435\u043d\u0442\u0430\u0440\u0438"
::msgcat::mcset bg "Author" "\u0410\u0432\u0442\u043e\u0440"
::msgcat::mcset bg "Committer" "\u041f\u043e\u0434\u0430\u0432\u0430\u0449"
::msgcat::mcset bg "Search" "\u0422\u044a\u0440\u0441\u0435\u043d\u0435"
::msgcat::mcset bg "Diff" "\u0420\u0430\u0437\u043b\u0438\u043a\u0438"
::msgcat::mcset bg "Old version" "\u0421\u0442\u0430\u0440\u0430 \u0432\u0435\u0440\u0441\u0438\u044f"
::msgcat::mcset bg "New version" "\u041d\u043e\u0432\u0430 \u0432\u0435\u0440\u0441\u0438\u044f"
::msgcat::mcset bg "Lines of context" "\u041a\u043e\u043d\u0442\u0435\u043a\u0441\u0442 \u0432 \u0440\u0435\u0434\u043e\u0432\u0435"
::msgcat::mcset bg "Ignore space change" "\u041f\u0440\u0430\u0437\u043d\u0438\u0442\u0435 \u0437\u043d\u0430\u0446\u0438 \u0431\u0435\u0437 \u0437\u043d\u0430\u0447\u0435\u043d\u0438\u0435"
::msgcat::mcset bg "Line diff" "\u041f\u043e\u0440\u0435\u0434\u043e\u0432\u0438 \u0440\u0430\u0437\u043b\u0438\u043a\u0438"
::msgcat::mcset bg "Patch" "\u041a\u0440\u044a\u043f\u043a\u0430"
::msgcat::mcset bg "Tree" "\u0414\u044a\u0440\u0432\u043e"
::msgcat::mcset bg "Diff this -> selected" "\u0420\u0430\u0437\u043b\u0438\u043a\u0438 \u043c\u0435\u0436\u0434\u0443 \u0442\u043e\u0432\u0430 \u0438 \u0438\u0437\u0431\u0440\u0430\u043d\u043e\u0442\u043e"
::msgcat::mcset bg "Diff selected -> this" "\u0420\u0430\u0437\u043b\u0438\u043a\u0438 \u043c\u0435\u0436\u0434\u0443 \u0438\u0437\u0431\u0440\u0430\u043d\u043e\u0442\u043e \u0438 \u0442\u043e\u0432\u0430"
::msgcat::mcset bg "Make patch" "\u0421\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u0440\u044a\u043f\u043a\u0430"
::msgcat::mcset bg "Create tag" "\u0421\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u0435\u0442\u0438\u043a\u0435\u0442"
::msgcat::mcset bg "Copy commit reference" "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0443\u043a\u0430\u0437\u0430\u0442\u0435\u043b\u044f \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Write commit to file" "\u0417\u0430\u043f\u0430\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u0432\u044a\u0432 \u0444\u0430\u0439\u043b"
::msgcat::mcset bg "Create new branch" "\u0421\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u043d\u043e\u0432 \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Cherry-pick this commit" "\u041e\u0442\u0431\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0442\u043e\u0432\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Reset HEAD branch to here" "\u041f\u0440\u0438\u0432\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0432\u044a\u0440\u0445\u0430 \u043d\u0430 \u043a\u043b\u043e\u043d\u0430 \u043a\u044a\u043c \u0442\u0435\u043a\u0443\u0449\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Mark this commit" "\u041e\u0442\u0431\u0435\u043b\u044f\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u0442\u043e\u0432\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Return to mark" "\u0412\u0440\u044a\u0449\u0430\u043d\u0435 \u043a\u044a\u043c \u043e\u0442\u0431\u0435\u043b\u044f\u0437\u0430\u043d\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Find descendant of this and mark" "\u041e\u0442\u043a\u0440\u0438\u0432\u0430\u043d\u0435 \u0438 \u043e\u0442\u0431\u0435\u043b\u044f\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u043d\u0430\u0441\u043b\u0435\u0434\u043d\u0438\u0446\u0438\u0442\u0435"
::msgcat::mcset bg "Compare with marked commit" "\u0421\u0440\u0430\u0432\u043d\u0435\u043d\u0438\u0435 \u0441 \u043e\u0442\u0431\u0435\u043b\u044f\u0437\u0430\u043d\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Diff this -> marked commit" "\u0420\u0430\u0437\u043b\u0438\u043a\u0438 \u043c\u0435\u0436\u0434\u0443 \u0442\u043e\u0432\u0430 \u0438 \u043e\u0442\u0431\u0435\u043b\u044f\u0437\u0430\u043d\u043e\u0442\u043e"
::msgcat::mcset bg "Diff marked commit -> this" "\u0420\u0430\u0437\u043b\u0438\u043a\u0438 \u043c\u0435\u0436\u0434\u0443 \u043e\u0442\u0431\u0435\u043b\u044f\u0437\u0430\u043d\u043e\u0442\u043e \u0438 \u0442\u043e\u0432\u0430"
::msgcat::mcset bg "Revert this commit" "\u041e\u0442\u043c\u044f\u043d\u0430 \u043d\u0430 \u0442\u043e\u0432\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Check out this branch" "\u0418\u0437\u0442\u0435\u0433\u043b\u044f\u043d\u0435 \u043d\u0430 \u0442\u043e\u0437\u0438 \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Rename this branch" "\u041f\u0440\u0435\u0438\u043c\u0435\u043d\u0443\u0432\u0430\u043d\u0435 \u043d\u0430 \u0442\u043e\u0437\u0438 \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Remove this branch" "\u0418\u0437\u0442\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u0442\u043e\u0437\u0438 \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Copy branch name" "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0438\u043c\u0435\u0442\u043e \u043d\u0430 \u043a\u043b\u043e\u043d\u0430"
::msgcat::mcset bg "Highlight this too" "\u041e\u0442\u0431\u0435\u043b\u044f\u0437\u0432\u0430\u043d\u0435 \u0438 \u043d\u0430 \u0442\u043e\u0432\u0430"
::msgcat::mcset bg "Highlight this only" "\u041e\u0442\u0431\u0435\u043b\u044f\u0437\u0432\u0430\u043d\u0435 \u0441\u0430\u043c\u043e \u043d\u0430 \u0442\u043e\u0432\u0430"
::msgcat::mcset bg "External diff" "\u0412\u044a\u043d\u0448\u043d\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438"
::msgcat::mcset bg "Blame parent commit" "\u0410\u043d\u043e\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0440\u043e\u0434\u0438\u0442\u0435\u043b\u0441\u043a\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Copy path" "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043f\u044a\u0442\u044f"
::msgcat::mcset bg "Show origin of this line" "\u041f\u043e\u043a\u0430\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u0438\u0437\u0445\u043e\u0434\u0430 \u043d\u0430 \u0442\u043e\u0437\u0438 \u0440\u0435\u0434"
::msgcat::mcset bg "Run git gui blame on this line" "\u0418\u0437\u043f\u044a\u043b\u043d\u0435\u043d\u0438\u0435 \u043d\u0430 \u201egit gui blame\u201c \u0432\u044a\u0440\u0445\u0443 \u0442\u043e\u0437\u0438 \u0440\u0435\u0434"
::msgcat::mcset bg "About gitk" "\u041e\u0442\u043d\u043e\u0441\u043d\u043e gitk"
::msgcat::mcset bg "\nGitk - a commit viewer for git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nUse and redistribute under the terms of the GNU General Public License" "\nGitk \u2014 \u0432\u0438\u0437\u0443\u0430\u043b\u0438\u0437\u0430\u0446\u0438\u044f \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f\u0442\u0430 \u0432 Git\n\n\u0410\u0432\u0442\u043e\u0440\u0441\u043a\u0438 \u043f\u0440\u0430\u0432\u0430: \u00a9 2005-2016 Paul Mackerras\n\n\u0418\u0437\u043f\u043e\u043b\u0437\u0432\u0430\u0439\u0442\u0435 \u0438 \u0440\u0430\u0437\u043f\u0440\u043e\u0441\u0442\u0440\u0430\u043d\u044f\u0432\u0430\u0439\u0442\u0435 \u043f\u0440\u0438 \u0443\u0441\u043b\u043e\u0432\u0438\u044f\u0442\u0430 \u043d\u0430 \u041e\u041f\u041b \u043d\u0430 \u0413\u041d\u0423"
::msgcat::mcset bg "Close" "\u0417\u0430\u0442\u0432\u0430\u0440\u044f\u043d\u0435"
::msgcat::mcset bg "Gitk key bindings" "\u041a\u043b\u0430\u0432\u0438\u0448\u043d\u0438 \u043a\u043e\u043c\u0431\u0438\u043d\u0430\u0446\u0438\u0438"
::msgcat::mcset bg "Gitk key bindings:" "\u041a\u043b\u0430\u0432\u0438\u0448\u043d\u0438 \u043a\u043e\u043c\u0431\u0438\u043d\u0430\u0446\u0438\u0438:"
::msgcat::mcset bg "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009\u0421\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430\u0442\u0430"
::msgcat::mcset bg "<%s-W>\u0009\u0009Close window" "<%s-W>\u0009\u0009\u0417\u0430\u0442\u0432\u0430\u0440\u044f\u043d\u0435 \u043d\u0430 \u043f\u0440\u043e\u0437\u043e\u0440\u0435\u0446\u0430"
::msgcat::mcset bg "<Home>\u0009\u0009Move to first commit" "<Home>\u0009\u0009\u041a\u044a\u043c \u043f\u044a\u0440\u0432\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "<End>\u0009\u0009Move to last commit" "<End>\u0009\u0009\u041a\u044a\u043c \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "<Up>, p, k\u0009Move up one commit" "<Up>, p, k\u0009\u0415\u0434\u043d\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430\u0433\u043e\u0440\u0435"
::msgcat::mcset bg "<Down>, n, j\u0009Move down one commit" "<Down>, n, j\u0009\u0415\u0434\u043d\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430\u0434\u043e\u043b\u0443"
::msgcat::mcset bg "<Left>, z, h\u0009Go back in history list" "<Left>, z, h\u0009\u041d\u0430\u0437\u0430\u0434 \u0432 \u0438\u0441\u0442\u043e\u0440\u0438\u044f\u0442\u0430"
::msgcat::mcset bg "<Right>, x, l\u0009Go forward in history list" "<Right>, x, l\u0009\u041d\u0430\u043f\u0440\u0435\u0434 \u0432 \u0438\u0441\u0442\u043e\u0440\u0438\u044f\u0442\u0430"
::msgcat::mcset bg "<%s-n>\u0009Go to n-th parent of current commit in history list" "<%s-n>\u0009\u041a\u044a\u043c n-\u0442\u0438\u044f \u0440\u043e\u0434\u0438\u0442\u0435\u043b \u043d\u0430 \u0442\u0435\u043a\u0443\u0449\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 \u0432 \u0438\u0441\u0442\u043e\u0440\u0438\u044f\u0442\u0430"
::msgcat::mcset bg "<PageUp>\u0009Move up one page in commit list" "<PageUp>\u0009\u0421\u0442\u0440\u0430\u043d\u0438\u0446\u0430 \u043d\u0430\u0433\u043e\u0440\u0435 \u0432 \u0441\u043f\u0438\u0441\u044a\u043a\u0430 \u0441 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f\u0442\u0430"
::msgcat::mcset bg "<PageDown>\u0009Move down one page in commit list" "<PageDown>\u0009\u0421\u0442\u0440\u0430\u043d\u0438\u0446\u0430 \u043d\u0430\u0434\u043e\u043b\u0443 \u0432 \u0441\u043f\u0438\u0441\u044a\u043a\u0430 \u0441 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f\u0442\u0430"
::msgcat::mcset bg "<%s-Home>\u0009Scroll to top of commit list" "<%s-Home>\u0009\u041a\u044a\u043c \u043d\u0430\u0447\u0430\u043b\u043e\u0442\u043e \u043d\u0430 \u0441\u043f\u0438\u0441\u044a\u043a\u0430 \u0441 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f\u0442\u0430"
::msgcat::mcset bg "<%s-End>\u0009Scroll to bottom of commit list" "<%s-End>\u0009\u041a\u044a\u043c \u043a\u0440\u0430\u044f \u043d\u0430 \u0441\u043f\u0438\u0441\u044a\u043a\u0430 \u0441 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f\u0442\u0430"
::msgcat::mcset bg "<%s-Up>\u0009Scroll commit list up one line" "<%s-Up>\u0009\u0420\u0435\u0434 \u043d\u0430\u0433\u043e\u0440\u0435 \u0432 \u0441\u043f\u0438\u0441\u044a\u043a\u0430 \u0441 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f"
::msgcat::mcset bg "<%s-Down>\u0009Scroll commit list down one line" "<%s-Down>\u0009\u0420\u0435\u0434 \u043d\u0430\u0434\u043e\u043b\u0443 \u0432 \u0441\u043f\u0438\u0441\u044a\u043a\u0430 \u0441 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f"
::msgcat::mcset bg "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-PageUp>\u0009\u0421\u0442\u0440\u0430\u043d\u0438\u0446\u0430 \u043d\u0430\u0433\u043e\u0440\u0435 \u0432 \u0441\u043f\u0438\u0441\u044a\u043a\u0430 \u0441 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f"
::msgcat::mcset bg "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-PageDown>\u0009\u0421\u0442\u0440\u0430\u043d\u0438\u0446\u0430 \u043d\u0430\u0434\u043e\u043b\u0443 \u0432 \u0441\u043f\u0438\u0441\u044a\u043a\u0430 \u0441 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f"
::msgcat::mcset bg "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Shift-Up>\u0009\u0422\u044a\u0440\u0441\u0435\u043d\u0435 \u043d\u0430\u0437\u0430\u0434 (\u0432\u0438\u0437\u0443\u0430\u043b\u043d\u043e \u043d\u0430\u0433\u043e\u0440\u0435, \u0438\u0441\u0442\u043e\u0440\u0438\u0447\u0435\u0441\u043a\u0438 \u2014 \u043f\u043e\u0441\u043b\u0435\u0434\u0432\u0430\u0449\u0438)"
::msgcat::mcset bg "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Shift-Down>\u0009\u0422\u044a\u0440\u0441\u0435\u043d\u0435 \u043d\u0430\u043f\u0440\u0435\u0434 (\u0432\u0438\u0437\u0443\u0430\u043b\u043d\u043e \u043d\u0430\u0434\u043e\u043b\u0443, \u0438\u0441\u0442\u043e\u0440\u0438\u0447\u0435\u0441\u043a\u0438 \u2014 \u043f\u0440\u0435\u0434\u0445\u043e\u0436\u0434\u0430\u0449\u0438)"
::msgcat::mcset bg "<Delete>, b\u0009Scroll diff view up one page" "<Delete>, b\u0009\u0421\u0442\u0440\u0430\u043d\u0438\u0446\u0430 \u043d\u0430\u0433\u043e\u0440\u0435 \u0432 \u0438\u0437\u0433\u043b\u0435\u0434\u0430 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438"
::msgcat::mcset bg "<Backspace>\u0009Scroll diff view up one page" "<Backspace>\u0009\u0421\u0442\u0440\u0430\u043d\u0438\u0446\u0430 \u043d\u0430\u0434\u043e\u043b\u0443 \u0432 \u0438\u0437\u0433\u043b\u0435\u0434\u0430 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438"
::msgcat::mcset bg "<Space>\u0009\u0009Scroll diff view down one page" "<Space>\u0009\u0009\u0421\u0442\u0440\u0430\u043d\u0438\u0446\u0430 \u043d\u0430\u0434\u043e\u043b\u0443 \u0432 \u0438\u0437\u0433\u043b\u0435\u0434\u0430 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438"
::msgcat::mcset bg "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u000918 \u0440\u0435\u0434\u0430 \u043d\u0430\u0433\u043e\u0440\u0435 \u0432 \u0438\u0437\u0433\u043b\u0435\u0434\u0430 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438"
::msgcat::mcset bg "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u000918 \u0440\u0435\u0434\u0430 \u043d\u0430\u0434\u043e\u043b\u0443 \u0432 \u0438\u0437\u0433\u043b\u0435\u0434\u0430 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438"
::msgcat::mcset bg "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009\u0422\u044a\u0440\u0441\u0435\u043d\u0435"
::msgcat::mcset bg "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009\u041a\u044a\u043c \u0441\u043b\u0435\u0434\u0432\u0430\u0449\u0430\u0442\u0430 \u043f\u043e\u044f\u0432\u0430"
::msgcat::mcset bg "<Return>\u0009Move to next find hit" "<Return>\u0009\u041a\u044a\u043c \u0441\u043b\u0435\u0434\u0432\u0430\u0449\u0430\u0442\u0430 \u043f\u043e\u044f\u0432\u0430"
::msgcat::mcset bg "g\u0009\u0009Go to commit" "g\u0009\u0009\u041a\u044a\u043c \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "/\u0009\u0009Focus the search box" "/\u0009\u0009\u0424\u043e\u043a\u0443\u0441 \u0432\u044a\u0440\u0445\u0443 \u043f\u043e\u043b\u0435\u0442\u043e \u0437\u0430 \u0442\u044a\u0440\u0441\u0435\u043d\u0435"
::msgcat::mcset bg "?\u0009\u0009Move to previous find hit" "?\u0009\u0009\u041a\u044a\u043c \u043f\u0440\u0435\u0434\u0438\u0448\u043d\u0430\u0442\u0430 \u043f\u043e\u044f\u0432\u0430"
::msgcat::mcset bg "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009\u0421\u043b\u0435\u0434\u0432\u0430\u0449 \u0444\u0430\u0439\u043b \u0432 \u0438\u0437\u0433\u043b\u0435\u0434\u0430 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438"
::msgcat::mcset bg "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009\u0422\u044a\u0440\u0441\u0435\u043d\u0435 \u043d\u0430 \u0441\u043b\u0435\u0434\u0432\u0430\u0449\u0430\u0442\u0430 \u043f\u043e\u044f\u0432\u0430 \u0432 \u0438\u0437\u0433\u043b\u0435\u0434\u0430 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438"
::msgcat::mcset bg "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009\u0422\u044a\u0440\u0441\u0435\u043d\u0435 \u043d\u0430 \u043f\u0440\u0435\u0434\u0438\u0448\u043d\u0430\u0442\u0430 \u043f\u043e\u044f\u0432\u0430 \u0432 \u0438\u0437\u0433\u043b\u0435\u0434\u0430 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438"
::msgcat::mcset bg "<%s-KP+>\u0009Increase font size" "<%s-KP+>\u0009\u041f\u043e-\u0433\u043e\u043b\u044f\u043c \u0440\u0430\u0437\u043c\u0435\u0440 \u043d\u0430 \u0448\u0440\u0438\u0444\u0442\u0430"
::msgcat::mcset bg "<%s-plus>\u0009Increase font size" "<%s-plus>\u0009\u041f\u043e-\u0433\u043e\u043b\u044f\u043c \u0440\u0430\u0437\u043c\u0435\u0440 \u043d\u0430 \u0448\u0440\u0438\u0444\u0442\u0430"
::msgcat::mcset bg "<%s-KP->\u0009Decrease font size" "<%s-KP->\u0009\u041f\u043e-\u043c\u0430\u043b\u044a\u043a \u0440\u0430\u0437\u043c\u0435\u0440 \u043d\u0430 \u0448\u0440\u0438\u0444\u0442\u0430"
::msgcat::mcset bg "<%s-minus>\u0009Decrease font size" "<%s-minus>\u0009\u041f\u043e-\u043c\u0430\u043b\u044a\u043a \u0440\u0430\u0437\u043c\u0435\u0440 \u043d\u0430 \u0448\u0440\u0438\u0444\u0442\u0430"
::msgcat::mcset bg "<F5>\u0009\u0009Update" "<F5>\u0009\u0009\u041e\u0431\u043d\u043e\u0432\u044f\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Error creating temporary directory %s:" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0441\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0432\u0440\u0435\u043c\u0435\u043d\u043d\u0430\u0442\u0430 \u0434\u0438\u0440\u0435\u043a\u0442\u043e\u0440\u0438\u044f \u201e%s\u201c:"
::msgcat::mcset bg "Error getting \"%s\" from %s:" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u043f\u043e\u043b\u0443\u0447\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u201e%s\u201c \u043e\u0442 %s:"
::msgcat::mcset bg "command failed:" "\u043d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0438\u0437\u043f\u044a\u043b\u043d\u0435\u043d\u0438\u0435 \u043d\u0430 \u043a\u043e\u043c\u0430\u043d\u0434\u0430:"
::msgcat::mcset bg "No such commit" "\u0422\u0430\u043a\u043e\u0432\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u044f\u043c\u0430"
::msgcat::mcset bg "git gui blame: command failed:" "\u201egit gui blame\u201c: \u043d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0438\u0437\u043f\u044a\u043b\u043d\u0435\u043d\u0438\u0435 \u043d\u0430 \u043a\u043e\u043c\u0430\u043d\u0434\u0430:"
::msgcat::mcset bg "Couldn't read merge head: %s" "\u0412\u044a\u0440\u0445\u044a\u0442 \u0437\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435 \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u043f\u0440\u043e\u0447\u0435\u0442\u0435: %s"
::msgcat::mcset bg "Error reading index: %s" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u043f\u0440\u043e\u0447\u0438\u0442\u0430\u043d\u0435 \u043d\u0430 \u0438\u043d\u0434\u0435\u043a\u0441\u0430: %s"
::msgcat::mcset bg "Couldn't start git blame: %s" "\u041a\u043e\u043c\u0430\u043d\u0434\u0430\u0442\u0430 \u201egit blame\u201c \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0441\u0442\u0430\u0440\u0442\u0438\u0440\u0430: %s"
::msgcat::mcset bg "Searching" "\u0422\u044a\u0440\u0441\u0435\u043d\u0435"
::msgcat::mcset bg "Error running git blame: %s" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0438\u0437\u043f\u044a\u043b\u043d\u0435\u043d\u0438\u0435\u0442\u043e \u043d\u0430 \u201egit blame\u201c: %s"
::msgcat::mcset bg "That line comes from commit %s,  which is not in this view" "\u0422\u043e\u0437\u0438 \u0440\u0435\u0434 \u0438\u0434\u0432\u0430 \u043e\u0442 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e %s, \u043a\u043e\u0435\u0442\u043e \u043d\u0435 \u0435 \u0432 \u0438\u0437\u0433\u043b\u0435\u0434\u0430"
::msgcat::mcset bg "External diff viewer failed:" "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u0438\u0437\u043f\u044a\u043b\u043d\u0435\u043d\u0438\u0435 \u043d\u0430 \u0432\u044a\u043d\u0448\u043d\u0430\u0442\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u0430 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438:"
::msgcat::mcset bg "All files" "\u0412\u0441\u0438\u0447\u043a\u0438 \u0444\u0430\u0439\u043b\u043e\u0432\u0435"
::msgcat::mcset bg "View" "\u0418\u0437\u0433\u043b\u0435\u0434"
::msgcat::mcset bg "Gitk view definition" "\u0414\u0435\u0444\u0438\u043d\u0438\u0446\u0438\u044f \u043d\u0430 \u0438\u0437\u0433\u043b\u0435\u0434 \u0432 Gitk"
::msgcat::mcset bg "Remember this view" "\u0417\u0430\u043f\u0430\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u0442\u043e\u0437\u0438 \u0438\u0437\u0433\u043b\u0435\u0434"
::msgcat::mcset bg "References (space separated list):" "\u0423\u043a\u0430\u0437\u0430\u0442\u0435\u043b\u0438 (\u0441\u043f\u0438\u0441\u044a\u043a \u0441 \u0440\u0430\u0437\u0434\u0435\u043b\u0438\u0442\u0435\u043b \u0438\u043d\u0442\u0435\u0440\u0432\u0430\u043b):"
::msgcat::mcset bg "Branches & tags:" "\u041a\u043b\u043e\u043d\u0438 \u0438 \u0435\u0442\u0438\u043a\u0435\u0442\u0438:"
::msgcat::mcset bg "All refs" "\u0412\u0441\u0438\u0447\u043a\u0438 \u0443\u043a\u0430\u0437\u0430\u0442\u0435\u043b\u0438"
::msgcat::mcset bg "All (local) branches" "\u0412\u0441\u0438\u0447\u043a\u0438 (\u043b\u043e\u043a\u0430\u043b\u043d\u0438) \u043a\u043b\u043e\u043d\u0438"
::msgcat::mcset bg "All tags" "\u0412\u0441\u0438\u0447\u043a\u0438 \u0435\u0442\u0438\u043a\u0435\u0442\u0438"
::msgcat::mcset bg "All remote-tracking branches" "\u0412\u0441\u0438\u0447\u043a\u0438 \u0441\u043b\u0435\u0434\u044f\u0449\u0438 \u043a\u043b\u043e\u043d\u0438"
::msgcat::mcset bg "Commit Info (regular expressions):" "\u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u0437\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 (\u0440\u0435\u0433. \u0438\u0437\u0440.):"
::msgcat::mcset bg "Author:" "\u0410\u0432\u0442\u043e\u0440:"
::msgcat::mcset bg "Committer:" "\u041f\u043e\u0434\u0430\u043b:"
::msgcat::mcset bg "Commit Message:" "\u0421\u044a\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u043f\u0440\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435:"
::msgcat::mcset bg "Matches all Commit Info criteria" "\u0421\u044a\u0432\u043f\u0430\u0434\u0435\u043d\u0438\u0435 \u043f\u043e \u0432\u0441\u0438\u0447\u043a\u0438 \u0445\u0430\u0440\u0430\u043a\u0442\u0435\u0440\u0438\u0441\u0442\u0438\u043a\u0438 \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e"
::msgcat::mcset bg "Matches no Commit Info criteria" "\u041d\u0435 \u0441\u044a\u0432\u043f\u0430\u0434\u0430 \u043f\u043e \u043d\u0438\u043a\u043e\u044f \u043e\u0442 \u0445\u0430\u0440\u0430\u043a\u0442\u0435\u0440\u0438\u0441\u0442\u0438\u043a\u0438\u0442\u0435 \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e"
::msgcat::mcset bg "Changes to Files:" "\u041f\u0440\u043e\u043c\u0435\u043d\u0438 \u043f\u043e \u0444\u0430\u0439\u043b\u043e\u0432\u0435\u0442\u0435:"
::msgcat::mcset bg "Fixed String" "\u0414\u043e\u0441\u043b\u043e\u0432\u0435\u043d \u043d\u0438\u0437"
::msgcat::mcset bg "Regular Expression" "\u0420\u0435\u0433\u0443\u043b\u044f\u0440\u0435\u043d \u0438\u0437\u0440\u0430\u0437"
::msgcat::mcset bg "Search string:" "\u041d\u0438\u0437 \u0437\u0430 \u0442\u044a\u0440\u0441\u0435\u043d\u0435:"
::msgcat::mcset bg "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "\u0414\u0430\u0442\u0430 \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435 (\u201e2 weeks ago\u201c (\u043f\u0440\u0435\u0434\u0438 2 \u0441\u0435\u0434\u043c\u0438\u0446\u0438), \u201e2009-03-17 15:27:38\u201c, \u201eMarch 17, 2009 15:27:38\u201c):"
::msgcat::mcset bg "Since:" "\u041e\u0442:"
::msgcat::mcset bg "Until:" "\u0414\u043e:"
::msgcat::mcset bg "Limit and/or skip a number of revisions (positive integer):" "\u041e\u0433\u0440\u0430\u043d\u0438\u0447\u0430\u0432\u0430\u043d\u0435 \u0438/\u0438\u043b\u0438 \u043f\u0440\u0435\u0441\u043a\u0430\u0447\u0430\u043d\u0435 \u043d\u0430 \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d \u0431\u0440\u043e\u0439 \u0432\u0435\u0440\u0441\u0438\u0438 (\u043d\u0435\u043e\u0442\u0440\u0438\u0446\u0430\u0442\u0435\u043b\u043d\u043e \u0446\u044f\u043b\u043e \u0447\u0438\u0441\u043b\u043e):"
::msgcat::mcset bg "Number to show:" "\u0411\u0440\u043e\u0439 \u043f\u043e\u043a\u0430\u0437\u0430\u043d\u0438:"
::msgcat::mcset bg "Number to skip:" "\u0411\u0440\u043e\u0439 \u043f\u0440\u0435\u0441\u043a\u043e\u0447\u0435\u043d\u0438:"
::msgcat::mcset bg "Miscellaneous options:" "\u0420\u0430\u0437\u043d\u0438:"
::msgcat::mcset bg "Strictly sort by date" "\u041f\u043e\u0434\u0440\u0435\u0436\u0434\u0430\u043d\u0435 \u043f\u043e \u0434\u0430\u0442\u0430"
::msgcat::mcset bg "Mark branch sides" "\u041e\u0442\u0431\u0435\u043b\u044f\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u0441\u0442\u0440\u0430\u043d\u0438\u0442\u0435 \u043f\u043e \u043a\u043b\u043e\u043d\u0430"
::msgcat::mcset bg "Limit to first parent" "\u0421\u0430\u043c\u043e \u043f\u044a\u0440\u0432\u0438\u044f \u0440\u043e\u0434\u0438\u0442\u0435\u043b"
::msgcat::mcset bg "Simple history" "\u041e\u043f\u0440\u043e\u0441\u0442\u0435\u043d\u0430 \u0438\u0441\u0442\u043e\u0440\u0438\u044f"
::msgcat::mcset bg "Additional arguments to git log:" "\u0414\u043e\u043f\u044a\u043b\u043d\u0438\u0442\u0435\u043b\u043d\u0438 \u0430\u0440\u0433\u0443\u043c\u0435\u043d\u0442\u0438 \u043a\u044a\u043c \u201egit log\u201c:"
::msgcat::mcset bg "Enter files and directories to include, one per line:" "\u0412\u044a\u0432\u0435\u0434\u0435\u0442\u0435 \u0444\u0430\u0439\u043b\u043e\u0432\u0435\u0442\u0435 \u0438 \u0434\u0438\u0440\u0435\u043a\u0442\u043e\u0440\u0438\u0438\u0442\u0435 \u0437\u0430 \u0432\u043a\u043b\u044e\u0447\u0432\u0430\u043d\u0435, \u043f\u043e \u0435\u043b\u0435\u043c\u0435\u043d\u0442 \u043d\u0430 \u0440\u0435\u0434"
::msgcat::mcset bg "Command to generate more commits to include:" "\u041a\u043e\u043c\u0430\u043d\u0434\u0430 \u0437\u0430 \u0433\u0435\u043d\u0435\u0440\u0438\u0440\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0434\u043e\u043f\u044a\u043b\u043d\u0438\u0442\u0435\u043b\u043d\u0438 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f, \u043a\u043e\u0438\u0442\u043e \u0434\u0430 \u0441\u0435 \u0432\u043a\u043b\u044e\u0447\u0430\u0442:"
::msgcat::mcset bg "Gitk: edit view" "Gitk: \u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u0438\u0437\u0433\u043b\u0435\u0434"
::msgcat::mcset bg "-- criteria for selecting revisions" "\u2014 \u043a\u0440\u0438\u0442\u0435\u0440\u0438\u0438 \u0437\u0430 \u0438\u0437\u0431\u043e\u0440 \u043d\u0430 \u0432\u0435\u0440\u0441\u0438\u0438"
::msgcat::mcset bg "View Name" "\u0418\u043c\u0435 \u043d\u0430 \u0438\u0437\u0433\u043b\u0435\u0434"
::msgcat::mcset bg "Apply (F5)" "\u041f\u0440\u0438\u043b\u0430\u0433\u0430\u043d\u0435 (F5)"
::msgcat::mcset bg "Error in commit selection arguments:" "\u0413\u0440\u0435\u0448\u043a\u0430 \u0432 \u0430\u0440\u0433\u0443\u043c\u0435\u043d\u0442\u0438\u0442\u0435 \u0437\u0430 \u0438\u0437\u0431\u043e\u0440 \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f:"
::msgcat::mcset bg "None" "\u041d\u044f\u043c\u0430"
::msgcat::mcset bg "Descendant" "\u041d\u0430\u0441\u043b\u0435\u0434\u043d\u0438\u043a"
::msgcat::mcset bg "Not descendant" "\u041d\u0435 \u0435 \u043d\u0430\u0441\u043b\u0435\u0434\u043d\u0438\u043a"
::msgcat::mcset bg "Ancestor" "\u041f\u0440\u0435\u0434\u0448\u0435\u0441\u0442\u0432\u0435\u043d\u0438\u043a"
::msgcat::mcset bg "Not ancestor" "\u041d\u0435 \u0435 \u043f\u0440\u0435\u0434\u0448\u0435\u0441\u0442\u0432\u0435\u043d\u0438\u043a"
::msgcat::mcset bg "Local changes checked in to index but not committed" "\u041b\u043e\u043a\u0430\u043b\u043d\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438 \u0434\u043e\u0431\u0430\u0432\u0435\u043d\u0438 \u043a\u044a\u043c \u0438\u043d\u0434\u0435\u043a\u0441\u0430, \u043d\u043e \u043d\u0435\u043f\u043e\u0434\u0430\u0434\u0435\u043d\u0438"
::msgcat::mcset bg "Local uncommitted changes, not checked in to index" "\u041b\u043e\u043a\u0430\u043b\u043d\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438 \u0438\u0437\u0432\u044a\u043d \u0438\u043d\u0434\u0435\u043a\u0441\u0430"
::msgcat::mcset bg "Error starting web browser:" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0441\u0442\u0430\u0440\u0442\u0438\u0440\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0443\u0435\u0431 \u0431\u0440\u0430\u0443\u0437\u044a\u0440:"
::msgcat::mcset bg "and many more" "\u0438 \u043e\u0449\u0435 \u043c\u043d\u043e\u0433\u043e"
::msgcat::mcset bg "many" "\u043c\u043d\u043e\u0433\u043e"
::msgcat::mcset bg "Tags:" "\u0415\u0442\u0438\u043a\u0435\u0442\u0438:"
::msgcat::mcset bg "Parent" "\u0420\u043e\u0434\u0438\u0442\u0435\u043b"
::msgcat::mcset bg "Child" "\u0414\u0435\u0442\u0435"
::msgcat::mcset bg "Branch" "\u041a\u043b\u043e\u043d"
::msgcat::mcset bg "Follows" "\u0421\u043b\u0435\u0434\u0432\u0430"
::msgcat::mcset bg "Precedes" "\u041f\u0440\u0435\u0434\u0448\u0435\u0441\u0442\u0432\u0430"
::msgcat::mcset bg "Error getting diffs: %s" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u043f\u043e\u043b\u0443\u0447\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438\u0442\u0435: %s"
::msgcat::mcset bg "Goto:" "\u041a\u044a\u043c \u0440\u0435\u0434:"
::msgcat::mcset bg "Short commit ID %s is ambiguous" "\u0421\u044a\u043a\u0440\u0430\u0442\u0435\u043d\u0430\u0442\u0430 \u043a\u043e\u043d\u0442\u0440\u043e\u043b\u043d\u0430 \u0441\u0443\u043c\u0430 %s \u043d\u0435 \u0435 \u0435\u0434\u043d\u043e\u0437\u043d\u0430\u0447\u043d\u0430"
::msgcat::mcset bg "Revision %s is not known" "\u041d\u0435\u043f\u043e\u0437\u043d\u0430\u0442\u0430 \u0432\u0435\u0440\u0441\u0438\u044f %s"
::msgcat::mcset bg "Commit ID %s is not known" "\u041d\u0435\u043f\u043e\u0437\u043d\u0430\u0442\u0430 \u043a\u043e\u043d\u0442\u0440\u043e\u043b\u043d\u0430 \u0441\u0443\u043c\u0430 %s"
::msgcat::mcset bg "Revision %s is not in the current view" "\u0412\u0435\u0440\u0441\u0438\u044f %s \u043d\u0435 \u0435 \u0432 \u0442\u0435\u043a\u0443\u0449\u0438\u044f \u0438\u0437\u0433\u043b\u0435\u0434"
::msgcat::mcset bg "Date" "\u0414\u0430\u0442\u0430"
::msgcat::mcset bg "Children" "\u0414\u0435\u0446\u0430"
::msgcat::mcset bg "Reset %s branch to here" "\u0417\u0430\u043d\u0443\u043b\u044f\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d\u0430 \u201e%s\u201c \u043a\u044a\u043c \u0442\u0435\u043a\u0443\u0449\u043e\u0442\u043e \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Detached head: can't reset" "\u041d\u0435\u0441\u0432\u044a\u0440\u0437\u0430\u043d \u0432\u0440\u044a\u0445: \u043d\u0435\u0432\u044a\u0437\u043c\u043e\u0436\u043d\u043e \u0437\u0430\u043d\u0443\u043b\u044f\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Skipping merge commit " "\u041f\u0440\u043e\u043f\u0443\u0441\u043a\u0430\u043d\u0435 \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0441\u043b\u0438\u0432\u0430\u043d\u0435\u0442\u043e"
::msgcat::mcset bg "Error getting patch ID for " "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u043f\u043e\u043b\u0443\u0447\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0438\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440\u0430 \u043d\u0430 "
::msgcat::mcset bg " - stopping\n" " \u2014 \u0441\u043f\u0438\u0440\u0430\u043d\u0435\n"
::msgcat::mcset bg "Commit " "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg " is the same patch as\n       " " \u0435 \u0441\u044a\u0449\u0430\u0442\u0430 \u043a\u0440\u044a\u043f\u043a\u0430 \u043a\u0430\u0442\u043e\n       "
::msgcat::mcset bg " differs from\n       " " \u0441\u0435 \u0440\u0430\u0437\u043b\u0438\u0447\u0430\u0432\u0430 \u043e\u0442\n       "
::msgcat::mcset bg "Diff of commits:\n\n" "\u0420\u0430\u0437\u043b\u0438\u043a\u0430 \u043c\u0435\u0436\u0434\u0443 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f\u0442\u0430:\n\n"
::msgcat::mcset bg " has %s children - stopping\n" " \u0438\u043c\u0430 %s \u0434\u0435\u0446\u0430 \u2014 \u0441\u043f\u0438\u0440\u0430\u043d\u0435\n"
::msgcat::mcset bg "Error writing commit to file: %s" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0437\u0430\u043f\u0430\u0437\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u0432\u044a\u0432 \u0444\u0430\u0439\u043b: %s"
::msgcat::mcset bg "Error diffing commits: %s" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0438\u0437\u0447\u0438\u0441\u043b\u044f\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438\u0442\u0435 \u043c\u0435\u0436\u0434\u0443 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f\u0442\u0430: %s"
::msgcat::mcset bg "Top" "\u041d\u0430\u0439-\u0433\u043e\u0440\u0435"
::msgcat::mcset bg "From" "\u041e\u0442"
::msgcat::mcset bg "To" "\u0414\u043e"
::msgcat::mcset bg "Generate patch" "\u0413\u0435\u043d\u0435\u0440\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043a\u0440\u044a\u043f\u043a\u0430"
::msgcat::mcset bg "From:" "\u041e\u0442:"
::msgcat::mcset bg "To:" "\u0414\u043e:"
::msgcat::mcset bg "Reverse" "\u041e\u0431\u0440\u044a\u0449\u0430\u043d\u0435"
::msgcat::mcset bg "Output file:" "\u0417\u0430\u043f\u0430\u0437\u0432\u0430\u043d\u0435 \u0432\u044a\u0432 \u0444\u0430\u0439\u043b\u0430:"
::msgcat::mcset bg "Generate" "\u0413\u0435\u043d\u0435\u0440\u0438\u0440\u0430\u043d\u0435"
::msgcat::mcset bg "Error creating patch:" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0441\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u043a\u0440\u044a\u043f\u043a\u0430:"
::msgcat::mcset bg "ID:" "\u0418\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440:"
::msgcat::mcset bg "Tag name:" "\u0418\u043c\u0435 \u043d\u0430 \u0435\u0442\u0438\u043a\u0435\u0442:"
::msgcat::mcset bg "Tag message is optional" "\u0421\u044a\u043e\u0431\u0449\u0435\u043d\u0438\u0435\u0442\u043e \u0437\u0430 \u0435\u0442\u0438\u043a\u0435\u0442 \u0435 \u043d\u0435\u0437\u0430\u0434\u044a\u043b\u0436\u0438\u0442\u0435\u043b\u043d\u043e"
::msgcat::mcset bg "Tag message:" "\u0421\u044a\u043e\u0431\u0449\u0435\u043d\u0438\u0435 \u0437\u0430 \u0435\u0442\u0438\u043a\u0435\u0442:"
::msgcat::mcset bg "Create" "\u0421\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "No tag name specified" "\u041b\u0438\u043f\u0441\u0432\u0430 \u0438\u043c\u0435 \u043d\u0430 \u0435\u0442\u0438\u043a\u0435\u0442"
::msgcat::mcset bg "Tag \"%s\" already exists" "\u0415\u0442\u0438\u043a\u0435\u0442\u044a\u0442 \u201e%s\u201c \u0432\u0435\u0447\u0435 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430"
::msgcat::mcset bg "Error creating tag:" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0441\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0435\u0442\u0438\u043a\u0435\u0442:"
::msgcat::mcset bg "Command:" "\u041a\u043e\u043c\u0430\u043d\u0434\u0430:"
::msgcat::mcset bg "Write" "\u0417\u0430\u043f\u0430\u0437\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Error writing commit:" "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u0437\u0430\u043f\u0430\u0437\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e:"
::msgcat::mcset bg "Create branch" "\u0421\u044a\u0437\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Rename branch %s" "\u041f\u0440\u0435\u0438\u043c\u0435\u043d\u0443\u0432\u0430\u043d\u0435 \u043d\u0430 \u043a\u043b\u043e\u043d\u0430 \u201e%s\u201c"
::msgcat::mcset bg "Rename" "\u041f\u0440\u0435\u0438\u043c\u0435\u043d\u0443\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Name:" "\u0418\u043c\u0435:"
::msgcat::mcset bg "Please specify a name for the new branch" "\u0423\u043a\u0430\u0436\u0435\u0442\u0435 \u0438\u043c\u0435 \u0437\u0430 \u043d\u043e\u0432\u0438\u044f \u043a\u043b\u043e\u043d"
::msgcat::mcset bg "Branch '%s' already exists. Overwrite?" "\u041a\u043b\u043e\u043d\u044a\u0442 \u201e%s\u201c \u0432\u0435\u0447\u0435 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430. \u0414\u0430 \u0441\u0435 \u043f\u0440\u0435\u0437\u0430\u043f\u0438\u0448\u0435 \u043b\u0438?"
::msgcat::mcset bg "Please specify a new name for the branch" "\u0423\u043a\u0430\u0436\u0435\u0442\u0435 \u043d\u043e\u0432\u043e \u0438\u043c\u0435 \u0437\u0430 \u043a\u043b\u043e\u043d\u0430"
::msgcat::mcset bg "Commit %s is already included in branch %s -- really re-apply it?" "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u201e%s\u201c \u0432\u0435\u0447\u0435 \u0435 \u0432\u043a\u043b\u044e\u0447\u0435\u043d\u043e \u0432 \u043a\u043b\u043e\u043d\u0430 \u201e%s\u201c \u2014 \u0434\u0430 \u0441\u0435 \u043f\u0440\u0438\u043b\u043e\u0436\u0438 \u043b\u0438 \u043e\u0442\u043d\u043e\u0432\u043e?"
::msgcat::mcset bg "Cherry-picking" "\u041e\u0442\u0431\u0438\u0440\u0430\u043d\u0435"
::msgcat::mcset bg "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043e\u0442\u0431\u0438\u0440\u0430\u043d\u0435, \u0437\u0430\u0449\u043e\u0442\u043e \u0432\u044a\u0432 \u0444\u0430\u0439\u043b\u0430 \u201e%s\u201c \u0438\u043c\u0430 \u043b\u043e\u043a\u0430\u043b\u043d\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438.\n\u041f\u043e\u0434\u0430\u0439\u0442\u0435, \u0437\u0430\u043d\u0443\u043b\u0435\u0442\u0435 \u0438\u043b\u0438 \u0433\u0438 \u0441\u043a\u0430\u0442\u0430\u0439\u0442\u0435 \u0438 \u043f\u0440\u043e\u0431\u0432\u0430\u0439\u0442\u0435 \u043e\u0442\u043d\u043e\u0432\u043e."
::msgcat::mcset bg "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043e\u0442\u0431\u0438\u0440\u0430\u043d\u0435 \u043f\u043e\u0440\u0430\u0434\u0438 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0438 \u043f\u0440\u0438 \u0441\u043b\u0438\u0432\u0430\u043d\u0435.\n\u0418\u0441\u043a\u0430\u0442\u0435 \u043b\u0438 \u0434\u0430 \u0433\u0438 \u043a\u043e\u0440\u0438\u0433\u0438\u0440\u0430\u0442\u0435 \u0447\u0440\u0435\u0437 \u201egit citool\u201c?"
::msgcat::mcset bg "No changes committed" "\u041d\u0435 \u0441\u0430 \u043f\u043e\u0434\u0430\u0434\u0435\u043d\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438"
::msgcat::mcset bg "Commit %s is not included in branch %s -- really revert it?" "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0435\u0442\u043e \u201e%s\u201c \u043d\u0435 \u0435 \u0432\u043a\u043b\u044e\u0447\u0435\u043d\u043e \u0432 \u043a\u043b\u043e\u043d\u0430 \u201e%s\u201c. \u0414\u0430 \u0441\u0435 \u043e\u0442\u043c\u0435\u043d\u0435\u043d\u0438 \u043b\u0438?"
::msgcat::mcset bg "Reverting" "\u041e\u0442\u043c\u044f\u043d\u0430"
::msgcat::mcset bg "Revert failed because of local changes to the following files:%s Please commit, reset or stash  your changes and try again." "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u0430 \u043e\u0442\u043c\u044f\u043d\u0430, \u0437\u0430\u0449\u043e\u0442\u043e \u0432\u044a\u0432 \u0444\u0430\u0439\u043b\u0430 \u201e%s\u201c \u0438\u043c\u0430 \u043b\u043e\u043a\u0430\u043b\u043d\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438.\n\u041f\u043e\u0434\u0430\u0439\u0442\u0435, \u0437\u0430\u043d\u0443\u043b\u0435\u0442\u0435 \u0438\u043b\u0438 \u0433\u0438 \u0441\u043a\u0430\u0442\u0430\u0439\u0442\u0435 \u0438 \u043f\u0440\u043e\u0431\u0432\u0430\u0439\u0442\u0435 \u043e\u0442\u043d\u043e\u0432\u043e."
::msgcat::mcset bg "Revert failed because of merge conflict.\n Do you wish to run git citool to resolve it?" "\u041d\u0435\u0443\u0441\u043f\u0435\u0448\u043d\u043e \u043e\u0442\u043c\u044f\u043d\u0430 \u043f\u043e\u0440\u0430\u0434\u0438 \u043a\u043e\u043d\u0444\u043b\u0438\u043a\u0442\u0438 \u043f\u0440\u0438 \u0441\u043b\u0438\u0432\u0430\u043d\u0435.\n\u0418\u0441\u043a\u0430\u0442\u0435 \u043b\u0438 \u0434\u0430 \u0433\u0438 \u043a\u043e\u0440\u0438\u0433\u0438\u0440\u0430\u0442\u0435 \u0447\u0440\u0435\u0437 \u201egit citool\u201c?"
::msgcat::mcset bg "Confirm reset" "\u041f\u043e\u0442\u0432\u044a\u0440\u0436\u0434\u0430\u0432\u0430\u043d\u0435 \u043d\u0430 \u0437\u0430\u043d\u0443\u043b\u044f\u0432\u0430\u043d\u0435\u0442\u043e"
::msgcat::mcset bg "Reset branch %s to %s?" "\u0414\u0430 \u0441\u0435 \u0437\u0430\u043d\u0443\u043b\u0438 \u043b\u0438 \u043a\u043b\u043e\u043d\u044a\u0442 \u201e%s\u201c \u043a\u044a\u043c \u201e%s\u201c?"
::msgcat::mcset bg "Reset type:" "\u0412\u0438\u0434 \u0437\u0430\u043d\u0443\u043b\u044f\u0432\u0430\u043d\u0435:"
::msgcat::mcset bg "Soft: Leave working tree and index untouched" "\u0421\u043b\u0430\u0431\u043e: \u0440\u0430\u0431\u043e\u0442\u043d\u043e\u0442\u043e \u0434\u044a\u0440\u0432\u043e \u0438 \u0438\u043d\u0434\u0435\u043a\u0441\u0430 \u043e\u0441\u0442\u0430\u0432\u0430\u0442 \u0441\u044a\u0449\u0438\u0442\u0435"
::msgcat::mcset bg "Mixed: Leave working tree untouched, reset index" "\u0421\u043c\u0435\u0441\u0435\u043d\u043e: \u0440\u0430\u0431\u043e\u0442\u043d\u043e\u0442\u043e \u0434\u044a\u0440\u0432\u043e \u043e\u0441\u0442\u0430\u0432\u0430 \u0441\u044a\u0449\u043e\u0442\u043e, \u0438\u043d\u0434\u0435\u043a\u0441\u044a\u0442 \u0441\u0435 \u0437\u0430\u043d\u0443\u043b\u044f\u0432\u0430"
::msgcat::mcset bg "Hard: Reset working tree and index\n(discard ALL local changes)" "\u0421\u0438\u043b\u043d\u043e: \u0437\u0430\u043d\u0443\u043b\u044f\u0432\u0430\u043d\u0435 \u0438 \u043d\u0430 \u0440\u0430\u0431\u043e\u0442\u043d\u043e\u0442\u043e \u0434\u044a\u0440\u0432\u043e, \u0438 \u043d\u0430 \u0438\u043d\u0434\u0435\u043a\u0441\u0430\n(\u0412\u0421\u0418\u0427\u041a\u0418 \u043b\u043e\u043a\u0430\u043b\u043d\u0438 \u043f\u0440\u043e\u043c\u0435\u043d\u0438 \u0449\u0435 \u0441\u0435 \u0437\u0430\u0433\u0443\u0431\u044f\u0442 \u0431\u0435\u0437\u0432\u044a\u0437\u0432\u0440\u0430\u0442\u043d\u043e)"
::msgcat::mcset bg "Resetting" "\u0417\u0430\u043d\u0443\u043b\u044f\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "A local branch named %s exists already" "\u0412\u0435\u0447\u0435 \u0441\u044a\u0449\u0435\u0441\u0442\u0432\u0443\u0432\u0430 \u043b\u043e\u043a\u0430\u043b\u0435\u043d \u043a\u043b\u043e\u043d \u201e%s\u201c."
::msgcat::mcset bg "Checking out" "\u0418\u0437\u0442\u0435\u0433\u043b\u044f\u043d\u0435"
::msgcat::mcset bg "Cannot delete the currently checked-out branch" "\u0422\u0435\u043a\u0443\u0449\u043e \u0438\u0437\u0442\u0435\u0433\u043b\u0435\u043d\u0438\u044f\u0442 \u043a\u043b\u043e\u043d \u043d\u0435 \u043c\u043e\u0436\u0435 \u0434\u0430 \u0441\u0435 \u0438\u0437\u0442\u0440\u0438\u0435"
::msgcat::mcset bg "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "\u041f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f\u0442\u0430 \u043d\u0430 \u043a\u043b\u043e\u043d\u0430 \u201e%s\u201c \u043d\u0435 \u0441\u0430 \u043d\u0430 \u043d\u0438\u043a\u043e\u0439 \u0434\u0440\u0443\u0433 \u043a\u043b\u043e\u043d.\n\u041d\u0430\u0438\u0441\u0442\u0438\u043d\u0430 \u043b\u0438 \u0438\u0441\u043a\u0430\u0442\u0435 \u0434\u0430 \u0438\u0437\u0442\u0440\u0438\u0435\u0442\u0435 \u043a\u043b\u043e\u043d\u0430 \u201e%s\u201c?"
::msgcat::mcset bg "Tags and heads: %s" "\u0415\u0442\u0438\u043a\u0435\u0442\u0438 \u0438 \u0432\u044a\u0440\u0445\u043e\u0432\u0435: %s"
::msgcat::mcset bg "Filter" "\u0424\u0438\u043b\u0442\u0440\u0438\u0440\u0430\u043d\u0435"
::msgcat::mcset bg "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "\u0413\u0440\u0435\u0448\u043a\u0430 \u043f\u0440\u0438 \u043f\u0440\u043e\u0447\u0438\u0442\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0442\u043e\u043f\u043e\u043b\u043e\u0433\u0438\u044f\u0442\u0430 \u043d\u0430 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f\u0442\u0430. \u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f\u0442\u0430 \u0437\u0430 \u043a\u043b\u043e\u043d\u0430 \u0438 \u043f\u0440\u0435\u0434\u0448\u0435\u0441\u0442\u0432\u0430\u0449\u0438\u0442\u0435/\u0441\u043b\u0435\u0434\u0432\u0430\u0449\u0438\u0442\u0435 \u0435\u0442\u0438\u043a\u0435\u0442\u0438 \u0449\u0435 \u0435 \u043d\u0435\u043f\u044a\u043b\u043d\u0430."
::msgcat::mcset bg "Tag" "\u0415\u0442\u0438\u043a\u0435\u0442"
::msgcat::mcset bg "Id" "\u0418\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440"
::msgcat::mcset bg "Gitk font chooser" "\u0418\u0437\u0431\u043e\u0440 \u043d\u0430 \u0448\u0440\u0438\u0444\u0442 \u0437\u0430 Gitk"
::msgcat::mcset bg "B" "\u0427"
::msgcat::mcset bg "I" "\u041a"
::msgcat::mcset bg "Commit list display options" "\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u043d\u0430 \u0441\u043f\u0438\u0441\u044a\u043a\u0430 \u0441 \u043f\u043e\u0434\u0430\u0432\u0430\u043d\u0438\u044f"
::msgcat::mcset bg "Maximum graph width (lines)" "\u041c\u0430\u043a\u0441\u0438\u043c\u0430\u043b\u043d\u0430 \u0448\u0438\u0440\u043e\u0447\u0438\u043d\u0430 \u043d\u0430 \u0433\u0440\u0430\u0444\u0430 (\u0432 \u0440\u0435\u0434\u043e\u0432\u0435)"
::msgcat::mcset bg "Maximum graph width (% of pane)" "\u041c\u0430\u043a\u0441\u0438\u043c\u0430\u043b\u043d\u0430 \u0448\u0438\u0440\u043e\u0447\u0438\u043d\u0430 \u043d\u0430 \u0433\u0440\u0430\u0444\u0430 (% \u043e\u0442 \u043f\u0430\u043d\u0435\u043b\u0430)"
::msgcat::mcset bg "Show local changes" "\u041f\u043e\u043a\u0430\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u043b\u043e\u043a\u0430\u043b\u043d\u0438\u0442\u0435 \u043f\u0440\u043e\u043c\u0435\u043d\u0438"
::msgcat::mcset bg "Hide remote refs" "\u0421\u043a\u0440\u0438\u0432\u0430\u043d\u0435 \u043d\u0430 \u043e\u0442\u0434\u0430\u043b\u0435\u0447\u0435\u043d\u0438\u0442\u0435 \u0443\u043a\u0430\u0437\u0430\u0442\u0435\u043b\u0438"
::msgcat::mcset bg "Copy commit ID to clipboard" "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043a\u043e\u043d\u0442\u0440\u043e\u043b\u043d\u0430\u0442\u0430 \u0441\u0443\u043c\u0430 \u043a\u044a\u043c \u0431\u0443\u0444\u0435\u0440\u0430 \u0437\u0430 \u043e\u0431\u043c\u0435\u043d"
::msgcat::mcset bg "Copy commit ID to X11 selection" "\u041a\u043e\u043f\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 \u043a\u043e\u043d\u0442\u0440\u043e\u043b\u043d\u0430\u0442\u0430 \u0441\u0443\u043c\u0430 \u0432 \u0441\u0435\u043b\u0435\u043a\u0446\u0438\u044f\u0442\u0430 \u043d\u0430 X11"
::msgcat::mcset bg "Length of commit ID to copy" "\u0414\u044a\u043b\u0436\u0438\u043d\u0430 \u043d\u0430 \u043a\u043e\u043d\u0442\u0440\u043e\u043b\u043d\u0430\u0442\u0430 \u0441\u0443\u043c\u0430, \u043a\u043e\u044f\u0442\u043e \u0441\u0435 \u043a\u043e\u043f\u0438\u0440\u0430"
::msgcat::mcset bg "Diff display options" "\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u043d\u0430 \u043f\u043e\u043a\u0430\u0437\u0432\u0430\u043d\u0435\u0442\u043e \u043d\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438\u0442\u0435"
::msgcat::mcset bg "Tab spacing" "\u0428\u0438\u0440\u043e\u0447\u0438\u043d\u0430 \u043d\u0430 \u0442\u0430\u0431\u0443\u043b\u0430\u0442\u043e\u0440\u0430"
::msgcat::mcset bg "Wrap comment text" "\u041f\u0440\u0435\u043d\u0430\u0441\u044f\u043d\u0435 \u043d\u0430 \u0434\u0443\u043c\u0438\u0442\u0435 \u0432 \u043a\u043e\u043c\u0435\u043d\u0442\u0430\u0440\u0438\u0442\u0435"
::msgcat::mcset bg "Wrap other text" "\u041f\u0440\u0435\u043d\u0430\u0441\u044f\u043d\u0435 \u043d\u0430 \u0434\u0440\u0443\u0433\u0438\u044f \u0442\u0435\u043a\u0441\u0442"
::msgcat::mcset bg "Display nearby tags/heads" "\u0418\u0437\u0432\u0435\u0436\u0434\u0430\u043d\u0435 \u043d\u0430 \u0431\u043b\u0438\u0437\u043a\u0438\u0442\u0435 \u0435\u0442\u0438\u043a\u0435\u0442\u0438 \u0438 \u0432\u044a\u0440\u0445\u043e\u0432\u0435"
::msgcat::mcset bg "Maximum # tags/heads to show" "\u041c\u0430\u043a\u0441\u0438\u043c\u0430\u043b\u0435\u043d \u0431\u0440\u043e\u0439 \u0435\u0442\u0438\u043a\u0435\u0442\u0438/\u0432\u044a\u0440\u0445\u043e\u0432\u0435 \u0437\u0430 \u043f\u043e\u043a\u0430\u0437\u0432\u0430\u043d\u0435"
::msgcat::mcset bg "Limit diffs to listed paths" "\u0420\u0430\u0437\u043b\u0438\u043a\u0430 \u0441\u0430\u043c\u043e \u0432 \u0438\u0437\u0431\u0440\u0430\u043d\u0438\u0442\u0435 \u043f\u044a\u0442\u0438\u0449\u0430"
::msgcat::mcset bg "Support per-file encodings" "\u041f\u043e\u0434\u0434\u0440\u044a\u0436\u043a\u0430 \u043d\u0430 \u0440\u0430\u0437\u043b\u0438\u0447\u043d\u0438 \u043a\u043e\u0434\u0438\u0440\u0430\u043d\u0438\u044f \u0437\u0430 \u0432\u0441\u0435\u043a\u0438 \u0444\u0430\u0439\u043b"
::msgcat::mcset bg "External diff tool" "\u0412\u044a\u043d\u0448\u0435\u043d \u0438\u043d\u0441\u0442\u0440\u0443\u043c\u0435\u043d\u0442 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438"
::msgcat::mcset bg "Choose..." "\u0418\u0437\u0431\u043e\u0440\u2026"
::msgcat::mcset bg "Web browser" "\u0423\u0435\u0431 \u0431\u0440\u0430\u0443\u0437\u044a\u0440"
::msgcat::mcset bg "General options" "\u041e\u0431\u0449\u0438 \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438"
::msgcat::mcset bg "Use themed widgets" "\u0418\u0437\u043f\u043e\u043b\u0437\u0432\u0430\u043d\u0435 \u043d\u0430 \u0442\u0435\u043c\u0430 \u0437\u0430 \u0433\u0440\u0430\u0444\u0438\u0447\u043d\u0438\u0442\u0435 \u043e\u0431\u0435\u043a\u0442\u0438"
::msgcat::mcset bg "(change requires restart)" "(\u043f\u0440\u043e\u043c\u044f\u043d\u0430\u0442\u0430 \u0438\u0437\u0438\u0441\u043a\u0432\u0430 \u0440\u0435\u0441\u0442\u0430\u0440\u0442\u0438\u0440\u0430\u043d\u0435 \u043d\u0430 Gitk)"
::msgcat::mcset bg "(currently unavailable)" "(\u0432 \u043c\u043e\u043c\u0435\u043d\u0442\u0430 \u043d\u0435\u0434\u043e\u0441\u0442\u044a\u043f\u043d\u043e)"
::msgcat::mcset bg "Colors: press to choose" "\u0426\u0432\u0435\u0442\u043e\u0432\u0435: \u0438\u0437\u0431\u0438\u0440\u0430 \u0441\u0435 \u0441 \u043d\u0430\u0442\u0438\u0441\u043a\u0430\u043d\u0435"
::msgcat::mcset bg "Interface" "\u0418\u043d\u0442\u0435\u0440\u0444\u0435\u0439\u0441"
::msgcat::mcset bg "interface" "\u0438\u043d\u0442\u0435\u0440\u0444\u0435\u0439\u0441"
::msgcat::mcset bg "Background" "\u0424\u043e\u043d"
::msgcat::mcset bg "background" "\u0444\u043e\u043d"
::msgcat::mcset bg "Foreground" "\u0417\u043d\u0430\u0446\u0438"
::msgcat::mcset bg "foreground" "\u0437\u043d\u0430\u0446\u0438"
::msgcat::mcset bg "Diff: old lines" "\u0420\u0430\u0437\u043b\u0438\u043a\u0430: \u0441\u0442\u0430\u0440\u0438 \u0440\u0435\u0434\u043e\u0432\u0435"
::msgcat::mcset bg "diff old lines" "\u0440\u0430\u0437\u043b\u0438\u043a\u0430, \u0441\u0442\u0430\u0440\u0438 \u0440\u0435\u0434\u043e\u0432\u0435"
::msgcat::mcset bg "Diff: old lines bg" "\u0420\u0430\u0437\u043b\u0438\u043a\u0430: \u0444\u043e\u043d \u043d\u0430 \u0441\u0442\u0430\u0440\u0438 \u0440\u0435\u0434\u043e\u0432\u0435"
::msgcat::mcset bg "diff old lines bg" "\u0440\u0430\u0437\u043b\u0438\u043a\u0430, \u0444\u043e\u043d \u043d\u0430 \u0441\u0442\u0430\u0440\u0438 \u0440\u0435\u0434\u043e\u0432\u0435"
::msgcat::mcset bg "Diff: new lines" "\u0420\u0430\u0437\u043b\u0438\u043a\u0430: \u043d\u043e\u0432\u0438 \u0440\u0435\u0434\u043e\u0432\u0435"
::msgcat::mcset bg "diff new lines" "\u0440\u0430\u0437\u043b\u0438\u043a\u0430, \u043d\u043e\u0432\u0438 \u0440\u0435\u0434\u043e\u0432\u0435"
::msgcat::mcset bg "Diff: new lines bg" "\u0420\u0430\u0437\u043b\u0438\u043a\u0430: \u0444\u043e\u043d \u043d\u0430 \u043d\u043e\u0432\u0438 \u0440\u0435\u0434\u043e\u0432\u0435"
::msgcat::mcset bg "diff new lines bg" "\u0440\u0430\u0437\u043b\u0438\u043a\u0430, \u0444\u043e\u043d \u043d\u0430 \u043d\u043e\u0432\u0438 \u0440\u0435\u0434\u043e\u0432\u0435"
::msgcat::mcset bg "Diff: hunk header" "\u0420\u0430\u0437\u043b\u0438\u043a\u0430: \u043d\u0430\u0447\u0430\u043b\u043e \u043d\u0430 \u043f\u0430\u0440\u0447\u0435"
::msgcat::mcset bg "diff hunk header" "\u0440\u0430\u0437\u043b\u0438\u043a\u0430, \u043d\u0430\u0447\u0430\u043b\u043e \u043d\u0430 \u043f\u0430\u0440\u0447\u0435"
::msgcat::mcset bg "Marked line bg" "\u0424\u043e\u043d \u043d\u0430 \u043e\u0442\u0431\u0435\u043b\u044f\u0437\u0430\u043d \u0440\u0435\u0434"
::msgcat::mcset bg "marked line background" "\u0444\u043e\u043d \u043d\u0430 \u043e\u0442\u0431\u0435\u043b\u044f\u0437\u0430\u043d \u0440\u0435\u0434"
::msgcat::mcset bg "Select bg" "\u0418\u0437\u0431\u043e\u0440 \u043d\u0430 \u0444\u043e\u043d"
::msgcat::mcset bg "Fonts: press to choose" "\u0428\u0440\u0438\u0444\u0442\u043e\u0432\u0435: \u0438\u0437\u0431\u0438\u0440\u0430 \u0441\u0435 \u0441 \u043d\u0430\u0442\u0438\u0441\u043a\u0430\u043d\u0435"
::msgcat::mcset bg "Main font" "\u041e\u0441\u043d\u043e\u0432\u0435\u043d \u0448\u0440\u0438\u0444\u0442"
::msgcat::mcset bg "Diff display font" "\u0428\u0440\u0438\u0444\u0442 \u0437\u0430 \u0440\u0430\u0437\u043b\u0438\u043a\u0438\u0442\u0435"
::msgcat::mcset bg "User interface font" "\u0428\u0440\u0438\u0444\u0442 \u043d\u0430 \u0438\u043d\u0442\u0435\u0440\u0444\u0435\u0439\u0441\u0430"
::msgcat::mcset bg "Gitk preferences" "\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u043d\u0430 Gitk"
::msgcat::mcset bg "General" "\u041e\u0431\u0449\u0438"
::msgcat::mcset bg "Colors" "\u0426\u0432\u0435\u0442\u043e\u0432\u0435"
::msgcat::mcset bg "Fonts" "\u0428\u0440\u0438\u0444\u0442\u043e\u0432\u0435"
::msgcat::mcset bg "Gitk: choose color for %s" "Gitk: \u0438\u0437\u0431\u043e\u0440 \u043d\u0430 \u0446\u0432\u044f\u0442 \u043d\u0430 \u201e%s\u201c"
::msgcat::mcset bg "Sorry, gitk cannot run with this version of Tcl/Tk.\n Gitk requires at least Tcl/Tk 8.4." "\u0422\u0430\u0437\u0438 \u0432\u0435\u0440\u0441\u0438\u044f \u043d\u0430 Tcl/Tk \u043d\u0435 \u0441\u0435 \u043f\u043e\u0434\u0434\u044a\u0440\u0436\u0430 \u043e\u0442 Gitk.\n \u041d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u0430 \u0432\u0438 \u0435 \u043f\u043e\u043d\u0435 Tcl/Tk 8.4."
::msgcat::mcset bg "Cannot find a git repository here." "\u0422\u0443\u043a \u043b\u0438\u043f\u0441\u0432\u0430 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u043d\u0430 Git."
::msgcat::mcset bg "Ambiguous argument '%s': both revision and filename" "\u041d\u0435\u0435\u0434\u043d\u043e\u0437\u043d\u0430\u0447\u0435\u043d \u0430\u0440\u0433\u0443\u043c\u0435\u043d\u0442 \u201e%s\u201c: \u0438\u043c\u0430 \u0438 \u0442\u0430\u043a\u0430\u0432\u0430 \u0432\u0435\u0440\u0441\u0438\u044f, \u0438 \u0442\u0430\u043a\u044a\u0432 \u0444\u0430\u0439\u043b"
::msgcat::mcset bg "Bad arguments to gitk:" "\u041d\u0435\u043f\u0440\u0430\u0432\u0438\u043b\u043d\u0438 \u0430\u0440\u0433\u0443\u043c\u0435\u043d\u0442\u0438 \u043d\u0430 gitk:"
