.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_global_set_mem_functions" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_global_set_mem_functions \- API function
.SH SYNOPSIS
.B #include <gnutls/compat.h>
.sp
.BI "void gnutls_global_set_mem_functions(gnutls_alloc_function " alloc_func ", gnutls_alloc_function " secure_alloc_func ", gnutls_is_secure_function " is_secure_func ", gnutls_realloc_function " realloc_func ", gnutls_free_function " free_func ");"
.SH ARGUMENTS
.IP "gnutls_alloc_function alloc_func" 12
it's the default memory allocation function. Like \fBmalloc()\fP.
.IP "gnutls_alloc_function secure_alloc_func" 12
This is the memory allocation function that will be used for sensitive data.
.IP "gnutls_is_secure_function is_secure_func" 12
a function that returns 0 if the memory given is not secure. May be NULL.
.IP "gnutls_realloc_function realloc_func" 12
A realloc function
.IP "gnutls_free_function free_func" 12
The function that frees allocated data. Must accept a NULL pointer.
.SH "DEPRECATED"
since 3.3.0 it is no longer possible to replace the internally used 
memory allocation functions

This is the function where you set the memory allocation functions
gnutls is going to use. By default the libc's allocation functions
(\fBmalloc()\fP, \fBfree()\fP), are used by gnutls, to allocate both sensitive
and not sensitive data.  This function is provided to set the
memory allocation functions to something other than the defaults

This function must be called before \fBgnutls_global_init()\fP is called.
This function is not thread safe.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
