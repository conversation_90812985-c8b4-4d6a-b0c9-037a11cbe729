.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs7_verify_direct" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs7_verify_direct \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs7.h>
.sp
.BI "int gnutls_pkcs7_verify_direct(gnutls_pkcs7_t " pkcs7 ", gnutls_x509_crt_t " signer ", unsigned " idx ", const gnutls_datum_t * " data ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs7_t pkcs7" 12
should contain a \fBgnutls_pkcs7_t\fP type
.IP "gnutls_x509_crt_t signer" 12
the certificate believed to have signed the structure
.IP "unsigned idx" 12
the index of the signature info to check
.IP "const gnutls_datum_t * data" 12
The data to be verified or \fBNULL\fP
.IP "unsigned flags" 12
Zero or an OR list of \fBgnutls_certificate_verify_flags\fP
.SH "DESCRIPTION"
This function will verify the provided data against the signature
present in the SignedData of the PKCS \fB7\fP structure. If the data
provided are NULL then the data in the encapsulatedContent field
will be used instead.

Note that, unlike \fBgnutls_pkcs7_verify()\fP this function does not
verify the key purpose of the signer. It is expected for the caller
to verify the intended purpose of the \fBsigner\fP \-e.g., via \fBgnutls_x509_crt_get_key_purpose_oid()\fP,
or \fBgnutls_x509_crt_check_key_purpose()\fP.

Note also, that since GnuTLS 3.5.6 this function introduces checks in the
end certificate ( \fIsigner\fP ), including time checks and key usage checks.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value. A verification error results to a
\fBGNUTLS_E_PK_SIG_VERIFY_FAILED\fP and the lack of encapsulated data
to verify to a \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP.
.SH "SINCE"
3.4.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
