<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>ldd</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using-utils.html" title="Cygwin Utilities"><link rel="prev" href="kill.html" title="kill"><link rel="next" href="locale.html" title="locale"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">ldd</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="kill.html">Prev</a>&#160;</td><th width="60%" align="center">Cygwin Utilities</th><td width="20%" align="right">&#160;<a accesskey="n" href="locale.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="ldd"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>ldd &#8212; Print shared library dependencies</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="cmdsynopsis"><p><code class="command">ldd</code>  [-ruv]  <em class="replaceable"><code>FILE</code></em>... </p></div><div class="cmdsynopsis"><p><code class="command">ldd</code>    -h  |   -V  </p></div></div><div class="refsect1"><a name="ldd-options"></a><h2>Options</h2><pre class="screen">
  -h, --help              print this help and exit
  -V, --version           print version information and exit
  -r, --function-relocs   process data and function relocations
                          (currently unimplemented)
  -u, --unused            print unused direct dependencies
                          (currently unimplemented)
  -v, --verbose           print all information
                          (currently unimplemented)
</pre></div><div class="refsect1"><a name="ldd-desc"></a><h2>Description</h2><p><span class="command"><strong>ldd</strong></span> prints the shared libraries (DLLs) loaded
      when running an executable or DLL.</p><div class="refsect2"><a name="ldd-desc-security"></a><h3>Security</h3><p>
	<span class="command"><strong>ldd</strong></span> invokes the Windows loader on the file specified,
	then uses the Windows debugging interface to report DLLs loaded, and
	(for executables) to attempt to stop execution before the entrypoint.
	Thus, you should never use ldd on an untrusted file.
      </p></div></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="kill.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using-utils.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="locale.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">kill&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;locale</td></tr></table></div></body></html>
