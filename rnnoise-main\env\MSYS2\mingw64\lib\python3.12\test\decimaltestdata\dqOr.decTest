------------------------------------------------------------------------
-- dqOr.decTest -- digitwise logical OR for decQuads                  --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- Sanity check (truth table)
dqor001 or             0    0 ->    0
dqor002 or             0    1 ->    1
dqor003 or             1    0 ->    1
dqor004 or             1    1 ->    1
dqor005 or          1100 1010 -> 1110
-- and at msd and msd-1
dqor006 or 0000000000000000000000000000000000 0000000000000000000000000000000000 ->           0
dqor007 or 0000000000000000000000000000000000 1000000000000000000000000000000000 ->   1000000000000000000000000000000000
dqor008 or 1000000000000000000000000000000000 0000000000000000000000000000000000 ->   1000000000000000000000000000000000
dqor009 or 1000000000000000000000000000000000 1000000000000000000000000000000000 ->   1000000000000000000000000000000000
dqor010 or 0000000000000000000000000000000000 0000000000000000000000000000000000 ->           0
dqor011 or 0000000000000000000000000000000000 0100000000000000000000000000000000 ->    100000000000000000000000000000000
dqor012 or 0100000000000000000000000000000000 0000000000000000000000000000000000 ->    100000000000000000000000000000000
dqor013 or 0100000000000000000000000000000000 0100000000000000000000000000000000 ->    100000000000000000000000000000000

-- Various lengths
dqor601 or 0111111111111111111111111111111111 1111111111111111111111111111111110  -> 1111111111111111111111111111111111
dqor602 or 1011111111111111111111111111111111 1111111111111111111111111111111101  -> 1111111111111111111111111111111111
dqor603 or 1101111111111111111111111111111111 1111111111111111111111111111111011  -> 1111111111111111111111111111111111
dqor604 or 1110111111111111111111111111111111 1111111111111111111111111111110111  -> 1111111111111111111111111111111111
dqor605 or 1111011111111111111111111111111111 1111111111111111111111111111101111  -> 1111111111111111111111111111111111
dqor606 or 1111101111111111111111111111111111 1111111111111111111111111111011111  -> 1111111111111111111111111111111111
dqor607 or 1111110111111111111111111111111111 1111111111111111111111111110111111  -> 1111111111111111111111111111111111
dqor608 or 1111111011111111111111111111111111 1111111111111111111111111101111111  -> 1111111111111111111111111111111111
dqor609 or 1111111101111111111111111111111111 1111111111111111111111111011111111  -> 1111111111111111111111111111111111
dqor610 or 1111111110111111111111111111111111 1111111111111111111111110111111111  -> 1111111111111111111111111111111111
dqor611 or 1111111111011111111111111111111111 1111111111111111111111101111111111  -> 1111111111111111111111111111111111
dqor612 or 1111111111101111111111111111111111 1111111111111111111111011111111111  -> 1111111111111111111111111111111111
dqor613 or 1111111111110111111111111111111111 1111111111111111111110111111111111  -> 1111111111111111111111111111111111
dqor614 or 1111111111111011111111111111111111 1111111111111111111101111111111111  -> 1111111111111111111111111111111111
dqor615 or 1111111111111101111111111111111111 1111111111111111111011111111111111  -> 1111111111111111111111111111111111
dqor616 or 1111111111111110111111111111111111 1111111111111111110111111111111111  -> 1111111111111111111111111111111111
dqor617 or 1111111111111111011111111111111111 1111111111111111101111111111111111  -> 1111111111111111111111111111111111
dqor618 or 1111111111111111101111111111111111 1111111111111111011111111111111111  -> 1111111111111111111111111111111111
dqor619 or 1111111111111111110111111111111111 1111111111111110111111111111111111  -> 1111111111111111111111111111111111
dqor620 or 1111111111111111111011111111111111 1111111111111101111111111111111111  -> 1111111111111111111111111111111111
dqor621 or 1111111111111111111101111111111111 1111111111111011111111111111111111  -> 1111111111111111111111111111111111
dqor622 or 1111111111111111111110111111111111 1111111111110111111111111111111111  -> 1111111111111111111111111111111111
dqor623 or 1111111111111111111111011111111111 1111111111101111111111111111111111  -> 1111111111111111111111111111111111
dqor624 or 1111111111111111111111101111111111 1111111111011111111111111111111111  -> 1111111111111111111111111111111111
dqor625 or 1111111111111111111111110111111111 1111111110111111111111111111111111  -> 1111111111111111111111111111111111
dqor626 or 1111111111111111111111111011111111 1111111101111111111111111111111111  -> 1111111111111111111111111111111111
dqor627 or 1111111111111111111111111101111111 1111111011111111111111111111111111  -> 1111111111111111111111111111111111
dqor628 or 1111111111111111111111111110111111 1111110111111111111111111111111111  -> 1111111111111111111111111111111111
dqor629 or 1111111111111111111111111111011111 1111101111111111111111111111111111  -> 1111111111111111111111111111111111
dqor630 or 1111111111111111111111111111101111 1111011111111111111111111111111111  -> 1111111111111111111111111111111111
dqor631 or 1111111111111111111111111111110111 1110111111111111111111111111111111  -> 1111111111111111111111111111111111
dqor632 or 1111111111111111111111111111111011 1101111111111111111111111111111111  -> 1111111111111111111111111111111111
dqor633 or 1111111111111111111111111111111101 1011111111111111111111111111111111  -> 1111111111111111111111111111111111
dqor634 or 1111111111111111111111111111111110 0111111111111111111111111111111111  -> 1111111111111111111111111111111111

dqor641 or 1111111111111111111111111111111110 0111111111111111111111111111111111  -> 1111111111111111111111111111111111
dqor642 or 1111111111111111111111111111111101 1011111111111111111111111111111111  -> 1111111111111111111111111111111111
dqor643 or 1111111111111111111111111111111011 1101111111111111111111111111111111  -> 1111111111111111111111111111111111
dqor644 or 1111111111111111111111111111110111 1110111111111111111111111111111111  -> 1111111111111111111111111111111111
dqor645 or 1111111111111111111111111111101111 1111011111111111111111111111111111  -> 1111111111111111111111111111111111
dqor646 or 1111111111111111111111111111011111 1111101111111111111111111111111111  -> 1111111111111111111111111111111111
dqor647 or 1111111111111111111111111110111111 1111110111111111111111111111111111  -> 1111111111111111111111111111111111
dqor648 or 1111111111111111111111111101111111 1111111011111111111111111111111111  -> 1111111111111111111111111111111111
dqor649 or 1111111111111111111111111011111111 1111111101111111111111111111111111  -> 1111111111111111111111111111111111
dqor650 or 1111111111111111111111110111111111 1111111110111111111111111111111111  -> 1111111111111111111111111111111111
dqor651 or 1111111111111111111111101111111111 1111111111011111111111111111111111  -> 1111111111111111111111111111111111
dqor652 or 1111111111111111111111011111111111 1111111111101111111111111111111111  -> 1111111111111111111111111111111111
dqor653 or 1111111111111111111110111111111111 1111111111110111111111111111111111  -> 1111111111111111111111111111111111
dqor654 or 1111111111111111111101111111111111 1111111111111011111111111111111111  -> 1111111111111111111111111111111111
dqor655 or 1111111111111111111011111111111111 1111111111111101111111111111111111  -> 1111111111111111111111111111111111
dqor656 or 1111111111111111110111111111111111 1111111111111110111111111111111111  -> 1111111111111111111111111111111111
dqor657 or 1010101010101010101010101010101010 1010101010101010001010101010101010  -> 1010101010101010101010101010101010
dqor658 or 1111111111111111011111111111111111 1111111111111111101111111111111111  -> 1111111111111111111111111111111111
dqor659 or 1111111111111110111111111111111111 1111111111111111110111111111111111  -> 1111111111111111111111111111111111
dqor660 or 1111111111111101111111111111111111 1111111111111111111011111111111111  -> 1111111111111111111111111111111111
dqor661 or 1111111111111011111111111111111111 1111111111111111111101111111111111  -> 1111111111111111111111111111111111
dqor662 or 1111111111110111111111111111111111 1111111111111111111110111111111111  -> 1111111111111111111111111111111111
dqor663 or 1111111111101111111111111111111111 1111111111111111111111011111111111  -> 1111111111111111111111111111111111
dqor664 or 1111111111011111111111111111111111 1111111111111111111111101111111111  -> 1111111111111111111111111111111111
dqor665 or 1111111110111111111111111111111111 1111111111111111111111110111111111  -> 1111111111111111111111111111111111
dqor666 or 0101010101010101010101010101010101 0101010101010101010101010001010101  ->  101010101010101010101010101010101
dqor667 or 1111111011111111111111111111111111 1111111111111111111111111101111111  -> 1111111111111111111111111111111111
dqor668 or 1111110111111111111111111111111111 1111111111111111111111111110111111  -> 1111111111111111111111111111111111
dqor669 or 1111101111111111111111111111111111 1111111111111111111111111111011111  -> 1111111111111111111111111111111111
dqor670 or 1111011111111111111111111111111111 1111111111111111111111111111101111  -> 1111111111111111111111111111111111
dqor671 or 1110111111111111111111111111111111 1111111111111111111111111111110111  -> 1111111111111111111111111111111111
dqor672 or 1101111111111111111111111111111111 1111111111111111111111111111111011  -> 1111111111111111111111111111111111
dqor673 or 1011111111111111111111111111111111 1111111111111111111111111111111101  -> 1111111111111111111111111111111111
dqor674 or 0111111111111111111111111111111111 1111111111111111111111111111111110  -> 1111111111111111111111111111111111
dqor675 or 0111111111111111111111111111111110 1111111111111111111111111111111110  -> 1111111111111111111111111111111110
dqor676 or 1111111111111111111111111111111110 1111111111111111111111111111111110  -> 1111111111111111111111111111111110

dqor681 or 0111111111111111111111111111111111 0111111111011111111111111111111110  ->  111111111111111111111111111111111
dqor682 or 1011111111111111111111111111111111 1011111110101111111111111111111101  -> 1011111111111111111111111111111111
dqor683 or 1101111111111111111111111111111111 1101111101110111111111111111111011  -> 1101111111111111111111111111111111
dqor684 or 1110111111111111111111111111111111 1110111011111011111111111111110111  -> 1110111111111111111111111111111111
dqor685 or 1111011111111111111111111111111111 1111010111111101111111111111101111  -> 1111011111111111111111111111111111
dqor686 or 1111101111111111111111111111111111 1111101111111110111111111111011111  -> 1111101111111111111111111111111111
dqor687 or 1111110111111111111111111111111111 1111010111111111011111111110111111  -> 1111110111111111111111111111111111
dqor688 or 1111111011111111111111111111111111 1110111011111111101111111101111111  -> 1111111011111111111111111111111111
dqor689 or 1111111101111111111111111111111111 1101111101111111110111111011111111  -> 1111111101111111111111111111111111
dqor690 or 1111111110111111111111111111111111 1011111110111111111011110111111110  -> 1111111110111111111111111111111111
dqor691 or 1111111111011111111111111111111111 0111111111011111111101101111111101  -> 1111111111011111111111111111111111
dqor692 or 1111111111101111111111111111111111 1111111111101111111110011111111011  -> 1111111111101111111111111111111111
dqor693 or 1111111111110111111111111111111111 1111111111110111111110011111110111  -> 1111111111110111111111111111111111
dqor694 or 1111111111111011111111111111111111 1111111111111011111101101111101111  -> 1111111111111011111111111111111111
dqor695 or 1111111111111101111111111111111111 1111111111111101111011110111011111  -> 1111111111111101111111111111111111
dqor696 or 1111111111111110111111111111111111 1111111111111110110111111010111111  -> 1111111111111110111111111111111111
dqor697 or 1111111111111111011111111111111111 1111111111111111001111111101111111  -> 1111111111111111011111111111111111
dqor698 or 1111111111111111101111111111111111 1111111111111111001111111010111111  -> 1111111111111111101111111111111111
dqor699 or 1111111111111111110111111111111111 1111111111111110110111110111011111  -> 1111111111111111110111111111111111
dqor700 or 1111111111111111111011111111111111 1111111111111101111011101111101111  -> 1111111111111111111011111111111111
dqor701 or 1111111111111111111101111111111111 1111111111111011111101011111110111  -> 1111111111111111111101111111111111
dqor702 or 1111111111111111111110111111111111 1111111111110111111110111111111011  -> 1111111111111111111110111111111111
dqor703 or 1111111111111111111111011111111111 1111111111101111111101011111111101  -> 1111111111111111111111011111111111
dqor704 or 1111111111111111111111101111111111 1111111111011111111011101111111110  -> 1111111111111111111111101111111111
dqor705 or 1111111111111111111111110111111111 0111111110111111110111110111111111  -> 1111111111111111111111110111111111
dqor706 or 1111111111111111111111111011111111 1011111101111111101111111011111111  -> 1111111111111111111111111011111111
dqor707 or 1111111111111111111111111101111111 1101111011111111011111111101111111  -> 1111111111111111111111111101111111
dqor708 or 1111111111111111111111111110111111 1110110111111110111111111110111111  -> 1111111111111111111111111110111111
dqor709 or 1111111111111111111111111111011111 1111001111111101111111111111011111  -> 1111111111111111111111111111011111
dqor710 or 1111111111111111111111111111101111 1111001111111011111111111111101111  -> 1111111111111111111111111111101111
dqor711 or 1111111111111111111111111111110111 1110110111110111111111111111110111  -> 1111111111111111111111111111110111
dqor712 or 1111111111111111111111111111111011 1101111011101111111111111111111011  -> 1111111111111111111111111111111011
dqor713 or 1111111111111111111111111111111101 1011111101011111111111111111111101  -> 1111111111111111111111111111111101
dqor714 or 1111111111111111111111111111111110 0111111110111111111111111111111110  -> 1111111111111111111111111111111110



--         1234567890123456     1234567890123456 1234567890123456
dqor020 or 1111111111111111     1111111111111111  ->  1111111111111111
dqor021 or  111111111111111      111111111111111  ->   111111111111111
dqor022 or   11111111111111       11111111111111  ->    11111111111111
dqor023 or    1111111111111        1111111111111  ->     1111111111111
dqor024 or     111111111111         111111111111  ->      111111111111
dqor025 or      11111111111          11111111111  ->       11111111111
dqor026 or       1111111111           1111111111  ->        1111111111
dqor027 or        111111111            111111111  ->         111111111
dqor028 or         11111111             11111111  ->          11111111
dqor029 or          1111111              1111111  ->           1111111
dqor030 or           111111               111111  ->            111111
dqor031 or            11111                11111  ->             11111
dqor032 or             1111                 1111  ->              1111
dqor033 or              111                  111  ->               111
dqor034 or               11                   11  ->                11
dqor035 or                1                    1  ->                 1
dqor036 or                0                    0  ->                 0

dqor042 or  111111110000000     1111111110000000  ->  1111111110000000
dqor043 or   11111110000000     1000000100000000  ->  1011111110000000
dqor044 or    1111110000000     1000001000000000  ->  1001111110000000
dqor045 or     111110000000     1000010000000000  ->  1000111110000000
dqor046 or      11110000000     1000100000000000  ->  1000111110000000
dqor047 or       1110000000     1001000000000000  ->  1001001110000000
dqor048 or        110000000     1010000000000000  ->  1010000110000000
dqor049 or         10000000     1100000000000000  ->  1100000010000000

dqor090 or 011111111  111101111  ->  111111111
dqor091 or 101111111  111101111  ->  111111111
dqor092 or 110111111  111101111  ->  111111111
dqor093 or 111011111  111101111  ->  111111111
dqor094 or 111101111  111101111  ->  111101111
dqor095 or 111110111  111101111  ->  111111111
dqor096 or 111111011  111101111  ->  111111111
dqor097 or 111111101  111101111  ->  111111111
dqor098 or 111111110  111101111  ->  111111111

dqor100 or 111101111  011111111  ->  111111111
dqor101 or 111101111  101111111  ->  111111111
dqor102 or 111101111  110111111  ->  111111111
dqor103 or 111101111  111011111  ->  111111111
dqor104 or 111101111  111101111  ->  111101111
dqor105 or 111101111  111110111  ->  111111111
dqor106 or 111101111  111111011  ->  111111111
dqor107 or 111101111  111111101  ->  111111111
dqor108 or 111101111  111111110  ->  111111111

-- non-0/1 should not be accepted, nor should signs
dqor220 or 111111112  111111111  ->  NaN Invalid_operation
dqor221 or 333333333  333333333  ->  NaN Invalid_operation
dqor222 or 555555555  555555555  ->  NaN Invalid_operation
dqor223 or 777777777  777777777  ->  NaN Invalid_operation
dqor224 or 999999999  999999999  ->  NaN Invalid_operation
dqor225 or 222222222  999999999  ->  NaN Invalid_operation
dqor226 or 444444444  999999999  ->  NaN Invalid_operation
dqor227 or 666666666  999999999  ->  NaN Invalid_operation
dqor228 or 888888888  999999999  ->  NaN Invalid_operation
dqor229 or 999999999  222222222  ->  NaN Invalid_operation
dqor230 or 999999999  444444444  ->  NaN Invalid_operation
dqor231 or 999999999  666666666  ->  NaN Invalid_operation
dqor232 or 999999999  888888888  ->  NaN Invalid_operation
-- a few randoms
dqor240 or  567468689 -934981942 ->  NaN Invalid_operation
dqor241 or  567367689  934981942 ->  NaN Invalid_operation
dqor242 or -631917772 -706014634 ->  NaN Invalid_operation
dqor243 or -756253257  138579234 ->  NaN Invalid_operation
dqor244 or  835590149  567435400 ->  NaN Invalid_operation
-- test MSD
dqor250 or  2000000111000111000111000000000000 1000000111000111000111000000000000 ->  NaN Invalid_operation
dqor251 or  7000000111000111000111000000000000 1000000111000111000111000000000000 ->  NaN Invalid_operation
dqor252 or  8000000111000111000111000000000000 1000000111000111000111000000000000 ->  NaN Invalid_operation
dqor253 or  9000000111000111000111000000000000 1000000111000111000111000000000000 ->  NaN Invalid_operation
dqor254 or  2000000111000111000111000000000000 0000000111000111000111000000000000 ->  NaN Invalid_operation
dqor255 or  7000000111000111000111000000000000 0000000111000111000111000000000000 ->  NaN Invalid_operation
dqor256 or  8000000111000111000111000000000000 0000000111000111000111000000000000 ->  NaN Invalid_operation
dqor257 or  9000000111000111000111000000000000 0000000111000111000111000000000000 ->  NaN Invalid_operation
dqor258 or  1000000111000111000111000000000000 2000000111000111000111000000000000 ->  NaN Invalid_operation
dqor259 or  1000000111000111000111000000000000 7000000111000111000111000000000000 ->  NaN Invalid_operation
dqor260 or  1000000111000111000111000000000000 8000000111000111000111000000000000 ->  NaN Invalid_operation
dqor261 or  1000000111000111000111000000000000 9000000111000111000111000000000000 ->  NaN Invalid_operation
dqor262 or  0000000111000111000111000000000000 2000000111000111000111000000000000 ->  NaN Invalid_operation
dqor263 or  0000000111000111000111000000000000 7000000111000111000111000000000000 ->  NaN Invalid_operation
dqor264 or  0000000111000111000111000000000000 8000000111000111000111000000000000 ->  NaN Invalid_operation
dqor265 or  0000000111000111000111000000000000 9000000111000111000111000000000000 ->  NaN Invalid_operation
-- test MSD-1
dqor270 or  0200000111000111000111001000000000 1000000111000111000111100000000010 ->  NaN Invalid_operation
dqor271 or  0700000111000111000111000100000000 1000000111000111000111010000000100 ->  NaN Invalid_operation
dqor272 or  0800000111000111000111000010000000 1000000111000111000111001000001000 ->  NaN Invalid_operation
dqor273 or  0900000111000111000111000001000000 1000000111000111000111000100010000 ->  NaN Invalid_operation
dqor274 or  1000000111000111000111000000100000 0200000111000111000111000010100000 ->  NaN Invalid_operation
dqor275 or  1000000111000111000111000000010000 0700000111000111000111000001000000 ->  NaN Invalid_operation
dqor276 or  1000000111000111000111000000001000 0800000111000111000111000010100000 ->  NaN Invalid_operation
dqor277 or  1000000111000111000111000000000100 0900000111000111000111000000010000 ->  NaN Invalid_operation
-- test LSD
dqor280 or  0010000111000111000111000000000002 1000000111000111000111000100000001 ->  NaN Invalid_operation
dqor281 or  0001000111000111000111000000000007 1000000111000111000111001000000011 ->  NaN Invalid_operation
dqor282 or  0000000111000111000111100000000008 1000000111000111000111010000000001 ->  NaN Invalid_operation
dqor283 or  0000000111000111000111010000000009 1000000111000111000111100000000001 ->  NaN Invalid_operation
dqor284 or  1000000111000111000111001000000000 0001000111000111000111000000000002 ->  NaN Invalid_operation
dqor285 or  1000000111000111000111000100000000 0010000111000111000111000000000007 ->  NaN Invalid_operation
dqor286 or  1000000111000111000111000010000000 0100000111000111000111000000000008 ->  NaN Invalid_operation
dqor287 or  1000000111000111000111000001000000 1000000111000111000111000000000009 ->  NaN Invalid_operation
-- test Middie
dqor288 or  0010000111000111000111000020000000 1000000111000111000111001000000000 ->  NaN Invalid_operation
dqor289 or  0001000111000111000111000070000001 1000000111000111000111000100000000 ->  NaN Invalid_operation
dqor290 or  0000000111000111000111100080000010 1000000111000111000111000010000000 ->  NaN Invalid_operation
dqor291 or  0000000111000111000111010090000100 1000000111000111000111000001000000 ->  NaN Invalid_operation
dqor292 or  1000000111000111000111001000001000 0000000111000111000111000020100000 ->  NaN Invalid_operation
dqor293 or  1000000111000111000111000100010000 0000000111000111000111000070010000 ->  NaN Invalid_operation
dqor294 or  1000000111000111000111000010100000 0000000111000111000111000080001000 ->  NaN Invalid_operation
dqor295 or  1000000111000111000111000001000000 0000000111000111000111000090000100 ->  NaN Invalid_operation
-- signs
dqor296 or -1000000111000111000111000001000000 -0000001110001110001110010000000100 ->  NaN Invalid_operation
dqor297 or -1000000111000111000111000001000000  0000001110001110001110000010000100 ->  NaN Invalid_operation
dqor298 or  1000000111000111000111000001000000 -0000001110001110001110001000000100 ->  NaN Invalid_operation
dqor299 or  1000000111000111000111000001000000  0000001110001110001110000011000100 ->  1000001111001111001111000011000100

-- Nmax, Nmin, Ntiny-like
dqor331 or  2   9.99999999E+1999    -> NaN Invalid_operation
dqor332 or  3   1E-1999             -> NaN Invalid_operation
dqor333 or  4   1.00000000E-1999    -> NaN Invalid_operation
dqor334 or  5   1E-1009             -> NaN Invalid_operation
dqor335 or  6   -1E-1009            -> NaN Invalid_operation
dqor336 or  7   -1.00000000E-1999   -> NaN Invalid_operation
dqor337 or  8   -1E-1999            -> NaN Invalid_operation
dqor338 or  9   -9.99999999E+1999   -> NaN Invalid_operation
dqor341 or  9.99999999E+2999    -18 -> NaN Invalid_operation
dqor342 or  1E-2999              01 -> NaN Invalid_operation
dqor343 or  1.00000000E-2999    -18 -> NaN Invalid_operation
dqor344 or  1E-1009              18 -> NaN Invalid_operation
dqor345 or  -1E-1009            -10 -> NaN Invalid_operation
dqor346 or  -1.00000000E-2999    18 -> NaN Invalid_operation
dqor347 or  -1E-2999             10 -> NaN Invalid_operation
dqor348 or  -9.99999999E+2999   -18 -> NaN Invalid_operation

-- A few other non-integers
dqor361 or  1.0                  1  -> NaN Invalid_operation
dqor362 or  1E+1                 1  -> NaN Invalid_operation
dqor363 or  0.0                  1  -> NaN Invalid_operation
dqor364 or  0E+1                 1  -> NaN Invalid_operation
dqor365 or  9.9                  1  -> NaN Invalid_operation
dqor366 or  9E+1                 1  -> NaN Invalid_operation
dqor371 or  0 1.0                   -> NaN Invalid_operation
dqor372 or  0 1E+1                  -> NaN Invalid_operation
dqor373 or  0 0.0                   -> NaN Invalid_operation
dqor374 or  0 0E+1                  -> NaN Invalid_operation
dqor375 or  0 9.9                   -> NaN Invalid_operation
dqor376 or  0 9E+1                  -> NaN Invalid_operation

-- All Specials are in error
dqor780 or -Inf  -Inf   -> NaN Invalid_operation
dqor781 or -Inf  -1000  -> NaN Invalid_operation
dqor782 or -Inf  -1     -> NaN Invalid_operation
dqor783 or -Inf  -0     -> NaN Invalid_operation
dqor784 or -Inf   0     -> NaN Invalid_operation
dqor785 or -Inf   1     -> NaN Invalid_operation
dqor786 or -Inf   1000  -> NaN Invalid_operation
dqor787 or -1000 -Inf   -> NaN Invalid_operation
dqor788 or -Inf  -Inf   -> NaN Invalid_operation
dqor789 or -1    -Inf   -> NaN Invalid_operation
dqor790 or -0    -Inf   -> NaN Invalid_operation
dqor791 or  0    -Inf   -> NaN Invalid_operation
dqor792 or  1    -Inf   -> NaN Invalid_operation
dqor793 or  1000 -Inf   -> NaN Invalid_operation
dqor794 or  Inf  -Inf   -> NaN Invalid_operation

dqor800 or  Inf  -Inf   -> NaN Invalid_operation
dqor801 or  Inf  -1000  -> NaN Invalid_operation
dqor802 or  Inf  -1     -> NaN Invalid_operation
dqor803 or  Inf  -0     -> NaN Invalid_operation
dqor804 or  Inf   0     -> NaN Invalid_operation
dqor805 or  Inf   1     -> NaN Invalid_operation
dqor806 or  Inf   1000  -> NaN Invalid_operation
dqor807 or  Inf   Inf   -> NaN Invalid_operation
dqor808 or -1000  Inf   -> NaN Invalid_operation
dqor809 or -Inf   Inf   -> NaN Invalid_operation
dqor810 or -1     Inf   -> NaN Invalid_operation
dqor811 or -0     Inf   -> NaN Invalid_operation
dqor812 or  0     Inf   -> NaN Invalid_operation
dqor813 or  1     Inf   -> NaN Invalid_operation
dqor814 or  1000  Inf   -> NaN Invalid_operation
dqor815 or  Inf   Inf   -> NaN Invalid_operation

dqor821 or  NaN -Inf    -> NaN Invalid_operation
dqor822 or  NaN -1000   -> NaN Invalid_operation
dqor823 or  NaN -1      -> NaN Invalid_operation
dqor824 or  NaN -0      -> NaN Invalid_operation
dqor825 or  NaN  0      -> NaN Invalid_operation
dqor826 or  NaN  1      -> NaN Invalid_operation
dqor827 or  NaN  1000   -> NaN Invalid_operation
dqor828 or  NaN  Inf    -> NaN Invalid_operation
dqor829 or  NaN  NaN    -> NaN Invalid_operation
dqor830 or -Inf  NaN    -> NaN Invalid_operation
dqor831 or -1000 NaN    -> NaN Invalid_operation
dqor832 or -1    NaN    -> NaN Invalid_operation
dqor833 or -0    NaN    -> NaN Invalid_operation
dqor834 or  0    NaN    -> NaN Invalid_operation
dqor835 or  1    NaN    -> NaN Invalid_operation
dqor836 or  1000 NaN    -> NaN Invalid_operation
dqor837 or  Inf  NaN    -> NaN Invalid_operation

dqor841 or  sNaN -Inf   ->  NaN  Invalid_operation
dqor842 or  sNaN -1000  ->  NaN  Invalid_operation
dqor843 or  sNaN -1     ->  NaN  Invalid_operation
dqor844 or  sNaN -0     ->  NaN  Invalid_operation
dqor845 or  sNaN  0     ->  NaN  Invalid_operation
dqor846 or  sNaN  1     ->  NaN  Invalid_operation
dqor847 or  sNaN  1000  ->  NaN  Invalid_operation
dqor848 or  sNaN  NaN   ->  NaN  Invalid_operation
dqor849 or  sNaN sNaN   ->  NaN  Invalid_operation
dqor850 or  NaN  sNaN   ->  NaN  Invalid_operation
dqor851 or -Inf  sNaN   ->  NaN  Invalid_operation
dqor852 or -1000 sNaN   ->  NaN  Invalid_operation
dqor853 or -1    sNaN   ->  NaN  Invalid_operation
dqor854 or -0    sNaN   ->  NaN  Invalid_operation
dqor855 or  0    sNaN   ->  NaN  Invalid_operation
dqor856 or  1    sNaN   ->  NaN  Invalid_operation
dqor857 or  1000 sNaN   ->  NaN  Invalid_operation
dqor858 or  Inf  sNaN   ->  NaN  Invalid_operation
dqor859 or  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
dqor861 or  NaN1   -Inf    -> NaN Invalid_operation
dqor862 or +NaN2   -1000   -> NaN Invalid_operation
dqor863 or  NaN3    1000   -> NaN Invalid_operation
dqor864 or  NaN4    Inf    -> NaN Invalid_operation
dqor865 or  NaN5   +NaN6   -> NaN Invalid_operation
dqor866 or -Inf     NaN7   -> NaN Invalid_operation
dqor867 or -1000    NaN8   -> NaN Invalid_operation
dqor868 or  1000    NaN9   -> NaN Invalid_operation
dqor869 or  Inf    +NaN10  -> NaN Invalid_operation
dqor871 or  sNaN11  -Inf   -> NaN Invalid_operation
dqor872 or  sNaN12  -1000  -> NaN Invalid_operation
dqor873 or  sNaN13   1000  -> NaN Invalid_operation
dqor874 or  sNaN14   NaN17 -> NaN Invalid_operation
dqor875 or  sNaN15  sNaN18 -> NaN Invalid_operation
dqor876 or  NaN16   sNaN19 -> NaN Invalid_operation
dqor877 or -Inf    +sNaN20 -> NaN Invalid_operation
dqor878 or -1000    sNaN21 -> NaN Invalid_operation
dqor879 or  1000    sNaN22 -> NaN Invalid_operation
dqor880 or  Inf     sNaN23 -> NaN Invalid_operation
dqor881 or +NaN25  +sNaN24 -> NaN Invalid_operation
dqor882 or -NaN26    NaN28 -> NaN Invalid_operation
dqor883 or -sNaN27  sNaN29 -> NaN Invalid_operation
dqor884 or  1000    -NaN30 -> NaN Invalid_operation
dqor885 or  1000   -sNaN31 -> NaN Invalid_operation
