.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_psk_server_get_username2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_psk_server_get_username2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_psk_server_get_username2(gnutls_session_t " session ", gnutls_datum_t * " username ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.IP "gnutls_datum_t * username" 12
a datum that will be filled in by this function
.SH "DESCRIPTION"
Return a pointer to the username of the peer in the supplied datum. Does not
need to be null\-terminated.

This should only be called in case of PSK authentication and in
case of a server.

The returned pointer should be considered constant (do not free) and valid 
for the lifetime of the session.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP, or a negative value in case of an error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
