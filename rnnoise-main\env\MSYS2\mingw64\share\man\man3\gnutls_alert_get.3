.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_alert_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_alert_get \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_alert_description_t gnutls_alert_get(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
This function will return the last alert number received.  This
function should be called when \fBGNUTLS_E_WARNING_ALERT_RECEIVED\fP or
\fBGNUTLS_E_FATAL_ALERT_RECEIVED\fP errors are returned by a gnutls
function.  The peer may send alerts if he encounters an error.
If no alert has been received the returned value is undefined.
.SH "RETURNS"
the last alert received, a
\fBgnutls_alert_description_t\fP value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
