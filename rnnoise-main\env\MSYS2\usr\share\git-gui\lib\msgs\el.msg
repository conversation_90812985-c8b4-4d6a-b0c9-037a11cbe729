set ::msgcat::header "Project-Id-Version: el\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2009-06-23 21:33+0300\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: Greek <<EMAIL>>\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nX-Generator: Lokalize 0.3\nPlural-Forms: nplurals=2; plural=(n != 1);\n"
::msgcat::mcset el "git-gui: fatal error" "git-gui: \u03ba\u03c1\u03af\u03c3\u03b9\u03bc\u03bf \u03c3\u03c6\u03ac\u03bb\u03bc\u03b1"
::msgcat::mcset el "Invalid font specified in %s:" "\u039c\u03b7 \u03ad\u03b3\u03ba\u03c5\u03c1\u03b7 \u03b3\u03c1\u03b1\u03bc\u03bc\u03b1\u03c4\u03bf\u03c3\u03b5\u03b9\u03c1\u03ac \u03c3\u03c4\u03bf %s:"
::msgcat::mcset el "Main Font" "\u039a\u03cd\u03c1\u03b9\u03b1 \u0393\u03c1\u03b1\u03bc\u03bc\u03b1\u03c4\u03bf\u03c3\u03b5\u03b9\u03c1\u03ac"
::msgcat::mcset el "Diff/Console Font" "\u0393\u03c1\u03b1\u03bc\u03bc\u03b1\u03c4\u03bf\u03c3\u03b5\u03b9\u03c1\u03ac \u0394\u03b9\u03b1\u03c6\u03bf\u03c1\u03ac\u03c2/\u039a\u03bf\u03bd\u03c3\u03cc\u03bb\u03b1\u03c2"
::msgcat::mcset el "Cannot find git in PATH." "\u0394\u03b5 \u03b2\u03c1\u03ad\u03b8\u03b7\u03ba\u03b5 \u03c4\u03bf git \u03c3\u03c4\u03bf PATH."
::msgcat::mcset el "Cannot parse Git version string:" "\u0391\u03b4\u03cd\u03bd\u03b1\u03c4\u03b7 \u03b7 \u03b1\u03bd\u03ac\u03b3\u03bd\u03c9\u03c3\u03b7 \u03c3\u03c4\u03bf\u03b9\u03c7\u03b5\u03b9\u03bf\u03c3\u03b5\u03b9\u03c1\u03ac\u03c2 \u03ad\u03ba\u03b4\u03bf\u03c3\u03b7\u03c2 Git:"
::msgcat::mcset el "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "\u0394\u03b5 \u03bc\u03c0\u03bf\u03c1\u03b5\u03af \u03bd\u03b1 \u03b1\u03bd\u03b9\u03c7\u03bd\u03b5\u03c5\u03c4\u03b5\u03af \u03b7 \u03ad\u03ba\u03b4\u03bf\u03c3\u03b7 \u03c4\u03bf\u03c5 Git. \n\n\u03a4\u03bf %s \u03c5\u03c0\u03bf\u03c3\u03c4\u03b7\u03c1\u03af\u03b6\u03b5\u03b9 \u03c0\u03c9\u03c2 \u03b5\u03af\u03bd\u03b1\u03b9 \u03b7 \u03ad\u03ba\u03b4\u03bf\u03c3\u03b7 '%s'.\n\n\u03a4\u03bf %s  \u03b1\u03c0\u03b1\u03b9\u03c4\u03b5\u03af \u03c4\u03bf\u03c5\u03bb\u03ac\u03c7\u03b9\u03c3\u03c4\u03bf\u03bd Git 1.5.0 \u03ae \u03c0\u03b9\u03cc \u03c0\u03c1\u03cc\u03c3\u03c6\u03b1\u03c4\u03b7.\n\n\u039d\u03b1 \u03c5\u03c0\u03bf\u03c4\u03b5\u03b8\u03b5\u03af \u03c0\u03c9\u03c2 \u03c4\u03bf '%s' \u03b5\u03af\u03bd\u03b1\u03b9 \u03b7 \u03ad\u03ba\u03b4\u03bf\u03c3\u03b7 1.5.0;\n"
::msgcat::mcset el "Git directory not found:" "\u0394\u03b5 \u03b2\u03c1\u03ad\u03b8\u03b7\u03ba\u03b5 \u03c6\u03ac\u03ba\u03b5\u03bb\u03bf\u03c2 Git:"
::msgcat::mcset el "Cannot move to top of working directory:" "\u0394\u03b5\u03bd \u03b5\u03af\u03bd\u03b1\u03b9 \u03b4\u03c5\u03bd\u03b1\u03c4\u03ae \u03b7 \u03bc\u03b5\u03c4\u03b1\u03ba\u03af\u03bd\u03b7\u03c3\u03b7 \u03c3\u03c4\u03b7\u03bd \u03ba\u03bf\u03c1\u03c5\u03c6\u03ae \u03c4\u03bf\u03c5 \u03c6\u03b1\u03ba\u03ad\u03bb\u03bf\u03c5 \u03b5\u03c1\u03b3\u03b1\u03c3\u03af\u03b1\u03c2:"
::msgcat::mcset el "Cannot use funny .git directory:" "\u0394\u03b5\u03bd \u03b5\u03af\u03bd\u03b1\u03b9 \u03b4\u03c5\u03bd\u03b1\u03c4\u03ae \u03b7 \u03c7\u03c1\u03ae\u03c3\u03b7 \u03c0\u03b5\u03c1\u03af\u03b5\u03c1\u03b3\u03bf\u03c5 \u03c6\u03b1\u03ba\u03ad\u03bb\u03bf\u03c5 .git:"
::msgcat::mcset el "No working directory" "\u0394\u03b5\u03bd \u03c5\u03c0\u03ac\u03c1\u03c7\u03b5\u03b9 \u03c6\u03ac\u03ba\u03b5\u03bb\u03bf\u03c2 \u03b5\u03c1\u03b3\u03b1\u03c3\u03af\u03b1\u03c2"
::msgcat::mcset el "Refreshing file status..." "\u0391\u03bd\u03b1\u03bd\u03ad\u03c9\u03c3\u03b7 \u03ba\u03b1\u03c4\u03ac\u03c3\u03c4\u03b1\u03c3\u03b7\u03c2 \u03b1\u03c1\u03c7\u03b5\u03af\u03bf\u03c5..."
::msgcat::mcset el "Scanning for modified files ..." "\u0391\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7 \u03b3\u03b9\u03b1 \u03c4\u03c1\u03bf\u03c0\u03bf\u03c0\u03bf\u03b9\u03b7\u03bc\u03ad\u03bd\u03b1 \u03b1\u03c1\u03c7\u03b5\u03af\u03b1..."
::msgcat::mcset el "Ready." "\u0388\u03c4\u03bf\u03b9\u03bc\u03bf."
::msgcat::mcset el "Unmodified" "\u039c\u03b7 \u03c4\u03c1\u03bf\u03c0\u03bf\u03c0\u03bf\u03b9\u03b7\u03bc\u03ad\u03bd\u03bf"
::msgcat::mcset el "Modified, not staged" "\u03a4\u03c1\u03bf\u03c0\u03bf\u03c0\u03bf\u03b9\u03b7\u03bc\u03ad\u03bd\u03bf, \u03bc\u03b7 \u03c3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03b9\u03b7\u03bc\u03ad\u03bd\u03bf"
::msgcat::mcset el "Staged for commit" "\u03a3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03b9\u03b7\u03bc\u03ad\u03bd\u03bf \u03c0\u03c1\u03bf\u03c2 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae"
::msgcat::mcset el "Portions staged for commit" "\u039c\u03ad\u03c1\u03b7 \u03c3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03b9\u03b7\u03bc\u03ad\u03bd\u03b1 \u03c0\u03c1\u03bf\u03c2 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae"
::msgcat::mcset el "Staged for commit, missing" "\u03a3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03b9\u03b7\u03bc\u03ad\u03bd\u03bf \u03c0\u03c1\u03bf\u03c2 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae, \u03bb\u03b5\u03af\u03c0\u03b5\u03b9"
::msgcat::mcset el "Untracked, not staged" "\u039c\u03b7 \u03c0\u03b1\u03c1\u03b1\u03ba\u03bf\u03bb\u03bf\u03c5\u03b8\u03bf\u03cd\u03bc\u03b5\u03bd\u03bf, \u03bc\u03b7 \u03c3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03b9\u03b7\u03bc\u03ad\u03bd\u03bf"
::msgcat::mcset el "Missing" "\u039b\u03b5\u03af\u03c0\u03b5\u03b9"
::msgcat::mcset el "Staged for removal" "\u03a3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03b9\u03b7\u03bc\u03ad\u03bd\u03bf \u03c0\u03c1\u03bf\u03c2 \u03b1\u03c6\u03b1\u03af\u03c1\u03b5\u03c3\u03b7"
::msgcat::mcset el "Staged for removal, still present" "\u03a3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03b9\u03b7\u03bc\u03ad\u03bd\u03bf \u03c0\u03c1\u03bf\u03c2 \u03b1\u03c6\u03b1\u03af\u03c1\u03b5\u03c3\u03b7, \u03b1\u03ba\u03cc\u03bc\u03b1 \u03c0\u03b1\u03c1\u03cc\u03bd"
::msgcat::mcset el "Requires merge resolution" "\u0391\u03c0\u03b1\u03b9\u03c4\u03b5\u03af \u03b5\u03c0\u03af\u03bb\u03c5\u03c3\u03b7 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2"
::msgcat::mcset el "Starting gitk... please wait..." "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03b5\u03ba\u03ba\u03af\u03bd\u03b7\u03c3\u03b7 \u03c4\u03bf\u03c5 gitk... \u03c0\u03b1\u03c1\u03b1\u03ba\u03b1\u03bb\u03ce \u03c0\u03b5\u03c1\u03b9\u03bc\u03ad\u03bd\u03b5\u03c4\u03b5..."
::msgcat::mcset el "Unable to start gitk:\n\n%s does not exist" "\u0391\u03b4\u03c5\u03bd\u03b1\u03bc\u03af\u03b1 \u03b5\u03ba\u03ba\u03af\u03bd\u03b7\u03c3\u03b7\u03c2 \u03c4\u03bf\u03c5 gitk:\n\n\u03a4\u03bf %s \u03b4\u03b5\u03bd \u03c5\u03c0\u03ac\u03c1\u03c7\u03b5\u03b9"
::msgcat::mcset el "Repository" "\u0391\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf"
::msgcat::mcset el "Edit" "\u0395\u03c0\u03b5\u03be\u03b5\u03c1\u03b3\u03b1\u03c3\u03af\u03b1"
::msgcat::mcset el "Branch" "\u039a\u03bb\u03ac\u03b4\u03bf\u03c2"
::msgcat::mcset el "Commit@@noun" "\u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae@@noun"
::msgcat::mcset el "Merge" "\u03a3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7"
::msgcat::mcset el "Remote" "\u0391\u03c0\u03bf\u03bc\u03b1\u03ba\u03c1\u03c5\u03c3\u03bc\u03ad\u03bd\u03bf"
::msgcat::mcset el "Browse Current Branch's Files" "\u03a0\u03b5\u03c1\u03b9\u03ae\u03b3\u03b7\u03c3\u03b7 \u0391\u03c1\u03c7\u03b5\u03af\u03c9\u03bd \u03a4\u03c1\u03ad\u03c7\u03bf\u03bd\u03c4\u03b1 \u039a\u03bb\u03ac\u03b4\u03bf\u03c5"
::msgcat::mcset el "Browse Branch Files..." "\u03a0\u03b5\u03c1\u03b9\u03ae\u03b3\u03b7\u03c3\u03b7 \u0391\u03c1\u03c7\u03b5\u03af\u03c9\u03bd \u039a\u03bb\u03ac\u03b4\u03bf\u03c5..."
::msgcat::mcset el "Visualize Current Branch's History" "\u0391\u03c0\u03b5\u03b9\u03ba\u03cc\u03bd\u03b9\u03c3\u03b7 \u0399\u03c3\u03c4\u03bf\u03c1\u03b9\u03ba\u03bf\u03cd \u03a4\u03c1\u03ad\u03c7\u03bf\u03bd\u03c4\u03b1 \u039a\u03bb\u03ac\u03b4\u03bf\u03c5"
::msgcat::mcset el "Visualize All Branch History" "\u0391\u03c0\u03b5\u03b9\u03ba\u03cc\u03bd\u03b9\u03c3\u03b7 \u0399\u03c3\u03c4\u03bf\u03c1\u03b9\u03ba\u03bf\u03cd \u038c\u03bb\u03c9\u03bd \u03c4\u03c9\u03bd \u039a\u03bb\u03ac\u03b4\u03c9\u03bd"
::msgcat::mcset el "Browse %s's Files" "\u03a0\u03b5\u03c1\u03b9\u03ae\u03b3\u03b7\u03c3\u03b7 \u0391\u03c1\u03c7\u03b5\u03af\u03c9\u03bd \u03c4\u03bf\u03c5 %s"
::msgcat::mcset el "Visualize %s's History" "\u0391\u03c0\u03b5\u03b9\u03ba\u03cc\u03bd\u03b9\u03c3\u03b7 \u0399\u03c3\u03c4\u03bf\u03c1\u03b9\u03ba\u03bf\u03cd \u03c4\u03bf\u03c5 %s"
::msgcat::mcset el "Database Statistics" "\u03a3\u03c4\u03b1\u03c4\u03b9\u03c3\u03c4\u03b9\u03ba\u03ac \u0392\u03ac\u03c3\u03b7\u03c2 \u0394\u03b5\u03b4\u03bf\u03bc\u03ad\u03bd\u03c9\u03bd"
::msgcat::mcset el "Compress Database" "\u03a3\u03c5\u03bc\u03c0\u03af\u03b5\u03c3\u03b7 \u0392\u03ac\u03c3\u03b7\u03c2 \u0394\u03b5\u03b4\u03bf\u03bc\u03ad\u03bd\u03c9\u03bd"
::msgcat::mcset el "Verify Database" "\u0395\u03c0\u03b1\u03bb\u03ae\u03b8\u03b5\u03c5\u03c3\u03b7 \u0392\u03ac\u03c3\u03b7\u03c2 \u0394\u03b5\u03b4\u03bf\u03bc\u03ad\u03bd\u03c9\u03bd"
::msgcat::mcset el "Create Desktop Icon" "\u0394\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03af\u03b1 \u0395\u03b9\u03ba\u03bf\u03bd\u03b9\u03b4\u03af\u03bf\u03c5 \u0395\u03c0\u03b9\u03c6\u03ac\u03bd\u03b5\u03b9\u03b1\u03c2 \u0395\u03c1\u03b3\u03b1\u03c3\u03af\u03b1\u03c2"
::msgcat::mcset el "Quit" "\u0388\u03be\u03bf\u03b4\u03bf\u03c2"
::msgcat::mcset el "Undo" "\u0391\u03bd\u03b1\u03af\u03c1\u03b5\u03c3\u03b7"
::msgcat::mcset el "Redo" "\u039e\u03b1\u03bd\u03ac"
::msgcat::mcset el "Cut" "\u0391\u03c0\u03bf\u03ba\u03bf\u03c0\u03ae"
::msgcat::mcset el "Copy" "\u0391\u03bd\u03c4\u03b9\u03b3\u03c1\u03b1\u03c6\u03ae"
::msgcat::mcset el "Paste" "\u0395\u03c0\u03b9\u03ba\u03cc\u03bb\u03bb\u03b7\u03c3\u03b7"
::msgcat::mcset el "Delete" "\u0394\u03b9\u03b1\u03b3\u03c1\u03b1\u03c6\u03ae"
::msgcat::mcset el "Select All" "\u0395\u03c0\u03b9\u03bb\u03bf\u03b3\u03ae \u038c\u03bb\u03c9\u03bd"
::msgcat::mcset el "Create..." "\u0394\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03af\u03b1..."
::msgcat::mcset el "Checkout..." "\u0395\u03be\u03b1\u03b3\u03c9\u03b3\u03ae..."
::msgcat::mcset el "Rename..." "\u039c\u03b5\u03c4\u03bf\u03bd\u03bf\u03bc\u03b1\u03c3\u03af\u03b1..."
::msgcat::mcset el "Delete..." "\u0394\u03b9\u03b1\u03b3\u03c1\u03b1\u03c6\u03ae..."
::msgcat::mcset el "Reset..." "\u0395\u03c0\u03b1\u03bd\u03b1\u03c6\u03bf\u03c1\u03ac..."
::msgcat::mcset el "New Commit" "\u039d\u03ad\u03b1 \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae"
::msgcat::mcset el "Amend Last Commit" "\u0394\u03b9\u03cc\u03c1\u03b8\u03c9\u03c3\u03b7 \u03a4\u03b5\u03bb\u03b5\u03c5\u03c4\u03b1\u03af\u03b1\u03c2 \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2"
::msgcat::mcset el "Rescan" "\u0395\u03c0\u03b1\u03bd\u03b1\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7"
::msgcat::mcset el "Stage To Commit" "\u03a3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7 \u03a0\u03c1\u03bf\u03c2 \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae"
::msgcat::mcset el "Stage Changed Files To Commit" "\u03a3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7 \u0391\u03bb\u03bb\u03b1\u03b3\u03bc\u03ad\u03bd\u03c9\u03bd \u0391\u03c1\u03c7\u03b5\u03af\u03c9\u03bd \u03a0\u03c1\u03bf\u03c2 \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae"
::msgcat::mcset el "Unstage From Commit" "\u0391\u03c0\u03bf\u03c3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7 \u0391\u03c0\u03cc \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae"
::msgcat::mcset el "Revert Changes" "\u0391\u03bd\u03b1\u03af\u03c1\u03b5\u03c3\u03b7 \u0391\u03bb\u03bb\u03b1\u03b3\u03ce\u03bd"
::msgcat::mcset el "Sign Off" "\u0391\u03c0\u03bf\u03c3\u03cd\u03bd\u03b4\u03b5\u03c3\u03b7"
::msgcat::mcset el "Commit@@verb" "\u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae@@verb"
::msgcat::mcset el "Local Merge..." "\u03a4\u03bf\u03c0\u03b9\u03ba\u03ae \u03a3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7..."
::msgcat::mcset el "Abort Merge..." "\u0391\u03ba\u03cd\u03c1\u03c9\u03c3\u03b7 \u03a3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2..."
::msgcat::mcset el "Push..." "\u038f\u03b8\u03b7\u03c3\u03b7..."
::msgcat::mcset el "About %s" "\u03a0\u03b5\u03c1\u03af %s"
::msgcat::mcset el "Preferences..." "\u03a0\u03c1\u03bf\u03c4\u03b9\u03bc\u03ae\u03c3\u03b5\u03b9\u03c2..."
::msgcat::mcset el "Options..." "\u0395\u03c0\u03b9\u03bb\u03bf\u03b3\u03ad\u03c2..."
::msgcat::mcset el "Help" "\u0392\u03bf\u03ae\u03b8\u03b5\u03b9\u03b1"
::msgcat::mcset el "Online Documentation" "\u0394\u03b9\u03b1\u03b4\u03b9\u03ba\u03c4\u03c5\u03b1\u03ba\u03ae \u03a4\u03b5\u03ba\u03bc\u03b7\u03c1\u03af\u03c9\u03c3\u03b7"
::msgcat::mcset el "fatal: cannot stat path %s: No such file or directory" "\u03ba\u03c1\u03af\u03c3\u03b9\u03bc\u03bf: \u03b4\u03b5 \u03b2\u03c1\u03ad\u03b8\u03b7\u03ba\u03b5 \u03b7 \u03b4\u03b9\u03b1\u03b4\u03c1\u03bf\u03bc\u03ae: %s: \u0394\u03b5\u03bd \u03c5\u03c0\u03ac\u03c1\u03c7\u03b5\u03b9 \u03c4\u03bf \u03b1\u03c1\u03c7\u03b5\u03af\u03bf \u03ae \u03bf \u03c6\u03ac\u03ba\u03b5\u03bb\u03bf\u03c2"
::msgcat::mcset el "Current Branch:" "\u03a4\u03c1\u03ad\u03c7\u03c9\u03bd \u039a\u03bb\u03ac\u03b4\u03bf\u03c2:"
::msgcat::mcset el "Staged Changes (Will Commit)" "\u03a3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03b9\u03b7\u03bc\u03ad\u03bd\u03b5\u03c2 \u0391\u03bb\u03bb\u03b1\u03b3\u03ad\u03c2 (\u0398\u03b1 \u03a5\u03c0\u03bf\u03b2\u03bb\u03b7\u03b8\u03bf\u03cd\u03bd)"
::msgcat::mcset el "Unstaged Changes" "\u039c\u03b7 \u03a3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03b9\u03b7\u03bc\u03ad\u03bd\u03b5\u03c2 \u0391\u03bb\u03bb\u03b1\u03b3\u03ad\u03c2"
::msgcat::mcset el "Stage Changed" "\u03a3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7 \u0391\u03bb\u03bb\u03b1\u03b3\u03ce\u03bd"
::msgcat::mcset el "Push" "\u038f\u03b8\u03b7\u03c3\u03b7"
::msgcat::mcset el "Initial Commit Message:" "\u0391\u03c1\u03c7\u03b9\u03ba\u03cc \u039c\u03ae\u03bd\u03c5\u03bc\u03b1 \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2:"
::msgcat::mcset el "Amended Commit Message:" "\u0394\u03b9\u03bf\u03c1\u03b8\u03c9\u03bc\u03ad\u03bd\u03bf \u039c\u03ae\u03bd\u03c5\u03bc\u03b1 \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2:"
::msgcat::mcset el "Amended Initial Commit Message:" "\u0394\u03b9\u03bf\u03c1\u03b8\u03c9\u03bc\u03ad\u03bd\u03bf \u0391\u03c1\u03c7\u03b9\u03ba\u03cc \u039c\u03ae\u03bd\u03c5\u03bc\u03b1 \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2:"
::msgcat::mcset el "Amended Merge Commit Message:" "\u0394\u03b9\u03bf\u03c1\u03b8\u03c9\u03bc\u03ad\u03bd\u03bf \u039c\u03ae\u03bd\u03c5\u03bc\u03b1 \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2 \u03a3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2:"
::msgcat::mcset el "Merge Commit Message:" "\u039c\u03ae\u03bd\u03c5\u03bc\u03b1 \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2 \u03a3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2:"
::msgcat::mcset el "Commit Message:" "\u039c\u03ae\u03bd\u03c5\u03bc\u03b1 \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2:"
::msgcat::mcset el "Copy All" "\u0391\u03bd\u03c4\u03b9\u03b3\u03c1\u03b1\u03c6\u03ae \u038c\u03bb\u03c9\u03bd"
::msgcat::mcset el "File:" "\u0391\u03c1\u03c7\u03b5\u03af\u03bf:"
::msgcat::mcset el "Apply/Reverse Hunk" "\u0395\u03c6\u03b1\u03c1\u03bc\u03bf\u03b3\u03ae/\u0391\u03bd\u03c4\u03b9\u03c3\u03c4\u03c1\u03bf\u03c6\u03ae \u039a\u03bf\u03bc\u03bc\u03b1\u03c4\u03b9\u03bf\u03cd"
::msgcat::mcset el "Show Less Context" "\u03a0\u03c1\u03bf\u03b2\u03bf\u03bb\u03ae \u03a3\u03c4\u03b5\u03bd\u03cc\u03c4\u03b5\u03c1\u03bf\u03c5 \u03a0\u03bb\u03b1\u03b9\u03c3\u03af\u03bf\u03c5"
::msgcat::mcset el "Show More Context" "\u03a0\u03c1\u03bf\u03b2\u03bf\u03bb\u03ae \u0395\u03c5\u03c1\u03cd\u03c4\u03b5\u03c1\u03bf\u03c5 \u03a0\u03bb\u03b1\u03b9\u03c3\u03af\u03bf\u03c5"
::msgcat::mcset el "Refresh" "\u0391\u03bd\u03b1\u03bd\u03ad\u03c9\u03c3\u03b7"
::msgcat::mcset el "Decrease Font Size" "\u039c\u03b5\u03af\u03c9\u03c3\u03b7 \u039c\u03b5\u03b3\u03ad\u03b8\u03bf\u03c5\u03c2 \u0393\u03c1\u03b1\u03bc\u03bc\u03b1\u03c4\u03bf\u03c3\u03b5\u03b9\u03c1\u03ac\u03c2"
::msgcat::mcset el "Increase Font Size" "\u0391\u03cd\u03be\u03b7\u03c3\u03b7 \u039c\u03b5\u03b3\u03ad\u03b8\u03bf\u03c5\u03c2 \u0393\u03c1\u03b1\u03bc\u03bc\u03b1\u03c4\u03bf\u03c3\u03b5\u03b9\u03c1\u03ac\u03c2"
::msgcat::mcset el "Unstage Hunk From Commit" "\u0391\u03c0\u03bf\u03c3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7 \u039a\u03bf\u03bc\u03bc\u03b1\u03c4\u03b9\u03bf\u03cd \u0391\u03c0\u03cc \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae"
::msgcat::mcset el "Stage Hunk For Commit" "\u03a3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7 \u039a\u03bf\u03bc\u03bc\u03b1\u03c4\u03b9\u03bf\u03cd \u03a0\u03c1\u03bf\u03c2 \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae"
::msgcat::mcset el "Initializing..." "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03b1\u03c1\u03c7\u03b9\u03ba\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7..."
::msgcat::mcset el "Possible environment issues exist.\n\nThe following environment variables are probably\ngoing to be ignored by any Git subprocess run\nby %s:\n\n" "\u03a5\u03c0\u03ac\u03c1\u03c7\u03bf\u03c5\u03bd \u03c0\u03b9\u03b8\u03b1\u03bd\u03ac \u03b8\u03ad\u03bc\u03b1\u03c4\u03b1 \u03bc\u03b5 \u03c4\u03bf \u03c0\u03b5\u03c1\u03b9\u03b2\u03ac\u03bb\u03bb\u03bf\u03bd.\n\n\u039f\u03b9 \u03b5\u03be\u03ae\u03c2 \u03bc\u03b5\u03c4\u03b1\u03b2\u03bb\u03b7\u03c4\u03ad\u03c2 \u03c0\u03b5\u03c1\u03b9\u03b2\u03ac\u03bb\u03bb\u03bf\u03bd\u03c4\u03bf\u03c2 \u03bc\u03ac\u03bb\u03bb\u03bf\u03bd \u03b8\u03b1\n\u03b1\u03b3\u03bd\u03bf\u03b7\u03b8\u03bf\u03cd\u03bd \u03b1\u03c0\u03cc \u03c0\u03b9\u03b8\u03b1\u03bd\u03ae \u03b5\u03ba\u03c4\u03ad\u03bb\u03b5\u03c3\u03b7 \u03c5\u03c0\u03bf\u03b4\u03b9\u03b5\u03c1\u03b3\u03b1\u03c3\u03af\u03b1\u03c2 Git\n\u03b1\u03c0\u03cc \u03c4\u03bf %s:\n\n"
::msgcat::mcset el "\nThis is due to a known issue with the\nTcl binary distributed by Cygwin." "\n\u0391\u03c5\u03c4\u03cc \u03bf\u03c6\u03b5\u03af\u03bb\u03b5\u03c4\u03b1\u03b9 \u03c3\u03b5 \u03ad\u03bd\u03b1 \u03b3\u03bd\u03c9\u03c3\u03c4\u03cc \u03b8\u03ad\u03bc\u03b1 \u03bc\u03b5 \u03c4\u03bf\n\u03b5\u03ba\u03c4\u03b5\u03bb\u03ad\u03c3\u03b9\u03bc\u03bf Tcl \u03c0\u03bf\u03c5 \u03b4\u03b9\u03b1\u03bd\u03ad\u03bc\u03b5\u03c4\u03b1\u03b9 \u03bc\u03b5 \u03c4\u03bf Cygwin."
::msgcat::mcset el "\n\nA good replacement for %s\nis placing values for the user.name and\nuser.email settings into your personal\n~/.gitconfig file.\n" "\n\n\u0388\u03bd\u03b1 \u03ba\u03b1\u03bb\u03cc \u03c5\u03c0\u03bf\u03ba\u03b1\u03c4\u03ac\u03c3\u03c4\u03b1\u03c4\u03bf \u03b3\u03b9\u03b1 \u03c4\u03bf %s\n\u03b5\u03af\u03bd\u03b1\u03b9 \u03b7 \u03c4\u03bf\u03c0\u03bf\u03b8\u03ad\u03c4\u03b7\u03c3\u03b7 \u03c4\u03b9\u03bc\u03ce\u03bd \u03b3\u03b9\u03b1 \u03c4\u03b9\u03c2 \u03c1\u03c5\u03b8\u03bc\u03af\u03c3\u03b5\u03b9\u03c2\nuser.name \u03ba\u03b1\u03b9 user.email \u03c3\u03c4\u03bf \u03c0\u03c1\u03bf\u03c3\u03c9\u03c0\u03b9\u03ba\u03cc \u03c3\u03b1\u03c2\n\u03b1\u03c1\u03c7\u03b5\u03af\u03bf ~/.gitconfig .\n"
::msgcat::mcset el "git-gui - a graphical user interface for Git." "git-gui - \u03ad\u03bd\u03b1 \u03b3\u03c1\u03b1\u03c6\u03b9\u03ba\u03cc \u03c0\u03b5\u03c1\u03b9\u03b2\u03ac\u03bb\u03bb\u03bf\u03bd \u03b3\u03b9\u03b1 \u03c4\u03bf Git."
::msgcat::mcset el "File Viewer" "\u0395\u03c6\u03b1\u03c1\u03bc\u03bf\u03b3\u03ae \u03a0\u03c1\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2 \u0391\u03c1\u03c7\u03b5\u03af\u03bf\u03c5"
::msgcat::mcset el "Commit:" "\u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae:"
::msgcat::mcset el "Copy Commit" "\u0391\u03bd\u03c4\u03b9\u03b3\u03c1\u03b1\u03c6\u03ae \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2"
::msgcat::mcset el "Reading %s..." "\u0391\u03bd\u03ac\u03b3\u03bd\u03c9\u03c3\u03b7 %s..."
::msgcat::mcset el "Loading copy/move tracking annotations..." "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03c6\u03cc\u03c1\u03c4\u03c9\u03c3\u03b7 \u03c3\u03c7\u03bf\u03bb\u03af\u03c9\u03bd \u03c0\u03b1\u03c1\u03b1\u03ba\u03bf\u03bb\u03bf\u03cd\u03b8\u03b7\u03c3\u03b7\u03c2 \u03b1\u03bd\u03c4\u03b9\u03b3\u03c1\u03b1\u03c6\u03ae\u03c2/\u03bc\u03b5\u03c4\u03b1\u03ba\u03af\u03bd\u03b7\u03c3\u03b7\u03c2..."
::msgcat::mcset el "lines annotated" "\u03b3\u03c1\u03b1\u03bc\u03bc\u03ad\u03c2 \u03c3\u03c7\u03bf\u03bb\u03b9\u03b1\u03c3\u03bc\u03ad\u03bd\u03b5\u03c2"
::msgcat::mcset el "Loading original location annotations..." "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03c6\u03cc\u03c1\u03c4\u03c9\u03c3\u03b7 \u03c3\u03c7\u03bf\u03bb\u03af\u03c9\u03bd \u03b1\u03c1\u03c7\u03b9\u03ba\u03ae\u03c2 \u03c4\u03bf\u03c0\u03bf\u03b8\u03b5\u03c3\u03af\u03b1\u03c2..."
::msgcat::mcset el "Annotation complete." "\u0388\u03b3\u03b9\u03bd\u03b5 \u03bf\u03bb\u03bf\u03ba\u03bb\u03ae\u03c1\u03c9\u03c3\u03b7 \u03c4\u03bf\u03c5 \u03c3\u03c7\u03bf\u03bb\u03b9\u03b1\u03c3\u03bc\u03bf\u03cd."
::msgcat::mcset el "Loading annotation..." "\u03a6\u03cc\u03c1\u03c4\u03c9\u03c3\u03b7 \u03c3\u03c7\u03bf\u03bb\u03af\u03bf\u03c5..."
::msgcat::mcset el "Author:" "\u0394\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03cc\u03c2:"
::msgcat::mcset el "Committer:" "\u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ad\u03b1\u03c2:"
::msgcat::mcset el "Original File:" "\u0391\u03c1\u03c7\u03b9\u03ba\u03cc \u0391\u03c1\u03c7\u03b5\u03af\u03bf:"
::msgcat::mcset el "Originally By:" "\u0391\u03c1\u03c7\u03b9\u03ba\u03ac \u0391\u03c0\u03cc:"
::msgcat::mcset el "In File:" "\u03a3\u03c4\u03bf \u0391\u03c1\u03c7\u03b5\u03af\u03bf:"
::msgcat::mcset el "Copied Or Moved Here By:" "\u0391\u03bd\u03c4\u03b9\u03b3\u03c1\u03ac\u03c6\u03b7\u03ba\u03b5 \u03ae \u039c\u03b5\u03c4\u03b1\u03ba\u03b9\u03bd\u03ae\u03b8\u03b7\u03ba\u03b5 \u0395\u03b4\u03ce \u0391\u03c0\u03cc:"
::msgcat::mcset el "Checkout Branch" "\u0395\u03be\u03b1\u03b3\u03c9\u03b3\u03ae \u039a\u03bb\u03ac\u03b4\u03bf\u03c5"
::msgcat::mcset el "Checkout" "\u0395\u03be\u03b1\u03b3\u03c9\u03b3\u03ae"
::msgcat::mcset el "Cancel" "\u0391\u03ba\u03cd\u03c1\u03c9\u03c3\u03b7"
::msgcat::mcset el "Revision" "\u0391\u03bd\u03b1\u03b8\u03b5\u03ce\u03c1\u03b7\u03c3\u03b7"
::msgcat::mcset el "Options" "\u0395\u03c0\u03b9\u03bb\u03bf\u03b3\u03ad\u03c2"
::msgcat::mcset el "Fetch Tracking Branch" "\u0391\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7 \u039a\u03bb\u03ac\u03b4\u03bf\u03c5 \u03a0\u03b1\u03c1\u03b1\u03ba\u03bf\u03bb\u03bf\u03cd\u03b8\u03b7\u03c3\u03b7\u03c2"
::msgcat::mcset el "Detach From Local Branch" "\u0391\u03c0\u03bf\u03ba\u03cc\u03bb\u03bb\u03b7\u03c3\u03b7 \u0391\u03c0\u03cc \u03a4\u03bf\u03c0\u03b9\u03ba\u03cc \u039a\u03bb\u03ac\u03b4\u03bf"
::msgcat::mcset el "Create Branch" "\u0394\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03af\u03b1 \u039a\u03bb\u03ac\u03b4\u03bf\u03c5"
::msgcat::mcset el "Create New Branch" "\u0394\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03af\u03b1 \u039d\u03ad\u03bf\u03c5 \u039a\u03bb\u03ac\u03b4\u03bf\u03c5"
::msgcat::mcset el "Create" "\u0394\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03af\u03b1"
::msgcat::mcset el "Branch Name" "\u038c\u03bd\u03bf\u03bc\u03b1 \u039a\u03bb\u03ac\u03b4\u03bf\u03c5"
::msgcat::mcset el "Name:" "\u038c\u03bd\u03bf\u03bc\u03b1:"
::msgcat::mcset el "Match Tracking Branch Name" "\u03a3\u03c5\u03bc\u03c6\u03c9\u03bd\u03af\u03b1 \u039f\u03bd\u03cc\u03bc\u03b1\u03c4\u03bf\u03c2 \u039a\u03bb\u03ac\u03b4\u03bf\u03c5 \u03a0\u03b1\u03c1\u03b1\u03ba\u03bf\u03bb\u03bf\u03cd\u03b8\u03b7\u03c3\u03b7\u03c2"
::msgcat::mcset el "Starting Revision" "\u0391\u03c1\u03c7\u03b9\u03ba\u03ae \u0391\u03bd\u03b1\u03b8\u03b5\u03ce\u03c1\u03b7\u03c3\u03b7"
::msgcat::mcset el "Update Existing Branch:" "\u0395\u03bd\u03b7\u03bc\u03ad\u03c1\u03c9\u03c3\u03b7 \u03a5\u03c0\u03ac\u03c1\u03c7\u03bf\u03bd\u03c4\u03b1 \u039a\u03bb\u03ac\u03b4\u03bf\u03c5:"
::msgcat::mcset el "Fast Forward Only" "\u03a3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7 \u0395\u03c0\u03b9\u03c4\u03ac\u03c7\u03c5\u03bd\u03c3\u03b7\u03c2 \u039c\u03cc\u03bd\u03bf"
::msgcat::mcset el "Reset" "\u0395\u03c0\u03b1\u03bd\u03b1\u03c6\u03bf\u03c1\u03ac"
::msgcat::mcset el "Checkout After Creation" "\u0395\u03be\u03b1\u03b3\u03c9\u03b3\u03ae \u039c\u03b5\u03c4\u03ac \u03c4\u03b7 \u0394\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03af\u03b1"
::msgcat::mcset el "Please select a tracking branch." "\u03a0\u03b1\u03c1\u03b1\u03ba\u03b1\u03bb\u03ce \u03b5\u03c0\u03b9\u03bb\u03ad\u03be\u03c4\u03b5 \u03ad\u03bd\u03b1\u03bd \u03ba\u03bb\u03ac\u03b4\u03bf \u03c0\u03b1\u03c1\u03b1\u03ba\u03bf\u03bb\u03bf\u03cd\u03b8\u03b7\u03c3\u03b7\u03c2."
::msgcat::mcset el "Tracking branch %s is not a branch in the remote repository." "\u039f \u03ba\u03bb\u03ac\u03b4\u03bf\u03c2 \u03c0\u03b1\u03c1\u03b1\u03ba\u03bf\u03bb\u03bf\u03cd\u03b8\u03b7\u03c3\u03b7\u03c2 %s \u03b4\u03b5\u03bd \u03b5\u03af\u03bd\u03b1\u03b9 \u03ba\u03bb\u03ac\u03b4\u03bf\u03c2 \u03c0\u03bf\u03c5 \u03b2\u03c1\u03af\u03c3\u03ba\u03b5\u03c4\u03b1\u03b9 \u03c3\u03c4\u03bf \u03b1\u03c0\u03bf\u03bc\u03b1\u03ba\u03c1\u03c5\u03c3\u03bc\u03ad\u03bd\u03bf \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf."
::msgcat::mcset el "Please supply a branch name." "\u03a0\u03b1\u03c1\u03b1\u03ba\u03b1\u03bb\u03ce \u03b4\u03ce\u03c3\u03c4\u03b5 \u03ad\u03bd\u03b1 \u03cc\u03bd\u03bf\u03bc\u03b1 \u03ba\u03bb\u03ac\u03b4\u03bf\u03c5."
::msgcat::mcset el "'%s' is not an acceptable branch name." "'%s' \u03b4\u03b5\u03bd \u03b5\u03af\u03bd\u03b1\u03b9 \u03b1\u03c0\u03bf\u03b4\u03b5\u03ba\u03c4\u03cc \u03cc\u03bd\u03bf\u03bc\u03b1 \u03ba\u03bb\u03ac\u03b4\u03bf\u03c5."
::msgcat::mcset el "Delete Branch" "\u0394\u03b9\u03b1\u03b3\u03c1\u03b1\u03c6\u03ae \u039a\u03bb\u03ac\u03b4\u03bf\u03c5"
::msgcat::mcset el "Delete Local Branch" "\u0394\u03b9\u03b1\u03b3\u03c1\u03b1\u03c6\u03ae \u03a4\u03bf\u03c0\u03b9\u03ba\u03bf\u03cd \u039a\u03bb\u03ac\u03b4\u03bf\u03c5"
::msgcat::mcset el "Local Branches" "\u03a4\u03bf\u03c0\u03b9\u03ba\u03bf\u03af \u039a\u03bb\u03ac\u03b4\u03bf\u03b9"
::msgcat::mcset el "Delete Only If Merged Into" "\u0394\u03b9\u03b1\u03b3\u03c1\u03b1\u03c6\u03ae \u039c\u03cc\u03bd\u03bf \u0395\u03ac\u03bd \u0395\u03af\u03bd\u03b1\u03b9 \u03a3\u03c5\u03b3\u03c7\u03c9\u03bd\u03b5\u03c5\u03bc\u03ad\u03bd\u03bf \u039c\u03b5"
::msgcat::mcset el "Always (Do not perform merge test.)" "\u03a0\u03ac\u03bd\u03c4\u03b1 (\u039c\u03b7 \u03b4\u03b9\u03b5\u03bd\u03b5\u03c1\u03b3\u03b7\u03b8\u03b5\u03af \u03b4\u03bf\u03ba\u03b9\u03bc\u03ae \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2.)"
::msgcat::mcset el "The following branches are not completely merged into %s:" "\u039f\u03b9 \u03b5\u03be\u03ae\u03c2 \u03ba\u03bb\u03ac\u03b4\u03bf\u03b9 \u03b4\u03b5\u03bd \u03b5\u03af\u03bd\u03b1\u03b9 \u03c0\u03bb\u03ae\u03c1\u03c9\u03c2 \u03c3\u03c5\u03b3\u03c7\u03c9\u03bd\u03b5\u03c5\u03bc\u03ad\u03bd\u03bf\u03b9 \u03bc\u03b5 \u03c4\u03bf %s:"
::msgcat::mcset el "Recovering deleted branches is difficult. \n\n Delete the selected branches?" "\u0397 \u03b1\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7 \u03b4\u03b9\u03b5\u03b3\u03c1\u03b1\u03bc\u03bc\u03ad\u03bd\u03c9\u03bd \u03ba\u03bb\u03ac\u03b4\u03c9\u03bd \u03b5\u03af\u03bd\u03b1\u03b9 \u03b4\u03cd\u03c3\u03ba\u03bf\u03bb\u03b7.\n\n\u0394\u03b9\u03b1\u03b3\u03c1\u03b1\u03c6\u03ae \u03c4\u03c9\u03bd \u03b5\u03c0\u03b9\u03bb\u03b5\u03b3\u03bc\u03ad\u03bd\u03c9\u03bd \u03ba\u03bb\u03ac\u03b4\u03c9\u03bd;"
::msgcat::mcset el "Failed to delete branches:\n%s" "\u0391\u03c0\u03bf\u03c4\u03c5\u03c7\u03af\u03b1 \u03b4\u03b9\u03b1\u03b3\u03c1\u03b1\u03c6\u03ae\u03c2 \u03ba\u03bb\u03ac\u03b4\u03c9\u03bd:\n%s"
::msgcat::mcset el "Rename Branch" "\u039c\u03b5\u03c4\u03bf\u03bd\u03bf\u03bc\u03b1\u03c3\u03af\u03b1 \u039a\u03bb\u03ac\u03b4\u03bf\u03c5"
::msgcat::mcset el "Rename" "\u039c\u03b5\u03c4\u03bf\u03bd\u03bf\u03bc\u03b1\u03c3\u03af\u03b1"
::msgcat::mcset el "Branch:" "\u039a\u03bb\u03ac\u03b4\u03bf\u03c2:"
::msgcat::mcset el "New Name:" "\u039d\u03ad\u03bf \u038c\u03bd\u03bf\u03bc\u03b1:"
::msgcat::mcset el "Please select a branch to rename." "\u03a0\u03b1\u03c1\u03b1\u03ba\u03b1\u03bb\u03ce \u03b5\u03c0\u03b9\u03bb\u03ad\u03be\u03c4\u03b5 \u03ba\u03bb\u03ac\u03b4\u03bf \u03c0\u03c1\u03bf\u03c2 \u03bc\u03b5\u03c4\u03bf\u03bd\u03bf\u03bc\u03b1\u03c3\u03af\u03b1:"
::msgcat::mcset el "Branch '%s' already exists." "\u039f \u039a\u03bb\u03ac\u03b4\u03bf\u03c2 '%s' \u03c5\u03c0\u03ac\u03c1\u03c7\u03b5\u03b9 \u03ae\u03b4\u03b7."
::msgcat::mcset el "Failed to rename '%s'." "\u0391\u03c0\u03bf\u03c4\u03c5\u03c7\u03af\u03b1 \u03bc\u03b5\u03c4\u03bf\u03bd\u03bf\u03bc\u03b1\u03c3\u03af\u03b1\u03c2 '%s'."
::msgcat::mcset el "Starting..." "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u0395\u03ba\u03ba\u03af\u03bd\u03b7\u03c3\u03b7..."
::msgcat::mcset el "File Browser" "\u03a0\u03b5\u03c1\u03b9\u03b7\u03b3\u03b7\u03c4\u03ae\u03c2 \u0391\u03c1\u03c7\u03b5\u03af\u03c9\u03bd"
::msgcat::mcset el "Loading %s..." "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03c6\u03cc\u03c1\u03c4\u03c9\u03c3\u03b7 %s..."
::msgcat::mcset el "Browse Branch Files" "\u03a0\u03b5\u03c1\u03b9\u03ae\u03b3\u03b7\u03c3\u03b7 \u0391\u03c1\u03c7\u03b5\u03af\u03c9\u03bd \u039a\u03bb\u03ac\u03b4\u03bf\u03c5"
::msgcat::mcset el "Browse" "\u03a0\u03b5\u03c1\u03b9\u03ae\u03b3\u03b7\u03c3\u03b7"
::msgcat::mcset el "Fetching %s from %s" "\u0391\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7 %s \u03b1\u03c0\u03cc \u03c4\u03bf %s"
::msgcat::mcset el "fatal: Cannot resolve %s" "\u03ba\u03c1\u03af\u03c3\u03b9\u03bc\u03bf: \u0394\u03b5 \u03bc\u03c0\u03cc\u03c1\u03b5\u03c3\u03b5 \u03bd\u03b1 \u03b5\u03c0\u03b9\u03bb\u03c5\u03b8\u03b5\u03af \u03c4\u03bf %s"
::msgcat::mcset el "Close" "\u039a\u03bb\u03b5\u03af\u03c3\u03b9\u03bc\u03bf"
::msgcat::mcset el "Branch '%s' does not exist." "\u039f \u039a\u03bb\u03ac\u03b4\u03bf\u03c2 '%s' \u03b4\u03b5\u03bd \u03c5\u03c0\u03ac\u03c1\u03c7\u03b5\u03b9."
::msgcat::mcset el "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "\u039f \u039a\u03bb\u03ac\u03b4\u03bf\u03c2 '%s' \u03c5\u03c0\u03ac\u03c1\u03c7\u03b5\u03b9 \u03ae\u03b4\u03b7.\n\n\u0394\u03b5 \u03b3\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7 \u03b5\u03c0\u03b9\u03c4\u03ac\u03c7\u03c5\u03bd\u03c3\u03ae\u03c2 \u03c4\u03bf\u03c5 \u03c3\u03c4\u03bf %s.\n\u0391\u03c0\u03b1\u03b9\u03c4\u03b5\u03af\u03c4\u03b1\u03b9 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7."
::msgcat::mcset el "Merge strategy '%s' not supported." "\u0397 \u03c3\u03c4\u03c1\u03b1\u03c4\u03b7\u03b3\u03b9\u03ba\u03ae \u03a3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2 %s \u03b4\u03b5\u03bd \u03c5\u03c0\u03bf\u03c3\u03c4\u03b7\u03c1\u03af\u03b6\u03b5\u03c4\u03b1\u03b9."
::msgcat::mcset el "Failed to update '%s'." "\u0391\u03c0\u03bf\u03c4\u03c5\u03c7\u03af\u03b1 \u03b5\u03bd\u03b7\u03bc\u03ad\u03c1\u03c9\u03c3\u03b7\u03c2 '%s'."
::msgcat::mcset el "Staging area (index) is already locked." "\u0397 \u03c0\u03b5\u03c1\u03b9\u03bf\u03c7\u03ae \u03c3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7\u03c2 (\u03c4\u03bf \u03b5\u03c5\u03c1\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf) \u03b5\u03af\u03bd\u03b1\u03b9 \u03ae\u03b4\u03b7 \u03ba\u03bb\u03b5\u03b9\u03b4\u03c9\u03bc\u03ad\u03bd\u03b7."
::msgcat::mcset el "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before the current branch can be changed.\n\nThe rescan will be automatically started now.\n" "\u0397 \u03c4\u03b5\u03bb\u03b5\u03c5\u03c4\u03b1\u03af\u03b1 \u03ba\u03b1\u03c4\u03ac\u03c3\u03c4\u03b1\u03c3\u03b7 \u03c0\u03bf\u03c5 \u03b1\u03bd\u03b9\u03c7\u03bd\u03b5\u03cd\u03b8\u03b7\u03ba\u03b5 \u03b4\u03b5 \u03c3\u03c5\u03bc\u03c6\u03c9\u03bd\u03b5\u03af \u03bc\u03b5 \u03c4\u03b7\u03bd \u03ba\u03b1\u03c4\u03ac\u03c3\u03c4\u03b1\u03c3\u03b7 \u03c4\u03bf\u03c5 \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5.\n\n\u039a\u03ac\u03c0\u03bf\u03b9\u03bf \u03ac\u03bb\u03bb\u03bf \u03c0\u03c1\u03cc\u03b3\u03c1\u03b1\u03bc\u03bc\u03b1 Git \u03c4\u03c1\u03bf\u03c0\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b5 \u03c4\u03bf \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf \u03b1\u03c0\u03cc \u03c4\u03b7\u03bd \u03c4\u03b5\u03bb\u03b5\u03c5\u03c4\u03b1\u03af\u03b1 \u03b1\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7. \u03a0\u03c1\u03ad\u03c0\u03b5\u03b9 \u03bd\u03b1 \u03b3\u03af\u03bd\u03b5\u03b9 \u03b5\u03c0\u03b1\u03bd\u03b1\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7 \u03c0\u03c1\u03b9\u03bd \u03bd\u03b1 \u03b1\u03bb\u03bb\u03b1\u03c7\u03b8\u03b5\u03af \u03bf \u03c4\u03c1\u03ad\u03c7\u03c9\u03bd \u03ba\u03bb\u03ac\u03b4\u03bf\u03c2.\n\n\u0397 \u03b5\u03c0\u03b1\u03bd\u03b1\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7 \u03b8\u03b1 \u03be\u03b5\u03ba\u03b9\u03bd\u03ae\u03c3\u03b5\u03b9 \u03b1\u03c5\u03c4\u03cc\u03bc\u03b1\u03c4\u03b1 \u03c4\u03ce\u03c1\u03b1.\n"
::msgcat::mcset el "Updating working directory to '%s'..." "\u0395\u03bd\u03b7\u03bc\u03ad\u03c1\u03c9\u03c3\u03b7 \u03c6\u03b1\u03ba\u03ad\u03bb\u03bf\u03c5 \u03b5\u03c1\u03b3\u03b1\u03c3\u03af\u03b1\u03c2 \u03c3\u03b5 '%s'..."
::msgcat::mcset el "files checked out" "\u03b1\u03c1\u03c7\u03b5\u03af\u03b1 \u03ad\u03c7\u03bf\u03c5\u03bd \u03b5\u03be\u03b1\u03c7\u03b8\u03b5\u03af"
::msgcat::mcset el "Aborted checkout of '%s' (file level merging is required)." "\u0388\u03b3\u03b9\u03bd\u03b5 \u03b1\u03ba\u03cd\u03c1\u03c9\u03c3\u03b7 \u03b5\u03be\u03b1\u03b3\u03c9\u03b3\u03ae\u03c2 \u03c4\u03bf\u03c5 '%s' (\u03b1\u03c0\u03b1\u03b9\u03c4\u03b5\u03af\u03c4\u03b1\u03b9 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7 \u03b5\u03c0\u03b9\u03c0\u03ad\u03b4\u03bf\u03c5 \u03b1\u03c1\u03c7\u03b5\u03af\u03bf\u03c5)."
::msgcat::mcset el "File level merge required." "\u0391\u03c0\u03b1\u03b9\u03c4\u03b5\u03af\u03c4\u03b1\u03b9 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7 \u03b5\u03c0\u03b9\u03c0\u03ad\u03b4\u03bf\u03c5 \u03b1\u03c1\u03c7\u03b5\u03af\u03bf\u03c5."
::msgcat::mcset el "Staying on branch '%s'." "\u03a0\u03b1\u03c1\u03b1\u03bc\u03bf\u03bd\u03ae \u03c3\u03c4\u03bf\u03bd \u03ba\u03bb\u03ac\u03b4\u03bf '%s'."
::msgcat::mcset el "You are no longer on a local branch.\n\nIf you wanted to be on a branch, create one now starting from 'This Detached Checkout'." "\u0394\u03b5 \u03b2\u03c1\u03af\u03c3\u03ba\u03b5\u03c3\u03c4\u03b5 \u03c0\u03b9\u03b1 \u03c3\u03b5 \u03c4\u03bf\u03c0\u03b9\u03ba\u03cc \u03ba\u03bb\u03ac\u03b4\u03bf.\n\n\u0391\u03bd \u03b8\u03ad\u03bb\u03b1\u03c4\u03b5 \u03bd\u03b1 \u03b2\u03c1\u03af\u03c3\u03ba\u03b5\u03c3\u03c4\u03b5 \u03c3\u03b5 \u03ba\u03bb\u03ac\u03b4\u03bf, \u03b4\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03ae\u03c3\u03c4\u03b5 \u03ad\u03bd\u03b1\u03bd \u03c4\u03ce\u03c1\u03b1 \u03b1\u03c1\u03c7\u03af\u03b6\u03bf\u03bd\u03c4\u03b1\u03c2 \u03b1\u03c0\u03cc 'This Detached Checkout'."
::msgcat::mcset el "Checked out '%s'." "\u0388\u03b3\u03b9\u03bd\u03b5 \u03b5\u03be\u03b1\u03b3\u03c9\u03b3\u03ae \u03c4\u03bf\u03c5 '%s'."
::msgcat::mcset el "Resetting '%s' to '%s' will lose the following commits:" "\u0397 \u03b5\u03c0\u03b1\u03bd\u03b1\u03c6\u03bf\u03c1\u03ac '%s' \u03c3\u03c4\u03bf '%s' \u03b8\u03b1 \u03c7\u03ac\u03c3\u03b5\u03b9 \u03c4\u03b9\u03c2 \u03b5\u03be\u03ae\u03c2 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ad\u03c2:"
::msgcat::mcset el "Recovering lost commits may not be easy." "\u0397 \u03b1\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7 \u03c7\u03b1\u03bc\u03ad\u03bd\u03c9\u03bd \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ce\u03bd \u03bc\u03c0\u03bf\u03c1\u03b5\u03af \u03bd\u03b1 \u03b5\u03af\u03bd\u03b1\u03b9 \u03b4\u03cd\u03c3\u03ba\u03bf\u03bb\u03b7."
::msgcat::mcset el "Reset '%s'?" "\u0395\u03c0\u03b1\u03bd\u03b1\u03c6\u03bf\u03c1\u03ac '%s';"
::msgcat::mcset el "Visualize" "\u0391\u03c0\u03b5\u03b9\u03ba\u03cc\u03bd\u03b9\u03c3\u03b7"
::msgcat::mcset el "Failed to set current branch.\n\nThis working directory is only partially switched.  We successfully updated your files, but failed to update an internal Git file.\n\nThis should not have occurred.  %s will now close and give up." "\u0391\u03c0\u03bf\u03c4\u03c5\u03c7\u03af\u03b1 \u03bf\u03c1\u03b9\u03c3\u03bc\u03bf\u03cd \u03c4\u03c1\u03ad\u03c7\u03bf\u03bd\u03c4\u03bf\u03c2 \u03ba\u03bb\u03ac\u03b4\u03bf\u03c5.\n\n\u0391\u03c5\u03c4\u03cc\u03c2 \u03bf \u03c6\u03ac\u03ba\u03b5\u03bb\u03bf\u03c2 \u03b5\u03c1\u03b3\u03b1\u03c3\u03af\u03b1\u03c2 \u03b5\u03af\u03bd\u03b1\u03b9 \u03bc\u03cc\u03bd\u03bf \u03b5\u03bd \u03bc\u03ad\u03c1\u03b5\u03b9 \u03b5\u03c0\u03b9\u03bb\u03b5\u03b3\u03bc\u03ad\u03bd\u03bf\u03c2. '\u0395\u03b3\u03b9\u03bd\u03b5 \u03b5\u03c0\u03b9\u03c4\u03c5\u03c7\u03ae\u03c2 \u03b5\u03bd\u03b7\u03bc\u03ad\u03c1\u03c9\u03c3\u03b7 \u03c4\u03c9\u03bd \u03b1\u03c1\u03c7\u03b5\u03af\u03c9\u03bd \u03c3\u03b1\u03c2, \u03b1\u03bb\u03bb\u03ac \u03b1\u03c0\u03ad\u03c4\u03c5\u03c7\u03b5 \u03b7 \u03b5\u03bd\u03b7\u03bc\u03ad\u03c1\u03c9\u03c3\u03b7 \u03b5\u03bd\u03cc\u03c2 \u03b5\u03c3\u03c9\u03c4\u03b5\u03c1\u03b9\u03ba\u03bf\u03cd \u03b1\u03c1\u03c7\u03b5\u03af\u03bf\u03c5 \u03c4\u03bf\u03c5 Git.\n\n\u0391\u03c5\u03c4\u03cc \u03b4\u03b5 \u03b8\u03b1 \u03ad\u03c0\u03c1\u03b5\u03c0\u03b5 \u03bd\u03b1 \u03c3\u03c5\u03bc\u03b2\u03b5\u03af. \u03a4\u03bf %s \u03b8\u03b1 \u03ba\u03bb\u03b5\u03af\u03c3\u03b5\u03b9 \u03ba\u03b1\u03b9 \u03b8\u03b1 \u03b5\u03b3\u03ba\u03b1\u03c4\u03b1\u03bb\u03b5\u03af\u03c8\u03b5\u03b9 \u03c4\u03ce\u03c1\u03b1."
::msgcat::mcset el "Select" "\u0395\u03c0\u03b9\u03bb\u03bf\u03b3\u03ae"
::msgcat::mcset el "Font Family" "\u039f\u03b9\u03ba\u03bf\u03b3\u03ad\u03bd\u03b5\u03b9\u03b1 \u0393\u03c1\u03b1\u03bc\u03bc\u03b1\u03c4\u03bf\u03c3\u03b5\u03b9\u03c1\u03ac\u03c2"
::msgcat::mcset el "Font Size" "\u039c\u03ad\u03b3\u03b5\u03b8\u03bf\u03c2 \u0393\u03c1\u03b1\u03bc\u03bc\u03b1\u03c4\u03bf\u03c3\u03b5\u03b9\u03c1\u03ac\u03c2"
::msgcat::mcset el "Font Example" "\u03a0\u03b1\u03c1\u03ac\u03b4\u03b5\u03b9\u03b3\u03bc\u03b1 \u0393\u03c1\u03b1\u03bc\u03bc\u03b1\u03c4\u03bf\u03c3\u03b5\u03b9\u03c1\u03ac\u03c2"
::msgcat::mcset el "This is example text.\nIf you like this text, it can be your font." "\u0391\u03c5\u03c4\u03cc \u03b5\u03af\u03bd\u03b1\u03b9 \u03ad\u03bd\u03b1 \u03c0\u03b1\u03c1\u03ac\u03b4\u03b5\u03b9\u03b3\u03bc\u03b1 \u03ba\u03b5\u03b9\u03bc\u03ad\u03bd\u03bf\u03c5.\n\u0391\u03bd \u03c3\u03b1\u03c2 \u03b1\u03c1\u03ad\u03c3\u03b5\u03b9 \u03b1\u03c5\u03c4\u03cc \u03c4\u03bf \u03ba\u03b5\u03af\u03bc\u03b5\u03bd\u03bf, \u03bc\u03c0\u03bf\u03c1\u03b5\u03af \u03bd\u03b1 \u03b3\u03af\u03bd\u03b5\u03b9 \u03b4\u03b9\u03ba\u03cc \u03c3\u03b1\u03c2."
::msgcat::mcset el "Git Gui" "\u0393\u03c1\u03b1\u03c6\u03b9\u03ba\u03cc \u03a0\u03b5\u03c1\u03b9\u03b2\u03ac\u03bb\u03bb\u03bf\u03bd Git"
::msgcat::mcset el "Create New Repository" "\u0394\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03af\u03b1 \u039d\u03ad\u03bf\u03c5 \u0391\u03c0\u03bf\u03b8\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5"
::msgcat::mcset el "New..." "\u039d\u03ad\u03bf..."
::msgcat::mcset el "Clone Existing Repository" "\u039a\u03bb\u03c9\u03bd\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7 \u03a5\u03c0\u03ac\u03c1\u03c7\u03bf\u03bd\u03c4\u03bf\u03c2 \u0391\u03c0\u03bf\u03b8\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5"
::msgcat::mcset el "Clone..." "\u039a\u03bb\u03c9\u03bd\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7..."
::msgcat::mcset el "Open Existing Repository" "\u0386\u03bd\u03bf\u03b9\u03b3\u03bc\u03b1 \u03a5\u03c0\u03ac\u03c1\u03c7\u03bf\u03bd\u03c4\u03bf\u03c2 \u0391\u03c0\u03bf\u03b8\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5"
::msgcat::mcset el "Open..." "\u0386\u03bd\u03bf\u03b9\u03b3\u03bc\u03b1..."
::msgcat::mcset el "Recent Repositories" "\u03a0\u03c1\u03cc\u03c3\u03c6\u03b1\u03c4\u03b1 \u0391\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03b1"
::msgcat::mcset el "Open Recent Repository:" "\u0386\u03bd\u03bf\u03b9\u03b3\u03bc\u03b1 \u03a0\u03c1\u03cc\u03c3\u03c6\u03b1\u03c4\u03bf\u03c5 \u0391\u03c0\u03bf\u03b8\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5:"
::msgcat::mcset el "Failed to create repository %s:" "\u0391\u03c0\u03bf\u03c4\u03c5\u03c7\u03af\u03b1 \u03b4\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03af\u03b1\u03c2 \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5 %s:"
::msgcat::mcset el "Directory:" "\u03a6\u03ac\u03ba\u03b5\u03bb\u03bf\u03c2:"
::msgcat::mcset el "Git Repository" "\u0391\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf Git"
::msgcat::mcset el "Directory %s already exists." "\u039f \u03a6\u03ac\u03ba\u03b5\u03bb\u03bf\u03c2 '%s' \u03c5\u03c0\u03ac\u03c1\u03c7\u03b5\u03b9 \u03ae\u03b4\u03b7."
::msgcat::mcset el "File %s already exists." "\u03a4\u03bf \u03b1\u03c1\u03c7\u03b5\u03af\u03bf %s \u03c5\u03c0\u03ac\u03c1\u03c7\u03b5\u03b9 \u03ae\u03b4\u03b7."
::msgcat::mcset el "Clone" "\u039a\u03bb\u03ce\u03bd\u03bf\u03c2"
::msgcat::mcset el "Clone Type:" "\u03a4\u03cd\u03c0\u03bf\u03c2 \u039a\u03bb\u03ce\u03bd\u03bf\u03c5:"
::msgcat::mcset el "Standard (Fast, Semi-Redundant, Hardlinks)" "\u03a4\u03c5\u03c0\u03b9\u03ba\u03cc (\u03a4\u03b1\u03c7\u03cd, \u0397\u03bc\u03b9-\u03a0\u03bb\u03b5\u03bf\u03bd\u03ac\u03b6\u03bf\u03bd, Hardlinks)"
::msgcat::mcset el "Full Copy (Slower, Redundant Backup)" "\u03a0\u03bb\u03ae\u03c1\u03b5\u03c2 \u0391\u03bd\u03c4\u03af\u03b3\u03c1\u03b1\u03c6\u03bf (\u03a0\u03b9\u03bf \u03b1\u03c1\u03b3\u03cc, \u03a0\u03bb\u03b5\u03bf\u03bd\u03ac\u03b6\u03bf\u03bd \u0391\u03bd\u03c4\u03af\u03b3\u03c1\u03b1\u03c6\u03bf \u0391\u03c3\u03c6\u03b1\u03bb\u03b5\u03af\u03b1\u03c2)"
::msgcat::mcset el "Shared (Fastest, Not Recommended, No Backup)" "\u039a\u03bf\u03b9\u03bd\u03ae \u03a7\u03c1\u03ae\u03c3\u03b7 (\u03a4\u03b1\u03c7\u03cd\u03c4\u03b1\u03c4\u03bf, \u0394\u03b5 \u03a3\u03c5\u03bd\u03b9\u03c3\u03c4\u03ac\u03c4\u03b1\u03b9, \u039a\u03b1\u03bd\u03ad\u03bd\u03b1 \u0391\u03bd\u03c4\u03af\u03b3\u03c1\u03b1\u03c6\u03bf \u0391\u03c3\u03c6\u03b1\u03bb\u03b5\u03af\u03b1\u03c2)"
::msgcat::mcset el "Not a Git repository: %s" "\u0394\u03b5\u03bd \u03b5\u03af\u03bd\u03b1\u03b9 \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf Git: %s"
::msgcat::mcset el "Standard only available for local repository." "\"\u03a4\u03c5\u03c0\u03b9\u03ba\u03cc\" \u03b4\u03b9\u03b1\u03b8\u03ad\u03c3\u03b9\u03bc\u03bf \u03bc\u03cc\u03bd\u03bf \u03b3\u03b9\u03b1 \u03c4\u03bf\u03c0\u03b9\u03ba\u03cc \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf."
::msgcat::mcset el "Shared only available for local repository." "\"\u039a\u03bf\u03b9\u03bd\u03ae \u03a7\u03c1\u03ae\u03c3\u03b7\" \u03b4\u03b9\u03b1\u03b8\u03ad\u03c3\u03b9\u03bc\u03b7 \u03bc\u03cc\u03bd\u03bf \u03b3\u03b9\u03b1 \u03c4\u03bf\u03c0\u03b9\u03ba\u03cc \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf."
::msgcat::mcset el "Location %s already exists." "\u0397 \u03a4\u03bf\u03c0\u03bf\u03b8\u03b5\u03c3\u03af\u03b1 %s \u03c5\u03c0\u03ac\u03c1\u03c7\u03b5\u03b9 \u03ae\u03b4\u03b7."
::msgcat::mcset el "Failed to configure origin" "\u0391\u03c0\u03bf\u03c4\u03c5\u03c7\u03af\u03b1 \u03c1\u03cd\u03b8\u03bc\u03b9\u03c3\u03b7\u03c2 \u03c0\u03b7\u03b3\u03ae\u03c2"
::msgcat::mcset el "Counting objects" "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03ba\u03b1\u03c4\u03b1\u03bc\u03ad\u03c4\u03c1\u03b7\u03c3\u03b7 \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03b9\u03bc\u03ad\u03bd\u03c9\u03bd"
::msgcat::mcset el "Unable to copy objects/info/alternates: %s" "\u0391\u03b4\u03c5\u03bd\u03b1\u03bc\u03af\u03b1 \u03b1\u03bd\u03c4\u03b9\u03b3\u03c1\u03b1\u03c6\u03ae\u03c2 \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03b9\u03bc\u03ad\u03bd\u03c9\u03bd/\u03c0\u03bb\u03b7\u03c1\u03bf\u03c6\u03bf\u03c1\u03b9\u03ce\u03bd/\u03b5\u03bd\u03bd\u03b1\u03bb\u03b1\u03ba\u03c4\u03b9\u03ba\u03ce\u03bd: %s"
::msgcat::mcset el "Nothing to clone from %s." "\u03a4\u03af\u03c0\u03bf\u03c4\u03b1 \u03c0\u03c1\u03bf\u03c2 \u03ba\u03bb\u03c9\u03bd\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7 \u03b1\u03c0\u03cc \u03c4\u03bf %s."
::msgcat::mcset el "The 'master' branch has not been initialized." "\u039f \u03ba\u03bb\u03ac\u03b4\u03bf\u03c2 'master' \u03b4\u03b5\u03bd \u03ad\u03c7\u03b5\u03b9 \u03b1\u03c1\u03c7\u03b9\u03ba\u03bf\u03c0\u03bf\u03b9\u03b7\u03b8\u03b5\u03af."
::msgcat::mcset el "Hardlinks are unavailable.  Falling back to copying." "Hardlinks \u03bc\u03b7 \u03b4\u03b9\u03b1\u03b8\u03ad\u03c3\u03b9\u03bc\u03b1. \u039c\u03b5\u03c4\u03ac\u03c0\u03c4\u03c9\u03c3\u03b7 \u03c3\u03b5 \u03b1\u03bd\u03c4\u03b9\u03b3\u03c1\u03b1\u03c6\u03ae."
::msgcat::mcset el "Cloning from %s" "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03ba\u03bb\u03c9\u03bd\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7 \u03b1\u03c0\u03cc \u03c4\u03bf %s"
::msgcat::mcset el "Copying objects" "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03b1\u03bd\u03c4\u03b9\u03b3\u03c1\u03b1\u03c6\u03ae \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03b9\u03bc\u03ad\u03bd\u03c9\u03bd"
::msgcat::mcset el "Unable to copy object: %s" "\u0391\u03b4\u03c5\u03bd\u03b1\u03bc\u03af\u03b1 \u03b1\u03bd\u03c4\u03b9\u03b3\u03c1\u03b1\u03c6\u03ae\u03c2 \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03b9\u03bc\u03ad\u03bd\u03bf\u03c5: %s"
::msgcat::mcset el "Linking objects" "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03c3\u03cd\u03bd\u03b4\u03b5\u03c3\u03b7 \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03b9\u03bc\u03ad\u03bd\u03c9\u03bd"
::msgcat::mcset el "objects" "\u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03af\u03bc\u03b5\u03bd\u03b1"
::msgcat::mcset el "Unable to hardlink object: %s" "\u0391\u03b4\u03c5\u03bd\u03b1\u03bc\u03af\u03b1 hardlink \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03b9\u03bc\u03ad\u03bd\u03bf\u03c5: %s"
::msgcat::mcset el "Cannot fetch branches and objects.  See console output for details." "\u0394\u03b5 \u03bc\u03c0\u03cc\u03c1\u03b5\u03c3\u03b5 \u03bd\u03b1 \u03b3\u03af\u03bd\u03b5\u03b9 \u03b1\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7 \u03ba\u03bb\u03ac\u03b4\u03c9\u03bd \u03ba\u03b1\u03b9 \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03b9\u03bc\u03ad\u03bd\u03c9\u03bd. \u0394\u03b5\u03af\u03c4\u03b5 \u03c4\u03b7\u03bd \u03ad\u03be\u03bf\u03b4\u03bf \u03ba\u03bf\u03bd\u03c3\u03cc\u03bb\u03b1\u03c2 \u03b3\u03b9\u03b1 \u03bb\u03b5\u03c0\u03c4\u03bf\u03bc\u03ad\u03c1\u03b5\u03b9\u03b5\u03c2."
::msgcat::mcset el "Cannot fetch tags.  See console output for details." "\u0394\u03b5 \u03bc\u03c0\u03cc\u03c1\u03b5\u03c3\u03b5 \u03bd\u03b1 \u03b3\u03af\u03bd\u03b5\u03b9 \u03b1\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7 \u03b5\u03c4\u03b9\u03ba\u03b5\u03c4\u03ce\u03bd. \u0394\u03b5\u03af\u03c4\u03b5 \u03c4\u03b7\u03bd \u03ad\u03be\u03bf\u03b4\u03bf \u03ba\u03bf\u03bd\u03c3\u03cc\u03bb\u03b1\u03c2 \u03b3\u03b9\u03b1 \u03bb\u03b5\u03c0\u03c4\u03bf\u03bc\u03ad\u03c1\u03b5\u03b9\u03b5\u03c2."
::msgcat::mcset el "Cannot determine HEAD.  See console output for details." "\u0394\u03b5 \u03bc\u03c0\u03cc\u03c1\u03b5\u03c3\u03b5 \u03bd\u03b1 \u03b3\u03af\u03bd\u03b5\u03b9 \u03ba\u03b1\u03b8\u03bf\u03c1\u03b9\u03c3\u03bc\u03cc\u03c2 \u03c4\u03bf\u03c5 HEAD (\u03c0\u03b1\u03c1\u03b1\u03ba\u03bb\u03b1\u03b4\u03b9\u03bf\u03cd). \u0394\u03b5\u03af\u03c4\u03b5 \u03c4\u03b7\u03bd \u03ad\u03be\u03bf\u03b4\u03bf \u03ba\u03bf\u03bd\u03c3\u03cc\u03bb\u03b1\u03c2 \u03b3\u03b9\u03b1 \u03bb\u03b5\u03c0\u03c4\u03bf\u03bc\u03ad\u03c1\u03b5\u03b9\u03b5\u03c2."
::msgcat::mcset el "Unable to cleanup %s" "\u0391\u03b4\u03c5\u03bd\u03b1\u03bc\u03af\u03b1 \u03b5\u03ba\u03ba\u03b1\u03b8\u03ac\u03c1\u03b9\u03c3\u03b7\u03c2 %s"
::msgcat::mcset el "Clone failed." "\u0391\u03c0\u03bf\u03c4\u03c5\u03c7\u03af\u03b1 \u03ba\u03bb\u03c9\u03bd\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7\u03c2."
::msgcat::mcset el "No default branch obtained." "\u0394\u03b5\u03bd \u03ad\u03b3\u03b9\u03bd\u03b5 \u03b1\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7 \u03c0\u03c1\u03bf\u03b5\u03c0\u03b9\u03bb\u03b5\u03b3\u03bc\u03ad\u03bd\u03bf\u03c5 \u03ba\u03bb\u03ac\u03b4\u03bf\u03c5."
::msgcat::mcset el "Cannot resolve %s as a commit." "\u0394\u03b5 \u03bc\u03c0\u03cc\u03c1\u03b5\u03c3\u03b5 \u03bd\u03b1 \u03b5\u03c0\u03b9\u03bb\u03c5\u03b8\u03b5\u03af \u03c4\u03bf %s \u03c9\u03c2 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae."
::msgcat::mcset el "Creating working directory" "\u0394\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03af\u03b1 \u03c6\u03b1\u03ba\u03ad\u03bb\u03bf\u03c5 \u03b5\u03c1\u03b3\u03b1\u03c3\u03af\u03b1\u03c2"
::msgcat::mcset el "files" "\u03b1\u03c1\u03c7\u03b5\u03af\u03b1"
::msgcat::mcset el "Initial file checkout failed." "\u0397 \u03b1\u03c1\u03c7\u03b9\u03ba\u03ae \u03b5\u03be\u03b1\u03b3\u03c9\u03b3\u03ae \u03b1\u03c1\u03c7\u03b5\u03af\u03bf\u03c5 \u03b1\u03c0\u03ad\u03c4\u03c5\u03c7\u03b5."
::msgcat::mcset el "Open" "\u0386\u03bd\u03bf\u03b9\u03b3\u03bc\u03b1"
::msgcat::mcset el "Repository:" "\u0391\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf:"
::msgcat::mcset el "Failed to open repository %s:" "\u0391\u03c0\u03bf\u03c4\u03c5\u03c7\u03af\u03b1 \u03b1\u03bd\u03bf\u03af\u03b3\u03bc\u03b1\u03c4\u03bf\u03c2 \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5 %s:"
::msgcat::mcset el "Revision Expression:" "\u0388\u03ba\u03c6\u03c1\u03b1\u03c3\u03b7 \u0391\u03bd\u03b1\u03b8\u03b5\u03ce\u03c1\u03b7\u03c3\u03b7\u03c2:"
::msgcat::mcset el "Local Branch" "\u03a4\u03bf\u03c0\u03b9\u03ba\u03cc\u03c2 \u039a\u03bb\u03ac\u03b4\u03bf\u03c2"
::msgcat::mcset el "Tracking Branch" "\u039a\u03bb\u03ac\u03b4\u03bf\u03c2 \u03a0\u03b1\u03c1\u03b1\u03ba\u03bf\u03bb\u03bf\u03cd\u03b8\u03b7\u03c3\u03b7\u03c2"
::msgcat::mcset el "Tag" "\u0395\u03c4\u03b9\u03ba\u03ad\u03c4\u03b1"
::msgcat::mcset el "Invalid revision: %s" "\u039c\u03b7 \u03ad\u03b3\u03ba\u03c5\u03c1\u03b7 \u03b1\u03bd\u03b1\u03b8\u03b5\u03ce\u03c1\u03b7\u03c3\u03b7: %s"
::msgcat::mcset el "No revision selected." "\u0394\u03b5\u03bd \u03ad\u03c7\u03b5\u03b9 \u03b5\u03c0\u03b9\u03bb\u03b5\u03b3\u03b5\u03af \u03b1\u03bd\u03b1\u03b8\u03b5\u03ce\u03c1\u03b7\u03c3\u03b7."
::msgcat::mcset el "Revision expression is empty." "\u0397 \u03ad\u03ba\u03c6\u03c1\u03b1\u03c3\u03b7 \u03b1\u03bd\u03b1\u03b8\u03b5\u03ce\u03c1\u03b7\u03c3\u03b7\u03c2 \u03b5\u03af\u03bd\u03b1\u03b9 \u03ba\u03b5\u03bd\u03ae."
::msgcat::mcset el "Updated" "\u0395\u03bd\u03b7\u03bc\u03b5\u03c1\u03c9\u03bc\u03ad\u03bd\u03bf"
::msgcat::mcset el "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "\u0394\u03b5\u03bd \u03c5\u03c0\u03ac\u03c1\u03c7\u03b5\u03b9 \u03ba\u03ac\u03c4\u03b9 \u03c0\u03c1\u03bf\u03c2 \u03b4\u03b9\u03cc\u03c1\u03b8\u03c9\u03c3\u03b7.\n\n\u03a0\u03c1\u03cc\u03ba\u03b5\u03b9\u03c4\u03b1\u03b9 \u03bd\u03b1 \u03b4\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03ae\u03c3\u03b5\u03c4\u03b5 \u03c4\u03b7\u03bd \u03b1\u03c1\u03c7\u03b9\u03ba\u03ae \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae. \u0394\u03b5\u03bd \u03c5\u03c0\u03ac\u03c1\u03c7\u03b5\u03b9 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae \u03c0\u03c1\u03b9\u03bd \u03b1\u03c0\u03cc \u03b1\u03c5\u03c4\u03ae \u03b3\u03b9\u03b1 \u03bd\u03b1 \u03b4\u03b9\u03bf\u03c1\u03b8\u03ce\u03c3\u03b5\u03c4\u03b5.\n"
::msgcat::mcset el "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "\u0394\u03b5 \u03b3\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03b4\u03b9\u03cc\u03c1\u03b8\u03c9\u03c3\u03b7 \u03ba\u03b1\u03b8\u03ce\u03c2 \u03c3\u03c5\u03b3\u03c7\u03c9\u03bd\u03b5\u03cd\u03b5\u03c4\u03b5.\n\n\u0392\u03c1\u03af\u03c3\u03ba\u03b5\u03c3\u03c4\u03b5 \u03c3\u03c4\u03bf \u03bc\u03ad\u03c3\u03bf \u03bc\u03b9\u03b1\u03c2 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2 \u03c0\u03bf\u03c5 \u03b4\u03b5\u03bd \u03ad\u03c7\u03b5\u03b9 \u03bf\u03bb\u03bf\u03ba\u03bb\u03b7\u03c1\u03c9\u03b8\u03b5\u03af. \u0394\u03b5 \u03bc\u03c0\u03bf\u03c1\u03b5\u03af\u03c4\u03b5 \u03bd\u03b1 \u03b4\u03b9\u03bf\u03c1\u03b8\u03ce\u03c3\u03b5\u03c4\u03b5 \u03c4\u03b7\u03bd \u03c0\u03c1\u03bf\u03b7\u03b3\u03bf\u03cd\u03bc\u03b5\u03bd\u03b7 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae \u03b5\u03ba\u03c4\u03cc\u03c2 \u03b5\u03ac\u03bd \u03b1\u03ba\u03c5\u03c1\u03ce\u03c3\u03b5\u03c4\u03b5 \u03c4\u03b7\u03bd \u03c4\u03c1\u03ad\u03c7\u03bf\u03c5\u03c3\u03b1 \u03b5\u03bd\u03ad\u03c1\u03b3\u03b5\u03b9\u03b1 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2.\n"
::msgcat::mcset el "Error loading commit data for amend:" "\u03a3\u03c6\u03ac\u03bb\u03bc\u03b1 \u03c6\u03cc\u03c1\u03c4\u03c9\u03c3\u03b7\u03c2 \u03b4\u03b5\u03b4\u03bf\u03bc\u03ad\u03bd\u03c9\u03bd \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2 \u03c0\u03c1\u03bf\u03c2 \u03b4\u03b9\u03cc\u03c1\u03b8\u03c9\u03c3\u03b7:"
::msgcat::mcset el "Unable to obtain your identity:" "\u0391\u03b4\u03c5\u03bd\u03b1\u03bc\u03af\u03b1 \u03b1\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7\u03c2 \u03c4\u03b7\u03c2 \u03c4\u03b1\u03c5\u03c4\u03cc\u03c4\u03b7\u03c4\u03ac\u03c2 \u03c3\u03b1\u03c2:"
::msgcat::mcset el "Invalid GIT_COMMITTER_IDENT:" "\u039c\u03b7 \u03ad\u03b3\u03ba\u03c5\u03c1\u03bf GIT_COMMITTER_IDENT:"
::msgcat::mcset el "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before another commit can be created.\n\nThe rescan will be automatically started now.\n" "\u0397 \u03c4\u03b5\u03bb\u03b5\u03c5\u03c4\u03b1\u03af\u03b1 \u03ba\u03b1\u03c4\u03ac\u03c3\u03c4\u03b1\u03c3\u03b7 \u03c0\u03bf\u03c5 \u03b1\u03bd\u03b9\u03c7\u03bd\u03b5\u03cd\u03b8\u03b7\u03ba\u03b5 \u03b4\u03b5 \u03c3\u03c5\u03bc\u03c6\u03c9\u03bd\u03b5\u03af \u03bc\u03b5 \u03c4\u03b7\u03bd \u03ba\u03b1\u03c4\u03ac\u03c3\u03c4\u03b1\u03c3\u03b7 \u03c4\u03bf\u03c5 \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5.\n\n\u039a\u03ac\u03c0\u03bf\u03b9\u03bf \u03ac\u03bb\u03bb\u03bf \u03c0\u03c1\u03cc\u03b3\u03c1\u03b1\u03bc\u03bc\u03b1 Git \u03c4\u03c1\u03bf\u03c0\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b5 \u03c4\u03bf \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf \u03b1\u03c0\u03cc \u03c4\u03b7\u03bd \u03c4\u03b5\u03bb\u03b5\u03c5\u03c4\u03b1\u03af\u03b1 \u03b1\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7. \u03a0\u03c1\u03ad\u03c0\u03b5\u03b9 \u03bd\u03b1 \u03b3\u03af\u03bd\u03b5\u03b9 \u03b5\u03c0\u03b1\u03bd\u03b1\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7 \u03c0\u03c1\u03b9\u03bd \u03c4\u03b7 \u03b4\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03af\u03b1 \u03bd\u03ad\u03b1\u03c2 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2.\n\n\u0397 \u03b5\u03c0\u03b1\u03bd\u03b1\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7 \u03b8\u03b1 \u03be\u03b5\u03ba\u03b9\u03bd\u03ae\u03c3\u03b5\u03b9 \u03b1\u03c5\u03c4\u03cc\u03bc\u03b1\u03c4\u03b1 \u03c4\u03ce\u03c1\u03b1.\n"
::msgcat::mcset el "Unmerged files cannot be committed.\n\nFile %s has merge conflicts.  You must resolve them and stage the file before committing.\n" "\u03a4\u03b1 \u03bc\u03b7 \u03c3\u03c5\u03b3\u03c7\u03c9\u03bd\u03b5\u03c5\u03bc\u03ad\u03bd\u03b1 \u03b1\u03c1\u03c7\u03b5\u03af\u03b1 \u03b4\u03b5 \u03bc\u03c0\u03bf\u03c1\u03bf\u03cd\u03bd \u03bd\u03b1 \u03c5\u03c0\u03bf\u03b2\u03bb\u03b7\u03b8\u03bf\u03cd\u03bd.\n\n\u03a4\u03bf \u03b1\u03c1\u03c7\u03b5\u03af\u03bf %s \u03ad\u03c7\u03b5\u03b9 \u03c3\u03c5\u03b3\u03ba\u03c1\u03bf\u03cd\u03c3\u03b5\u03b9\u03c2 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2. \u03a0\u03c1\u03ad\u03c0\u03b5\u03b9 \u03bd\u03b1 \u03c4\u03b9\u03c2 \u03b5\u03c0\u03b9\u03bb\u03cd\u03c3\u03b5\u03c4\u03b5 \u03ba\u03b1\u03b9 \u03bd\u03b1 \u03c3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03b9\u03ae\u03c3\u03b5\u03c4\u03b5 \u03c4\u03bf \u03b1\u03c1\u03c7\u03b5\u03af\u03bf \u03c0\u03c1\u03b9\u03bd \u03c4\u03b7\u03bd \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae.\n"
::msgcat::mcset el "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "\u0386\u03b3\u03bd\u03c9\u03c3\u03c4\u03b7 \u03ba\u03b1\u03c4\u03ac\u03c3\u03c4\u03b1\u03c3\u03b7 \u03b1\u03c1\u03c7\u03b5\u03af\u03bf\u03c5 %s \u03b1\u03bd\u03b9\u03c7\u03bd\u03b5\u03cd\u03b8\u03b7\u03ba\u03b5.\n\n\u03a4\u03bf \u03b1\u03c1\u03c7\u03b5\u03af\u03bf %s \u03b4\u03b5 \u03bc\u03c0\u03bf\u03c1\u03b5\u03af \u03bd\u03b1 \u03c5\u03c0\u03bf\u03b2\u03bb\u03b7\u03b8\u03b5\u03af \u03b1\u03c0\u03cc \u03b1\u03c5\u03c4\u03cc \u03c4\u03bf \u03c0\u03c1\u03cc\u03b3\u03c1\u03b1\u03bc\u03bc\u03b1.\n"
::msgcat::mcset el "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "\u0394\u03b5\u03bd \u03c5\u03c0\u03ac\u03c1\u03c7\u03bf\u03c5\u03bd \u03b1\u03bb\u03bb\u03b1\u03b3\u03ad\u03c2 \u03c0\u03c1\u03bf\u03c2 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae.\n\n \u03a0\u03c1\u03ad\u03c0\u03b5\u03b9 \u03bd\u03b1 \u03c3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03b9\u03ae\u03c3\u03b5\u03c4\u03b5 \u03c4\u03bf\u03c5\u03bb\u03ac\u03c7\u03b9\u03c3\u03c4\u03bf\u03bd 1 \u03b1\u03c1\u03c7\u03b5\u03af\u03bf \u03c0\u03c1\u03b9\u03bd \u03bd\u03b1 \u03ba\u03ac\u03bd\u03b5\u03c4\u03b5 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae.\n"
::msgcat::mcset el "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "\u03a0\u03b1\u03c1\u03b1\u03ba\u03b1\u03bb\u03ce \u03b4\u03ce\u03c3\u03c4\u03b5 \u03ad\u03bd\u03b1 \u03bc\u03ae\u03bd\u03c5\u03bc\u03b1 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2.\n\n\u0388\u03bd\u03b1 \u03c3\u03c9\u03c3\u03c4\u03cc \u03bc\u03ae\u03bd\u03c5\u03bc\u03b1 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2 \u03ad\u03c7\u03b5\u03b9 \u03c4\u03b7\u03bd \u03b5\u03be\u03ae\u03c2 \u03bc\u03bf\u03c1\u03c6\u03ae:\n\n- \u03a0\u03c1\u03ce\u03c4\u03b7 \u03b3\u03c1\u03b1\u03bc\u03bc\u03ae: \u03a0\u03b5\u03c1\u03b9\u03b3\u03c1\u03b1\u03c6\u03ae \u03c3\u03b5 \u03bc\u03af\u03b1 \u03c0\u03c1\u03cc\u03c4\u03b1\u03c3\u03b7 \u03c4\u03bf\u03c5 \u03c4\u03b9 \u03ba\u03ac\u03bd\u03b1\u03c4\u03b5.\n- \u0394\u03b5\u03cd\u03c4\u03b5\u03c1\u03b7 \u03b3\u03c1\u03b1\u03bc\u03bc\u03ae: \u039a\u03b5\u03bd\u03ae\n- \u03a5\u03c0\u03cc\u03bb\u03bf\u03b9\u03c0\u03b5\u03c2 \u03b3\u03c1\u03b1\u03bc\u03bc\u03ad\u03c2: \u03a0\u03b5\u03c1\u03b9\u03b3\u03c1\u03b1\u03c6\u03ae \u03c4\u03bf\u03c5 \u03b3\u03b9\u03b1\u03c4\u03af \u03b1\u03c5\u03c4\u03ae \u03b7 \u03b1\u03bb\u03bb\u03b1\u03b3\u03ae \u03b5\u03af\u03bd\u03b1\u03b9 \u03c3\u03c9\u03c3\u03c4\u03ae.\n"
::msgcat::mcset el "warning: Tcl does not support encoding '%s'." "\u03c0\u03c1\u03bf\u03b5\u03b9\u03b4\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7: H Tcl \u03b4\u03b5\u03bd \u03c5\u03c0\u03bf\u03c3\u03c4\u03b7\u03c1\u03af\u03b6\u03b5\u03b9 \u03c4\u03b7\u03bd \u03ba\u03c9\u03b4\u03b9\u03ba\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7 '%s'."
::msgcat::mcset el "Calling pre-commit hook..." "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03ba\u03bb\u03ae\u03c3\u03b7 \u03c4\u03bf\u03c5 pre-commit hook..."
::msgcat::mcset el "Commit declined by pre-commit hook." "\u0397 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae \u03b1\u03c0\u03bf\u03c1\u03c1\u03af\u03c6\u03b8\u03b7\u03ba\u03b5 \u03b1\u03c0\u03cc \u03c4\u03bf pre-commit hook."
::msgcat::mcset el "Calling commit-msg hook..." "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03ba\u03bb\u03ae\u03c3\u03b7 \u03c4\u03bf\u03c5 commit-msg hook..."
::msgcat::mcset el "Commit declined by commit-msg hook." "\u0397 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae \u03b1\u03c0\u03bf\u03c1\u03c1\u03af\u03c6\u03b8\u03b7\u03ba\u03b5 \u03b1\u03c0\u03cc \u03c4\u03bf commit-msg hook."
::msgcat::mcset el "Committing changes..." "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae \u03c4\u03c9\u03bd \u03b1\u03bb\u03bb\u03b1\u03b3\u03ce\u03bd..."
::msgcat::mcset el "write-tree failed:" "\u03c4\u03bf write-tree \u03b1\u03c0\u03ad\u03c4\u03c5\u03c7\u03b5:"
::msgcat::mcset el "Commit failed." "\u0397 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae \u03b1\u03c0\u03ad\u03c4\u03c5\u03c7\u03b5."
::msgcat::mcset el "Commit %s appears to be corrupt" "\u0397 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae %s \u03b4\u03b5\u03af\u03c7\u03bd\u03b5\u03b9 \u03ba\u03b1\u03c4\u03b5\u03c3\u03c4\u03c1\u03b1\u03bc\u03bc\u03ad\u03bd\u03b7"
::msgcat::mcset el "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "\u0394\u03b5\u03bd \u03c5\u03c0\u03ac\u03c1\u03c7\u03bf\u03c5\u03bd \u03b1\u03bb\u03bb\u03b1\u03b3\u03ad\u03c2 \u03c0\u03c1\u03bf\u03c2 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae.\n\n\u0394\u03b5\u03bd \u03c4\u03c1\u03bf\u03c0\u03bf\u03c0\u03bf\u03b9\u03ae\u03b8\u03b7\u03ba\u03b1\u03bd \u03b1\u03c1\u03c7\u03b5\u03af\u03b1 \u03b1\u03c0\u03cc \u03b1\u03c5\u03c4\u03ae \u03c4\u03b7\u03bd \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae \u03ba\u03b1\u03b9 \u03b4\u03b5\u03bd \u03ae\u03c4\u03b1\u03bd \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2.\n\n\u0398\u03b1 \u03be\u03b5\u03ba\u03b9\u03bd\u03ae\u03c3\u03b5\u03b9 \u03b1\u03c5\u03c4\u03cc\u03bc\u03b1\u03c4\u03b1 \u03b5\u03c0\u03b1\u03bd\u03b1\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7 \u03c4\u03ce\u03c1\u03b1.\n"
::msgcat::mcset el "No changes to commit." "\u0394\u03b5\u03bd \u03c5\u03c0\u03ac\u03c1\u03c7\u03bf\u03c5\u03bd \u03b1\u03bb\u03bb\u03b1\u03b3\u03ad\u03c2 \u03c0\u03c1\u03bf\u03c2 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae."
::msgcat::mcset el "commit-tree failed:" "\u03c4\u03bf commit-tree \u03b1\u03c0\u03ad\u03c4\u03c5\u03c7\u03b5:"
::msgcat::mcset el "update-ref failed:" "\u03c4\u03bf update-ref \u03b1\u03c0\u03ad\u03c4\u03c5\u03c7\u03b5:"
::msgcat::mcset el "Created commit %s: %s" "\u0394\u03b7\u03bc\u03b9\u03bf\u03c5\u03c1\u03b3\u03ae\u03b8\u03b7\u03ba\u03b5 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae %s: %s"
::msgcat::mcset el "Working... please wait..." "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03b5\u03c1\u03b3\u03b1\u03c3\u03af\u03b1... \u03a0\u03b1\u03c1\u03b1\u03ba\u03b1\u03bb\u03ce \u03c0\u03b5\u03c1\u03b9\u03bc\u03ad\u03bd\u03b5\u03c4\u03b5..."
::msgcat::mcset el "Success" "\u0395\u03c0\u03b9\u03c4\u03c5\u03c7\u03af\u03b1"
::msgcat::mcset el "Error: Command Failed" "\u03a3\u03c6\u03ac\u03bb\u03bc\u03b1: \u0397 \u0395\u03bd\u03c4\u03bf\u03bb\u03ae \u0391\u03c0\u03ad\u03c4\u03c5\u03c7\u03b5"
::msgcat::mcset el "Number of loose objects" "\u0391\u03c1\u03b9\u03b8\u03bc\u03cc\u03c2 \u03b5\u03bb\u03b5\u03cd\u03b8\u03b5\u03c1\u03c9\u03bd \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03b9\u03bc\u03ad\u03bd\u03c9\u03bd"
::msgcat::mcset el "Disk space used by loose objects" "\u03a7\u03ce\u03c1\u03bf\u03c2 \u03ba\u03b1\u03c4\u03b5\u03b9\u03bb\u03bb\u03b7\u03bc\u03ad\u03bd\u03bf\u03c2 \u03b1\u03c0\u03cc \u03b5\u03bb\u03b5\u03cd\u03b8\u03b5\u03c1\u03b1 \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03af\u03bc\u03b5\u03bd\u03b1"
::msgcat::mcset el "Number of packed objects" "\u0391\u03c1\u03b9\u03b8\u03bc\u03cc\u03c2 \u03c0\u03b1\u03ba\u03b5\u03c4\u03b1\u03c1\u03b9\u03c3\u03bc\u03ad\u03bd\u03c9\u03bd \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03b9\u03bc\u03ad\u03bd\u03c9\u03bd"
::msgcat::mcset el "Number of packs" "\u0391\u03c1\u03b9\u03b8\u03bc\u03cc\u03c2 \u03c0\u03b1\u03ba\u03ad\u03c4\u03c9\u03bd"
::msgcat::mcset el "Disk space used by packed objects" "\u03a7\u03ce\u03c1\u03bf\u03c2 \u03ba\u03b1\u03c4\u03b5\u03b9\u03bb\u03bb\u03b7\u03bc\u03ad\u03bd\u03bf\u03c2 \u03b1\u03c0\u03cc \u03c0\u03b1\u03ba\u03b5\u03c4\u03b1\u03c1\u03b9\u03c3\u03bc\u03ad\u03bd\u03b1 \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03af\u03bc\u03b5\u03bd\u03b1"
::msgcat::mcset el "Packed objects waiting for pruning" "\u03a0\u03b1\u03ba\u03b5\u03c4\u03b1\u03c1\u03b9\u03c3\u03bc\u03ad\u03bd\u03b1 \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03af\u03bc\u03b5\u03bd\u03b1 \u03ad\u03c4\u03bf\u03b9\u03bc\u03b1 \u03b3\u03b9\u03b1 \u03ba\u03bb\u03ac\u03b4\u03b5\u03bc\u03b1"
::msgcat::mcset el "Garbage files" "\u0386\u03c7\u03c1\u03b7\u03c3\u03c4\u03b1 \u03b1\u03c1\u03c7\u03b5\u03af\u03b1"
::msgcat::mcset el "Compressing the object database" "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03c3\u03c5\u03bc\u03c0\u03af\u03b5\u03c3\u03b7 \u03c4\u03b7\u03c2 \u03b2\u03ac\u03c3\u03b7\u03c2 \u03b4\u03b5\u03b4\u03bf\u03bc\u03ad\u03bd\u03c9\u03bd \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03b9\u03bc\u03ad\u03bd\u03c9\u03bd"
::msgcat::mcset el "Verifying the object database with fsck-objects" "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03b5\u03c0\u03b1\u03bb\u03ae\u03b8\u03b5\u03c5\u03c3\u03b7 \u03c4\u03b7\u03c2 \u03b2\u03ac\u03c3\u03b7\u03c2 \u03b4\u03b5\u03b4\u03bf\u03bc\u03ad\u03bd\u03c9\u03bd \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03b9\u03bc\u03ad\u03bd\u03c9\u03bd \u03bc\u03b5 \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03af\u03bc\u03b5\u03bd\u03b1 fsck"
::msgcat::mcset el "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database when more than %i loose objects exist.\n\nCompress the database now?" "\u0391\u03c5\u03c4\u03cc \u03c4\u03bf \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf \u03ad\u03c7\u03b5\u03b9 \u03b1\u03c5\u03c4\u03ae \u03c4\u03b7 \u03c3\u03c4\u03b9\u03b3\u03bc\u03ae \u03c0\u03b5\u03c1\u03af\u03c0\u03bf\u03c5 %i \u03b5\u03bb\u03b5\u03cd\u03b8\u03b5\u03c1\u03b1 \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03af\u03bc\u03b5\u03bd\u03b1.\n\n\u0393\u03b9\u03b1 \u03c4\u03b7 \u03b4\u03b9\u03b1\u03c4\u03ae\u03c1\u03b7\u03c3\u03b7 \u03b2\u03ad\u03bb\u03c4\u03b9\u03c3\u03c4\u03c9\u03bd \u03b5\u03c0\u03b9\u03b4\u03cc\u03c3\u03b5\u03c9\u03bd \u03c3\u03c5\u03bd\u03b9\u03c3\u03c4\u03ac\u03c4\u03b1\u03b9 \u03bd\u03b1 \u03c3\u03c5\u03bc\u03c0\u03b9\u03ad\u03c3\u03b5\u03c4\u03b5 \u03c4\u03b7 \u03b2\u03ac\u03c3\u03b7 \u03b4\u03b5\u03b4\u03bf\u03bc\u03ad\u03bd\u03c9\u03bd \u03cc\u03c4\u03b1\u03bd \u03c5\u03c0\u03ac\u03c1\u03c7\u03bf\u03c5\u03bd \u03c0\u03b5\u03c1\u03b9\u03c3\u03c3\u03cc\u03c4\u03b5\u03c1\u03b1 \u03b1\u03c0\u03cc %i \u03b5\u03bb\u03b5\u03cd\u03b8\u03b5\u03c1\u03b1 \u03b1\u03bd\u03c4\u03b9\u03ba\u03b5\u03af\u03bc\u03b5\u03bd\u03b1.\n\n\u03a3\u03c5\u03bc\u03c0\u03af\u03b5\u03c3\u03b7 \u03c4\u03b7\u03c2 \u03b2\u03ac\u03c3\u03b7\u03c2 \u03b4\u03b5\u03b4\u03bf\u03bc\u03ad\u03bd\u03c9\u03bd \u03c4\u03ce\u03c1\u03b1;"
::msgcat::mcset el "Invalid date from Git: %s" "\u039c\u03b7 \u03ad\u03b3\u03ba\u03c5\u03c1\u03b7 \u03b7\u03bc\u03b5\u03c1\u03bf\u03bc\u03b7\u03bd\u03af\u03b1 \u03b1\u03c0\u03cc \u03c4\u03bf Git: %s"
::msgcat::mcset el "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "\u0394\u03b5\u03bd \u03b1\u03bd\u03b9\u03c7\u03bd\u03b5\u03cd\u03b8\u03b7\u03ba\u03b1\u03bd \u03b4\u03b9\u03b1\u03c6\u03bf\u03c1\u03ad\u03c2.\n\n\u03a4\u03bf %s \u03b4\u03b5\u03bd \u03ad\u03c7\u03b5\u03b9 \u03b1\u03bb\u03bb\u03b1\u03b3\u03ad\u03c2.\n\u0397 \u03b7\u03bc\u03b5\u03c1\u03bf\u03bc\u03b7\u03bd\u03af\u03b1 \u03c4\u03c1\u03bf\u03c0\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7\u03c2 \u03b1\u03c5\u03c4\u03bf\u03cd \u03c4\u03bf\u03c5 \u03b1\u03c1\u03c7\u03b5\u03af\u03bf\u03c5 \u03b5\u03bd\u03b7\u03bc\u03b5\u03c1\u03ce\u03b8\u03b7\u03ba\u03b5 \u03b1\u03c0\u03cc \u03ac\u03bb\u03bb\u03b7 \u03b5\u03c6\u03b1\u03c1\u03bc\u03bf\u03b3\u03ae, \u03b1\u03bb\u03bb\u03ac \u03c4\u03bf \u03c0\u03b5\u03c1\u03b9\u03b5\u03c7\u03cc\u03bc\u03b5\u03bd\u03bf \u03c4\u03bf\u03c5 \u03b1\u03c1\u03c7\u03b5\u03af\u03bf\u03c5 \u03b4\u03b5\u03bd \u03ac\u03bb\u03bb\u03b1\u03be\u03b5.\n\n\u0398\u03b1 \u03be\u03b5\u03ba\u03b9\u03bd\u03ae\u03c3\u03b5\u03b9 \u03b1\u03c5\u03c4\u03cc\u03bc\u03b1\u03c4\u03b1 \u03b5\u03c0\u03b1\u03bd\u03b1\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7 \u03b3\u03b9\u03b1 \u03bd\u03b1 \u03b2\u03c1\u03b5\u03b8\u03bf\u03cd\u03bd \u03ac\u03bb\u03bb\u03b1 \u03b1\u03c1\u03c7\u03b5\u03af\u03b1 \u03c0\u03bf\u03c5 \u03bc\u03c0\u03bf\u03c1\u03b5\u03af \u03bd\u03b1 \u03b2\u03c1\u03af\u03c3\u03ba\u03bf\u03bd\u03c4\u03b1\u03b9 \u03c3\u03b5 \u03af\u03b4\u03b9\u03b1 \u03ba\u03b1\u03c4\u03ac\u03c3\u03c4\u03b1\u03c3\u03b7."
::msgcat::mcset el "Loading diff of %s..." "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03c6\u03cc\u03c1\u03c4\u03c9\u03c3\u03b7 \u03b4\u03b9\u03b1\u03c6\u03bf\u03c1\u03ac\u03c2 \u03c4\u03bf\u03c5 %s..."
::msgcat::mcset el "Unable to display %s" "\u0394\u03b5\u03bd \u03b5\u03af\u03bd\u03b1\u03b9 \u03b4\u03c5\u03bd\u03b1\u03c4\u03ae \u03b7 \u03c0\u03c1\u03bf\u03b2\u03bf\u03bb\u03ae \u03c4\u03bf\u03c5 %s"
::msgcat::mcset el "Error loading file:" "\u03a3\u03c6\u03ac\u03bb\u03bc\u03b1 \u03c6\u03cc\u03c1\u03c4\u03c9\u03c3\u03b7\u03c2 \u03b1\u03c1\u03c7\u03b5\u03af\u03bf\u03c5:"
::msgcat::mcset el "Git Repository (subproject)" "\u0391\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf Git (\u03b8\u03c5\u03b3\u03b1\u03c4\u03c1\u03b9\u03ba\u03cc \u03ad\u03c1\u03b3\u03bf)"
::msgcat::mcset el "* Binary file (not showing content)." "* \u0394\u03c5\u03b1\u03b4\u03b9\u03ba\u03cc \u03b1\u03c1\u03c7\u03b5\u03af\u03bf (\u03bc\u03b7 \u03b5\u03bc\u03c6\u03ac\u03bd\u03b9\u03c3\u03b7 \u03c0\u03b5\u03c1\u03b9\u03b5\u03c7\u03bf\u03bc\u03ad\u03bd\u03bf\u03c5)."
::msgcat::mcset el "Error loading diff:" "\u03a3\u03c6\u03ac\u03bb\u03bc\u03b1 \u03c6\u03cc\u03c1\u03c4\u03c9\u03c3\u03b7\u03c2 \u03b4\u03b9\u03b1\u03c6\u03bf\u03c1\u03ac\u03c2:"
::msgcat::mcset el "Failed to unstage selected hunk." "\u0391\u03c0\u03bf\u03c4\u03c5\u03c7\u03af\u03b1 \u03b1\u03c0\u03bf\u03c3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7\u03c2 \u03b5\u03c0\u03b9\u03bb\u03b5\u03b3\u03bc\u03ad\u03bd\u03bf\u03c5 \u03ba\u03bf\u03bc\u03bc\u03b1\u03c4\u03b9\u03bf\u03cd."
::msgcat::mcset el "Failed to stage selected hunk." "\u0391\u03c0\u03bf\u03c4\u03c5\u03c7\u03af\u03b1 \u03c3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7\u03c2 \u03b5\u03c0\u03b9\u03bb\u03b5\u03b3\u03bc\u03ad\u03bd\u03bf\u03c5 \u03ba\u03bf\u03bc\u03bc\u03b1\u03c4\u03b9\u03bf\u03cd."
::msgcat::mcset el "error" "\u03c3\u03c6\u03ac\u03bb\u03bc\u03b1"
::msgcat::mcset el "warning" "\u03c0\u03c1\u03bf\u03b5\u03b9\u03b4\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7"
::msgcat::mcset el "You must correct the above errors before committing." "\u03a0\u03c1\u03ad\u03c0\u03b5\u03b9 \u03bd\u03b1 \u03b4\u03b9\u03bf\u03c1\u03b8\u03ce\u03c3\u03b5\u03c4\u03b5 \u03c4\u03b1 \u03c0\u03b1\u03c1\u03b1\u03c0\u03ac\u03bd\u03c9 \u03bb\u03ac\u03b8\u03b7 \u03c0\u03c1\u03b9\u03bd \u03c4\u03b7\u03bd \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae."
::msgcat::mcset el "Unable to unlock the index." "\u0391\u03b4\u03c5\u03bd\u03b1\u03bc\u03af\u03b1 \u03be\u03b5\u03ba\u03bb\u03b5\u03b9\u03b4\u03ce\u03bc\u03b1\u03c4\u03bf\u03c2 \u03c4\u03bf\u03c5 \u03b5\u03c5\u03c1\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5."
::msgcat::mcset el "Index Error" "\u03a3\u03c6\u03ac\u03bb\u03bc\u03b1 \u0395\u03c5\u03c1\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5"
::msgcat::mcset el "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "\u0397 \u03b5\u03bd\u03b7\u03bc\u03ad\u03c1\u03c9\u03c3\u03b7 \u03c4\u03bf\u03c5 \u03b5\u03c5\u03c1\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5 Git \u03b1\u03c0\u03ad\u03c4\u03c5\u03c7\u03b5. \u0398\u03b1 \u03be\u03b5\u03ba\u03b9\u03bd\u03ae\u03c3\u03b5\u03b9 \u03b1\u03c5\u03c4\u03cc\u03bc\u03b1\u03c4\u03b1 \u03b5\u03c0\u03b1\u03bd\u03b1\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7 \u03b3\u03b9\u03b1 \u03b5\u03c0\u03b1\u03bd\u03b1\u03c3\u03c5\u03b3\u03c7\u03c1\u03bf\u03bd\u03b9\u03c3\u03bc\u03cc \u03c4\u03bf\u03c5 git-gui."
::msgcat::mcset el "Continue" "\u03a3\u03c5\u03bd\u03ad\u03c7\u03b5\u03b9\u03b1"
::msgcat::mcset el "Unlock Index" "\u039e\u03b5\u03ba\u03bb\u03b5\u03af\u03b4\u03c9\u03bc\u03b1 \u0395\u03c5\u03c1\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5"
::msgcat::mcset el "Unstaging %s from commit" "\u0391\u03c0\u03bf\u03c3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b7 %s \u03b1\u03c0\u03cc \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae"
::msgcat::mcset el "Ready to commit." "\u0388\u03c4\u03bf\u03b9\u03bc\u03bf \u03c0\u03c1\u03bf\u03c2 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae."
::msgcat::mcset el "Adding %s" "\u03a0\u03c1\u03bf\u03c3\u03b8\u03ae\u03ba\u03b7 %s"
::msgcat::mcset el "Revert changes in file %s?" "\u0391\u03bd\u03b1\u03af\u03c1\u03b5\u03c3\u03b7 \u03b1\u03bb\u03bb\u03b1\u03b3\u03ce\u03bd \u03c3\u03c4\u03bf \u03b1\u03c1\u03c7\u03b5\u03af\u03bf %s;"
::msgcat::mcset el "Revert changes in these %i files?" "\u0391\u03bd\u03b1\u03af\u03c1\u03b5\u03c3\u03b7 \u03b1\u03bb\u03bb\u03b1\u03b3\u03ce\u03bd \u03c3\u03b5 \u03b1\u03c5\u03c4\u03ac \u03c4\u03b1 %i \u03b1\u03c1\u03c7\u03b5\u03af\u03b1;"
::msgcat::mcset el "Any unstaged changes will be permanently lost by the revert." "\u038c\u03bb\u03b5\u03c2 \u03bf\u03b9 \u03bc\u03b7 \u03c3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03b9\u03b7\u03bc\u03ad\u03bd\u03b5\u03c2 \u03b1\u03bb\u03bb\u03b1\u03b3\u03ad\u03c2 \u03b8\u03b1 \u03c7\u03b1\u03b8\u03bf\u03cd\u03bd \u03bf\u03c1\u03b9\u03c3\u03c4\u03b9\u03ba\u03ac \u03b1\u03c0\u03cc \u03c4\u03b7\u03bd \u03b1\u03bd\u03b1\u03af\u03c1\u03b5\u03c3\u03b7."
::msgcat::mcset el "Do Nothing" "\u039a\u03b1\u03bc\u03af\u03b1 \u0395\u03bd\u03ad\u03c1\u03b3\u03b5\u03b9\u03b1"
::msgcat::mcset el "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "\u0394\u03b5 \u03b3\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7 \u03ba\u03b1\u03b8\u03ce\u03c2 \u03b4\u03b9\u03bf\u03c1\u03b8\u03ce\u03bd\u03b5\u03c4\u03b5.\n\n\u03a0\u03c1\u03ad\u03c0\u03b5\u03b9 \u03bd\u03b1 \u03c4\u03b5\u03bb\u03b5\u03b9\u03ce\u03c3\u03b5\u03c4\u03b5 \u03c4\u03b7 \u03b4\u03b9\u03cc\u03c1\u03b8\u03c9\u03c3\u03b7 \u03b1\u03c5\u03c4\u03ae\u03c2 \u03c4\u03b7\u03c2 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2 \u03c0\u03c1\u03b9\u03bd \u03bd\u03b1 \u03be\u03b5\u03ba\u03b9\u03bd\u03ae\u03c3\u03b5\u03c4\u03b5 \u03bf\u03c0\u03bf\u03b9\u03b1\u03c3\u03b4\u03ae\u03c0\u03bf\u03c4\u03b5 \u03bc\u03bf\u03c1\u03c6\u03ae\u03c2 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7.\n"
::msgcat::mcset el "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before a merge can be performed.\n\nThe rescan will be automatically started now.\n" "\u0397 \u03c4\u03b5\u03bb\u03b5\u03c5\u03c4\u03b1\u03af\u03b1 \u03ba\u03b1\u03c4\u03ac\u03c3\u03c4\u03b1\u03c3\u03b7 \u03c0\u03bf\u03c5 \u03b1\u03bd\u03b9\u03c7\u03bd\u03b5\u03cd\u03b8\u03b7\u03ba\u03b5 \u03b4\u03b5 \u03c3\u03c5\u03bc\u03c6\u03c9\u03bd\u03b5\u03af \u03bc\u03b5 \u03c4\u03b7\u03bd \u03ba\u03b1\u03c4\u03ac\u03c3\u03c4\u03b1\u03c3\u03b7 \u03c4\u03bf\u03c5 \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03b7\u03c1\u03af\u03bf\u03c5.\n\n\u039a\u03ac\u03c0\u03bf\u03b9\u03bf \u03ac\u03bb\u03bb\u03bf \u03c0\u03c1\u03cc\u03b3\u03c1\u03b1\u03bc\u03bc\u03b1 Git \u03c4\u03c1\u03bf\u03c0\u03bf\u03c0\u03bf\u03af\u03b7\u03c3\u03b5 \u03c4\u03bf \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf \u03b1\u03c0\u03cc \u03c4\u03b7\u03bd \u03c4\u03b5\u03bb\u03b5\u03c5\u03c4\u03b1\u03af\u03b1 \u03b1\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7. \u03a0\u03c1\u03ad\u03c0\u03b5\u03b9 \u03bd\u03b1 \u03b3\u03af\u03bd\u03b5\u03b9 \u03b5\u03c0\u03b1\u03bd\u03b1\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7 \u03c0\u03c1\u03b9\u03bd \u03c4\u03b7 \u03b4\u03b9\u03b5\u03bd\u03ad\u03c1\u03b3\u03b5\u03b9\u03b1 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2.\n\n\u0397 \u03b5\u03c0\u03b1\u03bd\u03b1\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7 \u03b8\u03b1 \u03be\u03b5\u03ba\u03b9\u03bd\u03ae\u03c3\u03b5\u03b9 \u03b1\u03c5\u03c4\u03cc\u03bc\u03b1\u03c4\u03b1 \u03c4\u03ce\u03c1\u03b1.\n"
::msgcat::mcset el "You are in the middle of a conflicted merge.\n\nFile %s has merge conflicts.\n\nYou must resolve them, stage the file, and commit to complete the current merge.  Only then can you begin another merge.\n" "\u0392\u03c1\u03af\u03c3\u03ba\u03b5\u03c3\u03c4\u03b5 \u03c3\u03c4\u03bf \u03bc\u03ad\u03c3\u03bf \u03bc\u03b9\u03b1\u03c2 \u03c3\u03c5\u03b3\u03ba\u03c1\u03bf\u03c5\u03cc\u03bc\u03b5\u03bd\u03b7\u03c2 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2.\n\n\u03a4\u03bf \u03b1\u03c1\u03c7\u03b5\u03af\u03bf %s \u03ad\u03c7\u03b5\u03b9 \u03c3\u03c5\u03b3\u03ba\u03c1\u03bf\u03cd\u03c3\u03b5\u03b9\u03c2 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2.\n\n\u03a0\u03c1\u03ad\u03c0\u03b5\u03b9 \u03bd\u03b1 \u03c4\u03b9\u03c2 \u03b5\u03c0\u03b9\u03bb\u03cd\u03c3\u03b5\u03c4\u03b5, \u03bd\u03b1 \u03c3\u03c4\u03b1\u03b4\u03b9\u03bf\u03c0\u03bf\u03b9\u03ae\u03c3\u03b5\u03c4\u03b5 \u03c4\u03bf \u03b1\u03c1\u03c7\u03b5\u03af\u03bf, \u03ba\u03b1\u03b9 \u03bd\u03b1 \u03ba\u03ac\u03bd\u03b5\u03c4\u03b5 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae \u03b3\u03b9\u03b1 \u03bd\u03b1 \u03bf\u03bb\u03bf\u03ba\u03bb\u03b7\u03c1\u03ce\u03c3\u03b5\u03c4\u03b5 \u03c4\u03b7\u03bd \u03c4\u03c1\u03ad\u03c7\u03bf\u03c5\u03c3\u03b1 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7. \u039c\u03cc\u03bd\u03bf \u03c4\u03cc\u03c4\u03b5 \u03bc\u03c0\u03bf\u03c1\u03b5\u03af\u03c4\u03b5 \u03bd\u03b1 \u03be\u03b5\u03ba\u03b9\u03bd\u03ae\u03c3\u03b5\u03c4\u03b5 \u03ac\u03bb\u03bb\u03b7 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7.\n"
::msgcat::mcset el "You are in the middle of a change.\n\nFile %s is modified.\n\nYou should complete the current commit before starting a merge.  Doing so will help you abort a failed merge, should the need arise.\n" "\u0392\u03c1\u03af\u03c3\u03ba\u03b5\u03c3\u03c4\u03b5 \u03c3\u03c4\u03bf \u03bc\u03ad\u03c3\u03bf \u03bc\u03b9\u03b1\u03c2 \u03b1\u03bb\u03bb\u03b1\u03b3\u03ae\u03c2.\n\n\u03a4\u03bf \u03b1\u03c1\u03c7\u03b5\u03af\u03bf %s \u03ad\u03c7\u03b5\u03b9 \u03c4\u03c1\u03bf\u03c0\u03bf\u03c0\u03bf\u03b9\u03b7\u03b8\u03b5\u03af.\n\n\u03a0\u03c1\u03ad\u03c0\u03b5\u03b9 \u03bd\u03b1 \u03bf\u03bb\u03bf\u03ba\u03bb\u03b7\u03c1\u03ce\u03c3\u03b5\u03c4\u03b5 \u03c4\u03b7\u03bd \u03c4\u03c1\u03ad\u03c7\u03bf\u03c5\u03c3\u03b1 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7 \u03c0\u03c1\u03b9\u03bd \u03bd\u03b1 \u03be\u03b5\u03ba\u03b9\u03bd\u03ae\u03c3\u03b5\u03c4\u03b5 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7. \u0391\u03c5\u03c4\u03cc \u03b2\u03bf\u03b7\u03b8\u03ac \u03c3\u03c4\u03b7\u03bd \u03b1\u03ba\u03cd\u03c1\u03c9\u03c3\u03b7 \u03b1\u03c0\u03bf\u03c4\u03c5\u03c7\u03b7\u03bc\u03ad\u03bd\u03b7\u03c2 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2, \u03b5\u03ac\u03bd \u03c7\u03c1\u03b5\u03b9\u03b1\u03c3\u03c4\u03b5\u03af.\n"
::msgcat::mcset el "%s of %s" "%s \u03b1\u03c0\u03cc %s"
::msgcat::mcset el "Merging %s and %s..." "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7 \u03c4\u03bf\u03c5 %s \u03bc\u03b5 \u03c4\u03bf %s..."
::msgcat::mcset el "Merge completed successfully." "\u0397 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7 \u03bf\u03bb\u03bf\u03ba\u03bb\u03b7\u03c1\u03ce\u03b8\u03b7\u03ba\u03b5 \u03b5\u03c0\u03b9\u03c4\u03c5\u03c7\u03ce\u03c2."
::msgcat::mcset el "Merge failed.  Conflict resolution is required." "\u0397 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7 \u03b1\u03c0\u03ad\u03c4\u03c5\u03c7\u03b5. \u0391\u03c0\u03b1\u03b9\u03c4\u03b5\u03af\u03c4\u03b1\u03b9 \u03b5\u03c0\u03af\u03bb\u03c5\u03c3\u03b7 \u03c3\u03c5\u03b3\u03ba\u03c1\u03bf\u03cd\u03c3\u03b5\u03c9\u03bd."
::msgcat::mcset el "Merge Into %s" "\u03a3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7 \u03bc\u03b5 %s"
::msgcat::mcset el "Revision To Merge" "\u0391\u03bd\u03b1\u03b8\u03b5\u03ce\u03c1\u03b7\u03c3\u03b7 \u03a0\u03c1\u03bf\u03c2 \u03a3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7"
::msgcat::mcset el "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "\u0394\u03b5 \u03b3\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03b1\u03ba\u03cd\u03c1\u03c9\u03c3\u03b7 \u03ba\u03b1\u03b8\u03ce\u03c2 \u03b4\u03b9\u03bf\u03c1\u03b8\u03ce\u03bd\u03b5\u03c4\u03b5.\n\n\u03a0\u03c1\u03ad\u03c0\u03b5\u03b9 \u03bd\u03b1 \u03c4\u03b5\u03bb\u03b5\u03b9\u03ce\u03c3\u03b5\u03c4\u03b5 \u03c4\u03b7 \u03b4\u03b9\u03cc\u03c1\u03b8\u03c9\u03c3\u03b7 \u03b1\u03c5\u03c4\u03ae\u03c2 \u03c4\u03b7\u03c2 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2.\n"
::msgcat::mcset el "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "\u0391\u03ba\u03cd\u03c1\u03c9\u03c3\u03b7 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2;\n\n\u0397 \u03b1\u03ba\u03cd\u03c1\u03c9\u03c3\u03b7 \u03c4\u03b7\u03c2 \u03c4\u03c1\u03ad\u03c7\u03bf\u03c5\u03c3\u03b1\u03c2 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2 \u03b8\u03b1 \u03c0\u03c1\u03bf\u03ba\u03b1\u03bb\u03ad\u03c3\u03b5\u03b9 \u03b1\u03c0\u03ce\u03bb\u03b5\u03b9\u03b1 *\u039f\u039b\u03a9\u039d* \u03c4\u03c9\u03bd \u03bc\u03b7 \u03c5\u03c0\u03bf\u03b2\u03b5\u03b2\u03bb\u03b7\u03bc\u03ad\u03bd\u03c9\u03bd \u03b1\u03bb\u03bb\u03b1\u03b3\u03ce\u03bd.\n\n\u039d\u03b1 \u03c0\u03c1\u03bf\u03c7\u03c9\u03c1\u03ae\u03c3\u03b5\u03b9 \u03b7 \u03b1\u03ba\u03cd\u03c1\u03c9\u03c3\u03b7 \u03c4\u03b7\u03c2 \u03c4\u03c1\u03ad\u03c7\u03bf\u03c5\u03c3\u03b1\u03c2 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2;"
::msgcat::mcset el "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "\u0395\u03c0\u03b1\u03bd\u03b1\u03c6\u03bf\u03c1\u03ac \u03b1\u03bb\u03bb\u03b1\u03b3\u03ce\u03bd;\n\n\u0397 \u03b5\u03c0\u03b1\u03bd\u03b1\u03c6\u03bf\u03c1\u03ac \u03c4\u03c9\u03bd \u03b1\u03bb\u03bb\u03b1\u03b3\u03ce\u03bd \u03b8\u03b1 \u03c0\u03c1\u03bf\u03ba\u03b1\u03bb\u03ad\u03c3\u03b5\u03b9 \u03b1\u03c0\u03ce\u03bb\u03b5\u03b9\u03b1 *\u039f\u039b\u03a9\u039d* \u03c4\u03c9\u03bd \u03bc\u03b7 \u03c5\u03c0\u03bf\u03b2\u03b5\u03b2\u03bb\u03b7\u03bc\u03ad\u03bd\u03c9\u03bd \u03b1\u03bb\u03bb\u03b1\u03b3\u03ce\u03bd.\n\n\u039d\u03b1 \u03c3\u03c5\u03bd\u03b5\u03c7\u03af\u03c3\u03b5\u03b9 \u03b7 \u03b5\u03c0\u03b1\u03bd\u03b1\u03c6\u03bf\u03c1\u03ac \u03c4\u03c9\u03bd \u03c4\u03c1\u03ad\u03c7\u03bf\u03c5\u03c3\u03c9\u03bd \u03b1\u03bb\u03bb\u03b1\u03b3\u03ce\u03bd;"
::msgcat::mcset el "Aborting" "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03b1\u03ba\u03cd\u03c1\u03c9\u03c3\u03b7"
::msgcat::mcset el "files reset" "\u03b1\u03c1\u03c7\u03b5\u03af\u03b1 \u03c0\u03bf\u03c5 \u03b5\u03c0\u03b1\u03bd\u03b1\u03c6\u03ad\u03c1\u03b8\u03b7\u03ba\u03b1\u03bd"
::msgcat::mcset el "Abort failed." "\u0397 \u03b1\u03ba\u03cd\u03c1\u03c9\u03c3\u03b7 \u03b1\u03c0\u03ad\u03c4\u03c5\u03c7\u03b5."
::msgcat::mcset el "Abort completed.  Ready." "\u0397 \u03b1\u03ba\u03cd\u03c1\u03c9\u03c3\u03b7 \u03b1\u03c0\u03ad\u03c4\u03c5\u03c7\u03b5. \u0388\u03c4\u03bf\u03b9\u03bc\u03bf."
::msgcat::mcset el "Restore Defaults" "\u0395\u03c0\u03b1\u03bd\u03b1\u03c6\u03bf\u03c1\u03ac \u03a0\u03c1\u03bf\u03b5\u03c0\u03b9\u03bb\u03bf\u03b3\u03ce\u03bd"
::msgcat::mcset el "Save" "\u0391\u03c0\u03bf\u03b8\u03ae\u03ba\u03b5\u03c5\u03c3\u03b7"
::msgcat::mcset el "%s Repository" "%s \u0391\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf"
::msgcat::mcset el "Global (All Repositories)" "\u039f\u03bb\u03b9\u03ba\u03cc (\u038c\u03bb\u03b1 \u03c4\u03b1 \u0391\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03b1)"
::msgcat::mcset el "User Name" "\u038c\u03bd\u03bf\u03bc\u03b1 \u03a7\u03c1\u03ae\u03c3\u03c4\u03b7"
::msgcat::mcset el "Email Address" "\u0394\u03b9\u03b5\u03cd\u03b8\u03c5\u03bd\u03c3\u03b7 Email"
::msgcat::mcset el "Summarize Merge Commits" "\u03a0\u03b5\u03c1\u03af\u03bb\u03b7\u03c8\u03b7 \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ce\u03bd \u03a3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2"
::msgcat::mcset el "Merge Verbosity" "\u039b\u03b5\u03c0\u03c4\u03bf\u03bc\u03ad\u03c1\u03b5\u03b9\u03b1 \u03a3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2"
::msgcat::mcset el "Show Diffstat After Merge" "\u03a0\u03c1\u03bf\u03b2\u03bf\u03bb\u03ae \u03a3\u03c4\u03b1\u03c4\u03b9\u03c3\u03c4\u03b9\u03ba\u03ce\u03bd \u0394\u03b9\u03b1\u03c6\u03bf\u03c1\u03ac\u03c2 \u039c\u03b5\u03c4\u03ac \u03b1\u03c0\u03cc \u03a3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7"
::msgcat::mcset el "Trust File Modification Timestamps" "\u0395\u03bc\u03c0\u03b9\u03c3\u03c4\u03bf\u03c3\u03cd\u03bd\u03b7 \u0397\u03bc\u03b5\u03c1\u03bf\u03bc\u03b7\u03bd\u03b9\u03ce\u03bd \u039c\u03b5\u03c4\u03b1\u03c4\u03c1\u03bf\u03c0\u03ae\u03c2 \u0391\u03c1\u03c7\u03b5\u03af\u03c9\u03bd"
::msgcat::mcset el "Prune Tracking Branches During Fetch" "\u039a\u03bb\u03ac\u03b4\u03b5\u03bc\u03b1 \u039a\u03bb\u03ac\u03b4\u03c9\u03bd \u03a0\u03b1\u03c1\u03b1\u03ba\u03bf\u03bb\u03bf\u03cd\u03b8\u03b7\u03c3\u03b7\u03c2 \u039a\u03b1\u03c4\u03ac \u03a4\u03b7\u03bd \u0391\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7"
::msgcat::mcset el "Match Tracking Branches" "\u03a3\u03c5\u03bc\u03c6\u03c9\u03bd\u03af\u03b1 \u039a\u03bb\u03ac\u03b4\u03c9\u03bd \u03a0\u03b1\u03c1\u03b1\u03ba\u03bf\u03bb\u03bf\u03cd\u03b8\u03b7\u03c3\u03b7\u03c2"
::msgcat::mcset el "Number of Diff Context Lines" "\u0391\u03c1\u03b9\u03b8\u03bc\u03cc\u03c2 \u0393\u03c1\u03b1\u03bc\u03bc\u03ce\u03bd \u0395\u03bd\u03bd\u03bf\u03b9\u03bf\u03bb\u03bf\u03b3\u03b9\u03ba\u03bf\u03cd \u03a0\u03bb\u03b1\u03b9\u03c3\u03af\u03bf\u03c5 \u0394\u03b9\u03b1\u03c6\u03bf\u03c1\u03ac\u03c2"
::msgcat::mcset el "Commit Message Text Width" "\u03a0\u03bb\u03ac\u03c4\u03bf\u03c2 \u039a\u03b5\u03b9\u03bc\u03ad\u03bd\u03bf\u03c5 \u039c\u03b7\u03bd\u03cd\u03bc\u03b1\u03c4\u03bf\u03c2 \u03a5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ae\u03c2"
::msgcat::mcset el "New Branch Name Template" "\u039d\u03ad\u03bf \u03a0\u03c1\u03cc\u03c4\u03c5\u03c0\u03bf \u039f\u03bd\u03cc\u03bc\u03b1\u03c4\u03bf\u03c2 \u039a\u03bb\u03ac\u03b4\u03bf\u03c5"
::msgcat::mcset el "Spelling Dictionary:" "\u039b\u03b5\u03be\u03b9\u03ba\u03cc \u039f\u03c1\u03b8\u03bf\u03b3\u03c1\u03b1\u03c6\u03af\u03b1\u03c2:"
::msgcat::mcset el "Change Font" "\u0391\u03bb\u03bb\u03b1\u03b3\u03ae \u0393\u03c1\u03b1\u03bc\u03bc\u03b1\u03c4\u03bf\u03c3\u03b5\u03b9\u03c1\u03ac\u03c2"
::msgcat::mcset el "Choose %s" "\u0395\u03c0\u03b9\u03bb\u03bf\u03b3\u03ae %s"
::msgcat::mcset el "Preferences" "\u03a0\u03c1\u03bf\u03c4\u03b9\u03bc\u03ae\u03c3\u03b5\u03b9\u03c2"
::msgcat::mcset el "Failed to completely save options:" "\u0391\u03c0\u03bf\u03c4\u03c5\u03c7\u03af\u03b1 \u03c0\u03bb\u03ae\u03c1\u03bf\u03c5\u03c2 \u03b1\u03c0\u03bf\u03b8\u03ae\u03ba\u03b5\u03c5\u03c3\u03b7\u03c2 \u03b5\u03c0\u03b9\u03bb\u03bf\u03b3\u03ce\u03bd:"
::msgcat::mcset el "Delete Remote Branch" "\u0394\u03b9\u03b1\u03b3\u03c1\u03b1\u03c6\u03ae \u0391\u03c0\u03bf\u03bc\u03b1\u03ba\u03c1\u03c5\u03c3\u03bc\u03ad\u03bd\u03bf\u03c5 \u039a\u03bb\u03ac\u03b4\u03bf\u03c5"
::msgcat::mcset el "From Repository" "\u0391\u03c0\u03cc \u0391\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf"
::msgcat::mcset el "Remote:" "\u0391\u03c0\u03bf\u03bc\u03b1\u03ba\u03c1\u03c5\u03c3\u03bc\u03ad\u03bd\u03bf:"
::msgcat::mcset el "Branches" "\u039a\u03bb\u03ac\u03b4\u03bf\u03b9"
::msgcat::mcset el "Delete Only If" "\u0394\u03b9\u03b1\u03b3\u03c1\u03b1\u03c6\u03ae \u039c\u03cc\u03bd\u03bf \u0395\u03ac\u03bd"
::msgcat::mcset el "Merged Into:" "\u03a3\u03c5\u03b3\u03c7\u03c9\u03bd\u03b5\u03c5\u03bc\u03ad\u03bd\u03bf \u039c\u03b5:"
::msgcat::mcset el "Always (Do not perform merge checks)" "\u03a0\u03ac\u03bd\u03c4\u03b1 (\u039c\u03b7 \u03b4\u03b9\u03b5\u03bd\u03b5\u03c1\u03b3\u03b7\u03b8\u03bf\u03cd\u03bd \u03ad\u03bb\u03b5\u03b3\u03c7\u03bf\u03b9 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2)"
::msgcat::mcset el "A branch is required for 'Merged Into'." "\u0391\u03c0\u03b1\u03b9\u03c4\u03b5\u03af\u03c4\u03b1\u03b9 \u03ad\u03bd\u03b1\u03c2 \u03ba\u03bb\u03ac\u03b4\u03bf\u03c2 \u03b3\u03b9\u03b1 '\u03a3\u03c5\u03b3\u03c7\u03c9\u03bd\u03b5\u03c5\u03bc\u03ad\u03bd\u03bf \u039c\u03b5'."
::msgcat::mcset el "The following branches are not completely merged into %s:\n\n - %s" "\u039f\u03b9 \u03b5\u03be\u03ae\u03c2 \u03ba\u03bb\u03ac\u03b4\u03bf\u03b9 \u03b4\u03b5\u03bd \u03b5\u03af\u03bd\u03b1\u03b9 \u03c0\u03bb\u03ae\u03c1\u03c9\u03c2 \u03c3\u03c5\u03b3\u03c7\u03c9\u03bd\u03b5\u03c5\u03bc\u03ad\u03bd\u03bf\u03b9 \u03bc\u03b5 \u03c4\u03bf %s:\n\n - %s"
::msgcat::mcset el "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "\u039c\u03af\u03b1 \u03ae \u03c0\u03b5\u03c1\u03b9\u03c3\u03c3\u03cc\u03c4\u03b5\u03c1\u03b5\u03c2 \u03b1\u03c0\u03cc \u03c4\u03b9\u03c2 \u03b4\u03bf\u03ba\u03b9\u03bc\u03ad\u03c2 \u03c3\u03c5\u03b3\u03c7\u03ce\u03bd\u03b5\u03c5\u03c3\u03b7\u03c2 \u03b1\u03c0\u03ad\u03c4\u03c5\u03c7\u03b1\u03bd \u03b5\u03c0\u03b5\u03b9\u03b4\u03ae \u03b4\u03b5\u03bd \u03ad\u03c7\u03b5\u03c4\u03b5 \u03c6\u03ad\u03c1\u03b5\u03b9 \u03c4\u03b9\u03c2 \u03b1\u03bd\u03b1\u03b3\u03ba\u03b1\u03af\u03b5\u03c2 \u03c5\u03c0\u03bf\u03b2\u03bf\u03bb\u03ad\u03c2. \u0394\u03bf\u03ba\u03b9\u03bc\u03ac\u03c3\u03c4\u03b5 \u03b1\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7 \u03b1\u03c0\u03cc \u03c4\u03bf %s \u03c0\u03c1\u03ce\u03c4\u03b1."
::msgcat::mcset el "Please select one or more branches to delete." "\u03a0\u03b1\u03c1\u03b1\u03ba\u03b1\u03bb\u03ce \u03b5\u03c0\u03b9\u03bb\u03ad\u03be\u03c4\u03b5 \u03ad\u03bd\u03b1\u03bd \u03ae \u03c0\u03b5\u03c1\u03b9\u03c3\u03c3\u03cc\u03c4\u03b5\u03c1\u03bf\u03c5\u03c2 \u03ba\u03bb\u03ac\u03b4\u03bf\u03c5\u03c2 \u03c0\u03c1\u03bf\u03c2 \u03b4\u03b9\u03b1\u03b3\u03c1\u03b1\u03c6\u03ae."
::msgcat::mcset el "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "\u0397 \u03b1\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7 \u03b4\u03b9\u03b5\u03b3\u03c1\u03b1\u03bc\u03bc\u03ad\u03bd\u03c9\u03bd \u03ba\u03bb\u03ac\u03b4\u03c9\u03bd \u03b5\u03af\u03bd\u03b1\u03b9 \u03b4\u03cd\u03c3\u03ba\u03bf\u03bb\u03b7.\n\n\u0394\u03b9\u03b1\u03b3\u03c1\u03b1\u03c6\u03ae \u03c4\u03c9\u03bd \u03b5\u03c0\u03b9\u03bb\u03b5\u03b3\u03bc\u03ad\u03bd\u03c9\u03bd \u03ba\u03bb\u03ac\u03b4\u03c9\u03bd;"
::msgcat::mcset el "Deleting branches from %s" "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03b4\u03b9\u03b1\u03b3\u03c1\u03b1\u03c6\u03ae \u03ba\u03bb\u03ac\u03b4\u03c9\u03bd \u03b1\u03c0\u03cc %s"
::msgcat::mcset el "No repository selected." "\u0394\u03b5\u03bd \u03ad\u03c7\u03b5\u03b9 \u03b5\u03c0\u03b9\u03bb\u03b5\u03b3\u03b5\u03af \u03b1\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf."
::msgcat::mcset el "Scanning %s..." "\u0391\u03bd\u03af\u03c7\u03bd\u03b5\u03c5\u03c3\u03b7 %s..."
::msgcat::mcset el "Prune from" "\u039a\u03bb\u03ac\u03b4\u03b5\u03bc\u03b1 \u03b1\u03c0\u03cc"
::msgcat::mcset el "Fetch from" "\u0391\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7 \u03b1\u03c0\u03cc"
::msgcat::mcset el "Push to" "\u038f\u03b8\u03b7\u03c3\u03b7 \u03c3\u03b5"
::msgcat::mcset el "Cannot write shortcut:" "\u0394\u03b5 \u03bc\u03c0\u03cc\u03c1\u03b5\u03c3\u03b5 \u03bd\u03b1 \u03b1\u03c0\u03bf\u03b8\u03b7\u03ba\u03b5\u03c5\u03c4\u03b5\u03af \u03b7 \u03c3\u03c5\u03bd\u03c4\u03cc\u03bc\u03b5\u03c5\u03c3\u03b7:"
::msgcat::mcset el "Cannot write icon:" "\u0394\u03b5 \u03bc\u03c0\u03cc\u03c1\u03b5\u03c3\u03b5 \u03bd\u03b1 \u03b1\u03c0\u03bf\u03b8\u03b7\u03ba\u03b5\u03c5\u03c4\u03b5\u03af \u03c4\u03bf \u03b5\u03b9\u03ba\u03bf\u03bd\u03af\u03b4\u03b9\u03bf:"
::msgcat::mcset el "Unsupported spell checker" "M\u03b7 \u03c5\u03c0\u03bf\u03c3\u03c4\u03b7\u03c1\u03b9\u03b6\u03cc\u03bc\u03b5\u03bd\u03bf\u03c2 \u03b5\u03bb\u03b5\u03b3\u03ba\u03c4\u03ae\u03c2 \u03bf\u03c1\u03b8\u03bf\u03b3\u03c1\u03b1\u03c6\u03af\u03b1\u03c2"
::msgcat::mcset el "Spell checking is unavailable" "\u0388\u03bb\u03b5\u03b3\u03c7\u03bf\u03c2 \u03bf\u03c1\u03b8\u03bf\u03b3\u03c1\u03b1\u03c6\u03af\u03b1\u03c2 \u03bc\u03b7 \u03b4\u03b9\u03b1\u03b8\u03ad\u03c3\u03b9\u03bc\u03bf\u03c2"
::msgcat::mcset el "Invalid spell checking configuration" "\u039c\u03b7 \u03ad\u03b3\u03ba\u03c5\u03c1\u03b7 \u03c1\u03cd\u03b8\u03bc\u03b9\u03c3\u03b7 \u03b5\u03bb\u03ad\u03b3\u03c7\u03bf\u03c5 \u03bf\u03c1\u03b8\u03bf\u03b3\u03c1\u03b1\u03c6\u03af\u03b1\u03c2"
::msgcat::mcset el "Reverting dictionary to %s." "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03b5\u03c0\u03b1\u03bd\u03b1\u03c6\u03bf\u03c1\u03ac \u03c4\u03bf\u03c5 \u03bb\u03b5\u03be\u03b9\u03ba\u03bf\u03cd \u03c3\u03b5 %s."
::msgcat::mcset el "Spell checker silently failed on startup" "\u039f \u03b5\u03bb\u03b5\u03b3\u03ba\u03c4\u03ae\u03c2 \u03bf\u03c1\u03b8\u03bf\u03b3\u03c1\u03b1\u03c6\u03af\u03b1\u03c2 \u03b1\u03c0\u03ad\u03c4\u03c5\u03c7\u03b5 \u03c3\u03b9\u03c9\u03c0\u03b7\u03bb\u03ac \u03ba\u03b1\u03c4\u03ac \u03c4\u03b7\u03bd \u03b5\u03ba\u03ba\u03af\u03bd\u03b7\u03c3\u03b7"
::msgcat::mcset el "Unrecognized spell checker" "\u039c\u03b7 \u03b1\u03bd\u03b1\u03b3\u03bd\u03c9\u03c1\u03af\u03c3\u03b9\u03bc\u03bf\u03c2 \u03b5\u03bb\u03b5\u03b3\u03ba\u03c4\u03ae\u03c2 \u03bf\u03c1\u03b8\u03bf\u03b3\u03c1\u03b1\u03c6\u03af\u03b1\u03c2"
::msgcat::mcset el "No Suggestions" "\u039a\u03b1\u03bc\u03af\u03b1 \u03a0\u03c1\u03cc\u03c4\u03b1\u03c3\u03b7"
::msgcat::mcset el "Unexpected EOF from spell checker" "\u039c\u03b7 \u03b1\u03bd\u03b1\u03bc\u03b5\u03bd\u03cc\u03bc\u03b5\u03bd\u03bf \u03c4\u03ad\u03bb\u03bf\u03c2 \u03b1\u03c1\u03c7\u03b5\u03af\u03bf\u03c5 \u03b1\u03c0\u03cc \u03c4\u03bf\u03bd \u03b5\u03bb\u03b5\u03b3\u03ba\u03c4\u03ae \u03bf\u03c1\u03b8\u03bf\u03b3\u03c1\u03b1\u03c6\u03af\u03b1\u03c2"
::msgcat::mcset el "Spell Checker Failed" "\u0391\u03c0\u03bf\u03c4\u03c5\u03c7\u03af\u03b1 \u0395\u03bb\u03b5\u03b3\u03ba\u03c4\u03ae \u039f\u03c1\u03b8\u03bf\u03b3\u03c1\u03b1\u03c6\u03af\u03b1\u03c2"
::msgcat::mcset el "%s ... %*i of %*i %s (%3i%%)" "%s ... %*i \u03b1\u03c0\u03cc %*i %s (%3i%%)"
::msgcat::mcset el "fetch %s" "\u03b1\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7 %s"
::msgcat::mcset el "Fetching new changes from %s" "\u0391\u03bd\u03ac\u03ba\u03c4\u03b7\u03c3\u03b7 \u03bd\u03ad\u03c9\u03bd \u03b1\u03bb\u03bb\u03b1\u03b3\u03ce\u03bd \u03b1\u03c0\u03cc \u03c4\u03bf %s"
::msgcat::mcset el "remote prune %s" "\u03b1\u03c0\u03bf\u03bc\u03b1\u03ba\u03c1\u03c5\u03c3\u03bc\u03ad\u03bd\u03bf \u03ba\u03bb\u03ac\u03b4\u03b5\u03bc\u03b1 %s"
::msgcat::mcset el "Pruning tracking branches deleted from %s" "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03ba\u03bb\u03ac\u03b4\u03b5\u03bc\u03b1 \u03ba\u03bb\u03ac\u03b4\u03c9\u03bd \u03c0\u03b1\u03c1\u03b1\u03ba\u03bf\u03bb\u03bf\u03cd\u03b8\u03b7\u03c3\u03b7\u03c2 \u03c0\u03bf\u03c5 \u03b4\u03b9\u03b5\u03b3\u03c1\u03ac\u03c6\u03b7\u03c3\u03b1\u03bd \u03b1\u03c0\u03cc \u03c4\u03bf %s"
::msgcat::mcset el "push %s" "\u03ce\u03b8\u03b7\u03c3\u03b7 %s"
::msgcat::mcset el "Pushing changes to %s" "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03ce\u03b8\u03b7\u03c3\u03b7 \u03b1\u03bb\u03bb\u03b1\u03b3\u03ce\u03bd \u03c3\u03c4\u03bf %s"
::msgcat::mcset el "Pushing %s %s to %s" "\u0393\u03af\u03bd\u03b5\u03c4\u03b1\u03b9 \u03ce\u03b8\u03b7\u03c3\u03b7 %s %s \u03c3\u03c4\u03bf %s"
::msgcat::mcset el "Push Branches" "\u038f\u03b8\u03b7\u03c3\u03b7 \u039a\u03bb\u03ac\u03b4\u03c9\u03bd"
::msgcat::mcset el "Source Branches" "\u03a0\u03b7\u03b3\u03b1\u03af\u03bf\u03b9 \u039a\u03bb\u03ac\u03b4\u03bf\u03b9"
::msgcat::mcset el "Destination Repository" "\u0391\u03c0\u03bf\u03b8\u03b5\u03c4\u03ae\u03c1\u03b9\u03bf \u03a0\u03c1\u03bf\u03bf\u03c1\u03b9\u03c3\u03bc\u03bf\u03cd"
::msgcat::mcset el "Transfer Options" "\u0395\u03c0\u03b9\u03bb\u03bf\u03b3\u03ad\u03c2 \u039c\u03b5\u03c4\u03b1\u03c6\u03bf\u03c1\u03ac\u03c2"
::msgcat::mcset el "Force overwrite existing branch (may discard changes)" "\u0395\u03be\u03b1\u03bd\u03b1\u03b3\u03ba\u03b1\u03c3\u03bc\u03cc\u03c2 \u03b5\u03c0\u03b5\u03b3\u03b3\u03c1\u03b1\u03c6\u03ae\u03c2 \u03c5\u03c0\u03ac\u03c1\u03c7\u03bf\u03bd\u03c4\u03bf\u03c2 \u03ba\u03bb\u03ac\u03b4\u03bf\u03c5 (\u03bc\u03c0\u03bf\u03c1\u03b5\u03af \u03bd\u03b1 \u03b1\u03c0\u03bf\u03c1\u03c1\u03af\u03c8\u03b5\u03b9 \u03b1\u03bb\u03bb\u03b1\u03b3\u03ad\u03c2)"
::msgcat::mcset el "Use thin pack (for slow network connections)" "\u03a7\u03c1\u03ae\u03c3\u03b7 \u03b9\u03c3\u03c7\u03bd\u03bf\u03cd \u03c0\u03b1\u03ba\u03ad\u03c4\u03bf\u03c5 (\u03b3\u03b9\u03b1 \u03b1\u03c1\u03b3\u03ad\u03c2 \u03c3\u03c5\u03bd\u03b4\u03ad\u03c3\u03b5\u03b9\u03c2 \u03b4\u03b9\u03ba\u03c4\u03cd\u03bf\u03c5)"
::msgcat::mcset el "Include tags" "\u03a3\u03c5\u03bc\u03c0\u03b5\u03c1\u03af\u03bb\u03b7\u03c8\u03b7 \u03b5\u03c4\u03b9\u03ba\u03b5\u03c4\u03ce\u03bd"
