/* Common Backend requirements.

   Copyright (C) 2015-2025 Free Software Foundation, Inc.
   Contributed by <PERSON> <<EMAIL>>

This file is part of GCC.

GCC is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free
Software Foundation; either version 3, or (at your option) any later
version.

GCC is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHA<PERSON><PERSON><PERSON>ITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.

You should have received a copy of the GNU General Public License
along with GCC; see the file COPYING3.  If not see
<http://www.gnu.org/licenses/>.  */

#ifndef GCC_BACKEND_H
#define GCC_BACKEND_H

/* This is an aggregation header file. This means it should contain only
   other include files.  */

#include "tm.h"
#include "function.h"
#include "bitmap.h"
#include "sbitmap.h"
#include "basic-block.h"
#include "cfg.h"

#endif /*GCC_BACKEND_H */
