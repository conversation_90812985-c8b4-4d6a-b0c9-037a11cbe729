/*
 * Copyright (C) 2023 Mohamad Al-Jaf
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.storage.streams.idl";

namespace Windows.Security.Credentials.UI {
    typedef enum UserConsentVerificationResult UserConsentVerificationResult;
    typedef enum UserConsentVerifierAvailability UserConsentVerifierAvailability;

    interface IUserConsentVerifierStatics;

    runtimeclass UserConsentVerifier;

    declare {
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.Security.Credentials.UI.UserConsentVerificationResult>;
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.Security.Credentials.UI.UserConsentVerifierAvailability>;
        interface Windows.Foundation.IAsyncOperation<Windows.Security.Credentials.UI.UserConsentVerificationResult>;
        interface Windows.Foundation.IAsyncOperation<Windows.Security.Credentials.UI.UserConsentVerifierAvailability>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum UserConsentVerificationResult
    {
        Verified             = 0,
        DeviceNotPresent     = 1,
        NotConfiguredForUser = 2,
        DisabledByPolicy     = 3,
        DeviceBusy           = 4,
        RetriesExhausted     = 5,
        Canceled             = 6,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum UserConsentVerifierAvailability
    {
        Available            = 0,
        DeviceNotPresent     = 1,
        NotConfiguredForUser = 2,
        DisabledByPolicy     = 3,
        DeviceBusy           = 4,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Security.Credentials.UI.UserConsentVerifier),
        uuid(af4f3f91-564c-4ddc-b8b5-973447627c65)
    ]
    interface IUserConsentVerifierStatics : IInspectable
    {
        HRESULT CheckAvailabilityAsync([out, retval] Windows.Foundation.IAsyncOperation<Windows.Security.Credentials.UI.UserConsentVerifierAvailability> **result);
        HRESULT RequestVerificationAsync([in] HSTRING message, [out, retval] Windows.Foundation.IAsyncOperation<Windows.Security.Credentials.UI.UserConsentVerificationResult> **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        static(Windows.Security.Credentials.UI.IUserConsentVerifierStatics, Windows.Foundation.UniversalApiContract, 1.0),
        threading(both)
    ]
    runtimeclass UserConsentVerifier
    {
    }
}
