.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_get_type" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_get_type \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "gnutls_privkey_type_t gnutls_privkey_get_type(gnutls_privkey_t " key ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t key" 12
should contain a \fBgnutls_privkey_t\fP type
.SH "DESCRIPTION"
This function will return the type of the private key. This is
actually the type of the subsystem used to set this private key.
.SH "RETURNS"
a member of the \fBgnutls_privkey_type_t\fP enumeration on
success, or a negative error code on error.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
