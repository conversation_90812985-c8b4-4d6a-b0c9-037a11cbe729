.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "dane_strerror" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
dane_strerror \- API function
.SH SYNOPSIS
.B #include <gnutls/dane.h>
.sp
.BI "const char * dane_strerror(int " error ");"
.SH ARGUMENTS
.IP "int error" 12
is a DANE error code, a negative error code
.SH "DESCRIPTION"
This function is similar to strerror.  The difference is that it
accepts an error number returned by a gnutls function; In case of
an unknown error a descriptive string is sent instead of \fBNULL\fP.

Error codes are always a negative error code.
.SH "RETURNS"
A string explaining the DANE error message.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
