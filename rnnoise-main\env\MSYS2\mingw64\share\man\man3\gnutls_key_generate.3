.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_key_generate" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_key_generate \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_key_generate(gnutls_datum_t * " key ", unsigned int " key_size ");"
.SH ARGUMENTS
.IP "gnutls_datum_t * key" 12
is a pointer to a \fBgnutls_datum_t\fP which will contain a newly
created key
.IP "unsigned int key_size" 12
the number of bytes of the key
.SH "DESCRIPTION"
Generates a random key of  \fIkey_size\fP bytes.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, or an
error code.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
