.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_set_max_size" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_set_max_size \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "ssize_t gnutls_record_set_max_size(gnutls_session_t " session ", size_t " size ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "size_t size" 12
is the new size
.SH "DESCRIPTION"
This function sets the maximum amount of plaintext sent and
received in a record in this connection.

Prior to 3.6.4, this function was implemented using a TLS extension
called 'max fragment length', which limits the acceptable values to
512(=2^9), 1024(=2^10), 2048(=2^11) and 4096(=2^12).

Since 3.6.4, the limit is also negotiated through a new TLS
extension called 'record size limit', which doesn't have the
limitation, as long as the value ranges between 512 and 16384.
Note that while the 'record size limit' extension is preferred, not
all TLS implementations use or even understand the extension.
.SH "DEPRECATED"
if the client can assume that the 'record size limit'
extension is supported by the server, we recommend using
\fBgnutls_record_set_max_recv_size()\fP instead.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
