.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_set_ptr" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_set_ptr \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "void gnutls_x509_trust_list_set_ptr(gnutls_x509_trust_list_t " tlist ", void * " ptr ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t tlist" 12
is a \fBgnutls_x509_trust_list_t\fP type.
.IP "void * ptr" 12
is the user pointer
.SH "DESCRIPTION"
This function will set (associate) the user given pointer  \fIptr\fP to
the tlist structure. This pointer can be accessed with
\fBgnutls_x509_trust_list_get_ptr()\fP. Useful in the callback function
gnutls_x509_trust_list_set_getissuer_function.
.SH "SINCE"
3.7.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
