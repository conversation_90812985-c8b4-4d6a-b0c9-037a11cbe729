<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>Building and Using DLLs</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="programming.html" title="Chapter&#160;4.&#160;Programming with Cygwin"><link rel="prev" href="gdb.html" title="Debugging Cygwin Programs"><link rel="next" href="windres.html" title="Defining Windows Resources"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">Building and Using DLLs</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="gdb.html">Prev</a>&#160;</td><th width="60%" align="center">Chapter&#160;4.&#160;Programming with Cygwin</th><td width="20%" align="right">&#160;<a accesskey="n" href="windres.html">Next</a></td></tr></table><hr></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="dll"></a>Building and Using DLLs</h2></div></div></div><p>DLLs are Dynamic Link Libraries, which means that they're linked
into your program at run time instead of build time.  There are three
parts to a DLL:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p> the exports </p></li><li class="listitem"><p> the code and data </p></li><li class="listitem"><p> the import library </p></li></ul></div><p>The code and data are the parts you write - functions,
variables, etc.  All these are merged together, like if you were
building one big object files, and put into the dll.  They are not
put into your .exe at all.</p><p>The exports contains a list of functions and variables that the
dll makes available to other programs.  Think of this as the list of
"global" symbols, the rest being hidden.  Normally, you'd create this
list by hand with a text editor, but it's possible to do it
automatically from the list of functions in your code.  The
<code class="filename">dlltool</code> program creates the exports section of
the dll from your text file of exported symbols.</p><p>The import library is a regular UNIX-like
<code class="filename">.a</code> library, but it only contains the tiny bit of
information needed to tell the OS how your program interacts with
("imports") the dll.  This information is linked into your
<code class="filename">.exe</code>.  This is also generated by
<code class="filename">dlltool</code>.</p><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="dll-build"></a>Building DLLs</h3></div></div></div><p>This page gives only a few simple examples of gcc's DLL-building 
capabilities. To begin an exploration of the many additional options,
see the gcc documentation and website, currently at 
<a class="ulink" href="http://gcc.gnu.org/" target="_top">http://gcc.gnu.org/</a>
</p><p>Let's go through a simple example of how to build a dll.
For this example, we'll use a single file
<code class="filename">myprog.c</code> for the program
(<code class="filename">myprog.exe</code>) and a single file
<code class="filename">mydll.c</code> for the contents of the dll
(<code class="filename">mydll.dll</code>).</p><p>Fortunately, with the latest gcc and binutils the process for building a dll
is now pretty simple. Say you want to build this minimal function in mydll.c:</p><pre class="screen">
#include &lt;stdio.h&gt;

int
hello()
{
  printf ("Hello World!\n");
}  
</pre><p>First compile mydll.c to object code:</p><pre class="screen">gcc -c mydll.c</pre><p>Then, tell gcc that it is building a shared library:</p><pre class="screen">gcc -shared -o mydll.dll mydll.o</pre><p>
That's it! To finish up the example, you can now link to the
dll with a simple program:
</p><pre class="screen">
int 
main ()
{
  hello ();
}  
</pre><p>
Then link to your dll with a command like:
</p><pre class="screen">gcc -o myprog myprog.c -L./ -lmydll</pre><p>However, if you are building a dll as an export library,
you will probably want to use the complete syntax:</p><pre class="screen">gcc -shared -o cyg${module}.dll \
    -Wl,--out-implib=lib${module}.dll.a \
    -Wl,--export-all-symbols \
    -Wl,--enable-auto-import \
    -Wl,--whole-archive ${old_libs} \
    -Wl,--no-whole-archive ${dependency_libs}</pre><p>
The name of your library is <code class="literal">${module}</code>, prefixed with
<code class="literal">cyg</code> for the DLL and <code class="literal">lib</code> for the
import library. Cygwin DLLs use the <code class="literal">cyg</code> prefix to 
differentiate them from native-Windows MinGW DLLs, see 
<a class="ulink" href="http://mingw.org" target="_top">the MinGW website</a> for more details.
<code class="literal">${old_libs}</code> are all
your object files, bundled together in static libs or single object
files and the <code class="literal">${dependency_libs}</code> are import libs you 
need to link against, e.g 
<strong class="userinput"><code>'-lpng -lz -L/usr/local/special -lmyspeciallib'</code></strong>.
</p></div><div class="sect2"><div class="titlepage"><div><div><h3 class="title"><a name="dll-link"></a>Linking Against DLLs</h3></div></div></div><p>If you have an existing DLL already, you need to build a
Cygwin-compatible import library.  If you have the source to compile
the DLL, see <a class="xref" href="dll.html#dll-build" title="Building DLLs">the section called &#8220;Building DLLs&#8221;</a> for details on having 
<code class="filename">gcc</code> build one for you.  If you do not have the
source or a supplied working import library, you can get most of
the way by creating a .def file with these commands (you might need to
do this in <code class="filename">bash</code> for the quoting to work
correctly):</p><pre class="screen">
echo EXPORTS &gt; foo.def
nm foo.dll | grep ' T _' | sed 's/.* T _//' &gt;&gt; foo.def
</pre><p>Note that this will only work if the DLL is not stripped.
Otherwise you will get an error message: "No symbols in
foo.dll".</p><p>Once you have the <code class="filename">.def</code> file, you can create
an import library from it like this:</p><pre class="screen">
dlltool --def foo.def --dllname foo.dll --output-lib foo.a
</pre></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="gdb.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="programming.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="windres.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Debugging Cygwin Programs&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;Defining Windows Resources</td></tr></table></div></body></html>
