.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_cpy_crl_dist_points" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_cpy_crl_dist_points \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_cpy_crl_dist_points(gnutls_x509_crt_t " dst ", gnutls_x509_crt_t " src ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t dst" 12
a certificate of type \fBgnutls_x509_crt_t\fP
.IP "gnutls_x509_crt_t src" 12
the certificate where the dist points will be copied from
.SH "DESCRIPTION"
This function will copy the CRL distribution points certificate
extension, from the source to the destination certificate.
This may be useful to copy from a CA certificate to issued ones.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
