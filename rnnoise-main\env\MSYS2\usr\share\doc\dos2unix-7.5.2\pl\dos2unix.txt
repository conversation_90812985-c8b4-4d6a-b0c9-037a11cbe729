NAZWA
    dos2unix - konwerter formatu plików tekstowych między systemami DOS/Mac
    a Uniksem

SKŁADNIA
        dos2unix [opcje] [PLIK ...] [-n PLIK_WEJ PLIK_WYJ ...]
        unix2dos [opcje] [PLIK ...] [-n PLIK_WEJ PLIK_WYJ ...]

OPIS
    Pakiet Dos2unix zawiera narzędzia "dos2unix" oraz "unix2dos" do
    konwersji zwykłych plików tekstowych między formatami używanymi w
    systemach DOS lub Mac a formatem uniksowym.

    W plikach tekstowych systemu DOS/Windows oznaczenie końca linii to
    połączenie dwóch znaków: powrotu karetki (CR) i przesunięcia linii (LF).
    W uniksowych plikach tekstowych koniec linii to pojedynczy znak LF. W
    plikach tekstowych systemu Mac sprzed Mac OS X koniec linii był
    pojedynczym znakiem CR. Obecnie Mac OS wykorzystuje uniksowe końce linii
    (LF).

    Oprócz oznaczeń końców linii Dos2unix potrafi konwertować także
    kodowanie plików. Kilko stron kodowych DOS-a może być przekonwertowanych
    do uniksowego Latin-1, a windowsowy Unicode (UTF-16) do
    powszechniejszego pod Uniksem kodowania Unicode UTF-8.

    Pliki binarne są pomijane automatycznie, chyba że konwersja zostanie
    wymuszona.

    Pliki inne niż zwykłe, np. katalogi lub FIFO, są pomijane automatycznie.

    Dowiązania symboliczne i ich cele są domyślnie pozostawiane bez zmian.
    Dowiązania symboliczne mogą być opcjonalnie zastępowane, albo wyjście
    może być zapisywane do celu dowiązania. Zapis do celu dowiązania
    symbolicznego nie jest obsługiwane pod Windows.

    Dos2unix powstał na podstawie narzędzia dos2unix z systemu
    SunOS/Solaris. Jest jedna istotna różnica w stosunku do oryginalnej
    wersji z SunOS-a/Solarisa: ta wersja domyślnie wykonuje konwersję w
    miejscu (tryb starego pliku), podczas gdy oryginalna obsługiwała tylko
    konwersję parami (tryb nowego pliku) - p. także opcje "-o" i "-n".
    Ponadto wersja z SunOS-a/Solarisa domyślnie wykonuje konwersję w trybie
    *iso*, podczas gdy ta wersja domyślnie wykonuje konwersję w trybie
    *ascii*.

OPCJE
    --  Potraktowanie wszystkich kolejnych opcji jako nazw plików. Tej opcji
        należy użyć, aby przekonwertować pliki, których nazwy zaczynają się
        od minusa. Przykładowo, aby przekonwertować plik o nazwie "-foo",
        można użyć polecenia:

            dos2unix -- -foo

        Lub w trybie nowego pliku:

            dos2unix -n -- -foo wynik.txt

    --allow-chown
        Zezwolenie na zmianę właściciela w trybie starego pliku.

        W przypadku użycia tej opcji, konwersja nie zostanie przerwana,
        jeśli nie ma możliwości zachowania właściciela i/lub grupy
        oryginalnego pliku w trybie starego pliku. Konwersja będzie
        kontynuowana, a przekonwertowany plik będzie miał tego samego
        właściciela, jakiego by miał w trybie nowego pliku. P. także opcje
        "-o" i "-n". Opcja jest dostępna tylko wtedy, gdy dos2unix ma
        obsługę zachowywania użytkownika i grupy plików.

    -ascii
        Domyślny tryb konwersji. Więcej w sekcji TRYBY KONWERSJI.

    -iso
        Konwersja między zestawami znaków DOS i ISO-8859-1. Więcej w sekcji
        TRYBY KONWERSJI.

    -1252
        Użycie strony kodowej Windows 1252 (zachodnioeuropejskiej).

    -437
        Użycie strony kodowej DOS 437 (US). Jest to domyślna strona kodowa
        używana przy konwersji ISO.

    -850
        Użycie strony kodowej DOS 850 (zachodnioeuropejskiej).

    -860
        Użycie strony kodowej DOS 860 (portugalskiej).

    -863
        Użycie strony kodowej DOS 863 (kanadyjskiej francuskiej).

    -865
        Użycie strony kodowej DOS 865 (nordyckiej).

    -7  Konwersja znaków 8-bitowych do przestrzeni 7-bitowej.

    -b, --keep-bom
        Zachowanie znaku BOM (Byte Order Makr). Jeżeli plik wejściowy
        zawiera BOM, powoduje zapisanie go w pliku wyjściowym. Jest to
        domyślne zachowanie przy konwersji na DOS-owe końce linii. P. także
        opcja "-r".

    -c, --convmode TRYB_KONW
        Ustawienie trybu konwersji. TRYB_KONW to jeden z: *ascii*, *7bit*,
        *iso*, *mac*, przy czym domyślny jest ascii.

    -D, --display-enc KODOWANIE
        Ustawienie kodowania wyświetlanego tekstu. KODOWANIE to jedno z:
        *ansi*, *unicode*, *unicodebom*, *utf8*, *utf8bom*, przy czym
        domyślne to ansi.

        Ta opcja jest dostępna wyłączenie w programie dos2unix dla Windows z
        obsługą nazw plików Unicode. Nie ma wpływu na same nawy
        odczytywanych i zapisywanych plików, a jedynie na sposób ich
        wyświetlania.

        Istnieje kilka sposobów wyświetlania tekstu w konsoli Windows w
        zależności od kodowania tekstu. Wszystkie mają swoje zalety i wady.

        ansi
            Domyślna metoda programu dos2unix to stosowanie tekstu
            kodowanego w ANSI. Zaletą jest wsteczna zgodność. Działa z
            fontami rastrowymi, jak i TrueType. W niektórych rejonach może
            być potrzeba zmiany aktywnej strony kodowej DOS OEM na systemową
            stronę kodową Windows ANSI przy użyciu polecenia "chcp",
            ponieważ dos2unix wykorzystuje systemową stronę kodową Windows.

            Wadą kodowania ansi jest fakt, że międzynarodowe nazwy plików ze
            znakami spoza domyślnej systemowej strony kodowej nie są
            wyświetlane właściwie. Można zamiast tego zobaczyć znak
            zapytania albo niewłaściwy symbol. Jeżeli nie pracujemy z obcymi
            nazwami plików, ta metoda jest poprawna.

        unicode, unicodebom
            Zaletą kodowania unicode (windowsową nazwą dla UTF-16) jest
            (zwykle) właściwe wyświetlanie tekstu. Nie ma potrzeby zmiany
            aktywnej strony kodowej. Może być potrzeba zmiany fontu konsoli
            na font TrueType, aby znaki międzynarodowe były wyświetlane
            poprawnie. Jeśli znak nie jest obecny w foncie TrueType, zwykle
            widać mały kwadrat, czasami ze znakiem zapytania w środku.

            W przypadku używania konsoli ConEmu cały tekst jest wyświetlany
            poprawnie, ponieważ ConEmu automatycznie wybiera dobry font.

            Wadą kodowania unicode jest niezgodność z ASCII. Wyjście nie
            jest łatwe do obsłużenia w przypadku przekierowania do innego
            programu lub pliku.

            W przypadku użycia metody "unicodebom", tekst w unikodzie jest
            poprzedzony znakiem BOM (Byte Order Mark). BOM jest wymagany do
            poprawnego przekierowania lub przekazywania przez potok w
            powłoce PowerShell.

        utf8, utf8bom
            Zaletą kodowania utf8 jest zgodność z ASCII. Trzeba ustawić font
            konsoli na font TrueType. Przy użyciu fontu TrueType tekst jest
            wyświetlany podobnie do kodowania "unicode".

            Wadą jest fakt, że w przypadku używania domyślnego fontu
            rastrowego, wszystkie znaki spoza ASCII są wyświetlane
            niepoprawnie. Nie tylko unikodowe nazwy plików, ale także
            przetłumaczone komunikaty stają się nieczytelne. W Windows
            skonfigurowanym dla rejonu Azji Wschodniej widać dużo migotania
            konsoli w trakcie wyświetlania komunikatów.

            W konsoli ConEmu metoda kodowania utf8 działa dobrze.

            W przypadku użycia metody "utf8bom", tekst w UTF-8 jest
            poprzedzony znakiem BOM (Byte Order Mark). BOM jest wymagany do
            poprawnego przekierowania lub przekazywania przez potok w
            powłoce PowerShell.

        Domyślne kodowanie można zmienić przy użyciu zmiennej środowiskowej
        DOS2UNIX_DISPLAY_ENC, ustawiając ją na "unicode", "unicodebom",
        "utf8" lub "utf8bom".

    -e, --add-eol
        Dodawanie znaku końca linii do ostatniej linii, jeśli ta go nie
        zawiera. Działa przy każdej konwersji.

        Pliki przekształcone z formatu DOS do Uniksa mogą nie mieć w
        ostatnim wierszu znaku końca linii. Istnieją edytory tekstu
        zapisujące pliki tekstowe bez znaku końca wiersza w ostatniej linii.
        Niektóre programy uniksowe mają problemy przy przetwarzaniu takich
        plików, ponieważ standard POSIX określa, że każdy wiersz pliku
        tekstowego musi kończyć się znakiem końca linii. Na przykład
        łączenie plików może nie dać oczekiwanego wyniku.

    -f, --force
        Wymuszenie konwersji plików binarnych.

    -gb, --gb18030
        Pod Windows pliki w UTF-16 są domyślnie konwertowane do UTF-8,
        niezależnie od ustawienia lokalizacji. Ta opcja pozwala
        przekonwertować pliki w UTF-16 do GB18030. Opcja jest dostępna tylko
        pod Windows, więcej w sekcji dotyczącej GB18030.

    -h, --help
        Wyświetlenie opisu i zakończenie.

    -i[FLAGI], --info[=FLAGI] PLIK ...
        Wyświetlenie informacji o pliku. Konwersja nie jest wykonywana.

        Wypisywane są następujące informacje, w tej kolejności: liczba
        DOS-owych końców linii, liczba uniksowych końców linii, liczba
        macowych końców linii, znacznik BOM, tekstowy lub binarny, nazwa
        pliku.

        Przykładowe wyjście:

             6       0       0  no_bom    text    dos.txt
             0       6       0  no_bom    text    unix.txt
             0       0       6  no_bom    text    mac.txt
             6       6       6  no_bom    text    mixed.txt
            50       0       0  UTF-16LE  text    utf16le.txt
             0      50       0  no_bom    text    utf8unix.txt
            50       0       0  UTF-8     text    utf8dos.txt
             2     418     219  no_bom    binary  dos2unix.exe

        Uwaga: czasami plik binarny może być błędnie rozpoznany jako
        tekstowy. P. także opcja "-s".

        Jeśli dodatkowo użyta jest opcja "-e" lub "--add-eol", wypisywany
        jest także rodzaj końca linii z ostatniej linii, lub "noeol", jeśli
        nie ma żadnego.

        Przykładowe wyjście:

             6       0       0  no_bom    text   dos     dos.txt
             0       6       0  no_bom    text   unix    unix.txt
             0       0       6  no_bom    text   mac     mac.txt
             1       0       0  no_bom    text   noeol   noeol_dos.txt
        Opcjonalnie można ustawić dodatkowe flagi, aby zmienić wyjście. Można dodać jedną lub więcej flag.

        0   Wypisanie wierszy informacji o pliku zakończonych znakiem NUL
            zamiast znaku nowej linii. Pozwala to na poprawną interpretację
            nazw plików zawierających spacje lub cudzysłowy w przypadku
            użycia flagi c. Flagi należy używać w połączeniu z opcją -0 lub
            "--null" programu xargs(1).

        d   Wypisanie liczby DOS-owych końców linii.

        u   Wypisanie liczby uniksowych końców linii.

        m   Wypisanie liczby macowych końców linii.

        b   Wypisanie znacznika BOM.

        t   Wypisanie, czy plik jest tekstowy, czy binarny.

        e   Wypisanie rodzaju końca wiersza w ostatnim wierszu, lub "noeol",
            jeśli nie ma żadnego.

        c   Wypisanie tylko plików, które zostałyby przekonwertowane.

            Z flagą "c" dos2unix wypisze tylko pliki zawierające DOS-owe
            końce linii, a unix2dos wypisze tylko nazwy plików zawierających
            uniksowe końce linii.

            Jeśli dodatkowo użyta jest opcja "-e" lub "--add-eol",
            wypisywane są także pliki bez zakończenia ostatniej linii.

        h   Wypisanie nagłówka.

        p   Wyświetlanie nazw plików bez ścieżki.

        Przykłady:

        Pokazanie informacji o wszystkich plikach *.txt:

            dos2unix -i *.txt

        Pokazanie tylko liczby DOS-owych i uniksowych końców linii:

            dos2unix -idu *.txt

        Pokazanie tylko znacznika BOM:

            dos2unix --info=b *.txt

        Wypisanie listy plików zawierających DOS-owe końce linii:

            dos2unix -ic *.txt

        Wypisanie listy plików zawierających uniksowe końce linii:

            unix2dos -ic *.txt

        Wypisanie listy plików zawierających DOS-owe końce linii lub bez
        znaku końca w ostatniej linii:

            dos2unix -e -ic *.txt

        Konwersja tylko plików mających DOS-owe końce linii, pozostawienie
        pozostałych bez zmian:

            dos2unix -ic0 *.txt | xargs -0 dos2unix

        Wyszukanie plików tekstowych zawierających DOS-owe końce linii:

            find -name '*.txt' -print0 | xargs -0 dos2unix -ic

    -k, --keepdate
        Zachowanie znacznika czasu pliku wyjściowego takiego samego, jak
        pliku wejściowego.

    -L, --license
        Wyświetlenie licencji programu.

    -l, --newline
        Dodanie dodatkowego znaku końca linii.

        dos2unix: tylko DOS-owe znaki końca linii są zamieniane na dwa
        uniksowe. W trybie Mac tylko macowe znaki końca linii są zamieniane
        na dwa uniksowe.

        unix2dos: tylko uniksowe znaki końca linii są zamieniane na dwa
        DOS-owe. W trybie Mac uniksowe znaki końca linii są zamieniane na
        dwa macowe.

    -m, --add-bom
        Zapisanie znacznika BOM (Byte Order Mark) w pliku wyjściowym.
        Domyślnie zapisywany jest BOM UTF-8.

        Jeśli plik wejściowy jest w kodowaniu UTF-16 i użyto opcji "-u",
        zostanie zapisany BOM UTF-16.

        Nigdy nie należy używać tej opcji, jeśli kodowanie wyjściowe jest
        inne niż UTF-8, UTF-16 lub GB18030. Więcej w sekcji UNICODE.

    -n, --newfile PLIK_WEJ PLIK_WYJ ...
        Tryb nowego pliku. Konwersja PLIKU_WEJ z zapisem wyjścia do
        PLIKU_WYJ. Nazwy plików muszą być podane parami, a masek *nie*
        należy używać, gdyż *spowoduje* to utratę plików.

        Osoba uruchamiająca konwersję w trybie nowego pliku (par) będzie
        właścicielem przekonwertowanego pliku. Prawa odczytu/zapisu nowego
        pliku będą pochodziły z praw pliku oryginalnego po odjęciu umask(1)
        osoby uruchamiającej konwersję.

    --no-allow-chown
        Bez zezwolenia na zmianę właściciela pliku w trybie starego pliku
        (domyślne).

        Przerwanie konwersji, jeżeli użytkownik lub grupa oryginalnego pliku
        nie może być zachowana w trybie starego pliku. P. także opcje "-o"
        oraz "-n". Ta opcja jest dostępna tylko jeśli dos2unix ma obsługę
        zachowywania użytkownika i grupy plików.

    --no-add-eol
        Bez dodawania znaku końca linii do ostatniego wiersza, jeśli ten go
        nie zawiera.

    -O, --to-stdout
        Zapis na standardowe wyjście, jak filtr uniksowy. Opcja "-o"
        przywróci tryb starego pliku (zamianę z miejscu).

        W połączeniu z opcją "-e" pliki mogą być właściwie łączone - bez
        połączonych ostatnich i pierwszych linii oraz znaków unikodowych BOM
        w środku połączonego pliku. Przykład:

            dos2unix -e -O plik1.txt plik2.txt > wynik.txt

    -o, --oldfile PLIK ...
        Tryb starego pliku. Konwersja PLIKU i nadpisanie go wyjściem.
        Program działa domyślnie w tym trybie. Można używać masek.

        W trybie starego pliku (w miejscu) przekonwertowany plik otrzymuje
        tego samego właściciela, grupę oraz prawa odczytu/zapisu, jak plik
        oryginalny - także wtedy, gdy plik jest konwertowany przez innego
        użytkownika, mającego prawo zapisu do pliku (np. przez użytkownika
        root). Konwersja zostanie przerwana, jeśli nie będzie możliwe
        zachowanie oryginalnych wartości. Zmiana właściciela mogłaby
        oznaczać, że pierwotny właściciel nie może już odczytać pliku.
        Zmiana grupy mogłaby być zagrożeniem bezpieczeństwa, plik mógłby być
        czytelny dla nie zamierzonych osób. Zachowanie właściciela, grupy i
        praw odczytu/zapisu jest obsługiwane tylko na Uniksie.

        Aby sprawdzić, czy doswunix ma obsługę zachowywania użytkownika i
        grupy plików, można napisać "dos2unix -V".

        Konwersja jest wykonywana zawsze przy użyciu pliku tymczasowego.
        Jeśli w trakcie konwersji wystąpi błąd, plik tymczasowy jest
        usuwany, a plik oryginalny pozostaje nietknięty. Jeśli konwersja się
        powiedzie, plik oryginalny jest zastępowany plikiem tymczasowym.
        Można mieć prawa zapisu do pliku oryginalnego, ale brak uprawnień,
        aby nadać tego samego właściciela i/lub grupę, co plik oryginalny,
        plikowi tymczasowemu. Oznacza to, że nie można zachować użytkownika
        i/lub grupy oryginalnego pliku. W takim przypadku można użyć opcji
        "--allow-chown", aby kontynuować konwersję:

            dos2unix --allow-chown foo.txt

        Inny sposób to użycie trybu nowego pliku:

            dos2unix -n foo.txt foo.txt

        Zaletą opcji "--allow-chown" jest możliwość użycia masek oraz
        zachowanie właściciela w miarę możliwości.

    -q, --quiet
        Tryb cichy. Pominięcie wszystkich ostrzeżeń i komunikatów. Zwracanym
        kodem jest zero, chyba że podano błędne opcje linii poleceń.

    -r, --remove-bom
        Usunięcie znaków BOM (Byte Order Mark). Bez zapisywania BOM do pliku
        wyjściowego. Jest to domyślne zachowanie przy konwersji na uniksowe
        końce linii. P. także opcja "-b".

    -s, --safe
        Pominięcie plików binarnych (domyślne).

        Pomijanie plików binarnych ma na celu zapobieżenie przypadkowym
        błędom. Uwaga: wykrywanie plików binarnych nie jest w 100% odporne
        na błędy. Pliki wejściowe są przeszukiwane pod kątem symboli
        binarnych, które zwykle nie występują w plikach tekstowych. Może się
        zdarzyć, że plik binarny zawiera tylko zwykłe znaki tekstowe. Taki
        plik binarny będzie błędnie widziany jako plik tekstowy.

    -u, --keep-utf16
        Zachowanie oryginalnego kodowania pliku wejściowego UTF-16. Plik
        wyjściowy zostanie zapisany w tym samym kodowaniu UTF-16 (little lub
        big endian), co plik wejściowy. Zapobiega to przekształceniu do
        UTF-8. Do pliku zostanie zapisany odpowiedni znacznik BOM UTF-16. Tę
        opcję można wyłączyć opcją "-ascii".

    -ul, --assume-utf16le
        Przyjęcie, że format pliku wejściowego to UTF-16LE.

        Jeśli w pliku wejściowym jest znacznik BOM (Byte Order Mark), ma on
        priorytet nad tą opcją.

        Jeśli przyjęto błędne założenie (plik wejściowy nie jest w formacie
        UTF-16LE), a konwersja się uda, wynikiem będzie plik wyjściowy UTF-8
        ze złym tekstem. Konwersję tę można odwrócić przy użyciu polecenia
        iconv(1) do konwersji wyjścia UTF-8 z powrotem do UTF-16LE.
        Przywróci to plik oryginalny.

        Przyjęcie UTF-16LE działa jako *tryb konwersji*. Przy przełączeniu
        na domyślny tryb *ascii* przyjęcie UTF-16LE jest wyłączane.

    -ub, --assume-utf16be
        Przyjęcie, że format pliku wejściowego to UTF-16BE.

        Ta opcja działa analogicznie do "-ul".

    -v, --verbose
        Wyświetlanie szczegółowych komunikatów. Wyświetlane śa dodatkowe
        informacje o znacznikach BOM (Byte Order Mark) oraz liczbie
        przekonwertowanych końców linii.

    -F, --follow-symlink
        Podążanie za dowiązaniami symbolicznymi i konwertowanie ich celów

    -R, --replace-symlink
        Zastępowanie dowiązań symbolicznych przekonwertowanymi plikami
        (oryginalne pliki docelowe pozostają bez zmian).

    -S, --skip-symlink
        Pozostawienie dowiązań symbolicznych i celów bez zmian (domyślne).

    -V, --version
        Wyświetlenie informacji o wersji i zakończenie.

TRYB MAC
    Domyślnie znaki końca linii są konwertowane z DOS-a do Uniksa i
    odwrotnie. Znaki końca linii systemu Mac nie są konwertowane.

    W trybie Mac znaki końca linii są konwertowane z formatu Maca do Uniksa
    i odwrotnie. Znaki końca linii systemu DOS nie są zmieniane.

    Aby uruchomić program w trybie Mac, należy użyć opcji linii poleceń "-c
    mac" albo użyć poleceń "mac2unix" lub "unix2mac".

TRYBY KONWERSJI
    ascii
        To jest domyślny tryb konwersji. Służy do konwersji plików
        kodowanych ASCII lub zgodnie z ASCII, np. UTF-8. Włączenie trybu
        ascii wyłącza tryby 7bit i iso.

        Jeśli dos2unix ma obsługę UTF-16, pliki kodowane UTF-16 są
        konwertowane do bieżącego kodowania w systamach zgodnych z POSIX lub
        do UTF-8 na Windows. Włączenie trybu ascii wyłącza opcję
        zachowywania kodowania UTF-16 ("-u") oraz opcje zakładające wejście
        UTF-16 ("-ul" oraz "-ub"). Aby sprawdzić, czy dos2unix ma obsługę
        UTF-16, można wywołać "dos2unix -V". Więcej w sekcji UNICODE.

    7bit
        W tym trybie wszystkie znaki 8-bitowe spoza ASCII (o wartościach od
        128 do 255) są konwertowane do przestrzeni 7-bitowej.

    iso W tym trybie znaki są konwertowane między zestawem znaków DOS
        (stroną kodową) a zestawem znaków ISO-8859-1 (Latin-1) używanym na
        Uniksie. Znaki DOS-owe nie mające odpowiednika w ISO-8859-1, których
        nie da się przekonwertować, są zamieniane na kropkę. To samo dotyczy
        znaków ISO-8859-1 bez odpowiednika w DOS-ie.

        Jeśli używana jest tylko opcja "-iso", dos2unix próbuje wykryć
        aktywną stronę kodową. Jeśli nie jest to możliwe, dos2unix używa
        domyślnej strony kodowej CP437, stosowanej głównie w USA. Aby
        wymusić określoną stronę kodową, należy użyć opcji -437 (US), -850
        (zachodnioeuropejska), -860 (portugalska), -863 (kanadyjska
        francuska) lub -865 (nordycka). Ponadto obsługiwana jest strona
        kodowa Windows CP1252 (zachodnioeuropejska) przy użyciu opcji -1252.
        W przypadku innych stron kodowych można użyć narzędzia dos2unix wraz
        z iconv(1). Iconv potrafi konwertować między wieloma kodowaniami
        znaków.

        Nigdy nie należy używać konwersji ISO na plikach tekstowych w
        Unicode. Uszkodziłaby pliki kodowane UTF-8.

        Kilka przykładów:

        Konwersja z domyślnej strony kodowej DOS do uniksowego Latin-1:

            dos2unix -iso -n wejście.txt wynik.txt

        Konwersja ze strony kodowej DOS CP850 do uniksowego Latin-1:

            dos2unix -850 -n wejście.txt wynik.txt

        Konwersja ze strony kodowej Windows CP1252 do uniksowego Latin-1:

            dos2unix -1252 -n wejście.txt wynik.txt

        Konwersja ze strony kodowej Windows CP1252 do uniksowego UTF-8
        (Unicode):

            iconv -f CP1252 -t UTF-8 wejście.txt | dos2unix > wynik.txt

        Konwersa z uniksowego Latin-1 do domyślnej strony kodowej DOS:

            unix2dos -iso -n wejście.txt wynik.txt

        Konwersja z uniksowego Latin-1 do strony kodowej DOS CP850:

            unix2dos -850 -n wejście.txt wynik.txt

        Konwersja z uniksowego Latin-1 do strony kodowej Windows CP1252:

            unix2dos -1252 -n wejście.txt wynik.txt

        Konwersja z uniksowego UTF-8 (Unicode) do strony kodowej Windows
        CP1252:

            unix2dos < wejście.txt | iconv -f UTF-8 -t CP1252 > wynik.txt

        Więcej pod adresem <http://czyborra.com/charsets/codepages.html>
        oraz <http://czyborra.com/charsets/iso8859.html>.

UNICODE
  Kodowania
    Istnieją różne kodowania Unicode. Pod Uniksem i Linuksem pliki Unicode
    są zwykle kodowane z użyciem UTF-8. Pod Windows pliki tekstowe Unicode
    mogą być kodowane w UTF-8, UTF-16, UTF-16 big-endian, ale przeważnie są
    kodowane w UTF-16.

  Konwersje
    Pliki tekstowe Unicode mogą mieć znaki końca linii systemu DOS, Unix lub
    Mac, podobnie jak pliki tekstowe ASCII.

    Wszystkie wersje dos2unix i unix2dos potrafią konwertować pliki kodowane
    UTF-8, ponieważ UTF-8 jest wstecznie zgodne z ASCII.

    Dos2unix i unix2dos z obsługą Unicode UTF-16 potrafią odczytywać pliki
    tekstowe kodowane UTF-16 little- oraz big-endian. Aby sprawdzić, czy
    dos2unix został zbudowany z obsługą UTF-16, należy napisać "dos2unix
    -V".

    Pod Uniksem/Linuksem pliki w kodowaniu UTF-16 są konwertowane do
    kodowania znaków ustawionej lokalizacji. Kodowanie znaków dla
    lokalizacji można sprawdzić poleceniem locale(1). Jeśli konwersja nie
    jest możliwa, wystąpi błąd, a plik zostanie pominięty.

    Pod Windows pliki UTF-16 są domyślnie konwertowane do UTF-8. Pliki
    tekstkowe w kodowaniu UTF-8 są dobrze obsługiwane zarówno pod Windows,
    jak i Uniksem/Linuksem.

    Kodowania UTF-16 i UTF-8 są w pełni zgodne, konwersja nie spowoduje
    utraty żadnej części tekstu. W przypadku wystąpienia błędu konwersji, na
    przykład w przypadku błędu w pliku wejściowym UTF-16, plik zostanie
    pominięty.

    W przypadku użycia opcji "-u", plik wejściowy zostanie zapisany w tym
    samym kodowaniu UTF-16, co plik wejściowy. Opcja "-u" zapobiega
    konwersji do UTF-8.

    Dos2unix oraz unix2dos nie mają opcji pozwalającej na konwersję plików
    UTF-8 do UTF-16.

    Tryby konwersji ISO i 7-bit nie działają na plikach UTF-16.

  Znacznik BOM
    W systemie Windows pliki tekstowe zwykle zawierają znacznik BOM (Byte
    Order Mark), ponieważ wiele programów dla Windows (w tym Notepad) dodaje
    domyślnie znaczniki BOM. Więcej informacji można znaleźć pod adresem
    <https://pl.wikipedia.org/wiki/BOM_(informatyka)>.

    Pod Uniksem pliki Unicode zwykle nie mają znacznika BOM. Pliki tekstowe
    są traktowane jako kodowane zgodnie z kodowaniem znaków ustawionej
    lokalizacji.

    Dos2unix potrafi wykryć tylko, czy plik jest w formacie UTF-16, jeśli
    zawiera znacznik BOM. Jeśli plik UTF-16 nie ma tego znacznika, dos2unix
    potraktuje plik jako binarny.

    Do konwersji pliku UTF-16 bez znacznika BOM można użyć opcji "-ul" lub
    "-ub".

    Dos2unix nie zapisuje domyślnie znaku BOM w pliku wyjściowym. Z opcją
    "-b" Dos2unix zapisuje BOM, jeśli plik wejściowy zawiera BOM.

    Unix2dos domyślnie zapisuje znaczniki BOM w pliku wyjściowym, jeśli plik
    wejściowy ma BOM. Aby usunąć BOM, można użyć opcji "-r".

    Dos2unix oraz unix2dos zawsze zapisują znaczniki BOM, jeśli użyta
    zostanie opcja "-m".

  Unikodowe nazwy plików w Windows
    Dos2unix ma opcjonalną obsługę odczytu i zapisu nazw plików Unicode w
    linii poleceń Windows. Oznacza to, że dos2unix potrafi otwierać pliki
    zawierające w nazwie znaki spoza domyślnej systemowej strony kodowej
    ANSI. Aby sprawdzić, czy dos2unix dla Windows został zbudowany z obsługą
    nazw plików Unicode, można wpisać "dos2unix -V".

    Przy wyświetlaniu nazw plików Unicode w konsoli Windows występuje kilka
    problemów. Więcej informacji w opisie opcji "-D", "--display-enc". Nazwy
    plików mogą być wyświetlane błędnie na konsoli, ale pliki będą
    zapisywane z poprawną nazwą.

  Przykłady Unicode
    Konwersja pliku UTF-16 (z BOM) z formatu Windows do uniksowego UTF-8:

        dos2unix -n wejście.txt wynik.txt

    Konwersja pliku UTF-16LE (bez BOM) z formatu Windows do uniksowego
    UTF-8:

        dos2unix -ul -n wejście.txt wynik.txt

    Konwersja z uniksowego UTF-8 do UTF-8 z BOM dla Windows:

        unix2dos -m -n wejście.txt wynik.txt

    Konwersja z uniksowego UTF-8 do UTF-16 dla Windows:

        unix2dos < wejście.txt | iconv -f UTF-8 -t UTF-16 > wynik.txt

GB18030
    GB18030 to standard urzędowy w Chinach. Obowiązkowy podzbiór standardu
    GB18030 jest oficjalnym wymaganiem każdego oprogramowania sprzedawanego
    w Chinach. Więcej pod adresem <https://en.wikipedia.org/wiki/GB_18030>.

    GB18030 jest w pełni zgodny z Unicode i może być uważany za format
    transformacji unikodu. Podobnie jak UTF-8, GB18030 jest zgodny z ASCII.
    Jest także zgodny ze stroną kodową Windows 936, znaną też jako GBK.

    Pod Uniksem/Linuksem pliki UTF-16 są konwertowane do GB18030, jeśli
    kodowanie dla lokalizacji jest ustawione na GB18030. Uwaga: będzie to
    działać tylko, jeśli lokalizacja jest obsługiwana przez system. Listę
    obsługiwanych lokalizacji można sprawdzić poleceniem "locale -a".

    Pod Windows w celu konwersji plików UTF-16 do GB18030 należy użyć opcji
    "-gb".

    Pliki w kodowaniu GB18030 mogą mieć znacznik BOM, podobnie jak pliki w
    Unicode.

PRZYKŁADY
    Odczyt ze standardowego wejścia i zapis na standardowe wyjście:

        dos2unix < a.txt
        cat a.txt | dos2unix

    Konwersja i zastąpienie a.txt; konwersja i zastąpienie b.txt:

        dos2unix a.txt b.txt
        dos2unix -o a.txt b.txt

    Konwersja i zastąpienie a.txt w trybie ascii:

        dos2unix a.txt

    Konwersja i zastąpienie a.txt w trybie ascii; konwersja i zastąpienie
    b.txt w trybie 7-bitowym:

        dos2unix a.txt -c 7bit b.txt
        dos2unix -c ascii a.txt -c 7bit b.txt
        dos2unix -ascii a.txt -7 b.txt

    Konwersja a.txt z formatu Mac do formatu uniksowego:

        dos2unix -c mac a.txt
        mac2unix a.txt

    Konwersja a.txt z formatu uniksowego do formatu Mac:

        unix2dos -c mac a.txt
        unix2mac a.txt

    Konwersja i zastąpienie a.txt z zachowaniem oryginalnego znacznika
    czasu:

        dos2unix -k a.txt
        dos2unix -k -o a.txt

    Konwersja a.txt i zapis do e.txt:

        dos2unix -n a.txt e.txt

    Konwersja a.txt i zapis do e.txt z zachowaniem znacznika czasu e.txt
    takiego, jak a.txt:

        dos2unix -k -n a.txt e.txt

    Konwersja i zastąpienie a.txt; konwersja b.txt i zapis do e.txt:

        dos2unix a.txt -n b.txt e.txt
        dos2unix -o a.txt -n b.txt e.txt

    Konwersja c.txt i zapis do e.txt; konwersja i zastąpienie a.txt;
    konwersja i zastąpienie b.txt; konwersja d.txt i zapis do f.txt:

        dos2unix -n c.txt e.txt -o a.txt b.txt -n d.txt f.txt

KONWERSJA REKURENCYJNA
    W powłoce uniksowej można użyć poleceń find(1) i xargs(1) do
    rekurencyjnego uruchomienia dos2unix na wszystkich plikach tekstowych w
    strukturze drzewa katalogów. Na przykład, aby przekonwertować wszystkie
    pliki .txt w drzewie katalogów poniżej katalogu bieżącego, należy
    napisać:

        find . -name '*.txt' -print0 |xargs -0 dos2unix

    Opcja "-print0" polecenia find(1) i odpowiadająca jej opcja -0 polecenia
    xargs(1) są potrzebne, jeśli istnieją pliki ze spacjami lub cudzysłowami
    w nazwie. W przeciwnym wypadku opcje te można pominąć. Inny sposób to
    użycie find(1) z opcją "-exec":

        find . -name '*.txt' -exec dos2unix {} \;

    Z poziomu linii poleceń Windows można użyć następującego polecenia:

        for /R %G in (*.txt) do dos2unix "%G"

    Użytkownicy powłoki PowerShell mogą użyć następującego polecenia w
    Windows PowerShell:

        get-childitem -path . -filter '*.txt' -recurse | foreach-object {dos2unix $_.Fullname}

LOKALIZACJA
    LANG
        Główny język wybiera się zmienną środowiskową LANG. Zmienna LANG
        składa się z kilku części. Pierwsza część to małe litery oznaczające
        kod języka. Druga część jest opcjonalna i zawiera kod kraju pisany
        wielkimi literami, poprzedzony podkreśleniem. Jest także opcjonalna
        trzecia część: kodowanie znaków, poprzedzone kropką. Kilka
        przykładów dla powłok zgodnych ze standardem POSIX:

            export LANG=nl               holenderski
            export LANG=nl_NL            holenderski, Holandia
            export LANG=nl_BE            holenderski, Belgia
            export LANG=es_ES            hiszpański, Hiszpania
            export LANG=es_MX            hiszpański, Meksyk
            export LANG=en_US.iso88591   angielski, USA, kodowanie Latin-1
            export LANG=en_GB.UTF-8      angielski, Wlk. Brytania, kodowanie UTF-8

        Pełną listę kodów języków i krajów można znaleźć w podręczniku do
        gettexta:
        <https://www.gnu.org/software/gettext/manual/html_node/Usual-Languag
        e-Codes.html>

        W systemach uniksowych do uzyskania informacji dotyczących
        lokalizacji można użyć polecenia locale(1).

    LANGUAGE
        Przy użyciu zmiennej środowiskowej LANGUAGE można określić listę
        języków wg priorytetu, oddzielonych dwukropkami. Dos2unix przyjmuje
        pierwszeństwo zmiennej LANGUAGE nad LANG. Na przykład, najpierw
        holenderski, następnie niemiecki: "LANGUAGE=nl:de". Aby skorzystać z
        listy wg priorytetów ze zmiennej LANGUAGE, trzeba najpierw włączyć
        lokalizację przez ustawienie zmiennej LANG (lub LC_ALL) na wartość
        inną niż "C". Więcej informacji znajduje się w podręczniku do
        gettexta:
        <https://www.gnu.org/software/gettext/manual/html_node/The-LANGUAGE-
        variable.html>

        W przypadku wybrania niedostępnego języka, otrzymamy standardowe,
        angielskie komunikaty.

    DOS2UNIX_LOCALEDIR
        Przy użyciu zmiennej środowiskowej DOS2UNIX_LOCALEDIR, można
        nadpisać ustawienie LOCALEDIR z czasu kompilacji. LOCALEDIR to
        katalog używany do znalezienia plików lokalizacji. Domyślną
        wartością dla GNU jest "/usr/local/share/locale". Opcja --version
        wyświetla używaną wartość LOCALEDIR.

        Przykład (dla powłoki POSIX):

            export DOS2UNIX_LOCALEDIR=$HOME/share/locale

WARTOŚĆ ZWRACANA
    W przypadku powodzenia zwracane jest zero. Jeśli wystąpi błąd systemowy,
    zwracany jest ostatni błąd systemowy. W przypadku innych błędów zwracane
    jest 1.

    Wartość zwracana w trybie cichym to zawsze zero, z wyjątkiem sytuacji
    podania błędnych opcji linii poleceń.

STANDARDY
    <https://pl.wikipedia.org/wiki/Plik_tekstowy>

    <https://en.wikipedia.org/wiki/Carriage_return>

    <https://pl.wikipedia.org/wiki/Koniec_linii>

    <https://pl.wikipedia.org/wiki/Unicode>

AUTORZY
    Benjamin Lin <<EMAIL>>; Bernd Johannes Wuebben (tryb
    mac2unix) <<EMAIL>>; Christian Wurll (dodawanie dodatkowej nowej
    linii) <<EMAIL>>; Erwin Waterlander <<EMAIL>>
    (prowadzący)

    Strona projektu: <https://waterlan.home.xs4all.nl/dos2unix.html>

    Strona SourceForge: <https://sourceforge.net/projects/dos2unix/>

ZOBACZ TAKŻE
    file(1) find(1) iconv(1) locale(1) xargs(1)

