.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_ext_import_crl_dist_points" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_ext_import_crl_dist_points \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_ext_import_crl_dist_points(const gnutls_datum_t * " ext ", gnutls_x509_crl_dist_points_t " cdp ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * ext" 12
the DER encoded extension data
.IP "gnutls_x509_crl_dist_points_t cdp" 12
A pointer to an initialized CRL distribution points.
.IP "unsigned int flags" 12
should be zero
.SH "DESCRIPTION"
This function will extract the CRL distribution points extension (*********) 
and store it into the provided type.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
