.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_token_set_pin" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_token_set_pin \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_token_set_pin(const char * " token_url ", const char * " oldpin ", const char * " newpin ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const char * token_url" 12
A PKCS \fB11\fP URL specifying a token
.IP "const char * oldpin" 12
old user's PIN
.IP "const char * newpin" 12
new user's PIN
.IP "unsigned int flags" 12
one of \fBgnutls_pin_flag_t\fP.
.SH "DESCRIPTION"
This function will modify or set a user or administrator's PIN for
the given token.  If it is called to set a PIN for first time
the oldpin must be \fBNULL\fP. When setting the admin's PIN with the
\fBGNUTLS_PIN_SO\fP flag, the  \fIoldpin\fP value must be provided (this requirement
is relaxed after GnuTLS 3.6.5 since which the PIN will be requested if missing).
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
