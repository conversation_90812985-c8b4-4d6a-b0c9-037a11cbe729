/*
 * Copyright (C) 2008 Google (<PERSON><PERSON>)
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "ocidl.idl";
/* import "dxtrans.idl"; */
import "amstream.idl";
import "msxml.idl";

[
    object,
    uuid(0579154A-2B53-4994-B0D0-E773148EFF85),
    local,
    pointer_default(unique)
]
interface ISampleGrabberCB : IUnknown
{
    HRESULT SampleCB(
        double SampleTime,
        IMediaSample * pSample
    );
    HRESULT BufferCB(
        double SampleTime,
        BYTE * pBuffer,
        LONG BufferLen
    );
}

[
    object,
    uuid(6B652FFF-11FE-4fce-92AD-0266B5D7C78F),
    local,
    pointer_default(unique)
]
interface ISampleGrabber: IUnknown
{
    HRESULT SetOneShot(
        BOOL OneShot
    );

    HRESULT SetMediaType(
        const AM_MEDIA_TYPE * pType
    );

    HRESULT GetConnectedMediaType(
        AM_MEDIA_TYPE * pType
    );

    HRESULT SetBufferSamples(
        BOOL BufferThem
    );

    HRESULT GetCurrentBuffer(
        [in,out] LONG * pBufferSize,
        [out] LONG * pBuffer
    );

    HRESULT GetCurrentSample(
        [out] IMediaSample ** ppSample
    );

    HRESULT SetCallback(
        ISampleGrabberCB * pCallback,
        LONG WhichMethodToCallback
    );
}

[
    uuid(c1f400a0-3f08-11d3-9f0b-006008039e37)
]
coclass SampleGrabber
{
    [default] interface ISampleGrabber;
}

[
    object,
    uuid(65bd0710-24d2-4FF7-9324-ed2e5d3abafa),
    pointer_default(unique)
]
interface IMediaDet : IUnknown
{
    HRESULT get_Filter(
        [out] IUnknown* *pVal
    );

    HRESULT put_Filter(
        IUnknown* newVal
    );

    HRESULT get_OutputStreams(
        [out] LONG *pVal
    );

    HRESULT get_CurrentStream(
        [out] LONG *pVal
    );

    HRESULT put_CurrentStream(
        LONG newVal
    );

    HRESULT get_StreamType(
        [out] GUID *pVal
    );

    HRESULT get_StreamTypeB(
        [out] BSTR *pVal
    );

    HRESULT get_StreamLength(
        [out] double *pVal
    );

    HRESULT get_Filename(
        [out] BSTR *pVal
    );

    HRESULT put_Filename(
        BSTR newVal
    );

    HRESULT GetBitmapBits(
        double StreamTime,
        LONG * pBufferSize,
        char * pBuffer,
        LONG Width,
        LONG Height
    );

    HRESULT WriteBitmapBits(
        double StreamTime,
        LONG Width,
        LONG Height,
        BSTR Filename
    );

    HRESULT get_StreamMediaType(
        [out] AM_MEDIA_TYPE * pVal
    );

    HRESULT GetSampleGrabber(
        [out] ISampleGrabber ** ppVal
    );

    HRESULT get_FrameRate(
        [out] double *pVal
    );

    HRESULT EnterBitmapGrabMode(
        double SeekTime
    );
}

[
    uuid(65BD0711-24D2-4ff7-9324-ED2E5D3ABAFA),
]
coclass MediaDet
{
    [default] interface IMediaDet;
}

[
    object,
    uuid(288581E0-66CE-11d2-918F-00C0DF10D434),
    odl,
    pointer_default(unique)
]
interface IMediaLocator : IUnknown
{
    HRESULT FindMediaFile(
        BSTR input,
        BSTR filter,
        BSTR * output,
        long flags
    );

    HRESULT AddFoundLocation(
        BSTR dir
    );
}

typedef struct
{
    BSTR name;
    DISPID dispID;
    LONG nValues;
} DEXTER_PARAM;

typedef struct
{
    VARIANT v;
    REFERENCE_TIME rt;
    DWORD dwInterp;
} DEXTER_VALUE;

[
    object,
    uuid(AE9472BD-B0C3-11D2-8D24-00A0C9441E20),
    pointer_default(unique)
]
interface IPropertySetter : IUnknown
{
    HRESULT LoadXML(
        [in] IUnknown * pxml
    );

    HRESULT PrintXML(
        [out] char * xml,
        [in] int size,
        [out] int * printed,
        [in] int indent
    );

    HRESULT CloneProps(
        [out] IPropertySetter ** setter,
        [in] REFERENCE_TIME start,
        [in] REFERENCE_TIME stop
    );

    HRESULT AddProp(
        [in] DEXTER_PARAM param,
        [in] DEXTER_VALUE * value
    );

    HRESULT GetProps(
        [out] LONG * params,
        [out] DEXTER_PARAM ** param,
        [out] DEXTER_VALUE ** value
    );

    HRESULT FreeProps(
        [in] LONG params,
        [in] DEXTER_PARAM * param,
        [in] DEXTER_VALUE * value
    );

    HRESULT ClearProps();

    HRESULT SaveToBlob(
        [out] LONG * size,
        [out] BYTE ** blob
    );

    HRESULT LoadFromBlob(
        [in] LONG size,
        [in] BYTE * blob
    );

    HRESULT SetProps(
        [in] IUnknown * target,
        [in] REFERENCE_TIME now
    );
}

[
    object,
    uuid(E43E73A2-0EFA-11d3-9601-00A0C9441E20),
    odl,
    pointer_default(unique)
]
interface IAMErrorLog : IUnknown
{
    HRESULT LogError(
        long severity,
        BSTR error_str,
        long error_code,
        long hresult,
        [in] VARIANT * extra
    );
}

[
    object,
    uuid(963566DA-BE21-4eaf-88E9-35704F8F52A1),
    odl,
    pointer_default(unique)
]
interface IAMSetErrorLog : IUnknown
{
    [propget] HRESULT ErrorLog(
        [out, retval] IAMErrorLog ** log
    );

    [propput] HRESULT ErrorLog(
        [in] IAMErrorLog * log
    );
}

interface IAMTimeline;
interface IAMTimelineGroup;
interface IAMTimelineObj;
interface IAMTimelineSrc;

typedef enum
{
    TIMELINE_MAJOR_TYPE_COMPOSITE = 1,
    TIMELINE_MAJOR_TYPE_TRACK = 2,
    TIMELINE_MAJOR_TYPE_SOURCE = 4,
    TIMELINE_MAJOR_TYPE_TRANSITION = 8,
    TIMELINE_MAJOR_TYPE_EFFECT = 16,
    TIMELINE_MAJOR_TYPE_GROUP = 128
} TIMELINE_MAJOR_TYPE;

[
    object,
    uuid(78530B74-61F9-11D2-8CAD-00A024580902),
    odl,
    pointer_default(unique)
]
interface IAMTimeline : IUnknown
{
    HRESULT CreateEmptyNode(
        [out] IAMTimelineObj ** obj,
        TIMELINE_MAJOR_TYPE type
    );

    HRESULT AddGroup(
        IAMTimelineObj * group
    );

    HRESULT RemGroupFromList(
        IAMTimelineObj * group
    );

    HRESULT GetGroup(
        [out] IAMTimelineObj ** group,
        long index
    );

    HRESULT GetGroupCount(
        long * count
    );

    HRESULT ClearAllGroups();

    HRESULT GetInsertMode(
        long * mode
    );

    HRESULT SetInsertMode(
        long mode
    );

    HRESULT EnableTransitions(
        BOOL enabled
    );

    HRESULT TransitionsEnabled(
        BOOL * enabled
    );

    HRESULT EnableEffects(
        BOOL enabled
    );

    HRESULT EffectsEnabled(
        BOOL * enabled
    );

    HRESULT SetInterestRange(
        REFERENCE_TIME start,
        REFERENCE_TIME stop
    );

    HRESULT GetDuration(
        REFERENCE_TIME * duration
    );

    HRESULT GetDuration2(
        double * duration
    );

    HRESULT SetDefaultFPS(
        double fps
    );

    HRESULT GetDefaultFPS(
        double * fps
    );

    HRESULT IsDirty(
        BOOL * dirty
    );

    HRESULT GetDirtyRange(
        REFERENCE_TIME * start,
        REFERENCE_TIME * stop
    );

    HRESULT GetCountOfType(
        long group,
        long * value,
        long * value_with_comps,
        TIMELINE_MAJOR_TYPE type
    );

    HRESULT ValidateSourceNames(
        long flags,
        IMediaLocator * override,
        LONG_PTR notify_event
    );

    HRESULT SetDefaultTransition(
        GUID * guid
    );

    HRESULT GetDefaultTransition(
        GUID * guid
    );

    HRESULT SetDefaultEffect(
        GUID * guid
    );

    HRESULT GetDefaultEffect(
        GUID * guid
    );

    HRESULT SetDefaultTransitionB(
        BSTR guidb
    );

    HRESULT GetDefaultTransitionB(
        [out,retval] BSTR * guidb
    );

    HRESULT SetDefaultEffectB(
        BSTR guidb
    );

    HRESULT GetDefaultEffectB(
        [out,retval] BSTR * guidb
    );
}

[
    uuid(78530B75-61F9-11D2-8CAD-00A024580902)
]
coclass AMTimeline
{
    [default] interface IAMTimeline;
    interface IPersistStream;
    interface IAMSetErrorLog;
}

[
    object,
    uuid(9EED4F00-B8A6-11d2-8023-00C0DF10D434),
    odl,
    pointer_default(unique)
]
interface IAMTimelineGroup : IUnknown
{
    HRESULT SetTimeline(
        IAMTimeline * timeline
    );

    HRESULT GetTimeline(
        [out] IAMTimeline ** timeline
    );

    HRESULT GetPriority(
        long * priority
    );

    HRESULT GetMediaType(
        [out] AM_MEDIA_TYPE *
    );

    HRESULT SetMediaType(
        [in] AM_MEDIA_TYPE *
    );

    HRESULT SetOutputFPS(
        double fps
    );

    HRESULT GetOutputFPS(
        double * fps
    );

    HRESULT SetGroupName(
        BSTR name
    );

    HRESULT GetGroupName(
        [out,retval] BSTR * name
    );

    HRESULT SetPreviewMode(
        BOOL preview
    );

    HRESULT GetPreviewMode(
        BOOL * preview
    );

    HRESULT SetMediaTypeForVB(
        [in] long type
    );

    HRESULT GetOutputBuffering(
        [out] int * buffer
    );

    HRESULT SetOutputBuffering(
        [in] int buffer
    );

    HRESULT SetSmartRecompressFormat(
        long * format
    );

    HRESULT GetSmartRecompressFormat(
        long ** format
    );

    HRESULT IsSmartRecompressFormatSet(
        BOOL * set
    );

    HRESULT IsRecompressFormatDirty(
        BOOL * dirty
    );

    HRESULT ClearRecompressFormatDirty();

    HRESULT SetRecompFormatFromSource(
        IAMTimelineSrc * source
    );
}

[
    object,
    local,
    uuid(78530B77-61F9-11D2-8CAD-00A024580902),
    odl,
    pointer_default(unique)
]
interface IAMTimelineObj : IUnknown
{
    HRESULT GetStartStop(
        REFERENCE_TIME * start,
        REFERENCE_TIME * stop
    );

    HRESULT GetStartStop2(
        REFTIME * start, REFTIME * stop
    );

    HRESULT FixTimes(
        REFERENCE_TIME * start, REFERENCE_TIME * stop
    );

    HRESULT FixTimes2(
        REFTIME * start, REFTIME * stop
    );

    HRESULT SetStartStop(
        REFERENCE_TIME start,
        REFERENCE_TIME stop
    );

    HRESULT SetStartStop2(
        REFTIME start,
        REFTIME stop
    );

    HRESULT GetPropertySetter(
        [out,retval] IPropertySetter ** setter
    );

    HRESULT SetPropertySetter(
        IPropertySetter * setter
    );

    HRESULT GetSubObject(
        [out,retval] IUnknown ** obj
    );

    HRESULT SetSubObject(
        IUnknown * obj
    );

    HRESULT SetSubObjectGUID(
        GUID guid
    );

    HRESULT SetSubObjectGUIDB(
        BSTR guidb
    );

    HRESULT GetSubObjectGUID(
        GUID * guid
    );

    HRESULT GetSubObjectGUIDB(
        [out,retval] BSTR * guidb
    );

    HRESULT GetSubObjectLoaded(
        BOOL * loaded
    );

    HRESULT GetTimelineType(
        TIMELINE_MAJOR_TYPE * type
    );

    HRESULT SetTimelineType(
        TIMELINE_MAJOR_TYPE type
    );

    HRESULT GetUserID(
        long * id
    );

    HRESULT SetUserID(
        long id
    );

    HRESULT GetGenID(
        long * id
    );

    HRESULT GetUserName(
        [out,retval] BSTR * name
    );

    HRESULT SetUserName(
        BSTR name
    );

    HRESULT GetUserData(
        BYTE * data,
        long * size
    );

    HRESULT SetUserData(
        BYTE * data,
        long size
    );

    HRESULT GetMuted(
        BOOL * muted
    );

    HRESULT SetMuted(
        BOOL muted
    );

    HRESULT GetLocked(
        BOOL * locked
    );

    HRESULT SetLocked(
        BOOL locked
    );

    HRESULT GetDirtyRange(
        REFERENCE_TIME * start,
        REFERENCE_TIME * stop
    );

    HRESULT GetDirtyRange2(
        REFTIME * start,
        REFTIME * stop
    );

    HRESULT SetDirtyRange(
        REFERENCE_TIME start,
        REFERENCE_TIME stop
    );

    HRESULT SetDirtyRange2(
        REFTIME start,
        REFTIME stop
    );

    HRESULT ClearDirty();

    HRESULT Remove();

    HRESULT RemoveAll();

    HRESULT GetTimelineNoRef(
        IAMTimeline ** timeline
    );

    HRESULT GetGroupIBelongTo(
        [out] IAMTimelineGroup ** group
    );

    HRESULT GetEmbedDepth(
        long * depth
    );
}

[
    object,
    uuid(78530B79-61F9-11D2-8CAD-00A024580902),
    odl,
    pointer_default(unique)
]
interface IAMTimelineSrc : IUnknown
{
    HRESULT GetMediaTimes(
        REFERENCE_TIME * start,
        REFERENCE_TIME * stop
    );

    HRESULT GetMediaTimes2(
        REFTIME * start,
        REFTIME * stop
    );

    HRESULT ModifyStopTime(
        REFERENCE_TIME stop
    );

    HRESULT ModifyStopTime2(
        REFTIME stop
    );

    HRESULT FixMediaTimes(
        REFERENCE_TIME * start,
        REFERENCE_TIME * stop
    );

    HRESULT FixMediaTimes2(
        REFTIME * start,
        REFTIME * stop
    );

    HRESULT SetMediaTimes(
        REFERENCE_TIME Start,
        REFERENCE_TIME Stop
    );

    HRESULT SetMediaTimes2(
        REFTIME Start,
        REFTIME Stop
    );

    HRESULT SetMediaLength(
        REFERENCE_TIME length
    );

    HRESULT SetMediaLength2(
        REFTIME length
    );

    HRESULT GetMediaLength(
        REFERENCE_TIME * length
    );

    HRESULT GetMediaLength2(
        REFTIME * length
    );

    HRESULT GetMediaName(
        [out,retval] BSTR * name
    );

    HRESULT SetMediaName(
        BSTR name
    );

    HRESULT SpliceWithNext(
        IAMTimelineObj * next
    );

    HRESULT GetStreamNumber(
        long * num
    );

    HRESULT SetStreamNumber(
        long num
    );

    HRESULT IsNormalRate(
        BOOL * normal
    );

    HRESULT GetDefaultFPS(
        double * fps
    );

    HRESULT SetDefaultFPS(
        double fps
    );

    HRESULT GetStretchMode(
        int * mode
    );

    HRESULT SetStretchMode(
        int mode
    );
}

enum
{
    E_NOTINTREE = 0x80040400,
    E_RENDER_ENGINE_IS_BROKEN = 0x80040401,
    E_MUST_INIT_RENDERER = 0x80040402,
    E_NOTDETERMINED = 0x80040403,
    E_NO_TIMELINE = 0x80040404,
    S_WARN_OUTPUTRESET = 40404
};
