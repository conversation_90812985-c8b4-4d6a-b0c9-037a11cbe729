<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CTX_set_generate_session_id</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_CTX_set_generate_session_id, SSL_set_generate_session_id, SSL_has_matching_session_id, GEN_SESSION_CB - manipulate generation of SSL session IDs (server only)</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

typedef int (*GEN_SESSION_CB)(SSL *ssl, unsigned char *id,
                              unsigned int *id_len);

int SSL_CTX_set_generate_session_id(SSL_CTX *ctx, GEN_SESSION_CB cb);
int SSL_set_generate_session_id(SSL *ssl, GEN_SESSION_CB, cb);
int SSL_has_matching_session_id(const SSL *ssl, const unsigned char *id,
                                unsigned int id_len);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_CTX_set_generate_session_id() sets the callback function for generating new session ids for SSL/TLS sessions for <b>ctx</b> to be <b>cb</b>.</p>

<p>SSL_set_generate_session_id() sets the callback function for generating new session ids for SSL/TLS sessions for <b>ssl</b> to be <b>cb</b>.</p>

<p>SSL_has_matching_session_id() checks, whether a session with id <b>id</b> (of length <b>id_len</b>) is already contained in the internal session cache of the parent context of <b>ssl</b>.</p>

<h1 id="NOTES">NOTES</h1>

<p>When a new session is established between client and server, the server generates a session id. The session id is an arbitrary sequence of bytes. The length of the session id is between 1 and 32 bytes. The session id is not security critical but must be unique for the server. Additionally, the session id is transmitted in the clear when reusing the session so it must not contain sensitive information.</p>

<p>Without a callback being set, an OpenSSL server will generate a unique session id from pseudo random numbers of the maximum possible length. Using the callback function, the session id can be changed to contain additional information like e.g. a host id in order to improve load balancing or external caching techniques.</p>

<p>The callback function receives a pointer to the memory location to put <b>id</b> into and a pointer to the maximum allowed length <b>id_len</b>. The buffer at location <b>id</b> is only guaranteed to have the size <b>id_len</b>. The callback is only allowed to generate a shorter id and reduce <b>id_len</b>; the callback <b>must never</b> increase <b>id_len</b> or write to the location <b>id</b> exceeding the given limit.</p>

<p>The location <b>id</b> is filled with 0x00 before the callback is called, so the callback may only fill part of the possible length and leave <b>id_len</b> untouched while maintaining reproducibility.</p>

<p>Since the sessions must be distinguished, session ids must be unique. Without the callback a random number is used, so that the probability of generating the same session id is extremely small (2^256 for SSLv3/TLSv1). In order to assure the uniqueness of the generated session id, the callback must call SSL_has_matching_session_id() and generate another id if a conflict occurs. If an id conflict is not resolved, the handshake will fail. If the application codes e.g. a unique host id, a unique process number, and a unique sequence number into the session id, uniqueness could easily be achieved without randomness added (it should however be taken care that no confidential information is leaked this way). If the application can not guarantee uniqueness, it is recommended to use the maximum <b>id_len</b> and fill in the bytes not used to code special information with random data to avoid collisions.</p>

<p>SSL_has_matching_session_id() will only query the internal session cache, not the external one. Since the session id is generated before the handshake is completed, it is not immediately added to the cache. If another thread is using the same internal session cache, a race condition can occur in that another thread generates the same session id. Collisions can also occur when using an external session cache, since the external cache is not tested with SSL_has_matching_session_id() and the same race condition applies.</p>

<p>The callback must return 0 if it cannot generate a session id for whatever reason and return 1 on success.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_CTX_set_generate_session_id() and SSL_set_generate_session_id() return 1 on success and 0 for failure.</p>

<p>SSL_has_matching_session_id() returns 1 if another session with the same id is already in the cache, or 0 otherwise.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>The callback function listed will generate a session id with the server id given, and will fill the rest with pseudo random bytes:</p>

<pre><code>const char session_id_prefix = &quot;www-18&quot;;

#define MAX_SESSION_ID_ATTEMPTS 10
static int generate_session_id(SSL *ssl, unsigned char *id,
                               unsigned int *id_len)
{
    unsigned int count = 0;

    do {
        RAND_pseudo_bytes(id, *id_len);
        /*
         * Prefix the session_id with the required prefix. NB: If our
         * prefix is too long, clip it - but there will be worse effects
         * anyway, e.g. the server could only possibly create 1 session
         * ID (i.e. the prefix!) so all future session negotiations will
         * fail due to conflicts.
         */
        memcpy(id, session_id_prefix, strlen(session_id_prefix) &lt; *id_len ?
                                      strlen(session_id_prefix) : *id_len);
    } while (SSL_has_matching_session_id(ssl, id, *id_len)
              &amp;&amp; ++count &lt; MAX_SESSION_ID_ATTEMPTS);
    if (count &gt;= MAX_SESSION_ID_ATTEMPTS)
        return 0;
    return 1;
}</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_get_version.html">SSL_get_version(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2001-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


