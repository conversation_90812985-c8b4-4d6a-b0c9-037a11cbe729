<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CONF_CTX_set_ssl_ctx</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_CONF_CTX_finish, SSL_CONF_CTX_set_ssl_ctx, SSL_CONF_CTX_set_ssl - set context to configure</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

void SSL_CONF_CTX_set_ssl_ctx(SSL_CONF_CTX *cctx, SSL_CTX *ctx);
void SSL_CONF_CTX_set_ssl(SSL_CONF_CTX *cctx, SSL *ssl);
int SSL_CONF_CTX_finish(SSL_CONF_CTX *cctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_CONF_CTX_set_ssl_ctx() sets the context associated with <b>cctx</b> to the <b>SSL_CTX</b> structure <b>ctx</b>. Any previous <b>SSL</b> or <b>SSL_CTX</b> associated with <b>cctx</b> is cleared. Subsequent calls to SSL_CONF_cmd() will be sent to <b>ctx</b>.</p>

<p>SSL_CONF_CTX_set_ssl() sets the context associated with <b>cctx</b> to the <b>SSL</b> structure <b>ssl</b>. Any previous <b>SSL</b> or <b>SSL_CTX</b> associated with <b>cctx</b> is cleared. Subsequent calls to SSL_CONF_cmd() will be sent to <b>ssl</b>.</p>

<p>The function SSL_CONF_CTX_finish() must be called after all configuration operations have been completed. It is used to finalise any operations or to process defaults.</p>

<h1 id="NOTES">NOTES</h1>

<p>The context need not be set or it can be set to <b>NULL</b> in which case only syntax checking of commands is performed, where possible.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_CONF_CTX_set_ssl_ctx() and SSL_CTX_set_ssl() do not return a value.</p>

<p>SSL_CONF_CTX_finish() returns 1 for success and 0 for failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_CONF_CTX_new.html">SSL_CONF_CTX_new(3)</a>, <a href="../man3/SSL_CONF_CTX_set_flags.html">SSL_CONF_CTX_set_flags(3)</a>, <a href="../man3/SSL_CONF_CTX_set1_prefix.html">SSL_CONF_CTX_set1_prefix(3)</a>, <a href="../man3/SSL_CONF_cmd.html">SSL_CONF_cmd(3)</a>, <a href="../man3/SSL_CONF_cmd_argv.html">SSL_CONF_cmd_argv(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 1.0.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2012-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


