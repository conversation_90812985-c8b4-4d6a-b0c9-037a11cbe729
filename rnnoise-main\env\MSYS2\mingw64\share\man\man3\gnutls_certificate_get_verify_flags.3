.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_get_verify_flags" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_get_verify_flags \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned int gnutls_certificate_get_verify_flags(gnutls_certificate_credentials_t " res ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t res" 12
is a gnutls_certificate_credentials_t type
.SH "DESCRIPTION"
Returns the verification flags set with
\fBgnutls_certificate_set_verify_flags()\fP.
.SH "RETURNS"
The certificate verification flags used by  \fIres\fP .
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
