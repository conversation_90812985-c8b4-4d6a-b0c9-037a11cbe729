set ::msgcat::header "Project-Id-Version: gitk\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2015-10-05 22:23-0600\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: Catalan\nLanguage: ca\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 1.8.5\n"
::msgcat::mcset ca "Couldn't get list of unmerged files:" "No s'ha pogut obtenir la llista de fitxers no fusionats:"
::msgcat::mcset ca "Color words" "Colora les paraules"
::msgcat::mcset ca "Markup words" "Marca les paraules"
::msgcat::mcset ca "Error parsing revisions:" "Error en analitzar les revisions:"
::msgcat::mcset ca "Error executing --argscmd command:" "Error en executar l'ordre --argscmd:"
::msgcat::mcset ca "No files selected: --merge specified but no files are unmerged." "No hi ha fitxers seleccionats: s'ha especificat --merge per\u00f2 cap fitxer est\u00e0 sense fusionar."
::msgcat::mcset ca "No files selected: --merge specified but no unmerged files are within file limit." "No hi ha fitxers seleccionats: s'ha especificat --merge per\u00f2 cap fitxer sense fusionar est\u00e0 dins del l\u00edmit de fitxers."
::msgcat::mcset ca "Error executing git log:" "Error en executar git log:"
::msgcat::mcset ca "Reading" "Llegint"
::msgcat::mcset ca "Reading commits..." "Llegint les revisions..."
::msgcat::mcset ca "No commits selected" "Cap comissi\u00f3 seleccionada"
::msgcat::mcset ca "Command line" "L\u00ednia d'ordres"
::msgcat::mcset ca "Can't parse git log output:" "No es pot analitzar la sortida del git log:"
::msgcat::mcset ca "No commit information available" "Cap informaci\u00f3 de comissi\u00f3 disponible"
::msgcat::mcset ca "OK" "D'acord"
::msgcat::mcset ca "Cancel" "Cancel\u00b7la"
::msgcat::mcset ca "&Update" "Actualitza"
::msgcat::mcset ca "&Reload" "Recarrega"
::msgcat::mcset ca "Reread re&ferences" "Rellegeix les refer\u00e8ncies"
::msgcat::mcset ca "&List references" "Llista les refer\u00e8ncies"
::msgcat::mcset ca "Start git &gui" "Inicia el git gui"
::msgcat::mcset ca "&Quit" "Surt"
::msgcat::mcset ca "&File" "Fitxer"
::msgcat::mcset ca "&Preferences" "Prefer\u00e8ncies"
::msgcat::mcset ca "&Edit" "Edita"
::msgcat::mcset ca "&New view..." "Vista nova..."
::msgcat::mcset ca "&Edit view..." "Edita la vista..."
::msgcat::mcset ca "&Delete view" "Suprimeix la vista"
::msgcat::mcset ca "&All files" "Tots els fitxers"
::msgcat::mcset ca "&View" "Vista"
::msgcat::mcset ca "&About gitk" "Quant al gitk"
::msgcat::mcset ca "&Key bindings" "Associacions de tecles"
::msgcat::mcset ca "&Help" "Ajuda"
::msgcat::mcset ca "SHA1 ID:" "ID SHA1:"
::msgcat::mcset ca "Row" "Fila"
::msgcat::mcset ca "Find" "Cerca"
::msgcat::mcset ca "commit" "comissi\u00f3"
::msgcat::mcset ca "containing:" "que contingui:"
::msgcat::mcset ca "touching paths:" "que toqui els camins:"
::msgcat::mcset ca "adding/removing string:" "que afegeixi/elimini la cadena:"
::msgcat::mcset ca "changing lines matching:" "que tingui l\u00ednies canviades coincidents amb:"
::msgcat::mcset ca "Exact" "Exacte"
::msgcat::mcset ca "IgnCase" "Ignora maj\u00fascula i min\u00fascula"
::msgcat::mcset ca "Regexp" "Regexp"
::msgcat::mcset ca "All fields" "Tots els camps"
::msgcat::mcset ca "Headline" "Titular"
::msgcat::mcset ca "Comments" "Comentaris"
::msgcat::mcset ca "Author" "Autor"
::msgcat::mcset ca "Committer" "Comitent"
::msgcat::mcset ca "Search" "Cerca"
::msgcat::mcset ca "Diff" "Difer\u00e8ncia"
::msgcat::mcset ca "Old version" "Versi\u00f3 antiga"
::msgcat::mcset ca "New version" "Versi\u00f3 nova"
::msgcat::mcset ca "Lines of context" "L\u00ednies de context"
::msgcat::mcset ca "Ignore space change" "Ignora canvis d'espai"
::msgcat::mcset ca "Line diff" "Difer\u00e8ncia de l\u00ednies"
::msgcat::mcset ca "Patch" "Peda\u00e7"
::msgcat::mcset ca "Tree" "Arbre"
::msgcat::mcset ca "Diff this -> selected" "Diferencia aquesta -> la seleccionada"
::msgcat::mcset ca "Diff selected -> this" "Diferencia la seleccionada -> aquesta"
::msgcat::mcset ca "Make patch" "Fes peda\u00e7"
::msgcat::mcset ca "Create tag" "Crea etiqueta"
::msgcat::mcset ca "Write commit to file" "Escriu la comissi\u00f3 a un fitxer"
::msgcat::mcset ca "Create new branch" "Crea una branca nova"
::msgcat::mcset ca "Cherry-pick this commit" "Recull aquesta comissi\u00f3 com a cirera"
::msgcat::mcset ca "Reset HEAD branch to here" "Restableix la branca HEAD aqu\u00ed"
::msgcat::mcset ca "Mark this commit" "Marca aquesta comissi\u00f3"
::msgcat::mcset ca "Return to mark" "Torna a la marca"
::msgcat::mcset ca "Find descendant of this and mark" "Troba la descendent d'aquesta i marca-la"
::msgcat::mcset ca "Compare with marked commit" "Compara amb la comissi\u00f3 marcada"
::msgcat::mcset ca "Diff this -> marked commit" "Diferencia aquesta -> la comissi\u00f3 marcada"
::msgcat::mcset ca "Diff marked commit -> this" "Diferencia la comissi\u00f3 seleccionada -> aquesta"
::msgcat::mcset ca "Revert this commit" "Reverteix aquesta comissi\u00f3"
::msgcat::mcset ca "Check out this branch" "Agafa aquesta branca"
::msgcat::mcset ca "Remove this branch" "Elimina aquesta branca"
::msgcat::mcset ca "Copy branch name" "Copia el nom de branca"
::msgcat::mcset ca "Highlight this too" "Ressalta aquest tamb\u00e9"
::msgcat::mcset ca "Highlight this only" "Ressalta nom\u00e9s aquest"
::msgcat::mcset ca "External diff" "Difer\u00e8ncia externa"
::msgcat::mcset ca "Blame parent commit" "Culpabilitat de la comissi\u00f3 mare"
::msgcat::mcset ca "Copy path" "Copia el cam\u00ed"
::msgcat::mcset ca "Show origin of this line" "Mostra l'origen d'aquesta l\u00ednia"
::msgcat::mcset ca "Run git gui blame on this line" "Executa git gui blame en aquesta l\u00ednia"
::msgcat::mcset ca "\nGitk - a commit viewer for git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nUse and redistribute under the terms of the GNU General Public License" "\nGitk - visualitzador de comissions per al git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nUseu-lo i redistribu\u00efu-lo sota els termes de la Llic\u00e8ncia P\u00fablica General GNU"
::msgcat::mcset ca "Close" "Tanca"
::msgcat::mcset ca "Gitk key bindings" "Associacions de tecles del Gitk"
::msgcat::mcset ca "Gitk key bindings:" "Associacions de tecles del Gitk:"
::msgcat::mcset ca "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009Surt"
::msgcat::mcset ca "<%s-W>\u0009\u0009Close window" "<%s-W>\u0009\u0009Tanca la finestra"
::msgcat::mcset ca "<Home>\u0009\u0009Move to first commit" "<Inici>\u0009\u0009V\u00e9s a la primera comissi\u00f3"
::msgcat::mcset ca "<End>\u0009\u0009Move to last commit" "<Fi>\u0009\u0009V\u00e9s a l'\u00faltima comissi\u00f3"
::msgcat::mcset ca "<Up>, p, k\u0009Move up one commit" "<Amunt>, p, k\u0009Mou-te cap amunt per una comissi\u00f3"
::msgcat::mcset ca "<Down>, n, j\u0009Move down one commit" "<Avall>, n, j\u0009Mou-te cap avall per una comissi\u00f3"
::msgcat::mcset ca "<Left>, z, h\u0009Go back in history list" "<Esquerra>, z, h\u0009Retrocedeix en la llista d'hist\u00f2ria"
::msgcat::mcset ca "<Right>, x, l\u0009Go forward in history list" "<Dreta>, x, l\u0009Avan\u00e7a en la llista d'hist\u00f2ria"
::msgcat::mcset ca "<%s-n>\u0009Go to n-th parent of current commit in history list" "<%s-n>\u0009V\u00e9s a l'en\u00e8sima mare de la comissi\u00f3 actual en la llista d'hist\u00f2ria"
::msgcat::mcset ca "<PageUp>\u0009Move up one page in commit list" "<ReP\u00e0g>\u0009Mou-te cap amunt per una p\u00e0gina en la llista de comissions"
::msgcat::mcset ca "<PageDown>\u0009Move down one page in commit list" "<AvP\u00e0g>\u0009Mou-te cap avall per una p\u00e0gina en la llista de comissions"
::msgcat::mcset ca "<%s-Home>\u0009Scroll to top of commit list" "<%s-Inici>\u0009Despla\u00e7a't a la part superior de la llista de comissions"
::msgcat::mcset ca "<%s-End>\u0009Scroll to bottom of commit list" "<%s-Fi>\u0009Despla\u00e7a't a la part inferior de la llista de comissions"
::msgcat::mcset ca "<%s-Up>\u0009Scroll commit list up one line" "<%s-Amunt>\u0009Despla\u00e7a la llista de comissions cap amunt per una l\u00ednia"
::msgcat::mcset ca "<%s-Down>\u0009Scroll commit list down one line" "<%s-Avall>\u0009Despla\u00e7a la llista de comissions cap avall per una l\u00ednia"
::msgcat::mcset ca "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-ReP\u00e0g>\u0009Despla\u00e7a la llista de comissions cap amunt per una p\u00e0gina"
::msgcat::mcset ca "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-AvP\u00e0g>\u0009Despla\u00e7a la llista de comissions cap avall per una p\u00e0gina"
::msgcat::mcset ca "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Maj-Amunt>\u0009Cerca cap enrere (cap amunt, les comissions m\u00e9s noves)"
::msgcat::mcset ca "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Maj-Avall>\u0009Cerca cap endavant (cap avall, les comissions m\u00e9s velles)"
::msgcat::mcset ca "<Delete>, b\u0009Scroll diff view up one page" "<Supr>, b\u0009Despla\u00e7a la vista de difer\u00e8ncia cap amunt per una p\u00e0gina"
::msgcat::mcset ca "<Backspace>\u0009Scroll diff view up one page" "<Retroc\u00e9s>\u0009Despla\u00e7a la vista de difer\u00e8ncia cap amunt per una p\u00e0gina"
::msgcat::mcset ca "<Space>\u0009\u0009Scroll diff view down one page" "<Espai>\u0009\u0009Despla\u00e7a la vista de difer\u00e8ncia cap avall per una p\u00e0gina"
::msgcat::mcset ca "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u0009Despla\u00e7a la vista de difer\u00e8ncia cap amunt per 18 l\u00ednies"
::msgcat::mcset ca "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u0009Despla\u00e7a la vista de difer\u00e8ncia cap avall per 18 l\u00ednies"
::msgcat::mcset ca "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009Cerca"
::msgcat::mcset ca "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009Mou-te a la propera coincid\u00e8ncia de la cerca"
::msgcat::mcset ca "<Return>\u0009Move to next find hit" "<Retorn>\u0009Mou-te a la propera coincid\u00e8ncia de la cerca"
::msgcat::mcset ca "g\u0009\u0009Go to commit" "g\u0009\u0009V\u00e9s a l'\u00faltima comissi\u00f3"
::msgcat::mcset ca "/\u0009\u0009Focus the search box" "/\u0009\u0009Posa el focus a la caixa de cerca"
::msgcat::mcset ca "?\u0009\u0009Move to previous find hit" "?\u0009\u0009Mou a la coincid\u00e8ncia pr\u00e8via de la cerca"
::msgcat::mcset ca "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009Despla\u00e7a la vista de difer\u00e8ncia al proper fitxer"
::msgcat::mcset ca "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009Cerca la propera coincid\u00e8ncia en la vista de difer\u00e8ncia"
::msgcat::mcset ca "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009Cerca la coincid\u00e8ncia pr\u00e8via en la vista de difer\u00e8ncia"
::msgcat::mcset ca "<%s-KP+>\u0009Increase font size" "<%s-KP+>\u0009Augmenta la mida de lletra"
::msgcat::mcset ca "<%s-plus>\u0009Increase font size" "<%s-m\u00e9s>\u0009Augmenta la mida de lletra"
::msgcat::mcset ca "<%s-KP->\u0009Decrease font size" "<%s-KP->\u0009Disminueix la mida de lletra"
::msgcat::mcset ca "<%s-minus>\u0009Decrease font size" "<%s-menys>\u0009Disminueix la mida de lletra"
::msgcat::mcset ca "<F5>\u0009\u0009Update" "<F5>\u0009\u0009Actualitza"
::msgcat::mcset ca "Error creating temporary directory %s:" "Error en crear el directori temporal %s:"
::msgcat::mcset ca "Error getting \"%s\" from %s:" "Error en obtenir \"%s\" de %s:"
::msgcat::mcset ca "command failed:" "l'ordre ha fallat:"
::msgcat::mcset ca "No such commit" "Cap comissi\u00f3 aix\u00ed"
::msgcat::mcset ca "git gui blame: command failed:" "git gui blame: l'ordre ha fallat:"
::msgcat::mcset ca "Couldn't read merge head: %s" "No s'ha pogut llegir el cap de fusi\u00f3: %s"
::msgcat::mcset ca "Error reading index: %s" "Error en llegir l'\u00edndex: %s"
::msgcat::mcset ca "Couldn't start git blame: %s" "No s'ha pogut iniciar el git blame: %s"
::msgcat::mcset ca "Searching" "Cercant"
::msgcat::mcset ca "Error running git blame: %s" "Error en executar el git blame: %s"
::msgcat::mcset ca "That line comes from commit %s,  which is not in this view" "Aquella l\u00ednia ve de la comissi\u00f3 %s, la qual no \u00e9s en aquesta visualitzaci\u00f3"
::msgcat::mcset ca "External diff viewer failed:" "El visualitzador de difer\u00e8ncia extern ha fallat:"
::msgcat::mcset ca "Gitk view definition" "Definici\u00f3 de vista del Gitk"
::msgcat::mcset ca "Remember this view" "Recorda aquesta vista"
::msgcat::mcset ca "References (space separated list):" "Refer\u00e8ncies (llista separada per espais)"
::msgcat::mcset ca "Branches & tags:" "Branques i etiquetes:"
::msgcat::mcset ca "All refs" "Totes les refer\u00e8ncies"
::msgcat::mcset ca "All (local) branches" "Totes les branques (locals)"
::msgcat::mcset ca "All tags" "Totes les etiquetes"
::msgcat::mcset ca "All remote-tracking branches" "Totes les branques amb seguiment remot"
::msgcat::mcset ca "Commit Info (regular expressions):" "Informaci\u00f3 de comissi\u00f3 (expressions regulars):"
::msgcat::mcset ca "Author:" "Autor:"
::msgcat::mcset ca "Committer:" "Comitent:"
::msgcat::mcset ca "Commit Message:" "Missatge de comissi\u00f3:"
::msgcat::mcset ca "Matches all Commit Info criteria" "Coincideix amb tots els criteris d'informaci\u00f3 de comissi\u00f3"
::msgcat::mcset ca "Matches no Commit Info criteria" "No coincideix amb cap criteri d'informaci\u00f3 de comissi\u00f3"
::msgcat::mcset ca "Changes to Files:" "Canvis als fitxers:"
::msgcat::mcset ca "Fixed String" "Cadena fixa"
::msgcat::mcset ca "Regular Expression" "Expressi\u00f3 regular"
::msgcat::mcset ca "Search string:" "Cadena de cerca:"
::msgcat::mcset ca "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "Dates de comissi\u00f3 (\"fa 2 setmanes\", \"2009-03-17 15:27:38\", \"17 abr 2009 15:27:38\"):"
::msgcat::mcset ca "Since:" "Des de:"
::msgcat::mcset ca "Until:" "Fins:"
::msgcat::mcset ca "Limit and/or skip a number of revisions (positive integer):" "Limita o salta un nombre de revisions (nombre enter positiu)"
::msgcat::mcset ca "Number to show:" "Nombre a mostrar:"
::msgcat::mcset ca "Number to skip:" "Nombre a saltar:"
::msgcat::mcset ca "Miscellaneous options:" "Opcions miscel\u00b7l\u00e0nies:"
::msgcat::mcset ca "Strictly sort by date" "Ordena estrictament per data"
::msgcat::mcset ca "Mark branch sides" "Marca els costats de les branques"
::msgcat::mcset ca "Limit to first parent" "Limita a la primera mare"
::msgcat::mcset ca "Simple history" "Hist\u00f2ria senzilla"
::msgcat::mcset ca "Additional arguments to git log:" "Par\u00e0metres addicionals al git log:"
::msgcat::mcset ca "Enter files and directories to include, one per line:" "Introdu\u00efu els fitxers i directoris a incloure, un per l\u00ednia:"
::msgcat::mcset ca "Command to generate more commits to include:" "Ordre per a generar m\u00e9s comissions a incloure:"
::msgcat::mcset ca "Gitk: edit view" "Gitk: vista d'edici\u00f3"
::msgcat::mcset ca "-- criteria for selecting revisions" "-- criteris per a seleccionar les revisions"
::msgcat::mcset ca "View Name" "Nom de vista"
::msgcat::mcset ca "Apply (F5)" "Aplica (F5)"
::msgcat::mcset ca "Error in commit selection arguments:" "Error en els par\u00e0metres de selecci\u00f3 de comissions:"
::msgcat::mcset ca "None" "Cap"
::msgcat::mcset ca "Descendant" "Descendent"
::msgcat::mcset ca "Not descendant" "No descendent"
::msgcat::mcset ca "Ancestor" "Avantpassat"
::msgcat::mcset ca "Not ancestor" "No avantpassat"
::msgcat::mcset ca "Local changes checked in to index but not committed" "Canvis locals registrats en l'\u00edndex per\u00f2 no comesos"
::msgcat::mcset ca "Local uncommitted changes, not checked in to index" "Canvis locals sense cometre, no registrats en l'\u00edndex"
::msgcat::mcset ca "and many more" "i moltes m\u00e9s"
::msgcat::mcset ca "many" "moltes"
::msgcat::mcset ca "Tags:" "Etiquetes:"
::msgcat::mcset ca "Parent" "Mare"
::msgcat::mcset ca "Child" "Filla"
::msgcat::mcset ca "Branch" "Branca"
::msgcat::mcset ca "Follows" "Segueix"
::msgcat::mcset ca "Precedes" "Precedeix"
::msgcat::mcset ca "Error getting diffs: %s" "Error en obtenir les difer\u00e8ncies: %s"
::msgcat::mcset ca "Goto:" "V\u00e9s a:"
::msgcat::mcset ca "Short SHA1 id %s is ambiguous" "L'id SHA1 curta %s \u00e9s ambigua"
::msgcat::mcset ca "Revision %s is not known" "La revisi\u00f3 %s \u00e9s desconeguda"
::msgcat::mcset ca "SHA1 id %s is not known" "L'id SHA1 %s \u00e9s desconeguda"
::msgcat::mcset ca "Revision %s is not in the current view" "La revisi\u00f3 %s no \u00e9s en la vista actual"
::msgcat::mcset ca "Date" "Data"
::msgcat::mcset ca "Children" "Filles"
::msgcat::mcset ca "Reset %s branch to here" "Restableix la branca %s aqu\u00ed"
::msgcat::mcset ca "Detached head: can't reset" "Cap separat: no es pot restablir"
::msgcat::mcset ca "Skipping merge commit " "Saltant la comissi\u00f3 de fusi\u00f3 "
::msgcat::mcset ca "Error getting patch ID for " "Error en obtenir l'ID de peda\u00e7 de "
::msgcat::mcset ca " - stopping\n" " - aturant\n"
::msgcat::mcset ca "Commit " "Comissi\u00f3 "
::msgcat::mcset ca " is the same patch as\n       " " \u00e9s el mateix peda\u00e7 que\n       "
::msgcat::mcset ca " differs from\n       " " difereix de\n       "
::msgcat::mcset ca "Diff of commits:\n\n" "Difer\u00e8ncia entre comissions:\n\n"
::msgcat::mcset ca " has %s children - stopping\n" " t\u00e9 %s filles - aturant\n"
::msgcat::mcset ca "Error writing commit to file: %s" "Error en escriure la comissi\u00f3 al fitxer: %s"
::msgcat::mcset ca "Error diffing commits: %s" "Error en diferenciar les comissions: %s"
::msgcat::mcset ca "Top" "Part superior"
::msgcat::mcset ca "From" "De"
::msgcat::mcset ca "To" "A"
::msgcat::mcset ca "Generate patch" "Genera peda\u00e7"
::msgcat::mcset ca "From:" "De:"
::msgcat::mcset ca "To:" "A:"
::msgcat::mcset ca "Reverse" "Inverteix"
::msgcat::mcset ca "Output file:" "Fitxer de sortida:"
::msgcat::mcset ca "Generate" "Genera"
::msgcat::mcset ca "Error creating patch:" "Error en crear el peda\u00e7:"
::msgcat::mcset ca "ID:" "ID:"
::msgcat::mcset ca "Tag name:" "Nom d'etiqueta:"
::msgcat::mcset ca "Tag message is optional" "El missatge d'etiqueta \u00e9s opcional"
::msgcat::mcset ca "Tag message:" "Missatge d'etiqueta:"
::msgcat::mcset ca "Create" "Crea"
::msgcat::mcset ca "No tag name specified" "No s'ha especificat cap nom d'etiqueta"
::msgcat::mcset ca "Tag \"%s\" already exists" "L'etiqueta \"%s\" ja existeix"
::msgcat::mcset ca "Error creating tag:" "Error en crear l'etiqueta:"
::msgcat::mcset ca "Command:" "Ordre:"
::msgcat::mcset ca "Write" "Escriu"
::msgcat::mcset ca "Error writing commit:" "Error en escriure la comissi\u00f3:"
::msgcat::mcset ca "Name:" "Nom:"
::msgcat::mcset ca "Please specify a name for the new branch" "Si us plau, especifiqueu un nom per a la branca nova"
::msgcat::mcset ca "Branch '%s' already exists. Overwrite?" "La branca '%s' ja existeix. Voleu sobreescriure?"
::msgcat::mcset ca "Commit %s is already included in branch %s -- really re-apply it?" "La comissi\u00f3 %s ja est\u00e0 inclosa en la branca %s -- realment voleu tornar a aplicar-la?"
::msgcat::mcset ca "Cherry-picking" "Recollint cireres"
::msgcat::mcset ca "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "El recull de cireres ha fallat a causa de canvis locals al fitxer '%s'.\nSi us plau, cometeu, restabliu o emmagatzemeu els vostres canvis i torneu a intentar."
::msgcat::mcset ca "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "El recull de cireres ha fallat a causa d'un conflicte de fusi\u00f3.\nVoleu executar el git citool per a resoldre'l?"
::msgcat::mcset ca "No changes committed" "Cap canvi com\u00e8s"
::msgcat::mcset ca "Commit %s is not included in branch %s -- really revert it?" "La comissi\u00f3 %s no s'inclou en la branca %s -- realment voleu revertir-la?"
::msgcat::mcset ca "Reverting" "Revertint"
::msgcat::mcset ca "Revert failed because of local changes to the following files:%s Please commit, reset or stash  your changes and try again." "La reversi\u00f3 ha fallat a causa de canvis locals als fitxers seg\u00fcents:%s Si us plau, cometeu, restabliu o emmagatzemeu els vostres canvis i torneu-ho a intentar."
::msgcat::mcset ca "Revert failed because of merge conflict.\n Do you wish to run git citool to resolve it?" "La reversi\u00f3 ha fallat a causa d'un conflicte de fusi\u00f3.\n Voleu executar el git citool per a resoldre'l?"
::msgcat::mcset ca "Confirm reset" "Confirma el restabliment"
::msgcat::mcset ca "Reset branch %s to %s?" "Voleu restablir la branca %s a %s?"
::msgcat::mcset ca "Reset type:" "Tipus de restabliment:"
::msgcat::mcset ca "Soft: Leave working tree and index untouched" "Suau: Deixa l'arbre de treball i l'\u00edndex sense tocar"
::msgcat::mcset ca "Mixed: Leave working tree untouched, reset index" "Mixt: Deixa l'arbre de treball sense tocar, restableix l'\u00edndex"
::msgcat::mcset ca "Hard: Reset working tree and index\n(discard ALL local changes)" "Dur: Restableix l'arbre de treball i l'\u00edndex\n(descarta TOTS els canvis locals)"
::msgcat::mcset ca "Resetting" "Restablint"
::msgcat::mcset ca "Checking out" "Agafant"
::msgcat::mcset ca "Cannot delete the currently checked-out branch" "No es pot suprimir la branca actualment agafada"
::msgcat::mcset ca "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "Les comissions en la branca %s no s\u00f3n en cap altra branca.\nRealment voleu suprimir la branca %s?"
::msgcat::mcset ca "Tags and heads: %s" "Etiquetes i caps: %s"
::msgcat::mcset ca "Filter" "Filtre"
::msgcat::mcset ca "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "Error en llegir la informaci\u00f3 de topologia de comissi\u00f3; la informaci\u00f3 sobre branques i etiquetes precedents/seg\u00fcents ser\u00e0 incompleta."
::msgcat::mcset ca "Tag" "Etiqueta"
::msgcat::mcset ca "Id" "Id"
::msgcat::mcset ca "Gitk font chooser" "Selector de tipus de lletra del Gitk"
::msgcat::mcset ca "B" "B"
::msgcat::mcset ca "I" "I"
::msgcat::mcset ca "Commit list display options" "Opcions de visualitzaci\u00f3 de la llista de comissions"
::msgcat::mcset ca "Maximum graph width (lines)" "Amplada m\u00e0xima del gr\u00e0fic (l\u00ednies)"
::msgcat::mcset ca "Maximum graph width (% of pane)" "Amplada m\u00e0xima del gr\u00e0fic (% del panell)"
::msgcat::mcset ca "Show local changes" "Mostra els canvis locals"
::msgcat::mcset ca "Auto-select SHA1 (length)" "Selecciona autom\u00e0ticament l'SHA1 (longitud)"
::msgcat::mcset ca "Hide remote refs" "Amaga les refer\u00e8ncies remotes"
::msgcat::mcset ca "Diff display options" "Opcions de visualitzaci\u00f3 de difer\u00e8ncia"
::msgcat::mcset ca "Tab spacing" "Espaiat de tabulaci\u00f3"
::msgcat::mcset ca "Display nearby tags/heads" "Mostra etiquetes/caps propers"
::msgcat::mcset ca "Maximum # tags/heads to show" "Nombre m\u00e0xim d'etiquetes/caps a mostrar"
::msgcat::mcset ca "Limit diffs to listed paths" "Limita les difer\u00e8ncies als camins llistats"
::msgcat::mcset ca "Support per-file encodings" "Admet codificacions espec\u00edfiques per a cada fitxer"
::msgcat::mcset ca "External diff tool" "Eina de difer\u00e8ncia externa"
::msgcat::mcset ca "Choose..." "Trieu..."
::msgcat::mcset ca "General options" "Opcions generals"
::msgcat::mcset ca "Use themed widgets" "Usa els ginys tematitzats"
::msgcat::mcset ca "(change requires restart)" "(el canvi requereix reiniciar)"
::msgcat::mcset ca "(currently unavailable)" "(actualment no disponible)"
::msgcat::mcset ca "Colors: press to choose" "Colors: pressiona per a triar"
::msgcat::mcset ca "Interface" "Interf\u00edcie"
::msgcat::mcset ca "interface" "interf\u00edcie"
::msgcat::mcset ca "Background" "Fons"
::msgcat::mcset ca "background" "fons"
::msgcat::mcset ca "Foreground" "Primer pla"
::msgcat::mcset ca "foreground" "primer pla"
::msgcat::mcset ca "Diff: old lines" "Difer\u00e8ncia: l\u00ednies velles"
::msgcat::mcset ca "diff old lines" "diferencia les l\u00ednies velles"
::msgcat::mcset ca "Diff: new lines" "Difer\u00e8ncia: l\u00ednies noves"
::msgcat::mcset ca "diff new lines" "diferencia les l\u00ednies noves"
::msgcat::mcset ca "Diff: hunk header" "Difer\u00e8ncia: cap\u00e7alera de tros"
::msgcat::mcset ca "diff hunk header" "diferencia la cap\u00e7alera de tros"
::msgcat::mcset ca "Marked line bg" "Fons de la l\u00ednia marcada"
::msgcat::mcset ca "marked line background" "fons de la l\u00ednia marcada"
::msgcat::mcset ca "Select bg" "Fons de la selecci\u00f3"
::msgcat::mcset ca "Fonts: press to choose" "Tipus de lletra: pressiona per a triar"
::msgcat::mcset ca "Main font" "Tipus de lletra principal"
::msgcat::mcset ca "Diff display font" "Tipus de lletra de visualitzaci\u00f3 de difer\u00e8ncia"
::msgcat::mcset ca "User interface font" "Tipus de lletra de la interf\u00edcie d'usuari"
::msgcat::mcset ca "Gitk preferences" "Prefer\u00e8ncies del Gitk"
::msgcat::mcset ca "General" "General"
::msgcat::mcset ca "Colors" "Colors"
::msgcat::mcset ca "Fonts" "Tipus de lletra"
::msgcat::mcset ca "Gitk: choose color for %s" "Gitk: tria el color per a %s"
::msgcat::mcset ca "Sorry, gitk cannot run with this version of Tcl/Tk.\n Gitk requires at least Tcl/Tk 8.4." "Perd\u00f3, el gitk no pot executar-se amb aquesta versi\u00f3 de Tcl/Tk.\n El Gitk requereix com a m\u00ednim el Tcl/Tk 8.4."
::msgcat::mcset ca "Cannot find a git repository here." "No es pot trobar cap dip\u00f2sit de git aqu\u00ed."
::msgcat::mcset ca "Ambiguous argument '%s': both revision and filename" "Par\u00e0metre ambigu '%s': \u00e9s tant revisi\u00f3 com nom de fitxer"
::msgcat::mcset ca "Bad arguments to gitk:" "Par\u00e0metres dolents al gitk:"
