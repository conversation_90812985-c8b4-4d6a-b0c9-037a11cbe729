.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_obj_flags_get_str" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_obj_flags_get_str \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "char * gnutls_pkcs11_obj_flags_get_str(unsigned int " flags ");"
.SH ARGUMENTS
.IP "unsigned int flags" 12
holds the flags
.SH "DESCRIPTION"
This function given an or\-sequence of \fBGNUTLS_PKCS11_OBJ_FLAG_MARK\fP,
will return an allocated string with its description. The string
needs to be deallocated using \fBgnutls_free()\fP.
.SH "RETURNS"
If flags is zero \fBNULL\fP is returned, otherwise an allocated string.
.SH "SINCE"
3.3.7
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
