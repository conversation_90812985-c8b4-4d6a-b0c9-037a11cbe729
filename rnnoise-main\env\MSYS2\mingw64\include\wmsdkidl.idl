/*
 * Copyright 2012 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "wmsbuffer.idl";
import "drmexternals.idl";

typedef struct _WMMediaType
{
    GUID majortype;
    GUID subtype;
    BOOL bFixedSizeSamples;
    BOOL bTemporalCompression;
    ULONG lSampleSize;
    GUID formattype;
    IUnknown *pUnk;
    ULONG cbFormat;
    [size_is(cbFormat)] BYTE *pbFormat;
} WM_MEDIA_TYPE;

typedef struct _WMWriterStatistics
{
    QWORD qwSampleCount;
    QWORD qwByteCount;
    QWORD qwDroppedSampleCount;
    QWORD qwDroppedByteCount;
    DWORD dwCurrentBitrate;
    DWORD dwAverageBitrate;
    DWORD dwExpectedBitrate;
    DWORD dwCurrentSampleRate;
    DWORD dwAverageSampleRate;
    DWORD dwExpectedSampleRate;
} WM_WRITER_STATISTICS;

typedef struct _WMWriterStatisticsEx
{
    DWORD dwBitratePlusOverhead;
    DWORD dwCurrentSampleDropRateInQueue;
    DWORD dwCurrentSampleDropRateInCodec;
    DWORD dwCurrentSampleDropRateInMultiplexer;
    DWORD dwTotalSampleDropsInQueue;
    DWORD dwTotalSampleDropsInCodec;
    DWORD dwTotalSampleDropsInMultiplexer;
} WM_WRITER_STATISTICS_EX;

typedef struct _WMReaderStatistics
{
    DWORD cbSize;
    DWORD dwBandwidth;
    DWORD cPacketsReceived;
    DWORD cPacketsRecovered;
    DWORD cPacketsLost;
    WORD wQuality;
} WM_READER_STATISTICS;

typedef struct _WMReaderClientInfo
{
    DWORD cbSize;
    WCHAR *wszLang;
    WCHAR *wszBrowserUserAgent;
    WCHAR *wszBrowserWebPage;
    QWORD qwReserved;
    LPARAM *pReserved;
    WCHAR *wszHostExe;
    QWORD qwHostVersion;
    WCHAR *wszPlayerUserAgent;
} WM_READER_CLIENTINFO;

enum
{
    WM_SF_CLEANPOINT    = 0x1,
    WM_SF_DISCONTINUITY = 0x2,
    WM_SF_DATALOSS      = 0x4,
};

typedef enum WMT_ATTR_DATATYPE
{
    WMT_TYPE_DWORD      = 0,
    WMT_TYPE_STRING     = 1,
    WMT_TYPE_BINARY     = 2,
    WMT_TYPE_BOOL       = 3,
    WMT_TYPE_QWORD      = 4,
    WMT_TYPE_WORD       = 5,
    WMT_TYPE_GUID       = 6,
} WMT_ATTR_DATATYPE;

typedef enum WMT_STATUS
{
    WMT_ERROR                       =  0,
    WMT_OPENED                      =  1,
    WMT_BUFFERING_START             =  2,
    WMT_BUFFERING_STOP              =  3,
    WMT_EOF                         =  4,
    WMT_END_OF_FILE                 =  4,
    WMT_END_OF_SEGMENT              =  5,
    WMT_END_OF_STREAMING            =  6,
    WMT_LOCATING                    =  7,
    WMT_CONNECTING                  =  8,
    WMT_NO_RIGHTS                   =  9,
    WMT_MISSING_CODEC               = 10,
    WMT_STARTED                     = 11,
    WMT_STOPPED                     = 12,
    WMT_CLOSED                      = 13,
    WMT_STRIDING                    = 14,
    WMT_TIMER                       = 15,
    WMT_INDEX_PROGRESS              = 16,
    WMT_SAVEAS_START                = 17,
    WMT_SAVEAS_STOP                 = 18,
    WMT_NEW_SOURCEFLAGS             = 19,
    WMT_NEW_METADATA                = 20,
    WMT_BACKUPRESTORE_BEGIN         = 21,
    WMT_SOURCE_SWITCH               = 22,
    WMT_ACQUIRE_LICENSE             = 23,
    WMT_INDIVIDUALIZE               = 24,
    WMT_NEEDS_INDIVIDUALIZATION     = 25,
    WMT_NO_RIGHTS_EX                = 26,
    WMT_BACKUPRESTORE_END           = 27,
    WMT_BACKUPRESTORE_CONNECTING    = 28,
    WMT_BACKUPRESTORE_DISCONNECTING = 29,
    WMT_ERROR_WITHURL               = 30,
    WMT_RESTRICTED_LICENSE          = 31,
    WMT_CLIENT_CONNECT              = 32,
    WMT_CLIENT_DISCONNECT           = 33,
    WMT_NATIVE_OUTPUT_PROPS_CHANGED = 34,
    WMT_RECONNECT_START             = 35,
    WMT_RECONNECT_END               = 36,
    WMT_CLIENT_CONNECT_EX           = 37,
    WMT_CLIENT_DISCONNECT_EX        = 38,
    WMT_SET_FEC_SPAN                = 39,
    WMT_PREROLL_READY               = 40,
    WMT_PREROLL_COMPLETE            = 41,
    WMT_CLIENT_PROPERTIES           = 42,
    WMT_LICENSEURL_SIGNATURE_STATE  = 43,
    WMT_INIT_PLAYLIST_BURN          = 44,
    WMT_TRANSCRYPTOR_INIT           = 45,
    WMT_TRANSCRYPTOR_SEEKED         = 46,
    WMT_TRANSCRYPTOR_READ           = 47,
    WMT_TRANSCRYPTOR_CLOSED         = 48,
    WMT_PROXIMITY_RESULT            = 49,
    WMT_PROXIMITY_COMPLETED         = 50,
    WMT_CONTENT_ENABLER             = 51
} WMT_STATUS;

typedef enum WMT_STREAM_SELECTION
{
    WMT_OFF               = 0,
    WMT_CLEANPOINT_ONLY   = 1,
    WMT_ON                = 2,
} WMT_STREAM_SELECTION;

typedef enum WMT_VERSION
{
    WMT_VER_4_0 = 0x00040000,
    WMT_VER_7_0 = 0x00070000,
    WMT_VER_8_0 = 0x00080000,
    WMT_VER_9_0 = 0x00090000
} WMT_VERSION;

typedef enum WMT_PLAY_MODE
{
    WMT_PLAY_MODE_AUTOSELECT,
    WMT_PLAY_MODE_LOCAL,
    WMT_PLAY_MODE_DOWNLOAD,
    WMT_PLAY_MODE_STREAMING
} WMT_PLAY_MODE;

typedef enum tagWMT_OFFSET_FORMAT
{
    WMT_OFFSET_FORMAT_100NS,
    WMT_OFFSET_FORMAT_FRAME_NUMBERS,
    WMT_OFFSET_FORMAT_PLAYLIST_OFFSET,
    WMT_OFFSET_FORMAT_TIMECODE,
    WMT_OFFSET_FORMAT_100NS_APPROXIMATE
} WMT_OFFSET_FORMAT;

typedef enum WMT_CODEC_INFO_TYPE
{
    WMT_CODECINFO_AUDIO   = 0,
    WMT_CODECINFO_VIDEO   = 1,
    WMT_CODECINFO_UNKNOWN = 0xFFFFFFFF,
} WMT_CODEC_INFO_TYPE;

typedef enum WMT_PROXY_SETTINGS
{
    WMT_PROXY_SETTING_NONE     = 0,
    WMT_PROXY_SETTING_MANUAL   = 1,
    WMT_PROXY_SETTING_AUTO     = 2,
    WMT_PROXY_SETTING_BROWSER  = 3,
    WMT_PROXY_SETTING_MAX
} WMT_PROXY_SETTINGS;

typedef enum tagWMT_STORAGE_FORMAT
{
    WMT_Storage_Format_MP3  = 0,
    WMT_Storage_Format_V1   = 1
} WMT_STORAGE_FORMAT;

#pragma pack(push,2)
typedef struct _WMStreamPrioritizationRecord
{
    WORD wStreamNumber;
    BOOL fMandatory;
} WM_STREAM_PRIORITY_RECORD;

typedef struct _WMT_TIMECODE_EXTENSION_DATA
{
    WORD  wRange;
    DWORD dwTimecode;
    DWORD dwUserbits;
    DWORD dwAmFlags;
} WMT_TIMECODE_EXTENSION_DATA;
#pragma pack(pop)

typedef struct _WM_PORT_NUMBER_RANGE
{
    WORD wPortBegin;
    WORD wPortEnd;
} WM_PORT_NUMBER_RANGE;

typedef LPCWSTR LPCWSTR_WMSDK_TYPE_SAFE;

[
    object,
    uuid(6d7cdc70-9888-11d3-8edc-00c04f6109cf),
    pointer_default(unique),
    local
]
interface IWMStatusCallback : IUnknown
{
    HRESULT OnStatus(
        [in] WMT_STATUS Status,
        [in] HRESULT hr,
        [in] WMT_ATTR_DATATYPE dwType,
        [in] BYTE *pValue,
        [in] void *pvContext);
}

[
    object,
    uuid(96406bd8-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMReaderCallback : IWMStatusCallback
{
    HRESULT OnSample(
        [in] DWORD dwOutputNum,
        [in] QWORD cnsSampleTime,
        [in] QWORD cnsSampleDuration,
        [in] DWORD dwFlags,
        [in] INSSBuffer *pSample,
        [in] void *pvContext);
}

[
    object,
    local,
    uuid(96406beb-2b2b-11d3-b36b-00c04f6108ff),
]
interface IWMReaderCallbackAdvanced : IUnknown
{
    HRESULT OnStreamSample(WORD stream_number, QWORD pts, QWORD duration, DWORD flags, INSSBuffer *sample, void *context);
    HRESULT OnTime(QWORD time, void *context);
    HRESULT OnStreamSelection(WORD count, WORD *stream_numbers, WMT_STREAM_SELECTION *selections, void *context);
    HRESULT OnOutputPropsChanged(DWORD output_number, WM_MEDIA_TYPE *mt, void *context);
    HRESULT AllocateForStream(WORD stream_number, DWORD size, INSSBuffer **sample, void *context);
    HRESULT AllocateForOutput(DWORD output_number, DWORD size, INSSBuffer **sample, void *context);
}

[
    object,
    uuid(96406Bdd-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMStreamList : IUnknown
{
    HRESULT GetStreams(
        [out, size_is(*pcStreams)] WORD *pwStreamNumArray,
        [in, out] WORD *pcStreams);

    HRESULT AddStream([in] WORD wStreamNum);
    HRESULT RemoveStream([in] WORD wStreamNum);
}

[
    object,
    uuid(96406Bde-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMMutualExclusion : IWMStreamList
{
    HRESULT GetType([out] GUID *pguidType);
    HRESULT SetType([in] REFGUID guidType);
}

[
    object,
    uuid(ad694af1-f8d9-42f8-bc47-70311b0c4f9e),
    pointer_default(unique),
    local
]
interface IWMBandwidthSharing : IWMStreamList
{
    HRESULT GetType([out] GUID *guid);
    HRESULT SetType([in] REFGUID guid);

    HRESULT GetBandwidth(
        [out] DWORD *bitrate,
        [out] DWORD *buffer);

    HRESULT SetBandwidth(
        [in] DWORD bitrate,
        [in] DWORD buffer);
}

[
    object,
    uuid(8c1c6090-f9a8-4748-8ec3-dd1108ba1e77),
    pointer_default(unique),
    local
]
interface IWMStreamPrioritization : IUnknown
{
    HRESULT GetPriorityRecords(
        [out] WM_STREAM_PRIORITY_RECORD *array,
        [in, out] WORD *records);

    HRESULT SetPriorityRecords(
        [in] WM_STREAM_PRIORITY_RECORD *array,
        [in] WORD records);
}

[
    object,
    uuid(96406Bdc-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMStreamConfig : IUnknown
{
    HRESULT GetStreamType([out] GUID *pguidStreamType);
    HRESULT GetStreamNumber([out] WORD *pwStreamNum);
    HRESULT SetStreamNumber([in] WORD wStreamNum);

    HRESULT GetStreamName(
        [out, size_is(*pcchStreamName)] WCHAR *pwszStreamName,
        [in, out] WORD *pcchStreamName);

    HRESULT SetStreamName([in] LPCWSTR_WMSDK_TYPE_SAFE pwszStreamName);

    HRESULT GetConnectionName(
        [out, size_is(*pcchInputName)] WCHAR *pwszInputName,
        [in, out] WORD *pcchInputName);

    HRESULT SetConnectionName([in] LPCWSTR_WMSDK_TYPE_SAFE pwszInputName);
    HRESULT GetBitrate([out] DWORD *pdwBitrate);
    HRESULT SetBitrate([in] DWORD pdwBitrate);
    HRESULT GetBufferWindow([out] DWORD *pmsBufferWindow);
    HRESULT SetBufferWindow([in] DWORD msBufferWindow);
}

[
    object,
    uuid(96406bdb-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMProfile : IUnknown
{
    HRESULT GetVersion(
        [out] WMT_VERSION *pdwVersion);

    HRESULT GetName(
        [out, size_is(*pcchName)] WCHAR *pwszName,
        [in, out] DWORD *pcchName);

    HRESULT SetName(
        [in] const WCHAR *pwszName);

    HRESULT GetDescription(
        [out, size_is(*pcchDescription)] WCHAR *pwszDescription,
        [in, out] DWORD *pcchDescription);

    HRESULT SetDescription(
        [in] const WCHAR *pwszDescription);

    HRESULT GetStreamCount(
        [out] DWORD *pcStreams);

    HRESULT GetStream(
        [in] DWORD dwStreamIndex,
        [out] IWMStreamConfig **ppConfig);

    HRESULT GetStreamByNumber(
        [in] WORD wStreamNum,
        [out] IWMStreamConfig **ppConfig);

    HRESULT RemoveStream(
        [in] IWMStreamConfig *pConfig);

    HRESULT RemoveStreamByNumber(
        [in] WORD wStreamNum);

    HRESULT AddStream(
        [in] IWMStreamConfig *pConfig);

    HRESULT ReconfigStream(
        [in] IWMStreamConfig *pConfig);

    HRESULT CreateNewStream(
        [in] REFGUID guidStreamType,
        [out] IWMStreamConfig **ppConfig);

    HRESULT GetMutualExclusionCount(
        [out] DWORD *pcME);

    HRESULT GetMutualExclusion(
        [in] DWORD dwMEIndex,
        [out] IWMMutualExclusion **ppME);

    HRESULT RemoveMutualExclusion(
        [in] IWMMutualExclusion *pME);

    HRESULT AddMutualExclusion(
        [in] IWMMutualExclusion *pME);

    HRESULT CreateNewMutualExclusion(
        [out] IWMMutualExclusion **ppME);
}

[
    object,
    uuid(07e72d33-d94e-4be7-8843-60ae5ff7e5f5),
    pointer_default(unique),
    local
]
interface IWMProfile2 : IWMProfile
{
    HRESULT GetProfileID([out] GUID *guid);
}

[
    object,
    uuid(00ef96cc-a461-4546-8bcd-c9a28f0e06f5),
    pointer_default(unique),
    local
]
interface IWMProfile3 : IWMProfile2
{
    HRESULT GetStorageFormat([out] WMT_STORAGE_FORMAT *storage);
    HRESULT SetStorageFormat([in] WMT_STORAGE_FORMAT storage);

    HRESULT GetBandwidthSharingCount([out] DWORD *count);

    HRESULT GetBandwidthSharing(
        [in] DWORD index,
        [out] IWMBandwidthSharing **bandwidth);

    HRESULT RemoveBandwidthSharing([in] IWMBandwidthSharing *bandwidth);

    HRESULT AddBandwidthSharing([in] IWMBandwidthSharing *bandwidth);

    HRESULT CreateNewBandwidthSharing([out] IWMBandwidthSharing **bandwidth);

    HRESULT GetStreamPrioritization([out] IWMStreamPrioritization **stream);

    HRESULT SetStreamPrioritization([in] IWMStreamPrioritization *stream);

    HRESULT RemoveStreamPrioritization();

    HRESULT CreateNewStreamPrioritization([out] IWMStreamPrioritization **stream);

    HRESULT GetExpectedPacketCount(
        [in] QWORD duration,
        [out] QWORD *packets);
}

[
    object,
    uuid(d16679f2-6ca0-472d-8d31-2f5d55aee155),
    pointer_default(unique),
    local
]
interface IWMProfileManager : IUnknown
{
    HRESULT CreateEmptyProfile(
        [in] WMT_VERSION dwVersion,
        [out] IWMProfile **ppProfile);

    HRESULT LoadProfileByID(
        [in] REFGUID guidProfile,
        [out] IWMProfile **ppProfile);

    HRESULT LoadProfileByData(
        [in] const WCHAR *pwszProfile,
        [out] IWMProfile **ppProfile);

    HRESULT SaveProfile(
        [in] IWMProfile *pIWMProfile,
        [in] WCHAR *pwszProfile,
        [in, out] DWORD *pdwLength);

    HRESULT GetSystemProfileCount(
        [out] DWORD *pcProfiles);

    HRESULT LoadSystemProfile(
        [in] DWORD dwProfileIndex,
        [out] IWMProfile **ppProfile);
}

[
    object,
    uuid(7a924e51-73c1-494d-8019-23d37ed9b89a),
    pointer_default(unique),
    local
]
interface IWMProfileManager2 : IWMProfileManager
{
    HRESULT GetSystemProfileVersion(WMT_VERSION *version);
    HRESULT SetSystemProfileVersion(WMT_VERSION version);
};

cpp_quote("HRESULT WINAPI WMCreateProfileManager(IWMProfileManager**);")

[
    object,
    uuid(a970f41e-34de-4a98-b3ba-e4b3ca7528f0),
    pointer_default(unique),
    local
]
interface IWMCodecInfo : IUnknown
{
    HRESULT GetCodecInfoCount(
        [in] REFGUID guid,
        [out] DWORD *count);

    HRESULT GetCodecFormatCount(
        [in] REFGUID guid,
        [in] DWORD codecindex,
        [out] DWORD *formatcount);

    HRESULT GetCodecFormat(
        [in] REFGUID guid,
        [in] DWORD codecindex,
        [in] DWORD formatindex,
        [out] IWMStreamConfig **streamconfig);
}

[
    object,
    uuid(aa65e273-b686-4056-91ec-dd768d4df710),
    pointer_default(unique),
    local
]
interface IWMCodecInfo2 : IWMCodecInfo
{
    HRESULT GetCodecName(
        [in] REFGUID guid,
        [in] DWORD codecindex,
        [out, size_is(*namesize)] WCHAR *name,
        [in, out] DWORD *namesize);

    HRESULT GetCodecFormatDesc(
        [in] REFGUID guid,
        [in] DWORD codecindex,
        [in] DWORD formatindex,
        [out] IWMStreamConfig **streamconfig,
        [out, size_is(*descrsize)] WCHAR *description,
        [in, out] DWORD *descrsize);
}

[
    object,
    uuid(7e51f487-4d93-4f98-8ab4-27d0565adc51),
    pointer_default(unique),
    local
]
interface IWMCodecInfo3 : IWMCodecInfo2
{
    HRESULT GetCodecFormatProp(
        [in] REFGUID guid,
        [in] DWORD codecindex,
        [in] DWORD formatindex,
        [in] const WCHAR *name,
        [out] WMT_ATTR_DATATYPE *type,
        [out, size_is(*size)] BYTE *value,
        [in, out] DWORD *size);

    HRESULT GetCodecProp(
        [in] REFGUID guid,
        [in] DWORD codecindex,
        [in] const WCHAR *name,
        [out] WMT_ATTR_DATATYPE *type,
        [out, size_is(*size)] BYTE *value,
        [in, out] DWORD *size);

    HRESULT SetCodecEnumerationSetting(
        [in] REFGUID guid,
        [in] DWORD codecindex,
        [in] const WCHAR *name,
        [in] WMT_ATTR_DATATYPE type,
        [in, size_is(size)] const BYTE *value,
        [in] DWORD size);

    HRESULT GetCodecEnumerationSetting(
        [in] REFGUID guid,
        [in] DWORD codecindex,
        [in] const WCHAR *name,
        [out] WMT_ATTR_DATATYPE *type,
        [out, size_is(*size)] BYTE *value,
        [in, out] DWORD *size);
}

cpp_quote("static const WCHAR g_wszNumPasses[] = {'_','P','A','S','S','E','S','U','S','E','D',0};")
cpp_quote("static const WCHAR g_wszVBREnabled[] = {'_','V','B','R','E','N','A','B','L','E','D',0};")

[
    object,
    uuid(96406bce-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMMediaProps : IUnknown
{
    HRESULT GetType(
        [out] GUID *pguidType);

    HRESULT GetMediaType(
        [out] WM_MEDIA_TYPE *pType,
        [in, out] DWORD *pcbType);

    HRESULT SetMediaType(
        [in] WM_MEDIA_TYPE *pType);
}

[
    object,
    uuid(96406bd7-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMOutputMediaProps : IWMMediaProps
{
    HRESULT GetStreamGroupName(
        [out, size_is(*pcchName)] WCHAR *pwszName,
        [in, out] WORD *pcchName);

    HRESULT GetConnectionName(
        [out, size_is(*pcchName)] WCHAR *pwszName,
        [in, out] WORD *pcchName);
}

[
    object,
    uuid(96406bd9-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMMetadataEditor : IUnknown
{
    HRESULT Open(
        [in] const WCHAR *pwszFilename);

    HRESULT Close();

    HRESULT Flush();
}

[
    object,
    uuid(96406bd6-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMReader : IUnknown
{
    HRESULT Open(
        [in] const WCHAR *pwszURL,
        [in] IWMReaderCallback *pCallback,
        [in] void *pvContext);

    HRESULT Close();

    HRESULT GetOutputCount(
        [out] DWORD *pcOutputs);

    HRESULT GetOutputProps(
        [in] DWORD dwOutputNum,
        [out] IWMOutputMediaProps **ppOutput);

    HRESULT SetOutputProps(
        [in] DWORD dwOutputNum,
        [in] IWMOutputMediaProps *pOutput);

    HRESULT GetOutputFormatCount(
        [in] DWORD dwOutputNumber,
        [out] DWORD *pcFormats);

    HRESULT GetOutputFormat(
        [in] DWORD dwOutputNumber,
        [in] DWORD dwFormatNumber,
        [out] IWMOutputMediaProps** ppProps);

    HRESULT Start(
        [in] QWORD cnsStart,
        [in] QWORD cnsDuration,
        [in] float fRate,
        [in] void *pvContext);

    HRESULT Stop();

    HRESULT Pause();

    HRESULT Resume();
}

[
    object,
    uuid(e5b7ca9a-0f1c-4f66-9002-74ec50d8b304),
    pointer_default(unique),
    local
]
interface IWMPlayerHook : IUnknown
{
    HRESULT PreDecode();
}

[
    object,
    uuid(96406bea-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMReaderAdvanced : IUnknown
{
    HRESULT SetUserProvidedClock(
        [in] BOOL fUserClock);

    HRESULT GetUserProvidedClock(
        [out] BOOL *pfUserClock);

    HRESULT DeliverTime(
        [in] QWORD cnsTime);

    HRESULT SetManualStreamSelection(
        [in] BOOL fSelection);

    HRESULT GetManualStreamSelection(
        [out] BOOL *pfSelection);

    HRESULT SetStreamsSelected(
        [in] WORD cStreamCount,
        [in] WORD *pwStreamNumbers,
        [in] WMT_STREAM_SELECTION *pSelections);

    HRESULT GetStreamSelected(
        [in] WORD wStreamNum,
        [out] WMT_STREAM_SELECTION *pSelection);

    HRESULT SetReceiveSelectionCallbacks(
        [in] BOOL fGetCallbacks);

    HRESULT GetReceiveSelectionCallbacks(
        [out] BOOL *pfGetCallbacks);

    HRESULT SetReceiveStreamSamples(
        [in] WORD wStreamNum,
        [in] BOOL fReceiveStreamSamples);

    HRESULT GetReceiveStreamSamples(
        [in] WORD wStreamNum,
        [out] BOOL *pfReceiveStreamSamples);

    HRESULT SetAllocateForOutput(
        [in] DWORD dwOutputNum,
        [in] BOOL fAllocate);

    HRESULT GetAllocateForOutput(
        [in] DWORD dwOutputNum,
        [out] BOOL *pfAllocate);

    HRESULT SetAllocateForStream(
        [in] WORD wStreamNum,
        [in] BOOL fAllocate);

    HRESULT GetAllocateForStream(
        [in] WORD dwStreamNum,
        [out] BOOL *pfAllocate);

    HRESULT GetStatistics(
        [in, out] WM_READER_STATISTICS *pStatistics);

    HRESULT SetClientInfo(
        [in] WM_READER_CLIENTINFO *pClientInfo);

    HRESULT GetMaxOutputSampleSize(
        [in] DWORD dwOutput,
        [out] DWORD *pcbMax);

    HRESULT GetMaxStreamSampleSize(
        [in] WORD wStream,
        [out] DWORD *pcbMax);

    HRESULT NotifyLateDelivery(
        QWORD cnsLateness);
}

[
    object,
    uuid(ae14a945-b90c-4d0d-9127-80d665f7d73e),
    pointer_default(unique),
    local
]
interface IWMReaderAdvanced2 : IWMReaderAdvanced
{
    HRESULT SetPlayMode(
        [in] WMT_PLAY_MODE Mode);

    HRESULT GetPlayMode(
        [out] WMT_PLAY_MODE *pMode);

    HRESULT GetBufferProgress(
        [out] DWORD *pdwPercent,
        [out] QWORD *pcnsBuffering);

    HRESULT GetDownloadProgress(
        [out] DWORD *pdwPercent,
        [out] QWORD *pqwBytesDownloaded,
        [out] QWORD *pcnsDownload);

    HRESULT GetSaveAsProgress(
        [out] DWORD *pdwPercent);

    HRESULT SaveFileAs(
        [in] const WCHAR *pwszFilename);

    HRESULT GetProtocolName(
        [out, size_is(*pcchProtocol)] WCHAR *pwszProtocol,
        [in, out] DWORD *pcchProtocol);

    HRESULT StartAtMarker(
        [in] WORD wMarkerIndex,
        [in] QWORD cnsDuration,
        [in] float fRate,
        [in] void *pvContext);

    HRESULT GetOutputSetting(
        [in] DWORD dwOutputNum,
        [in] LPCWSTR pszName,
        [out] WMT_ATTR_DATATYPE *pType,
        [out, size_is(*pcbLength)] BYTE *pValue,
        [in, out] WORD *pcbLength);

    HRESULT SetOutputSetting(
        [in] DWORD dwOutputNum,
        [in] LPCWSTR pszName,
        [in] WMT_ATTR_DATATYPE Type,
        [in, size_is(cbLength)] const BYTE *pValue,
        [in] WORD cbLength);

    HRESULT Preroll(
        [in] QWORD cnsStart,
        [in] QWORD cnsDuration,
        [in] float fRate);

    HRESULT SetLogClientID(
        [in] BOOL fLogClientID);

    HRESULT GetLogClientID(
        [out] BOOL *pfLogClientID);

    HRESULT StopBuffering();

    HRESULT OpenStream(
        [in] IStream *pStream,
        [in] IWMReaderCallback *pCallback,
        [in] void *pvContext);
}

[
    object,
    uuid(5dc0674b-f04B-4a4e-9f2a-b1afde2c8100),
    pointer_default(unique),
    local
]
interface IWMReaderAdvanced3 : IWMReaderAdvanced2
{
    HRESULT StopNetStreaming();

    HRESULT StartAtPosition(
        [in] WORD wStreamNum,
        [in] void *pvOffsetStart,
        [in] void *pvDuration,
        [in] WMT_OFFSET_FORMAT dwOffsetFormat,
        [in] float fRate,
        [in] void *pvContext);
}

[
    object,
    uuid(945a76a2-12ae-4d48-bd3c-cd1d90399b85),
    pointer_default(unique),
    local
]
interface IWMReaderAdvanced4 : IWMReaderAdvanced3
{
    HRESULT GetLanguageCount(
        [in] DWORD dwOutputNum,
        [out] WORD *pwLanguageCount);

    HRESULT GetLanguage(
        [in] DWORD dwOutputNum,
        [in] WORD wLanguage,
        [out, size_is(*pcchLanguageStringLength)] WCHAR *pwszLanguageString,
        [in, out] WORD *pcchLanguageStringLength);

    HRESULT GetMaxSpeedFactor(
        [out] double *pdblFactor);

    HRESULT IsUsingFastCache(
        [out] BOOL *pfUsingFastCache);

    HRESULT AddLogParam(
        [in] LPCWSTR wszNameSpace,
        [in] LPCWSTR wszName,
        [in] LPCWSTR wszValue);

    HRESULT SendLogParams();

    HRESULT CanSaveFileAs(
        [out] BOOL *pfCanSave);

    HRESULT CancelSaveFileAs();

    HRESULT GetURL(
        [out, size_is(*pcchURL)] WCHAR *pwszURL,
        [in, out] DWORD *pcchURL);
}

[
    object,
    uuid(24c44db0-55d1-49ae-a5cc-f13815e36363),
    pointer_default(unique),
    local
]
interface IWMReaderAdvanced5 : IWMReaderAdvanced4
{
    HRESULT SetPlayerHook(
        [in] DWORD dwOutputNum,
        [in] IWMPlayerHook *pHook);
}

[
    object,
    uuid(18a2e7f8-428f-4acd-8a00-e64639bc93de),
    pointer_default(unique),
    local
]
interface IWMReaderAdvanced6 : IWMReaderAdvanced5
{
    HRESULT SetProtectStreamSamples(
        [in, size_is(cbCertificate)] BYTE *pbCertificate,
        [in] DWORD cbCertificate,
        [in] DWORD dwCertificateType,
        [in] DWORD dwFlags,
        [out, size_is(*pcbInitializationVector)] BYTE *pbInitializationVector,
        [in, out] DWORD *pcbInitializationVector);
}

[
    object,
    uuid(9397f121-7705-4dc9-b049-98b698188414),
    pointer_default( unique ),
    local
]
interface IWMSyncReader : IUnknown
{
    HRESULT Open(
        [in] const WCHAR *pwszFilename);

    HRESULT Close();

    HRESULT SetRange(
        [in] QWORD cnsStartTime,
        [in] LONGLONG cnsDuration);

    HRESULT SetRangeByFrame(
        [in] WORD wStreamNum,
        [in] QWORD qwFrameNumber,
        [in] LONGLONG cFramesToRead);

    HRESULT GetNextSample(
        [in] WORD wStreamNum,
        [out] INSSBuffer **ppSample,
        [out] QWORD *pcnsSampleTime,
        [out] QWORD *pcnsDuration,
        [out] DWORD *pdwFlags,
        [out] DWORD *pdwOutputNum,
        [out] WORD *pwStreamNum);

    HRESULT SetStreamsSelected(
        [in] WORD cStreamCount,
        [in] WORD *pwStreamNumbers,
        [in] WMT_STREAM_SELECTION *pSelections);

    HRESULT GetStreamSelected(
        [in] WORD wStreamNum,
        [out] WMT_STREAM_SELECTION *pSelection);

    HRESULT SetReadStreamSamples(
        [in] WORD wStreamNum,
        [in] BOOL fCompressed);

    HRESULT GetReadStreamSamples(
        [in] WORD wStreamNum,
        [out] BOOL *pfCompressed);

    HRESULT GetOutputSetting(
        [in] DWORD dwOutputNum,
        [in] LPCWSTR pszName,
        [out] WMT_ATTR_DATATYPE *pType,
        [out, size_is(*pcbLength)] BYTE *pValue,
        [in, out] WORD *pcbLength);

    HRESULT SetOutputSetting(
        [in] DWORD dwOutputNum,
        [in] LPCWSTR pszName,
        [in] WMT_ATTR_DATATYPE Type,
        [in, size_is(cbLength)] const BYTE *pValue,
        [in] WORD cbLength);

    HRESULT GetOutputCount(
        [out] DWORD *pcOutputs);

    HRESULT GetOutputProps(
        [in] DWORD dwOutputNum,
        [out] IWMOutputMediaProps **ppOutput);

    HRESULT SetOutputProps(
        [in] DWORD dwOutputNum,
        [in] IWMOutputMediaProps *pOutput);

    HRESULT GetOutputFormatCount(
        [in] DWORD dwOutputNum,
        [out] DWORD *pcFormats);

    HRESULT GetOutputFormat(
        [in] DWORD dwOutputNum,
        [in] DWORD dwFormatNum,
        [out] IWMOutputMediaProps **ppProps);

    HRESULT GetOutputNumberForStream(
        [in] WORD wStreamNum,
        [out] DWORD *pdwOutputNum);

    HRESULT GetStreamNumberForOutput(
        [in] DWORD dwOutputNum,
        [out] WORD *pwStreamNum);

    HRESULT GetMaxOutputSampleSize(
        [in] DWORD dwOutput,
        [out] DWORD *pcbMax);

    HRESULT GetMaxStreamSampleSize(
        [in] WORD wStream,
        [out] DWORD *pcbMax);

    HRESULT OpenStream(
        [in] IStream *pStream);
}

[
    object,
    uuid(9f762fa7-a22e-428d-93c9-ac82f3aafe5a),
    pointer_default( unique ),
    local
]
interface IWMReaderAllocatorEx : IUnknown
{
    HRESULT AllocateForStreamEx(
        [in] WORD wStreamNum,
        [in] DWORD cbBuffer,
        [out] INSSBuffer **ppBuffer,
        [in] DWORD dwFlags,
        [in] QWORD cnsSampleTime,
        [in] QWORD cnsSampleDuration,
        [in] void *pvContext);

    HRESULT AllocateForOutputEx(
        [in] DWORD output,
        [in] DWORD cbBuffer,
        [out] INSSBuffer **ppBuffer,
        [in] DWORD dwFlags,
        [in] QWORD cnsSampleTime,
        [in] QWORD cnsSampleDuration,
        [in] void *pvContext);
}

[
    object,
    uuid(faed3d21-1b6b-4af7-8cb6-3e189bbc187b),
    pointer_default( unique ),
    local
]
interface IWMSyncReader2 : IWMSyncReader
{
    HRESULT SetRangeByTimecode(
        [in] WORD wStreamNum,
        [in] WMT_TIMECODE_EXTENSION_DATA *pStart,
        [in] WMT_TIMECODE_EXTENSION_DATA *pEnd);

    HRESULT SetRangeByFrameEx(
        [in] WORD wStreamNum,
        [in] QWORD qwFrameNumber,
        [in] LONGLONG cFramesToRead,
        [out] QWORD *pcnsStartTime);

    HRESULT SetAllocateForOutput(
        [in] DWORD dwOutputNum,
        [in] IWMReaderAllocatorEx *pAllocator);

    HRESULT GetAllocateForOutput(
        [in] DWORD dwOutputNum,
        [out] IWMReaderAllocatorEx **ppAllocator);

    HRESULT SetAllocateForStream(
        [in] DWORD dwStreamNum,
        [in] IWMReaderAllocatorEx *pAllocator);

    HRESULT GetAllocateForStream(
        [in] DWORD dwStreamNum,
        [out] IWMReaderAllocatorEx **ppAllocator);
}

[
    object,
    uuid(96406bd5-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMInputMediaProps : IWMMediaProps
{
    HRESULT GetConnectionName(
        [out, size_is(*pcchName)] WCHAR *pwszName,
        [in, out] WORD *pcchName);

    HRESULT GetGroupName(
        [out, size_is(*pcchName)] WCHAR *pwszName,
        [in, out] WORD *pcchName);
}

[
    object,
    uuid(96406be4-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMWriterSink : IUnknown
{
    HRESULT OnHeader(
        [in] INSSBuffer *pHeader);

    HRESULT IsRealTime(
        [out] BOOL *pfRealTime);

    HRESULT AllocateDataUnit(
        [in] DWORD cbDataUnit,
        [out] INSSBuffer **ppDataUnit);

    HRESULT OnDataUnit(
        [in] INSSBuffer *pDataUnit);

    HRESULT OnEndWriting();
}

[
    object,
    uuid(96406bd4-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMWriter : IUnknown
{
    HRESULT SetProfileByID(
        [in] REFGUID guidProfile);

    HRESULT SetProfile(
        [in] IWMProfile *pProfile);

    HRESULT SetOutputFilename(
        [in] const WCHAR *pwszFilename);

    HRESULT GetInputCount(
        [out] DWORD *pcInputs);

    HRESULT GetInputProps(
        [in] DWORD dwInputNum,
        [out] IWMInputMediaProps **ppInput);

    HRESULT SetInputProps(
        [in] DWORD dwInputNum,
        [in] IWMInputMediaProps *pInput);

    HRESULT GetInputFormatCount(
        [in] DWORD dwInputNumber,
        [out] DWORD *pcFormats);

    HRESULT GetInputFormat(
        [in] DWORD dwInputNumber,
        [in] DWORD dwFormatNumber,
        [out] IWMInputMediaProps **pProps);

    HRESULT BeginWriting();

    HRESULT EndWriting();

    HRESULT AllocateSample(
        [in] DWORD dwSampleSize,
        [out] INSSBuffer **ppSample);

    HRESULT WriteSample(
        [in] DWORD dwInputNum,
        [in] QWORD cnsSampleTime,
        [in] DWORD dwFlags,
        [in] INSSBuffer *pSample);

    HRESULT Flush();
}

[
    object,
    uuid(96406be3-2b2b-11d3-b36b-00C04f6108ff),
    pointer_default(unique),
    local
]
interface IWMWriterAdvanced : IUnknown
{
    HRESULT GetSinkCount(
        [out] DWORD *pcSinks);

    HRESULT GetSink(
        [in] DWORD dwSinkNum,
        [out] IWMWriterSink **ppSink);

    HRESULT AddSink(
        [in] IWMWriterSink *pSink);

    HRESULT RemoveSink(
        [in] IWMWriterSink *pSink);

    HRESULT WriteStreamSample(
        [in] WORD wStreamNum,
        [in] QWORD cnsSampleTime,
        [in] DWORD msSampleSendTime,
        [in] QWORD cnsSampleDuration,
        [in] DWORD dwFlags,
        [in] INSSBuffer *pSample);

    HRESULT SetLiveSource(
        BOOL fIsLiveSource);

    HRESULT IsRealTime(
        [out] BOOL *pfRealTime);

    HRESULT GetWriterTime(
        [out] QWORD *pCurrentTime);

    HRESULT GetStatistics(
        [in] WORD wStreamNum,
        [out] WM_WRITER_STATISTICS *pStats);

    HRESULT SetSyncTolerance(
        [in] DWORD msWindow);

    HRESULT GetSyncTolerance(
        [out] DWORD *pmsWindow);
}

[
    object,
    uuid(962dc1ec-c046-4db8-9cc7-26ceae500817),
    pointer_default(unique),
    local
]
interface IWMWriterAdvanced2 : IWMWriterAdvanced
{
    HRESULT GetInputSetting(
        [in] DWORD dwInputNum,
        [in] LPCWSTR pszName,
        [out] WMT_ATTR_DATATYPE *pType,
        [out, size_is(*pcbLength)] BYTE *pValue,
        [in, out] WORD *pcbLength);

    HRESULT SetInputSetting(
        [in] DWORD dwInputNum,
        [in] LPCWSTR pszName,
        [in] WMT_ATTR_DATATYPE Type,
        [in, size_is(cbLength)] const BYTE *pValue,
        [in] WORD cbLength);
}

[
    object,
    uuid(2cd6492d-7c37-4e76-9d3b-59261183a22e),
    pointer_default(unique),
    local
]
interface IWMWriterAdvanced3 : IWMWriterAdvanced2
{
    HRESULT GetStatisticsEx(
        [in] WORD wStreamNum,
        [out] WM_WRITER_STATISTICS_EX *pStats);

    HRESULT SetNonBlocking();
}

[
    object,
    uuid(96406bda-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMHeaderInfo : IUnknown
{
    HRESULT GetAttributeCount(
        [in] WORD stream_num,
        [out] WORD *attributes );

    HRESULT GetAttributeByIndex(
        [in] WORD index,
        [in, out] WORD *stream_num,
        [out, size_is( *name_len )] WCHAR *name,
        [in, out] WORD *name_len,
        [out] WMT_ATTR_DATATYPE *type,
        [out, size_is( *length )] BYTE *value,
        [in, out] WORD *length );

    HRESULT GetAttributeByName(
        [in, out] WORD *stream_num,
        [in] LPCWSTR name,
        [out] WMT_ATTR_DATATYPE *type,
        [out, size_is( *length )] BYTE *value,
        [in, out] WORD *length );

    HRESULT SetAttribute(
        [in] WORD stream_num,
        [in] LPCWSTR name,
        [in] WMT_ATTR_DATATYPE type,
        [in, size_is( length )] const BYTE *value,
        [in] WORD length );

    HRESULT GetMarkerCount(
        [out] WORD *markers );

    HRESULT GetMarker(
        [in] WORD index,
        [out, size_is( *marker_len )] WCHAR *marker_name,
        [in, out] WORD *marker_len,
        [out] QWORD *marker_time );

    HRESULT AddMarker(
        [in] LPCWSTR_WMSDK_TYPE_SAFE marker_name,
        [in] QWORD marker_time );

    HRESULT RemoveMarker(
        [in] WORD index );

    HRESULT GetScriptCount(
        [out] WORD *scripts );

    HRESULT GetScript(
        [in] WORD index,
        [out, size_is( *type_len )] WCHAR *type,
        [in, out] WORD *type_len,
        [out, size_is( *command_len )] WCHAR *command,
        [in, out] WORD *command_len,
        [out] QWORD *script_time );

    HRESULT AddScript(
        [in] LPCWSTR_WMSDK_TYPE_SAFE type,
        [in] LPCWSTR_WMSDK_TYPE_SAFE command,
        [in] QWORD script_time );

    HRESULT RemoveScript(
        [in] WORD index );
}

[
    object,
    uuid(15cf9781-454e-482e-b393-85fae487a810),
    pointer_default(unique),
    local
]
interface IWMHeaderInfo2 : IWMHeaderInfo
{
    HRESULT GetCodecInfoCount(
        [out] DWORD *codec_infos );

    HRESULT GetCodecInfo(
        [in] DWORD   index,
        [in, out] WORD *name_len,
        [out, size_is( *name_len )] WCHAR *name,
        [in, out] WORD *description_len,
        [out, size_is( *description_len )] WCHAR *description,
        [out] WMT_CODEC_INFO_TYPE *codec_type,
        [in, out] WORD *codec_info_cnt,
        [out, size_is( *codec_info_cnt )] BYTE *codec_info );
}

[
    object,
    uuid(15cc68e3-27cc-4ecd-b222-3f5d02d80bd5),
    pointer_default(unique),
    local
]
interface IWMHeaderInfo3 : IWMHeaderInfo2
{
    HRESULT GetAttributeCountEx(
        [in] WORD stream_num,
        [out] WORD *attributes );

    HRESULT GetAttributeIndices(
        [in] WORD stream_num,
        [in] LPCWSTR name,
        [in] WORD *lang_index,
        [out, size_is( *count )] WORD *indices,
        [in, out] WORD *count );

   HRESULT GetAttributeByIndexEx(
        [in] WORD stream_num,
        [in] WORD index,
        [out, size_is( *name_len )] LPWSTR name,
        [in, out] WORD *name_len,
        [out] WMT_ATTR_DATATYPE *type,
        [out] WORD *lang_index,
        [out, size_is( *data_len )] BYTE *value,
        [in, out] DWORD *data_len );

    HRESULT ModifyAttribute(
        [in] WORD stream_num,
        [in] WORD index,
        [in] WMT_ATTR_DATATYPE type,
        [in] WORD lang_index,
        [in, size_is( length )] const BYTE *value,
        [in] DWORD length );

    HRESULT AddAttribute(
        [in] WORD stream_num,
        [in] LPCWSTR name,
        [out] WORD *index,
        [in] WMT_ATTR_DATATYPE type,
        [in] WORD lang_index,
        [in, size_is( length )] const BYTE *value,
        [in] DWORD length );

    HRESULT DeleteAttribute(
        [in] WORD stream_num,
        [in] WORD index );

    HRESULT AddCodecInfo(
        [in] LPCWSTR_WMSDK_TYPE_SAFE name,
        [in] LPCWSTR_WMSDK_TYPE_SAFE description,
        [in] WMT_CODEC_INFO_TYPE codec_type,
        [in] WORD codec_info_cnt,
        [in, size_is( codec_info_cnt )] BYTE *codec_info );
}

[
    object,
    uuid(96406bec-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]
interface IWMReaderNetworkConfig : IUnknown
{
    HRESULT GetBufferingTime([out] QWORD *buffering_time);
    HRESULT SetBufferingTime([in] QWORD buffering_time);

    HRESULT GetUDPPortRanges(
        [out, size_is( *ranges )] WM_PORT_NUMBER_RANGE *array,
        [in, out] DWORD *ranges);

    HRESULT SetUDPPortRanges(
        [in, size_is( ranges )] WM_PORT_NUMBER_RANGE *array,
        [in] DWORD ranges);

    HRESULT GetProxySettings(
        [in] const WCHAR *protocol,
        [out] WMT_PROXY_SETTINGS *proxy);

    HRESULT SetProxySettings(
        [in] LPCWSTR protocol,
        [in] WMT_PROXY_SETTINGS proxy);

    HRESULT GetProxyHostName(
        [in] const WCHAR *protocol,
        [out, size_is( *size )] WCHAR *hostname,
        [in, out] DWORD *size);

    HRESULT SetProxyHostName(
        [in] const WCHAR *protocol,
        [in] const WCHAR *hostname);

    HRESULT GetProxyPort(
        [in] const WCHAR *protocol,
        [out] DWORD *port);

    HRESULT SetProxyPort(
        [in] const WCHAR *protocol,
        [in] DWORD port);

    HRESULT GetProxyExceptionList(
        [in] const WCHAR *protocol,
        [out, size_is( *count )] WCHAR *exceptions,
        [in, out] DWORD *count);

    HRESULT SetProxyExceptionList(
        [in] const WCHAR *protocol,
        [in] const WCHAR *exceptions);

    HRESULT GetProxyBypassForLocal(
        [in] const WCHAR *protocol,
        [out] BOOL *bypass);

    HRESULT SetProxyBypassForLocal(
        [in] const WCHAR *protocol,
        [in] BOOL bypass);

    HRESULT GetForceRerunAutoProxyDetection([out] BOOL *detection);
    HRESULT SetForceRerunAutoProxyDetection([in] BOOL detection);

    HRESULT GetEnableMulticast([out] BOOL *multicast);
    HRESULT SetEnableMulticast([in] BOOL multicast);

    HRESULT GetEnableHTTP([out] BOOL *enable);
    HRESULT SetEnableHTTP([in] BOOL enable);

    HRESULT GetEnableUDP([out] BOOL *enable);
    HRESULT SetEnableUDP([in] BOOL enable);

    HRESULT GetEnableTCP([out] BOOL *enable);
    HRESULT SetEnableTCP([in] BOOL enable);

    HRESULT ResetProtocolRollover();

    HRESULT GetConnectionBandwidth([out] DWORD *bandwidth);
    HRESULT SetConnectionBandwidth([in] DWORD bandwidth);

    HRESULT GetNumProtocolsSupported([out] DWORD *protocols);

    HRESULT GetSupportedProtocolName(
        [in]  DWORD protocol_num,
        [out, size_is( *size )] WCHAR *protocol,
        [in, out] DWORD *size);

    HRESULT AddLoggingUrl([in] const WCHAR *url);

    HRESULT GetLoggingUrl(
        [in] DWORD index,
        [out, size_is( *size )] WCHAR *url,
        [in, out] DWORD *size);

    HRESULT GetLoggingUrlCount([out] DWORD *count);

    HRESULT ResetLoggingUrlList();
}

[
    object,
    uuid(d979a853-042b-4050-8387-c939db22013f),
    pointer_default(unique),
    local
]
interface IWMReaderNetworkConfig2 : IWMReaderNetworkConfig
{
    HRESULT GetEnableContentCaching([out] BOOL *enable);
    HRESULT SetEnableContentCaching([in] BOOL enable);

    HRESULT GetEnableFastCache([out] BOOL *enable);
    HRESULT SetEnableFastCache([in] BOOL enable);

    HRESULT GetAcceleratedStreamingDuration([out] QWORD *duration);
    HRESULT SetAcceleratedStreamingDuration([in] QWORD duration);

    HRESULT GetAutoReconnectLimit([out] DWORD *limit);
    HRESULT SetAutoReconnectLimit([in] DWORD limit);

    HRESULT GetEnableResends([out] BOOL *enable);
    HRESULT SetEnableResends([in] BOOL enable);

    HRESULT GetEnableThinning([out] BOOL *enable);
    HRESULT SetEnableThinning([in] BOOL enable);

    HRESULT GetMaxNetPacketSize([out] DWORD *packet_size);
}

[
    object,
    uuid(96406bed-2b2b-11d3-b36b-00c04f6108ff),
    pointer_default(unique),
    local
]

interface IWMReaderStreamClock : IUnknown
{
    HRESULT GetTime([in] QWORD *now);

    HRESULT SetTimer([in] QWORD when,
                     [in] void *param,
                     [out] DWORD *id);

    HRESULT KillTimer([in] DWORD id);
}

[
    object,
    uuid(cdfb97ab-188f-40b3-b643-5b7903975c59),
    pointer_default(unique),
    local
]
interface IWMPacketSize : IUnknown
{
    HRESULT GetMaxPacketSize([out] DWORD *size);
    HRESULT SetMaxPacketSize([in] DWORD size);
}

[
    object,
    uuid(8bfc2b9e-b646-4233-a877-1c6a079669dc),
    pointer_default(unique),
    local
]
interface IWMPacketSize2 : IWMPacketSize
{
    HRESULT GetMinPacketSize([out] DWORD *size);
    HRESULT SetMinPacketSize([in] DWORD size);
}

[
    object,
    uuid(d2827540-3ee7-432c-b14c-dc17f085d3b3),
    pointer_default(unique),
    local
]
interface IWMDRMReader : IUnknown
{
    HRESULT AcquireLicense([in] DWORD flags);
    HRESULT CancelLicenseAcquisition();

    HRESULT Individualize([in] DWORD flags);
    HRESULT CancelIndividualization();

    HRESULT MonitorLicenseAcquisition();
    HRESULT CancelMonitorLicenseAcquisition();

    HRESULT SetDRMProperty(
        [in] const WCHAR *name,
        [in] WMT_ATTR_DATATYPE type,
        [in, size_is( length )] const BYTE *value,
        [in] WORD length);

    HRESULT GetDRMProperty(
        [in] const WCHAR *name,
        [out] WMT_ATTR_DATATYPE *type,
        [out, size_is( *length )] BYTE *value,
        [in, out] WORD *length);
}

[
    object,
    uuid(befe7a75-9f1d-4075-b9d9-a3c37bda49a0),
    pointer_default(unique),
    local
]
interface IWMDRMReader2 : IWMDRMReader
{
    HRESULT SetEvaluateOutputLevelLicenses([in] BOOL evaluate);
    HRESULT GetPlayOutputLevels(
        [out, size_is( *length )] DRM_PLAY_OPL *play,
        [in, out] DWORD *length,
        [out] DWORD *level);

    HRESULT GetCopyOutputLevels(
        [out, size_is( *length )] DRM_COPY_OPL *copy,
        [in, out] DWORD *length,
        [out] DWORD *level);

    HRESULT TryNextLicense();
}

[
    object,
    uuid(e08672de-f1e7-4ff4-a0a3-fc4b08e4caf8),
    pointer_default(unique),
    local
]
interface IWMDRMReader3 : IWMDRMReader2
{
    HRESULT GetInclusionList(
        [out] GUID **guids,
        [out] DWORD *count);
}

[
    object,
    uuid(bddc4d08-944d-4d52-a612-46c3fda07dd4),
    pointer_default( unique ),
    local
]
interface IWMReaderAccelerator : IUnknown
{
    HRESULT GetCodecInterface(
        [in] DWORD output,
        [in] REFIID riid,
        [out] void **codec);

    HRESULT Notify(
        [in] DWORD output,
        [in] WM_MEDIA_TYPE *subtype);
}

[
    object,
    uuid(f369e2f0-e081-4fe6-8450-b810b2f410d1),
    pointer_default(unique),
    local
]
interface IWMReaderTimecode : IUnknown
{
    HRESULT GetTimecodeRangeCount(
        [in] WORD num,
        [out] WORD *count);

    HRESULT GetTimecodeRangeBounds(
        [in] WORD stream,
        [in] WORD range,
        [out] DWORD *start_timecode,
        [out] DWORD *end_timecode);
}

[
    object,
    uuid(fdbe5592-81a1-41ea-93bd-735cad1adc05),
    pointer_default(unique),
    local
]
interface IWMReaderTypeNegotiation : IUnknown
{
    HRESULT TryOutputProps(
        [in] DWORD output,
        [in] IWMOutputMediaProps *props);
}

[
    object,
    uuid(df683f00-2d49-4d8e-92b7-fb19f6a0dc57),
    pointer_default(unique),
    local
]
interface IWMLanguageList : IUnknown
{
    HRESULT GetLanguageCount(
        [out] WORD *count);

    HRESULT GetLanguageDetails(
        [in] WORD index,
        [out, size_is( *length )] WCHAR *language,
        [in, out] WORD *length);

    HRESULT AddLanguageByRFC1766String(
        [in] LPCWSTR_WMSDK_TYPE_SAFE language,
        [out] WORD *index);
}

[
    object,
    uuid(f28c0300-9baa-4477-a846-1744d9cbf533),
    pointer_default(unique),
    local
]
interface IWMReaderPlaylistBurn : IUnknown
{
    HRESULT InitPlaylistBurn(
        [in] DWORD count,
        [in] LPCWSTR_WMSDK_TYPE_SAFE *filenames,
        [in] IWMStatusCallback *callback,
        [in] void *context);

    HRESULT GetInitResults(
        [in] DWORD count,
        [out] HRESULT *stat);

    HRESULT Cancel();

    HRESULT EndPlaylistBurn([in] HRESULT result);
}

[
    object,
    uuid(72995a79-5090-42a4-9c8c-d9d0b6d34be5),
    pointer_default(unique),
    local
]
interface IWMPropertyVault : IUnknown
{
    HRESULT GetPropertyCount([in] DWORD *count);

    HRESULT GetPropertyByName(
        [in] const WCHAR *name,
        [out] WMT_ATTR_DATATYPE *type,
        [out, size_is( *size )] BYTE *value,
        [in, out] DWORD *size);

    HRESULT SetProperty(
        [in] const WCHAR *name,
        [in] WMT_ATTR_DATATYPE type,
        [in] BYTE *value,
        [in] DWORD size);

    HRESULT GetPropertyByIndex(
        [in] DWORD index,
        [out, size_is( *pdwNameLen )] WCHAR *name,
        [in, out]  DWORD *length,
        [out] WMT_ATTR_DATATYPE *type,
        [out, size_is( *size )] BYTE *value,
        [in, out] DWORD *size);

    HRESULT CopyPropertiesFrom([in] IWMPropertyVault *vault);

    HRESULT Clear();
};

[
    object,
    uuid(fc54a285-38c4-45b5-aa23-85b9f7cb424b),
    pointer_default(unique),
    local
]
interface IWMWriterPreprocess : IUnknown
{
    HRESULT GetMaxPreprocessingPasses(
        [in] DWORD input,
        [in] DWORD flags,
        [out] DWORD *passes);

    HRESULT SetNumPreprocessingPasses(
        [in] DWORD input,
        [in] DWORD flags,
        [in] DWORD passes);

    HRESULT BeginPreprocessingPass(
        [in] DWORD input,
        [in] DWORD flags);

    HRESULT PreprocessSample(
        [in] DWORD input,
        [in] QWORD sample_time,
        [in] DWORD flags,
        [in] INSSBuffer *sample);

    HRESULT EndPreprocessingPass(
        [in] DWORD input,
        [in] DWORD flags);
};

[
    object,
    uuid(05e5ac9f-3fb6-4508-bb43-a4067ba1ebe8),
    pointer_default(unique),
    local
]
interface IWMLicenseBackup : IUnknown
{
    HRESULT BackupLicenses(
        [in] DWORD dwFlags,
        [in] IWMStatusCallback *pCallback);

    HRESULT CancelLicenseBackup();
}

[
    object,
    uuid(c70b6334-a22e-4efb-a245-15e65a004a13),
    pointer_default(unique),
    local
]
interface IWMLicenseRestore : IUnknown
{
    HRESULT RestoreLicenses(
        [in] DWORD dwFlags,
        [in] IWMStatusCallback *pCallback);

    HRESULT CancelLicenseRestore();
}

[
    object,
    local,
    uuid(6816dad3-2b4b-4c8e-8149-874c3483a753),
]
interface IWMIStreamProps : IUnknown
{
    HRESULT GetProperty(const WCHAR *name, WMT_ATTR_DATATYPE *type, BYTE *value, DWORD *size);
}

[
    object,
    local,
    uuid(342e0eb7-e651-450c-975b-2ace2c90c48e),
]
interface IWMCredentialCallback : IUnknown
{
    HRESULT AcquireCredentials(WCHAR *realm, WCHAR *site, WCHAR *username, DWORD username_size,
            WCHAR *password, DWORD password_size, HRESULT hr, DWORD *flags);
}

cpp_quote("HRESULT WINAPI WMCheckURLExtension(LPCWSTR);")
cpp_quote("HRESULT WINAPI WMCheckURLScheme(LPCWSTR);")
cpp_quote("HRESULT WINAPI WMCreateWriter(IUnknown*,IWMWriter**);")
cpp_quote("HRESULT WINAPI WMCreateReader(IUnknown*,DWORD,IWMReader**);")
cpp_quote("HRESULT WINAPI WMCreateSyncReader(IUnknown*,DWORD,IWMSyncReader**);")
cpp_quote("HRESULT WINAPI WMCreateEditor(IWMMetadataEditor**);")
cpp_quote("HRESULT WINAPI WMCreateBackupRestorer(IUnknown*,IWMLicenseBackup**);")
cpp_quote("HRESULT WINAPI WMCreateProfileManager(IWMProfileManager**);")
cpp_quote("HRESULT WINAPI WMIsContentProtected(const WCHAR*, WINBOOL*);")


cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_Base,   0x00000000,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIATYPE_Video,     0x73646976,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_RGB1,   0xe436eb78,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_RGB4,   0xe436eb79,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_RGB8,   0xe436eb7a,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_RGB565, 0xe436eb7b,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_RGB555, 0xe436eb7c,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_RGB24,  0xe436eb7d,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_RGB32,  0xe436eb7e,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_I420,   0x30323449,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_IYUV,   0x56555949,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_YV12,   0x32315659,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_YUY2,   0x32595559,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_P422,   0x32323450,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_UYVY,   0x59565955,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_YVYU,   0x55595659,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_YVU9,   0x39555659,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_VIDEOIMAGE, 0x1d4a45f2,0xe5f6,0x4b44,0x83,0x88,0xf0,0xae,0x5c,0x0e,0x0c,0x37);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_MP43,   0x3334504d,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_MP4S,   0x5334504d,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_M4S2,   0x3253344d,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_WMV1,   0x31564d57,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_WMV2,   0x32564d57,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_MSS1,   0x3153534d,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_MPEG2_VIDEO, 0xe06d8026,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea);")
cpp_quote("EXTERN_GUID(WMMEDIATYPE_Audio,     0x73647561,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_PCM,    0x00000001,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_DRM,    0x00000009,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_WMAudioV9,        0x00000162,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_WMAudio_Lossless, 0x00000163,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_MSS2,   0x3253534d,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_WMSP1,  0x0000000a,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_WMSP2,  0x0000000b,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_WMV3,   0x33564d57,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_WMVP,   0x50564d57,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_WVP2,   0x32505657,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_WMVA,   0x41564d57,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
cpp_quote("EXTERN_GUID(WMMEDIASUBTYPE_WVC1,   0x31435657,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);")
