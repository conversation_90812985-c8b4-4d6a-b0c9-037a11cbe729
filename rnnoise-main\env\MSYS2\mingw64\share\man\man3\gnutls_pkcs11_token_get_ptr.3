.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_token_get_ptr" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_token_get_ptr \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_token_get_ptr(const char * " url ", void ** " ptr ", unsigned long * " slot_id ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const char * url" 12
should contain a PKCS\fB11\fP URL identifying a token
.IP "void ** ptr" 12
will contain the CK_FUNCTION_LIST_PTR pointer
.IP "unsigned long * slot_id" 12
will contain the slot_id (may be \fBNULL\fP)
.IP "unsigned int flags" 12
should be zero
.SH "DESCRIPTION"
This function will return the function pointer of the specified
token by the URL. The returned pointers are valid until
gnutls is deinitialized, c.f. \fB_global_deinit()\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success or a negative error code
on error.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
