.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_privkey_get_pk_algorithm" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_privkey_get_pk_algorithm \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_privkey_get_pk_algorithm(gnutls_pkcs11_privkey_t " key ", unsigned int * " bits ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_privkey_t key" 12
should contain a \fBgnutls_pkcs11_privkey_t\fP type
.IP "unsigned int * bits" 12
if bits is non null it will hold the size of the parameters' in bits
.SH "DESCRIPTION"
This function will return the public key algorithm of a private
key.
.SH "RETURNS"
a member of the \fBgnutls_pk_algorithm_t\fP enumeration on
success, or a negative error code on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
