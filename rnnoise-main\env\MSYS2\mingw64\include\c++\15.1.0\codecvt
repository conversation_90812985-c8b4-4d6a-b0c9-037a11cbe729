// <codecvt> -*- C++ -*-

// Copyright (C) 2015-2025 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

// ISO C++ 14882: 22.5  Standard code conversion facets

/** @file include/codecvt
 *  This is a Standard C++ Library header.
 */

#ifndef _GLIBCXX_CODECVT
#define _GLIBCXX_CODECVT 1

#ifdef _GLIBCXX_SYSHDR
#pragma GCC system_header
#endif

#if __cplusplus < 201103L
# include <bits/c++0x_warning.h>
#else

#include <bits/locale_classes.h>
#include <bits/codecvt.h>

namespace std _GLIBCXX_VISIBILITY(default)
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

  enum codecvt_mode
  {
    consume_header = 4,
    generate_header = 2,
    little_endian = 1
  };

  template<typename _Elem, unsigned long _Maxcode = 0x10ffff,
	   codecvt_mode _Mode = (codecvt_mode)0>
    class codecvt_utf8 : public codecvt<_Elem, char, mbstate_t>
    {
    public:
      explicit
      codecvt_utf8(size_t __refs = 0);

      ~codecvt_utf8();
    };

  template<typename _Elem, unsigned long _Maxcode = 0x10ffff,
	   codecvt_mode _Mode = (codecvt_mode)0>
    class codecvt_utf16 : public codecvt<_Elem, char, mbstate_t>
    {
    public:
      explicit
      codecvt_utf16(size_t __refs = 0);

      ~codecvt_utf16();
    };

  template<typename _Elem, unsigned long _Maxcode = 0x10ffff,
	   codecvt_mode _Mode = (codecvt_mode)0>
    class codecvt_utf8_utf16 : public codecvt<_Elem, char, mbstate_t>
    {
    public:
      explicit
      codecvt_utf8_utf16(size_t __refs = 0);

      ~codecvt_utf8_utf16();
    };

#define _GLIBCXX_CODECVT_SPECIALIZATION2(_NAME, _ELEM) \
  template<> \
    class _NAME<_ELEM> \
    : public codecvt<_ELEM, char, mbstate_t> \
    { \
    public: \
      typedef _ELEM			intern_type; \
      typedef char			extern_type; \
      typedef mbstate_t			state_type; \
 \
    protected: \
      _NAME(unsigned long __maxcode, codecvt_mode __mode, size_t __refs) \
      : codecvt(__refs), _M_maxcode(__maxcode), _M_mode(__mode) { } \
 \
      virtual \
      ~_NAME(); \
 \
      virtual result \
      do_out(state_type& __state, const intern_type* __from, \
	     const intern_type* __from_end, const intern_type*& __from_next, \
	     extern_type* __to, extern_type* __to_end, \
	     extern_type*& __to_next) const; \
 \
      virtual result \
      do_unshift(state_type& __state, \
		 extern_type* __to, extern_type* __to_end, \
		 extern_type*& __to_next) const; \
 \
      virtual result \
      do_in(state_type& __state, \
	     const extern_type* __from, const extern_type* __from_end, \
	     const extern_type*& __from_next, \
	     intern_type* __to, intern_type* __to_end, \
	     intern_type*& __to_next) const; \
 \
      virtual \
      int do_encoding() const throw(); \
 \
      virtual \
      bool do_always_noconv() const throw(); \
 \
      virtual \
      int do_length(state_type&, const extern_type* __from, \
		    const extern_type* __end, size_t __max) const; \
 \
      virtual int \
      do_max_length() const throw(); \
 \
    private: \
      unsigned long	_M_maxcode; \
      codecvt_mode	_M_mode; \
    }

#define _GLIBCXX_CODECVT_SPECIALIZATION(_NAME, _ELEM) \
  _GLIBCXX_CODECVT_SPECIALIZATION2(__ ## _NAME ## _base, _ELEM); \
  template<unsigned long _Maxcode, codecvt_mode _Mode> \
    class _NAME<_ELEM, _Maxcode, _Mode> \
    : public __ ## _NAME ## _base<_ELEM> \
    { \
    public: \
      explicit \
      _NAME(size_t __refs = 0) \
      : __ ## _NAME ## _base<_ELEM>(std::min(_Maxcode, 0x10fffful), \
				    _Mode, __refs) \
      { } \
    }

  template<typename _Elem> class __codecvt_utf8_base;
  template<typename _Elem> class __codecvt_utf16_base;
  template<typename _Elem> class __codecvt_utf8_utf16_base;

  _GLIBCXX_CODECVT_SPECIALIZATION(codecvt_utf8, char16_t);
  _GLIBCXX_CODECVT_SPECIALIZATION(codecvt_utf16, char16_t);
  _GLIBCXX_CODECVT_SPECIALIZATION(codecvt_utf8_utf16, char16_t);

  _GLIBCXX_CODECVT_SPECIALIZATION(codecvt_utf8, char32_t);
  _GLIBCXX_CODECVT_SPECIALIZATION(codecvt_utf16, char32_t);
  _GLIBCXX_CODECVT_SPECIALIZATION(codecvt_utf8_utf16, char32_t);

#ifdef _GLIBCXX_USE_WCHAR_T
  _GLIBCXX_CODECVT_SPECIALIZATION(codecvt_utf8, wchar_t);
  _GLIBCXX_CODECVT_SPECIALIZATION(codecvt_utf16, wchar_t);
  _GLIBCXX_CODECVT_SPECIALIZATION(codecvt_utf8_utf16, wchar_t);
#endif

_GLIBCXX_END_NAMESPACE_VERSION
} // namespace

#endif // C++11

#endif /* _GLIBCXX_CODECVT */
