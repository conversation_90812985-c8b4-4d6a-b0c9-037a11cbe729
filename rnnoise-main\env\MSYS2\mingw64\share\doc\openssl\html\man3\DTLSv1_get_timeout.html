<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>DTLSv1_get_timeout</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>DTLSv1_get_timeout - determine when a DTLS or QUIC SSL object next needs a timeout event to be handled</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

int DTLSv1_get_timeout(SSL *s, struct timeval *tv);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>DTLSv1_get_timeout() can be used on a DTLS or QUIC SSL object to determine when the SSL object next needs to perform internal processing due to the passage of time.</p>

<p>Calling DTLSv1_get_timeout() results in <i>*tv</i> being written with an amount of time left before the SSL object needs have DTLSv1_handle_timeout() called on it. If the SSL object needs to be ticked immediately, <i>*tv</i> is zeroed and the function succeeds, returning 1. If no timeout is currently active, this function returns 0.</p>

<p>This function is only applicable to DTLS and QUIC objects. It fails if called on any other kind of SSL object.</p>

<p>Note that the value output by a call to DTLSv1_get_timeout() may change as a result of other calls to the SSL object.</p>

<p>Once the timeout expires, DTLSv1_handle_timeout() should be called to handle any internal processing which is due; for more information, see <a href="../man3/DTLSv1_handle_timeout.html">DTLSv1_handle_timeout(3)</a>.</p>

<p><a href="../man3/SSL_get_event_timeout.html">SSL_get_event_timeout(3)</a> supersedes all use cases for this this function and may be used instead of it.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>On success, writes a duration to <i>*tv</i> and returns 1.</p>

<p>Returns 0 on failure, or if no timeout is currently active.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/DTLSv1_handle_timeout.html">DTLSv1_handle_timeout(3)</a>, <a href="../man3/SSL_get_event_timeout.html">SSL_get_event_timeout(3)</a>, <a href="../man7/ssl.html">ssl(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


