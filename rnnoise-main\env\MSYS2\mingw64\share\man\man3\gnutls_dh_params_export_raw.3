.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dh_params_export_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dh_params_export_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_dh_params_export_raw(gnutls_dh_params_t " params ", gnutls_datum_t * " prime ", gnutls_datum_t * " generator ", unsigned int * " bits ");"
.SH ARGUMENTS
.IP "gnutls_dh_params_t params" 12
Holds the DH parameters
.IP "gnutls_datum_t * prime" 12
will hold the new prime
.IP "gnutls_datum_t * generator" 12
will hold the new generator
.IP "unsigned int * bits" 12
if non null will hold the secret key's number of bits
.SH "DESCRIPTION"
This function will export the pair of prime and generator for use
in the <PERSON><PERSON><PERSON>\-<PERSON><PERSON> key exchange.  The new parameters will be
allocated using \fBgnutls_malloc()\fP and will be stored in the
appropriate datum.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
