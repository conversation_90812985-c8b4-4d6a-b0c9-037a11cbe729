/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#undef IOCPARM_MASK
#undef IOC_VOID
#undef IOC_OUT
#undef IOC_IN
#undef IOC_INOUT

#undef _IO
#undef _IOR
#undef _IOW

#undef FIONREAD
#undef FIONBIO
#undef FIOASYNC

#undef SIOCSHIWAT
#undef SIOCGHIWAT
#undef SIOCSLOWAT
#undef SIOCGLOWAT
#undef SIOCATMARK

#undef IPPROTO_IP
#undef IPPROTO_ICMP
#undef IPPROTO_IGMP
#undef IPPROTO_GGP
#undef IPPROTO_TCP
#undef IPPROTO_PUP
#undef IPPROTO_UDP
#undef IPPROTO_IDP
#undef IPPROTO_ND

#undef IPPROTO_RAW
#undef IPPROTO_MAX

#undef IPPORT_ECHO
#undef IPPORT_DISCARD
#undef IPPORT_SYSTAT
#undef IPPORT_DAYTIME
#undef IPPORT_NETSTAT
#undef IPPORT_FTP
#undef IPPORT_TELNET
#undef IPPORT_SMTP
#undef IPPORT_TIMESERVER
#undef IPPORT_NAMESERVER
#undef IPPORT_WHOIS
#undef IPPORT_MTP

#undef IPPORT_TFTP
#undef IPPORT_RJE
#undef IPPORT_FINGER
#undef IPPORT_TTYLINK
#undef IPPORT_SUPDUP

#undef IPPORT_EXECSERVER
#undef IPPORT_LOGINSERVER
#undef IPPORT_CMDSERVER
#undef IPPORT_EFSSERVER

#undef IPPORT_BIFFUDP
#undef IPPORT_WHOSERVER
#undef IPPORT_ROUTESERVER

#undef IPPORT_RESERVED

#undef IMPLINK_IP
#undef IMPLINK_LOWEXPER
#undef IMPLINK_HIGHEXPER

#undef IN_CLASSA
#undef IN_CLASSA_NET
#undef IN_CLASSA_NSHIFT
#undef IN_CLASSA_HOST
#undef IN_CLASSA_MAX

#undef IN_CLASSB
#undef IN_CLASSB_NET
#undef IN_CLASSB_NSHIFT
#undef IN_CLASSB_HOST
#undef IN_CLASSB_MAX

#undef IN_CLASSC
#undef IN_CLASSC_NET
#undef IN_CLASSC_NSHIFT
#undef IN_CLASSC_HOST

#undef INADDR_ANY
#undef INADDR_LOOPBACK
#undef INADDR_BROADCAST
#undef INADDR_NONE

#undef IP_OPTIONS
#undef IP_MULTICAST_IF
#undef IP_MULTICAST_TTL
#undef IP_MULTICAST_LOOP
#undef IP_ADD_MEMBERSHIP
#undef IP_DROP_MEMBERSHIP
#undef IP_TTL
#undef IP_TOS
#undef IP_DONTFRAGMENT

#undef IP_DEFAULT_MULTICAST_TTL
#undef IP_DEFAULT_MULTICAST_LOOP
#undef IP_MAX_MEMBERSHIPS

#undef SOCK_STREAM
#undef SOCK_DGRAM
#undef SOCK_RAW
#undef SOCK_RDM
#undef SOCK_SEQPACKET

#undef SO_DEBUG
#undef SO_ACCEPTCONN
#undef SO_REUSEADDR
#undef SO_KEEPALIVE
#undef SO_DONTROUTE
#undef SO_BROADCAST
#undef SO_USELOOPBACK
#undef SO_LINGER
#undef SO_OOBINLINE

#undef SO_DONTLINGER

#undef SO_SNDBUF
#undef SO_RCVBUF
#undef SO_SNDLOWAT
#undef SO_RCVLOWAT
#undef SO_SNDTIMEO
#undef SO_RCVTIMEO
#undef SO_ERROR
#undef SO_TYPE

#undef SO_CONNDATA
#undef SO_CONNOPT
#undef SO_DISCDATA
#undef SO_DISCOPT
#undef SO_CONNDATALEN
#undef SO_CONNOPTLEN
#undef SO_DISCDATALEN
#undef SO_DISCOPTLEN

#undef SO_OPENTYPE

#undef SO_SYNCHRONOUS_ALERT
#undef SO_SYNCHRONOUS_NONALERT

#undef SO_MAXDG
#undef SO_MAXPATHDG
#undef SO_UPDATE_ACCEPT_CONTEXT
#undef SO_CONNECT_TIME

#undef TCP_NODELAY
#undef TCP_BSDURGENT

#undef AF_UNSPEC
#undef AF_UNIX
#undef AF_INET
#undef AF_IMPLINK
#undef AF_PUP
#undef AF_CHAOS
#undef AF_IPX
#undef AF_NS
#undef AF_ISO
#undef AF_OSI
#undef AF_ECMA
#undef AF_DATAKIT
#undef AF_CCITT
#undef AF_SNA
#undef AF_DECnet
#undef AF_DLI
#undef AF_LAT
#undef AF_HYLINK
#undef AF_APPLETALK
#undef AF_NETBIOS
#undef AF_VOICEVIEW
#undef AF_FIREFOX
#undef AF_UNKNOWN1
#undef AF_BAN

#undef AF_MAX

#undef PF_UNSPEC
#undef PF_UNIX
#undef PF_INET
#undef PF_IMPLINK
#undef PF_PUP
#undef PF_CHAOS
#undef PF_NS
#undef PF_IPX
#undef PF_ISO
#undef PF_OSI
#undef PF_ECMA
#undef PF_DATAKIT
#undef PF_CCITT
#undef PF_SNA
#undef PF_DECnet
#undef PF_DLI
#undef PF_LAT
#undef PF_HYLINK
#undef PF_APPLETALK
#undef PF_VOICEVIEW
#undef PF_FIREFOX
#undef PF_UNKNOWN1
#undef PF_BAN

#undef PF_MAX

#undef SOL_SOCKET

#undef SOMAXCONN

#undef MSG_OOB
#undef MSG_PEEK
#undef MSG_DONTROUTE

#undef MSG_MAXIOVLEN

#undef MSG_PARTIAL

#undef MAXGETHOSTSTRUCT

#undef FD_READ
#undef FD_WRITE
#undef FD_OOB
#undef FD_ACCEPT
#undef FD_CONNECT
#undef FD_CLOSE

#undef TF_DISCONNECT
#undef TF_REUSE_SOCKET
#undef TF_WRITE_BEHIND

#undef WSAMAKEASYNCREPLY
#undef WSAMAKESELECTREPLY
#undef WSAGETASYNCBUFLEN
#undef WSAGETASYNCERROR
#undef WSAGETSELECTEVENT
#undef WSAGETSELECTERROR

