.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_oid_to_digest" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_oid_to_digest \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_digest_algorithm_t gnutls_oid_to_digest(const char * " oid ");"
.SH ARGUMENTS
.IP "const char * oid" 12
is an object identifier
.SH "DESCRIPTION"
Converts a textual object identifier to a \fBgnutls_digest_algorithm_t\fP value.
.SH "RETURNS"
a \fBgnutls_digest_algorithm_t\fP id of the specified digest
algorithm, or \fBGNUTLS_DIG_UNKNOWN\fP on failure.
.SH "SINCE"
3.4.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
