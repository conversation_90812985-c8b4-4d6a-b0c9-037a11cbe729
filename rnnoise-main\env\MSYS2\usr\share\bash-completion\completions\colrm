_colrm_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="--version --help"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	case $COMP_CWORD in
		1)
			COMPREPLY=( $(compgen -W "startcol" -- $cur) )
			;;
		2)
			COMPREPLY=( $(compgen -W "endcol" -- $cur) )
			;;
	esac
	return 0
}
complete -F _colrm_module colrm
