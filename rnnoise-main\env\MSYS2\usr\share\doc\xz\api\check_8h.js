var check_8h =
[
    [ "LZMA_CHECK_ID_MAX", "check_8h.html#acd221ababe30230d9647aab469ad80cb", null ],
    [ "LZMA_CHECK_SIZE_MAX", "check_8h.html#a379e931cf86351ab1d97896cda9abbe0", null ],
    [ "lzma_check", "check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f", [
      [ "LZMA_CHECK_NONE", "check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3fa7b9851d75abfabc08d7fc5b4aaeb6f20", null ],
      [ "LZMA_CHECK_CRC32", "check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3fa0be65014a40b5cb4ab32252b3709bef7", null ],
      [ "LZMA_CHECK_CRC64", "check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3fa87b4b0697a1e1ccb6766dd5c2fa24afc", null ],
      [ "LZMA_CHECK_SHA256", "check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3faf26a55ddd204a50ae87ec3432e7bc309", null ]
    ] ],
    [ "lzma_check_is_supported", "check_8h.html#ae9391ed2acfad0ce9357b68c608f07d8", null ],
    [ "lzma_check_size", "check_8h.html#afd3fda19575d9d4f864c626c02b7cb48", null ],
    [ "lzma_crc32", "check_8h.html#a760b569cce91bdd01e4ce9d78823c96d", null ],
    [ "lzma_crc64", "check_8h.html#aff2e74ce671b9f82a96adb549c68cea2", null ],
    [ "lzma_get_check", "check_8h.html#a8d7c3ffabfd024485f03fa209536c746", null ]
];