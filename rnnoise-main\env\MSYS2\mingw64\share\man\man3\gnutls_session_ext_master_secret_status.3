.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_ext_master_secret_status" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_ext_master_secret_status \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned gnutls_session_ext_master_secret_status(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
Get the status of the extended master secret extension negotiation.
This is in accordance to RFC7627. That information is also
available to the more generic \fBgnutls_session_get_flags()\fP.
.SH "RETURNS"
Non\-zero if the negotiation was successful or zero otherwise.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
