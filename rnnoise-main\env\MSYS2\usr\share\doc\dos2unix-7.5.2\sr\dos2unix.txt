НАЗИВ
    dos2unix – претварач формата текстуалних датотека из ДОС/Мек-а у Јуникс
    и обратно

УВОД
        dos2unix [опције] [ДАТОТЕКА ...] [-n УЛАЗНА_ДАТОТЕКА ИЗЛАЗНА_ДАТОТЕКА ...]
        unix2dos [опције] [ДАТОТЕКА ...] [-n УЛАЗНА_ДАТОТЕКА ИЗЛАЗНА_ДАТОТЕКА ...]

ОПИС
    “dos2unix“ пакет укључује помагала „"dos2unix"“ и „"unix2dos"“ за
    претварање обичне текстуалне датотеке у ДОС или Мек формат у Јуникс
    формат и обратно.

    У ДОС/Виндоуз текстуалним датотекама преламање реда, такође познато и
    као нови ред, је комбинација два знака: „Carriage Return“ (CR) за којим
    следи „Line Feed“ (LF). У Јуникс текстуалним датотекама преламање реда
    је један знак: „Line Feed“ (LF). У Мек текстуалним датотекама, пре Мек
    ОС Икс-а, преламање реда беше један знак „Carriage Return“ (CR). Данас
    Мек ОС користи преламање реда у Јуникс стилу (LF).

    Поред преламања реда „dos2unix“ такође може претворити кодирање
    датотека. Неколико ДОС кодних страница се може претворити у Јуникс
    Latin-1. А Виндоуз Јуникод (УТФ-16) датотеке се могу претворити у Јуникс
    Јуникод (УТФ-8) датотеке.

    Бинарне датотеке се аутоматски прескачу, осим ако претварање није
    приморано.

    Нередовне датотеке, као што су директоријуму и ПУПИ, се аутоматски
    прескачу.

    Симболичке везе и њихови циљеви се подразумевано не дирају. Симболичке
    везе се по жељи могу заменити, или се излаз може записати на мету
    симболичке везе. Писање на мету симболичке везе није подржано на
    Виндоузу.

    „dos2unix“ је направљен по узору на „dos2unix“ под СанОС/Соларисом.
    Постоји једна важна разлика са изворним СанОС/Соларис издањем. Ово
    издање подразумевано врши претварање у месту (стари режим датотеке), док
    изворно СанОС/Соларис издање подржава само упарено претварање (нови
    режим датотеке). Такође погледајте опције „"-o"“ и „"-n"“. Друга разлика
    је у томе што издање СанОС/Солариса подразумевано користи режим „*iso*“
    претварања док ово издање подразумевано користи режим „*ascii*“
    претварања.

ОПЦИЈЕ
    --  Сматра све следеће опције као називе датотека. Користите ову опцију
        ако желите да претворите датотеке чији називи почињу цртицом. На
        пример да претворите датотеку под називом „-foo“, можете користити
        ову наредбу:

            dos2unix -- -foo

        Или у режим нове датотеке:

            dos2unix -n -- -foo излаз.txt

    --allow-chown
        Сва власништва датотеке се мењају у режим старе датотеке.

        Када се користи ова опција, претварање се неће прекинути када се
        власништво корисника и/или групе изворне датотеке не може сачувати у
        старом режиму датотеке. Претварање ће се наставити и претворена
        датотека ће добити исто ново власништво као да је претворена у
        режиму нове датотеке. Такође погледајте опције „"-o"“ и „"-n"“. Ова
        опција је доступна само ако „dos2unix“ има подршку за очување
        власништва корисника и групе над датотекама.

    -ascii
        Default conversion mode. See also section CONVERSION MODES.

    -iso
        Претварање између ДОС и ИСО-8859-1 скупа знакова. Видите такође
        одељак РЕЖИМИ ПРЕТВАРАЊА.

    -1252
        Користи Виндоуз кодну страницу 1252 (западноевропски).

    -437
        Користи ДОС кодну страницу 437 (Сједињене Државе). Ово је основна
        кодна страница коришћена за ИСО претварање.

    -850
        Користи ДОС кодну страницу 850 (западноевропски).

    -860
        Користи ДОС кодну страницу 860 (португалски).

    -863
        Користи ДОС кодну страницу 863 (француски канадски).

    -865
        Користи ДОС кодну страницу 865 (нордијски).

    -7  Претвара 8 битне знакове у 7 битне размаке.

    -b, --keep-bom
        Задржава Ознаку редоследа бајта (BOM). Када улазна датотека има ОРБ,
        пише ОРБ у излазну датотеку. Ово је основно понашање приликом
        претварања у ДОС преламање реда. Видите такође опцију „"-r"“.

    -c, --convmode РЕЖИМ_ПРЕТВАРАЊА
        Поставља режим претварања. Где је РЕЖИМ_ПРЕТВАРАЊА један од:
        *ascii*, *7bit*, *iso*, *mac* где је „ascii“ основни.

    -D, --display-enc КОДИРАЊЕ
        Поставља кодирање приказаног текста. Где је КОДИРАЊЕ једно од:
        *ansi*, *unicode*, *unicodebom*, *utf8*, *utf8bom* где је „ansi“
        основно.

        Ова опција је доступна само у „dos2unix“ за Виндоуз са подршком за
        Јуникод назив датотеке. Ова опција нема утицаја на стварно читање и
        писање назива датотека, већ само на начин на који се приказују.

        Постоји неколико метода за приказивање текста у Виндоуз конзоли на
        основу кодирања текста. Сви они имају своје предности и недостатке.

        ansi
            Подразумевани метод „dos2unix“-а је да се користи АНСИ кодиран
            текст. Предност је у томе што је повратно сагласан. Ради са
            растерским и „TrueType“ словима. У неким регионима ћете можда
            морати да промените активну ДОС ОЕМ кодну страницу у АНСИ кодну
            страницу Виндоуз система помоћу наредбе „"chcp"“, јер „dos2unix“
            користи кодну страницу Виндоуз система.

            Недостатак анси-ја је што се међународни називи датотека са
            знаковима који нису унутар системски подразумеване кодне
            странице не приказују правилно. Уместо тога видећете знак
            питања, или погрешан симбол. Када не радите са страним називима
            датотека, овај метод је у реду.

        unicode, unicodebom
            Предност јуникод кодирања (Виндоуз назив за УТФ-16) је у томе
            што се текст обично правилно приказује. Нема потребе за променом
            активне кодне странице. Можда ћете морати да поставите слова
            конзоле на „TrueType“ да би се међународни знаци правилно
            приказивали. Када знак није укључен у „TrueType“ слова обично
            видите мали квадрат, понекад са знаком питања у њему.

            Када користите „ConEmu“ конзолу сав текст се исправно приказује,
            јер „ConEmu“ аутоматски бира добра слова.

            Недостатак јуникод-а је што није сагласан са АСКРИ-јем. Излазом
            није лако руковати када га преусмерите на други програм.

            Када се користи „"unicodebom"“ метод Јуникод тексту ће
            претходити ОРБ (Ознака редоследа вбајтоа). ОРБ је потребан за
            правилно преусмеравање или преспајање у „PowerShell“-у.

        utf8, utf8bom
            Предност утф8 је у томе што је сагласан са АСКРИ-јем. Слова
            конзоле треба да поставити на „TrueType“ слова. Са „TrueType“
            словима текст се приказује слично као и са „"unicode"“
            кодирањем.

            Недостатак је што се приликом коришћења подразумеваних
            растерских слова сви не-АСКРИ знакови се погрешно приказују. Не
            само да Јуникод називи датотека, већ и преведене поруке постају
            нечитљиве. У Виндоузу подешеном за регију Источне Азије, можда
            ћете видети пуно треперења конзоле приликом приказивања порука.

            У „ConEmu“ конзоли утф8 метода кодирања ради лепо.

            Када се користи „"utf8bom"“ метод УТФ-8 тексту ће претходити ОРБ
            (Ознака редоследа вбајтоа). ОРБ је потребна за правилно
            преусмеравање или преспајање у „PowerShell“-у.

        Подразумевано кодирање можете изменити променљивом окружења
        „DOS2UNIX_DISPLAY_ENC“ тако што ћете је поставити на „"unicode"“,
        „"unicodebom"“, „"utf8"“, или „"utf8bom"“.

    -e, --add-eol
        Додаје прекид реда у последњи ред ако га нема. Ово ради за сва
        претварања.

        Датотеци претвореној из ДОС у Јуникс формат може да недостаје прекид
        реда у последњем реду. Постоје уређивачи текста који пишу текстуалне
        датотеке без прекида реда у последњем реду. Неки Јуникс програми
        имају проблема са обрадом тих датотека, јер POSIX стандард одређује
        да сваки ред у текстуалној датотеци мора да се завршава крајњим
        знаком новог реда. Јер надовезане датотеке неће давати очекивани
        резултат.

    -f, --force
        Присиљава претварање бинарних датотека.

    -gb, --gb18030
        У Виндоузу УТФ-16 датотеке се подразумевано претварају у УТФ-8, без
        обзира на поставке језика. Користите ову опцију за претварање УТФ-16
        датотека у „GB18030“. Ова опција је доступна само у Виндоузу. Такође
        погледајте одељак „GB18030“.

    -h, --help
        Приказује помоћ и излази.

    -i[ЗАСТАВИЦЕ], --info[=ЗАСТАВИЦЕ] ДАТОТЕКА ...
        Приказује податке о датотеци. Претварање се не ради.

        Следеће информације се исписују, овим редом: број ДОС прелома реда,
        број Јуникс прелома реда, број Мек прелома реда, ознака редоследа
        бајтова, текст или бинарна, назив датотеке.

        Пример излаза:

             6       0       0  no_bom    текст    dos.txt
             0       6       0  no_bom    текст    unix.txt
             0       0       6  no_bom    текст    mac.txt
             6       6       6  no_bom    текст    mixed.txt
            50       0       0  UTF-16LE  текст    utf16le.txt
             0      50       0  no_bom    текст    utf8unix.txt
            50       0       0  UTF-8     текст    utf8dos.txt
             2     418     219  no_bom    бинарна  dos2unix.exe

        Знајте да понекад бинарна датотека може грешком бити узета за
        текстуалну датотеку. Видите такође опцију „"-s"“.

        If in addition option "-e" or "--add-eol" is used also the type of
        the line break of the last line is printed, or "noeol" if there is
        none.

        Пример излаза:

             6       0       0  no_bom    text   dos     dos.txt
             0       6       0  no_bom    text   unix    unix.txt
             0       0       6  no_bom    text   mac     mac.txt
             1       0       0  no_bom    text   noeol   noeol_dos.txt

        Изборне додатне заставице се могу поставити да измене излаз. Могу се
        додати једна или више заставица.

        0   Исписује редове информација о датотеци за којима следи нулти
            знак уместо знака новог реда. Ово омогућава тачно тумачење
            назива датотека с размацима или наводницима када се користи
            заставица „c“. Користите ову заставицу у комбинацији са
            „xargs(1)“ опцијом „-0“ или „"--null"“.

        d   Исписује број ДОС преламања реда.

        u   Исписује број Јуникс преламања реда.

        m   Исписује број Мек преламања реда.

        b   Исписује ознаку редоследа бајтова.

        t   Исписује да ли је датотека текстуална или бинарна.

        e   Print the type of the line break of the last line, or "noeol" if
            there is none.

        c   Исписује само датотеке које ће бити претворене.

            Са заставицом „"c"“ „dos2unix“ ће исписати само датотеке које
            садрже ДОС преламања реда, „unix2dos“ ће исписат само називе
            датотека које имају Јуникс преламања реда.

            If in addition option "-e" or "--add-eol" is used also the files
            that lack a line break on the last line will be printed.

        h   Исписује заглавље.

        p   Приказује називе датотека без путање.

        Примери:

        Приказује податке за све „*.txt“ датотеке:

            dos2unix -i *.txt

        Приказује само број ДОС преламања реда и број Јуникс преламања реда:

            dos2unix -idu *.txt

        Приказује само ознаку редоследа бајтова:

            dos2unix --info=b *.txt

        Исписује датотеке које имају ДОС преламање реда:

            dos2unix -ic *.txt

        Исписује датотеке које имају Јуникс преламање реда:

            unix2dos -ic *.txt

        List the files that have DOS line breaks or lack a line break on the
        last line:

            dos2unix -e -ic *.txt

        Претвара само датотеке које имају ДОС преламање реда и оставља друге
        датотеке нетакнутим:

            dos2unix -ic0 *.txt | xargs -0 dos2unix

        Налази текстуалне датотеке које имају ДОС преламање реда:

            find -name '*.txt' -print0 | xargs -0 dos2unix -ic

    -k, --keepdate
        Задржава печат датума излазне датотеке истим као код улазне.

    -L, --license
        Приказује лиценцу програма.

    -l, --newline
        Додаје додатни нови ред.

        dos2unix: Само ДОС преламања реда се мењају у два Јуникс преламања
        реда. У Мек режиму само Мек преламања реда се мењају у два Јуникс
        преламања реда.

        unix2dos: Само Јуникс преламања реда се мењају у два ДОС преламања
        реда. У Мек режиму Јуникс преламања реда се мењају у два Мек
        преламања реда.

    -m, --add-bom
        Пише Ознаку редоследа бајтова (BOM) у излазну датотеку.
        Подразумевано се записује УТФ-8 ОРБ.

        Када је улазна датотека УТФ-16, и користи се опција „"-u"“, биће
        записана УТФ-16 ОРБ.

        Никада не користите ову опцију када је излазно кодирање другачије од
        „UTF-8“, „UTF-16“, или „GB18030“. Видите такође одељак ЈУНИКОД.

    -n, --newfile УЛАЗНА_ДАТОТЕКА ИЗЛАЗНА_ДАТОТЕКА ...
        Нови режим датотеке. Претвара датотеку УЛ_ДАТОТЕКА и пише излаз у
        датотеку ИЗЛ_ДАТОТЕКА. Називи датотека морају бити дати у паровима и
        називи џокера се „*не*“ могу користити или „*ћете*“ изгубити своје
        датотеке.

        Особа која започне претварање у режиму нове (упарене) датотеке биће
        власник претворене датотеке. Овлашћења за читање/писање нове
        датотеке биће овлашћење изворне датотеке мање „umask(1)“ особе која
        покреће претварање.

    --no-allow-chown
        Не дозвољава да се власништва датотеке промене у режиму старе
        датотеке.

        Прекида претварање када власништво корисника и/или групе изворне
        датотеке не може бити очувано у старом режиму датотеке. Такође
        видите опције „"-o"“ и „"-n"“. Ова опција је доступна само ако
        „dos2unix“ има подршку за очување власништва корисника и група над
        датотекама.

    --no-add-eol
        Не додаје прекид реда у последњи ред ако га нема.

    -O, --to-stdout
        Пише на стандардни излаз, као Јуникс филтер. Користите опцију „-o“
        да се вратите на режим старе датотеке (одмах).

        Заједно са опцијом „-e“ датотеке се могу исправно надовезати. Нема
        стопљеног првог и последњег реда, и нема ознаке поретка Јуникод
        бајта по средини надовезане датотеке. Пример:

            dos2unix -e -O дттка1.txt дттка2.txt > излаз.txt

    -o, --oldfile ДАТОТЕКА ...
        Режим старе датотеке. Претвара датотеку ДАТОТЕКА и преписује излаз у
        њу. Програм подразумевано ради у овом режиму. Џокери назива се могу
        користити.

        У режиму старе датотеке (у месту) претворена датотека добија иста
        овлашћења власника, групе и читања/писања као и изворна датотека.
        Такође када датотеку претвори други корисник који има овлашћење
        писања над датотеком (нпр. корисник администратор). Претварање ће се
        прекинути када не буде било могуће очувати изворне вредности.
        Промена власника може значити да првобитни власник више не може да
        чита датотеку. Промена групе може представљати безбедносни ризик,
        датотека може постати читљива од стране особа којима није намењена.
        Очување овлашћења власника, групе и читања/писања подржано је само
        на Јуниксу.

        Да проверите да ли „dos2unix“ има подршку очувања власништва
        корисника и групе над датотеком упишите „"dos2unix -V"“.

        Претварање се увек врши путем привремене датотеке. Када се на пола
        претварања догоди грешка, привремена датотека се брише а изворна
        датотека остаје нетакнута. Када претварање успе, изворна датотека се
        замењује привременом датотеком. Можете имати дозволу за писање над
        изворном датотеком, али немате дозволу да ставите иста својстава
        овлашћења корисника и/или груп у привремену датотеку као што има и
        изворна датотека. То значи да нисте у могућности да сачувате
        власништво корисника и/или групе над изворном датотеком. У овом
        случају можете користити опцију „"--allow-chown"“ да наставите са
        претварањем:

            dos2unix --allow-chown foo.txt

        Друга могућност је да користи режим нове датотеке:

            dos2unix -n foo.txt foo.txt

        Предност опције „"--allow-chown"“ је да можете користити џокере, а
        својства власништва биће очувана када је могуће.

    -q, --quiet
        Тихи режим. Потискује сва упозорења и поруке. Резултантна вредност
        је нула. Осим када се користе погрешне опције линије наредби.

    -r, --remove-bom
        Уклања Ознаку редоследа бајтова (BOM). Не пише ОРБ у излазну
        датотеку. Ово је основно понашање приликом претварања у Јуникс
        преламање реда. Видите такође опцију „"-b"“.

    -s, --safe
        Прескаче извршне датотеке (основно).

        Прескакање бинарних датотека врши се како би се избегле случајне
        грешке. Имајте на уму да откривање бинарних датотека није 100%
        поуздано. У улазним датотекама се траже бинарни симболи који се
        обично не налазе у текстуалним датотекама. Могуће је да бинарна
        датотека садржи само обичне текстуалне знакове. Таква бинарна
        датотека ће се погрешно сматрати текстуалном датотеком.

    -u, --keep-utf16
        Задржава изворно УТФ-16 кодирање улазне датотеке. Излазна датотека
        биће записана у истом УТФ-16 кодирању, малом или великом крајношћу,
        као и улазна датотека. Ово спречава преображај у УТФ-8. УТФ-16 ОРБ
        биће записан у складу с тим. Ова опција се може онемогућити помоћу
        опције „"-ascii"“.

    -ul, --assume-utf16le
        Подразумева да је формат улазне датотеке „UTF-16LE“.

        Када постоји Ознака редоследа бајтова у улазној датотеци ОРБ има
        предност над овом опцијом.

        Када сте погрешно претпоставили (улазна датотека није била у
        УТФ-16ЛЕ формату) а претварање је успело, добићете УТФ-8 излазну
        датотеку са погрешним текстом. Погрешно претварање можете да
        опозовете помоћу „iconv(1)“ претварањем УТФ-8 излазне датотеке назад
        у УТФ-16ЛЕ. Ово ће вратити изворну датотеку.

        Претпоставка УТФ-16ЛЕ ради као „*conversion mode*“. Пребацивањем на
        основни „*ascii*“ режим УТФ-16ЛЕ претпоставка се искључује.

    -ub, --assume-utf16be
        Подразумева да је формат улазне датотеке „UTF-16BE“.

        Ова опција ради исто као и опција „"-ul"“.

    -v, --verbose
        Приказује опширне поруке. Додатне информације се приказују о Ознаци
        редоследа бајтова и количини претворених преламања реда.

    -F, --follow-symlink
        Прати симболичке везе и претвара циљеве.

    -R, --replace-symlink
        Замењује симболичке везе претвореним датотекама (изворне циљне
        датотеке остају непромењене).

    -S, --skip-symlink
        Задржава неизмењеним симболичке везе и циљеве (основно).

    -V, --version
        Приказује податке о издању и излази.

МЕК РЕЖИМ
    By default line breaks are converted from DOS to Unix and vice versa.
    Mac line breaks are not converted.

    У Мек режиму преламања реда се претварју из Мек-а у Јуникс и обратно.
    ДОС преламања реда се не мењају.

    Да покренете у Мек режиму користите опцију „"-c mac"“ или користите
    наредбе „"mac2unix"“ или „"unix2mac"“.

РЕЖИМИ ПРЕТВАРАЊА
    ascii
        This is the default conversion mode. This mode is for converting
        ASCII and ASCII-compatible encoded files, like UTF-8. Enabling ascii
        mode disables 7bit and iso mode.

        If dos2unix has UTF-16 support, UTF-16 encoded files are converted
        to the current locale character encoding on POSIX systems and to
        UTF-8 on Windows. Enabling ascii mode disables the option to keep
        UTF-16 encoding ("-u") and the options to assume UTF-16 input ("-ul"
        and "-ub"). To see if dos2unix has UTF-16 support type "dos2unix
        -V". See also section UNICODE.

    7bit
        У овом режиму сви 8 битни не-АСКРИ знаци (са вредностима од 128 до
        255) се претварају у 7 битне размаке.

    iso Знакови се претварају између ДОС скупа знакова (кодна страница) и
        ИСО скупа знакова ИСО-8859-1 (Latin-1) на Јуниксу. ДОС знакови без
        ИСО-8859-1 еквивалента, за које претварање није могуће, претварају
        се у тачку. Исто се рачуна и за ИСО-8859-1 знакове без ДОС-а.

        Када се користи само опција „"-iso"“, „dos2unix“ ће покушати да
        одреди активну кодну страницу. Када то није могуће, „dos2unix“ ће
        користити подразумевану кодну страницу ЦП437, која се углавном
        користи у САД-у. Да бисте приморали одређену кодну страницу,
        користите опције „-437“ (САД), „-850“ (западноевропски), „-860“
        (португалски), „-863“ (француски канадски) или „-865“ (нордијски).
        Виндоуз кодна страница ЦП1252 (западноевропски) је такође подржана
        опцијом „-1252“. За остале кодне странице користите „dos2unix“ у
        комбинацији са „iconv(1)“. Иконв може да претвара између дугог
        списка кодирања знакова.

        Никада не користите ИСО претварање над Јуникод текстуалним
        датотекама. Оштетиће УТФ-8 кодиране датотеке.

        Неки примери:

        Претворите из ДОС основне кодне странице у Јуникс Latin-1:

            dos2unix -iso -n  улаз.txt излаз.txt

        Претворите из ДОС CP850 у Јуникс Latin-1:

            dos2unix -850 -n  улаз.txt излаз.txt

        Претворите из Виндоуз CP1252 у Јуникс Latin-1:

            dos2unix -1252 -n  улаз.txt излаз.txt

        Претворите из Виндоуз CP1252 у Јуникс УТФ-8 (Јуникод):

            iconv -f CP1252 -t UTF-8  улаз.txt | dos2unix > излаз.txt

        Претворите из Јуникс Latin-1 у ДОС основну кодну страницу:

            unix2dos -iso -n улаз.txt излаз.txt

        Претворите из Јуникс Latin-1 у ДОС CP850:

            unix2dos -850 -n улаз.txt излаз.txt

        Претворите из Јуникс Latin-1 у Вондоуз CP1252:

            unix2dos -1252 -n улаз.txt излаз.txt

        Претворите из Јуникс УТФ-8 (Јуникод) у Вондоуз CP1252:

            unix2dos < улаз.txt | iconv -f UTF-8 -t CP1252 > излаз.txt

        Видите такође <http://czyborra.com/charsets/codepages.html> и
        <http://czyborra.com/charsets/iso8859.html>.

УНИКОД
  Кодирања
    Постоје различита Јуникод кодирања. На Јуниксу и Линуксу Јуникод
    датотеке су обично кодиране у УТФ-8 кодирању. На Виндоузу Јуникод
    текстуалне датотеке могу бити кодиране у УТФ-8, УТФ-16 или УТФ-16
    великој крајности, али су углавном кодиране у УТФ-16 формату.

  Претварање
    Unicode text files can have DOS, Unix or Mac line breaks, like ASCII
    text files.

    Сва издања „dos2unix“-а и „unix2do“-а могу претворити УТФ-8 кодиране
    датотеке, јер је УТФ-8 дизајниран за повратну сагласност са АСКРИ-ијем.

    „dos2unix“ и „unix2dos“ са Јуникод УТФ-16 подршком, може читати УТФ-16
    кодиране текстуалне датотеке мале и велике крајности. Да видите да ли је
    „dos2unix“ изграђен са УТФ-16 подршком упишите „"dos2unix -V"“.

    На Јуниксу/Линуксу УТФ-16 кодиране датотеке се претварају у локално
    кодирање знакова. Користите наредбу „locale(1)“ да сазнате које је
    кодирање знакова локализације. Када претварање није могуће, десиће се
    грешка претварања а датотека ће бити прескочена.

    На Виндоузу УТФ-16 датотеке се подразумевано претварају у УТФ-8. УТФ-8
    форматиране текстуалне датотеке су добро подржане и на Виндоузу и на
    Јуникс/Линуксу.

    УТФ-16 и УТФ-8 кодирања су у потпуности сагласна, у претварању се неће
    изгубити никакав текст. Када се догоди грешка претварања УТФ-16 у УТФ-8,
    на пример када улазна УТФ-16 датотека садржи грешку, датотека ће бити
    прескочена.

    Када се користи опција „"-u"“, излазна датотека ће бити записана у истом
    УТФ-16 кодирању као и улазна датотека. Опција „"-u"“ спречава претварање
    у УТФ-8.

    „dos2unix“ и „unix2dos“ немају могућности да претворе УТФ-8 датотеке у
    УТФ-16.

    ИСО и 7-битни режим претварања не ради на УТФ-16 датотекама.

  Ознака редоследа бајтова
    On Windows Unicode text files typically have a Byte Order Mark (BOM),
    because many Windows programs (including Notepad) add BOMs by default.
    See also <https://en.wikipedia.org/wiki/Byte_order_mark>.

    На Јуниксу Јуникод датотеке обично немају ОРБ. Претпоставља се да су
    текстуалне датотеке кодиране у језичком кодирању знака.

    „dos2unix“ може открити само да ли је датотека у УТФ-16 формату и да ли
    датотека има ОРБ. Када УТФ-16 датотека нема ОРБ, „dos2unix“ ће видети
    датотеку као бинарну.

    Користите опцију „"-ul"“ или „"-ub"“ да претворите УТФ-16 датотеку без
    ОРБ-а.

    „dos2unix“ подразумевано не записује ОРБ у излазну датотеку. Са опцијом
    „"-b"“ „dos2unix записује ОРБ када улазна датотека има ОРБ.

    „unix2dos“ подразумевано записује ОРБ у излазну датотеку када улазна
    датотека има ОРБ. Користите опцију „"-r"“ да уклоните ОРБ.

    „dos2unix“ и „unix2dos“ увек записује ОРБ када се користи „"-m"“ опција.

  Јуникод називи датотека на Виндоузу
    „dos2unix“ има изборну подршку за читање и писање Јуникод назива
    датотека у Виндоуз командној линији. То значи да „dos2unix“ може
    отворити датотеке које у називу имају знакове који нису део
    подразумеване системске АНСИ кодне странице. Да бисте видели да ли је
    „dos2unix“ за Виндоуз изграђен са подршком Јуникод назива датотеке
    упишите „"dos2unix -V"“.

    Постоје проблеми са приказом Јуникод назива датотека у Виндоуз конзоли.
    Видите опцију „"-D"“, „"--display-enc"“. Називи датотека могу бити
    погрешно приказани у конзоли, али ће датотеке бити записане под тачним
    називом.

  Примери Уникода
    Претвара из Виндоуз УТФ-16 (са ОРБ-ом) у Јуникс УТФ-8:

        dos2unix -n улаз.txt излаз.txt

    Претвара из Виндоуз УТФ-16ЛЕ (без ОРБ-а) у Јуникс УТФ-8:

        dos2unix -ul -n улаз.txt излаз.txt

    Претворите из Јуникс УТФ-8 у Вондоуз УТФ-8 са ОРБ-ом:

        unix2dos -m -n улаз.txt излаз.txt

    Претворите из Јуникс УТФ-8 у Вондоуз УТФ-16:

        unix2dos < улаз.txt | iconv -f UTF-8 -t UTF-16 > излаз.txt

GB18030
    GB18030 is a Chinese government standard. A mandatory subset of the
    GB18030 standard is officially required for all software products sold
    in China. See also <https://en.wikipedia.org/wiki/GB_18030>.

    „GB18030“ је у потпуности сагласан са Јуникод-ом, и може се сматрати
    Јуникод форматом преображаја. Као УТФ-8, „GB18030“ је сагласан са АСКРИ.
    „GB18030“ је такође сагласан са Виндоуз кодном страницом 936, познатом и
    као ГБК.

    На Јуниксу/Линуксу УТФ-16 датотеке се претварају у „GB18030“ када је
    језичко кодирање постављено на „GB18030“. Знајте да ће ово радити само
    ако је језик подржан системом. Користите наредбу „"locale -a"“ да
    добавите списак подржаних језика.

    На Виндоузу треба да користите опцију „"-gb"“ да преведете УТФ-16
    датотеке у „GB18030“.

    „GB18030“ кодиране датотеке могу имати Ознаку редоследа бајтова, као
    Јуникод датотеке.

ПРИМЕРИ
    Чита улаз са „стнд_улаза“ и пише излаз на „стнд_излаз“:

        dos2unix < а.txt
        cat а.txt | dos2unix

    Претвара и замењује „а.txt“. Претвара и замењује „б.txt“:

        dos2unix а.txt б.txt
        dos2unix -o а.txt б.txt

    Претвара и замењује „а.txt“ у аскри режиму претварања:

        dos2unix а.txt

    Претвара и замењује „а.txt“ у аскри режиму претварања, Претвара и
    замењује „б.txt“ у 7битном режиму претварања:

        dos2unix а.txt -c 7bit б.txt
        dos2unix -c ascii а.txt -c 7bit б.txt
        dos2unix -ascii а.txt -7 б.txt

    Претвара „а.txt“ из Мек у Јуникс формат:

        dos2unix -c mac а.txt
        mac2unix а.txt

    Претвара „а.txt“ из Јуникс у Мек формат:

        unix2dos -c mac а.txt
        unix2mac а.txt

    Претвара и замењује „а.txt“ док задржава изворни печат датума:

        dos2unix -k а.txt
        dos2unix -k -o а.txt

    Претвара „а.txt“ и пише на „е.txt“:

        dos2unix -n а.txt е.txt

    Претвара „а.txt“ и пише на „е.txt“, задржава печат датума „е.txt“-а
    истим као „а.txt“:

        dos2unix -k -n а.txt е.txt

    Претвара и замењује „а.txt“, претвара „б.txt“ и пише на „.txt“:

        dos2unix а.txt -n б.txt е.txt
        dos2unix -o а.txt -n б.txt е.txt

    Претвара „ц.txt“ и пише на „е.txt“, претвара и замењује„а.txt“, претвара
    и замењује „б.txt“, претвара „д.txt“ и пише у „ф.txt“:

        dos2unix -n ц.txt е.txt -o а.txt б.txt -n д.txt ф.txt

ДУБИНСКО ПРЕТВАРАЊЕ
    У Јуникс шкољци наредбе „find(1)“ и „xargs(1)“ се могу користити за
    покретање „dos2unix“-а дубински преко свих текстуалних датотека у стаблу
    директоријума. На пример за претварање свих „.txt“ датотека у стаблу
    директоријума под текућим директоријумом упишите:

        find . -name '*.txt' -print0 |xargs -0 dos2unix

    „find(1)“-ова опција „"-print0"“ и одговарајућа „xargs(1)“-ова опција
    „-0“ су потребне када постоје датотеке са размацима и наводницима у
    називима. У супротном се ове опције могу изоставити. Друга могућност је
    да користите „find(1)“ са „"-exec"“ опцијом:

        find . -name '*.txt' -exec dos2unix {} \;

    У Виндоуз командном упиту следеће наредбе се могу користити:

        for /R %G in (*.txt) do dos2unix "%G"

    Корисници „PowerShell“-а могу користити следеће наредбе у Виндоуз
    „PowerShell“-у:

        get-childitem -path . -filter '*.txt' -recurse | foreach-object {dos2unix $_.Fullname}

ЈЕЗИЧЕЊЕ
    ЈЕЗ Примарни језик се бира променљивом окружења „LANG“. Променљива
        „LANG“ се састоји из неколико делова. Први део је малим словима
        језички код. Други је необавезан и представља код државе великим
        словима, којем претходи доња црта. Ту је и изборни трећи део:
        кодирање знакова, којем претходи тачка. Неколико примера за врсте
        шкољки ПОСИКС стандарда:

            export LANG=nl               Холандски
            export LANG=nl_NL            Холандски, Низоземска
            export LANG=nl_BE            Холандски, Белгија
            export LANG=es_ES            Шпански, Шпанија
            export LANG=es_MX            Шпански, Мексико
            export LANG=en_US.iso88591   Енглески, САД, Latin-1 кодирање
            export LANG=en_GB.UTF-8      Енглески, УК, УТФ-8 кодирање

        For a complete list of language and country codes see the gettext
        manual:
        <https://www.gnu.org/software/gettext/manual/html_node/Usual-Languag
        e-Codes.html>

        На Јуникс системима можете користити наредбу „locale(1)“ да видите
        особене податке језика.

    ЈЕЗИК
        With the LANGUAGE environment variable you can specify a priority
        list of languages, separated by colons. Dos2unix gives preference to
        LANGUAGE over LANG. For instance, first Dutch and then German:
        "LANGUAGE=nl:de". You have to first enable localization, by setting
        LANG (or LC_ALL) to a value other than "C", before you can use a
        language priority list through the LANGUAGE variable. See also the
        gettext manual:
        <https://www.gnu.org/software/gettext/manual/html_node/The-LANGUAGE-
        variable.html>

        Ако изаберете језик који није доступан добићете стандардне поруке на
        енглеском.

    DOS2UNIX_LOCALEDIR
        Са променљивом окружења „DOS2UNIX_LOCALEDIR“ „LOCALEDIR“ поставка у
        време компилације се може поништити. „LOCALEDIR“ се користи за
        налажење датотека језика. ГНУ-ова основна вредност је
        „"/usr/local/share/locale"“. Опција „--version“ ће приказати
        „LOCALEDIR“ који се користи.

        Пример (ПОСИКС љуска):

            export DOS2UNIX_LOCALEDIR=$HOME/share/locale

РЕЗУЛТНА ВРЕДНОСТ
    На успех, резултат је нула. Када има системских грешака резултат је
    последња грешка система. За друге грешке резултат је 1.

    Вредност резултата је увек нула у тихом режиму, изузев када се користе
    погрешне опције линије наредби.

СТАНДАРДИ
    <https://en.wikipedia.org/wiki/Text_file>

    <https://en.wikipedia.org/wiki/Carriage_return>

    <https://en.wikipedia.org/wiki/Newline>

    <https://en.wikipedia.org/wiki/Unicode>

АУТОРИ
    Бенџамин Лин – <<EMAIL>>, Бернд Јоханес Вебен (mac2unix) –
    <<EMAIL>>, Кристијан Вирл (додаје додатни нови ред) –
    <<EMAIL>>, Ервин Вотерландер – <<EMAIL>>
    (одржавалац)

    Project page: <https://waterlan.home.xs4all.nl/dos2unix.html>

    SourceForge page: <https://sourceforge.net/projects/dos2unix/>

ВИДИТЕ ТАКОЂЕ
    file(1) find(1) iconv(1) locale(1) xargs(1)

