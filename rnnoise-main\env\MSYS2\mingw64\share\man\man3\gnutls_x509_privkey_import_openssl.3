.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_import_openssl" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_import_openssl \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_privkey_import_openssl(gnutls_x509_privkey_t " key ", const gnutls_datum_t * " data ", const char * " password ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t key" 12
The data to store the parsed key
.IP "const gnutls_datum_t * data" 12
The DER or PEM encoded key.
.IP "const char * password" 12
the password to decrypt the key (if it is encrypted).
.SH "DESCRIPTION"
This function will convert the given PEM encrypted to 
the native gnutls_x509_privkey_t format. The
output will be stored in  \fIkey\fP .  

The  \fIpassword\fP should be in ASCII. If the password is not provided
or wrong then \fBGNUTLS_E_DECRYPTION_FAILED\fP will be returned.

If the Certificate is PEM encoded it should have a header of
"PRIVATE KEY" and the "DEK\-Info" header. 
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
