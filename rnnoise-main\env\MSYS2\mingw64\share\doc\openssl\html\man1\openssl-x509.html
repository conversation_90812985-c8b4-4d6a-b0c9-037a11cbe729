<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-x509</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a>
    <ul>
      <li><a href="#Input-Output-and-General-Purpose-Options">Input, Output, and General Purpose Options</a></li>
      <li><a href="#Certificate-Printing-Options">Certificate Printing Options</a></li>
      <li><a href="#Certificate-Checking-Options">Certificate Checking Options</a></li>
      <li><a href="#Certificate-Output-Options">Certificate Output Options</a></li>
      <li><a href="#Micro-CA-Options">Micro-CA Options</a></li>
      <li><a href="#Trust-Settings">Trust Settings</a></li>
      <li><a href="#Generic-options">Generic options</a></li>
      <li><a href="#Text-Printing-Flags">Text Printing Flags</a></li>
    </ul>
  </li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-x509 - Certificate display and signing command</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>x509</b> [<b>-help</b>] [<b>-in</b> <i>filename</i>|<i>uri</i>] [<b>-passin</b> <i>arg</i>] [<b>-new</b>] [<b>-x509toreq</b>] [<b>-req</b>] [<b>-copy_extensions</b> <i>arg</i>] [<b>-inform</b> <b>DER</b>|<b>PEM</b>] [<b>-vfyopt</b> <i>nm</i>:<i>v</i>] [<b>-key</b> <i>filename</i>|<i>uri</i>] [<b>-keyform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>|<b>ENGINE</b>] [<b>-signkey</b> <i>filename</i>|<i>uri</i>] [<b>-out</b> <i>filename</i>] [<b>-outform</b> <b>DER</b>|<b>PEM</b>] [<b>-nocert</b>] [<b>-noout</b>] [<b>-dateopt</b>] [<b>-text</b>] [<b>-certopt</b> <i>option</i>] [<b>-fingerprint</b>] [<b>-alias</b>] [<b>-serial</b>] [<b>-startdate</b>] [<b>-enddate</b>] [<b>-dates</b>] [<b>-subject</b>] [<b>-issuer</b>] [<b>-nameopt</b> <i>option</i>] [<b>-email</b>] [<b>-hash</b>] [<b>-subject_hash</b>] [<b>-subject_hash_old</b>] [<b>-issuer_hash</b>] [<b>-issuer_hash_old</b>] [<b>-ext</b> <i>extensions</i>] [<b>-ocspid</b>] [<b>-ocsp_uri</b>] [<b>-purpose</b>] [<b>-pubkey</b>] [<b>-modulus</b>] [<b>-checkend</b> <i>num</i>] [<b>-checkhost</b> <i>host</i>] [<b>-checkemail</b> <i>host</i>] [<b>-checkip</b> <i>ipaddr</i>] [<b>-set_serial</b> <i>n</i>] [<b>-next_serial</b>] [<b>-not_before</b> <i>date</i>] [<b>-not_after</b> <i>date</i>] [<b>-days</b> <i>arg</i>] [<b>-preserve_dates</b>] [<b>-set_issuer</b> <i>arg</i>] [<b>-set_subject</b> <i>arg</i>] [<b>-subj</b> <i>arg</i>] [<b>-force_pubkey</b> <i>filename</i>] [<b>-clrext</b>] [<b>-extfile</b> <i>filename</i>] [<b>-extensions</b> <i>section</i>] [<b>-sigopt</b> <i>nm</i>:<i>v</i>] [<b>-badsig</b>] [<b>-<i>digest</i></b>] [<b>-CA</b> <i>filename</i>|<i>uri</i>] [<b>-CAform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>] [<b>-CAkey</b> <i>filename</i>|<i>uri</i>] [<b>-CAkeyform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>|<b>ENGINE</b>] [<b>-CAserial</b> <i>filename</i>] [<b>-CAcreateserial</b>] [<b>-trustout</b>] [<b>-setalias</b> <i>arg</i>] [<b>-clrtrust</b>] [<b>-addtrust</b> <i>arg</i>] [<b>-clrreject</b>] [<b>-addreject</b> <i>arg</i>] [<b>-rand</b> <i>files</i>] [<b>-writerand</b> <i>file</i>] [<b>-engine</b> <i>id</i>] [<b>-provider</b> <i>name</i>] [<b>-provider-path</b> <i>path</i>] [<b>-provparam</b> <i>[name:]key=value</i>] [<b>-propquery</b> <i>propq</i>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This command is a multi-purposes certificate handling command. It can be used to print certificate information, convert certificates to various forms, edit certificate trust settings, generate certificates from scratch or from certification requests and then self-signing them or signing them like a &quot;micro CA&quot;.</p>

<p>Generated certificates bear X.509 version 3. Unless specified otherwise, key identifier extensions are included as described in <a href="../man5/x509v3_config.html">x509v3_config(5)</a>.</p>

<p>Since there are a large number of options they will split up into various sections.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<h2 id="Input-Output-and-General-Purpose-Options">Input, Output, and General Purpose Options</h2>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="in-filename-uri"><b>-in</b> <i>filename</i>|<i>uri</i></dt>
<dd>

<p>This specifies the input to read a certificate from or the input file for reading a certificate request if the <b>-req</b> flag is used. In both cases this defaults to standard input.</p>

<p>This option cannot be combined with the <b>-new</b> flag.</p>

</dd>
<dt id="passin-arg"><b>-passin</b> <i>arg</i></dt>
<dd>

<p>The key and certificate file password source. For more information about the format of <i>arg</i> see <a href="../man1/openssl-passphrase-options.html">openssl-passphrase-options(1)</a>.</p>

</dd>
<dt id="new"><b>-new</b></dt>
<dd>

<p>Generate a certificate from scratch, not using an input certificate or certificate request. So this excludes the <b>-in</b> and <b>-req</b> options. Instead, the <b>-set_subject</b> option needs to be given. The public key to include can be given with the <b>-force_pubkey</b> option and defaults to the key given with the <b>-key</b> (or <b>-signkey</b>) option, which implies self-signature.</p>

</dd>
<dt id="x509toreq"><b>-x509toreq</b></dt>
<dd>

<p>Output a PKCS#10 certificate request (rather than a certificate). The <b>-key</b> (or <b>-signkey</b>) option must be used to provide the private key for self-signing; the corresponding public key is placed in the subjectPKInfo field.</p>

<p>X.509 extensions included in a certificate input are not copied by default. X.509 extensions to be added can be specified using the <b>-extfile</b> option.</p>

</dd>
<dt id="req"><b>-req</b></dt>
<dd>

<p>By default a certificate is expected on input. With this option a PKCS#10 certificate request is expected instead, which must be correctly self-signed.</p>

<p>X.509 extensions included in the request are not copied by default. X.509 extensions to be added can be specified using the <b>-extfile</b> option.</p>

</dd>
<dt id="copy_extensions-arg"><b>-copy_extensions</b> <i>arg</i></dt>
<dd>

<p>Determines how to handle X.509 extensions when converting from a certificate to a request using the <b>-x509toreq</b> option or converting from a request to a certificate using the <b>-req</b> option. If <i>arg</i> is <b>none</b> or this option is not present then extensions are ignored. If <i>arg</i> is <b>copy</b> or <b>copyall</b> then all extensions are copied, except that subject identifier and authority key identifier extensions are not taken over when producing a certificate request.</p>

<p>The <b>-ext</b> option can be used to further restrict which extensions to copy.</p>

</dd>
<dt id="inform-DER-PEM"><b>-inform</b> <b>DER</b>|<b>PEM</b></dt>
<dd>

<p>The input file format to use; by default PEM is tried first. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

</dd>
<dt id="vfyopt-nm:v"><b>-vfyopt</b> <i>nm</i>:<i>v</i></dt>
<dd>

<p>Pass options to the signature algorithm during verify operations. Names and values of these options are algorithm-specific.</p>

</dd>
<dt id="key-filename-uri"><b>-key</b> <i>filename</i>|<i>uri</i></dt>
<dd>

<p>This option provides the private key for signing a new certificate or certificate request. Unless <b>-force_pubkey</b> is given, the corresponding public key is placed in the new certificate or certificate request, resulting in a self-signature.</p>

<p>This option cannot be used in conjunction with the <b>-CA</b> option.</p>

<p>It sets the issuer name to the subject name (i.e., makes it self-issued). Unless the <b>-preserve_dates</b> option is supplied, it sets the validity start date to the current time and the end date to a value determined by the <b>-days</b> option. Start date and end date can also be explicitly supplied with options <b>-not_before</b> and <b>-not_after</b>.</p>

</dd>
<dt id="signkey-filename-uri"><b>-signkey</b> <i>filename</i>|<i>uri</i></dt>
<dd>

<p>This option is an alias of <b>-key</b>.</p>

</dd>
<dt id="keyform-DER-PEM-P12-ENGINE"><b>-keyform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>|<b>ENGINE</b></dt>
<dd>

<p>The key input format; unspecified by default. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

</dd>
<dt id="out-filename"><b>-out</b> <i>filename</i></dt>
<dd>

<p>This specifies the output filename to write to or standard output by default.</p>

</dd>
<dt id="outform-DER-PEM"><b>-outform</b> <b>DER</b>|<b>PEM</b></dt>
<dd>

<p>The output format; the default is <b>PEM</b>. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

</dd>
<dt id="nocert"><b>-nocert</b></dt>
<dd>

<p>Do not output a certificate (except for printing as requested by below options).</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>This option prevents output except for printing as requested by below options.</p>

</dd>
</dl>

<h2 id="Certificate-Printing-Options">Certificate Printing Options</h2>

<p>Note: the <b>-alias</b> and <b>-purpose</b> options are also printing options but are described in the <a href="#Trust-Settings">&quot;Trust Settings&quot;</a> section.</p>

<dl>

<dt id="dateopt"><b>-dateopt</b></dt>
<dd>

<p>Specify the date output format. Values are: rfc_822 and iso_8601. Defaults to rfc_822.</p>

</dd>
<dt id="text"><b>-text</b></dt>
<dd>

<p>Prints out the certificate in text form. Full details are printed including the public key, signature algorithms, issuer and subject names, serial number any extensions present and any trust settings.</p>

</dd>
<dt id="certopt-option"><b>-certopt</b> <i>option</i></dt>
<dd>

<p>Customise the print format used with <b>-text</b>. The <i>option</i> argument can be a single option or multiple options separated by commas. The <b>-certopt</b> switch may be also be used more than once to set multiple options. See the <a href="#Text-Printing-Flags">&quot;Text Printing Flags&quot;</a> section for more information.</p>

</dd>
<dt id="fingerprint"><b>-fingerprint</b></dt>
<dd>

<p>Calculates and prints the digest of the DER encoded version of the entire certificate (see digest options). This is commonly called a &quot;fingerprint&quot;. Because of the nature of message digests, the fingerprint of a certificate is unique to that certificate and two certificates with the same fingerprint can be considered to be the same.</p>

</dd>
<dt id="alias"><b>-alias</b></dt>
<dd>

<p>Prints the certificate &quot;alias&quot; (nickname), if any.</p>

</dd>
<dt id="serial"><b>-serial</b></dt>
<dd>

<p>Prints the certificate serial number.</p>

</dd>
<dt id="startdate"><b>-startdate</b></dt>
<dd>

<p>Prints out the start date of the certificate, that is the notBefore date.</p>

</dd>
<dt id="enddate"><b>-enddate</b></dt>
<dd>

<p>Prints out the expiry date of the certificate, that is the notAfter date.</p>

</dd>
<dt id="dates"><b>-dates</b></dt>
<dd>

<p>Prints out the start and expiry dates of a certificate.</p>

</dd>
<dt id="subject"><b>-subject</b></dt>
<dd>

<p>Prints the subject name.</p>

</dd>
<dt id="issuer"><b>-issuer</b></dt>
<dd>

<p>Prints the issuer name.</p>

</dd>
<dt id="nameopt-option"><b>-nameopt</b> <i>option</i></dt>
<dd>

<p>This specifies how the subject or issuer names are displayed. See <a href="../man1/openssl-namedisplay-options.html">openssl-namedisplay-options(1)</a> for details.</p>

</dd>
<dt id="email"><b>-email</b></dt>
<dd>

<p>Prints the email address(es) if any.</p>

</dd>
<dt id="hash"><b>-hash</b></dt>
<dd>

<p>Synonym for &quot;-subject_hash&quot; for backward compatibility reasons.</p>

</dd>
<dt id="subject_hash"><b>-subject_hash</b></dt>
<dd>

<p>Prints the &quot;hash&quot; of the certificate subject name. This is used in OpenSSL to form an index to allow certificates in a directory to be looked up by subject name.</p>

</dd>
<dt id="subject_hash_old"><b>-subject_hash_old</b></dt>
<dd>

<p>Prints the &quot;hash&quot; of the certificate subject name using the older algorithm as used by OpenSSL before version 1.0.0.</p>

</dd>
<dt id="issuer_hash"><b>-issuer_hash</b></dt>
<dd>

<p>Prints the &quot;hash&quot; of the certificate issuer name.</p>

</dd>
<dt id="issuer_hash_old"><b>-issuer_hash_old</b></dt>
<dd>

<p>Prints the &quot;hash&quot; of the certificate issuer name using the older algorithm as used by OpenSSL before version 1.0.0.</p>

</dd>
<dt id="ext-extensions"><b>-ext</b> <i>extensions</i></dt>
<dd>

<p>Prints out the certificate extensions in text form. Can also be used to restrict which extensions to copy. Extensions are specified with a comma separated string, e.g., &quot;subjectAltName, subjectKeyIdentifier&quot;. See the <a href="../man5/x509v3_config.html">x509v3_config(5)</a> manual page for the extension names.</p>

</dd>
<dt id="ocspid"><b>-ocspid</b></dt>
<dd>

<p>Prints the OCSP hash values for the subject name and public key.</p>

</dd>
<dt id="ocsp_uri"><b>-ocsp_uri</b></dt>
<dd>

<p>Prints the OCSP responder address(es) if any.</p>

</dd>
<dt id="purpose"><b>-purpose</b></dt>
<dd>

<p>This option performs tests on the certificate extensions and outputs the results. For a more complete description see <a href="../man1/openssl-verification-options.html">&quot;Certificate Extensions&quot; in openssl-verification-options(1)</a>.</p>

</dd>
<dt id="pubkey"><b>-pubkey</b></dt>
<dd>

<p>Prints the certificate&#39;s SubjectPublicKeyInfo block in PEM format.</p>

</dd>
<dt id="modulus"><b>-modulus</b></dt>
<dd>

<p>This option prints out the value of the modulus of the public key contained in the certificate.</p>

</dd>
</dl>

<h2 id="Certificate-Checking-Options">Certificate Checking Options</h2>

<dl>

<dt id="checkend-arg"><b>-checkend</b> <i>arg</i></dt>
<dd>

<p>Checks if the certificate expires within the next <i>arg</i> seconds and exits nonzero if yes it will expire or zero if not.</p>

</dd>
<dt id="checkhost-host"><b>-checkhost</b> <i>host</i></dt>
<dd>

<p>Check that the certificate matches the specified host.</p>

</dd>
<dt id="checkemail-email"><b>-checkemail</b> <i>email</i></dt>
<dd>

<p>Check that the certificate matches the specified email address.</p>

</dd>
<dt id="checkip-ipaddr"><b>-checkip</b> <i>ipaddr</i></dt>
<dd>

<p>Check that the certificate matches the specified IP address.</p>

</dd>
</dl>

<h2 id="Certificate-Output-Options">Certificate Output Options</h2>

<dl>

<dt id="set_serial-n"><b>-set_serial</b> <i>n</i></dt>
<dd>

<p>Specifies the serial number to use. This option can be used with the <b>-key</b>, <b>-signkey</b>, or <b>-CA</b> options. If used in conjunction with the <b>-CA</b> option the serial number file (as specified by the <b>-CAserial</b> option) is not used.</p>

<p>The serial number can be decimal or hex (if preceded by <code>0x</code>).</p>

</dd>
<dt id="next_serial"><b>-next_serial</b></dt>
<dd>

<p>Set the serial to be one more than the number in the certificate.</p>

</dd>
<dt id="not_before-date"><b>-not_before</b> <i>date</i></dt>
<dd>

<p>This allows the start date to be explicitly set. The format of the date is YYMMDDHHMMSSZ (the same as an ASN1 UTCTime structure), or YYYYMMDDHHMMSSZ (the same as an ASN1 GeneralizedTime structure). In both formats, seconds SS and timezone Z must be present. Alternatively, you can also use &quot;today&quot;.</p>

<p>Cannot be used together with the <b>-preserve_dates</b> option.</p>

</dd>
<dt id="not_after-date"><b>-not_after</b> <i>date</i></dt>
<dd>

<p>This allows the expiry date to be explicitly set. The format of the date is YYMMDDHHMMSSZ (the same as an ASN1 UTCTime structure), or YYYYMMDDHHMMSSZ (the same as an ASN1 GeneralizedTime structure). In both formats, seconds SS and timezone Z must be present. Alternatively, you can also use &quot;today&quot;.</p>

<p>Cannot be used together with the <b>-preserve_dates</b> option. This overrides the option <b>-days</b>.</p>

</dd>
<dt id="days-arg"><b>-days</b> <i>arg</i></dt>
<dd>

<p>Specifies the number of days from today until a newly generated certificate expires. The default is 30.</p>

<p>Cannot be used together with the option <b>-preserve_dates</b>. If option <b>-not_after</b> is set, the explicit expiry date takes precedence.</p>

</dd>
<dt id="preserve_dates"><b>-preserve_dates</b></dt>
<dd>

<p>When signing a certificate, preserve &quot;notBefore&quot; and &quot;notAfter&quot; dates of any input certificate instead of adjusting them to current time and duration. Cannot be used together with the options <b>-days</b>, <b>-not_before</b> and <b>-not_after</b>.</p>

</dd>
<dt id="set_issuer-arg"><b>-set_issuer</b> <i>arg</i></dt>
<dd>

<p>When a certificate is created set its issuer name to the given value.</p>

<p>See <b>-set_subject</b> on how the arg must be formatted.</p>

</dd>
<dt id="set_subject-arg"><b>-set_subject</b> <i>arg</i></dt>
<dd>

<p>When a certificate is created set its subject name to the given value. When the certificate is self-signed the issuer name is set to the same value, unless the <b>-set_issuer</b> option is given.</p>

<p>The arg must be formatted as <code>/type0=value0/type1=value1/type2=...</code>. Special characters may be escaped by <code>\</code> (backslash), whitespace is retained. Empty values are permitted, but the corresponding type will not be included in the certificate. Giving a single <code>/</code> will lead to an empty sequence of RDNs (a NULL-DN). Multi-valued RDNs can be formed by placing a <code>+</code> character instead of a <code>/</code> between the AttributeValueAssertions (AVAs) that specify the members of the set. Example:</p>

<p><code>/DC=org/DC=OpenSSL/DC=users/UID=123456+CN=John Doe</code></p>

<p>This option can be used with the <b>-new</b> and <b>-force_pubkey</b> options to create a new certificate without providing an input certificate or certificate request.</p>

</dd>
<dt id="subj-arg"><b>-subj</b> <i>arg</i></dt>
<dd>

<p>This option is an alias of <b>-set_subject</b>.</p>

</dd>
<dt id="force_pubkey-filename"><b>-force_pubkey</b> <i>filename</i></dt>
<dd>

<p>When a new certificate or certificate request is created set its public key to the given key instead of the key contained in the input or given with the <b>-key</b> (or <b>-signkey</b>) option. If the input contains no public key but a private key, its public part is used.</p>

<p>This option can be used in conjunction with b&lt;-new&gt; and <b>-set_subject</b> to directly generate a certificate containing any desired public key.</p>

<p>This option is also useful for creating self-issued certificates that are not self-signed, for instance when the key cannot be used for signing, such as DH.</p>

</dd>
<dt id="clrext"><b>-clrext</b></dt>
<dd>

<p>When transforming a certificate to a new certificate by default all certificate extensions are retained.</p>

<p>When transforming a certificate or certificate request, the <b>-clrext</b> option prevents taking over any extensions from the source. In any case, when producing a certificate request, neither subject identifier nor authority key identifier extensions are included.</p>

</dd>
<dt id="extfile-filename"><b>-extfile</b> <i>filename</i></dt>
<dd>

<p>Configuration file containing certificate and request X.509 extensions to add.</p>

</dd>
<dt id="extensions-section"><b>-extensions</b> <i>section</i></dt>
<dd>

<p>The section in the extfile to add X.509 extensions from. If this option is not specified then the extensions should either be contained in the unnamed (default) section or the default section should contain a variable called &quot;extensions&quot; which contains the section to use.</p>

<p>See the <a href="../man5/x509v3_config.html">x509v3_config(5)</a> manual page for details of the extension section format.</p>

<p>Unless specified otherwise, key identifier extensions are included as described in <a href="../man5/x509v3_config.html">x509v3_config(5)</a>.</p>

</dd>
<dt id="sigopt-nm:v"><b>-sigopt</b> <i>nm</i>:<i>v</i></dt>
<dd>

<p>Pass options to the signature algorithm during sign operations. This option may be given multiple times. Names and values provided using this option are algorithm-specific.</p>

</dd>
<dt id="badsig"><b>-badsig</b></dt>
<dd>

<p>Corrupt the signature before writing it; this can be useful for testing.</p>

</dd>
<dt id="digest"><b>-<i>digest</i></b></dt>
<dd>

<p>The digest to use. This affects any signing or printing option that uses a message digest, such as the <b>-fingerprint</b>, <b>-key</b>, and <b>-CA</b> options. Any digest supported by the <a href="../man1/openssl-dgst.html">openssl-dgst(1)</a> command can be used. If not specified then SHA1 is used with <b>-fingerprint</b> or the default digest for the signing algorithm is used, typically SHA256.</p>

</dd>
</dl>

<h2 id="Micro-CA-Options">Micro-CA Options</h2>

<dl>

<dt id="CA-filename-uri"><b>-CA</b> <i>filename</i>|<i>uri</i></dt>
<dd>

<p>Specifies the &quot;CA&quot; certificate to be used for signing. When present, this behaves like a &quot;micro CA&quot; as follows: The subject name of the &quot;CA&quot; certificate is placed as issuer name in the new certificate, which is then signed using the &quot;CA&quot; key given as detailed below.</p>

<p>This option cannot be used in conjunction with <b>-key</b> (or <b>-signkey</b>). This option is normally combined with the <b>-req</b> option referencing a CSR. Without the <b>-req</b> option the input must be an existing certificate unless the <b>-new</b> option is given, which generates a certificate from scratch.</p>

</dd>
<dt id="CAform-DER-PEM-P12"><b>-CAform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>,</dt>
<dd>

<p>The format for the CA certificate; unspecified by default. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

</dd>
<dt id="CAkey-filename-uri"><b>-CAkey</b> <i>filename</i>|<i>uri</i></dt>
<dd>

<p>Sets the CA private key to sign a certificate with. The private key must match the public key of the certificate given with <b>-CA</b>. If this option is not provided then the key must be present in the <b>-CA</b> input.</p>

</dd>
<dt id="CAkeyform-DER-PEM-P12-ENGINE"><b>-CAkeyform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>|<b>ENGINE</b></dt>
<dd>

<p>The format for the CA key; unspecified by default. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

</dd>
<dt id="CAserial-filename"><b>-CAserial</b> <i>filename</i></dt>
<dd>

<p>Sets the CA serial number file to use.</p>

<p>When creating a certificate with this option and with the <b>-CA</b> option, the certificate serial number is stored in the given file. This file consists of one line containing an even number of hex digits with the serial number used last time. After reading this number, it is incremented and used, and the file is updated.</p>

<p>The default filename consists of the CA certificate file base name with <i>.srl</i> appended. For example if the CA certificate file is called <i>mycacert.pem</i> it expects to find a serial number file called <i>mycacert.srl</i>.</p>

<p>If the <b>-CA</b> option is specified and neither &lt;-CAserial&gt; or &lt;-CAcreateserial&gt; is given and the default serial number file does not exist, a random number is generated; this is the recommended practice.</p>

</dd>
<dt id="CAcreateserial"><b>-CAcreateserial</b></dt>
<dd>

<p>With this option and the <b>-CA</b> option the CA serial number file is created if it does not exist. A random number is generated, used for the certificate, and saved into the serial number file determined as described above.</p>

</dd>
</dl>

<h2 id="Trust-Settings">Trust Settings</h2>

<p>A <b>trusted certificate</b> is an ordinary certificate which has several additional pieces of information attached to it such as the permitted and prohibited uses of the certificate and possibly an &quot;alias&quot; (nickname).</p>

<p>Normally when a certificate is being verified at least one certificate must be &quot;trusted&quot;. By default a trusted certificate must be stored locally and must be a root CA: any certificate chain ending in this CA is then usable for any purpose.</p>

<p>Trust settings currently are only used with a root CA. They allow a finer control over the purposes the root CA can be used for. For example, a CA may be trusted for SSL client but not SSL server use.</p>

<p>See <a href="../man1/openssl-verification-options.html">openssl-verification-options(1)</a> for more information on the meaning of trust settings.</p>

<p>Future versions of OpenSSL will recognize trust settings on any certificate: not just root CAs.</p>

<dl>

<dt id="trustout"><b>-trustout</b></dt>
<dd>

<p>Mark any certificate PEM output as &lt;trusted&gt; certificate rather than ordinary. An ordinary or trusted certificate can be input but by default an ordinary certificate is output and any trust settings are discarded. With the <b>-trustout</b> option a trusted certificate is output. A trusted certificate is automatically output if any trust settings are modified.</p>

</dd>
<dt id="setalias-arg"><b>-setalias</b> <i>arg</i></dt>
<dd>

<p>Sets the &quot;alias&quot; of the certificate. This will allow the certificate to be referred to using a nickname for example &quot;Steve&#39;s Certificate&quot;.</p>

</dd>
<dt id="clrtrust"><b>-clrtrust</b></dt>
<dd>

<p>Clears all the permitted or trusted uses of the certificate.</p>

</dd>
<dt id="addtrust-arg"><b>-addtrust</b> <i>arg</i></dt>
<dd>

<p>Adds a trusted certificate use. Any object name can be used here but currently only <b>clientAuth</b>, <b>serverAuth</b>, <b>emailProtection</b>, and <b>anyExtendedKeyUsage</b> are defined. As of OpenSSL 1.1.0, the last of these blocks all purposes when rejected or enables all purposes when trusted. Other OpenSSL applications may define additional uses.</p>

</dd>
<dt id="clrreject"><b>-clrreject</b></dt>
<dd>

<p>Clears all the prohibited or rejected uses of the certificate.</p>

</dd>
<dt id="addreject-arg"><b>-addreject</b> <i>arg</i></dt>
<dd>

<p>Adds a prohibited trust anchor purpose. It accepts the same values as the <b>-addtrust</b> option.</p>

</dd>
</dl>

<h2 id="Generic-options">Generic options</h2>

<dl>

<dt id="rand-files--writerand-file"><b>-rand</b> <i>files</i>, <b>-writerand</b> <i>file</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Random State Options&quot; in openssl(1)</a> for details.</p>

</dd>
<dt id="engine-id"><b>-engine</b> <i>id</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Engine Options&quot; in openssl(1)</a>. This option is deprecated.</p>

</dd>
<dt id="provider-name"><b>-provider</b> <i>name</i></dt>
<dd>

</dd>
<dt id="provider-path-path"><b>-provider-path</b> <i>path</i></dt>
<dd>

</dd>
<dt id="provparam-name:-key-value"><b>-provparam</b> <i>[name:]key=value</i></dt>
<dd>

</dd>
<dt id="propquery-propq"><b>-propquery</b> <i>propq</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Provider Options&quot; in openssl(1)</a>, <a href="../man7/provider.html">provider(7)</a>, and <a href="../man7/property.html">property(7)</a>.</p>

</dd>
</dl>

<h2 id="Text-Printing-Flags">Text Printing Flags</h2>

<p>As well as customising the name printing format, it is also possible to customise the actual fields printed using the <b>certopt</b> option when the <b>text</b> option is present. The default behaviour is to print all fields.</p>

<dl>

<dt id="compatible"><b>compatible</b></dt>
<dd>

<p>Use the old format. This is equivalent to specifying no printing options at all.</p>

</dd>
<dt id="no_header"><b>no_header</b></dt>
<dd>

<p>Don&#39;t print header information: that is the lines saying &quot;Certificate&quot; and &quot;Data&quot;.</p>

</dd>
<dt id="no_version"><b>no_version</b></dt>
<dd>

<p>Don&#39;t print out the version number.</p>

</dd>
<dt id="no_serial"><b>no_serial</b></dt>
<dd>

<p>Don&#39;t print out the serial number.</p>

</dd>
<dt id="no_signame"><b>no_signame</b></dt>
<dd>

<p>Don&#39;t print out the signature algorithm used.</p>

</dd>
<dt id="no_validity"><b>no_validity</b></dt>
<dd>

<p>Don&#39;t print the validity, that is the <b>notBefore</b> and <b>notAfter</b> fields.</p>

</dd>
<dt id="no_subject"><b>no_subject</b></dt>
<dd>

<p>Don&#39;t print out the subject name.</p>

</dd>
<dt id="no_issuer"><b>no_issuer</b></dt>
<dd>

<p>Don&#39;t print out the issuer name.</p>

</dd>
<dt id="no_pubkey"><b>no_pubkey</b></dt>
<dd>

<p>Don&#39;t print out the public key.</p>

</dd>
<dt id="no_sigdump"><b>no_sigdump</b></dt>
<dd>

<p>Don&#39;t give a hexadecimal dump of the certificate signature.</p>

</dd>
<dt id="no_aux"><b>no_aux</b></dt>
<dd>

<p>Don&#39;t print out certificate trust information.</p>

</dd>
<dt id="no_extensions"><b>no_extensions</b></dt>
<dd>

<p>Don&#39;t print out any X509V3 extensions.</p>

</dd>
<dt id="ext_default"><b>ext_default</b></dt>
<dd>

<p>Retain default extension behaviour: attempt to print out unsupported certificate extensions.</p>

</dd>
<dt id="ext_error"><b>ext_error</b></dt>
<dd>

<p>Print an error message for unsupported certificate extensions.</p>

</dd>
<dt id="ext_parse"><b>ext_parse</b></dt>
<dd>

<p>ASN1 parse unsupported extensions.</p>

</dd>
<dt id="ext_dump"><b>ext_dump</b></dt>
<dd>

<p>Hex dump unsupported extensions.</p>

</dd>
<dt id="ca_default"><b>ca_default</b></dt>
<dd>

<p>The value used by <a href="../man1/openssl-ca.html">openssl-ca(1)</a>, equivalent to <b>no_issuer</b>, <b>no_pubkey</b>, <b>no_header</b>, and <b>no_version</b>.</p>

</dd>
</dl>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Note: in these examples the &#39;\&#39; means the example should be all on one line.</p>

<p>Print the contents of a certificate:</p>

<pre><code>openssl x509 -in cert.pem -noout -text</code></pre>

<p>Print the &quot;Subject Alternative Name&quot; extension of a certificate:</p>

<pre><code>openssl x509 -in cert.pem -noout -ext subjectAltName</code></pre>

<p>Print more extensions of a certificate:</p>

<pre><code>openssl x509 -in cert.pem -noout -ext subjectAltName,nsCertType</code></pre>

<p>Print the certificate serial number:</p>

<pre><code>openssl x509 -in cert.pem -noout -serial</code></pre>

<p>Print the certificate subject name:</p>

<pre><code>openssl x509 -in cert.pem -noout -subject</code></pre>

<p>Print the certificate subject name in RFC2253 form:</p>

<pre><code>openssl x509 -in cert.pem -noout -subject -nameopt RFC2253</code></pre>

<p>Print the certificate subject name in oneline form on a terminal supporting UTF8:</p>

<pre><code>openssl x509 -in cert.pem -noout -subject -nameopt oneline,-esc_msb</code></pre>

<p>Print the certificate SHA1 fingerprint:</p>

<pre><code>openssl x509 -sha1 -in cert.pem -noout -fingerprint</code></pre>

<p>Convert a certificate from PEM to DER format:</p>

<pre><code>openssl x509 -in cert.pem -inform PEM -out cert.der -outform DER</code></pre>

<p>Convert a certificate to a certificate request:</p>

<pre><code>openssl x509 -x509toreq -in cert.pem -out req.pem -key key.pem</code></pre>

<p>Convert a certificate request into a self-signed certificate using extensions for a CA:</p>

<pre><code>openssl x509 -req -in careq.pem -extfile openssl.cnf -extensions v3_ca \
       -key key.pem -out cacert.pem</code></pre>

<p>Sign a certificate request using the CA certificate above and add user certificate extensions:</p>

<pre><code>openssl x509 -req -in req.pem -extfile openssl.cnf -extensions v3_usr \
       -CA cacert.pem -CAkey key.pem -CAcreateserial</code></pre>

<p>Set a certificate to be trusted for SSL client use and change set its alias to &quot;Steve&#39;s Class 1 CA&quot;</p>

<pre><code>openssl x509 -in cert.pem -addtrust clientAuth \
       -setalias &quot;Steve&#39;s Class 1 CA&quot; -out trust.pem</code></pre>

<h1 id="NOTES">NOTES</h1>

<p>The conversion to UTF8 format used with the name options assumes that T61Strings use the ISO8859-1 character set. This is wrong but Netscape and MSIE do this as do many certificates. So although this is incorrect it is more likely to print the majority of certificates correctly.</p>

<p>The <b>-email</b> option searches the subject name and the subject alternative name extension. Only unique email addresses will be printed out: it will not print the same address more than once.</p>

<h1 id="BUGS">BUGS</h1>

<p>It is possible to produce invalid certificates or requests by specifying the wrong private key, using unsuitable X.509 extensions, or using inconsistent options in some cases: these should be checked.</p>

<p>There should be options to explicitly set such things as start and end dates rather than an offset from the current time.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/openssl.html">openssl(1)</a>, <a href="../man1/openssl-req.html">openssl-req(1)</a>, <a href="../man1/openssl-ca.html">openssl-ca(1)</a>, <a href="../man1/openssl-genrsa.html">openssl-genrsa(1)</a>, <a href="../man1/openssl-gendsa.html">openssl-gendsa(1)</a>, <a href="../man1/openssl-verify.html">openssl-verify(1)</a>, <a href="../man5/x509v3_config.html">x509v3_config(5)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The hash algorithm used in the <b>-subject_hash</b> and <b>-issuer_hash</b> options before OpenSSL 1.0.0 was based on the deprecated MD5 algorithm and the encoding of the distinguished name. In OpenSSL 1.0.0 and later it is based on a canonical version of the DN using SHA1. This means that any directories using the old form must have their links rebuilt using <a href="../man1/openssl-rehash.html">openssl-rehash(1)</a> or similar.</p>

<p>The <b>-signkey</b> option has been renamed to <b>-key</b> in OpenSSL 3.0, keeping the old name as an alias.</p>

<p>The <b>-engine</b> option was deprecated in OpenSSL 3.0.</p>

<p>The <b>-C</b> option was removed in OpenSSL 3.0.</p>

<p>Since OpenSSL 3.2, generated certificates bear X.509 version 3, and key identifier extensions are included by default.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


