.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_export_pkcs11" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_export_pkcs11 \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_export_pkcs11(gnutls_privkey_t " pkey ", gnutls_pkcs11_privkey_t * " key ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t pkey" 12
The private key
.IP "gnutls_pkcs11_privkey_t * key" 12
Location for the key to be exported.
.SH "DESCRIPTION"
Converts the given abstract private key to a \fBgnutls_pkcs11_privkey_t\fP
type. The key must be of type \fBGNUTLS_PRIVKEY_PKCS11\fP. The key
returned in  \fIkey\fP must be deinitialized with
\fBgnutls_pkcs11_privkey_deinit()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
