.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hex_encode2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hex_encode2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_hex_encode2(const gnutls_datum_t * " data ", gnutls_datum_t * " result ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * data" 12
contain the raw data
.IP "gnutls_datum_t * result" 12
the result in an allocated string
.SH "DESCRIPTION"
This function will convert the given data to printable data, using
the hex encoding, as used in the PSK password files.

Note that the size of the result does NOT include the null terminator.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
