CMake 3.9 Release Notes
***********************

.. only:: html

  .. contents::

Changes made since CMake 3.8 include the following.

New Features
============

Languages
---------

* ``CUDA`` is now supported by the :ref:`Visual Studio Generators`
  for VS 2010 and above.  This complements the existing support by the
  :ref:`Makefile Generators` and the :generator:`Ninja` generator.
  CUDA 8.0.61 or higher is recommended due to known bugs in the VS
  integration by earlier versions.

* CMake is now aware of the :prop_tgt:`C++ standards <CXX_STANDARD>` and
  :prop_tgt:`C standards <C_STANDARD>` and their associated meta-features for
  the following :variable:`compiler ids <CMAKE_<LANG>_COMPILER_ID>`: ``Cray``,
  ``PGI``, and ``XL``.

Generators
----------

* :ref:`Visual Studio Generators` for VS 2010 and above learned to support
  the ``ASM_NASM`` language when ``nasm`` is installed.

* The :generator:`Xcode` generator learned to create Xcode schema files.
  This is an experimental feature and can be activated by setting the
  :variable:`CMAKE_XCODE_GENERATE_SCHEME` variable to a ``TRUE`` value.

Commands
--------

* The :command:`add_library` command ``IMPORTED`` option learned to support
  :ref:`Object Libraries`.

* The :command:`find_library` command learned to search ``libx32`` paths
  when the build targets the ``x32`` ABI.  See the
  :prop_gbl:`FIND_LIBRARY_USE_LIBX32_PATHS` global property.

* The :command:`include_external_msproject` command learned to use
  the :prop_tgt:`MAP_IMPORTED_CONFIG_<CONFIG>` target property
  to map current configurations to the external configurations.

* The :command:`install(TARGETS)` command learned a new ``OBJECTS`` option to
  specify where to install :ref:`Object Libraries`.

* The :command:`install(EXPORT)` command learned how to export
  :ref:`Object Libraries`.

* The :command:`project` command learned an optional ``DESCRIPTION``
  parameter to set the :variable:`PROJECT_DESCRIPTION` variable.

* The :command:`separate_arguments` command gained a ``NATIVE_COMMAND`` mode
  that performs argument separation depending on the host operating system.

Variables
---------

* A :variable:`CMAKE_ANDROID_NDK_DEPRECATED_HEADERS` variable was added
  for use when :ref:`Cross Compiling for Android with the NDK` to request
  use of the deprecated headers even when unified headers are available.
  The default is now to use unified headers if available.

* A :variable:`CMAKE_AUTOMOC_DEPEND_FILTERS` variable was introduced to
  allow :variable:`CMAKE_AUTOMOC` to extract additional dependency file names
  for ``moc`` from the contents of source files.

* A :variable:`CMAKE_AUTOUIC_SEARCH_PATHS` variable was introduced to
  allow :variable:`CMAKE_AUTOUIC` to search for ``foo.ui`` in more
  places than the vicinity of the file including ``ui_foo.h``.

* A :variable:`CMAKE_FIND_LIBRARY_CUSTOM_LIB_SUFFIX` variable was added to
  tell the :command:`find_library` command to search in a ``lib<suffix>``
  directory before each ``lib`` directory that would normally be searched.

* A :variable:`CMAKE_INTERPROCEDURAL_OPTIMIZATION` variable was added to
  initialize the :prop_tgt:`INTERPROCEDURAL_OPTIMIZATION` property on all
  targets.

* A :variable:`CMAKE_<LANG>_COMPILER_AR` variable was added to hold
  the path to the GCC/Clang wrapper of ``ar``.

* A :variable:`CMAKE_<LANG>_COMPILER_RANLIB` variable was added to hold
  the path to the GCC/Clang wrapper of ``ranlib``.

* The :variable:`CMAKE_SYSROOT_COMPILE` and :variable:`CMAKE_SYSROOT_LINK`
  variables were added to use separate sysroots for compiling and linking.

Properties
----------

* A new :prop_tgt:`AUTOGEN_BUILD_DIR` target property was introduced to set
  a custom output directory for :prop_tgt:`AUTOMOC`, :prop_tgt:`AUTOUIC`,
  and :prop_tgt:`AUTORCC`.

* A new :prop_tgt:`AUTOMOC_DEPEND_FILTERS` target property was introduced to
  allow :prop_tgt:`AUTOMOC` to extract additional dependency file names
  for ``moc`` from the contents of source files.

* A new :prop_tgt:`AUTOUIC_SEARCH_PATHS` target property was introduced to
  allow :prop_tgt:`AUTOUIC` to search for ``foo.ui`` in more
  places than the vicinity of the file including ``ui_foo.h``.

* Global properties :prop_gbl:`AUTOGEN_SOURCE_GROUP`,
  :prop_gbl:`AUTOMOC_SOURCE_GROUP` and
  :prop_gbl:`AUTORCC_SOURCE_GROUP` were
  introduced to allow files generated by :prop_tgt:`AUTOMOC` or
  :prop_tgt:`AUTORCC` to be placed in a :command:`source_group`.

* A :prop_tgt:`BUILD_WITH_INSTALL_NAME_DIR` target property and corresponding
  :variable:`CMAKE_BUILD_WITH_INSTALL_NAME_DIR` variable were added to
  control whether to use the :prop_tgt:`INSTALL_NAME_DIR` target property
  value for binaries in the build tree.  This is for macOS ``install_name``
  as :prop_tgt:`BUILD_WITH_INSTALL_RPATH` is for ``RPATH``.

* A :prop_tgt:`CUDA_PTX_COMPILATION` target property was added to
  :ref:`Object Libraries` to support compiling to ``.ptx`` files
  instead of host object files.

* A :prop_gbl:`GENERATOR_IS_MULTI_CONFIG` global property was
  added to determine whether the current generator is a multi-configuration
  generator (such as :ref:`Visual Studio Generators` or :generator:`Xcode`).

* The :prop_tgt:`INTERPROCEDURAL_OPTIMIZATION` target property is now enforced
  when enabled.  CMake will add IPO flags unconditionally or produce an error
  if it does not know the flags for the current compiler.  The project is now
  responsible to use the :module:`CheckIPOSupported` module to check for IPO
  support before enabling the target property.  See policy :policy:`CMP0069`.

* The :prop_tgt:`WINDOWS_EXPORT_ALL_SYMBOLS` target property may now
  be used in combination with explicit ``.def`` files in order to
  export all symbols from the object files within a target plus
  an explicit list of symbols that the linker finds in dependencies
  (e.g. ``msvcrt.lib``).

Modules
-------

* A :module:`CheckIPOSupported` module was added to help projects
  check whether interprocedural optimization (IPO) is supported by
  the current toolchain and CMake version.

* The :module:`CMakeFindDependencyMacro` module ``find_dependency`` macro
  now forwards all arguments to the underlying :command:`find_package`
  call.  Existing uses will continue to function as before, but callers can
  now access the full suite of arguments that :command:`find_package` accepts.

* The :module:`FeatureSummary` module :command:`feature_summary` command now
  accepts the new ``DEFAULT_DESCRIPTION`` option that will print the default
  title for the selected package type.

* The :module:`FeatureSummary` module gained a new
  :variable:`FeatureSummary_<TYPE>_DESCRIPTION` variable that can be defined
  for each ``<TYPE>`` to replace the type name with the specified string
  whenever the package type is used in an output string by the module.

* The :module:`FindDoxygen` module learned to control Doxygen behavior using
  CMake variables and generate documentation via the newly added
  :command:`doxygen_add_docs` function. The Doxygen input file (``Doxyfile``)
  is automatically generated and doxygen is run as part of a custom target.
  Additional components can be specified to find optional tools: ``dot``,
  ``mscgen`` and ``dia``.

* The :module:`FindMPI` module now provides imported targets.

* The :module:`FindProtobuf` module :command:`protobuf_generate_cpp`
  command gained an ``EXPORT_MACRO`` option to specify the name of
  a DLL export markup macro.

* The :module:`FindProtobuf` module now supports usage of static libraries
  for Unix via a new ``Protobuf_USE_STATIC_LIBS`` input variable.

* The :module:`FindProtobuf` module now provides imported targets
  when the libraries are found.

* A new :module:`GoogleTest` module was added to provide the
  :command:`gtest_add_tests` function independently of the :module:`FindGTest`
  module. The function was also updated to support keyword arguments, with
  functionality expanded to allow a test name prefix and suffix to be
  specified, the dependency on the source files to be optional and the list of
  discovered test cases to be returned to the caller.

CTest
-----

* The :command:`ctest_submit` command gained a ``HTTPHEADER`` option
  to specify custom headers to send during submission.

* The :manual:`ctest(1)` executable gained new options which allow the
  developer to disable automatically adding tests to the test set to satisfy
  fixture dependencies. ``-FS`` prevents adding setup tests for fixtures
  matching the provided regular expression, ``-FC`` prevents adding cleanup
  tests for matching fixtures and ``-FA`` prevents adding any test for matching
  fixtures.

* A :prop_test:`DISABLED` test property was added to mark tests that
  are configured but explicitly disabled so they do not run.

CPack
-----

* The :cpack_gen:`CPack Archive Generator` learned to modify the filename
  per-component.  See the :variable:`CPACK_ARCHIVE_FILE_NAME` variable and
  its per-component version :variable:`CPACK_ARCHIVE_<component>_FILE_NAME`.

* The :module:`CPackComponent` module :command:`cpack_add_component` command
  gained a new ``PLIST <filename>`` option to specify the ``pkgbuild``
  ``--component-plist`` argument when using the
  :module:`productbuild <CPackProductBuild>` generator.

* The :module:`CPackIFW` module :command:`cpack_ifw_configure_component` and
  :command:`cpack_ifw_configure_component_group` commands gained
  internationalization support for ``DISPLAY_NAME`` and ``DESCRIPTION``
  options.

* The :cpack_gen:`CPack IFW Generator` learned the new hint
  :variable:`CPACK_IFW_ROOT` variable for finding the QtIFW tool suite
  installed in a non-standard place.

* The :cpack_gen:`CPack productbuild Generator` gained a new
  :variable:`CPACK_PRODUCTBUILD_RESOURCES_DIR` variable to
  specify resources to be copied into the ``Resources``
  directory.

* The :cpack_gen:`CPack RPM Generator` learned to modify the ``debuginfo``
  package name.  See the :variable:`CPACK_RPM_DEBUGINFO_FILE_NAME` variable.

* The :cpack_gen:`CPack WIX Generator` patching system now has the
  ability to set additional attributes.  This can be done by specifying
  attributes with the ``CPackWiXFragment`` XML tag after the ``Id`` attribute.
  See the :variable:`CPACK_WIX_PATCH_FILE` variable.

* The :cpack_gen:`CPack WIX Generator` implemented a new
  :variable:`CPACK_WIX_ROOT_FOLDER_ID` variable which allows
  using a custom root folder ID instead of the default
  ``ProgramFilesFolder`` / ``ProgramFiles64Folder``.

Other
-----

* Interprocedural optimization (IPO) is now supported for GNU and Clang
  compilers using link time optimization (LTO) flags.  See the
  :prop_tgt:`INTERPROCEDURAL_OPTIMIZATION` target property and
  :module:`CheckIPOSupported` module.

* The ``TARGET_OBJECTS``
  :manual:`generator expression <cmake-generator-expressions(7)>`
  is now supported by the :command:`add_custom_command` and
  :command:`file(GENERATE)` commands.

* Two new informational generator expressions to retrieve Apple Bundle
  directories have been added. The first one :genex:`$<TARGET_BUNDLE_DIR:tgt>`
  outputs the full path to the Bundle directory, the other one
  :genex:`$<TARGET_BUNDLE_CONTENT_DIR:tgt>` outputs the full path to the
  ``Contents`` directory of macOS Bundles and App Bundles. For all other
  bundle types and SDKs it is identical with :genex:`$<TARGET_BUNDLE_DIR:tgt>`.
  The new expressions are helpful to query Bundle locations independent of
  the different Bundle types and layouts on macOS and iOS.

Deprecated and Removed Features
===============================

* An explicit deprecation diagnostic was added for policies ``CMP0036``
  and below.  The :manual:`cmake-policies(7)` manual explains that the
  OLD behaviors of all policies are deprecated and that projects should
  always port to the NEW behaviors as soon as possible.

* The :generator:`Visual Studio 8 2005` generator is now deprecated
  and will be removed in a future version of CMake.

* The :generator:`Visual Studio 7 .NET 2003` generator has been removed.

* The :generator:`Xcode` generator dropped support for Xcode versions
  older than 3.

* The :module:`FindDoxygen` module has deprecated several variables.

* The version of curl bundled with CMake no longer accepts URLs of the form
  ``file://c:/...`` on Windows due to a change in upstream curl 7.52.  Use
  the form ``file:///c:/...`` instead to work on all versions.

Other Changes
=============

* When using :prop_tgt:`AUTOMOC`, CMake now scans for the presence of the
  ``Q_PLUGIN_METADATA`` macro and reruns moc when the file from the
  macro's ``FILE`` argument changes.

* When :prop_tgt:`AUTOMOC` detects an include statement of the form
  ``#include "moc_<basename>.cpp"`` the search for the respective header file
  now looks in the :prop_tgt:`INCLUDE_DIRECTORIES` of the target as well.

* When running tests, CTest learned to treat skipped tests (using the
  :prop_test:`SKIP_RETURN_CODE` property) the same as tests with the new
  :prop_test:`DISABLED` property. Due to this change, CTest will not indicate
  failure when all tests are either skipped or pass.

* The :generator:`Ninja` generator has loosened the dependencies of object
  compilation.  Object compilation now depends only on custom targets
  and custom commands associated with libraries on which the object's target
  depends and no longer depends on the libraries themselves.  Source files
  in dependent targets may now compile without waiting for their targets'
  dependencies to link.

* On macOS, ``RPATH`` settings such as :prop_tgt:`BUILD_WITH_INSTALL_RPATH`
  no longer affect the ``install_name`` field.  See policy :policy:`CMP0068`.

* The :generator:`Visual Studio 14 2015` generator has been taught about
  a change to the ``v140`` toolset made by a VS 2015 update.  VS changed
  the set of values it understands for the ``GenerateDebugInformation``
  linker setting that produces the ``-DEBUG`` linker flag variants.

Updates
=======

Changes made since CMake 3.9.0 include the following.

3.9.1
-----

* The ``find_`` command ``PACKAGE_ROOT`` search path group added by
  CMake 3.9.0 has been removed for the 3.9 series due to regressions
  caused by new use of ``<PackageName>_ROOT`` variables.  The behavior
  may be re-introduced in the future in a more-compatible way.

3.9.2
-----

* On macOS, the default application bundle ``Info.plist`` file no longer
  enables Hi-DPI support as it did in 3.9.0 and 3.9.1.  The change had
  to be reverted because it broke iOS applications.

* The Xcode generator no longer adds "outputPaths" to custom script
  build phases as it did in 3.9.0 and 3.9.1.  This was added in an
  attempt to support Xcode 9's new build system, but broke incremental
  rebuilds for both the old and new Xcode build systems.
