.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_generate2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_generate2 \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_privkey_generate2(gnutls_x509_privkey_t " key ", gnutls_pk_algorithm_t " algo ", unsigned int " bits ", unsigned int " flags ", const gnutls_keygen_data_st * " data ", unsigned " data_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t key" 12
a key
.IP "gnutls_pk_algorithm_t algo" 12
is one of the algorithms in \fBgnutls_pk_algorithm_t\fP.
.IP "unsigned int bits" 12
the size of the modulus
.IP "unsigned int flags" 12
Must be zero or flags from \fBgnutls_privkey_flags_t\fP.
.IP "const gnutls_keygen_data_st * data" 12
Allow specifying \fBgnutls_keygen_data_st\fP types such as the seed to be used.
.IP "unsigned data_size" 12
The number of  \fIdata\fP available.
.SH "DESCRIPTION"
This function will generate a random private key. Note that this
function must be called on an initialized private key.

The flag \fBGNUTLS_PRIVKEY_FLAG_PROVABLE\fP
instructs the key generation process to use algorithms like Shawe\-Taylor
(from FIPS PUB186\-4) which generate provable parameters out of a seed
for RSA and DSA keys. On DSA keys the PQG parameters are generated using the
seed, while on RSA the two primes. To specify an explicit seed
(by default a random seed is used), use the  \fIdata\fP with a \fBGNUTLS_KEYGEN_SEED\fP
type.

Note that when generating an elliptic curve key, the curve
can be substituted in the place of the bits parameter using the
\fBGNUTLS_CURVE_TO_BITS()\fP macro.

To export the generated keys in memory or in files it is recommended to use the
PKCS\fB8\fP form as it can handle all key types, and can store additional parameters
such as the seed, in case of provable RSA or DSA keys.
Generated keys can be exported in memory using \fBgnutls_privkey_export_x509()\fP,
and then with \fBgnutls_x509_privkey_export2_pkcs8()\fP.

If key generation is part of your application, avoid setting the number
of bits directly, and instead use \fBgnutls_sec_param_to_pk_bits()\fP.
That way the generated keys will adapt to the security levels
of the underlying GnuTLS library.

See also \fBgnutls_privkey_generate2()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
