.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_token_get_mechanism" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_token_get_mechanism \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_token_get_mechanism(const char * " url ", unsigned int " idx ", unsigned long * " mechanism ");"
.SH ARGUMENTS
.IP "const char * url" 12
should contain a PKCS 11 URL
.IP "unsigned int idx" 12
The index of the mechanism
.IP "unsigned long * mechanism" 12
The PKCS \fB11\fP mechanism ID
.SH "DESCRIPTION"
This function will return the names of the supported mechanisms
by the token. It should be called with an increasing index until
it return GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success or a negative error code on error.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
