<!-- Creator     : groff version 1.22.3 -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<meta name="Content-Style" content="text/css">
<style type="text/css">
       p       { margin-top: 0; margin-bottom: 0; vertical-align: top }
       pre     { margin-top: 0; margin-bottom: 0; vertical-align: top }
       table   { margin-top: 0; margin-bottom: 0; vertical-align: top }
       h1      { text-align: center }
</style>
<title>ICONV</title>

</head>
<body>

<h1 align="center">ICONV</h1>

<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#RETURN VALUE">RETURN VALUE</a><br>
<a href="#ERRORS">ERRORS</a><br>
<a href="#CONFORMING TO">CONFORMING TO</a><br>
<a href="#NOTES">NOTES</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>


<h2>NAME
<a name="NAME"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">iconv &minus;
perform character set conversion</p>

<h2>SYNOPSIS
<a name="SYNOPSIS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em"><b>#include
&lt;iconv.h&gt;</b></p>

<p style="margin-left:11%; margin-top: 1em"><b>size_t iconv
(iconv_t</b> <i>cd</i><b>, <br>
const char* *</b> <i>inbuf</i><b>, size_t *</b>
<i>inbytesleft</i><b>, <br>
char* *</b> <i>outbuf</i><b>, size_t *</b>
<i>outbytesleft</i><b>);</b></p>

<h2>DESCRIPTION
<a name="DESCRIPTION"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The argument
<i>cd</i> must be a conversion descriptor created using the
function <b>iconv_open</b>.</p>

<p style="margin-left:11%; margin-top: 1em">The main case
is when <i>inbuf</i> is not NULL and <i>*inbuf</i> is not
NULL. In this case, the <b>iconv</b> function converts the
multibyte sequence starting at <i>*inbuf</i> to a multibyte
sequence starting at <i>*outbuf</i>. At most
<i>*inbytesleft</i> bytes, starting at <i>*inbuf</i>, will
be read. At most <i>*outbytesleft</i> bytes, starting at
<i>*outbuf</i>, will be written.</p>

<p style="margin-left:11%; margin-top: 1em">The
<b>iconv</b> function converts one multibyte character at a
time, and for each character conversion it increments
<i>*inbuf</i> and decrements <i>*inbytesleft</i> by the
number of converted input bytes, it increments
<i>*outbuf</i> and decrements <i>*outbytesleft</i> by the
number of converted output bytes, and it updates the
conversion state contained in <i>cd</i>. If the character
encoding of the input is stateful, the <b>iconv</b> function
can also convert a sequence of input bytes to an update of
the conversion state without producing any output bytes;
such input is called a <i>shift sequence</i>. The conversion
can stop for five reasons:</p>

<p style="margin-left:11%; margin-top: 1em">1. An invalid
multibyte sequence is encountered in the input. In this case
it sets <b>errno</b> to <b>EILSEQ</b> and returns
(size_t)(&minus;1). <i>*inbuf</i> is left pointing to the
beginning of the invalid multibyte sequence.</p>

<p style="margin-left:11%; margin-top: 1em">2. A multibyte
sequence is encountered that is valid but that cannot be
translated to the character encoding of the output. This
condition depends on the implementation and on the
conversion descriptor. In the GNU C library and GNU
libiconv, if <i>cd</i> was created without the suffix
<b>//TRANSLIT</b> or <b>//IGNORE</b> or
<b>//NON_IDENTICAL_DISCARD</b>, the conversion is strict:
lossy conversions produce this condition. If the suffix
<b>//TRANSLIT</b> was specified, transliteration can avoid
this condition in some cases. In the musl C library, this
condition cannot occur because a conversion to <b>'*'</b> is
used as a fallback. In the FreeBSD, NetBSD, and Solaris
implementations of <b>iconv</b>, this condition cannot occur
either, because a conversion to <b>'?'</b> is used as a
fallback. When this condition is met, the <b>iconv</b>
function sets <b>errno</b> to <b>EILSEQ</b> and returns
(size_t)(&minus;1). <i>*inbuf</i> is left pointing to the
beginning of the unconvertible multibyte sequence.</p>

<p style="margin-left:11%; margin-top: 1em">3. The input
byte sequence has been entirely converted, i.e.
<i>*inbytesleft</i> has gone down to 0. In this case
<b>iconv</b> returns the number of non-reversible
conversions performed during this call.</p>

<p style="margin-left:11%; margin-top: 1em">4. An
incomplete multibyte sequence is encountered in the input,
and the input byte sequence terminates after it. In this
case it sets <b>errno</b> to <b>EINVAL</b> and returns
(size_t)(&minus;1). <i>*inbuf</i> is left pointing to the
beginning of the incomplete multibyte sequence.</p>

<p style="margin-left:11%; margin-top: 1em">5. The output
buffer has no more room for the next converted character. In
this case it sets <b>errno</b> to <b>E2BIG</b> and returns
(size_t)(&minus;1).</p>

<p style="margin-left:11%; margin-top: 1em">A different
case is when <i>inbuf</i> is NULL or <i>*inbuf</i> is NULL,
but <i>outbuf</i> is not NULL and <i>*outbuf</i> is not
NULL. In this case, the <b>iconv</b> function attempts to
set <i>cd</i>&rsquo;s conversion state to the initial state
and store a corresponding shift sequence at <i>*outbuf</i>.
At most <i>*outbytesleft</i> bytes, starting at
<i>*outbuf</i>, will be written. If the output buffer has no
more room for this reset sequence, it sets <b>errno</b> to
<b>E2BIG</b> and returns (size_t)(&minus;1). Otherwise it
increments <i>*outbuf</i> and decrements
<i>*outbytesleft</i> by the number of bytes written.</p>

<p style="margin-left:11%; margin-top: 1em">A third case is
when <i>inbuf</i> is NULL or <i>*inbuf</i> is NULL, and
<i>outbuf</i> is NULL or <i>*outbuf</i> is NULL. In this
case, the <b>iconv</b> function sets <i>cd</i>&rsquo;s
conversion state to the initial state.</p>

<h2>RETURN VALUE
<a name="RETURN VALUE"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The
<b>iconv</b> function returns the number of characters
converted in a non-reversible way during this call;
reversible conversions are not counted. In case of error, it
sets <b>errno</b> and returns (size_t)(&minus;1).</p>

<h2>ERRORS
<a name="ERRORS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The following
errors can occur, among others:</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p><b>E2BIG</b></p></td>
<td width="2%"></td>
<td width="78%">


<p>There is not sufficient room at <i>*outbuf</i>.</p></td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p><b>EILSEQ</b></p></td>
<td width="2%"></td>
<td width="78%">


<p>An invalid multibyte sequence has been encountered in
the input.</p></td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p><b>EINVAL</b></p></td>
<td width="2%"></td>
<td width="78%">


<p>An incomplete multibyte sequence has been encountered in
the input.</p></td></tr>
</table>

<h2>CONFORMING TO
<a name="CONFORMING TO"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">POSIX:2024</p>

<h2>NOTES
<a name="NOTES"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">In each series
of calls to the <b>iconv</b> function, the last should be
one with <i>inbuf</i> or <i>*inbuf</i> equal to NULL, in
order to complete the conversion of any partially converted
input.</p>

<p style="margin-left:11%; margin-top: 1em">Although
<i>inbuf</i> and <i>outbuf</i> are typed as <b>const char
**</b> and <b>char **</b>, respectively, this does not mean
that the objects they point can be interpreted as C strings
or as arrays of characters: the interpretation of character
byte sequences is handled internally by the conversion
functions. In some encodings, a zero byte may be a valid
part of a multibyte character.</p>

<p style="margin-left:11%; margin-top: 1em">The caller of
the <b>iconv</b> function must ensure that the pointers
passed to the function are suitable for accessing characters
in the appropriate character set. For the encodings
<b>UCS-2-INTERNAL</b>, <b>UCS-4-INTERNAL</b>, and
<b>wchar_t</b>, this includes ensuring correct
alignment.</p>

<h2>SEE ALSO
<a name="SEE ALSO"></a>
</h2>



<p style="margin-left:11%; margin-top: 1em"><b>iconv_open</b>(3),
<b>iconvctl</b>(3), <b>iconv_close</b>(3)</p>
<hr>
</body>
</html>
