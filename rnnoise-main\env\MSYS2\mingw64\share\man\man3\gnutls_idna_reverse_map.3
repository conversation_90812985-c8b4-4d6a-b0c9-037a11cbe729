.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_idna_reverse_map" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_idna_reverse_map \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_idna_reverse_map(const char * " input ", unsigned " ilen ", gnutls_datum_t * " out ", unsigned " flags ");"
.SH ARGUMENTS
.IP "const char * input" 12
contain the ACE (IDNA) formatted domain name
.IP "unsigned ilen" 12
the length of the provided string
.IP "gnutls_datum_t * out" 12
the result in an null\-terminated allocated UTF\-8 string
.IP "unsigned flags" 12
should be zero
.SH "DESCRIPTION"
This function will convert an ACE (ASCII\-encoded) domain name to a UTF\-8 domain name.

If GnuTLS is compiled without IDNA support, then this function
will return \fBGNUTLS_E_UNIMPLEMENTED_FEATURE\fP.

Note also, that this function will return an empty string if an
empty string is provided as input.
.SH "RETURNS"
A negative error code on error, or 0 on success.
.SH "SINCE"
3.5.8
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
