.de1 NOP
.  it 1 an-trap
.  if \\n[.$] \,\\$*\/
..
.ie t \
.ds B-Font [CB]
.ds I-Font [CI]
.ds R-Font [CR]
.el \
.ds B-Font B
.ds I-Font I
.ds R-Font R
.TH certtool 1 "08 Feb 2025" "3.8.9" "User Commands"
.SH NAME
\f\*[B-Font]certtool\fP
\- GnuTLS certificate tool
.SH SYNOPSIS
\f\*[B-Font]certtool\fP
.\" Mixture of short (flag) options and long options
[\f\*[B-Font]\-flags\f[]]
[\f\*[B-Font]\-flag\f[] [\f\*[I-Font]value\f[]]]
[\f\*[B-Font]\-\-option-name\f[][[=| ]\f\*[I-Font]value\f[]]]
.sp \n(Ppu
.ne 2

All arguments must be options.
.sp \n(Ppu
.ne 2
.SH "DESCRIPTION"
Tool to parse and generate X.509 certificates, requests and private keys.
It can be used interactively or non interactively by
specifying the template command line option.
.sp
The tool accepts files or supported URIs via the --infile option. In case PIN
is required for URI access you can provide it using the environment variables GNUTLS_PIN 
and GNUTLS_SO_PIN.
.sp
.SH "OPTIONS"
.TP
.NOP \f\*[B-Font]\-d\f[] \f\*[I-Font]num\f[], \f\*[B-Font]\-\-debug\f[]=\f\*[I-Font]num\f[]
Enable debugging.
This option takes an integer number as its argument.
The value of
\f\*[I-Font]num\f[]
is constrained to being:
.in +4
.nf
.na
in the range 0 through 9999
.fi
.in -4
.sp
Specifies the debug level.
.TP
.NOP \f\*[B-Font]\-V\f[], \f\*[B-Font]\-\-verbose\f[]
More verbose output.
.sp
.TP
.NOP \f\*[B-Font]\-\-infile\f[]=\f\*[I-Font]file\f[]
Input file.
.sp
.TP
.NOP \f\*[B-Font]\-\-outfile\f[]=\f\*[I-Font]str\f[]
Output file.
.sp
.TP
.NOP \f\*[B-Font]\-\-attime\f[]=\f\*[I-Font]timestamp\f[]
Perform validation at the timestamp instead of the system time.
.sp
timestamp is an instance in time encoded as Unix time or in a human
 readable timestring such as "29 Feb 2004", "2004\-02\-29".
Full documentation available at 
<https://www.gnu.org/software/coreutils/manual/html_node/Date\-input\-formats.html>
or locally via info '(coreutils) date invocation'.
.SS "Certificate related options"
.TP
.NOP \f\*[B-Font]\-i\f[], \f\*[B-Font]\-\-certificate\-info\f[]
Print information on the given certificate.
.sp
.TP
.NOP \f\*[B-Font]\-\-pubkey\-info\f[]
Print information on a public key.
.sp
The option combined with \-\-load\-request, \-\-load\-pubkey, \-\-load\-privkey and \-\-load\-certificate will extract the public key of the object in question.
.TP
.NOP \f\*[B-Font]\-s\f[], \f\*[B-Font]\-\-generate\-self\-signed\f[]
Generate a self-signed certificate.
.sp
.TP
.NOP \f\*[B-Font]\-c\f[], \f\*[B-Font]\-\-generate\-certificate\f[]
Generate a signed certificate.
.sp
.TP
.NOP \f\*[B-Font]\-\-generate\-proxy\f[]
Generates a proxy certificate.
.sp
.TP
.NOP \f\*[B-Font]\-u\f[], \f\*[B-Font]\-\-update\-certificate\f[]
Update a signed certificate.
.sp
.TP
.NOP \f\*[B-Font]\-\-fingerprint\f[]
Print the fingerprint of the given certificate.
.sp
This is a simple hash of the DER encoding of the certificate. It can be combined with the \-\-hash parameter. However, it is recommended for identification to use the key\-id which depends only on the certificate's key.
.TP
.NOP \f\*[B-Font]\-\-key\-id\f[]
Print the key ID of the given certificate.
.sp
This is a hash of the public key of the given certificate. It identifies the key uniquely, remains the same on a certificate renewal and depends only on signed fields of the certificate.
.TP
.NOP \f\*[B-Font]\-\-certificate\-pubkey\f[]
Print certificate's public key.
.sp
This option is deprecated as a duplicate of \-\-pubkey\-info
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-\-v1\f[]
Generate an X.509 version 1 certificate (with no extensions).
.sp
.TP
.NOP \f\*[B-Font]\-\-sign\-params\f[]=\f\*[I-Font]str\f[]
Sign a certificate with a specific signature algorithm.
.sp
This option can be combined with \-\-generate\-certificate, to sign the certificate with
a specific signature algorithm variant. The only option supported is 'RSA\-PSS', and should be
specified when the signer does not have a certificate which is marked for RSA\-PSS use only.
.SS "Certificate request related options"
.TP
.NOP \f\*[B-Font]\-\-crq\-info\f[]
Print information on the given certificate request.
.sp
.TP
.NOP \f\*[B-Font]\-q\f[], \f\*[B-Font]\-\-generate\-request\f[]
Generate a PKCS #10 certificate request.
This option must not appear in combination with any of the following options:
infile.
.sp
Will generate a PKCS #10 certificate request. To specify a private key use \-\-load\-privkey.
.TP
.NOP \f\*[B-Font]\-\-no\-crq\-extensions\f[]
Do not use extensions in certificate requests.
.sp
.SS "PKCS#12 file related options"
.TP
.NOP \f\*[B-Font]\-\-p12\-info\f[]
Print information on a PKCS #12 structure.
.sp
This option will dump the contents and print the metadata of the provided PKCS #12 structure.
.TP
.NOP \f\*[B-Font]\-\-p12\-name\f[]=\f\*[I-Font]str\f[]
The PKCS #12 friendly name to use.
.sp
The name to be used for the primary certificate and private key in a PKCS #12 file.
.TP
.NOP \f\*[B-Font]\-\-to\-p12\f[]
Generate a PKCS #12 structure.
.sp
It requires a certificate, a private key and possibly a CA certificate to be specified.
.TP
.NOP \f\*[B-Font]\-\-pbmac1\f[]
Use PBMAC1 in a PKCS #12 structure.
.sp
.SS "Private key related options"
.TP
.NOP \f\*[B-Font]\-k\f[], \f\*[B-Font]\-\-key\-info\f[]
Print information on a private key.
.sp
.TP
.NOP \f\*[B-Font]\-\-p8\-info\f[]
Print information on a PKCS #8 structure.
.sp
This option will print information about encrypted PKCS #8 structures. That option does not require the decryption of the structure.
.TP
.NOP \f\*[B-Font]\-\-to\-rsa\f[]
Convert an RSA-PSS key to raw RSA format.
.sp
It requires an RSA\-PSS key as input and will output a raw RSA
key. This command is necessary for compatibility with applications that
cannot read RSA\-PSS keys.
.TP
.NOP \f\*[B-Font]\-p\f[], \f\*[B-Font]\-\-generate\-privkey\f[]
Generate a private key.
.sp
When generating RSA\-PSS or RSA\-OAEP private keys, the \-\-hash option will
restrict the allowed hash for the key; For RSA\-PSS keys the \-\-salt\-size
option is also acceptable.
.TP
.NOP \f\*[B-Font]\-\-key\-type\f[]=\f\*[I-Font]str\f[]
Specify the key type to use on key generation.
.sp
This option can be combined with \-\-generate\-privkey, to specify
the key type to be generated. Valid options are, 'rsa', 'rsa\-pss', 'rsa\-oaep', 'dsa', 'ecdsa', 'ed25519, 'ed448', 'x25519', and 'x448'.'.
When combined with certificate generation it can be used to specify an
RSA\-PSS certificate when an RSA key is given.
.TP
.NOP \f\*[B-Font]\-\-bits\f[]=\f\*[I-Font]num\f[]
Specify the number of bits for key generation.
This option takes an integer number as its argument.
.sp
.TP
.NOP \f\*[B-Font]\-\-curve\f[]=\f\*[I-Font]str\f[]
Specify the curve used for EC key generation.
.sp
Supported values are secp192r1, secp224r1, secp256r1, secp384r1 and secp521r1.
.TP
.NOP \f\*[B-Font]\-\-sec\-param\f[]=\f\*[I-Font]security parameter\f[]
Specify the security level [low, legacy, medium, high, ultra].
.sp
This is alternative to the bits option.
.TP
.NOP \f\*[B-Font]\-\-to\-p8\f[]
Convert a given key to a PKCS #8 structure.
.sp
This needs to be combined with \-\-load\-privkey.
.TP
.NOP \f\*[B-Font]\-8\f[], \f\*[B-Font]\-\-pkcs8\f[]
Use PKCS #8 format for private keys.
.sp
.TP
.NOP \f\*[B-Font]\-\-provable\f[]
Generate a private key or parameters from a seed using a provable method.
.sp
This will use the FIPS PUB186\-4 algorithms (i.e., Shawe\-Taylor) for provable key generation.
When specified the private keys or parameters will be generated from a seed, and can be
later validated with \-\-verify\-provable\-privkey to be correctly generated from the seed. You may
specify \-\-seed or allow GnuTLS to generate one (recommended). This option can be combined with
\-\-generate\-privkey or \-\-generate\-dh\-params.
.sp
That option applies to RSA and DSA keys. On the DSA keys the PQG parameters
are generated using the seed, and on RSA the two primes.
.TP
.NOP \f\*[B-Font]\-\-verify\-provable\-privkey\f[]
Verify a private key generated from a seed using a provable method.
.sp
This will use the FIPS\-186\-4 algorithms for provable key generation. You may specify \-\-seed or use the seed stored in the private key structure.
.TP
.NOP \f\*[B-Font]\-\-seed\f[]=\f\*[I-Font]str\f[]
When generating a private key use the given hex-encoded seed.
.sp
The seed acts as a security parameter for the private key, and
thus a seed size which corresponds to the security level of the private key
should be provided (e.g., 256\-bits seed).
.SS "CRL related options"
.TP
.NOP \f\*[B-Font]\-l\f[], \f\*[B-Font]\-\-crl\-info\f[]
Print information on the given CRL structure.
.sp
.TP
.NOP \f\*[B-Font]\-\-generate\-crl\f[]
Generate a CRL.
.sp
This option generates a Certificate Revocation List. When combined with \-\-load\-crl it would use the loaded CRL as base for the generated (i.e., all revoked certificates in the base will be copied to the new CRL).
To add new certificates to the CRL use \-\-load\-certificate.
.TP
.NOP \f\*[B-Font]\-\-verify\-crl\f[]
Verify a Certificate Revocation List using a trusted list.
This option must appear in combination with the following options:
load-ca-certificate.
.sp
The trusted certificate list must be loaded with \-\-load\-ca\-certificate.
.SS "Certificate verification related options"
.TP
.NOP \f\*[B-Font]\-e\f[], \f\*[B-Font]\-\-verify\-chain\f[]
Verify a PEM encoded certificate chain.
.sp
Verifies the validity of a certificate chain. That is, an ordered set of
certificates where each one is the issuer of the previous, and the first is
the end\-certificate to be validated. In a proper chain the last certificate
is a self signed one. It can be combined with \-\-verify\-purpose or \-\-verify\-hostname.
.TP
.NOP \f\*[B-Font]\-\-verify\f[]
Verify a PEM encoded certificate (chain) against a trusted set.
.sp
The trusted certificate list can be loaded with \-\-load\-ca\-certificate. If no
certificate list is provided, then the system's trusted certificate list is used. Note that
during verification multiple paths may be explored. On a successful verification
the successful path will be the last one. It can be combined with \-\-verify\-purpose or \-\-verify\-hostname.
.TP
.NOP \f\*[B-Font]\-\-verify\-hostname\f[]=\f\*[I-Font]str\f[]
Specify a hostname to be used for certificate chain verification.
.sp
This is to be combined with one of the verify certificate options.
.TP
.NOP \f\*[B-Font]\-\-verify\-email\f[]=\f\*[I-Font]str\f[]
Specify a email to be used for certificate chain verification.
This option must not appear in combination with any of the following options:
verify-hostname.
.sp
This is to be combined with one of the verify certificate options.
.TP
.NOP \f\*[B-Font]\-\-verify\-purpose\f[]=\f\*[I-Font]str\f[]
Specify a purpose OID to be used for certificate chain verification.
.sp
This object identifier restricts the purpose of the certificates to be verified. Example purposes are *******.*******.1 (TLS WWW), *******.*******.4 (EMAIL) etc. Note that a CA certificate without a purpose set (extended key usage) is valid for any purpose.
.TP
.NOP \f\*[B-Font]\-\-verify\-allow\-broken\f[]
Allow broken algorithms, such as MD5 for verification.
.sp
This can be combined with \-\-p7\-verify, \-\-verify or \-\-verify\-chain.
.TP
.NOP \f\*[B-Font]\-\-verify\-profile\f[]=\f\*[I-Font]str\f[]
Specify a security level profile to be used for verification.
.sp
This option can be used to specify a certificate verification profile. Certificate
    verification profiles correspond to the security level. This should be one of
    'none', 'very weak', 'low', 'legacy', 'medium', 'high', 'ultra',
    'future'. Note that by default no profile is applied, unless one is set
    as minimum in the gnutls configuration file.
.SS "PKCS#7 structure options"
.TP
.NOP \f\*[B-Font]\-\-p7\-generate\f[]
Generate a PKCS #7 structure.
.sp
This option generates a PKCS #7 certificate container structure. To add certificates in the structure use \-\-load\-certificate and \-\-load\-crl.
.TP
.NOP \f\*[B-Font]\-\-p7\-sign\f[]
Signs using a PKCS #7 structure.
.sp
This option generates a PKCS #7 structure containing a signature for the provided data from infile. The data are stored within the structure. The signer certificate has to be specified using \-\-load\-certificate and \-\-load\-privkey. The input to \-\-load\-certificate can be a list of certificates. In case of a list, the first certificate is used for signing and the other certificates are included in the structure.
.TP
.NOP \f\*[B-Font]\-\-p7\-detached\-sign\f[]
Signs using a detached PKCS #7 structure.
.sp
This option generates a PKCS #7 structure containing a signature for the provided data from infile. The signer certificate has to be specified using \-\-load\-certificate and \-\-load\-privkey. The input to \-\-load\-certificate can be a list of certificates. In case of a list, the first certificate is used for signing and the other certificates are included in the structure.
.TP
.NOP \f\*[B-Font]\-\-p7\-include\-cert\f[], \f\*[B-Font]\-\-no\-p7\-include\-cert\f[]
The signer's certificate will be included in the cert list.
The \fIno\-p7\-include\-cert\fP form will disable the option.
This option is enabled by default.
.sp
This options works with \-\-p7\-sign or \-\-p7\-detached\-sign and will include or exclude the signer's certificate into the generated signature.
.TP
.NOP \f\*[B-Font]\-\-p7\-time\f[], \f\*[B-Font]\-\-no\-p7\-time\f[]
Will include a timestamp in the PKCS #7 structure.
The \fIno\-p7\-time\fP form will disable the option.
.sp
This option will include a timestamp in the generated signature
.TP
.NOP \f\*[B-Font]\-\-p7\-show\-data\f[], \f\*[B-Font]\-\-no\-p7\-show\-data\f[]
Will show the embedded data in the PKCS #7 structure.
The \fIno\-p7\-show\-data\fP form will disable the option.
.sp
This option can be combined with \-\-p7\-verify or \-\-p7\-info and will display the embedded signed data in the PKCS #7 structure.
.TP
.NOP \f\*[B-Font]\-\-p7\-info\f[]
Print information on a PKCS #7 structure.
.sp
.TP
.NOP \f\*[B-Font]\-\-p7\-verify\f[]
Verify the provided PKCS #7 structure.
.sp
This option verifies the signed PKCS #7 structure. The certificate list to use for verification can be specified with \-\-load\-ca\-certificate. When no certificate list is provided, then the system's certificate list is used. Alternatively a direct signer can be provided using \-\-load\-certificate. A key purpose can be enforced with the \-\-verify\-purpose option, and the \-\-load\-data option will utilize detached data.
.TP
.NOP \f\*[B-Font]\-\-smime\-to\-p7\f[]
Convert S/MIME to PKCS #7 structure.
.sp
.SS "Other options"
.TP
.NOP \f\*[B-Font]\-\-generate\-dh\-params\f[]
Generate PKCS #3 encoded Diffie-Hellman parameters.
.sp
The will generate random parameters to be used with
Diffie\-Hellman key exchange. The output parameters will be in PKCS #3
format. Note that it is recommended to use the \-\-get\-dh\-params option
instead.
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-\-get\-dh\-params\f[]
List the included PKCS #3 encoded Diffie-Hellman parameters.
.sp
Returns stored DH parameters in GnuTLS. Those parameters returned
are defined in RFC7919, and can be considered standard parameters for a TLS
key exchange. This option is provided for old applications which require
DH parameters to be specified; modern GnuTLS applications should not require
them.
.TP
.NOP \f\*[B-Font]\-\-dh\-info\f[]
Print information PKCS #3 encoded Diffie-Hellman parameters.
.sp
.TP
.NOP \f\*[B-Font]\-\-load\-privkey\f[]=\f\*[I-Font]str\f[]
Loads a private key file.
.sp
This can be either a file or a PKCS #11 URL
.TP
.NOP \f\*[B-Font]\-\-load\-pubkey\f[]=\f\*[I-Font]str\f[]
Loads a public key file.
.sp
This can be either a file or a PKCS #11 URL
.TP
.NOP \f\*[B-Font]\-\-load\-request\f[]=\f\*[I-Font]str\f[]
Loads a certificate request file.
.sp
This option can be used with a file
.TP
.NOP \f\*[B-Font]\-\-load\-certificate\f[]=\f\*[I-Font]str\f[]
Loads a certificate file.
.sp
This option can be used with a file
.TP
.NOP \f\*[B-Font]\-\-load\-ca\-privkey\f[]=\f\*[I-Font]str\f[]
Loads the certificate authority's private key file.
.sp
This can be either a file or a PKCS #11 URL
.TP
.NOP \f\*[B-Font]\-\-load\-ca\-certificate\f[]=\f\*[I-Font]str\f[]
Loads the certificate authority's certificate file.
.sp
This can be either a file or a PKCS #11 URL
.TP
.NOP \f\*[B-Font]\-\-load\-crl\f[]=\f\*[I-Font]str\f[]
Loads the provided CRL.
.sp
This option can be used with a file
.TP
.NOP \f\*[B-Font]\-\-load\-data\f[]=\f\*[I-Font]str\f[]
Loads auxiliary data.
.sp
This option can be used with a file
.TP
.NOP \f\*[B-Font]\-\-password\f[]=\f\*[I-Font]str\f[]
Password to use.
.sp
You can use this option to specify the password in the command line instead of reading it from the tty. Note, that the command line arguments are available for view in others in the system. Specifying password as '' is the same as specifying no password.
.TP
.NOP \f\*[B-Font]\-\-null\-password\f[]
Enforce a NULL password.
.sp
This option enforces a NULL password. This is different than the empty or no password in schemas like PKCS #8.
.TP
.NOP \f\*[B-Font]\-\-empty\-password\f[]
Enforce an empty password.
.sp
This option enforces an empty password. This is different than the NULL or no password in schemas like PKCS #8.
.TP
.NOP \f\*[B-Font]\-\-hex\-numbers\f[]
Print big number in an easier format to parse.
.sp
.TP
.NOP \f\*[B-Font]\-\-cprint\f[]
In certain operations it prints the information in C-friendly format.
.sp
In certain operations it prints the information in C\-friendly format, suitable for including into C programs.
.TP
.NOP \f\*[B-Font]\-\-rsa\f[]
Generate RSA key.
.sp
When combined with \-\-generate\-privkey generates an RSA private key.
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-\-dsa\f[]
Generate DSA key.
.sp
When combined with \-\-generate\-privkey generates a DSA private key.
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-\-ecc\f[]
Generate ECC (ECDSA) key.
.sp
When combined with \-\-generate\-privkey generates an elliptic curve private key to be used with ECDSA.
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-\-ecdsa\f[]
This is an alias for the \fI--ecc\fR option.
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-\-hash\f[]=\f\*[I-Font]str\f[]
Hash algorithm to use for signing.
.sp
Available hash functions are SHA1, RMD160, SHA256, SHA384, SHA512, SHA3\-224, SHA3\-256, SHA3\-384, SHA3\-512.
.TP
.NOP \f\*[B-Font]\-\-salt\-size\f[]=\f\*[I-Font]num\f[]
Specify the RSA-PSS key default salt size.
This option takes an integer number as its argument.
.sp
Typical keys shouldn't set or restrict this option.
.TP
.NOP \f\*[B-Font]\-\-label\f[]=\f\*[I-Font]str\f[]
Specify the RSA-OAEP label, encoded in hexadecimal.
.sp
Typical keys shouldn't set or restrict this option.
.TP
.NOP \f\*[B-Font]\-\-inder\f[], \f\*[B-Font]\-\-no\-inder\f[]
Use DER format for input certificates, private keys, and DH parameters .
The \fIno\-inder\fP form will disable the option.
.sp
The input files will be assumed to be in DER or RAW format. 
Unlike options that in PEM input would allow multiple input data (e.g. multiple 
certificates), when reading in DER format a single data structure is read.
.TP
.NOP \f\*[B-Font]\-\-inraw\f[]
This is an alias for the \fI--inder\fR option.
.TP
.NOP \f\*[B-Font]\-\-outder\f[], \f\*[B-Font]\-\-no\-outder\f[]
Use DER format for output certificates, private keys, and DH parameters.
The \fIno\-outder\fP form will disable the option.
.sp
The output will be in DER or RAW format.
.TP
.NOP \f\*[B-Font]\-\-outraw\f[]
This is an alias for the \fI--outder\fR option.
.TP
.NOP \f\*[B-Font]\-\-disable\-quick\-random\f[]
No effect.
.sp
.sp
.B
NOTE: THIS OPTION IS DEPRECATED
.TP
.NOP \f\*[B-Font]\-\-template\f[]=\f\*[I-Font]str\f[]
Template file to use for non-interactive operation.
.sp
.TP
.NOP \f\*[B-Font]\-\-stdout\-info\f[]
Print information to stdout instead of stderr.
.sp
.TP
.NOP \f\*[B-Font]\-\-ask\-pass\f[]
Enable interaction for entering password when in batch mode.
.sp
This option will enable interaction to enter password when in batch mode. That is useful when the template option has been specified.
.TP
.NOP \f\*[B-Font]\-\-pkcs\-cipher\f[]=\f\*[I-Font]cipher\f[]
Cipher to use for PKCS #8 and #12 operations.
.sp
Cipher may be one of 3des, 3des\-pkcs12, aes\-128, aes\-192, aes\-256, rc2\-40, arcfour.
.TP
.NOP \f\*[B-Font]\-\-provider\f[]=\f\*[I-Font]str\f[]
Specify the PKCS #11 provider library.
.sp
This will override the default options in /etc/gnutls/pkcs11.conf
.TP
.NOP \f\*[B-Font]\-\-text\f[], \f\*[B-Font]\-\-no\-text\f[]
Output textual information before PEM-encoded certificates, private keys, etc.
The \fIno\-text\fP form will disable the option.
This option is enabled by default.
.sp
Output textual information before PEM\-encoded data
.TP
.NOP \f\*[B-Font]\-v\f[] \f\*[I-Font]arg\f[], \f\*[B-Font]\-\-version\f[]=\f\*[I-Font]arg\f[]
Output version of program and exit.  The default mode is `v', a simple
version.  The `c' mode will print copyright information and `n' will
print the full copyright notice.
.TP
.NOP \f\*[B-Font]\-h\f[], \f\*[B-Font]\-\-help\f[]
Display usage information and exit.
.TP
.NOP \f\*[B-Font]\-!\f[], \f\*[B-Font]\-\-more\-help\f[]
Pass the extended usage information through a pager.

.SH FILES
.br
\fBCerttool's template file format\fP
.br
A template file can be used to avoid the interactive questions of
certtool. Initially create a file named 'cert.cfg' that contains the information
about the certificate. The template can be used as below:
.sp
.br
.in +4
.nf
$ certtool \-\-generate\-certificate \-\-load\-privkey key.pem  \
   \-\-template cert.cfg \-\-outfile cert.pem \
   \-\-load\-ca\-certificate ca\-cert.pem \-\-load\-ca\-privkey ca\-key.pem
.in -4
.fi
.sp
An example certtool template file that can be used to generate a certificate
request or a self signed certificate follows.
.sp
.br
.in +4
.nf
# X.509 Certificate options
#
# DN options
.sp
# The organization of the subject.
organization = "Koko inc."
.sp
# The organizational unit of the subject.
unit = "sleeping dept."
.sp
# The locality of the subject.
# locality =
.sp
# The state of the certificate owner.
state = "Attiki"
.sp
# The country of the subject. Two letter code.
country = GR
.sp
# The common name of the certificate owner.
cn = "Cindy Lauper"
.sp
# A user id of the certificate owner.
#uid = "clauper"
.sp
# Set domain components
#dc = "name"
#dc = "domain"
.sp
# If the supported DN OIDs are not adequate you can set
# any OID here.
# For example set the X.520 Title and the X.520 Pseudonym
# by using OID and string pairs.
#dn_oid = "******** Dr."
#dn_oid = "******** jackal"
.sp
# This is deprecated and should not be used in new
# certificates.
# pkcs9_email = "<EMAIL>"
.sp
# An alternative way to set the certificate's distinguished name directly
# is with the "dn" option. The attribute names allowed are:
# C (country), street, O (organization), OU (unit), title, CN (common name),
# L (locality), ST (state), placeOfBirth, gender, countryOfCitizenship, 
# countryOfResidence, serialNumber, telephoneNumber, surName, initials, 
# generationQualifier, givenName, pseudonym, dnQualifier, postalCode, name, 
# businessCategory, DC, UID, jurisdictionOfIncorporationLocalityName, 
# jurisdictionOfIncorporationStateOrProvinceName,
# jurisdictionOfIncorporationCountryName, XmppAddr, and numeric OIDs.
.sp
#dn = "cn = Nikos,st = New\, Something,C=GR,surName=Mavrogiannopoulos,*******=Arkadias"
.sp
# The serial number of the certificate
# The value is in decimal (i.e. 1963) or hex (i.e. 0x07ab).
# Comment the field for a random serial number.
serial = 007
.sp
# In how many days, counting from today, this certificate will expire.
# Use \-1 if there is no expiration date.
expiration_days = 700
.sp
# Alternatively you may set concrete dates and time. The GNU date string 
# formats are accepted. See:
# https://www.gnu.org/software/tar/manual/html_node/Date\-input\-formats.html
.sp
#activation_date = "2004\-02\-29 16:21:42"
#expiration_date = "2025\-02\-29 16:24:41"
.sp
# X.509 v3 extensions
.sp
# A dnsname in case of a WWW server.
#dns_name = "www.none.org"
#dns_name = "www.morethanone.org"
.sp
# An othername defined by an OID and a hex encoded string
#other_name = "*******.5.2.2 302ca00d1b0b56414e5245494e2e4f5247a11b3019a006020400000002a10f300d1b047269636b1b0561646d696e"
#other_name_utf8 = "*******.6 A UTF8 string"
#other_name_octet = "*******.6 A string that will be encoded as ASN.1 octet string"
.sp
# Allows writing an XmppAddr Identifier
#xmpp_name = <EMAIL>
.sp
# Names used in PKINIT
#krb5_principal = <EMAIL>
#krb5_principal = HTTP/<EMAIL>
.sp
# A subject alternative name URI
#uri = "https://www.example.com"
.sp
# An IP address in case of a server.
#ip_address = "***********"
.sp
# An email in case of a person
email = "<EMAIL>"
.sp
# TLS feature (rfc7633) extension. That can is used to indicate mandatory TLS
# extension features to be provided by the server. In practice this is used
# to require the Status Request (extid: 5) extension from the server. That is,
# to require the server holding this certificate to provide a stapled OCSP response.
# You can have multiple lines for multiple TLS features.
.sp
# To ask for OCSP status request use:
#tls_feature = 5
.sp
# Challenge password used in certificate requests
challenge_password = 123456
.sp
# Password when encrypting a private key
#password = secret
.sp
# An URL that has CRLs (certificate revocation lists)
# available. Needed in CA certificates.
#crl_dist_points = "https://www.getcrl.crl/getcrl/"
.sp
# Whether this is a CA certificate or not
#ca
.sp
# Subject Unique ID (in hex)
#subject_unique_id = 00153224
.sp
# Issuer Unique ID (in hex)
#issuer_unique_id = 00153225
.sp
#### Key usage
.sp
# The following key usage flags are used by CAs and end certificates
.sp
# Whether this certificate will be used to sign data (needed
# in TLS DHE ciphersuites). This is the digitalSignature flag
# in RFC5280 terminology.
signing_key
.sp
# Whether this certificate will be used to encrypt data (needed
# in TLS RSA ciphersuites). Note that it is preferred to use different
# keys for encryption and signing. This is the keyEncipherment flag
# in RFC5280 terminology.
encryption_key
.sp
# Whether this key will be used to sign other certificates. The
# keyCertSign flag in RFC5280 terminology.
#cert_signing_key
.sp
# Whether this key will be used to sign CRLs. The
# cRLSign flag in RFC5280 terminology.
#crl_signing_key
.sp
# The keyAgreement flag of RFC5280. Its purpose is loosely
# defined. Not use it unless required by a protocol.
#key_agreement
.sp
# The dataEncipherment flag of RFC5280. Its purpose is loosely
# defined. Not use it unless required by a protocol.
#data_encipherment
.sp
# The nonRepudiation flag of RFC5280. Its purpose is loosely
# defined. Not use it unless required by a protocol.
#non_repudiation
.sp
#### Extended key usage (key purposes)
.sp
# The following extensions are used in an end certificate
# to clarify its purpose. Some CAs also use it to indicate
# the types of certificates they are purposed to sign.
.sp
.sp
# Whether this certificate will be used for a TLS client;
# this sets the id\-kp\-clientAuth (*******.*******.2) of
# extended key usage.
#tls_www_client
.sp
# Whether this certificate will be used for a TLS server;
# this sets the id\-kp\-serverAuth (*******.*******.1) of
# extended key usage.
#tls_www_server
.sp
# Whether this key will be used to sign code. This sets the
# id\-kp\-codeSigning (*******.*******.3) of extended key usage
# extension.
#code_signing_key
.sp
# Whether this key will be used to sign OCSP data. This sets the
# id\-kp\-OCSPSigning (*******.*******.9) of extended key usage extension.
#ocsp_signing_key
.sp
# Whether this key will be used for time stamping. This sets the
# id\-kp\-timeStamping (*******.*******.8) of extended key usage extension.
#time_stamping_key
.sp
# Whether this key will be used for email protection. This sets the
# id\-kp\-emailProtection (*******.*******.4) of extended key usage extension.
#email_protection_key
.sp
# Whether this key will be used for IPsec IKE operations (*******.*******.17).
#ipsec_ike_key
.sp
## adding custom key purpose OIDs
.sp
# for microsoft smart card logon
# key_purpose_oid = *******.4.1.311.20.2.2
.sp
# for email protection
# key_purpose_oid = *******.*******.4
.sp
# for any purpose (must not be used in intermediate CA certificates)
# key_purpose_oid = *********.0
.sp
### end of key purpose OIDs
.sp
### Adding arbitrary extensions
# This requires to provide the extension OIDs, as well as the extension data in
# hex format. The following two options are available since GnuTLS 3.5.3.
#add_extension = "******* 0x0AAB01ACFE"
.sp
# As above but encode the data as an octet string
#add_extension = "******* octet_string(0x0AAB01ACFE)"
.sp
# For portability critical extensions shouldn't be set to certificates.
#add_critical_extension = "******* 0x1AAB01ACFE"
.sp
# When generating a certificate from a certificate
# request, then honor the extensions stored in the request
# and store them in the real certificate.
#honor_crq_extensions
.sp
# Alternatively only specific extensions can be copied.
#honor_crq_ext = 2.5.29.17
#honor_crq_ext = 2.5.29.15
.sp
# Path length constraint. Sets the maximum number of
# certificates that can be used to certify this certificate.
# (i.e. the certificate chain length)
#path_len = \-1
#path_len = 2
.sp
# OCSP URI
# ocsp_uri = https://my.ocsp.server/ocsp
.sp
# CA issuers URI
# ca_issuers_uri = https://my.ca.issuer
.sp
# Certificate policies
#policy1 = *******.4.1.5484.*********.0
#policy1_txt = "This is a long policy to summarize"
#policy1_url = https://www.example.com/a\-policy\-to\-read
.sp
#policy2 = *******.4.1.5484.*********.1
#policy2_txt = "This is a short policy"
#policy2_url = https://www.example.com/another\-policy\-to\-read
.sp
# The number of additional certificates that may appear in a
# path before the anyPolicy is no longer acceptable.
#inhibit_anypolicy_skip_certs 1
.sp
# Name constraints
.sp
# DNS
#nc_permit_dns = example.com
#nc_exclude_dns = test.example.com
.sp
# EMAIL
#nc_permit_email = "<EMAIL>"
.sp
# Exclude subdomains of example.com
#nc_exclude_email = .example.com
.sp
# Exclude all e\-mail addresses of example.com
#nc_exclude_email = example.com
.sp
# IP
#nc_permit_ip = ***********/16
#nc_exclude_ip = ***********/24
#nc_permit_ip = fc0a:eef2:e7e7:a56e::/64
.sp
.sp
# Options for proxy certificates
#proxy_policy_language = *******.********.1
.sp
.sp
# Options for generating a CRL
.sp
# The number of days the next CRL update will be due.
# next CRL update will be in 43 days
#crl_next_update = 43
.sp
# this is the 5th CRL by this CA
# The value is in decimal (i.e. 1963) or hex (i.e. 0x07ab).
# Comment the field for a time\-based number.
# Time\-based CRL numbers generated in GnuTLS 3.6.3 and later
# are significantly larger than those generated in previous
# versions. Since CRL numbers need to be monotonic, you need
# to specify the CRL number here manually if you intend to
# downgrade to an earlier version than 3.6.3 after publishing
# the CRL as it is not possible to specify CRL numbers greater
# than 2**63\-2 using hex notation in those versions.
#crl_number = 5
.sp
# Specify the update dates more precisely.
#crl_this_update_date = "2004\-02\-29 16:21:42"
#crl_next_update_date = "2025\-02\-29 16:24:41"
.sp
# The date that the certificates will be made seen as
# being revoked.
#crl_revocation_date = "2025\-02\-29 16:24:41"
.sp
.in -4
.fi
.sp
.SH EXAMPLES
.br
\fBGenerating private keys\fP
.br
To create an RSA private key, run:
.br
.in +4
.nf
$ certtool \-\-generate\-privkey \-\-outfile key.pem \-\-rsa
.in -4
.fi
.sp
To create a DSA or elliptic curves (ECDSA) private key use the
above command combined with 'dsa' or 'ecc' options.
.sp
.br
\fBGenerating certificate requests\fP
.br
To create a certificate request (needed when the certificate is  issued  by
another party), run:
.br
.in +4
.nf
certtool \-\-generate\-request \-\-load\-privkey key.pem \
   \-\-outfile request.pem
.in -4
.fi
.sp
If the private key is stored in a smart card you can generate
a request by specifying the private key object URL.
.br
.in +4
.nf
$ ./certtool \-\-generate\-request \-\-load\-privkey "pkcs11:..." \
  \-\-load\-pubkey "pkcs11:..." \-\-outfile request.pem
.in -4
.fi
.sp
.sp
.br
\fBGenerating a self\-signed certificate\fP
.br
To create a self signed certificate, use the command:
.br
.in +4
.nf
$ certtool \-\-generate\-privkey \-\-outfile ca\-key.pem
$ certtool \-\-generate\-self\-signed \-\-load\-privkey ca\-key.pem \
   \-\-outfile ca\-cert.pem
.in -4
.fi
.sp
Note that a self\-signed certificate usually belongs to a certificate
authority, that signs other certificates.
.sp
.br
\fBGenerating a certificate\fP
.br
To generate a certificate using the previous request, use the command:
.br
.in +4
.nf
$ certtool \-\-generate\-certificate \-\-load\-request request.pem \
   \-\-outfile cert.pem \-\-load\-ca\-certificate ca\-cert.pem \
   \-\-load\-ca\-privkey ca\-key.pem
.in -4
.fi
.sp
To generate a certificate using the private key only, use the command:
.br
.in +4
.nf
$ certtool \-\-generate\-certificate \-\-load\-privkey key.pem \
   \-\-outfile cert.pem \-\-load\-ca\-certificate ca\-cert.pem \
   \-\-load\-ca\-privkey ca\-key.pem
.in -4
.fi
.sp
.br
\fBCertificate information\fP
.br
To view the certificate information, use:
.br
.in +4
.nf
$ certtool \-\-certificate\-info \-\-infile cert.pem
.in -4
.fi
.sp
.br
\fBChanging the certificate format\fP
.br
To convert the certificate from PEM to DER format, use:
.br
.in +4
.nf
$ certtool \-\-certificate\-info \-\-infile cert.pem \-\-outder \-\-outfile cert.der
.in -4
.fi
.sp
.br
\fBPKCS #12 structure generation\fP
.br
To generate a PKCS #12 structure using the previous key and certificate,
use the command:
.br
.in +4
.nf
$ certtool \-\-load\-certificate cert.pem \-\-load\-privkey key.pem \
   \-\-to\-p12 \-\-outder \-\-outfile key.p12
.in -4
.fi
.sp
Some tools (reportedly web browsers) have problems with that file
because it does not contain the CA certificate for the certificate.
To work around that problem in the tool, you can use the
\-\-load\-ca\-certificate parameter as follows:
.sp
.br
.in +4
.nf
$ certtool \-\-load\-ca\-certificate ca.pem \
  \-\-load\-certificate cert.pem \-\-load\-privkey key.pem \
  \-\-to\-p12 \-\-outder \-\-outfile key.p12
.in -4
.fi
.sp
.br
\fBObtaining Diffie\-Hellman parameters\fP
.br
To obtain the RFC7919 parameters for Diffie\-Hellman key exchange, use the command:
.br
.in +4
.nf
$ certtool \-\-get\-dh\-params \-\-outfile dh.pem \-\-sec\-param medium
.in -4
.fi
.sp
.br
\fBVerifying a certificate\fP
.br
To verify a certificate in a file against the system's CA trust store
use the following command:
.br
.in +4
.nf
$ certtool \-\-verify \-\-infile cert.pem
.in -4
.fi
.sp
It is also possible to simulate hostname verification with the following
options:
.br
.in +4
.nf
$ certtool \-\-verify \-\-verify\-hostname www.example.com \-\-infile cert.pem
.in -4
.fi
.sp
.sp
.br
\fBProxy certificate generation\fP
.br
Proxy certificate can be used to delegate your credential to a
temporary, typically short\-lived, certificate.  To create one from the
previously created certificate, first create a temporary key and then
generate a proxy certificate for it, using the commands:
.sp
.br
.in +4
.nf
$ certtool \-\-generate\-privkey > proxy\-key.pem
$ certtool \-\-generate\-proxy \-\-load\-ca\-privkey key.pem \
  \-\-load\-privkey proxy\-key.pem \-\-load\-certificate cert.pem \
  \-\-outfile proxy\-cert.pem
.in -4
.fi
.sp
.br
\fBCertificate revocation list generation\fP
.br
To create an empty Certificate Revocation List (CRL) do:
.sp
.br
.in +4
.nf
$ certtool \-\-generate\-crl \-\-load\-ca\-privkey x509\-ca\-key.pem \
           \-\-load\-ca\-certificate x509\-ca.pem
.in -4
.fi
.sp
To create a CRL that contains some revoked certificates, place the
certificates in a file and use \fB\-\-load\-certificate\fP as follows:
.sp
.br
.in +4
.nf
$ certtool \-\-generate\-crl \-\-load\-ca\-privkey x509\-ca\-key.pem \
  \-\-load\-ca\-certificate x509\-ca.pem \-\-load\-certificate revoked\-certs.pem
.in -4
.fi
.sp
To verify a Certificate Revocation List (CRL) do:
.sp
.br
.in +4
.nf
$ certtool \-\-verify\-crl \-\-load\-ca\-certificate x509\-ca.pem < crl.pem
.in -4
.fi
.SH "EXIT STATUS"
One of the following exit values will be returned:
.TP
.NOP 0 " (EXIT_SUCCESS)"
Successful program execution.
.TP
.NOP 1 " (EXIT_FAILURE)"
The operation failed or the command syntax was not valid.
.PP
.SH "SEE ALSO"
p11tool (1), psktool (1), srptool (1)
.SH "AUTHORS"

.SH "COPYRIGHT"
Copyright (C) 2020-2023 Free Software Foundation, and others all rights reserved.
This program is released under the terms of
the GNU General Public License, version 3 or later
.
.SH "BUGS"
Please send bug reports to: <EMAIL>
