.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_cipher_suite_info" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_cipher_suite_info \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const char * gnutls_cipher_suite_info(size_t " idx ", unsigned char * " cs_id ", gnutls_kx_algorithm_t * " kx ", gnutls_cipher_algorithm_t * " cipher ", gnutls_mac_algorithm_t * " mac ", gnutls_protocol_t * " min_version ");"
.SH ARGUMENTS
.IP "size_t idx" 12
index of cipher suite to get information about, starts on 0.
.IP "unsigned char * cs_id" 12
output buffer with room for 2 bytes, indicating cipher suite value
.IP "gnutls_kx_algorithm_t * kx" 12
output variable indicating key exchange algorithm, or \fBNULL\fP.
.IP "gnutls_cipher_algorithm_t * cipher" 12
output variable indicating cipher, or \fBNULL\fP.
.IP "gnutls_mac_algorithm_t * mac" 12
output variable indicating MAC algorithm, or \fBNULL\fP.
.IP "gnutls_protocol_t * min_version" 12
output variable indicating TLS protocol version, or \fBNULL\fP.
.SH "DESCRIPTION"
Get information about supported cipher suites.  Use the function
iteratively to get information about all supported cipher suites.
Call with idx=0 to get information about first cipher suite, then
idx=1 and so on until the function returns NULL.
.SH "RETURNS"
the name of  \fIidx\fP cipher suite, and set the information
about the cipher suite in the output variables.  If  \fIidx\fP is out of
bounds, \fBNULL\fP is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
