.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_priority_init2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_priority_init2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_priority_init2(gnutls_priority_t * " priority_cache ", const char * " priorities ", const char ** " err_pos ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_priority_t * priority_cache" 12
is a \fBgnutls_priority_t\fP type.
.IP "const char * priorities" 12
is a string describing priorities (may be \fBNULL\fP)
.IP "const char ** err_pos" 12
In case of an error this will have the position in the string the error occurred
.IP "unsigned flags" 12
zero or \fBGNUTLS_PRIORITY_INIT_DEF_APPEND\fP
.SH "DESCRIPTION"
Sets priorities for the ciphers, key exchange methods, and macs.
The  \fIpriority_cache\fP should be deinitialized
using \fBgnutls_priority_deinit()\fP.

The \fBpriorities\fP option allows you to specify a colon
separated list of the cipher priorities to enable.
Some keywords are defined to provide quick access
to common preferences.

When  \fIflags\fP is set to \fBGNUTLS_PRIORITY_INIT_DEF_APPEND\fP then the  \fIpriorities\fP specified will be appended to the default options.

Unless there is a special need, use the "NORMAL" keyword to
apply a reasonable security level, or "NORMAL:%COMPAT" for compatibility.

"PERFORMANCE" means all the "secure" ciphersuites are enabled,
limited to 128 bit ciphers and sorted by terms of speed
performance.

"LEGACY" the NORMAL settings for GnuTLS 3.2.x or earlier. There is
no verification profile set, and the allowed DH primes are considered
weak today.

"NORMAL" means all "secure" ciphersuites. The 256\-bit ciphers are
included as a fallback only.  The ciphers are sorted by security
margin.

"PFS" means all "secure" ciphersuites that support perfect forward secrecy.
The 256\-bit ciphers are included as a fallback only.
The ciphers are sorted by security margin.

"SECURE128" means all "secure" ciphersuites of security level 128\-bit
or more.

"SECURE192" means all "secure" ciphersuites of security level 192\-bit
or more.

"SUITEB128" means all the NSA SuiteB ciphersuites with security level
of 128.

"SUITEB192" means all the NSA SuiteB ciphersuites with security level
of 192.

"NONE" means nothing is enabled.  This disables everything, including protocols.

"@KEYWORD1,KEYWORD2,..." The system administrator imposed settings.
The provided keyword(s) will be expanded from a configuration\-time
provided file \- default is: /etc/gnutls/config.
Any attributes that follow it, will be appended to the expanded
string. If multiple keywords are provided, separated by commas,
then the first keyword that exists in the configuration file
will be used. At least one of the keywords must exist, or this
function will return an error. Typical usage would be to specify
an application specified keyword first, followed by "SYSTEM" as
a default fallback. e.g., " \fILIBVIRT\fP ,SYSTEM:!\-VERS\-SSL3.0" will
first try to find a config file entry matching "LIBVIRT", but if
that does not exist will use the entry for "SYSTEM". If "SYSTEM"
does not exist either, an error will be returned. In all cases,
the SSL3.0 protocol will be disabled. The system priority file
entries should be formatted as "KEYWORD=VALUE", e.g.,
"SYSTEM=NORMAL:+ARCFOUR\-128".

Special keywords are "!", "\-" and "+".
"!" or "\-" appended with an algorithm will remove this algorithm.
"+" appended with an algorithm will add this algorithm.

Check the GnuTLS manual section "Priority strings" for detailed
information.
.SH "EXAMPLES"

"NONE:+VERS\-TLS\-ALL:+MAC\-ALL:+RSA:+AES\-128\-CBC:+SIGN\-ALL:+COMP\-NULL"

"NORMAL:+ARCFOUR\-128" means normal ciphers plus ARCFOUR\-128.

"SECURE128:\-VERS\-SSL3.0" means that only secure ciphers are
and enabled, SSL3.0 is disabled.

"NONE:+VERS\-TLS\-ALL:+AES\-128\-CBC:+RSA:+SHA1:+COMP\-NULL:+SIGN\-RSA\-SHA1",

"NONE:+VERS\-TLS\-ALL:+AES\-128\-CBC:+ECDHE\-RSA:+SHA1:+COMP\-NULL:+SIGN\-RSA\-SHA1:+CURVE\-SECP256R1",

"SECURE256:+SECURE128",

Note that "NORMAL:%COMPAT" is the most compatible mode.

A \fBNULL\fP  \fIpriorities\fP string indicates the default priorities to be
used (this is available since GnuTLS 3.3.0).
.SH "RETURNS"
On syntax error \fBGNUTLS_E_INVALID_REQUEST\fP is returned,
\fBGNUTLS_E_SUCCESS\fP on success, or an error code.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
