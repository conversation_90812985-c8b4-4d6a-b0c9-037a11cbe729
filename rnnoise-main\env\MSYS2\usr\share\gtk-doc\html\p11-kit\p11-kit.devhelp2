<?xml version="1.0" encoding="utf-8" standalone="no"?>
<book xmlns="http://www.devhelp.net/book" title="p11-kit" link="index.html" author="" name="p11-kit" version="2" language="c">
  <chapters>
    <sub name="PKCS#11 Configuration" link="config.html">
      <sub name="Consistent configuration" link="config.html#config-introduction"/>
      <sub name="Example" link="config-example.html"/>
      <sub name="Configuration Files" link="config-files.html"/>
    </sub>
    <sub name="Sharing PKCS#11 modules" link="sharing.html">
      <sub name="Multiple consumers of PKCS#11 in a process" link="sharing.html#sharing-problem"/>
      <sub name="Managed modules" link="sharing-managed.html"/>
    </sub>
    <sub name="Proxy Module" link="sharing.html"/>
    <sub name="Remoting / Forwarding" link="remoting.html"/>
    <sub name="Trust Policy Module" link="trust-module.html">
      <sub name="Paths loaded by the Module" link="trust-module.html#trust-files"/>
      <sub name="Using the Trust Policy Module with NSS" link="trust-nss.html"/>
      <sub name="Using the Trust Policy Module with glib-networking" link="trust-glib-networking.html"/>
      <sub name="Disabling the Trust Policy Module" link="trust-disable.html"/>
    </sub>
    <sub name="Manual Pages" link="tools.html">
      <sub name="p11-kit" link="p11-kit.html"/>
      <sub name="pkcs11.conf" link="pkcs11-conf.html"/>
      <sub name="trust" link="trust.html"/>
    </sub>
    <sub name="API Reference" link="reference.html">
      <sub name="Modules" link="p11-kit-Modules.html"/>
      <sub name="URIs" link="p11-kit-URIs.html"/>
      <sub name="PIN Callbacks" link="p11-kit-PIN-Callbacks.html"/>
      <sub name="Utilities" link="p11-kit-Utilities.html"/>
      <sub name="Future" link="p11-kit-Future.html"/>
      <sub name="Deprecated" link="p11-kit-Deprecated.html"/>
      <sub name="API Index" link="reference.html#api-index-full"/>
      <sub name="Annotation Glossary" link="reference.html#annotation-glossary"/>
    </sub>
    <sub name="Building, Packaging, and Contributing to p11-kit" link="devel.html">
      <sub name="Helpful Resources" link="devel.html#devel-links"/>
      <sub name="Packaging PKCS#11 module configs" link="devel-paths.html">
        <sub name="Path to place module configuration" link="devel-paths.html#devel-paths-config"/>
        <sub name="Default path for modules with relative paths" link="devel-paths.html#devel-paths-modules"/>
      </sub>
      <sub name="Customizing installed commands" link="devel-commands.html"/>
      <sub name="Compiling p11-kit from Source" link="devel-building.html">
        <sub name="Building on UNIX" link="devel-building.html#devel-building-unix"/>
        <sub name="Optional Dependencies" link="devel-building.html#devel-building-dependencies"/>
        <sub name="Extra Configuration Options" link="devel-building.html#devel-building-configure"/>
      </sub>
      <sub name="Coding Style" link="devel-building-style.html"/>
      <sub name="Testing and Code Coverage" link="devel-testing.html"/>
      <sub name="Debugging Tips" link="devel-debugging.html"/>
    </sub>
  </chapters>
  <functions>
    <keyword type="function" name="p11_kit_modules_load_and_initialize ()" link="p11-kit-Modules.html#p11-kit-modules-load-and-initialize"/>
    <keyword type="function" name="p11_kit_modules_finalize_and_release ()" link="p11-kit-Modules.html#p11-kit-modules-finalize-and-release"/>
    <keyword type="function" name="p11_kit_modules_load ()" link="p11-kit-Modules.html#p11-kit-modules-load"/>
    <keyword type="function" name="p11_kit_modules_initialize ()" link="p11-kit-Modules.html#p11-kit-modules-initialize"/>
    <keyword type="function" name="p11_kit_modules_finalize ()" link="p11-kit-Modules.html#p11-kit-modules-finalize"/>
    <keyword type="function" name="p11_kit_modules_release ()" link="p11-kit-Modules.html#p11-kit-modules-release"/>
    <keyword type="function" name="p11_kit_module_load ()" link="p11-kit-Modules.html#p11-kit-module-load"/>
    <keyword type="function" name="p11_kit_module_initialize ()" link="p11-kit-Modules.html#p11-kit-module-initialize"/>
    <keyword type="function" name="p11_kit_module_finalize ()" link="p11-kit-Modules.html#p11-kit-module-finalize"/>
    <keyword type="function" name="p11_kit_module_release ()" link="p11-kit-Modules.html#p11-kit-module-release"/>
    <keyword type="function" name="p11_kit_module_for_name ()" link="p11-kit-Modules.html#p11-kit-module-for-name"/>
    <keyword type="function" name="p11_kit_module_get_name ()" link="p11-kit-Modules.html#p11-kit-module-get-name"/>
    <keyword type="function" name="p11_kit_module_get_flags ()" link="p11-kit-Modules.html#p11-kit-module-get-flags"/>
    <keyword type="function" name="p11_kit_module_get_filename ()" link="p11-kit-Modules.html#p11-kit-module-get-filename"/>
    <keyword type="function" name="p11_kit_config_option ()" link="p11-kit-Modules.html#p11-kit-config-option"/>
    <keyword type="macro" name="P11_KIT_MODULE_CRITICAL" link="p11-kit-Modules.html#P11-KIT-MODULE-CRITICAL:CAPS"/>
    <keyword type="macro" name="P11_KIT_MODULE_UNMANAGED" link="p11-kit-Modules.html#P11-KIT-MODULE-UNMANAGED:CAPS"/>
    <keyword type="function" name="p11_kit_uri_new ()" link="p11-kit-URIs.html#p11-kit-uri-new"/>
    <keyword type="function" name="p11_kit_uri_get_module_info ()" link="p11-kit-URIs.html#p11-kit-uri-get-module-info"/>
    <keyword type="function" name="p11_kit_uri_match_module_info ()" link="p11-kit-URIs.html#p11-kit-uri-match-module-info"/>
    <keyword type="function" name="p11_kit_uri_get_slot_info ()" link="p11-kit-URIs.html#p11-kit-uri-get-slot-info"/>
    <keyword type="function" name="p11_kit_uri_match_slot_info ()" link="p11-kit-URIs.html#p11-kit-uri-match-slot-info"/>
    <keyword type="function" name="p11_kit_uri_get_slot_id ()" link="p11-kit-URIs.html#p11-kit-uri-get-slot-id"/>
    <keyword type="function" name="p11_kit_uri_set_slot_id ()" link="p11-kit-URIs.html#p11-kit-uri-set-slot-id"/>
    <keyword type="function" name="p11_kit_uri_get_token_info ()" link="p11-kit-URIs.html#p11-kit-uri-get-token-info"/>
    <keyword type="function" name="p11_kit_uri_match_token_info ()" link="p11-kit-URIs.html#p11-kit-uri-match-token-info"/>
    <keyword type="function" name="p11_kit_uri_get_attributes ()" link="p11-kit-URIs.html#p11-kit-uri-get-attributes"/>
    <keyword type="function" name="p11_kit_uri_set_attributes ()" link="p11-kit-URIs.html#p11-kit-uri-set-attributes"/>
    <keyword type="function" name="p11_kit_uri_clear_attributes ()" link="p11-kit-URIs.html#p11-kit-uri-clear-attributes"/>
    <keyword type="function" name="p11_kit_uri_match_attributes ()" link="p11-kit-URIs.html#p11-kit-uri-match-attributes"/>
    <keyword type="function" name="p11_kit_uri_get_attribute ()" link="p11-kit-URIs.html#p11-kit-uri-get-attribute"/>
    <keyword type="function" name="p11_kit_uri_set_attribute ()" link="p11-kit-URIs.html#p11-kit-uri-set-attribute"/>
    <keyword type="function" name="p11_kit_uri_clear_attribute ()" link="p11-kit-URIs.html#p11-kit-uri-clear-attribute"/>
    <keyword type="function" name="p11_kit_uri_set_unrecognized ()" link="p11-kit-URIs.html#p11-kit-uri-set-unrecognized"/>
    <keyword type="function" name="p11_kit_uri_any_unrecognized ()" link="p11-kit-URIs.html#p11-kit-uri-any-unrecognized"/>
    <keyword type="function" name="p11_kit_uri_get_pin_value ()" link="p11-kit-URIs.html#p11-kit-uri-get-pin-value"/>
    <keyword type="function" name="p11_kit_uri_set_pin_value ()" link="p11-kit-URIs.html#p11-kit-uri-set-pin-value"/>
    <keyword type="function" name="p11_kit_uri_get_pin_source ()" link="p11-kit-URIs.html#p11-kit-uri-get-pin-source"/>
    <keyword type="function" name="p11_kit_uri_set_pin_source ()" link="p11-kit-URIs.html#p11-kit-uri-set-pin-source"/>
    <keyword type="function" name="p11_kit_uri_get_pinfile ()" link="p11-kit-URIs.html#p11-kit-uri-get-pinfile" deprecated="use p11_kit_uri_get_pin_source()."/>
    <keyword type="function" name="p11_kit_uri_set_pinfile ()" link="p11-kit-URIs.html#p11-kit-uri-set-pinfile" deprecated="use p11_kit_uri_set_pin_source()."/>
    <keyword type="function" name="p11_kit_uri_get_module_name ()" link="p11-kit-URIs.html#p11-kit-uri-get-module-name"/>
    <keyword type="function" name="p11_kit_uri_set_module_name ()" link="p11-kit-URIs.html#p11-kit-uri-set-module-name"/>
    <keyword type="function" name="p11_kit_uri_get_module_path ()" link="p11-kit-URIs.html#p11-kit-uri-get-module-path"/>
    <keyword type="function" name="p11_kit_uri_set_module_path ()" link="p11-kit-URIs.html#p11-kit-uri-set-module-path"/>
    <keyword type="function" name="p11_kit_uri_get_vendor_query ()" link="p11-kit-URIs.html#p11-kit-uri-get-vendor-query"/>
    <keyword type="function" name="p11_kit_uri_set_vendor_query ()" link="p11-kit-URIs.html#p11-kit-uri-set-vendor-query"/>
    <keyword type="function" name="p11_kit_uri_format ()" link="p11-kit-URIs.html#p11-kit-uri-format"/>
    <keyword type="function" name="p11_kit_uri_parse ()" link="p11-kit-URIs.html#p11-kit-uri-parse"/>
    <keyword type="function" name="p11_kit_uri_free ()" link="p11-kit-URIs.html#p11-kit-uri-free"/>
    <keyword type="function" name="p11_kit_uri_message ()" link="p11-kit-URIs.html#p11-kit-uri-message"/>
    <keyword type="macro" name="P11_KIT_URI_SCHEME" link="p11-kit-URIs.html#P11-KIT-URI-SCHEME:CAPS"/>
    <keyword type="macro" name="P11_KIT_URI_SCHEME_LEN" link="p11-kit-URIs.html#P11-KIT-URI-SCHEME-LEN:CAPS"/>
    <keyword type="enum" name="enum P11KitUriType" link="p11-kit-URIs.html#P11KitUriType"/>
    <keyword type="enum" name="enum P11KitUriResult" link="p11-kit-URIs.html#P11KitUriResult"/>
    <keyword type="typedef" name="P11KitUri" link="p11-kit-URIs.html#P11KitUri"/>
    <keyword type="typedef" name="p11_kit_uri" link="p11-kit-URIs.html#p11-kit-uri"/>
    <keyword type="macro" name="P11_KIT_URI_NO_MEMORY" link="p11-kit-URIs.html#P11-KIT-URI-NO-MEMORY:CAPS"/>
    <keyword type="function" name="p11_kit_pin_new ()" link="p11-kit-PIN-Callbacks.html#p11-kit-pin-new"/>
    <keyword type="function" name="p11_kit_pin_new_for_buffer ()" link="p11-kit-PIN-Callbacks.html#p11-kit-pin-new-for-buffer"/>
    <keyword type="function" name="p11_kit_pin_new_for_string ()" link="p11-kit-PIN-Callbacks.html#p11-kit-pin-new-for-string"/>
    <keyword type="function" name="p11_kit_pin_get_value ()" link="p11-kit-PIN-Callbacks.html#p11-kit-pin-get-value"/>
    <keyword type="function" name="p11_kit_pin_get_length ()" link="p11-kit-PIN-Callbacks.html#p11-kit-pin-get-length"/>
    <keyword type="function" name="p11_kit_pin_ref ()" link="p11-kit-PIN-Callbacks.html#p11-kit-pin-ref"/>
    <keyword type="function" name="p11_kit_pin_unref ()" link="p11-kit-PIN-Callbacks.html#p11-kit-pin-unref"/>
    <keyword type="function" name="p11_kit_pin_register_callback ()" link="p11-kit-PIN-Callbacks.html#p11-kit-pin-register-callback"/>
    <keyword type="function" name="p11_kit_pin_unregister_callback ()" link="p11-kit-PIN-Callbacks.html#p11-kit-pin-unregister-callback"/>
    <keyword type="function" name="p11_kit_pin_callback ()" link="p11-kit-PIN-Callbacks.html#p11-kit-pin-callback"/>
    <keyword type="function" name="p11_kit_pin_request ()" link="p11-kit-PIN-Callbacks.html#p11-kit-pin-request"/>
    <keyword type="function" name="p11_kit_pin_destroy_func ()" link="p11-kit-PIN-Callbacks.html#p11-kit-pin-destroy-func"/>
    <keyword type="function" name="p11_kit_pin_file_callback ()" link="p11-kit-PIN-Callbacks.html#p11-kit-pin-file-callback"/>
    <keyword type="typedef" name="P11KitPin" link="p11-kit-PIN-Callbacks.html#P11KitPin"/>
    <keyword type="enum" name="enum P11KitPinFlags" link="p11-kit-PIN-Callbacks.html#P11KitPinFlags"/>
    <keyword type="macro" name="P11_KIT_PIN_FALLBACK" link="p11-kit-PIN-Callbacks.html#P11-KIT-PIN-FALLBACK:CAPS"/>
    <keyword type="function" name="p11_kit_strerror ()" link="p11-kit-Utilities.html#p11-kit-strerror"/>
    <keyword type="function" name="p11_kit_message ()" link="p11-kit-Utilities.html#p11-kit-message"/>
    <keyword type="function" name="p11_kit_space_strdup ()" link="p11-kit-Utilities.html#p11-kit-space-strdup"/>
    <keyword type="function" name="p11_kit_space_strlen ()" link="p11-kit-Utilities.html#p11-kit-space-strlen"/>
    <keyword type="function" name="p11_kit_be_quiet ()" link="p11-kit-Utilities.html#p11-kit-be-quiet"/>
    <keyword type="function" name="p11_kit_be_loud ()" link="p11-kit-Utilities.html#p11-kit-be-loud"/>
    <keyword type="function" name="p11_kit_set_progname ()" link="p11-kit-Future.html#p11-kit-set-progname"/>
    <keyword type="function" name="p11_kit_destroyer ()" link="p11-kit-Future.html#p11-kit-destroyer"/>
    <keyword type="function" name="p11_kit_iter_new ()" link="p11-kit-Future.html#p11-kit-iter-new"/>
    <keyword type="function" name="p11_kit_iter_set_uri ()" link="p11-kit-Future.html#p11-kit-iter-set-uri"/>
    <keyword type="function" name="p11_kit_iter_add_callback ()" link="p11-kit-Future.html#p11-kit-iter-add-callback"/>
    <keyword type="function" name="p11_kit_iter_add_filter ()" link="p11-kit-Future.html#p11-kit-iter-add-filter"/>
    <keyword type="function" name="p11_kit_iter_callback ()" link="p11-kit-Future.html#p11-kit-iter-callback"/>
    <keyword type="function" name="p11_kit_iter_begin ()" link="p11-kit-Future.html#p11-kit-iter-begin"/>
    <keyword type="function" name="p11_kit_iter_begin_with ()" link="p11-kit-Future.html#p11-kit-iter-begin-with"/>
    <keyword type="function" name="p11_kit_iter_next ()" link="p11-kit-Future.html#p11-kit-iter-next"/>
    <keyword type="function" name="p11_kit_iter_get_kind ()" link="p11-kit-Future.html#p11-kit-iter-get-kind"/>
    <keyword type="function" name="p11_kit_iter_get_module ()" link="p11-kit-Future.html#p11-kit-iter-get-module"/>
    <keyword type="function" name="p11_kit_iter_get_slot ()" link="p11-kit-Future.html#p11-kit-iter-get-slot"/>
    <keyword type="function" name="p11_kit_iter_get_slot_info ()" link="p11-kit-Future.html#p11-kit-iter-get-slot-info"/>
    <keyword type="function" name="p11_kit_iter_get_token ()" link="p11-kit-Future.html#p11-kit-iter-get-token"/>
    <keyword type="function" name="p11_kit_iter_get_session ()" link="p11-kit-Future.html#p11-kit-iter-get-session"/>
    <keyword type="function" name="p11_kit_iter_keep_session ()" link="p11-kit-Future.html#p11-kit-iter-keep-session"/>
    <keyword type="function" name="p11_kit_iter_get_object ()" link="p11-kit-Future.html#p11-kit-iter-get-object"/>
    <keyword type="function" name="p11_kit_iter_get_attributes ()" link="p11-kit-Future.html#p11-kit-iter-get-attributes"/>
    <keyword type="function" name="p11_kit_iter_load_attributes ()" link="p11-kit-Future.html#p11-kit-iter-load-attributes"/>
    <keyword type="function" name="p11_kit_iter_destroy_object ()" link="p11-kit-Future.html#p11-kit-iter-destroy-object"/>
    <keyword type="function" name="p11_kit_iter_free ()" link="p11-kit-Future.html#p11-kit-iter-free"/>
    <keyword type="function" name="p11_kit_remote_serve_module ()" link="p11-kit-Future.html#p11-kit-remote-serve-module"/>
    <keyword type="function" name="p11_kit_remote_serve_token ()" link="p11-kit-Future.html#p11-kit-remote-serve-token" deprecated="use p11_kit_remote_serve_tokens()"/>
    <keyword type="function" name="p11_kit_remote_serve_tokens ()" link="p11-kit-Future.html#p11-kit-remote-serve-tokens"/>
    <keyword type="typedef" name="P11KitIter" link="p11-kit-Future.html#P11KitIter"/>
    <keyword type="typedef" name="p11_kit_iter" link="p11-kit-Future.html#p11-kit-iter"/>
    <keyword type="enum" name="enum P11KitIterKind" link="p11-kit-Future.html#P11KitIterKind"/>
    <keyword type="enum" name="enum P11KitIterBehavior" link="p11-kit-Future.html#P11KitIterBehavior"/>
    <keyword type="function" name="p11_kit_initialize_registered ()" link="p11-kit-Deprecated.html#p11-kit-initialize-registered" deprecated="Since: 0.19.0: Use p11_kit_modules_load() instead."/>
    <keyword type="function" name="p11_kit_finalize_registered ()" link="p11-kit-Deprecated.html#p11-kit-finalize-registered" deprecated="Since 0.19.0: Use p11_kit_modules_release() instead."/>
    <keyword type="function" name="p11_kit_registered_modules ()" link="p11-kit-Deprecated.html#p11-kit-registered-modules" deprecated="Since 0.19.0: Use p11_kit_modules_load() instead."/>
    <keyword type="function" name="p11_kit_registered_module_to_name ()" link="p11-kit-Deprecated.html#p11-kit-registered-module-to-name" deprecated="Since 0.19.0: Use p11_kit_module_get_name() instead."/>
    <keyword type="function" name="p11_kit_registered_name_to_module ()" link="p11-kit-Deprecated.html#p11-kit-registered-name-to-module" deprecated="Since 0.19.0: Use p11_kit_module_for_name() instead."/>
    <keyword type="function" name="p11_kit_registered_option ()" link="p11-kit-Deprecated.html#p11-kit-registered-option" deprecated="Since 0.19.0: Use p11_kit_config_option() instead."/>
    <keyword type="function" name="p11_kit_initialize_module ()" link="p11-kit-Deprecated.html#p11-kit-initialize-module" deprecated="Since 0.19.0: Use p11_kit_module_initialize() instead."/>
    <keyword type="function" name="p11_kit_load_initialize_module ()" link="p11-kit-Deprecated.html#p11-kit-load-initialize-module" deprecated="Since 0.19.0: Use p11_kit_module_load() instead."/>
    <keyword type="function" name="p11_kit_finalize_module ()" link="p11-kit-Deprecated.html#p11-kit-finalize-module" deprecated=""/>
    <keyword type="macro" name="P11_KIT_DEPRECATED_FOR()" link="p11-kit-Deprecated.html#P11-KIT-DEPRECATED-FOR:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_FOR_OBJECT" link="p11-kit-URIs.html#P11-KIT-URI-FOR-OBJECT:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_FOR_TOKEN" link="p11-kit-URIs.html#P11-KIT-URI-FOR-TOKEN:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_FOR_SLOT" link="p11-kit-URIs.html#P11-KIT-URI-FOR-SLOT:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_FOR_MODULE" link="p11-kit-URIs.html#P11-KIT-URI-FOR-MODULE:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_FOR_MODULE_WITH_VERSION" link="p11-kit-URIs.html#P11-KIT-URI-FOR-MODULE-WITH-VERSION:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_FOR_OBJECT_ON_TOKEN" link="p11-kit-URIs.html#P11-KIT-URI-FOR-OBJECT-ON-TOKEN:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_FOR_OBJECT_ON_TOKEN_AND_MODULE" link="p11-kit-URIs.html#P11-KIT-URI-FOR-OBJECT-ON-TOKEN-AND-MODULE:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_FOR_ANY" link="p11-kit-URIs.html#P11-KIT-URI-FOR-ANY:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_OK" link="p11-kit-URIs.html#P11-KIT-URI-OK:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_UNEXPECTED" link="p11-kit-URIs.html#P11-KIT-URI-UNEXPECTED:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_BAD_SCHEME" link="p11-kit-URIs.html#P11-KIT-URI-BAD-SCHEME:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_BAD_ENCODING" link="p11-kit-URIs.html#P11-KIT-URI-BAD-ENCODING:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_BAD_SYNTAX" link="p11-kit-URIs.html#P11-KIT-URI-BAD-SYNTAX:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_BAD_VERSION" link="p11-kit-URIs.html#P11-KIT-URI-BAD-VERSION:CAPS"/>
    <keyword type="constant" name="P11_KIT_URI_NOT_FOUND" link="p11-kit-URIs.html#P11-KIT-URI-NOT-FOUND:CAPS"/>
    <keyword type="constant" name="P11_KIT_PIN_FLAGS_USER_LOGIN" link="p11-kit-PIN-Callbacks.html#P11-KIT-PIN-FLAGS-USER-LOGIN:CAPS"/>
    <keyword type="constant" name="P11_KIT_PIN_FLAGS_SO_LOGIN" link="p11-kit-PIN-Callbacks.html#P11-KIT-PIN-FLAGS-SO-LOGIN:CAPS"/>
    <keyword type="constant" name="P11_KIT_PIN_FLAGS_CONTEXT_LOGIN" link="p11-kit-PIN-Callbacks.html#P11-KIT-PIN-FLAGS-CONTEXT-LOGIN:CAPS"/>
    <keyword type="constant" name="P11_KIT_PIN_FLAGS_RETRY" link="p11-kit-PIN-Callbacks.html#P11-KIT-PIN-FLAGS-RETRY:CAPS"/>
    <keyword type="constant" name="P11_KIT_PIN_FLAGS_MANY_TRIES" link="p11-kit-PIN-Callbacks.html#P11-KIT-PIN-FLAGS-MANY-TRIES:CAPS"/>
    <keyword type="constant" name="P11_KIT_PIN_FLAGS_FINAL_TRY" link="p11-kit-PIN-Callbacks.html#P11-KIT-PIN-FLAGS-FINAL-TRY:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_KIND_MODULE" link="p11-kit-Future.html#P11-KIT-ITER-KIND-MODULE:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_KIND_SLOT" link="p11-kit-Future.html#P11-KIT-ITER-KIND-SLOT:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_KIND_TOKEN" link="p11-kit-Future.html#P11-KIT-ITER-KIND-TOKEN:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_KIND_OBJECT" link="p11-kit-Future.html#P11-KIT-ITER-KIND-OBJECT:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_KIND_SESSION" link="p11-kit-Future.html#P11-KIT-ITER-KIND-SESSION:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_KIND_UNKNOWN" link="p11-kit-Future.html#P11-KIT-ITER-KIND-UNKNOWN:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_BUSY_SESSIONS" link="p11-kit-Future.html#P11-KIT-ITER-BUSY-SESSIONS:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_WANT_WRITABLE" link="p11-kit-Future.html#P11-KIT-ITER-WANT-WRITABLE:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_WITH_MODULES" link="p11-kit-Future.html#P11-KIT-ITER-WITH-MODULES:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_WITH_SLOTS" link="p11-kit-Future.html#P11-KIT-ITER-WITH-SLOTS:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_WITH_TOKENS" link="p11-kit-Future.html#P11-KIT-ITER-WITH-TOKENS:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_WITHOUT_OBJECTS" link="p11-kit-Future.html#P11-KIT-ITER-WITHOUT-OBJECTS:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_WITH_LOGIN" link="p11-kit-Future.html#P11-KIT-ITER-WITH-LOGIN:CAPS"/>
    <keyword type="constant" name="P11_KIT_ITER_WITH_SESSIONS" link="p11-kit-Future.html#P11-KIT-ITER-WITH-SESSIONS:CAPS"/>
  </functions>
</book>
