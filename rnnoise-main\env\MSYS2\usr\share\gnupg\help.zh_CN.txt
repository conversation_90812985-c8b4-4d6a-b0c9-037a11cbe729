# help.zh_CN.txt - zh_CN GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.

# Update by bobwxc<<EMAIL>> 2020

# Note that this help file needs to be UTF-8 encoded.  When looking
# for a help item, Gnu<PERSON><PERSON> scans the help files in the following order
# (assuming a GNU or Unix system):
#
#    /etc/gnupg/help.LL_TT.txt
#    /etc/gnupg/help.LL.txt
#    /etc/gnupg/help.txt
#    /usr/share/gnupg/help.LL_TT.txt
#    /usr/share/gnupg/help.LL.txt
#    /usr/share/gnupg/help.txt
#
# Here LL_TT denotes the full name of the current locale with the
# territory (.e.g. "de_DE"), LL denotes just the locale name
# (e.g. "de").  The first matching item is returned.  To put a dot or
# a hash mark at the beginning of a help text line, it needs to be
# prefixed with ". ".  A single dot may be used to terminated ahelp
# entry.

.#pinentry.qualitybar.tooltip
# [remove the hash mark from the key to enable this text]
# This entry is just an example on how to customize the tooltip shown
# when hovering over the quality bar of the pinentry.  We don't
# install this text so that the hardcoded translation takes
# precedence.  An administrator should write up a short help to tell
# the users about the configured passphrase constraints and save that
# to /etc/gnupg/help.txt.  The help text should not be longer than
# about 800 characters.
This bar indicates the quality of the passphrase entered above.

As long as the bar is shown in red, GnuPG considers the passphrase too
weak to accept.  Please ask your administrator for details about the
configured passphrase constraints.
.


.gnupg.agent-problem
# 无法连接或启动 Gpg-Agent 。
无法连接到运行的 Gpg-Agent 或与正在运行的 Gpg-Agent 发生通信问题。

本系统使用一个称为 Gpg-Agent (Gpg-代理)的后台进程来处理私钥和请求密
码。代理通常在用户登录时启动，并在用户在线时运行。如果没有可用的代
理，系统会尝试动态启动一个代理，但该状态的代理在功能上有一定的限制，
可能会导致一些小问题。

您可能需要询问管理员以解决该问题。您也可以尝试注销并重新登录来尝试
解决此问题。无论如何，请通知管理员此错误，因为这表明软件中存在错误。
.


.gnupg.dirmngr-problem
# 无法连接到 dirmngr
无法连接到一个运行的 Dirmngr 或与正在运行的 Dirmngr 发生通信问题。

若要查找证书吊销列表（CRL），请执行OCSP验证并通过LDAP服务器查找公钥，
本系统依赖于名为 Dirmngr 的外部服务程序。Dirmngr 通常作为系统服务
（守护进程）运行，无需用户管理。当守护进程出现问题时，系统可能会为每
个请求启动自己的Dirmngr副本，这是一种性能有限的解决方法。

如果遇到此问题，应询问系统管理员如何解决。作为一个临时解决方案，您可
以尝试在 gpgsm 的配置中禁用 CRL 检查。
.


.gpg.edit_ownertrust.value
#标识前缀为“gpg”的帮助曾经为gpg的硬编码
#现在可能被此文件中的帮助文本覆盖。
在这里指定的数值完全由您自己决定；这些数值永远不会被导出给任何第三方。
我们需要它来实现“信任网络”；这与被隐式创建的“证书验证网络”无关。
.


.gpg.edit_ownertrust.set_ultimate.okay
要建立起信任网络，GnuPG 需要知道哪些密钥是可绝对信任的――通常就是
您拥有私钥的那些密钥。回答“yes”将此密钥设成可绝对信任的。
.


.gpg.untrusted_key.override
如果您坚持要求使用这把未被信任的公钥，请回答“yes”。
.


.gpg.pklist.user_id.enter
输入您要所发送报文的接收者的用户标识。
.


.gpg.keygen.algo
选择使用的算法。

DSA 即“数字签名算法”(曾用于美国国家标准DSS)，只能够用作签名。

Elgamal 是一种只能用作加密的算法。

RSA 可以用作签名或加密。

第一把密钥(主钥)必须具有签名的能力。
.


.gpg.keygen.algo.rsa_se
通常来说用同一把密钥签名和加密并不是个好主意。这个算法只在特定的情况
下使用。请事先咨询您的安全指导专家。
.


.gpg.keygen.cardkey
从卡中选择要使用的密钥。

列表显示了密钥索引，钥柄（keygrip，一个十六进制数字串），卡特别的密
钥引用，密钥算法。
括注为密钥允许的用法（cert 验证, sign 签名, auth 授权, encr 加密），
有星号标记的为密钥的标准用法。
.


.gpg.keygen.flags
选择密钥的功能。

密钥功能受限于所选算法。

要快速完成功能设置，可以输入“=”作为第一个字符，后跟字母列表；
指示设置的功能：“s”用于签名，“e”用于加密，而“a”用于身份验证；
无效字母和不可选的功能将被忽略。
使用快速设置后，此子菜单将立即结束。
.


.gpg.keygen.size
请输入密钥长度。

默认选项通常是个不错的选择。

如果您想使用一个较大的密钥长度，例如4096位，请认真考虑它是否真的对
你有意义，长密钥可能会降低性能。您可以参考该网页
    http://www.xkcd.com/538/
.


.gpg.keygen.size.huge.okay
请回答“yes”或“no”
.


.gpg.keygen.size.large.okay
请回答“yes”或“no”
.


.gpg.keygen.valid
请按提示输入所要求的数值。
您可以输入 ISO 日期格式(YYYY-MM-DD)，但出错时您可能不会得到友好的提
示――系统可能会尝试将其解释为时间间隔。
.


.gpg.keygen.valid.okay
请回答“yes”或“no”
.

.gpg.keygen.name
请输入密钥持有人的名字
“<”与“>”字符是不允许的。
例如：ZhangSan
.


.gpg.keygen.email
请输入电子邮件地址(可选项，但强烈推荐使用)
例如：<EMAIL>
.


.gpg.keygen.comment
请输入注释(可选项)
“(”与“)”字符是不允许的。
通常无需输入注释。
.


.gpg.keygen.userid.cmd
# （开头来一个空行）—— help.txt要求的

N  修改姓名。
C  修改注释。
E  修改电子邮件地址。
O  继续生成密钥。
Q  中止生成密钥。
.


.gpg.keygen.sub.okay
如果您允许生成子钥，请回答“yes”(或者“y”)。
.


.gpg.sign_uid.okay
请回答“yes”或“no”
.


.gpg.sign_uid.class
当您对某把密钥上某个用户标识进行签名认证时，您必须首先验证这把密钥
是否确实属于在用户标识上署名的那个人。而让他人了解您对此进行了多么
仔细的验证是非常必要的。

“0” 表示您对验证这把密钥所属者真实性问题不表态。

“1” 表示您相信这把密钥属于那个声称是主人的人，但是您不能或根本没
      有验证过。如果您为一把属于类似虚拟人物的密钥签名，这个选项很
      有用。

“2” 表示您随意地验证了那把密钥。例如，您验证了这把密钥的指纹，或
      比对了照片以验证用户标识。

“3” 表示您做了大量而详尽的验证密钥工作。例如，您同密钥持有人直接
      联系验证了密钥指纹，而且通过查验附带照片且难以伪造的证件（如
      身份证)确认了密钥持有人的姓名与密钥上的用户标识一致，最后您还
      （通过电子邮件往来）验证了密钥上的电子邮件地址确实属于密钥持
      有人。

请注意上述关于验证级别 2 和 3 的说明仅是例子而已，最终还是由您自己
决定当您为其他密钥签名时，什么是“随意”，而什么是“大量而详尽”。

如果您不知道应该选什么，请选“0”。
.


.gpg.change_passwd.empty.okay
请回答“yes”或“no”
.


.gpg.keyedit.save.okay
请回答“yes”或“no”
.


.gpg.keyedit.cancel.okay
请回答“yes”或“no”
.


.gpg.keyedit.sign_all.okay
如果您想要为所有用户标识签名的话就选“yes”
.


.gpg.keyedit.remove.uid.okay
如果您真的想要删除这个用户标识的话就回答“yes”。
所有相关认证在此之后也会丢失！
.

.gpg.keyedit.remove.subkey.okay
如果要删除这把子钥，请回答“yes”
.

.gpg.keyedit.delsig.valid
这是一份在这把密钥上有效的签名；通常您不会想要删除这份签名，因为要
与“这把密钥”或“拥有这把密钥的签名的密钥”建立认证关系可能相当重
要。
.

.gpg.keyedit.delsig.unknown
这份签名无法被检验，因为您没有相应的公钥。您应该暂缓删除它，直到您
知道此签名使用了哪一把密钥；因为用来签名的密钥可能与其他已经验证的
密钥存在信任关系。
.

.gpg.keyedit.delsig.invalid
这份签名无效。应当把它从您的钥匙环里删除。
.

.gpg.keyedit.delsig.selfsig
这是一份将密钥与用户标识相联系的签名，通常不应删除这样的签名。一旦
删除，GnuPG 将可能无法再使用这把密钥。因此，只有在这把密钥的第一个
自签名因某些原因失效，而拥有另一个可用自签名的情况下才这么做。
.

.gpg.keyedit.updpref.okay
用现有的首选项更新所有（或选定的）用户标识的首选项。所有受影响的自
签名的时间戳都会增加一秒钟。
.


.gpg.passphrase.enter
# （开头来一个空行）—— help.txt要求的

请输入密码：
.

.gpg.passphrase.repeat
请再次输入密码，以确认输入了正确的密码。
.

.gpg.detached_signature.filename
请给定要添加签名的文件名
.

.gpg.openfile.overwrite.okay
如果要覆盖这个文件，请回答“yes”
.

.gpg.openfile.askoutname
请输入一个新的文件名。直接按下回车以使用默认文件名（括号中）。
.

.gpg.ask_revocation_reason.code
您需要为这份吊销证书指定一个原因。根据情况的不同，您可以从下列清单中
选出一项：
  “密钥已泄漏”
      如果您相信有某个未经许可的人已取得了您的私钥，请选此项。
  “密钥已替换”
      如果您已用一把新密钥代替旧的，请选此项。
  “密钥不再被使用”
      如果您已决定让这把密钥退休，请选此项
  “用户标识不再有效”
      如果这个用户标识不再被使用了，请选此项；这通常用表明某个电子
      邮件地址已不再有效。
.

.gpg.ask_revocation_reason.text
# revoke.c (ask_revocation_reason)
您也可以输入一串文字，描述发布这份吊销证书的理由，请尽量简明扼要。
输入一个空行以结束输入。
.


.gpg.tofu.conflict
# tofu.c
TOFU 检测到了另一个电子邮件地址相同（或非常相似）的密钥。可能是用户
创建了一个新的密钥；在这种情况下，您可以放心地信任新密钥（请通过询
问此人来确认这一点）。但此密钥也可能是伪造的，或者有一个活跃的中间
人（MitM）攻击；在这种情况下，应该将密钥标记为不受信的，这样它就不
可信了。将密钥标记为不受信任意味着其任何签名都将被认为是无效的，并
将标记所有使用该密钥进行的加密。如果您不确定且当前无法检查，则应选
择接受一次或拒绝一次。
.


.gpgsm.root-cert-not-trusted
# This text gets displayed by the audit log if
# a root certificates was not trusted.
#如果根证书不受信任，审核日志将显示此文本。
根证书（信任基础）不受信任。根据配置，可能会提示您将根证书标记为受
信任的，或者您需要手动告诉 GnuPG 信任该证书。记载了受信任证书的
trustlist.txt 在GnuPG的主目录中。如有疑问，请询问系统管理员是否应信
任此证书。
.


.gpgsm.crl-problem
#当CRL或OCSP检查出现问题，审核日志将显示此文本。
您的配置在检索CRL或执行OCSP检查时出现问题。有很多不同的原因，查看手
册以了解可能的解决方案。
.


# Local variables:
# mode: fundamental
# coding: utf-8
# End:
