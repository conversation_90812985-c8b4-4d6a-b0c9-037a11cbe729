.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_list_import_url" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_list_import_url \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_list_import_url(gnutls_x509_crt_t ** " certs ", unsigned int * " size ", const char * " url ", gnutls_pin_callback_t " pin_fn ", void * " pin_fn_userdata ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t ** certs" 12
Will hold the allocated certificate list.
.IP "unsigned int * size" 12
It will contain the size of the list.
.IP "const char * url" 12
A PKCS 11 url
.IP "gnutls_pin_callback_t pin_fn" 12
a PIN callback if not globally set
.IP "void * pin_fn_userdata" 12
parameter for the PIN callback
.IP "unsigned int flags" 12
One of GNUTLS_PKCS11_OBJ_* flags for PKCS\fB11\fP URLs or zero otherwise
.SH "DESCRIPTION"
This function will import a certificate chain present in a PKCS\fB11\fP token
or any type of back\-end that supports URLs. The certificates
must be deinitialized afterwards using \fBgnutls_x509_crt_deinit()\fP
and the returned pointer must be freed using \fBgnutls_free()\fP.

The URI provided must be the first certificate in the chain; subsequent
certificates will be retrieved using \fBgnutls_pkcs11_get_raw_issuer()\fP or
equivalent functionality for the supported URI.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
