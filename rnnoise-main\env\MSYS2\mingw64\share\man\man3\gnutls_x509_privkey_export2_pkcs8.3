.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_export2_pkcs8" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_export2_pkcs8 \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_privkey_export2_pkcs8(gnutls_x509_privkey_t " key ", gnutls_x509_crt_fmt_t " format ", const char * " password ", unsigned int " flags ", gnutls_datum_t * " out ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t key" 12
Holds the key
.IP "gnutls_x509_crt_fmt_t format" 12
the format of output params. One of PEM or DER.
.IP "const char * password" 12
the password that will be used to encrypt the key.
.IP "unsigned int flags" 12
an ORed sequence of gnutls_pkcs_encrypt_flags_t
.IP "gnutls_datum_t * out" 12
will contain a private key PEM or DER encoded
.SH "DESCRIPTION"
This function will export the private key to a PKCS8 structure.
Both RSA and DSA keys can be exported. For DSA keys we use
PKCS \fB11\fP definitions. If the flags do not specify the encryption
cipher, then the default 3DES (PBES2) will be used.

The  \fIpassword\fP can be either ASCII or UTF\-8 in the default PBES2
encryption schemas, or ASCII for the PKCS12 schemas.

The output buffer is allocated using \fBgnutls_malloc()\fP.

If the structure is PEM encoded, it will have a header
of "BEGIN ENCRYPTED PRIVATE KEY" or "BEGIN PRIVATE KEY" if
encryption is not used.
.SH "RETURNS"
In case of failure a negative error code will be
returned, and 0 on success.

Since 3.1.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
