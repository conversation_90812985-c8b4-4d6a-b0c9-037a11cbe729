<?xml version="1.0"?>
<!DOCTYPE syscalls_info SYSTEM "gdb-syscalls.dtd">
<!-- Copyright (C) 2009-2024 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->
<!-- This file was generated using the following file:

     arch/s390/kernel/syscalls/syscall.tbl

     The file mentioned above belongs to the Linux Kernel.  -->
<syscalls_info>
  <syscall name="exit" number="1" groups="process"/>
  <syscall name="fork" number="2" groups="process"/>
  <syscall name="read" number="3" groups="descriptor"/>
  <syscall name="write" number="4" groups="descriptor"/>
  <syscall name="open" number="5" groups="descriptor,file"/>
  <syscall name="close" number="6" groups="descriptor"/>
  <syscall name="restart_syscall" number="7"/>
  <syscall name="creat" number="8" groups="descriptor,file"/>
  <syscall name="link" number="9" groups="file"/>
  <syscall name="unlink" number="10" groups="file"/>
  <syscall name="execve" number="11" groups="file,process"/>
  <syscall name="chdir" number="12" groups="file"/>
  <syscall name="mknod" number="14" groups="file"/>
  <syscall name="chmod" number="15" groups="file"/>
  <syscall name="lseek" number="19" groups="descriptor"/>
  <syscall name="getpid" number="20"/>
  <syscall name="mount" number="21" groups="file"/>
  <syscall name="umount" number="22" groups="file"/>
  <syscall name="ptrace" number="26"/>
  <syscall name="alarm" number="27"/>
  <syscall name="pause" number="29" groups="signal"/>
  <syscall name="utime" number="30" groups="file"/>
  <syscall name="access" number="33" groups="file"/>
  <syscall name="nice" number="34"/>
  <syscall name="sync" number="36"/>
  <syscall name="kill" number="37" groups="signal,process"/>
  <syscall name="rename" number="38" groups="file"/>
  <syscall name="mkdir" number="39" groups="file"/>
  <syscall name="rmdir" number="40" groups="file"/>
  <syscall name="dup" number="41" groups="descriptor"/>
  <syscall name="pipe" number="42" groups="descriptor"/>
  <syscall name="times" number="43"/>
  <syscall name="brk" number="45" groups="memory"/>
  <syscall name="signal" number="48" groups="signal"/>
  <syscall name="acct" number="51" groups="file"/>
  <syscall name="umount2" number="52" groups="file"/>
  <syscall name="ioctl" number="54" groups="descriptor"/>
  <syscall name="fcntl" number="55" groups="descriptor"/>
  <syscall name="setpgid" number="57"/>
  <syscall name="umask" number="60"/>
  <syscall name="chroot" number="61" groups="file"/>
  <syscall name="ustat" number="62"/>
  <syscall name="dup2" number="63" groups="descriptor"/>
  <syscall name="getppid" number="64"/>
  <syscall name="getpgrp" number="65"/>
  <syscall name="setsid" number="66"/>
  <syscall name="sigaction" number="67" groups="signal"/>
  <syscall name="sigsuspend" number="72" groups="signal"/>
  <syscall name="sigpending" number="73" groups="signal"/>
  <syscall name="sethostname" number="74"/>
  <syscall name="setrlimit" number="75"/>
  <syscall name="getrusage" number="77"/>
  <syscall name="gettimeofday" number="78"/>
  <syscall name="settimeofday" number="79"/>
  <syscall name="symlink" number="83" groups="file"/>
  <syscall name="readlink" number="85" groups="file"/>
  <syscall name="uselib" number="86" groups="file"/>
  <syscall name="swapon" number="87" groups="file"/>
  <syscall name="reboot" number="88"/>
  <syscall name="readdir" number="89" groups="descriptor"/>
  <syscall name="mmap" number="90" groups="descriptor,memory"/>
  <syscall name="munmap" number="91" groups="memory"/>
  <syscall name="truncate" number="92" groups="file"/>
  <syscall name="ftruncate" number="93" groups="descriptor"/>
  <syscall name="fchmod" number="94" groups="descriptor"/>
  <syscall name="getpriority" number="96"/>
  <syscall name="setpriority" number="97"/>
  <syscall name="statfs" number="99" groups="file"/>
  <syscall name="fstatfs" number="100" groups="descriptor"/>
  <syscall name="socketcall" number="102" groups="descriptor"/>
  <syscall name="syslog" number="103"/>
  <syscall name="setitimer" number="104"/>
  <syscall name="getitimer" number="105"/>
  <syscall name="stat" number="106" groups="file"/>
  <syscall name="lstat" number="107" groups="file"/>
  <syscall name="fstat" number="108" groups="descriptor"/>
  <syscall name="lookup_dcookie" number="110"/>
  <syscall name="vhangup" number="111"/>
  <syscall name="idle" number="112"/>
  <syscall name="wait4" number="114" groups="process"/>
  <syscall name="swapoff" number="115" groups="file"/>
  <syscall name="sysinfo" number="116"/>
  <syscall name="ipc" number="117" groups="ipc"/>
  <syscall name="fsync" number="118" groups="descriptor"/>
  <syscall name="sigreturn" number="119" groups="signal"/>
  <syscall name="clone" number="120" groups="process"/>
  <syscall name="setdomainname" number="121"/>
  <syscall name="uname" number="122"/>
  <syscall name="adjtimex" number="124"/>
  <syscall name="mprotect" number="125" groups="memory"/>
  <syscall name="sigprocmask" number="126" groups="signal"/>
  <syscall name="create_module" number="127"/>
  <syscall name="init_module" number="128"/>
  <syscall name="delete_module" number="129"/>
  <syscall name="get_kernel_syms" number="130"/>
  <syscall name="quotactl" number="131" groups="file"/>
  <syscall name="getpgid" number="132"/>
  <syscall name="fchdir" number="133" groups="descriptor"/>
  <syscall name="bdflush" number="134"/>
  <syscall name="sysfs" number="135"/>
  <syscall name="personality" number="136"/>
  <syscall name="afs_syscall" number="137"/>
  <syscall name="getdents" number="141" groups="descriptor"/>
  <syscall name="select" number="142" groups="descriptor"/>
  <syscall name="flock" number="143" groups="descriptor"/>
  <syscall name="msync" number="144" groups="memory"/>
  <syscall name="readv" number="145" groups="descriptor"/>
  <syscall name="writev" number="146" groups="descriptor"/>
  <syscall name="getsid" number="147"/>
  <syscall name="fdatasync" number="148" groups="descriptor"/>
  <syscall name="_sysctl" number="149"/>
  <syscall name="mlock" number="150" groups="memory"/>
  <syscall name="munlock" number="151" groups="memory"/>
  <syscall name="mlockall" number="152" groups="memory"/>
  <syscall name="munlockall" number="153" groups="memory"/>
  <syscall name="sched_setparam" number="154"/>
  <syscall name="sched_getparam" number="155"/>
  <syscall name="sched_setscheduler" number="156"/>
  <syscall name="sched_getscheduler" number="157"/>
  <syscall name="sched_yield" number="158"/>
  <syscall name="sched_get_priority_max" number="159"/>
  <syscall name="sched_get_priority_min" number="160"/>
  <syscall name="sched_rr_get_interval" number="161"/>
  <syscall name="nanosleep" number="162"/>
  <syscall name="mremap" number="163" groups="memory"/>
  <syscall name="query_module" number="167"/>
  <syscall name="poll" number="168" groups="descriptor"/>
  <syscall name="nfsservctl" number="169"/>
  <syscall name="prctl" number="172"/>
  <syscall name="rt_sigreturn" number="173" groups="signal"/>
  <syscall name="rt_sigaction" number="174" groups="signal"/>
  <syscall name="rt_sigprocmask" number="175" groups="signal"/>
  <syscall name="rt_sigpending" number="176" groups="signal"/>
  <syscall name="rt_sigtimedwait" number="177" groups="signal"/>
  <syscall name="rt_sigqueueinfo" number="178" groups="signal,process"/>
  <syscall name="rt_sigsuspend" number="179" groups="signal"/>
  <syscall name="pread64" number="180" groups="descriptor"/>
  <syscall name="pwrite64" number="181" groups="descriptor"/>
  <syscall name="getcwd" number="183" groups="file"/>
  <syscall name="capget" number="184"/>
  <syscall name="capset" number="185"/>
  <syscall name="sigaltstack" number="186" groups="signal"/>
  <syscall name="sendfile" number="187" groups="descriptor,network"/>
  <syscall name="getpmsg" number="188" groups="network"/>
  <syscall name="putpmsg" number="189" groups="network"/>
  <syscall name="vfork" number="190" groups="process"/>
  <syscall name="getrlimit" number="191"/>
  <syscall name="lchown" number="198" groups="file"/>
  <syscall name="getuid" number="199"/>
  <syscall name="getgid" number="200"/>
  <syscall name="geteuid" number="201"/>
  <syscall name="getegid" number="202"/>
  <syscall name="setreuid" number="203"/>
  <syscall name="setregid" number="204"/>
  <syscall name="getgroups" number="205"/>
  <syscall name="setgroups" number="206"/>
  <syscall name="fchown" number="207" groups="descriptor"/>
  <syscall name="setresuid" number="208"/>
  <syscall name="getresuid" number="209"/>
  <syscall name="setresgid" number="210"/>
  <syscall name="getresgid" number="211"/>
  <syscall name="chown" number="212" groups="file"/>
  <syscall name="setuid" number="213"/>
  <syscall name="setgid" number="214"/>
  <syscall name="setfsuid" number="215"/>
  <syscall name="setfsgid" number="216"/>
  <syscall name="pivot_root" number="217" groups="file"/>
  <syscall name="mincore" number="218" groups="memory"/>
  <syscall name="madvise" number="219" groups="memory"/>
  <syscall name="getdents64" number="220" groups="descriptor"/>
  <syscall name="readahead" number="222" groups="descriptor"/>
  <syscall name="setxattr" number="224" groups="file"/>
  <syscall name="lsetxattr" number="225" groups="file"/>
  <syscall name="fsetxattr" number="226" groups="descriptor"/>
  <syscall name="getxattr" number="227" groups="file"/>
  <syscall name="lgetxattr" number="228" groups="file"/>
  <syscall name="fgetxattr" number="229" groups="descriptor"/>
  <syscall name="listxattr" number="230" groups="file"/>
  <syscall name="llistxattr" number="231" groups="file"/>
  <syscall name="flistxattr" number="232" groups="descriptor"/>
  <syscall name="removexattr" number="233" groups="file"/>
  <syscall name="lremovexattr" number="234" groups="file"/>
  <syscall name="fremovexattr" number="235" groups="descriptor"/>
  <syscall name="gettid" number="236"/>
  <syscall name="tkill" number="237" groups="signal,process"/>
  <syscall name="futex" number="238"/>
  <syscall name="sched_setaffinity" number="239"/>
  <syscall name="sched_getaffinity" number="240"/>
  <syscall name="tgkill" number="241" groups="signal,process"/>
  <syscall name="io_setup" number="243" groups="memory"/>
  <syscall name="io_destroy" number="244" groups="memory"/>
  <syscall name="io_getevents" number="245"/>
  <syscall name="io_submit" number="246"/>
  <syscall name="io_cancel" number="247"/>
  <syscall name="exit_group" number="248" groups="process"/>
  <syscall name="epoll_create" number="249" groups="descriptor"/>
  <syscall name="epoll_ctl" number="250" groups="descriptor"/>
  <syscall name="epoll_wait" number="251" groups="descriptor"/>
  <syscall name="set_tid_address" number="252"/>
  <syscall name="fadvise64" number="253" groups="descriptor"/>
  <syscall name="timer_create" number="254"/>
  <syscall name="timer_settime" number="255"/>
  <syscall name="timer_gettime" number="256"/>
  <syscall name="timer_getoverrun" number="257"/>
  <syscall name="timer_delete" number="258"/>
  <syscall name="clock_settime" number="259"/>
  <syscall name="clock_gettime" number="260"/>
  <syscall name="clock_getres" number="261"/>
  <syscall name="clock_nanosleep" number="262"/>
  <syscall name="statfs64" number="265" groups="file"/>
  <syscall name="fstatfs64" number="266" groups="descriptor"/>
  <syscall name="remap_file_pages" number="267" groups="memory"/>
  <syscall name="mbind" number="268" groups="memory"/>
  <syscall name="get_mempolicy" number="269" groups="memory"/>
  <syscall name="set_mempolicy" number="270" groups="memory"/>
  <syscall name="mq_open" number="271" groups="descriptor"/>
  <syscall name="mq_unlink" number="272"/>
  <syscall name="mq_timedsend" number="273" groups="descriptor"/>
  <syscall name="mq_timedreceive" number="274" groups="descriptor"/>
  <syscall name="mq_notify" number="275" groups="descriptor"/>
  <syscall name="mq_getsetattr" number="276" groups="descriptor"/>
  <syscall name="kexec_load" number="277"/>
  <syscall name="add_key" number="278"/>
  <syscall name="request_key" number="279"/>
  <syscall name="keyctl" number="280"/>
  <syscall name="waitid" number="281" groups="process"/>
  <syscall name="ioprio_set" number="282"/>
  <syscall name="ioprio_get" number="283"/>
  <syscall name="inotify_init" number="284" groups="descriptor"/>
  <syscall name="inotify_add_watch" number="285" groups="descriptor,file"/>
  <syscall name="inotify_rm_watch" number="286" groups="descriptor"/>
  <syscall name="migrate_pages" number="287" groups="memory"/>
  <syscall name="openat" number="288" groups="descriptor,file"/>
  <syscall name="mkdirat" number="289" groups="descriptor,file"/>
  <syscall name="mknodat" number="290" groups="descriptor,file"/>
  <syscall name="fchownat" number="291" groups="descriptor,file"/>
  <syscall name="futimesat" number="292" groups="descriptor,file"/>
  <syscall name="newfstatat" number="293" groups="descriptor,file"/>
  <syscall name="unlinkat" number="294" groups="descriptor,file"/>
  <syscall name="renameat" number="295" groups="descriptor,file"/>
  <syscall name="linkat" number="296" groups="descriptor,file"/>
  <syscall name="symlinkat" number="297" groups="descriptor,file"/>
  <syscall name="readlinkat" number="298" groups="descriptor,file"/>
  <syscall name="fchmodat" number="299" groups="descriptor,file"/>
  <syscall name="faccessat" number="300" groups="descriptor,file"/>
  <syscall name="pselect6" number="301" groups="descriptor"/>
  <syscall name="ppoll" number="302" groups="descriptor"/>
  <syscall name="unshare" number="303"/>
  <syscall name="set_robust_list" number="304"/>
  <syscall name="get_robust_list" number="305"/>
  <syscall name="splice" number="306" groups="descriptor"/>
  <syscall name="sync_file_range" number="307" groups="descriptor"/>
  <syscall name="tee" number="308" groups="descriptor"/>
  <syscall name="vmsplice" number="309" groups="descriptor"/>
  <syscall name="move_pages" number="310" groups="memory"/>
  <syscall name="getcpu" number="311"/>
  <syscall name="epoll_pwait" number="312" groups="descriptor"/>
  <syscall name="utimes" number="313" groups="file"/>
  <syscall name="fallocate" number="314" groups="descriptor"/>
  <syscall name="utimensat" number="315" groups="descriptor,file"/>
  <syscall name="signalfd" number="316" groups="descriptor,signal"/>
  <syscall name="timerfd" number="317" groups="descriptor"/>
  <syscall name="eventfd" number="318" groups="descriptor"/>
  <syscall name="timerfd_create" number="319" groups="descriptor"/>
  <syscall name="timerfd_settime" number="320" groups="descriptor"/>
  <syscall name="timerfd_gettime" number="321" groups="descriptor"/>
  <syscall name="signalfd4" number="322" groups="descriptor,signal"/>
  <syscall name="eventfd2" number="323" groups="descriptor"/>
  <syscall name="inotify_init1" number="324" groups="descriptor"/>
  <syscall name="pipe2" number="325" groups="descriptor"/>
  <syscall name="dup3" number="326" groups="descriptor"/>
  <syscall name="epoll_create1" number="327" groups="descriptor"/>
  <syscall name="preadv" number="328" groups="descriptor"/>
  <syscall name="pwritev" number="329" groups="descriptor"/>
  <syscall name="rt_tgsigqueueinfo" number="330" groups="process,signal"/>
  <syscall name="perf_event_open" number="331" groups="descriptor"/>
  <syscall name="fanotify_init" number="332" groups="descriptor"/>
  <syscall name="fanotify_mark" number="333" groups="descriptor,file"/>
  <syscall name="prlimit64" number="334"/>
  <syscall name="name_to_handle_at" number="335" groups="descriptor,file"/>
  <syscall name="open_by_handle_at" number="336" groups="descriptor"/>
  <syscall name="clock_adjtime" number="337"/>
  <syscall name="syncfs" number="338" groups="descriptor"/>
  <syscall name="setns" number="339" groups="descriptor"/>
  <syscall name="process_vm_readv" number="340"/>
  <syscall name="process_vm_writev" number="341"/>
  <syscall name="s390_runtime_instr" number="342"/>
  <syscall name="kcmp" number="343"/>
  <syscall name="finit_module" number="344" groups="descriptor"/>
  <syscall name="sched_setattr" number="345"/>
  <syscall name="sched_getattr" number="346"/>
  <syscall name="renameat2" number="347" groups="descriptor,file"/>
  <syscall name="seccomp" number="348"/>
  <syscall name="getrandom" number="349"/>
  <syscall name="memfd_create" number="350" groups="descriptor"/>
  <syscall name="bpf" number="351" groups="descriptor"/>
  <syscall name="s390_pci_mmio_write" number="352"/>
  <syscall name="s390_pci_mmio_read" number="353"/>
  <syscall name="execveat" number="354" groups="descriptor,file,process"/>
  <syscall name="userfaultfd" number="355" groups="descriptor"/>
  <syscall name="membarrier" number="356"/>
  <syscall name="recvmmsg" number="357" groups="network"/>
  <syscall name="sendmmsg" number="358" groups="network"/>
  <syscall name="socket" number="359" groups="network"/>
  <syscall name="socketpair" number="360" groups="network"/>
  <syscall name="bind" number="361" groups="network"/>
  <syscall name="connect" number="362" groups="network"/>
  <syscall name="listen" number="363" groups="network"/>
  <syscall name="accept4" number="364" groups="network"/>
  <syscall name="getsockopt" number="365" groups="network"/>
  <syscall name="setsockopt" number="366" groups="network"/>
  <syscall name="getsockname" number="367" groups="network"/>
  <syscall name="getpeername" number="368" groups="network"/>
  <syscall name="sendto" number="369" groups="network"/>
  <syscall name="sendmsg" number="370" groups="network"/>
  <syscall name="recvfrom" number="371" groups="network"/>
  <syscall name="recvmsg" number="372" groups="network"/>
  <syscall name="shutdown" number="373" groups="network"/>
  <syscall name="mlock2" number="374" groups="memory"/>
  <syscall name="copy_file_range" number="375" groups="descriptor"/>
  <syscall name="preadv2" number="376" groups="descriptor"/>
  <syscall name="pwritev2" number="377" groups="descriptor"/>
  <syscall name="s390_guarded_storage" number="378"/>
  <syscall name="statx" number="379" groups="descriptor,file"/>
  <syscall name="s390_sthyi" number="380"/>
  <syscall name="kexec_file_load" number="381" groups="descriptor"/>
  <syscall name="io_pgetevents" number="382"/>
  <syscall name="rseq" number="383"/>
  <syscall name="pkey_mprotect" number="384" groups="memory"/>
  <syscall name="pkey_alloc" number="385"/>
  <syscall name="pkey_free" number="386"/>
  <syscall name="semtimedop" number="392" groups="ipc"/>
  <syscall name="semget" number="393" groups="ipc"/>
  <syscall name="semctl" number="394" groups="ipc"/>
  <syscall name="shmget" number="395" groups="ipc"/>
  <syscall name="shmctl" number="396" groups="ipc"/>
  <syscall name="shmat" number="397" groups="ipc,memory"/>
  <syscall name="shmdt" number="398" groups="ipc,memory"/>
  <syscall name="msgget" number="399" groups="ipc"/>
  <syscall name="msgsnd" number="400" groups="ipc"/>
  <syscall name="msgrcv" number="401" groups="ipc"/>
  <syscall name="msgctl" number="402" groups="ipc"/>
  <syscall name="pidfd_send_signal" number="424" groups="descriptor,signal,process"/>
  <syscall name="io_uring_setup" number="425" groups="descriptor"/>
  <syscall name="io_uring_enter" number="426" groups="descriptor,signal"/>
  <syscall name="io_uring_register" number="427" groups="descriptor,memory"/>
  <syscall name="open_tree" number="428" groups="descriptor,file"/>
  <syscall name="move_mount" number="429" groups="descriptor,file"/>
  <syscall name="fsopen" number="430" groups="descriptor"/>
  <syscall name="fsconfig" number="431" groups="descriptor,file"/>
  <syscall name="fsmount" number="432" groups="descriptor"/>
  <syscall name="fspick" number="433" groups="descriptor,file"/>
  <syscall name="pidfd_open" number="434" groups="descriptor"/>
  <syscall name="clone3" number="435" groups="process"/>
  <syscall name="close_range" number="436"/>
  <syscall name="openat2" number="437" groups="descriptor,file"/>
  <syscall name="pidfd_getfd" number="438" groups="descriptor"/>
  <syscall name="faccessat2" number="439" groups="descriptor,file"/>
  <syscall name="process_madvise" number="440" groups="descriptor"/>
  <syscall name="epoll_pwait2" number="441" groups="descriptor"/>
  <syscall name="mount_setattr" number="442" groups="descriptor,file"/>
  <syscall name="quotactl_fd" number="443" groups="descriptor"/>
  <syscall name="landlock_create_ruleset" number="444" groups="descriptor"/>
  <syscall name="landlock_add_rule" number="445" groups="descriptor"/>
  <syscall name="landlock_restrict_self" number="446" groups="descriptor"/>
  <syscall name="memfd_secret" number="447" groups="descriptor"/>
  <syscall name="process_mrelease" number="448" groups="descriptor"/>
  <syscall name="futex_waitv" number="449"/>
  <syscall name="set_mempolicy_home_node" number="450" groups="memory"/>
  <syscall name="cachestat" number="451" groups="descriptor"/>
  <syscall name="fchmodat2" number="452" groups="descriptor,file"/>
  <syscall name="map_shadow_stack" number="453" groups="memory"/>
  <syscall name="futex_wake" number="454"/>
  <syscall name="futex_wait" number="455"/>
  <syscall name="futex_requeue" number="456"/>
  <syscall name="statmount" number="457"/>
  <syscall name="listmount" number="458"/>
  <syscall name="lsm_get_self_attr" number="459"/>
  <syscall name="lsm_set_self_attr" number="460"/>
  <syscall name="lsm_list_modules" number="461"/>
  <syscall name="mseal" number="462" groups="memory"/>
  <syscall name="setxattrat" number="463"/>
  <syscall name="getxattrat" number="464"/>
  <syscall name="listxattrat" number="465"/>
  <syscall name="removexattrat" number="466"/>
</syscalls_info>
