# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.



# The name this table is to be known by, with the format of the mappings in
# the main body of the table, and what all code points missing from this file
# map to.
$Unicode::UCD::SwashInfo{'ToNFKCQC'}{'format'} = 's'; # string
$Unicode::UCD::SwashInfo{'ToNFKCQC'}{'missing'} = 'Yes';

return <<'END';
A0		N
A8		N
AA		N
AF		N
B2	B5	N
B8	BA	N
BC	BE	N
132	133	N
13F	140	N
149		N
17F		N
1C4	1CC	N
1F1	1F3	N
2B0	2B8	N
2D8	2DD	N
2E0	2E4	N
300	304	M
306	30C	M
30F		M
311		M
313	314	M
31B		M
323	328	M
32D	32E	M
330	331	M
338		M
340	341	N
342		M
343	344	N
345		M
374		N
37A		N
37E		N
384	385	N
387		N
3D0	3D6	N
3F0	3F2	N
3F4	3F5	N
3F9		N
587		N
653	655	M
675	678	N
93C		M
958	95F	N
9BE		M
9D7		M
9DC	9DD	N
9DF		N
A33		N
A36		N
A59	A5B	N
A5E		N
B3E		M
B56	B57	M
B5C	B5D	N
BBE		M
BD7		M
C56		M
CC2		M
CD5	CD6	M
D3E		M
D57		M
DCA		M
DCF		M
DDF		M
E33		N
EB3		N
EDC	EDD	N
F0C		N
F43		N
F4D		N
F52		N
F57		N
F5C		N
F69		N
F73		N
F75	F79	N
F81		N
F93		N
F9D		N
FA2		N
FA7		N
FAC		N
FB9		N
102E		M
10FC		N
1161	1175	M
11A8	11C2	M
1B35		M
1D2C	1D2E	N
1D30	1D3A	N
1D3C	1D4D	N
1D4F	1D6A	N
1D78		N
1D9B	1DBF	N
1E9A	1E9B	N
1F71		N
1F73		N
1F75		N
1F77		N
1F79		N
1F7B		N
1F7D		N
1FBB		N
1FBD	1FC1	N
1FC9		N
1FCB		N
1FCD	1FCF	N
1FD3		N
1FDB		N
1FDD	1FDF	N
1FE3		N
1FEB		N
1FED	1FEF	N
1FF9		N
1FFB		N
1FFD	1FFE	N
2000	200A	N
2011		N
2017		N
2024	2026	N
202F		N
2033	2034	N
2036	2037	N
203C		N
203E		N
2047	2049	N
2057		N
205F		N
2070	2071	N
2074	208E	N
2090	209C	N
20A8		N
2100	2103	N
2105	2107	N
2109	2113	N
2115	2116	N
2119	211D	N
2120	2122	N
2124		N
2126		N
2128		N
212A	212D	N
212F	2131	N
2133	2139	N
213B	2140	N
2145	2149	N
2150	217F	N
2189		N
222C	222D	N
222F	2230	N
2329	232A	N
2460	24EA	N
2A0C		N
2A74	2A76	N
2ADC		N
2C7C	2C7D	N
2D6F		N
2E9F		N
2EF3		N
2F00	2FD5	N
3000		N
3036		N
3038	303A	N
3099	309A	M
309B	309C	N
309F		N
30FF		N
3131	318E	N
3192	319F	N
3200	321E	N
3220	3247	N
3250	327E	N
3280	33FF	N
A69C	A69D	N
A770		N
A7F2	A7F4	N
A7F8	A7F9	N
AB5C	AB5F	N
AB69		N
F900	FA0D	N
FA10		N
FA12		N
FA15	FA1E	N
FA20		N
FA22		N
FA25	FA26	N
FA2A	FA6D	N
FA70	FAD9	N
FB00	FB06	N
FB13	FB17	N
FB1D		N
FB1F	FB36	N
FB38	FB3C	N
FB3E		N
FB40	FB41	N
FB43	FB44	N
FB46	FBB1	N
FBD3	FD3D	N
FD50	FD8F	N
FD92	FDC7	N
FDF0	FDFC	N
FE10	FE19	N
FE30	FE44	N
FE47	FE52	N
FE54	FE66	N
FE68	FE6B	N
FE70	FE72	N
FE74		N
FE76	FEFC	N
FF01	FFBE	N
FFC2	FFC7	N
FFCA	FFCF	N
FFD2	FFD7	N
FFDA	FFDC	N
FFE0	FFE6	N
FFE8	FFEE	N
10781	10785	N
10787	107B0	N
107B2	107BA	N
110BA		M
11127		M
1133E		M
11357		M
114B0		M
114BA		M
114BD		M
115AF		M
11930		M
1D15E	1D164	N
1D1BB	1D1C0	N
1D400	1D454	N
1D456	1D49C	N
1D49E	1D49F	N
1D4A2		N
1D4A5	1D4A6	N
1D4A9	1D4AC	N
1D4AE	1D4B9	N
1D4BB		N
1D4BD	1D4C3	N
1D4C5	1D505	N
1D507	1D50A	N
1D50D	1D514	N
1D516	1D51C	N
1D51E	1D539	N
1D53B	1D53E	N
1D540	1D544	N
1D546		N
1D54A	1D550	N
1D552	1D6A5	N
1D6A8	1D7CB	N
1D7CE	1D7FF	N
1E030	1E06D	N
1EE00	1EE03	N
1EE05	1EE1F	N
1EE21	1EE22	N
1EE24		N
1EE27		N
1EE29	1EE32	N
1EE34	1EE37	N
1EE39		N
1EE3B		N
1EE42		N
1EE47		N
1EE49		N
1EE4B		N
1EE4D	1EE4F	N
1EE51	1EE52	N
1EE54		N
1EE57		N
1EE59		N
1EE5B		N
1EE5D		N
1EE5F		N
1EE61	1EE62	N
1EE64		N
1EE67	1EE6A	N
1EE6C	1EE72	N
1EE74	1EE77	N
1EE79	1EE7C	N
1EE7E		N
1EE80	1EE89	N
1EE8B	1EE9B	N
1EEA1	1EEA3	N
1EEA5	1EEA9	N
1EEAB	1EEBB	N
1F100	1F10A	N
1F110	1F12E	N
1F130	1F14F	N
1F16A	1F16C	N
1F190		N
1F200	1F202	N
1F210	1F23B	N
1F240	1F248	N
1F250	1F251	N
1FBF0	1FBF9	N
2F800	2FA1D	N
END
