.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_memcmp" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_memcmp \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_memcmp(const void * " s1 ", const void * " s2 ", size_t " n ");"
.SH ARGUMENTS
.IP "const void * s1" 12
the first address to compare
.IP "const void * s2" 12
the second address to compare
.IP "size_t n" 12
the size of memory to compare
.SH "DESCRIPTION"
This function will operate similarly to \fBmemcmp()\fP, but will operate
on time that depends only on the size of the string. That is will
not return early if the strings don't match on the first byte.
.SH "RETURNS"
non zero on difference and zero if the buffers are identical.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
