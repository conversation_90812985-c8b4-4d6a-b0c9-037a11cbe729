set ::msgcat::header "Project-Id-Version: gitk\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2010-12-06 23:39-0200\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: Brazilian Portuguese <>\nLanguage: pt_BR\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\n"
::msgcat::mcset pt_br "Couldn't get list of unmerged files:" "N\u00e3o foi poss\u00edvel obter a lista dos arquivos n\u00e3o mesclados:"
::msgcat::mcset pt_br "Error parsing revisions:" "Erro ao interpretar revis\u00f5es:"
::msgcat::mcset pt_br "Error executing --argscmd command:" "Erro ao executar o comando--argscmd:"
::msgcat::mcset pt_br "No files selected: --merge specified but no files are unmerged." "Nenhum arquivo foi selecionado: --merge especificado mas n\u00e3o h\u00e1 arquivos n\u00e3o-mesclados."
::msgcat::mcset pt_br "No files selected: --merge specified but no unmerged files are within file limit." "Nenhum arquivo foi selecionado: --merge especificado mas n\u00e3o h\u00e1 arquivos n\u00e3o-mesclados dentro dos limites."
::msgcat::mcset pt_br "Error executing git log:" "Erro ao executar git log:"
::msgcat::mcset pt_br "Reading" "Lendo"
::msgcat::mcset pt_br "Reading commits..." "Lendo revis\u00f5es..."
::msgcat::mcset pt_br "No commits selected" "Nenhuma revis\u00e3o foi selecionada"
::msgcat::mcset pt_br "Command line" "Linha de comando"
::msgcat::mcset pt_br "Can't parse git log output:" "N\u00e3o foi poss\u00edvel interpretar a sa\u00edda do \"git log\":"
::msgcat::mcset pt_br "No commit information available" "N\u00e3o h\u00e1 informa\u00e7\u00f5es dispon\u00edveis sobre a revis\u00e3o"
::msgcat::mcset pt_br "OK" "Ok"
::msgcat::mcset pt_br "Cancel" "Cancelar"
::msgcat::mcset pt_br "&Update" "Atualizar"
::msgcat::mcset pt_br "&Reload" "Recarregar"
::msgcat::mcset pt_br "Reread re&ferences" "Ler as refer\u00eancias novamente"
::msgcat::mcset pt_br "&List references" "Listar refer\u00eancias"
::msgcat::mcset pt_br "Start git &gui" "Iniciar Git GUI"
::msgcat::mcset pt_br "&Quit" "Sair"
::msgcat::mcset pt_br "&File" "Arquivo"
::msgcat::mcset pt_br "&Preferences" "Prefer\u00eancias"
::msgcat::mcset pt_br "&Edit" "Editar"
::msgcat::mcset pt_br "&New view..." "Nova vista..."
::msgcat::mcset pt_br "&Edit view..." "Editar vista..."
::msgcat::mcset pt_br "&Delete view" "Apagar vista"
::msgcat::mcset pt_br "&All files" "Todos os arquivos"
::msgcat::mcset pt_br "&View" "Exibir"
::msgcat::mcset pt_br "&About gitk" "Sobre o gitk"
::msgcat::mcset pt_br "&Key bindings" "Atalhos de teclado"
::msgcat::mcset pt_br "&Help" "Ajuda"
::msgcat::mcset pt_br "SHA1 ID:" "SHA1 ID:"
::msgcat::mcset pt_br "Row" "Linha"
::msgcat::mcset pt_br "Find" "Encontrar"
::msgcat::mcset pt_br "commit" "Revis\u00e3o"
::msgcat::mcset pt_br "containing:" "contendo:"
::msgcat::mcset pt_br "touching paths:" "envolvendo os caminhos:"
::msgcat::mcset pt_br "adding/removing string:" "Adicionando/removendo texto:"
::msgcat::mcset pt_br "Exact" "Exatamente"
::msgcat::mcset pt_br "IgnCase" "Ignorar mai\u00fasculas/min\u00fasculas"
::msgcat::mcset pt_br "Regexp" "Express\u00e3o regular"
::msgcat::mcset pt_br "All fields" "Todos os campos"
::msgcat::mcset pt_br "Headline" "Assunto"
::msgcat::mcset pt_br "Comments" "Descri\u00e7\u00e3o da revis\u00e3o"
::msgcat::mcset pt_br "Author" "Autor"
::msgcat::mcset pt_br "Committer" "Revisor"
::msgcat::mcset pt_br "Search" "Buscar"
::msgcat::mcset pt_br "Diff" "Diferen\u00e7as"
::msgcat::mcset pt_br "Old version" "Vers\u00e3o antiga"
::msgcat::mcset pt_br "New version" "Vers\u00e3o nova"
::msgcat::mcset pt_br "Lines of context" "N\u00famero de linhas de contexto"
::msgcat::mcset pt_br "Ignore space change" "Ignorar mudan\u00e7as de caixa"
::msgcat::mcset pt_br "Patch" "Diferen\u00e7as"
::msgcat::mcset pt_br "Tree" "\u00c1rvore"
::msgcat::mcset pt_br "Diff this -> selected" "Comparar esta revis\u00e3o com a selecionada"
::msgcat::mcset pt_br "Diff selected -> this" "Comparar a revis\u00e3o selecionada com esta"
::msgcat::mcset pt_br "Make patch" "Criar patch"
::msgcat::mcset pt_br "Create tag" "Criar etiqueta"
::msgcat::mcset pt_br "Write commit to file" "Salvar revis\u00e3o para um arquivo"
::msgcat::mcset pt_br "Create new branch" "Criar novo ramo"
::msgcat::mcset pt_br "Cherry-pick this commit" "Fazer cherry-pick desta revis\u00e3o"
::msgcat::mcset pt_br "Reset HEAD branch to here" "Redefinir HEAD para c\u00e1"
::msgcat::mcset pt_br "Mark this commit" "Marcar esta revis\u00e3o"
::msgcat::mcset pt_br "Return to mark" "Voltar \u00e0 marca"
::msgcat::mcset pt_br "Find descendant of this and mark" "Encontrar descendente e marcar"
::msgcat::mcset pt_br "Compare with marked commit" "Comparar com a revis\u00e3o marcada"
::msgcat::mcset pt_br "Check out this branch" "Efetuar checkout deste ramo"
::msgcat::mcset pt_br "Remove this branch" "Excluir este ramo"
::msgcat::mcset pt_br "Highlight this too" "Marcar este tamb\u00e9m"
::msgcat::mcset pt_br "Highlight this only" "Marcar apenas este"
::msgcat::mcset pt_br "External diff" "Diff externo"
::msgcat::mcset pt_br "Blame parent commit" "Anotar revis\u00e3o anterior"
::msgcat::mcset pt_br "Show origin of this line" "Exibir origem desta linha"
::msgcat::mcset pt_br "Run git gui blame on this line" "Executar 'git blame' nesta linha"
::msgcat::mcset pt_br "Close" "Fechar"
::msgcat::mcset pt_br "Gitk key bindings" "Atalhos de teclado"
::msgcat::mcset pt_br "Gitk key bindings:" "Atalhos de teclado:"
::msgcat::mcset pt_br "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009Sair"
::msgcat::mcset pt_br "<%s-W>\u0009\u0009Close window" "<%s-W>\u0009\u0009Fechar janela"
::msgcat::mcset pt_br "<Home>\u0009\u0009Move to first commit" "<Home>\u0009\u0009Ir para a primeira revis\u00e3o"
::msgcat::mcset pt_br "<End>\u0009\u0009Move to last commit" "<End>\u0009\u0009Ir para a \u00faltima revis\u00e3o"
::msgcat::mcset pt_br "<Right>, x, l\u0009Go forward in history list" "<Right>, x, l\u0009Avan\u00e7ar no hist\u00f3rico"
::msgcat::mcset pt_br "<PageUp>\u0009Move up one page in commit list" "<PageUp>\u0009Subir uma p\u00e1gina na lista de revis\u00f5es"
::msgcat::mcset pt_br "<PageDown>\u0009Move down one page in commit list" "<PageDown>\u0009Descer uma p\u00e1gina na lista de revis\u00f5es"
::msgcat::mcset pt_br "<%s-Home>\u0009Scroll to top of commit list" "<%s-Home>\u0009Rolar para o in\u00edcio da lista de revis\u00f5es"
::msgcat::mcset pt_br "<%s-End>\u0009Scroll to bottom of commit list" "<%s-End>\u0009Rolar para o final da lista de revis\u00f5es"
::msgcat::mcset pt_br "<%s-Up>\u0009Scroll commit list up one line" "<%s-Up>\u0009Rolar uma linha acima na lista de revis\u00f5es"
::msgcat::mcset pt_br "<%s-Down>\u0009Scroll commit list down one line" "<%s-Down>\u0009Rolar uma linha abaixo na lista de revis\u00f5es"
::msgcat::mcset pt_br "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-PageUp>\u0009Rolar uma p\u00e1gina acima na lista de revis\u00f5es"
::msgcat::mcset pt_br "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-PageDown>\u0009Rolar uma p\u00e1gina abaixo na lista de revis\u00f5es"
::msgcat::mcset pt_br "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Shift-Up>\u0009Procurar pr\u00f3xima (revis\u00f5es mas recentes)"
::msgcat::mcset pt_br "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Shift-Down>\u0009Procurar anterior (revis\u00f5es mais antigas)"
::msgcat::mcset pt_br "<Delete>, b\u0009Scroll diff view up one page" "<Delete>, b\u0009Rola altera\u00e7\u00f5es uma p\u00e1gina acima"
::msgcat::mcset pt_br "<Backspace>\u0009Scroll diff view up one page" "<Backspace>\u0009Rolar altera\u00e7\u00f5es uma p\u00e1gina abaixo"
::msgcat::mcset pt_br "<Space>\u0009\u0009Scroll diff view down one page" "<Space>\u0009\u0009Rolar altera\u00e7\u00f5es uma p\u00e1gina abaixo"
::msgcat::mcset pt_br "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u0009Rolar altera\u00e7\u00f5es 18 linhas acima"
::msgcat::mcset pt_br "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u0009Rolar altera\u00e7\u00f5es 18 linhas abaixo"
::msgcat::mcset pt_br "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009Procurar"
::msgcat::mcset pt_br "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009Ir para a pr\u00f3xima ocorr\u00eancia"
::msgcat::mcset pt_br "<Return>\u0009Move to next find hit" "<Return>\u0009Ir para a pr\u00f3xima ocorr\u00eancia"
::msgcat::mcset pt_br "/\u0009\u0009Focus the search box" "/\u0009\u0009Por foco na caixa de busca"
::msgcat::mcset pt_br "?\u0009\u0009Move to previous find hit" "?\u0009\u0009Ir para a ocorr\u00eancia anterior"
::msgcat::mcset pt_br "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009Rolar altera\u00e7\u00f5es para o pr\u00f3ximo arquivo"
::msgcat::mcset pt_br "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009Procurar a pr\u00f3xima ocorr\u00eancia na lista de altera\u00e7\u00f5es"
::msgcat::mcset pt_br "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009Procurar ocorr\u00eancia anterior na lista de altera\u00e7\u00f5es"
::msgcat::mcset pt_br "<%s-KP+>\u0009Increase font size" "<%s-KP+>\u0009Aumentar tamanho da fonte"
::msgcat::mcset pt_br "<%s-plus>\u0009Increase font size" "<%s-plus>\u0009Aumentar tamanho da fonte"
::msgcat::mcset pt_br "<%s-KP->\u0009Decrease font size" "<%s-KP->\u0009Reduzir tamanho da fonte"
::msgcat::mcset pt_br "<%s-minus>\u0009Decrease font size" "<%s-minus>\u0009Reduzir tamanho da fonte"
::msgcat::mcset pt_br "<F5>\u0009\u0009Update" "<F5>\u0009\u0009Atualizar"
::msgcat::mcset pt_br "Error creating temporary directory %s:" "Erro ao criar o diret\u00f3rio tempor\u00e1rio %s:"
::msgcat::mcset pt_br "Error getting \"%s\" from %s:" "Erro ao ler \"%s\" de %s:"
::msgcat::mcset pt_br "command failed:" "O comando falhou:"
::msgcat::mcset pt_br "No such commit" "Revis\u00e3o n\u00e3o encontrada"
::msgcat::mcset pt_br "git gui blame: command failed:" "Comando 'git gui blame' falhou:"
::msgcat::mcset pt_br "Couldn't read merge head: %s" "Imposs\u00edvel ler merge head: %s"
::msgcat::mcset pt_br "Error reading index: %s" "Erro ao ler o \u00edndice: %s"
::msgcat::mcset pt_br "Couldn't start git blame: %s" "N\u00e3o foi poss\u00edvel inciar o 'git blame': %s"
::msgcat::mcset pt_br "Searching" "Procurando"
::msgcat::mcset pt_br "Error running git blame: %s" "Erro ao executar 'git blame': %s"
::msgcat::mcset pt_br "That line comes from commit %s,  which is not in this view" "Esta linha vem da revis\u00e3o %s, que n\u00e3o est\u00e1 nesta vista"
::msgcat::mcset pt_br "External diff viewer failed:" "Erro do visualizador de altera\u00e7\u00f5es externo:"
::msgcat::mcset pt_br "Gitk view definition" "Definir vista"
::msgcat::mcset pt_br "Remember this view" "Lembrar esta vista"
::msgcat::mcset pt_br "References (space separated list):" "Refer\u00eancias (separar a lista com um espa\u00e7o):"
::msgcat::mcset pt_br "Branches & tags:" "Ramos & etiquetas:"
::msgcat::mcset pt_br "All refs" "Todas as refer\u00eancias"
::msgcat::mcset pt_br "All (local) branches" "Todos os ramos locais"
::msgcat::mcset pt_br "All tags" "Todas as etiquetas"
::msgcat::mcset pt_br "All remote-tracking branches" "Todos os ramos de rastreio"
::msgcat::mcset pt_br "Commit Info (regular expressions):" "Informa\u00e7\u00f5es da revis\u00e3o (express\u00f5es regulares):"
::msgcat::mcset pt_br "Author:" "Autor:"
::msgcat::mcset pt_br "Committer:" "Revisor:"
::msgcat::mcset pt_br "Commit Message:" "Descri\u00e7\u00e3o da revis\u00e3o:"
::msgcat::mcset pt_br "Matches all Commit Info criteria" "Coincidir todos os crit\u00e9rios de informa\u00e7\u00f5es da revis\u00e3o"
::msgcat::mcset pt_br "Changes to Files:" "Mudan\u00e7as para os arquivos:"
::msgcat::mcset pt_br "Fixed String" "Texto fixo"
::msgcat::mcset pt_br "Regular Expression" "Express\u00e3o regular"
::msgcat::mcset pt_br "Search string:" "Texto de busca"
::msgcat::mcset pt_br "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "Datas de revis\u00e3o (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):"
::msgcat::mcset pt_br "Since:" "Desde:"
::msgcat::mcset pt_br "Until:" "At\u00e9:"
::msgcat::mcset pt_br "Limit and/or skip a number of revisions (positive integer):" "Limitar e/ou ignorar um n\u00famero de revis\u00f5es (inteiro positivo):"
::msgcat::mcset pt_br "Number to show:" "N\u00famero para mostrar:"
::msgcat::mcset pt_br "Number to skip:" "N\u00famero para ignorar:"
::msgcat::mcset pt_br "Miscellaneous options:" "Op\u00e7\u00f5es diversas:"
::msgcat::mcset pt_br "Strictly sort by date" "Ordenar estritamente pela data"
::msgcat::mcset pt_br "Mark branch sides" "Marcar os dois lados do ramo"
::msgcat::mcset pt_br "Limit to first parent" "Limitar ao primeiro antecessor"
::msgcat::mcset pt_br "Simple history" "Hist\u00f3rico simplificado"
::msgcat::mcset pt_br "Additional arguments to git log:" "Argumentos adicionais para o 'git log':"
::msgcat::mcset pt_br "Enter files and directories to include, one per line:" "Arquivos e diret\u00f3rios para incluir, um por linha"
::msgcat::mcset pt_br "Command to generate more commits to include:" "Comando para gerar mais revis\u00f5es para incluir:"
::msgcat::mcset pt_br "Gitk: edit view" "Gitk: editar vista"
::msgcat::mcset pt_br "-- criteria for selecting revisions" "-- crit\u00e9rios para selecionar revis\u00f5es"
::msgcat::mcset pt_br "View Name" "Nome da vista"
::msgcat::mcset pt_br "Apply (F5)" "Aplicar (F5)"
::msgcat::mcset pt_br "Error in commit selection arguments:" "Erro nos argumentos de sele\u00e7\u00e3o de revis\u00f5es:"
::msgcat::mcset pt_br "None" "Nenhum"
::msgcat::mcset pt_br "Descendant" "Descendente de"
::msgcat::mcset pt_br "Not descendant" "N\u00e3o descendente de"
::msgcat::mcset pt_br "Ancestor" "Antecessor de"
::msgcat::mcset pt_br "Not ancestor" "N\u00e3o antecessor de"
::msgcat::mcset pt_br "Local changes checked in to index but not committed" "Mudan\u00e7as locais marcadas, por\u00e9m n\u00e3o salvas"
::msgcat::mcset pt_br "Local uncommitted changes, not checked in to index" "Mudan\u00e7as locais n\u00e3o marcadas"
::msgcat::mcset pt_br "many" "muitas"
::msgcat::mcset pt_br "Tags:" "Etiquetas:"
::msgcat::mcset pt_br "Parent" "Antecessor"
::msgcat::mcset pt_br "Child" "Descendente"
::msgcat::mcset pt_br "Branch" "Ramo"
::msgcat::mcset pt_br "Follows" "Segue"
::msgcat::mcset pt_br "Precedes" "Precede"
::msgcat::mcset pt_br "Error getting diffs: %s" "Erro ao obter diferen\u00e7as: %s"
::msgcat::mcset pt_br "Goto:" "Ir para:"
::msgcat::mcset pt_br "Short SHA1 id %s is ambiguous" "O id SHA1 %s \u00e9 amb\u00edguo"
::msgcat::mcset pt_br "Revision %s is not known" "Revis\u00e3o %s desconhecida"
::msgcat::mcset pt_br "SHA1 id %s is not known" "Id SHA1 %s desconhecido"
::msgcat::mcset pt_br "Revision %s is not in the current view" "A revis\u00e3o %s n\u00e3o est\u00e1 na vista atual"
::msgcat::mcset pt_br "Date" "Data"
::msgcat::mcset pt_br "Children" "Descendentes"
::msgcat::mcset pt_br "Reset %s branch to here" "Redefinir ramo %s para este ponto"
::msgcat::mcset pt_br "Detached head: can't reset" "Detached head: imposs\u00edvel redefinir"
::msgcat::mcset pt_br "Skipping merge commit " "Saltando revis\u00e3o de mesclagem"
::msgcat::mcset pt_br "Error getting patch ID for " "Erro ao obter patch ID para"
::msgcat::mcset pt_br " - stopping\n" "- parando\n"
::msgcat::mcset pt_br "Commit " "Revis\u00e3o"
::msgcat::mcset pt_br " is the same patch as\n       " "\u00e9 o mesmo patch que\n       "
::msgcat::mcset pt_br " differs from\n       " "difere de"
::msgcat::mcset pt_br "Diff of commits:\n\n" "Diferen\u00e7a de revis\u00f5es:\n\n"
::msgcat::mcset pt_br " has %s children - stopping\n" "possui %s descendentes - parando\n"
::msgcat::mcset pt_br "Error writing commit to file: %s" "Erro ao salvar revis\u00e3o para o arquivo: %s"
::msgcat::mcset pt_br "Error diffing commits: %s" "Erro ao comparar revis\u00f5es: %s"
::msgcat::mcset pt_br "Top" "In\u00edcio"
::msgcat::mcset pt_br "From" "De"
::msgcat::mcset pt_br "To" "Para"
::msgcat::mcset pt_br "Generate patch" "Gerar patch"
::msgcat::mcset pt_br "From:" "De:"
::msgcat::mcset pt_br "To:" "Para:"
::msgcat::mcset pt_br "Reverse" "Inverter"
::msgcat::mcset pt_br "Output file:" "Arquivo de sa\u00edda:"
::msgcat::mcset pt_br "Generate" "Gerar"
::msgcat::mcset pt_br "Error creating patch:" "Erro ao criar patch:"
::msgcat::mcset pt_br "ID:" "ID:"
::msgcat::mcset pt_br "Tag name:" "Nome da etiqueta:"
::msgcat::mcset pt_br "Tag message is optional" "A descri\u00e7\u00e3o da etiqueta \u00e9 opcional"
::msgcat::mcset pt_br "Tag message:" "Descri\u00e7\u00e3o da etiqueta"
::msgcat::mcset pt_br "Create" "Criar"
::msgcat::mcset pt_br "No tag name specified" "Nome da etiqueta n\u00e3o indicado"
::msgcat::mcset pt_br "Tag \"%s\" already exists" "Etiqueta \"%s\" j\u00e1 existe"
::msgcat::mcset pt_br "Error creating tag:" "Erro ao criar etiqueta:"
::msgcat::mcset pt_br "Command:" "Comando:"
::msgcat::mcset pt_br "Write" "Exportar"
::msgcat::mcset pt_br "Error writing commit:" "Erro ao exportar revis\u00e3o"
::msgcat::mcset pt_br "Name:" "Nome:"
::msgcat::mcset pt_br "Please specify a name for the new branch" "Indique um nome para o novo ramo"
::msgcat::mcset pt_br "Branch '%s' already exists. Overwrite?" "O ramo \"%s\" j\u00e1 existe. Sobrescrever?"
::msgcat::mcset pt_br "Commit %s is already included in branch %s -- really re-apply it?" "Revis\u00e3o %s j\u00e1 inclusa no ramo %s -- voc\u00ea realmente deseja reaplic\u00e1-la?"
::msgcat::mcset pt_br "Cherry-picking" "Cherry-picking"
::msgcat::mcset pt_br "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "O cherry-pick falhou porque o arquivo \"%s\" possui mudan\u00e7as locais.\nSalve a uma revis\u00e3o, redefina ou armazene (stash) suas mudan\u00e7as e tente novamente."
::msgcat::mcset pt_br "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "O cherry-pick falhou porque houve um conflito na mesclagem.\nExecutar o 'git citool' para resolv\u00ea-lo?"
::msgcat::mcset pt_br "No changes committed" "Nenhuma revis\u00e3o foi salva"
::msgcat::mcset pt_br "Confirm reset" "Confirmar redefini\u00e7\u00e3o"
::msgcat::mcset pt_br "Reset branch %s to %s?" "Voc\u00ea realmente deseja redefinir o ramo %s para %s?"
::msgcat::mcset pt_br "Reset type:" "Tipo de redefini\u00e7\u00e3o"
::msgcat::mcset pt_br "Soft: Leave working tree and index untouched" "Soft: deixa a \u00e1rvore de trabalho e o \u00edndice intocados"
::msgcat::mcset pt_br "Mixed: Leave working tree untouched, reset index" "Misto: Deixa a \u00e1rvore de trabalho intocada, redefine o \u00edndice"
::msgcat::mcset pt_br "Hard: Reset working tree and index\n(discard ALL local changes)" "Hard: Redefine a \u00e1rvore de trabalho e o \u00edndice\n(descarta TODAS as mudan\u00e7as locais)"
::msgcat::mcset pt_br "Resetting" "Redefinindo"
::msgcat::mcset pt_br "Checking out" "Abrindo"
::msgcat::mcset pt_br "Cannot delete the currently checked-out branch" "Imposs\u00edvel excluir o ramo atualmente aberto"
::msgcat::mcset pt_br "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "As revis\u00f5es do ramo \"%s\" n\u00e3o existem em nenhum outro ramo.\nVoc\u00ea realmente deseja excluir ramo \"%s\"?"
::msgcat::mcset pt_br "Tags and heads: %s" "Refer\u00eancias: %s"
::msgcat::mcset pt_br "Filter" "Filtro"
::msgcat::mcset pt_br "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "Erro ao ler a topologia das revis\u00f5es; as informa\u00e7\u00f5es dos ramos e etiquetas antecessoras/sucessoras estar\u00e3o incompletas"
::msgcat::mcset pt_br "Tag" "Etiqueta"
::msgcat::mcset pt_br "Id" "Id"
::msgcat::mcset pt_br "Gitk font chooser" "Selecionar fontes do Gitk"
::msgcat::mcset pt_br "B" "B"
::msgcat::mcset pt_br "I" "I"
::msgcat::mcset pt_br "Commit list display options" "Op\u00e7\u00f5es da lista de revis\u00f5es"
::msgcat::mcset pt_br "Maximum graph width (lines)" "Largura m\u00e1xima do grafo (linhas)"
::msgcat::mcset pt_br "Maximum graph width (% of pane)" "Largura m\u00e1xima do grafo (% do painel)"
::msgcat::mcset pt_br "Show local changes" "Exibir mudan\u00e7as locais"
::msgcat::mcset pt_br "Hide remote refs" "Ocultar refer\u00eancias remotas"
::msgcat::mcset pt_br "Diff display options" "Op\u00e7\u00f5es de exibi\u00e7\u00e3o das altera\u00e7\u00f5es"
::msgcat::mcset pt_br "Tab spacing" "Espa\u00e7os por tabula\u00e7\u00e3o"
::msgcat::mcset pt_br "Limit diffs to listed paths" "Limitar diferen\u00e7as aos caminhos listados"
::msgcat::mcset pt_br "Support per-file encodings" "Usar codifica\u00e7\u00f5es distintas por arquivo"
::msgcat::mcset pt_br "External diff tool" "Ferramenta 'diff' externa"
::msgcat::mcset pt_br "Choose..." "Selecionar..."
::msgcat::mcset pt_br "General options" "Op\u00e7\u00f5es gerais"
::msgcat::mcset pt_br "Use themed widgets" "Usar temas para as janelas"
::msgcat::mcset pt_br "(change requires restart)" "(exige reinicializa\u00e7\u00e3o)"
::msgcat::mcset pt_br "(currently unavailable)" "(atualmente indispon\u00edvel)"
::msgcat::mcset pt_br "Colors: press to choose" "Cores: clique para escolher"
::msgcat::mcset pt_br "Interface" "Interface"
::msgcat::mcset pt_br "interface" "interface"
::msgcat::mcset pt_br "Background" "Segundo plano"
::msgcat::mcset pt_br "background" "segundo plano"
::msgcat::mcset pt_br "Foreground" "Primeiro plano"
::msgcat::mcset pt_br "foreground" "primeiro plano"
::msgcat::mcset pt_br "Diff: old lines" "Diff: linhas exclu\u00eddas"
::msgcat::mcset pt_br "diff old lines" "linhas exclu\u00eddas"
::msgcat::mcset pt_br "Diff: new lines" "Diff: linhas adicionadas"
::msgcat::mcset pt_br "diff new lines" "linhas adicionadas"
::msgcat::mcset pt_br "Diff: hunk header" "Diff: cabe\u00e7alho do bloco"
::msgcat::mcset pt_br "diff hunk header" "cabe\u00e7alho do bloco"
::msgcat::mcset pt_br "Marked line bg" "2\u00ba plano da linha marcada"
::msgcat::mcset pt_br "marked line background" "segundo plano da linha marcada"
::msgcat::mcset pt_br "Select bg" "2\u00ba plano da sele\u00e7\u00e3o"
::msgcat::mcset pt_br "Fonts: press to choose" "Fontes: clique para escolher"
::msgcat::mcset pt_br "Main font" "Fonte principal"
::msgcat::mcset pt_br "Diff display font" "Fonte da lista de mudan\u00e7as"
::msgcat::mcset pt_br "User interface font" "Fonte da interface"
::msgcat::mcset pt_br "Gitk preferences" "Prefer\u00eancias do Gitk"
::msgcat::mcset pt_br "Gitk: choose color for %s" "Gitk: selecionar cor para %s"
::msgcat::mcset pt_br "Cannot find a git repository here." "N\u00e3o h\u00e1 nenhum reposit\u00f3rio git aqui."
::msgcat::mcset pt_br "Ambiguous argument '%s': both revision and filename" "O argumento \"%s\" \u00e9 amb\u00edguo (especifica tanto uma revis\u00e3o e um nome de arquivo)"
::msgcat::mcset pt_br "Bad arguments to gitk:" "Argumentos incorretos para o gitk:"
