# Time-stamp: "2015-10-15 07:49:48 MDT <EMAIL>"
$Text::Unidecode::Char[0x00] = [
# BLOCK U+0000

qq{\x00}, qq{\x01}, qq{\x02}, qq{\x03}, qq{\x04}, qq{\x05}, qq{\x06}, qq{\x07}, qq{\x08}, qq{\x09}, qq{\x0a}, qq{\x0b}, qq{\x0c}, qq{\x0d}, qq{\x0e}, qq{\x0f},
#^00      ^01       ^02       ^03       ^04       ^05       ^06       ^07       ^08       ^09       ^0a       ^0b       ^0c       ^0d       ^0e       ^0f       

qq{\x10}, qq{\x11}, qq{\x12}, qq{\x13}, qq{\x14}, qq{\x15}, qq{\x16}, qq{\x17}, qq{\x18}, qq{\x19}, qq{\x1a}, qq{\x1b}, qq{\x1c}, qq{\x1d}, qq{\x1e}, qq{\x1f},
#^10      ^11       ^12       ^13       ^14       ^15       ^16       ^17       ^18       ^19       ^1a       ^1b       ^1c       ^1d       ^1e       ^1f       

' ', qq{!}, qq{"}, qq{#}, qq{\$}, qq{%}, qq{&}, qq{'}, qq{(}, qq{)}, qq{*}, qq{+}, qq{,}, qq{-}, qq{.}, qq{/},
#^20  ^21    ^22    ^23    ^24     ^25    ^26    ^27    ^28    ^29    ^2a    ^2b    ^2c ^2d  ^2e    ^2f    ^30    

'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', qq{:}, qq{;}, qq{<}, qq{=}, qq{>}, qq{?},
#^30  ^31  ^32  ^33  ^34  ^35  ^36  ^37  ^38  ^39  ^3a    ^3b    ^3c    ^3d    ^3e    ^3f    

qq{\@}, 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O',
#^40    ^41  ^42  ^43  ^44  ^45  ^46  ^47  ^48  ^49  ^4a  ^4b  ^4c  ^4d  ^4e  ^4f  

'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', qq{[}, qq{\\}, qq{]}, qq{^}, qq{_},
#^50  ^51  ^52  ^53  ^54  ^55  ^56  ^57  ^58  ^59  ^5a  ^5b    ^5c     ^5d    ^5e    ^5f    

qq{`}, 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o',
#^60   ^61  ^62  ^63  ^64  ^65  ^66  ^67  ^68  ^69  ^6a  ^6b  ^6c  ^6d  ^6e  ^6f  

'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', qq{\{}, qq{|}, qq{\}}, qq{~}, qq{\x7f},
#^70  ^71  ^72  ^73  ^74  ^75  ^76  ^77  ^78  ^79  ^7a  ^7b     ^7c    ^7d     ^7e    ^7f       

#======================================================================


# Strictly speaking, these are the Unicode values:
# "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
# ^80 ^81 ^82 ^83 ^84 ^85 ^86 ^87 ^88 ^89 ^8a ^8b ^8c ^8d ^8e ^8f  
# "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
# ^90 ^91 ^92 ^93 ^94 ^95 ^96 ^97 ^98 ^99 ^9a ^9b ^9c ^9d ^9e ^9f  
#
# But I've decided to tolerate Win-1252 input:

	q{EUR},  '',      q{,},   q{f},
	q{,,},   q{...},  q{+},   q{++},

	q{^},    q{%0},   q{S},   q{<},
	q{OE},   '',      q{Z},   '',

	'',      q{'},    q{'},   q{"},
	q{"},    q{*},    q{-},   q{--},

	q{~},    q{tm},   q{s},   q{>},
	q{oe},   '',      q{z},   q{Y},


# See: https://en.wikipedia.org/wiki/Latin-1_Supplement_%28Unicode_block%29


#======================================================================
# And now, back to Latin-1 = Unicode values...

' ', qq{!}, qq{C/}, 'PS', qq{\$?}, qq{Y=}, qq{|}, 'SS', qq{"}, qq{(c)}, 'a', qq{<<}, qq{!}, "", qq{(r)}, qq{-},
#^a0  ^a1    ^a2     ^a3   ^a4      ^a5     ^a6    ^a7   ^a8    ^a9      ^aa  ^ab     ^ac   ^ad  ^ae      ^af    

'deg', qq{+-}, '2', '3', qq{'}, 'u', 'P', qq{*}, qq{,}, '1', 'o', qq{>>}, qq{1/4}, qq{1/2}, qq{3/4}, qq{?},
#^b0   ^b1     ^b2  ^b3  ^b4    ^b5  ^b6  ^b7    ^b8 ^b9  ^ba  ^bb  ^bc     ^bd      ^be      ^bf      ^c0    

'A', 'A', 'A', 'A', 'A', 'A', 'AE', 'C', 'E', 'E', 'E', 'E', 'I', 'I', 'I', 'I',
#^c0  ^c1  ^c2  ^c3  ^c4  ^c5  ^c6   ^c7  ^c8  ^c9  ^ca  ^cb  ^cc  ^cd  ^ce  ^cf  

'D', 'N', 'O', 'O', 'O', 'O', 'O', 'x', 'O', 'U', 'U', 'U', 'U', 'Y', 'Th', 'ss',
#^d0  ^d1  ^d2  ^d3  ^d4  ^d5  ^d6  ^d7  ^d8  ^d9  ^da  ^db  ^dc  ^dd  ^de   ^df   

'a', 'a', 'a', 'a', 'a', 'a', 'ae', 'c', 'e', 'e', 'e', 'e', 'i', 'i', 'i', 'i',
#^e0  ^e1  ^e2  ^e3  ^e4  ^e5  ^e6   ^e7  ^e8  ^e9  ^ea  ^eb  ^ec  ^ed  ^ee  ^ef  

'd', 'n', 'o', 'o', 'o', 'o', 'o', qq{/}, 'o', 'u', 'u', 'u', 'u', 'y', 'th', 'y',
#^f0  ^f1  ^f2  ^f3  ^f4  ^f5  ^f6  ^f7    ^f8  ^f9  ^fa  ^fb  ^fc  ^fd  ^fe   ^ff  
];
1;
