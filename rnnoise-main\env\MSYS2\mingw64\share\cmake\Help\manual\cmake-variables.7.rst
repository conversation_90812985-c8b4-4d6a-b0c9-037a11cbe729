.. cmake-manual-description: CMake Variables Reference

cmake-variables(7)
******************

.. only:: html

   .. contents::

This page documents variables that are provided by CMake
or have meaning to C<PERSON><PERSON> when set by project code.

For general information on variables, see the
:ref:`Variables <CMake Language Variables>`
section in the cmake-language manual.

.. include:: ID_RESERVE.txt

Variables that Provide Information
==================================

.. toctree::
   :maxdepth: 1

   /variable/CMAKE_AR
   /variable/CMAKE_ARGC
   /variable/CMAKE_ARGV0
   /variable/CMAKE_BINARY_DIR
   /variable/CMAKE_BUILD_TOOL
   /variable/CMAKE_CACHE_MAJOR_VERSION
   /variable/CMAKE_CACHE_MINOR_VERSION
   /variable/CMAKE_CACHE_PATCH_VERSION
   /variable/CMAKE_CACHEFILE_DIR
   /variable/CMAKE_CFG_INTDIR
   /variable/CMAKE_COMMAND
   /variable/CMAKE_CPACK_COMMAND
   /variable/CMAKE_CROSSCOMPILING
   /variable/CMAKE_CROSSCOMPILING_EMULATOR
   /variable/CMAKE_CTEST_COMMAND
   /variable/CMAKE_CURRENT_BINARY_DIR
   /variable/CMAKE_CURRENT_FUNCTION
   /variable/CMAKE_CURRENT_FUNCTION_LIST_DIR
   /variable/CMAKE_CURRENT_FUNCTION_LIST_FILE
   /variable/CMAKE_CURRENT_FUNCTION_LIST_LINE
   /variable/CMAKE_CURRENT_LIST_DIR
   /variable/CMAKE_CURRENT_LIST_FILE
   /variable/CMAKE_CURRENT_LIST_LINE
   /variable/CMAKE_CURRENT_SOURCE_DIR
   /variable/CMAKE_DEBUG_TARGET_PROPERTIES
   /variable/CMAKE_DIRECTORY_LABELS
   /variable/CMAKE_DL_LIBS
   /variable/CMAKE_DOTNET_SDK
   /variable/CMAKE_DOTNET_TARGET_FRAMEWORK
   /variable/CMAKE_DOTNET_TARGET_FRAMEWORK_VERSION
   /variable/CMAKE_EDIT_COMMAND
   /variable/CMAKE_EXECUTABLE_SUFFIX
   /variable/CMAKE_EXECUTABLE_SUFFIX_LANG
   /variable/CMAKE_EXTRA_SHARED_LIBRARY_SUFFIXES
   /variable/CMAKE_FIND_DEBUG_MODE
   /variable/CMAKE_FIND_PACKAGE_NAME
   /variable/CMAKE_FIND_PACKAGE_REDIRECTS_DIR
   /variable/CMAKE_FIND_PACKAGE_SORT_DIRECTION
   /variable/CMAKE_FIND_PACKAGE_SORT_ORDER
   /variable/CMAKE_GENERATOR
   /variable/CMAKE_GENERATOR_INSTANCE
   /variable/CMAKE_GENERATOR_PLATFORM
   /variable/CMAKE_GENERATOR_TOOLSET
   /variable/CMAKE_IMPORT_LIBRARY_PREFIX
   /variable/CMAKE_IMPORT_LIBRARY_SUFFIX
   /variable/CMAKE_JOB_POOL_COMPILE
   /variable/CMAKE_JOB_POOL_LINK
   /variable/CMAKE_JOB_POOL_PRECOMPILE_HEADER
   /variable/CMAKE_JOB_POOLS
   /variable/CMAKE_LANG_COMPILER_AR
   /variable/CMAKE_LANG_COMPILER_FRONTEND_VARIANT
   /variable/CMAKE_LANG_COMPILER_LINKER
   /variable/CMAKE_LANG_COMPILER_LINKER_FRONTEND_VARIANT
   /variable/CMAKE_LANG_COMPILER_LINKER_ID
   /variable/CMAKE_LANG_COMPILER_LINKER_VERSION
   /variable/CMAKE_LANG_COMPILER_RANLIB
   /variable/CMAKE_LANG_DEVICE_LINK_MODE
   /variable/CMAKE_LANG_LINK_LIBRARY_SUFFIX
   /variable/CMAKE_LANG_LINK_MODE
   /variable/CMAKE_LINK_LIBRARY_SUFFIX
   /variable/CMAKE_LINK_SEARCH_END_STATIC
   /variable/CMAKE_LINK_SEARCH_START_STATIC
   /variable/CMAKE_LIST_FILE_NAME
   /variable/CMAKE_MAJOR_VERSION
   /variable/CMAKE_MAKE_PROGRAM
   /variable/CMAKE_MATCH_COUNT
   /variable/CMAKE_MATCH_n
   /variable/CMAKE_MINIMUM_REQUIRED_VERSION
   /variable/CMAKE_MINOR_VERSION
   /variable/CMAKE_NETRC
   /variable/CMAKE_NETRC_FILE
   /variable/CMAKE_OBJDUMP
   /variable/CMAKE_PARENT_LIST_FILE
   /variable/CMAKE_PATCH_VERSION
   /variable/CMAKE_PROJECT_DESCRIPTION
   /variable/CMAKE_PROJECT_HOMEPAGE_URL
   /variable/CMAKE_PROJECT_NAME
   /variable/CMAKE_PROJECT_VERSION
   /variable/CMAKE_PROJECT_VERSION_MAJOR
   /variable/CMAKE_PROJECT_VERSION_MINOR
   /variable/CMAKE_PROJECT_VERSION_PATCH
   /variable/CMAKE_PROJECT_VERSION_TWEAK
   /variable/CMAKE_RANLIB
   /variable/CMAKE_ROOT
   /variable/CMAKE_RULE_MESSAGES
   /variable/CMAKE_SCRIPT_MODE_FILE
   /variable/CMAKE_SHARED_LIBRARY_PREFIX
   /variable/CMAKE_SHARED_LIBRARY_SUFFIX
   /variable/CMAKE_SHARED_LIBRARY_ARCHIVE_SUFFIX
   /variable/CMAKE_SHARED_MODULE_PREFIX
   /variable/CMAKE_SHARED_MODULE_SUFFIX
   /variable/CMAKE_SIZEOF_VOID_P
   /variable/CMAKE_SKIP_INSTALL_RULES
   /variable/CMAKE_SKIP_RPATH
   /variable/CMAKE_SOURCE_DIR
   /variable/CMAKE_STATIC_LIBRARY_PREFIX
   /variable/CMAKE_STATIC_LIBRARY_SUFFIX
   /variable/CMAKE_Swift_COMPILATION_MODE
   /variable/CMAKE_Swift_MODULE_DIRECTORY
   /variable/CMAKE_Swift_NUM_THREADS
   /variable/CMAKE_TEST_LAUNCHER
   /variable/CMAKE_TOOLCHAIN_FILE
   /variable/CMAKE_TWEAK_VERSION
   /variable/CMAKE_VERBOSE_MAKEFILE
   /variable/CMAKE_VERSION
   /variable/CMAKE_VS_DEVENV_COMMAND
   /variable/CMAKE_VS_MSBUILD_COMMAND
   /variable/CMAKE_VS_NsightTegra_VERSION
   /variable/CMAKE_VS_NUGET_PACKAGE_RESTORE
   /variable/CMAKE_VS_PLATFORM_NAME
   /variable/CMAKE_VS_PLATFORM_NAME_DEFAULT
   /variable/CMAKE_VS_PLATFORM_TOOLSET
   /variable/CMAKE_VS_PLATFORM_TOOLSET_CUDA
   /variable/CMAKE_VS_PLATFORM_TOOLSET_CUDA_CUSTOM_DIR
   /variable/CMAKE_VS_PLATFORM_TOOLSET_FORTRAN
   /variable/CMAKE_VS_PLATFORM_TOOLSET_HOST_ARCHITECTURE
   /variable/CMAKE_VS_PLATFORM_TOOLSET_VERSION
   /variable/CMAKE_VS_TARGET_FRAMEWORK_IDENTIFIER
   /variable/CMAKE_VS_TARGET_FRAMEWORK_TARGETS_VERSION
   /variable/CMAKE_VS_TARGET_FRAMEWORK_VERSION
   /variable/CMAKE_VS_USE_DEBUG_LIBRARIES
   /variable/CMAKE_VS_VERSION_BUILD_NUMBER
   /variable/CMAKE_VS_WINDOWS_TARGET_PLATFORM_MIN_VERSION
   /variable/CMAKE_VS_WINDOWS_TARGET_PLATFORM_VERSION
   /variable/CMAKE_VS_WINDOWS_TARGET_PLATFORM_VERSION_MAXIMUM
   /variable/CMAKE_WINDOWS_KMDF_VERSION
   /variable/CMAKE_XCODE_BUILD_SYSTEM
   /variable/CMAKE_XCODE_PLATFORM_TOOLSET
   /variable/PROJECT-NAME_BINARY_DIR
   /variable/PROJECT-NAME_DESCRIPTION
   /variable/PROJECT-NAME_HOMEPAGE_URL
   /variable/PROJECT-NAME_IS_TOP_LEVEL
   /variable/PROJECT-NAME_SOURCE_DIR
   /variable/PROJECT-NAME_VERSION
   /variable/PROJECT-NAME_VERSION_MAJOR
   /variable/PROJECT-NAME_VERSION_MINOR
   /variable/PROJECT-NAME_VERSION_PATCH
   /variable/PROJECT-NAME_VERSION_TWEAK
   /variable/PROJECT_BINARY_DIR
   /variable/PROJECT_DESCRIPTION
   /variable/PROJECT_HOMEPAGE_URL
   /variable/PROJECT_IS_TOP_LEVEL
   /variable/PROJECT_NAME
   /variable/PROJECT_SOURCE_DIR
   /variable/PROJECT_VERSION
   /variable/PROJECT_VERSION_MAJOR
   /variable/PROJECT_VERSION_MINOR
   /variable/PROJECT_VERSION_PATCH
   /variable/PROJECT_VERSION_TWEAK

Variables that Change Behavior
==============================

.. toctree::
   :maxdepth: 1

   /variable/BUILD_SHARED_LIBS
   /variable/BUILD_TESTING
   /variable/CMAKE_ABSOLUTE_DESTINATION_FILES
   /variable/CMAKE_ADD_CUSTOM_COMMAND_DEPENDS_EXPLICIT_ONLY
   /variable/CMAKE_APPBUNDLE_PATH
   /variable/CMAKE_BUILD_TYPE
   /variable/CMAKE_CLANG_VFS_OVERLAY
   /variable/CMAKE_CODEBLOCKS_COMPILER_ID
   /variable/CMAKE_CODEBLOCKS_EXCLUDE_EXTERNAL_FILES
   /variable/CMAKE_CODELITE_USE_TARGETS
   /variable/CMAKE_COLOR_DIAGNOSTICS
   /variable/CMAKE_COLOR_MAKEFILE
   /variable/CMAKE_CONFIGURATION_TYPES
   /variable/CMAKE_DEPENDS_IN_PROJECT_ONLY
   /variable/CMAKE_DISABLE_FIND_PACKAGE_PackageName
   /variable/CMAKE_ECLIPSE_GENERATE_LINKED_RESOURCES
   /variable/CMAKE_ECLIPSE_GENERATE_SOURCE_PROJECT
   /variable/CMAKE_ECLIPSE_MAKE_ARGUMENTS
   /variable/CMAKE_ECLIPSE_RESOURCE_ENCODING
   /variable/CMAKE_ECLIPSE_VERSION
   /variable/CMAKE_ERROR_DEPRECATED
   /variable/CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION
   /variable/CMAKE_EXECUTE_PROCESS_COMMAND_ECHO
   /variable/CMAKE_EXECUTE_PROCESS_COMMAND_ERROR_IS_FATAL
   /variable/CMAKE_EXPORT_BUILD_DATABASE
   /variable/CMAKE_EXPORT_COMPILE_COMMANDS
   /variable/CMAKE_EXPORT_SARIF
   /variable/CMAKE_EXPORT_PACKAGE_REGISTRY
   /variable/CMAKE_EXPORT_NO_PACKAGE_REGISTRY
   /variable/CMAKE_FIND_APPBUNDLE
   /variable/CMAKE_FIND_FRAMEWORK
   /variable/CMAKE_FIND_LIBRARY_CUSTOM_LIB_SUFFIX
   /variable/CMAKE_FIND_LIBRARY_PREFIXES
   /variable/CMAKE_FIND_LIBRARY_SUFFIXES
   /variable/CMAKE_FIND_NO_INSTALL_PREFIX
   /variable/CMAKE_FIND_PACKAGE_PREFER_CONFIG
   /variable/CMAKE_FIND_PACKAGE_RESOLVE_SYMLINKS
   /variable/CMAKE_FIND_PACKAGE_TARGETS_GLOBAL
   /variable/CMAKE_FIND_PACKAGE_WARN_NO_MODULE
   /variable/CMAKE_FIND_ROOT_PATH
   /variable/CMAKE_FIND_ROOT_PATH_MODE_INCLUDE
   /variable/CMAKE_FIND_ROOT_PATH_MODE_LIBRARY
   /variable/CMAKE_FIND_ROOT_PATH_MODE_PACKAGE
   /variable/CMAKE_FIND_ROOT_PATH_MODE_PROGRAM
   /variable/CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH
   /variable/CMAKE_FIND_USE_CMAKE_PATH
   /variable/CMAKE_FIND_USE_CMAKE_SYSTEM_PATH
   /variable/CMAKE_FIND_USE_INSTALL_PREFIX
   /variable/CMAKE_FIND_USE_PACKAGE_REGISTRY
   /variable/CMAKE_FIND_USE_PACKAGE_ROOT_PATH
   /variable/CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH
   /variable/CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY
   /variable/CMAKE_FRAMEWORK_PATH
   /variable/CMAKE_IGNORE_PATH
   /variable/CMAKE_IGNORE_PREFIX_PATH
   /variable/CMAKE_INCLUDE_DIRECTORIES_BEFORE
   /variable/CMAKE_INCLUDE_DIRECTORIES_PROJECT_BEFORE
   /variable/CMAKE_INCLUDE_PATH
   /variable/CMAKE_INSTALL_DEFAULT_COMPONENT_NAME
   /variable/CMAKE_INSTALL_DEFAULT_DIRECTORY_PERMISSIONS
   /variable/CMAKE_INSTALL_MESSAGE
   /variable/CMAKE_INSTALL_PREFIX
   /variable/CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT
   /variable/CMAKE_KATE_FILES_MODE
   /variable/CMAKE_KATE_MAKE_ARGUMENTS
   /variable/CMAKE_LIBRARY_PATH
   /variable/CMAKE_LINK_DIRECTORIES_BEFORE
   /variable/CMAKE_LINK_LIBRARIES_ONLY_TARGETS
   /variable/CMAKE_MAXIMUM_RECURSION_DEPTH
   /variable/CMAKE_MESSAGE_CONTEXT
   /variable/CMAKE_MESSAGE_CONTEXT_SHOW
   /variable/CMAKE_MESSAGE_INDENT
   /variable/CMAKE_MESSAGE_LOG_LEVEL
   /variable/CMAKE_MFC_FLAG
   /variable/CMAKE_MODULE_PATH
   /variable/CMAKE_PKG_CONFIG_DISABLE_UNINSTALLED
   /variable/CMAKE_PKG_CONFIG_PC_LIB_DIRS
   /variable/CMAKE_PKG_CONFIG_PC_PATH
   /variable/CMAKE_PKG_CONFIG_SYSROOT_DIR
   /variable/CMAKE_PKG_CONFIG_TOP_BUILD_DIR
   /variable/CMAKE_POLICY_DEFAULT_CMPNNNN
   /variable/CMAKE_POLICY_VERSION_MINIMUM
   /variable/CMAKE_POLICY_WARNING_CMPNNNN
   /variable/CMAKE_PREFIX_PATH
   /variable/CMAKE_PROGRAM_PATH
   /variable/CMAKE_PROJECT_INCLUDE
   /variable/CMAKE_PROJECT_INCLUDE_BEFORE
   /variable/CMAKE_PROJECT_PROJECT-NAME_INCLUDE
   /variable/CMAKE_PROJECT_PROJECT-NAME_INCLUDE_BEFORE
   /variable/CMAKE_PROJECT_TOP_LEVEL_INCLUDES
   /variable/CMAKE_REQUIRE_FIND_PACKAGE_PackageName
   /variable/CMAKE_SKIP_INSTALL_ALL_DEPENDENCY
   /variable/CMAKE_SKIP_TEST_ALL_DEPENDENCY
   /variable/CMAKE_STAGING_PREFIX
   /variable/CMAKE_SUBLIME_TEXT_2_ENV_SETTINGS
   /variable/CMAKE_SUBLIME_TEXT_2_EXCLUDE_BUILD_TREE
   /variable/CMAKE_SUPPRESS_REGENERATION
   /variable/CMAKE_SYSROOT
   /variable/CMAKE_SYSROOT_COMPILE
   /variable/CMAKE_SYSROOT_LINK
   /variable/CMAKE_SYSTEM_APPBUNDLE_PATH
   /variable/CMAKE_SYSTEM_FRAMEWORK_PATH
   /variable/CMAKE_SYSTEM_IGNORE_PATH
   /variable/CMAKE_SYSTEM_IGNORE_PREFIX_PATH
   /variable/CMAKE_SYSTEM_INCLUDE_PATH
   /variable/CMAKE_SYSTEM_LIBRARY_PATH
   /variable/CMAKE_SYSTEM_PREFIX_PATH
   /variable/CMAKE_SYSTEM_PROGRAM_PATH
   /variable/CMAKE_TLS_CAINFO
   /variable/CMAKE_TLS_VERIFY
   /variable/CMAKE_TLS_VERSION
   /variable/CMAKE_USER_MAKE_RULES_OVERRIDE
   /variable/CMAKE_WARN_DEPRECATED
   /variable/CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION
   /variable/CMAKE_XCODE_GENERATE_SCHEME
   /variable/CMAKE_XCODE_GENERATE_TOP_LEVEL_PROJECT_ONLY
   /variable/CMAKE_XCODE_LINK_BUILD_PHASE_MODE
   /variable/CMAKE_XCODE_SCHEME_ADDRESS_SANITIZER
   /variable/CMAKE_XCODE_SCHEME_ADDRESS_SANITIZER_USE_AFTER_RETURN
   /variable/CMAKE_XCODE_SCHEME_DEBUG_DOCUMENT_VERSIONING
   /variable/CMAKE_XCODE_SCHEME_DISABLE_MAIN_THREAD_CHECKER
   /variable/CMAKE_XCODE_SCHEME_DYNAMIC_LIBRARY_LOADS
   /variable/CMAKE_XCODE_SCHEME_DYNAMIC_LINKER_API_USAGE
   /variable/CMAKE_XCODE_SCHEME_ENABLE_GPU_API_VALIDATION
   /variable/CMAKE_XCODE_SCHEME_ENABLE_GPU_FRAME_CAPTURE_MODE
   /variable/CMAKE_XCODE_SCHEME_ENABLE_GPU_SHADER_VALIDATION
   /variable/CMAKE_XCODE_SCHEME_ENVIRONMENT
   /variable/CMAKE_XCODE_SCHEME_GUARD_MALLOC
   /variable/CMAKE_XCODE_SCHEME_LAUNCH_CONFIGURATION
   /variable/CMAKE_XCODE_SCHEME_TEST_CONFIGURATION
   /variable/CMAKE_XCODE_SCHEME_LAUNCH_MODE
   /variable/CMAKE_XCODE_SCHEME_LLDB_INIT_FILE
   /variable/CMAKE_XCODE_SCHEME_MAIN_THREAD_CHECKER_STOP
   /variable/CMAKE_XCODE_SCHEME_MALLOC_GUARD_EDGES
   /variable/CMAKE_XCODE_SCHEME_MALLOC_SCRIBBLE
   /variable/CMAKE_XCODE_SCHEME_MALLOC_STACK
   /variable/CMAKE_XCODE_SCHEME_THREAD_SANITIZER
   /variable/CMAKE_XCODE_SCHEME_THREAD_SANITIZER_STOP
   /variable/CMAKE_XCODE_SCHEME_UNDEFINED_BEHAVIOUR_SANITIZER
   /variable/CMAKE_XCODE_SCHEME_UNDEFINED_BEHAVIOUR_SANITIZER_STOP
   /variable/CMAKE_XCODE_SCHEME_WORKING_DIRECTORY
   /variable/CMAKE_XCODE_SCHEME_ZOMBIE_OBJECTS
   /variable/CMAKE_XCODE_XCCONFIG
   /variable/PackageName_ROOT

Variables that Describe the System
==================================

.. toctree::
   :maxdepth: 1

   /variable/AIX
   /variable/ANDROID
   /variable/APPLE
   /variable/BORLAND
   /variable/BSD
   /variable/CMAKE_ANDROID_NDK_VERSION
   /variable/CMAKE_CL_64
   /variable/CMAKE_COMPILER_2005
   /variable/CMAKE_HOST_APPLE
   /variable/CMAKE_HOST_AIX
   /variable/CMAKE_HOST_BSD
   /variable/CMAKE_HOST_EXECUTABLE_SUFFIX
   /variable/CMAKE_HOST_LINUX
   /variable/CMAKE_HOST_SOLARIS
   /variable/CMAKE_HOST_SYSTEM
   /variable/CMAKE_HOST_SYSTEM_NAME
   /variable/CMAKE_HOST_SYSTEM_PROCESSOR
   /variable/CMAKE_HOST_SYSTEM_VERSION
   /variable/CMAKE_HOST_UNIX
   /variable/CMAKE_HOST_WIN32
   /variable/CMAKE_LIBRARY_ARCHITECTURE
   /variable/CMAKE_LIBRARY_ARCHITECTURE_REGEX
   /variable/CMAKE_OBJECT_PATH_MAX
   /variable/CMAKE_SYSTEM
   /variable/CMAKE_SYSTEM_NAME
   /variable/CMAKE_SYSTEM_PROCESSOR
   /variable/CMAKE_SYSTEM_VERSION
   /variable/CYGWIN
   /variable/GHSMULTI
   /variable/IOS
   /variable/LINUX
   /variable/MINGW
   /variable/MSVC
   /variable/MSVC_IDE
   /variable/MSVC_TOOLSET_VERSION
   /variable/MSVC_VERSION
   /variable/MSYS
   /variable/UNIX
   /variable/WASI
   /variable/WIN32
   /variable/WINCE
   /variable/WINDOWS_PHONE
   /variable/WINDOWS_STORE
   /variable/XCODE
   /variable/XCODE_VERSION

Variables that Control the Build
================================

.. toctree::
   :maxdepth: 1

   /variable/CMAKE_ADSP_ROOT
   /variable/CMAKE_AIX_SHARED_LIBRARY_ARCHIVE
   /variable/CMAKE_AIX_EXPORT_ALL_SYMBOLS
   /variable/CMAKE_ANDROID_ANT_ADDITIONAL_OPTIONS
   /variable/CMAKE_ANDROID_API
   /variable/CMAKE_ANDROID_API_MIN
   /variable/CMAKE_ANDROID_ARCH
   /variable/CMAKE_ANDROID_ARCH_ABI
   /variable/CMAKE_ANDROID_ARM_MODE
   /variable/CMAKE_ANDROID_ARM_NEON
   /variable/CMAKE_ANDROID_ASSETS_DIRECTORIES
   /variable/CMAKE_ANDROID_EXCEPTIONS
   /variable/CMAKE_ANDROID_GUI
   /variable/CMAKE_ANDROID_JAR_DEPENDENCIES
   /variable/CMAKE_ANDROID_JAR_DIRECTORIES
   /variable/CMAKE_ANDROID_JAVA_SOURCE_DIR
   /variable/CMAKE_ANDROID_NATIVE_LIB_DEPENDENCIES
   /variable/CMAKE_ANDROID_NATIVE_LIB_DIRECTORIES
   /variable/CMAKE_ANDROID_NDK
   /variable/CMAKE_ANDROID_NDK_DEPRECATED_HEADERS
   /variable/CMAKE_ANDROID_NDK_TOOLCHAIN_HOST_TAG
   /variable/CMAKE_ANDROID_NDK_TOOLCHAIN_VERSION
   /variable/CMAKE_ANDROID_PROCESS_MAX
   /variable/CMAKE_ANDROID_PROGUARD
   /variable/CMAKE_ANDROID_PROGUARD_CONFIG_PATH
   /variable/CMAKE_ANDROID_RTTI
   /variable/CMAKE_ANDROID_SECURE_PROPS_PATH
   /variable/CMAKE_ANDROID_SKIP_ANT_STEP
   /variable/CMAKE_ANDROID_STANDALONE_TOOLCHAIN
   /variable/CMAKE_ANDROID_STL_TYPE
   /variable/CMAKE_APPLE_SILICON_PROCESSOR
   /variable/CMAKE_ARCHIVE_OUTPUT_DIRECTORY
   /variable/CMAKE_ARCHIVE_OUTPUT_DIRECTORY_CONFIG
   /variable/CMAKE_AUTOGEN_BETTER_GRAPH_MULTI_CONFIG
   /variable/CMAKE_AUTOGEN_COMMAND_LINE_LENGTH_MAX
   /variable/CMAKE_AUTOGEN_ORIGIN_DEPENDS
   /variable/CMAKE_AUTOGEN_PARALLEL
   /variable/CMAKE_AUTOGEN_USE_SYSTEM_INCLUDE
   /variable/CMAKE_AUTOGEN_VERBOSE
   /variable/CMAKE_AUTOMOC
   /variable/CMAKE_AUTOMOC_COMPILER_PREDEFINES
   /variable/CMAKE_AUTOMOC_DEPEND_FILTERS
   /variable/CMAKE_AUTOMOC_MACRO_NAMES
   /variable/CMAKE_AUTOMOC_MOC_OPTIONS
   /variable/CMAKE_AUTOMOC_PATH_PREFIX
   /variable/CMAKE_AUTOMOC_EXECUTABLE
   /variable/CMAKE_AUTORCC
   /variable/CMAKE_AUTORCC_OPTIONS
   /variable/CMAKE_AUTORCC_EXECUTABLE
   /variable/CMAKE_AUTOUIC
   /variable/CMAKE_AUTOUIC_OPTIONS
   /variable/CMAKE_AUTOUIC_SEARCH_PATHS
   /variable/CMAKE_AUTOUIC_EXECUTABLE
   /variable/CMAKE_BUILD_RPATH
   /variable/CMAKE_BUILD_RPATH_USE_ORIGIN
   /variable/CMAKE_BUILD_WITH_INSTALL_NAME_DIR
   /variable/CMAKE_BUILD_WITH_INSTALL_RPATH
   /variable/CMAKE_COMPILE_PDB_OUTPUT_DIRECTORY
   /variable/CMAKE_COMPILE_PDB_OUTPUT_DIRECTORY_CONFIG
   /variable/CMAKE_COMPILE_WARNING_AS_ERROR
   /variable/CMAKE_CONFIG_POSTFIX
   /variable/CMAKE_CROSS_CONFIGS
   /variable/CMAKE_CTEST_ARGUMENTS
   /variable/CMAKE_CUDA_RESOLVE_DEVICE_SYMBOLS
   /variable/CMAKE_CUDA_RUNTIME_LIBRARY
   /variable/CMAKE_CUDA_SEPARABLE_COMPILATION
   /variable/CMAKE_CXX_MODULE_STD
   /variable/CMAKE_CXX_SCAN_FOR_MODULES
   /variable/CMAKE_DEBUG_POSTFIX
   /variable/CMAKE_DEBUGGER_WORKING_DIRECTORY
   /variable/CMAKE_DEFAULT_BUILD_TYPE
   /variable/CMAKE_DEFAULT_CONFIGS
   /variable/CMAKE_DEPENDS_USE_COMPILER
   /variable/CMAKE_DISABLE_PRECOMPILE_HEADERS
   /variable/CMAKE_DLL_NAME_WITH_SOVERSION
   /variable/CMAKE_ENABLE_EXPORTS
   /variable/CMAKE_EXECUTABLE_ENABLE_EXPORTS
   /variable/CMAKE_EXE_LINKER_FLAGS
   /variable/CMAKE_EXE_LINKER_FLAGS_CONFIG
   /variable/CMAKE_EXE_LINKER_FLAGS_CONFIG_INIT
   /variable/CMAKE_EXE_LINKER_FLAGS_INIT
   /variable/CMAKE_EXPORT_FIND_PACKAGE_NAME
   /variable/CMAKE_FOLDER
   /variable/CMAKE_Fortran_FORMAT
   /variable/CMAKE_Fortran_MODULE_DIRECTORY
   /variable/CMAKE_Fortran_PREPROCESS
   /variable/CMAKE_FRAMEWORK
   /variable/CMAKE_FRAMEWORK_MULTI_CONFIG_POSTFIX_CONFIG
   /variable/CMAKE_GHS_NO_SOURCE_GROUP_FILE
   /variable/CMAKE_GLOBAL_AUTOGEN_TARGET
   /variable/CMAKE_GLOBAL_AUTOGEN_TARGET_NAME
   /variable/CMAKE_GLOBAL_AUTORCC_TARGET
   /variable/CMAKE_GLOBAL_AUTORCC_TARGET_NAME
   /variable/CMAKE_GNUtoMS
   /variable/CMAKE_INCLUDE_CURRENT_DIR
   /variable/CMAKE_INCLUDE_CURRENT_DIR_IN_INTERFACE
   /variable/CMAKE_INSTALL_NAME_DIR
   /variable/CMAKE_INSTALL_REMOVE_ENVIRONMENT_RPATH
   /variable/CMAKE_INSTALL_RPATH
   /variable/CMAKE_INSTALL_RPATH_USE_LINK_PATH
   /variable/CMAKE_INTERPROCEDURAL_OPTIMIZATION
   /variable/CMAKE_INTERPROCEDURAL_OPTIMIZATION_CONFIG
   /variable/CMAKE_LANG_CLANG_TIDY
   /variable/CMAKE_LANG_CLANG_TIDY_EXPORT_FIXES_DIR
   /variable/CMAKE_LANG_COMPILER_LAUNCHER
   /variable/CMAKE_LANG_CPPCHECK
   /variable/CMAKE_LANG_CPPLINT
   /variable/CMAKE_LANG_INCLUDE_WHAT_YOU_USE
   /variable/CMAKE_LANG_LINK_GROUP_USING_FEATURE
   /variable/CMAKE_LANG_LINK_GROUP_USING_FEATURE_SUPPORTED
   /variable/CMAKE_LANG_LINK_LIBRARY_FEATURE_ATTRIBUTES
   /variable/CMAKE_LANG_LINK_LIBRARY_FILE_FLAG
   /variable/CMAKE_LANG_LINK_LIBRARY_FLAG
   /variable/CMAKE_LANG_LINK_LIBRARY_USING_FEATURE
   /variable/CMAKE_LANG_LINK_LIBRARY_USING_FEATURE_SUPPORTED
   /variable/CMAKE_LANG_LINK_WHAT_YOU_USE_FLAG
   /variable/CMAKE_LANG_LINKER_LAUNCHER
   /variable/CMAKE_LANG_USING_LINKER_TYPE
   /variable/CMAKE_LANG_VISIBILITY_PRESET
   /variable/CMAKE_LIBRARY_OUTPUT_DIRECTORY
   /variable/CMAKE_LIBRARY_OUTPUT_DIRECTORY_CONFIG
   /variable/CMAKE_LIBRARY_PATH_FLAG
   /variable/CMAKE_LINK_DEF_FILE_FLAG
   /variable/CMAKE_LINK_DEPENDS_NO_SHARED
   /variable/CMAKE_LINK_DEPENDS_USE_LINKER
   /variable/CMAKE_LINK_GROUP_USING_FEATURE
   /variable/CMAKE_LINK_GROUP_USING_FEATURE_SUPPORTED
   /variable/CMAKE_LINK_INTERFACE_LIBRARIES
   /variable/CMAKE_LINK_LIBRARIES_STRATEGY
   /variable/CMAKE_LINK_LIBRARY_FEATURE_ATTRIBUTES
   /variable/CMAKE_LINK_LIBRARY_FILE_FLAG
   /variable/CMAKE_LINK_LIBRARY_FLAG
   /variable/CMAKE_LINK_LIBRARY_USING_FEATURE
   /variable/CMAKE_LINK_LIBRARY_USING_FEATURE_SUPPORTED
   /variable/CMAKE_LINK_WARNING_AS_ERROR
   /variable/CMAKE_LINK_WHAT_YOU_USE
   /variable/CMAKE_LINK_WHAT_YOU_USE_CHECK
   /variable/CMAKE_LINKER_TYPE
   /variable/CMAKE_MACOSX_BUNDLE
   /variable/CMAKE_MACOSX_RPATH
   /variable/CMAKE_MAP_IMPORTED_CONFIG_CONFIG
   /variable/CMAKE_MODULE_LINKER_FLAGS
   /variable/CMAKE_MODULE_LINKER_FLAGS_CONFIG
   /variable/CMAKE_MODULE_LINKER_FLAGS_CONFIG_INIT
   /variable/CMAKE_MODULE_LINKER_FLAGS_INIT
   /variable/CMAKE_MSVC_DEBUG_INFORMATION_FORMAT
   /variable/CMAKE_MSVC_RUNTIME_CHECKS
   /variable/CMAKE_MSVC_RUNTIME_LIBRARY
   /variable/CMAKE_MSVCIDE_RUN_PATH
   /variable/CMAKE_NINJA_OUTPUT_PATH_PREFIX
   /variable/CMAKE_NO_BUILTIN_CHRPATH
   /variable/CMAKE_NO_SYSTEM_FROM_IMPORTED
   /variable/CMAKE_OPTIMIZE_DEPENDENCIES
   /variable/CMAKE_OSX_ARCHITECTURES
   /variable/CMAKE_OSX_DEPLOYMENT_TARGET
   /variable/CMAKE_OSX_SYSROOT
   /variable/CMAKE_PCH_INSTANTIATE_TEMPLATES
   /variable/CMAKE_PCH_WARN_INVALID
   /variable/CMAKE_PDB_OUTPUT_DIRECTORY
   /variable/CMAKE_PDB_OUTPUT_DIRECTORY_CONFIG
   /variable/CMAKE_PLATFORM_NO_VERSIONED_SONAME
   /variable/CMAKE_POSITION_INDEPENDENT_CODE
   /variable/CMAKE_RUNTIME_OUTPUT_DIRECTORY
   /variable/CMAKE_RUNTIME_OUTPUT_DIRECTORY_CONFIG
   /variable/CMAKE_SHARED_LIBRARY_ENABLE_EXPORTS
   /variable/CMAKE_SHARED_LINKER_FLAGS
   /variable/CMAKE_SHARED_LINKER_FLAGS_CONFIG
   /variable/CMAKE_SHARED_LINKER_FLAGS_CONFIG_INIT
   /variable/CMAKE_SHARED_LINKER_FLAGS_INIT
   /variable/CMAKE_SKIP_BUILD_RPATH
   /variable/CMAKE_SKIP_INSTALL_RPATH
   /variable/CMAKE_STATIC_LINKER_FLAGS
   /variable/CMAKE_STATIC_LINKER_FLAGS_CONFIG
   /variable/CMAKE_STATIC_LINKER_FLAGS_CONFIG_INIT
   /variable/CMAKE_STATIC_LINKER_FLAGS_INIT
   /variable/CMAKE_TASKING_TOOLSET
   /variable/CMAKE_TRY_COMPILE_CONFIGURATION
   /variable/CMAKE_TRY_COMPILE_NO_PLATFORM_VARIABLES
   /variable/CMAKE_TRY_COMPILE_PLATFORM_VARIABLES
   /variable/CMAKE_TRY_COMPILE_TARGET_TYPE
   /variable/CMAKE_UNITY_BUILD
   /variable/CMAKE_UNITY_BUILD_BATCH_SIZE
   /variable/CMAKE_UNITY_BUILD_RELOCATABLE
   /variable/CMAKE_UNITY_BUILD_UNIQUE_ID
   /variable/CMAKE_VERIFY_INTERFACE_HEADER_SETS
   /variable/CMAKE_VISIBILITY_INLINES_HIDDEN
   /variable/CMAKE_VS_DEBUGGER_COMMAND
   /variable/CMAKE_VS_DEBUGGER_COMMAND_ARGUMENTS
   /variable/CMAKE_VS_DEBUGGER_ENVIRONMENT
   /variable/CMAKE_VS_DEBUGGER_WORKING_DIRECTORY
   /variable/CMAKE_VS_GLOBALS
   /variable/CMAKE_VS_INCLUDE_INSTALL_TO_DEFAULT_BUILD
   /variable/CMAKE_VS_INCLUDE_PACKAGE_TO_DEFAULT_BUILD
   /variable/CMAKE_VS_JUST_MY_CODE_DEBUGGING
   /variable/CMAKE_VS_NO_COMPILE_BATCHING
   /variable/CMAKE_VS_SDK_EXCLUDE_DIRECTORIES
   /variable/CMAKE_VS_SDK_EXECUTABLE_DIRECTORIES
   /variable/CMAKE_VS_SDK_INCLUDE_DIRECTORIES
   /variable/CMAKE_VS_SDK_LIBRARY_DIRECTORIES
   /variable/CMAKE_VS_SDK_LIBRARY_WINRT_DIRECTORIES
   /variable/CMAKE_VS_SDK_REFERENCE_DIRECTORIES
   /variable/CMAKE_VS_SDK_SOURCE_DIRECTORIES
   /variable/CMAKE_VS_WINRT_BY_DEFAULT
   /variable/CMAKE_WATCOM_RUNTIME_LIBRARY
   /variable/CMAKE_WIN32_EXECUTABLE
   /variable/CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS
   /variable/CMAKE_XCODE_ATTRIBUTE_an-attribute
   /variable/EXECUTABLE_OUTPUT_PATH
   /variable/LIBRARY_OUTPUT_PATH

Variables for Languages
=======================

.. toctree::
   :maxdepth: 1

   /variable/CMAKE_C_COMPILE_FEATURES
   /variable/CMAKE_C_EXTENSIONS
   /variable/CMAKE_C_STANDARD
   /variable/CMAKE_C_STANDARD_REQUIRED
   /variable/CMAKE_CUDA_ARCHITECTURES
   /variable/CMAKE_CUDA_COMPILE_FEATURES
   /variable/CMAKE_CUDA_EXTENSIONS
   /variable/CMAKE_CUDA_HOST_COMPILER
   /variable/CMAKE_CUDA_STANDARD
   /variable/CMAKE_CUDA_STANDARD_REQUIRED
   /variable/CMAKE_CUDA_TOOLKIT_INCLUDE_DIRECTORIES
   /variable/CMAKE_CXX_COMPILE_FEATURES
   /variable/CMAKE_CXX_COMPILER_IMPORT_STD
   /variable/CMAKE_CXX_EXTENSIONS
   /variable/CMAKE_CXX_STANDARD
   /variable/CMAKE_CXX_STANDARD_REQUIRED
   /variable/CMAKE_Fortran_MODDIR_DEFAULT
   /variable/CMAKE_Fortran_MODDIR_FLAG
   /variable/CMAKE_Fortran_MODOUT_FLAG
   /variable/CMAKE_HIP_ARCHITECTURES
   /variable/CMAKE_HIP_COMPILE_FEATURES
   /variable/CMAKE_HIP_EXTENSIONS
   /variable/CMAKE_HIP_PLATFORM
   /variable/CMAKE_HIP_STANDARD
   /variable/CMAKE_HIP_STANDARD_REQUIRED
   /variable/CMAKE_ISPC_HEADER_DIRECTORY
   /variable/CMAKE_ISPC_HEADER_SUFFIX
   /variable/CMAKE_ISPC_INSTRUCTION_SETS
   /variable/CMAKE_LANG_ANDROID_TOOLCHAIN_MACHINE
   /variable/CMAKE_LANG_ANDROID_TOOLCHAIN_PREFIX
   /variable/CMAKE_LANG_ANDROID_TOOLCHAIN_SUFFIX
   /variable/CMAKE_LANG_ARCHIVE_APPEND
   /variable/CMAKE_LANG_ARCHIVE_CREATE
   /variable/CMAKE_LANG_ARCHIVE_FINISH
   /variable/CMAKE_LANG_ARCHIVER_WRAPPER_FLAG
   /variable/CMAKE_LANG_ARCHIVER_WRAPPER_FLAG_SEP
   /variable/CMAKE_LANG_BYTE_ORDER
   /variable/CMAKE_LANG_COMPILE_OBJECT
   /variable/CMAKE_LANG_COMPILER
   /variable/CMAKE_LANG_COMPILER_EXTERNAL_TOOLCHAIN
   /variable/CMAKE_LANG_COMPILER_ID
   /variable/CMAKE_LANG_COMPILER_LOADED
   /variable/CMAKE_LANG_COMPILER_PREDEFINES_COMMAND
   /variable/CMAKE_LANG_COMPILER_TARGET
   /variable/CMAKE_LANG_COMPILER_VERSION
   /variable/CMAKE_LANG_CREATE_SHARED_LIBRARY
   /variable/CMAKE_LANG_CREATE_SHARED_LIBRARY_ARCHIVE
   /variable/CMAKE_LANG_CREATE_SHARED_MODULE
   /variable/CMAKE_LANG_CREATE_STATIC_LIBRARY
   /variable/CMAKE_LANG_EXTENSIONS
   /variable/CMAKE_LANG_EXTENSIONS_DEFAULT
   /variable/CMAKE_LANG_FLAGS
   /variable/CMAKE_LANG_FLAGS_CONFIG
   /variable/CMAKE_LANG_FLAGS_CONFIG_INIT
   /variable/CMAKE_LANG_FLAGS_DEBUG
   /variable/CMAKE_LANG_FLAGS_DEBUG_INIT
   /variable/CMAKE_LANG_FLAGS_INIT
   /variable/CMAKE_LANG_FLAGS_MINSIZEREL
   /variable/CMAKE_LANG_FLAGS_MINSIZEREL_INIT
   /variable/CMAKE_LANG_FLAGS_RELEASE
   /variable/CMAKE_LANG_FLAGS_RELEASE_INIT
   /variable/CMAKE_LANG_FLAGS_RELWITHDEBINFO
   /variable/CMAKE_LANG_FLAGS_RELWITHDEBINFO_INIT
   /variable/CMAKE_LANG_HOST_COMPILER
   /variable/CMAKE_LANG_HOST_COMPILER_ID
   /variable/CMAKE_LANG_HOST_COMPILER_VERSION
   /variable/CMAKE_LANG_IGNORE_EXTENSIONS
   /variable/CMAKE_LANG_IMPLICIT_INCLUDE_DIRECTORIES
   /variable/CMAKE_LANG_IMPLICIT_LINK_DIRECTORIES
   /variable/CMAKE_LANG_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES
   /variable/CMAKE_LANG_IMPLICIT_LINK_LIBRARIES
   /variable/CMAKE_LANG_LIBRARY_ARCHITECTURE
   /variable/CMAKE_LANG_LINK_EXECUTABLE
   /variable/CMAKE_LANG_LINKER_WRAPPER_FLAG
   /variable/CMAKE_LANG_LINKER_WRAPPER_FLAG_SEP
   /variable/CMAKE_LANG_OUTPUT_EXTENSION
   /variable/CMAKE_LANG_SIMULATE_ID
   /variable/CMAKE_LANG_SIMULATE_VERSION
   /variable/CMAKE_LANG_SIZEOF_DATA_PTR
   /variable/CMAKE_LANG_SOURCE_FILE_EXTENSIONS
   /variable/CMAKE_LANG_STANDARD
   /variable/CMAKE_LANG_STANDARD_DEFAULT
   /variable/CMAKE_LANG_STANDARD_INCLUDE_DIRECTORIES
   /variable/CMAKE_LANG_STANDARD_LATEST
   /variable/CMAKE_LANG_STANDARD_LIBRARIES
   /variable/CMAKE_LANG_STANDARD_LINK_DIRECTORIES
   /variable/CMAKE_LANG_STANDARD_REQUIRED
   /variable/CMAKE_OBJC_EXTENSIONS
   /variable/CMAKE_OBJC_STANDARD
   /variable/CMAKE_OBJC_STANDARD_REQUIRED
   /variable/CMAKE_OBJCXX_EXTENSIONS
   /variable/CMAKE_OBJCXX_STANDARD
   /variable/CMAKE_OBJCXX_STANDARD_REQUIRED
   /variable/CMAKE_Swift_LANGUAGE_VERSION
   /variable/CMAKE_USER_MAKE_RULES_OVERRIDE_LANG

Variables for CTest
===================

.. toctree::
   :maxdepth: 1

   /variable/CTEST_BINARY_DIRECTORY
   /variable/CTEST_BUILD_COMMAND
   /variable/CTEST_BUILD_NAME
   /variable/CTEST_BZR_COMMAND
   /variable/CTEST_BZR_UPDATE_OPTIONS
   /variable/CTEST_CHANGE_ID
   /variable/CTEST_CHECKOUT_COMMAND
   /variable/CTEST_CONFIGURATION_TYPE
   /variable/CTEST_CONFIGURE_COMMAND
   /variable/CTEST_COVERAGE_COMMAND
   /variable/CTEST_COVERAGE_EXTRA_FLAGS
   /variable/CTEST_CUSTOM_COVERAGE_EXCLUDE
   /variable/CTEST_CUSTOM_ERROR_EXCEPTION
   /variable/CTEST_CUSTOM_ERROR_MATCH
   /variable/CTEST_CUSTOM_ERROR_POST_CONTEXT
   /variable/CTEST_CUSTOM_ERROR_PRE_CONTEXT
   /variable/CTEST_CUSTOM_MAXIMUM_FAILED_TEST_OUTPUT_SIZE
   /variable/CTEST_CUSTOM_MAXIMUM_NUMBER_OF_ERRORS
   /variable/CTEST_CUSTOM_MAXIMUM_NUMBER_OF_WARNINGS
   /variable/CTEST_CUSTOM_MAXIMUM_PASSED_TEST_OUTPUT_SIZE
   /variable/CTEST_CUSTOM_MEMCHECK_IGNORE
   /variable/CTEST_CUSTOM_POST_MEMCHECK
   /variable/CTEST_CUSTOM_POST_TEST
   /variable/CTEST_CUSTOM_PRE_MEMCHECK
   /variable/CTEST_CUSTOM_PRE_TEST
   /variable/CTEST_CUSTOM_TEST_OUTPUT_TRUNCATION
   /variable/CTEST_CUSTOM_TESTS_IGNORE
   /variable/CTEST_CUSTOM_WARNING_EXCEPTION
   /variable/CTEST_CUSTOM_WARNING_MATCH
   /variable/CTEST_CVS_COMMAND
   /variable/CTEST_CVS_UPDATE_OPTIONS
   /variable/CTEST_DROP_LOCATION
   /variable/CTEST_DROP_METHOD
   /variable/CTEST_DROP_SITE
   /variable/CTEST_DROP_SITE_CDASH
   /variable/CTEST_DROP_SITE_PASSWORD
   /variable/CTEST_DROP_SITE_USER
   /variable/CTEST_EXTRA_COVERAGE_GLOB
   /variable/CTEST_EXTRA_SUBMIT_FILES
   /variable/CTEST_GIT_COMMAND
   /variable/CTEST_GIT_INIT_SUBMODULES
   /variable/CTEST_GIT_UPDATE_CUSTOM
   /variable/CTEST_GIT_UPDATE_OPTIONS
   /variable/CTEST_HG_COMMAND
   /variable/CTEST_HG_UPDATE_OPTIONS
   /variable/CTEST_LABELS_FOR_SUBPROJECTS
   /variable/CTEST_MEMORYCHECK_COMMAND
   /variable/CTEST_MEMORYCHECK_COMMAND_OPTIONS
   /variable/CTEST_MEMORYCHECK_SANITIZER_OPTIONS
   /variable/CTEST_MEMORYCHECK_SUPPRESSIONS_FILE
   /variable/CTEST_MEMORYCHECK_TYPE
   /variable/CTEST_NIGHTLY_START_TIME
   /variable/CTEST_NOTES_FILES
   /variable/CTEST_P4_CLIENT
   /variable/CTEST_P4_COMMAND
   /variable/CTEST_P4_OPTIONS
   /variable/CTEST_P4_UPDATE_OPTIONS
   /variable/CTEST_RESOURCE_SPEC_FILE
   /variable/CTEST_RUN_CURRENT_SCRIPT
   /variable/CTEST_SCRIPT_DIRECTORY
   /variable/CTEST_SITE
   /variable/CTEST_SOURCE_DIRECTORY
   /variable/CTEST_SUBMIT_INACTIVITY_TIMEOUT
   /variable/CTEST_SUBMIT_URL
   /variable/CTEST_SVN_COMMAND
   /variable/CTEST_SVN_OPTIONS
   /variable/CTEST_SVN_UPDATE_OPTIONS
   /variable/CTEST_TEST_LOAD
   /variable/CTEST_TEST_TIMEOUT
   /variable/CTEST_TLS_VERIFY
   /variable/CTEST_TLS_VERSION
   /variable/CTEST_UPDATE_COMMAND
   /variable/CTEST_UPDATE_OPTIONS
   /variable/CTEST_UPDATE_VERSION_ONLY
   /variable/CTEST_UPDATE_VERSION_OVERRIDE
   /variable/CTEST_USE_LAUNCHERS

Variables for CPack
===================

.. toctree::
   :maxdepth: 1

   /variable/CPACK_ABSOLUTE_DESTINATION_FILES
   /variable/CPACK_COMPONENT_INCLUDE_TOPLEVEL_DIRECTORY
   /variable/CPACK_CUSTOM_INSTALL_VARIABLES
   /variable/CPACK_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION
   /variable/CPACK_INCLUDE_TOPLEVEL_DIRECTORY
   /variable/CPACK_INSTALL_DEFAULT_DIRECTORY_PERMISSIONS
   /variable/CPACK_PACKAGING_INSTALL_PREFIX
   /variable/CPACK_SET_DESTDIR
   /variable/CPACK_WARN_ON_ABSOLUTE_INSTALL_DESTINATION

Variable Expansion Operators
============================

.. toctree::
   :maxdepth: 1

   /variable/CACHE
   /variable/ENV

Internal Variables
==================

CMake has many internal variables.  Most of them are undocumented.
Some of them, however, were at some point described as normal
variables, and therefore may be encountered in legacy code. They
are subject to change, and not recommended for use in project code.

.. toctree::
   :maxdepth: 1

   /variable/CMAKE_HOME_DIRECTORY
   /variable/CMAKE_INTERNAL_PLATFORM_ABI
   /variable/CMAKE_LANG_COMPILER_ABI
   /variable/CMAKE_LANG_COMPILER_ARCHITECTURE_ID
   /variable/CMAKE_LANG_COMPILER_VERSION_INTERNAL
   /variable/CMAKE_LANG_LINKER_PREFERENCE
   /variable/CMAKE_LANG_LINKER_PREFERENCE_PROPAGATES
   /variable/CMAKE_LANG_PLATFORM_ID
   /variable/CMAKE_NOT_USING_CONFIG_FLAGS
   /variable/CMAKE_VS_INTEL_Fortran_PROJECT_VERSION

Deprecated Variables that Provide Information
=============================================

.. toctree::
   :maxdepth: 1

   /variable/CMAKE_EXTRA_GENERATOR

Deprecated Variables that Change Behavior
=========================================

.. toctree::
   :maxdepth: 1

   /variable/CMAKE_AUTOMOC_RELAXED_MODE
   /variable/CMAKE_BACKWARDS_COMPATIBILITY
   /variable/CMAKE_FIND_PACKAGE_NO_PACKAGE_REGISTRY
   /variable/CMAKE_FIND_PACKAGE_NO_SYSTEM_PACKAGE_REGISTRY

Deprecated Variables that Describe the System
=============================================

.. toctree::
   :maxdepth: 1

   /variable/MSVC10
   /variable/MSVC11
   /variable/MSVC12
   /variable/MSVC14
   /variable/MSVC60
   /variable/MSVC70
   /variable/MSVC71
   /variable/MSVC80
   /variable/MSVC90

Deprecated Variables that Control the Build
===========================================

.. toctree::
   :maxdepth: 1

   /variable/CMAKE_IOS_INSTALL_COMBINED
   /variable/CMAKE_LANG_USING_LINKER_MODE
   /variable/CMAKE_USE_RELATIVE_PATHS

Deprecated Variables for Languages
==================================

.. toctree::
   :maxdepth: 1

   /variable/CMAKE_COMPILER_IS_GNUCC
   /variable/CMAKE_COMPILER_IS_GNUCXX
   /variable/CMAKE_COMPILER_IS_GNUG77

Deprecated Variables for CTest
==============================

.. toctree::
   :maxdepth: 1

   /variable/CTEST_CURL_OPTIONS
   /variable/CTEST_CVS_CHECKOUT
   /variable/CTEST_SCP_COMMAND
   /variable/CTEST_TRIGGER_SITE
