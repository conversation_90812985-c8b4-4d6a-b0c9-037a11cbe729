set ::msgcat::header "Project-Id-Version: gitk\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2025-05-07 09:17\nLast-Translator: \u0ba4\u0bae\u0bbf\u0bb4\u0bcd\u0ba8\u0bc7\u0bb0\u0bae\u0bcd (TamilNeram.github.io)\nLanguage-Team: Tamil\nLanguage: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\n"
::msgcat::mcset ta "Couldn't get list of unmerged files:" "\u0b92\u0bb0\u0bc1\u0b99\u0bcd\u0b95\u0bbf\u0ba3\u0bc8\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bbe\u0ba4 \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bbf\u0ba9\u0bcd \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bc8\u0baa\u0bcd \u0baa\u0bc6\u0bb1 \u0bae\u0bc1\u0b9f\u0bbf\u0baf\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8:"
::msgcat::mcset ta "Color words" "\u0bb5\u0ba3\u0bcd\u0ba3 \u0b9a\u0bca\u0bb1\u0bcd\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Markup words" "\u0b95\u0bc1\u0bb1\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0b9a\u0bca\u0bb1\u0bcd\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Error parsing revisions:" "\u0baa\u0bbf\u0bb4\u0bc8\u0b95\u0bb3\u0bc8 \u0baa\u0bbe\u0b95\u0bc1\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0ba4\u0bb2\u0bcd:"
::msgcat::mcset ta "Error executing --argscmd command:" "--argscmd \u0b95\u0b9f\u0bcd\u0b9f\u0bb3\u0bc8\u0baf\u0bc8 \u0b87\u0baf\u0b95\u0bcd\u0b95\u0bc1\u0bb5\u0ba4\u0bbf\u0bb2\u0bcd \u0baa\u0bbf\u0bb4\u0bc8:"
::msgcat::mcset ta "No files selected: --merge specified but no files are unmerged." "\u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bcd \u0b8e\u0ba4\u0bc1\u0bb5\u0bc1\u0bae\u0bcd \u0ba4\u0bc7\u0bb0\u0bcd\u0ba8\u0bcd\u0ba4\u0bc6\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8: --\u0b92\u0ba9\u0bcd\u0bb1\u0bbf\u0ba3\u0bc8 \u0b95\u0bc1\u0bb1\u0bbf\u0baa\u0bcd\u0baa\u0bbf\u0b9f\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f\u0bc1\u0bb3\u0bcd\u0bb3\u0ba4\u0bc1, \u0b86\u0ba9\u0bbe\u0bb2\u0bcd \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bcd \u0b8e\u0ba4\u0bc1\u0bb5\u0bc1\u0bae\u0bcd \u0b85\u0bb5\u0bbf\u0bb4\u0bcd\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8."
::msgcat::mcset ta "No files selected: --merge specified but no unmerged files are within file limit." "\u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bcd \u0b8e\u0ba4\u0bc1\u0bb5\u0bc1\u0bae\u0bcd \u0ba4\u0bc7\u0bb0\u0bcd\u0ba8\u0bcd\u0ba4\u0bc6\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8: --\u0b92\u0ba9\u0bcd\u0bb1\u0bbf\u0ba3\u0bc8 \u0b95\u0bc1\u0bb1\u0bbf\u0baa\u0bcd\u0baa\u0bbf\u0b9f\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f\u0bc1\u0bb3\u0bcd\u0bb3\u0ba4\u0bc1, \u0b86\u0ba9\u0bbe\u0bb2\u0bcd \u0b85\u0bb5\u0bbf\u0bb4\u0bcd\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bbe\u0ba4 \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bcd \u0b8e\u0ba4\u0bc1\u0bb5\u0bc1\u0bae\u0bcd \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1 \u0bb5\u0bb0\u0bae\u0bcd\u0baa\u0bbf\u0bb1\u0bcd\u0b95\u0bc1\u0bb3\u0bcd \u0b87\u0bb2\u0bcd\u0bb2\u0bc8."
::msgcat::mcset ta "Error executing git log:" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf \u0baa\u0ba4\u0bbf\u0bb5\u0bc8 \u0b87\u0baf\u0b95\u0bcd\u0b95\u0bc1\u0bb5\u0ba4\u0bbf\u0bb2\u0bcd \u0baa\u0bbf\u0bb4\u0bc8:"
::msgcat::mcset ta "Reading" "\u0baa\u0b9f\u0bbf\u0ba4\u0bcd\u0ba4\u0bb2\u0bcd"
::msgcat::mcset ta "Reading commits..." "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0b95\u0bb3\u0bc8\u0baa\u0bcd \u0baa\u0b9f\u0bbf\u0ba4\u0bcd\u0ba4\u0bb2\u0bcd..."
::msgcat::mcset ta "No commits selected" "\u0b8e\u0ba8\u0bcd\u0ba4 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0b95\u0bb3\u0bc1\u0bae\u0bcd \u0ba4\u0bc7\u0bb0\u0bcd\u0ba8\u0bcd\u0ba4\u0bc6\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8"
::msgcat::mcset ta "Command line" "\u0b95\u0b9f\u0bcd\u0b9f\u0bb3\u0bc8 \u0bb5\u0bb0\u0bbf"
::msgcat::mcset ta "Can't parse git log output:" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf \u0baa\u0ba4\u0bbf\u0bb5\u0bc1 \u0bb5\u0bc6\u0bb3\u0bbf\u0baf\u0bc0\u0b9f\u0bcd\u0b9f\u0bc8 \u0b85\u0bb2\u0b9a \u0bae\u0bc1\u0b9f\u0bbf\u0baf\u0bbe\u0ba4\u0bc1:"
::msgcat::mcset ta "No commit information available" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bbf \u0b8e\u0ba4\u0bc1\u0bb5\u0bc1\u0bae\u0bcd \u0b95\u0bbf\u0b9f\u0bc8\u0b95\u0bcd\u0b95\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8"
::msgcat::mcset ta "OK" "\u0b9a\u0bb0\u0bbf"
::msgcat::mcset ta "Cancel" "\u0ba8\u0bc0\u0b95\u0bcd\u0b95\u0bb1\u0bb2\u0bcd"
::msgcat::mcset ta "&Update" "\u0baa\u0bc1\u0ba4\u0bc1\u0baa\u0bcd\u0baa\u0bbf\u0ba4\u0bcd\u0ba4\u0bb2\u0bcd"
::msgcat::mcset ta "&Reload" "\u0bae\u0bc0\u0ba3\u0bcd\u0b9f\u0bc1\u0bae\u0bcd \u0b8f\u0bb1\u0bcd\u0bb1\u0bc1"
::msgcat::mcset ta "Reread re&ferences" "\u0b95\u0bc1\u0bb1\u0bbf\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bc8 \u0bae\u0bc0\u0ba3\u0bcd\u0b9f\u0bc1\u0bae\u0bcd \u0baa\u0b9f\u0bbf"
::msgcat::mcset ta "&List references" "\u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bcd \u0b95\u0bc1\u0bb1\u0bbf\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Start git &gui" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf \u0b87\u0b9f\u0bc8\u0bae\u0bc1\u0b95\u0ba4\u0bcd\u0ba4\u0bc8\u0ba4\u0bcd \u0ba4\u0bca\u0b9f\u0b99\u0bcd\u0b95\u0bc1"
::msgcat::mcset ta "&Quit" "\u0bb5\u0bc6\u0bb3\u0bbf\u0baf\u0bc7\u0bb1\u0bc1"
::msgcat::mcset ta "&File" "\u0b95\u0bc7\u0bbe\u0baa\u0bcd\u0baa\u0bc1"
::msgcat::mcset ta "&Preferences" "\u0bb5\u0bbf\u0bb0\u0bc1\u0baa\u0bcd\u0baa\u0ba4\u0bcd\u0ba4\u0bc7\u0bb0\u0bcd\u0bb5\u0bc1\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "&Edit" "\u0ba4\u0bbf\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "&New view..." "\u0baa\u0bc1\u0ba4\u0bbf\u0baf \u0baa\u0bbe\u0bb0\u0bcd\u0bb5\u0bc8..."
::msgcat::mcset ta "&Edit view..." "\u0baa\u0bbe\u0bb0\u0bcd\u0bb5\u0bc8\u0baf\u0bc8\u0ba4\u0bcd \u0ba4\u0bbf\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1..."
::msgcat::mcset ta "&Delete view" "\u0baa\u0bbe\u0bb0\u0bcd\u0bb5\u0bc8\u0baf\u0bc8 \u0ba8\u0bc0\u0b95\u0bcd\u0b95\u0bc1"
::msgcat::mcset ta "&All files" "\u0b85\u0ba9\u0bc8\u0ba4\u0bcd\u0ba4\u0bc1 \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "&View" "\u0b95\u0bbe\u0ba3\u0bcd\u0b95"
::msgcat::mcset ta "&About gitk" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf\u0b95\u0bc7 \u0baa\u0bb1\u0bcd\u0bb1\u0bbf"
::msgcat::mcset ta "&Key bindings" "\u0bae\u0bc1\u0b95\u0bcd\u0b95\u0bbf\u0baf \u0baa\u0bbf\u0ba3\u0bc8\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "&Help" "\u0b89\u0ba4\u0bb5\u0bbf"
::msgcat::mcset ta "Commit ID:" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b85\u0b9f\u0bc8\u0baf\u0bbe\u0bb3\u0bae\u0bcd:"
::msgcat::mcset ta "Row" "\u0ba8\u0bbf\u0bb0\u0bc8"
::msgcat::mcset ta "Find" "\u0b95\u0ba3\u0bcd\u0b9f\u0bc1\u0baa\u0bbf\u0b9f\u0bbf"
::msgcat::mcset ta "commit" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf"
::msgcat::mcset ta "containing:" "\u0b95\u0bca\u0ba3\u0bcd\u0b9f\u0bbf\u0bb0\u0bc1\u0b95\u0bcd\u0b95\u0bbf\u0bb1\u0ba4\u0bc1:"
::msgcat::mcset ta "touching paths:" "\u0ba4\u0bca\u0b9f\u0bc1\u0bae\u0bcd \u0baa\u0bbe\u0ba4\u0bc8\u0b95\u0bb3\u0bcd:"
::msgcat::mcset ta "adding/removing string:" "\u0b9a\u0bb0\u0ba4\u0bcd\u0ba4\u0bc8\u0b9a\u0bcd \u0b9a\u0bc7\u0bb0\u0bcd\u0baa\u0bcd\u0baa\u0ba4\u0bc1/\u0b85\u0b95\u0bb1\u0bcd\u0bb1\u0bc1\u0bb5\u0ba4\u0bc1:"
::msgcat::mcset ta "changing lines matching:" "\u0baa\u0bca\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0b95\u0bcd\u0b95\u0bc2\u0b9f\u0bbf\u0baf \u0bb5\u0bb0\u0bbf\u0b95\u0bb3\u0bc8 \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0bc1\u0ba4\u0bb2\u0bcd:"
::msgcat::mcset ta "Exact" "\u0b9a\u0bb0\u0bbf\u0baf\u0bbe\u0ba9"
::msgcat::mcset ta "IgnCase" "\u0bb5\u0bb4\u0b95\u0bcd\u0b95\u0bc1\u0ba4\u0bb5\u0bbf\u0bb0\u0bcd"
::msgcat::mcset ta "Regexp" "\u0bb5\u0bb4\u0b95\u0bcd\u0b95\u0bb5\u0bc6\u0bb3\u0bbf"
::msgcat::mcset ta "All fields" "\u0b85\u0ba9\u0bc8\u0ba4\u0bcd\u0ba4\u0bc1 \u0baa\u0bc1\u0bb2\u0b99\u0bcd\u0b95\u0bb3\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "Headline" "\u0ba4\u0bb2\u0bc8\u0baa\u0bcd\u0baa\u0bc1"
::msgcat::mcset ta "Comments" "\u0b95\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Author" "\u0ba8\u0bc2\u0bb2\u0bbe\u0b9a\u0bbf\u0bb0\u0bbf\u0baf\u0bb0\u0bcd"
::msgcat::mcset ta "Committer" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0baa\u0bb5\u0bb0\u0bcd"
::msgcat::mcset ta "Search" "\u0ba4\u0bc7\u0b9f\u0bc1"
::msgcat::mcset ta "Diff" "\u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1"
::msgcat::mcset ta "Old version" "\u0baa\u0bb4\u0bc8\u0baf \u0baa\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc1"
::msgcat::mcset ta "New version" "\u0baa\u0bc1\u0ba4\u0bbf\u0baf \u0baa\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc1"
::msgcat::mcset ta "Lines of context" "\u0b9a\u0bc2\u0bb4\u0bb2\u0bbf\u0ba9\u0bcd \u0bb5\u0bb0\u0bbf\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Ignore space change" "\u0b87\u0b9f\u0bc8\u0bb5\u0bc6\u0bb3\u0bbf \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0ba4\u0bcd\u0ba4\u0bc8 \u0baa\u0bc1\u0bb1\u0b95\u0bcd\u0b95\u0ba3\u0bbf"
::msgcat::mcset ta "Line diff" "\u0bb5\u0bb0\u0bbf \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1"
::msgcat::mcset ta "Patch" "\u0b92\u0b9f\u0bcd\u0b9f\u0bc1"
::msgcat::mcset ta "Tree" "\u0bae\u0bb0\u0bae\u0bcd"
::msgcat::mcset ta "Diff this -> selected" "\u0b87\u0ba4\u0bc8 \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0b99\u0bcd\u0b95\u0bb3\u0bcd -> \u0ba4\u0bc7\u0bb0\u0bcd\u0ba8\u0bcd\u0ba4\u0bc6\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f\u0ba4\u0bc1"
::msgcat::mcset ta "Diff selected -> this" "\u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 \u0ba4\u0bc7\u0bb0\u0bcd\u0ba8\u0bcd\u0ba4\u0bc6\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f\u0ba4\u0bc1 -> \u0b87\u0ba4\u0bc1"
::msgcat::mcset ta "Make patch" "\u0b92\u0b9f\u0bcd\u0b9f\u0bc1 \u0b9a\u0bc6\u0baf\u0bcd"
::msgcat::mcset ta "Create tag" "\u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb2\u0bcd\u0bb2\u0bc8 \u0b89\u0bb0\u0bc1\u0bb5\u0bbe\u0b95\u0bcd\u0b95\u0bc1"
::msgcat::mcset ta "Copy commit reference" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b95\u0bc1\u0bb1\u0bbf\u0baa\u0bcd\u0baa\u0bc1 \u0ba8\u0b95\u0bb2\u0bc6\u0b9f\u0bc1"
::msgcat::mcset ta "Write commit to file" "\u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bbf\u0bb2\u0bcd \u0b89\u0bb1\u0bb5\u0bc8 \u0b8e\u0bb4\u0bc1\u0ba4\u0bc1\u0b99\u0bcd\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Create new branch" "\u0baa\u0bc1\u0ba4\u0bbf\u0baf \u0b95\u0bbf\u0bb3\u0bc8\u0baf\u0bc8 \u0b89\u0bb0\u0bc1\u0bb5\u0bbe\u0b95\u0bcd\u0b95\u0bc1"
::msgcat::mcset ta "Cherry-pick this commit" "\u0b95\u0ba9\u0bbf-\u0b8e\u0b9f\u0bc1 \u0b87\u0ba8\u0bcd\u0ba4 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf"
::msgcat::mcset ta "Reset HEAD branch to here" "\u0ba4\u0bb2\u0bc8 \u0b95\u0bbf\u0bb3\u0bc8\u0baf\u0bc8 \u0b87\u0b99\u0bcd\u0b95\u0bc7 \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8"
::msgcat::mcset ta "Mark this commit" "\u0b87\u0ba8\u0bcd\u0ba4 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0baf\u0bc8\u0b95\u0bcd \u0b95\u0bc1\u0bb1\u0bbf"
::msgcat::mcset ta "Return to mark" "\u0bae\u0bbe\u0bb0\u0bcd\u0b95\u0bcd\u0b95\u0bc1\u0b95\u0bcd\u0b95\u0bc1\u0ba4\u0bcd \u0ba4\u0bbf\u0bb0\u0bc1\u0bae\u0bcd\u0baa\u0bc1"
::msgcat::mcset ta "Find descendant of this and mark" "\u0b87\u0ba4\u0ba9\u0bcd \u0bb5\u0bb4\u0bbf\u0ba4\u0bcd\u0ba4\u0bcb\u0ba9\u0bcd\u0bb1\u0bb2\u0bc8\u0b95\u0bcd \u0b95\u0ba3\u0bcd\u0b9f\u0bc1\u0baa\u0bbf\u0b9f\u0bbf\u0ba4\u0bcd\u0ba4\u0bc1 \u0b95\u0bc1\u0bb1\u0bbf"
::msgcat::mcset ta "Compare with marked commit" "\u0b95\u0bc1\u0bb1\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bbe\u0b9f\u0bcd\u0b9f\u0bc1\u0b9f\u0ba9\u0bcd \u0b92\u0baa\u0bcd\u0baa\u0bbf\u0b9f\u0bc1\u0b95"
::msgcat::mcset ta "Diff this -> marked commit" "\u0b87\u0ba4\u0bc8 \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0b99\u0bcd\u0b95\u0bb3\u0bcd -> \u0b95\u0bc1\u0bb1\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf"
::msgcat::mcset ta "Diff marked commit -> this" "\u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 \u0b95\u0bc1\u0bb1\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf -> \u0b87\u0ba4\u0bc1"
::msgcat::mcset ta "Revert this commit" "\u0b87\u0ba8\u0bcd\u0ba4 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bbe\u0b9f\u0bcd\u0b9f\u0bc8 \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0bb5\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "Check out this branch" "\u0b87\u0ba8\u0bcd\u0ba4 \u0b95\u0bbf\u0bb3\u0bc8\u0baf\u0bc8\u0baa\u0bcd \u0baa\u0bbe\u0bb0\u0bc1\u0b99\u0bcd\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Rename this branch" "\u0b87\u0ba8\u0bcd\u0ba4 \u0b95\u0bbf\u0bb3\u0bc8\u0baf\u0bc8 \u0bae\u0bb1\u0bc1\u0baa\u0bc6\u0baf\u0bb0\u0bbf\u0b9f\u0bc1"
::msgcat::mcset ta "Remove this branch" "\u0b87\u0ba8\u0bcd\u0ba4 \u0b95\u0bbf\u0bb3\u0bc8\u0baf\u0bc8 \u0b85\u0b95\u0bb1\u0bcd\u0bb1\u0bc1"
::msgcat::mcset ta "Copy branch name" "\u0b95\u0bbf\u0bb3\u0bc8 \u0baa\u0bc6\u0baf\u0bb0\u0bc8 \u0ba8\u0b95\u0bb2\u0bc6\u0b9f\u0bc1"
::msgcat::mcset ta "Highlight this too" "\u0b87\u0ba4\u0bc8 \u0bae\u0bc1\u0ba9\u0bcd\u0ba9\u0bbf\u0bb2\u0bc8\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "Highlight this only" "\u0b87\u0ba4\u0bc8 \u0bae\u0bc1\u0ba9\u0bcd\u0ba9\u0bbf\u0bb2\u0bc8\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "External diff" "\u0bb5\u0bc6\u0bb3\u0bbf\u0baa\u0bcd\u0baa\u0bc1\u0bb1 \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1"
::msgcat::mcset ta "Blame parent commit" "\u0baa\u0bc6\u0bb1\u0bcd\u0bb1\u0bcb\u0bb0\u0bc8 \u0b95\u0bc1\u0bb1\u0bcd\u0bb1\u0bae\u0bcd \u0b9a\u0bbe\u0b9f\u0bcd\u0b9f\u0bc1"
::msgcat::mcset ta "Copy path" "\u0ba8\u0b95\u0bb2\u0bcd \u0baa\u0bbe\u0ba4\u0bc8"
::msgcat::mcset ta "Show origin of this line" "\u0b87\u0ba8\u0bcd\u0ba4 \u0bb5\u0bb0\u0bbf\u0baf\u0bbf\u0ba9\u0bcd \u0ba4\u0bcb\u0bb1\u0bcd\u0bb1\u0ba4\u0bcd\u0ba4\u0bc8\u0b95\u0bcd \u0b95\u0bbe\u0b9f\u0bcd\u0b9f\u0bc1"
::msgcat::mcset ta "Run git gui blame on this line" "\u0b87\u0ba8\u0bcd\u0ba4 \u0bb5\u0bb0\u0bbf\u0baf\u0bbf\u0bb2\u0bcd \u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf \u0b87\u0b9f\u0bc8\u0bae\u0bc1\u0b95\u0bae\u0bcd \u0baa\u0bb4\u0bbf\u0baf\u0bc8 \u0b87\u0baf\u0b95\u0bcd\u0b95\u0bc1"
::msgcat::mcset ta "About gitk" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf\u0b95\u0bc7 \u0baa\u0bb1\u0bcd\u0bb1\u0bbf"
::msgcat::mcset ta "\nGitk - a commit viewer for git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nUse and redistribute under the terms of the GNU General Public License" "\n\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf\u0b95\u0bc7 - \u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf \u0b92\u0bb0\u0bc1 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0baa\u0bbe\u0bb0\u0bcd\u0bb5\u0bc8\u0baf\u0bbe\u0bb3\u0bb0\u0bcd \n\n\u0baa\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc1\u0bb0\u0bbf\u0bae\u0bc8 \u00a9 2005-2016 \u0baa\u0bbe\u0bb2\u0bcd \u0bae\u0bc6\u0b95\u0bcd\u0b95\u0bc6\u0bb0\u0bbe\u0b9a\u0bcd \n\n\u0b95\u0bc1\u0ba9\u0bc1 \u0baa\u0bca\u0ba4\u0bc1 \u0baa\u0bca\u0ba4\u0bc1\u0bae\u0b95\u0bcd\u0b95\u0bb3\u0bcd \u0b89\u0bb0\u0bbf\u0bae\u0ba4\u0bcd\u0ba4\u0bbf\u0ba9\u0bcd \u0bb5\u0bbf\u0ba4\u0bbf\u0bae\u0bc1\u0bb1\u0bc8\u0b95\u0bb3\u0bbf\u0ba9\u0bcd \u0b95\u0bc0\u0bb4\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bb5\u0bc1\u0bae\u0bcd \u0bae\u0bb1\u0bc1\u0baa\u0b95\u0bbf\u0bb0\u0bcd\u0bb5\u0bc1 \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0bb5\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "Close" "\u0bae\u0bc2\u0b9f\u0bc1"
::msgcat::mcset ta "Gitk key bindings" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf\u0b95\u0bc7 \u0bb5\u0bbf\u0b9a\u0bc8 \u0baa\u0bbf\u0ba3\u0bc8\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Gitk key bindings:" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf\u0b95\u0bc7 \u0bb5\u0bbf\u0b9a\u0bc8 \u0baa\u0bbf\u0ba3\u0bc8\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bcd:"
::msgcat::mcset ta "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009\u0bb5\u0bc6\u0bb3\u0bbf\u0baf\u0bc7\u0bb1\u0bc1"
::msgcat::mcset ta "<%s-W>\u0009\u0009Close window" "<%s-w>\u0009\u0009\u0b9a\u0bbe\u0bb3\u0bb0\u0ba4\u0bcd\u0ba4\u0bc8 \u0bae\u0bc2\u0b9f\u0bc1"
::msgcat::mcset ta "<Home>\u0009\u0009Move to first commit" "<\u0bb5\u0bc0\u0b9f\u0bc1> \u0bae\u0bc1\u0ba4\u0bb2\u0bcd \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0b95\u0bcd\u0b95\u0bc1 \u0ba8\u0b95\u0bb0\u0bcd\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "<End>\u0009\u0009Move to last commit" "<\u0bae\u0bc1\u0b9f\u0bbf> \u0b95\u0b9f\u0bc8\u0b9a\u0bbf \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0b95\u0bcd\u0b95\u0bc1 \u0ba8\u0b95\u0bb0\u0bcd\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "<Up>, p, k\u0009Move up one commit" "<\u0bae\u0bc7\u0bb2\u0bc7>, \u0baa\u0bbf, \u0b95\u0bc7\u0009\u0b92\u0bb0\u0bc1 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0baf\u0bc8 \u0bae\u0bc7\u0bb2\u0bc7 \u0ba8\u0b95\u0bb0\u0bcd\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "<Down>, n, j\u0009Move down one commit" "<\u0b95\u0bc0\u0bb4\u0bcd>, n, j\u0009\u0b92\u0bb0\u0bc1 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0baf\u0bc8 \u0b95\u0bc0\u0bb4\u0bc7 \u0ba8\u0b95\u0bb0\u0bcd\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "<Left>, z, h\u0009Go back in history list" "<\u0b87\u0b9f\u0ba4\u0bc1>, z, h\u0009\u0bb5\u0bb0\u0bb2\u0bbe\u0bb1\u0bcd\u0bb1\u0bc1 \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bbf\u0bb2\u0bcd \u0ba4\u0bbf\u0bb0\u0bc1\u0bae\u0bcd\u0baa\u0bbf\u0b9a\u0bcd \u0b9a\u0bc6\u0bb2\u0bcd"
::msgcat::mcset ta "<Right>, x, l\u0009Go forward in history list" "<\u0bb5\u0bb2\u0ba4\u0bc1>, x, l\u0009\u0bb5\u0bb0\u0bb2\u0bbe\u0bb1\u0bcd\u0bb1\u0bc1 \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bbf\u0bb2\u0bcd \u0bae\u0bc1\u0ba9\u0bcd\u0ba9\u0bcb\u0b95\u0bcd\u0b95\u0bbf \u0b9a\u0bc6\u0bb2\u0bcd"
::msgcat::mcset ta "<%s-n>\u0009Go to n-th parent of current commit in history list" "<%s-n> \u0bb5\u0bb0\u0bb2\u0bbe\u0bb1\u0bcd\u0bb1\u0bc1 \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bbf\u0bb2\u0bcd \u0ba4\u0bb1\u0bcd\u0baa\u0bcb\u0ba4\u0bc8\u0baf \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bbe\u0b9f\u0bcd\u0b9f\u0bbf\u0ba9\u0bcd n- \u0bb5\u0ba4\u0bc1 \u0baa\u0bc6\u0bb1\u0bcd\u0bb1\u0bcb\u0bb0\u0bbf\u0b9f\u0bae\u0bcd \u0b9a\u0bc6\u0bb2\u0bcd"
::msgcat::mcset ta "<PageUp>\u0009Move up one page in commit list" "<\u0baa\u0b95\u0bcd\u0b95\u0bae\u0bcd\u0bae\u0bc7\u0bb2\u0bcd>\u0009\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bbf\u0bb2\u0bcd \u0b92\u0bb0\u0bc1 \u0baa\u0b95\u0bcd\u0b95\u0ba4\u0bcd\u0ba4\u0bc8 \u0ba8\u0b95\u0bb0\u0bcd\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "<PageDown>\u0009Move down one page in commit list" "<\u0baa\u0b95\u0bcd\u0b95\u0bae\u0bcd\u0b95\u0bc0\u0bb4\u0bcd>\u0009\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bbf\u0bb2\u0bcd \u0b92\u0bb0\u0bc1 \u0baa\u0b95\u0bcd\u0b95\u0ba4\u0bcd\u0ba4\u0bc8 \u0ba8\u0b95\u0bb0\u0bcd\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "<%s-Home>\u0009Scroll to top of commit list" "<%s-\u0bb5\u0bc0\u0b9f\u0bc1>\u0009\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bc8 \u0bae\u0bc7\u0bb2\u0bcd \u0baa\u0b95\u0bc1\u0ba4\u0bbf\u0b95\u0bcd\u0b95\u0bc1 \u0b89\u0bb0\u0bc1\u0b9f\u0bcd\u0b9f\u0bb5\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "<%s-End>\u0009Scroll to bottom of commit list" "<%s-\u0bae\u0bc1\u0b9f\u0bbf> \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bbf\u0ba9\u0bcd \u0b95\u0bc0\u0bb4\u0bcd \u0baa\u0b95\u0bc1\u0ba4\u0bbf\u0b95\u0bcd\u0b95\u0bc1 \u0b89\u0bb0\u0bc1\u0b9f\u0bcd\u0b9f\u0bb5\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "<%s-Up>\u0009Scroll commit list up one line" "<%s-\u0bae\u0bc7\u0bb2\u0bc7>\u0009\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bc8 \u0b92\u0bb0\u0bc1 \u0bb5\u0bb0\u0bbf \u0bae\u0bc7\u0bb2\u0bc7 \u0b89\u0bb0\u0bc1\u0b9f\u0bcd\u0b9f\u0bb5\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "<%s-Down>\u0009Scroll commit list down one line" "<%s-\u0b95\u0bc0\u0bb4\u0bcd>\u0009\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bc8 \u0b92\u0bb0\u0bc1 \u0bb5\u0bb0\u0bbf \u0b95\u0bc0\u0bb4\u0bc7 \u0b89\u0bb0\u0bc1\u0b9f\u0bcd\u0b9f\u0bb5\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-\u0baa\u0b95\u0bcd\u0b95\u0bae\u0bcd\u0bae\u0bc7\u0bb2\u0bc7>\u0009\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bc8 \u0b92\u0bb0\u0bc1 \u0baa\u0b95\u0bcd\u0b95\u0bae\u0bcd \u0bae\u0bc7\u0bb2\u0bc7 \u0b89\u0bb0\u0bc1\u0b9f\u0bcd\u0b9f\u0bb5\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-\u0baa\u0b95\u0bcd\u0b95\u0bae\u0bcd\u0b95\u0bc0\u0bb4\u0bcd>\u0009\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bc8 \u0b92\u0bb0\u0bc1 \u0baa\u0b95\u0bcd\u0b95\u0bae\u0bcd \u0b95\u0bc0\u0bb4\u0bc7 \u0b89\u0bb0\u0bc1\u0b9f\u0bcd\u0b9f\u0bb5\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<\u0b89\u0baf\u0bb0\u0bcd\u0ba4\u0bcd\u0ba4\u0bc1-\u0bae\u0bc7\u0bb2\u0bc7>\u0009\u0baa\u0bbf\u0ba9\u0bcd\u0ba9\u0bcb\u0b95\u0bcd\u0b95\u0bbf \u0b95\u0ba3\u0bcd\u0b9f\u0bc1\u0baa\u0bbf\u0b9f\u0bbf (\u0bae\u0bc7\u0bb2\u0bcd\u0ba8\u0bcb\u0b95\u0bcd\u0b95\u0bbf, \u0baa\u0bbf\u0ba9\u0bcd\u0ba9\u0bb0\u0bcd \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0b95\u0bb3\u0bcd)"
::msgcat::mcset ta "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<\u0b89\u0baf\u0bb0\u0bcd\u0ba4\u0bcd\u0ba4\u0bc1-\u0b95\u0bc0\u0bb4\u0bc7>\u0009\u0bae\u0bc1\u0ba9\u0bcd\u0ba9\u0bcb\u0b95\u0bcd\u0b95\u0bc1\u0b95\u0bb3\u0bc8\u0b95\u0bcd \u0b95\u0ba3\u0bcd\u0b9f\u0bb1\u0bbf\u0baf\u0bb5\u0bc1\u0bae\u0bcd (\u0b95\u0bc0\u0bb4\u0bcd\u0ba8\u0bcb\u0b95\u0bcd\u0b95\u0bbf, \u0bae\u0bc1\u0ba8\u0bcd\u0ba4\u0bc8\u0baf \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0b95\u0bb3\u0bcd)"
::msgcat::mcset ta "<Delete>, b\u0009Scroll diff view up one page" "<\u0ba8\u0bc0\u0b95\u0bcd\u0b95\u0bc1>, b\u0009\u0b9a\u0bc1\u0bb0\u0bc1\u0bb3\u0bcd \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 \u0b92\u0bb0\u0bc1 \u0baa\u0b95\u0bcd\u0b95\u0ba4\u0bcd\u0ba4\u0bc8 \u0bae\u0bc7\u0bb2\u0bc7 \u0b95\u0bbe\u0ba3\u0bcd\u0b95"
::msgcat::mcset ta "<Backspace>\u0009Scroll diff view up one page" "<\u0baa\u0bbf\u0ba9\u0bcd\u0bb5\u0bc6\u0bb3\u0bbf>\u0009\u0b9a\u0bc1\u0bb0\u0bc1\u0bb3\u0bcd \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 \u0b92\u0bb0\u0bc1 \u0baa\u0b95\u0bcd\u0b95\u0ba4\u0bcd\u0ba4\u0bc8 \u0bae\u0bc7\u0bb2\u0bc7 \u0b95\u0bbe\u0ba3\u0bcd\u0b95"
::msgcat::mcset ta "<Space>\u0009\u0009Scroll diff view down one page" "<Space>\u0009\u0009\u0b9a\u0bc1\u0bb0\u0bc1\u0bb3\u0bcd \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 \u0b92\u0bb0\u0bc1 \u0baa\u0b95\u0bcd\u0b95\u0ba4\u0bcd\u0ba4\u0bc8\u0b95\u0bcd \u0b95\u0bc0\u0bb4\u0bc7 \u0b95\u0bbe\u0ba3\u0bcd\u0b95"
::msgcat::mcset ta "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u0009\u0b9a\u0bc1\u0bb0\u0bc1\u0bb3\u0bcd \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 18 \u0bb5\u0bb0\u0bbf\u0b95\u0bb3\u0bc8 \u0bae\u0bc7\u0bb2\u0bc7 \u0b95\u0bbe\u0ba3\u0bcd\u0b95"
::msgcat::mcset ta "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u0009\u0b9a\u0bc1\u0bb0\u0bc1\u0bb3\u0bcd \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 18 \u0bb5\u0bb0\u0bbf\u0b95\u0bb3\u0bc8\u0b95\u0bcd \u0b95\u0bc0\u0bb4\u0bc7 \u0b95\u0bbe\u0ba3\u0bcd\u0b95"
::msgcat::mcset ta "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009\u0b95\u0ba3\u0bcd\u0b9f\u0bc1\u0baa\u0bbf\u0b9f\u0bbf"
::msgcat::mcset ta "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009\u0b85\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4 \u0b95\u0ba3\u0bcd\u0b9f\u0bc1\u0baa\u0bbf\u0b9f\u0bbf\u0baa\u0bcd\u0baa\u0bc1 \u0bb5\u0bc6\u0bb1\u0bcd\u0bb1\u0bbf\u0b95\u0bcd\u0b95\u0bc1 \u0b9a\u0bc6\u0bb2\u0bcd"
::msgcat::mcset ta "<Return>\u0009Move to next find hit" "<\u0ba4\u0bbf\u0bb0\u0bc1\u0bae\u0bcd\u0baa\u0bc1>\u0009\u0b85\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0ba4\u0bc8\u0b95\u0bcd \u0b95\u0ba3\u0bcd\u0b9f\u0bc1\u0baa\u0bbf\u0b9f\u0bbf"
::msgcat::mcset ta "g\u0009\u0009Go to commit" "g\u0009\u0009\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0b95\u0bcd\u0b95\u0bc1 \u0b9a\u0bc6\u0bb2\u0bcd"
::msgcat::mcset ta "/\u0009\u0009Focus the search box" "/\u0009\u0009\u0ba4\u0bc7\u0b9f\u0bb2\u0bcd \u0baa\u0bc6\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bbf\u0bb2\u0bcd \u0b95\u0bb5\u0ba9\u0bae\u0bcd \u0b9a\u0bc6\u0bb2\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "?\u0009\u0009Move to previous find hit" "?\u0009\u0009\u0bae\u0bc1\u0ba8\u0bcd\u0ba4\u0bc8\u0baf \u0b95\u0ba3\u0bcd\u0b9f\u0bc1\u0baa\u0bbf\u0b9f\u0bbf\u0baa\u0bcd\u0baa\u0bc1 \u0bb5\u0bc6\u0bb1\u0bcd\u0bb1\u0bbf\u0b95\u0bcd\u0b95\u0bc1 \u0b9a\u0bc6\u0bb2\u0bcd"
::msgcat::mcset ta "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009\u0b85\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4 \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bbf\u0bb1\u0bcd\u0b95\u0bc1 \u0b89\u0bb0\u0bc1\u0bb3\u0bcd \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 \u0baa\u0bbe\u0bb0\u0bcd\u0bb5\u0bc8"
::msgcat::mcset ta "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009\u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 \u0baa\u0bbe\u0bb0\u0bcd\u0bb5\u0bc8\u0baf\u0bbf\u0bb2\u0bcd \u0b85\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4 \u0bb5\u0bc6\u0bb1\u0bcd\u0bb1\u0bbf\u0baf\u0bc8\u0ba4\u0bcd \u0ba4\u0bc7\u0b9f\u0bc1\u0b99\u0bcd\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-r> \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0b9f\u0bcd\u0b9f \u0baa\u0bbe\u0bb0\u0bcd\u0bb5\u0bc8\u0baf\u0bbf\u0bb2\u0bcd \u0bae\u0bc1\u0ba8\u0bcd\u0ba4\u0bc8\u0baf \u0bb5\u0bc6\u0bb1\u0bcd\u0bb1\u0bbf\u0baf\u0bc8\u0ba4\u0bcd \u0ba4\u0bc7\u0b9f\u0bc1\u0b99\u0bcd\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "<%s-KP+>\u0009Increase font size" "<%s-KP+>\u0009\u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1 \u0b85\u0bb3\u0bb5\u0bc8 \u0b85\u0ba4\u0bbf\u0b95\u0bb0\u0bbf"
::msgcat::mcset ta "<%s-plus>\u0009Increase font size" "<%s-plus>\u0009\u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1 \u0b85\u0bb3\u0bb5\u0bc8 \u0b85\u0ba4\u0bbf\u0b95\u0bb0\u0bbf"
::msgcat::mcset ta "<%s-KP->\u0009Decrease font size" "<%s-KP->\u0009\u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1 \u0b85\u0bb3\u0bb5\u0bc8\u0b95\u0bcd \u0b95\u0bc1\u0bb1\u0bc8"
::msgcat::mcset ta "<%s-minus>\u0009Decrease font size" "<%s-minus>\u0009\u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1 \u0b85\u0bb3\u0bb5\u0bc8\u0b95\u0bcd \u0b95\u0bc1\u0bb1\u0bc8"
::msgcat::mcset ta "<F5>\u0009\u0009Update" "<F5>\u0009\u0009\u0baa\u0bc1\u0ba4\u0bc1\u0baa\u0bcd\u0baa\u0bbf\u0baa\u0bcd\u0baa\u0bc1"
::msgcat::mcset ta "Error creating temporary directory %s:" "\u0ba4\u0bb1\u0bcd\u0b95\u0bbe\u0bb2\u0bbf\u0b95 \u0b85\u0b9f\u0bc8\u0bb5\u0bc1 %s \u0b90 \u0b89\u0bb0\u0bc1\u0bb5\u0bbe\u0b95\u0bcd\u0b95\u0bc1\u0bb5\u0ba4\u0bc1 \u0baa\u0bbf\u0bb4\u0bc8:"
::msgcat::mcset ta "Error getting \"%s\" from %s:" "%s \u0b87\u0bb2\u0bbf\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1 \" %s\" \u0baa\u0bc6\u0bb1\u0bc1\u0bb5\u0ba4\u0bc1 \u0baa\u0bbf\u0bb4\u0bc8:"
::msgcat::mcset ta "command failed:" "\u0b95\u0b9f\u0bcd\u0b9f\u0bb3\u0bc8 \u0ba4\u0bcb\u0bb2\u0bcd\u0bb5\u0bbf\u0baf\u0bc1\u0bb1\u0bcd\u0bb1\u0ba4\u0bc1:"
::msgcat::mcset ta "No such commit" "\u0b85\u0ba4\u0bcd\u0ba4\u0b95\u0bc8\u0baf \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b87\u0bb2\u0bcd\u0bb2\u0bc8"
::msgcat::mcset ta "git gui blame: command failed:" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf \u0b87\u0b9f\u0bc8\u0bae\u0bc1\u0b95 \u0baa\u0bb4\u0bbf: \u0b95\u0b9f\u0bcd\u0b9f\u0bb3\u0bc8 \u0ba4\u0bcb\u0bb2\u0bcd\u0bb5\u0bbf\u0baf\u0bc1\u0bb1\u0bcd\u0bb1\u0ba4\u0bc1:"
::msgcat::mcset ta "Couldn't read merge head: %s" "\u0b92\u0ba9\u0bcd\u0bb1\u0bbf\u0ba3\u0bc8\u0baa\u0bcd\u0baa\u0bc1 \u0ba4\u0bb2\u0bc8\u0baf\u0bc8\u0baa\u0bcd \u0baa\u0b9f\u0bbf\u0b95\u0bcd\u0b95 \u0bae\u0bc1\u0b9f\u0bbf\u0baf\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8: %s"
::msgcat::mcset ta "Error reading index: %s" "\u0baa\u0bbf\u0bb4\u0bc8 \u0bb5\u0bbe\u0b9a\u0bbf\u0baa\u0bcd\u0baa\u0bc1 \u0b95\u0bc1\u0bb1\u0bbf\u0baf\u0bc0\u0b9f\u0bc1: %s"
::msgcat::mcset ta "Couldn't start git blame: %s" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf \u0baa\u0bb4\u0bbf\u0baf\u0bc8\u0ba4\u0bcd \u0ba4\u0bca\u0b9f\u0b99\u0bcd\u0b95 \u0bae\u0bc1\u0b9f\u0bbf\u0baf\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8: %s"
::msgcat::mcset ta "Searching" "\u0ba4\u0bc7\u0b9f\u0bc1\u0b95\u0bbf\u0bb1\u0ba4\u0bc1"
::msgcat::mcset ta "Error running git blame: %s" "\u0baa\u0bbf\u0bb4\u0bc8 \u0b87\u0baf\u0b99\u0bcd\u0b95\u0bc1\u0bae\u0bcd \u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf \u0baa\u0bb4\u0bbf: %s"
::msgcat::mcset ta "That line comes from commit %s,  which is not in this view" "\u0b85\u0ba8\u0bcd\u0ba4 \u0bb5\u0bb0\u0bbf \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf %s \u0b8e\u0ba9\u0bcd\u0baa\u0ba4\u0bbf\u0bb2\u0bbf\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1 \u0bb5\u0bb0\u0bc1\u0b95\u0bbf\u0bb1\u0ba4\u0bc1, \u0b87\u0ba4\u0bc1 \u0b87\u0ba8\u0bcd\u0ba4 \u0baa\u0bbe\u0bb0\u0bcd\u0bb5\u0bc8\u0baf\u0bbf\u0bb2\u0bcd \u0b87\u0bb2\u0bcd\u0bb2\u0bc8"
::msgcat::mcset ta "External diff viewer failed:" "\u0bb5\u0bc6\u0bb3\u0bbf\u0baa\u0bcd\u0baa\u0bc1\u0bb1 \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 \u0baa\u0bbe\u0bb0\u0bcd\u0bb5\u0bc8\u0baf\u0bbe\u0bb3\u0bb0\u0bcd \u0ba4\u0bcb\u0bb2\u0bcd\u0bb5\u0bbf\u0baf\u0bc1\u0bb1\u0bcd\u0bb1\u0ba4\u0bc1:"
::msgcat::mcset ta "All files" "\u0b85\u0ba9\u0bc8\u0ba4\u0bcd\u0ba4\u0bc1 \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "View" "\u0b95\u0bbe\u0ba3\u0bcd\u0b95"
::msgcat::mcset ta "Gitk view definition" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf\u0b95\u0bc7 \u0baa\u0bbe\u0bb0\u0bcd\u0bb5\u0bc8 \u0bb5\u0bb0\u0bc8\u0baf\u0bb1\u0bc8"
::msgcat::mcset ta "Remember this view" "\u0b87\u0ba8\u0bcd\u0ba4 \u0baa\u0bbe\u0bb0\u0bcd\u0bb5\u0bc8\u0baf\u0bc8 \u0ba8\u0bbf\u0ba9\u0bc8\u0bb5\u0bbf\u0bb2\u0bcd \u0b95\u0bca\u0bb3\u0bcd\u0bb3\u0bc1\u0b99\u0bcd\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "References (space separated list):" "\u0b95\u0bc1\u0bb1\u0bbf\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bcd (\u0b87\u0b9f\u0bc8\u0bb5\u0bc6\u0bb3\u0bbf \u0baa\u0bbf\u0bb0\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bcd):"
::msgcat::mcset ta "Branches & tags:" "\u0b95\u0bbf\u0bb3\u0bc8\u0b95\u0bb3\u0bcd \u0bae\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd \u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb1\u0bcd\u0b95\u0bb3\u0bcd:"
::msgcat::mcset ta "All refs" "\u0b85\u0ba9\u0bc8\u0ba4\u0bcd\u0ba4\u0bc1 \u0b95\u0bc1\u0bb1\u0bbf\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "All (local) branches" "\u0b85\u0ba9\u0bc8\u0ba4\u0bcd\u0ba4\u0bc1 (\u0b89\u0bb3\u0bcd\u0bb3\u0b95) \u0b95\u0bbf\u0bb3\u0bc8\u0b95\u0bb3\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "All tags" "\u0b85\u0ba9\u0bc8\u0ba4\u0bcd\u0ba4\u0bc1 \u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb1\u0bcd\u0b95\u0bb3\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "All remote-tracking branches" "\u0b85\u0ba9\u0bc8\u0ba4\u0bcd\u0ba4\u0bc1 \u0ba4\u0bca\u0bb2\u0bc8-\u0b95\u0ba3\u0bcd\u0b95\u0bbe\u0ba3\u0bbf\u0baa\u0bcd\u0baa\u0bc1 \u0b95\u0bbf\u0bb3\u0bc8\u0b95\u0bb3\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "Commit Info (regular expressions):" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bbf (\u0bb5\u0bb4\u0b95\u0bcd\u0b95\u0bae\u0bbe\u0ba9 \u0bb5\u0bc6\u0bb3\u0bbf\u0baa\u0bcd\u0baa\u0bbe\u0b9f\u0bc1\u0b95\u0bb3\u0bcd):"
::msgcat::mcset ta "Author:" "\u0b86\u0b9a\u0bbf\u0bb0\u0bbf\u0baf\u0bb0\u0bcd:"
::msgcat::mcset ta "Committer:" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0baa\u0bb5\u0bb0\u0bcd:"
::msgcat::mcset ta "Commit Message:" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bbf:"
::msgcat::mcset ta "Matches all Commit Info criteria" "\u0b85\u0ba9\u0bc8\u0ba4\u0bcd\u0ba4\u0bc1 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bbf \u0b85\u0bb3\u0bb5\u0bc1\u0b95\u0bcb\u0bb2\u0bcd\u0b95\u0bb3\u0bc8\u0baf\u0bc1\u0bae\u0bcd \u0baa\u0bca\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0b95\u0bbf\u0bb1\u0ba4\u0bc1"
::msgcat::mcset ta "Matches no Commit Info criteria" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bbf \u0b85\u0bb3\u0bb5\u0bc1\u0b95\u0bcb\u0bb2\u0bcd\u0b95\u0bb3\u0bc1\u0b9f\u0ba9\u0bcd \u0baa\u0bca\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8"
::msgcat::mcset ta "Changes to Files:" "\u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bbf\u0bb2\u0bcd \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bcd:"
::msgcat::mcset ta "Fixed String" "\u0ba8\u0bbf\u0bb2\u0bc8\u0baf\u0bbe\u0ba9 \u0b9a\u0bb0\u0bae\u0bcd"
::msgcat::mcset ta "Regular Expression" "\u0bb5\u0bb4\u0b95\u0bcd\u0b95\u0bae\u0bbe\u0ba9 \u0bb5\u0bc6\u0bb3\u0bbf\u0baa\u0bcd\u0baa\u0bbe\u0b9f\u0bc1"
::msgcat::mcset ta "Search string:" "\u0ba4\u0bc7\u0b9f\u0bb2\u0bcd \u0b9a\u0bb0\u0bae\u0bcd:"
::msgcat::mcset ta "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0ba4\u0bc7\u0ba4\u0bbf\u0b95\u0bb3\u0bcd (\"2 \u0bb5\u0bbe\u0bb0\u0b99\u0bcd\u0b95\u0bb3\u0bc1\u0b95\u0bcd\u0b95\u0bc1 \u0bae\u0bc1\u0ba9\u0bcd\u0baa\u0bc1\", \"2009-01-16 15:27:38\", \"\u0bae\u0bbe\u0bb0\u0bcd\u0b9a\u0bcd 17, 2009 15:27:38\"):"
::msgcat::mcset ta "Since:" "\u0baa\u0bbf\u0ba9\u0bcd\u0ba9\u0bb0\u0bcd:"
::msgcat::mcset ta "Until:" "\u0bb5\u0bb0\u0bc8:"
::msgcat::mcset ta "Limit and/or skip a number of revisions (positive integer):" "\u0baa\u0bb2 \u0ba4\u0bbf\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0b99\u0bcd\u0b95\u0bb3\u0bc8 (\u0ba8\u0bc7\u0bb0\u0bcd\u0bae\u0bb1\u0bc8 \u0bae\u0bc1\u0bb4\u0bc1 \u0b8e\u0ba3\u0bcd) \u0b95\u0b9f\u0bcd\u0b9f\u0bc1\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1 \u0bae\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd/\u0b85\u0bb2\u0bcd\u0bb2\u0ba4\u0bc1 \u0ba4\u0bb5\u0bbf\u0bb0\u0bcd:"
::msgcat::mcset ta "Number to show:" "\u0b95\u0bbe\u0ba3\u0bcd\u0baa\u0bbf\u0b95\u0bcd\u0b95 \u0b8e\u0ba3\u0bcd:"
::msgcat::mcset ta "Number to skip:" "\u0ba4\u0bb5\u0bbf\u0bb0\u0bcd\u0b95\u0bcd\u0b95 \u0b8e\u0ba3\u0bcd:"
::msgcat::mcset ta "Miscellaneous options:" "\u0b87\u0ba4\u0bb0 \u0bb5\u0bbf\u0bb0\u0bc1\u0baa\u0bcd\u0baa\u0b99\u0bcd\u0b95\u0bb3\u0bcd:"
::msgcat::mcset ta "Strictly sort by date" "\u0b95\u0ba3\u0bcd\u0b9f\u0bbf\u0baa\u0bcd\u0baa\u0bbe\u0b95 \u0ba4\u0bc7\u0ba4\u0bbf\u0baf\u0bbf\u0ba9\u0bcd\u0baa\u0b9f\u0bbf \u0bb5\u0bb0\u0bbf\u0b9a\u0bc8\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "Mark branch sides" "\u0b95\u0bbf\u0bb3\u0bc8 \u0baa\u0b95\u0bcd\u0b95\u0b99\u0bcd\u0b95\u0bb3\u0bc8\u0b95\u0bcd \u0b95\u0bc1\u0bb1\u0bbf"
::msgcat::mcset ta "Limit to first parent" "\u0bae\u0bc1\u0ba4\u0bb2\u0bcd \u0baa\u0bc6\u0bb1\u0bcd\u0bb1\u0bcb\u0bb0\u0bc1\u0b95\u0bcd\u0b95\u0bc1 \u0bb5\u0bb0\u0bae\u0bcd\u0baa\u0bc1"
::msgcat::mcset ta "Simple history" "\u0b8e\u0bb3\u0bbf\u0baf \u0bb5\u0bb0\u0bb2\u0bbe\u0bb1\u0bc1"
::msgcat::mcset ta "Additional arguments to git log:" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf \u0baa\u0ba4\u0bbf\u0bb5\u0bc1\u0b95\u0bcd\u0b95\u0bc1 \u0b95\u0bc2\u0b9f\u0bc1\u0ba4\u0bb2\u0bcd \u0bb5\u0bbe\u0ba4\u0b99\u0bcd\u0b95\u0bb3\u0bcd:"
::msgcat::mcset ta "Enter files and directories to include, one per line:" "\u0b9a\u0bc7\u0bb0\u0bcd\u0b95\u0bcd\u0b95 \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bcd \u0bae\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0b95\u0b99\u0bcd\u0b95\u0bb3\u0bc8 \u0b89\u0bb3\u0bcd\u0bb3\u0bbf\u0b9f\u0bb5\u0bc1\u0bae\u0bcd, \u0b92\u0bb0\u0bc1 \u0bb5\u0bb0\u0bbf\u0b95\u0bcd\u0b95\u0bc1 \u0b92\u0ba9\u0bcd\u0bb1\u0bc1:"
::msgcat::mcset ta "Command to generate more commits to include:" "\u0b9a\u0bc7\u0bb0\u0bcd\u0b95\u0bcd\u0b95 \u0b95\u0bc2\u0b9f\u0bc1\u0ba4\u0bb2\u0bcd \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0b95\u0bb3\u0bc8 \u0b89\u0bb0\u0bc1\u0bb5\u0bbe\u0b95\u0bcd\u0b95 \u0b95\u0b9f\u0bcd\u0b9f\u0bb3\u0bc8:"
::msgcat::mcset ta "Gitk: edit view" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf\u0b95\u0bc7: \u0ba4\u0bbf\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1 \u0baa\u0bbe\u0bb0\u0bcd\u0bb5\u0bc8"
::msgcat::mcset ta "-- criteria for selecting revisions" "-- \u0ba4\u0bbf\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0b99\u0bcd\u0b95\u0bb3\u0bc8\u0ba4\u0bcd \u0ba4\u0bc7\u0bb0\u0bcd\u0ba8\u0bcd\u0ba4\u0bc6\u0b9f\u0bc1\u0baa\u0bcd\u0baa\u0ba4\u0bb1\u0bcd\u0b95\u0bbe\u0ba9 \u0b85\u0bb3\u0bb5\u0bc1\u0b95\u0bcb\u0bb2\u0bcd\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "View Name" "\u0baa\u0bc6\u0baf\u0bb0\u0bc8\u0b95\u0bcd \u0b95\u0bbe\u0ba3\u0bcd\u0b95"
::msgcat::mcset ta "Apply (F5)" "\u0b87\u0b9f\u0bc1 (F5)"
::msgcat::mcset ta "Error in commit selection arguments:" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0ba4\u0bc7\u0bb0\u0bcd\u0bb5\u0bc1 \u0bb5\u0bbe\u0ba4\u0b99\u0bcd\u0b95\u0bb3\u0bbf\u0bb2\u0bcd \u0baa\u0bbf\u0bb4\u0bc8:"
::msgcat::mcset ta "None" "\u0b8e\u0ba4\u0bc1\u0bb5\u0bc1\u0bae\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8"
::msgcat::mcset ta "Descendant" "\u0bb5\u0bb4\u0bbf\u0ba4\u0bcd\u0ba4\u0bcb\u0ba9\u0bcd\u0bb1\u0bb2\u0bcd"
::msgcat::mcset ta "Not descendant" "\u0bb5\u0bb4\u0bbf\u0ba4\u0bcd\u0ba4\u0bcb\u0ba9\u0bcd\u0bb1\u0bb2\u0bcd \u0b85\u0bb2\u0bcd\u0bb2"
::msgcat::mcset ta "Ancestor" "\u0bae\u0bc2\u0ba4\u0bbe\u0ba4\u0bc8\u0baf\u0bb0\u0bcd"
::msgcat::mcset ta "Not ancestor" "\u0bae\u0bc2\u0ba4\u0bbe\u0ba4\u0bc8\u0baf\u0bb0\u0bcd \u0b85\u0bb2\u0bcd\u0bb2"
::msgcat::mcset ta "Local changes checked in to index but not committed" "\u0b89\u0bb3\u0bcd\u0bb3\u0b95 \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b95\u0bc1\u0bb1\u0bbf\u0baf\u0bc0\u0b9f\u0bcd\u0b9f\u0bbf\u0bb2\u0bcd \u0b9a\u0bb0\u0bbf\u0baa\u0bbe\u0bb0\u0bcd\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f\u0ba9, \u0b86\u0ba9\u0bbe\u0bb2\u0bcd \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0baf\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8"
::msgcat::mcset ta "Local uncommitted changes, not checked in to index" "\u0b89\u0bb3\u0bcd\u0bb3\u0b95 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0baf\u0bbe\u0ba4 \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bcd, \u0b95\u0bc1\u0bb1\u0bbf\u0baf\u0bc0\u0b9f\u0bcd\u0b9f\u0bbf\u0bb2\u0bcd \u0b9a\u0bb0\u0bbf\u0baa\u0bbe\u0bb0\u0bcd\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8"
::msgcat::mcset ta "Error starting web browser:" "\u0bb5\u0bb2\u0bc8 \u0b89\u0bb2\u0bbe\u0bb5\u0bbf\u0baf\u0bc8\u0ba4\u0bcd \u0ba4\u0bca\u0b9f\u0b99\u0bcd\u0b95\u0bc1\u0bb5\u0ba4\u0bbf\u0bb2\u0bcd \u0baa\u0bbf\u0bb4\u0bc8:"
::msgcat::mcset ta "and many more" "\u0bae\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd \u0bae\u0bc7\u0bb2\u0bc1\u0bae\u0bcd \u0baa\u0bb2"
::msgcat::mcset ta "many" "\u0baa\u0bb2"
::msgcat::mcset ta "Tags:" "\u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb1\u0bcd\u0b95\u0bb3\u0bcd:"
::msgcat::mcset ta "Parent" "\u0baa\u0bc6\u0bb1\u0bcd\u0bb1\u0bcb\u0bb0\u0bcd"
::msgcat::mcset ta "Child" "\u0b95\u0bc1\u0bb4\u0ba8\u0bcd\u0ba4\u0bc8"
::msgcat::mcset ta "Branch" "\u0b95\u0bbf\u0bb3\u0bc8"
::msgcat::mcset ta "Follows" "\u0baa\u0bbf\u0ba9\u0bcd\u0bb5\u0bb0\u0bc1\u0bae\u0bbe\u0bb1\u0bc1"
::msgcat::mcset ta "Precedes" "\u0bae\u0bc1\u0ba9\u0bcd\u0ba9\u0bbe\u0bb2\u0bcd"
::msgcat::mcset ta "Error getting diffs: %s" "\u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 \u0baa\u0bc6\u0bb1\u0bc1\u0bb5\u0ba4\u0bbf\u0bb2\u0bcd \u0baa\u0bbf\u0bb4\u0bc8: %s"
::msgcat::mcset ta "Goto:" "\u0b9a\u0bc6\u0bb2\u0bcd:"
::msgcat::mcset ta "Short commit ID %s is ambiguous" "\u0b95\u0bc1\u0bb1\u0bc1\u0b95\u0bbf\u0baf \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b85\u0b9f\u0bc8\u0baf\u0bbe\u0bb3\u0bae\u0bcd %s \u0ba4\u0bc6\u0bb3\u0bbf\u0bb5\u0bb1\u0bcd\u0bb1\u0bb5\u0bc8"
::msgcat::mcset ta "Revision %s is not known" "\u0ba4\u0bbf\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0bae\u0bcd %s \u0ba4\u0bc6\u0bb0\u0bbf\u0baf\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8"
::msgcat::mcset ta "Commit ID %s is not known" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b85\u0b9f\u0bc8\u0baf\u0bbe\u0bb3\u0bae\u0bcd %s \u0b85\u0bb1\u0bbf\u0baf\u0baa\u0bcd\u0baa\u0b9f\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8"
::msgcat::mcset ta "Revision %s is not in the current view" "\u0ba4\u0bbf\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0bae\u0bcd %s \u0ba4\u0bb1\u0bcd\u0baa\u0bcb\u0ba4\u0bc8\u0baf \u0baa\u0bbe\u0bb0\u0bcd\u0bb5\u0bc8\u0baf\u0bbf\u0bb2\u0bcd \u0b87\u0bb2\u0bcd\u0bb2\u0bc8"
::msgcat::mcset ta "Date" "\u0ba4\u0bbf\u0b95\u0ba4\u0bbf"
::msgcat::mcset ta "Children" "\u0b95\u0bc1\u0bb4\u0ba8\u0bcd\u0ba4\u0bc8\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Reset %s branch to here" "%s \u0b95\u0bbf\u0bb3\u0bc8\u0baf\u0bc8 \u0b87\u0b99\u0bcd\u0b95\u0bc7 \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8"
::msgcat::mcset ta "Detached head: can't reset" "\u0baa\u0bbf\u0bb0\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0ba4\u0bb2\u0bc8: \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8\u0b95\u0bcd\u0b95 \u0bae\u0bc1\u0b9f\u0bbf\u0baf\u0bbe\u0ba4\u0bc1"
::msgcat::mcset ta "Skipping merge commit " "\u0b92\u0ba9\u0bcd\u0bb1\u0bbf\u0ba3\u0bc8 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0baf\u0bc8 \u0ba4\u0bb5\u0bb0\u0bcd\u0b95\u0bbf\u0bb1\u0ba4\u0bc1 "
::msgcat::mcset ta "Error getting patch ID for " "\u0b92\u0b9f\u0bcd\u0b9f\u0bc1 \u0b85\u0b9f\u0bc8\u0baf\u0bbe\u0bb3\u0ba4\u0bcd\u0ba4\u0bc8\u0baa\u0bcd \u0baa\u0bc6\u0bb1\u0bc1\u0bb5\u0ba4\u0bbf\u0bb2\u0bcd \u0baa\u0bbf\u0bb4\u0bc8"
::msgcat::mcset ta " - stopping\n" "- \u0ba8\u0bbf\u0bb1\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0ba4\u0bb2\u0bcd\n"
::msgcat::mcset ta "Commit " "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf"
::msgcat::mcset ta " is the same patch as\n       " "\u0b85\u0ba4\u0bc7 \u0b92\u0b9f\u0bcd\u0b9f\u0bc1\n       "
::msgcat::mcset ta " differs from\n       " "\u0b87\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1 \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0b9f\u0bc1\u0b95\u0bbf\u0bb1\u0ba4\u0bc1\n       "
::msgcat::mcset ta "Diff of commits:\n\n" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0baf\u0bbf\u0ba9\u0bcd \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1:\n\n"
::msgcat::mcset ta " has %s children - stopping\n" "%s \u0b95\u0bc1\u0bb4\u0ba8\u0bcd\u0ba4\u0bc8\u0b95\u0bb3\u0bcd \u0b89\u0bb3\u0bcd\u0bb3\u0ba9\u0bb0\u0bcd - \u0ba8\u0bbf\u0bb1\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0ba4\u0bb2\u0bcd\n"
::msgcat::mcset ta "Error writing commit to file: %s" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bbf\u0bb2\u0bcd \u0b8e\u0bb4\u0bc1\u0ba4\u0bc1\u0ba4\u0bb2\u0bcd  \u0baa\u0bbf\u0bb4\u0bc8: %s"
::msgcat::mcset ta "Error diffing commits: %s" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0b95\u0bb3\u0bcd \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 \u0baa\u0bbf\u0bb4\u0bc8: %s"
::msgcat::mcset ta "Top" "\u0bae\u0bc7\u0bb2\u0bc7"
::msgcat::mcset ta "From" "\u0b87\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "To" "\u0baa\u0bc6\u0bb1\u0bc1\u0ba8\u0bb0\u0bcd"
::msgcat::mcset ta "Generate patch" "\u0b92\u0b9f\u0bcd\u0b9f\u0bc8 \u0b89\u0bb0\u0bc1\u0bb5\u0bbe\u0b95\u0bcd\u0b95\u0bc1"
::msgcat::mcset ta "From:" "\u0b87\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1:"
::msgcat::mcset ta "To:" "\u0b87\u0ba4\u0bb1\u0bcd\u0b95\u0bc1:"
::msgcat::mcset ta "Reverse" "\u0ba4\u0bb2\u0bc8\u0b95\u0bc0\u0bb4\u0bcd"
::msgcat::mcset ta "Output file:" "\u0bb5\u0bc6\u0bb3\u0bbf\u0baf\u0bc0\u0b9f\u0bcd\u0b9f\u0bc1 \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1:"
::msgcat::mcset ta "Generate" "\u0b89\u0bb0\u0bc1\u0bb5\u0bbe\u0b95\u0bcd\u0b95\u0bc1"
::msgcat::mcset ta "Error creating patch:" "\u0b92\u0b9f\u0bcd\u0b9f\u0bc8 \u0b89\u0bb0\u0bc1\u0bb5\u0bbe\u0b95\u0bcd\u0b95\u0bc1 \u0baa\u0bbf\u0bb4\u0bc8:"
::msgcat::mcset ta "ID:" "\u0b85\u0b9f\u0bc8\u0baf\u0bbe\u0bb3\u0bae\u0bcd:"
::msgcat::mcset ta "Tag name:" "\u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb2\u0bcd \u0baa\u0bc6\u0baf\u0bb0\u0bcd:"
::msgcat::mcset ta "Tag message is optional" "\u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb2\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bbf \u0bb5\u0bbf\u0bb0\u0bc1\u0baa\u0bcd\u0baa\u0bae\u0bbe\u0ba9\u0ba4\u0bc1"
::msgcat::mcset ta "Tag message:" "\u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb2\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bbf:"
::msgcat::mcset ta "Create" "\u0b89\u0bb0\u0bc1\u0bb5\u0bbe\u0b95\u0bcd\u0b95\u0bc1"
::msgcat::mcset ta "No tag name specified" "\u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb2\u0bcd \u0baa\u0bc6\u0baf\u0bb0\u0bcd \u0b95\u0bc1\u0bb1\u0bbf\u0baa\u0bcd\u0baa\u0bbf\u0b9f\u0baa\u0bcd\u0baa\u0b9f\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8"
::msgcat::mcset ta "Tag \"%s\" already exists" "\u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb2\u0bcd \"%s\" \u0b8f\u0bb1\u0bcd\u0b95\u0ba9\u0bb5\u0bc7 \u0b89\u0bb3\u0bcd\u0bb3\u0ba4\u0bc1"
::msgcat::mcset ta "Error creating tag:" "\u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb2\u0bcd\u0bb2\u0bc8 \u0b89\u0bb0\u0bc1\u0bb5\u0bbe\u0b95\u0bcd\u0b95\u0bc1 \u0baa\u0bbf\u0bb4\u0bc8:"
::msgcat::mcset ta "Command:" "\u0b95\u0b9f\u0bcd\u0b9f\u0bb3\u0bc8:"
::msgcat::mcset ta "Write" "\u0b8e\u0bb4\u0bc1\u0ba4\u0bc1"
::msgcat::mcset ta "Error writing commit:" "\u0baa\u0bbf\u0bb4\u0bc8 \u0b8e\u0bb4\u0bc1\u0ba4\u0bc1\u0ba4\u0bb2\u0bcd \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf:"
::msgcat::mcset ta "Create branch" "\u0b95\u0bbf\u0bb3\u0bc8\u0baf\u0bc8 \u0b89\u0bb0\u0bc1\u0bb5\u0bbe\u0b95\u0bcd\u0b95\u0bc1"
::msgcat::mcset ta "Rename branch %s" "%s \u0b95\u0bbf\u0bb3\u0bc8\u0baf\u0bc8 \u0bae\u0bb1\u0bc1\u0baa\u0bc6\u0baf\u0bb0\u0bbf\u0b9f\u0bc1"
::msgcat::mcset ta "Rename" "\u0bae\u0bb1\u0bc1\u0baa\u0bc6\u0baf\u0bb0\u0bbf\u0b9f\u0bc1"
::msgcat::mcset ta "Name:" "\u0baa\u0bc6\u0baf\u0bb0\u0bcd:"
::msgcat::mcset ta "Please specify a name for the new branch" "\u0baa\u0bc1\u0ba4\u0bbf\u0baf \u0b95\u0bbf\u0bb3\u0bc8\u0b95\u0bcd\u0b95\u0bc1 \u0b92\u0bb0\u0bc1 \u0baa\u0bc6\u0baf\u0bb0\u0bc8\u0b95\u0bcd \u0b95\u0bc1\u0bb1\u0bbf\u0baa\u0bcd\u0baa\u0bbf\u0b9f\u0bc1"
::msgcat::mcset ta "Branch '%s' already exists. Overwrite?" "\u0b95\u0bbf\u0bb3\u0bc8 '%s' \u0b8f\u0bb1\u0bcd\u0b95\u0ba9\u0bb5\u0bc7 \u0b89\u0bb3\u0bcd\u0bb3\u0ba4\u0bc1. \u0bae\u0bc7\u0bb2\u0bc6\u0bb4\u0bc1\u0ba4\u0bb5\u0bbe?"
::msgcat::mcset ta "Please specify a new name for the branch" "\u0b95\u0bbf\u0bb3\u0bc8\u0b95\u0bcd\u0b95\u0bc1 \u0b92\u0bb0\u0bc1 \u0baa\u0bc1\u0ba4\u0bbf\u0baf \u0baa\u0bc6\u0baf\u0bb0\u0bc8\u0b95\u0bcd \u0b95\u0bc1\u0bb1\u0bbf\u0baa\u0bcd\u0baa\u0bbf\u0b9f\u0bc1"
::msgcat::mcset ta "Commit %s is already included in branch %s -- really re-apply it?" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf %s \u0b8f\u0bb1\u0bcd\u0b95\u0ba9\u0bb5\u0bc7 \u0b95\u0bbf\u0bb3\u0bc8 %s \u0b9a\u0bc7\u0bb0\u0bcd\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f\u0bc1\u0bb3\u0bcd\u0bb3\u0ba9-\u0b89\u0ba3\u0bcd\u0bae\u0bc8\u0baf\u0bbf\u0bb2\u0bcd \u0b85\u0ba4\u0bc8 \u0bae\u0bc0\u0ba3\u0bcd\u0b9f\u0bc1\u0bae\u0bcd \u0b87\u0b9f\u0bb5\u0bbe?"
::msgcat::mcset ta "Cherry-picking" "\u0b95\u0ba9\u0bbf \u0b8e\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "'%s' \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bbf\u0bb2\u0bcd \u0b89\u0bb3\u0bcd\u0bb3\u0b95 \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b95\u0bbe\u0bb0\u0ba3\u0bae\u0bbe\u0b95 \u0b95\u0ba9\u0bbf-\u0b8e\u0b9f\u0bc1 \u0ba4\u0bcb\u0bb2\u0bcd\u0bb5\u0bbf\u0baf\u0b9f\u0bc8\u0ba8\u0bcd\u0ba4\u0ba4\u0bc1. \n\u0ba4\u0baf\u0bb5\u0bc1\u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bc1 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bc8\u0b9a\u0bcd \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf, \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8 \u0b85\u0bb2\u0bcd\u0bb2\u0ba4\u0bc1 \u0b9a\u0bc7\u0bae\u0bbf \u0baa\u0bbf\u0bb1\u0b95\u0bc1 \u0bae\u0bc0\u0ba3\u0bcd\u0b9f\u0bc1\u0bae\u0bcd \u0bae\u0bc1\u0baf\u0bb1\u0bcd\u0b9a\u0bbf."
::msgcat::mcset ta "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "\u0b92\u0ba9\u0bcd\u0bb1\u0bbf\u0ba3\u0bc8\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0bcd \u0bae\u0bcb\u0ba4\u0bb2\u0bcd \u0b95\u0bbe\u0bb0\u0ba3\u0bae\u0bbe\u0b95 \u0b95\u0ba9\u0bbf-\u0b8e\u0b9f\u0bc1 \u0ba4\u0bcb\u0bb2\u0bcd\u0bb5\u0bbf\u0baf\u0b9f\u0bc8\u0ba8\u0bcd\u0ba4\u0ba4\u0bc1. \n\u0b85\u0ba4\u0bc8 \u0ba4\u0bc0\u0bb0\u0bcd\u0b95\u0bcd\u0b95 \u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf \u0b9a\u0bbf\u0b90\u0b95\u0bb0\u0bc1\u0bb5\u0bbf\u0baf\u0bc8 \u0b87\u0baf\u0b95\u0bcd\u0b95 \u0bb5\u0bbf\u0bb0\u0bc1\u0bae\u0bcd\u0baa\u0bc1\u0b95\u0bbf\u0bb1\u0bc0\u0bb0\u0bcd\u0b95\u0bb3\u0bbe?"
::msgcat::mcset ta "No changes committed" "\u0b8e\u0ba8\u0bcd\u0ba4 \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bc1\u0bae\u0bcd \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0baf\u0baa\u0bcd\u0baa\u0b9f\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8"
::msgcat::mcset ta "Commit %s is not included in branch %s -- really revert it?" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf %s \u0b95\u0bbf\u0bb3\u0bc8 %s \u0b9a\u0bc7\u0bb0\u0bcd\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8 - \u0b89\u0ba3\u0bcd\u0bae\u0bc8\u0baf\u0bbf\u0bb2\u0bcd \u0b85\u0ba4\u0bc8 \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bc6\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0bb5\u0bbe?"
::msgcat::mcset ta "Reverting" "\u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bc6\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bb2\u0bcd"
::msgcat::mcset ta "Revert failed because of local changes to the following files:%s Please commit, reset or stash  your changes and try again." "\u0baa\u0bbf\u0ba9\u0bcd\u0bb5\u0bb0\u0bc1\u0bae\u0bcd \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bbf\u0bb2\u0bcd \u0b89\u0bb3\u0bcd\u0bb3\u0b95 \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b95\u0bbe\u0bb0\u0ba3\u0bae\u0bbe\u0b95 \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bc6\u0b9f\u0bc1 \u0ba4\u0bcb\u0bb2\u0bcd\u0bb5\u0bbf\u0baf\u0bc1\u0bb1\u0bcd\u0bb1\u0ba4\u0bc1:%s \u0ba4\u0baf\u0bb5\u0bc1\u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bc1 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bc8\u0b9a\u0bcd \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf, \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8 \u0b85\u0bb2\u0bcd\u0bb2\u0ba4\u0bc1 \u0b9a\u0bc7\u0bae\u0bbf \u0bae\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd \u0bae\u0bc0\u0ba3\u0bcd\u0b9f\u0bc1\u0bae\u0bcd \u0bae\u0bc1\u0baf\u0bb1\u0bcd\u0b9a\u0bbf."
::msgcat::mcset ta "Revert failed because of merge conflict.\n Do you wish to run git citool to resolve it?" "\u0b92\u0ba9\u0bcd\u0bb1\u0bbf\u0ba3\u0bc8\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0bcd \u0bae\u0bcb\u0ba4\u0bb2\u0bcd \u0b95\u0bbe\u0bb0\u0ba3\u0bae\u0bbe\u0b95 \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bc6\u0b9f\u0bc1 \u0ba4\u0bcb\u0bb2\u0bcd\u0bb5\u0bbf\u0baf\u0b9f\u0bc8\u0ba8\u0bcd\u0ba4\u0ba4\u0bc1. \n\u0b85\u0ba4\u0bc8 \u0ba4\u0bc0\u0bb0\u0bcd\u0b95\u0bcd\u0b95 \u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf \u0b9a\u0bbf\u0b90\u0b95\u0bb0\u0bc1\u0bb5\u0bbf\u0baf\u0bc8 \u0b87\u0baf\u0b95\u0bcd\u0b95 \u0bb5\u0bbf\u0bb0\u0bc1\u0bae\u0bcd\u0baa\u0bc1\u0b95\u0bbf\u0bb1\u0bc0\u0bb0\u0bcd\u0b95\u0bb3\u0bbe?"
::msgcat::mcset ta "Confirm reset" "\u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8\u0baa\u0bcd\u0baa\u0bc8 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "Reset branch %s to %s?" "%s \u0b95\u0bbf\u0bb3\u0bc8\u0baf\u0bc8 %s \u0b95\u0bcd\u0b95\u0bc1 \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8\u0b95\u0bcd\u0b95\u0bb5\u0bbe?"
::msgcat::mcset ta "Reset type:" "\u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8 \u0bb5\u0b95\u0bc8:"
::msgcat::mcset ta "Soft: Leave working tree and index untouched" "\u0bae\u0bc6\u0ba9\u0bcd\u0bae\u0bc8: \u0bb5\u0bc7\u0bb2\u0bc8 \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0bc1\u0bae\u0bcd \u0bae\u0bb0\u0bae\u0bcd \u0bae\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd \u0b95\u0bc1\u0bb1\u0bbf\u0baf\u0bc0\u0b9f\u0bcd\u0b9f\u0bc8\u0ba4\u0bcd \u0ba4\u0bc0\u0ba3\u0bcd\u0b9f\u0bbe\u0bae\u0bb2\u0bcd \u0bb5\u0bbf\u0b9f\u0bc1"
::msgcat::mcset ta "Mixed: Leave working tree untouched, reset index" "\u0b95\u0bb2\u0baa\u0bcd\u0baa\u0bc1: \u0bb5\u0bc7\u0bb2\u0bc8 \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0bc1\u0bae\u0bcd \u0bae\u0bb0\u0ba4\u0bcd\u0ba4\u0bc8 \u0ba4\u0bc0\u0ba3\u0bcd\u0b9f\u0bbe\u0bae\u0bb2\u0bcd \u0bb5\u0bbf\u0b9f\u0bc1, \u0b95\u0bc1\u0bb1\u0bbf\u0baf\u0bc0\u0b9f\u0bcd\u0b9f\u0bc8 \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8"
::msgcat::mcset ta "Hard: Reset working tree and index\n(discard ALL local changes)" "\u0b95\u0b9f\u0bbf\u0ba9\u0bae\u0bcd: \u0bb5\u0bc7\u0bb2\u0bc8 \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0bc1\u0bae\u0bcd \u0bae\u0bb0\u0bae\u0bcd \u0bae\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd \u0b95\u0bc1\u0bb1\u0bbf\u0baf\u0bc0\u0b9f\u0bcd\u0b9f\u0bc8 \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8 \n(\u0b85\u0ba9\u0bc8\u0ba4\u0bcd\u0ba4\u0bc1 \u0b89\u0bb3\u0bcd\u0bb3\u0b95 \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bc8\u0baf\u0bc1\u0bae\u0bcd \u0ba8\u0bbf\u0bb0\u0bbe\u0b95\u0bb0\u0bbf)"
::msgcat::mcset ta "Resetting" "\u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8\u0ba4\u0bcd\u0ba4\u0bb2\u0bcd"
::msgcat::mcset ta "A local branch named %s exists already" "%s \u0b8e\u0ba9\u0bcd\u0bb1 \u0b89\u0bb3\u0bcd\u0bb3\u0b95 \u0b95\u0bbf\u0bb3\u0bc8 \u0b8f\u0bb1\u0bcd\u0b95\u0ba9\u0bb5\u0bc7 \u0b89\u0bb3\u0bcd\u0bb3\u0ba4\u0bc1"
::msgcat::mcset ta "Checking out" "\u0b9a\u0bb0\u0bbf\u0baa\u0bbe\u0bb0\u0bcd"
::msgcat::mcset ta "Cannot delete the currently checked-out branch" "\u0ba4\u0bb1\u0bcd\u0baa\u0bcb\u0ba4\u0bc1 \u0b9a\u0bb0\u0bbf\u0baa\u0bbe\u0bb0\u0bcd\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0b95\u0bbf\u0bb3\u0bc8\u0baf\u0bc8 \u0ba8\u0bc0\u0b95\u0bcd\u0b95 \u0bae\u0bc1\u0b9f\u0bbf\u0baf\u0bbe\u0ba4\u0bc1"
::msgcat::mcset ta "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "\u0b95\u0bbf\u0bb3\u0bc8 %s \u0bae\u0bc0\u0ba4\u0bbe\u0ba9 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf\u0b95\u0bb3\u0bcd \u0bb5\u0bc7\u0bb1\u0bc1 \u0b8e\u0ba8\u0bcd\u0ba4 \u0b95\u0bbf\u0bb3\u0bc8\u0baf\u0bbf\u0bb2\u0bc1\u0bae\u0bcd \u0b87\u0bb2\u0bcd\u0bb2\u0bc8. \n\u0b89\u0ba3\u0bcd\u0bae\u0bc8\u0baf\u0bbf\u0bb2\u0bcd \u0b95\u0bbf\u0bb3\u0bc8 %s \u0ba8\u0bc0\u0b95\u0bcd\u0b95\u0bb5\u0bbe?"
::msgcat::mcset ta "Tags and heads: %s" "\u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb1\u0bcd\u0b95\u0bb3\u0bcd \u0bae\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd \u0ba4\u0bb2\u0bc8\u0b95\u0bb3\u0bcd: %s"
::msgcat::mcset ta "Filter" "\u0bb5\u0b9f\u0bbf\u0baa\u0bcd\u0baa\u0bbf"
::msgcat::mcset ta "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b87\u0b9f\u0bb5\u0bbf\u0baf\u0bb2\u0bcd \u0ba4\u0b95\u0bb5\u0bb2\u0bc8 \u0baa\u0b9f\u0bbf\u0baa\u0bcd\u0baa\u0ba4\u0bbf\u0bb2\u0bcd \u0baa\u0bbf\u0bb4\u0bc8; \u0b95\u0bbf\u0bb3\u0bc8 \u0bae\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd \u0b85\u0ba4\u0bb1\u0bcd\u0b95\u0bc1 \u0bae\u0bc1\u0ba8\u0bcd\u0ba4\u0bc8\u0baf/\u0baa\u0bbf\u0ba9\u0bcd\u0bb5\u0bb0\u0bc1\u0bae\u0bcd \u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb2\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bbf \u0bae\u0bc1\u0bb4\u0bc1\u0bae\u0bc8\u0baf\u0b9f\u0bc8\u0baf\u0bbe\u0ba4\u0bc1."
::msgcat::mcset ta "Tag" "\u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb2\u0bcd"
::msgcat::mcset ta "Id" "\u0b85\u0b9f\u0bc8\u0baf\u0bbe\u0bb3\u0bae\u0bcd"
::msgcat::mcset ta "Gitk font chooser" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf\u0b95\u0bc7 \u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1 \u0ba4\u0bc7\u0bb0\u0bcd\u0bb5\u0bc1"
::msgcat::mcset ta "B" "\u0baa\u0bbf"
::msgcat::mcset ta "I" "\u0b90"
::msgcat::mcset ta "Commit list display options" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bcd \u0b95\u0bbe\u0b9f\u0bcd\u0b9a\u0bbf \u0bb5\u0bbf\u0bb0\u0bc1\u0baa\u0bcd\u0baa\u0b99\u0bcd\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Maximum graph width (lines)" "\u0b85\u0ba4\u0bbf\u0b95\u0baa\u0b9f\u0bcd\u0b9a \u0bb5\u0bb0\u0bc8\u0baa\u0b9f \u0b85\u0b95\u0bb2\u0bae\u0bcd (\u0b95\u0bcb\u0b9f\u0bc1\u0b95\u0bb3\u0bcd)"
::msgcat::mcset ta "Maximum graph width (% of pane)" "\u0b85\u0ba4\u0bbf\u0b95\u0baa\u0b9f\u0bcd\u0b9a \u0bb5\u0bb0\u0bc8\u0baa\u0b9f \u0b85\u0b95\u0bb2\u0bae\u0bcd (\u0baa\u0bb2\u0b95\u0ba4\u0bcd\u0ba4\u0bbf\u0ba9\u0bcd %)"
::msgcat::mcset ta "Show local changes" "\u0b89\u0bb3\u0bcd\u0bb3\u0b95 \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bc8\u0b95\u0bcd \u0b95\u0bbe\u0b9f\u0bcd\u0b9f\u0bc1"
::msgcat::mcset ta "Hide remote refs" "\u0ba4\u0bca\u0bb2\u0bc8 \u0b95\u0bc1\u0bb1\u0bbf\u0b95\u0bb3\u0bc8 \u0bae\u0bb1\u0bc8"
::msgcat::mcset ta "Copy commit ID to clipboard" "\u0b87\u0b9f\u0bc8\u0ba8\u0bbf\u0bb2\u0bc8\u0baa\u0bcd\u0baa\u0bb2\u0b95\u0bc8\u0b95\u0bcd\u0b95\u0bc1 \u0b85\u0b9f\u0bc8\u0baf\u0bbe\u0bb3\u0ba4\u0bcd\u0ba4\u0bc8 \u0ba8\u0b95\u0bb2\u0bc6\u0b9f\u0bc1"
::msgcat::mcset ta "Copy commit ID to X11 selection" "\u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b85\u0b9f\u0bc8\u0baf\u0bbe\u0bb3\u0ba4\u0bcd\u0ba4\u0bc8 \u0b8311 \u0baa\u0b95\u0bc1\u0ba4\u0bbf\u0b95\u0bcd\u0b95\u0bc1 \u0ba8\u0b95\u0bb2\u0bc6\u0b9f\u0bc1"
::msgcat::mcset ta "Length of commit ID to copy" "\u0ba8\u0b95\u0bb2\u0bc6\u0b9f\u0bc1\u0b95\u0bcd\u0b95 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0bae\u0bca\u0bb4\u0bbf \u0b85\u0b9f\u0bc8\u0baf\u0bbe\u0bb3\u0ba4\u0bcd\u0ba4\u0bbf\u0ba9\u0bcd \u0ba8\u0bc0\u0bb3\u0bae\u0bcd"
::msgcat::mcset ta "Diff display options" "\u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 \u0b95\u0bbe\u0b9f\u0bcd\u0b9a\u0bbf \u0bb5\u0bbf\u0bb0\u0bc1\u0baa\u0bcd\u0baa\u0b99\u0bcd\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Tab spacing" "\u0ba4\u0bbe\u0bb5\u0bb2\u0bcd \u0b87\u0b9f\u0bc8\u0bb5\u0bc6\u0bb3\u0bbf"
::msgcat::mcset ta "Wrap comment text" "\u0b95\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1 \u0b89\u0bb0\u0bc8\u0baf\u0bc8 \u0bae\u0b9f\u0bbf"
::msgcat::mcset ta "Wrap other text" "\u0bae\u0bb1\u0bcd\u0bb1 \u0b89\u0bb0\u0bc8\u0baf\u0bc8 \u0bae\u0b9f\u0bbf"
::msgcat::mcset ta "Display nearby tags/heads" "\u0b85\u0bb0\u0bc1\u0b95\u0bbf\u0bb2\u0bc1\u0bb3\u0bcd\u0bb3 \u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb1\u0bcd\u0b95\u0bb3\u0bcd/\u0ba4\u0bb2\u0bc8\u0b95\u0bb3\u0bc8\u0b95\u0bcd \u0b95\u0bbe\u0ba3\u0bcd\u0baa\u0bbf"
::msgcat::mcset ta "Maximum # tags/heads to show" "\u0b95\u0bbe\u0ba3\u0bcd\u0baa\u0bbf\u0b95\u0bcd\u0b95 \u0b85\u0ba4\u0bbf\u0b95\u0baa\u0b9f\u0bcd\u0b9a # \u0b95\u0bc1\u0bb1\u0bbf\u0b9a\u0bcd\u0b9a\u0bca\u0bb1\u0bcd\u0b95\u0bb3\u0bcd/\u0ba4\u0bb2\u0bc8\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Limit diffs to listed paths" "\u0baa\u0b9f\u0bcd\u0b9f\u0bbf\u0baf\u0bb2\u0bbf\u0b9f\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0baa\u0bbe\u0ba4\u0bc8\u0b95\u0bb3\u0bc1\u0b95\u0bcd\u0b95\u0bc1 \u0bb5\u0bb0\u0bae\u0bcd\u0baa\u0bc1 \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0b9f\u0bc1\u0b95\u0bbf\u0bb1\u0ba4\u0bc1"
::msgcat::mcset ta "Support per-file encodings" "\u0b92\u0bb0\u0bc1 \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1 \u0b95\u0bc1\u0bb1\u0bbf\u0baf\u0bc0\u0b9f\u0bc1\u0b95\u0bb3\u0bc8 \u0b86\u0ba4\u0bb0\u0bbf"
::msgcat::mcset ta "External diff tool" "\u0bb5\u0bc6\u0bb3\u0bbf\u0baa\u0bcd\u0baa\u0bc1\u0bb1 \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1 \u0b95\u0bb0\u0bc1\u0bb5\u0bbf"
::msgcat::mcset ta "Choose..." "\u0ba4\u0bc7\u0bb0\u0bcd\u0bb5\u0bc1..."
::msgcat::mcset ta "Web browser" "\u0bb5\u0bb2\u0bc8 \u0b89\u0bb2\u0bbe\u0bb5\u0bbf"
::msgcat::mcset ta "General options" "\u0baa\u0bca\u0ba4\u0bc1 \u0bb5\u0bbf\u0bb0\u0bc1\u0baa\u0bcd\u0baa\u0b99\u0bcd\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Use themed widgets" "\u0b95\u0bb0\u0bc1\u0baa\u0bcd\u0baa\u0bca\u0bb0\u0bc1\u0bb3\u0bcd \u0ba8\u0bbf\u0bb0\u0bb2\u0bcd\u0baa\u0bb2\u0b95\u0bc8\u0b95\u0bb3\u0bc8\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "(change requires restart)" "(\u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0ba4\u0bcd\u0ba4\u0bbf\u0bb1\u0bcd\u0b95\u0bc1 \u0bae\u0bb1\u0bc1\u0ba4\u0bca\u0b9f\u0b95\u0bcd\u0b95\u0bae\u0bcd \u0ba4\u0bc7\u0bb5\u0bc8)"
::msgcat::mcset ta "(currently unavailable)" "(\u0ba4\u0bb1\u0bcd\u0baa\u0bcb\u0ba4\u0bc1 \u0b95\u0bbf\u0b9f\u0bc8\u0b95\u0bcd\u0b95\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8)"
::msgcat::mcset ta "Colors: press to choose" "\u0ba8\u0bbf\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bcd: \u0ba4\u0bc7\u0bb0\u0bcd\u0bb5\u0bc1 \u0b9a\u0bc6\u0baf\u0bcd\u0baf \u0b85\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bb5\u0bc1\u0bae\u0bcd"
::msgcat::mcset ta "Interface" "\u0b87\u0b9f\u0bc8\u0bae\u0bc1\u0b95\u0bae\u0bcd"
::msgcat::mcset ta "interface" "\u0b87\u0b9f\u0bc8\u0bae\u0bc1\u0b95\u0bae\u0bcd"
::msgcat::mcset ta "Background" "\u0baa\u0bbf\u0ba9\u0bcd\u0ba9\u0ba3\u0bbf"
::msgcat::mcset ta "background" "\u0baa\u0bbf\u0ba9\u0bcd\u0ba9\u0ba3\u0bbf"
::msgcat::mcset ta "Foreground" "\u0bae\u0bc1\u0ba9\u0bcd\u0baa\u0bc1\u0bb1\u0bae\u0bcd"
::msgcat::mcset ta "foreground" "\u0bae\u0bc1\u0ba9\u0bcd\u0baa\u0bc1\u0bb1\u0bae\u0bcd"
::msgcat::mcset ta "Diff: old lines" "\u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1: \u0baa\u0bb4\u0bc8\u0baf \u0bb5\u0bb0\u0bbf\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "diff old lines" "\u0baa\u0bb4\u0bc8\u0baf \u0bb5\u0bb0\u0bbf\u0b95\u0bb3\u0bcd \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1"
::msgcat::mcset ta "Diff: old lines bg" "\u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1: \u0baa\u0bb4\u0bc8\u0baf \u0bb5\u0bb0\u0bbf\u0b95\u0bb3\u0bcd \u0baa\u0bbf\u0ba9\u0bcd\u0ba3\u0ba9\u0bbf"
::msgcat::mcset ta "diff old lines bg" "\u0baa\u0bb4\u0bc8\u0baf \u0bb5\u0bb0\u0bbf\u0b95\u0bb3\u0bcd \u0baa\u0bbf\u0ba9\u0bcd\u0ba3\u0ba9\u0bbf \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1"
::msgcat::mcset ta "Diff: new lines" "\u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1: \u0baa\u0bc1\u0ba4\u0bbf\u0baf \u0b95\u0bcb\u0b9f\u0bc1\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "diff new lines" "\u0baa\u0bc1\u0ba4\u0bbf\u0baf \u0bb5\u0bb0\u0bbf\u0b95\u0bb3\u0bcd \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1"
::msgcat::mcset ta "Diff: new lines bg" "\u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1: \u0baa\u0bc1\u0ba4\u0bbf\u0baf \u0bb5\u0bb0\u0bbf\u0b95\u0bb3\u0bcd \u0baa\u0bbf\u0ba9\u0bcd\u0ba3\u0ba9\u0bbf"
::msgcat::mcset ta "diff new lines bg" "\u0baa\u0bc1\u0ba4\u0bbf\u0baf \u0bb5\u0bb0\u0bbf\u0b95\u0bb3\u0bc8 \u0baa\u0bbf\u0ba9\u0bcd\u0ba3\u0ba9\u0bbf \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1"
::msgcat::mcset ta "Diff: hunk header" "\u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1: \u0b85\u0b99\u0bcd\u0b95\u0bcd \u0ba4\u0bb2\u0bc8\u0baa\u0bcd\u0baa\u0bc1"
::msgcat::mcset ta "diff hunk header" "\u0b85\u0b99\u0bcd\u0b95\u0bcd \u0ba4\u0bb2\u0bc8\u0baa\u0bcd\u0baa\u0bc1 \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1"
::msgcat::mcset ta "Marked line bg" "\u0b95\u0bc1\u0bb1\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0bb5\u0bb0\u0bbf \u0baa\u0bbf\u0ba9\u0bcd\u0ba9\u0ba3\u0bbf"
::msgcat::mcset ta "marked line background" "\u0b95\u0bc1\u0bb1\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0bb5\u0bb0\u0bbf \u0baa\u0bbf\u0ba9\u0bcd\u0ba9\u0ba3\u0bbf"
::msgcat::mcset ta "Select bg" "\u0baa\u0bbf\u0ba9\u0bcd\u0ba9\u0ba3\u0bbf \u0ba4\u0bc7\u0bb0\u0bcd\u0bb5\u0bc1"
::msgcat::mcset ta "Fonts: press to choose" "\u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1\u0b95\u0bcd\u0b95\u0bb3\u0bcd: \u0ba4\u0bc7\u0bb0\u0bcd\u0bb5\u0bc1 \u0b9a\u0bc6\u0baf\u0bcd\u0baf \u0b85\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1"
::msgcat::mcset ta "Main font" "\u0bae\u0bc1\u0ba4\u0ba9\u0bcd\u0bae\u0bc8\u0baf\u0bbe\u0ba9 \u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1"
::msgcat::mcset ta "Diff display font" "\u0b95\u0bbe\u0b9f\u0bcd\u0b9a\u0bbf \u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1 \u0bb5\u0bc7\u0bb1\u0bc1\u0baa\u0bbe\u0b9f\u0bc1"
::msgcat::mcset ta "User interface font" "\u0baa\u0baf\u0ba9\u0bb0\u0bcd \u0b87\u0b9f\u0bc8\u0bae\u0bc1\u0b95 \u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1"
::msgcat::mcset ta "Gitk preferences" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf\u0b95\u0bc7 \u0bb5\u0bbf\u0bb0\u0bc1\u0baa\u0bcd\u0baa\u0ba4\u0bcd\u0ba4\u0bc7\u0bb0\u0bcd\u0bb5\u0bc1\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "General" "\u0baa\u0bc6\u0bbe\u0ba4\u0bc1"
::msgcat::mcset ta "Colors" "\u0ba8\u0bbf\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Fonts" "\u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1\u0b95\u0bcd\u0b95\u0bb3\u0bcd"
::msgcat::mcset ta "Gitk: choose color for %s" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf\u0b95\u0bc7: %s \u0b95\u0bcd\u0b95\u0bc1 \u0bb5\u0ba3\u0bcd\u0ba3\u0ba4\u0bcd\u0ba4\u0bc8\u0ba4\u0bcd \u0ba4\u0bc7\u0bb0\u0bcd\u0bb5\u0bc1\u0b9a\u0bc6\u0baf\u0bcd\u0b95"
::msgcat::mcset ta "Sorry, gitk cannot run with this version of Tcl/Tk.\n Gitk requires at least Tcl/Tk 8.4." "\u0bae\u0ba9\u0bcd\u0ba9\u0bbf\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd, \u0b9f\u0bbf\u0b9a\u0bbf\u0b8e\u0bb2\u0bcd/\u0b9f\u0bbf\u0b95\u0bc7\u0baf\u0bbf\u0ba9\u0bcd \u0b87\u0ba8\u0bcd\u0ba4 \u0baa\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc8\u0b95\u0bcd \u0b95\u0bca\u0ba3\u0bcd\u0b9f\u0bc1 \u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf\u0b95\u0bc7 \u0b87\u0baf\u0b95\u0bcd\u0b95 \u0bae\u0bc1\u0b9f\u0bbf\u0baf\u0bbe\u0ba4\u0bc1. \n\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf\u0b95\u0bc7\u0bb5\u0bc1\u0b95\u0bcd\u0b95\u0bc1 \u0b95\u0bc1\u0bb1\u0bc8\u0ba8\u0bcd\u0ba4\u0baa\u0b9f\u0bcd\u0b9a\u0bae\u0bcd \u0b9f\u0bbf\u0b9a\u0bbf\u0b8e\u0bb2\u0bcd/\u0b9f\u0bbf\u0b95\u0bc7 8.4 \u0ba4\u0bc7\u0bb5\u0bc8\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0b95\u0bbf\u0bb1\u0ba4\u0bc1."
::msgcat::mcset ta "Cannot find a git repository here." "\u0b87\u0b99\u0bcd\u0b95\u0bc7 \u0b92\u0bb0\u0bc1 \u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf \u0b95\u0bb3\u0b9e\u0bcd\u0b9a\u0bbf\u0baf\u0ba4\u0bcd\u0ba4\u0bc8\u0b95\u0bcd \u0b95\u0ba3\u0bcd\u0b9f\u0bc1\u0baa\u0bbf\u0b9f\u0bbf\u0b95\u0bcd\u0b95 \u0bae\u0bc1\u0b9f\u0bbf\u0baf\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8."
::msgcat::mcset ta "Ambiguous argument '%s': both revision and filename" "\u0ba4\u0bc6\u0bb3\u0bbf\u0bb5\u0bb1\u0bcd\u0bb1 \u0bb5\u0bbe\u0ba4\u0bae\u0bcd '%s': \u0ba4\u0bbf\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0bae\u0bcd \u0bae\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc1 \u0baa\u0bc6\u0baf\u0bb0\u0bcd"
::msgcat::mcset ta "Bad arguments to gitk:" "\u0b85\u0bb1\u0bbf\u0bb5\u0bbf\u0bb2\u0bbf\u0b95\u0bc7\u0bb5\u0bbf\u0bb1\u0bcd\u0b95\u0bc1 \u0bae\u0bcb\u0b9a\u0bae\u0bbe\u0ba9 \u0bb5\u0bbe\u0ba4\u0b99\u0bcd\u0b95\u0bb3\u0bcd:"
