.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_heartbeat_enable" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_heartbeat_enable \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_heartbeat_enable(gnutls_session_t " session ", unsigned int " type ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned int type" 12
one of the GNUTLS_HB_* flags
.SH "DESCRIPTION"
If this function is called with the \fBGNUTLS_HB_PEER_ALLOWED_TO_SEND\fP
 \fItype\fP , GnuTLS will allow heartbeat messages to be received. Moreover it also
request the peer to accept heartbeat messages. This function
must be called prior to TLS handshake.

If the  \fItype\fP used is \fBGNUTLS_HB_LOCAL_ALLOWED_TO_SEND\fP, then the peer
will be asked to accept heartbeat messages but not send ones.

The function \fBgnutls_heartbeat_allowed()\fP can be used to test Whether
locally generated heartbeat messages can be accepted by the peer.
.SH "SINCE"
3.1.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
