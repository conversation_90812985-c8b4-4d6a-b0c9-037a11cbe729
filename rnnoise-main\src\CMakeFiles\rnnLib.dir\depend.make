# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

src/CMakeFiles/rnnLib.dir/celt_lpc.c.o: src/arch.h
src/CMakeFiles/rnnLib.dir/celt_lpc.c.o: src/celt_lpc.c
src/CMakeFiles/rnnLib.dir/celt_lpc.c.o: src/celt_lpc.h
src/CMakeFiles/rnnLib.dir/celt_lpc.c.o: src/common.h
src/CMakeFiles/rnnLib.dir/celt_lpc.c.o: src/opus_types.h
src/CMakeFiles/rnnLib.dir/celt_lpc.c.o: src/pitch.h

src/CMakeFiles/rnnLib.dir/denoise.c.o: src/arch.h
src/CMakeFiles/rnnLib.dir/denoise.c.o: src/common.h
src/CMakeFiles/rnnLib.dir/denoise.c.o: src/denoise.c
src/CMakeFiles/rnnLib.dir/denoise.c.o: src/kiss_fft.h
src/CMakeFiles/rnnLib.dir/denoise.c.o: src/opus_types.h
src/CMakeFiles/rnnLib.dir/denoise.c.o: src/pitch.h
src/CMakeFiles/rnnLib.dir/denoise.c.o: src/rnn.h
src/CMakeFiles/rnnLib.dir/denoise.c.o: src/rnn_data.h
src/CMakeFiles/rnnLib.dir/denoise.c.o: include/rnnoise.h

src/CMakeFiles/rnnLib.dir/denoise16.c.o: src/arch.h
src/CMakeFiles/rnnLib.dir/denoise16.c.o: src/common.h
src/CMakeFiles/rnnLib.dir/denoise16.c.o: src/denoise16.c
src/CMakeFiles/rnnLib.dir/denoise16.c.o: src/kiss_fft.h
src/CMakeFiles/rnnLib.dir/denoise16.c.o: src/opus_types.h
src/CMakeFiles/rnnLib.dir/denoise16.c.o: src/pitch.h
src/CMakeFiles/rnnLib.dir/denoise16.c.o: src/rnn.h
src/CMakeFiles/rnnLib.dir/denoise16.c.o: src/rnn_data.h
src/CMakeFiles/rnnLib.dir/denoise16.c.o: include/rnnoise.h

src/CMakeFiles/rnnLib.dir/kiss_fft.c.o: src/_kiss_fft_guts.h
src/CMakeFiles/rnnLib.dir/kiss_fft.c.o: src/arch.h
src/CMakeFiles/rnnLib.dir/kiss_fft.c.o: src/common.h
src/CMakeFiles/rnnLib.dir/kiss_fft.c.o: src/kiss_fft.c
src/CMakeFiles/rnnLib.dir/kiss_fft.c.o: src/kiss_fft.h
src/CMakeFiles/rnnLib.dir/kiss_fft.c.o: src/opus_types.h

src/CMakeFiles/rnnLib.dir/pitch.c.o: src/arch.h
src/CMakeFiles/rnnLib.dir/pitch.c.o: src/celt_lpc.h
src/CMakeFiles/rnnLib.dir/pitch.c.o: src/common.h
src/CMakeFiles/rnnLib.dir/pitch.c.o: src/opus_types.h
src/CMakeFiles/rnnLib.dir/pitch.c.o: src/pitch.c
src/CMakeFiles/rnnLib.dir/pitch.c.o: src/pitch.h

src/CMakeFiles/rnnLib.dir/rnn.c.o: src/arch.h
src/CMakeFiles/rnnLib.dir/rnn.c.o: src/common.h
src/CMakeFiles/rnnLib.dir/rnn.c.o: src/opus_types.h
src/CMakeFiles/rnnLib.dir/rnn.c.o: src/rnn.c
src/CMakeFiles/rnnLib.dir/rnn.c.o: src/rnn.h
src/CMakeFiles/rnnLib.dir/rnn.c.o: src/rnn_data.h
src/CMakeFiles/rnnLib.dir/rnn.c.o: src/tansig_table.h

src/CMakeFiles/rnnLib.dir/rnn_data.c.o: src/opus_types.h
src/CMakeFiles/rnnLib.dir/rnn_data.c.o: src/rnn.h
src/CMakeFiles/rnnLib.dir/rnn_data.c.o: src/rnn_data.c

