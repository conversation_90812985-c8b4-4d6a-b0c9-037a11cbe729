/*
 * Copyright (C) 2023 Biswapriyo <PERSON>h
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "ocidl.idl";
import "dxgicommon.idl";
import "d3d12.idl";

typedef enum D3D12_VIDEO_FIELD_TYPE
{
    D3D12_VIDEO_FIELD_TYPE_NONE = 0,
    D3D12_VIDEO_FIELD_TYPE_INTERLACED_TOP_FIELD_FIRST = 1,
    D3D12_VIDEO_FIELD_TYPE_INTERLACED_BOTTOM_FIELD_FIRST = 2,
} D3D12_VIDEO_FIELD_TYPE;

typedef enum D3D12_VIDEO_FRAME_STEREO_FORMAT
{
    D3D12_VIDEO_FRAME_STEREO_FORMAT_NONE = 0,
    D3D12_VIDEO_FRAME_STEREO_FORMAT_MONO = 1,
    D3D12_VIDEO_FRAME_STEREO_FORMAT_HORIZONTAL = 2,
    D3D12_VIDEO_FRAME_STEREO_FORMAT_VERTICAL = 3,
    D3D12_VIDEO_FRAME_STEREO_FORMAT_SEPARATE = 4,
} D3D12_VIDEO_FRAME_STEREO_FORMAT;

typedef enum D3D12_VIDEO_FRAME_CODED_INTERLACE_TYPE
{
    D3D12_VIDEO_FRAME_CODED_INTERLACE_TYPE_NONE = 0,
    D3D12_VIDEO_FRAME_CODED_INTERLACE_TYPE_FIELD_BASED = 1,
} D3D12_VIDEO_FRAME_CODED_INTERLACE_TYPE;

typedef enum D3D12_FEATURE_VIDEO
{
    D3D12_FEATURE_VIDEO_DECODE_SUPPORT = 0,
    D3D12_FEATURE_VIDEO_DECODE_PROFILES = 1,
    D3D12_FEATURE_VIDEO_DECODE_FORMATS = 2,
    D3D12_FEATURE_VIDEO_DECODE_CONVERSION_SUPPORT = 3,
    D3D12_FEATURE_VIDEO_PROCESS_SUPPORT = 5,
    D3D12_FEATURE_VIDEO_PROCESS_MAX_INPUT_STREAMS = 6,
    D3D12_FEATURE_VIDEO_PROCESS_REFERENCE_INFO = 7,
    D3D12_FEATURE_VIDEO_DECODER_HEAP_SIZE = 8,
    D3D12_FEATURE_VIDEO_PROCESSOR_SIZE = 9,
    D3D12_FEATURE_VIDEO_DECODE_PROFILE_COUNT = 10,
    D3D12_FEATURE_VIDEO_DECODE_FORMAT_COUNT = 11,
    D3D12_FEATURE_VIDEO_ARCHITECTURE = 17,
    D3D12_FEATURE_VIDEO_DECODE_HISTOGRAM = 18,
    D3D12_FEATURE_VIDEO_FEATURE_AREA_SUPPORT = 19,
    D3D12_FEATURE_VIDEO_MOTION_ESTIMATOR = 20,
    D3D12_FEATURE_VIDEO_MOTION_ESTIMATOR_SIZE = 21,
    D3D12_FEATURE_VIDEO_EXTENSION_COMMAND_COUNT = 22,
    D3D12_FEATURE_VIDEO_EXTENSION_COMMANDS = 23,
    D3D12_FEATURE_VIDEO_EXTENSION_COMMAND_PARAMETER_COUNT = 24,
    D3D12_FEATURE_VIDEO_EXTENSION_COMMAND_PARAMETERS = 25,
    D3D12_FEATURE_VIDEO_EXTENSION_COMMAND_SUPPORT = 26,
    D3D12_FEATURE_VIDEO_EXTENSION_COMMAND_SIZE = 27,
    D3D12_FEATURE_VIDEO_DECODE_PROTECTED_RESOURCES = 28,
    D3D12_FEATURE_VIDEO_PROCESS_PROTECTED_RESOURCES = 29,
    D3D12_FEATURE_VIDEO_MOTION_ESTIMATOR_PROTECTED_RESOURCES = 30,
    D3D12_FEATURE_VIDEO_DECODER_HEAP_SIZE1 = 31,
    D3D12_FEATURE_VIDEO_PROCESSOR_SIZE1 = 32,
    D3D12_FEATURE_VIDEO_ENCODER_CODEC = 33,
    D3D12_FEATURE_VIDEO_ENCODER_PROFILE_LEVEL = 34,
    D3D12_FEATURE_VIDEO_ENCODER_OUTPUT_RESOLUTION_RATIOS_COUNT = 35,
    D3D12_FEATURE_VIDEO_ENCODER_OUTPUT_RESOLUTION = 36,
    D3D12_FEATURE_VIDEO_ENCODER_INPUT_FORMAT = 37,
    D3D12_FEATURE_VIDEO_ENCODER_RATE_CONTROL_MODE = 38,
    D3D12_FEATURE_VIDEO_ENCODER_INTRA_REFRESH_MODE = 39,
    D3D12_FEATURE_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE = 40,
    D3D12_FEATURE_VIDEO_ENCODER_HEAP_SIZE = 41,
    D3D12_FEATURE_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT = 42,
    D3D12_FEATURE_VIDEO_ENCODER_SUPPORT = 43,
    D3D12_FEATURE_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT = 44,
    D3D12_FEATURE_VIDEO_ENCODER_RESOURCE_REQUIREMENTS = 45,
} D3D12_FEATURE_VIDEO;

typedef enum D3D12_BITSTREAM_ENCRYPTION_TYPE
{
    D3D12_BITSTREAM_ENCRYPTION_TYPE_NONE = 0,
} D3D12_BITSTREAM_ENCRYPTION_TYPE;

typedef struct D3D12_VIDEO_DECODE_CONFIGURATION
{
    GUID DecodeProfile;
    D3D12_BITSTREAM_ENCRYPTION_TYPE BitstreamEncryption;
    D3D12_VIDEO_FRAME_CODED_INTERLACE_TYPE InterlaceType;
} D3D12_VIDEO_DECODE_CONFIGURATION;

typedef struct D3D12_VIDEO_DECODER_DESC
{
    UINT NodeMask;
    D3D12_VIDEO_DECODE_CONFIGURATION Configuration;
} D3D12_VIDEO_DECODER_DESC;

typedef struct D3D12_VIDEO_DECODER_HEAP_DESC
{
    UINT NodeMask;
    D3D12_VIDEO_DECODE_CONFIGURATION Configuration;
    UINT DecodeWidth;
    UINT DecodeHeight;
    DXGI_FORMAT Format;
    DXGI_RATIONAL FrameRate;
    UINT BitRate;
    UINT MaxDecodePictureBufferCount;
} D3D12_VIDEO_DECODER_HEAP_DESC;

typedef struct D3D12_VIDEO_SIZE_RANGE
{
    UINT MaxWidth;
    UINT MaxHeight;
    UINT MinWidth;
    UINT MinHeight;
} D3D12_VIDEO_SIZE_RANGE;

typedef enum D3D12_VIDEO_PROCESS_FILTER
{
    D3D12_VIDEO_PROCESS_FILTER_BRIGHTNESS = 0,
    D3D12_VIDEO_PROCESS_FILTER_CONTRAST = 1,
    D3D12_VIDEO_PROCESS_FILTER_HUE = 2,
    D3D12_VIDEO_PROCESS_FILTER_SATURATION = 3,
    D3D12_VIDEO_PROCESS_FILTER_NOISE_REDUCTION = 4,
    D3D12_VIDEO_PROCESS_FILTER_EDGE_ENHANCEMENT = 5,
    D3D12_VIDEO_PROCESS_FILTER_ANAMORPHIC_SCALING = 6,
    D3D12_VIDEO_PROCESS_FILTER_STEREO_ADJUSTMENT = 7,
} D3D12_VIDEO_PROCESS_FILTER;

typedef enum D3D12_VIDEO_PROCESS_FILTER_FLAGS
{
    D3D12_VIDEO_PROCESS_FILTER_FLAG_NONE = 0x0,
    D3D12_VIDEO_PROCESS_FILTER_FLAG_BRIGHTNESS = (1 << D3D12_VIDEO_PROCESS_FILTER_BRIGHTNESS),
    D3D12_VIDEO_PROCESS_FILTER_FLAG_CONTRAST = (1 << D3D12_VIDEO_PROCESS_FILTER_CONTRAST),
    D3D12_VIDEO_PROCESS_FILTER_FLAG_HUE = (1 << D3D12_VIDEO_PROCESS_FILTER_HUE),
    D3D12_VIDEO_PROCESS_FILTER_FLAG_SATURATION = (1 << D3D12_VIDEO_PROCESS_FILTER_SATURATION),
    D3D12_VIDEO_PROCESS_FILTER_FLAG_NOISE_REDUCTION = (1 << D3D12_VIDEO_PROCESS_FILTER_NOISE_REDUCTION),
    D3D12_VIDEO_PROCESS_FILTER_FLAG_EDGE_ENHANCEMENT = (1 << D3D12_VIDEO_PROCESS_FILTER_EDGE_ENHANCEMENT),
    D3D12_VIDEO_PROCESS_FILTER_FLAG_ANAMORPHIC_SCALING = (1 << D3D12_VIDEO_PROCESS_FILTER_ANAMORPHIC_SCALING),
    D3D12_VIDEO_PROCESS_FILTER_FLAG_STEREO_ADJUSTMENT = (1 << D3D12_VIDEO_PROCESS_FILTER_STEREO_ADJUSTMENT),
} D3D12_VIDEO_PROCESS_FILTER_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_PROCESS_FILTER_FLAGS);")

typedef enum D3D12_VIDEO_PROCESS_DEINTERLACE_FLAGS
{
    D3D12_VIDEO_PROCESS_DEINTERLACE_FLAG_NONE = 0x0,
    D3D12_VIDEO_PROCESS_DEINTERLACE_FLAG_BOB = 0x1,
    D3D12_VIDEO_PROCESS_DEINTERLACE_FLAG_CUSTOM = 0x80000000,
} D3D12_VIDEO_PROCESS_DEINTERLACE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_PROCESS_DEINTERLACE_FLAGS );")

typedef struct D3D12_VIDEO_PROCESS_LUMA_KEY
{
    BOOL Enable;
    FLOAT Lower;
    FLOAT Upper;
} D3D12_VIDEO_PROCESS_LUMA_KEY;

typedef struct D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC
{
    DXGI_FORMAT Format;
    DXGI_COLOR_SPACE_TYPE ColorSpace;
    DXGI_RATIONAL SourceAspectRatio;
    DXGI_RATIONAL DestinationAspectRatio;
    DXGI_RATIONAL FrameRate;
    D3D12_VIDEO_SIZE_RANGE SourceSizeRange;
    D3D12_VIDEO_SIZE_RANGE DestinationSizeRange;
    BOOL EnableOrientation;
    D3D12_VIDEO_PROCESS_FILTER_FLAGS FilterFlags;
    D3D12_VIDEO_FRAME_STEREO_FORMAT StereoFormat;
    D3D12_VIDEO_FIELD_TYPE FieldType;
    D3D12_VIDEO_PROCESS_DEINTERLACE_FLAGS DeinterlaceMode;
    BOOL EnableAlphaBlending;
    D3D12_VIDEO_PROCESS_LUMA_KEY LumaKey;
    UINT NumPastFrames;
    UINT NumFutureFrames;
    BOOL EnableAutoProcessing;
} D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC;

typedef enum D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE
{
    D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE_OPAQUE = 0,
    D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE_BACKGROUND = 1,
    D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE_DESTINATION = 2,
    D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE_SOURCE_STREAM = 3,
} D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE;

typedef struct D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC
{
    DXGI_FORMAT Format;
    DXGI_COLOR_SPACE_TYPE ColorSpace;
    D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE AlphaFillMode;
    UINT AlphaFillModeSourceStreamIndex;
    FLOAT BackgroundColor[4];
    DXGI_RATIONAL FrameRate;
    BOOL EnableStereo;
} D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC;

[
    uuid(0946b7c9-ebf6-4047-bb73-8683e27dbb1f),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoDecoderHeap : ID3D12Pageable
{
    D3D12_VIDEO_DECODER_HEAP_DESC GetDesc();
}

[
    uuid(1f052807-0b46-4acc-8a89-364f793718a4),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoDevice : IUnknown
{
    HRESULT CheckFeatureSupport(
        D3D12_FEATURE_VIDEO feature_video,
        [in, out] void *feature_support_data,
        UINT feature_support_data_size
    );

    HRESULT CreateVideoDecoder(
        [in] const D3D12_VIDEO_DECODER_DESC *desc,
        [in] REFIID riid,
        [out, iid_is(riid)] void **video_decoder
    );

    HRESULT CreateVideoDecoderHeap(
        [in] const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,
        [in] REFIID riid,
        [out, iid_is(riid)] void **video_decoder_heap
    );

    HRESULT CreateVideoProcessor(
        UINT node_mask,
        [in] const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,
        [in] UINT input_stream_descs_count,
        [in] const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,
        [in] REFIID riid,
        [out, iid_is(riid)] void **video_processor
    );
}

[
    uuid(c59b6bdc-7720-4074-a136-17a156037470),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoDecoder : ID3D12Pageable
{
    D3D12_VIDEO_DECODER_DESC GetDesc();
}

typedef enum D3D12_VIDEO_DECODE_TIER
{
    D3D12_VIDEO_DECODE_TIER_NOT_SUPPORTED = 0,
    D3D12_VIDEO_DECODE_TIER_1 = 1,
    D3D12_VIDEO_DECODE_TIER_2 = 2,
    D3D12_VIDEO_DECODE_TIER_3 = 3,
} D3D12_VIDEO_DECODE_TIER;

typedef enum D3D12_VIDEO_DECODE_SUPPORT_FLAGS
{
    D3D12_VIDEO_DECODE_SUPPORT_FLAG_NONE = 0x0,
    D3D12_VIDEO_DECODE_SUPPORT_FLAG_SUPPORTED = 0x1,
} D3D12_VIDEO_DECODE_SUPPORT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_DECODE_SUPPORT_FLAGS);")

typedef enum D3D12_VIDEO_DECODE_CONFIGURATION_FLAGS
{
    D3D12_VIDEO_DECODE_CONFIGURATION_FLAG_NONE = 0x0,
    D3D12_VIDEO_DECODE_CONFIGURATION_FLAG_HEIGHT_ALIGNMENT_MULTIPLE_32_REQUIRED = 0x1,
    D3D12_VIDEO_DECODE_CONFIGURATION_FLAG_POST_PROCESSING_SUPPORTED = 0x2,
    D3D12_VIDEO_DECODE_CONFIGURATION_FLAG_REFERENCE_ONLY_ALLOCATIONS_REQUIRED = 0x4,
    D3D12_VIDEO_DECODE_CONFIGURATION_FLAG_ALLOW_RESOLUTION_CHANGE_ON_NON_KEY_FRAME = 0x8,
} D3D12_VIDEO_DECODE_CONFIGURATION_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_DECODE_CONFIGURATION_FLAGS);")

typedef enum D3D12_VIDEO_DECODE_ARGUMENT_TYPE
{
    D3D12_VIDEO_DECODE_ARGUMENT_TYPE_PICTURE_PARAMETERS = 0,
    D3D12_VIDEO_DECODE_ARGUMENT_TYPE_INVERSE_QUANTIZATION_MATRIX = 1,
    D3D12_VIDEO_DECODE_ARGUMENT_TYPE_SLICE_CONTROL = 2,
    D3D12_VIDEO_DECODE_ARGUMENT_TYPE_MAX_VALID = 3,
} D3D12_VIDEO_DECODE_ARGUMENT_TYPE;

typedef struct D3D12_FEATURE_DATA_VIDEO_DECODE_SUPPORT
{
    UINT NodeIndex;
    D3D12_VIDEO_DECODE_CONFIGURATION Configuration;
    UINT Width;
    UINT Height;
    DXGI_FORMAT DecodeFormat;
    DXGI_RATIONAL FrameRate;
    UINT BitRate;
    D3D12_VIDEO_DECODE_SUPPORT_FLAGS SupportFlags;
    D3D12_VIDEO_DECODE_CONFIGURATION_FLAGS ConfigurationFlags;
    D3D12_VIDEO_DECODE_TIER DecodeTier;
} D3D12_FEATURE_DATA_VIDEO_DECODE_SUPPORT;

typedef struct D3D12_VIDEO_DECODE_FRAME_ARGUMENT
{
    D3D12_VIDEO_DECODE_ARGUMENT_TYPE Type;
    UINT Size;
    void *pData;
} D3D12_VIDEO_DECODE_FRAME_ARGUMENT;

typedef struct D3D12_VIDEO_DECODE_REFERENCE_FRAMES
{
    UINT NumTexture2Ds;
    ID3D12Resource **ppTexture2Ds;
    UINT *pSubresources;
    ID3D12VideoDecoderHeap **ppHeaps;
} D3D12_VIDEO_DECODE_REFERENCE_FRAMES;

typedef struct D3D12_VIDEO_DECODE_COMPRESSED_BITSTREAM
{
    ID3D12Resource *pBuffer;
    UINT64 Offset;
    UINT64 Size;
} D3D12_VIDEO_DECODE_COMPRESSED_BITSTREAM;

typedef struct D3D12_VIDEO_DECODE_CONVERSION_ARGUMENTS
{
    BOOL Enable;
    ID3D12Resource *pReferenceTexture2D;
    UINT ReferenceSubresource;
    DXGI_COLOR_SPACE_TYPE OutputColorSpace;
    DXGI_COLOR_SPACE_TYPE DecodeColorSpace;
} D3D12_VIDEO_DECODE_CONVERSION_ARGUMENTS;

typedef struct D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS
{
    UINT NumFrameArguments;
    D3D12_VIDEO_DECODE_FRAME_ARGUMENT FrameArguments[D3D12_VIDEO_DECODE_MAX_ARGUMENTS];
    D3D12_VIDEO_DECODE_REFERENCE_FRAMES ReferenceFrames;
    D3D12_VIDEO_DECODE_COMPRESSED_BITSTREAM CompressedBitstream;
    ID3D12VideoDecoderHeap *pHeap;
} D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS;

typedef struct D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS
{
    ID3D12Resource *pOutputTexture2D;
    UINT OutputSubresource;
    D3D12_VIDEO_DECODE_CONVERSION_ARGUMENTS ConversionArguments;
} D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS;

[
    uuid(3b60536e-ad29-4e64-a269-f853837e5e53),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoDecodeCommandList : ID3D12CommandList
{
    HRESULT Close();

    HRESULT Reset(
        [in] ID3D12CommandAllocator *allocator
    );

    void ClearState();

    void ResourceBarrier(
        [in] UINT barriers_count,
        [in] const D3D12_RESOURCE_BARRIER *barriers
    );

    void DiscardResource(
        [in] ID3D12Resource *resource,
        [in, optional] const D3D12_DISCARD_REGION *region
    );

    void BeginQuery(
        [in] ID3D12QueryHeap *query_heap,
        [in] D3D12_QUERY_TYPE type,
        [in] UINT index
    );

    void EndQuery(
        [in] ID3D12QueryHeap *query_heap,
        [in] D3D12_QUERY_TYPE type,
        [in] UINT index
    );

    void ResolveQueryData(
        [in] ID3D12QueryHeap *query_heap,
        [in] D3D12_QUERY_TYPE type,
        [in] UINT start_index,
        [in] UINT queries_count,
        [in] ID3D12Resource *destination_buffer,
        [in] UINT64 aligned_destination_buffer_offset
    );

    void SetPredication(
        [in, optional] ID3D12Resource *buffer,
        [in] UINT64 aligned_buffer_offset,
        [in] D3D12_PREDICATION_OP operation
    );

    void SetMarker(
        [in] UINT metadata,
        [in] const void *data,
        [in] UINT size
    );

    void BeginEvent(
        [in] UINT metadata,
        [in] const void *data,
        [in] UINT size
    );

    void EndEvent();

    void DecodeFrame(
        [in] ID3D12VideoDecoder *decoder,
        [in] const D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS *output_arguments,
        [in] const D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS *input_arguments
    );

    void WriteBufferImmediate(
        [in] UINT count,
        [in] const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,
        [in, optional] const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes
    );
}

typedef struct D3D12_VIDEO_DECODE_OUTPUT_HISTOGRAM
{
    UINT64 Offset;
    ID3D12Resource *pBuffer;
} D3D12_VIDEO_DECODE_OUTPUT_HISTOGRAM;

typedef struct D3D12_VIDEO_DECODE_CONVERSION_ARGUMENTS1
{
    BOOL Enable;
    ID3D12Resource *pReferenceTexture2D;
    UINT ReferenceSubresource;
    DXGI_COLOR_SPACE_TYPE OutputColorSpace;
    DXGI_COLOR_SPACE_TYPE DecodeColorSpace;
    UINT OutputWidth;
    UINT OutputHeight;
} D3D12_VIDEO_DECODE_CONVERSION_ARGUMENTS1;

typedef struct D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS1
{
    ID3D12Resource *pOutputTexture2D;
    UINT OutputSubresource;
    D3D12_VIDEO_DECODE_CONVERSION_ARGUMENTS1 ConversionArguments;
    D3D12_VIDEO_DECODE_OUTPUT_HISTOGRAM Histograms[D3D12_VIDEO_DECODE_MAX_HISTOGRAM_COMPONENTS];
} D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS1;

[
    uuid(d52f011b-b56e-453c-a05a-a7f311c8f472),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoDecodeCommandList1 : ID3D12VideoDecodeCommandList
{
    void DecodeFrame1(
        [in] ID3D12VideoDecoder *decoder,
        [in] const D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS1 *output_arguments,
        [in] const D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS *input_arguments
    );
}

typedef enum D3D12_VIDEO_MOTION_ESTIMATOR_SEARCH_BLOCK_SIZE
{
    D3D12_VIDEO_MOTION_ESTIMATOR_SEARCH_BLOCK_SIZE_8X8 = 0,
    D3D12_VIDEO_MOTION_ESTIMATOR_SEARCH_BLOCK_SIZE_16X16 = 1,
} D3D12_VIDEO_MOTION_ESTIMATOR_SEARCH_BLOCK_SIZE;

typedef enum D3D12_VIDEO_MOTION_ESTIMATOR_VECTOR_PRECISION
{
    D3D12_VIDEO_MOTION_ESTIMATOR_VECTOR_PRECISION_QUARTER_PEL = 0,
} D3D12_VIDEO_MOTION_ESTIMATOR_VECTOR_PRECISION;

typedef struct D3D12_FEATURE_DATA_VIDEO_FEATURE_AREA_SUPPORT
{
    UINT NodeIndex;
    BOOL VideoDecodeSupport;
    BOOL VideoProcessSupport;
    BOOL VideoEncodeSupport;
} D3D12_FEATURE_DATA_VIDEO_FEATURE_AREA_SUPPORT;

typedef struct D3D12_VIDEO_MOTION_ESTIMATOR_DESC
{
    UINT NodeMask;
    DXGI_FORMAT InputFormat;
    D3D12_VIDEO_MOTION_ESTIMATOR_SEARCH_BLOCK_SIZE BlockSize;
    D3D12_VIDEO_MOTION_ESTIMATOR_VECTOR_PRECISION Precision;
    D3D12_VIDEO_SIZE_RANGE SizeRange;
} D3D12_VIDEO_MOTION_ESTIMATOR_DESC;

[
    uuid(33fdae0e-098b-428f-87bb-34b695de08f8),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoMotionEstimator : ID3D12Pageable
{
    D3D12_VIDEO_MOTION_ESTIMATOR_DESC GetDesc();

    HRESULT GetProtectedResourceSession(
        [in] REFIID riid,
        [out, iid_is(riid)] void **protected_session
    );
}

typedef struct D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC
{
    UINT NodeMask;
    DXGI_FORMAT InputFormat;
    D3D12_VIDEO_MOTION_ESTIMATOR_SEARCH_BLOCK_SIZE BlockSize;
    D3D12_VIDEO_MOTION_ESTIMATOR_VECTOR_PRECISION Precision;
    D3D12_VIDEO_SIZE_RANGE SizeRange;
} D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC;

[
    uuid(5be17987-743a-4061-834b-23d22daea505),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoMotionVectorHeap : ID3D12Pageable
{
    D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC GetDesc();

    HRESULT GetProtectedResourceSession(
        [in] REFIID riid,
        [out, iid_is(riid)] void **protected_session
    );
}

[
    uuid(981611ad-a144-4c83-9890-f30e26d658ab),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoDevice1 : ID3D12VideoDevice
{
    HRESULT CreateVideoMotionEstimator(
        [in] const D3D12_VIDEO_MOTION_ESTIMATOR_DESC *desc,
        [in, optional] ID3D12ProtectedResourceSession *protected_resource_session,
        [in] REFIID riid,
        [out, iid_is(riid)] void **video_motion_estimator
    );

    HRESULT CreateVideoMotionVectorHeap(
        [in] const D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC *desc,
        [in, optional] ID3D12ProtectedResourceSession *protected_resource_session,
        [in] REFIID riid,
        [out, iid_is(riid)] void **video_motion_vector_heap
    );
}

typedef struct D3D12_RESOURCE_COORDINATE
{
    UINT64 X;
    UINT Y;
    UINT Z;
    UINT SubresourceIndex;
} D3D12_RESOURCE_COORDINATE;

typedef struct D3D12_VIDEO_MOTION_ESTIMATOR_OUTPUT
{
    ID3D12VideoMotionVectorHeap *pMotionVectorHeap;
} D3D12_VIDEO_MOTION_ESTIMATOR_OUTPUT;

typedef struct D3D12_VIDEO_MOTION_ESTIMATOR_INPUT
{
    ID3D12Resource *pInputTexture2D;
    UINT InputSubresourceIndex;
    ID3D12Resource *pReferenceTexture2D;
    UINT ReferenceSubresourceIndex;
    ID3D12VideoMotionVectorHeap *pHintMotionVectorHeap;
} D3D12_VIDEO_MOTION_ESTIMATOR_INPUT;

typedef struct D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_OUTPUT
{
    ID3D12Resource *pMotionVectorTexture2D;
    D3D12_RESOURCE_COORDINATE MotionVectorCoordinate;
} D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_OUTPUT;

typedef struct D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_INPUT
{
    ID3D12VideoMotionVectorHeap *pMotionVectorHeap;
    UINT PixelWidth;
    UINT PixelHeight;
} D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_INPUT;

[
    uuid(8455293a-0cbd-4831-9b39-fbdbab724723),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoEncodeCommandList : ID3D12CommandList
{
    HRESULT Close();

    HRESULT Reset(
        [in] ID3D12CommandAllocator *allocator
    );

    void ClearState();

    void ResourceBarrier(
        [in] UINT barriers_count,
        [in] const D3D12_RESOURCE_BARRIER *barriers
    );

    void DiscardResource(
        [in] ID3D12Resource *resource,
        [in, optional] const D3D12_DISCARD_REGION *region
    );

    void BeginQuery(
        [in] ID3D12QueryHeap *query_heap,
        [in] D3D12_QUERY_TYPE type,
        [in] UINT index
    );

    void EndQuery(
        [in] ID3D12QueryHeap *query_heap,
        [in] D3D12_QUERY_TYPE type,
        [in] UINT index
    );

    void ResolveQueryData(
        [in] ID3D12QueryHeap *query_heap,
        [in] D3D12_QUERY_TYPE type,
        [in] UINT start_index,
        [in] UINT queries_count,
        [in] ID3D12Resource *destination_buffer,
        [in] UINT64 aligned_destination_buffer_offset
    );

    void SetPredication(
        [in, optional] ID3D12Resource *buffer,
        [in] UINT64 aligned_buffer_offset,
        [in] D3D12_PREDICATION_OP operation
    );

    void SetMarker(
        [in] UINT metadata,
        [in] const void *data,
        [in] UINT size
    );

    void BeginEvent(
        [in] UINT metadata,
        [in] const void *data,
        [in] UINT size
    );

    void EndEvent();

    void EstimateMotion(
        [in] ID3D12VideoMotionEstimator *motion_estimator,
        [in] const D3D12_VIDEO_MOTION_ESTIMATOR_OUTPUT *output_arguments,
        [in] const D3D12_VIDEO_MOTION_ESTIMATOR_INPUT *input_arguments
    );

    void ResolveMotionVectorHeap(
        [in] const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_OUTPUT *output_arguments,
        [in] const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_INPUT *input_arguments
    );

    void WriteBufferImmediate(
        [in] UINT count,
        [in] const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,
        [in] const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes
    );

    void SetProtectedResourceSession(
        [in, optional]ID3D12ProtectedResourceSession *protected_resource_session
    );
}

typedef struct D3D12_VIDEO_EXTENSION_COMMAND_DESC
{
    UINT NodeMask;
    GUID CommandId;
} D3D12_VIDEO_EXTENSION_COMMAND_DESC;

[
    uuid(554e41e8-ae8e-4a8c-b7d2-5b4f274a30e4),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoExtensionCommand : ID3D12Pageable
{
    D3D12_VIDEO_EXTENSION_COMMAND_DESC GetDesc();

    HRESULT GetProtectedResourceSession(
        [in] REFIID riid,
        [out, iid_is(riid)] void **protected_session
    );
}

[
    uuid(f019ac49-f838-4a95-9b17-579437c8f513),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoDevice2 : ID3D12VideoDevice1
{
    HRESULT CreateVideoDecoder1(
        [in] const D3D12_VIDEO_DECODER_DESC *desc,
        [in, optional] ID3D12ProtectedResourceSession *protected_resource_session,
        [in] REFIID riid,
        [out, iid_is(riid)] void **video_decoder
    );

    HRESULT CreateVideoDecoderHeap1(
        [in] const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,
        [in, optional] ID3D12ProtectedResourceSession *protected_resource_session,
        [in] REFIID riid,
        [out, iid_is(riid)] void **video_decoder_heap
    );

    HRESULT CreateVideoProcessor1(
        [in] UINT node_mask,
        [in] const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,
        [in] UINT input_stream_descs_count,
        [in] const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,
        [in, optional] ID3D12ProtectedResourceSession *protected_resource_session,
        [in] REFIID riid,
        [out, iid_is(riid)] void **video_processor
    );

    HRESULT CreateVideoExtensionCommand(
        [in] const D3D12_VIDEO_EXTENSION_COMMAND_DESC *desc,
        [in] const void *creation_parameters,
        [in] SIZE_T creation_parameters_data_size_in_bytes,
        [in, optional] ID3D12ProtectedResourceSession *protected_resource_session,
        [in] REFIID riid,
        [out] void **video_extension_command
    );

    HRESULT ExecuteExtensionCommand(
        [in] ID3D12VideoExtensionCommand *extension_command,
        [in] const void *execution_parameters,
        [in] SIZE_T execution_parameters_size_in_bytes,
        [out] void *output_data,
        [in] SIZE_T output_data_size_in_bytes
    );
}

[
    uuid(94971eca-2bdb-4769-88cf-3675ea757ebc),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoEncodeCommandList1 : ID3D12VideoEncodeCommandList
{
    void InitializeExtensionCommand(
        [in] ID3D12VideoExtensionCommand *extension_command,
        [in] const void *initialization_parameters,
        [in] SIZE_T initialization_parameters_size_in_bytes
    );

    void ExecuteExtensionCommand(
        [in] ID3D12VideoExtensionCommand *extension_command,
        [in] const void *execution_parameters,
        [in] SIZE_T execution_parameters_size_in_bytes
    );
}

cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_MPEG2, 0xee27417f, 0x5e28, 0x4e65, 0xbe, 0xea, 0x1d, 0x26, 0xb5, 0x08, 0xad, 0xc9);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_MPEG1_AND_MPEG2, 0x86695f12, 0x340e, 0x4f04, 0x9f, 0xd3, 0x92, 0x53, 0xdd, 0x32, 0x74, 0x60);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_H264, 0x1b81be68, 0xa0c7, 0x11d3, 0xb9, 0x84, 0x00, 0xc0, 0x4f, 0x2e, 0x73, 0xc5);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_H264_STEREO_PROGRESSIVE, 0xd79be8da, 0x0cf1, 0x4c81, 0xb8, 0x2a, 0x69, 0xa4, 0xe2, 0x36, 0xf4, 0x3d);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_H264_STEREO, 0xf9aaccbb, 0xc2b6, 0x4cfc, 0x87, 0x79, 0x57, 0x07, 0xb1, 0x76, 0x05, 0x52);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_H264_MULTIVIEW, 0x705b9d82, 0x76cf, 0x49d6, 0xb7, 0xe6, 0xac, 0x88, 0x72, 0xdb, 0x01, 0x3c);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_VC1, 0x1b81beA3, 0xa0c7, 0x11d3, 0xb9, 0x84, 0x00, 0xc0, 0x4f, 0x2e, 0x73, 0xc5);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_VC1_D2010, 0x1b81beA4, 0xa0c7, 0x11d3, 0xb9, 0x84, 0x00, 0xc0, 0x4f, 0x2e, 0x73, 0xc5);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_MPEG4PT2_SIMPLE, 0xefd64d74, 0xc9e8,0x41d7,0xa5,0xe9,0xe9,0xb0,0xe3,0x9f,0xa3,0x19);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_MPEG4PT2_ADVSIMPLE_NOGMC, 0xed418a9f, 0x010d, 0x4eda, 0x9a, 0xe3, 0x9a, 0x65, 0x35, 0x8d, 0x8d, 0x2e);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_HEVC_MAIN, 0x5b11d51b, 0x2f4c, 0x4452, 0xbc, 0xc3, 0x09, 0xf2, 0xa1, 0x16, 0x0c, 0xc0);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_HEVC_MAIN10, 0x107af0e0, 0xef1a, 0x4d19, 0xab, 0xa8, 0x67, 0xa1, 0x63, 0x07, 0x3d, 0x13);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_VP9, 0x463707f8, 0xa1d0, 0x4585, 0x87, 0x6d, 0x83, 0xaa, 0x6d, 0x60, 0xb8, 0x9e);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_VP9_10BIT_PROFILE2, 0xa4c749ef, 0x6ecf, 0x48aa, 0x84, 0x48, 0x50, 0xa7, 0xa1, 0x16, 0x5f, 0xf7);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_VP8, 0x90b899ea, 0x3a62, 0x4705, 0x88, 0xb3, 0x8d, 0xf0, 0x4b, 0x27, 0x44, 0xe7);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_AV1_PROFILE0, 0xb8be4ccb, 0xcf53, 0x46ba, 0x8d, 0x59, 0xd6, 0xb8, 0xa6, 0xda, 0x5d, 0x2a);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_AV1_PROFILE1, 0x6936ff0f, 0x45b1, 0x4163, 0x9c, 0xc1, 0x64, 0x6e, 0xf6, 0x94, 0x61, 0x08);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_AV1_PROFILE2, 0x0c5f2aa1, 0xe541, 0x4089, 0xbb, 0x7b, 0x98, 0x11, 0x0a, 0x19, 0xd7, 0xc8);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_AV1_12BIT_PROFILE2, 0x17127009, 0xa00f, 0x4ce1, 0x99, 0x4e, 0xbf, 0x40, 0x81, 0xf6, 0xf3, 0xf0);")
cpp_quote("DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_AV1_12BIT_PROFILE2_420, 0x2d80bed6, 0x9cac, 0x4835, 0x9e, 0x91, 0x32, 0x7b, 0xbc, 0x4f, 0x9e, 0xe8);")

typedef enum D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE
{
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE_ABSOLUTE_QP_MAP = 0,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE_CQP  = 1,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE_CBR  = 2,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE_VBR  = 3,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE_QVBR = 4,
} D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE;

typedef enum D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAGS
{
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_ENABLE_DELTA_QP = 0x1,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_ENABLE_FRAME_ANALYSIS = 0x2,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_ENABLE_QP_RANGE = 0x4,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_ENABLE_INITIAL_QP = 0x8,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_ENABLE_MAX_FRAME_SIZE = 0x10,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_ENABLE_VBV_SIZES = 0x20,
} D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAGS);")

typedef struct D3D12_VIDEO_ENCODER_RATE_CONTROL_CQP
{
    UINT ConstantQP_FullIntracodedFrame;
    UINT ConstantQP_InterPredictedFrame_PrevRefOnly;
    UINT ConstantQP_InterPredictedFrame_BiDirectionalRef;
} D3D12_VIDEO_ENCODER_RATE_CONTROL_CQP;

typedef struct D3D12_VIDEO_ENCODER_RATE_CONTROL_CBR
{
    UINT InitialQP;
    UINT MinQP;
    UINT MaxQP;
    UINT64 MaxFrameBitSize;
    UINT64 TargetBitRate;
    UINT64 VBVCapacity;
    UINT64 InitialVBVFullness;
} D3D12_VIDEO_ENCODER_RATE_CONTROL_CBR;

typedef struct D3D12_VIDEO_ENCODER_RATE_CONTROL_VBR
{
    UINT InitialQP;
    UINT MinQP;
    UINT MaxQP;
    UINT64 MaxFrameBitSize;
    UINT64 TargetAvgBitRate;
    UINT64 PeakBitRate;
    UINT64 VBVCapacity;
    UINT64 InitialVBVFullness;
} D3D12_VIDEO_ENCODER_RATE_CONTROL_VBR;

typedef struct D3D12_VIDEO_ENCODER_RATE_CONTROL_QVBR
{
    UINT InitialQP;
    UINT MinQP;
    UINT MaxQP;
    UINT64 MaxFrameBitSize;
    UINT64 TargetAvgBitRate;
    UINT64 PeakBitRate;
    UINT ConstantQualityTarget;
} D3D12_VIDEO_ENCODER_RATE_CONTROL_QVBR;

typedef struct D3D12_VIDEO_ENCODER_RATE_CONTROL_CONFIGURATION_PARAMS
{
    UINT DataSize;
    union
    {
        const D3D12_VIDEO_ENCODER_RATE_CONTROL_CQP *pConfiguration_CQP;
        const D3D12_VIDEO_ENCODER_RATE_CONTROL_CBR *pConfiguration_CBR;
        const D3D12_VIDEO_ENCODER_RATE_CONTROL_VBR *pConfiguration_VBR;
        const D3D12_VIDEO_ENCODER_RATE_CONTROL_QVBR *pConfiguration_QVBR;
    };
} D3D12_VIDEO_ENCODER_RATE_CONTROL_CONFIGURATION_PARAMS;

typedef struct D3D12_VIDEO_ENCODER_RATE_CONTROL
{
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE Mode;
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAGS Flags;
    D3D12_VIDEO_ENCODER_RATE_CONTROL_CONFIGURATION_PARAMS ConfigParams;
    DXGI_RATIONAL TargetFrameRate;
} D3D12_VIDEO_ENCODER_RATE_CONTROL;

typedef enum D3D12_VIDEO_ENCODER_CODEC
{
    D3D12_VIDEO_ENCODER_CODEC_H264 = 0,
    D3D12_VIDEO_ENCODER_CODEC_HEVC = 1,
} D3D12_VIDEO_ENCODER_CODEC;

typedef enum D3D12_VIDEO_ENCODER_PROFILE_H264
{
    D3D12_VIDEO_ENCODER_PROFILE_H264_MAIN = 0,
    D3D12_VIDEO_ENCODER_PROFILE_H264_HIGH = 1,
    D3D12_VIDEO_ENCODER_PROFILE_H264_HIGH_10 = 2,
} D3D12_VIDEO_ENCODER_PROFILE_H264;

typedef enum D3D12_VIDEO_ENCODER_PROFILE_HEVC
{
    D3D12_VIDEO_ENCODER_PROFILE_HEVC_MAIN = 0,
    D3D12_VIDEO_ENCODER_PROFILE_HEVC_MAIN10 = 1,
} D3D12_VIDEO_ENCODER_PROFILE_HEVC;

typedef struct D3D12_VIDEO_ENCODER_PROFILE_DESC
{
    UINT DataSize;
    union
    {
        D3D12_VIDEO_ENCODER_PROFILE_H264 *pH264Profile;
        D3D12_VIDEO_ENCODER_PROFILE_HEVC *pHEVCProfile;
    };
} D3D12_VIDEO_ENCODER_PROFILE_DESC;

typedef enum D3D12_VIDEO_ENCODER_LEVELS_H264
{
    D3D12_VIDEO_ENCODER_LEVELS_H264_1 = 0,
    D3D12_VIDEO_ENCODER_LEVELS_H264_1b = 1,
    D3D12_VIDEO_ENCODER_LEVELS_H264_11 = 2,
    D3D12_VIDEO_ENCODER_LEVELS_H264_12 = 3,
    D3D12_VIDEO_ENCODER_LEVELS_H264_13 = 4,
    D3D12_VIDEO_ENCODER_LEVELS_H264_2 = 5,
    D3D12_VIDEO_ENCODER_LEVELS_H264_21 = 6,
    D3D12_VIDEO_ENCODER_LEVELS_H264_22 = 7,
    D3D12_VIDEO_ENCODER_LEVELS_H264_3 = 8,
    D3D12_VIDEO_ENCODER_LEVELS_H264_31 = 9,
    D3D12_VIDEO_ENCODER_LEVELS_H264_32 = 10,
    D3D12_VIDEO_ENCODER_LEVELS_H264_4 = 11,
    D3D12_VIDEO_ENCODER_LEVELS_H264_41 = 12,
    D3D12_VIDEO_ENCODER_LEVELS_H264_42 = 13,
    D3D12_VIDEO_ENCODER_LEVELS_H264_5 = 14,
    D3D12_VIDEO_ENCODER_LEVELS_H264_51 = 15,
    D3D12_VIDEO_ENCODER_LEVELS_H264_52 = 16,
    D3D12_VIDEO_ENCODER_LEVELS_H264_6 = 17,
    D3D12_VIDEO_ENCODER_LEVELS_H264_61 = 18,
    D3D12_VIDEO_ENCODER_LEVELS_H264_62 = 19,
} D3D12_VIDEO_ENCODER_LEVELS_H264;

typedef enum D3D12_VIDEO_ENCODER_TIER_HEVC
{
    D3D12_VIDEO_ENCODER_TIER_HEVC_MAIN = 0,
    D3D12_VIDEO_ENCODER_TIER_HEVC_HIGH = 1,
} D3D12_VIDEO_ENCODER_TIER_HEVC;

typedef enum D3D12_VIDEO_ENCODER_LEVELS_HEVC
{
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_1 = 0,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_2 = 1,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_21 = 2,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_3 = 3,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_31 = 4,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_4 = 5,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_41 = 6,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_5 = 7,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_51 = 8,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_52 = 9,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_6 = 10,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_61 = 11,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_62 = 12,
} D3D12_VIDEO_ENCODER_LEVELS_HEVC;

typedef struct D3D12_VIDEO_ENCODER_LEVEL_TIER_CONSTRAINTS_HEVC
{
    D3D12_VIDEO_ENCODER_LEVELS_HEVC Level;
    D3D12_VIDEO_ENCODER_TIER_HEVC Tier;
} D3D12_VIDEO_ENCODER_LEVEL_TIER_CONSTRAINTS_HEVC;

typedef struct D3D12_VIDEO_ENCODER_LEVEL_SETTING
{
    UINT DataSize;
    union
    {
        D3D12_VIDEO_ENCODER_LEVELS_H264 *pH264LevelSetting;
        D3D12_VIDEO_ENCODER_LEVEL_TIER_CONSTRAINTS_HEVC *pHEVCLevelSetting;
    };
} D3D12_VIDEO_ENCODER_LEVEL_SETTING;

typedef struct D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC
{
    UINT Width;
    UINT Height;
} D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC;

typedef struct D3D12_FEATURE_DATA_VIDEO_ENCODER_RATE_CONTROL_MODE
{
    UINT NodeIndex;
    D3D12_VIDEO_ENCODER_CODEC Codec;
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE RateControlMode;
    BOOL IsSupported;
} D3D12_FEATURE_DATA_VIDEO_ENCODER_RATE_CONTROL_MODE;

typedef enum D3D12_VIDEO_ENCODER_INTRA_REFRESH_MODE
{
    D3D12_VIDEO_ENCODER_INTRA_REFRESH_MODE_NONE = 0,
    D3D12_VIDEO_ENCODER_INTRA_REFRESH_MODE_ROW_BASED = 1,
} D3D12_VIDEO_ENCODER_INTRA_REFRESH_MODE;

typedef enum D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE
{
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE_FULL_FRAME = 0,
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE_BYTES_PER_SUBREGION = 1,
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE_SQUARE_UNITS_PER_SUBREGION_ROW_UNALIGNED  = 2,
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE_UNIFORM_PARTITIONING_ROWS_PER_SUBREGION = 3,
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE_UNIFORM_PARTITIONING_SUBREGIONS_PER_FRAME = 4,
} D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE;

typedef enum D3D12_VIDEO_ENCODER_HEAP_FLAGS
{
    D3D12_VIDEO_ENCODER_HEAP_FLAG_NONE = 0x0,
} D3D12_VIDEO_ENCODER_HEAP_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_HEAP_FLAGS);")

typedef struct D3D12_VIDEO_ENCODER_HEAP_DESC
{
    UINT NodeMask;
    D3D12_VIDEO_ENCODER_HEAP_FLAGS Flags;
    D3D12_VIDEO_ENCODER_CODEC EncodeCodec;
    D3D12_VIDEO_ENCODER_PROFILE_DESC EncodeProfile;
    D3D12_VIDEO_ENCODER_LEVEL_SETTING EncodeLevel;
    UINT ResolutionsListCount;
    const D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC *pResolutionList;
} D3D12_VIDEO_ENCODER_HEAP_DESC;

typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAGS
{
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_CABAC_ENCODING_SUPPORT = 0x1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_INTRA_SLICE_CONSTRAINED_ENCODING_SUPPORT = 0x2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_BFRAME_LTR_COMBINED_SUPPORT = 0x4,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_ADAPTIVE_8x8_TRANSFORM_ENCODING_SUPPORT = 0x8,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_DIRECT_SPATIAL_ENCODING_SUPPORT = 0x10,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_DIRECT_TEMPORAL_ENCODING_SUPPORT = 0x20,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_CONSTRAINED_INTRAPREDICTION_SUPPORT = 0x40,
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAGS);")

typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODES
{
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_0_ALL_LUMA_CHROMA_SLICE_BLOCK_EDGES_ALWAYS_FILTERED = 0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_1_DISABLE_ALL_SLICE_BLOCK_EDGES = 1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_2_DISABLE_SLICE_BOUNDARIES_BLOCKS = 2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_3_USE_TWO_STAGE_DEBLOCKING = 3,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_4_DISABLE_CHROMA_BLOCK_EDGES = 4,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_5_DISABLE_CHROMA_BLOCK_EDGES_AND_LUMA_BOUNDARIES = 5,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_6_DISABLE_CHROMA_BLOCK_EDGES_AND_USE_LUMA_TWO_STAGE_DEBLOCKING = 6,
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODES;

typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAGS
{
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_0_ALL_LUMA_CHROMA_SLICE_BLOCK_EDGES_ALWAYS_FILTERED = (1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_0_ALL_LUMA_CHROMA_SLICE_BLOCK_EDGES_ALWAYS_FILTERED),
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_1_DISABLE_ALL_SLICE_BLOCK_EDGES = (1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_1_DISABLE_ALL_SLICE_BLOCK_EDGES),
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_2_DISABLE_SLICE_BOUNDARIES_BLOCKS = (1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_2_DISABLE_SLICE_BOUNDARIES_BLOCKS),
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_3_USE_TWO_STAGE_DEBLOCKING = (1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_3_USE_TWO_STAGE_DEBLOCKING),
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_4_DISABLE_CHROMA_BLOCK_EDGES = (1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_4_DISABLE_CHROMA_BLOCK_EDGES),
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_5_DISABLE_CHROMA_BLOCK_EDGES_AND_LUMA_BOUNDARIES = (1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_5_DISABLE_CHROMA_BLOCK_EDGES_AND_LUMA_BOUNDARIES),
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_6_DISABLE_CHROMA_BLOCK_EDGES_AND_USE_LUMA_TWO_STAGE_DEBLOCKING = (1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_6_DISABLE_CHROMA_BLOCK_EDGES_AND_USE_LUMA_TWO_STAGE_DEBLOCKING),
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAGS);")

typedef struct D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264
{
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAGS SupportFlags;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAGS DisableDeblockingFilterSupportedModes;
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264;

typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAGS
{
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_BFRAME_LTR_COMBINED_SUPPORT = 0x1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_INTRA_SLICE_CONSTRAINED_ENCODING_SUPPORT = 0x2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_CONSTRAINED_INTRAPREDICTION_SUPPORT = 0x4,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_SAO_FILTER_SUPPORT = 0x8,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_ASYMETRIC_MOTION_PARTITION_SUPPORT = 0x10,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_ASYMETRIC_MOTION_PARTITION_REQUIRED = 0x20,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_TRANSFORM_SKIP_SUPPORT = 0x40,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_DISABLING_LOOP_FILTER_ACROSS_SLICES_SUPPORT = 0x80,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_P_FRAMES_IMPLEMENTED_AS_LOW_DELAY_B_FRAMES = 0x100,
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAGS);")

typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE_8x8 = 0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE_16x16 = 1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE_32x32 = 2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE_64x64 = 3,
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE;

typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE_4x4 = 0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE_8x8 = 1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE_16x16 = 2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE_32x32 = 3,
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE;

typedef struct D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC
{
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAGS SupportFlags;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE MinLumaCodingUnitSize;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE MaxLumaCodingUnitSize;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE MinLumaTransformUnitSize;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE MaxLumaTransformUnitSize;
    UCHAR max_transform_hierarchy_depth_inter;
    UCHAR max_transform_hierarchy_depth_intra;
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC;

typedef struct D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT
{
    UINT DataSize;
    union
    {
        D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264 *pH264Support;
        D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC *pHEVCSupport;
    };
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT;

typedef struct D3D12_FEATURE_DATA_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT
{
    UINT NodeIndex;
    D3D12_VIDEO_ENCODER_CODEC Codec;
    D3D12_VIDEO_ENCODER_PROFILE_DESC Profile;
    BOOL IsSupported;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT CodecSupportLimits;
} D3D12_FEATURE_DATA_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT;

typedef struct D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT_H264
{
    UINT MaxL0ReferencesForP;
    UINT MaxL0ReferencesForB;
    UINT MaxL1ReferencesForB;
    UINT MaxLongTermReferences;
    UINT MaxDPBCapacity;
} D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT_H264;

typedef struct D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT_HEVC
{
    UINT MaxL0ReferencesForP;
    UINT MaxL0ReferencesForB;
    UINT MaxL1ReferencesForB;
    UINT MaxLongTermReferences;
    UINT MaxDPBCapacity;
} D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT_HEVC;

typedef struct D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT
{
    UINT DataSize;
    union
    {
        D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT_H264 *pH264Support;
        D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT_HEVC *pHEVCSupport;
    };
} D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT;

typedef struct D3D12_FEATURE_DATA_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT
{
    UINT NodeIndex;
    D3D12_VIDEO_ENCODER_CODEC Codec;
    D3D12_VIDEO_ENCODER_PROFILE_DESC Profile;
    BOOL IsSupported;
    D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT PictureSupport;
} D3D12_FEATURE_DATA_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT;

typedef enum D3D12_VIDEO_ENCODER_SUPPORT_FLAGS
{
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_GENERAL_SUPPORT_OK = 0x1,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_RECONFIGURATION_AVAILABLE = 0x2,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RESOLUTION_RECONFIGURATION_AVAILABLE = 0x4,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_VBV_SIZE_CONFIG_AVAILABLE = 0x8,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_FRAME_ANALYSIS_AVAILABLE = 0x10,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RECONSTRUCTED_FRAMES_REQUIRE_TEXTURE_ARRAYS = 0x20,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_DELTA_QP_AVAILABLE = 0x40,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_SUBREGION_LAYOUT_RECONFIGURATION_AVAILABLE = 0x80,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_ADJUSTABLE_QP_RANGE_AVAILABLE = 0x100,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_INITIAL_QP_AVAILABLE = 0x200,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_MAX_FRAME_SIZE_AVAILABLE = 0x400,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_SEQUENCE_GOP_RECONFIGURATION_AVAILABLE = 0x800,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_MOTION_ESTIMATION_PRECISION_MODE_LIMIT_AVAILABLE = 0x1000,
} D3D12_VIDEO_ENCODER_SUPPORT_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_SUPPORT_FLAGS);")

typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAGS
{
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAG_USE_CONSTRAINED_INTRAPREDICTION = 0x1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAG_USE_ADAPTIVE_8x8_TRANSFORM = 0x2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAG_ENABLE_CABAC_ENCODING = 0x4,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAG_ALLOW_REQUEST_INTRA_CONSTRAINED_SLICES = 0x8,
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAGS);")

typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_DIRECT_MODES {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_DIRECT_MODES_DISABLED = 0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_DIRECT_MODES_TEMPORAL = 1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_DIRECT_MODES_SPATIAL = 2,
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_DIRECT_MODES;

typedef struct D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264
{
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAGS ConfigurationFlags;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_DIRECT_MODES DirectModeConfig;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODES DisableDeblockingFilterConfig;
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264;

typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAGS
{
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_DISABLE_LOOP_FILTER_ACROSS_SLICES = 0x1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_ALLOW_REQUEST_INTRA_CONSTRAINED_SLICES = 0x2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_ENABLE_SAO_FILTER = 0x4,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_ENABLE_LONG_TERM_REFERENCES = 0x8,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_USE_ASYMETRIC_MOTION_PARTITION = 0x10,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_ENABLE_TRANSFORM_SKIPPING = 0x20,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_USE_CONSTRAINED_INTRAPREDICTION = 0x40,
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAGS);")

typedef struct D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAGS ConfigurationFlags;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE MinLumaCodingUnitSize;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE MaxLumaCodingUnitSize;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE MinLumaTransformUnitSize;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE MaxLumaTransformUnitSize;
    UCHAR max_transform_hierarchy_depth_inter;
    UCHAR max_transform_hierarchy_depth_intra;
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC;

typedef struct D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION
{
    UINT DataSize;
    union
    {
        D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264 *pH264Config;
        D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC *pHEVCConfig;
    };
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION;

typedef struct D3D12_VIDEO_ENCODER_INTRA_REFRESH
{
    D3D12_VIDEO_ENCODER_INTRA_REFRESH_MODE Mode;
    UINT IntraRefreshDuration;
} D3D12_VIDEO_ENCODER_INTRA_REFRESH;

typedef enum D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE
{
    D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE_MAXIMUM = 0,
    D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE_FULL_PIXEL = 1,
    D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE_HALF_PIXEL = 2,
    D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE_QUARTER_PIXEL = 3,
} D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE;

typedef struct D3D12_FEATURE_DATA_VIDEO_ENCODER_RESOLUTION_SUPPORT_LIMITS
{
    UINT MaxSubregionsNumber;
    UINT MaxIntraRefreshFrameDuration;
    UINT SubregionBlockPixelsSize;
    UINT QPMapRegionPixelsSize;
} D3D12_FEATURE_DATA_VIDEO_ENCODER_RESOLUTION_SUPPORT_LIMITS;

typedef enum D3D12_VIDEO_ENCODER_VALIDATION_FLAGS
{
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_CODEC_NOT_SUPPORTED = 0x1,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_INPUT_FORMAT_NOT_SUPPORTED = 0x8,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_CODEC_CONFIGURATION_NOT_SUPPORTED = 0x10,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_RATE_CONTROL_MODE_NOT_SUPPORTED = 0x20,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_RATE_CONTROL_CONFIGURATION_NOT_SUPPORTED = 0x40,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_INTRA_REFRESH_MODE_NOT_SUPPORTED = 0x80,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_SUBREGION_LAYOUT_MODE_NOT_SUPPORTED = 0x100,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_RESOLUTION_NOT_SUPPORTED_IN_LIST = 0x200,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_GOP_STRUCTURE_NOT_SUPPORTED = 0x800,
} D3D12_VIDEO_ENCODER_VALIDATION_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_VALIDATION_FLAGS);")

typedef struct D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE_H264
{
    UINT GOPLength;
    UINT PPicturePeriod;
    UCHAR pic_order_cnt_type;
    UCHAR log2_max_frame_num_minus4;
    UCHAR log2_max_pic_order_cnt_lsb_minus4;
} D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE_H264;

typedef struct D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE_HEVC
{
    UINT GOPLength;
    UINT PPicturePeriod;
    UCHAR log2_max_pic_order_cnt_lsb_minus4;
} D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE_HEVC;

typedef struct D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE
{
    UINT DataSize;
    union
    {
        D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE_H264 *pH264GroupOfPictures;
        D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE_HEVC *pHEVCGroupOfPictures;
    };
} D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE;

typedef struct D3D12_FEATURE_DATA_VIDEO_ENCODER_SUPPORT
{
    UINT NodeIndex;
    D3D12_VIDEO_ENCODER_CODEC Codec;
    DXGI_FORMAT InputFormat;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION CodecConfiguration;
    D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE CodecGopSequence;
    D3D12_VIDEO_ENCODER_RATE_CONTROL RateControl;
    D3D12_VIDEO_ENCODER_INTRA_REFRESH_MODE IntraRefresh;
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE SubregionFrameEncoding;
    UINT ResolutionsListCount;
    const D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC *pResolutionList;
    UINT MaxReferenceFramesInDPB;
    D3D12_VIDEO_ENCODER_VALIDATION_FLAGS ValidationFlags;
    D3D12_VIDEO_ENCODER_SUPPORT_FLAGS SupportFlags;
    D3D12_VIDEO_ENCODER_PROFILE_DESC SuggestedProfile;
    D3D12_VIDEO_ENCODER_LEVEL_SETTING SuggestedLevel;
    D3D12_FEATURE_DATA_VIDEO_ENCODER_RESOLUTION_SUPPORT_LIMITS *pResolutionDependentSupport;
} D3D12_FEATURE_DATA_VIDEO_ENCODER_SUPPORT;

typedef struct D3D12_FEATURE_DATA_VIDEO_ENCODER_RESOURCE_REQUIREMENTS
{
    UINT NodeIndex;
    D3D12_VIDEO_ENCODER_CODEC Codec;
    D3D12_VIDEO_ENCODER_PROFILE_DESC Profile;
    DXGI_FORMAT InputFormat;
    D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC PictureTargetResolution;
    BOOL IsSupported;
    UINT CompressedBitstreamBufferAccessAlignment;
    UINT EncoderMetadataBufferAccessAlignment;
    UINT MaxEncoderOutputMetadataBufferSize;
} D3D12_FEATURE_DATA_VIDEO_ENCODER_RESOURCE_REQUIREMENTS;

typedef enum D3D12_VIDEO_ENCODER_FLAGS
{
    D3D12_VIDEO_ENCODER_FLAG_NONE = 0x0,
} D3D12_VIDEO_ENCODER_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_FLAGS);")

typedef struct D3D12_VIDEO_ENCODER_DESC
{
    UINT NodeMask;
    D3D12_VIDEO_ENCODER_FLAGS Flags;
    D3D12_VIDEO_ENCODER_CODEC EncodeCodec;
    D3D12_VIDEO_ENCODER_PROFILE_DESC EncodeProfile;
    DXGI_FORMAT InputFormat;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION CodecConfiguration;
    D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE MaxMotionEstimationPrecision;
} D3D12_VIDEO_ENCODER_DESC;

[
    uuid(2e0d212d-8df9-44a6-a770-bb289b182737),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoEncoder : ID3D12Pageable
{
    UINT GetNodeMask();

    D3D12_VIDEO_ENCODER_FLAGS GetEncoderFlags();

    D3D12_VIDEO_ENCODER_CODEC GetCodec();

    HRESULT GetCodecProfile(
        [in, out] D3D12_VIDEO_ENCODER_PROFILE_DESC dst_profile
    );

    HRESULT GetCodecConfiguration(
        [in, out] D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION dst_codec_config
    );

    DXGI_FORMAT GetInputFormat();

    D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE GetMaxMotionEstimationPrecision();
}

[
    uuid(22b35d96-876a-44c0-b25e-fb8c9c7f1c4a),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoEncoderHeap : ID3D12Pageable
{
    UINT GetNodeMask();

    D3D12_VIDEO_ENCODER_HEAP_FLAGS GetEncoderHeapFlags();

    D3D12_VIDEO_ENCODER_CODEC GetCodec();

    HRESULT GetCodecProfile(
        [in, out] D3D12_VIDEO_ENCODER_PROFILE_DESC dst_profile
    );

    HRESULT GetCodecLevel(
        [in, out] D3D12_VIDEO_ENCODER_LEVEL_SETTING dst_level
    );

    UINT GetResolutionListCount();

    HRESULT GetResolutionList(
        [in] const UINT resolutions_list_count,
        [out] D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC *resolution_list
    );
}

[
    uuid(4243adb4-3a32-4666-973c-0ccc5625dc44),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoDevice3 : ID3D12VideoDevice2
{
    HRESULT CreateVideoEncoder(
        [in] const D3D12_VIDEO_ENCODER_DESC *desc,
        [in] REFIID riid,
        [out] void **video_encoder
    );

    HRESULT CreateVideoEncoderHeap(
        [in] const D3D12_VIDEO_ENCODER_HEAP_DESC *desc,
        [in] REFIID riid,
        [out] void **video_encoder_heap
    );
}

typedef enum D3D12_VIDEO_ENCODER_FRAME_TYPE_H264
{
    D3D12_VIDEO_ENCODER_FRAME_TYPE_H264_I_FRAME = 0,
    D3D12_VIDEO_ENCODER_FRAME_TYPE_H264_P_FRAME = 1,
    D3D12_VIDEO_ENCODER_FRAME_TYPE_H264_B_FRAME = 2,
    D3D12_VIDEO_ENCODER_FRAME_TYPE_H264_IDR_FRAME = 3,
} D3D12_VIDEO_ENCODER_FRAME_TYPE_H264;

typedef struct D3D12_VIDEO_ENCODER_REFERENCE_PICTURE_DESCRIPTOR_H264
{
    UINT ReconstructedPictureResourceIndex;
    BOOL IsLongTermReference;
    UINT LongTermPictureIdx;
    UINT PictureOrderCountNumber;
    UINT FrameDecodingOrderNumber;
    UINT TemporalLayerIndex;
} D3D12_VIDEO_ENCODER_REFERENCE_PICTURE_DESCRIPTOR_H264;

typedef enum D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_FLAGS
{
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_FLAG_REQUEST_INTRA_CONSTRAINED_SLICES = 0x1,
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_FLAGS);")

typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_MARKING_OPERATION
{
    UCHAR memory_management_control_operation;
    UINT difference_of_pic_nums_minus1;
    UINT long_term_pic_num;
    UINT long_term_frame_idx;
    UINT max_long_term_frame_idx_plus1;
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_MARKING_OPERATION;

typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_LIST_MODIFICATION_OPERATION
{
    UCHAR modification_of_pic_nums_idc;
    UINT abs_diff_pic_num_minus1;
    UINT long_term_pic_num;
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_LIST_MODIFICATION_OPERATION;

typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264
{
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_FLAGS Flags;
    D3D12_VIDEO_ENCODER_FRAME_TYPE_H264 FrameType;
    UINT pic_parameter_set_id;
    UINT idr_pic_id;
    UINT PictureOrderCountNumber;
    UINT FrameDecodingOrderNumber;
    UINT TemporalLayerIndex;
    UINT List0ReferenceFramesCount;
    UINT *pList0ReferenceFrames;
    UINT List1ReferenceFramesCount;
    UINT *pList1ReferenceFrames;
    UINT ReferenceFramesReconPictureDescriptorsCount;
    D3D12_VIDEO_ENCODER_REFERENCE_PICTURE_DESCRIPTOR_H264 *pReferenceFramesReconPictureDescriptors;
    UCHAR adaptive_ref_pic_marking_mode_flag;
    UINT RefPicMarkingOperationsCommandsCount;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_MARKING_OPERATION *pRefPicMarkingOperationsCommands;
    UINT List0RefPicModificationsCount;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_LIST_MODIFICATION_OPERATION *pList0RefPicModifications;
    UINT List1RefPicModificationsCount;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_LIST_MODIFICATION_OPERATION *pList1RefPicModifications;
    UINT QPMapValuesCount;
    INT8 *pRateControlQPMap;
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264;

typedef enum D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC
{
    D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC_I_FRAME = 0,
    D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC_P_FRAME = 1,
    D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC_B_FRAME = 2,
    D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC_IDR_FRAME = 3,
} D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC;

typedef struct D3D12_VIDEO_ENCODER_REFERENCE_PICTURE_DESCRIPTOR_HEVC
{
    UINT ReconstructedPictureResourceIndex;
    BOOL IsRefUsedByCurrentPic;
    BOOL IsLongTermReference;
    UINT PictureOrderCountNumber;
    UINT TemporalLayerIndex;
} D3D12_VIDEO_ENCODER_REFERENCE_PICTURE_DESCRIPTOR_HEVC;

typedef enum D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC_FLAGS
{
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC_FLAG_REQUEST_INTRA_CONSTRAINED_SLICES = 0x1,
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC_FLAGS);")

typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC
{
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC_FLAGS Flags;
    D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC FrameType;
    UINT slice_pic_parameter_set_id;
    UINT PictureOrderCountNumber;
    UINT TemporalLayerIndex;
    UINT List0ReferenceFramesCount;
    UINT *pList0ReferenceFrames;
    UINT List1ReferenceFramesCount;
    UINT *pList1ReferenceFrames;
    UINT ReferenceFramesReconPictureDescriptorsCount;
    D3D12_VIDEO_ENCODER_REFERENCE_PICTURE_DESCRIPTOR_HEVC *pReferenceFramesReconPictureDescriptors;
    UINT List0RefPicModificationsCount;
    UINT *pList0RefPicModifications;
    UINT List1RefPicModificationsCount;
    UINT *pList1RefPicModifications;
    UINT QPMapValuesCount;
    INT8 *pRateControlQPMap;
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC;

typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA
{
    UINT DataSize;
    union
    {
        D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264 *pH264PicData;
        D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC *pHEVCPicData;
    };
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA;

typedef struct D3D12_VIDEO_ENCODE_REFERENCE_FRAMES
{
    UINT NumTexture2Ds;
    ID3D12Resource **ppTexture2Ds;
    UINT *pSubresources;
} D3D12_VIDEO_ENCODE_REFERENCE_FRAMES;

typedef enum D3D12_VIDEO_ENCODER_PICTURE_CONTROL_FLAGS
{
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_FLAG_USED_AS_REFERENCE_PICTURE = 0x1,
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_PICTURE_CONTROL_FLAGS);")

typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_DESC
{
    UINT IntraRefreshFrameIndex;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_FLAGS Flags;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA PictureControlCodecData;
    D3D12_VIDEO_ENCODE_REFERENCE_FRAMES ReferenceFrames;
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_DESC;

typedef enum D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAGS
{
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAG_RESOLUTION_CHANGE = 0x1,
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAG_RATE_CONTROL_CHANGE = 0x2,
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAG_SUBREGION_LAYOUT_CHANGE = 0x4,
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAG_REQUEST_INTRA_REFRESH = 0x8,
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAG_GOP_SEQUENCE_CHANGE = 0x10,
} D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAGS);")

typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA_SLICES
{
    union
    {
        UINT MaxBytesPerSlice;
        UINT NumberOfCodingUnitsPerSlice;
        UINT NumberOfRowsPerSlice;
        UINT NumberOfSlicesPerFrame;
    };
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA_SLICES;

typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA
{
    UINT DataSize;
    union
    {
        const D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA_SLICES *pSlicesPartition_H264;
        const D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA_SLICES *pSlicesPartition_HEVC;
    };
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA;

typedef struct D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_DESC
{
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAGS Flags;
    D3D12_VIDEO_ENCODER_INTRA_REFRESH IntraRefreshConfig;
    D3D12_VIDEO_ENCODER_RATE_CONTROL RateControl;
    D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC PictureTargetResolution;
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE SelectedLayoutMode;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA FrameSubregionsLayoutData;
    D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE CodecGopSequence;
} D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_DESC;

typedef struct D3D12_VIDEO_ENCODER_ENCODEFRAME_INPUT_ARGUMENTS
{
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_DESC SequenceControlDesc;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_DESC PictureControlDesc;
    ID3D12Resource *pInputFrame;
    UINT InputFrameSubresource;
    UINT CurrentFrameBitstreamMetadataSize;
} D3D12_VIDEO_ENCODER_ENCODEFRAME_INPUT_ARGUMENTS;

typedef struct D3D12_VIDEO_ENCODER_COMPRESSED_BITSTREAM
{
    ID3D12Resource *pBuffer;
    UINT64 FrameStartOffset;
} D3D12_VIDEO_ENCODER_COMPRESSED_BITSTREAM;

typedef struct D3D12_VIDEO_ENCODER_RECONSTRUCTED_PICTURE
{
    ID3D12Resource *pReconstructedPicture;
    UINT ReconstructedPictureSubresource;
} D3D12_VIDEO_ENCODER_RECONSTRUCTED_PICTURE;

typedef struct D3D12_VIDEO_ENCODER_FRAME_SUBREGION_METADATA
{
    UINT64 bSize;
    UINT64 bStartOffset;
    UINT64 bHeaderSize;
} D3D12_VIDEO_ENCODER_FRAME_SUBREGION_METADATA;

typedef enum D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAGS
{
    D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAG_NO_ERROR = 0x0,
    D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAG_CODEC_PICTURE_CONTROL_NOT_SUPPORTED = 0x1,
    D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAG_SUBREGION_LAYOUT_CONFIGURATION_NOT_SUPPORTED = 0x2,
    D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAG_INVALID_REFERENCE_PICTURES = 0x4,
    D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAG_RECONFIGURATION_REQUEST_NOT_SUPPORTED = 0x8,
    D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAG_INVALID_METADATA_BUFFER_SOURCE = 0x10,
} D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAGS;
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAGS);")

typedef struct D3D12_VIDEO_ENCODER_OUTPUT_METADATA_STATISTICS
{
    UINT64 AverageQP;
    UINT64 IntraCodingUnitsCount;
    UINT64 InterCodingUnitsCount;
    UINT64 SkipCodingUnitsCount;
    UINT64 AverageMotionEstimationXDirection;
    UINT64 AverageMotionEstimationYDirection;
} D3D12_VIDEO_ENCODER_OUTPUT_METADATA_STATISTICS;

typedef struct D3D12_VIDEO_ENCODER_OUTPUT_METADATA
{
    UINT64 EncodeErrorFlags;
    D3D12_VIDEO_ENCODER_OUTPUT_METADATA_STATISTICS EncodeStats;
    UINT64 EncodedBitstreamWrittenBytesCount;
    UINT64 WrittenSubregionsCount;
} D3D12_VIDEO_ENCODER_OUTPUT_METADATA;

typedef struct D3D12_VIDEO_ENCODER_ENCODE_OPERATION_METADATA_BUFFER
{
    ID3D12Resource *pBuffer;
    UINT64 Offset;
} D3D12_VIDEO_ENCODER_ENCODE_OPERATION_METADATA_BUFFER;

typedef struct D3D12_VIDEO_ENCODER_RESOLVE_METADATA_INPUT_ARGUMENTS
{
    D3D12_VIDEO_ENCODER_CODEC EncoderCodec;
    D3D12_VIDEO_ENCODER_PROFILE_DESC EncoderProfile;
    DXGI_FORMAT EncoderInputFormat;
    D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC EncodedPictureEffectiveResolution;
    D3D12_VIDEO_ENCODER_ENCODE_OPERATION_METADATA_BUFFER HWLayoutMetadata;
} D3D12_VIDEO_ENCODER_RESOLVE_METADATA_INPUT_ARGUMENTS;

typedef struct D3D12_VIDEO_ENCODER_RESOLVE_METADATA_OUTPUT_ARGUMENTS
{
    D3D12_VIDEO_ENCODER_ENCODE_OPERATION_METADATA_BUFFER ResolvedLayoutMetadata;
} D3D12_VIDEO_ENCODER_RESOLVE_METADATA_OUTPUT_ARGUMENTS;

typedef struct D3D12_VIDEO_ENCODER_ENCODEFRAME_OUTPUT_ARGUMENTS
{
    D3D12_VIDEO_ENCODER_COMPRESSED_BITSTREAM Bitstream;
    D3D12_VIDEO_ENCODER_RECONSTRUCTED_PICTURE ReconstructedPicture;
    D3D12_VIDEO_ENCODER_ENCODE_OPERATION_METADATA_BUFFER EncoderOutputMetadata;
} D3D12_VIDEO_ENCODER_ENCODEFRAME_OUTPUT_ARGUMENTS;

[
    uuid(895491e2-e701-46a9-9a1f-8d3480ed867a),
    object,
    local,
    pointer_default(unique)
]
interface ID3D12VideoEncodeCommandList2 : ID3D12VideoEncodeCommandList1
{
    void EncodeFrame(
        [in] ID3D12VideoEncoder *encoder,
        [in] ID3D12VideoEncoderHeap *heap,
        [in] const D3D12_VIDEO_ENCODER_ENCODEFRAME_INPUT_ARGUMENTS *input_arguments,
        [in] const D3D12_VIDEO_ENCODER_ENCODEFRAME_OUTPUT_ARGUMENTS *output_arguments
    );

    void ResolveEncoderOutputMetadata(
        [in] const D3D12_VIDEO_ENCODER_RESOLVE_METADATA_INPUT_ARGUMENTS *input_arguments,
        [in] const D3D12_VIDEO_ENCODER_RESOLVE_METADATA_OUTPUT_ARGUMENTS *output_arguments
    );
}
