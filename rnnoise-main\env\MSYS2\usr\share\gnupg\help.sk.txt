# help.sk.txt - sk GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


.gpg.edit_ownertrust.value
Je na Vás, aby ste sem priradili hodnotu; táto hodnota nebude nikdy
exportovaná tretej strane. Potrebujeme ju k implementácii "pavučiny
dôvery"; nemá to nič spoločné s (implicitne vytvorenou) "pavučinou
certifikátov".
.

.gpg.edit_ownertrust.set_ultimate.okay
Aby bolo možné vybudovať pavučinu dôvery, musí GnuPG vedieť, ktorým kľúčom
dôverujete absolútne - obyčajne sú to tie kľúče, pre ktoré máte prístup
k tajným kľúčom. Odpovedzte "ano", aby ste nastavili tieto kľúče
ako absolútne dôveryhodné

.

.gpg.untrusted_key.override
Pokiaľ aj tak chcete použiť tento nedôveryhodný kľúč, odpovedzte "ano".
.

.gpg.pklist.user_id.enter
Vložte identifikátor adresáta, ktorému chcete poslať správu.
.

.#gpg.keygen.algo
# fixme: Please translate and remove the hash mark from the key line.
Select the algorithm to use.

DSA (aka DSS) is the Digital Signature Algorithm and can only be used
for signatures.

Elgamal is an encrypt-only algorithm.

RSA may be used for signatures or encryption.

The first (primary) key must always be a key which is capable of signing.
.

.gpg.keygen.algo.rsa_se
Všebecne nemožno odporúčať používať rovnaký kľúč na šifrovanie a podeisovanie
Tento algoritmus je vhodné použiť len za určitých podmienok.
Kontaktujte prosím najprv bezpečnostného špecialistu.
.

.gpg.keygen.size
Vložte dĺžku kľúča
.

.gpg.keygen.size.huge.okay
Odpovedzte "ano" alebo "nie"
.

.gpg.keygen.size.large.okay
Odpovedzte "ano" alebo "nie"
.

.gpg.keygen.valid
Vložte požadovanú hodnotu tak, ako je uvedené v príkazovom riadku.
Je možné vložiť dátum vo formáte ISO (RRRR-MM-DD), ale nedostanete
správnu chybovú hlášku - miesto toho systém skúsi interpretovať
zadanú hodnotu ako interval.
.

.gpg.keygen.valid.okay
Odpovedzte "ano" alebo "nie"
.

.gpg.keygen.name
Vložte meno držiteľa kľúča
.

.gpg.keygen.email
prosím, vložte e-mailovú adresu (nepovinné, ale veľmi odporúčané)
.

.gpg.keygen.comment
Prosím, vložte nepovinný komentár
.

.gpg.keygen.userid.cmd
N  pre zmenu názvu.
C  pre zmenu komentára.
E  pre zmenu e-mailovej adresy.
O  pre pokračovanie generovania kľúča.
Q  pre ukončenie generovania kľúča.
.

.gpg.keygen.sub.okay
Ak chcete generovať podkľúč, odpovedzte "ano" (alebo len "a").
.

.gpg.sign_uid.okay
Odpovedzte "ano" alebo "nie"
.

.gpg.sign_uid.class
Skôr ako podpíšete id užívateľa, mali by ste najprv overiť, či kľúč
patrí osobe, ktorej meno je uvedené v identifikátore užívateľa.
Je veľmi užitočné, keď ostatní vedia, ako dôsledne ste previedli
takéto overenie.

"0" znamená, že neuvádzate, ako dôsledne ste pravosť kľúča overili

"1" znamená, že veríte tomu, že kľúč patrí osobe, ktorá je uvedená,
    v užívateľskom ID, ale nemohli ste alebo jste nepreverili túto skutočnosť.
    To je užitočné pre "osobnú" verifikáciu, keď podpisujete kľúče, ktoré
    používajú pseudonym užívateľa.

"2" znamená, že ste čiastočne overili pravosť kľúča. Napr. ste overili
    fingerprint kľúča a skontrolovali identifikátor užívateľa
    uvedený na kľúči s fotografickým id.

"3" Znamená, že ste vykonali veľmi dôkladné overenie pravosti kľúča.
    To môže napríklad znamenať, že ste overili fingerprint kľúča 
    jeho vlastníka osobne a ďalej ste pomocou tažko falšovateľného 
    dokumentu s fotografiou (napríklad pasu) overili, že meno majiteľa
    kľúča sa zhoduje s menom uvedeným v užívateľskom ID a ďalej ste 
    overili (výmenou elektronických dopisov), že elektronická adresa uvedená 
    v ID užívateľa patrí majiteľovi kľúča.

Prosím nezabúdajte, že príklady uvedené pre úroveň 2 a 3 sú *len*
príklady.
Je len na Vašom rozhodnutí, čo "čiastočné" a "dôkladné" overenie znamená
keď budete podpisovať kľúče iným užívateľom.

Pokiaľ neviete, aká je správna odpoveď, odpovedzte "0".
.

.gpg.change_passwd.empty.okay
Odpovedzte "ano" alebo "nie"
.

.gpg.keyedit.save.okay
Odpovedzte "ano" alebo "nie"
.

.gpg.keyedit.cancel.okay
Odpovedzte "ano" alebo "nie"
.

.#gpg.keyedit.sign_all.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" if you want to sign ALL the user IDs
.

.gpg.keyedit.remove.uid.okay
Pokiaľ skutočne chcete zmazať tento identifikátor užívateľa, odpovedzte "ano".
Všetky certifikáty budú tiež stratené!
.

.gpg.keyedit.remove.subkey.okay
Odpovedzte "ano", pokiaľ chcete zmazať podkľúč
.

.gpg.keyedit.delsig.valid
Toto je platný podpis kľúča; normálne nechcete tento podpis zmazať,
pretože môže byť dôležitý pri vytváraní dôvery kľúča alebo iného kľúča
ceritifikovaného týmto kľúčom.
.

.gpg.keyedit.delsig.unknown
Tento podpis nemôže byť overený, pretože nemáte zodpovedajúci verejný kľúč.
Jeho zmazanie by ste mali odložiť do času, keď budete vedieť, ktorý kľúč
bol použitý, pretože tento podpisovací kľúč môže vytvoriť dôveru
prostredníctvom iného už certifikovaného kľúča.
.

.gpg.keyedit.delsig.invalid
Podpis je neplatný. Je rozumné ho odstrániť z Vášho súboru kľúčov.
.

.gpg.keyedit.delsig.selfsig
Toto je podpis, ktorý viaže identifikátor užívateľa ku kľúču. Zvyčajne
nie je dobré takýto podpis odstrániť. GnuPG nemôže tento kľúč naďalej
používať. Urobte to len v prípade, keď je tento podpis kľúča
ním samým z nejakého dôvodu neplatný a keď je k dispozícii iný kľúč.
.

.gpg.keyedit.updpref.okay
Zmeniť predvoľby pre všetky užívateľské ID (alebo len pre označené)
na aktuálny zoznam predvolieb. Časové razítka všetkých dotknutých podpisov
kľúčov nimi samotnými budú posunuté o jednu sekundu dopredu.

.

.gpg.passphrase.enter
Prosím, vložte heslo; toto je tajná veta 

.

.gpg.passphrase.repeat
Prosím, zopakujte posledné heslo, aby ste si boli istý, čo ste napísali.
.

.gpg.detached_signature.filename
Zadajte názov súboru, ku ktorému sa podpis vzťahuje
.

.gpg.openfile.overwrite.okay
Ak si prajete prepísanie súboru, odpovedzte "ano"
.

.gpg.openfile.askoutname
Prosím, vložte nový názov súboru. Ak len stlačíte RETURN, bude
použitý implicitný súbor (ktorý je zobrazený v zátvorkách).
.

.gpg.ask_revocation_reason.code
Mali by ste špecifikovať dôvod certifikácie. V závislosti na kontexte
máte možnosť si vybrať zo zoznamu:
  "kľúč bol kompromitovaný"
      Toto použite, pokiaľ si myslíte, že k Vášmu tajnému kľúču získali
       prístup neoprávnené osoby.
  "kľúč je nahradený"
      Toto použite, pokiaľ ste tento kľúč nahradili novším kľúčom.
  "kľúč sa už nepoužíva"
      Toto použite, pokiaľ tento kľúč už nepoužívate.
  "Identifikátor užívateľa už nie je platný"
      Toto použite, pokiaľ by sa identifikátor užívateľa už nemal používať;
      normálne sa používa na označenie neplatnej e-mailové adresy.

.

.gpg.ask_revocation_reason.text
Ak chcete, môžete vložiť text popisujúcí pôvod vzniku tohto revokačného
ceritifikátu. Prosím, stručne. 
Text končí prázdnym riadkom.

.



# Local variables:
# mode: fundamental
# coding: utf-8
# End:
