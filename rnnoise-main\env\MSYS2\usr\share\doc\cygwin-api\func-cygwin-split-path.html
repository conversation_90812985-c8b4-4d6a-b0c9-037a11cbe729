<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>cygwin_split_path</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="cygwin-functions.html#func-cygwin-path" title="Path conversion functions"><link rel="prev" href="func-cygwin-posix-path-list-p.html" title="cygwin_posix_path_list_p"><link rel="next" href="func-cygwin-login.html" title="Helper functions to change user context"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">cygwin_split_path</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="func-cygwin-posix-path-list-p.html">Prev</a>&#160;</td><th width="60%" align="center">Path conversion functions</th><td width="20%" align="right">&#160;<a accesskey="n" href="func-cygwin-login.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="func-cygwin-split-path"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>cygwin_split_path</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="funcsynopsis"><pre class="funcsynopsisinfo">
#include &lt;sys/cygwin.h&gt;
</pre><p><code class="funcdef">void
<b class="fsfunc">cygwin_split_path</b>
(</code>const char * <var class="pdparam">path</var>, char * <var class="pdparam">dir</var>, char * <var class="pdparam">file</var><code>)</code>;</p></div></div><div class="refsect1"><a name="func-cygwin-split-path-desc"></a><h2>Description</h2><p>Split a path into the directory and the file portions.  Both
<em class="parameter"><code>dir</code></em> and <em class="parameter"><code>file</code></em> are
expected to point to buffers of sufficient size.  </p></div><div class="refsect1"><a name="func-cygwin-split-path-example"></a><h2>Example</h2><div class="example"><a name="func-cygwin-split-path-example-example"></a><p class="title"><b>Example&#160;2.2.&#160;Example use of cygwin_split_path</b></p><div class="example-contents"><pre class="programlisting">
char dir[200], file[100];
cygwin_split_path("c:/foo/bar.c", dir, file);
printf("dir=%s, file=%s\n", dir, file);
</pre></div></div><br class="example-break"></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="func-cygwin-posix-path-list-p.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="cygwin-functions.html#func-cygwin-path">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="func-cygwin-login.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">cygwin_posix_path_list_p&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;Helper functions to change user context</td></tr></table></div></body></html>
