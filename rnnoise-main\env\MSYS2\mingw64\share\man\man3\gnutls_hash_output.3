.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hash_output" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hash_output \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "void gnutls_hash_output(gnutls_hash_hd_t " handle ", void * " digest ");"
.SH ARGUMENTS
.IP "gnutls_hash_hd_t handle" 12
is a \fBgnutls_hash_hd_t\fP type
.IP "void * digest" 12
is the output value of the hash
.SH "DESCRIPTION"
This function will output the current hash value and reset the
state of the hash. If  \fIdigest\fP is \fBNULL\fP, it only resets the state of
the hash.
.SH "SINCE"
2.10.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
