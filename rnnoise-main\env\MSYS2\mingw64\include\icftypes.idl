/*
 * Types for the ICF api
 *
 * Copyright 2007 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 *
 */

import "wtypes.idl";

typedef enum NET_FW_PROFILE_TYPE_
{
    NET_FW_PROFILE_DOMAIN,
    NET_FW_PROFILE_STANDARD,
    NET_FW_PROFILE_CURRENT,
    NET_FW_PROFILE_TYPE_MAX
}   NET_FW_PROFILE_TYPE;

typedef enum NET_FW_PROFILE_TYPE2_
{
   NET_FW_PROFILE2_DOMAIN   = 0x0001,
   NET_FW_PROFILE2_PRIVATE  = 0x0002,
   NET_FW_PROFILE2_PUBLIC   = 0x0004,
   NET_FW_PROFILE2_ALL      = 0x7FFFFFFF
} NET_FW_PROFILE_TYPE2;

typedef enum NET_FW_IP_VERSION_
{
    NET_FW_IP_VERSION_V4,
    NET_FW_IP_VERSION_V6,
    NET_FW_IP_VERSION_ANY,
    NET_FW_IP_VERSION_MAX
}   NET_FW_IP_VERSION;

typedef enum NET_FW_POLICY_TYPE_
{
    NET_FW_POLICY_GROUP,
    NET_FW_POLICY_LOCAL,
    NET_FW_POLICY_EFFECTIVE,
    NET_FW_POLICY_TYPE_MAX
}   NET_FW_POLICY_TYPE;

typedef enum NET_FW_SCOPE_
{
    NET_FW_SCOPE_ALL,
    NET_FW_SCOPE_LOCAL_SUBNET,
    NET_FW_SCOPE_CUSTOM,
    NET_FW_SCOPE_MAX
}   NET_FW_SCOPE;

typedef enum NET_FW_SERVICE_TYPE_
{
    NET_FW_SERVICE_FILE_AND_PRINT,
    NET_FW_SERVICE_UPNP,
    NET_FW_SERVICE_REMOTE_DESKTOP,
    NET_FW_SERVICE_NONE,
    NET_FW_SERVICE_TYPE_MAX
}   NET_FW_SERVICE_TYPE;

typedef enum NET_FW_IP_PROTOCOL_
{
    NET_FW_IP_PROTOCOL_TCP =  6,
    NET_FW_IP_PROTOCOL_UDP = 17
}   NET_FW_IP_PROTOCOL;

typedef enum NET_FW_RULE_DIRECTION_
{
    NET_FW_RULE_DIR_IN = 1,
    NET_FW_RULE_DIR_OUT,
    NET_FW_RULE_DIR_MAX
} NET_FW_RULE_DIRECTION;

typedef enum NET_FW_ACTION_
{
    NET_FW_ACTION_BLOCK,
    NET_FW_ACTION_ALLOW,
    NET_FW_ACTION_MAX
} NET_FW_ACTION;

typedef enum NET_FW_MODIFY_STATE_
{
    NET_FW_MODIFY_STATE_OK,
    NET_FW_MODIFY_STATE_GP_OVERRIDE,
    NET_FW_MODIFY_STATE_INBOUND_BLOCKED
} NET_FW_MODIFY_STATE;
