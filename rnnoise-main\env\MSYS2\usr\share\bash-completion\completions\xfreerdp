# xfreerdp completion                                      -*- shell-script -*-

_comp_cmd_xfreerdp()
{
    local cur prev words cword comp_args
    _comp_initialize -n : -- "$@" || return

    case $prev in
        -k)
            _comp_compgen_split -- "$("$1" --kbd-list |
                _comp_awk '/^0x/ { print $1 }')"
            return
            ;;
        -a)
            _comp_compgen -- -W '8 15 16 24 32'
            return
            ;;
        -x)
            _comp_compgen -- -W 'broadband modem lan'
            return
            ;;
        --plugin)
            _comp_compgen -- -W 'cliprdr rdpsnd rdpdr'
            return
            ;;
        /help | /version | -h | --help | --version)
            return
            ;;
    esac

    case $cur in
        /kbd:*)
            local kbd_list
            kbd_list=$("$1" /kbd-list 2>/dev/null) ||
                kbd_list=$("$1" /list:kbd 2>/dev/null)
            _comp_compgen -c "${cur#/kbd:}" split -- "$(
                _comp_awk '/^0x/ { print $1 }' <<<"$kbd_list"
            )"
            return
            ;;
        /bpp:*)
            _comp_compgen -c "${cur#/bpp:}" -- -W '8 15 16 24 32'
            return
            ;;
        /*:*)
            return
            ;;
    esac

    if [[ $cur == /* ]]; then
        _comp_compgen_filedir rdp
        _comp_compgen -a split -- "$(
            "$1" --help | _comp_awk '$1 ~ /^\// && $1 !~ /^.(flag$|option:)/ {
                sub(":.*",":",$1); print $1 }'
        )"
        [[ ${COMPREPLY-} == *: ]] && compopt -o nospace
    elif [[ $cur == [+-]* ]]; then
        local char=${cur:0:1}
        local help="$("$1" --help)"
        if [[ $help == */help* ]]; then # new/slash syntax
            _comp_compgen_split -- "$(_comp_awk '$1 ~ /^[+-]/ && $1 !~ /^.toggle$/ {
                    sub("^.","'"$char"'",$1); print $1 }' <<<"$help")"
        else # old/dash syntax
            _comp_compgen -R help - <<<"$help"
            ((${#COMPREPLY[@]})) &&
                _comp_compgen -- -W '"${COMPREPLY[@]%:}"'
        fi
    else
        _comp_compgen_filedir rdp
        _comp_compgen -a split -- "$(
            _comp_awk '{print $1}' ~/.freerdp/known_hosts 2>/dev/null
        )"
    fi

} &&
    complete -F _comp_cmd_xfreerdp xfreerdp

# ex: filetype=sh
