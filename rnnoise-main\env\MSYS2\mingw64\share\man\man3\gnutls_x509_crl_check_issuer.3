.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crl_check_issuer" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crl_check_issuer \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "unsigned gnutls_x509_crl_check_issuer(gnutls_x509_crl_t " crl ", gnutls_x509_crt_t " issuer ");"
.SH ARGUMENTS
.IP "gnutls_x509_crl_t crl" 12
is the CRL to be checked
.IP "gnutls_x509_crt_t issuer" 12
is the certificate of a possible issuer
.SH "DESCRIPTION"
This function will check if the given CRL was issued by the given
issuer certificate.
.SH "RETURNS"
true (1) if the given CRL was issued by the given issuer,
and false (0) if not.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
