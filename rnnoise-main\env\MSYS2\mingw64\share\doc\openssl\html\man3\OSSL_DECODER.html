<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_DECODER</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_DECODER, OSSL_DECODER_fetch, OSSL_DECODER_up_ref, OSSL_DECODER_free, OSSL_DECODER_get0_provider, OSSL_DECODER_get0_properties, OSSL_DECODER_is_a, OSSL_DECODER_get0_name, OSSL_DECODER_get0_description, OSSL_DECODER_do_all_provided, OSSL_DECODER_names_do_all, OSSL_DECODER_gettable_params, OSSL_DECODER_get_params - Decoder method routines</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/decoder.h&gt;

typedef struct ossl_decoder_st OSSL_DECODER;

OSSL_DECODER *OSSL_DECODER_fetch(OSSL_LIB_CTX *ctx, const char *name,
                                 const char *properties);
int OSSL_DECODER_up_ref(OSSL_DECODER *decoder);
void OSSL_DECODER_free(OSSL_DECODER *decoder);
const OSSL_PROVIDER *OSSL_DECODER_get0_provider(const OSSL_DECODER *decoder);
const char *OSSL_DECODER_get0_properties(const OSSL_DECODER *decoder);
int OSSL_DECODER_is_a(const OSSL_DECODER *decoder, const char *name);
const char *OSSL_DECODER_get0_name(const OSSL_DECODER *decoder);
const char *OSSL_DECODER_get0_description(const OSSL_DECODER *decoder);
void OSSL_DECODER_do_all_provided(OSSL_LIB_CTX *libctx,
                                  void (*fn)(OSSL_DECODER *decoder, void *arg),
                                  void *arg);
int OSSL_DECODER_names_do_all(const OSSL_DECODER *decoder,
                              void (*fn)(const char *name, void *data),
                              void *data);
const OSSL_PARAM *OSSL_DECODER_gettable_params(OSSL_DECODER *decoder);
int OSSL_DECODER_get_params(OSSL_DECODER_CTX *ctx, const OSSL_PARAM params[]);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p><b>OSSL_DECODER</b> is a method for decoders, which know how to decode encoded data into an object of some type that the rest of OpenSSL knows how to handle.</p>

<p>OSSL_DECODER_fetch() looks for an algorithm within the provider that has been loaded into the <b>OSSL_LIB_CTX</b> given by <i>ctx</i>, having the name given by <i>name</i> and the properties given by <i>properties</i>. The <i>name</i> determines what type of object the fetched decoder method is expected to be able to decode, and the properties are used to determine the expected output type. For known properties and the values they may have, please have a look in <a href="../man7/provider-encoder.html">&quot;Names and properties&quot; in provider-encoder(7)</a>.</p>

<p>OSSL_DECODER_up_ref() increments the reference count for the given <i>decoder</i>.</p>

<p>OSSL_DECODER_free() decrements the reference count for the given <i>decoder</i>, and when the count reaches zero, frees it. If the argument is NULL, nothing is done.</p>

<p>OSSL_DECODER_get0_provider() returns the provider of the given <i>decoder</i>.</p>

<p>OSSL_DECODER_get0_properties() returns the property definition associated with the given <i>decoder</i>.</p>

<p>OSSL_DECODER_is_a() checks if <i>decoder</i> is an implementation of an algorithm that&#39;s identifiable with <i>name</i>.</p>

<p>OSSL_DECODER_get0_name() returns the name used to fetch the given <i>decoder</i>.</p>

<p>OSSL_DECODER_get0_description() returns a description of the <i>decoder</i>, meant for display and human consumption. The description is at the discretion of the <i>decoder</i> implementation.</p>

<p>OSSL_DECODER_names_do_all() traverses all names for the given <i>decoder</i>, and calls <i>fn</i> with each name and <i>data</i> as arguments.</p>

<p>OSSL_DECODER_do_all_provided() traverses all decoder implementations by all activated providers in the library context <i>libctx</i>, and for each of the implementations, calls <i>fn</i> with the implementation method and <i>arg</i> as arguments.</p>

<p>OSSL_DECODER_gettable_params() returns an <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array of parameter descriptors.</p>

<p>OSSL_DECODER_get_params() attempts to get parameters specified with an <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array <i>params</i>. Parameters that the implementation doesn&#39;t recognise should be ignored.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_DECODER_fetch() returns a pointer to an OSSL_DECODER object, or NULL on error.</p>

<p>OSSL_DECODER_up_ref() returns 1 on success, or 0 on error.</p>

<p>OSSL_DECODER_free() doesn&#39;t return any value.</p>

<p>OSSL_DECODER_get0_provider() returns a pointer to a provider object, or NULL on error.</p>

<p>OSSL_DECODER_get0_properties() returns a pointer to a property definition string, or NULL on error.</p>

<p>OSSL_DECODER_is_a() returns 1 if <i>decoder</i> was identifiable, otherwise 0.</p>

<p>OSSL_DECODER_get0_name() returns the algorithm name from the provided implementation for the given <i>decoder</i>. Note that the <i>decoder</i> may have multiple synonyms associated with it. In this case the first name from the algorithm definition is returned. Ownership of the returned string is retained by the <i>decoder</i> object and should not be freed by the caller.</p>

<p>OSSL_DECODER_get0_description() returns a pointer to a description, or NULL if there isn&#39;t one.</p>

<p>OSSL_DECODER_names_do_all() returns 1 if the callback was called for all names. A return value of 0 means that the callback was not called for any names.</p>

<h1 id="NOTES">NOTES</h1>

<p>OSSL_DECODER_fetch() may be called implicitly by other fetching functions, using the same library context and properties. Any other API that uses keys will typically do this.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>To list all decoders in a provider to a bio_out:</p>

<pre><code>static void collect_decoders(OSSL_DECODER *decoder, void *stack)
{
    STACK_OF(OSSL_DECODER) *decoder_stack = stack;

    sk_OSSL_DECODER_push(decoder_stack, decoder);
    OSSL_DECODER_up_ref(decoder);
}

void print_name(const char *name, void *vdata)
{
    BIO *bio = vdata;

    BIO_printf(bio, &quot;%s &quot;, name);
}


STACK_OF(OSSL_DECODER) *decoders;
int i;

decoders = sk_OSSL_DECODER_new_null();

BIO_printf(bio_out, &quot;DECODERs provided by %s:\n&quot;, provider);
OSSL_DECODER_do_all_provided(NULL, collect_decoders,
                             decoders);

for (i = 0; i &lt; sk_OSSL_DECODER_num(decoders); i++) {
    OSSL_DECODER *decoder = sk_OSSL_DECODER_value(decoders, i);

    if (strcmp(OSSL_PROVIDER_get0_name(OSSL_DECODER_get0_provider(decoder)),
               provider) != 0)
        continue;

    if (OSSL_DECODER_names_do_all(decoder, print_name, bio_out))
           BIO_printf(bio_out, &quot;\n&quot;);
}
sk_OSSL_DECODER_pop_free(decoders, OSSL_DECODER_free);</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider.html">provider(7)</a>, <a href="../man3/OSSL_DECODER_CTX.html">OSSL_DECODER_CTX(3)</a>, <a href="../man3/OSSL_DECODER_from_bio.html">OSSL_DECODER_from_bio(3)</a>, <a href="../man3/OSSL_DECODER_CTX_new_for_pkey.html">OSSL_DECODER_CTX_new_for_pkey(3)</a>, <a href="../man3/OSSL_LIB_CTX.html">OSSL_LIB_CTX(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The functions described here were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


