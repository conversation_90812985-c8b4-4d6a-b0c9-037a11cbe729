CMAKE_INSTALL_MESSAGE
---------------------

.. versionadded:: 3.1

Specify verbosity of installation script code generated by the
:command:`install` command (using the :command:`file(INSTALL)` command).
For paths that are newly installed or updated, installation
may print lines like::

  -- Installing: /some/destination/path

For paths that are already up to date, installation may print
lines like::

  -- Up-to-date: /some/destination/path

The ``CMAKE_INSTALL_MESSAGE`` variable may be set to control
which messages are printed:

``ALWAYS``
  Print both ``Installing`` and ``Up-to-date`` messages.

``LAZY``
  Print ``Installing`` but not ``Up-to-date`` messages.

``NEVER``
  Print neither ``Installing`` nor ``Up-to-date`` messages.

Other values have undefined behavior and may not be diagnosed.

If this variable is not set, the default behavior is ``ALWAYS``.
