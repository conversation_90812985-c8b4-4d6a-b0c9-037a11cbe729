/* -*- mode: C; buffer-read-only: t -*-
 *
 *    opcode.h
 *
 *    Copyright (C) 1993, 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001,
 *    2002, 2003, 2004, 2005, 2006, 2007 by <PERSON> and others
 *
 *    You may distribute under the terms of either the GNU General Public
 *    License or the Artistic License, as specified in the README file.
 *
 * !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
 * This file is built by regen/opcode.pl from its data.
 * Any changes made here will be lost!
 */

#if defined(PERL_CORE) || defined(PERL_EXT)

#define Perl_pp_scalar Perl_pp_null
#define Perl_pp_padany Perl_unimplemented_op
#define Perl_pp_regcmaybe Perl_pp_null
#define Perl_pp_transr Perl_pp_trans
#define Perl_pp_chomp Perl_pp_chop
#define Perl_pp_schomp Perl_pp_schop
#define Perl_pp_i_preinc Perl_pp_preinc
#define Perl_pp_i_predec Perl_pp_predec
#define Perl_pp_i_postinc Perl_pp_postinc
#define Perl_pp_i_postdec Perl_pp_postdec
#define Perl_pp_slt Perl_pp_sle
#define Perl_pp_sgt Perl_pp_sle
#define Perl_pp_sge Perl_pp_sle
#define Perl_pp_bit_xor Perl_pp_bit_or
#define Perl_pp_nbit_xor Perl_pp_nbit_or
#define Perl_pp_sbit_xor Perl_pp_sbit_or
#define Perl_pp_cos Perl_pp_sin
#define Perl_pp_exp Perl_pp_sin
#define Perl_pp_log Perl_pp_sin
#define Perl_pp_sqrt Perl_pp_sin
#define Perl_pp_hex Perl_pp_oct
#define Perl_pp_rindex Perl_pp_index
#define Perl_pp_lcfirst Perl_pp_ucfirst
#define Perl_pp_aelemfast_lex Perl_pp_aelemfast
#define Perl_pp_avalues Perl_pp_akeys
#define Perl_pp_values Perl_do_kv
#define Perl_pp_keys Perl_do_kv
#define Perl_pp_rv2hv Perl_pp_rv2av
#define Perl_pp_pop Perl_pp_shift
#define Perl_pp_mapstart Perl_pp_grepstart
#define Perl_pp_dor Perl_pp_defined
#define Perl_pp_andassign Perl_pp_and
#define Perl_pp_orassign Perl_pp_or
#define Perl_pp_dorassign Perl_pp_defined
#define Perl_pp_lineseq Perl_pp_null
#define Perl_pp_scope Perl_pp_null
#define Perl_pp_dump Perl_pp_goto
#define Perl_pp_dbmclose Perl_pp_untie
#define Perl_pp_read Perl_pp_sysread
#define Perl_pp_say Perl_pp_print
#define Perl_pp_seek Perl_pp_sysseek
#define Perl_pp_fcntl Perl_pp_ioctl
#ifdef HAS_SOCKET
#define Perl_pp_send Perl_pp_syswrite
#define Perl_pp_recv Perl_pp_sysread
#else
#define Perl_pp_send Perl_unimplemented_op
#define Perl_pp_recv Perl_unimplemented_op
#define Perl_pp_socket Perl_unimplemented_op
#endif
#ifdef HAS_SOCKET
#define Perl_pp_connect Perl_pp_bind
#define Perl_pp_gsockopt Perl_pp_ssockopt
#define Perl_pp_getsockname Perl_pp_getpeername
#else
#define Perl_pp_bind Perl_unimplemented_op
#define Perl_pp_connect Perl_unimplemented_op
#define Perl_pp_listen Perl_unimplemented_op
#define Perl_pp_accept Perl_unimplemented_op
#define Perl_pp_shutdown Perl_unimplemented_op
#define Perl_pp_gsockopt Perl_unimplemented_op
#define Perl_pp_ssockopt Perl_unimplemented_op
#define Perl_pp_getsockname Perl_unimplemented_op
#define Perl_pp_getpeername Perl_unimplemented_op
#endif
#define Perl_pp_lstat Perl_pp_stat
#define Perl_pp_ftrwrite Perl_pp_ftrread
#define Perl_pp_ftrexec Perl_pp_ftrread
#define Perl_pp_fteread Perl_pp_ftrread
#define Perl_pp_ftewrite Perl_pp_ftrread
#define Perl_pp_fteexec Perl_pp_ftrread
#define Perl_pp_ftsize Perl_pp_ftis
#define Perl_pp_ftmtime Perl_pp_ftis
#define Perl_pp_ftatime Perl_pp_ftis
#define Perl_pp_ftctime Perl_pp_ftis
#define Perl_pp_fteowned Perl_pp_ftrowned
#define Perl_pp_ftzero Perl_pp_ftrowned
#define Perl_pp_ftsock Perl_pp_ftrowned
#define Perl_pp_ftchr Perl_pp_ftrowned
#define Perl_pp_ftblk Perl_pp_ftrowned
#define Perl_pp_ftfile Perl_pp_ftrowned
#define Perl_pp_ftdir Perl_pp_ftrowned
#define Perl_pp_ftpipe Perl_pp_ftrowned
#define Perl_pp_ftsuid Perl_pp_ftrowned
#define Perl_pp_ftsgid Perl_pp_ftrowned
#define Perl_pp_ftsvtx Perl_pp_ftrowned
#define Perl_pp_ftbinary Perl_pp_fttext
#define Perl_pp_unlink Perl_pp_chown
#define Perl_pp_chmod Perl_pp_chown
#define Perl_pp_utime Perl_pp_chown
#define Perl_pp_symlink Perl_pp_link
#define Perl_pp_kill Perl_pp_chown
#define Perl_pp_localtime Perl_pp_gmtime
#define Perl_pp_shmget Perl_pp_semget
#define Perl_pp_shmctl Perl_pp_semctl
#define Perl_pp_shmread Perl_pp_shmwrite
#define Perl_pp_msgget Perl_pp_semget
#define Perl_pp_msgctl Perl_pp_semctl
#define Perl_pp_msgsnd Perl_pp_shmwrite
#define Perl_pp_msgrcv Perl_pp_shmwrite
#define Perl_pp_semop Perl_pp_shmwrite
#define Perl_pp_dofile Perl_pp_require
#define Perl_pp_ghbyname Perl_pp_ghostent
#define Perl_pp_ghbyaddr Perl_pp_ghostent
#define Perl_pp_gnbyname Perl_pp_gnetent
#define Perl_pp_gnbyaddr Perl_pp_gnetent
#define Perl_pp_gpbyname Perl_pp_gprotoent
#define Perl_pp_gpbynumber Perl_pp_gprotoent
#define Perl_pp_gsbyname Perl_pp_gservent
#define Perl_pp_gsbyport Perl_pp_gservent
#define Perl_pp_snetent Perl_pp_shostent
#define Perl_pp_sprotoent Perl_pp_shostent
#define Perl_pp_sservent Perl_pp_shostent
#define Perl_pp_enetent Perl_pp_ehostent
#define Perl_pp_eprotoent Perl_pp_ehostent
#define Perl_pp_eservent Perl_pp_ehostent
#define Perl_pp_gpwnam Perl_pp_gpwent
#define Perl_pp_gpwuid Perl_pp_gpwent
#define Perl_pp_spwent Perl_pp_ehostent
#define Perl_pp_epwent Perl_pp_ehostent
#define Perl_pp_ggrnam Perl_pp_ggrent
#define Perl_pp_ggrgid Perl_pp_ggrent
#define Perl_pp_sgrent Perl_pp_ehostent
#define Perl_pp_egrent Perl_pp_ehostent
#define Perl_pp_custom Perl_unimplemented_op

#endif /* End of if defined(PERL_CORE) || defined(PERL_EXT) */

START_EXTERN_C

EXTCONST char* const PL_op_name[] INIT({
	"null",
	"stub",
	"scalar",
	"pushmark",
	"wantarray",
	"const",
	"gvsv",
	"gv",
	"gelem",
	"padsv",
	"padsv_store",
	"padav",
	"padhv",
	"padany",
	"rv2gv",
	"rv2sv",
	"av2arylen",
	"rv2cv",
	"anoncode",
	"prototype",
	"refgen",
	"srefgen",
	"ref",
	"bless",
	"backtick",
	"glob",
	"readline",
	"rcatline",
	"regcmaybe",
	"regcreset",
	"regcomp",
	"match",
	"qr",
	"subst",
	"substcont",
	"trans",
	"transr",
	"sassign",
	"aassign",
	"chop",
	"schop",
	"chomp",
	"schomp",
	"defined",
	"undef",
	"study",
	"pos",
	"preinc",
	"i_preinc",
	"predec",
	"i_predec",
	"postinc",
	"i_postinc",
	"postdec",
	"i_postdec",
	"pow",
	"multiply",
	"i_multiply",
	"divide",
	"i_divide",
	"modulo",
	"i_modulo",
	"repeat",
	"add",
	"i_add",
	"subtract",
	"i_subtract",
	"concat",
	"multiconcat",
	"stringify",
	"left_shift",
	"right_shift",
	"lt",
	"i_lt",
	"gt",
	"i_gt",
	"le",
	"i_le",
	"ge",
	"i_ge",
	"eq",
	"i_eq",
	"ne",
	"i_ne",
	"ncmp",
	"i_ncmp",
	"slt",
	"sgt",
	"sle",
	"sge",
	"seq",
	"sne",
	"scmp",
	"bit_and",
	"bit_xor",
	"bit_or",
	"nbit_and",
	"nbit_xor",
	"nbit_or",
	"sbit_and",
	"sbit_xor",
	"sbit_or",
	"negate",
	"i_negate",
	"not",
	"complement",
	"ncomplement",
	"scomplement",
	"smartmatch",
	"atan2",
	"sin",
	"cos",
	"rand",
	"srand",
	"exp",
	"log",
	"sqrt",
	"int",
	"hex",
	"oct",
	"abs",
	"length",
	"substr",
	"vec",
	"index",
	"rindex",
	"sprintf",
	"formline",
	"ord",
	"chr",
	"crypt",
	"ucfirst",
	"lcfirst",
	"uc",
	"lc",
	"quotemeta",
	"rv2av",
	"aelemfast",
	"aelemfast_lex",
	"aelemfastlex_store",
	"aelem",
	"aslice",
	"kvaslice",
	"aeach",
	"avalues",
	"akeys",
	"each",
	"values",
	"keys",
	"delete",
	"exists",
	"rv2hv",
	"helem",
	"hslice",
	"kvhslice",
	"multideref",
	"unpack",
	"pack",
	"split",
	"join",
	"list",
	"lslice",
	"anonlist",
	"anonhash",
	"emptyavhv",
	"splice",
	"push",
	"pop",
	"shift",
	"unshift",
	"sort",
	"reverse",
	"grepstart",
	"grepwhile",
	"mapstart",
	"mapwhile",
	"range",
	"flip",
	"flop",
	"and",
	"or",
	"xor",
	"dor",
	"cond_expr",
	"andassign",
	"orassign",
	"dorassign",
	"entersub",
	"leavesub",
	"leavesublv",
	"argcheck",
	"argelem",
	"argdefelem",
	"caller",
	"warn",
	"die",
	"reset",
	"lineseq",
	"nextstate",
	"dbstate",
	"unstack",
	"enter",
	"leave",
	"scope",
	"enteriter",
	"iter",
	"enterloop",
	"leaveloop",
	"return",
	"last",
	"next",
	"redo",
	"dump",
	"goto",
	"exit",
	"method",
	"method_named",
	"method_super",
	"method_redir",
	"method_redir_super",
	"entergiven",
	"leavegiven",
	"enterwhen",
	"leavewhen",
	"break",
	"continue",
	"open",
	"close",
	"pipe_op",
	"fileno",
	"umask",
	"binmode",
	"tie",
	"untie",
	"tied",
	"dbmopen",
	"dbmclose",
	"sselect",
	"select",
	"getc",
	"read",
	"enterwrite",
	"leavewrite",
	"prtf",
	"print",
	"say",
	"sysopen",
	"sysseek",
	"sysread",
	"syswrite",
	"eof",
	"tell",
	"seek",
	"truncate",
	"fcntl",
	"ioctl",
	"flock",
	"send",
	"recv",
	"socket",
	"sockpair",
	"bind",
	"connect",
	"listen",
	"accept",
	"shutdown",
	"gsockopt",
	"ssockopt",
	"getsockname",
	"getpeername",
	"lstat",
	"stat",
	"ftrread",
	"ftrwrite",
	"ftrexec",
	"fteread",
	"ftewrite",
	"fteexec",
	"ftis",
	"ftsize",
	"ftmtime",
	"ftatime",
	"ftctime",
	"ftrowned",
	"fteowned",
	"ftzero",
	"ftsock",
	"ftchr",
	"ftblk",
	"ftfile",
	"ftdir",
	"ftpipe",
	"ftsuid",
	"ftsgid",
	"ftsvtx",
	"ftlink",
	"fttty",
	"fttext",
	"ftbinary",
	"chdir",
	"chown",
	"chroot",
	"unlink",
	"chmod",
	"utime",
	"rename",
	"link",
	"symlink",
	"readlink",
	"mkdir",
	"rmdir",
	"open_dir",
	"readdir",
	"telldir",
	"seekdir",
	"rewinddir",
	"closedir",
	"fork",
	"wait",
	"waitpid",
	"system",
	"exec",
	"kill",
	"getppid",
	"getpgrp",
	"setpgrp",
	"getpriority",
	"setpriority",
	"time",
	"tms",
	"localtime",
	"gmtime",
	"alarm",
	"sleep",
	"shmget",
	"shmctl",
	"shmread",
	"shmwrite",
	"msgget",
	"msgctl",
	"msgsnd",
	"msgrcv",
	"semop",
	"semget",
	"semctl",
	"require",
	"dofile",
	"hintseval",
	"entereval",
	"leaveeval",
	"entertry",
	"leavetry",
	"ghbyname",
	"ghbyaddr",
	"ghostent",
	"gnbyname",
	"gnbyaddr",
	"gnetent",
	"gpbyname",
	"gpbynumber",
	"gprotoent",
	"gsbyname",
	"gsbyport",
	"gservent",
	"shostent",
	"snetent",
	"sprotoent",
	"sservent",
	"ehostent",
	"enetent",
	"eprotoent",
	"eservent",
	"gpwnam",
	"gpwuid",
	"gpwent",
	"spwent",
	"epwent",
	"ggrnam",
	"ggrgid",
	"ggrent",
	"sgrent",
	"egrent",
	"getlogin",
	"syscall",
	"lock",
	"once",
	"custom",
	"coreargs",
	"avhvswitch",
	"runcv",
	"fc",
	"padcv",
	"introcv",
	"clonecv",
	"padrange",
	"refassign",
	"lvref",
	"lvrefslice",
	"lvavref",
	"anonconst",
	"isa",
	"cmpchain_and",
	"cmpchain_dup",
	"entertrycatch",
	"leavetrycatch",
	"poptry",
	"catch",
	"pushdefer",
	"is_bool",
	"is_weak",
	"weaken",
	"unweaken",
	"blessed",
	"refaddr",
	"reftype",
	"ceil",
	"floor",
	"is_tainted",
	"helemexistsor",
	"methstart",
	"initfield",
        "freed",
});

EXTCONST char* const PL_op_desc[] INIT({
	"null operation",
	"stub",
	"scalar",
	"pushmark",
	"wantarray",
	"constant item",
	"scalar variable",
	"glob value",
	"glob elem",
	"private variable",
	"padsv scalar assignment",
	"private array",
	"private hash",
	"private value",
	"ref-to-glob cast",
	"scalar dereference",
	"array length",
	"subroutine dereference",
	"anonymous subroutine",
	"subroutine prototype",
	"reference constructor",
	"single ref constructor",
	"reference-type operator",
	"bless",
	"quoted execution (``, qx)",
	"glob",
	"<HANDLE>",
	"append I/O operator",
	"regexp internal guard",
	"regexp internal reset",
	"regexp compilation",
	"pattern match (m//)",
	"pattern quote (qr//)",
	"substitution (s///)",
	"substitution iterator",
	"transliteration (tr///)",
	"transliteration (tr///)",
	"scalar assignment",
	"list assignment",
	"chop",
	"scalar chop",
	"chomp",
	"scalar chomp",
	"defined operator",
	"undef operator",
	"study",
	"match position",
	"preincrement (++)",
	"integer preincrement (++)",
	"predecrement (--)",
	"integer predecrement (--)",
	"postincrement (++)",
	"integer postincrement (++)",
	"postdecrement (--)",
	"integer postdecrement (--)",
	"exponentiation (**)",
	"multiplication (*)",
	"integer multiplication (*)",
	"division (/)",
	"integer division (/)",
	"modulus (%)",
	"integer modulus (%)",
	"repeat (x)",
	"addition (+)",
	"integer addition (+)",
	"subtraction (-)",
	"integer subtraction (-)",
	"concatenation (.) or string",
	"concatenation (.) or string",
	"string",
	"left bitshift (<<)",
	"right bitshift (>>)",
	"numeric lt (<)",
	"integer lt (<)",
	"numeric gt (>)",
	"integer gt (>)",
	"numeric le (<=)",
	"integer le (<=)",
	"numeric ge (>=)",
	"integer ge (>=)",
	"numeric eq (==)",
	"integer eq (==)",
	"numeric ne (!=)",
	"integer ne (!=)",
	"numeric comparison (<=>)",
	"integer comparison (<=>)",
	"string lt",
	"string gt",
	"string le",
	"string ge",
	"string eq",
	"string ne",
	"string comparison (cmp)",
	"bitwise and (&)",
	"bitwise xor (^)",
	"bitwise or (|)",
	"numeric bitwise and (&)",
	"numeric bitwise xor (^)",
	"numeric bitwise or (|)",
	"string bitwise and (&.)",
	"string bitwise xor (^.)",
	"string bitwise or (|.)",
	"negation (-)",
	"integer negation (-)",
	"not",
	"1's complement (~)",
	"numeric 1's complement (~)",
	"string 1's complement (~)",
	"smart match",
	"atan2",
	"sin",
	"cos",
	"rand",
	"srand",
	"exp",
	"log",
	"sqrt",
	"int",
	"hex",
	"oct",
	"abs",
	"length",
	"substr",
	"vec",
	"index",
	"rindex",
	"sprintf",
	"formline",
	"ord",
	"chr",
	"crypt",
	"ucfirst",
	"lcfirst",
	"uc",
	"lc",
	"quotemeta",
	"array dereference",
	"constant array element",
	"constant lexical array element",
	"const lexical array element store",
	"array element",
	"array slice",
	"index/value array slice",
	"each on array",
	"values on array",
	"keys on array",
	"each",
	"values",
	"keys",
	"delete",
	"exists",
	"hash dereference",
	"hash element",
	"hash slice",
	"key/value hash slice",
	"array or hash lookup",
	"unpack",
	"pack",
	"split",
	"join or string",
	"list",
	"list slice",
	"anonymous array ([])",
	"anonymous hash ({})",
	"empty anon hash/array",
	"splice",
	"push",
	"pop",
	"shift",
	"unshift",
	"sort",
	"reverse",
	"grep",
	"grep iterator",
	"map",
	"map iterator",
	"flipflop",
	"range (or flip)",
	"range (or flop)",
	"logical and (&&)",
	"logical or (||)",
	"logical xor",
	"defined or (//)",
	"conditional expression",
	"logical and assignment (&&=)",
	"logical or assignment (||=)",
	"defined or assignment (//=)",
	"subroutine entry",
	"subroutine exit",
	"lvalue subroutine return",
	"check subroutine arguments",
	"subroutine argument",
	"subroutine argument default value",
	"caller",
	"warn",
	"die",
	"symbol reset",
	"line sequence",
	"next statement",
	"debug next statement",
	"iteration finalizer",
	"block entry",
	"block exit",
	"block",
	"foreach loop entry",
	"foreach loop iterator",
	"loop entry",
	"loop exit",
	"return",
	"last",
	"next",
	"redo",
	"dump",
	"goto",
	"exit",
	"method lookup",
	"method with known name",
	"super with known name",
	"redirect method with known name",
	"redirect super method with known name",
	"given()",
	"leave given block",
	"when()",
	"leave when block",
	"break",
	"continue",
	"open",
	"close",
	"pipe",
	"fileno",
	"umask",
	"binmode",
	"tie",
	"untie",
	"tied",
	"dbmopen",
	"dbmclose",
	"select system call",
	"select",
	"getc",
	"read",
	"write",
	"write exit",
	"printf",
	"print",
	"say",
	"sysopen",
	"sysseek",
	"sysread",
	"syswrite",
	"eof",
	"tell",
	"seek",
	"truncate",
	"fcntl",
	"ioctl",
	"flock",
	"send",
	"recv",
	"socket",
	"socketpair",
	"bind",
	"connect",
	"listen",
	"accept",
	"shutdown",
	"getsockopt",
	"setsockopt",
	"getsockname",
	"getpeername",
	"lstat",
	"stat",
	"-R",
	"-W",
	"-X",
	"-r",
	"-w",
	"-x",
	"-e",
	"-s",
	"-M",
	"-A",
	"-C",
	"-O",
	"-o",
	"-z",
	"-S",
	"-c",
	"-b",
	"-f",
	"-d",
	"-p",
	"-u",
	"-g",
	"-k",
	"-l",
	"-t",
	"-T",
	"-B",
	"chdir",
	"chown",
	"chroot",
	"unlink",
	"chmod",
	"utime",
	"rename",
	"link",
	"symlink",
	"readlink",
	"mkdir",
	"rmdir",
	"opendir",
	"readdir",
	"telldir",
	"seekdir",
	"rewinddir",
	"closedir",
	"fork",
	"wait",
	"waitpid",
	"system",
	"exec",
	"kill",
	"getppid",
	"getpgrp",
	"setpgrp",
	"getpriority",
	"setpriority",
	"time",
	"times",
	"localtime",
	"gmtime",
	"alarm",
	"sleep",
	"shmget",
	"shmctl",
	"shmread",
	"shmwrite",
	"msgget",
	"msgctl",
	"msgsnd",
	"msgrcv",
	"semop",
	"semget",
	"semctl",
	"require",
	"do \"file\"",
	"eval hints",
	"eval \"string\"",
	"eval \"string\" exit",
	"eval {block}",
	"eval {block} exit",
	"gethostbyname",
	"gethostbyaddr",
	"gethostent",
	"getnetbyname",
	"getnetbyaddr",
	"getnetent",
	"getprotobyname",
	"getprotobynumber",
	"getprotoent",
	"getservbyname",
	"getservbyport",
	"getservent",
	"sethostent",
	"setnetent",
	"setprotoent",
	"setservent",
	"endhostent",
	"endnetent",
	"endprotoent",
	"endservent",
	"getpwnam",
	"getpwuid",
	"getpwent",
	"setpwent",
	"endpwent",
	"getgrnam",
	"getgrgid",
	"getgrent",
	"setgrent",
	"endgrent",
	"getlogin",
	"syscall",
	"lock",
	"once",
	"unknown custom operator",
	"CORE:: subroutine",
	"Array/hash switch",
	"__SUB__",
	"fc",
	"private subroutine",
	"private subroutine",
	"private subroutine",
	"list of private variables",
	"lvalue ref assignment",
	"lvalue ref assignment",
	"lvalue ref assignment",
	"lvalue array reference",
	"anonymous constant",
	"derived class test",
	"comparison chaining",
	"comparand shuffling",
	"try {block}",
	"try {block} exit",
	"pop try",
	"catch {} block",
	"push defer {} block",
	"boolean type test",
	"weakref type test",
	"reference weaken",
	"reference unweaken",
	"blessed",
	"refaddr",
	"reftype",
	"ceil",
	"floor",
	"is_tainted",
	"hash element exists or",
	"method start",
	"initialise field",
    "freed op",
});

END_EXTERN_C

START_EXTERN_C

EXT Perl_ppaddr_t PL_ppaddr[] /* or perlvars.h */
INIT({
	Perl_pp_null,
	Perl_pp_stub,
	Perl_pp_scalar,	/* implemented by Perl_pp_null */
	Perl_pp_pushmark,
	Perl_pp_wantarray,
	Perl_pp_const,
	Perl_pp_gvsv,
	Perl_pp_gv,
	Perl_pp_gelem,
	Perl_pp_padsv,
	Perl_pp_padsv_store,
	Perl_pp_padav,
	Perl_pp_padhv,
	Perl_pp_padany,	/* implemented by Perl_unimplemented_op */
	Perl_pp_rv2gv,
	Perl_pp_rv2sv,
	Perl_pp_av2arylen,
	Perl_pp_rv2cv,
	Perl_pp_anoncode,
	Perl_pp_prototype,
	Perl_pp_refgen,
	Perl_pp_srefgen,
	Perl_pp_ref,
	Perl_pp_bless,
	Perl_pp_backtick,
	Perl_pp_glob,
	Perl_pp_readline,
	Perl_pp_rcatline,
	Perl_pp_regcmaybe,	/* implemented by Perl_pp_null */
	Perl_pp_regcreset,
	Perl_pp_regcomp,
	Perl_pp_match,
	Perl_pp_qr,
	Perl_pp_subst,
	Perl_pp_substcont,
	Perl_pp_trans,
	Perl_pp_transr,	/* implemented by Perl_pp_trans */
	Perl_pp_sassign,
	Perl_pp_aassign,
	Perl_pp_chop,
	Perl_pp_schop,
	Perl_pp_chomp,	/* implemented by Perl_pp_chop */
	Perl_pp_schomp,	/* implemented by Perl_pp_schop */
	Perl_pp_defined,
	Perl_pp_undef,
	Perl_pp_study,
	Perl_pp_pos,
	Perl_pp_preinc,
	Perl_pp_i_preinc,	/* implemented by Perl_pp_preinc */
	Perl_pp_predec,
	Perl_pp_i_predec,	/* implemented by Perl_pp_predec */
	Perl_pp_postinc,
	Perl_pp_i_postinc,	/* implemented by Perl_pp_postinc */
	Perl_pp_postdec,
	Perl_pp_i_postdec,	/* implemented by Perl_pp_postdec */
	Perl_pp_pow,
	Perl_pp_multiply,
	Perl_pp_i_multiply,
	Perl_pp_divide,
	Perl_pp_i_divide,
	Perl_pp_modulo,
	Perl_pp_i_modulo,
	Perl_pp_repeat,
	Perl_pp_add,
	Perl_pp_i_add,
	Perl_pp_subtract,
	Perl_pp_i_subtract,
	Perl_pp_concat,
	Perl_pp_multiconcat,
	Perl_pp_stringify,
	Perl_pp_left_shift,
	Perl_pp_right_shift,
	Perl_pp_lt,
	Perl_pp_i_lt,
	Perl_pp_gt,
	Perl_pp_i_gt,
	Perl_pp_le,
	Perl_pp_i_le,
	Perl_pp_ge,
	Perl_pp_i_ge,
	Perl_pp_eq,
	Perl_pp_i_eq,
	Perl_pp_ne,
	Perl_pp_i_ne,
	Perl_pp_ncmp,
	Perl_pp_i_ncmp,
	Perl_pp_slt,	/* implemented by Perl_pp_sle */
	Perl_pp_sgt,	/* implemented by Perl_pp_sle */
	Perl_pp_sle,
	Perl_pp_sge,	/* implemented by Perl_pp_sle */
	Perl_pp_seq,
	Perl_pp_sne,
	Perl_pp_scmp,
	Perl_pp_bit_and,
	Perl_pp_bit_xor,	/* implemented by Perl_pp_bit_or */
	Perl_pp_bit_or,
	Perl_pp_nbit_and,
	Perl_pp_nbit_xor,	/* implemented by Perl_pp_nbit_or */
	Perl_pp_nbit_or,
	Perl_pp_sbit_and,
	Perl_pp_sbit_xor,	/* implemented by Perl_pp_sbit_or */
	Perl_pp_sbit_or,
	Perl_pp_negate,
	Perl_pp_i_negate,
	Perl_pp_not,
	Perl_pp_complement,
	Perl_pp_ncomplement,
	Perl_pp_scomplement,
	Perl_pp_smartmatch,
	Perl_pp_atan2,
	Perl_pp_sin,
	Perl_pp_cos,	/* implemented by Perl_pp_sin */
	Perl_pp_rand,
	Perl_pp_srand,
	Perl_pp_exp,	/* implemented by Perl_pp_sin */
	Perl_pp_log,	/* implemented by Perl_pp_sin */
	Perl_pp_sqrt,	/* implemented by Perl_pp_sin */
	Perl_pp_int,
	Perl_pp_hex,	/* implemented by Perl_pp_oct */
	Perl_pp_oct,
	Perl_pp_abs,
	Perl_pp_length,
	Perl_pp_substr,
	Perl_pp_vec,
	Perl_pp_index,
	Perl_pp_rindex,	/* implemented by Perl_pp_index */
	Perl_pp_sprintf,
	Perl_pp_formline,
	Perl_pp_ord,
	Perl_pp_chr,
	Perl_pp_crypt,
	Perl_pp_ucfirst,
	Perl_pp_lcfirst,	/* implemented by Perl_pp_ucfirst */
	Perl_pp_uc,
	Perl_pp_lc,
	Perl_pp_quotemeta,
	Perl_pp_rv2av,
	Perl_pp_aelemfast,
	Perl_pp_aelemfast_lex,	/* implemented by Perl_pp_aelemfast */
	Perl_pp_aelemfastlex_store,
	Perl_pp_aelem,
	Perl_pp_aslice,
	Perl_pp_kvaslice,
	Perl_pp_aeach,
	Perl_pp_avalues,	/* implemented by Perl_pp_akeys */
	Perl_pp_akeys,
	Perl_pp_each,
	Perl_pp_values,	/* implemented by Perl_do_kv */
	Perl_pp_keys,	/* implemented by Perl_do_kv */
	Perl_pp_delete,
	Perl_pp_exists,
	Perl_pp_rv2hv,	/* implemented by Perl_pp_rv2av */
	Perl_pp_helem,
	Perl_pp_hslice,
	Perl_pp_kvhslice,
	Perl_pp_multideref,
	Perl_pp_unpack,
	Perl_pp_pack,
	Perl_pp_split,
	Perl_pp_join,
	Perl_pp_list,
	Perl_pp_lslice,
	Perl_pp_anonlist,
	Perl_pp_anonhash,
	Perl_pp_emptyavhv,
	Perl_pp_splice,
	Perl_pp_push,
	Perl_pp_pop,	/* implemented by Perl_pp_shift */
	Perl_pp_shift,
	Perl_pp_unshift,
	Perl_pp_sort,
	Perl_pp_reverse,
	Perl_pp_grepstart,
	Perl_pp_grepwhile,
	Perl_pp_mapstart,	/* implemented by Perl_pp_grepstart */
	Perl_pp_mapwhile,
	Perl_pp_range,
	Perl_pp_flip,
	Perl_pp_flop,
	Perl_pp_and,
	Perl_pp_or,
	Perl_pp_xor,
	Perl_pp_dor,	/* implemented by Perl_pp_defined */
	Perl_pp_cond_expr,
	Perl_pp_andassign,	/* implemented by Perl_pp_and */
	Perl_pp_orassign,	/* implemented by Perl_pp_or */
	Perl_pp_dorassign,	/* implemented by Perl_pp_defined */
	Perl_pp_entersub,
	Perl_pp_leavesub,
	Perl_pp_leavesublv,
	Perl_pp_argcheck,
	Perl_pp_argelem,
	Perl_pp_argdefelem,
	Perl_pp_caller,
	Perl_pp_warn,
	Perl_pp_die,
	Perl_pp_reset,
	Perl_pp_lineseq,	/* implemented by Perl_pp_null */
	Perl_pp_nextstate,
	Perl_pp_dbstate,
	Perl_pp_unstack,
	Perl_pp_enter,
	Perl_pp_leave,
	Perl_pp_scope,	/* implemented by Perl_pp_null */
	Perl_pp_enteriter,
	Perl_pp_iter,
	Perl_pp_enterloop,
	Perl_pp_leaveloop,
	Perl_pp_return,
	Perl_pp_last,
	Perl_pp_next,
	Perl_pp_redo,
	Perl_pp_dump,	/* implemented by Perl_pp_goto */
	Perl_pp_goto,
	Perl_pp_exit,
	Perl_pp_method,
	Perl_pp_method_named,
	Perl_pp_method_super,
	Perl_pp_method_redir,
	Perl_pp_method_redir_super,
	Perl_pp_entergiven,
	Perl_pp_leavegiven,
	Perl_pp_enterwhen,
	Perl_pp_leavewhen,
	Perl_pp_break,
	Perl_pp_continue,
	Perl_pp_open,
	Perl_pp_close,
	Perl_pp_pipe_op,
	Perl_pp_fileno,
	Perl_pp_umask,
	Perl_pp_binmode,
	Perl_pp_tie,
	Perl_pp_untie,
	Perl_pp_tied,
	Perl_pp_dbmopen,
	Perl_pp_dbmclose,	/* implemented by Perl_pp_untie */
	Perl_pp_sselect,
	Perl_pp_select,
	Perl_pp_getc,
	Perl_pp_read,	/* implemented by Perl_pp_sysread */
	Perl_pp_enterwrite,
	Perl_pp_leavewrite,
	Perl_pp_prtf,
	Perl_pp_print,
	Perl_pp_say,	/* implemented by Perl_pp_print */
	Perl_pp_sysopen,
	Perl_pp_sysseek,
	Perl_pp_sysread,
	Perl_pp_syswrite,
	Perl_pp_eof,
	Perl_pp_tell,
	Perl_pp_seek,	/* implemented by Perl_pp_sysseek */
	Perl_pp_truncate,
	Perl_pp_fcntl,	/* implemented by Perl_pp_ioctl */
	Perl_pp_ioctl,
	Perl_pp_flock,
	Perl_pp_send,	/* implemented by Perl_pp_syswrite */
	Perl_pp_recv,	/* implemented by Perl_pp_sysread */
	Perl_pp_socket,
	Perl_pp_sockpair,
	Perl_pp_bind,
	Perl_pp_connect,	/* implemented by Perl_pp_bind */
	Perl_pp_listen,
	Perl_pp_accept,
	Perl_pp_shutdown,
	Perl_pp_gsockopt,	/* implemented by Perl_pp_ssockopt */
	Perl_pp_ssockopt,
	Perl_pp_getsockname,	/* implemented by Perl_pp_getpeername */
	Perl_pp_getpeername,
	Perl_pp_lstat,	/* implemented by Perl_pp_stat */
	Perl_pp_stat,
	Perl_pp_ftrread,
	Perl_pp_ftrwrite,	/* implemented by Perl_pp_ftrread */
	Perl_pp_ftrexec,	/* implemented by Perl_pp_ftrread */
	Perl_pp_fteread,	/* implemented by Perl_pp_ftrread */
	Perl_pp_ftewrite,	/* implemented by Perl_pp_ftrread */
	Perl_pp_fteexec,	/* implemented by Perl_pp_ftrread */
	Perl_pp_ftis,
	Perl_pp_ftsize,	/* implemented by Perl_pp_ftis */
	Perl_pp_ftmtime,	/* implemented by Perl_pp_ftis */
	Perl_pp_ftatime,	/* implemented by Perl_pp_ftis */
	Perl_pp_ftctime,	/* implemented by Perl_pp_ftis */
	Perl_pp_ftrowned,
	Perl_pp_fteowned,	/* implemented by Perl_pp_ftrowned */
	Perl_pp_ftzero,	/* implemented by Perl_pp_ftrowned */
	Perl_pp_ftsock,	/* implemented by Perl_pp_ftrowned */
	Perl_pp_ftchr,	/* implemented by Perl_pp_ftrowned */
	Perl_pp_ftblk,	/* implemented by Perl_pp_ftrowned */
	Perl_pp_ftfile,	/* implemented by Perl_pp_ftrowned */
	Perl_pp_ftdir,	/* implemented by Perl_pp_ftrowned */
	Perl_pp_ftpipe,	/* implemented by Perl_pp_ftrowned */
	Perl_pp_ftsuid,	/* implemented by Perl_pp_ftrowned */
	Perl_pp_ftsgid,	/* implemented by Perl_pp_ftrowned */
	Perl_pp_ftsvtx,	/* implemented by Perl_pp_ftrowned */
	Perl_pp_ftlink,
	Perl_pp_fttty,
	Perl_pp_fttext,
	Perl_pp_ftbinary,	/* implemented by Perl_pp_fttext */
	Perl_pp_chdir,
	Perl_pp_chown,
	Perl_pp_chroot,
	Perl_pp_unlink,	/* implemented by Perl_pp_chown */
	Perl_pp_chmod,	/* implemented by Perl_pp_chown */
	Perl_pp_utime,	/* implemented by Perl_pp_chown */
	Perl_pp_rename,
	Perl_pp_link,
	Perl_pp_symlink,	/* implemented by Perl_pp_link */
	Perl_pp_readlink,
	Perl_pp_mkdir,
	Perl_pp_rmdir,
	Perl_pp_open_dir,
	Perl_pp_readdir,
	Perl_pp_telldir,
	Perl_pp_seekdir,
	Perl_pp_rewinddir,
	Perl_pp_closedir,
	Perl_pp_fork,
	Perl_pp_wait,
	Perl_pp_waitpid,
	Perl_pp_system,
	Perl_pp_exec,
	Perl_pp_kill,	/* implemented by Perl_pp_chown */
	Perl_pp_getppid,
	Perl_pp_getpgrp,
	Perl_pp_setpgrp,
	Perl_pp_getpriority,
	Perl_pp_setpriority,
	Perl_pp_time,
	Perl_pp_tms,
	Perl_pp_localtime,	/* implemented by Perl_pp_gmtime */
	Perl_pp_gmtime,
	Perl_pp_alarm,
	Perl_pp_sleep,
	Perl_pp_shmget,	/* implemented by Perl_pp_semget */
	Perl_pp_shmctl,	/* implemented by Perl_pp_semctl */
	Perl_pp_shmread,	/* implemented by Perl_pp_shmwrite */
	Perl_pp_shmwrite,
	Perl_pp_msgget,	/* implemented by Perl_pp_semget */
	Perl_pp_msgctl,	/* implemented by Perl_pp_semctl */
	Perl_pp_msgsnd,	/* implemented by Perl_pp_shmwrite */
	Perl_pp_msgrcv,	/* implemented by Perl_pp_shmwrite */
	Perl_pp_semop,	/* implemented by Perl_pp_shmwrite */
	Perl_pp_semget,
	Perl_pp_semctl,
	Perl_pp_require,
	Perl_pp_dofile,	/* implemented by Perl_pp_require */
	Perl_pp_hintseval,
	Perl_pp_entereval,
	Perl_pp_leaveeval,
	Perl_pp_entertry,
	Perl_pp_leavetry,
	Perl_pp_ghbyname,	/* implemented by Perl_pp_ghostent */
	Perl_pp_ghbyaddr,	/* implemented by Perl_pp_ghostent */
	Perl_pp_ghostent,
	Perl_pp_gnbyname,	/* implemented by Perl_pp_gnetent */
	Perl_pp_gnbyaddr,	/* implemented by Perl_pp_gnetent */
	Perl_pp_gnetent,
	Perl_pp_gpbyname,	/* implemented by Perl_pp_gprotoent */
	Perl_pp_gpbynumber,	/* implemented by Perl_pp_gprotoent */
	Perl_pp_gprotoent,
	Perl_pp_gsbyname,	/* implemented by Perl_pp_gservent */
	Perl_pp_gsbyport,	/* implemented by Perl_pp_gservent */
	Perl_pp_gservent,
	Perl_pp_shostent,
	Perl_pp_snetent,	/* implemented by Perl_pp_shostent */
	Perl_pp_sprotoent,	/* implemented by Perl_pp_shostent */
	Perl_pp_sservent,	/* implemented by Perl_pp_shostent */
	Perl_pp_ehostent,
	Perl_pp_enetent,	/* implemented by Perl_pp_ehostent */
	Perl_pp_eprotoent,	/* implemented by Perl_pp_ehostent */
	Perl_pp_eservent,	/* implemented by Perl_pp_ehostent */
	Perl_pp_gpwnam,	/* implemented by Perl_pp_gpwent */
	Perl_pp_gpwuid,	/* implemented by Perl_pp_gpwent */
	Perl_pp_gpwent,
	Perl_pp_spwent,	/* implemented by Perl_pp_ehostent */
	Perl_pp_epwent,	/* implemented by Perl_pp_ehostent */
	Perl_pp_ggrnam,	/* implemented by Perl_pp_ggrent */
	Perl_pp_ggrgid,	/* implemented by Perl_pp_ggrent */
	Perl_pp_ggrent,
	Perl_pp_sgrent,	/* implemented by Perl_pp_ehostent */
	Perl_pp_egrent,	/* implemented by Perl_pp_ehostent */
	Perl_pp_getlogin,
	Perl_pp_syscall,
	Perl_pp_lock,
	Perl_pp_once,
	Perl_pp_custom,	/* implemented by Perl_unimplemented_op */
	Perl_pp_coreargs,
	Perl_pp_avhvswitch,
	Perl_pp_runcv,
	Perl_pp_fc,
	Perl_pp_padcv,
	Perl_pp_introcv,
	Perl_pp_clonecv,
	Perl_pp_padrange,
	Perl_pp_refassign,
	Perl_pp_lvref,
	Perl_pp_lvrefslice,
	Perl_pp_lvavref,
	Perl_pp_anonconst,
	Perl_pp_isa,
	Perl_pp_cmpchain_and,
	Perl_pp_cmpchain_dup,
	Perl_pp_entertrycatch,
	Perl_pp_leavetrycatch,
	Perl_pp_poptry,
	Perl_pp_catch,
	Perl_pp_pushdefer,
	Perl_pp_is_bool,
	Perl_pp_is_weak,
	Perl_pp_weaken,
	Perl_pp_unweaken,
	Perl_pp_blessed,
	Perl_pp_refaddr,
	Perl_pp_reftype,
	Perl_pp_ceil,
	Perl_pp_floor,
	Perl_pp_is_tainted,
	Perl_pp_helemexistsor,
	Perl_pp_methstart,
	Perl_pp_initfield,
});

EXT Perl_check_t PL_check[] /* or perlvars.h */
INIT({
	Perl_ck_null,		/* null */
	Perl_ck_null,		/* stub */
	Perl_ck_fun,		/* scalar */
	Perl_ck_null,		/* pushmark */
	Perl_ck_null,		/* wantarray */
	Perl_ck_svconst,	/* const */
	Perl_ck_null,		/* gvsv */
	Perl_ck_null,		/* gv */
	Perl_ck_null,		/* gelem */
	Perl_ck_null,		/* padsv */
	Perl_ck_sassign,	/* padsv_store */
	Perl_ck_null,		/* padav */
	Perl_ck_null,		/* padhv */
	Perl_ck_null,		/* padany */
	Perl_ck_rvconst,	/* rv2gv */
	Perl_ck_rvconst,	/* rv2sv */
	Perl_ck_null,		/* av2arylen */
	Perl_ck_rvconst,	/* rv2cv */
	Perl_ck_anoncode,	/* anoncode */
	Perl_ck_prototype,	/* prototype */
	Perl_ck_spair,		/* refgen */
	Perl_ck_null,		/* srefgen */
	Perl_ck_fun,		/* ref */
	Perl_ck_fun,		/* bless */
	Perl_ck_backtick,	/* backtick */
	Perl_ck_glob,		/* glob */
	Perl_ck_readline,	/* readline */
	Perl_ck_null,		/* rcatline */
	Perl_ck_fun,		/* regcmaybe */
	Perl_ck_fun,		/* regcreset */
	Perl_ck_null,		/* regcomp */
	Perl_ck_match,		/* match */
	Perl_ck_match,		/* qr */
	Perl_ck_match,		/* subst */
	Perl_ck_null,		/* substcont */
	Perl_ck_match,		/* trans */
	Perl_ck_match,		/* transr */
	Perl_ck_sassign,	/* sassign */
	Perl_ck_null,		/* aassign */
	Perl_ck_spair,		/* chop */
	Perl_ck_null,		/* schop */
	Perl_ck_spair,		/* chomp */
	Perl_ck_null,		/* schomp */
	Perl_ck_defined,	/* defined */
	Perl_ck_fun,		/* undef */
	Perl_ck_fun,		/* study */
	Perl_ck_fun,		/* pos */
	Perl_ck_lfun,		/* preinc */
	Perl_ck_lfun,		/* i_preinc */
	Perl_ck_lfun,		/* predec */
	Perl_ck_lfun,		/* i_predec */
	Perl_ck_lfun,		/* postinc */
	Perl_ck_lfun,		/* i_postinc */
	Perl_ck_lfun,		/* postdec */
	Perl_ck_lfun,		/* i_postdec */
	Perl_ck_null,		/* pow */
	Perl_ck_null,		/* multiply */
	Perl_ck_null,		/* i_multiply */
	Perl_ck_null,		/* divide */
	Perl_ck_null,		/* i_divide */
	Perl_ck_null,		/* modulo */
	Perl_ck_null,		/* i_modulo */
	Perl_ck_repeat,		/* repeat */
	Perl_ck_null,		/* add */
	Perl_ck_null,		/* i_add */
	Perl_ck_null,		/* subtract */
	Perl_ck_null,		/* i_subtract */
	Perl_ck_concat,		/* concat */
	Perl_ck_null,		/* multiconcat */
	Perl_ck_stringify,	/* stringify */
	Perl_ck_bitop,		/* left_shift */
	Perl_ck_bitop,		/* right_shift */
	Perl_ck_cmp,		/* lt */
	Perl_ck_cmp,		/* i_lt */
	Perl_ck_cmp,		/* gt */
	Perl_ck_cmp,		/* i_gt */
	Perl_ck_cmp,		/* le */
	Perl_ck_cmp,		/* i_le */
	Perl_ck_cmp,		/* ge */
	Perl_ck_cmp,		/* i_ge */
	Perl_ck_cmp,		/* eq */
	Perl_ck_cmp,		/* i_eq */
	Perl_ck_cmp,		/* ne */
	Perl_ck_cmp,		/* i_ne */
	Perl_ck_null,		/* ncmp */
	Perl_ck_null,		/* i_ncmp */
	Perl_ck_null,		/* slt */
	Perl_ck_null,		/* sgt */
	Perl_ck_null,		/* sle */
	Perl_ck_null,		/* sge */
	Perl_ck_null,		/* seq */
	Perl_ck_null,		/* sne */
	Perl_ck_null,		/* scmp */
	Perl_ck_bitop,		/* bit_and */
	Perl_ck_bitop,		/* bit_xor */
	Perl_ck_bitop,		/* bit_or */
	Perl_ck_bitop,		/* nbit_and */
	Perl_ck_bitop,		/* nbit_xor */
	Perl_ck_bitop,		/* nbit_or */
	Perl_ck_bitop,		/* sbit_and */
	Perl_ck_bitop,		/* sbit_xor */
	Perl_ck_bitop,		/* sbit_or */
	Perl_ck_null,		/* negate */
	Perl_ck_null,		/* i_negate */
	Perl_ck_null,		/* not */
	Perl_ck_bitop,		/* complement */
	Perl_ck_bitop,		/* ncomplement */
	Perl_ck_null,		/* scomplement */
	Perl_ck_smartmatch,	/* smartmatch */
	Perl_ck_fun,		/* atan2 */
	Perl_ck_fun,		/* sin */
	Perl_ck_fun,		/* cos */
	Perl_ck_fun,		/* rand */
	Perl_ck_fun,		/* srand */
	Perl_ck_fun,		/* exp */
	Perl_ck_fun,		/* log */
	Perl_ck_fun,		/* sqrt */
	Perl_ck_fun,		/* int */
	Perl_ck_fun,		/* hex */
	Perl_ck_fun,		/* oct */
	Perl_ck_fun,		/* abs */
	Perl_ck_length,		/* length */
	Perl_ck_substr,		/* substr */
	Perl_ck_fun,		/* vec */
	Perl_ck_index,		/* index */
	Perl_ck_index,		/* rindex */
	Perl_ck_lfun,		/* sprintf */
	Perl_ck_fun,		/* formline */
	Perl_ck_fun,		/* ord */
	Perl_ck_fun,		/* chr */
	Perl_ck_fun,		/* crypt */
	Perl_ck_fun,		/* ucfirst */
	Perl_ck_fun,		/* lcfirst */
	Perl_ck_fun,		/* uc */
	Perl_ck_fun,		/* lc */
	Perl_ck_fun,		/* quotemeta */
	Perl_ck_rvconst,	/* rv2av */
	Perl_ck_null,		/* aelemfast */
	Perl_ck_null,		/* aelemfast_lex */
	Perl_ck_null,		/* aelemfastlex_store */
	Perl_ck_null,		/* aelem */
	Perl_ck_null,		/* aslice */
	Perl_ck_null,		/* kvaslice */
	Perl_ck_each,		/* aeach */
	Perl_ck_each,		/* avalues */
	Perl_ck_each,		/* akeys */
	Perl_ck_each,		/* each */
	Perl_ck_each,		/* values */
	Perl_ck_each,		/* keys */
	Perl_ck_delete,		/* delete */
	Perl_ck_exists,		/* exists */
	Perl_ck_rvconst,	/* rv2hv */
	Perl_ck_null,		/* helem */
	Perl_ck_null,		/* hslice */
	Perl_ck_null,		/* kvhslice */
	Perl_ck_null,		/* multideref */
	Perl_ck_fun,		/* unpack */
	Perl_ck_fun,		/* pack */
	Perl_ck_split,		/* split */
	Perl_ck_join,		/* join */
	Perl_ck_null,		/* list */
	Perl_ck_null,		/* lslice */
	Perl_ck_fun,		/* anonlist */
	Perl_ck_fun,		/* anonhash */
	Perl_ck_fun,		/* emptyavhv */
	Perl_ck_fun,		/* splice */
	Perl_ck_fun,		/* push */
	Perl_ck_shift,		/* pop */
	Perl_ck_shift,		/* shift */
	Perl_ck_fun,		/* unshift */
	Perl_ck_sort,		/* sort */
	Perl_ck_fun,		/* reverse */
	Perl_ck_grep,		/* grepstart */
	Perl_ck_null,		/* grepwhile */
	Perl_ck_grep,		/* mapstart */
	Perl_ck_null,		/* mapwhile */
	Perl_ck_null,		/* range */
	Perl_ck_null,		/* flip */
	Perl_ck_null,		/* flop */
	Perl_ck_null,		/* and */
	Perl_ck_null,		/* or */
	Perl_ck_null,		/* xor */
	Perl_ck_null,		/* dor */
	Perl_ck_null,		/* cond_expr */
	Perl_ck_null,		/* andassign */
	Perl_ck_null,		/* orassign */
	Perl_ck_null,		/* dorassign */
	Perl_ck_subr,		/* entersub */
	Perl_ck_null,		/* leavesub */
	Perl_ck_null,		/* leavesublv */
	Perl_ck_null,		/* argcheck */
	Perl_ck_null,		/* argelem */
	Perl_ck_null,		/* argdefelem */
	Perl_ck_fun,		/* caller */
	Perl_ck_fun,		/* warn */
	Perl_ck_fun,		/* die */
	Perl_ck_fun,		/* reset */
	Perl_ck_null,		/* lineseq */
	Perl_ck_null,		/* nextstate */
	Perl_ck_null,		/* dbstate */
	Perl_ck_null,		/* unstack */
	Perl_ck_null,		/* enter */
	Perl_ck_null,		/* leave */
	Perl_ck_null,		/* scope */
	Perl_ck_null,		/* enteriter */
	Perl_ck_null,		/* iter */
	Perl_ck_null,		/* enterloop */
	Perl_ck_null,		/* leaveloop */
	Perl_ck_return,		/* return */
	Perl_ck_null,		/* last */
	Perl_ck_null,		/* next */
	Perl_ck_null,		/* redo */
	Perl_ck_null,		/* dump */
	Perl_ck_null,		/* goto */
	Perl_ck_fun,		/* exit */
	Perl_ck_method,		/* method */
	Perl_ck_null,		/* method_named */
	Perl_ck_null,		/* method_super */
	Perl_ck_null,		/* method_redir */
	Perl_ck_null,		/* method_redir_super */
	Perl_ck_null,		/* entergiven */
	Perl_ck_null,		/* leavegiven */
	Perl_ck_null,		/* enterwhen */
	Perl_ck_null,		/* leavewhen */
	Perl_ck_null,		/* break */
	Perl_ck_null,		/* continue */
	Perl_ck_open,		/* open */
	Perl_ck_fun,		/* close */
	Perl_ck_fun,		/* pipe_op */
	Perl_ck_fun,		/* fileno */
	Perl_ck_fun,		/* umask */
	Perl_ck_fun,		/* binmode */
	Perl_ck_fun,		/* tie */
	Perl_ck_fun,		/* untie */
	Perl_ck_fun,		/* tied */
	Perl_ck_fun,		/* dbmopen */
	Perl_ck_fun,		/* dbmclose */
	Perl_ck_select,		/* sselect */
	Perl_ck_select,		/* select */
	Perl_ck_eof,		/* getc */
	Perl_ck_fun,		/* read */
	Perl_ck_fun,		/* enterwrite */
	Perl_ck_null,		/* leavewrite */
	Perl_ck_listiob,	/* prtf */
	Perl_ck_listiob,	/* print */
	Perl_ck_listiob,	/* say */
	Perl_ck_fun,		/* sysopen */
	Perl_ck_fun,		/* sysseek */
	Perl_ck_fun,		/* sysread */
	Perl_ck_fun,		/* syswrite */
	Perl_ck_eof,		/* eof */
	Perl_ck_tell,		/* tell */
	Perl_ck_tell,		/* seek */
	Perl_ck_trunc,		/* truncate */
	Perl_ck_fun,		/* fcntl */
	Perl_ck_fun,		/* ioctl */
	Perl_ck_fun,		/* flock */
	Perl_ck_fun,		/* send */
	Perl_ck_fun,		/* recv */
	Perl_ck_fun,		/* socket */
	Perl_ck_fun,		/* sockpair */
	Perl_ck_fun,		/* bind */
	Perl_ck_fun,		/* connect */
	Perl_ck_fun,		/* listen */
	Perl_ck_fun,		/* accept */
	Perl_ck_fun,		/* shutdown */
	Perl_ck_fun,		/* gsockopt */
	Perl_ck_fun,		/* ssockopt */
	Perl_ck_fun,		/* getsockname */
	Perl_ck_fun,		/* getpeername */
	Perl_ck_ftst,		/* lstat */
	Perl_ck_ftst,		/* stat */
	Perl_ck_ftst,		/* ftrread */
	Perl_ck_ftst,		/* ftrwrite */
	Perl_ck_ftst,		/* ftrexec */
	Perl_ck_ftst,		/* fteread */
	Perl_ck_ftst,		/* ftewrite */
	Perl_ck_ftst,		/* fteexec */
	Perl_ck_ftst,		/* ftis */
	Perl_ck_ftst,		/* ftsize */
	Perl_ck_ftst,		/* ftmtime */
	Perl_ck_ftst,		/* ftatime */
	Perl_ck_ftst,		/* ftctime */
	Perl_ck_ftst,		/* ftrowned */
	Perl_ck_ftst,		/* fteowned */
	Perl_ck_ftst,		/* ftzero */
	Perl_ck_ftst,		/* ftsock */
	Perl_ck_ftst,		/* ftchr */
	Perl_ck_ftst,		/* ftblk */
	Perl_ck_ftst,		/* ftfile */
	Perl_ck_ftst,		/* ftdir */
	Perl_ck_ftst,		/* ftpipe */
	Perl_ck_ftst,		/* ftsuid */
	Perl_ck_ftst,		/* ftsgid */
	Perl_ck_ftst,		/* ftsvtx */
	Perl_ck_ftst,		/* ftlink */
	Perl_ck_ftst,		/* fttty */
	Perl_ck_ftst,		/* fttext */
	Perl_ck_ftst,		/* ftbinary */
	Perl_ck_trunc,		/* chdir */
	Perl_ck_fun,		/* chown */
	Perl_ck_fun,		/* chroot */
	Perl_ck_fun,		/* unlink */
	Perl_ck_fun,		/* chmod */
	Perl_ck_fun,		/* utime */
	Perl_ck_fun,		/* rename */
	Perl_ck_fun,		/* link */
	Perl_ck_fun,		/* symlink */
	Perl_ck_fun,		/* readlink */
	Perl_ck_fun,		/* mkdir */
	Perl_ck_fun,		/* rmdir */
	Perl_ck_fun,		/* open_dir */
	Perl_ck_fun,		/* readdir */
	Perl_ck_fun,		/* telldir */
	Perl_ck_fun,		/* seekdir */
	Perl_ck_fun,		/* rewinddir */
	Perl_ck_fun,		/* closedir */
	Perl_ck_null,		/* fork */
	Perl_ck_null,		/* wait */
	Perl_ck_fun,		/* waitpid */
	Perl_ck_exec,		/* system */
	Perl_ck_exec,		/* exec */
	Perl_ck_fun,		/* kill */
	Perl_ck_null,		/* getppid */
	Perl_ck_fun,		/* getpgrp */
	Perl_ck_fun,		/* setpgrp */
	Perl_ck_fun,		/* getpriority */
	Perl_ck_fun,		/* setpriority */
	Perl_ck_null,		/* time */
	Perl_ck_null,		/* tms */
	Perl_ck_fun,		/* localtime */
	Perl_ck_fun,		/* gmtime */
	Perl_ck_fun,		/* alarm */
	Perl_ck_fun,		/* sleep */
	Perl_ck_fun,		/* shmget */
	Perl_ck_fun,		/* shmctl */
	Perl_ck_fun,		/* shmread */
	Perl_ck_fun,		/* shmwrite */
	Perl_ck_fun,		/* msgget */
	Perl_ck_fun,		/* msgctl */
	Perl_ck_fun,		/* msgsnd */
	Perl_ck_fun,		/* msgrcv */
	Perl_ck_fun,		/* semop */
	Perl_ck_fun,		/* semget */
	Perl_ck_fun,		/* semctl */
	Perl_ck_require,	/* require */
	Perl_ck_fun,		/* dofile */
	Perl_ck_svconst,	/* hintseval */
	Perl_ck_eval,		/* entereval */
	Perl_ck_null,		/* leaveeval */
	Perl_ck_eval,		/* entertry */
	Perl_ck_null,		/* leavetry */
	Perl_ck_fun,		/* ghbyname */
	Perl_ck_fun,		/* ghbyaddr */
	Perl_ck_null,		/* ghostent */
	Perl_ck_fun,		/* gnbyname */
	Perl_ck_fun,		/* gnbyaddr */
	Perl_ck_null,		/* gnetent */
	Perl_ck_fun,		/* gpbyname */
	Perl_ck_fun,		/* gpbynumber */
	Perl_ck_null,		/* gprotoent */
	Perl_ck_fun,		/* gsbyname */
	Perl_ck_fun,		/* gsbyport */
	Perl_ck_null,		/* gservent */
	Perl_ck_fun,		/* shostent */
	Perl_ck_fun,		/* snetent */
	Perl_ck_fun,		/* sprotoent */
	Perl_ck_fun,		/* sservent */
	Perl_ck_null,		/* ehostent */
	Perl_ck_null,		/* enetent */
	Perl_ck_null,		/* eprotoent */
	Perl_ck_null,		/* eservent */
	Perl_ck_fun,		/* gpwnam */
	Perl_ck_fun,		/* gpwuid */
	Perl_ck_null,		/* gpwent */
	Perl_ck_null,		/* spwent */
	Perl_ck_null,		/* epwent */
	Perl_ck_fun,		/* ggrnam */
	Perl_ck_fun,		/* ggrgid */
	Perl_ck_null,		/* ggrent */
	Perl_ck_null,		/* sgrent */
	Perl_ck_null,		/* egrent */
	Perl_ck_null,		/* getlogin */
	Perl_ck_fun,		/* syscall */
	Perl_ck_rfun,		/* lock */
	Perl_ck_null,		/* once */
	Perl_ck_null,		/* custom */
	Perl_ck_null,		/* coreargs */
	Perl_ck_null,		/* avhvswitch */
	Perl_ck_null,		/* runcv */
	Perl_ck_fun,		/* fc */
	Perl_ck_null,		/* padcv */
	Perl_ck_null,		/* introcv */
	Perl_ck_null,		/* clonecv */
	Perl_ck_null,		/* padrange */
	Perl_ck_refassign,	/* refassign */
	Perl_ck_null,		/* lvref */
	Perl_ck_null,		/* lvrefslice */
	Perl_ck_null,		/* lvavref */
	Perl_ck_null,		/* anonconst */
	Perl_ck_isa,		/* isa */
	Perl_ck_null,		/* cmpchain_and */
	Perl_ck_null,		/* cmpchain_dup */
	Perl_ck_trycatch,	/* entertrycatch */
	Perl_ck_null,		/* leavetrycatch */
	Perl_ck_null,		/* poptry */
	Perl_ck_null,		/* catch */
	Perl_ck_null,		/* pushdefer */
	Perl_ck_null,		/* is_bool */
	Perl_ck_null,		/* is_weak */
	Perl_ck_null,		/* weaken */
	Perl_ck_null,		/* unweaken */
	Perl_ck_null,		/* blessed */
	Perl_ck_null,		/* refaddr */
	Perl_ck_null,		/* reftype */
	Perl_ck_null,		/* ceil */
	Perl_ck_null,		/* floor */
	Perl_ck_null,		/* is_tainted */
	Perl_ck_helemexistsor,	/* helemexistsor */
	Perl_ck_null,		/* methstart */
	Perl_ck_null,		/* initfield */
});

EXTCONST U32 PL_opargs[] INIT({
	0x00000000,	/* null */
	0x00000000,	/* stub */
	0x00001b04,	/* scalar */
	0x00000004,	/* pushmark */
	0x00000004,	/* wantarray */
	0x00000604,	/* const */
	0x00000644,	/* gvsv */
	0x00000644,	/* gv */
	0x00011244,	/* gelem */
	0x00000044,	/* padsv */
	0x00011104,	/* padsv_store */
	0x00000040,	/* padav */
	0x00000040,	/* padhv */
	0x00000040,	/* padany */
	0x00000144,	/* rv2gv */
	0x00000144,	/* rv2sv */
	0x00000104,	/* av2arylen */
	0x00000140,	/* rv2cv */
	0x00000604,	/* anoncode */
	0x00009b84,	/* prototype */
	0x00002101,	/* refgen */
	0x00001106,	/* srefgen */
	0x00009b8c,	/* ref */
	0x00091404,	/* bless */
	0x00009b88,	/* backtick */
	0x00009408,	/* glob */
	0x0000eb08,	/* readline */
	0x00000608,	/* rcatline */
	0x00001104,	/* regcmaybe */
	0x00001104,	/* regcreset */
	0x00001304,	/* regcomp */
	0x00000500,	/* match */
	0x00000504,	/* qr */
	0x00001504,	/* subst */
	0x00000304,	/* substcont */
	0x00001804,	/* trans */
	0x00001804,	/* transr */
	0x00011204,	/* sassign */
	0x00022208,	/* aassign */
	0x00002b0d,	/* chop */
	0x00009b8c,	/* schop */
	0x00002b1d,	/* chomp */
	0x00009b9c,	/* schomp */
	0x00009b84,	/* defined */
	0x0000fb04,	/* undef */
	0x00009b84,	/* study */
	0x0000fb8c,	/* pos */
	0x00001164,	/* preinc */
	0x00001144,	/* i_preinc */
	0x00001164,	/* predec */
	0x00001144,	/* i_predec */
	0x0000112c,	/* postinc */
	0x0000110c,	/* i_postinc */
	0x0000112c,	/* postdec */
	0x0000110c,	/* i_postdec */
	0x0001121e,	/* pow */
	0x0001123e,	/* multiply */
	0x0001121e,	/* i_multiply */
	0x0001123e,	/* divide */
	0x0001121e,	/* i_divide */
	0x0001123e,	/* modulo */
	0x0001121e,	/* i_modulo */
	0x0001220b,	/* repeat */
	0x0001123e,	/* add */
	0x0001121e,	/* i_add */
	0x0001123e,	/* subtract */
	0x0001121e,	/* i_subtract */
	0x0001121e,	/* concat */
	0x00000f1c,	/* multiconcat */
	0x0000141e,	/* stringify */
	0x0001121e,	/* left_shift */
	0x0001121e,	/* right_shift */
	0x00011226,	/* lt */
	0x00011206,	/* i_lt */
	0x00011226,	/* gt */
	0x00011206,	/* i_gt */
	0x00011226,	/* le */
	0x00011206,	/* i_le */
	0x00011226,	/* ge */
	0x00011206,	/* i_ge */
	0x00011226,	/* eq */
	0x00011206,	/* i_eq */
	0x00011226,	/* ne */
	0x00011206,	/* i_ne */
	0x0001122e,	/* ncmp */
	0x0001120e,	/* i_ncmp */
	0x00011206,	/* slt */
	0x00011206,	/* sgt */
	0x00011206,	/* sle */
	0x00011206,	/* sge */
	0x00011206,	/* seq */
	0x00011206,	/* sne */
	0x0001120e,	/* scmp */
	0x0001120e,	/* bit_and */
	0x0001120e,	/* bit_xor */
	0x0001120e,	/* bit_or */
	0x0001121e,	/* nbit_and */
	0x0001121e,	/* nbit_xor */
	0x0001121e,	/* nbit_or */
	0x0001120e,	/* sbit_and */
	0x0001120e,	/* sbit_xor */
	0x0001120e,	/* sbit_or */
	0x0000112e,	/* negate */
	0x0000110e,	/* i_negate */
	0x00001106,	/* not */
	0x0000110e,	/* complement */
	0x0000111e,	/* ncomplement */
	0x0000111e,	/* scomplement */
	0x00000204,	/* smartmatch */
	0x0001141e,	/* atan2 */
	0x00009b9e,	/* sin */
	0x00009b9e,	/* cos */
	0x00009b1c,	/* rand */
	0x00009b1c,	/* srand */
	0x00009b9e,	/* exp */
	0x00009b9e,	/* log */
	0x00009b9e,	/* sqrt */
	0x00009b9e,	/* int */
	0x00009b9e,	/* hex */
	0x00009b9e,	/* oct */
	0x00009b9e,	/* abs */
	0x00009b9e,	/* length */
	0x0991140c,	/* substr */
	0x0011140c,	/* vec */
	0x0091141c,	/* index */
	0x0091141c,	/* rindex */
	0x0002140f,	/* sprintf */
	0x00021405,	/* formline */
	0x00009b9e,	/* ord */
	0x00009b9e,	/* chr */
	0x0001141e,	/* crypt */
	0x00009b8e,	/* ucfirst */
	0x00009b8e,	/* lcfirst */
	0x00009b8e,	/* uc */
	0x00009b8e,	/* lc */
	0x00009b8e,	/* quotemeta */
	0x00000148,	/* rv2av */
	0x00013644,	/* aelemfast */
	0x00013040,	/* aelemfast_lex */
	0x00013140,	/* aelemfastlex_store */
	0x00013204,	/* aelem */
	0x00023401,	/* aslice */
	0x00023401,	/* kvaslice */
	0x00003b40,	/* aeach */
	0x00003b48,	/* avalues */
	0x00003b08,	/* akeys */
	0x00004b40,	/* each */
	0x00004b48,	/* values */
	0x00004b08,	/* keys */
	0x00001b00,	/* delete */
	0x00001b04,	/* exists */
	0x00000148,	/* rv2hv */
	0x00014204,	/* helem */
	0x00024401,	/* hslice */
	0x00024401,	/* kvhslice */
	0x00000f44,	/* multideref */
	0x00091480,	/* unpack */
	0x0002140f,	/* pack */
	0x00111508,	/* split */
	0x0002140f,	/* join */
	0x00002401,	/* list */
	0x00224200,	/* lslice */
	0x00002405,	/* anonlist */
	0x00002405,	/* anonhash */
	0x0000241c,	/* emptyavhv */
	0x02993401,	/* splice */
	0x0002341d,	/* push */
	0x0000bb04,	/* pop */
	0x0000bb04,	/* shift */
	0x0002341d,	/* unshift */
	0x0002d401,	/* sort */
	0x00002409,	/* reverse */
	0x00025401,	/* grepstart */
	0x00000308,	/* grepwhile */
	0x00025401,	/* mapstart */
	0x00000308,	/* mapwhile */
	0x00011300,	/* range */
	0x00011100,	/* flip */
	0x00000100,	/* flop */
	0x00000300,	/* and */
	0x00000300,	/* or */
	0x00011206,	/* xor */
	0x00000300,	/* dor */
	0x00000300,	/* cond_expr */
	0x00000304,	/* andassign */
	0x00000304,	/* orassign */
	0x00000304,	/* dorassign */
	0x00002141,	/* entersub */
	0x00000100,	/* leavesub */
	0x00000100,	/* leavesublv */
	0x00000f00,	/* argcheck */
	0x00000f00,	/* argelem */
	0x00000300,	/* argdefelem */
	0x00009b08,	/* caller */
	0x0000240d,	/* warn */
	0x0000240d,	/* die */
	0x00009b04,	/* reset */
	0x00000400,	/* lineseq */
	0x00000a04,	/* nextstate */
	0x00000a04,	/* dbstate */
	0x00000004,	/* unstack */
	0x00000000,	/* enter */
	0x00000400,	/* leave */
	0x00000400,	/* scope */
	0x00000940,	/* enteriter */
	0x00000000,	/* iter */
	0x00000940,	/* enterloop */
	0x00000200,	/* leaveloop */
	0x00002401,	/* return */
	0x00000d04,	/* last */
	0x00000d04,	/* next */
	0x00000d04,	/* redo */
	0x00000d44,	/* dump */
	0x00000d04,	/* goto */
	0x00009b04,	/* exit */
	0x00000e40,	/* method */
	0x00000e40,	/* method_named */
	0x00000e40,	/* method_super */
	0x00000e40,	/* method_redir */
	0x00000e40,	/* method_redir_super */
	0x00000340,	/* entergiven */
	0x00000100,	/* leavegiven */
	0x00000340,	/* enterwhen */
	0x00000100,	/* leavewhen */
	0x00000000,	/* break */
	0x00000000,	/* continue */
	0x0029640d,	/* open */
	0x0000eb04,	/* close */
	0x00066404,	/* pipe_op */
	0x00006b0c,	/* fileno */
	0x00009b0c,	/* umask */
	0x00096404,	/* binmode */
	0x00217445,	/* tie */
	0x00007b04,	/* untie */
	0x00007b44,	/* tied */
	0x00114404,	/* dbmopen */
	0x00004b04,	/* dbmclose */
	0x01111408,	/* sselect */
	0x0000e40c,	/* select */
	0x0000eb0c,	/* getc */
	0x0917640d,	/* read */
	0x0000eb04,	/* enterwrite */
	0x00000100,	/* leavewrite */
	0x0002e405,	/* prtf */
	0x0002e405,	/* print */
	0x0002e405,	/* say */
	0x09116404,	/* sysopen */
	0x00116404,	/* sysseek */
	0x0917640d,	/* sysread */
	0x0991640d,	/* syswrite */
	0x0000eb04,	/* eof */
	0x0000eb0c,	/* tell */
	0x00116404,	/* seek */
	0x00011404,	/* truncate */
	0x0011640c,	/* fcntl */
	0x0011640c,	/* ioctl */
	0x0001641c,	/* flock */
	0x0911640d,	/* send */
	0x0117640d,	/* recv */
	0x01116404,	/* socket */
	0x11166404,	/* sockpair */
	0x00016404,	/* bind */
	0x00016404,	/* connect */
	0x00016404,	/* listen */
	0x0006640c,	/* accept */
	0x0001640c,	/* shutdown */
	0x00116404,	/* gsockopt */
	0x01116404,	/* ssockopt */
	0x00006b04,	/* getsockname */
	0x00006b04,	/* getpeername */
	0x0000ec80,	/* lstat */
	0x0000ec80,	/* stat */
	0x00006c84,	/* ftrread */
	0x00006c84,	/* ftrwrite */
	0x00006c84,	/* ftrexec */
	0x00006c84,	/* fteread */
	0x00006c84,	/* ftewrite */
	0x00006c84,	/* fteexec */
	0x00006c84,	/* ftis */
	0x00006c8c,	/* ftsize */
	0x00006c8c,	/* ftmtime */
	0x00006c8c,	/* ftatime */
	0x00006c8c,	/* ftctime */
	0x00006c84,	/* ftrowned */
	0x00006c84,	/* fteowned */
	0x00006c84,	/* ftzero */
	0x00006c84,	/* ftsock */
	0x00006c84,	/* ftchr */
	0x00006c84,	/* ftblk */
	0x00006c84,	/* ftfile */
	0x00006c84,	/* ftdir */
	0x00006c84,	/* ftpipe */
	0x00006c84,	/* ftsuid */
	0x00006c84,	/* ftsgid */
	0x00006c84,	/* ftsvtx */
	0x00006c84,	/* ftlink */
	0x00006c04,	/* fttty */
	0x00006c84,	/* fttext */
	0x00006c84,	/* ftbinary */
	0x00009b1c,	/* chdir */
	0x0000241d,	/* chown */
	0x00009b9c,	/* chroot */
	0x0000249d,	/* unlink */
	0x0000241d,	/* chmod */
	0x0000241d,	/* utime */
	0x0001141c,	/* rename */
	0x0001141c,	/* link */
	0x0001141c,	/* symlink */
	0x00009b8c,	/* readlink */
	0x0009949c,	/* mkdir */
	0x00009b9c,	/* rmdir */
	0x00016404,	/* open_dir */
	0x00006b00,	/* readdir */
	0x00006b0c,	/* telldir */
	0x00016404,	/* seekdir */
	0x00006b04,	/* rewinddir */
	0x00006b04,	/* closedir */
	0x0000000c,	/* fork */
	0x0000001c,	/* wait */
	0x0001141c,	/* waitpid */
	0x0002941d,	/* system */
	0x0002941d,	/* exec */
	0x0000241d,	/* kill */
	0x0000001c,	/* getppid */
	0x00009b1c,	/* getpgrp */
	0x0009941c,	/* setpgrp */
	0x0001141c,	/* getpriority */
	0x0011141c,	/* setpriority */
	0x0000001c,	/* time */
	0x00000000,	/* tms */
	0x00009b08,	/* localtime */
	0x00009b08,	/* gmtime */
	0x00009b8c,	/* alarm */
	0x00009b1c,	/* sleep */
	0x0011140d,	/* shmget */
	0x0011140d,	/* shmctl */
	0x0111140d,	/* shmread */
	0x0111140d,	/* shmwrite */
	0x0001140d,	/* msgget */
	0x0011140d,	/* msgctl */
	0x0011140d,	/* msgsnd */
	0x1111140d,	/* msgrcv */
	0x0001140d,	/* semop */
	0x0011140d,	/* semget */
	0x0111140d,	/* semctl */
	0x00009bc4,	/* require */
	0x00001140,	/* dofile */
	0x00000604,	/* hintseval */
	0x00009bc0,	/* entereval */
	0x00001100,	/* leaveeval */
	0x00000340,	/* entertry */
	0x00000400,	/* leavetry */
	0x00001b00,	/* ghbyname */
	0x00011400,	/* ghbyaddr */
	0x00000000,	/* ghostent */
	0x00001b00,	/* gnbyname */
	0x00011400,	/* gnbyaddr */
	0x00000000,	/* gnetent */
	0x00001b00,	/* gpbyname */
	0x00001400,	/* gpbynumber */
	0x00000000,	/* gprotoent */
	0x00011400,	/* gsbyname */
	0x00011400,	/* gsbyport */
	0x00000000,	/* gservent */
	0x00001b04,	/* shostent */
	0x00001b04,	/* snetent */
	0x00001b04,	/* sprotoent */
	0x00001b04,	/* sservent */
	0x00000004,	/* ehostent */
	0x00000004,	/* enetent */
	0x00000004,	/* eprotoent */
	0x00000004,	/* eservent */
	0x00001b00,	/* gpwnam */
	0x00001b00,	/* gpwuid */
	0x00000000,	/* gpwent */
	0x00000004,	/* spwent */
	0x00000004,	/* epwent */
	0x00001b00,	/* ggrnam */
	0x00001b00,	/* ggrgid */
	0x00000000,	/* ggrent */
	0x00000004,	/* sgrent */
	0x00000004,	/* egrent */
	0x0000000c,	/* getlogin */
	0x0002140d,	/* syscall */
	0x00007b04,	/* lock */
	0x00000300,	/* once */
	0x00000000,	/* custom */
	0x00000600,	/* coreargs */
	0x00000108,	/* avhvswitch */
	0x00000004,	/* runcv */
	0x00009b8e,	/* fc */
	0x00000040,	/* padcv */
	0x00000040,	/* introcv */
	0x00000040,	/* clonecv */
	0x00000040,	/* padrange */
	0x00000244,	/* refassign */
	0x00000b40,	/* lvref */
	0x00000440,	/* lvrefslice */
	0x00000b40,	/* lvavref */
	0x00000144,	/* anonconst */
	0x00000204,	/* isa */
	0x00000300,	/* cmpchain_and */
	0x00000100,	/* cmpchain_dup */
	0x00000300,	/* entertrycatch */
	0x00000400,	/* leavetrycatch */
	0x00000400,	/* poptry */
	0x00000300,	/* catch */
	0x00000300,	/* pushdefer */
	0x00000106,	/* is_bool */
	0x00000106,	/* is_weak */
	0x00000100,	/* weaken */
	0x00000100,	/* unweaken */
	0x00000106,	/* blessed */
	0x0000011e,	/* refaddr */
	0x0000011e,	/* reftype */
	0x0000011e,	/* ceil */
	0x0000011e,	/* floor */
	0x00000106,	/* is_tainted */
	0x00011300,	/* helemexistsor */
	0x00000f00,	/* methstart */
	0x00000f00,	/* initfield */
});

END_EXTERN_C


#define OPpARGELEM_SV           0x00
#define OPpLVREF_SV             0x00
#define OPpARG1_MASK            0x01
#define OPpCOREARGS_DEREF1      0x01
#define OPpENTERSUB_INARGS      0x01
#define OPpPADHV_ISKEYS         0x01
#define OPpRV2HV_ISKEYS         0x01
#define OPpSORT_NUMERIC         0x01
#define OPpTRANS_CAN_FORCE_UTF8 0x01
#define OPpARGELEM_AV           0x02
#define OPpCONST_NOVER          0x02
#define OPpCOREARGS_DEREF2      0x02
#define OPpEVAL_HAS_HH          0x02
#define OPpFT_ACCESS            0x02
#define OPpHINT_STRICT_REFS     0x02
#define OPpINITFIELD_AV         0x02
#define OPpITER_REVERSED        0x02
#define OPpSORT_INTEGER         0x02
#define OPpTRANS_USE_SVOP       0x02
#define OPpARG2_MASK            0x03
#define OPpAVHVSWITCH_MASK      0x03
#define OPpARGELEM_HV           0x04
#define OPpASSIGN_TRUEBOOL      0x04
#define OPpCONST_SHORTCIRCUIT   0x04
#define OPpDONT_INIT_GV         0x04
#define OPpENTERSUB_HASTARG     0x04
#define OPpEVAL_UNICODE         0x04
#define OPpFT_STACKED           0x04
#define OPpINITFIELD_HV         0x04
#define OPpLVREF_ELEM           0x04
#define OPpSLICEWARNING         0x04
#define OPpSORT_REVERSE         0x04
#define OPpSPLIT_IMPLIM         0x04
#define OPpTRANS_IDENTICAL      0x04
#define OPpUSEINT               0x04
#define OPpARGELEM_MASK         0x06
#define OPpARG3_MASK            0x07
#define OPpPADRANGE_COUNTSHIFT  0x07
#define OPpCONST_STRICT         0x08
#define OPpENTERSUB_AMPER       0x08
#define OPpEVAL_BYTES           0x08
#define OPpFT_STACKING          0x08
#define OPpITER_DEF             0x08
#define OPpLVREF_ITER           0x08
#define OPpMAYBE_LVSUB          0x08
#define OPpMULTICONCAT_STRINGIFY 0x08
#define OPpREVERSE_INPLACE      0x08
#define OPpSORT_INPLACE         0x08
#define OPpSPLIT_LEX            0x08
#define OPpTRANS_SQUASH         0x08
#define OPpARG4_MASK            0x0f
#define OPpASSIGN_COMMON_AGG    0x10
#define OPpCONST_ENTERED        0x10
#define OPpDEREF_AV             0x10
#define OPpEVAL_COPHH           0x10
#define OPpFT_AFTER_t           0x10
#define OPpLVREF_AV             0x10
#define OPpMAYBE_TRUEBOOL       0x10
#define OPpMULTIDEREF_EXISTS    0x10
#define OPpOPEN_IN_RAW          0x10
#define OPpSORT_DESCEND         0x10
#define OPpSPLIT_ASSIGN         0x10
#define OPpSUBSTR_REPL_FIRST    0x10
#define OPpTARGET_MY            0x10
#define OPpASSIGN_COMMON_RC1    0x20
#define OPpDEREF_HV             0x20
#define OPpEARLY_CV             0x20
#define OPpEMPTYAVHV_IS_HV      0x20
#define OPpEVAL_RE_REPARSING    0x20
#define OPpHUSH_VMSISH          0x20
#define OPpKVSLICE              0x20
#define OPpLVREF_HV             0x20
#define OPpMAY_RETURN_CONSTANT  0x20
#define OPpMULTICONCAT_FAKE     0x20
#define OPpMULTIDEREF_DELETE    0x20
#define OPpOPEN_IN_CRLF         0x20
#define OPpTRANS_COMPLEMENT     0x20
#define OPpTRUEBOOL             0x20
#define OPpUNDEF_KEEP_PV        0x20
#define OPpDEREF                0x30
#define OPpDEREF_SV             0x30
#define OPpLVREF_CV             0x30
#define OPpLVREF_TYPE           0x30
#define OPpALLOW_FAKE           0x40
#define OPpARG_IF_FALSE         0x40
#define OPpASSIGN_BACKWARDS     0x40
#define OPpASSIGN_COMMON_SCALAR 0x40
#define OPpCONCAT_NESTED        0x40
#define OPpCONST_BARE           0x40
#define OPpCOREARGS_SCALARMOD   0x40
#define OPpENTERSUB_DB          0x40
#define OPpEVAL_EVALSV          0x40
#define OPpEXISTS_SUB           0x40
#define OPpFLIP_LINENUM         0x40
#define OPpINDEX_BOOLNEG        0x40
#define OPpLIST_GUESSED         0x40
#define OPpLVAL_DEFER           0x40
#define OPpMULTICONCAT_APPEND   0x40
#define OPpOPEN_OUT_RAW         0x40
#define OPpOUR_INTRO            0x40
#define OPpPAD_STATE            0x40
#define OPpREFCOUNTED           0x40
#define OPpREPEAT_DOLIST        0x40
#define OPpSLICE                0x40
#define OPpTRANS_GROWS          0x40
#define OPpPADRANGE_COUNTMASK   0x7f
#define OPpARG_IF_UNDEF         0x80
#define OPpASSIGN_CV_TO_GV      0x80
#define OPpCOREARGS_PUSHMARK    0x80
#define OPpDEFER_FINALLY        0x80
#define OPpENTERSUB_NOPAREN     0x80
#define OPpHELEMEXISTSOR_DELETE 0x80
#define OPpINITFIELDS           0x80
#define OPpLVALUE               0x80
#define OPpLVAL_INTRO           0x80
#define OPpOFFBYONE             0x80
#define OPpOPEN_OUT_CRLF        0x80
#define OPpPV_IS_UTF8           0x80
#define OPpTRANS_DELETE         0x80
START_EXTERN_C

#ifndef DOINIT

/* data about the flags in op_private */

EXTCONST I16  PL_op_private_bitdef_ix[];
EXTCONST U16  PL_op_private_bitdefs[];
EXTCONST char PL_op_private_labels[];
EXTCONST I16  PL_op_private_bitfields[];
EXTCONST U8   PL_op_private_valid[];

#else


/* PL_op_private_labels[]: the short descriptions of private flags.
 * All labels are concatenated into a single char array
 * (separated by \0's) for compactness.
 */

EXTCONST char PL_op_private_labels[] = {
    '$','M','O','D','\0',
    '+','1','\0',
    '-','\0',
    'A','M','P','E','R','\0',
    'A','N','O','N','H','A','S','H','\0',
    'A','P','P','E','N','D','\0',
    'A','S','S','I','G','N','\0',
    'A','V','\0',
    'B','A','R','E','\0',
    'B','K','W','A','R','D','\0',
    'B','O','O','L','\0',
    'B','O','O','L','?','\0',
    'B','Y','T','E','S','\0',
    'C','A','N','_','F','O','R','C','E','_','U','T','F','8','\0',
    'C','O','M','P','L','\0',
    'C','O','M','_','A','G','G','\0',
    'C','O','M','_','R','C','1','\0',
    'C','O','M','_','S','C','A','L','A','R','\0',
    'C','O','N','S','T','\0',
    'C','O','P','H','H','\0',
    'C','V','\0',
    'C','V','2','G','V','\0',
    'D','B','G','\0',
    'D','E','F','\0',
    'D','E','L','\0',
    'D','E','L','E','T','E','\0',
    'D','E','R','E','F','1','\0',
    'D','E','R','E','F','2','\0',
    'D','E','S','C','\0',
    'D','O','L','I','S','T','\0',
    'D','R','E','F','A','V','\0',
    'D','R','E','F','H','V','\0',
    'D','R','E','F','S','V','\0',
    'E','A','R','L','Y','C','V','\0',
    'E','L','E','M','\0',
    'E','N','T','E','R','E','D','\0',
    'E','V','A','L','S','V','\0',
    'E','X','I','S','T','S','\0',
    'F','A','K','E','\0',
    'F','I','N','A','L','L','Y','\0',
    'F','T','A','C','C','E','S','S','\0',
    'F','T','A','F','T','E','R','t','\0',
    'F','T','S','T','A','C','K','E','D','\0',
    'F','T','S','T','A','C','K','I','N','G','\0',
    'G','R','O','W','S','\0',
    'G','U','E','S','S','E','D','\0',
    'H','A','S','_','H','H','\0',
    'H','U','S','H','\0',
    'H','V','\0',
    'I','D','E','N','T','\0',
    'I','F','_','F','A','L','S','E','\0',
    'I','F','_','U','N','D','E','F','\0',
    'I','M','P','L','I','M','\0',
    'I','N','A','R','G','S','\0',
    'I','N','B','I','N','\0',
    'I','N','C','R','\0',
    'I','N','I','T','F','I','E','L','D','S','\0',
    'I','N','I','T','F','I','E','L','D','_','A','V','\0',
    'I','N','I','T','F','I','E','L','D','_','H','V','\0',
    'I','N','P','L','A','C','E','\0',
    'I','N','T','\0',
    'I','T','E','R','\0',
    'K','E','E','P','_','P','V','\0',
    'K','E','Y','S','\0',
    'K','V','S','L','I','C','E','\0',
    'L','E','X','\0',
    'L','I','N','E','N','U','M','\0',
    'L','V','\0',
    'L','V','D','E','F','E','R','\0',
    'L','V','I','N','T','R','O','\0',
    'L','V','S','U','B','\0',
    'M','A','R','K','\0',
    'N','E','G','\0',
    'N','E','S','T','E','D','\0',
    'N','O','(',')','\0',
    'N','O','I','N','I','T','\0',
    'N','O','V','E','R','\0',
    'N','U','M','\0',
    'O','U','R','I','N','T','R','\0',
    'O','U','T','B','I','N','\0',
    'O','U','T','C','R','\0',
    'R','E','F','C','\0',
    'R','E','P','A','R','S','E','\0',
    'R','E','P','L','1','S','T','\0',
    'R','E','V','\0',
    'R','E','V','E','R','S','E','D','\0',
    'S','H','O','R','T','\0',
    'S','L','I','C','E','\0',
    'S','L','I','C','E','W','A','R','N','\0',
    'S','Q','U','A','S','H','\0',
    'S','T','A','T','E','\0',
    'S','T','R','I','C','T','\0',
    'S','T','R','I','N','G','I','F','Y','\0',
    'S','U','B','\0',
    'S','V','\0',
    'T','A','R','G','\0',
    'T','A','R','G','M','Y','\0',
    'U','N','I','\0',
    'U','S','E','I','N','T','\0',
    'U','S','E','_','S','V','O','P','\0',
    'U','T','F','\0',
    'k','e','y','\0',
    'o','f','f','s','e','t','\0',
    'r','a','n','g','e','\0',

};



/* PL_op_private_bitfields[]: details about each bit field type.
 * Each definition consists of the following list of words:
 *    bitmin
 *    label (index into PL_op_private_labels[]; -1 if no label)
 *    repeat for each enum entry (if any):
 *       enum value
 *       enum label (index into PL_op_private_labels[])
 *    -1
 */

EXTCONST I16 PL_op_private_bitfields[] = {
    0, 8, -1,
    0, 8, -1,
    0, 675, -1,
    0, 8, -1,
    0, 8, -1,
    0, 682, -1,
    0, 671, -1,
    1, -1, 0, 632, 1, 39, 2, 319, -1,
    4, -1, 1, 185, 2, 192, 3, 199, -1,
    4, -1, 0, 632, 1, 39, 2, 319, 3, 131, -1,

};


/* PL_op_private_bitdef_ix[]: map an op number to a starting position
 * in PL_op_private_bitdefs.  If -1, the op has no bits defined */

EXTCONST I16  PL_op_private_bitdef_ix[] = {
      -1, /* null */
      -1, /* stub */
       0, /* scalar */
       1, /* pushmark */
       3, /* wantarray */
       4, /* const */
       9, /* gvsv */
      11, /* gv */
      12, /* gelem */
      13, /* padsv */
      16, /* padsv_store */
      19, /* padav */
      24, /* padhv */
      -1, /* padany */
      31, /* rv2gv */
      38, /* rv2sv */
      43, /* av2arylen */
      45, /* rv2cv */
      -1, /* anoncode */
       0, /* prototype */
       0, /* refgen */
       0, /* srefgen */
      52, /* ref */
      55, /* bless */
      56, /* backtick */
      55, /* glob */
       0, /* readline */
      -1, /* rcatline */
       0, /* regcmaybe */
       0, /* regcreset */
       0, /* regcomp */
      -1, /* match */
      -1, /* qr */
      61, /* subst */
       0, /* substcont */
      62, /* trans */
      62, /* transr */
      69, /* sassign */
      72, /* aassign */
       0, /* chop */
       0, /* schop */
      78, /* chomp */
      78, /* schomp */
       0, /* defined */
      80, /* undef */
       0, /* study */
      85, /* pos */
       0, /* preinc */
       0, /* i_preinc */
       0, /* predec */
       0, /* i_predec */
       0, /* postinc */
       0, /* i_postinc */
       0, /* postdec */
       0, /* i_postdec */
      88, /* pow */
      88, /* multiply */
      88, /* i_multiply */
      88, /* divide */
      88, /* i_divide */
      88, /* modulo */
      88, /* i_modulo */
      90, /* repeat */
      88, /* add */
      88, /* i_add */
      88, /* subtract */
      88, /* i_subtract */
      92, /* concat */
      95, /* multiconcat */
     101, /* stringify */
     103, /* left_shift */
     103, /* right_shift */
      12, /* lt */
      12, /* i_lt */
      12, /* gt */
      12, /* i_gt */
      12, /* le */
      12, /* i_le */
      12, /* ge */
      12, /* i_ge */
      12, /* eq */
      12, /* i_eq */
      12, /* ne */
      12, /* i_ne */
      12, /* ncmp */
      12, /* i_ncmp */
      12, /* slt */
      12, /* sgt */
      12, /* sle */
      12, /* sge */
      12, /* seq */
      12, /* sne */
      12, /* scmp */
     105, /* bit_and */
     105, /* bit_xor */
     105, /* bit_or */
     103, /* nbit_and */
     103, /* nbit_xor */
     103, /* nbit_or */
     105, /* sbit_and */
     105, /* sbit_xor */
     105, /* sbit_or */
       0, /* negate */
       0, /* i_negate */
       0, /* not */
     105, /* complement */
     103, /* ncomplement */
      78, /* scomplement */
      12, /* smartmatch */
     101, /* atan2 */
      78, /* sin */
      78, /* cos */
     101, /* rand */
     101, /* srand */
      78, /* exp */
      78, /* log */
      78, /* sqrt */
      78, /* int */
      78, /* hex */
      78, /* oct */
      78, /* abs */
     106, /* length */
     109, /* substr */
     112, /* vec */
     114, /* index */
     114, /* rindex */
      55, /* sprintf */
      55, /* formline */
      78, /* ord */
      78, /* chr */
     101, /* crypt */
       0, /* ucfirst */
       0, /* lcfirst */
       0, /* uc */
       0, /* lc */
       0, /* quotemeta */
     118, /* rv2av */
     125, /* aelemfast */
     125, /* aelemfast_lex */
     125, /* aelemfastlex_store */
     126, /* aelem */
     131, /* aslice */
     134, /* kvaslice */
       0, /* aeach */
       0, /* avalues */
      43, /* akeys */
       0, /* each */
      43, /* values */
      43, /* keys */
     135, /* delete */
     139, /* exists */
     141, /* rv2hv */
     126, /* helem */
     131, /* hslice */
     134, /* kvhslice */
     149, /* multideref */
      55, /* unpack */
      55, /* pack */
     156, /* split */
      55, /* join */
     161, /* list */
      12, /* lslice */
      55, /* anonlist */
      55, /* anonhash */
     163, /* emptyavhv */
      55, /* splice */
     101, /* push */
       0, /* pop */
       0, /* shift */
     101, /* unshift */
     168, /* sort */
     173, /* reverse */
       0, /* grepstart */
     175, /* grepwhile */
       0, /* mapstart */
       0, /* mapwhile */
       0, /* range */
     177, /* flip */
     177, /* flop */
       0, /* and */
       0, /* or */
      12, /* xor */
       0, /* dor */
     179, /* cond_expr */
       0, /* andassign */
       0, /* orassign */
       0, /* dorassign */
     181, /* entersub */
     188, /* leavesub */
     188, /* leavesublv */
       0, /* argcheck */
     190, /* argelem */
     192, /* argdefelem */
     195, /* caller */
      55, /* warn */
      55, /* die */
      55, /* reset */
      -1, /* lineseq */
     197, /* nextstate */
     197, /* dbstate */
      -1, /* unstack */
      -1, /* enter */
     198, /* leave */
      -1, /* scope */
     200, /* enteriter */
     204, /* iter */
      -1, /* enterloop */
     205, /* leaveloop */
      -1, /* return */
     207, /* last */
     207, /* next */
     207, /* redo */
     207, /* dump */
     207, /* goto */
      55, /* exit */
       0, /* method */
       0, /* method_named */
       0, /* method_super */
       0, /* method_redir */
       0, /* method_redir_super */
       0, /* entergiven */
       0, /* leavegiven */
       0, /* enterwhen */
       0, /* leavewhen */
      -1, /* break */
      -1, /* continue */
     209, /* open */
      55, /* close */
      55, /* pipe_op */
      55, /* fileno */
      55, /* umask */
      55, /* binmode */
      55, /* tie */
       0, /* untie */
       0, /* tied */
      55, /* dbmopen */
       0, /* dbmclose */
      55, /* sselect */
      55, /* select */
      55, /* getc */
      55, /* read */
      55, /* enterwrite */
     188, /* leavewrite */
      -1, /* prtf */
      -1, /* print */
      -1, /* say */
      55, /* sysopen */
      55, /* sysseek */
      55, /* sysread */
      55, /* syswrite */
      55, /* eof */
      55, /* tell */
      55, /* seek */
      55, /* truncate */
      55, /* fcntl */
      55, /* ioctl */
     101, /* flock */
      55, /* send */
      55, /* recv */
      55, /* socket */
      55, /* sockpair */
      55, /* bind */
      55, /* connect */
      55, /* listen */
      55, /* accept */
      55, /* shutdown */
      55, /* gsockopt */
      55, /* ssockopt */
       0, /* getsockname */
       0, /* getpeername */
       0, /* lstat */
       0, /* stat */
     214, /* ftrread */
     214, /* ftrwrite */
     214, /* ftrexec */
     214, /* fteread */
     214, /* ftewrite */
     214, /* fteexec */
     219, /* ftis */
     219, /* ftsize */
     219, /* ftmtime */
     219, /* ftatime */
     219, /* ftctime */
     219, /* ftrowned */
     219, /* fteowned */
     219, /* ftzero */
     219, /* ftsock */
     219, /* ftchr */
     219, /* ftblk */
     219, /* ftfile */
     219, /* ftdir */
     219, /* ftpipe */
     219, /* ftsuid */
     219, /* ftsgid */
     219, /* ftsvtx */
     219, /* ftlink */
     219, /* fttty */
     219, /* fttext */
     219, /* ftbinary */
     101, /* chdir */
     101, /* chown */
      78, /* chroot */
     101, /* unlink */
     101, /* chmod */
     101, /* utime */
     101, /* rename */
     101, /* link */
     101, /* symlink */
       0, /* readlink */
     101, /* mkdir */
      78, /* rmdir */
      55, /* open_dir */
       0, /* readdir */
       0, /* telldir */
      55, /* seekdir */
       0, /* rewinddir */
       0, /* closedir */
      -1, /* fork */
     223, /* wait */
     101, /* waitpid */
     101, /* system */
     101, /* exec */
     101, /* kill */
     223, /* getppid */
     101, /* getpgrp */
     101, /* setpgrp */
     101, /* getpriority */
     101, /* setpriority */
     223, /* time */
      -1, /* tms */
       0, /* localtime */
      55, /* gmtime */
       0, /* alarm */
     101, /* sleep */
      55, /* shmget */
      55, /* shmctl */
      55, /* shmread */
      55, /* shmwrite */
      55, /* msgget */
      55, /* msgctl */
      55, /* msgsnd */
      55, /* msgrcv */
      55, /* semop */
      55, /* semget */
      55, /* semctl */
       0, /* require */
       0, /* dofile */
      -1, /* hintseval */
     224, /* entereval */
     188, /* leaveeval */
       0, /* entertry */
      -1, /* leavetry */
       0, /* ghbyname */
      55, /* ghbyaddr */
      -1, /* ghostent */
       0, /* gnbyname */
      55, /* gnbyaddr */
      -1, /* gnetent */
       0, /* gpbyname */
      55, /* gpbynumber */
      -1, /* gprotoent */
      55, /* gsbyname */
      55, /* gsbyport */
      -1, /* gservent */
       0, /* shostent */
       0, /* snetent */
       0, /* sprotoent */
       0, /* sservent */
      -1, /* ehostent */
      -1, /* enetent */
      -1, /* eprotoent */
      -1, /* eservent */
       0, /* gpwnam */
       0, /* gpwuid */
      -1, /* gpwent */
      -1, /* spwent */
      -1, /* epwent */
       0, /* ggrnam */
       0, /* ggrgid */
      -1, /* ggrent */
      -1, /* sgrent */
      -1, /* egrent */
      -1, /* getlogin */
      55, /* syscall */
       0, /* lock */
       0, /* once */
      -1, /* custom */
     231, /* coreargs */
     235, /* avhvswitch */
       3, /* runcv */
       0, /* fc */
      -1, /* padcv */
      -1, /* introcv */
      -1, /* clonecv */
     237, /* padrange */
     239, /* refassign */
     245, /* lvref */
     251, /* lvrefslice */
      16, /* lvavref */
       0, /* anonconst */
      12, /* isa */
       0, /* cmpchain_and */
       0, /* cmpchain_dup */
       0, /* entertrycatch */
      -1, /* leavetrycatch */
      -1, /* poptry */
       0, /* catch */
     252, /* pushdefer */
       0, /* is_bool */
       0, /* is_weak */
       0, /* weaken */
       0, /* unweaken */
      52, /* blessed */
      78, /* refaddr */
      78, /* reftype */
      78, /* ceil */
      78, /* floor */
       0, /* is_tainted */
     254, /* helemexistsor */
     256, /* methstart */
     258, /* initfield */

};



/* PL_op_private_bitdefs[]: given a starting position in this array (as
 * supplied by PL_op_private_bitdef_ix[]), each word (until a stop bit is
 * seen) defines the meaning of a particular op_private bit for a
 * particular op. Each word consists of:
 *  bit  0:     stop bit: this is the last bit def for the current op
 *  bit  1:     bitfield: if set, this defines a bit field rather than a flag
 *  bits 2..4:  unsigned number in the range 0..7 which is the bit number
 *  bits 5..15: unsigned number in the range 0..2047 which is an index
 *              into PL_op_private_labels[]    (for a flag), or
 *              into PL_op_private_bitfields[] (for a bit field)
 */

EXTCONST U16  PL_op_private_bitdefs[] = {
    0x0003, /* scalar, prototype, refgen, srefgen, readline, regcmaybe, regcreset, regcomp, substcont, chop, schop, defined, study, preinc, i_preinc, predec, i_predec, postinc, i_postinc, postdec, i_postdec, negate, i_negate, not, ucfirst, lcfirst, uc, lc, quotemeta, aeach, avalues, each, pop, shift, grepstart, mapstart, mapwhile, range, and, or, dor, andassign, orassign, dorassign, argcheck, method, method_named, method_super, method_redir, method_redir_super, entergiven, leavegiven, enterwhen, leavewhen, untie, tied, dbmclose, getsockname, getpeername, lstat, stat, readlink, readdir, telldir, rewinddir, closedir, localtime, alarm, require, dofile, entertry, ghbyname, gnbyname, gpbyname, shostent, snetent, sprotoent, sservent, gpwnam, gpwuid, ggrnam, ggrgid, lock, once, fc, anonconst, cmpchain_and, cmpchain_dup, entertrycatch, catch, is_bool, is_weak, weaken, unweaken, is_tainted */
    0x3abc, 0x4bb9, /* pushmark */
    0x00bd, /* wantarray, runcv */
    0x0558, 0x1b70, 0x4c6c, 0x4808, 0x3fe5, /* const */
    0x3abc, 0x4139, /* gvsv */
    0x19d5, /* gv */
    0x0067, /* gelem, lt, i_lt, gt, i_gt, le, i_le, ge, i_ge, eq, i_eq, ne, i_ne, ncmp, i_ncmp, slt, sgt, sle, sge, seq, sne, scmp, smartmatch, lslice, xor, isa */
    0x3abc, 0x4bb8, 0x03d7, /* padsv */
    0x3abc, 0x4bb8, 0x0003, /* padsv_store, lvavref */
    0x3abc, 0x4bb8, 0x06d4, 0x3bac, 0x4989, /* padav */
    0x3abc, 0x4bb8, 0x06d4, 0x0770, 0x3bac, 0x4988, 0x3621, /* padhv */
    0x3abc, 0x1e38, 0x03d6, 0x3bac, 0x3f08, 0x4c64, 0x0003, /* rv2gv */
    0x3abc, 0x4138, 0x03d6, 0x4c64, 0x0003, /* rv2sv */
    0x3bac, 0x0003, /* av2arylen, akeys, values, keys */
    0x3e7c, 0x1198, 0x0ef4, 0x014c, 0x4f68, 0x4c64, 0x0003, /* rv2cv */
    0x06d4, 0x0770, 0x0003, /* ref, blessed */
    0x018f, /* bless, glob, sprintf, formline, unpack, pack, join, anonlist, anonhash, splice, warn, die, reset, exit, close, pipe_op, fileno, umask, binmode, tie, dbmopen, sselect, select, getc, read, enterwrite, sysopen, sysseek, sysread, syswrite, eof, tell, seek, truncate, fcntl, ioctl, send, recv, socket, sockpair, bind, connect, listen, accept, shutdown, gsockopt, ssockopt, open_dir, seekdir, gmtime, shmget, shmctl, shmread, shmwrite, msgget, msgctl, msgsnd, msgrcv, semop, semget, semctl, ghbyaddr, gnbyaddr, gpbynumber, gsbyname, gsbyport, syscall */
    0x431c, 0x4238, 0x2dd4, 0x2d10, 0x0003, /* backtick */
    0x06d5, /* subst */
    0x129c, 0x24b8, 0x0ad4, 0x4acc, 0x2848, 0x5244, 0x08e1, /* trans, transr */
    0x10dc, 0x05f8, 0x0067, /* sassign */
    0x0d98, 0x0c94, 0x0b90, 0x3bac, 0x06c8, 0x0067, /* aassign */
    0x5010, 0x0003, /* chomp, schomp, scomplement, sin, cos, exp, log, sqrt, int, hex, oct, abs, ord, chr, chroot, rmdir, refaddr, reftype, ceil, floor */
    0x3abc, 0x4bb8, 0x3534, 0x5010, 0x0003, /* undef */
    0x06d4, 0x3bac, 0x0003, /* pos */
    0x5010, 0x0067, /* pow, multiply, i_multiply, divide, i_divide, modulo, i_modulo, add, i_add, subtract, i_subtract */
    0x1658, 0x0067, /* repeat */
    0x3d98, 0x5010, 0x0067, /* concat */
    0x3abc, 0x0338, 0x1e34, 0x5010, 0x4d4c, 0x0003, /* multiconcat */
    0x5010, 0x018f, /* stringify, atan2, rand, srand, crypt, push, unshift, flock, chdir, chown, unlink, chmod, utime, rename, link, symlink, mkdir, waitpid, system, exec, kill, getpgrp, setpgrp, getpriority, setpriority, sleep */
    0x5010, 0x5169, /* left_shift, right_shift, nbit_and, nbit_xor, nbit_or, ncomplement */
    0x5169, /* bit_and, bit_xor, bit_or, sbit_and, sbit_xor, sbit_or, complement */
    0x06d4, 0x5010, 0x0003, /* length */
    0x4570, 0x3bac, 0x012b, /* substr */
    0x3bac, 0x0067, /* vec */
    0x3d18, 0x06d4, 0x5010, 0x018f, /* index, rindex */
    0x3abc, 0x4138, 0x06d4, 0x3bac, 0x4988, 0x4c64, 0x0003, /* rv2av */
    0x025f, /* aelemfast, aelemfast_lex, aelemfastlex_store */
    0x3abc, 0x39b8, 0x03d6, 0x3bac, 0x0067, /* aelem, helem */
    0x3abc, 0x3bac, 0x4989, /* aslice, hslice */
    0x3bad, /* kvaslice, kvhslice */
    0x3abc, 0x48d8, 0x36d4, 0x0003, /* delete */
    0x4e98, 0x0003, /* exists */
    0x3abc, 0x4138, 0x06d4, 0x0770, 0x3bac, 0x4988, 0x4c64, 0x3621, /* rv2hv */
    0x3abc, 0x39b8, 0x1314, 0x1d50, 0x3bac, 0x4c64, 0x0003, /* multideref */
    0x3abc, 0x4138, 0x0410, 0x37cc, 0x2b49, /* split */
    0x3abc, 0x2579, /* list */
    0x3abc, 0x4bb8, 0x0214, 0x5010, 0x018f, /* emptyavhv */
    0x15b0, 0x330c, 0x4668, 0x3404, 0x40a1, /* sort */
    0x330c, 0x0003, /* reverse */
    0x06d4, 0x0003, /* grepwhile */
    0x3858, 0x0003, /* flip, flop */
    0x3abc, 0x0003, /* cond_expr */
    0x3abc, 0x1198, 0x03d6, 0x014c, 0x4f68, 0x4c64, 0x2c21, /* entersub */
    0x43d8, 0x0003, /* leavesub, leavesublv, leavewrite, leaveeval */
    0x02aa, 0x0003, /* argelem */
    0x2a3c, 0x2918, 0x0003, /* argdefelem */
    0x00bc, 0x018f, /* caller */
    0x2755, /* nextstate, dbstate */
    0x395c, 0x43d9, /* leave */
    0x3abc, 0x4138, 0x120c, 0x46e5, /* enteriter */
    0x46e5, /* iter */
    0x395c, 0x0067, /* leaveloop */
    0x537c, 0x0003, /* last, next, redo, dump, goto */
    0x431c, 0x4238, 0x2dd4, 0x2d10, 0x018f, /* open */
    0x20f0, 0x234c, 0x2208, 0x1fc4, 0x0003, /* ftrread, ftrwrite, ftrexec, fteread, ftewrite, fteexec */
    0x20f0, 0x234c, 0x2208, 0x0003, /* ftis, ftsize, ftmtime, ftatime, ftctime, ftrowned, fteowned, ftzero, ftsock, ftchr, ftblk, ftfile, ftdir, ftpipe, ftsuid, ftsgid, ftsvtx, ftlink, fttty, fttext, ftbinary */
    0x5011, /* wait, getppid, time */
    0x1c78, 0x4474, 0x0fb0, 0x082c, 0x50e8, 0x2664, 0x0003, /* entereval */
    0x3c7c, 0x0018, 0x14c4, 0x13e1, /* coreargs */
    0x3bac, 0x00c7, /* avhvswitch */
    0x3abc, 0x01fb, /* padrange */
    0x3abc, 0x4bb8, 0x04f6, 0x348c, 0x1ac8, 0x0067, /* refassign */
    0x3abc, 0x4bb8, 0x04f6, 0x348c, 0x1ac8, 0x0003, /* lvref */
    0x3abd, /* lvrefslice */
    0x1edc, 0x0003, /* pushdefer */
    0x131c, 0x0003, /* helemexistsor */
    0x2e7c, 0x0003, /* methstart */
    0x3168, 0x2fc4, 0x0003, /* initfield */

};


/* PL_op_private_valid: for each op, indexed by op_type, indicate which
 * flags bits in op_private are legal */

EXTCONST U8 PL_op_private_valid[] = {
    /* NULL       */ (0xff),
    /* STUB       */ (0),
    /* SCALAR     */ (OPpARG1_MASK),
    /* PUSHMARK   */ (OPpPAD_STATE|OPpLVAL_INTRO),
    /* WANTARRAY  */ (OPpOFFBYONE),
    /* CONST      */ (OPpCONST_NOVER|OPpCONST_SHORTCIRCUIT|OPpCONST_STRICT|OPpCONST_ENTERED|OPpCONST_BARE),
    /* GVSV       */ (OPpOUR_INTRO|OPpLVAL_INTRO),
    /* GV         */ (OPpEARLY_CV),
    /* GELEM      */ (OPpARG2_MASK),
    /* PADSV      */ (OPpDEREF|OPpPAD_STATE|OPpLVAL_INTRO),
    /* PADSV_STORE */ (OPpARG1_MASK|OPpPAD_STATE|OPpLVAL_INTRO),
    /* PADAV      */ (OPpSLICEWARNING|OPpMAYBE_LVSUB|OPpTRUEBOOL|OPpPAD_STATE|OPpLVAL_INTRO),
    /* PADHV      */ (OPpPADHV_ISKEYS|OPpSLICEWARNING|OPpMAYBE_LVSUB|OPpMAYBE_TRUEBOOL|OPpTRUEBOOL|OPpPAD_STATE|OPpLVAL_INTRO),
    /* PADANY     */ (0),
    /* RV2GV      */ (OPpARG1_MASK|OPpHINT_STRICT_REFS|OPpDONT_INIT_GV|OPpMAYBE_LVSUB|OPpDEREF|OPpALLOW_FAKE|OPpLVAL_INTRO),
    /* RV2SV      */ (OPpARG1_MASK|OPpHINT_STRICT_REFS|OPpDEREF|OPpOUR_INTRO|OPpLVAL_INTRO),
    /* AV2ARYLEN  */ (OPpARG1_MASK|OPpMAYBE_LVSUB),
    /* RV2CV      */ (OPpARG1_MASK|OPpHINT_STRICT_REFS|OPpENTERSUB_HASTARG|OPpENTERSUB_AMPER|OPpMAY_RETURN_CONSTANT|OPpENTERSUB_DB|OPpENTERSUB_NOPAREN),
    /* ANONCODE   */ (0),
    /* PROTOTYPE  */ (OPpARG1_MASK),
    /* REFGEN     */ (OPpARG1_MASK),
    /* SREFGEN    */ (OPpARG1_MASK),
    /* REF        */ (OPpARG1_MASK|OPpMAYBE_TRUEBOOL|OPpTRUEBOOL),
    /* BLESS      */ (OPpARG4_MASK),
    /* BACKTICK   */ (OPpARG1_MASK|OPpOPEN_IN_RAW|OPpOPEN_IN_CRLF|OPpOPEN_OUT_RAW|OPpOPEN_OUT_CRLF),
    /* GLOB       */ (OPpARG4_MASK),
    /* READLINE   */ (OPpARG1_MASK),
    /* RCATLINE   */ (0),
    /* REGCMAYBE  */ (OPpARG1_MASK),
    /* REGCRESET  */ (OPpARG1_MASK),
    /* REGCOMP    */ (OPpARG1_MASK),
    /* MATCH      */ (0),
    /* QR         */ (0),
    /* SUBST      */ (OPpTRUEBOOL),
    /* SUBSTCONT  */ (OPpARG1_MASK),
    /* TRANS      */ (OPpTRANS_CAN_FORCE_UTF8|OPpTRANS_USE_SVOP|OPpTRANS_IDENTICAL|OPpTRANS_SQUASH|OPpTRANS_COMPLEMENT|OPpTRANS_GROWS|OPpTRANS_DELETE),
    /* TRANSR     */ (OPpTRANS_CAN_FORCE_UTF8|OPpTRANS_USE_SVOP|OPpTRANS_IDENTICAL|OPpTRANS_SQUASH|OPpTRANS_COMPLEMENT|OPpTRANS_GROWS|OPpTRANS_DELETE),
    /* SASSIGN    */ (OPpARG2_MASK|OPpASSIGN_BACKWARDS|OPpASSIGN_CV_TO_GV),
    /* AASSIGN    */ (OPpARG2_MASK|OPpASSIGN_TRUEBOOL|OPpMAYBE_LVSUB|OPpASSIGN_COMMON_AGG|OPpASSIGN_COMMON_RC1|OPpASSIGN_COMMON_SCALAR),
    /* CHOP       */ (OPpARG1_MASK),
    /* SCHOP      */ (OPpARG1_MASK),
    /* CHOMP      */ (OPpARG1_MASK|OPpTARGET_MY),
    /* SCHOMP     */ (OPpARG1_MASK|OPpTARGET_MY),
    /* DEFINED    */ (OPpARG1_MASK),
    /* UNDEF      */ (OPpARG1_MASK|OPpTARGET_MY|OPpUNDEF_KEEP_PV|OPpPAD_STATE|OPpLVAL_INTRO),
    /* STUDY      */ (OPpARG1_MASK),
    /* POS        */ (OPpARG1_MASK|OPpMAYBE_LVSUB|OPpTRUEBOOL),
    /* PREINC     */ (OPpARG1_MASK),
    /* I_PREINC   */ (OPpARG1_MASK),
    /* PREDEC     */ (OPpARG1_MASK),
    /* I_PREDEC   */ (OPpARG1_MASK),
    /* POSTINC    */ (OPpARG1_MASK),
    /* I_POSTINC  */ (OPpARG1_MASK),
    /* POSTDEC    */ (OPpARG1_MASK),
    /* I_POSTDEC  */ (OPpARG1_MASK),
    /* POW        */ (OPpARG2_MASK|OPpTARGET_MY),
    /* MULTIPLY   */ (OPpARG2_MASK|OPpTARGET_MY),
    /* I_MULTIPLY */ (OPpARG2_MASK|OPpTARGET_MY),
    /* DIVIDE     */ (OPpARG2_MASK|OPpTARGET_MY),
    /* I_DIVIDE   */ (OPpARG2_MASK|OPpTARGET_MY),
    /* MODULO     */ (OPpARG2_MASK|OPpTARGET_MY),
    /* I_MODULO   */ (OPpARG2_MASK|OPpTARGET_MY),
    /* REPEAT     */ (OPpARG2_MASK|OPpREPEAT_DOLIST),
    /* ADD        */ (OPpARG2_MASK|OPpTARGET_MY),
    /* I_ADD      */ (OPpARG2_MASK|OPpTARGET_MY),
    /* SUBTRACT   */ (OPpARG2_MASK|OPpTARGET_MY),
    /* I_SUBTRACT */ (OPpARG2_MASK|OPpTARGET_MY),
    /* CONCAT     */ (OPpARG2_MASK|OPpTARGET_MY|OPpCONCAT_NESTED),
    /* MULTICONCAT */ (OPpARG1_MASK|OPpMULTICONCAT_STRINGIFY|OPpTARGET_MY|OPpMULTICONCAT_FAKE|OPpMULTICONCAT_APPEND|OPpLVAL_INTRO),
    /* STRINGIFY  */ (OPpARG4_MASK|OPpTARGET_MY),
    /* LEFT_SHIFT */ (OPpUSEINT|OPpTARGET_MY),
    /* RIGHT_SHIFT */ (OPpUSEINT|OPpTARGET_MY),
    /* LT         */ (OPpARG2_MASK),
    /* I_LT       */ (OPpARG2_MASK),
    /* GT         */ (OPpARG2_MASK),
    /* I_GT       */ (OPpARG2_MASK),
    /* LE         */ (OPpARG2_MASK),
    /* I_LE       */ (OPpARG2_MASK),
    /* GE         */ (OPpARG2_MASK),
    /* I_GE       */ (OPpARG2_MASK),
    /* EQ         */ (OPpARG2_MASK),
    /* I_EQ       */ (OPpARG2_MASK),
    /* NE         */ (OPpARG2_MASK),
    /* I_NE       */ (OPpARG2_MASK),
    /* NCMP       */ (OPpARG2_MASK),
    /* I_NCMP     */ (OPpARG2_MASK),
    /* SLT        */ (OPpARG2_MASK),
    /* SGT        */ (OPpARG2_MASK),
    /* SLE        */ (OPpARG2_MASK),
    /* SGE        */ (OPpARG2_MASK),
    /* SEQ        */ (OPpARG2_MASK),
    /* SNE        */ (OPpARG2_MASK),
    /* SCMP       */ (OPpARG2_MASK),
    /* BIT_AND    */ (OPpUSEINT),
    /* BIT_XOR    */ (OPpUSEINT),
    /* BIT_OR     */ (OPpUSEINT),
    /* NBIT_AND   */ (OPpUSEINT|OPpTARGET_MY),
    /* NBIT_XOR   */ (OPpUSEINT|OPpTARGET_MY),
    /* NBIT_OR    */ (OPpUSEINT|OPpTARGET_MY),
    /* SBIT_AND   */ (OPpUSEINT),
    /* SBIT_XOR   */ (OPpUSEINT),
    /* SBIT_OR    */ (OPpUSEINT),
    /* NEGATE     */ (OPpARG1_MASK),
    /* I_NEGATE   */ (OPpARG1_MASK),
    /* NOT        */ (OPpARG1_MASK),
    /* COMPLEMENT */ (OPpUSEINT),
    /* NCOMPLEMENT */ (OPpUSEINT|OPpTARGET_MY),
    /* SCOMPLEMENT */ (OPpARG1_MASK|OPpTARGET_MY),
    /* SMARTMATCH */ (OPpARG2_MASK),
    /* ATAN2      */ (OPpARG4_MASK|OPpTARGET_MY),
    /* SIN        */ (OPpARG1_MASK|OPpTARGET_MY),
    /* COS        */ (OPpARG1_MASK|OPpTARGET_MY),
    /* RAND       */ (OPpARG4_MASK|OPpTARGET_MY),
    /* SRAND      */ (OPpARG4_MASK|OPpTARGET_MY),
    /* EXP        */ (OPpARG1_MASK|OPpTARGET_MY),
    /* LOG        */ (OPpARG1_MASK|OPpTARGET_MY),
    /* SQRT       */ (OPpARG1_MASK|OPpTARGET_MY),
    /* INT        */ (OPpARG1_MASK|OPpTARGET_MY),
    /* HEX        */ (OPpARG1_MASK|OPpTARGET_MY),
    /* OCT        */ (OPpARG1_MASK|OPpTARGET_MY),
    /* ABS        */ (OPpARG1_MASK|OPpTARGET_MY),
    /* LENGTH     */ (OPpARG1_MASK|OPpTARGET_MY|OPpTRUEBOOL),
    /* SUBSTR     */ (OPpARG3_MASK|OPpMAYBE_LVSUB|OPpSUBSTR_REPL_FIRST),
    /* VEC        */ (OPpARG2_MASK|OPpMAYBE_LVSUB),
    /* INDEX      */ (OPpARG4_MASK|OPpTARGET_MY|OPpTRUEBOOL|OPpINDEX_BOOLNEG),
    /* RINDEX     */ (OPpARG4_MASK|OPpTARGET_MY|OPpTRUEBOOL|OPpINDEX_BOOLNEG),
    /* SPRINTF    */ (OPpARG4_MASK),
    /* FORMLINE   */ (OPpARG4_MASK),
    /* ORD        */ (OPpARG1_MASK|OPpTARGET_MY),
    /* CHR        */ (OPpARG1_MASK|OPpTARGET_MY),
    /* CRYPT      */ (OPpARG4_MASK|OPpTARGET_MY),
    /* UCFIRST    */ (OPpARG1_MASK),
    /* LCFIRST    */ (OPpARG1_MASK),
    /* UC         */ (OPpARG1_MASK),
    /* LC         */ (OPpARG1_MASK),
    /* QUOTEMETA  */ (OPpARG1_MASK),
    /* RV2AV      */ (OPpARG1_MASK|OPpHINT_STRICT_REFS|OPpSLICEWARNING|OPpMAYBE_LVSUB|OPpTRUEBOOL|OPpOUR_INTRO|OPpLVAL_INTRO),
    /* AELEMFAST  */ (255),
    /* AELEMFAST_LEX */ (255),
    /* AELEMFASTLEX_STORE */ (255),
    /* AELEM      */ (OPpARG2_MASK|OPpMAYBE_LVSUB|OPpDEREF|OPpLVAL_DEFER|OPpLVAL_INTRO),
    /* ASLICE     */ (OPpSLICEWARNING|OPpMAYBE_LVSUB|OPpLVAL_INTRO),
    /* KVASLICE   */ (OPpMAYBE_LVSUB),
    /* AEACH      */ (OPpARG1_MASK),
    /* AVALUES    */ (OPpARG1_MASK),
    /* AKEYS      */ (OPpARG1_MASK|OPpMAYBE_LVSUB),
    /* EACH       */ (OPpARG1_MASK),
    /* VALUES     */ (OPpARG1_MASK|OPpMAYBE_LVSUB),
    /* KEYS       */ (OPpARG1_MASK|OPpMAYBE_LVSUB),
    /* DELETE     */ (OPpARG1_MASK|OPpKVSLICE|OPpSLICE|OPpLVAL_INTRO),
    /* EXISTS     */ (OPpARG1_MASK|OPpEXISTS_SUB),
    /* RV2HV      */ (OPpRV2HV_ISKEYS|OPpHINT_STRICT_REFS|OPpSLICEWARNING|OPpMAYBE_LVSUB|OPpMAYBE_TRUEBOOL|OPpTRUEBOOL|OPpOUR_INTRO|OPpLVAL_INTRO),
    /* HELEM      */ (OPpARG2_MASK|OPpMAYBE_LVSUB|OPpDEREF|OPpLVAL_DEFER|OPpLVAL_INTRO),
    /* HSLICE     */ (OPpSLICEWARNING|OPpMAYBE_LVSUB|OPpLVAL_INTRO),
    /* KVHSLICE   */ (OPpMAYBE_LVSUB),
    /* MULTIDEREF */ (OPpARG1_MASK|OPpHINT_STRICT_REFS|OPpMAYBE_LVSUB|OPpMULTIDEREF_EXISTS|OPpMULTIDEREF_DELETE|OPpLVAL_DEFER|OPpLVAL_INTRO),
    /* UNPACK     */ (OPpARG4_MASK),
    /* PACK       */ (OPpARG4_MASK),
    /* SPLIT      */ (OPpSPLIT_IMPLIM|OPpSPLIT_LEX|OPpSPLIT_ASSIGN|OPpOUR_INTRO|OPpLVAL_INTRO),
    /* JOIN       */ (OPpARG4_MASK),
    /* LIST       */ (OPpLIST_GUESSED|OPpLVAL_INTRO),
    /* LSLICE     */ (OPpARG2_MASK),
    /* ANONLIST   */ (OPpARG4_MASK),
    /* ANONHASH   */ (OPpARG4_MASK),
    /* EMPTYAVHV  */ (OPpARG4_MASK|OPpTARGET_MY|OPpEMPTYAVHV_IS_HV|OPpPAD_STATE|OPpLVAL_INTRO),
    /* SPLICE     */ (OPpARG4_MASK),
    /* PUSH       */ (OPpARG4_MASK|OPpTARGET_MY),
    /* POP        */ (OPpARG1_MASK),
    /* SHIFT      */ (OPpARG1_MASK),
    /* UNSHIFT    */ (OPpARG4_MASK|OPpTARGET_MY),
    /* SORT       */ (OPpSORT_NUMERIC|OPpSORT_INTEGER|OPpSORT_REVERSE|OPpSORT_INPLACE|OPpSORT_DESCEND),
    /* REVERSE    */ (OPpARG1_MASK|OPpREVERSE_INPLACE),
    /* GREPSTART  */ (OPpARG1_MASK),
    /* GREPWHILE  */ (OPpARG1_MASK|OPpTRUEBOOL),
    /* MAPSTART   */ (OPpARG1_MASK),
    /* MAPWHILE   */ (OPpARG1_MASK),
    /* RANGE      */ (OPpARG1_MASK),
    /* FLIP       */ (OPpARG1_MASK|OPpFLIP_LINENUM),
    /* FLOP       */ (OPpARG1_MASK|OPpFLIP_LINENUM),
    /* AND        */ (OPpARG1_MASK),
    /* OR         */ (OPpARG1_MASK),
    /* XOR        */ (OPpARG2_MASK),
    /* DOR        */ (OPpARG1_MASK),
    /* COND_EXPR  */ (OPpARG1_MASK|OPpLVAL_INTRO),
    /* ANDASSIGN  */ (OPpARG1_MASK),
    /* ORASSIGN   */ (OPpARG1_MASK),
    /* DORASSIGN  */ (OPpARG1_MASK),
    /* ENTERSUB   */ (OPpENTERSUB_INARGS|OPpHINT_STRICT_REFS|OPpENTERSUB_HASTARG|OPpENTERSUB_AMPER|OPpDEREF|OPpENTERSUB_DB|OPpLVAL_INTRO),
    /* LEAVESUB   */ (OPpARG1_MASK|OPpREFCOUNTED),
    /* LEAVESUBLV */ (OPpARG1_MASK|OPpREFCOUNTED),
    /* ARGCHECK   */ (OPpARG1_MASK),
    /* ARGELEM    */ (OPpARG1_MASK|OPpARGELEM_MASK),
    /* ARGDEFELEM */ (OPpARG1_MASK|OPpARG_IF_FALSE|OPpARG_IF_UNDEF),
    /* CALLER     */ (OPpARG4_MASK|OPpOFFBYONE),
    /* WARN       */ (OPpARG4_MASK),
    /* DIE        */ (OPpARG4_MASK),
    /* RESET      */ (OPpARG4_MASK),
    /* LINESEQ    */ (0),
    /* NEXTSTATE  */ (OPpHUSH_VMSISH),
    /* DBSTATE    */ (OPpHUSH_VMSISH),
    /* UNSTACK    */ (0),
    /* ENTER      */ (0),
    /* LEAVE      */ (OPpREFCOUNTED|OPpLVALUE),
    /* SCOPE      */ (0),
    /* ENTERITER  */ (OPpITER_REVERSED|OPpITER_DEF|OPpOUR_INTRO|OPpLVAL_INTRO),
    /* ITER       */ (OPpITER_REVERSED),
    /* ENTERLOOP  */ (0),
    /* LEAVELOOP  */ (OPpARG2_MASK|OPpLVALUE),
    /* RETURN     */ (0),
    /* LAST       */ (OPpARG1_MASK|OPpPV_IS_UTF8),
    /* NEXT       */ (OPpARG1_MASK|OPpPV_IS_UTF8),
    /* REDO       */ (OPpARG1_MASK|OPpPV_IS_UTF8),
    /* DUMP       */ (OPpARG1_MASK|OPpPV_IS_UTF8),
    /* GOTO       */ (OPpARG1_MASK|OPpPV_IS_UTF8),
    /* EXIT       */ (OPpARG4_MASK),
    /* METHOD     */ (OPpARG1_MASK),
    /* METHOD_NAMED */ (OPpARG1_MASK),
    /* METHOD_SUPER */ (OPpARG1_MASK),
    /* METHOD_REDIR */ (OPpARG1_MASK),
    /* METHOD_REDIR_SUPER */ (OPpARG1_MASK),
    /* ENTERGIVEN */ (OPpARG1_MASK),
    /* LEAVEGIVEN */ (OPpARG1_MASK),
    /* ENTERWHEN  */ (OPpARG1_MASK),
    /* LEAVEWHEN  */ (OPpARG1_MASK),
    /* BREAK      */ (0),
    /* CONTINUE   */ (0),
    /* OPEN       */ (OPpARG4_MASK|OPpOPEN_IN_RAW|OPpOPEN_IN_CRLF|OPpOPEN_OUT_RAW|OPpOPEN_OUT_CRLF),
    /* CLOSE      */ (OPpARG4_MASK),
    /* PIPE_OP    */ (OPpARG4_MASK),
    /* FILENO     */ (OPpARG4_MASK),
    /* UMASK      */ (OPpARG4_MASK),
    /* BINMODE    */ (OPpARG4_MASK),
    /* TIE        */ (OPpARG4_MASK),
    /* UNTIE      */ (OPpARG1_MASK),
    /* TIED       */ (OPpARG1_MASK),
    /* DBMOPEN    */ (OPpARG4_MASK),
    /* DBMCLOSE   */ (OPpARG1_MASK),
    /* SSELECT    */ (OPpARG4_MASK),
    /* SELECT     */ (OPpARG4_MASK),
    /* GETC       */ (OPpARG4_MASK),
    /* READ       */ (OPpARG4_MASK),
    /* ENTERWRITE */ (OPpARG4_MASK),
    /* LEAVEWRITE */ (OPpARG1_MASK|OPpREFCOUNTED),
    /* PRTF       */ (0),
    /* PRINT      */ (0),
    /* SAY        */ (0),
    /* SYSOPEN    */ (OPpARG4_MASK),
    /* SYSSEEK    */ (OPpARG4_MASK),
    /* SYSREAD    */ (OPpARG4_MASK),
    /* SYSWRITE   */ (OPpARG4_MASK),
    /* EOF        */ (OPpARG4_MASK),
    /* TELL       */ (OPpARG4_MASK),
    /* SEEK       */ (OPpARG4_MASK),
    /* TRUNCATE   */ (OPpARG4_MASK),
    /* FCNTL      */ (OPpARG4_MASK),
    /* IOCTL      */ (OPpARG4_MASK),
    /* FLOCK      */ (OPpARG4_MASK|OPpTARGET_MY),
    /* SEND       */ (OPpARG4_MASK),
    /* RECV       */ (OPpARG4_MASK),
    /* SOCKET     */ (OPpARG4_MASK),
    /* SOCKPAIR   */ (OPpARG4_MASK),
    /* BIND       */ (OPpARG4_MASK),
    /* CONNECT    */ (OPpARG4_MASK),
    /* LISTEN     */ (OPpARG4_MASK),
    /* ACCEPT     */ (OPpARG4_MASK),
    /* SHUTDOWN   */ (OPpARG4_MASK),
    /* GSOCKOPT   */ (OPpARG4_MASK),
    /* SSOCKOPT   */ (OPpARG4_MASK),
    /* GETSOCKNAME */ (OPpARG1_MASK),
    /* GETPEERNAME */ (OPpARG1_MASK),
    /* LSTAT      */ (OPpARG1_MASK),
    /* STAT       */ (OPpARG1_MASK),
    /* FTRREAD    */ (OPpARG1_MASK|OPpFT_ACCESS|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTRWRITE   */ (OPpARG1_MASK|OPpFT_ACCESS|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTREXEC    */ (OPpARG1_MASK|OPpFT_ACCESS|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTEREAD    */ (OPpARG1_MASK|OPpFT_ACCESS|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTEWRITE   */ (OPpARG1_MASK|OPpFT_ACCESS|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTEEXEC    */ (OPpARG1_MASK|OPpFT_ACCESS|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTIS       */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTSIZE     */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTMTIME    */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTATIME    */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTCTIME    */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTROWNED   */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTEOWNED   */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTZERO     */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTSOCK     */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTCHR      */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTBLK      */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTFILE     */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTDIR      */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTPIPE     */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTSUID     */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTSGID     */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTSVTX     */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTLINK     */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTTTY      */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTTEXT     */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* FTBINARY   */ (OPpARG1_MASK|OPpFT_STACKED|OPpFT_STACKING|OPpFT_AFTER_t),
    /* CHDIR      */ (OPpARG4_MASK|OPpTARGET_MY),
    /* CHOWN      */ (OPpARG4_MASK|OPpTARGET_MY),
    /* CHROOT     */ (OPpARG1_MASK|OPpTARGET_MY),
    /* UNLINK     */ (OPpARG4_MASK|OPpTARGET_MY),
    /* CHMOD      */ (OPpARG4_MASK|OPpTARGET_MY),
    /* UTIME      */ (OPpARG4_MASK|OPpTARGET_MY),
    /* RENAME     */ (OPpARG4_MASK|OPpTARGET_MY),
    /* LINK       */ (OPpARG4_MASK|OPpTARGET_MY),
    /* SYMLINK    */ (OPpARG4_MASK|OPpTARGET_MY),
    /* READLINK   */ (OPpARG1_MASK),
    /* MKDIR      */ (OPpARG4_MASK|OPpTARGET_MY),
    /* RMDIR      */ (OPpARG1_MASK|OPpTARGET_MY),
    /* OPEN_DIR   */ (OPpARG4_MASK),
    /* READDIR    */ (OPpARG1_MASK),
    /* TELLDIR    */ (OPpARG1_MASK),
    /* SEEKDIR    */ (OPpARG4_MASK),
    /* REWINDDIR  */ (OPpARG1_MASK),
    /* CLOSEDIR   */ (OPpARG1_MASK),
    /* FORK       */ (0),
    /* WAIT       */ (OPpTARGET_MY),
    /* WAITPID    */ (OPpARG4_MASK|OPpTARGET_MY),
    /* SYSTEM     */ (OPpARG4_MASK|OPpTARGET_MY),
    /* EXEC       */ (OPpARG4_MASK|OPpTARGET_MY),
    /* KILL       */ (OPpARG4_MASK|OPpTARGET_MY),
    /* GETPPID    */ (OPpTARGET_MY),
    /* GETPGRP    */ (OPpARG4_MASK|OPpTARGET_MY),
    /* SETPGRP    */ (OPpARG4_MASK|OPpTARGET_MY),
    /* GETPRIORITY */ (OPpARG4_MASK|OPpTARGET_MY),
    /* SETPRIORITY */ (OPpARG4_MASK|OPpTARGET_MY),
    /* TIME       */ (OPpTARGET_MY),
    /* TMS        */ (0),
    /* LOCALTIME  */ (OPpARG1_MASK),
    /* GMTIME     */ (OPpARG4_MASK),
    /* ALARM      */ (OPpARG1_MASK),
    /* SLEEP      */ (OPpARG4_MASK|OPpTARGET_MY),
    /* SHMGET     */ (OPpARG4_MASK),
    /* SHMCTL     */ (OPpARG4_MASK),
    /* SHMREAD    */ (OPpARG4_MASK),
    /* SHMWRITE   */ (OPpARG4_MASK),
    /* MSGGET     */ (OPpARG4_MASK),
    /* MSGCTL     */ (OPpARG4_MASK),
    /* MSGSND     */ (OPpARG4_MASK),
    /* MSGRCV     */ (OPpARG4_MASK),
    /* SEMOP      */ (OPpARG4_MASK),
    /* SEMGET     */ (OPpARG4_MASK),
    /* SEMCTL     */ (OPpARG4_MASK),
    /* REQUIRE    */ (OPpARG1_MASK),
    /* DOFILE     */ (OPpARG1_MASK),
    /* HINTSEVAL  */ (0),
    /* ENTEREVAL  */ (OPpARG1_MASK|OPpEVAL_HAS_HH|OPpEVAL_UNICODE|OPpEVAL_BYTES|OPpEVAL_COPHH|OPpEVAL_RE_REPARSING|OPpEVAL_EVALSV),
    /* LEAVEEVAL  */ (OPpARG1_MASK|OPpREFCOUNTED),
    /* ENTERTRY   */ (OPpARG1_MASK),
    /* LEAVETRY   */ (0),
    /* GHBYNAME   */ (OPpARG1_MASK),
    /* GHBYADDR   */ (OPpARG4_MASK),
    /* GHOSTENT   */ (0),
    /* GNBYNAME   */ (OPpARG1_MASK),
    /* GNBYADDR   */ (OPpARG4_MASK),
    /* GNETENT    */ (0),
    /* GPBYNAME   */ (OPpARG1_MASK),
    /* GPBYNUMBER */ (OPpARG4_MASK),
    /* GPROTOENT  */ (0),
    /* GSBYNAME   */ (OPpARG4_MASK),
    /* GSBYPORT   */ (OPpARG4_MASK),
    /* GSERVENT   */ (0),
    /* SHOSTENT   */ (OPpARG1_MASK),
    /* SNETENT    */ (OPpARG1_MASK),
    /* SPROTOENT  */ (OPpARG1_MASK),
    /* SSERVENT   */ (OPpARG1_MASK),
    /* EHOSTENT   */ (0),
    /* ENETENT    */ (0),
    /* EPROTOENT  */ (0),
    /* ESERVENT   */ (0),
    /* GPWNAM     */ (OPpARG1_MASK),
    /* GPWUID     */ (OPpARG1_MASK),
    /* GPWENT     */ (0),
    /* SPWENT     */ (0),
    /* EPWENT     */ (0),
    /* GGRNAM     */ (OPpARG1_MASK),
    /* GGRGID     */ (OPpARG1_MASK),
    /* GGRENT     */ (0),
    /* SGRENT     */ (0),
    /* EGRENT     */ (0),
    /* GETLOGIN   */ (0),
    /* SYSCALL    */ (OPpARG4_MASK),
    /* LOCK       */ (OPpARG1_MASK),
    /* ONCE       */ (OPpARG1_MASK),
    /* CUSTOM     */ (0xff),
    /* COREARGS   */ (OPpCOREARGS_DEREF1|OPpCOREARGS_DEREF2|OPpCOREARGS_SCALARMOD|OPpCOREARGS_PUSHMARK),
    /* AVHVSWITCH */ (OPpAVHVSWITCH_MASK|OPpMAYBE_LVSUB),
    /* RUNCV      */ (OPpOFFBYONE),
    /* FC         */ (OPpARG1_MASK),
    /* PADCV      */ (0),
    /* INTROCV    */ (0),
    /* CLONECV    */ (0),
    /* PADRANGE   */ (OPpPADRANGE_COUNTMASK|OPpLVAL_INTRO),
    /* REFASSIGN  */ (OPpARG2_MASK|OPpLVREF_ELEM|OPpLVREF_ITER|OPpLVREF_TYPE|OPpPAD_STATE|OPpLVAL_INTRO),
    /* LVREF      */ (OPpARG1_MASK|OPpLVREF_ELEM|OPpLVREF_ITER|OPpLVREF_TYPE|OPpPAD_STATE|OPpLVAL_INTRO),
    /* LVREFSLICE */ (OPpLVAL_INTRO),
    /* LVAVREF    */ (OPpARG1_MASK|OPpPAD_STATE|OPpLVAL_INTRO),
    /* ANONCONST  */ (OPpARG1_MASK),
    /* ISA        */ (OPpARG2_MASK),
    /* CMPCHAIN_AND */ (OPpARG1_MASK),
    /* CMPCHAIN_DUP */ (OPpARG1_MASK),
    /* ENTERTRYCATCH */ (OPpARG1_MASK),
    /* LEAVETRYCATCH */ (0),
    /* POPTRY     */ (0),
    /* CATCH      */ (OPpARG1_MASK),
    /* PUSHDEFER  */ (OPpARG1_MASK|OPpDEFER_FINALLY),
    /* IS_BOOL    */ (OPpARG1_MASK),
    /* IS_WEAK    */ (OPpARG1_MASK),
    /* WEAKEN     */ (OPpARG1_MASK),
    /* UNWEAKEN   */ (OPpARG1_MASK),
    /* BLESSED    */ (OPpARG1_MASK|OPpMAYBE_TRUEBOOL|OPpTRUEBOOL),
    /* REFADDR    */ (OPpARG1_MASK|OPpTARGET_MY),
    /* REFTYPE    */ (OPpARG1_MASK|OPpTARGET_MY),
    /* CEIL       */ (OPpARG1_MASK|OPpTARGET_MY),
    /* FLOOR      */ (OPpARG1_MASK|OPpTARGET_MY),
    /* IS_TAINTED */ (OPpARG1_MASK),
    /* HELEMEXISTSOR */ (OPpARG1_MASK|OPpHELEMEXISTSOR_DELETE),
    /* METHSTART  */ (OPpARG1_MASK|OPpINITFIELDS),
    /* INITFIELD  */ (OPpARG1_MASK|OPpINITFIELD_AV|OPpINITFIELD_HV),

};

#endif /* !DOINIT */

END_EXTERN_C



/* ex: set ro ft=c: */
