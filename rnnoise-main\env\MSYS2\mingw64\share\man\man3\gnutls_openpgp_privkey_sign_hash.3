.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_openpgp_privkey_sign_hash" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_openpgp_privkey_sign_hash \- API function
.SH SYNOPSIS
.B #include <gnutls/compat.h>
.sp
.BI "int gnutls_openpgp_privkey_sign_hash(gnutls_openpgp_privkey_t " key ", const gnutls_datum_t * " hash ", gnutls_datum_t * " signature ");"
.SH ARGUMENTS
.IP "gnutls_openpgp_privkey_t key" 12
Holds the key
.IP "const gnutls_datum_t * hash" 12
holds the data to be signed
.IP "gnutls_datum_t * signature" 12
will contain newly allocated signature
.SH "DESCRIPTION"
This function is no\-op.
.SH "RETURNS"
\fBGNUTLS_E_UNIMPLEMENTED_FEATURE\fP.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
