/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCL<PERSON>IMER within this package.
 */

import "oaidl.idl";
import "fsrmenums.idl";

cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

interface IFsrmObject;
interface IFsrmCollection;
interface IFsrmMutableCollection;
interface IFsrmCommittableCollection;
interface IFsrmAction;
interface IFsrmActionEmail;
interface IFsrmActionReport;
interface IFsrmActionEventLog;
interface IFsrmActionCommand;
interface IFsrmSetting;
interface IFsrmPathMapper;
interface IFsrmExportImport;

const DISPID FSRM_DISPID_FEATURE_MASK = 0xf000000;
const DISPID FSRM_DISPID_INTERFACE_A_MASK = 0xf00000;
const DISPID FSRM_DISPID_INTERFACE_B_MASK = 0xf0000;
const DISPID FSRM_DISPID_INTERFACE_C_MASK = 0xf000;
const DISPID FSRM_DISPID_INTERFACE_D_MASK = 0xf00;
const DISPID FSRM_DISPID_INTERFACE_MASK = 0xffff00;
const DISPID FSRM_DISPID_IS_PROPERTY = 0x80;
const DISPID FSRM_DISPID_METHOD_NUM_MASK = 0x7f;
const DISPID FSRM_DISPID_METHOD_MASK = 0xff;

const DISPID FSRM_DISPID_FEATURE_GENERAL = 0x1000000;
const DISPID FSRM_DISPID_FEATURE_QUOTA = 0x2000000;
const DISPID FSRM_DISPID_FEATURE_FILESCREEN = 0x3000000;
const DISPID FSRM_DISPID_FEATURE_REPORTS = 0x4000000;
const DISPID FSRM_DISPID_FEATURE_CLASSIFICATION = 0x5000000;
const DISPID FSRM_DISPID_FEATURE_PIPELINE = 0x6000000;

const DISPID FSRM_DISPID_OBJECT = FSRM_DISPID_FEATURE_GENERAL | 0x100000;
const DISPID FSRM_DISPID_COLLECTION = FSRM_DISPID_FEATURE_GENERAL | 0x200000;
const DISPID FSRM_DISPID_COLLECTION_MUTABLE = FSRM_DISPID_COLLECTION | 0x10000;
const DISPID FSRM_DISPID_COLLECTION_COMMITTABLE = FSRM_DISPID_COLLECTION_MUTABLE | 0x1000;
const DISPID FSRM_DISPID_ACTION = FSRM_DISPID_FEATURE_GENERAL | 0x300000;
const DISPID FSRM_DISPID_ACTION_EMAIL = FSRM_DISPID_ACTION | 0x10000;
const DISPID FSRM_DISPID_ACTION_REPORT = FSRM_DISPID_ACTION | 0x20000;
const DISPID FSRM_DISPID_ACTION_EVENTLOG = FSRM_DISPID_ACTION | 0x30000;
const DISPID FSRM_DISPID_ACTION_COMMAND = FSRM_DISPID_ACTION | 0x40000;
const DISPID FSRM_DISPID_ACTION_EMAIL2 = FSRM_DISPID_ACTION | 0x50000;
const DISPID FSRM_DISPID_SETTING = FSRM_DISPID_FEATURE_GENERAL | 0x400000;
const DISPID FSRM_DISPID_PATHMAPPER = FSRM_DISPID_FEATURE_GENERAL | 0x500000;
const DISPID FSRM_DISPID_EXPORTIMPORT = FSRM_DISPID_FEATURE_GENERAL | 0x600000;
const DISPID FSRM_DISPID_DERIVEDOBJECTSRESULT = FSRM_DISPID_FEATURE_GENERAL | 0x700000;
const DISPID FSRM_DISPID_ADR = FSRM_DISPID_FEATURE_GENERAL | 0x800000;

[object, uuid (96deb3b5-8b91-4a2a-9d93-80a35d8aa847), dual, hidden, nonextensible, pointer_default (unique)]
interface IFsrmCommittableCollection : IFsrmMutableCollection {
  [id (FSRM_DISPID_COLLECTION_COMMITTABLE | 0x1)] HRESULT Commit ([in] FsrmCommitOptions options,[out, retval] IFsrmCollection **results);
};

[object, uuid (40002314-590b-45a5-8e1b-8c05da527e52), dual, oleautomation, pointer_default (unique)]
interface IFsrmAccessDeniedRemediationClient : IDispatch {
  [id (FSRM_DISPID_ADR | 0x01)] HRESULT Show ([in] ULONG_PTR parentWnd,[in] BSTR accessPath,[in] AdrClientErrorType errorType,[in] long flags,[in, defaultvalue (L"")] BSTR windowTitle,[in, defaultvalue (L"")] BSTR windowMessage,[out, retval] long *result);
};

[object, uuid (6cd6408a-ae60-463b-9ef1-e117534d69dc), dual, pointer_default (unique)]
interface IFsrmAction : IDispatch {
  [propget, id (FSRM_DISPID_ACTION | 0x81)] HRESULT Id ([out, retval] FSRM_OBJECT_ID *id);
  [propget, id (FSRM_DISPID_ACTION | 0x82)] HRESULT ActionType ([out, retval] FsrmActionType *actionType);
  [propget, id (FSRM_DISPID_ACTION | 0x83)] HRESULT RunLimitInterval ([out, retval] long *minutes);
  [propput, id (FSRM_DISPID_ACTION | 0x83)] HRESULT RunLimitInterval ([in] long minutes);
  [id (FSRM_DISPID_ACTION | 0x1)] HRESULT Delete ();
};

[object, uuid (12937789-e247-4917-9c20-f3ee9c7ee783), dual, pointer_default (unique)]
interface IFsrmActionCommand : IFsrmAction {
  [propget, id (FSRM_DISPID_ACTION_COMMAND | 0x81)] HRESULT ExecutablePath ([out, retval] BSTR *executablePath);
  [propput, id (FSRM_DISPID_ACTION_COMMAND | 0x81)] HRESULT ExecutablePath ([in] BSTR executablePath);
  [propget, id (FSRM_DISPID_ACTION_COMMAND | 0x82)] HRESULT Arguments ([out, retval] BSTR *arguments);
  [propput, id (FSRM_DISPID_ACTION_COMMAND | 0x82)] HRESULT Arguments ([in] BSTR arguments);
  [propget, id (FSRM_DISPID_ACTION_COMMAND | 0x83)] HRESULT Account ([out, retval] FsrmAccountType *account);
  [propput, id (FSRM_DISPID_ACTION_COMMAND | 0x83)] HRESULT Account ([in] FsrmAccountType account);
  [propget, id (FSRM_DISPID_ACTION_COMMAND | 0x84)] HRESULT WorkingDirectory ([out, retval] BSTR *workingDirectory);
  [propput, id (FSRM_DISPID_ACTION_COMMAND | 0x84)] HRESULT WorkingDirectory ([in] BSTR workingDirectory);
  [propget, id (FSRM_DISPID_ACTION_COMMAND | 0x85)] HRESULT MonitorCommand ([out, retval] VARIANT_BOOL *monitorCommand);
  [propput, id (FSRM_DISPID_ACTION_COMMAND | 0x85)] HRESULT MonitorCommand ([in] VARIANT_BOOL monitorCommand);
  [propget, id (FSRM_DISPID_ACTION_COMMAND | 0x86)] HRESULT KillTimeOut ([out, retval] long *minutes);
  [propput, id (FSRM_DISPID_ACTION_COMMAND | 0x86)] HRESULT KillTimeOut ([in] long minutes);
  [propget, id (FSRM_DISPID_ACTION_COMMAND | 0x87)] HRESULT LogResult ([out, retval] VARIANT_BOOL *logResults);
  [propput, id (FSRM_DISPID_ACTION_COMMAND | 0x87)] HRESULT LogResult ([in] VARIANT_BOOL logResults);
};

[object, uuid (4c8f96c3-5d94-4f37-a4f4-f56ab463546f), dual, pointer_default (unique)]
interface IFsrmActionEventLog : IFsrmAction {
  [propget, id (FSRM_DISPID_ACTION_EVENTLOG | 0x81)] HRESULT EventType ([out, retval] FsrmEventType *eventType);
  [propput, id (FSRM_DISPID_ACTION_EVENTLOG | 0x81)] HRESULT EventType ([in] FsrmEventType eventType);
  [propget, id (FSRM_DISPID_ACTION_EVENTLOG | 0x82)] HRESULT MessageText ([out, retval] BSTR *messageText);
  [propput, id (FSRM_DISPID_ACTION_EVENTLOG | 0x82)] HRESULT MessageText ([in] BSTR messageText);
};

[object, uuid (2dbe63c4-b340-48a0-a5b0-158e07fc567e), dual, pointer_default (unique)]
interface IFsrmActionReport : IFsrmAction {
  [propget, id (FSRM_DISPID_ACTION_REPORT | 0x81)] HRESULT ReportTypes ([out, retval] SAFEARRAY (VARIANT) *reportTypes);
  [propput, id (FSRM_DISPID_ACTION_REPORT | 0x81)] HRESULT ReportTypes ([in] SAFEARRAY (VARIANT) reportTypes);
  [propget, id (FSRM_DISPID_ACTION_REPORT | 0x82)] HRESULT MailTo ([out, retval] BSTR *mailTo);
  [propput, id (FSRM_DISPID_ACTION_REPORT | 0x82)] HRESULT MailTo ([in] BSTR mailTo);
};

[object, uuid (f76fbf3b-8ddd-4b42-b05a-cb1c3ff1fee8), dual, hidden, nonextensible, pointer_default (unique)]
interface IFsrmCollection : IDispatch {
  [propget, id (DISPID_NEWENUM), restricted] HRESULT _NewEnum ([out, retval] IUnknown **unknown);
  [propget, id (DISPID_VALUE)] HRESULT Item ([in] long index,[out, retval] VARIANT *item);
  [propget, id (FSRM_DISPID_COLLECTION | 0x81)] HRESULT Count ([out, retval] long *count);
  [propget, id (FSRM_DISPID_COLLECTION | 0x82)] HRESULT State ([out, retval] FsrmCollectionState *state);
  [id (FSRM_DISPID_COLLECTION | 0x1)] HRESULT Cancel ();
  [id (FSRM_DISPID_COLLECTION | 0x2)] HRESULT WaitForCompletion ([in] long waitSeconds,[out, retval] VARIANT_BOOL *completed);
  [id (FSRM_DISPID_COLLECTION | 0x3)] HRESULT GetById ([in] FSRM_OBJECT_ID id,[out, retval] VARIANT *entry);
};

[object, uuid (39322a2d-38ee-4d0d-8095-421a80849a82), dual, pointer_default (unique)]
interface IFsrmDerivedObjectsResult : IDispatch {
  [propget, id (FSRM_DISPID_DERIVEDOBJECTSRESULT | 0x81)] HRESULT DerivedObjects ([out, retval] IFsrmCollection **derivedObjects);
  [propget, id (FSRM_DISPID_DERIVEDOBJECTSRESULT | 0x82)] HRESULT Results ([out, retval] IFsrmCollection **results);
};

[object, uuid (efcb0ab1-16c4-4a79-812c-725614c3306b), dual, pointer_default (unique)]
interface IFsrmExportImport : IDispatch {
  [id (FSRM_DISPID_EXPORTIMPORT | 0x1)] HRESULT ExportFileGroups ([in] BSTR filePath,[in, defaultvalue (NULL)] VARIANT *fileGroupNamesSafeArray,[in, defaultvalue (L"")] BSTR remoteHost);
  [id (FSRM_DISPID_EXPORTIMPORT | 0x2)] HRESULT ImportFileGroups ([in] BSTR filePath,[in, defaultvalue (NULL)] VARIANT *fileGroupNamesSafeArray,[in, defaultvalue (L"")] BSTR remoteHost,[out, retval] IFsrmCommittableCollection **fileGroups);
  [id (FSRM_DISPID_EXPORTIMPORT | 0x3)] HRESULT ExportFileScreenTemplates ([in] BSTR filePath,[in, defaultvalue (NULL)] VARIANT *templateNamesSafeArray,[in, defaultvalue (L"")] BSTR remoteHost);
  [id (FSRM_DISPID_EXPORTIMPORT | 0x4)] HRESULT ImportFileScreenTemplates ([in] BSTR filePath,[in, defaultvalue (NULL)] VARIANT *templateNamesSafeArray,[in, defaultvalue (L"")] BSTR remoteHost,[out, retval] IFsrmCommittableCollection **templates);
  [id (FSRM_DISPID_EXPORTIMPORT | 0x5)] HRESULT ExportQuotaTemplates ([in] BSTR filePath,[in, defaultvalue (NULL)] VARIANT *templateNamesSafeArray,[in, defaultvalue (L"")] BSTR remoteHost);
  [id (FSRM_DISPID_EXPORTIMPORT | 0x6)] HRESULT ImportQuotaTemplates ([in] BSTR filePath,[in, defaultvalue (NULL)] VARIANT *templateNamesSafeArray,[in, defaultvalue (L"")] BSTR remoteHost,[out, retval] IFsrmCommittableCollection **templates);
};

[object, uuid (22bcef93-4a3f-4183-89f9-2f8b8a628aee), dual, hidden, nonextensible, pointer_default (unique)]
interface IFsrmObject : IDispatch {
  [propget, id (FSRM_DISPID_OBJECT | 0x81)] HRESULT Id ([out, retval] FSRM_OBJECT_ID *id);
  [propget, id (FSRM_DISPID_OBJECT | 0x82)] HRESULT Description ([out, retval] BSTR *description);
  [propput, id (FSRM_DISPID_OBJECT | 0x82)] HRESULT Description ([in] BSTR description);
  [id (FSRM_DISPID_OBJECT | 0x1)] HRESULT Delete ();
  [id (FSRM_DISPID_OBJECT | 0x2)] HRESULT Commit ();
};

[object, uuid (6f4dbfff-6920-4821-a6c3-b7e94c1fd60c), dual, pointer_default (unique)]
interface IFsrmPathMapper : IDispatch {
  [id (FSRM_DISPID_PATHMAPPER | 0x01)] HRESULT GetSharePathsForLocalPath ([in] BSTR localPath,[out, retval] SAFEARRAY (VARIANT) *sharePaths);
};

[object, uuid (f411d4fd-14be-4260-8c40-03b7c95e608a), dual, pointer_default (unique)]
interface IFsrmSetting : IDispatch {
  [propget, id (FSRM_DISPID_SETTING | 0x81)] HRESULT SmtpServer ([out, retval] BSTR *smtpServer);
  [propput, id (FSRM_DISPID_SETTING | 0x81)] HRESULT SmtpServer ([in] BSTR smtpServer);
  [propget, id (FSRM_DISPID_SETTING | 0x82)] HRESULT MailFrom ([out, retval] BSTR *mailFrom);
  [propput, id (FSRM_DISPID_SETTING | 0x82)] HRESULT MailFrom ([in] BSTR mailFrom);
  [propget, id (FSRM_DISPID_SETTING | 0x83)] HRESULT AdminEmail ([out, retval] BSTR *adminEmail);
  [propput, id (FSRM_DISPID_SETTING | 0x83)] HRESULT AdminEmail ([in] BSTR adminEmail);
  [propget, id (FSRM_DISPID_SETTING | 0x84)] HRESULT DisableCommandLine ([out, retval] VARIANT_BOOL *disableCommandLine);
  [propput, id (FSRM_DISPID_SETTING | 0x84)] HRESULT DisableCommandLine ([in] VARIANT_BOOL disableCommandLine);
  [propget, id (FSRM_DISPID_SETTING | 0x85)] HRESULT EnableScreeningAudit ([out, retval] VARIANT_BOOL *enableScreeningAudit);
  [propput, id (FSRM_DISPID_SETTING | 0x85)] HRESULT EnableScreeningAudit ([in] VARIANT_BOOL enableScreeningAudit);
  [id (FSRM_DISPID_SETTING | 0x01)] HRESULT EmailTest ([in] BSTR mailTo);
  [id (FSRM_DISPID_SETTING | 0x02)] HRESULT SetActionRunLimitInterval ([in] FsrmActionType actionType,[in] long delayTimeMinutes);
  [id (FSRM_DISPID_SETTING | 0x03)] HRESULT GetActionRunLimitInterval ([in] FsrmActionType actionType,[out, retval] long *delayTimeMinutes);
};

[object, uuid (d646567d-26ae-4caa-9f84-4e0aad207fca), dual, pointer_default (unique)]
interface IFsrmActionEmail : IFsrmAction {
  [propget, id (FSRM_DISPID_ACTION_EMAIL | 0x81)] HRESULT MailFrom ([out, retval] BSTR *mailFrom);
  [propput, id (FSRM_DISPID_ACTION_EMAIL | 0x81)] HRESULT MailFrom ([in] BSTR mailFrom);
  [propget, id (FSRM_DISPID_ACTION_EMAIL | 0x82)] HRESULT MailReplyTo ([out, retval] BSTR *mailReplyTo);
  [propput, id (FSRM_DISPID_ACTION_EMAIL | 0x82)] HRESULT MailReplyTo ([in] BSTR mailReplyTo);
  [propget, id (FSRM_DISPID_ACTION_EMAIL | 0x83)] HRESULT MailTo ([out, retval] BSTR *mailTo);
  [propput, id (FSRM_DISPID_ACTION_EMAIL | 0x83)] HRESULT MailTo ([in] BSTR mailTo);
  [propget, id (FSRM_DISPID_ACTION_EMAIL | 0x84)] HRESULT MailCc ([out, retval] BSTR *mailCc);
  [propput, id (FSRM_DISPID_ACTION_EMAIL | 0x84)] HRESULT MailCc ([in] BSTR mailCc);
  [propget, id (FSRM_DISPID_ACTION_EMAIL | 0x85)] HRESULT MailBcc ([out, retval] BSTR *mailBcc);
  [propput, id (FSRM_DISPID_ACTION_EMAIL | 0x85)] HRESULT MailBcc ([in] BSTR mailBcc);
  [propget, id (FSRM_DISPID_ACTION_EMAIL | 0x86)] HRESULT MailSubject ([out, retval] BSTR *mailSubject);
  [propput, id (FSRM_DISPID_ACTION_EMAIL | 0x86)] HRESULT MailSubject ([in] BSTR mailSubject);
  [propget, id (FSRM_DISPID_ACTION_EMAIL | 0x87)] HRESULT MessageText ([out, retval] BSTR *messageText);
  [propput, id (FSRM_DISPID_ACTION_EMAIL | 0x87)] HRESULT MessageText ([in] BSTR messageText);
};

[object, uuid (8276702f-2532-4839-89bf-4872609a2ea4), dual, pointer_default (unique)]
interface IFsrmActionEmail2 : IFsrmActionEmail {
  [propget, id (FSRM_DISPID_ACTION_EMAIL2 | 0x81)] HRESULT AttachmentFileListSize ([out, retval] long *attachmentFileListSize);
  [propput, id (FSRM_DISPID_ACTION_EMAIL2 | 0x81)] HRESULT AttachmentFileListSize ([in] long attachmentFileListSize);
};

[object, uuid (1bb617b8-3886-49dc-af82-a6c90fa35dda), dual, hidden, nonextensible, pointer_default (unique)]
interface IFsrmMutableCollection : IFsrmCollection {
  [id (FSRM_DISPID_COLLECTION_MUTABLE | 0x1)] HRESULT Add ([in] VARIANT item);
  [id (FSRM_DISPID_COLLECTION_MUTABLE | 0x2)] HRESULT Remove ([in] long index);
  [id (FSRM_DISPID_COLLECTION_MUTABLE | 0x3)] HRESULT RemoveById ([in] FSRM_OBJECT_ID id);
  [id (FSRM_DISPID_COLLECTION_MUTABLE | 0x4)] HRESULT Clone ([out, retval] IFsrmMutableCollection **collection);
};
cpp_quote("#endif")
