.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs12_import" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs12_import \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs12.h>
.sp
.BI "int gnutls_pkcs12_import(gnutls_pkcs12_t " pkcs12 ", const gnutls_datum_t * " data ", gnutls_x509_crt_fmt_t " format ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs12_t pkcs12" 12
The data to store the parsed PKCS12.
.IP "const gnutls_datum_t * data" 12
The DER or PEM encoded PKCS12.
.IP "gnutls_x509_crt_fmt_t format" 12
One of DER or PEM
.IP "unsigned int flags" 12
an ORed sequence of gnutls_privkey_pkcs8_flags
.SH "DESCRIPTION"
This function will convert the given DER or PEM encoded PKCS12
to the native gnutls_pkcs12_t format. The output will be stored in 'pkcs12'.

If the PKCS12 is PEM encoded it should have a header of "PKCS12".
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
