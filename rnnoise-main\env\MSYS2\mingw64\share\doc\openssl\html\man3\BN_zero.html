<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BN_zero</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BN_zero, BN_one, BN_value_one, BN_set_word, BN_get_word - BIGNUM assignment operations</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/bn.h&gt;

void BN_zero(BIGNUM *a);
int BN_one(BIGNUM *a);

const BIGNUM *BN_value_one(void);

int BN_set_word(BIGNUM *a, BN_ULONG w);
unsigned BN_ULONG BN_get_word(BIGNUM *a);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p><b>BN_ULONG</b> is a macro that will be an unsigned integral type optimized for the most efficient implementation on the local platform.</p>

<p>BN_zero(), BN_one() and BN_set_word() set <b>a</b> to the values 0, 1 and <b>w</b> respectively. BN_zero() and BN_one() are macros.</p>

<p>BN_value_one() returns a <b>BIGNUM</b> constant of value 1. This constant is useful for use in comparisons and assignment.</p>

<p>BN_get_word() returns <b>a</b>, if it can be represented as a <b>BN_ULONG</b>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BN_get_word() returns the value <b>a</b>, or all-bits-set if <b>a</b> cannot be represented as a single integer.</p>

<p>BN_one() and BN_set_word() return 1 on success, 0 otherwise. BN_value_one() returns the constant. BN_zero() never fails and returns no value.</p>

<h1 id="BUGS">BUGS</h1>

<p>If a <b>BIGNUM</b> is equal to the value of all-bits-set, it will collide with the error condition returned by BN_get_word() which uses that as an error value.</p>

<p><b>BN_ULONG</b> should probably be a typedef.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/BN_bn2bin.html">BN_bn2bin(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>In OpenSSL 0.9.8, BN_zero() was changed to not return a value; previous versions returned an int.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


