.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hmac_set_nonce" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hmac_set_nonce \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "void gnutls_hmac_set_nonce(gnutls_hmac_hd_t " handle ", const void * " nonce ", size_t " nonce_len ");"
.SH ARGUMENTS
.IP "gnutls_hmac_hd_t handle" 12
is a \fBgnutls_hmac_hd_t\fP type
.IP "const void * nonce" 12
the data to set as nonce
.IP "size_t nonce_len" 12
the length of data
.SH "DESCRIPTION"
This function will set the nonce in the MAC algorithm.
.SH "SINCE"
3.2.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
