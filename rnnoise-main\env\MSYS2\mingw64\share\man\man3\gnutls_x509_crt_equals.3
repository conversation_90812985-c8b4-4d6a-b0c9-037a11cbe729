.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_equals" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_equals \- This function compares two gnutls_x509_crt_t certificates
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "unsigned gnutls_x509_crt_equals(gnutls_x509_crt_t " cert1 ", gnutls_x509_crt_t " cert2 ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert1" 12
The first certificate
.IP "gnutls_x509_crt_t cert2" 12
The second certificate
.SH "DESCRIPTION"
This function will compare two X.509 certificate structures.
.SH "RETURNS"
On equality non\-zero is returned, otherwise zero.
.SH "SINCE"
3.5.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
