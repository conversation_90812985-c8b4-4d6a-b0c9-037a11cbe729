.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "dane_verification_status_print" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
dane_verification_status_print \- API function
.SH SYNOPSIS
.B #include <gnutls/dane.h>
.sp
.BI "int dane_verification_status_print(unsigned int " status ", gnutls_datum_t * " out ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "unsigned int status" 12
The status flags to be printed
.IP "gnutls_datum_t * out" 12
Newly allocated datum with (0) terminated string.
.IP "unsigned int flags" 12
should be zero
.SH "DESCRIPTION"
This function will pretty print the status of a verification
process \-\- eg. the one obtained by \fBdane_verify_crt()\fP.

The output  \fIout\fP needs to be deallocated using \fBgnutls_free()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
