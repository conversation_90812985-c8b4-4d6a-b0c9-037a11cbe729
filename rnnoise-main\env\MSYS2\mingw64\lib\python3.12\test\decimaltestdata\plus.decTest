------------------------------------------------------------------------
-- plus.decTest -- decimal monadic addition                           --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- This set of tests primarily tests the existence of the operator.
-- Addition and rounding, and most overflows, are tested elsewhere.

extended:    1
precision:   9
rounding:    half_up
maxExponent: 384
minexponent: -383

plux001 plus '1'      -> '1'
plux002 plus '-1'     -> '-1'
plux003 plus '1.00'   -> '1.00'
plux004 plus '-1.00'  -> '-1.00'
plux005 plus '0'      -> '0'
plux006 plus '0.00'   -> '0.00'
plux007 plus '00.0'   -> '0.0'
plux008 plus '00.00'  -> '0.00'
plux009 plus '00'     -> '0'

plux010 plus '-2'     -> '-2'
plux011 plus '2'      -> '2'
plux012 plus '-2.00'  -> '-2.00'
plux013 plus '2.00'   -> '2.00'
plux014 plus '-0'     -> '0'
plux015 plus '-0.00'  -> '0.00'
plux016 plus '-00.0'  -> '0.0'
plux017 plus '-00.00' -> '0.00'
plux018 plus '-00'    -> '0'

plux020 plus '-2000000' -> '-2000000'
plux021 plus '2000000'  -> '2000000'
precision: 7
plux022 plus '-2000000' -> '-2000000'
plux023 plus '2000000'  -> '2000000'
precision: 6
plux024 plus '-2000000' -> '-2.00000E+6' Rounded
plux025 plus '2000000'  -> '2.00000E+6' Rounded
precision: 3
plux026 plus '-2000000' -> '-2.00E+6' Rounded
plux027 plus '2000000'  -> '2.00E+6' Rounded

-- more fixed, potential LHS swaps if done by add 0
precision: 9
plux060 plus '56267E-10'   -> '0.0000056267'
plux061 plus '56267E-5'    -> '0.56267'
plux062 plus '56267E-2'    -> '562.67'
plux063 plus '56267E-1'    -> '5626.7'
plux065 plus '56267E-0'    -> '56267'
plux066 plus '56267E+0'    -> '56267'
plux067 plus '56267E+1'    -> '5.6267E+5'
plux068 plus '56267E+2'    -> '5.6267E+6'
plux069 plus '56267E+3'    -> '5.6267E+7'
plux070 plus '56267E+4'    -> '5.6267E+8'
plux071 plus '56267E+5'    -> '5.6267E+9'
plux072 plus '56267E+6'    -> '5.6267E+10'
plux080 plus '-56267E-10'  -> '-0.0000056267'
plux081 plus '-56267E-5'   -> '-0.56267'
plux082 plus '-56267E-2'   -> '-562.67'
plux083 plus '-56267E-1'   -> '-5626.7'
plux085 plus '-56267E-0'   -> '-56267'
plux086 plus '-56267E+0'   -> '-56267'
plux087 plus '-56267E+1'   -> '-5.6267E+5'
plux088 plus '-56267E+2'   -> '-5.6267E+6'
plux089 plus '-56267E+3'   -> '-5.6267E+7'
plux090 plus '-56267E+4'   -> '-5.6267E+8'
plux091 plus '-56267E+5'   -> '-5.6267E+9'
plux092 plus '-56267E+6'   -> '-5.6267E+10'

-- "lhs" zeros in plus and minus have exponent = operand
plux120 plus '-0E3'   -> '0E+3'
plux121 plus '-0E2'   -> '0E+2'
plux122 plus '-0E1'   -> '0E+1'
plux123 plus '-0E0'   -> '0'
plux124 plus '+0E0'   -> '0'
plux125 plus '+0E1'   -> '0E+1'
plux126 plus '+0E2'   -> '0E+2'
plux127 plus '+0E3'   -> '0E+3'

plux130 plus '-5E3'   -> '-5E+3'
plux131 plus '-5E8'   -> '-5E+8'
plux132 plus '-5E13'  -> '-5E+13'
plux133 plus '-5E18'  -> '-5E+18'
plux134 plus '+5E3'   -> '5E+3'
plux135 plus '+5E8'   -> '5E+8'
plux136 plus '+5E13'  -> '5E+13'
plux137 plus '+5E18'  -> '5E+18'

-- specials
plux150 plus 'Inf'    -> 'Infinity'
plux151 plus '-Inf'   -> '-Infinity'
plux152 plus   NaN    ->  NaN
plux153 plus  sNaN    ->  NaN   Invalid_operation
plux154 plus   NaN77  ->  NaN77
plux155 plus  sNaN88  ->  NaN88 Invalid_operation
plux156 plus  -NaN    -> -NaN
plux157 plus -sNaN    -> -NaN   Invalid_operation
plux158 plus  -NaN77  -> -NaN77
plux159 plus -sNaN88  -> -NaN88 Invalid_operation

-- overflow tests
maxexponent: 999999999
minexponent: -999999999
precision: 3
plux160 plus 9.999E+999999999  ->  Infinity Inexact Overflow Rounded
plux161 plus -9.999E+999999999 -> -Infinity Inexact Overflow Rounded

-- subnormals and underflow
precision: 3
maxexponent: 999
minexponent: -999
plux210 plus  1.00E-999        ->   1.00E-999
plux211 plus  0.1E-999         ->   1E-1000   Subnormal
plux212 plus  0.10E-999        ->   1.0E-1000 Subnormal
plux213 plus  0.100E-999       ->   1.0E-1000 Subnormal Rounded
plux214 plus  0.01E-999        ->   1E-1001   Subnormal
-- next is rounded to Emin
plux215 plus  0.999E-999       ->   1.00E-999 Inexact Rounded Subnormal Underflow
plux216 plus  0.099E-999       ->   1.0E-1000 Inexact Rounded Subnormal Underflow
plux217 plus  0.009E-999       ->   1E-1001   Inexact Rounded Subnormal Underflow
plux218 plus  0.001E-999       ->   0E-1001   Inexact Rounded Subnormal Underflow Clamped
plux219 plus  0.0009E-999      ->   0E-1001   Inexact Rounded Subnormal Underflow Clamped
plux220 plus  0.0001E-999      ->   0E-1001   Inexact Rounded Subnormal Underflow Clamped

plux230 plus -1.00E-999        ->  -1.00E-999
plux231 plus -0.1E-999         ->  -1E-1000   Subnormal
plux232 plus -0.10E-999        ->  -1.0E-1000 Subnormal
plux233 plus -0.100E-999       ->  -1.0E-1000 Subnormal Rounded
plux234 plus -0.01E-999        ->  -1E-1001   Subnormal
-- next is rounded to Emin
plux235 plus -0.999E-999       ->  -1.00E-999 Inexact Rounded Subnormal Underflow
plux236 plus -0.099E-999       ->  -1.0E-1000 Inexact Rounded Subnormal Underflow
plux237 plus -0.009E-999       ->  -1E-1001   Inexact Rounded Subnormal Underflow
plux238 plus -0.001E-999       ->  -0E-1001   Inexact Rounded Subnormal Underflow Clamped
plux239 plus -0.0009E-999      ->  -0E-1001   Inexact Rounded Subnormal Underflow Clamped
plux240 plus -0.0001E-999      ->  -0E-1001   Inexact Rounded Subnormal Underflow Clamped

-- subnormals clamped to 0-Etiny
precision:   16
maxExponent: 384
minExponent: -383
plux251 plus 7E-398     -> 7E-398 Subnormal
plux252 plus 0E-398     -> 0E-398
plux253 plus 7E-399     -> 1E-398 Subnormal Underflow Inexact Rounded
plux254 plus 4E-399     -> 0E-398 Clamped Subnormal Underflow Inexact Rounded
plux255 plus 7E-400     -> 0E-398 Clamped Subnormal Underflow Inexact Rounded
plux256 plus 7E-401     -> 0E-398 Clamped Subnormal Underflow Inexact Rounded
plux257 plus 0E-399     -> 0E-398 Clamped
plux258 plus 0E-400     -> 0E-398 Clamped
plux259 plus 0E-401     -> 0E-398 Clamped

-- long operand checks
maxexponent: 999
minexponent: -999
precision: 9
plux301 plus 12345678000  -> 1.23456780E+10 Rounded
plux302 plus 1234567800   -> 1.23456780E+9 Rounded
plux303 plus 1234567890   -> 1.23456789E+9 Rounded
plux304 plus 1234567891   -> 1.23456789E+9 Inexact Rounded
plux305 plus 12345678901  -> 1.23456789E+10 Inexact Rounded
plux306 plus 1234567896   -> 1.23456790E+9 Inexact Rounded

-- still checking
precision: 15
plux321 plus 12345678000  -> 12345678000
plux322 plus 1234567800   -> 1234567800
plux323 plus 1234567890   -> 1234567890
plux324 plus 1234567891   -> 1234567891
plux325 plus 12345678901  -> 12345678901
plux326 plus 1234567896   -> 1234567896
precision: 9

-- Null tests
plu900 plus  # -> NaN Invalid_operation

