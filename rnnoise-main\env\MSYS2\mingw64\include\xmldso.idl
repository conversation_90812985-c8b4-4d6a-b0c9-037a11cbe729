/*
 * Copyright (C) 2005 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#if 0
#pragma makedep install
#endif

#include <idispids.h>
#include <xmldsodid.h>

#if !defined(progid) && !defined(__WIDL__)
#define threading(model)
#define progid(str)
#define vi_progid(str)
#endif

interface IXMLDOMDocument;

[
local,
object,
odl,
dual,
oleautomation,
uuid(310afa62-0575-11d2-9ca9-0060b0ec3d39),
pointer_default(unique)
]
interface IXMLDSOControl : IDispatch
{
	[propget,id(DISPID_XMLDSO_DOCUMENT)] 
        HRESULT XMLDocument([out, retval] IXMLDOMDocument** ppDoc);

	[propput,id(DISPID_XMLDSO_DOCUMENT)] 
        HRESULT XMLDocument([in] IXMLDOMDocument* ppDoc);

	[propget,id(DISPID_XMLDSO_JAVADSOCOMPATIBLE)] 
        HRESULT JavaDSOCompatible([out, retval] BOOL* fJavaDSOCompatible);

	[propput,id(DISPID_XMLDSO_JAVADSOCOMPATIBLE)] 
        HRESULT JavaDSOCompatible([in]  BOOL fJavaDSOCompatible);

	[propget, id(DISPID_READYSTATE)] 
        HRESULT readyState([out, retval] long *state);
}

[
    helpstring("XML Data Source Object"),
    progid("Microsoft.XMLDSO.1.0"),
    vi_progid("Microsoft.XMLDSO"),
    threading(apartment),
    version(1.0),
    uuid(550dda30-0541-11d2-9ca9-0060b0ec3d39)
]
coclass XMLDSOControl
{
	[default] interface IXMLDSOControl;
}
