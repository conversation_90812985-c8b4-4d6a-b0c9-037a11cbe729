.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_fips140_set_mode" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_fips140_set_mode \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_fips140_set_mode(gnutls_fips_mode_t " mode ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_fips_mode_t mode" 12
the FIPS140\-2 mode to switch to
.IP "unsigned flags" 12
should be zero or \fBGNUTLS_FIPS140_SET_MODE_THREAD\fP
.SH "DESCRIPTION"
That function is not thread\-safe when changing the mode with no flags
(globally), and should be called prior to creating any threads. Its
behavior with no flags after threads are created is undefined.

When the flag \fBGNUTLS_FIPS140_SET_MODE_THREAD\fP is specified
then this call will change the FIPS140\-2 mode for this particular
thread and not for the whole process. That way an application
can utilize this function to set and reset mode for specific
operations.

This function never fails but will be a no\-op if used when
the library is not in FIPS140\-2 mode. When asked to switch to unknown
values for  \fImode\fP or to \fBGNUTLS_FIPS140_SELFTESTS\fP mode, the library
switches to \fBGNUTLS_FIPS140_STRICT\fP mode.
.SH "SINCE"
3.6.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
