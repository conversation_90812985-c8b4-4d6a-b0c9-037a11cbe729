CMP0008
-------

.. |REMOVED_IN_CMAKE_VERSION| replace:: 4.0
.. include:: REMOVED_PROLOGUE.txt

Libraries linked by full-path must have a valid library file name.

In CMake 2.4 and below it is possible to write code like

.. code-block:: cmake

  target_link_libraries(myexe /full/path/to/somelib)

where ``somelib`` is supposed to be a valid library file name such as
``libsomelib.a`` or ``somelib.lib``.  For Makefile generators this
produces an error at build time because the dependency on the full
path cannot be found.  For :ref:`Visual Studio Generators` IDE
and :generator:`Xcode` generators this used to
work by accident because CMake would always split off the library
directory and ask the linker to search for the library by name
(``-lsomelib`` or ``somelib.lib``).  Despite the failure with Make<PERSON><PERSON>, some
projects have code like this and build only with Visual Studio and/or Xcode.
This version of CMake prefers to pass the full path directly to the
native build tool, which will fail in this case because it does not
name a valid library file.

This policy determines what to do with full paths that do not appear
to name a valid library file.  The ``OLD`` behavior for this policy is to
split the library name from the path and ask the linker to search for
it.  The ``NEW`` behavior for this policy is to trust the given path and
pass it directly to the native build tool unchanged.

.. |INTRODUCED_IN_CMAKE_VERSION| replace:: 2.6.1
.. |WARNED_OR_DID_NOT_WARN| replace:: warned
.. include:: REMOVED_EPILOGUE.txt
