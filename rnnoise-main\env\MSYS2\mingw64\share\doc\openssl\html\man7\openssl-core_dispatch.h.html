<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-core_dispatch.h</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl/core_dispatch.h - OpenSSL provider dispatch numbers and function types</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/core_dispatch.h&gt;</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <i>&lt;openssl/core_dispatch.h&gt;</i> header defines all the operation numbers, dispatch numbers and provider interface function types currently available.</p>

<p>The operation and dispatch numbers are represented with macros, which are named as follows:</p>

<dl>

<dt id="operation-numbers">operation numbers</dt>
<dd>

<p>These macros have the form <code>OSSL_OP_<i>opname</i></code>.</p>

</dd>
<dt id="dipatch-numbers">dipatch numbers</dt>
<dd>

<p>These macros have the form <code>OSSL_FUNC_<i>opname</i>_<i>funcname</i></code>, where <code><i>opname</i></code> is the same as in the macro for the operation this function belongs to.</p>

</dd>
</dl>

<p>With every dispatch number, there is an associated function type.</p>

<p>For further information, please see the <a href="../man7/provider.html">provider(7)</a></p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider.html">provider(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The types and macros described here were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


