.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_anti_replay_set_window" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_anti_replay_set_window \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_anti_replay_set_window(gnutls_anti_replay_t " anti_replay ", unsigned int " window ");"
.SH ARGUMENTS
.IP "gnutls_anti_replay_t anti_replay" 12
is a \fBgnutls_anti_replay_t\fP type.
.IP "unsigned int window" 12
is the time window recording ClientHello, in milliseconds
.SH "DESCRIPTION"
Sets the time window used for ClientHello recording.  In order to
protect against replay attacks, the server records ClientHello
messages within this time period from the last update, and
considers it a replay when a ClientHello outside of the period; if
a ClientHello arrives within this period, the server checks the
database and detects duplicates.

For the details of the algorithm, see RFC 8446, section 8.2.
.SH "SINCE"
3.6.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
