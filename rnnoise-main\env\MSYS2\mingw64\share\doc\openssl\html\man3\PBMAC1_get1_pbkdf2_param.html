<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>PBMAC1_get1_pbkdf2_param</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>PBMAC1_get1_pbkdf2_param - Function to manipulate a PBMAC1 MAC structure</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

PBKDF2PARAM *PBMAC1_get1_pbkdf2_param(const X509_ALGOR *macalg);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>PBMAC1_get1_pbkdf2_param() retrieves a <b>PBKDF2PARAM</b> structure from an <i>X509_ALGOR</i> structure.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>PBMAC1_get1_pbkdf2_param() returns NULL in case when PBMAC1 uses an algorithm apart from <b>PBKDF2</b> or when passed incorrect parameters and a pointer to <b>PBKDF2PARAM</b> structure otherwise.</p>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>IETF RFC 9579 (<a href="https://tools.ietf.org/html/rfc9579">https://tools.ietf.org/html/rfc9579</a>)</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/openssl-pkcs12.html">openssl-pkcs12(1)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The <i>PBMAC1_get1_pbkdf2_param</i> function was added in OpenSSL 3.4.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


