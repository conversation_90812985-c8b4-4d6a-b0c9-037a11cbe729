.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_resp_get_response" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_resp_get_response \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_resp_get_response(gnutls_ocsp_resp_const_t " resp ", gnutls_datum_t * " response_type_oid ", gnutls_datum_t * " response ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_resp_const_t resp" 12
should contain a \fBgnutls_ocsp_resp_t\fP type
.IP "gnutls_datum_t * response_type_oid" 12
newly allocated output buffer with response type OID
.IP "gnutls_datum_t * response" 12
newly allocated output buffer with DER encoded response
.SH "DESCRIPTION"
This function will extract the response type OID in and the
response data from an OCSP response.  Normally the
 \fIresponse_type_oid\fP is always "*******.********.1.1" which means the
 \fIresponse\fP should be decoded as a Basic OCSP Response, but
technically other response types could be used.

This function is typically only useful when you want to extract the
response type OID of an response for diagnostic purposes.
Otherwise \fBgnutls_ocsp_resp_import()\fP will decode the basic OCSP
response part and the caller need not worry about that aspect.

Since 3.7.0  \fIresponse_type_oid\fP \->size does not account for the terminating
null byte.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
