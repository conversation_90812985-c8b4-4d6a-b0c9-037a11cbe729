## Syntax highlighting for C and C++ files.

syntax c "\.([ch](pp|xx)?|C|cc|c\+\+|cu|H|hh|ii?)$"
header "-\*-.*\<C(\+\+)?((;|[[:blank:]]).*)?-\*-"
magic "^(C|C\+\+) (source|program)"
comment "//"

# Constants.
color brightred "\<[[:upper:]_][[:upper:][:digit:]_]*\>"
# Labels.
color brightmagenta "^[[:blank:]]*[[:alpha:]_][[:alnum:]_]*:[[:blank:]]*$"
color normal ":[[:blank:]]*$"

# Types and related keywords.
color green "\<(auto|bool|char|const|double|enum|extern|float|inline|int|long|restrict|short|signed|sizeof|static|struct|typedef|union|unsigned|void)\>"
color green "\<([[:lower:]][[:lower:]_]*|(u_?)?int(8|16|32|64))_t\>"
color green "\<(_(Alignas|Alignof|Atomic|Bool|Complex|Generic|Imaginary|Noreturn|Static_assert|Thread_local))\>"
color green "\<(class|explicit|friend|mutable|namespace|override|private|protected|public|register|template|this|typename|using|virtual|volatile)\>"

# Flow control.
color brightyellow "\<(if|else|for|while|do|switch|case|default)\>"
color brightyellow "\<(try|throw|catch|operator|new|delete)\>"
color magenta "\<(break|continue|goto|return)\>"

# Single-quoted stuff (characters, backslash escapes, hex and octal byte codes).
color brightmagenta "'([^'\]|\\(["'\abfnrtv]|x[[:xdigit:]]{1,2}|[0-3]?[0-7]{1,2}))'"

# GCC builtins.
color cyan "__attribute__[[:blank:]]*\(\([^)]*\)\)|__(aligned|asm|builtin|hidden|inline|packed|restrict|section|typeof|weak)__"

# Strings and names of included files.
color brightyellow ""([^"]|\\")*"|#[[:blank:]]*include[[:blank:]]*<[^>]+>"

# Preprocessor directives.
color brightcyan start="^[[:blank:]]*#[[:blank:]]*(if(n?def)?|elif|warning|error|pragma)\>" end="(\`|[^\])$"
color brightcyan "^[[:blank:]]*#[[:blank:]]*((define|else|endif|include(_next)?|line|undef)\>|$)"

# Comments.
color brightblue "//[^"]*$|(^|[[:blank:]])//.*"
color brightblue start="/\*" end="\*/"

# Reminders.
color brightwhite,yellow "\<(FIXME|TODO|XXX)\>"

# Trailing whitespace.
color ,green "[[:space:]]+$"
