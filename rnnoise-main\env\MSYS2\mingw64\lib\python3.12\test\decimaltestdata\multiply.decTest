------------------------------------------------------------------------
-- multiply.decTest -- decimal multiplication                         --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
precision:   9
rounding:    half_up
maxExponent: 384
minexponent: -383

-- sanity checks (as base, above)
mulx000 multiply 2      2 -> 4
mulx001 multiply 2      3 -> 6
mulx002 multiply 5      1 -> 5
mulx003 multiply 5      2 -> 10
mulx004 multiply 1.20   2 -> 2.40
mulx005 multiply 1.20   0 -> 0.00
mulx006 multiply 1.20  -2 -> -2.40
mulx007 multiply -1.20  2 -> -2.40
mulx008 multiply -1.20  0 -> -0.00
mulx009 multiply -1.20 -2 -> 2.40
mulx010 multiply 5.09 7.1 -> 36.139
mulx011 multiply 2.5    4 -> 10.0
mulx012 multiply 2.50   4 -> 10.00
mulx013 multiply 1.23456789 1.00000000 -> 1.23456789 Rounded
mulx014 multiply 9.999999999 9.999999999 -> 100.000000 Inexact Rounded
mulx015 multiply 2.50   4 -> 10.00
precision: 6
mulx016 multiply 2.50   4 -> 10.00
mulx017 multiply  9.999999999  9.999999999 ->  100.000 Inexact Rounded
mulx018 multiply  9.999999999 -9.999999999 -> -100.000 Inexact Rounded
mulx019 multiply -9.999999999  9.999999999 -> -100.000 Inexact Rounded
mulx020 multiply -9.999999999 -9.999999999 ->  100.000 Inexact Rounded

-- 1999.12.21: next one is an edge case if intermediate longs are used
precision: 15
mulx059 multiply 999999999999 9765625 -> 9.76562499999023E+18 Inexact Rounded
precision: 30
mulx160 multiply 999999999999 9765625 -> 9765624999990234375
precision: 9
-----

-- zeros, etc.
mulx021 multiply  0      0     ->  0
mulx022 multiply  0     -0     -> -0
mulx023 multiply -0      0     -> -0
mulx024 multiply -0     -0     ->  0
mulx025 multiply -0.0   -0.0   ->  0.00
mulx026 multiply -0.0   -0.0   ->  0.00
mulx027 multiply -0.0   -0.0   ->  0.00
mulx028 multiply -0.0   -0.0   ->  0.00
mulx030 multiply  5.00   1E-3  ->  0.00500
mulx031 multiply  00.00  0.000 ->  0.00000
mulx032 multiply  00.00  0E-3  ->  0.00000     -- rhs is 0
mulx033 multiply  0E-3   00.00 ->  0.00000     -- lhs is 0
mulx034 multiply -5.00   1E-3  -> -0.00500
mulx035 multiply -00.00  0.000 -> -0.00000
mulx036 multiply -00.00  0E-3  -> -0.00000     -- rhs is 0
mulx037 multiply -0E-3   00.00 -> -0.00000     -- lhs is 0
mulx038 multiply  5.00  -1E-3  -> -0.00500
mulx039 multiply  00.00 -0.000 -> -0.00000
mulx040 multiply  00.00 -0E-3  -> -0.00000     -- rhs is 0
mulx041 multiply  0E-3  -00.00 -> -0.00000     -- lhs is 0
mulx042 multiply -5.00  -1E-3  ->  0.00500
mulx043 multiply -00.00 -0.000 ->  0.00000
mulx044 multiply -00.00 -0E-3  ->  0.00000     -- rhs is 0
mulx045 multiply -0E-3  -00.00 ->  0.00000     -- lhs is 0

-- examples from decarith
mulx050 multiply 1.20 3        -> 3.60
mulx051 multiply 7    3        -> 21
mulx052 multiply 0.9  0.8      -> 0.72
mulx053 multiply 0.9  -0       -> -0.0
mulx054 multiply 654321 654321 -> 4.28135971E+11  Inexact Rounded

mulx060 multiply 123.45 1e7  ->  1.2345E+9
mulx061 multiply 123.45 1e8  ->  1.2345E+10
mulx062 multiply 123.45 1e+9 ->  1.2345E+11
mulx063 multiply 123.45 1e10 ->  1.2345E+12
mulx064 multiply 123.45 1e11 ->  1.2345E+13
mulx065 multiply 123.45 1e12 ->  1.2345E+14
mulx066 multiply 123.45 1e13 ->  1.2345E+15


-- test some intermediate lengths
precision: 9
mulx080 multiply 0.1 123456789          -> 12345678.9
mulx081 multiply 0.1 1234567891         -> 123456789 Inexact Rounded
mulx082 multiply 0.1 12345678912        -> 1.23456789E+9 Inexact Rounded
mulx083 multiply 0.1 12345678912345     -> 1.23456789E+12 Inexact Rounded
mulx084 multiply 0.1 123456789          -> 12345678.9
precision: 8
mulx085 multiply 0.1 12345678912        -> 1.2345679E+9 Inexact Rounded
mulx086 multiply 0.1 12345678912345     -> 1.2345679E+12 Inexact Rounded
precision: 7
mulx087 multiply 0.1 12345678912        -> 1.234568E+9 Inexact Rounded
mulx088 multiply 0.1 12345678912345     -> 1.234568E+12 Inexact Rounded

precision: 9
mulx090 multiply 123456789          0.1 -> 12345678.9
mulx091 multiply 1234567891         0.1 -> 123456789 Inexact Rounded
mulx092 multiply 12345678912        0.1 -> 1.23456789E+9 Inexact Rounded
mulx093 multiply 12345678912345     0.1 -> 1.23456789E+12 Inexact Rounded
mulx094 multiply 123456789          0.1 -> 12345678.9
precision: 8
mulx095 multiply 12345678912        0.1 -> 1.2345679E+9 Inexact Rounded
mulx096 multiply 12345678912345     0.1 -> 1.2345679E+12 Inexact Rounded
precision: 7
mulx097 multiply 12345678912        0.1 -> 1.234568E+9 Inexact Rounded
mulx098 multiply 12345678912345     0.1 -> 1.234568E+12 Inexact Rounded

-- test some more edge cases and carries
maxexponent: 9999
minexponent: -9999
precision: 33
mulx101 multiply 9 9   -> 81
mulx102 multiply 9 90   -> 810
mulx103 multiply 9 900   -> 8100
mulx104 multiply 9 9000   -> 81000
mulx105 multiply 9 90000   -> 810000
mulx106 multiply 9 900000   -> 8100000
mulx107 multiply 9 9000000   -> 81000000
mulx108 multiply 9 90000000   -> 810000000
mulx109 multiply 9 900000000   -> 8100000000
mulx110 multiply 9 9000000000   -> 81000000000
mulx111 multiply 9 90000000000   -> 810000000000
mulx112 multiply 9 900000000000   -> 8100000000000
mulx113 multiply 9 9000000000000   -> 81000000000000
mulx114 multiply 9 90000000000000   -> 810000000000000
mulx115 multiply 9 900000000000000   -> 8100000000000000
mulx116 multiply 9 9000000000000000   -> 81000000000000000
mulx117 multiply 9 90000000000000000   -> 810000000000000000
mulx118 multiply 9 900000000000000000   -> 8100000000000000000
mulx119 multiply 9 9000000000000000000   -> 81000000000000000000
mulx120 multiply 9 90000000000000000000   -> 810000000000000000000
mulx121 multiply 9 900000000000000000000   -> 8100000000000000000000
mulx122 multiply 9 9000000000000000000000   -> 81000000000000000000000
mulx123 multiply 9 90000000000000000000000   -> 810000000000000000000000
-- test some more edge cases without carries
mulx131 multiply 3 3   -> 9
mulx132 multiply 3 30   -> 90
mulx133 multiply 3 300   -> 900
mulx134 multiply 3 3000   -> 9000
mulx135 multiply 3 30000   -> 90000
mulx136 multiply 3 300000   -> 900000
mulx137 multiply 3 3000000   -> 9000000
mulx138 multiply 3 30000000   -> 90000000
mulx139 multiply 3 300000000   -> 900000000
mulx140 multiply 3 3000000000   -> 9000000000
mulx141 multiply 3 30000000000   -> 90000000000
mulx142 multiply 3 300000000000   -> 900000000000
mulx143 multiply 3 3000000000000   -> 9000000000000
mulx144 multiply 3 30000000000000   -> 90000000000000
mulx145 multiply 3 300000000000000   -> 900000000000000
mulx146 multiply 3 3000000000000000   -> 9000000000000000
mulx147 multiply 3 30000000000000000   -> 90000000000000000
mulx148 multiply 3 300000000000000000   -> 900000000000000000
mulx149 multiply 3 3000000000000000000   -> 9000000000000000000
mulx150 multiply 3 30000000000000000000   -> 90000000000000000000
mulx151 multiply 3 300000000000000000000   -> 900000000000000000000
mulx152 multiply 3 3000000000000000000000   -> 9000000000000000000000
mulx153 multiply 3 30000000000000000000000   -> 90000000000000000000000

maxexponent: 999999999
minexponent: -999999999
precision: 9
-- test some cases that are close to exponent overflow/underflow
mulx170 multiply 1 9e999999999    -> 9E+999999999
mulx171 multiply 1 9.9e999999999  -> 9.9E+999999999
mulx172 multiply 1 9.99e999999999 -> 9.99E+999999999
mulx173 multiply 9e999999999    1 -> 9E+999999999
mulx174 multiply 9.9e999999999  1 -> 9.9E+999999999
mulx176 multiply 9.99e999999999 1 -> 9.99E+999999999
mulx177 multiply 1 9.99999999e999999999 -> 9.99999999E+999999999
mulx178 multiply 9.99999999e999999999 1 -> 9.99999999E+999999999

mulx180 multiply 0.1 9e-999999998   -> 9E-999999999
mulx181 multiply 0.1 99e-999999998  -> 9.9E-999999998
mulx182 multiply 0.1 999e-999999998 -> 9.99E-999999997

mulx183 multiply 0.1 9e-999999998     -> 9E-999999999
mulx184 multiply 0.1 99e-999999998    -> 9.9E-999999998
mulx185 multiply 0.1 999e-999999998   -> 9.99E-999999997
mulx186 multiply 0.1 999e-999999997   -> 9.99E-999999996
mulx187 multiply 0.1 9999e-999999997  -> 9.999E-999999995
mulx188 multiply 0.1 99999e-999999997 -> 9.9999E-999999994

mulx190 multiply 1 9e-999999998   -> 9E-999999998
mulx191 multiply 1 99e-999999998  -> 9.9E-999999997
mulx192 multiply 1 999e-999999998 -> 9.99E-999999996
mulx193 multiply 9e-999999998   1 -> 9E-999999998
mulx194 multiply 99e-999999998  1 -> 9.9E-999999997
mulx195 multiply 999e-999999998 1 -> 9.99E-999999996

mulx196 multiply 1e-599999999 1e-400000000 -> 1E-999999999
mulx197 multiply 1e-600000000 1e-399999999 -> 1E-999999999
mulx198 multiply 1.2e-599999999 1.2e-400000000 -> 1.44E-999999999
mulx199 multiply 1.2e-600000000 1.2e-399999999 -> 1.44E-999999999

mulx201 multiply 1e599999999 1e400000000 -> 1E+999999999
mulx202 multiply 1e600000000 1e399999999 -> 1E+999999999
mulx203 multiply 1.2e599999999 1.2e400000000 -> 1.44E+999999999
mulx204 multiply 1.2e600000000 1.2e399999999 -> 1.44E+999999999

-- long operand triangle
precision: 33
mulx246 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.290801193369671916511992830 Inexact Rounded
precision: 32
mulx247 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.29080119336967191651199283  Inexact Rounded
precision: 31
mulx248 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.2908011933696719165119928   Inexact Rounded
precision: 30
mulx249 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.290801193369671916511993    Inexact Rounded
precision: 29
mulx250 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.29080119336967191651199     Inexact Rounded
precision: 28
mulx251 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.2908011933696719165120      Inexact Rounded
precision: 27
mulx252 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.290801193369671916512       Inexact Rounded
precision: 26
mulx253 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.29080119336967191651        Inexact Rounded
precision: 25
mulx254 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.2908011933696719165         Inexact Rounded
precision: 24
mulx255 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.290801193369671917          Inexact Rounded
precision: 23
mulx256 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.29080119336967192           Inexact Rounded
precision: 22
mulx257 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.2908011933696719            Inexact Rounded
precision: 21
mulx258 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.290801193369672             Inexact Rounded
precision: 20
mulx259 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.29080119336967              Inexact Rounded
precision: 19
mulx260 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.2908011933697               Inexact Rounded
precision: 18
mulx261 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.290801193370                Inexact Rounded
precision: 17
mulx262 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.29080119337                 Inexact Rounded
precision: 16
mulx263 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.2908011934                  Inexact Rounded
precision: 15
mulx264 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.290801193                   Inexact Rounded
precision: 14
mulx265 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.29080119                    Inexact Rounded
precision: 13
mulx266 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.2908012                     Inexact Rounded
precision: 12
mulx267 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.290801                      Inexact Rounded
precision: 11
mulx268 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.29080                       Inexact Rounded
precision: 10
mulx269 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.2908                        Inexact Rounded
precision:  9
mulx270 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.291                         Inexact Rounded
precision:  8
mulx271 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.29                          Inexact Rounded
precision:  7
mulx272 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433.3                           Inexact Rounded
precision:  6
mulx273 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 145433                            Inexact Rounded
precision:  5
mulx274 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 1.4543E+5                         Inexact Rounded
precision:  4
mulx275 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 1.454E+5                         Inexact Rounded
precision:  3
mulx276 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 1.45E+5                         Inexact Rounded
precision:  2
mulx277 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 1.5E+5                         Inexact Rounded
precision:  1
mulx278 multiply 30269.587755640502150977251770554 4.8046009735990873395936309640543 -> 1E+5                          Inexact Rounded

-- test some edge cases with exact rounding
maxexponent: 9999
minexponent: -9999
precision: 9
mulx301 multiply 9 9   -> 81
mulx302 multiply 9 90   -> 810
mulx303 multiply 9 900   -> 8100
mulx304 multiply 9 9000   -> 81000
mulx305 multiply 9 90000   -> 810000
mulx306 multiply 9 900000   -> 8100000
mulx307 multiply 9 9000000   -> 81000000
mulx308 multiply 9 90000000   -> 810000000
mulx309 multiply 9 900000000   -> 8.10000000E+9   Rounded
mulx310 multiply 9 9000000000   -> 8.10000000E+10  Rounded
mulx311 multiply 9 90000000000   -> 8.10000000E+11  Rounded
mulx312 multiply 9 900000000000   -> 8.10000000E+12  Rounded
mulx313 multiply 9 9000000000000   -> 8.10000000E+13  Rounded
mulx314 multiply 9 90000000000000   -> 8.10000000E+14  Rounded
mulx315 multiply 9 900000000000000   -> 8.10000000E+15  Rounded
mulx316 multiply 9 9000000000000000   -> 8.10000000E+16  Rounded
mulx317 multiply 9 90000000000000000   -> 8.10000000E+17  Rounded
mulx318 multiply 9 900000000000000000   -> 8.10000000E+18  Rounded
mulx319 multiply 9 9000000000000000000   -> 8.10000000E+19  Rounded
mulx320 multiply 9 90000000000000000000   -> 8.10000000E+20  Rounded
mulx321 multiply 9 900000000000000000000   -> 8.10000000E+21  Rounded
mulx322 multiply 9 9000000000000000000000   -> 8.10000000E+22  Rounded
mulx323 multiply 9 90000000000000000000000   -> 8.10000000E+23  Rounded

-- fastpath breakers
precision:   29
mulx330 multiply 1.491824697641270317824852952837224 1.105170918075647624811707826490246514675628614562883537345747603 -> 1.6487212707001281468486507878 Inexact Rounded
precision:   55
mulx331 multiply 0.8958341352965282506768545828765117803873717284891040428 0.8958341352965282506768545828765117803873717284891040428 -> 0.8025187979624784829842553829934069955890983696752228299 Inexact Rounded


-- tryzeros cases
precision:   7
rounding:    half_up
maxExponent: 92
minexponent: -92
mulx504  multiply  0E-60 1000E-60  -> 0E-98 Clamped
mulx505  multiply  100E+60 0E+60   -> 0E+92 Clamped

-- mixed with zeros
maxexponent: 999999999
minexponent: -999999999
precision: 9
mulx541 multiply  0    -1     -> -0
mulx542 multiply -0    -1     ->  0
mulx543 multiply  0     1     ->  0
mulx544 multiply -0     1     -> -0
mulx545 multiply -1     0     -> -0
mulx546 multiply -1    -0     ->  0
mulx547 multiply  1     0     ->  0
mulx548 multiply  1    -0     -> -0

mulx551 multiply  0.0  -1     -> -0.0
mulx552 multiply -0.0  -1     ->  0.0
mulx553 multiply  0.0   1     ->  0.0
mulx554 multiply -0.0   1     -> -0.0
mulx555 multiply -1.0   0     -> -0.0
mulx556 multiply -1.0  -0     ->  0.0
mulx557 multiply  1.0   0     ->  0.0
mulx558 multiply  1.0  -0     -> -0.0

mulx561 multiply  0    -1.0   -> -0.0
mulx562 multiply -0    -1.0   ->  0.0
mulx563 multiply  0     1.0   ->  0.0
mulx564 multiply -0     1.0   -> -0.0
mulx565 multiply -1     0.0   -> -0.0
mulx566 multiply -1    -0.0   ->  0.0
mulx567 multiply  1     0.0   ->  0.0
mulx568 multiply  1    -0.0   -> -0.0

mulx571 multiply  0.0  -1.0   -> -0.00
mulx572 multiply -0.0  -1.0   ->  0.00
mulx573 multiply  0.0   1.0   ->  0.00
mulx574 multiply -0.0   1.0   -> -0.00
mulx575 multiply -1.0   0.0   -> -0.00
mulx576 multiply -1.0  -0.0   ->  0.00
mulx577 multiply  1.0   0.0   ->  0.00
mulx578 multiply  1.0  -0.0   -> -0.00


-- Specials
mulx580 multiply  Inf  -Inf   -> -Infinity
mulx581 multiply  Inf  -1000  -> -Infinity
mulx582 multiply  Inf  -1     -> -Infinity
mulx583 multiply  Inf  -0     ->  NaN  Invalid_operation
mulx584 multiply  Inf   0     ->  NaN  Invalid_operation
mulx585 multiply  Inf   1     ->  Infinity
mulx586 multiply  Inf   1000  ->  Infinity
mulx587 multiply  Inf   Inf   ->  Infinity
mulx588 multiply -1000  Inf   -> -Infinity
mulx589 multiply -Inf   Inf   -> -Infinity
mulx590 multiply -1     Inf   -> -Infinity
mulx591 multiply -0     Inf   ->  NaN  Invalid_operation
mulx592 multiply  0     Inf   ->  NaN  Invalid_operation
mulx593 multiply  1     Inf   ->  Infinity
mulx594 multiply  1000  Inf   ->  Infinity
mulx595 multiply  Inf   Inf   ->  Infinity

mulx600 multiply -Inf  -Inf   ->  Infinity
mulx601 multiply -Inf  -1000  ->  Infinity
mulx602 multiply -Inf  -1     ->  Infinity
mulx603 multiply -Inf  -0     ->  NaN  Invalid_operation
mulx604 multiply -Inf   0     ->  NaN  Invalid_operation
mulx605 multiply -Inf   1     -> -Infinity
mulx606 multiply -Inf   1000  -> -Infinity
mulx607 multiply -Inf   Inf   -> -Infinity
mulx608 multiply -1000  Inf   -> -Infinity
mulx609 multiply -Inf  -Inf   ->  Infinity
mulx610 multiply -1    -Inf   ->  Infinity
mulx611 multiply -0    -Inf   ->  NaN  Invalid_operation
mulx612 multiply  0    -Inf   ->  NaN  Invalid_operation
mulx613 multiply  1    -Inf   -> -Infinity
mulx614 multiply  1000 -Inf   -> -Infinity
mulx615 multiply  Inf  -Inf   -> -Infinity

mulx621 multiply  NaN -Inf    ->  NaN
mulx622 multiply  NaN -1000   ->  NaN
mulx623 multiply  NaN -1      ->  NaN
mulx624 multiply  NaN -0      ->  NaN
mulx625 multiply  NaN  0      ->  NaN
mulx626 multiply  NaN  1      ->  NaN
mulx627 multiply  NaN  1000   ->  NaN
mulx628 multiply  NaN  Inf    ->  NaN
mulx629 multiply  NaN  NaN    ->  NaN
mulx630 multiply -Inf  NaN    ->  NaN
mulx631 multiply -1000 NaN    ->  NaN
mulx632 multiply -1    NaN    ->  NaN
mulx633 multiply -0    NaN    ->  NaN
mulx634 multiply  0    NaN    ->  NaN
mulx635 multiply  1    NaN    ->  NaN
mulx636 multiply  1000 NaN    ->  NaN
mulx637 multiply  Inf  NaN    ->  NaN

mulx641 multiply  sNaN -Inf   ->  NaN  Invalid_operation
mulx642 multiply  sNaN -1000  ->  NaN  Invalid_operation
mulx643 multiply  sNaN -1     ->  NaN  Invalid_operation
mulx644 multiply  sNaN -0     ->  NaN  Invalid_operation
mulx645 multiply  sNaN  0     ->  NaN  Invalid_operation
mulx646 multiply  sNaN  1     ->  NaN  Invalid_operation
mulx647 multiply  sNaN  1000  ->  NaN  Invalid_operation
mulx648 multiply  sNaN  NaN   ->  NaN  Invalid_operation
mulx649 multiply  sNaN sNaN   ->  NaN  Invalid_operation
mulx650 multiply  NaN  sNaN   ->  NaN  Invalid_operation
mulx651 multiply -Inf  sNaN   ->  NaN  Invalid_operation
mulx652 multiply -1000 sNaN   ->  NaN  Invalid_operation
mulx653 multiply -1    sNaN   ->  NaN  Invalid_operation
mulx654 multiply -0    sNaN   ->  NaN  Invalid_operation
mulx655 multiply  0    sNaN   ->  NaN  Invalid_operation
mulx656 multiply  1    sNaN   ->  NaN  Invalid_operation
mulx657 multiply  1000 sNaN   ->  NaN  Invalid_operation
mulx658 multiply  Inf  sNaN   ->  NaN  Invalid_operation
mulx659 multiply  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
mulx661 multiply  NaN9 -Inf   ->  NaN9
mulx662 multiply  NaN8  999   ->  NaN8
mulx663 multiply  NaN71 Inf   ->  NaN71
mulx664 multiply  NaN6  NaN5  ->  NaN6
mulx665 multiply -Inf   NaN4  ->  NaN4
mulx666 multiply -999   NaN33 ->  NaN33
mulx667 multiply  Inf   NaN2  ->  NaN2

mulx671 multiply  sNaN99 -Inf    ->  NaN99 Invalid_operation
mulx672 multiply  sNaN98 -11     ->  NaN98 Invalid_operation
mulx673 multiply  sNaN97  NaN    ->  NaN97 Invalid_operation
mulx674 multiply  sNaN16 sNaN94  ->  NaN16 Invalid_operation
mulx675 multiply  NaN95  sNaN93  ->  NaN93 Invalid_operation
mulx676 multiply -Inf    sNaN92  ->  NaN92 Invalid_operation
mulx677 multiply  088    sNaN91  ->  NaN91 Invalid_operation
mulx678 multiply  Inf    sNaN90  ->  NaN90 Invalid_operation
mulx679 multiply  NaN    sNaN89  ->  NaN89 Invalid_operation

mulx681 multiply -NaN9 -Inf   -> -NaN9
mulx682 multiply -NaN8  999   -> -NaN8
mulx683 multiply -NaN71 Inf   -> -NaN71
mulx684 multiply -NaN6 -NaN5  -> -NaN6
mulx685 multiply -Inf  -NaN4  -> -NaN4
mulx686 multiply -999  -NaN33 -> -NaN33
mulx687 multiply  Inf  -NaN2  -> -NaN2

mulx691 multiply -sNaN99 -Inf    -> -NaN99 Invalid_operation
mulx692 multiply -sNaN98 -11     -> -NaN98 Invalid_operation
mulx693 multiply -sNaN97  NaN    -> -NaN97 Invalid_operation
mulx694 multiply -sNaN16 -sNaN94 -> -NaN16 Invalid_operation
mulx695 multiply -NaN95  -sNaN93 -> -NaN93 Invalid_operation
mulx696 multiply -Inf    -sNaN92 -> -NaN92 Invalid_operation
mulx697 multiply  088    -sNaN91 -> -NaN91 Invalid_operation
mulx698 multiply  Inf    -sNaN90 -> -NaN90 Invalid_operation
mulx699 multiply -NaN    -sNaN89 -> -NaN89 Invalid_operation

mulx701 multiply -NaN  -Inf   -> -NaN
mulx702 multiply -NaN   999   -> -NaN
mulx703 multiply -NaN   Inf   -> -NaN
mulx704 multiply -NaN  -NaN   -> -NaN
mulx705 multiply -Inf  -NaN0  -> -NaN
mulx706 multiply -999  -NaN   -> -NaN
mulx707 multiply  Inf  -NaN   -> -NaN

mulx711 multiply -sNaN   -Inf    -> -NaN Invalid_operation
mulx712 multiply -sNaN   -11     -> -NaN Invalid_operation
mulx713 multiply -sNaN00  NaN    -> -NaN Invalid_operation
mulx714 multiply -sNaN   -sNaN   -> -NaN Invalid_operation
mulx715 multiply -NaN    -sNaN   -> -NaN Invalid_operation
mulx716 multiply -Inf    -sNaN   -> -NaN Invalid_operation
mulx717 multiply  088    -sNaN   -> -NaN Invalid_operation
mulx718 multiply  Inf    -sNaN   -> -NaN Invalid_operation
mulx719 multiply -NaN    -sNaN   -> -NaN Invalid_operation

-- overflow and underflow tests .. note subnormal results
maxexponent: 999999999
minexponent: -999999999
mulx730 multiply +1.23456789012345E-0 9E+999999999 -> Infinity Inexact Overflow Rounded
mulx731 multiply 9E+999999999 +1.23456789012345E-0 -> Infinity Inexact Overflow Rounded
mulx732 multiply +0.100 9E-999999999 -> 9.00E-1000000000 Subnormal
mulx733 multiply 9E-999999999 +0.100 -> 9.00E-1000000000 Subnormal
mulx735 multiply -1.23456789012345E-0 9E+999999999 -> -Infinity Inexact Overflow Rounded
mulx736 multiply 9E+999999999 -1.23456789012345E-0 -> -Infinity Inexact Overflow Rounded
mulx737 multiply -0.100 9E-999999999 -> -9.00E-1000000000 Subnormal
mulx738 multiply 9E-999999999 -0.100 -> -9.00E-1000000000 Subnormal

mulx739 multiply 1e-599999999 1e-400000001 -> 1E-1000000000 Subnormal
mulx740 multiply 1e-599999999 1e-400000000 -> 1E-999999999
mulx741 multiply 1e-600000000 1e-400000000 -> 1E-1000000000 Subnormal
mulx742 multiply 9e-999999998 0.01 -> 9E-1000000000 Subnormal
mulx743 multiply 9e-999999998 0.1  -> 9E-999999999
mulx744 multiply 0.01 9e-999999998 -> 9E-1000000000 Subnormal
mulx745 multiply 1e599999999 1e400000001 -> Infinity Overflow Inexact Rounded
mulx746 multiply 1e599999999 1e400000000 -> 1E+999999999
mulx747 multiply 1e600000000 1e400000000 -> Infinity Overflow Inexact Rounded
mulx748 multiply 9e999999998 100  -> Infinity Overflow Inexact Rounded
mulx749 multiply 9e999999998 10   -> 9.0E+999999999
mulx750 multiply 100  9e999999998 -> Infinity Overflow Inexact Rounded
-- signs
mulx751 multiply  1e+777777777  1e+411111111 ->  Infinity Overflow Inexact Rounded
mulx752 multiply  1e+777777777 -1e+411111111 -> -Infinity Overflow Inexact Rounded
mulx753 multiply -1e+777777777  1e+411111111 -> -Infinity Overflow Inexact Rounded
mulx754 multiply -1e+777777777 -1e+411111111 ->  Infinity Overflow Inexact Rounded
mulx755 multiply  1e-777777777  1e-411111111 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
mulx756 multiply  1e-777777777 -1e-411111111 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
mulx757 multiply -1e-777777777  1e-411111111 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
mulx758 multiply -1e-777777777 -1e-411111111 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped

-- 'subnormal' boundary (all hard underflow or overflow in base arithmetic)
precision: 9
mulx760 multiply 1e-600000000 1e-400000001 -> 1E-1000000001 Subnormal
mulx761 multiply 1e-600000000 1e-400000002 -> 1E-1000000002 Subnormal
mulx762 multiply 1e-600000000 1e-400000003 -> 1E-1000000003 Subnormal
mulx763 multiply 1e-600000000 1e-400000004 -> 1E-1000000004 Subnormal
mulx764 multiply 1e-600000000 1e-400000005 -> 1E-1000000005 Subnormal
mulx765 multiply 1e-600000000 1e-400000006 -> 1E-1000000006 Subnormal
mulx766 multiply 1e-600000000 1e-400000007 -> 1E-1000000007 Subnormal
mulx767 multiply 1e-600000000 1e-400000008 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
mulx768 multiply 1e-600000000 1e-400000009 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
mulx769 multiply 1e-600000000 1e-400000010 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
-- [no equivalent of 'subnormal' for overflow]
mulx770 multiply 1e+600000000 1e+400000001 -> Infinity Overflow Inexact Rounded
mulx771 multiply 1e+600000000 1e+400000002 -> Infinity Overflow Inexact Rounded
mulx772 multiply 1e+600000000 1e+400000003 -> Infinity Overflow Inexact Rounded
mulx773 multiply 1e+600000000 1e+400000004 -> Infinity Overflow Inexact Rounded
mulx774 multiply 1e+600000000 1e+400000005 -> Infinity Overflow Inexact Rounded
mulx775 multiply 1e+600000000 1e+400000006 -> Infinity Overflow Inexact Rounded
mulx776 multiply 1e+600000000 1e+400000007 -> Infinity Overflow Inexact Rounded
mulx777 multiply 1e+600000000 1e+400000008 -> Infinity Overflow Inexact Rounded
mulx778 multiply 1e+600000000 1e+400000009 -> Infinity Overflow Inexact Rounded
mulx779 multiply 1e+600000000 1e+400000010 -> Infinity Overflow Inexact Rounded

-- 'subnormal' test edge condition at higher precisions
precision: 99
mulx780 multiply 1e-600000000 1e-400000007 -> 1E-1000000007 Subnormal
mulx781 multiply 1e-600000000 1e-400000008 -> 1E-1000000008 Subnormal
mulx782 multiply 1e-600000000 1e-400000097 -> 1E-1000000097 Subnormal
mulx783 multiply 1e-600000000 1e-400000098 -> 0E-1000000097 Underflow Subnormal Inexact Rounded Clamped
precision: 999
mulx784 multiply 1e-600000000 1e-400000997 -> 1E-1000000997 Subnormal
mulx785 multiply 1e-600000000 1e-400000998 -> 0E-1000000997 Underflow Subnormal Inexact Rounded Clamped

-- following testcases [through mulx800] not yet run against code
precision: 9999
mulx786 multiply 1e-600000000 1e-400009997 -> 1E-1000009997 Subnormal
mulx787 multiply 1e-600000000 1e-400009998 -> 0E-1000009997 Underflow Subnormal Inexact Rounded Clamped
precision: 99999
mulx788 multiply 1e-600000000 1e-400099997 -> 1E-1000099997 Subnormal
mulx789 multiply 1e-600000000 1e-400099998 -> 0E-1000099997 Underflow Subnormal Inexact Rounded Clamped
precision: 999999
mulx790 multiply 1e-600000000 1e-400999997 -> 1E-1000999997 Subnormal
mulx791 multiply 1e-600000000 1e-400999998 -> 0E-1000999997 Underflow Subnormal Inexact Rounded Clamped
precision: 9999999
mulx792 multiply 1e-600000000 1e-409999997 -> 1E-1009999997 Subnormal
mulx793 multiply 1e-600000000 1e-409999998 -> 0E-1009999997 Underflow Subnormal Inexact Rounded Clamped
precision: 99999999
mulx794 multiply 1e-600000000 1e-499999997 -> 1E-1099999997 Subnormal
mulx795 multiply 1e-600000000 1e-499999998 -> 0E-1099999997 Underflow Subnormal Inexact Rounded Clamped
precision: 999999999
mulx796 multiply 1e-999999999 1e-999999997 -> 1E-1999999996 Subnormal
mulx797 multiply 1e-999999999 1e-999999998 -> 1E-1999999997 Subnormal
mulx798 multiply 1e-999999999 1e-999999999 -> 0E-1999999997 Underflow Subnormal Inexact Rounded Clamped
mulx799 multiply 1e-600000000 1e-400000007 -> 1E-1000000007 Subnormal
mulx800 multiply 1e-600000000 1e-400000008 -> 1E-1000000008 Subnormal

-- test subnormals rounding
precision:   5
maxExponent: 999
minexponent: -999
rounding:    half_even

mulx801 multiply  1.0000E-999  1     -> 1.0000E-999
mulx802 multiply  1.000E-999   1e-1  -> 1.000E-1000 Subnormal
mulx803 multiply  1.00E-999    1e-2  -> 1.00E-1001  Subnormal
mulx804 multiply  1.0E-999     1e-3  -> 1.0E-1002   Subnormal
mulx805 multiply  1.0E-999     1e-4  -> 1E-1003     Subnormal Rounded
mulx806 multiply  1.3E-999     1e-4  -> 1E-1003     Underflow Subnormal Inexact Rounded
mulx807 multiply  1.5E-999     1e-4  -> 2E-1003     Underflow Subnormal Inexact Rounded
mulx808 multiply  1.7E-999     1e-4  -> 2E-1003     Underflow Subnormal Inexact Rounded
mulx809 multiply  2.3E-999     1e-4  -> 2E-1003     Underflow Subnormal Inexact Rounded
mulx810 multiply  2.5E-999     1e-4  -> 2E-1003     Underflow Subnormal Inexact Rounded
mulx811 multiply  2.7E-999     1e-4  -> 3E-1003     Underflow Subnormal Inexact Rounded
mulx812 multiply  1.49E-999    1e-4  -> 1E-1003     Underflow Subnormal Inexact Rounded
mulx813 multiply  1.50E-999    1e-4  -> 2E-1003     Underflow Subnormal Inexact Rounded
mulx814 multiply  1.51E-999    1e-4  -> 2E-1003     Underflow Subnormal Inexact Rounded
mulx815 multiply  2.49E-999    1e-4  -> 2E-1003     Underflow Subnormal Inexact Rounded
mulx816 multiply  2.50E-999    1e-4  -> 2E-1003     Underflow Subnormal Inexact Rounded
mulx817 multiply  2.51E-999    1e-4  -> 3E-1003     Underflow Subnormal Inexact Rounded

mulx818 multiply  1E-999       1e-4  -> 1E-1003     Subnormal
mulx819 multiply  3E-999       1e-5  -> 0E-1003     Underflow Subnormal Inexact Rounded Clamped
mulx820 multiply  5E-999       1e-5  -> 0E-1003     Underflow Subnormal Inexact Rounded Clamped
mulx821 multiply  7E-999       1e-5  -> 1E-1003     Underflow Subnormal Inexact Rounded
mulx822 multiply  9E-999       1e-5  -> 1E-1003     Underflow Subnormal Inexact Rounded
mulx823 multiply  9.9E-999     1e-5  -> 1E-1003     Underflow Subnormal Inexact Rounded

mulx824 multiply  1E-999      -1e-4  -> -1E-1003    Subnormal
mulx825 multiply  3E-999      -1e-5  -> -0E-1003    Underflow Subnormal Inexact Rounded Clamped
mulx826 multiply -5E-999       1e-5  -> -0E-1003    Underflow Subnormal Inexact Rounded Clamped
mulx827 multiply  7E-999      -1e-5  -> -1E-1003    Underflow Subnormal Inexact Rounded
mulx828 multiply -9E-999       1e-5  -> -1E-1003    Underflow Subnormal Inexact Rounded
mulx829 multiply  9.9E-999    -1e-5  -> -1E-1003    Underflow Subnormal Inexact Rounded
mulx830 multiply  3.0E-999    -1e-5  -> -0E-1003    Underflow Subnormal Inexact Rounded Clamped

mulx831 multiply  1.0E-501     1e-501 -> 1.0E-1002   Subnormal
mulx832 multiply  2.0E-501     2e-501 -> 4.0E-1002   Subnormal
mulx833 multiply  4.0E-501     4e-501 -> 1.60E-1001  Subnormal
mulx834 multiply 10.0E-501    10e-501 -> 1.000E-1000 Subnormal
mulx835 multiply 30.0E-501    30e-501 -> 9.000E-1000 Subnormal
mulx836 multiply 40.0E-501    40e-501 -> 1.6000E-999

-- squares
mulx840 multiply  1E-502       1e-502 -> 0E-1003     Underflow Subnormal Inexact Rounded Clamped
mulx841 multiply  1E-501       1e-501 -> 1E-1002     Subnormal
mulx842 multiply  2E-501       2e-501 -> 4E-1002     Subnormal
mulx843 multiply  4E-501       4e-501 -> 1.6E-1001   Subnormal
mulx844 multiply 10E-501      10e-501 -> 1.00E-1000  Subnormal
mulx845 multiply 30E-501      30e-501 -> 9.00E-1000  Subnormal
mulx846 multiply 40E-501      40e-501 -> 1.600E-999

-- cubes
mulx850 multiply  1E-670     1e-335 -> 0E-1003    Underflow Subnormal Inexact Rounded Clamped
mulx851 multiply  1E-668     1e-334 -> 1E-1002    Subnormal
mulx852 multiply  4E-668     2e-334 -> 8E-1002    Subnormal
mulx853 multiply  9E-668     3e-334 -> 2.7E-1001  Subnormal
mulx854 multiply 16E-668     4e-334 -> 6.4E-1001  Subnormal
mulx855 multiply 25E-668     5e-334 -> 1.25E-1000 Subnormal
mulx856 multiply 10E-668   100e-334 -> 1.000E-999

-- test derived from result of 0.099 ** 999 at 15 digits with unlimited exponent
precision: 19
mulx860 multiply  6636851557994578716E-520 6636851557994578716E-520 -> 4.40477986028551E-1003 Underflow Subnormal Inexact Rounded

-- Long operand overflow may be a different path
precision: 3
maxExponent: 999999999
minexponent: -999999999
mulx870 multiply 1  9.999E+999999999   ->  Infinity Inexact Overflow Rounded
mulx871 multiply 1 -9.999E+999999999   -> -Infinity Inexact Overflow Rounded
mulx872 multiply    9.999E+999999999 1 ->  Infinity Inexact Overflow Rounded
mulx873 multiply   -9.999E+999999999 1 -> -Infinity Inexact Overflow Rounded

-- check for double-rounded subnormals
precision:   5
maxexponent: 79
minexponent: -79
mulx881 multiply  1.2347E-40  1.2347E-40  ->  1.524E-80  Inexact Rounded Subnormal Underflow
mulx882 multiply  1.234E-40  1.234E-40    ->  1.523E-80  Inexact Rounded Subnormal Underflow
mulx883 multiply  1.23E-40   1.23E-40     ->  1.513E-80  Inexact Rounded Subnormal Underflow
mulx884 multiply  1.2E-40    1.2E-40      ->  1.44E-80   Subnormal
mulx885 multiply  1.2E-40    1.2E-41      ->  1.44E-81   Subnormal
mulx886 multiply  1.2E-40    1.2E-42      ->  1.4E-82    Subnormal Inexact Rounded Underflow
mulx887 multiply  1.2E-40    1.3E-42      ->  1.6E-82    Subnormal Inexact Rounded Underflow
mulx888 multiply  1.3E-40    1.3E-42      ->  1.7E-82    Subnormal Inexact Rounded Underflow
mulx889 multiply  1.3E-40    1.3E-43      ->    2E-83    Subnormal Inexact Rounded Underflow
mulx890 multiply  1.3E-41    1.3E-43      ->    0E-83    Clamped Subnormal Inexact Rounded Underflow

mulx891 multiply  1.2345E-39   1.234E-40  ->  1.5234E-79 Inexact Rounded
mulx892 multiply  1.23456E-39  1.234E-40  ->  1.5234E-79 Inexact Rounded
mulx893 multiply  1.2345E-40   1.234E-40  ->  1.523E-80  Inexact Rounded Subnormal Underflow
mulx894 multiply  1.23456E-40  1.234E-40  ->  1.523E-80  Inexact Rounded Subnormal Underflow
mulx895 multiply  1.2345E-41   1.234E-40  ->  1.52E-81   Inexact Rounded Subnormal Underflow
mulx896 multiply  1.23456E-41  1.234E-40  ->  1.52E-81   Inexact Rounded Subnormal Underflow

-- Now explore the case where we get a normal result with Underflow
precision:   16
rounding:    half_up
maxExponent: 384
minExponent: -383

mulx900 multiply  0.3000000000E-191 0.3000000000E-191 -> 9.00000000000000E-384 Subnormal Rounded
mulx901 multiply  0.3000000001E-191 0.3000000001E-191 -> 9.00000000600000E-384 Underflow Inexact Subnormal Rounded
mulx902 multiply  9.999999999999999E-383  0.0999999999999         -> 9.99999999999000E-384 Underflow Inexact Subnormal Rounded
mulx903 multiply  9.999999999999999E-383  0.09999999999999        -> 9.99999999999900E-384 Underflow Inexact Subnormal Rounded
mulx904 multiply  9.999999999999999E-383  0.099999999999999       -> 9.99999999999990E-384 Underflow Inexact Subnormal Rounded
mulx905 multiply  9.999999999999999E-383  0.0999999999999999      -> 9.99999999999999E-384 Underflow Inexact Subnormal Rounded
-- prove operands are exact
mulx906 multiply  9.999999999999999E-383  1                       -> 9.999999999999999E-383
mulx907 multiply                       1  0.09999999999999999     -> 0.09999999999999999
-- the next rounds to Nmin
mulx908 multiply  9.999999999999999E-383  0.09999999999999999     -> 1.000000000000000E-383 Underflow Inexact Subnormal Rounded
mulx909 multiply  9.999999999999999E-383  0.099999999999999999    -> 1.000000000000000E-383 Underflow Inexact Subnormal Rounded
mulx910 multiply  9.999999999999999E-383  0.0999999999999999999   -> 1.000000000000000E-383 Underflow Inexact Subnormal Rounded
mulx911 multiply  9.999999999999999E-383  0.09999999999999999999  -> 1.000000000000000E-383 Underflow Inexact Subnormal Rounded


-- Examples from SQL proposal (Krishna Kulkarni)
precision:   34
rounding:    half_up
maxExponent: 6144
minExponent: -6143
mulx1001  multiply 130E-2  120E-2 -> 1.5600
mulx1002  multiply 130E-2  12E-1  -> 1.560
mulx1003  multiply 130E-2  1E0    -> 1.30
mulx1004  multiply 1E2     1E4    -> 1E+6

-- payload decapitate
precision: 5
mulx1010  multiply 11 -sNaN1234567890 -> -NaN67890  Invalid_operation

-- Null tests
mulx990 multiply 10  # -> NaN Invalid_operation
mulx991 multiply  # 10 -> NaN Invalid_operation

