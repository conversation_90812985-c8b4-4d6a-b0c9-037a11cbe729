.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_verify_limits" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_verify_limits \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_certificate_set_verify_limits(gnutls_certificate_credentials_t " res ", unsigned int " max_bits ", unsigned int " max_depth ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t res" 12
is a gnutls_certificate_credentials type
.IP "unsigned int max_bits" 12
is the number of bits of an acceptable certificate (default 8200)
.IP "unsigned int max_depth" 12
is maximum depth of the verification of a certificate chain (default 5)
.SH "DESCRIPTION"
This function will set some upper limits for the default
verification function, \fBgnutls_certificate_verify_peers2()\fP, to avoid
denial of service attacks.  You can set them to zero to disable
limits.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
