.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_anti_replay_set_add_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_anti_replay_set_add_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_anti_replay_set_add_function(gnutls_anti_replay_t " anti_replay ", gnutls_db_add_func " add_func ");"
.SH ARGUMENTS
.IP "gnutls_anti_replay_t anti_replay" 12
is a \fBgnutls_anti_replay_t\fP type.
.IP "gnutls_db_add_func add_func" 12
is the function.
.SH "DESCRIPTION"
Sets the function that will be used to store an entry if it is not
already present in the resumed sessions database.  This function returns 0
if the entry is successfully stored, and a negative error code
otherwise.  In particular, if the entry is found in the database,
it returns \fBGNUTLS_E_DB_ENTRY_EXISTS\fP.

The arguments to the  \fIadd_func\fP are:
\- \fBptr\fP: the pointer set with \fBgnutls_anti_replay_set_ptr()\fP
\- \fBexp_time\fP: the expiration time of the entry
\- \fBkey\fP: a pointer to the key
\- \fBdata\fP: a pointer to data to store

The data set by this function can be examined using
\fBgnutls_db_check_entry_expire_time()\fP and \fBgnutls_db_check_entry_time()\fP.
.SH "SINCE"
3.6.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
