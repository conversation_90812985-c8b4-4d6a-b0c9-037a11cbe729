.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_range_split" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_range_split \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_range_split(gnutls_session_t " session ", const gnutls_range_st * " orig ", gnutls_range_st * " next ", gnutls_range_st * " remainder ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type
.IP "const gnutls_range_st * orig" 12
is the original range provided by the user
.IP "gnutls_range_st * next" 12
is the returned range that can be conveyed in a TLS record
.IP "gnutls_range_st * remainder" 12
is the returned remaining range
.SH "DESCRIPTION"
This function should be used when it is required to hide the length
of very long data that cannot be directly provided to \fBgnutls_record_send_range()\fP.
In that case this function should be called with the desired length
hiding range in  \fIorig\fP . The returned  \fInext\fP value should then be used in
the next call to \fBgnutls_record_send_range()\fP with the partial data.
That process should be repeated until  \fIremainder\fP is (0,0).
.SH "RETURNS"
0 in case splitting succeeds, non zero in case of error.
Note that  \fIorig\fP is not changed, while the values of  \fInext\fP and  \fIremainder\fP are modified to store the resulting values.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
