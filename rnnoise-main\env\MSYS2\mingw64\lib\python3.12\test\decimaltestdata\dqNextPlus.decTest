------------------------------------------------------------------------
-- dqNextPlus.decTest -- decQuad next that is greater [754r nextup]   --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- All operands and results are decQuads.
extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

dqnextp001 nextplus  0.9999999999999999999999999999999995 ->   0.9999999999999999999999999999999996
dqnextp002 nextplus  0.9999999999999999999999999999999996 ->   0.9999999999999999999999999999999997
dqnextp003 nextplus  0.9999999999999999999999999999999997 ->   0.9999999999999999999999999999999998
dqnextp004 nextplus  0.9999999999999999999999999999999998 ->   0.9999999999999999999999999999999999
dqnextp005 nextplus  0.9999999999999999999999999999999999 ->   1.000000000000000000000000000000000
dqnextp006 nextplus  1.000000000000000000000000000000000  ->   1.000000000000000000000000000000001
dqnextp007 nextplus  1.0         ->   1.000000000000000000000000000000001
dqnextp008 nextplus  1           ->   1.000000000000000000000000000000001
dqnextp009 nextplus  1.000000000000000000000000000000001  ->   1.000000000000000000000000000000002
dqnextp010 nextplus  1.000000000000000000000000000000002  ->   1.000000000000000000000000000000003
dqnextp011 nextplus  1.000000000000000000000000000000003  ->   1.000000000000000000000000000000004
dqnextp012 nextplus  1.000000000000000000000000000000004  ->   1.000000000000000000000000000000005
dqnextp013 nextplus  1.000000000000000000000000000000005  ->   1.000000000000000000000000000000006
dqnextp014 nextplus  1.000000000000000000000000000000006  ->   1.000000000000000000000000000000007
dqnextp015 nextplus  1.000000000000000000000000000000007  ->   1.000000000000000000000000000000008
dqnextp016 nextplus  1.000000000000000000000000000000008  ->   1.000000000000000000000000000000009
dqnextp017 nextplus  1.000000000000000000000000000000009  ->   1.000000000000000000000000000000010
dqnextp018 nextplus  1.000000000000000000000000000000010  ->   1.000000000000000000000000000000011
dqnextp019 nextplus  1.000000000000000000000000000000011  ->   1.000000000000000000000000000000012

dqnextp021 nextplus -0.9999999999999999999999999999999995 ->  -0.9999999999999999999999999999999994
dqnextp022 nextplus -0.9999999999999999999999999999999996 ->  -0.9999999999999999999999999999999995
dqnextp023 nextplus -0.9999999999999999999999999999999997 ->  -0.9999999999999999999999999999999996
dqnextp024 nextplus -0.9999999999999999999999999999999998 ->  -0.9999999999999999999999999999999997
dqnextp025 nextplus -0.9999999999999999999999999999999999 ->  -0.9999999999999999999999999999999998
dqnextp026 nextplus -1.000000000000000000000000000000000  ->  -0.9999999999999999999999999999999999
dqnextp027 nextplus -1.0         ->  -0.9999999999999999999999999999999999
dqnextp028 nextplus -1           ->  -0.9999999999999999999999999999999999
dqnextp029 nextplus -1.000000000000000000000000000000001  ->  -1.000000000000000000000000000000000
dqnextp030 nextplus -1.000000000000000000000000000000002  ->  -1.000000000000000000000000000000001
dqnextp031 nextplus -1.000000000000000000000000000000003  ->  -1.000000000000000000000000000000002
dqnextp032 nextplus -1.000000000000000000000000000000004  ->  -1.000000000000000000000000000000003
dqnextp033 nextplus -1.000000000000000000000000000000005  ->  -1.000000000000000000000000000000004
dqnextp034 nextplus -1.000000000000000000000000000000006  ->  -1.000000000000000000000000000000005
dqnextp035 nextplus -1.000000000000000000000000000000007  ->  -1.000000000000000000000000000000006
dqnextp036 nextplus -1.000000000000000000000000000000008  ->  -1.000000000000000000000000000000007
dqnextp037 nextplus -1.000000000000000000000000000000009  ->  -1.000000000000000000000000000000008
dqnextp038 nextplus -1.000000000000000000000000000000010  ->  -1.000000000000000000000000000000009
dqnextp039 nextplus -1.000000000000000000000000000000011  ->  -1.000000000000000000000000000000010
dqnextp040 nextplus -1.000000000000000000000000000000012  ->  -1.000000000000000000000000000000011

-- Zeros
dqnextp100 nextplus  0           ->  1E-6176
dqnextp101 nextplus  0.00        ->  1E-6176
dqnextp102 nextplus  0E-300      ->  1E-6176
dqnextp103 nextplus  0E+300      ->  1E-6176
dqnextp104 nextplus  0E+30000    ->  1E-6176
dqnextp105 nextplus -0           ->  1E-6176
dqnextp106 nextplus -0.00        ->  1E-6176
dqnextp107 nextplus -0E-300      ->  1E-6176
dqnextp108 nextplus -0E+300      ->  1E-6176
dqnextp109 nextplus -0E+30000    ->  1E-6176

-- specials
dqnextp150 nextplus   Inf    ->  Infinity
dqnextp151 nextplus  -Inf    -> -9.999999999999999999999999999999999E+6144
dqnextp152 nextplus   NaN    ->  NaN
dqnextp153 nextplus  sNaN    ->  NaN   Invalid_operation
dqnextp154 nextplus   NaN77  ->  NaN77
dqnextp155 nextplus  sNaN88  ->  NaN88 Invalid_operation
dqnextp156 nextplus  -NaN    -> -NaN
dqnextp157 nextplus -sNaN    -> -NaN   Invalid_operation
dqnextp158 nextplus  -NaN77  -> -NaN77
dqnextp159 nextplus -sNaN88  -> -NaN88 Invalid_operation

-- Nmax, Nmin, Ntiny, subnormals
dqnextp170 nextplus  -9.999999999999999999999999999999999E+6144  -> -9.999999999999999999999999999999998E+6144
dqnextp171 nextplus  -9.999999999999999999999999999999998E+6144  -> -9.999999999999999999999999999999997E+6144
dqnextp172 nextplus  -1E-6143                  -> -9.99999999999999999999999999999999E-6144
dqnextp173 nextplus  -1.000000000000000E-6143  -> -9.99999999999999999999999999999999E-6144
dqnextp174 nextplus  -9E-6176                  -> -8E-6176
dqnextp175 nextplus  -9.9E-6175                -> -9.8E-6175
dqnextp176 nextplus  -9.99999999999999999999999999999E-6147      -> -9.99999999999999999999999999998E-6147
dqnextp177 nextplus  -9.99999999999999999999999999999999E-6144   -> -9.99999999999999999999999999999998E-6144
dqnextp178 nextplus  -9.99999999999999999999999999999998E-6144   -> -9.99999999999999999999999999999997E-6144
dqnextp179 nextplus  -9.99999999999999999999999999999997E-6144   -> -9.99999999999999999999999999999996E-6144
dqnextp180 nextplus  -0E-6176                  ->  1E-6176
dqnextp181 nextplus  -1E-6176                  -> -0E-6176
dqnextp182 nextplus  -2E-6176                  -> -1E-6176

dqnextp183 nextplus   0E-6176                  ->  1E-6176
dqnextp184 nextplus   1E-6176                  ->  2E-6176
dqnextp185 nextplus   2E-6176                  ->  3E-6176
dqnextp186 nextplus   10E-6176                 ->  1.1E-6175
dqnextp187 nextplus   100E-6176                ->  1.01E-6174
dqnextp188 nextplus   100000E-6176             ->  1.00001E-6171
dqnextp189 nextplus   1.00000000000000000000000000000E-6143      ->  1.000000000000000000000000000000001E-6143
dqnextp190 nextplus   1.000000000000000000000000000000000E-6143  ->  1.000000000000000000000000000000001E-6143
dqnextp191 nextplus   1E-6143                  ->  1.000000000000000000000000000000001E-6143
dqnextp192 nextplus   9.999999999999999999999999999999998E+6144  ->  9.999999999999999999999999999999999E+6144
dqnextp193 nextplus   9.999999999999999999999999999999999E+6144  ->  Infinity

-- Null tests
dqnextp900 nextplus  # -> NaN Invalid_operation

