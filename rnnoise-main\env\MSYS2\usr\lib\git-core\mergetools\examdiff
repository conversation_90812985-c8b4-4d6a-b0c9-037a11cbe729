diff_cmd () {
	"$merge_tool_path" "$LOCAL" "$REMOTE" -nh
}

diff_cmd_help () {
	echo "Use ExamDiff Pro (requires a graphical session)"
}

merge_cmd () {
	if $base_present
	then
		"$merge_tool_path" -merge "$LOCAL" "$BASE" "$REMOTE" -o:"$MERGED" -nh
	else
		"$merge_tool_path" -merge "$LOCAL" "$REMOTE" -o:"$MERGED" -nh
	fi
}

merge_cmd_help () {
	echo "Use ExamDiff Pro (requires a graphical session)"
}

translate_merge_tool_path() {
	mergetool_find_win32_cmd "ExamDiff.com" "ExamDiff Pro"
}
