Tests for Mediawiki-to-Git
==========================

Introduction
------------
This manual describes how to install the git-remote-mediawiki test
environment on a machine with git installed on it.

Prerequisite
------------

In order to run this test environment correctly, you will need to
install the following packages (Debian/Ubuntu names, may need to be
adapted for another distribution):

* lighttpd
* php
* php-cgi
* php-cli
* php-curl
* php-sqlite

Principles and Technical Choices
--------------------------------

The test environment makes it easy to install and manipulate one or
several MediaWiki instances. To allow developers to run the testsuite
easily, the environment does not require root privilege (except to
install the required packages if needed). It starts a webserver
instance on the user's account (using lighttpd greatly helps for
that), and does not need a separate database daemon (thanks to the use
of sqlite).

Run the test environment
------------------------

Install a new wiki
~~~~~~~~~~~~~~~~~~

Once you have all the prerequisite, you need to install a MediaWiki
instance on your machine. If you already have one, it is still
strongly recommended to install one with the script provided. Here's
how to work it:

a. change directory to contrib/mw-to-git/t/
b. if needed, edit test.config to choose your installation parameters
c. run `./install-wiki.sh install`
d. check on your favourite web browser if your wiki is correctly
   installed.

Remove an existing wiki
~~~~~~~~~~~~~~~~~~~~~~~

Edit the file test.config to fit the wiki you want to delete, and then
execute the command `./install-wiki.sh delete` from the
contrib/mw-to-git/t directory.

Run the existing tests
~~~~~~~~~~~~~~~~~~~~~~

The provided tests are currently in the `contrib/mw-to-git/t` directory.
The files are all the t936[0-9]-*.sh shell scripts.

a. Run all tests:
To do so, run "make test" from the contrib/mw-to-git/ directory.

b. Run a specific test:
To run a given test <test_name>, run ./<test_name> from the
contrib/mw-to-git/t directory.

How to create new tests
-----------------------

Available functions
~~~~~~~~~~~~~~~~~~~

The test environment of git-remote-mediawiki provides some functions
useful to test its behaviour. for more details about the functions'
parameters, please refer to the `test-gitmw-lib.sh` and
`test-gitmw.pl` files.

** `test_check_wiki_precond`:
Check if the tests must be skipped or not. Please use this function
at the beginning of each new test file.

** `wiki_getpage`:
Fetch a given page from the wiki and puts its content in the
directory in parameter.

** `wiki_delete_page`:
Delete a given page from the wiki.

** `wiki_edit_page`:
Create or modify a given page in the wiki. You can specify several
parameters like a summary for the page edition, or add the page to a
given category.
See test-gitmw.pl for more details.

** `wiki_getallpage`:
Fetch all pages from the wiki into a given directory. The directory
is created if it does not exists.

** `test_diff_directories`:
Compare the content of two directories. The content must be the same.
Use this function to compare the content of a git directory and a wiki
one created by wiki_getallpage.

** `test_contains_N_files`:
Check if the given directory contains a given number of file.

** `wiki_page_exists`:
Tests if a given page exists on the wiki.

** `wiki_reset`:
Reset the wiki, i.e. flush the database. Use this function at the
beginning of each new test, except if the test re-uses the same wiki
(and history) as the previous test.

How to write a new test
~~~~~~~~~~~~~~~~~~~~~~~

Please, follow the standards given by git. See git/t/README.
New file should be named as t936[0-9]-*.sh.
Be sure to reset your wiki regularly with the function `wiki_reset`.
