.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ext_set_data" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ext_set_data \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_ext_set_data(gnutls_session_t " session ", unsigned " tls_id ", gnutls_ext_priv_data_t " data ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
a \fBgnutls_session_t\fP opaque pointer
.IP "unsigned tls_id" 12
the numeric id of the extension
.IP "gnutls_ext_priv_data_t data" 12
the private data to set
.SH "DESCRIPTION"
This function allows an extension handler to store data in the current session
and retrieve them later on. The set data will be deallocated using
the gnutls_ext_deinit_data_func.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
