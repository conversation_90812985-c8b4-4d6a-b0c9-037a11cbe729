<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY-ML-DSA</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Keygen-Parameters">Keygen Parameters</a></li>
      <li><a href="#Common-ML-DSA-parameters">Common ML-DSA parameters</a></li>
      <li><a href="#Provider-configuration-parameters">Provider configuration parameters</a></li>
    </ul>
  </li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY-ML-DSA, EVP_KEYMGMT-ML-DSA, EVP_PKEY-ML-DSA-44, EVP_PKEY-ML-DSA-65, EVP_PKEY-ML-DSA-87 - EVP_PKEY ML-DSA keytype and algorithm support</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>ML-DSA implements the algorithms <b>ML-DSA-44</b>, <b>ML-DSA-65</b> and <b>ML-DSA-87</b>. The key types <b>EVP_PKEY_ML_DSA_44</b>, <b>EVP_PKEY_ML_DSA_65</b> and <b>EVP_PKEY_ML_DSA_87</b> are implemented in OpenSSL&#39;s default and FIPS providers. These implementations support the associated key, containing the public key <i>pub</i> and the private key <i>priv</i>.</p>

<p>Each of the different key types has an associated security category. This value is one of 2, 3 or 5 for key types <b>ML-DSA-44</b>, <b>ML-DSA-65</b> and <b>ML-DSA-87</b> respectively, which correspond to security strengths of 128, 192 and 256 repsectively.</p>

<h2 id="Keygen-Parameters">Keygen Parameters</h2>

<dl>

<dt id="seed-OSSL_PKEY_PARAM_ML_DSA_SEED-octet-string">&quot;seed&quot; (<b>OSSL_PKEY_PARAM_ML_DSA_SEED</b>) &lt;octet string&gt;</dt>
<dd>

<p>The seed can be used to generate the private and public key components in a deterministic manner. The length of the value supplied must be 32 bytes. When this value is not supplied the seed is generated randomly using a DRBG.</p>

<p>Generated keys default to retaining the seed used. The seed is also by default retained when keys are loaded from <b>PKCS#8</b> files in the seed format. When available, the seed parameter is also used during key export and import, with keys (by default) regenerated from the seed even when also provided on import. See <a href="#Provider-configuration-parameters">&quot;Provider configuration parameters&quot;</a> below for related controls.</p>

<p>When the seed is retained, it is also available as a <b>gettable</b> parameter, and private key output to <b>PKCS#8</b> files will by default include the seed. When the seed was not initially known, or was not retained, <b>PKCS#8</b> private key files will contain only the private key in FIPS 204 <code>sk</code> format.</p>

</dd>
<dt id="properties-OSSL_PKEY_PARAM_PROPERTIES-UTF8-string">&quot;properties&quot; (<b>OSSL_PKEY_PARAM_PROPERTIES</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>Sets properties to be used when fetching algorithm implementations used for ML-DSA hashing operations.</p>

</dd>
</dl>

<p>Use <a href="../man3/EVP_PKEY_CTX_set_params.html">EVP_PKEY_CTX_set_params(3)</a> after calling <a href="../man3/EVP_PKEY_keygen_init.html">EVP_PKEY_keygen_init(3)</a>.</p>

<h2 id="Common-ML-DSA-parameters">Common ML-DSA parameters</h2>

<p>In addition to the common parameters that all keytypes should support (see <a href="../man7/provider-keymgmt.html">&quot;Common Information Parameters&quot; in provider-keymgmt(7)</a>, the implementation of these key types support the parameters listed below. These are gettable using <a href="../man3/EVP_PKEY_get_octet_string_param.html">EVP_PKEY_get_octet_string_param(3)</a> or <a href="../man3/EVP_PKEY_get_params.html">EVP_PKEY_get_params(3)</a>. They can be initialised via <a href="../man3/EVP_PKEY_fromdata.html">EVP_PKEY_fromdata(3)</a>, and are returned by <a href="../man3/EVP_PKEY_todata.html">EVP_PKEY_todata(3)</a> given a suitable <i>selection</i>. Once a public or private key is configured, it can no longer be modified, nor can another key component be added.</p>

<dl>

<dt id="pub-OSSL_PKEY_PARAM_PUB_KEY-octet-string">&quot;pub&quot; (<b>OSSL_PKEY_PARAM_PUB_KEY</b>) &lt;octet string&gt;</dt>
<dd>

<p>The encoded public key value of size 1312, 1952 or 2592 bytes depending on the respective key type of <b>ML-DSA-44</b>, <b>ML-DSA-65</b> or <b>ML-DSA-87</b>.</p>

</dd>
<dt id="priv-OSSL_PKEY_PARAM_PRIV_KEY-octet-string">&quot;priv&quot; (<b>OSSL_PKEY_PARAM_PRIV_KEY</b>) &lt;octet string&gt;</dt>
<dd>

<p>The encoded private key value of size 2560, 4032 or 4896 bytes depending on the respective key type of <b>ML-DSA-44</b>, <b>ML-DSA-65</b> or <b>ML-DSA-87</b>.</p>

</dd>
</dl>

<h2 id="Provider-configuration-parameters">Provider configuration parameters</h2>

<p>See the description of the <b>-provparam</b> option in <a href="../man1/openssl.html">openssl(1)</a> to learn how to set provider configuration parameters in the command line tools. See <a href="../man3/OSSL_PROVIDER_add_conf_parameter.html">OSSL_PROVIDER_add_conf_parameter(3)</a> to learn how to set provider configuration options programmatically.</p>

<dl>

<dt id="ml-dsa.retain_seed-OSSL_PKEY_PARAM_ML_DSA_RETAIN_SEED-UTF8-string"><code>ml-dsa.retain_seed</code> (<b>OSSL_PKEY_PARAM_ML_DSA_RETAIN_SEED</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>When set to a string representing a false boolean value (see <a href="../man3/OSSL_PROVIDER_conf_get_bool.html">OSSL_PROVIDER_conf_get_bool(3)</a>), the seed will not be retained after key generation or key import from a seed value. If the resulting key is then written to a PKCS#8 object, it will contain only the FIPS 204 <code>sk</code> key.</p>

</dd>
<dt id="ml-dsa.prefer_seed-OSSL_PKEY_PARAM_ML_DSA_PREFER_SEED-UTF8-string"><code>ml-dsa.prefer_seed</code> (<b>OSSL_PKEY_PARAM_ML_DSA_PREFER_SEED</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>When decoding PKCS#8 objects that contain both a seed and the FIPS 204 <code>sk</code> private key, the seed is by default used to regenerate the key, and the companion private key is ignored. When this configuration parameter is set to a string representing a false boolean value (see <a href="../man3/OSSL_PROVIDER_conf_get_bool.html">OSSL_PROVIDER_conf_get_bool(3)</a>), the seed is ignored (neither used to regenerate the key, nor retained), and the companion key is used instead.</p>

</dd>
<dt id="ml-dsa.input_formats-OSSL_PKEY_PARAM_ML_DSA_INPUT_FORMATS-UTF8-string"><code>ml-dsa.input_formats</code> (<b>OSSL_PKEY_PARAM_ML_DSA_INPUT_FORMATS</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>List of enabled private key input formats when parsing PKCS#8 objects. List elements are separated by commas, spaces or tabs. The list of enabled formats can be specified in the configuration file, as seen in the <a href="#EXAMPLES">&quot;EXAMPLES&quot;</a> section below, or the via the <b>-provparam</b> command-line option (see also <a href="../man3/OSSL_PROVIDER_add_conf_parameter.html">OSSL_PROVIDER_add_conf_parameter(3)</a>).</p>

<p>Values specified on the command-line override any configuration file settings. By default all the supported formats are enabled. The supported formats are:</p>

<dl>

<dt id="seed-priv"><code>seed-priv</code>:</dt>
<dd>

<p>This format represents <b>PKCS#8</b> objects in which both the FIPS 204 32-byte <b>ξ</b> seed and the secret key <b>sk</b> are present in the private key as part of the DER encoding of the ASN.1 sequence:</p>

<pre><code>ML-DSA-PrivateKey ::= CHOICE {
  seed [0] IMPLICIT OCTET STRING (SIZE (32)),
  expandedKey OCTET STRING (SIZE (2560 | 4032 | 4896)),
  both SEQUENCE {
    seed OCTET STRING (SIZE (32)),
    expandedKey OCTET STRING (SIZE (2560 | 4032 | 4896)) } }</code></pre>

<p>If the <code>seed-priv</code> format is not included in the list, this format will not be recognised on input.</p>

</dd>
<dt id="seed-only"><code>seed-only</code>:</dt>
<dd>

<p>This format represents <b>PKCS#8</b> objects in which only the 32-byte FIPS 204 <b>ξ</b> seed is present in the above sequence. If the <code>seed-only</code> format is not included in the list, this format will not be recognised on input.</p>

</dd>
<dt id="priv-only"><code>priv-only</code>:</dt>
<dd>

<p>This format represents <b>PKCS#8</b> objects in which only the FIPS 204 private key <b>sk</b> is present in the above sequence. If the <code>priv-only</code> format is not included in the list, this format will not be recognised on input.</p>

</dd>
<dt id="oqskeypair"><code>oqskeypair</code>:</dt>
<dd>

<p>This format represents <b>PKCS#8</b> objects in which the private key is a DER encoding of an octet string containing the concatenaton of the FIPS 204 private key <b>sk</b> and the public key <b>pk</b>. This encoding is used in some builds of the <code>oqsprovider</code>. If the <code>oqskeypair</code> format is not included in the list, this format will not be recognised on input.</p>

</dd>
<dt id="bare-seed"><code>bare-seed</code>:</dt>
<dd>

<p>This format represents <b>PKCS#8</b> objects in which the private key contains the 32-byte FIPS 204 seed <b>ξ</b> without any ASN.1 encapsulation. If the <code>bare-seed</code> format is not included in the list, this format will not be recognised on input.</p>

</dd>
<dt id="bare-priv"><code>bare-priv</code>:</dt>
<dd>

<p>This format represents <b>PKCS#8</b> objects in which the private key contains the FIPS 204 secret key <b>sk</b> without any ASN.1 encapsulation. If the <code>bare-priv</code> format is not included in the list, this format will not be recognised on input.</p>

</dd>
</dl>

</dd>
<dt id="ml-dsa.output_formats-OSSL_PKEY_PARAM_ML_DSA_OUTPUT_FORMATS-UTF8-string"><code>ml-dsa.output_formats</code> (<b>OSSL_PKEY_PARAM_ML_DSA_OUTPUT_FORMATS</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>Ordered list of enabled private key output formats when writing <b>PKCS#8</b> files. List elements are separated by commas, spaces or tabs. The list of enabled formats can be specified in the configuration file, as seen in the <a href="#EXAMPLES">&quot;EXAMPLES&quot;</a> section below, or the via the <b>-provparam</b> command-line option.</p>

<p>This supports the same set of formats as described under <code>ml-dsa.input_formats</code> above. The order in which elements are listed is important, the selected format will be the first one that is possible to output. If the key seed is known, the first listed format will be selected. If the key seed is not known, the first format that omits the seed will be selected. The default order is equivalent to <code>seed-priv</code> first and <code>priv-only</code> second, with both seed and key output when the seed is available, and just the key otherwise. If <code>seed-only</code> is listed first, then the seed will be output without the key when available, otherwise the output will have just the key. If <code>priv-only</code> is listed first, then just the key is output regardless of whether the seed is present. The legacy <code>oqskeypair</code>, <code>bare-seed</code> and <code>bare-priv</code> formats can also be output, by listing those first.</p>

</dd>
</dl>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<dl>

<dt id="FIPS-204">FIPS 204</dt>
<dd>

</dd>
</dl>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>An <b>EVP_PKEY</b> context can be obtained by calling:</p>

<pre><code>EVP_PKEY_CTX *pctx =
    EVP_PKEY_CTX_new_from_name(NULL, &quot;ML-DSA-44&quot;, NULL);</code></pre>

<p>An <b>ML-DSA-44</b> key can be generated like this:</p>

<pre><code>pkey = EVP_PKEY_Q_keygen(NULL, NULL, &quot;ML-DSA-44&quot;);</code></pre>

<p>The key pair components can be extracted from a key by calling:</p>

<pre><code>/* Sizes large enough for ML-DSA-87 */
uint8_t pub[2592], priv[4896], seed[32]:
size_t priv_len, pub_len, seed_len;

EVP_PKEY_get_octet_string_param(pkey, OSSL_PKEY_PARAM_ML_DSA_SEED,
                                seed, sizeof(seed), &amp;seed_len);
EVP_PKEY_get_octet_string_param(pkey, OSSL_PKEY_PARAM_PRIV_KEY,
                                priv, sizeof(priv), &amp;priv_len);
EVP_PKEY_get_octet_string_param(pkey, OSSL_PKEY_PARAM_PUB_KEY,
                                pub, sizeof(pub), &amp;pub_len));</code></pre>

<p>An <b>ML-DSA</b> private key in seed format can be converted to a key in the FIPS 204 <b>sk</b> format by running:</p>

<pre><code>$ openssl pkey -provparam ml-dsa.retain_seed=no \
    -in seed-only.pem -out priv-only.pem</code></pre>

<p>To generate an, e.g., <b>ML-DSA-65</b> key, in FIPS 204 <b>sk</b> format, you can run:</p>

<pre><code>$ openssl genpkey -provparam ml-dsa.retain_seed=no \
    -algorithm ml-dsa-65 -out priv-only.pem</code></pre>

<p>If you have a <b>PKCS#8</b> file with both a seed and a key, and prefer to import the companion key rather than the seed, you can run:</p>

<pre><code>$ openssl pkey -provparam ml-dsa.prefer_seed=no \
    -in seed-priv.pem -out priv-only.pem</code></pre>

<p>In the <b>openssl.cnf</b> file, this looks like:</p>

<pre><code>openssl_conf = openssl_init

[openssl_init]
providers = providers_sect

# Can be referenced in one or more provider sections
[ml_dsa_sect]
prefer_seed = yes
retain_seed = yes
# OQS legacy formats disabled
input_formats = seed-priv, seed-only, priv-only
# Output either the seed alone, or else the key alone
output_formats = seed-only, priv-only

[providers_sect]
default = default_sect
# Or perhaps just: base = default_sect
base = base_sect

[default_sect]
ml-dsa = ml_dsa_sect

[base_sect]
ml-dsa = ml_dsa_sect</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_KEYMGMT.html">EVP_KEYMGMT(3)</a>, <a href="../man3/EVP_PKEY.html">EVP_PKEY(3)</a>, <a href="../man7/provider-keymgmt.html">provider-keymgmt(7)</a>, <a href="../man3/EVP_PKEY_get_raw_private_key.html">EVP_PKEY_get_raw_private_key(3)</a>, <a href="../man3/EVP_PKEY_get_raw_public_key.html">EVP_PKEY_get_raw_public_key(3)</a>, <a href="../man3/EVP_PKEY_get1_encoded_public_key.html">EVP_PKEY_get1_encoded_public_key(3)</a>, <a href="../man3/OSSL_PROVIDER_add_conf_parameter.html">OSSL_PROVIDER_add_conf_parameter(3)</a>, <a href="../man7/provider-keymgmt.html">provider-keymgmt(7)</a>, <a href="../man7/EVP_SIGNATURE-ML-DSA.html">EVP_SIGNATURE-ML-DSA(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>This functionality was added in OpenSSL 3.5.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2025 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


