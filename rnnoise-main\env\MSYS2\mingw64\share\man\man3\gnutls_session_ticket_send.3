.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_ticket_send" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_ticket_send \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_session_ticket_send(gnutls_session_t " session ", unsigned " nr ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned nr" 12
the number of tickets to send
.IP "unsigned flags" 12
must be zero
.SH "DESCRIPTION"
Sends a fresh session ticket to the peer. This is relevant only
in server side under TLS1.3. This function may also return \fBGNUTLS_E_AGAIN\fP
or \fBGNUTLS_E_INTERRUPTED\fP and in that case it must be called again.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, or a negative error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
