# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.



# The name this table is to be known by, with the format of the mappings in
# the main body of the table, and what all code points missing from this file
# map to.
$Unicode::UCD::SwashInfo{'ToNFDQC'}{'format'} = 's'; # string
$Unicode::UCD::SwashInfo{'ToNFDQC'}{'missing'} = 'Yes';

return <<'END';
C0	C5	N
C7	CF	N
D1	D6	N
D9	DD	N
E0	E5	N
E7	EF	N
F1	F6	N
F9	FD	N
FF	10F	N
112	125	N
128	130	N
134	137	N
139	13E	N
143	148	N
14C	151	N
154	165	N
168	17E	N
1A0	1A1	N
1AF	1B0	N
1CD	1DC	N
1DE	1E3	N
1E6	1F0	N
1F4	1F5	N
1F8	21B	N
21E	21F	N
226	233	N
340	341	N
343	344	N
374		N
37E		N
385	38A	N
38C		N
38E	390	N
3AA	3B0	N
3CA	3CE	N
3D3	3D4	N
400	401	N
403		N
407		N
40C	40E	N
419		N
439		N
450	451	N
453		N
457		N
45C	45E	N
476	477	N
4C1	4C2	N
4D0	4D3	N
4D6	4D7	N
4DA	4DF	N
4E2	4E7	N
4EA	4F5	N
4F8	4F9	N
622	626	N
6C0		N
6C2		N
6D3		N
929		N
931		N
934		N
958	95F	N
9CB	9CC	N
9DC	9DD	N
9DF		N
A33		N
A36		N
A59	A5B	N
A5E		N
B48		N
B4B	B4C	N
B5C	B5D	N
B94		N
BCA	BCC	N
C48		N
CC0		N
CC7	CC8	N
CCA	CCB	N
D4A	D4C	N
DDA		N
DDC	DDE	N
F43		N
F4D		N
F52		N
F57		N
F5C		N
F69		N
F73		N
F75	F76	N
F78		N
F81		N
F93		N
F9D		N
FA2		N
FA7		N
FAC		N
FB9		N
1026		N
1B06		N
1B08		N
1B0A		N
1B0C		N
1B0E		N
1B12		N
1B3B		N
1B3D		N
1B40	1B41	N
1B43		N
1E00	1E99	N
1E9B		N
1EA0	1EF9	N
1F00	1F15	N
1F18	1F1D	N
1F20	1F45	N
1F48	1F4D	N
1F50	1F57	N
1F59		N
1F5B		N
1F5D		N
1F5F	1F7D	N
1F80	1FB4	N
1FB6	1FBC	N
1FBE		N
1FC1	1FC4	N
1FC6	1FD3	N
1FD6	1FDB	N
1FDD	1FEF	N
1FF2	1FF4	N
1FF6	1FFD	N
2000	2001	N
2126		N
212A	212B	N
219A	219B	N
21AE		N
21CD	21CF	N
2204		N
2209		N
220C		N
2224		N
2226		N
2241		N
2244		N
2247		N
2249		N
2260		N
2262		N
226D	2271	N
2274	2275	N
2278	2279	N
2280	2281	N
2284	2285	N
2288	2289	N
22AC	22AF	N
22E0	22E3	N
22EA	22ED	N
2329	232A	N
2ADC		N
304C		N
304E		N
3050		N
3052		N
3054		N
3056		N
3058		N
305A		N
305C		N
305E		N
3060		N
3062		N
3065		N
3067		N
3069		N
3070	3071	N
3073	3074	N
3076	3077	N
3079	307A	N
307C	307D	N
3094		N
309E		N
30AC		N
30AE		N
30B0		N
30B2		N
30B4		N
30B6		N
30B8		N
30BA		N
30BC		N
30BE		N
30C0		N
30C2		N
30C5		N
30C7		N
30C9		N
30D0	30D1	N
30D3	30D4	N
30D6	30D7	N
30D9	30DA	N
30DC	30DD	N
30F4		N
30F7	30FA	N
30FE		N
AC00	D7A3	N
F900	FA0D	N
FA10		N
FA12		N
FA15	FA1E	N
FA20		N
FA22		N
FA25	FA26	N
FA2A	FA6D	N
FA70	FAD9	N
FB1D		N
FB1F		N
FB2A	FB36	N
FB38	FB3C	N
FB3E		N
FB40	FB41	N
FB43	FB44	N
FB46	FB4E	N
1109A		N
1109C		N
110AB		N
1112E	1112F	N
1134B	1134C	N
114BB	114BC	N
114BE		N
115BA	115BB	N
11938		N
1D15E	1D164	N
1D1BB	1D1C0	N
2F800	2FA1D	N
END
