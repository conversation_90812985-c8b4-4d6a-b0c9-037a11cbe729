# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /ssd/gaoyongyu/workspace/rnn_gao_new

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /ssd/gaoyongyu/workspace/rnn_gao_new

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(CMAKE_COMMAND) -E cmake_progress_start /ssd/gaoyongyu/workspace/rnn_gao_new/CMakeFiles /ssd/gaoyongyu/workspace/rnn_gao_new/src/CMakeFiles/progress.marks
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f CMakeFiles/Makefile2 src/all
	$(CMAKE_COMMAND) -E cmake_progress_start /ssd/gaoyongyu/workspace/rnn_gao_new/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f CMakeFiles/Makefile2 src/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f CMakeFiles/Makefile2 src/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f CMakeFiles/Makefile2 src/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
src/CMakeFiles/rnnLib.dir/rule:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f CMakeFiles/Makefile2 src/CMakeFiles/rnnLib.dir/rule
.PHONY : src/CMakeFiles/rnnLib.dir/rule

# Convenience name for target.
rnnLib: src/CMakeFiles/rnnLib.dir/rule

.PHONY : rnnLib

# fast build rule for target.
rnnLib/fast:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/build
.PHONY : rnnLib/fast

celt_lpc.o: celt_lpc.c.o

.PHONY : celt_lpc.o

# target to build an object file
celt_lpc.c.o:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/celt_lpc.c.o
.PHONY : celt_lpc.c.o

celt_lpc.i: celt_lpc.c.i

.PHONY : celt_lpc.i

# target to preprocess a source file
celt_lpc.c.i:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/celt_lpc.c.i
.PHONY : celt_lpc.c.i

celt_lpc.s: celt_lpc.c.s

.PHONY : celt_lpc.s

# target to generate assembly for a file
celt_lpc.c.s:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/celt_lpc.c.s
.PHONY : celt_lpc.c.s

denoise.o: denoise.c.o

.PHONY : denoise.o

# target to build an object file
denoise.c.o:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/denoise.c.o
.PHONY : denoise.c.o

denoise.i: denoise.c.i

.PHONY : denoise.i

# target to preprocess a source file
denoise.c.i:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/denoise.c.i
.PHONY : denoise.c.i

denoise.s: denoise.c.s

.PHONY : denoise.s

# target to generate assembly for a file
denoise.c.s:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/denoise.c.s
.PHONY : denoise.c.s

denoise16.o: denoise16.c.o

.PHONY : denoise16.o

# target to build an object file
denoise16.c.o:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/denoise16.c.o
.PHONY : denoise16.c.o

denoise16.i: denoise16.c.i

.PHONY : denoise16.i

# target to preprocess a source file
denoise16.c.i:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/denoise16.c.i
.PHONY : denoise16.c.i

denoise16.s: denoise16.c.s

.PHONY : denoise16.s

# target to generate assembly for a file
denoise16.c.s:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/denoise16.c.s
.PHONY : denoise16.c.s

kiss_fft.o: kiss_fft.c.o

.PHONY : kiss_fft.o

# target to build an object file
kiss_fft.c.o:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/kiss_fft.c.o
.PHONY : kiss_fft.c.o

kiss_fft.i: kiss_fft.c.i

.PHONY : kiss_fft.i

# target to preprocess a source file
kiss_fft.c.i:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/kiss_fft.c.i
.PHONY : kiss_fft.c.i

kiss_fft.s: kiss_fft.c.s

.PHONY : kiss_fft.s

# target to generate assembly for a file
kiss_fft.c.s:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/kiss_fft.c.s
.PHONY : kiss_fft.c.s

pitch.o: pitch.c.o

.PHONY : pitch.o

# target to build an object file
pitch.c.o:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/pitch.c.o
.PHONY : pitch.c.o

pitch.i: pitch.c.i

.PHONY : pitch.i

# target to preprocess a source file
pitch.c.i:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/pitch.c.i
.PHONY : pitch.c.i

pitch.s: pitch.c.s

.PHONY : pitch.s

# target to generate assembly for a file
pitch.c.s:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/pitch.c.s
.PHONY : pitch.c.s

rnn.o: rnn.c.o

.PHONY : rnn.o

# target to build an object file
rnn.c.o:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/rnn.c.o
.PHONY : rnn.c.o

rnn.i: rnn.c.i

.PHONY : rnn.i

# target to preprocess a source file
rnn.c.i:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/rnn.c.i
.PHONY : rnn.c.i

rnn.s: rnn.c.s

.PHONY : rnn.s

# target to generate assembly for a file
rnn.c.s:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/rnn.c.s
.PHONY : rnn.c.s

rnn_data.o: rnn_data.c.o

.PHONY : rnn_data.o

# target to build an object file
rnn_data.c.o:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/rnn_data.c.o
.PHONY : rnn_data.c.o

rnn_data.i: rnn_data.c.i

.PHONY : rnn_data.i

# target to preprocess a source file
rnn_data.c.i:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/rnn_data.c.i
.PHONY : rnn_data.c.i

rnn_data.s: rnn_data.c.s

.PHONY : rnn_data.s

# target to generate assembly for a file
rnn_data.c.s:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(MAKE) -f src/CMakeFiles/rnnLib.dir/build.make src/CMakeFiles/rnnLib.dir/rnn_data.c.s
.PHONY : rnn_data.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... rnnLib"
	@echo "... celt_lpc.o"
	@echo "... celt_lpc.i"
	@echo "... celt_lpc.s"
	@echo "... denoise.o"
	@echo "... denoise.i"
	@echo "... denoise.s"
	@echo "... denoise16.o"
	@echo "... denoise16.i"
	@echo "... denoise16.s"
	@echo "... kiss_fft.o"
	@echo "... kiss_fft.i"
	@echo "... kiss_fft.s"
	@echo "... pitch.o"
	@echo "... pitch.i"
	@echo "... pitch.s"
	@echo "... rnn.o"
	@echo "... rnn.i"
	@echo "... rnn.s"
	@echo "... rnn_data.o"
	@echo "... rnn_data.i"
	@echo "... rnn_data.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /ssd/gaoyongyu/workspace/rnn_gao_new && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

