_uuidd_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-p'|'--pid'|'-s'|'--socket')
			local IFS=$'\n'
			compopt -o filenames
			COMPREPLY=( $(compgen -f -- $cur) )
			return 0
			;;
		'-T'|'--timeout')
			local IFS=$'\n'
			compopt -o filenames
			COMPREPLY=( $(compgen -W "timeout" -- $cur) )
			return 0
			;;
		'-n'|'--uuids')
			local IFS=$'\n'
			compopt -o filenames
			COMPREPLY=( $(compgen -W "number" -- $cur) )
			return 0
			;;
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="--pid --socket --timeout --kill --random --time --uuids --no-pid --no-fork --socket-activation --debug --quiet --version --help"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	return 0
}
complete -F _uuidd_module uuidd
