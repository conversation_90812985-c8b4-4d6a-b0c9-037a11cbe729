.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_global_deinit" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_global_deinit \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_global_deinit( " void ");"
.SH ARGUMENTS
.IP " void" 12
.SH "DESCRIPTION"

This function deinitializes the global data, that were initialized
using \fBgnutls_global_init()\fP.

Since GnuTLS 3.3.0 this function is no longer necessary to be explicitly
called. GnuTLS will automatically deinitialize on library destructor. See
\fBgnutls_global_init()\fP for disabling the implicit initialization/deinitialization.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
