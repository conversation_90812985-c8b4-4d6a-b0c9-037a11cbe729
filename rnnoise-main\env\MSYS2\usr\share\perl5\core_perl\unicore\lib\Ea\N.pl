# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V563
0
32
127
161
169
170
171
172
181
182
187
188
192
198
199
208
209
215
217
222
226
230
231
232
235
236
238
240
241
242
244
247
251
252
253
254
255
257
258
273
274
275
276
283
284
294
296
299
300
305
308
312
313
319
323
324
325
328
332
333
334
338
340
358
360
363
364
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
593
594
609
610
708
709
711
712
713
716
717
718
720
721
728
732
733
734
735
736
768
880
913
930
931
938
945
962
963
970
1025
1026
1040
1104
1105
1106
4352
4448
8208
8209
8211
8215
8216
8218
8220
8222
8224
8227
8228
8232
8240
8241
8242
8244
8245
8246
8251
8252
8254
8255
8308
8309
8319
8320
8321
8325
8361
8362
8364
8365
8451
8452
8453
8454
8457
8458
8467
8468
8470
8471
8481
8483
8486
8487
8491
8492
8531
8533
8539
8543
8544
8556
8560
8570
8585
8586
8592
8602
8632
8634
8658
8659
8660
8661
8679
8680
8704
8705
8706
8708
8711
8713
8715
8716
8719
8720
8721
8722
8725
8726
8730
8731
8733
8737
8739
8740
8741
8742
8743
8749
8750
8751
8756
8760
8764
8766
8776
8777
8780
8781
8786
8787
8800
8802
8804
8808
8810
8812
8814
8816
8834
8836
8838
8840
8853
8854
8857
8858
8869
8870
8895
8896
8978
8979
8986
8988
9001
9003
9193
9197
9200
9201
9203
9204
9312
9450
9451
9548
9552
9588
9600
9616
9618
9622
9632
9634
9635
9642
9650
9652
9654
9656
9660
9662
9664
9666
9670
9673
9675
9676
9678
9682
9698
9702
9711
9712
9725
9727
9733
9735
9737
9738
9742
9744
9748
9750
9756
9757
9758
9759
9792
9793
9794
9795
9800
9812
9824
9826
9827
9830
9831
9835
9836
9838
9839
9840
9855
9856
9875
9876
9886
9888
9889
9890
9898
9900
9917
9920
9924
9954
9955
9956
9960
9984
9989
9990
9994
9996
10024
10025
10045
10046
10060
10061
10062
10063
10067
10070
10071
10072
10102
10112
10133
10136
10160
10161
10175
10176
10214
10222
10629
10631
11035
11037
11088
11089
11093
11098
11904
11930
11931
12020
12032
12246
12272
12284
12288
12351
12353
12439
12441
12544
12549
12592
12593
12687
12688
12772
12784
12831
12832
19904
19968
42125
42128
42183
43360
43389
44032
55204
57344
64256
65024
65050
65072
65107
65108
65127
65128
65132
65281
65471
65474
65480
65482
65488
65490
65496
65498
65501
65504
65511
65512
65519
65533
65534
94176
94181
94192
94194
94208
100344
100352
101590
101632
101641
110576
110580
110581
110588
110589
110591
110592
110883
110898
110899
110928
110931
110933
110934
110948
110952
110960
111356
126980
126981
127183
127184
127232
127243
127248
127278
127280
127338
127344
127405
127488
127491
127504
127548
127552
127561
127568
127570
127584
127590
127744
127777
127789
127798
127799
127869
127870
127892
127904
127947
127951
127956
127968
127985
127988
127989
127992
128063
128064
128065
128066
128253
128255
128318
128331
128335
128336
128360
128378
128379
128405
128407
128420
128421
128507
128592
128640
128710
128716
128717
128720
128723
128725
128728
128732
128736
128747
128749
128756
128765
128992
129004
129008
129009
129292
129339
129340
129350
129351
129536
129648
129661
129664
129673
129680
129726
129727
129734
129742
129756
129760
129769
129776
129785
131072
196606
196608
262142
917760
918000
983040
1048574
1048576
1114110
END
