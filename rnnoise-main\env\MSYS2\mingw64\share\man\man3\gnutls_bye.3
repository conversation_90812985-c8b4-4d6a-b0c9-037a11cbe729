.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_bye" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_bye \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_bye(gnutls_session_t " session ", gnutls_close_request_t " how ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_close_request_t how" 12
is an integer
.SH "DESCRIPTION"
Terminates the current TLS/SSL connection. The connection should
have been initiated using \fBgnutls_handshake()\fP.   \fIhow\fP should be one
of \fBGNUTLS_SHUT_RDWR\fP, \fBGNUTLS_SHUT_WR\fP.

In case of \fBGNUTLS_SHUT_RDWR\fP the TLS session gets
terminated and further receives and sends will be disallowed.  If
the return value is zero you may continue using the underlying
transport layer. \fBGNUTLS_SHUT_RDWR\fP sends an alert containing a close
request and waits for the peer to reply with the same message.

In case of \fBGNUTLS_SHUT_WR\fP the TLS session gets terminated
and further sends will be disallowed. In order to reuse the
connection you should wait for an EOF from the peer.
\fBGNUTLS_SHUT_WR\fP sends an alert containing a close request.

Note that not all implementations will properly terminate a TLS
connection.  Some of them, usually for performance reasons, will
terminate only the underlying transport layer, and thus not
distinguishing between a malicious party prematurely terminating
the connection and normal termination.

This function may also return \fBGNUTLS_E_AGAIN\fP or
\fBGNUTLS_E_INTERRUPTED\fP; cf.  \fBgnutls_record_get_direction()\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, or an error code, see
function documentation for entire semantics.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
