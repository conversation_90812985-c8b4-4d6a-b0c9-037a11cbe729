// <cinttypes> -*- C++ -*-

// Copyright (C) 2007-2025 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file include/cinttypes
 *  This is a Standard C++ Library header.
 */

#ifndef _GLIBCXX_CINTTYPES
#define _GLIBCXX_CINTTYPES 1

#ifdef _GLIBCXX_SYSHDR
#pragma GCC system_header
#endif

#if __cplusplus < 201103L
# include <bits/c++0x_warning.h>
#else

#include <cstdint>

// For 27.9.2/3 (see C99, Note 184)
#if _GLIBCXX_HAVE_INTTYPES_H
# ifndef __STDC_FORMAT_MACROS
#  define _UNDEF__STDC_FORMAT_MACROS
#  define __STDC_FORMAT_MACROS
# endif
# include <inttypes.h>
# ifdef _UNDEF__STDC_FORMAT_MACROS
#  undef __STDC_FORMAT_MACROS
#  undef _UNDEF__STDC_FORMAT_MACROS
# endif
#endif

#ifdef _GLIBCXX_USE_C99_INTTYPES

namespace std
{
  // types
  using ::imaxdiv_t;

  // functions
  using ::imaxabs;
  using ::imaxdiv;

  // GCC does not support extended integer types
  // intmax_t abs(intmax_t)
  // imaxdiv_t div(intmax_t, intmax_t)

  using ::strtoimax;
  using ::strtoumax;

#if defined(_GLIBCXX_USE_WCHAR_T) && _GLIBCXX_USE_C99_INTTYPES_WCHAR_T
  using ::wcstoimax;
  using ::wcstoumax;
#endif
} // namespace std

#endif // _GLIBCXX_USE_C99_INTTYPES

#endif // C++11

#endif // _GLIBCXX_CINTTYPES
