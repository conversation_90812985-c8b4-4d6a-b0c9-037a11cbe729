<?xml version="1.0"?>
<!DOCTYPE syscalls_info SYSTEM "gdb-syscalls.dtd">
<!-- Copyright (C) 2010-2024 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->
<!-- This file was generated using the following file:

     arch/sparc/kernel/syscalls/syscall.tbl

     The file mentioned above belongs to the Linux Kernel.  -->
<syscalls_info>
  <syscall name="restart_syscall" number="0"/>
  <syscall name="exit" number="1" groups="process"/>
  <syscall name="fork" number="2" groups="process"/>
  <syscall name="read" number="3" groups="descriptor"/>
  <syscall name="write" number="4" groups="descriptor"/>
  <syscall name="open" number="5" groups="descriptor,file"/>
  <syscall name="close" number="6" groups="descriptor"/>
  <syscall name="wait4" number="7" groups="process"/>
  <syscall name="creat" number="8" groups="descriptor,file"/>
  <syscall name="link" number="9" groups="file"/>
  <syscall name="unlink" number="10" groups="file"/>
  <syscall name="execv" number="11" groups="file,process"/>
  <syscall name="chdir" number="12" groups="file"/>
  <syscall name="chown" number="13" groups="file"/>
  <syscall name="mknod" number="14" groups="file"/>
  <syscall name="chmod" number="15" groups="file"/>
  <syscall name="lchown" number="16" groups="file"/>
  <syscall name="brk" number="17" groups="memory"/>
  <syscall name="perfctr" number="18"/>
  <syscall name="lseek" number="19" groups="descriptor"/>
  <syscall name="getpid" number="20"/>
  <syscall name="capget" number="21"/>
  <syscall name="capset" number="22"/>
  <syscall name="setuid" number="23"/>
  <syscall name="getuid" number="24"/>
  <syscall name="vmsplice" number="25" groups="descriptor"/>
  <syscall name="ptrace" number="26"/>
  <syscall name="alarm" number="27"/>
  <syscall name="sigaltstack" number="28" groups="signal"/>
  <syscall name="pause" number="29" groups="signal"/>
  <syscall name="utime" number="30" groups="file"/>
  <syscall name="lchown32" number="31" groups="file"/>
  <syscall name="fchown32" number="32" groups="descriptor"/>
  <syscall name="access" number="33" groups="file"/>
  <syscall name="nice" number="34"/>
  <syscall name="chown32" number="35" groups="file"/>
  <syscall name="sync" number="36"/>
  <syscall name="kill" number="37" groups="signal,process"/>
  <syscall name="stat" number="38" groups="file"/>
  <syscall name="sendfile" number="39" groups="descriptor,network"/>
  <syscall name="lstat" number="40" groups="file"/>
  <syscall name="dup" number="41" groups="descriptor"/>
  <syscall name="pipe" number="42" groups="descriptor"/>
  <syscall name="times" number="43"/>
  <syscall name="getuid32" number="44"/>
  <syscall name="umount2" number="45" groups="file"/>
  <syscall name="setgid" number="46"/>
  <syscall name="getgid" number="47"/>
  <syscall name="signal" number="48" groups="signal"/>
  <syscall name="geteuid" number="49"/>
  <syscall name="getegid" number="50"/>
  <syscall name="acct" number="51" groups="file"/>
  <syscall name="getgid32" number="53"/>
  <syscall name="ioctl" number="54" groups="descriptor"/>
  <syscall name="reboot" number="55"/>
  <syscall name="mmap2" number="56" groups="descriptor,memory"/>
  <syscall name="symlink" number="57" groups="file"/>
  <syscall name="readlink" number="58" groups="file"/>
  <syscall name="execve" number="59" groups="file,process"/>
  <syscall name="umask" number="60"/>
  <syscall name="chroot" number="61" groups="file"/>
  <syscall name="fstat" number="62" groups="descriptor"/>
  <syscall name="fstat64" number="63" groups="descriptor"/>
  <syscall name="getpagesize" number="64"/>
  <syscall name="msync" number="65" groups="memory"/>
  <syscall name="vfork" number="66" groups="process"/>
  <syscall name="pread64" number="67" groups="descriptor"/>
  <syscall name="pwrite64" number="68" groups="descriptor"/>
  <syscall name="geteuid32" number="69"/>
  <syscall name="getegid32" number="70"/>
  <syscall name="mmap" number="71" groups="descriptor,memory"/>
  <syscall name="setreuid32" number="72"/>
  <syscall name="munmap" number="73" groups="memory"/>
  <syscall name="mprotect" number="74" groups="memory"/>
  <syscall name="madvise" number="75" groups="memory"/>
  <syscall name="vhangup" number="76"/>
  <syscall name="truncate64" number="77" groups="file"/>
  <syscall name="mincore" number="78" groups="memory"/>
  <syscall name="getgroups" number="79"/>
  <syscall name="setgroups" number="80"/>
  <syscall name="getpgrp" number="81"/>
  <syscall name="setgroups32" number="82"/>
  <syscall name="setitimer" number="83"/>
  <syscall name="ftruncate64" number="84" groups="descriptor"/>
  <syscall name="swapon" number="85" groups="file"/>
  <syscall name="getitimer" number="86"/>
  <syscall name="setuid32" number="87"/>
  <syscall name="sethostname" number="88"/>
  <syscall name="setgid32" number="89"/>
  <syscall name="dup2" number="90" groups="descriptor"/>
  <syscall name="setfsuid32" number="91"/>
  <syscall name="fcntl" number="92" groups="descriptor"/>
  <syscall name="select" number="93" groups="descriptor"/>
  <syscall name="setfsgid32" number="94"/>
  <syscall name="fsync" number="95" groups="descriptor"/>
  <syscall name="setpriority" number="96"/>
  <syscall name="socket" number="97" groups="network"/>
  <syscall name="connect" number="98" groups="network"/>
  <syscall name="accept" number="99" groups="network"/>
  <syscall name="getpriority" number="100"/>
  <syscall name="rt_sigreturn" number="101" groups="signal"/>
  <syscall name="rt_sigaction" number="102" groups="signal"/>
  <syscall name="rt_sigprocmask" number="103" groups="signal"/>
  <syscall name="rt_sigpending" number="104" groups="signal"/>
  <syscall name="rt_sigtimedwait" number="105" groups="signal"/>
  <syscall name="rt_sigqueueinfo" number="106" groups="signal,process"/>
  <syscall name="rt_sigsuspend" number="107" groups="signal"/>
  <syscall name="setresuid32" number="108"/>
  <syscall name="getresuid32" number="109"/>
  <syscall name="setresgid32" number="110"/>
  <syscall name="getresgid32" number="111"/>
  <syscall name="setregid32" number="112"/>
  <syscall name="recvmsg" number="113" groups="network"/>
  <syscall name="sendmsg" number="114" groups="network"/>
  <syscall name="getgroups32" number="115"/>
  <syscall name="gettimeofday" number="116"/>
  <syscall name="getrusage" number="117"/>
  <syscall name="getsockopt" number="118" groups="network"/>
  <syscall name="getcwd" number="119" groups="file"/>
  <syscall name="readv" number="120" groups="descriptor"/>
  <syscall name="writev" number="121" groups="descriptor"/>
  <syscall name="settimeofday" number="122"/>
  <syscall name="fchown" number="123" groups="descriptor"/>
  <syscall name="fchmod" number="124" groups="descriptor"/>
  <syscall name="recvfrom" number="125" groups="network"/>
  <syscall name="setreuid" number="126"/>
  <syscall name="setregid" number="127"/>
  <syscall name="rename" number="128" groups="file"/>
  <syscall name="truncate" number="129" groups="file"/>
  <syscall name="ftruncate" number="130" groups="descriptor"/>
  <syscall name="flock" number="131" groups="descriptor"/>
  <syscall name="lstat64" number="132" groups="file"/>
  <syscall name="sendto" number="133" groups="network"/>
  <syscall name="shutdown" number="134" groups="network"/>
  <syscall name="socketpair" number="135" groups="network"/>
  <syscall name="mkdir" number="136" groups="file"/>
  <syscall name="rmdir" number="137" groups="file"/>
  <syscall name="utimes" number="138" groups="file"/>
  <syscall name="stat64" number="139" groups="file"/>
  <syscall name="sendfile64" number="140" groups="descriptor,network"/>
  <syscall name="getpeername" number="141" groups="network"/>
  <syscall name="futex" number="142"/>
  <syscall name="gettid" number="143"/>
  <syscall name="getrlimit" number="144"/>
  <syscall name="setrlimit" number="145"/>
  <syscall name="pivot_root" number="146" groups="file"/>
  <syscall name="prctl" number="147"/>
  <syscall name="pciconfig_read" number="148"/>
  <syscall name="pciconfig_write" number="149"/>
  <syscall name="getsockname" number="150" groups="network"/>
  <syscall name="inotify_init" number="151" groups="descriptor"/>
  <syscall name="inotify_add_watch" number="152" groups="descriptor,file"/>
  <syscall name="poll" number="153" groups="descriptor"/>
  <syscall name="getdents64" number="154" groups="descriptor"/>
  <syscall name="fcntl64" number="155" groups="descriptor"/>
  <syscall name="inotify_rm_watch" number="156" groups="descriptor"/>
  <syscall name="statfs" number="157" groups="file"/>
  <syscall name="fstatfs" number="158" groups="descriptor"/>
  <syscall name="umount" number="159" groups="file"/>
  <syscall name="sched_set_affinity" number="160"/>
  <syscall name="sched_get_affinity" number="161"/>
  <syscall name="getdomainname" number="162"/>
  <syscall name="setdomainname" number="163"/>
  <syscall name="quotactl" number="165" groups="file"/>
  <syscall name="set_tid_address" number="166"/>
  <syscall name="mount" number="167" groups="file"/>
  <syscall name="ustat" number="168"/>
  <syscall name="setxattr" number="169" groups="file"/>
  <syscall name="lsetxattr" number="170" groups="file"/>
  <syscall name="fsetxattr" number="171" groups="descriptor"/>
  <syscall name="getxattr" number="172" groups="file"/>
  <syscall name="lgetxattr" number="173" groups="file"/>
  <syscall name="getdents" number="174" groups="descriptor"/>
  <syscall name="setsid" number="175"/>
  <syscall name="fchdir" number="176" groups="descriptor"/>
  <syscall name="fgetxattr" number="177" groups="descriptor"/>
  <syscall name="listxattr" number="178" groups="file"/>
  <syscall name="llistxattr" number="179" groups="file"/>
  <syscall name="flistxattr" number="180" groups="descriptor"/>
  <syscall name="removexattr" number="181" groups="file"/>
  <syscall name="lremovexattr" number="182" groups="file"/>
  <syscall name="sigpending" number="183" groups="signal"/>
  <syscall name="query_module" number="184"/>
  <syscall name="setpgid" number="185"/>
  <syscall name="fremovexattr" number="186" groups="descriptor"/>
  <syscall name="tkill" number="187" groups="signal,process"/>
  <syscall name="exit_group" number="188" groups="process"/>
  <syscall name="uname" number="189"/>
  <syscall name="init_module" number="190"/>
  <syscall name="personality" number="191"/>
  <syscall name="remap_file_pages" number="192" groups="memory"/>
  <syscall name="epoll_create" number="193" groups="descriptor"/>
  <syscall name="epoll_ctl" number="194" groups="descriptor"/>
  <syscall name="epoll_wait" number="195" groups="descriptor"/>
  <syscall name="ioprio_set" number="196"/>
  <syscall name="getppid" number="197"/>
  <syscall name="sigaction" number="198" groups="signal"/>
  <syscall name="sgetmask" number="199" groups="signal"/>
  <syscall name="ssetmask" number="200" groups="signal"/>
  <syscall name="sigsuspend" number="201" groups="signal"/>
  <syscall name="oldlstat" number="202" groups="file"/>
  <syscall name="uselib" number="203" groups="file"/>
  <syscall name="readdir" number="204" groups="descriptor"/>
  <syscall name="readahead" number="205" groups="descriptor"/>
  <syscall name="socketcall" number="206" groups="descriptor"/>
  <syscall name="syslog" number="207"/>
  <syscall name="lookup_dcookie" number="208"/>
  <syscall name="fadvise64" number="209" groups="descriptor"/>
  <syscall name="fadvise64_64" number="210" groups="descriptor"/>
  <syscall name="tgkill" number="211" groups="signal,process"/>
  <syscall name="waitpid" number="212" groups="process"/>
  <syscall name="swapoff" number="213" groups="file"/>
  <syscall name="sysinfo" number="214"/>
  <syscall name="ipc" number="215" groups="ipc"/>
  <syscall name="sigreturn" number="216" groups="signal"/>
  <syscall name="clone" number="217" groups="process"/>
  <syscall name="ioprio_get" number="218"/>
  <syscall name="adjtimex" number="219"/>
  <syscall name="sigprocmask" number="220" groups="signal"/>
  <syscall name="create_module" number="221"/>
  <syscall name="delete_module" number="222"/>
  <syscall name="get_kernel_syms" number="223"/>
  <syscall name="getpgid" number="224"/>
  <syscall name="bdflush" number="225"/>
  <syscall name="sysfs" number="226"/>
  <syscall name="afs_syscall" number="227"/>
  <syscall name="setfsuid" number="228"/>
  <syscall name="setfsgid" number="229"/>
  <syscall name="_newselect" number="230" groups="descriptor"/>
  <syscall name="time" number="231"/>
  <syscall name="splice" number="232" groups="descriptor"/>
  <syscall name="stime" number="233"/>
  <syscall name="statfs64" number="234" groups="file"/>
  <syscall name="fstatfs64" number="235" groups="descriptor"/>
  <syscall name="_llseek" number="236" groups="descriptor"/>
  <syscall name="mlock" number="237" groups="memory"/>
  <syscall name="munlock" number="238" groups="memory"/>
  <syscall name="mlockall" number="239" groups="memory"/>
  <syscall name="munlockall" number="240" groups="memory"/>
  <syscall name="sched_setparam" number="241"/>
  <syscall name="sched_getparam" number="242"/>
  <syscall name="sched_setscheduler" number="243"/>
  <syscall name="sched_getscheduler" number="244"/>
  <syscall name="sched_yield" number="245"/>
  <syscall name="sched_get_priority_max" number="246"/>
  <syscall name="sched_get_priority_min" number="247"/>
  <syscall name="sched_rr_get_interval" number="248"/>
  <syscall name="nanosleep" number="249"/>
  <syscall name="mremap" number="250" groups="memory"/>
  <syscall name="_sysctl" number="251"/>
  <syscall name="getsid" number="252"/>
  <syscall name="fdatasync" number="253" groups="descriptor"/>
  <syscall name="nfsservctl" number="254"/>
  <syscall name="sync_file_range" number="255" groups="descriptor"/>
  <syscall name="clock_settime" number="256"/>
  <syscall name="clock_gettime" number="257"/>
  <syscall name="clock_getres" number="258"/>
  <syscall name="clock_nanosleep" number="259"/>
  <syscall name="sched_getaffinity" number="260"/>
  <syscall name="sched_setaffinity" number="261"/>
  <syscall name="timer_settime" number="262"/>
  <syscall name="timer_gettime" number="263"/>
  <syscall name="timer_getoverrun" number="264"/>
  <syscall name="timer_delete" number="265"/>
  <syscall name="timer_create" number="266"/>
  <syscall name="vserver" number="267"/>
  <syscall name="io_setup" number="268" groups="memory"/>
  <syscall name="io_destroy" number="269" groups="memory"/>
  <syscall name="io_submit" number="270"/>
  <syscall name="io_cancel" number="271"/>
  <syscall name="io_getevents" number="272"/>
  <syscall name="mq_open" number="273" groups="descriptor"/>
  <syscall name="mq_unlink" number="274"/>
  <syscall name="mq_timedsend" number="275" groups="descriptor"/>
  <syscall name="mq_timedreceive" number="276" groups="descriptor"/>
  <syscall name="mq_notify" number="277" groups="descriptor"/>
  <syscall name="mq_getsetattr" number="278" groups="descriptor"/>
  <syscall name="waitid" number="279" groups="process"/>
  <syscall name="tee" number="280" groups="descriptor"/>
  <syscall name="add_key" number="281"/>
  <syscall name="request_key" number="282"/>
  <syscall name="keyctl" number="283"/>
  <syscall name="openat" number="284" groups="descriptor,file"/>
  <syscall name="mkdirat" number="285" groups="descriptor,file"/>
  <syscall name="mknodat" number="286" groups="descriptor,file"/>
  <syscall name="fchownat" number="287" groups="descriptor,file"/>
  <syscall name="futimesat" number="288" groups="descriptor,file"/>
  <syscall name="fstatat64" number="289" groups="descriptor,file"/>
  <syscall name="unlinkat" number="290" groups="descriptor,file"/>
  <syscall name="renameat" number="291" groups="descriptor,file"/>
  <syscall name="linkat" number="292" groups="descriptor,file"/>
  <syscall name="symlinkat" number="293" groups="descriptor,file"/>
  <syscall name="readlinkat" number="294" groups="descriptor,file"/>
  <syscall name="fchmodat" number="295" groups="descriptor,file"/>
  <syscall name="faccessat" number="296" groups="descriptor,file"/>
  <syscall name="pselect6" number="297" groups="descriptor"/>
  <syscall name="ppoll" number="298" groups="descriptor"/>
  <syscall name="unshare" number="299"/>
  <syscall name="set_robust_list" number="300"/>
  <syscall name="get_robust_list" number="301"/>
  <syscall name="migrate_pages" number="302" groups="memory"/>
  <syscall name="mbind" number="303" groups="memory"/>
  <syscall name="get_mempolicy" number="304" groups="memory"/>
  <syscall name="set_mempolicy" number="305" groups="memory"/>
  <syscall name="kexec_load" number="306"/>
  <syscall name="move_pages" number="307" groups="memory"/>
  <syscall name="getcpu" number="308"/>
  <syscall name="epoll_pwait" number="309" groups="descriptor"/>
  <syscall name="utimensat" number="310" groups="descriptor,file"/>
  <syscall name="signalfd" number="311" groups="descriptor,signal"/>
  <syscall name="timerfd_create" number="312" groups="descriptor"/>
  <syscall name="eventfd" number="313" groups="descriptor"/>
  <syscall name="fallocate" number="314" groups="descriptor"/>
  <syscall name="timerfd_settime" number="315" groups="descriptor"/>
  <syscall name="timerfd_gettime" number="316" groups="descriptor"/>
  <syscall name="signalfd4" number="317" groups="descriptor,signal"/>
  <syscall name="eventfd2" number="318" groups="descriptor"/>
  <syscall name="epoll_create1" number="319" groups="descriptor"/>
  <syscall name="dup3" number="320" groups="descriptor"/>
  <syscall name="pipe2" number="321" groups="descriptor"/>
  <syscall name="inotify_init1" number="322" groups="descriptor"/>
  <syscall name="accept4" number="323" groups="network"/>
  <syscall name="preadv" number="324" groups="descriptor"/>
  <syscall name="pwritev" number="325" groups="descriptor"/>
  <syscall name="rt_tgsigqueueinfo" number="326" groups="process,signal"/>
  <syscall name="perf_event_open" number="327" groups="descriptor"/>
  <syscall name="recvmmsg" number="328" groups="network"/>
  <syscall name="fanotify_init" number="329" groups="descriptor"/>
  <syscall name="fanotify_mark" number="330" groups="descriptor,file"/>
  <syscall name="prlimit64" number="331"/>
  <syscall name="name_to_handle_at" number="332" groups="descriptor,file"/>
  <syscall name="open_by_handle_at" number="333" groups="descriptor"/>
  <syscall name="clock_adjtime" number="334"/>
  <syscall name="syncfs" number="335" groups="descriptor"/>
  <syscall name="sendmmsg" number="336" groups="network"/>
  <syscall name="setns" number="337" groups="descriptor"/>
  <syscall name="process_vm_readv" number="338"/>
  <syscall name="process_vm_writev" number="339"/>
  <syscall name="kern_features" number="340"/>
  <syscall name="kcmp" number="341"/>
  <syscall name="finit_module" number="342" groups="descriptor"/>
  <syscall name="sched_setattr" number="343"/>
  <syscall name="sched_getattr" number="344"/>
  <syscall name="renameat2" number="345" groups="descriptor,file"/>
  <syscall name="seccomp" number="346"/>
  <syscall name="getrandom" number="347"/>
  <syscall name="memfd_create" number="348" groups="descriptor"/>
  <syscall name="bpf" number="349" groups="descriptor"/>
  <syscall name="execveat" number="350" groups="descriptor,file,process"/>
  <syscall name="membarrier" number="351"/>
  <syscall name="userfaultfd" number="352" groups="descriptor"/>
  <syscall name="bind" number="353" groups="network"/>
  <syscall name="listen" number="354" groups="network"/>
  <syscall name="setsockopt" number="355" groups="network"/>
  <syscall name="mlock2" number="356" groups="memory"/>
  <syscall name="copy_file_range" number="357" groups="descriptor"/>
  <syscall name="preadv2" number="358" groups="descriptor"/>
  <syscall name="pwritev2" number="359" groups="descriptor"/>
  <syscall name="statx" number="360" groups="descriptor,file"/>
  <syscall name="io_pgetevents" number="361"/>
  <syscall name="pkey_mprotect" number="362" groups="memory"/>
  <syscall name="pkey_alloc" number="363"/>
  <syscall name="pkey_free" number="364"/>
  <syscall name="rseq" number="365"/>
  <syscall name="semget" number="393" groups="ipc"/>
  <syscall name="semctl" number="394" groups="ipc"/>
  <syscall name="shmget" number="395" groups="ipc"/>
  <syscall name="shmctl" number="396" groups="ipc"/>
  <syscall name="shmat" number="397" groups="ipc,memory"/>
  <syscall name="shmdt" number="398" groups="ipc,memory"/>
  <syscall name="msgget" number="399" groups="ipc"/>
  <syscall name="msgsnd" number="400" groups="ipc"/>
  <syscall name="msgrcv" number="401" groups="ipc"/>
  <syscall name="msgctl" number="402" groups="ipc"/>
  <syscall name="clock_gettime64" number="403"/>
  <syscall name="clock_settime64" number="404"/>
  <syscall name="clock_adjtime64" number="405"/>
  <syscall name="clock_getres_time64" number="406"/>
  <syscall name="clock_nanosleep_time64" number="407"/>
  <syscall name="timer_gettime64" number="408"/>
  <syscall name="timer_settime64" number="409"/>
  <syscall name="timerfd_gettime64" number="410" groups="descriptor"/>
  <syscall name="timerfd_settime64" number="411" groups="descriptor"/>
  <syscall name="utimensat_time64" number="412" groups="descriptor,file"/>
  <syscall name="pselect6_time64" number="413" groups="descriptor"/>
  <syscall name="ppoll_time64" number="414" groups="descriptor"/>
  <syscall name="io_pgetevents_time64" number="416"/>
  <syscall name="recvmmsg_time64" number="417" groups="network"/>
  <syscall name="mq_timedsend_time64" number="418" groups="descriptor"/>
  <syscall name="mq_timedreceive_time64" number="419" groups="descriptor"/>
  <syscall name="semtimedop_time64" number="420" groups="ipc"/>
  <syscall name="rt_sigtimedwait_time64" number="421" groups="signal"/>
  <syscall name="futex_time64" number="422"/>
  <syscall name="sched_rr_get_interval_time64" number="423"/>
  <syscall name="pidfd_send_signal" number="424" groups="descriptor,signal,process"/>
  <syscall name="io_uring_setup" number="425" groups="descriptor"/>
  <syscall name="io_uring_enter" number="426" groups="descriptor,signal"/>
  <syscall name="io_uring_register" number="427" groups="descriptor,memory"/>
  <syscall name="open_tree" number="428" groups="descriptor,file"/>
  <syscall name="move_mount" number="429" groups="descriptor,file"/>
  <syscall name="fsopen" number="430" groups="descriptor"/>
  <syscall name="fsconfig" number="431" groups="descriptor,file"/>
  <syscall name="fsmount" number="432" groups="descriptor"/>
  <syscall name="fspick" number="433" groups="descriptor,file"/>
  <syscall name="pidfd_open" number="434" groups="descriptor"/>
  <syscall name="close_range" number="436"/>
  <syscall name="openat2" number="437" groups="descriptor,file"/>
  <syscall name="pidfd_getfd" number="438" groups="descriptor"/>
  <syscall name="faccessat2" number="439" groups="descriptor,file"/>
  <syscall name="process_madvise" number="440" groups="descriptor"/>
  <syscall name="epoll_pwait2" number="441" groups="descriptor"/>
  <syscall name="mount_setattr" number="442" groups="descriptor,file"/>
  <syscall name="quotactl_fd" number="443" groups="descriptor"/>
  <syscall name="landlock_create_ruleset" number="444" groups="descriptor"/>
  <syscall name="landlock_add_rule" number="445" groups="descriptor"/>
  <syscall name="landlock_restrict_self" number="446" groups="descriptor"/>
  <syscall name="process_mrelease" number="448" groups="descriptor"/>
  <syscall name="futex_waitv" number="449"/>
  <syscall name="set_mempolicy_home_node" number="450" groups="memory"/>
  <syscall name="cachestat" number="451" groups="descriptor"/>
  <syscall name="fchmodat2" number="452" groups="descriptor,file"/>
  <syscall name="map_shadow_stack" number="453" groups="memory"/>
  <syscall name="futex_wake" number="454"/>
  <syscall name="futex_wait" number="455"/>
  <syscall name="futex_requeue" number="456"/>
  <syscall name="statmount" number="457"/>
  <syscall name="listmount" number="458"/>
  <syscall name="lsm_get_self_attr" number="459"/>
  <syscall name="lsm_set_self_attr" number="460"/>
  <syscall name="lsm_list_modules" number="461"/>
  <syscall name="mseal" number="462" groups="memory"/>
  <syscall name="setxattrat" number="463"/>
  <syscall name="getxattrat" number="464"/>
  <syscall name="listxattrat" number="465"/>
  <syscall name="removexattrat" number="466"/>
</syscalls_info>
