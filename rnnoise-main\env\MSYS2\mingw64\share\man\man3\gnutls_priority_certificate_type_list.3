.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_priority_certificate_type_list" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_priority_certificate_type_list \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_priority_certificate_type_list(gnutls_priority_t " pcache ", const unsigned int ** " list ");"
.SH ARGUMENTS
.IP "gnutls_priority_t pcache" 12
is a \fBgnutls_priority_t\fP type.
.IP "const unsigned int ** list" 12
will point to an integer list
.SH "DESCRIPTION"
Get a list of available certificate types in the priority
structure.

As of version 3.6.4 this function is an alias for
gnutls_priority_certificate_type_list2 with the target parameter
set to:
\- GNUTLS_CTYPE_SERVER, if the \fBSERVER_PRECEDENCE\fP option is set
\- GNUTLS_CTYPE_CLIENT, otherwise.
.SH "RETURNS"
the number of certificate types, or an error code.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
