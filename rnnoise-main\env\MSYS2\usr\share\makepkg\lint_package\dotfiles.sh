#!/usr/bin/bash
#
#   dotfiles.sh - check for dotfiles in the package root
#
# <AUTHOR> <EMAIL>
#
#   This program is free software; you can redistribute it and/or modify
#   it under the terms of the GNU General Public License as published by
#   the Free Software Foundation; either version 2 of the License, or
#   (at your option) any later version.
#
#   This program is distributed in the hope that it will be useful,
#   but WITHOUT ANY WARRANTY; without even the implied warranty of
#   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#   GNU General Public License for more details.
#
#   You should have received a copy of the GNU General Public License
#   along with this program.  If not, see <http://www.gnu.org/licenses/>.
#

[[ -n "$LIBMAKEPKG_LINT_PACKAGE_DOTFILES_SH" ]] && return
LIBMAKEPKG_LINT_PACKAGE_DOTFILES_SH=1

MAKEPKG_LIBRARY=${MAKEPKG_LIBRARY:-'/usr/share/makepkg'}

source "$MAKEPKG_LIBRARY/util/message.sh"

lint_package_functions+=('check_dotfiles')

check_dotfiles() {
	local ret=0

	local shellopts=$(shopt -p nullglob)
	shopt -s nullglob

	for f in "$pkgdir"/.*; do
		[[ ${f##*/} == . || ${f##*/} == .. ]] && continue
		error "$(gettext "Dotfile found in package root '%s'")" "$f"
		ret=1
	done

	eval "$shellopts"

	return $ret
}
