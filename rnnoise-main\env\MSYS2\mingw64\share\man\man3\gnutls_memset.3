.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_memset" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_memset \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_memset(void * " data ", int " c ", size_t " size ");"
.SH ARGUMENTS
.IP "void * data" 12
the memory to set
.IP "int c" 12
the constant byte to fill the memory with
.IP "size_t size" 12
the size of memory
.SH "DESCRIPTION"
This function will operate similarly to \fBmemset()\fP, but will
not be optimized out by the compiler.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
