/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#define WEBPOST_ERROR_FIRST __MSABI_LONG(0x40042100)
#define WEBPOST_ERROR_UNKNOWN __MSABI_LONG(0xC0042101)
#define WEBPOST_ERROR_PROVCLSID_UNKNOWN __MSABI_LONG(0xC0042102)
#define WEBPOST_ERROR_SITE_CORRUPT __MSABI_LONG(0xC0042103)
#define WEBPOST_ERROR_PROV_CORRUPT __MSABI_LONG(0xC0042104)
#define WEBPOST_ERROR_PROV_DLL __MSABI_LONG(0xC0042105)
#define WEBPOST_ERROR_PROV_EP __MSABI_LONG(0xC0042106)
#define WEBPOST_ERROR_INIT_FAILED __MSABI_LONG(0xC0042107)
#define WEBPOST_ERROR_LIST_SITES __MSABI_LONG(0xC0042108)
#define WEBPOST_ERROR_SITE_EXISTS __MSABI_LONG(0xC0042109)
#define WEBPOST_ERROR_CREATE_SITE __MSABI_LONG(0xC004210A)
#define WEBPOST_ERROR_SITE_DOESNOTEXIST __MSABI_LONG(0xC004210B)
#define WEBPOST_ERROR_DELETE_SITE __MSABI_LONG(0xC004210C)
#define WEBPOST_ERROR_ENUM_PROVS __MSABI_LONG(0xC004210D)
#define WEBPOST_ERROR_PROV_QI __MSABI_LONG(0xC004210E)
#define WEBPOST_ERROR_POST_FILES __MSABI_LONG(0xC004210F)
#define WEBPOST_ERROR_COCREATE_WIZARD __MSABI_LONG(0xC0042110)
#define WEBPOST_ERROR_POSTINFO_REQUIRED __MSABI_LONG(0xC0042111)
#define WEBPOST_ERROR_AUTOBIND_FAILED __MSABI_LONG(0xC0042112)
#define WEBPOST_ERROR_BAD_PROV_PTR __MSABI_LONG(0xC0042113)
#define WEBPOST_ERROR_PROV_NOT_IN_POSTINFO __MSABI_LONG(0x80042114)
#define WEBPOST_ERROR_EXTENDED_ERROR __MSABI_LONG(0xC0042116)
#define WEBPOST_ERROR_NO_EXT_ERR_INFO __MSABI_LONG(0xC0042117)
#define WEBPOST_ERROR_INVALID_POSTINFO __MSABI_LONG(0x40042118)
#define WEBPOST_ERROR_NO_POSTINFO __MSABI_LONG(0x40042119)
#define WEBPOST_ERROR_HTTP_GET_FAILED __MSABI_LONG(0x4004211A)
#define WEBPOST_ERROR_LAST __MSABI_LONG(0x400421FF)
