.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_add_provider" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_add_provider \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_add_provider(const char * " name ", const char * " params ");"
.SH ARGUMENTS
.IP "const char * name" 12
The filename of the module
.IP "const char * params" 12
should be NULL or a known string (see description)
.SH "DESCRIPTION"
This function will load and add a PKCS 11 module to the module
list used in gnutls. After this function is called the module will
be used for PKCS 11 operations.

When loading a module to be used for certificate verification,
use the string 'trusted' as  \fIparams\fP .

Note that this function is not thread safe.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
