# help.ru.txt - Russian GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
# Copyright (C) 2016 In<PERSON>ev <<EMAIL>> (translation)
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
#
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.

# The translated revision was taken from HEAD b8bb16c6c08d3c2947f1ff67
# which is the same as the revision from STABLE-BRANCH-2-0 776bee6d370

.#pinentry.qualitybar.tooltip
# [remove the hash mark from the key to enable this text]
# This entry is just an example on how to customize the tooltip shown
# when hovering over the quality bar of the pinentry.  We don't
# install this text so that the hardcoded translation takes
# precedence.  An administrator should write up a short help to tell
# the users about the configured passphrase constraints and save that
# to /etc/gnupg/help.txt.  The help text should not be longer than
# about 800 characters.
Этот индикатор показывает качество введенной выше фразы-пароля.

Пока индикатор красный, GnuPG считает фразу-пароль неприемлемо слабой.
Уточните у своего администратора принятые требования к фразе-паролю.
.


.gnupg.agent-problem
# There was a problem accessing or starting the agent.
К запущенному Gpg-Agent было невозможно подключиться, либо возникла
проблема соединения с ним.

Система использует фоновый процесс под названием Gpg-Agent
для обработки секретных ключей и запроса фраз-паролей. Обычно процесс
запускается при входе пользователя в систему и работает, пока
пользователь не выйдет. Если процесс недоступен, система пытается
запустить его на ходу, но функции этой версий несколько ограничены,
это может привести к небольшим проблемам.

Вероятно, для решения проблемы нужно обратиться к администратору.
В качестве временной меры можно выйти и снова войти в систему;
может быть, это поможет. В любом случае сообщите об этом
администратору, потому что это указывает на недочет в программе.
.


.gnupg.dirmngr-problem
# There was a problen accessing the dirmngr.
К запущенному Dirmngr было невозможно подключиться, либо возникла
проблема соединения с ним.

Для просмотра списков отзыва сертификатов во время проверки
сертификатов и для поиска ключей на локальных серверах система
пользуется внешней служебной программой Dirmngr. Обычно она работает
как системная служба (демон) и не нуждается в каких-либо действиях
со стороны пользователя. В случае проблем система может запускать
новую копию Dirmngr по каждому запросу; это запасной вариант
с ухудшенными характеристиками.

Если Вы столкнулись с этой проблемой, обратитесь к системному
администратору. В качестве временного решения можно попробовать
отключить проверку списков отзыва сертификатов в настройках gpgsm.
.


.gpg.edit_ownertrust.value
# The help identies prefixed with "gpg." used to be hard coded in gpg
# but may now be overridden by help texts from this file.
Если хотите, поставьте здесь значение; оно никогда не будет выводиться
для третьих сторон. Нам оно нужно для реализации сети доверия; оно
никак не связано с (неявно создаваемой) сетью сертификатов.
.

.gpg.edit_ownertrust.set_ultimate.okay
Для построения Сети доверия GnuPG нужно знать, каким ключам доверять
полностью - обычно это ключи, секретные части которых у Вас есть.
Ответ "да" установит полное доверие этому ключу.


.gpg.untrusted_key.override
Если Вы хотите все равно пользоваться этим недоверенным ключом,
ответьте "да".
.

.gpg.pklist.user_id.enter
Введите ID пользователя - получателя Вашего сообщения.
.

.gpg.keygen.algo
Выберите алгоритм.

DSA (он же DSS) можно применять только для подписей.

Elgamal - алгоритм только для шифрования.

RSA можно применять для шифрования или подписей.

Первый (первичный) ключ всегда должен быть пригоден для подписей.
.


.gpg.keygen.algo.rsa_se
В целом неразумно пользоваться одним и тем же ключом и для подписи,
и для шифрования. Это может быть полезно только в определенных
случаях. Проконсультируйтесь со своим экспертом по безопасности.
.


.gpg.keygen.flags
Поменять функции ключа.

Переключать можно только функции, доступные для выбранного
алгоритма.

Для быстрой установки сразу всех возможностей введите сначала '=',
а за ним список букв, задающих набор функций: '1' - подпись, '2' -
шифрование, '3' - аутентификация. Неправильные буквы и функции
не учитываются. Сразу после быстрого ввода это подменю закрывается.
.


.gpg.keygen.size
Введите размер ключа.

Предлагаемое значение обычно хорошо подходит.

Если Вам нужен ключ большого размера, например, 4096 бит, подумайте,
действительно ли это для Вас имеет смысл. См. комикс на странице
http://www.xkcd.com/538/ .
.

.gpg.keygen.size.huge.okay
Отвечайте "да" или "нет".
.


.gpg.keygen.size.large.okay
Отвечайте "да" или "нет".
.


.gpg.keygen.valid
Введите нужное значение, как показано в приглашении.
Можно ввести дату ИСО (ГГГГ-ММ-ДД), но сообщения об ошибках будут
неудобочитаемыми: система пытается интерпретировать данное значение
как интервал.
.

.gpg.keygen.valid.okay
Отвечайте "да" или "нет".
.


.gpg.keygen.name
Введите имя владельца ключа.
Символы "<" и ">" недопустимы.
Пример: Вася Пушкин
.


.gpg.keygen.email
Введите, пожалуйста, адрес электронной почты (необязательно,
но очень рекомендуется).
Пример: <EMAIL>
.

.gpg.keygen.comment
Введите, пожалуйста, необязательное примечание.
Символы "(" и ")" недопустимы.
В общем и целом оно не нужно.
.


.gpg.keygen.userid.cmd
# (Keep a leading empty line)

N  сменить имя.
C  сменить примечание.
E  сменить адрес.
O  продолжить создание ключа.
Q  прекратить создание ключа.
.

.gpg.keygen.sub.okay
Введите "да" (или "y"), чтобы разрешить создание ключа.
.

.gpg.sign_uid.okay
Отвечайте "да" или "нет".
.

.gpg.sign_uid.class
Когда Вы подписываете идентификатор пользователя в ключе, нужно сначала
удостовериться, что ключ принадлежит указанному в идентификаторе лицу.
Другим полезно знать, насколько тщательно Вы это проверили.

"0" значит, что Вы не указываете, насколько тщательно вы проверяли ключ.

"1" значит, что Вы считаете, что ключ принадлежит заявленному лицу, но Вы
    не могли проверить или не проверяли ключ. Это полезно для проверки
    "инкогнито", когда вы подписываете ключ с псевдонимом.

"2" значит, что Вы провели частичную проверку ключа. Например, проверили
    отпечаток ключа и идентификатор пользователя из ключа
    по фотоидентификатору.

"3" значит, что Вы провели тщательную проверку ключа. Например,
    Вы проверили отпечаток ключа, а также проверили по удостоверению
    личности (такому как паспорт), что имя владельца ключа совпадает
    с именем человека, записанным в идентификаторе пользователя ключа;
    наконец, Вы удостоверились (обменявшись электронной почтой), что
    адрес электронной почты принадлежит владельцу ключа.

Имейте в виду, что примеры, данные для уровней 2 и 3 - это *только*
примеры. В конечном счете Вы сами решаете, что значит "частичная"
и "тщательная" проверка, когда Вы подписываете другие ключи.

Если затрудняетесь с ответом, поставьте "0".
.

.gpg.change_passwd.empty.okay
Отвечайте "да" или "нет".
.


.gpg.keyedit.save.okay
Отвечайте "да" или "нет".
.


.gpg.keyedit.cancel.okay
Отвечайте "да" или "нет".
.

.gpg.keyedit.sign_all.okay
Ответьте "да", если хотите подписать ВСЕ идентификаторы пользователя.
.

.gpg.keyedit.remove.uid.okay
Ответьте "да", если действительно хотите удалить этот идентификатор
пользователя.
Все сертификаты будут также удалены!
.

.gpg.keyedit.remove.subkey.okay
Ответьте "да", если подключ можно удалить.
.


.gpg.keyedit.delsig.valid
Это верная подпись ключа; как правило, ее не нужно удалять,
поскольку может быть важно установить отношение доверия между
этим ключом и другими ключами.
.

.gpg.keyedit.delsig.unknown
Эту подпись нельзя проверить, поскольку отсутствует соответствующий
ключ. Удаление ее нужно отложить до тех пор, пока не станет
известно, какой из ключей был использован, так как подпись
этого ключа могло бы установить отношение доверия через
другой, уже сертифицированный ключ.
.

.gpg.keyedit.delsig.invalid
Подпись недействительна. Имеет смысл удалить ее из Вашей таблицы
ключей.
.

.gpg.keyedit.delsig.selfsig
Эта подпись связывает идентификатор пользователя с ключом. Обычно
удалять такие подписи не следует. Это может сделать ключ непригодным
для пользования с GnuPG. Так что делайте это только если эта
самоподпись по какой-то причине недействительна и есть другая.
.

.gpg.keyedit.updpref.okay
Изменить предпочтения для всех идентификаторов пользователя (или
только для выбранных) на текущий список предпочтений. Дата всех
самоподписей, которых это касается, будет сдвинута вперед
на одну секунду.
.


.gpg.passphrase.enter
# (keep a leading empty line)

Введите, пожалуйста, фразу-пароль (секретное предложение).
.


.gpg.passphrase.repeat
Повторите введенную фразу-пароль, чтобы проверить, что Вы не ошиблись.
.

.gpg.detached_signature.filename
Задайте имя файла, который подписывается.
.

.gpg.openfile.overwrite.okay
# openfile.c (overwrite_filep)
Ответьте "да", если файл можно перезаписать.
.

.gpg.openfile.askoutname
# openfile.c (ask_outfile_name)
Введите новое имя файла. Если просто нажать "Enter", будет
использован файл по умолчанию (указан в скобках).
.

.gpg.ask_revocation_reason.code
# revoke.c (ask_revocation_reason)
Нужно указать причину отзыва. Можно выбрать из списка:
  "Ключ был раскрыт"
      Есть основания полагать, что какие-то лица получили
      несанкционированный доступ к секретному ключу.
  "Ключ заменен другим"
      Вы заменили ключ на новый.
  "Ключ больше не используется"
      Вы дали ключу отставку.
  "ID пользователя больше не действителен"
      ID пользователя больше не должен употребляться; обычно это значит,
      что адрес электронной почты недействителен.
.

.gpg.ask_revocation_reason.text
# revoke.c (ask_revocation_reason)
Если хотите, можете ввести текст, поясняющий причину, по которой
выпущен этот сертификат отзыва. Выражайтесь, пожалуйста, ясно.
Текст заканчивается пустой строкой.
.




.gpgsm.root-cert-not-trusted
# This text gets displayed by the audit log if
# a root certificates was not trusted.
Нет доверия к корневому сертификату. В зависимости от настроек
Вам могли предложить пометить этот корневой сертификат как доверенный
или вручную указать GnuPG, что этому сертификату нужно доверять.
Доверенные сертификаты задаются в файле trustlist.txt в домашнем
каталоге GnuPG. Если сомневаетесь, спросите своего системного
администратора, следует ли Вам доверять этому сертификату.


.gpgsm.crl-problem
# This tex is displayed by the audit log for problems with
# the CRL or OCSP checking.
В зависимости от настроек возникла проблема в получении списка
отозванных сертификатов или в выполнении проверки по протоколу
OCSP. Это могло случиться по очень многим причинам. Обратитесь
к документации за возможными решениями.


# Local variables:
# mode: default-generic
# coding: utf-8
# End:
