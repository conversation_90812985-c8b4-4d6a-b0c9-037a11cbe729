<!-- Creator     : groff version 1.22.3 -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<meta name="Content-Style" content="text/css">
<style type="text/css">
       p       { margin-top: 0; margin-bottom: 0; vertical-align: top }
       pre     { margin-top: 0; margin-bottom: 0; vertical-align: top }
       table   { margin-top: 0; margin-bottom: 0; vertical-align: top }
       h1      { text-align: center }
</style>
<title>ICONV_CLOSE</title>

</head>
<body>

<h1 align="center">ICONV_CLOSE</h1>

<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#RETURN VALUE">RETURN VALUE</a><br>
<a href="#CONFORMING TO">CONFORMING TO</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>


<h2>NAME
<a name="NAME"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">iconv_close
&minus; deallocate descriptor for character set
conversion</p>

<h2>SYNOPSIS
<a name="SYNOPSIS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em"><b>#include
&lt;iconv.h&gt;</b></p>

<p style="margin-left:11%; margin-top: 1em"><b>int
iconv_close (iconv_t</b> <i>cd</i><b>);</b></p>

<h2>DESCRIPTION
<a name="DESCRIPTION"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The
<b>iconv_close</b> function deallocates a conversion
descriptor <i>cd</i> previously allocated using
<b>iconv_open</b>.</p>

<h2>RETURN VALUE
<a name="RETURN VALUE"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">When
successful, the <b>iconv_close</b> function returns 0. In
case of error, it sets <b>errno</b> and returns
&minus;1.</p>

<h2>CONFORMING TO
<a name="CONFORMING TO"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">POSIX:2024</p>

<h2>SEE ALSO
<a name="SEE ALSO"></a>
</h2>



<p style="margin-left:11%; margin-top: 1em"><b>iconv_open</b>(3)
<b>iconv</b>(3)</p>
<hr>
</body>
</html>
