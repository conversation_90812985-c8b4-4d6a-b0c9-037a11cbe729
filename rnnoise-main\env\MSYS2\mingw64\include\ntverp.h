/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#define VER_PRODUCTBUILD 3790
#define VER_PRODUCTBUILD_QFE 1830

#ifdef NASHVILLE
#include <ieverp.h>
#else

#define VER_PRODUCTMAJORVERSION 5
#define VER_PRODUCTMINORVERSION 2

#define VER_PRODUCTVERSION_W (0x0502)
#define VER_PRODUCTVERSION_DW (0x05020000 | VER_PRODUCTBUILD)

#define VER_PRODUCTBETA_STR ""

#define VER_PRODUCTVERSION_MAJORMINOR2(x,y) #x "." #y
#define VER_PRODUCTVERSION_MAJORMINOR1(x,y) VER_PRODUCTVERSION_MAJORMINOR2(x,y)
#define VER_PRODUCTVERSION_STRING VER_PRODUCTVERSION_MAJORMINOR1(VER_PRODUCTMAJORVERSION,VER_PRODUCTMINORVERSION)

#define LVER_PRODUCTVERSION_MAJORMINOR2(x,y) L#x L"." L#y
#define LVER_PRODUCTVERSION_MAJORMINOR1(x,y) LVER_PRODUCTVERSION_MAJORMINOR2(x,y)
#define LVER_PRODUCTVERSION_STRING LVER_PRODUCTVERSION_MAJORMINOR1(VER_PRODUCTMAJORVERSION,VER_PRODUCTMINORVERSION)

#define VER_PRODUCTVERSION VER_PRODUCTMAJORVERSION,VER_PRODUCTMINORVERSION,3790,VER_PRODUCTBUILD_QFE

#define VER_DDK_PRODUCTVERSION 5,02
#define VER_DDK_PRODUCTVERSION_STR "5.02"
#endif

#define VER_BPAD
#define LVER_BPAD

#define VER_PRODUCTVERSION_STR2(x,y) VER_PRODUCTVERSION_STRING "." VER_BPAD #x "." #y
#define VER_PRODUCTVERSION_STR1(x,y) VER_PRODUCTVERSION_STR2(x,y)
#define VER_PRODUCTVERSION_STR VER_PRODUCTVERSION_STR1(3790,VER_PRODUCTBUILD_QFE)

#define LVER_PRODUCTVERSION_STR2(x,y) LVER_PRODUCTVERSION_STRING L"." LVER_BPAD L#x L"." L#y
#define LVER_PRODUCTVERSION_STR1(x,y) LVER_PRODUCTVERSION_STR2(x,y)
#define LVER_PRODUCTVERSION_STR LVER_PRODUCTVERSION_STR1(3790,VER_PRODUCTBUILD_QFE)

#define VER_DEBUG 0
#define VER_PRERELEASE 0

#if OFFICIAL_BUILD
#define VER_PRIVATE 0
#else
#define VER_PRIVATE VS_FF_PRIVATEBUILD
#endif

#define VER_FILEFLAGSMASK VS_FFI_FILEFLAGSMASK
#define VER_FILEOS VOS_NT_WINDOWS32
#define VER_FILEFLAGS (VER_PRERELEASE|VER_DEBUG|VER_PRIVATE)

#define VER_COMPANYNAME_STR "Microsoft Corporation"
#ifdef RC_INVOKED
#define VER_PRODUCTNAME_STR L"Microsoft\256 Windows\256 Operating System"
#else
#define VER_PRODUCTNAME_STR "Microsoft\256 Windows\256 Operating System"
#endif
#define VER_LEGALTRADEMARKS_STR "Microsoft\256 is a registered trademark of Microsoft Corporation. Windows\256 is a registered trademark of Microsoft Corporation."
