.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_kx_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_kx_get \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_kx_algorithm_t gnutls_kx_get(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
Get the currently used key exchange algorithm.

This function will return \fBGNUTLS_KX_ECDHE_RSA\fP, or \fBGNUTLS_KX_DHE_RSA\fP
under TLS 1.3, to indicate an elliptic curve DH key exchange or
a finite field one. The precise group used is available
by calling \fBgnutls_group_get()\fP instead.
.SH "RETURNS"
the key exchange algorithm used in the last handshake, a
\fBgnutls_kx_algorithm_t\fP value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
