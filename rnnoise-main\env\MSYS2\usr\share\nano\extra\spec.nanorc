## Syntax highlighting for RPM spec files.

## Original author:  <PERSON><PERSON><PERSON>

syntax spec "\.spec(\.[^/]+)?$"
comment "#"

# Main tags.
color brightblue "\<(Name|Version|Release|Summary|Group|URL|Url|Epoch|Icon|Serial)[[:space:]]*:"
color brightblue "\<(BuildArch(itectures)?|Exclusive(Arch|OS)|Exclude(Arch|OS))[[:space:]]*:"
color brightblue "\<(Provides|Requires(\(.*\))?|Obsoletes|Conflicts|Recommends|Suggests|Supplements|Enhances|PreReq)[[:space:]]*:"
color brightblue "\<(BuildRoot|BuildRequires|BuildConflicts|Prefix|RemovePathPostfixes)[[:space:]]*:"
color brightblue "\<(AutoReq|AutoProv|AutoReqProv)[[:space:]]*:"
color brightblue "\<(License|Copyright|Distribution|Vendor|Packager)[[:space:]]*:"
color brightblue "\<((Source|Patch)[0-9]*|Nosource|Nopatch)[[:space:]]*:"
# Architectures.
color brightred "\<((a|loong)arch64|alpha(ev(56?|67?)|pca56)?|amd64|armv(3l|4b|4l|5t(ej?)?l|6h?l|7(hn?)?l|8h?l)|athlon|em64t|geode|i370|i(3|4|5|6)86|ia32e|ia64|m68k(mint)?|mips(64)?(el|r6|r6el)?)\>"
color brightred "\<(pentium(3|4)|ppc(32dy4|8260|8560)?|ppc64(le|p7)?|ppc(64)?(i|p)series|riscv64|rs6000|s390x?|sgi|sh(3|4|4a)?|sparc(64v?|v8|v9v?)?|x86_64(_v2|_v3|_v4)?|xtensa)\>"

# Architecture and OS conditionals.
color brightred "%(ifarch|elifarch|ifnarch|ifos|elifos|ifnos)\>"
# %* strings.
color green "%([A-Z_a-z_0-9_]*)"
color magenta "%_([A-Z_a-z_0-9_]*)"
color yellow start="%__" end="\ "
color magenta start="%\{" end="\}"
color yellow start="%\{__" end="\}"
# Sections.
color red "^%((prep|build|install|check|clean)$|(description|files|package|changelog)\>)"
color red "^%((pre|post)(trans|un)?|trigger(prein|in|un|postun)?)\>"
color red "^%(trans)?filetrigger(in|un|postun)\>"
color red "^%(sourcelist|patchlist|generate_buildrequires|verifyscript)\>"
# Conditionals and defines.
color brightred "%(if|elif|else|endif|define|global|undefine)\>"

# Comments.
color cyan "(#|%dnl[[:space:]]).*"
# Special case: "# norootforbuild" is handled as main tag.
color brightblue "^# norootforbuild"
# For %changelog entries: first the author, then just the date.
color yellow "^\* .+>"
color brightyellow "^\* [[:alnum:] ]+ [0-9]{4} "

# Trailing whitespace.
color ,green "[[:space:]]+$"
