diff_cmd () {
	"$merge_tool_path" -u -e "$LOCAL" "$REMOTE"
	return 0
}

diff_cmd_help () {
	echo "Use WinMerge (requires a graphical session)"
}

merge_cmd () {
	# mergetool.winmerge.trustExitCode is implicitly false.
	# touch $BACKUP so that we can check_unchanged.
	"$merge_tool_path" -u -e -dl Local -dr <PERSON><PERSON> \
		"$LOCAL" "$REMOTE" "$MERGED"
}

translate_merge_tool_path() {
	mergetool_find_win32_cmd "WinMergeU.exe" "WinMerge"
}

merge_cmd_help () {
	echo "Use WinMerge (requires a graphical session)"
}
