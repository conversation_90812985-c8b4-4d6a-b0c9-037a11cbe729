.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_get_verify_cert_status" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_get_verify_cert_status \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned int gnutls_session_get_verify_cert_status(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.SH "DESCRIPTION"
This function returns the status of the verification when initiated
via auto\-verification, i.e., by \fBgnutls_session_set_verify_cert2()\fP or
\fBgnutls_session_set_verify_cert()\fP. If no certificate verification
was occurred then the return value would be set to ((unsigned int)\-1).

The certificate verification status is the same as in \fBgnutls_certificate_verify_peers()\fP.
.SH "RETURNS"
the certificate verification status.
.SH "SINCE"
3.4.6
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
