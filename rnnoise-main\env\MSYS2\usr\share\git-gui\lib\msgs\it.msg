set ::msgcat::header "Project-Id-Version: git-gui\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2010-01-28 10:04+0100\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: Italian <<EMAIL>>\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\n"
::msgcat::mcset it "git-gui: fatal error" "git-gui: errore grave"
::msgcat::mcset it "Invalid font specified in %s:" "Caratteri non validi specificati in %s:"
::msgcat::mcset it "Main Font" "Caratteri principali"
::msgcat::mcset it "Diff/Console Font" "Caratteri per confronti e terminale"
::msgcat::mcset it "Cannot find git in PATH." "Impossibile trovare git nel PATH"
::msgcat::mcset it "Cannot parse Git version string:" "Impossibile determinare la versione di Git:"
::msgcat::mcset it "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "La versione di Git non pu\u00f2 essere determinata.\n\n%s riporta che la versione \u00e8 '%s'.\n\n%s richiede almeno Git 1.5.0 o superiore.\n\nAssumere che '%s' sia alla versione 1.5.0?\n"
::msgcat::mcset it "Git directory not found:" "Non trovo la directory di git: "
::msgcat::mcset it "Cannot move to top of working directory:" "Impossibile spostarsi sulla directory principale del progetto:"
::msgcat::mcset it "Cannot use bare repository:" "Impossibile usare un archivio senza directory di lavoro:"
::msgcat::mcset it "No working directory" "Nessuna directory di lavoro"
::msgcat::mcset it "Refreshing file status..." "Controllo dello stato dei file in corso..."
::msgcat::mcset it "Scanning for modified files ..." "Ricerca di file modificati in corso..."
::msgcat::mcset it "Calling prepare-commit-msg hook..." "Avvio prepare-commit-msg hook..."
::msgcat::mcset it "Commit declined by prepare-commit-msg hook." "Revisione rifiutata dal prepare-commit-msg hook."
::msgcat::mcset it "Ready." "Pronto."
::msgcat::mcset it "Displaying only %s of %s files." "Saranno mostrati solo %s file su %s."
::msgcat::mcset it "Unmodified" "Non modificato"
::msgcat::mcset it "Modified, not staged" "Modificato, non preparato per una nuova revisione"
::msgcat::mcset it "Staged for commit" "Preparato per una nuova revisione"
::msgcat::mcset it "Portions staged for commit" "Parti preparate per una nuova revisione"
::msgcat::mcset it "Staged for commit, missing" "Preparato per una nuova revisione, mancante"
::msgcat::mcset it "File type changed, not staged" "Tipo di file modificato, non preparato per una nuova revisione"
::msgcat::mcset it "File type changed, staged" "Tipo di file modificato, preparato per una nuova revisione"
::msgcat::mcset it "Untracked, not staged" "Non tracciato, non preparato per una nuova revisione"
::msgcat::mcset it "Missing" "Mancante"
::msgcat::mcset it "Staged for removal" "Preparato per la rimozione"
::msgcat::mcset it "Staged for removal, still present" "Preparato alla rimozione, ancora presente"
::msgcat::mcset it "Requires merge resolution" "Richiede risoluzione dei conflitti"
::msgcat::mcset it "Starting gitk... please wait..." "Avvio di gitk... attendere..."
::msgcat::mcset it "Couldn't find gitk in PATH" "Impossibile trovare gitk nel PATH"
::msgcat::mcset it "Couldn't find git gui in PATH" "Impossibile trovare git gui nel PATH"
::msgcat::mcset it "Repository" "Archivio"
::msgcat::mcset it "Edit" "Modifica"
::msgcat::mcset it "Branch" "Ramo"
::msgcat::mcset it "Commit@@noun" "Revisione"
::msgcat::mcset it "Merge" "Fusione (Merge)"
::msgcat::mcset it "Remote" "Remoto"
::msgcat::mcset it "Tools" "Accessori"
::msgcat::mcset it "Explore Working Copy" "Esplora copia di lavoro"
::msgcat::mcset it "Browse Current Branch's Files" "Esplora i file del ramo attuale"
::msgcat::mcset it "Browse Branch Files..." "Esplora i file del ramo..."
::msgcat::mcset it "Visualize Current Branch's History" "Visualizza la cronologia del ramo attuale"
::msgcat::mcset it "Visualize All Branch History" "Visualizza la cronologia di tutti i rami"
::msgcat::mcset it "Browse %s's Files" "Esplora i file di %s"
::msgcat::mcset it "Visualize %s's History" "Visualizza la cronologia di %s"
::msgcat::mcset it "Database Statistics" "Statistiche dell'archivio"
::msgcat::mcset it "Compress Database" "Comprimi l'archivio"
::msgcat::mcset it "Verify Database" "Verifica l'archivio"
::msgcat::mcset it "Create Desktop Icon" "Crea icona desktop"
::msgcat::mcset it "Quit" "Esci"
::msgcat::mcset it "Undo" "Annulla"
::msgcat::mcset it "Redo" "Ripeti"
::msgcat::mcset it "Cut" "Taglia"
::msgcat::mcset it "Copy" "Copia"
::msgcat::mcset it "Paste" "Incolla"
::msgcat::mcset it "Delete" "Elimina"
::msgcat::mcset it "Select All" "Seleziona tutto"
::msgcat::mcset it "Create..." "Crea..."
::msgcat::mcset it "Checkout..." "Attiva..."
::msgcat::mcset it "Rename..." "Rinomina"
::msgcat::mcset it "Delete..." "Elimina..."
::msgcat::mcset it "Reset..." "Ripristina..."
::msgcat::mcset it "Done" "Fatto"
::msgcat::mcset it "Commit@@verb" "Nuova revisione"
::msgcat::mcset it "New Commit" "Nuova revisione"
::msgcat::mcset it "Amend Last Commit" "Correggi l'ultima revisione"
::msgcat::mcset it "Rescan" "Analizza nuovamente"
::msgcat::mcset it "Stage To Commit" "Prepara per una nuova revisione"
::msgcat::mcset it "Stage Changed Files To Commit" "Prepara i file modificati per una nuova revisione"
::msgcat::mcset it "Unstage From Commit" "Annulla preparazione"
::msgcat::mcset it "Revert Changes" "Annulla modifiche"
::msgcat::mcset it "Show Less Context" "Mostra meno contesto"
::msgcat::mcset it "Show More Context" "Mostra pi\u00f9 contesto"
::msgcat::mcset it "Sign Off" "Sign Off"
::msgcat::mcset it "Local Merge..." "Fusione locale..."
::msgcat::mcset it "Abort Merge..." "Interrompi fusione..."
::msgcat::mcset it "Add..." "Aggiungi..."
::msgcat::mcset it "Push..." "Propaga..."
::msgcat::mcset it "Delete Branch..." "Elimina ramo..."
::msgcat::mcset it "Options..." "Opzioni..."
::msgcat::mcset it "Remove..." "Rimuovi..."
::msgcat::mcset it "Help" "Aiuto"
::msgcat::mcset it "About %s" "Informazioni su %s"
::msgcat::mcset it "Online Documentation" "Documentazione sul web"
::msgcat::mcset it "Show SSH Key" "Mostra chave SSH"
::msgcat::mcset it "fatal: cannot stat path %s: No such file or directory" "errore grave: impossibile effettuare lo stat del path %s: file o directory non trovata"
::msgcat::mcset it "Current Branch:" "Ramo attuale:"
::msgcat::mcset it "Staged Changes (Will Commit)" "Modifiche preparate (saranno nella nuova revisione)"
::msgcat::mcset it "Unstaged Changes" "Modifiche non preparate"
::msgcat::mcset it "Stage Changed" "Prepara modificati"
::msgcat::mcset it "Push" "Propaga (Push)"
::msgcat::mcset it "Initial Commit Message:" "Messaggio di revisione iniziale:"
::msgcat::mcset it "Amended Commit Message:" "Messaggio di revisione corretto:"
::msgcat::mcset it "Amended Initial Commit Message:" "Messaggio iniziale di revisione corretto:"
::msgcat::mcset it "Amended Merge Commit Message:" "Messaggio di fusione corretto:"
::msgcat::mcset it "Merge Commit Message:" "Messaggio di fusione:"
::msgcat::mcset it "Commit Message:" "Messaggio di revisione:"
::msgcat::mcset it "Copy All" "Copia tutto"
::msgcat::mcset it "File:" "File:"
::msgcat::mcset it "Refresh" "Rinfresca"
::msgcat::mcset it "Decrease Font Size" "Diminuisci dimensione caratteri"
::msgcat::mcset it "Increase Font Size" "Aumenta dimensione caratteri"
::msgcat::mcset it "Encoding" "Codifica"
::msgcat::mcset it "Apply/Reverse Hunk" "Applica/Inverti sezione"
::msgcat::mcset it "Apply/Reverse Line" "Applica/Inverti riga"
::msgcat::mcset it "Run Merge Tool" "Avvia programma esterno per la risoluzione dei conflitti"
::msgcat::mcset it "Use Remote Version" "Usa versione remota"
::msgcat::mcset it "Use Local Version" "Usa versione locale"
::msgcat::mcset it "Revert To Base" "Ritorna alla revisione comune"
::msgcat::mcset it "Visualize These Changes In The Submodule" "Visualizza queste modifiche nel sottomodulo"
::msgcat::mcset it "Visualize Current Branch History In The Submodule" "Visualizza la cronologia del ramo attuale nel sottomodulo"
::msgcat::mcset it "Visualize All Branch History In The Submodule" "Visualizza la cronologia di tutti i rami nel sottomodulo"
::msgcat::mcset it "Start git gui In The Submodule" "Avvia git gui nel sottomodulo"
::msgcat::mcset it "Unstage Hunk From Commit" "Annulla preparazione della sezione per una nuova revisione"
::msgcat::mcset it "Unstage Lines From Commit" "Annulla preparazione delle linee per una nuova revisione"
::msgcat::mcset it "Unstage Line From Commit" "Annulla preparazione della linea per una nuova revisione"
::msgcat::mcset it "Stage Hunk For Commit" "Prepara sezione per una nuova revisione"
::msgcat::mcset it "Stage Lines For Commit" "Prepara linee per una nuova revisione"
::msgcat::mcset it "Stage Line For Commit" "Prepara linea per una nuova revisione"
::msgcat::mcset it "Initializing..." "Inizializzazione..."
::msgcat::mcset it "Possible environment issues exist.\n\nThe following environment variables are probably\ngoing to be ignored by any Git subprocess run\nby %s:\n\n" "Possibili problemi con le variabili d'ambiente.\n\nLe seguenti variabili d'ambiente saranno probabilmente\nignorate da tutti i sottoprocessi di Git avviati\nda %s:\n\n"
::msgcat::mcset it "\nThis is due to a known issue with the\nTcl binary distributed by Cygwin." "\nCi\u00f2 \u00e8 dovuto a un problema conosciuto\ncausato dall'eseguibile Tcl distribuito da Cygwin."
::msgcat::mcset it "\n\nA good replacement for %s\nis placing values for the user.name and\nuser.email settings into your personal\n~/.gitconfig file.\n" "\n\nUna buona alternativa a %s\nconsiste nell'assegnare valori alle variabili di configurazione\nuser.name e user.email nel tuo file ~/.gitconfig personale.\n"
::msgcat::mcset it "git-gui - a graphical user interface for Git." "git-gui - un'interfaccia grafica per Git."
::msgcat::mcset it "File Viewer" "Mostra file"
::msgcat::mcset it "Commit:" "Revisione:"
::msgcat::mcset it "Copy Commit" "Copia revisione"
::msgcat::mcset it "Find Text..." "Trova testo..."
::msgcat::mcset it "Do Full Copy Detection" "Ricerca accurata delle copie"
::msgcat::mcset it "Show History Context" "Mostra contesto nella cronologia"
::msgcat::mcset it "Blame Parent Commit" "Annota la revisione precedente"
::msgcat::mcset it "Reading %s..." "Lettura di %s..."
::msgcat::mcset it "Loading copy/move tracking annotations..." "Caricamento annotazioni per copie/spostamenti..."
::msgcat::mcset it "lines annotated" "linee annotate"
::msgcat::mcset it "Loading original location annotations..." "Caricamento annotazioni per posizione originaria..."
::msgcat::mcset it "Annotation complete." "Annotazione completata."
::msgcat::mcset it "Busy" "Occupato"
::msgcat::mcset it "Annotation process is already running." "Il processo di annotazione \u00e8 gi\u00e0 in corso."
::msgcat::mcset it "Running thorough copy detection..." "Ricerca accurata delle copie in corso..."
::msgcat::mcset it "Loading annotation..." "Caricamento annotazioni..."
::msgcat::mcset it "Author:" "Autore:"
::msgcat::mcset it "Committer:" "Revisione creata da:"
::msgcat::mcset it "Original File:" "File originario:"
::msgcat::mcset it "Cannot find HEAD commit:" "Impossibile trovare la revisione HEAD:"
::msgcat::mcset it "Cannot find parent commit:" "Impossibile trovare la revisione precedente:"
::msgcat::mcset it "Unable to display parent" "Impossibile visualizzare la revisione precedente"
::msgcat::mcset it "Error loading diff:" "Errore nel caricamento delle differenze:"
::msgcat::mcset it "Originally By:" "In origine da:"
::msgcat::mcset it "In File:" "Nel file:"
::msgcat::mcset it "Copied Or Moved Here By:" "Copiato o spostato qui da:"
::msgcat::mcset it "Checkout Branch" "Attiva ramo"
::msgcat::mcset it "Checkout" "Attiva"
::msgcat::mcset it "Cancel" "Annulla"
::msgcat::mcset it "Revision" "Revisione"
::msgcat::mcset it "Options" "Opzioni"
::msgcat::mcset it "Fetch Tracking Branch" "Recupera duplicato locale di ramo remoto"
::msgcat::mcset it "Detach From Local Branch" "Stacca da ramo locale"
::msgcat::mcset it "Create Branch" "Crea ramo"
::msgcat::mcset it "Create New Branch" "Crea nuovo ramo"
::msgcat::mcset it "Create" "Crea"
::msgcat::mcset it "Branch Name" "Nome del ramo"
::msgcat::mcset it "Name:" "Nome:"
::msgcat::mcset it "Match Tracking Branch Name" "Appaia nome del duplicato locale di ramo remoto"
::msgcat::mcset it "Starting Revision" "Revisione iniziale"
::msgcat::mcset it "Update Existing Branch:" "Aggiorna ramo esistente:"
::msgcat::mcset it "No" "No"
::msgcat::mcset it "Fast Forward Only" "Solo fast forward"
::msgcat::mcset it "Reset" "Ripristina"
::msgcat::mcset it "Checkout After Creation" "Attiva dopo la creazione"
::msgcat::mcset it "Please select a tracking branch." "Scegliere un duplicato locale di ramo remoto"
::msgcat::mcset it "Tracking branch %s is not a branch in the remote repository." "Il duplicato locale del ramo remoto %s non \u00e8 un ramo nell'archivio remoto."
::msgcat::mcset it "Please supply a branch name." "Inserire un nome per il ramo."
::msgcat::mcset it "'%s' is not an acceptable branch name." "'%s' non \u00e8 utilizzabile come nome di ramo."
::msgcat::mcset it "Delete Branch" "Elimina ramo"
::msgcat::mcset it "Delete Local Branch" "Elimina ramo locale"
::msgcat::mcset it "Local Branches" "Rami locali"
::msgcat::mcset it "Delete Only If Merged Into" "Cancella solo se fuso con un altro ramo"
::msgcat::mcset it "Always (Do not perform merge checks)" "Sempre (non verificare le fusioni)"
::msgcat::mcset it "The following branches are not completely merged into %s:" "I rami seguenti non sono stati fusi completamente in %s:"
::msgcat::mcset it "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "Ripristinare rami cancellati \u00e8 difficile.\n\nCancellare i rami selezionati?"
::msgcat::mcset it "Failed to delete branches:\n%s" "Impossibile cancellare i rami:\n%s"
::msgcat::mcset it "Rename Branch" "Rinomina ramo"
::msgcat::mcset it "Rename" "Rinomina"
::msgcat::mcset it "Branch:" "Ramo:"
::msgcat::mcset it "New Name:" "Nuovo Nome:"
::msgcat::mcset it "Please select a branch to rename." "Scegliere un ramo da rinominare."
::msgcat::mcset it "Branch '%s' already exists." "Il ramo '%s' esiste gi\u00e0."
::msgcat::mcset it "Failed to rename '%s'." "Impossibile rinominare '%s'."
::msgcat::mcset it "Starting..." "Avvio in corso..."
::msgcat::mcset it "File Browser" "File browser"
::msgcat::mcset it "Loading %s..." "Caricamento %s..."
::msgcat::mcset it "\[Up To Parent\]" "\[Directory superiore\]"
::msgcat::mcset it "Browse Branch Files" "Esplora i file del ramo"
::msgcat::mcset it "Browse" "Esplora"
::msgcat::mcset it "Fetching %s from %s" "Recupero %s da %s"
::msgcat::mcset it "fatal: Cannot resolve %s" "errore grave: impossibile risolvere %s"
::msgcat::mcset it "Close" "Chiudi"
::msgcat::mcset it "Branch '%s' does not exist." "Il ramo '%s' non esiste."
::msgcat::mcset it "Failed to configure simplified git-pull for '%s'." "Impossibile configurare git-pull semplificato per '%s'."
::msgcat::mcset it "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "Il ramo '%s' esiste gi\u00e0.\n\nNon pu\u00f2 effettuare un 'fast-forward' a %s.\nE' necessaria una fusione."
::msgcat::mcset it "Merge strategy '%s' not supported." "La strategia di fusione '%s' non \u00e8 supportata."
::msgcat::mcset it "Failed to update '%s'." "Impossibile aggiornare '%s'."
::msgcat::mcset it "Staging area (index) is already locked." "L'area di preparazione per una nuova revisione (indice) \u00e8 gi\u00e0 bloccata."
::msgcat::mcset it "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before the current branch can be changed.\n\nThe rescan will be automatically started now.\n" "L'ultimo stato analizzato non corrisponde allo stato dell'archivio.\n\nUn altro programma Git ha modificato questo archivio dall'ultima analisi. Bisogna effettuare una nuova analisi prima di poter cambiare il ramo attuale.\n\nLa nuova analisi comincer\u00e0 ora.\n"
::msgcat::mcset it "Updating working directory to '%s'..." "Aggiornamento della directory di lavoro a '%s' in corso..."
::msgcat::mcset it "files checked out" "file presenti nella directory di lavoro"
::msgcat::mcset it "Aborted checkout of '%s' (file level merging is required)." "Attivazione di '%s' fallita (richiesta una fusione a livello file)."
::msgcat::mcset it "File level merge required." "E' richiesta una fusione a livello file."
::msgcat::mcset it "Staying on branch '%s'." "Si rimarr\u00e0 sul ramo '%s'."
::msgcat::mcset it "You are no longer on a local branch.\n\nIf you wanted to be on a branch, create one now starting from 'This Detached Checkout'." "Non si \u00e8 pi\u00f9 su un ramo locale\n\nSe si vuole rimanere su un ramo, crearne uno ora a partire da 'Questa revisione attiva staccata'."
::msgcat::mcset it "Checked out '%s'." "Attivazione di '%s' completata."
::msgcat::mcset it "Resetting '%s' to '%s' will lose the following commits:" "Ripristinare '%s' a '%s' comporter\u00e0 la perdita delle seguenti revisioni:"
::msgcat::mcset it "Recovering lost commits may not be easy." "Ricomporre le revisioni perdute potrebbe non essere semplice."
::msgcat::mcset it "Reset '%s'?" "Ripristinare '%s'?"
::msgcat::mcset it "Visualize" "Visualizza"
::msgcat::mcset it "Failed to set current branch.\n\nThis working directory is only partially switched.  We successfully updated your files, but failed to update an internal Git file.\n\nThis should not have occurred.  %s will now close and give up." "Impossibile preparare il ramo attuale.\n\nQuesta directory di lavoro \u00e8 stata convertita solo parzialmente. I file sono stati aggiornati correttamente, ma l'aggiornamento di un file di Git ha prodotto degli errori.\n\nQuesto non sarebbe dovuto succedere.  %s ora terminer\u00e0 senza altre azioni."
::msgcat::mcset it "Select" "Seleziona"
::msgcat::mcset it "Font Family" "Famiglia di caratteri"
::msgcat::mcset it "Font Size" "Dimensione caratteri"
::msgcat::mcset it "Font Example" "Esempio caratteri"
::msgcat::mcset it "This is example text.\nIf you like this text, it can be your font." "Questo \u00e8 un testo d'esempio.\nSe ti piace questo testo, scegli questo carattere."
::msgcat::mcset it "Git Gui" "Git Gui"
::msgcat::mcset it "Create New Repository" "Crea nuovo archivio"
::msgcat::mcset it "New..." "Nuovo..."
::msgcat::mcset it "Clone Existing Repository" "Clona archivio esistente"
::msgcat::mcset it "Clone..." "Clona..."
::msgcat::mcset it "Open Existing Repository" "Apri archivio esistente"
::msgcat::mcset it "Open..." "Apri..."
::msgcat::mcset it "Recent Repositories" "Archivi recenti"
::msgcat::mcset it "Open Recent Repository:" "Apri archivio recente:"
::msgcat::mcset it "Failed to create repository %s:" "Impossibile creare l'archivio %s:"
::msgcat::mcset it "Directory:" "Directory:"
::msgcat::mcset it "Git Repository" "Archivio Git"
::msgcat::mcset it "Directory %s already exists." "La directory %s esiste gi\u00e0."
::msgcat::mcset it "File %s already exists." "Il file %s esiste gi\u00e0."
::msgcat::mcset it "Clone" "Clona"
::msgcat::mcset it "Source Location:" "Posizione sorgente:"
::msgcat::mcset it "Target Directory:" "Directory di destinazione:"
::msgcat::mcset it "Clone Type:" "Tipo di clone:"
::msgcat::mcset it "Standard (Fast, Semi-Redundant, Hardlinks)" "Standard (veloce, semi-ridondante, con hardlink)"
::msgcat::mcset it "Full Copy (Slower, Redundant Backup)" "Copia completa (pi\u00f9 lento, backup ridondante)"
::msgcat::mcset it "Shared (Fastest, Not Recommended, No Backup)" "Shared (il pi\u00f9 veloce, non raccomandato, nessun backup)"
::msgcat::mcset it "Not a Git repository: %s" "%s non \u00e8 un archivio Git."
::msgcat::mcset it "Standard only available for local repository." "Standard \u00e8 disponibile solo per archivi locali."
::msgcat::mcset it "Shared only available for local repository." "Shared \u00e8 disponibile solo per archivi locali."
::msgcat::mcset it "Location %s already exists." "Il file/directory %s esiste gi\u00e0."
::msgcat::mcset it "Failed to configure origin" "Impossibile configurare origin"
::msgcat::mcset it "Counting objects" "Calcolo oggetti"
::msgcat::mcset it "Unable to copy objects/info/alternates: %s" "Impossibile copiare oggetti/info/alternate: %s"
::msgcat::mcset it "Nothing to clone from %s." "Niente da clonare da %s."
::msgcat::mcset it "The 'master' branch has not been initialized." "Il ramo 'master' non \u00e8 stato inizializzato."
::msgcat::mcset it "Hardlinks are unavailable.  Falling back to copying." "Impossibile utilizzare gli hardlink. Si ricorrer\u00e0 alla copia."
::msgcat::mcset it "Cloning from %s" "Clonazione da %s"
::msgcat::mcset it "Copying objects" "Copia degli oggetti"
::msgcat::mcset it "KiB" "KiB"
::msgcat::mcset it "Unable to copy object: %s" "Impossibile copiare oggetto: %s"
::msgcat::mcset it "Linking objects" "Collegamento oggetti"
::msgcat::mcset it "objects" "oggetti"
::msgcat::mcset it "Unable to hardlink object: %s" "Hardlink impossibile sull'oggetto: %s"
::msgcat::mcset it "Cannot fetch branches and objects.  See console output for details." "Impossibile recuperare rami e oggetti. Controllare i dettagli forniti dalla console."
::msgcat::mcset it "Cannot fetch tags.  See console output for details." "Impossibile recuperare le etichette. Controllare i dettagli forniti dalla console."
::msgcat::mcset it "Cannot determine HEAD.  See console output for details." "Impossibile determinare HEAD. Controllare i dettagli forniti dalla console."
::msgcat::mcset it "Unable to cleanup %s" "Impossibile ripulire %s"
::msgcat::mcset it "Clone failed." "Clonazione non riuscita."
::msgcat::mcset it "No default branch obtained." "Non \u00e8 stato trovato un ramo predefinito."
::msgcat::mcset it "Cannot resolve %s as a commit." "Impossibile risolvere %s come una revisione."
::msgcat::mcset it "Creating working directory" "Creazione directory di lavoro"
::msgcat::mcset it "files" "file"
::msgcat::mcset it "Initial file checkout failed." "Attivazione iniziale non riuscita."
::msgcat::mcset it "Open" "Apri"
::msgcat::mcset it "Repository:" "Archivio:"
::msgcat::mcset it "Failed to open repository %s:" "Impossibile accedere all'archivio %s:"
::msgcat::mcset it "This Detached Checkout" "Questa revisione attiva staccata"
::msgcat::mcset it "Revision Expression:" "Espressione di revisione:"
::msgcat::mcset it "Local Branch" "Ramo locale"
::msgcat::mcset it "Tracking Branch" "Duplicato locale di ramo remoto"
::msgcat::mcset it "Tag" "Etichetta"
::msgcat::mcset it "Invalid revision: %s" "Revisione non valida: %s"
::msgcat::mcset it "No revision selected." "Nessuna revisione selezionata."
::msgcat::mcset it "Revision expression is empty." "L'espressione di revisione \u00e8 vuota."
::msgcat::mcset it "Updated" "Aggiornato"
::msgcat::mcset it "URL" "URL"
::msgcat::mcset it "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "Non c'\u00e8 niente da correggere.\n\nStai per creare la revisione iniziale. Non esiste una revisione precedente da correggere.\n"
::msgcat::mcset it "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "Non \u00e8 possibile effettuare una correzione durante una fusione.\n\nIn questo momento si sta effettuando una fusione che non \u00e8 stata del tutto completata. Non puoi correggere la revisione precedente a meno che prima tu non interrompa l'operazione di fusione in corso.\n"
::msgcat::mcset it "Error loading commit data for amend:" "Errore durante il caricamento dei dati della revisione da correggere:"
::msgcat::mcset it "Unable to obtain your identity:" "Impossibile ottenere la tua identit\u00e0:"
::msgcat::mcset it "Invalid GIT_COMMITTER_IDENT:" "GIT_COMMITTER_IDENT non valida:"
::msgcat::mcset it "warning: Tcl does not support encoding '%s'." "attenzione: Tcl non supporta la codifica '%s'."
::msgcat::mcset it "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before another commit can be created.\n\nThe rescan will be automatically started now.\n" "L'ultimo stato analizzato non corrisponde allo stato dell'archivio.\n\nUn altro programma Git ha modificato questo archivio dall'ultima analisi. Bisogna effettuare una nuova analisi prima di poter creare una nuova revisione.\n\nLa nuova analisi comincer\u00e0 ora.\n"
::msgcat::mcset it "Unmerged files cannot be committed.\n\nFile %s has merge conflicts.  You must resolve them and stage the file before committing.\n" "Non \u00e8 possibile creare una revisione con file non sottoposti a fusione.\n\nIl file %s presenta dei conflitti. Devi risolverli e preparare il file per creare una nuova revisione prima di effettuare questa azione.\n"
::msgcat::mcset it "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "Stato di file %s sconosciuto.\n\nQuesto programma non pu\u00f2 creare una revisione contenente il file %s.\n"
::msgcat::mcset it "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "Nessuna modifica per la nuova revisione.\n\nDevi preparare per una nuova revisione almeno 1 file prima di effettuare questa operazione.\n"
::msgcat::mcset it "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "Bisogna fornire un messaggio di revisione.\n\nUn buon messaggio di revisione ha il seguente formato:\n\n- Prima linea: descrivi in una frase ci\u00f2 che hai fatto.\n- Seconda linea: vuota.\n- Terza linea: spiega a cosa serve la tua modifica.\n"
::msgcat::mcset it "Calling pre-commit hook..." "Avvio pre-commit hook..."
::msgcat::mcset it "Commit declined by pre-commit hook." "Revisione rifiutata dal pre-commit hook."
::msgcat::mcset it "Calling commit-msg hook..." "Avvio commit-msg hook..."
::msgcat::mcset it "Commit declined by commit-msg hook." "Revisione rifiutata dal commit-msg hook."
::msgcat::mcset it "Committing changes..." "Archiviazione modifiche..."
::msgcat::mcset it "write-tree failed:" "write-tree non riuscito:"
::msgcat::mcset it "Commit failed." "Impossibile creare una nuova revisione."
::msgcat::mcset it "Commit %s appears to be corrupt" "La revisione %s sembra essere danneggiata"
::msgcat::mcset it "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "Nessuna modifica per la nuova revisione.\n\nQuesta revisione non modifica alcun file e non effettua alcuna fusione.\n\nSi proceder\u00e0 subito ad una nuova analisi.\n"
::msgcat::mcset it "No changes to commit." "Nessuna modifica per la nuova revisione."
::msgcat::mcset it "commit-tree failed:" "commit-tree non riuscito:"
::msgcat::mcset it "update-ref failed:" "update-ref non riuscito:"
::msgcat::mcset it "Created commit %s: %s" "Creata revisione %s: %s"
::msgcat::mcset it "Working... please wait..." "Elaborazione in corso... attendere..."
::msgcat::mcset it "Success" "Successo"
::msgcat::mcset it "Error: Command Failed" "Errore: comando non riuscito"
::msgcat::mcset it "Number of loose objects" "Numero di oggetti slegati"
::msgcat::mcset it "Disk space used by loose objects" "Spazio su disco utilizzato da oggetti slegati"
::msgcat::mcset it "Number of packed objects" "Numero di oggetti impacchettati"
::msgcat::mcset it "Number of packs" "Numero di pacchetti"
::msgcat::mcset it "Disk space used by packed objects" "Spazio su disco utilizzato da oggetti impacchettati"
::msgcat::mcset it "Packed objects waiting for pruning" "Oggetti impacchettati che attendono la potatura"
::msgcat::mcset it "Garbage files" "File inutili"
::msgcat::mcset it "Compressing the object database" "Compressione dell'archivio in corso"
::msgcat::mcset it "Verifying the object database with fsck-objects" "Verifica dell'archivio con fsck-objects in corso"
::msgcat::mcset it "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database.\n\nCompress the database now?" "Questo archivio attualmente ha circa %i oggetti slegati.\n\nPer mantenere buone prestazioni si raccomanda di comprimere l'archivio.\n\nComprimere l'archivio ora?"
::msgcat::mcset it "Invalid date from Git: %s" "Git ha restituito una data non valida: %s"
::msgcat::mcset it "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "Non sono state trovate differenze.\n\n%s non ha modifiche.\n\nLa data di modifica di questo file \u00e8 stata cambiata da un'altra applicazione, ma il contenuto del file \u00e8 rimasto invariato.\n\nSi proceder\u00e0 automaticamente ad una nuova analisi per trovare altri file che potrebbero avere lo stesso stato."
::msgcat::mcset it "Loading diff of %s..." "Caricamento delle differenze di %s..."
::msgcat::mcset it "LOCAL: deleted\nREMOTE:\n" "LOCALE: cancellato\nREMOTO:\n"
::msgcat::mcset it "REMOTE: deleted\nLOCAL:\n" "REMOTO: cancellato\nLOCALE:\n"
::msgcat::mcset it "LOCAL:\n" "LOCALE:\n"
::msgcat::mcset it "REMOTE:\n" "REMOTO:\n"
::msgcat::mcset it "Unable to display %s" "Impossibile visualizzare %s"
::msgcat::mcset it "Error loading file:" "Errore nel caricamento del file:"
::msgcat::mcset it "Git Repository (subproject)" "Archivio Git (sottoprogetto)"
::msgcat::mcset it "* Binary file (not showing content)." "* File binario (il contenuto non sar\u00e0 mostrato)."
::msgcat::mcset it "* Untracked file is %d bytes.\n* Showing only first %d bytes.\n" "* Il file non tracciato \u00e8 di %d byte.\n* Saranno visualizzati solo i primi %d byte.\n"
::msgcat::mcset it "\n* Untracked file clipped here by %s.\n* To see the entire file, use an external editor.\n" "\n* %s non visualizza completamente questo file non tracciato.\n* Per visualizzare il file completo, usare un programma esterno.\n"
::msgcat::mcset it "Failed to unstage selected hunk." "Impossibile rimuovere la sezione scelta dalla nuova revisione."
::msgcat::mcset it "Failed to stage selected hunk." "Impossibile preparare la sezione scelta per una nuova revisione."
::msgcat::mcset it "Failed to unstage selected line." "Impossibile rimuovere la riga scelta dalla nuova revisione."
::msgcat::mcset it "Failed to stage selected line." "Impossibile preparare la riga scelta per una nuova revisione."
::msgcat::mcset it "Default" "Predefinito"
::msgcat::mcset it "System (%s)" "Codifica di sistema (%s)"
::msgcat::mcset it "Other" "Altro"
::msgcat::mcset it "error" "errore"
::msgcat::mcset it "warning" "attenzione"
::msgcat::mcset it "You must correct the above errors before committing." "Bisogna correggere gli errori suddetti prima di creare una nuova revisione."
::msgcat::mcset it "Unable to unlock the index." "Impossibile sbloccare l'accesso all'indice"
::msgcat::mcset it "Index Error" "Errore nell'indice"
::msgcat::mcset it "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "Impossibile aggiornare l'indice. Ora sar\u00e0 avviata una nuova analisi che aggiorner\u00e0 git-gui."
::msgcat::mcset it "Continue" "Continua"
::msgcat::mcset it "Unlock Index" "Sblocca l'accesso all'indice"
::msgcat::mcset it "Unstaging %s from commit" "%s non far\u00e0 parte della prossima revisione"
::msgcat::mcset it "Ready to commit." "Pronto per creare una nuova revisione."
::msgcat::mcset it "Adding %s" "Aggiunta di %s in corso"
::msgcat::mcset it "Revert changes in file %s?" "Annullare le modifiche nel file %s?"
::msgcat::mcset it "Revert changes in these %i files?" "Annullare le modifiche in questi %i file?"
::msgcat::mcset it "Any unstaged changes will be permanently lost by the revert." "Tutte le modifiche non preparate per una nuova revisione saranno perse per sempre."
::msgcat::mcset it "Do Nothing" "Non fare niente"
::msgcat::mcset it "Reverting selected files" "Annullo le modifiche nei file selezionati"
::msgcat::mcset it "Reverting %s" "Annullo le modifiche in %s"
::msgcat::mcset it "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "Non posso effettuare fusioni durante una correzione.\n\nBisogna finire di correggere questa revisione prima di iniziare una qualunque fusione.\n"
::msgcat::mcset it "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before a merge can be performed.\n\nThe rescan will be automatically started now.\n" "L'ultimo stato analizzato non corrisponde allo stato dell'archivio.\n\nUn altro programma Git ha modificato questo archivio dall'ultima analisi.Bisogna effettuare una nuova analisi prima di poter effettuare una fusione.\n\nLa nuova analisi comincer\u00e0 ora.\n"
::msgcat::mcset it "You are in the middle of a conflicted merge.\n\nFile %s has merge conflicts.\n\nYou must resolve them, stage the file, and commit to complete the current merge.  Only then can you begin another merge.\n" "Sei nel mezzo di una fusione con conflitti.\n\nIl file %s ha dei conflitti.\n\nBisogna risolvere i conflitti, preparare il file per una nuova revisione ed infine crearla per completare la fusione attuale. Solo a questo punto potrai iniziare un'altra fusione.\n"
::msgcat::mcset it "You are in the middle of a change.\n\nFile %s is modified.\n\nYou should complete the current commit before starting a merge.  Doing so will help you abort a failed merge, should the need arise.\n" "Sei nel mezzo di una modifica.\n\nIl file %s \u00e8 stato modificato.\n\nBisogna completare la creazione della revisione attuale prima di iniziare una fusione. In questo modo sar\u00e0 pi\u00f9 facile interrompere una fusione non riuscita, nel caso ce ne fosse bisogno.\n"
::msgcat::mcset it "%s of %s" "%s di %s"
::msgcat::mcset it "Merging %s and %s..." "Fusione di %s e %s in corso..."
::msgcat::mcset it "Merge completed successfully." "Fusione completata con successo."
::msgcat::mcset it "Merge failed.  Conflict resolution is required." "Fusione non riuscita. Bisogna risolvere i conflitti."
::msgcat::mcset it "Merge Into %s" "Fusione in %s"
::msgcat::mcset it "Revision To Merge" "Revisione da fondere"
::msgcat::mcset it "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "Interruzione impossibile durante una correzione.\n\nBisogna finire di correggere questa revisione.\n"
::msgcat::mcset it "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "Interrompere fusione?\n\nL'interruzione della fusione attuale causer\u00e0 la perdita di *TUTTE* le modifiche non ancora presenti nell'archivio.\n\nContinuare con l'interruzione della fusione attuale?"
::msgcat::mcset it "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "Ripristinare la revisione attuale e annullare le modifiche?\n\nL'annullamento delle modifiche causer\u00e0 la perdita di *TUTTE* le modifiche non ancora presenti nell'archivio.\n\nContinuare con l'annullamento delle modifiche attuali?"
::msgcat::mcset it "Aborting" "Interruzione"
::msgcat::mcset it "files reset" "ripristino file"
::msgcat::mcset it "Abort failed." "Interruzione non riuscita."
::msgcat::mcset it "Abort completed.  Ready." "Interruzione completata. Pronto."
::msgcat::mcset it "Force resolution to the base version?" "Imporre la risoluzione alla revisione comune?"
::msgcat::mcset it "Force resolution to this branch?" "Imporre la risoluzione al ramo attuale?"
::msgcat::mcset it "Force resolution to the other branch?" "Imporre la risoluzione all'altro ramo?"
::msgcat::mcset it "Note that the diff shows only conflicting changes.\n\n%s will be overwritten.\n\nThis operation can be undone only by restarting the merge." "Si stanno mostrando solo le modifiche con conflitti.\n\n%s sar\u00e0 sovrascritto.\n\nQuesta operazione pu\u00f2 essere modificata solo ricominciando la fusione."
::msgcat::mcset it "File %s seems to have unresolved conflicts, still stage?" "Il file %s sembra contenere conflitti non risolti, preparare per la prossima revisione?"
::msgcat::mcset it "Adding resolution for %s" "La risoluzione dei conflitti per %s \u00e8 preparata per la prossima revisione"
::msgcat::mcset it "Cannot resolve deletion or link conflicts using a tool" "Non \u00e8 possibile risolvere i conflitti per cancellazioni o link con un programma esterno"
::msgcat::mcset it "Conflict file does not exist" "Non esiste un file con conflitti."
::msgcat::mcset it "Not a GUI merge tool: '%s'" "'%s' non \u00e8 una GUI per la risoluzione dei conflitti."
::msgcat::mcset it "Unsupported merge tool '%s'" "Il programma '%s' non \u00e8 supportato"
::msgcat::mcset it "Merge tool is already running, terminate it?" "La risoluzione dei conflitti \u00e8 gi\u00e0 avviata, terminarla?"
::msgcat::mcset it "Error retrieving versions:\n%s" "Errore: revisione non trovata:\n%s"
::msgcat::mcset it "Could not start the merge tool:\n\n%s" "Impossibile avviare la risoluzione dei conflitti:\n\n%s"
::msgcat::mcset it "Running merge tool..." "Avvio del programma per la risoluzione dei conflitti in corso..."
::msgcat::mcset it "Merge tool failed." "Risoluzione dei conflitti non riuscita."
::msgcat::mcset it "Invalid global encoding '%s'" "La codifica dei caratteri '%s' specificata per tutti gli archivi non \u00e8 valida"
::msgcat::mcset it "Invalid repo encoding '%s'" "La codifica dei caratteri '%s' specificata per l'archivio attuale  non \u00e8 valida"
::msgcat::mcset it "Restore Defaults" "Ripristina valori predefiniti"
::msgcat::mcset it "Save" "Salva"
::msgcat::mcset it "%s Repository" "Archivio di %s"
::msgcat::mcset it "Global (All Repositories)" "Tutti gli archivi"
::msgcat::mcset it "User Name" "Nome utente"
::msgcat::mcset it "Email Address" "Indirizzo Email"
::msgcat::mcset it "Summarize Merge Commits" "Riepilogo nelle revisioni di fusione"
::msgcat::mcset it "Merge Verbosity" "Prolissit\u00e0 della fusione"
::msgcat::mcset it "Show Diffstat After Merge" "Mostra statistiche delle differenze dopo la fusione"
::msgcat::mcset it "Use Merge Tool" "Programma da utilizzare per la risoluzione dei conflitti"
::msgcat::mcset it "Trust File Modification Timestamps" "Fidati delle date di modifica dei file"
::msgcat::mcset it "Prune Tracking Branches During Fetch" "Effettua potatura dei duplicati locali di rami remoti durante il recupero"
::msgcat::mcset it "Match Tracking Branches" "Appaia duplicati locali di rami remoti"
::msgcat::mcset it "Blame Copy Only On Changed Files" "Ricerca copie solo nei file modificati"
::msgcat::mcset it "Minimum Letters To Blame Copy On" "Numero minimo di lettere che attivano la ricerca delle copie"
::msgcat::mcset it "Blame History Context Radius (days)" "Giorni di contesto nella cronologia delle annotazioni"
::msgcat::mcset it "Number of Diff Context Lines" "Numero di linee di contesto nelle differenze"
::msgcat::mcset it "Commit Message Text Width" "Larghezza del messaggio di revisione"
::msgcat::mcset it "New Branch Name Template" "Modello per il nome di un nuovo ramo"
::msgcat::mcset it "Default File Contents Encoding" "Codifica predefinita per il contenuto dei file"
::msgcat::mcset it "Change" "Cambia"
::msgcat::mcset it "Spelling Dictionary:" "Lingua dizionario:"
::msgcat::mcset it "Change Font" "Cambia caratteri"
::msgcat::mcset it "Choose %s" "Scegli %s"
::msgcat::mcset it "pt." "pt."
::msgcat::mcset it "Preferences" "Preferenze"
::msgcat::mcset it "Failed to completely save options:" "Impossibile salvare completamente le opzioni:"
::msgcat::mcset it "Remove Remote" "Rimuovi archivio remoto"
::msgcat::mcset it "Prune from" "Effettua potatura da"
::msgcat::mcset it "Fetch from" "Recupera da"
::msgcat::mcset it "Push to" "Propaga verso"
::msgcat::mcset it "Add Remote" "Aggiungi archivio remoto"
::msgcat::mcset it "Add New Remote" "Aggiungi nuovo archivio remoto"
::msgcat::mcset it "Add" "Aggiungi"
::msgcat::mcset it "Remote Details" "Dettagli sull'archivio remoto"
::msgcat::mcset it "Location:" "Posizione:"
::msgcat::mcset it "Further Action" "Altra azione"
::msgcat::mcset it "Fetch Immediately" "Recupera subito"
::msgcat::mcset it "Initialize Remote Repository and Push" "Inizializza l'archivio remoto e propaga"
::msgcat::mcset it "Do Nothing Else Now" "Non fare altro"
::msgcat::mcset it "Please supply a remote name." "Inserire un nome per l'archivio remoto."
::msgcat::mcset it "'%s' is not an acceptable remote name." "'%s' non \u00e8 utilizzabile come nome di archivio remoto."
::msgcat::mcset it "Failed to add remote '%s' of location '%s'." "Impossibile aggiungere l'archivio remoto '%s' posto in '%s'."
::msgcat::mcset it "fetch %s" "recupera da %s"
::msgcat::mcset it "Fetching the %s" "Recupero %s"
::msgcat::mcset it "Do not know how to initialize repository at location '%s'." "Impossibile inizializzare l'archivio posto in '%s'."
::msgcat::mcset it "push %s" "propaga verso %s"
::msgcat::mcset it "Setting up the %s (at %s)" "Imposto %s (in %s)"
::msgcat::mcset it "Delete Branch Remotely" "Elimina ramo remoto"
::msgcat::mcset it "From Repository" "Da archivio"
::msgcat::mcset it "Remote:" "Remoto:"
::msgcat::mcset it "Arbitrary Location:" "Posizione specifica:"
::msgcat::mcset it "Branches" "Rami"
::msgcat::mcset it "Delete Only If" "Elimina solo se"
::msgcat::mcset it "Merged Into:" "Fuso in:"
::msgcat::mcset it "A branch is required for 'Merged Into'." "Si richiede un ramo per 'Fuso in'."
::msgcat::mcset it "The following branches are not completely merged into %s:\n\n - %s" "I rami seguenti non sono stati fusi completamente in %s:\n\n - %s"
::msgcat::mcset it "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "Impossibile verificare una o pi\u00f9 fusioni: mancano le revisioni necessarie. Prova prima a recuperarle da %s."
::msgcat::mcset it "Please select one or more branches to delete." "Scegliere uno o pi\u00f9 rami da cancellare."
::msgcat::mcset it "Deleting branches from %s" "Cancellazione rami da %s"
::msgcat::mcset it "No repository selected." "Nessun archivio selezionato."
::msgcat::mcset it "Scanning %s..." "Analisi in corso %s..."
::msgcat::mcset it "Find:" "Trova:"
::msgcat::mcset it "Next" "Succ"
::msgcat::mcset it "Prev" "Prec"
::msgcat::mcset it "Case-Sensitive" "Distingui maiuscole"
::msgcat::mcset it "Cannot write shortcut:" "Impossibile scrivere shortcut:"
::msgcat::mcset it "Cannot write icon:" "Impossibile scrivere icona:"
::msgcat::mcset it "Unsupported spell checker" "Correttore ortografico non supportato"
::msgcat::mcset it "Spell checking is unavailable" "Correzione ortografica indisponibile"
::msgcat::mcset it "Invalid spell checking configuration" "La configurazione del correttore ortografico non \u00e8 valida"
::msgcat::mcset it "Reverting dictionary to %s." "Il dizionario \u00e8 stato reimpostato su %s."
::msgcat::mcset it "Spell checker silently failed on startup" "Il correttore ortografico ha riportato un errore all'avvio"
::msgcat::mcset it "Unrecognized spell checker" "Correttore ortografico non riconosciuto"
::msgcat::mcset it "No Suggestions" "Nessun suggerimento"
::msgcat::mcset it "Unexpected EOF from spell checker" "Il correttore ortografico ha mandato un EOF inaspettato"
::msgcat::mcset it "Spell Checker Failed" "Errore nel correttore ortografico"
::msgcat::mcset it "No keys found." "Chiavi non trovate."
::msgcat::mcset it "Found a public key in: %s" "Chiave pubblica trovata in: %s"
::msgcat::mcset it "Generate Key" "Crea chiave"
::msgcat::mcset it "Copy To Clipboard" "Copia negli appunti"
::msgcat::mcset it "Your OpenSSH Public Key" "La tua chiave pubblica OpenSSH"
::msgcat::mcset it "Generating..." "Creazione chiave in corso..."
::msgcat::mcset it "Could not start ssh-keygen:\n\n%s" "Impossibile avviare ssh-keygen:\n\n%s"
::msgcat::mcset it "Generation failed." "Errore durante la creazione della chiave."
::msgcat::mcset it "Generation succeeded, but no keys found." "La chiave \u00e8 stata creata con successo, ma non \u00e8 stata trovata."
::msgcat::mcset it "Your key is in: %s" "La chiave \u00e8 in: %s"
::msgcat::mcset it "%s ... %*i of %*i %s (%3i%%)" "%1\$s ... %6\$s: %2\$*i di %4\$*i (%7\$3i%%)"
::msgcat::mcset it "Running %s requires a selected file." "Bisogna selezionare un file prima di eseguire %s."
::msgcat::mcset it "Are you sure you want to run %s?" "Vuoi davvero eseguire %s?"
::msgcat::mcset it "Tool: %s" "Accessorio: %s"
::msgcat::mcset it "Running: %s" "Eseguo: %s"
::msgcat::mcset it "Tool completed successfully: %s" "Il programma esterno \u00e8 terminato con successo: %s"
::msgcat::mcset it "Tool failed: %s" "Il programma esterno ha riportato un errore: %s"
::msgcat::mcset it "Add Tool" "Aggiungi accessorio"
::msgcat::mcset it "Add New Tool Command" "Aggiungi un nuovo comando"
::msgcat::mcset it "Add globally" "Aggiungi per tutti gli archivi"
::msgcat::mcset it "Tool Details" "Dettagli sull'accessorio"
::msgcat::mcset it "Use '/' separators to create a submenu tree:" "Utilizza il separatore '/' per creare un albero di sottomenu:"
::msgcat::mcset it "Command:" "Comando:"
::msgcat::mcset it "Show a dialog before running" "Mostra una finestra di dialogo prima dell'avvio"
::msgcat::mcset it "Ask the user to select a revision (sets \$REVISION)" "Chiedi all'utente di scegliere una revisione (imposta \$REVISION)"
::msgcat::mcset it "Ask the user for additional arguments (sets \$ARGS)" "Chiedi all'utente di fornire argomenti aggiuntivi (imposta \$ARGS)"
::msgcat::mcset it "Don't show the command output window" "Non mostrare la finestra di comando"
::msgcat::mcset it "Run only if a diff is selected (\$FILENAME not empty)" "Avvia solo se \u00e8 selezionata una differenza (\$FILENAME non \u00e8 vuoto)"
::msgcat::mcset it "Please supply a name for the tool." "Bisogna dare un nome all'accessorio."
::msgcat::mcset it "Tool '%s' already exists." "L'accessorio '%s' esiste gi\u00e0."
::msgcat::mcset it "Could not add tool:\n%s" "Impossibile aggiungere l'accessorio:\n\n%s"
::msgcat::mcset it "Remove Tool" "Rimuovi accessorio"
::msgcat::mcset it "Remove Tool Commands" "Rimuovi i comandi accessori"
::msgcat::mcset it "Remove" "Rimuovi"
::msgcat::mcset it "(Blue denotes repository-local tools)" "(Il colore blu indica accessori per l'archivio locale)"
::msgcat::mcset it "Run Command: %s" "Avvia il comando: %s"
::msgcat::mcset it "Arguments" "Argomenti"
::msgcat::mcset it "OK" "OK"
::msgcat::mcset it "Fetching new changes from %s" "Recupero nuove modifiche da %s"
::msgcat::mcset it "remote prune %s" "potatura remota di %s"
::msgcat::mcset it "Pruning tracking branches deleted from %s" "Effettua potatura dei duplicati locali di rami remoti cancellati da %s"
::msgcat::mcset it "Pushing changes to %s" "Propagazione modifiche a %s"
::msgcat::mcset it "Mirroring to %s" "Mirroring verso %s"
::msgcat::mcset it "Pushing %s %s to %s" "Propagazione %s %s a %s"
::msgcat::mcset it "Push Branches" "Propaga rami"
::msgcat::mcset it "Source Branches" "Rami di origine"
::msgcat::mcset it "Destination Repository" "Archivio di destinazione"
::msgcat::mcset it "Transfer Options" "Opzioni di trasferimento"
::msgcat::mcset it "Force overwrite existing branch (may discard changes)" "Sovrascrivi ramo esistente (alcune modifiche potrebbero essere perse)"
::msgcat::mcset it "Use thin pack (for slow network connections)" "Utilizza 'thin pack' (per connessioni lente)"
::msgcat::mcset it "Include tags" "Includi etichette"
