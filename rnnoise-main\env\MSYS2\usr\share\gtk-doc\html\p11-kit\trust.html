<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>trust: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="tools.html" title="Manual Pages">
<link rel="prev" href="pkcs11-conf.html" title="pkcs11.conf">
<link rel="next" href="reference.html" title="API Reference">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="tools.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="pkcs11-conf.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="reference.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="refentry">
<a name="trust"></a><div class="titlepage"></div>
<div class="refnamediv"><table width="100%"><tr>
<td valign="top">
<h2><span class="refentrytitle">trust</span></h2>
<p>trust — Tool for operating on the trust policy store</p>
</td>
<td class="gallery_image" valign="top" align="right"></td>
</tr></table></div>
<div class="refsynopsisdiv">
<h2>Synopsis</h2>
<div class="cmdsynopsis"><p><code class="command">trust list</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">trust extract</code>   --filter=&lt;what&gt;   --format=&lt;type&gt;  /path/to/destination
	</p></div>
<div class="cmdsynopsis"><p><code class="command">trust anchor</code>  /path/to/certificate.crt
	</p></div>
<div class="cmdsynopsis"><p><code class="command">trust dump</code> </p></div>
<div class="cmdsynopsis"><p><code class="command">trust check-format</code>  /path/to/file.p11-kit...
	</p></div>
</div>
<div class="refsect1">
<a name="trust-description"></a><h2>Description</h2>
<p><span class="command"><strong>trust</strong></span> is a command line tool to examine and
	modify the shared trust policy store.</p>
<p>See the various sub commands below. The following global options
	can be used:</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">-v, --verbose</code></span></p></td>
<td><p>Run in verbose mode with debug
			output.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">-q, --quiet</code></span></p></td>
<td><p>Run in quiet mode without warning or
			failure messages.</p></td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="trust-list"></a><h2>List</h2>
<p>List trust policy store items.</p>
<pre class="programlisting">
$ trust list
</pre>
<p>List information about the various items in the trust policy store.
	Each item is listed with it's PKCS#11 URI and some descriptive information.</p>
<p>You can specify the following options to control what to list.</p>
<dt><span class="term"><code class="option">--filter=&lt;what&gt;</code></span></dt>
<dd>
<p>Specifies what certificates to extract. You can specify the following values:
		</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">ca-anchors</code></span></p></td>
<td><p>Certificate anchors</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">trust-policy</code></span></p></td>
<td><p>Anchors and blocklist (default)</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">blocklist</code></span></p></td>
<td><p>Distrusted certificates</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">certificates</code></span></p></td>
<td><p>All certificates</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">pkcs11:object=xx</code></span></p></td>
<td><p>A PKCS#11 URI to filter with</p></td>
</tr>
</tbody>
</table></div>
<p>
		</p>
<p>If an output format is chosen that cannot support type what has been
		specified by the filter, a message will be printed.</p>
<p>None of the available formats support storage of blocklist entries
		that do not contain a full certificate. Thus any certificates distrusted by
		their issuer and serial number alone, are not included in the extracted
		blocklist.</p>
</dd>
<dt><span class="term"><code class="option">--purpose=&lt;usage&gt;</code></span></dt>
<dd>
<p>Limit to certificates usable for the given purpose
		You can specify one of the following values:
		</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">server-auth</code></span></p></td>
<td><p>For authenticating servers</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">client-auth</code></span></p></td>
<td><p>For authenticating clients</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">email</code></span></p></td>
<td><p>For email protection</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">code-signing</code></span></p></td>
<td><p>For authenticated signed code</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">1.2.3.4.5...</code></span></p></td>
<td><p>An arbitrary purpose OID</p></td>
</tr>
</tbody>
</table></div>
<p>
		</p>
</dd>
</div>
<div class="refsect1">
<a name="trust-anchor"></a><h2>Anchor</h2>
<p>Store or remove trust anchors.</p>
<pre class="programlisting">
$ trust anchor /path/to/certificate.crt
$ trust anchor --remove /path/to/certificate.crt
$ trust anchor --remove "pkcs11:id=%AA%BB%CC%DD%EE;type=cert"
</pre>
<p>Store or remove trust anchors in the trust policy store. These are
	usually root certificate authorities.</p>
<p>Specify either the <code class="option">--store</code> or <code class="option">--remove</code>
	operations. If no operation is specified then <code class="option">--store</code> is
	assumed.</p>
<p>When storing, one or more certificate files are expected on the
	command line. These are stored as anchors, unless they are already
	present.</p>
<p>When removing an anchor, either specify certificate files or
	PKCS#11 URI's on the command line. Matching anchors will be removed.</p>
<p>It may be that this command needs to be run as root in order to
	modify the system trust policy store, if no user specific store is
	available.</p>
<p>You can specify the following options.</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">--remove</code></span></p></td>
<td><p>Remove one or more anchors from the trust
			policy store. Specify certificate files or PKCS#11 URI's
			on the command line.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--store</code></span></p></td>
<td><p>Store one or more anchors to the trust
			policy store. Specify certificate files on the command
			line.</p></td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="trust-extract"></a><h2>Extract</h2>
<p>Extract trust policy from the shared trust policy store.</p>
<pre class="programlisting">
$ trust extract --format=x509-directory --filter=ca-anchors /path/to/directory
</pre>
<p>You can specify the following options to control what to extract.
	The <code class="option">--filter</code> and <code class="option">--format</code> arguments
	should be specified. By default this command will not overwrite the
	destination file or directory.</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">--comment</code></span></p></td>
<td><p>Add identifying comments to PEM bundle output files
			before each certificate.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--filter=&lt;what&gt;</code></span></p></td>
<td>
<p>Specifies what certificates to extract. You can specify the following values:
			</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">ca-anchors</code></span></p></td>
<td><p>Certificate anchors (default)</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">trust-policy</code></span></p></td>
<td><p>Anchors and blocklist</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">blocklist</code></span></p></td>
<td><p>Distrusted certificates</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">certificates</code></span></p></td>
<td><p>All certificates</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">pkcs11:object=xx</code></span></p></td>
<td><p>A PKCS#11 URI</p></td>
</tr>
</tbody>
</table></div>
<p>
			</p>
<p>If an output format is chosen that cannot support type what has been
			specified by the filter, a message will be printed.</p>
<p>None of the available formats support storage of blocklist entries
			that do not contain a full certificate. Thus any certificates distrusted by
			their issuer and serial number alone, are not included in the extracted
			blocklist.</p>
</td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--format=&lt;type&gt;</code></span></p></td>
<td>
<p>The format of the destination file or directory.
			You can specify one of the following values:
			</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">x509-file</code></span></p></td>
<td><p>DER X.509 certificate file</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">x509-directory</code></span></p></td>
<td><p>directory of X.509 certificates</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">pem-bundle</code></span></p></td>
<td><p>File containing one or more certificate PEM blocks</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">pem-directory</code></span></p></td>
<td><p>Directory of PEM files each containing one certificate</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">pem-directory-hash</code></span></p></td>
<td><p>Directory of PEM files each containing one certificate, with hash symlinks</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">openssl-bundle</code></span></p></td>
<td><p>OpenSSL specific PEM bundle of certificates</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">openssl-directory</code></span></p></td>
<td><p>Directory of OpenSSL specific PEM files</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">java-cacerts</code></span></p></td>
<td><p>Java keystore 'cacerts' certificate bundle</p></td>
</tr>
</tbody>
</table></div>
<p>
			</p>
</td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--overwrite</code></span></p></td>
<td><p>Overwrite output file or directory.</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">--purpose=&lt;usage&gt;</code></span></p></td>
<td>
<p>Limit to certificates usable for the given purpose
			You can specify one of the following values:
			</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">server-auth</code></span></p></td>
<td><p>For authenticating servers</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">client-auth</code></span></p></td>
<td><p>For authenticating clients</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">email</code></span></p></td>
<td><p>For email protection</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">code-signing</code></span></p></td>
<td><p>For authenticated signed code</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">1.2.3.4.5...</code></span></p></td>
<td><p>An arbitrary purpose OID</p></td>
</tr>
</tbody>
</table></div>
<p>
			</p>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="trust-extract-compat"></a><h2>Extract Compat</h2>
<p>Extract compatibility trust certificate bundles.</p>
<pre class="programlisting">
$ trust extract-compat
</pre>
<p>OpenSSL, Java and some versions of GnuTLS cannot currently read
	trust information directly from the trust policy store. This command
	extracts trust information such as certificate anchors for use by
	these libraries.</p>
<p>What this command does, and where it extracts the files is
	distribution or site specific. Packagers or administrators are expected
	customize this command.</p>
</div>
<div class="refsect1">
<a name="trust-dump"></a><h2>Dump</h2>
<p>Dump PKCS#11 items in the various tokens.</p>
<pre class="programlisting">
$ trust dump
</pre>
<p>Dump information about the various PKCS#11 items in the tokens.
	Each item is dumped with it's PKCS#11 URI and information in the .p11-kit
	persistence format.</p>
<p>You can specify the following options to control what to dump.</p>
<dt><span class="term"><code class="option">--filter=&lt;what&gt;</code></span></dt>
<dd>
<p>Specifies what certificates to extract. You can specify the following values:
		</p>
<div class="variablelist"><table border="0" class="variablelist">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="option">all</code></span></p></td>
<td><p>All objects. This is the default</p></td>
</tr>
<tr>
<td><p><span class="term"><code class="option">pkcs11:object=xx</code></span></p></td>
<td><p>A PKCS#11 URI to filter with</p></td>
</tr>
</tbody>
</table></div>
<p>
		</p>
</dd>
</div>
<div class="refsect1">
<a name="trust-check-format"></a><h2>Check Format</h2>
<p>Check the format of .p11-kit files.</p>
<pre class="programlisting">
$ trust check-format /path/to/file.p11-kit...
</pre>
<p>Administrators sometimes need to write a custom .p11-kit file to amend
	the trust information. This is an error prone process as the file format is
	mainly for machine processing. Administrators can use this command to check
	whether a file has a correct .p11-kit format.</p>
<p>This command takes an arbitrary number of files as an input. Each file
	is then analysed and any mismatch with the .p11-kit format is reported on the
	standard error output. After the file is processed a check result is printed
	on the standard output.</p>
</div>
<div class="refsect1">
<a name="trust-bugs"></a><h2>Bugs</h2>
<p>
    Please send bug reports to either the distribution bug tracker
    or the upstream bug tracker at
    <a class="ulink" href="https://github.com/p11-glue/p11-kit/issues/" target="_top">https://github.com/p11-glue/p11-kit/issues/</a>.
  </p>
</div>
<div class="refsect1">
<a name="trust-see-also"></a><h2>See also</h2>
<span class="simplelist"><span class="citerefentry"><span class="refentrytitle">p11-kit</span>(8)</span></span><p>An explanatory document about storing trust policy:
    <a class="ulink" href="https://p11-glue.github.io/p11-glue/doc/storing-trust-policy/" target="_top">https://p11-glue.github.io/p11-glue/doc/storing-trust-policy/</a></p>
<p>
    Further details available in the p11-kit online documentation at
    <a class="ulink" href="https://p11-glue.github.io/p11-glue/p11-kit/manual/" target="_top">https://p11-glue.github.io/p11-glue/p11-kit/manual/</a>.
  </p>
</div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>