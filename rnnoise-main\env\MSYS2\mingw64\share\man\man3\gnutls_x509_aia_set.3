.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_aia_set" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_aia_set \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_aia_set(gnutls_x509_aia_t " aia ", const char * " oid ", unsigned " san_type ", const gnutls_datum_t * " san ");"
.SH ARGUMENTS
.IP "gnutls_x509_aia_t aia" 12
The authority info access
.IP "const char * oid" 12
the type of data.
.IP "unsigned san_type" 12
The type of the name (of \fBgnutls_subject_alt_names_t\fP)
.IP "const gnutls_datum_t * san" 12
The alternative name data
.SH "DESCRIPTION"
This function will store the specified alternative name in
the  \fIaia\fP type. 

Typically the value for  \fIoid\fP should be \fBGNUTLS_OID_AD_OCSP\fP, or
\fBGNUTLS_OID_AD_CAISSUERS\fP.

Since version 3.5.7 the \fBGNUTLS_SAN_RFC822NAME\fP, and \fBGNUTLS_SAN_DNSNAME\fP,
are converted to ACE format when necessary.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0), otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
