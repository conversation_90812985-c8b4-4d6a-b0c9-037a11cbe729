.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_crt_is_known" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_crt_is_known \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "unsigned gnutls_pkcs11_crt_is_known(const char * " url ", gnutls_x509_crt_t " cert ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const char * url" 12
A PKCS 11 url identifying a token
.IP "gnutls_x509_crt_t cert" 12
is the certificate to find issuer for
.IP "unsigned int flags" 12
Use zero or flags from \fBGNUTLS_PKCS11_OBJ_FLAG\fP.
.SH "DESCRIPTION"
This function will check whether the provided certificate is stored
in the specified token. This is useful in combination with 
\fBGNUTLS_PKCS11_OBJ_FLAG_RETRIEVE_TRUSTED\fP or
\fBGNUTLS_PKCS11_OBJ_FLAG_RETRIEVE_DISTRUSTED\fP,
to check whether a CA is present or a certificate is distrusted in
a trust PKCS \fB11\fP module.

This function can be used with a  \fIurl\fP of "pkcs11:", and in that case all modules
will be searched. To restrict the modules to the marked as trusted in p11\-kit
use the \fBGNUTLS_PKCS11_OBJ_FLAG_PRESENT_IN_TRUSTED_MODULE\fP flag.

Note that the flag \fBGNUTLS_PKCS11_OBJ_FLAG_RETRIEVE_DISTRUSTED\fP is
specific to p11\-kit trust modules.
.SH "RETURNS"
If the certificate exists non\-zero is returned, otherwise zero.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
