#IncludeRegexLine: ^[ 	]*#[ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

/ssd/gaoyongyu/workspace/rnn_gao_new/src/_kiss_fft_guts.h
kiss_fft.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/kiss_fft.h
arch.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arch.h
arm/kiss_fft_armv4.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arm/kiss_fft_armv4.h
arm/kiss_fft_armv5e.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arm/kiss_fft_armv5e.h
mips/kiss_fft_mipsr1.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/mips/kiss_fft_mipsr1.h

/ssd/gaoyongyu/workspace/rnn_gao_new/src/arch.h
opus_types.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/opus_types.h
common.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/common.h
stdio.h
-
stdlib.h
-
fixed_debug.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/fixed_debug.h
fixed_generic.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/fixed_generic.h
arm/fixed_arm64.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arm/fixed_arm64.h
arm/fixed_armv5e.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arm/fixed_armv5e.h
arm/fixed_armv4.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arm/fixed_armv4.h
fixed_bfin.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/fixed_bfin.h
fixed_c5x.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/fixed_c5x.h
fixed_c6x.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/fixed_c6x.h

/ssd/gaoyongyu/workspace/rnn_gao_new/src/celt_lpc.c
config.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/config.h
celt_lpc.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/celt_lpc.h
arch.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arch.h
common.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/common.h
pitch.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/pitch.h

/ssd/gaoyongyu/workspace/rnn_gao_new/src/celt_lpc.h
arch.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arch.h
common.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/common.h
x86/celt_lpc_sse.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/x86/celt_lpc_sse.h

/ssd/gaoyongyu/workspace/rnn_gao_new/src/common.h
stdlib.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/stdlib.h
string.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/string.h

/ssd/gaoyongyu/workspace/rnn_gao_new/src/denoise.c
config.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/config.h
stdlib.h
-
string.h
-
stdio.h
-
kiss_fft.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/kiss_fft.h
common.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/common.h
math.h
-
rnnoise.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnnoise.h
pitch.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/pitch.h
arch.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arch.h
rnn.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnn.h
rnn_data.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnn_data.h
dirent.h
-
time.h
-

/ssd/gaoyongyu/workspace/rnn_gao_new/src/denoise16.c
config.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/config.h
stdlib.h
-
string.h
-
stdio.h
-
kiss_fft.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/kiss_fft.h
common.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/common.h
math.h
-
rnnoise.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnnoise.h
pitch.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/pitch.h
arch.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arch.h
rnn.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnn.h
rnn_data.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnn_data.h

/ssd/gaoyongyu/workspace/rnn_gao_new/src/kiss_fft.c
config.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/config.h
_kiss_fft_guts.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/_kiss_fft_guts.h

/ssd/gaoyongyu/workspace/rnn_gao_new/src/kiss_fft.h
stdlib.h
-
math.h
-
arch.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arch.h
stdlib.h
-
xmmintrin.h
-
arch.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arch.h
arm/fft_arm.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arm/fft_arm.h

/ssd/gaoyongyu/workspace/rnn_gao_new/src/opus_types.h
stdint.h
-
_G_config.h
-
sys/types.h
-
sys/types.h
-
inttypes.h
-

/ssd/gaoyongyu/workspace/rnn_gao_new/src/pitch.c
config.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/config.h
pitch.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/pitch.h
common.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/common.h
celt_lpc.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/celt_lpc.h
math.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/math.h

/ssd/gaoyongyu/workspace/rnn_gao_new/src/pitch.h
arch.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arch.h

/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnn.c
config.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/config.h
math.h
-
opus_types.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/opus_types.h
common.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/common.h
arch.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/arch.h
tansig_table.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/tansig_table.h
rnn.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnn.h
rnn_data.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnn_data.h
stdio.h
-

/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnn.h
opus_types.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/opus_types.h

/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnn_data.c
config.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/config.h
rnn.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnn.h

/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnn_data.h
rnn.h
/ssd/gaoyongyu/workspace/rnn_gao_new/src/rnn.h

/ssd/gaoyongyu/workspace/rnn_gao_new/src/tansig_table.h

include/rnnoise.h

