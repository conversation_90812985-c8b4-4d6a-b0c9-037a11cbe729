<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>Other UNIX&#174; system interfaces, not in POSIX.1-2024, or deprecated:</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="compatibility.html" title="Chapter&#160;1.&#160;Compatibility"><link rel="prev" href="std-iso.html" title="System interfaces not in POSIX but compatible with ISO C requirements:"><link rel="next" href="std-notimpl.html" title="NOT implemented system interfaces from the Single UNIX&#174; Specification Version 5:"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">Other UNIX&#174; system interfaces, not in POSIX.1-2024, or deprecated:</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="std-iso.html">Prev</a>&#160;</td><th width="60%" align="center">Chapter&#160;1.&#160;Compatibility</th><td width="20%" align="right">&#160;<a accesskey="n" href="std-notimpl.html">Next</a></td></tr></table><hr></div><div class="sect1"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="std-deprec"></a>Other UNIX&#174; system interfaces, not in POSIX.1-2024, or deprecated:</h2></div></div></div><pre class="screen">
    _longjmp			(SUSv4)
    _setjmp			(SUSv4)
    _tolower			(SUSv4)
    _toupper			(SUSv4)
    acl_add_perm		(POSIX.1e draft)
    acl_calc_mask		(POSIX.1e draft)
    acl_check			(libacl)
    acl_clear_perms		(POSIX.1e draft)
    acl_cmp			(libacl)
    acl_copy_entry		(POSIX.1e draft)
    acl_copy_ext		(POSIX.1e draft)
    acl_copy_int		(POSIX.1e draft)
    acl_create_entry		(POSIX.1e draft)
    acl_delete_def_file		(POSIX.1e draft)
    acl_delete_entry		(POSIX.1e draft)
    acl_delete_perm		(POSIX.1e draft)
    acl_dup			(POSIX.1e draft)
    acl_entries			(libacl)
    acl_equiv_mode		(libacl)
    acl_error			(POSIX.1e draft)
    acl_extended_fd		(libacl)
    acl_extended_file		(libacl)
    acl_extended_file_nofollow	(libacl)
    acl_free			(POSIX.1e draft)
    acl_from_mode		(libacl)
    acl_from_text		(POSIX.1e draft)
    acl_get_entry		(POSIX.1e draft)
    acl_get_fd			(POSIX.1e draft)
    acl_get_file		(POSIX.1e draft)
    acl_get_perm		(libacl)
    acl_get_permset		(POSIX.1e draft)
    acl_get_qualifier		(POSIX.1e draft)
    acl_get_tag_type		(POSIX.1e draft)
    acl_init			(POSIX.1e draft)
    acl_set_fd			(POSIX.1e draft)
    acl_set_file		(POSIX.1e draft)
    acl_set_permset		(POSIX.1e draft)
    acl_set_qualifier		(POSIX.1e draft)
    acl_set_tag_type		(POSIX.1e draft)
    acl_size			(POSIX.1e draft)
    acl_to_any_text		(libacl)
    acl_to_text			(POSIX.1e draft)
    acl_valid			(POSIX.1e draft)
    atoff			(AIX)
    bcmp			(POSIX.1-2001, SUSv3)
    bcopy			(SUSv3)
    bzero			(SUSv3)
    chroot			(SUSv2 - see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    clock_setres		(QNX, VxWorks - see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    cuserid			(POSIX.1-1988, SUSv2)
    ecvt			(SUSv3)
    endutent			(XPG2)
    fcvt			(SUSv3)
    ftime			(SUSv3)
    ftw				(SUSv4)
    gcvt			(SUSv3)
    getcontext			(SUSv3)
    gethostbyaddr		(SUSv3)
    gethostbyname		(SUSv3)
    gethostbyname2		(first defined in BIND 4.9.4)
    getitimer			(SUSv4 - see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    getpass			(SUSv2)
    gets			(SUSv4)
    gettimeofday		(SUSv4)
    getutent			(XPG2)
    getutid			(XPG2)
    getutline			(XPG2)
    getw			(SVID)
    getwd			(SUSv3)
    h_errno			(SUSv3)
    index			(SUSv3)
    ioctl			(SUSv4)
    isascii			(SUSv4)
    makecontext			(SUSv3)
    mallinfo			(SVID)
    mallopt			(SVID)
    mktemp			(SUSv3)
    on_exit			(SunOS)
    pthread_attr_getstackaddr	(SUSv3)
    pthread_attr_setstackaddr	(SUSv3)
    pthread_continue		(XPG2)
    pthread_getconcurrency	(SUSv4)
    pthread_getsequence_np	(Tru64)
    pthread_setconcurrency	(SUSv4)
    pthread_suspend		(XPG2)
    pthread_yield		(POSIX.1c drafts)
    pututline			(XPG2)
    putw			(SVID)
    rand_r			(SUSv4)
    rindex			(SUSv3)
    scalb			(SUSv3)
    setcontext			(SUSv3)
    setitimer			(SUSv4 - see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    setpgrp			(SUSv4)
    setutent			(XPG2)
    sighold			(SUSv4)
    sigignore			(SUSv4)
    siginterrupt		(SUSv4)
    sigpause			(SUSv4 - see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
    sigrelse			(SUSv4)
    sigset			(SUSv4)
    stime			(SVID)
    swapcontext			(SUSv3)
    sys_errlist			(BSD)
    sys_nerr			(BSD)
    sys_siglist			(BSD)
    tempnam			(SUSv4)
    toascii			(SUSv4)
    ttyslot			(SUSv2)
    ualarm			(SUSv3)
    usleep			(SUSv3)
    utime			(SUSv4)
    utmpname			(XPG2)
    vfork			(SUSv3 - see <a class="xref" href="std-notes.html" title="Implementation Notes">the section called &#8220;Implementation Notes&#8221;</a>)
</pre></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="std-iso.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="compatibility.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="std-notimpl.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">System interfaces not in POSIX but compatible with ISO C requirements:&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;NOT implemented system interfaces from the Single UNIX&#174; Specification Version 5:</td></tr></table></div></body></html>
