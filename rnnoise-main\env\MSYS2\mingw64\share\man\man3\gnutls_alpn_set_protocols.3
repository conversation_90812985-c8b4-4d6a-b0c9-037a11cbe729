.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_alpn_set_protocols" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_alpn_set_protocols \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_alpn_set_protocols(gnutls_session_t " session ", const gnutls_datum_t * " protocols ", unsigned " protocols_size ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "const gnutls_datum_t * protocols" 12
is the protocol names to add.
.IP "unsigned protocols_size" 12
the number of protocols to add.
.IP "unsigned int flags" 12
zero or a sequence of \fBgnutls_alpn_flags_t\fP
.SH "DESCRIPTION"
This function is to be used by both clients and servers, to declare
the supported ALPN protocols, which are used during negotiation with peer.

See \fBgnutls_alpn_flags_t\fP description for the documentation of available
flags.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.

Since 3.2.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
