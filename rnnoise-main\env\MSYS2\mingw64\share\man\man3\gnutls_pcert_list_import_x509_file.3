.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pcert_list_import_x509_file" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pcert_list_import_x509_file \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_pcert_list_import_x509_file(gnutls_pcert_st * " pcert_list ", unsigned * " pcert_list_size ", const char * " file ", gnutls_x509_crt_fmt_t " format ", gnutls_pin_callback_t " pin_fn ", void * " pin_fn_userdata ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pcert_st * pcert_list" 12
The structures to store the certificates; must not contain initialized \fBgnutls_pcert_st\fP structures.
.IP "unsigned * pcert_list_size" 12
Initially must hold the maximum number of certs. It will be updated with the number of certs available.
.IP "const char * file" 12
A file or supported URI with the certificates to load
.IP "gnutls_x509_crt_fmt_t format" 12
\fBGNUTLS_X509_FMT_DER\fP or \fBGNUTLS_X509_FMT_PEM\fP if a file is given
.IP "gnutls_pin_callback_t pin_fn" 12
a PIN callback if not globally set
.IP "void * pin_fn_userdata" 12
parameter for the PIN callback
.IP "unsigned int flags" 12
zero or flags from \fBgnutls_certificate_import_flags\fP
.SH "DESCRIPTION"
This convenience function will import a certificate chain from the given
file or supported URI to \fBgnutls_pcert_st\fP structures. The structures
must be deinitialized afterwards using \fBgnutls_pcert_deinit()\fP.

This function will always return a sorted certificate chain.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value; if the  \fIpcert\fP list doesn't have enough space
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP will be returned.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
