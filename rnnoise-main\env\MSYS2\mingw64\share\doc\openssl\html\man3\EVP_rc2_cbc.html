<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_rc2_cbc</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_rc2_cbc, EVP_rc2_cfb, EVP_rc2_cfb64, EVP_rc2_ecb, EVP_rc2_ofb, EVP_rc2_40_cbc, EVP_rc2_64_cbc - EVP RC2 cipher</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

const EVP_CIPHER *EVP_rc2_cbc(void);
const EVP_CIPHER *EVP_rc2_cfb(void);
const EVP_CIPHER *EVP_rc2_cfb64(void);
const EVP_CIPHER *EVP_rc2_ecb(void);
const EVP_CIPHER *EVP_rc2_ofb(void);
const EVP_CIPHER *EVP_rc2_40_cbc(void);
const EVP_CIPHER *EVP_rc2_64_cbc(void);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The RC2 encryption algorithm for EVP.</p>

<dl>

<dt id="EVP_rc2_cbc-EVP_rc2_cfb-EVP_rc2_cfb64-EVP_rc2_ecb-EVP_rc2_ofb">EVP_rc2_cbc(), EVP_rc2_cfb(), EVP_rc2_cfb64(), EVP_rc2_ecb(), EVP_rc2_ofb()</dt>
<dd>

<p>RC2 encryption algorithm in CBC, CFB, ECB and OFB modes respectively. This is a variable key length cipher with an additional parameter called &quot;effective key bits&quot; or &quot;effective key length&quot;. By default both are set to 128 bits.</p>

</dd>
<dt id="EVP_rc2_40_cbc-EVP_rc2_64_cbc">EVP_rc2_40_cbc(), EVP_rc2_64_cbc()</dt>
<dd>

<p>RC2 algorithm in CBC mode with a default key length and effective key length of 40 and 64 bits.</p>

<p>WARNING: these functions are obsolete. Their usage should be replaced with the EVP_rc2_cbc(), EVP_CIPHER_CTX_set_key_length() and EVP_CIPHER_CTX_ctrl() functions to set the key length and effective key length.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>Developers should be aware of the negative performance implications of calling these functions multiple times and should consider using <a href="../man3/EVP_CIPHER_fetch.html">EVP_CIPHER_fetch(3)</a> with <a href="../man7/EVP_CIPHER-RC2.html">EVP_CIPHER-RC2(7)</a> instead. See <a href="../man7/crypto.html">&quot;Performance&quot; in crypto(7)</a> for further information.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>These functions return an <b>EVP_CIPHER</b> structure that contains the implementation of the symmetric cipher. See <a href="../man3/EVP_CIPHER_meth_new.html">EVP_CIPHER_meth_new(3)</a> for details of the <b>EVP_CIPHER</b> structure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/evp.html">evp(7)</a>, <a href="../man3/EVP_EncryptInit.html">EVP_EncryptInit(3)</a>, <a href="../man3/EVP_CIPHER_meth_new.html">EVP_CIPHER_meth_new(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


