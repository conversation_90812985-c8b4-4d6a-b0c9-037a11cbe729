.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ext_get_data" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ext_get_data \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_ext_get_data(gnutls_session_t " session ", unsigned " tls_id ", gnutls_ext_priv_data_t * " data ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
a \fBgnutls_session_t\fP opaque pointer
.IP "unsigned tls_id" 12
the numeric id of the extension
.IP "gnutls_ext_priv_data_t * data" 12
a pointer to the private data to retrieve
.SH "DESCRIPTION"
This function retrieves any data previously stored with \fBgnutls_ext_set_data()\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
