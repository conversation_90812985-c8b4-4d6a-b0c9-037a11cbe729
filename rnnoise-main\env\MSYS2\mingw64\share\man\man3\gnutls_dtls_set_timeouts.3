.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dtls_set_timeouts" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dtls_set_timeouts \- API function
.SH SYNOPSIS
.B #include <gnutls/dtls.h>
.sp
.BI "void gnutls_dtls_set_timeouts(gnutls_session_t " session ", unsigned int " retrans_timeout ", unsigned int " total_timeout ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned int retrans_timeout" 12
The time at which a retransmission will occur in milliseconds
.IP "unsigned int total_timeout" 12
The time at which the connection will be aborted, in milliseconds.
.SH "DESCRIPTION"
This function will set the timeouts required for the DTLS handshake
protocol. The retransmission timeout is the time after which a
message from the peer is not received, the previous messages will
be retransmitted. The total timeout is the time after which the
handshake will be aborted with \fBGNUTLS_E_TIMEDOUT\fP.

The DTLS protocol recommends the values of 1 sec and 60 seconds
respectively, and these are the default values.

To disable retransmissions set a  \fIretrans_timeout\fP larger than the  \fItotal_timeout\fP .
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
