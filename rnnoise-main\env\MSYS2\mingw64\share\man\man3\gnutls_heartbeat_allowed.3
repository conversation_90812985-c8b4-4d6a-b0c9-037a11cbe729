.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_heartbeat_allowed" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_heartbeat_allowed \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned gnutls_heartbeat_allowed(gnutls_session_t " session ", unsigned int " type ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned int type" 12
one of \fBGNUTLS_HB_LOCAL_ALLOWED_TO_SEND\fP and \fBGNUTLS_HB_PEER_ALLOWED_TO_SEND\fP
.SH "DESCRIPTION"
This function will check whether heartbeats are allowed
to be sent or received in this session. 
.SH "RETURNS"
Non zero if heartbeats are allowed.
.SH "SINCE"
3.1.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
