.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_psk_set_server_known_dh_params" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_psk_set_server_known_dh_params \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_psk_set_server_known_dh_params(gnutls_psk_server_credentials_t " res ", gnutls_sec_param_t " sec_param ");"
.SH ARGUMENTS
.IP "gnutls_psk_server_credentials_t res" 12
is a gnutls_psk_server_credentials_t type
.IP "gnutls_sec_param_t sec_param" 12
is an option of the \fBgnutls_sec_param_t\fP enumeration
.SH "DESCRIPTION"
This function will set the Diffie\-Hellman parameters for a
PSK server to use. These parameters will be used in
Ephemeral Diffie\-Hellman cipher suites and will be selected from
the FFDHE set of RFC7919 according to the security level provided.
.SH "DEPRECATED"
This function is unnecessary and discouraged on GnuTLS 3.6.0
or later. Since 3.6.0, DH parameters are negotiated
following RFC7919.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.5.6
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
