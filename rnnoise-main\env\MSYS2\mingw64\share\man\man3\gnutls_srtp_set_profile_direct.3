.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_srtp_set_profile_direct" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_srtp_set_profile_direct \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_srtp_set_profile_direct(gnutls_session_t " session ", const char * " profiles ", const char ** " err_pos ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "const char * profiles" 12
is a string that contains the supported SRTP profiles,
separated by colons.
.IP "const char ** err_pos" 12
In case of an error this will have the position in the string the error occurred, may be NULL.
.SH "DESCRIPTION"
This function is to be used by both clients and servers, to declare
what SRTP profiles they support, to negotiate with the peer.
.SH "RETURNS"
On syntax error \fBGNUTLS_E_INVALID_REQUEST\fP is returned,
\fBGNUTLS_E_SUCCESS\fP on success, or an error code.

Since 3.1.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
