.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_get_keylog_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_get_keylog_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_keylog_func gnutls_session_get_keylog_function(const gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "const gnutls_session_t session" 12
is \fBgnutls_session_t\fP type
.SH "DESCRIPTION"
This function will return the callback function set using
\fBgnutls_session_set_keylog_function()\fP.
.SH "RETURNS"
The function set or \fBNULL\fP otherwise.
.SH "SINCE"
3.6.13
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
