.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_cidr_to_rfc5280" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_cidr_to_rfc5280 \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_cidr_to_rfc5280(const char * " cidr ", gnutls_datum_t * " cidr_rfc5280 ");"
.SH ARGUMENTS
.IP "const char * cidr" 12
CIDR in RFC4632 format (IP/prefix), null\-terminated
.IP "gnutls_datum_t * cidr_rfc5280" 12
CIDR range converted to RFC5280 format
.SH "DESCRIPTION"
This function will convert text CIDR range with prefix (such as '10.0.0.0/8')
to RFC5280 (IP address in network byte order followed by its network mask).
Works for both IPv4 and IPv6.

The resulting object is directly usable for IP name constraints usage,
for example in functions \fBgnutls_x509_name_constraints_add_permitted\fP
or \fBgnutls_x509_name_constraints_add_excluded\fP.

The data in datum needs to be deallocated using \fBgnutls_free()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a negative error value.
.SH "SINCE"
3.5.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
