/*
 * Copyright (C) 2023 Mohamad Al-Jaf
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

#ifndef DO_NO_IMPORTS
import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.devices.enumeration.idl";
/* import "windows.phone.idl"; */
import "windows.ui.idl";
import "windows.ui.core.idl";
/* import "windows.ui.popups.idl"; */
/* import "windows.ui.windowmanagement.idl"; */
#endif

namespace Windows.UI.ViewManagement
{
    typedef enum HandPreference HandPreference;
    typedef enum UIColorType UIColorType;
    typedef enum UIElementType UIElementType;
    typedef enum UserInteractionMode UserInteractionMode;

    interface IUISettings;
    interface IUISettings2;
    interface IUISettings3;
    interface IUISettings4;
    interface IUISettings5;
    interface IUISettings6;
    interface IUISettingsAnimationsEnabledChangedEventArgs;
    interface IUISettingsAutoHideScrollBarsChangedEventArgs;
    interface IUISettingsMessageDurationChangedEventArgs;

    interface IUIViewSettings;
    interface IUIViewSettingsStatics;

    interface IInputPane;
    interface IInputPane2;
    interface IInputPaneControl;
    interface IInputPaneVisibilityEventArgs;
    interface IInputPaneStatics;
    interface IInputPaneStatics2;

    runtimeclass UISettings;
    runtimeclass UISettingsAnimationsEnabledChangedEventArgs;
    runtimeclass UISettingsAutoHideScrollBarsChangedEventArgs;
    runtimeclass UISettingsMessageDurationChangedEventArgs;
    runtimeclass UIViewSettings;
    runtimeclass InputPane;
    runtimeclass InputPaneVisibilityEventArgs;

    declare {
        interface Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.UISettings *, IInspectable *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.UISettings *, Windows.UI.ViewManagement.UISettingsAnimationsEnabledChangedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.UISettings *, Windows.UI.ViewManagement.UISettingsAutoHideScrollBarsChangedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.UISettings *, Windows.UI.ViewManagement.UISettingsMessageDurationChangedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.InputPane*, Windows.UI.ViewManagement.InputPaneVisibilityEventArgs*>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum HandPreference
    {
        LeftHanded  = 0,
        RightHanded = 1,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum UIColorType
    {
        Background   = 0,
        Foreground   = 1,
        AccentDark3  = 2,
        AccentDark2  = 3,
        AccentDark1  = 4,
        Accent       = 5,
        AccentLight1 = 6,
        AccentLight2 = 7,
        AccentLight3 = 8,
        Complement   = 9,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum UIElementType
    {
        ActiveCaption        = 0,
        Background           = 1,
        ButtonFace           = 2,
        ButtonText           = 3,
        CaptionText          = 4,
        GrayText             = 5,
        Highlight            = 6,
        HighlightText        = 7,
        Hotlight             = 8,
        InactiveCaption      = 9,
        InactiveCaptionText  = 10,
        Window               = 11,
        WindowText           = 12,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        AccentColor          = 1000,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        TextHigh             = 1001,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        TextMedium           = 1002,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        TextLow              = 1003,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        TextContrastWithHigh = 1004,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        NonTextHigh          = 1005,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        NonTextMediumHigh    = 1006,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        NonTextMedium        = 1007,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        NonTextMediumLow     = 1008,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        NonTextLow           = 1009,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        PageBackground       = 1010,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        PopupBackground      = 1011,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        OverlayOutsidePopup  = 1012,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum UserInteractionMode
    {
        Mouse = 0,
        Touch = 1,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.ViewManagement.UISettings),
        uuid(85361600-1c63-4627-bcb1-3a89e0bc9c55)
    ]
    interface IUISettings : IInspectable
    {
        [propget] HRESULT HandPreference([out, retval] Windows.UI.ViewManagement.HandPreference *value);
        [propget] HRESULT CursorSize([out, retval] Windows.Foundation.Size *value);
        [propget] HRESULT ScrollBarSize([out, retval] Windows.Foundation.Size *value);
        [propget] HRESULT ScrollBarArrowSize([out, retval] Windows.Foundation.Size *value);
        [propget] HRESULT ScrollBarThumbBoxSize([out, retval] Windows.Foundation.Size *value);
        [propget] HRESULT MessageDuration([out, retval] UINT32 *value);
        [propget] HRESULT AnimationsEnabled([out, retval] boolean *value);
        [propget] HRESULT CaretBrowsingEnabled([out, retval] boolean *value);
        [propget] HRESULT CaretBlinkRate([out, retval] UINT32 *value);
        [propget] HRESULT CaretWidth([out, retval] UINT32 *value);
        [propget] HRESULT DoubleClickTime([out, retval] UINT32 *value);
        [propget] HRESULT MouseHoverTime([out, retval] UINT32 *value);
        HRESULT UIElementColor([in] Windows.UI.ViewManagement.UIElementType element, [out, retval] Windows.UI.Color *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.ViewManagement.UISettings),
        uuid(bad82401-2721-44f9-bb91-2bb228be442f)
    ]
    interface IUISettings2 : IInspectable
    {
        [propget] HRESULT TextScaleFactor([out, retval] DOUBLE *value);
        [eventadd] HRESULT TextScaleFactorChanged([in] Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.UISettings *, IInspectable *> *handler,
                                                  [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT TextScaleFactorChanged([in] EventRegistrationToken cookie);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.ViewManagement.UISettings),
        uuid(03021be4-**************-5168f7d06d7b)
    ]
    interface IUISettings3 : IInspectable
    {
        HRESULT GetColorValue([in] Windows.UI.ViewManagement.UIColorType color, [out, retval] Windows.UI.Color *value);
        [eventadd] HRESULT ColorValuesChanged([in] Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.UISettings *, IInspectable *> *handler,
                                              [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT ColorValuesChanged([in] EventRegistrationToken cookie);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.ViewManagement.UISettings),
        uuid(52bb3002-919b-4d6b-9b78-8dd66ff4b93b)
    ]
    interface IUISettings4 : IInspectable
    {
        [propget] HRESULT AdvancedEffectsEnabled([out, retval] boolean *value);
        [eventadd] HRESULT AdvancedEffectsEnabledChanged(
            [in] Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.UISettings *, IInspectable *> *handler,
            [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT AdvancedEffectsEnabledChanged([in] EventRegistrationToken cookie);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 8.0),
        exclusiveto(Windows.UI.ViewManagement.UISettings),
        uuid(5349d588-0cb5-5f05-bd34-706b3231f0bd)
    ]
    interface IUISettings5 : IInspectable
    {
        [propget] HRESULT AutoHideScrollBars([out, retval] boolean *value);
        [eventadd] HRESULT AutoHideScrollBarsChanged(
            [in] Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.UISettings *, Windows.UI.ViewManagement.UISettingsAutoHideScrollBarsChangedEventArgs *> *handler,
            [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT AutoHideScrollBarsChanged([in] EventRegistrationToken token);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 10.0),
        exclusiveto(Windows.UI.ViewManagement.UISettings),
        uuid(aef19bd7-fe31-5a04-ada4-469aaec6dfa9)
    ]
    interface IUISettings6 : IInspectable
    {
        [eventadd] HRESULT AnimationsEnabledChanged(
            [in] Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.UISettings *, Windows.UI.ViewManagement.UISettingsAnimationsEnabledChangedEventArgs *> *handler,
            [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT AnimationsEnabledChanged([in] EventRegistrationToken token);
        [eventadd] HRESULT MessageDurationChanged(
            [in] Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.UISettings *, Windows.UI.ViewManagement.UISettingsMessageDurationChangedEventArgs *> *handler,
            [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT MessageDurationChanged([in] EventRegistrationToken token);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 10.0),
        exclusiveto(Windows.UI.ViewManagement.UISettingsAnimationsEnabledChangedEventArgs),
        uuid(0c7b4b3d-2ea1-533e-894d-415bc5243c29)
    ]
    interface IUISettingsAnimationsEnabledChangedEventArgs : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 8.0),
        exclusiveto(Windows.UI.ViewManagement.UISettingsAutoHideScrollBarsChangedEventArgs),
        uuid(87afd4b2-9146-5f02-8f6b-06d454174c0f)
    ]
    interface IUISettingsAutoHideScrollBarsChangedEventArgs : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 10.0),
        exclusiveto(Windows.UI.ViewManagement.UISettingsMessageDurationChangedEventArgs),
        uuid(338aad52-4a5d-5b59-8002-d930f608fd6e)
    ]
    interface IUISettingsMessageDurationChangedEventArgs : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.ViewManagement.UIViewSettings),
        uuid(c63657f6-8850-470d-88f8-455e16ea2c26)
    ]
    interface IUIViewSettings : IInspectable
    {
        [propget] HRESULT UserInteractionMode([out, retval] Windows.UI.ViewManagement.UserInteractionMode *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.ViewManagement.UIViewSettings),
        uuid(595c97a5-f8f6-41cf-b0fb-aacdb81fd5f6)
    ]
    interface IUIViewSettingsStatics : IInspectable
    {
        HRESULT GetForCurrentView([out, retval] Windows.UI.ViewManagement.UIViewSettings **current);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.ViewManagement.InputPane),
        uuid(640ada70-06f3-4c87-a678-9829c9127c28)
    ]
    interface IInputPane : IInspectable
    {
        [eventadd] HRESULT Showing([in] Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.InputPane*, Windows.UI.ViewManagement.InputPaneVisibilityEventArgs*> *handler, [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT Showing([in] EventRegistrationToken token);
        [eventadd] HRESULT Hiding([in] Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.InputPane*, Windows.UI.ViewManagement.InputPaneVisibilityEventArgs*> *handler, [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT Hiding([in] EventRegistrationToken token);
        [propget] HRESULT OccludedRect([out, retval] Windows.Foundation.Rect *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.ViewManagement.InputPane),
        uuid(8a6b3f26-7090-4793-944c-c3f2cde26276)
    ]
    interface IInputPane2 : IInspectable
    {
        HRESULT TryShow([out, retval] boolean *result);
        HRESULT TryHide([out, retval] boolean *result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.ViewManagement.InputPane),
        uuid(088bb24f-962f-489d-aa6e-c6be1a0a6e52)
    ]
    interface IInputPaneControl : IInspectable
    {
        [propget] HRESULT Visible([out, retval] boolean *value);
        [propput] HRESULT Visible([in] boolean value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.ViewManagement.InputPaneVisibilityEventArgs),
        uuid(d243e016-d907-4fcc-bb8d-f77baa5028f1)
    ]
    interface IInputPaneVisibilityEventArgs : IInspectable
    {
        [propget] HRESULT OccludedRect([out, retval] Windows.Foundation.Rect *value);
        [propput] HRESULT EnsuredFocusedElementInView([in] boolean value);
        [propget] HRESULT EnsuredFocusedElementInView([out, retval] boolean *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.ViewManagement.InputPane),
        uuid(95f4af3a-ef47-424a-9741-fd2815eba2bd)
    ]
    interface IInputPaneStatics : IInspectable
    {
        HRESULT GetForCurrentView([out, retval] Windows.UI.ViewManagement.InputPane **input_pane);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 8.0),
        exclusiveto(Windows.UI.ViewManagement.InputPane),
        uuid(1b63529b-d9ec-4531-8445-71bab9fb828e)
    ]
    interface IInputPaneStatics2 : IInspectable
    {
        HRESULT GetForUIContext([in] Windows.UI.UIContext *context, [out, retval] Windows.UI.ViewManagement.InputPane **result);
    }

    [
        activatable(Windows.Foundation.UniversalApiContract, 1.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass UISettings
    {
        [default] interface Windows.UI.ViewManagement.IUISettings;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.UI.ViewManagement.IUISettings2;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.UI.ViewManagement.IUISettings3;
        [contract(Windows.Foundation.UniversalApiContract, 4.0)] interface Windows.UI.ViewManagement.IUISettings4;
        [contract(Windows.Foundation.UniversalApiContract, 8.0)] interface Windows.UI.ViewManagement.IUISettings5;
        [contract(Windows.Foundation.UniversalApiContract, 10.0)] interface Windows.UI.ViewManagement.IUISettings6;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 10.0),
        marshaling_behavior(agile)
    ]
    runtimeclass UISettingsAnimationsEnabledChangedEventArgs
    {
        [default] interface Windows.UI.ViewManagement.IUISettingsAnimationsEnabledChangedEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 8.0),
        marshaling_behavior(agile)
    ]
    runtimeclass UISettingsAutoHideScrollBarsChangedEventArgs
    {
        [default] interface Windows.UI.ViewManagement.IUISettingsAutoHideScrollBarsChangedEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 10.0),
        marshaling_behavior(agile)
    ]
    runtimeclass UISettingsMessageDurationChangedEventArgs
    {
        [default] interface Windows.UI.ViewManagement.IUISettingsMessageDurationChangedEventArgs;
    }

    [
        static(Windows.UI.ViewManagement.IUIViewSettingsStatics, Windows.Foundation.UniversalApiContract, 1.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass UIViewSettings
    {
        [default] interface Windows.UI.ViewManagement.IUIViewSettings;
    }

    [
        static(Windows.UI.ViewManagement.IInputPaneStatics, Windows.Foundation.UniversalApiContract, 1.0),
        static(Windows.UI.ViewManagement.IInputPaneStatics2, Windows.Foundation.UniversalApiContract, 8.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass InputPane
    {
        [default] interface Windows.UI.ViewManagement.IInputPane;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.UI.ViewManagement.IInputPane2;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.UI.ViewManagement.IInputPaneControl;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass InputPaneVisibilityEventArgs
    {
        [default] interface Windows.UI.ViewManagement.IInputPaneVisibilityEventArgs;
    }
}
