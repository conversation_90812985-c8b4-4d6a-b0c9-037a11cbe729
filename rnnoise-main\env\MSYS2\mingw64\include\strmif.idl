/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

import "unknwn.idl";
import "objidl.idl";

#include "devenum.idl"
#include "axcore.idl"
#include "axextend.idl"
#include "dyngraph.idl"

cpp_quote("#ifndef __IReferenceClock2_FWD_DEFINED__")
cpp_quote("#define __IReferenceClock2_FWD_DEFINED__")
cpp_quote("typedef struct IReferenceClock2 IReferenceClock2;")
cpp_quote("#endif")
cpp_quote("#ifndef __IDistributorNotify_FWD_DEFINED__")
cpp_quote("#define __IDistributorNotify_FWD_DEFINED__")
cpp_quote("typedef struct IDistributorNotify IDistributorNotify;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMVfwCompressDialogs_FWD_DEFINED__")
cpp_quote("#define __IAMVfwCompressDialogs_FWD_DEFINED__")
cpp_quote("typedef struct IAMVfwCompressDialogs IAMVfwCompressDialogs;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMAudioInputMixer_FWD_DEFINED__")
cpp_quote("#define __IAMAudioInputMixer_FWD_DEFINED__")
cpp_quote("typedef struct IAMAudioInputMixer IAMAudioInputMixer;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMAnalogVideoDecoder_FWD_DEFINED__")
cpp_quote("#define __IAMAnalogVideoDecoder_FWD_DEFINED__")
cpp_quote("typedef struct IAMAnalogVideoDecoder IAMAnalogVideoDecoder;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IBPCSatelliteTuner_FWD_DEFINED__")
cpp_quote("#define __IBPCSatelliteTuner_FWD_DEFINED__")
cpp_quote("typedef struct IBPCSatelliteTuner IBPCSatelliteTuner;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMTVAudio_FWD_DEFINED__")
cpp_quote("#define __IAMTVAudio_FWD_DEFINED__")
cpp_quote("typedef struct IAMTVAudio IAMTVAudio;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMTVAudioNotification_FWD_DEFINED__")
cpp_quote("#define __IAMTVAudioNotification_FWD_DEFINED__")
cpp_quote("typedef struct IAMTVAudioNotification IAMTVAudioNotification;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMAnalogVideoEncoder_FWD_DEFINED__")
cpp_quote("#define __IAMAnalogVideoEncoder_FWD_DEFINED__")
cpp_quote("typedef struct IAMAnalogVideoEncoder IAMAnalogVideoEncoder;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMPhysicalPinInfo_FWD_DEFINED__")
cpp_quote("#define __IAMPhysicalPinInfo_FWD_DEFINED__")
cpp_quote("typedef struct IAMPhysicalPinInfo IAMPhysicalPinInfo;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMExtDevice_FWD_DEFINED__")
cpp_quote("#define __IAMExtDevice_FWD_DEFINED__")
cpp_quote("typedef struct IAMExtDevice IAMExtDevice;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMExtTransport_FWD_DEFINED__")
cpp_quote("#define __IAMExtTransport_FWD_DEFINED__")
cpp_quote("typedef struct IAMExtTransport IAMExtTransport;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMTimecodeReader_FWD_DEFINED__")
cpp_quote("#define __IAMTimecodeReader_FWD_DEFINED__")
cpp_quote("typedef struct IAMTimecodeReader IAMTimecodeReader;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMTimecodeGenerator_FWD_DEFINED__")
cpp_quote("#define __IAMTimecodeGenerator_FWD_DEFINED__")
cpp_quote("typedef struct IAMTimecodeGenerator IAMTimecodeGenerator;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMTimecodeDisplay_FWD_DEFINED__")
cpp_quote("#define __IAMTimecodeDisplay_FWD_DEFINED__")
cpp_quote("typedef struct IAMTimecodeDisplay IAMTimecodeDisplay;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMDevMemoryAllocator_FWD_DEFINED__")
cpp_quote("#define __IAMDevMemoryAllocator_FWD_DEFINED__")
cpp_quote("typedef struct IAMDevMemoryAllocator IAMDevMemoryAllocator;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMDevMemoryControl_FWD_DEFINED__")
cpp_quote("#define __IAMDevMemoryControl_FWD_DEFINED__")
cpp_quote("typedef struct IAMDevMemoryControl IAMDevMemoryControl;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMStreamSelect_FWD_DEFINED__")
cpp_quote("#define __IAMStreamSelect_FWD_DEFINED__")
cpp_quote("typedef struct IAMStreamSelect IAMStreamSelect;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMResourceControl_FWD_DEFINED__")
cpp_quote("#define __IAMResourceControl_FWD_DEFINED__")
cpp_quote("typedef struct IAMResourceControl IAMResourceControl;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMClockAdjust_FWD_DEFINED__")
cpp_quote("#define __IAMClockAdjust_FWD_DEFINED__")
cpp_quote("typedef struct IAMClockAdjust IAMClockAdjust;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDrawVideoImage_FWD_DEFINED__")
cpp_quote("#define __IDrawVideoImage_FWD_DEFINED__")
cpp_quote("typedef struct IDrawVideoImage IDrawVideoImage;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDecimateVideoImage_FWD_DEFINED__")
cpp_quote("#define __IDecimateVideoImage_FWD_DEFINED__")
cpp_quote("typedef struct IDecimateVideoImage IDecimateVideoImage;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMVideoDecimationProperties_FWD_DEFINED__")
cpp_quote("#define __IAMVideoDecimationProperties_FWD_DEFINED__")
cpp_quote("typedef struct IAMVideoDecimationProperties IAMVideoDecimationProperties;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMDeviceRemoval_FWD_DEFINED__")
cpp_quote("#define __IAMDeviceRemoval_FWD_DEFINED__")
cpp_quote("typedef struct IAMDeviceRemoval IAMDeviceRemoval;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDVEnc_FWD_DEFINED__")
cpp_quote("#define __IDVEnc_FWD_DEFINED__")
cpp_quote("typedef struct IDVEnc IDVEnc;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IIPDVDec_FWD_DEFINED__")
cpp_quote("#define __IIPDVDec_FWD_DEFINED__")
cpp_quote("typedef struct IIPDVDec IIPDVDec;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDVRGB219_FWD_DEFINED__")
cpp_quote("#define __IDVRGB219_FWD_DEFINED__")
cpp_quote("typedef struct IDVRGB219 IDVRGB219;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDVSplitter_FWD_DEFINED__")
cpp_quote("#define __IDVSplitter_FWD_DEFINED__")
cpp_quote("typedef struct IDVSplitter IDVSplitter;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMAudioRendererStats_FWD_DEFINED__")
cpp_quote("#define __IAMAudioRendererStats_FWD_DEFINED__")
cpp_quote("typedef struct IAMAudioRendererStats IAMAudioRendererStats;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMGraphStreams_FWD_DEFINED__")
cpp_quote("#define __IAMGraphStreams_FWD_DEFINED__")
cpp_quote("typedef struct IAMGraphStreams IAMGraphStreams;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMOverlayFX_FWD_DEFINED__")
cpp_quote("#define __IAMOverlayFX_FWD_DEFINED__")
cpp_quote("typedef struct IAMOverlayFX IAMOverlayFX;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMOpenProgress_FWD_DEFINED__")
cpp_quote("#define __IAMOpenProgress_FWD_DEFINED__")
cpp_quote("typedef struct IAMOpenProgress IAMOpenProgress;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IMpeg2Demultiplexer_FWD_DEFINED__")
cpp_quote("#define __IMpeg2Demultiplexer_FWD_DEFINED__")
cpp_quote("typedef struct IMpeg2Demultiplexer IMpeg2Demultiplexer;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IEnumStreamIdMap_FWD_DEFINED__")
cpp_quote("#define __IEnumStreamIdMap_FWD_DEFINED__")
cpp_quote("typedef struct IEnumStreamIdMap IEnumStreamIdMap;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IMPEG2StreamIdMap_FWD_DEFINED__")
cpp_quote("#define __IMPEG2StreamIdMap_FWD_DEFINED__")
cpp_quote("typedef struct IMPEG2StreamIdMap IMPEG2StreamIdMap;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IRegisterServiceProvider_FWD_DEFINED__")
cpp_quote("#define __IRegisterServiceProvider_FWD_DEFINED__")
cpp_quote("typedef struct IRegisterServiceProvider IRegisterServiceProvider;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __ICodecAPI_FWD_DEFINED__")
cpp_quote("#define __ICodecAPI_FWD_DEFINED__")
cpp_quote("typedef struct ICodecAPI ICodecAPI;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IGetCapabilitiesKey_FWD_DEFINED__")
cpp_quote("#define __IGetCapabilitiesKey_FWD_DEFINED__")
cpp_quote("typedef struct IGetCapabilitiesKey IGetCapabilitiesKey;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IEncoderAPI_FWD_DEFINED__")
cpp_quote("#define __IEncoderAPI_FWD_DEFINED__")
cpp_quote("typedef struct IEncoderAPI IEncoderAPI;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVideoEncoder_FWD_DEFINED__")
cpp_quote("#define __IVideoEncoder_FWD_DEFINED__")
cpp_quote("typedef struct IVideoEncoder IVideoEncoder;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMDecoderCaps_FWD_DEFINED__")
cpp_quote("#define __IAMDecoderCaps_FWD_DEFINED__")
cpp_quote("typedef struct IAMDecoderCaps IAMDecoderCaps;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDvdControl_FWD_DEFINED__")
cpp_quote("#define __IDvdControl_FWD_DEFINED__")
cpp_quote("typedef struct IDvdControl IDvdControl;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDvdInfo_FWD_DEFINED__")
cpp_quote("#define __IDvdInfo_FWD_DEFINED__")
cpp_quote("typedef struct IDvdInfo IDvdInfo;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDvdCmd_FWD_DEFINED__")
cpp_quote("#define __IDvdCmd_FWD_DEFINED__")
cpp_quote("typedef struct IDvdCmd IDvdCmd;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDvdState_FWD_DEFINED__")
cpp_quote("#define __IDvdState_FWD_DEFINED__")
cpp_quote("typedef struct IDvdState IDvdState;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDvdControl2_FWD_DEFINED__")
cpp_quote("#define __IDvdControl2_FWD_DEFINED__")
cpp_quote("typedef struct IDvdControl2 IDvdControl2;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDvdInfo2_FWD_DEFINED__")
cpp_quote("#define __IDvdInfo2_FWD_DEFINED__")
cpp_quote("typedef struct IDvdInfo2 IDvdInfo2;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDvdGraphBuilder_FWD_DEFINED__")
cpp_quote("#define __IDvdGraphBuilder_FWD_DEFINED__")
cpp_quote("typedef struct IDvdGraphBuilder IDvdGraphBuilder;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDDrawExclModeVideo_FWD_DEFINED__")
cpp_quote("#define __IDDrawExclModeVideo_FWD_DEFINED__")
cpp_quote("typedef struct IDDrawExclModeVideo IDDrawExclModeVideo;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDDrawExclModeVideoCallback_FWD_DEFINED__")
cpp_quote("#define __IDDrawExclModeVideoCallback_FWD_DEFINED__")
cpp_quote("typedef struct IDDrawExclModeVideoCallback IDDrawExclModeVideoCallback;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRImagePresenter_FWD_DEFINED__")
cpp_quote("#define __IVMRImagePresenter_FWD_DEFINED__")
cpp_quote("typedef struct IVMRImagePresenter IVMRImagePresenter;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRSurfaceAllocator_FWD_DEFINED__")
cpp_quote("#define __IVMRSurfaceAllocator_FWD_DEFINED__")
cpp_quote("typedef struct IVMRSurfaceAllocator IVMRSurfaceAllocator;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRSurfaceAllocatorNotify_FWD_DEFINED__")
cpp_quote("#define __IVMRSurfaceAllocatorNotify_FWD_DEFINED__")
cpp_quote("typedef struct IVMRSurfaceAllocatorNotify IVMRSurfaceAllocatorNotify;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRWindowlessControl_FWD_DEFINED__")
cpp_quote("#define __IVMRWindowlessControl_FWD_DEFINED__")
cpp_quote("typedef struct IVMRWindowlessControl IVMRWindowlessControl;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRMixerControl_FWD_DEFINED__")
cpp_quote("#define __IVMRMixerControl_FWD_DEFINED__")
cpp_quote("typedef struct IVMRMixerControl IVMRMixerControl;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRMonitorConfig_FWD_DEFINED__")
cpp_quote("#define __IVMRMonitorConfig_FWD_DEFINED__")
cpp_quote("typedef struct IVMRMonitorConfig IVMRMonitorConfig;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRFilterConfig_FWD_DEFINED__")
cpp_quote("#define __IVMRFilterConfig_FWD_DEFINED__")
cpp_quote("typedef struct IVMRFilterConfig IVMRFilterConfig;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRAspectRatioControl_FWD_DEFINED__")
cpp_quote("#define __IVMRAspectRatioControl_FWD_DEFINED__")
cpp_quote("typedef struct IVMRAspectRatioControl IVMRAspectRatioControl;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRDeinterlaceControl_FWD_DEFINED__")
cpp_quote("#define __IVMRDeinterlaceControl_FWD_DEFINED__")
cpp_quote("typedef struct IVMRDeinterlaceControl IVMRDeinterlaceControl;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRMixerBitmap_FWD_DEFINED__")
cpp_quote("#define __IVMRMixerBitmap_FWD_DEFINED__")
cpp_quote("typedef struct IVMRMixerBitmap IVMRMixerBitmap;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRImageCompositor_FWD_DEFINED__")
cpp_quote("#define __IVMRImageCompositor_FWD_DEFINED__")
cpp_quote("typedef struct IVMRImageCompositor IVMRImageCompositor;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRVideoStreamControl_FWD_DEFINED__")
cpp_quote("#define __IVMRVideoStreamControl_FWD_DEFINED__")
cpp_quote("typedef struct IVMRVideoStreamControl IVMRVideoStreamControl;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRSurface_FWD_DEFINED__")
cpp_quote("#define __IVMRSurface_FWD_DEFINED__")
cpp_quote("typedef struct IVMRSurface IVMRSurface;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRImagePresenterConfig_FWD_DEFINED__")
cpp_quote("#define __IVMRImagePresenterConfig_FWD_DEFINED__")
cpp_quote("typedef struct IVMRImagePresenterConfig IVMRImagePresenterConfig;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRImagePresenterExclModeConfig_FWD_DEFINED__")
cpp_quote("#define __IVMRImagePresenterExclModeConfig_FWD_DEFINED__")
cpp_quote("typedef struct IVMRImagePresenterExclModeConfig IVMRImagePresenterExclModeConfig;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVPManager_FWD_DEFINED__")
cpp_quote("#define __IVPManager_FWD_DEFINED__")
cpp_quote("typedef struct IVPManager IVPManager;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMAsyncReaderTimestampScaling_FWD_DEFINED__")
cpp_quote("#define __IAMAsyncReaderTimestampScaling_FWD_DEFINED__")
cpp_quote("typedef struct IAMAsyncReaderTimestampScaling IAMAsyncReaderTimestampScaling;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMPluginControl_FWD_DEFINED__")
cpp_quote("#define __IAMPluginControl_FWD_DEFINED__")
cpp_quote("typedef struct IAMPluginControl IAMPluginControl;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#include \"ocidl.h\"")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0125_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0125_v0_0_s_ifspec;")
cpp_quote("#ifndef __IReferenceClock2_INTERFACE_DEFINED__")
cpp_quote("#define __IReferenceClock2_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IReferenceClock2;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IReferenceClock2 : public IReferenceClock {")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IReferenceClock2Vtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IReferenceClock2 *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IReferenceClock2 *This);")
cpp_quote("      ULONG (WINAPI *Release)(IReferenceClock2 *This);")
cpp_quote("      HRESULT (WINAPI *GetTime)(IReferenceClock2 *This,REFERENCE_TIME *pTime);")
cpp_quote("      HRESULT (WINAPI *AdviseTime)(IReferenceClock2 *This,REFERENCE_TIME baseTime,REFERENCE_TIME streamTime,HEVENT hEvent,DWORD_PTR *pdwAdviseCookie);")
cpp_quote("      HRESULT (WINAPI *AdvisePeriodic)(IReferenceClock2 *This,REFERENCE_TIME startTime,REFERENCE_TIME periodTime,HSEMAPHORE hSemaphore,DWORD_PTR *pdwAdviseCookie);")
cpp_quote("      HRESULT (WINAPI *Unadvise)(IReferenceClock2 *This,DWORD_PTR dwAdviseCookie);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IReferenceClock2Vtbl;")
cpp_quote("  struct IReferenceClock2 {")
cpp_quote("    CONST_VTBL struct IReferenceClock2Vtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IReferenceClock2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IReferenceClock2_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IReferenceClock2_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IReferenceClock2_GetTime(This,pTime) (This)->lpVtbl->GetTime(This,pTime)")
cpp_quote("#define IReferenceClock2_AdviseTime(This,baseTime,streamTime,hEvent,pdwAdviseCookie) (This)->lpVtbl->AdviseTime(This,baseTime,streamTime,hEvent,pdwAdviseCookie)")
cpp_quote("#define IReferenceClock2_AdvisePeriodic(This,startTime,periodTime,hSemaphore,pdwAdviseCookie) (This)->lpVtbl->AdvisePeriodic(This,startTime,periodTime,hSemaphore,pdwAdviseCookie)")
cpp_quote("#define IReferenceClock2_Unadvise(This,dwAdviseCookie) (This)->lpVtbl->Unadvise(This,dwAdviseCookie)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef IReferenceClock2 *PREFERENCECLOCK2;")
cpp_quote("")
cpp_quote("#ifndef __IDistributorNotify_INTERFACE_DEFINED__")
cpp_quote("#define __IDistributorNotify_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDistributorNotify;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDistributorNotify : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI Stop(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI Pause(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI Run(REFERENCE_TIME tStart) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetSyncSource(IReferenceClock *pClock) = 0;")
cpp_quote("    virtual HRESULT WINAPI NotifyGraphChange(void) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDistributorNotifyVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDistributorNotify *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDistributorNotify *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDistributorNotify *This);")
cpp_quote("      HRESULT (WINAPI *Stop)(IDistributorNotify *This);")
cpp_quote("      HRESULT (WINAPI *Pause)(IDistributorNotify *This);")
cpp_quote("      HRESULT (WINAPI *Run)(IDistributorNotify *This,REFERENCE_TIME tStart);")
cpp_quote("      HRESULT (WINAPI *SetSyncSource)(IDistributorNotify *This,IReferenceClock *pClock);")
cpp_quote("      HRESULT (WINAPI *NotifyGraphChange)(IDistributorNotify *This);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDistributorNotifyVtbl;")
cpp_quote("  struct IDistributorNotify {")
cpp_quote("    CONST_VTBL struct IDistributorNotifyVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDistributorNotify_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDistributorNotify_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDistributorNotify_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDistributorNotify_Stop(This) (This)->lpVtbl->Stop(This)")
cpp_quote("#define IDistributorNotify_Pause(This) (This)->lpVtbl->Pause(This)")
cpp_quote("#define IDistributorNotify_Run(This,tStart) (This)->lpVtbl->Run(This,tStart)")
cpp_quote("#define IDistributorNotify_SetSyncSource(This,pClock) (This)->lpVtbl->SetSyncSource(This,pClock)")
cpp_quote("#define IDistributorNotify_NotifyGraphChange(This) (This)->lpVtbl->NotifyGraphChange(This)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDistributorNotify_Stop_Proxy(IDistributorNotify *This);")
cpp_quote("  void __RPC_STUB IDistributorNotify_Stop_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDistributorNotify_Pause_Proxy(IDistributorNotify *This);")
cpp_quote("  void __RPC_STUB IDistributorNotify_Pause_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDistributorNotify_Run_Proxy(IDistributorNotify *This,REFERENCE_TIME tStart);")
cpp_quote("  void __RPC_STUB IDistributorNotify_Run_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDistributorNotify_SetSyncSource_Proxy(IDistributorNotify *This,IReferenceClock *pClock);")
cpp_quote("  void __RPC_STUB IDistributorNotify_SetSyncSource_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDistributorNotify_NotifyGraphChange_Proxy(IDistributorNotify *This);")
cpp_quote("  void __RPC_STUB IDistributorNotify_NotifyGraphChange_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0167_0001 {")
cpp_quote("    CompressionCaps_CanQuality = 0x1,CompressionCaps_CanCrunch = 0x2,CompressionCaps_CanKeyFrame = 0x4,CompressionCaps_CanBFrame = 0x8,")
cpp_quote("    CompressionCaps_CanWindow = 0x10")
cpp_quote("  } CompressionCaps;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0167_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0167_v0_0_s_ifspec;")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0168_0001 {")
cpp_quote("    VfwCaptureDialog_Source = 0x1,VfwCaptureDialog_Format = 0x2,VfwCaptureDialog_Display = 0x4")
cpp_quote("  } VfwCaptureDialogs;")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0168_0002 {")
cpp_quote("    VfwCompressDialog_Config = 0x1,VfwCompressDialog_About = 0x2,VfwCompressDialog_QueryConfig = 0x4,VfwCompressDialog_QueryAbout = 0x8")
cpp_quote("  } VfwCompressDialogs;")
cpp_quote("")
cpp_quote("#ifndef __IAMVfwCompressDialogs_INTERFACE_DEFINED__")
cpp_quote("#define __IAMVfwCompressDialogs_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMVfwCompressDialogs;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMVfwCompressDialogs : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI ShowDialog(int iDialog,HWND hwnd) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetState(LPVOID pState,int *pcbState) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetState(LPVOID pState,int cbState) = 0;")
cpp_quote("    virtual HRESULT WINAPI SendDriverMessage(int uMsg,LONG dw1,LONG dw2) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMVfwCompressDialogsVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMVfwCompressDialogs *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMVfwCompressDialogs *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMVfwCompressDialogs *This);")
cpp_quote("      HRESULT (WINAPI *ShowDialog)(IAMVfwCompressDialogs *This,int iDialog,HWND hwnd);")
cpp_quote("      HRESULT (WINAPI *GetState)(IAMVfwCompressDialogs *This,LPVOID pState,int *pcbState);")
cpp_quote("      HRESULT (WINAPI *SetState)(IAMVfwCompressDialogs *This,LPVOID pState,int cbState);")
cpp_quote("      HRESULT (WINAPI *SendDriverMessage)(IAMVfwCompressDialogs *This,int uMsg,LONG dw1,LONG dw2);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMVfwCompressDialogsVtbl;")
cpp_quote("  struct IAMVfwCompressDialogs {")
cpp_quote("    CONST_VTBL struct IAMVfwCompressDialogsVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMVfwCompressDialogs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMVfwCompressDialogs_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMVfwCompressDialogs_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMVfwCompressDialogs_ShowDialog(This,iDialog,hwnd) (This)->lpVtbl->ShowDialog(This,iDialog,hwnd)")
cpp_quote("#define IAMVfwCompressDialogs_GetState(This,pState,pcbState) (This)->lpVtbl->GetState(This,pState,pcbState)")
cpp_quote("#define IAMVfwCompressDialogs_SetState(This,pState,cbState) (This)->lpVtbl->SetState(This,pState,cbState)")
cpp_quote("#define IAMVfwCompressDialogs_SendDriverMessage(This,uMsg,dw1,dw2) (This)->lpVtbl->SendDriverMessage(This,uMsg,dw1,dw2)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMVfwCompressDialogs_ShowDialog_Proxy(IAMVfwCompressDialogs *This,int iDialog,HWND hwnd);")
cpp_quote("  void __RPC_STUB IAMVfwCompressDialogs_ShowDialog_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMVfwCompressDialogs_GetState_Proxy(IAMVfwCompressDialogs *This,LPVOID pState,int *pcbState);")
cpp_quote("  void __RPC_STUB IAMVfwCompressDialogs_GetState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMVfwCompressDialogs_SetState_Proxy(IAMVfwCompressDialogs *This,LPVOID pState,int cbState);")
cpp_quote("  void __RPC_STUB IAMVfwCompressDialogs_SetState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMVfwCompressDialogs_SendDriverMessage_Proxy(IAMVfwCompressDialogs *This,int uMsg,LONG dw1,LONG dw2);")
cpp_quote("  void __RPC_STUB IAMVfwCompressDialogs_SendDriverMessage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#define AMF_AUTOMATICGAIN -1.0")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0171_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0171_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMAudioInputMixer_INTERFACE_DEFINED__")
cpp_quote("#define __IAMAudioInputMixer_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMAudioInputMixer;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMAudioInputMixer : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI put_Enable(WINBOOL fEnable) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Enable(WINBOOL *pfEnable) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Mono(WINBOOL fMono) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Mono(WINBOOL *pfMono) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_MixLevel(double Level) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_MixLevel(double *pLevel) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Pan(double Pan) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Pan(double *pPan) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Loudness(WINBOOL fLoudness) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Loudness(WINBOOL *pfLoudness) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Treble(double Treble) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Treble(double *pTreble) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_TrebleRange(double *pRange) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Bass(double Bass) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Bass(double *pBass) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_BassRange(double *pRange) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMAudioInputMixerVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMAudioInputMixer *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMAudioInputMixer *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMAudioInputMixer *This);")
cpp_quote("      HRESULT (WINAPI *put_Enable)(IAMAudioInputMixer *This,WINBOOL fEnable);")
cpp_quote("      HRESULT (WINAPI *get_Enable)(IAMAudioInputMixer *This,WINBOOL *pfEnable);")
cpp_quote("      HRESULT (WINAPI *put_Mono)(IAMAudioInputMixer *This,WINBOOL fMono);")
cpp_quote("      HRESULT (WINAPI *get_Mono)(IAMAudioInputMixer *This,WINBOOL *pfMono);")
cpp_quote("      HRESULT (WINAPI *put_MixLevel)(IAMAudioInputMixer *This,double Level);")
cpp_quote("      HRESULT (WINAPI *get_MixLevel)(IAMAudioInputMixer *This,double *pLevel);")
cpp_quote("      HRESULT (WINAPI *put_Pan)(IAMAudioInputMixer *This,double Pan);")
cpp_quote("      HRESULT (WINAPI *get_Pan)(IAMAudioInputMixer *This,double *pPan);")
cpp_quote("      HRESULT (WINAPI *put_Loudness)(IAMAudioInputMixer *This,WINBOOL fLoudness);")
cpp_quote("      HRESULT (WINAPI *get_Loudness)(IAMAudioInputMixer *This,WINBOOL *pfLoudness);")
cpp_quote("      HRESULT (WINAPI *put_Treble)(IAMAudioInputMixer *This,double Treble);")
cpp_quote("      HRESULT (WINAPI *get_Treble)(IAMAudioInputMixer *This,double *pTreble);")
cpp_quote("      HRESULT (WINAPI *get_TrebleRange)(IAMAudioInputMixer *This,double *pRange);")
cpp_quote("      HRESULT (WINAPI *put_Bass)(IAMAudioInputMixer *This,double Bass);")
cpp_quote("      HRESULT (WINAPI *get_Bass)(IAMAudioInputMixer *This,double *pBass);")
cpp_quote("      HRESULT (WINAPI *get_BassRange)(IAMAudioInputMixer *This,double *pRange);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMAudioInputMixerVtbl;")
cpp_quote("  struct IAMAudioInputMixer {")
cpp_quote("    CONST_VTBL struct IAMAudioInputMixerVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMAudioInputMixer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMAudioInputMixer_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMAudioInputMixer_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMAudioInputMixer_put_Enable(This,fEnable) (This)->lpVtbl->put_Enable(This,fEnable)")
cpp_quote("#define IAMAudioInputMixer_get_Enable(This,pfEnable) (This)->lpVtbl->get_Enable(This,pfEnable)")
cpp_quote("#define IAMAudioInputMixer_put_Mono(This,fMono) (This)->lpVtbl->put_Mono(This,fMono)")
cpp_quote("#define IAMAudioInputMixer_get_Mono(This,pfMono) (This)->lpVtbl->get_Mono(This,pfMono)")
cpp_quote("#define IAMAudioInputMixer_put_MixLevel(This,Level) (This)->lpVtbl->put_MixLevel(This,Level)")
cpp_quote("#define IAMAudioInputMixer_get_MixLevel(This,pLevel) (This)->lpVtbl->get_MixLevel(This,pLevel)")
cpp_quote("#define IAMAudioInputMixer_put_Pan(This,Pan) (This)->lpVtbl->put_Pan(This,Pan)")
cpp_quote("#define IAMAudioInputMixer_get_Pan(This,pPan) (This)->lpVtbl->get_Pan(This,pPan)")
cpp_quote("#define IAMAudioInputMixer_put_Loudness(This,fLoudness) (This)->lpVtbl->put_Loudness(This,fLoudness)")
cpp_quote("#define IAMAudioInputMixer_get_Loudness(This,pfLoudness) (This)->lpVtbl->get_Loudness(This,pfLoudness)")
cpp_quote("#define IAMAudioInputMixer_put_Treble(This,Treble) (This)->lpVtbl->put_Treble(This,Treble)")
cpp_quote("#define IAMAudioInputMixer_get_Treble(This,pTreble) (This)->lpVtbl->get_Treble(This,pTreble)")
cpp_quote("#define IAMAudioInputMixer_get_TrebleRange(This,pRange) (This)->lpVtbl->get_TrebleRange(This,pRange)")
cpp_quote("#define IAMAudioInputMixer_put_Bass(This,Bass) (This)->lpVtbl->put_Bass(This,Bass)")
cpp_quote("#define IAMAudioInputMixer_get_Bass(This,pBass) (This)->lpVtbl->get_Bass(This,pBass)")
cpp_quote("#define IAMAudioInputMixer_get_BassRange(This,pRange) (This)->lpVtbl->get_BassRange(This,pRange)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_put_Enable_Proxy(IAMAudioInputMixer *This,WINBOOL fEnable);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_put_Enable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_get_Enable_Proxy(IAMAudioInputMixer *This,WINBOOL *pfEnable);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_get_Enable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_put_Mono_Proxy(IAMAudioInputMixer *This,WINBOOL fMono);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_put_Mono_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_get_Mono_Proxy(IAMAudioInputMixer *This,WINBOOL *pfMono);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_get_Mono_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_put_MixLevel_Proxy(IAMAudioInputMixer *This,double Level);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_put_MixLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_get_MixLevel_Proxy(IAMAudioInputMixer *This,double *pLevel);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_get_MixLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_put_Pan_Proxy(IAMAudioInputMixer *This,double Pan);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_put_Pan_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_get_Pan_Proxy(IAMAudioInputMixer *This,double *pPan);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_get_Pan_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_put_Loudness_Proxy(IAMAudioInputMixer *This,WINBOOL fLoudness);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_put_Loudness_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_get_Loudness_Proxy(IAMAudioInputMixer *This,WINBOOL *pfLoudness);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_get_Loudness_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_put_Treble_Proxy(IAMAudioInputMixer *This,double Treble);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_put_Treble_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_get_Treble_Proxy(IAMAudioInputMixer *This,double *pTreble);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_get_Treble_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_get_TrebleRange_Proxy(IAMAudioInputMixer *This,double *pRange);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_get_TrebleRange_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_put_Bass_Proxy(IAMAudioInputMixer *This,double Bass);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_put_Bass_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_get_Bass_Proxy(IAMAudioInputMixer *This,double *pBass);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_get_Bass_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAudioInputMixer_get_BassRange_Proxy(IAMAudioInputMixer *This,double *pRange);")
cpp_quote("  void __RPC_STUB IAMAudioInputMixer_get_BassRange_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#define AnalogVideo_NTSC_Mask 0x00000007")
cpp_quote("#define AnalogVideo_PAL_Mask 0x00100FF0")
cpp_quote("#define AnalogVideo_SECAM_Mask 0x000FF000")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0173_0001 {")
cpp_quote("    VideoCopyProtectionMacrovisionBasic = 0,VideoCopyProtectionMacrovisionCBI = VideoCopyProtectionMacrovisionBasic + 1")
cpp_quote("  } VideoCopyProtectionType;")
cpp_quote("")
cpp_quote("  typedef enum tagPhysicalConnectorType {")
cpp_quote("    PhysConn_Video_Tuner = 1,")
cpp_quote("    PhysConn_Video_Composite,PhysConn_Video_SVideo,PhysConn_Video_RGB,")
cpp_quote("    PhysConn_Video_YRYBY,PhysConn_Video_SerialDigital,PhysConn_Video_ParallelDigital,")
cpp_quote("    PhysConn_Video_SCSI,PhysConn_Video_AUX,PhysConn_Video_1394,PhysConn_Video_USB,")
cpp_quote("    PhysConn_Video_VideoDecoder,PhysConn_Video_VideoEncoder,PhysConn_Video_SCART,PhysConn_Video_Black,")
cpp_quote("    PhysConn_Audio_Tuner = 0x1000,PhysConn_Audio_Line = 0x1001,PhysConn_Audio_Mic = 0x1002,")
cpp_quote("    PhysConn_Audio_AESDigital = 0x1003,PhysConn_Audio_SPDIFDigital = 0x1004,")
cpp_quote("    PhysConn_Audio_SCSI = 0x1005,PhysConn_Audio_AUX = 0x1006,PhysConn_Audio_1394 = 0x1007,")
cpp_quote("    PhysConn_Audio_USB = 0x1008,PhysConn_Audio_AudioDecoder = 0x1009")
cpp_quote("  } PhysicalConnectorType;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0173_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0173_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMAnalogVideoDecoder_INTERFACE_DEFINED__")
cpp_quote("#define __IAMAnalogVideoDecoder_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMAnalogVideoDecoder;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMAnalogVideoDecoder : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI get_AvailableTVFormats(LONG *lAnalogVideoStandard) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_TVFormat(LONG lAnalogVideoStandard) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_TVFormat(LONG *plAnalogVideoStandard) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_HorizontalLocked(LONG *plLocked) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_VCRHorizontalLocking(LONG lVCRHorizontalLocking) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_VCRHorizontalLocking(LONG *plVCRHorizontalLocking) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_NumberOfLines(LONG *plNumberOfLines) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_OutputEnable(LONG lOutputEnable) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_OutputEnable(LONG *plOutputEnable) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMAnalogVideoDecoderVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMAnalogVideoDecoder *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMAnalogVideoDecoder *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMAnalogVideoDecoder *This);")
cpp_quote("      HRESULT (WINAPI *get_AvailableTVFormats)(IAMAnalogVideoDecoder *This,LONG *lAnalogVideoStandard);")
cpp_quote("      HRESULT (WINAPI *put_TVFormat)(IAMAnalogVideoDecoder *This,LONG lAnalogVideoStandard);")
cpp_quote("      HRESULT (WINAPI *get_TVFormat)(IAMAnalogVideoDecoder *This,LONG *plAnalogVideoStandard);")
cpp_quote("      HRESULT (WINAPI *get_HorizontalLocked)(IAMAnalogVideoDecoder *This,LONG *plLocked);")
cpp_quote("      HRESULT (WINAPI *put_VCRHorizontalLocking)(IAMAnalogVideoDecoder *This,LONG lVCRHorizontalLocking);")
cpp_quote("      HRESULT (WINAPI *get_VCRHorizontalLocking)(IAMAnalogVideoDecoder *This,LONG *plVCRHorizontalLocking);")
cpp_quote("      HRESULT (WINAPI *get_NumberOfLines)(IAMAnalogVideoDecoder *This,LONG *plNumberOfLines);")
cpp_quote("      HRESULT (WINAPI *put_OutputEnable)(IAMAnalogVideoDecoder *This,LONG lOutputEnable);")
cpp_quote("      HRESULT (WINAPI *get_OutputEnable)(IAMAnalogVideoDecoder *This,LONG *plOutputEnable);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMAnalogVideoDecoderVtbl;")
cpp_quote("  struct IAMAnalogVideoDecoder {")
cpp_quote("    CONST_VTBL struct IAMAnalogVideoDecoderVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMAnalogVideoDecoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMAnalogVideoDecoder_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMAnalogVideoDecoder_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMAnalogVideoDecoder_get_AvailableTVFormats(This,lAnalogVideoStandard) (This)->lpVtbl->get_AvailableTVFormats(This,lAnalogVideoStandard)")
cpp_quote("#define IAMAnalogVideoDecoder_put_TVFormat(This,lAnalogVideoStandard) (This)->lpVtbl->put_TVFormat(This,lAnalogVideoStandard)")
cpp_quote("#define IAMAnalogVideoDecoder_get_TVFormat(This,plAnalogVideoStandard) (This)->lpVtbl->get_TVFormat(This,plAnalogVideoStandard)")
cpp_quote("#define IAMAnalogVideoDecoder_get_HorizontalLocked(This,plLocked) (This)->lpVtbl->get_HorizontalLocked(This,plLocked)")
cpp_quote("#define IAMAnalogVideoDecoder_put_VCRHorizontalLocking(This,lVCRHorizontalLocking) (This)->lpVtbl->put_VCRHorizontalLocking(This,lVCRHorizontalLocking)")
cpp_quote("#define IAMAnalogVideoDecoder_get_VCRHorizontalLocking(This,plVCRHorizontalLocking) (This)->lpVtbl->get_VCRHorizontalLocking(This,plVCRHorizontalLocking)")
cpp_quote("#define IAMAnalogVideoDecoder_get_NumberOfLines(This,plNumberOfLines) (This)->lpVtbl->get_NumberOfLines(This,plNumberOfLines)")
cpp_quote("#define IAMAnalogVideoDecoder_put_OutputEnable(This,lOutputEnable) (This)->lpVtbl->put_OutputEnable(This,lOutputEnable)")
cpp_quote("#define IAMAnalogVideoDecoder_get_OutputEnable(This,plOutputEnable) (This)->lpVtbl->get_OutputEnable(This,plOutputEnable)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoDecoder_get_AvailableTVFormats_Proxy(IAMAnalogVideoDecoder *This,LONG *lAnalogVideoStandard);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoDecoder_get_AvailableTVFormats_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoDecoder_put_TVFormat_Proxy(IAMAnalogVideoDecoder *This,LONG lAnalogVideoStandard);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoDecoder_put_TVFormat_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoDecoder_get_TVFormat_Proxy(IAMAnalogVideoDecoder *This,LONG *plAnalogVideoStandard);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoDecoder_get_TVFormat_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoDecoder_get_HorizontalLocked_Proxy(IAMAnalogVideoDecoder *This,LONG *plLocked);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoDecoder_get_HorizontalLocked_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoDecoder_put_VCRHorizontalLocking_Proxy(IAMAnalogVideoDecoder *This,LONG lVCRHorizontalLocking);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoDecoder_put_VCRHorizontalLocking_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoDecoder_get_VCRHorizontalLocking_Proxy(IAMAnalogVideoDecoder *This,LONG *plVCRHorizontalLocking);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoDecoder_get_VCRHorizontalLocking_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoDecoder_get_NumberOfLines_Proxy(IAMAnalogVideoDecoder *This,LONG *plNumberOfLines);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoDecoder_get_NumberOfLines_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoDecoder_put_OutputEnable_Proxy(IAMAnalogVideoDecoder *This,LONG lOutputEnable);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoDecoder_put_OutputEnable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoDecoder_get_OutputEnable_Proxy(IAMAnalogVideoDecoder *This,LONG *plOutputEnable);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoDecoder_get_OutputEnable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef enum tagCameraControlProperty {")
cpp_quote("    CameraControl_Pan = 0,")
cpp_quote("    CameraControl_Tilt,CameraControl_Roll,CameraControl_Zoom,CameraControl_Exposure,")
cpp_quote("    CameraControl_Iris,CameraControl_Focus")
cpp_quote("  } CameraControlProperty;")
cpp_quote("")
cpp_quote("  typedef enum tagCameraControlFlags {")
cpp_quote("    CameraControl_Flags_Auto = 0x1,CameraControl_Flags_Manual = 0x2")
cpp_quote("  } CameraControlFlags;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0175_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0175_v0_0_s_ifspec;")
cpp_quote("")
cpp_quote("  typedef enum tagVideoControlFlags {")
cpp_quote("    VideoControlFlag_FlipHorizontal = 0x1,VideoControlFlag_FlipVertical = 0x2,VideoControlFlag_ExternalTriggerEnable = 0x4,VideoControlFlag_Trigger = 0x8")
cpp_quote("  } VideoControlFlags;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0176_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0176_v0_0_s_ifspec;")
cpp_quote("")
cpp_quote("  typedef enum tagAMTunerSubChannel {")
cpp_quote("    AMTUNER_SUBCHAN_NO_TUNE = -2,AMTUNER_SUBCHAN_DEFAULT = -1")
cpp_quote("  } AMTunerSubChannel;")
cpp_quote("")
cpp_quote("  typedef enum tagAMTunerSignalStrength {")
cpp_quote("    AMTUNER_HASNOSIGNALSTRENGTH = -1,AMTUNER_NOSIGNAL = 0,AMTUNER_SIGNALPRESENT = 1")
cpp_quote("  } AMTunerSignalStrength;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0178_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0178_v0_0_s_ifspec;")
cpp_quote("")
cpp_quote("#ifndef __IBPCSatelliteTuner_INTERFACE_DEFINED__")
cpp_quote("#define __IBPCSatelliteTuner_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IBPCSatelliteTuner;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IBPCSatelliteTuner : public IAMTuner {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI get_DefaultSubChannelTypes(LONG *plDefaultVideoType,LONG *plDefaultAudioType) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_DefaultSubChannelTypes(LONG lDefaultVideoType,LONG lDefaultAudioType) = 0;")
cpp_quote("    virtual HRESULT WINAPI IsTapingPermitted(void) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IBPCSatelliteTunerVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IBPCSatelliteTuner *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IBPCSatelliteTuner *This);")
cpp_quote("      ULONG (WINAPI *Release)(IBPCSatelliteTuner *This);")
cpp_quote("      HRESULT (WINAPI *put_Channel)(IBPCSatelliteTuner *This,LONG lChannel,LONG lVideoSubChannel,LONG lAudioSubChannel);")
cpp_quote("      HRESULT (WINAPI *get_Channel)(IBPCSatelliteTuner *This,LONG *plChannel,LONG *plVideoSubChannel,LONG *plAudioSubChannel);")
cpp_quote("      HRESULT (WINAPI *ChannelMinMax)(IBPCSatelliteTuner *This,LONG *lChannelMin,LONG *lChannelMax);")
cpp_quote("      HRESULT (WINAPI *put_CountryCode)(IBPCSatelliteTuner *This,LONG lCountryCode);")
cpp_quote("      HRESULT (WINAPI *get_CountryCode)(IBPCSatelliteTuner *This,LONG *plCountryCode);")
cpp_quote("      HRESULT (WINAPI *put_TuningSpace)(IBPCSatelliteTuner *This,LONG lTuningSpace);")
cpp_quote("      HRESULT (WINAPI *get_TuningSpace)(IBPCSatelliteTuner *This,LONG *plTuningSpace);")
cpp_quote("      HRESULT (WINAPI *Logon)(IBPCSatelliteTuner *This,HANDLE hCurrentUser);")
cpp_quote("      HRESULT (WINAPI *Logout)(IBPCSatelliteTuner *This);")
cpp_quote("      HRESULT (WINAPI *SignalPresent)(IBPCSatelliteTuner *This,LONG *plSignalStrength);")
cpp_quote("      HRESULT (WINAPI *put_Mode)(IBPCSatelliteTuner *This,AMTunerModeType lMode);")
cpp_quote("      HRESULT (WINAPI *get_Mode)(IBPCSatelliteTuner *This,AMTunerModeType *plMode);")
cpp_quote("      HRESULT (WINAPI *GetAvailableModes)(IBPCSatelliteTuner *This,LONG *plModes);")
cpp_quote("      HRESULT (WINAPI *RegisterNotificationCallBack)(IBPCSatelliteTuner *This,IAMTunerNotification *pNotify,LONG lEvents);")
cpp_quote("      HRESULT (WINAPI *UnRegisterNotificationCallBack)(IBPCSatelliteTuner *This,IAMTunerNotification *pNotify);")
cpp_quote("      HRESULT (WINAPI *get_DefaultSubChannelTypes)(IBPCSatelliteTuner *This,LONG *plDefaultVideoType,LONG *plDefaultAudioType);")
cpp_quote("      HRESULT (WINAPI *put_DefaultSubChannelTypes)(IBPCSatelliteTuner *This,LONG lDefaultVideoType,LONG lDefaultAudioType);")
cpp_quote("      HRESULT (WINAPI *IsTapingPermitted)(IBPCSatelliteTuner *This);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IBPCSatelliteTunerVtbl;")
cpp_quote("  struct IBPCSatelliteTuner {")
cpp_quote("    CONST_VTBL struct IBPCSatelliteTunerVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IBPCSatelliteTuner_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IBPCSatelliteTuner_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IBPCSatelliteTuner_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IBPCSatelliteTuner_put_Channel(This,lChannel,lVideoSubChannel,lAudioSubChannel) (This)->lpVtbl->put_Channel(This,lChannel,lVideoSubChannel,lAudioSubChannel)")
cpp_quote("#define IBPCSatelliteTuner_get_Channel(This,plChannel,plVideoSubChannel,plAudioSubChannel) (This)->lpVtbl->get_Channel(This,plChannel,plVideoSubChannel,plAudioSubChannel)")
cpp_quote("#define IBPCSatelliteTuner_ChannelMinMax(This,lChannelMin,lChannelMax) (This)->lpVtbl->ChannelMinMax(This,lChannelMin,lChannelMax)")
cpp_quote("#define IBPCSatelliteTuner_put_CountryCode(This,lCountryCode) (This)->lpVtbl->put_CountryCode(This,lCountryCode)")
cpp_quote("#define IBPCSatelliteTuner_get_CountryCode(This,plCountryCode) (This)->lpVtbl->get_CountryCode(This,plCountryCode)")
cpp_quote("#define IBPCSatelliteTuner_put_TuningSpace(This,lTuningSpace) (This)->lpVtbl->put_TuningSpace(This,lTuningSpace)")
cpp_quote("#define IBPCSatelliteTuner_get_TuningSpace(This,plTuningSpace) (This)->lpVtbl->get_TuningSpace(This,plTuningSpace)")
cpp_quote("#define IBPCSatelliteTuner_Logon(This,hCurrentUser) (This)->lpVtbl->Logon(This,hCurrentUser)")
cpp_quote("#define IBPCSatelliteTuner_Logout(This) (This)->lpVtbl->Logout(This)")
cpp_quote("#define IBPCSatelliteTuner_SignalPresent(This,plSignalStrength) (This)->lpVtbl->SignalPresent(This,plSignalStrength)")
cpp_quote("#define IBPCSatelliteTuner_put_Mode(This,lMode) (This)->lpVtbl->put_Mode(This,lMode)")
cpp_quote("#define IBPCSatelliteTuner_get_Mode(This,plMode) (This)->lpVtbl->get_Mode(This,plMode)")
cpp_quote("#define IBPCSatelliteTuner_GetAvailableModes(This,plModes) (This)->lpVtbl->GetAvailableModes(This,plModes)")
cpp_quote("#define IBPCSatelliteTuner_RegisterNotificationCallBack(This,pNotify,lEvents) (This)->lpVtbl->RegisterNotificationCallBack(This,pNotify,lEvents)")
cpp_quote("#define IBPCSatelliteTuner_UnRegisterNotificationCallBack(This,pNotify) (This)->lpVtbl->UnRegisterNotificationCallBack(This,pNotify)")
cpp_quote("#define IBPCSatelliteTuner_get_DefaultSubChannelTypes(This,plDefaultVideoType,plDefaultAudioType) (This)->lpVtbl->get_DefaultSubChannelTypes(This,plDefaultVideoType,plDefaultAudioType)")
cpp_quote("#define IBPCSatelliteTuner_put_DefaultSubChannelTypes(This,lDefaultVideoType,lDefaultAudioType) (This)->lpVtbl->put_DefaultSubChannelTypes(This,lDefaultVideoType,lDefaultAudioType)")
cpp_quote("#define IBPCSatelliteTuner_IsTapingPermitted(This) (This)->lpVtbl->IsTapingPermitted(This)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IBPCSatelliteTuner_get_DefaultSubChannelTypes_Proxy(IBPCSatelliteTuner *This,LONG *plDefaultVideoType,LONG *plDefaultAudioType);")
cpp_quote("  void __RPC_STUB IBPCSatelliteTuner_get_DefaultSubChannelTypes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBPCSatelliteTuner_put_DefaultSubChannelTypes_Proxy(IBPCSatelliteTuner *This,LONG lDefaultVideoType,LONG lDefaultAudioType);")
cpp_quote("  void __RPC_STUB IBPCSatelliteTuner_put_DefaultSubChannelTypes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IBPCSatelliteTuner_IsTapingPermitted_Proxy(IBPCSatelliteTuner *This);")
cpp_quote("  void __RPC_STUB IBPCSatelliteTuner_IsTapingPermitted_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef enum tagAMTVAudioEventType {")
cpp_quote("    AMTVAUDIO_EVENT_CHANGED = 0x1")
cpp_quote("  } AMTVAudioEventType;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0182_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0182_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMTVAudio_INTERFACE_DEFINED__")
cpp_quote("#define __IAMTVAudio_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMTVAudio;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMTVAudio : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetHardwareSupportedTVAudioModes(LONG *plModes) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetAvailableTVAudioModes(LONG *plModes) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_TVAudioMode(LONG *plMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_TVAudioMode(LONG lMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI RegisterNotificationCallBack(IAMTunerNotification *pNotify,LONG lEvents) = 0;")
cpp_quote("    virtual HRESULT WINAPI UnRegisterNotificationCallBack(IAMTunerNotification *pNotify) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMTVAudioVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMTVAudio *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMTVAudio *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMTVAudio *This);")
cpp_quote("      HRESULT (WINAPI *GetHardwareSupportedTVAudioModes)(IAMTVAudio *This,LONG *plModes);")
cpp_quote("      HRESULT (WINAPI *GetAvailableTVAudioModes)(IAMTVAudio *This,LONG *plModes);")
cpp_quote("      HRESULT (WINAPI *get_TVAudioMode)(IAMTVAudio *This,LONG *plMode);")
cpp_quote("      HRESULT (WINAPI *put_TVAudioMode)(IAMTVAudio *This,LONG lMode);")
cpp_quote("      HRESULT (WINAPI *RegisterNotificationCallBack)(IAMTVAudio *This,IAMTunerNotification *pNotify,LONG lEvents);")
cpp_quote("      HRESULT (WINAPI *UnRegisterNotificationCallBack)(IAMTVAudio *This,IAMTunerNotification *pNotify);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMTVAudioVtbl;")
cpp_quote("  struct IAMTVAudio {")
cpp_quote("    CONST_VTBL struct IAMTVAudioVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMTVAudio_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMTVAudio_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMTVAudio_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMTVAudio_GetHardwareSupportedTVAudioModes(This,plModes) (This)->lpVtbl->GetHardwareSupportedTVAudioModes(This,plModes)")
cpp_quote("#define IAMTVAudio_GetAvailableTVAudioModes(This,plModes) (This)->lpVtbl->GetAvailableTVAudioModes(This,plModes)")
cpp_quote("#define IAMTVAudio_get_TVAudioMode(This,plMode) (This)->lpVtbl->get_TVAudioMode(This,plMode)")
cpp_quote("#define IAMTVAudio_put_TVAudioMode(This,lMode) (This)->lpVtbl->put_TVAudioMode(This,lMode)")
cpp_quote("#define IAMTVAudio_RegisterNotificationCallBack(This,pNotify,lEvents) (This)->lpVtbl->RegisterNotificationCallBack(This,pNotify,lEvents)")
cpp_quote("#define IAMTVAudio_UnRegisterNotificationCallBack(This,pNotify) (This)->lpVtbl->UnRegisterNotificationCallBack(This,pNotify)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMTVAudio_GetHardwareSupportedTVAudioModes_Proxy(IAMTVAudio *This,LONG *plModes);")
cpp_quote("  void __RPC_STUB IAMTVAudio_GetHardwareSupportedTVAudioModes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTVAudio_GetAvailableTVAudioModes_Proxy(IAMTVAudio *This,LONG *plModes);")
cpp_quote("  void __RPC_STUB IAMTVAudio_GetAvailableTVAudioModes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTVAudio_get_TVAudioMode_Proxy(IAMTVAudio *This,LONG *plMode);")
cpp_quote("  void __RPC_STUB IAMTVAudio_get_TVAudioMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTVAudio_put_TVAudioMode_Proxy(IAMTVAudio *This,LONG lMode);")
cpp_quote("  void __RPC_STUB IAMTVAudio_put_TVAudioMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTVAudio_RegisterNotificationCallBack_Proxy(IAMTVAudio *This,IAMTunerNotification *pNotify,LONG lEvents);")
cpp_quote("  void __RPC_STUB IAMTVAudio_RegisterNotificationCallBack_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTVAudio_UnRegisterNotificationCallBack_Proxy(IAMTVAudio *This,IAMTunerNotification *pNotify);")
cpp_quote("  void __RPC_STUB IAMTVAudio_UnRegisterNotificationCallBack_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMTVAudioNotification_INTERFACE_DEFINED__")
cpp_quote("#define __IAMTVAudioNotification_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMTVAudioNotification;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMTVAudioNotification : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI OnEvent(AMTVAudioEventType Event) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMTVAudioNotificationVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMTVAudioNotification *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMTVAudioNotification *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMTVAudioNotification *This);")
cpp_quote("      HRESULT (WINAPI *OnEvent)(IAMTVAudioNotification *This,AMTVAudioEventType Event);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMTVAudioNotificationVtbl;")
cpp_quote("  struct IAMTVAudioNotification {")
cpp_quote("    CONST_VTBL struct IAMTVAudioNotificationVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMTVAudioNotification_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMTVAudioNotification_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMTVAudioNotification_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMTVAudioNotification_OnEvent(This,Event) (This)->lpVtbl->OnEvent(This,Event)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMTVAudioNotification_OnEvent_Proxy(IAMTVAudioNotification *This,AMTVAudioEventType Event);")
cpp_quote("  void __RPC_STUB IAMTVAudioNotification_OnEvent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMAnalogVideoEncoder_INTERFACE_DEFINED__")
cpp_quote("#define __IAMAnalogVideoEncoder_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMAnalogVideoEncoder;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMAnalogVideoEncoder : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI get_AvailableTVFormats(LONG *lAnalogVideoStandard) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_TVFormat(LONG lAnalogVideoStandard) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_TVFormat(LONG *plAnalogVideoStandard) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_CopyProtection(LONG lVideoCopyProtection) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_CopyProtection(LONG *lVideoCopyProtection) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_CCEnable(LONG lCCEnable) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_CCEnable(LONG *lCCEnable) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMAnalogVideoEncoderVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMAnalogVideoEncoder *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMAnalogVideoEncoder *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMAnalogVideoEncoder *This);")
cpp_quote("      HRESULT (WINAPI *get_AvailableTVFormats)(IAMAnalogVideoEncoder *This,LONG *lAnalogVideoStandard);")
cpp_quote("      HRESULT (WINAPI *put_TVFormat)(IAMAnalogVideoEncoder *This,LONG lAnalogVideoStandard);")
cpp_quote("      HRESULT (WINAPI *get_TVFormat)(IAMAnalogVideoEncoder *This,LONG *plAnalogVideoStandard);")
cpp_quote("      HRESULT (WINAPI *put_CopyProtection)(IAMAnalogVideoEncoder *This,LONG lVideoCopyProtection);")
cpp_quote("      HRESULT (WINAPI *get_CopyProtection)(IAMAnalogVideoEncoder *This,LONG *lVideoCopyProtection);")
cpp_quote("      HRESULT (WINAPI *put_CCEnable)(IAMAnalogVideoEncoder *This,LONG lCCEnable);")
cpp_quote("      HRESULT (WINAPI *get_CCEnable)(IAMAnalogVideoEncoder *This,LONG *lCCEnable);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMAnalogVideoEncoderVtbl;")
cpp_quote("  struct IAMAnalogVideoEncoder {")
cpp_quote("    CONST_VTBL struct IAMAnalogVideoEncoderVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMAnalogVideoEncoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMAnalogVideoEncoder_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMAnalogVideoEncoder_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMAnalogVideoEncoder_get_AvailableTVFormats(This,lAnalogVideoStandard) (This)->lpVtbl->get_AvailableTVFormats(This,lAnalogVideoStandard)")
cpp_quote("#define IAMAnalogVideoEncoder_put_TVFormat(This,lAnalogVideoStandard) (This)->lpVtbl->put_TVFormat(This,lAnalogVideoStandard)")
cpp_quote("#define IAMAnalogVideoEncoder_get_TVFormat(This,plAnalogVideoStandard) (This)->lpVtbl->get_TVFormat(This,plAnalogVideoStandard)")
cpp_quote("#define IAMAnalogVideoEncoder_put_CopyProtection(This,lVideoCopyProtection) (This)->lpVtbl->put_CopyProtection(This,lVideoCopyProtection)")
cpp_quote("#define IAMAnalogVideoEncoder_get_CopyProtection(This,lVideoCopyProtection) (This)->lpVtbl->get_CopyProtection(This,lVideoCopyProtection)")
cpp_quote("#define IAMAnalogVideoEncoder_put_CCEnable(This,lCCEnable) (This)->lpVtbl->put_CCEnable(This,lCCEnable)")
cpp_quote("#define IAMAnalogVideoEncoder_get_CCEnable(This,lCCEnable) (This)->lpVtbl->get_CCEnable(This,lCCEnable)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoEncoder_get_AvailableTVFormats_Proxy(IAMAnalogVideoEncoder *This,LONG *lAnalogVideoStandard);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoEncoder_get_AvailableTVFormats_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoEncoder_put_TVFormat_Proxy(IAMAnalogVideoEncoder *This,LONG lAnalogVideoStandard);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoEncoder_put_TVFormat_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoEncoder_get_TVFormat_Proxy(IAMAnalogVideoEncoder *This,LONG *plAnalogVideoStandard);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoEncoder_get_TVFormat_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoEncoder_put_CopyProtection_Proxy(IAMAnalogVideoEncoder *This,LONG lVideoCopyProtection);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoEncoder_put_CopyProtection_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoEncoder_get_CopyProtection_Proxy(IAMAnalogVideoEncoder *This,LONG *lVideoCopyProtection);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoEncoder_get_CopyProtection_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoEncoder_put_CCEnable_Proxy(IAMAnalogVideoEncoder *This,LONG lCCEnable);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoEncoder_put_CCEnable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMAnalogVideoEncoder_get_CCEnable_Proxy(IAMAnalogVideoEncoder *This,LONG *lCCEnable);")
cpp_quote("  void __RPC_STUB IAMAnalogVideoEncoder_get_CCEnable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMPhysicalPinInfo_INTERFACE_DEFINED__")
cpp_quote("#define __IAMPhysicalPinInfo_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMPhysicalPinInfo;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMPhysicalPinInfo : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetPhysicalType(LONG *pType,LPOLESTR *ppszType) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMPhysicalPinInfoVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMPhysicalPinInfo *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMPhysicalPinInfo *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMPhysicalPinInfo *This);")
cpp_quote("      HRESULT (WINAPI *GetPhysicalType)(IAMPhysicalPinInfo *This,LONG *pType,LPOLESTR *ppszType);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMPhysicalPinInfoVtbl;")
cpp_quote("  struct IAMPhysicalPinInfo {")
cpp_quote("    CONST_VTBL struct IAMPhysicalPinInfoVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMPhysicalPinInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMPhysicalPinInfo_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMPhysicalPinInfo_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMPhysicalPinInfo_GetPhysicalType(This,pType,ppszType) (This)->lpVtbl->GetPhysicalType(This,pType,ppszType)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMPhysicalPinInfo_GetPhysicalType_Proxy(IAMPhysicalPinInfo *This,LONG *pType,LPOLESTR *ppszType);")
cpp_quote("  void __RPC_STUB IAMPhysicalPinInfo_GetPhysicalType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef IAMPhysicalPinInfo *PAMPHYSICALPININFO;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0338_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0338_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMExtDevice_INTERFACE_DEFINED__")
cpp_quote("#define __IAMExtDevice_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMExtDevice;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMExtDevice : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetCapability(LONG Capability,LONG *pValue,double *pdblValue) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_ExternalDeviceID(LPOLESTR *ppszData) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_ExternalDeviceVersion(LPOLESTR *ppszData) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_DevicePower(LONG PowerMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_DevicePower(LONG *pPowerMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI Calibrate(HEVENT hEvent,LONG Mode,LONG *pStatus) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_DevicePort(LONG DevicePort) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_DevicePort(LONG *pDevicePort) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMExtDeviceVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMExtDevice *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMExtDevice *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMExtDevice *This);")
cpp_quote("      HRESULT (WINAPI *GetCapability)(IAMExtDevice *This,LONG Capability,LONG *pValue,double *pdblValue);")
cpp_quote("      HRESULT (WINAPI *get_ExternalDeviceID)(IAMExtDevice *This,LPOLESTR *ppszData);")
cpp_quote("      HRESULT (WINAPI *get_ExternalDeviceVersion)(IAMExtDevice *This,LPOLESTR *ppszData);")
cpp_quote("      HRESULT (WINAPI *put_DevicePower)(IAMExtDevice *This,LONG PowerMode);")
cpp_quote("      HRESULT (WINAPI *get_DevicePower)(IAMExtDevice *This,LONG *pPowerMode);")
cpp_quote("      HRESULT (WINAPI *Calibrate)(IAMExtDevice *This,HEVENT hEvent,LONG Mode,LONG *pStatus);")
cpp_quote("      HRESULT (WINAPI *put_DevicePort)(IAMExtDevice *This,LONG DevicePort);")
cpp_quote("      HRESULT (WINAPI *get_DevicePort)(IAMExtDevice *This,LONG *pDevicePort);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMExtDeviceVtbl;")
cpp_quote("  struct IAMExtDevice {")
cpp_quote("    CONST_VTBL struct IAMExtDeviceVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMExtDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMExtDevice_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMExtDevice_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMExtDevice_GetCapability(This,Capability,pValue,pdblValue) (This)->lpVtbl->GetCapability(This,Capability,pValue,pdblValue)")
cpp_quote("#define IAMExtDevice_get_ExternalDeviceID(This,ppszData) (This)->lpVtbl->get_ExternalDeviceID(This,ppszData)")
cpp_quote("#define IAMExtDevice_get_ExternalDeviceVersion(This,ppszData) (This)->lpVtbl->get_ExternalDeviceVersion(This,ppszData)")
cpp_quote("#define IAMExtDevice_put_DevicePower(This,PowerMode) (This)->lpVtbl->put_DevicePower(This,PowerMode)")
cpp_quote("#define IAMExtDevice_get_DevicePower(This,pPowerMode) (This)->lpVtbl->get_DevicePower(This,pPowerMode)")
cpp_quote("#define IAMExtDevice_Calibrate(This,hEvent,Mode,pStatus) (This)->lpVtbl->Calibrate(This,hEvent,Mode,pStatus)")
cpp_quote("#define IAMExtDevice_put_DevicePort(This,DevicePort) (This)->lpVtbl->put_DevicePort(This,DevicePort)")
cpp_quote("#define IAMExtDevice_get_DevicePort(This,pDevicePort) (This)->lpVtbl->get_DevicePort(This,pDevicePort)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMExtDevice_GetCapability_Proxy(IAMExtDevice *This,LONG Capability,LONG *pValue,double *pdblValue);")
cpp_quote("  void __RPC_STUB IAMExtDevice_GetCapability_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtDevice_get_ExternalDeviceID_Proxy(IAMExtDevice *This,LPOLESTR *ppszData);")
cpp_quote("  void __RPC_STUB IAMExtDevice_get_ExternalDeviceID_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtDevice_get_ExternalDeviceVersion_Proxy(IAMExtDevice *This,LPOLESTR *ppszData);")
cpp_quote("  void __RPC_STUB IAMExtDevice_get_ExternalDeviceVersion_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtDevice_put_DevicePower_Proxy(IAMExtDevice *This,LONG PowerMode);")
cpp_quote("  void __RPC_STUB IAMExtDevice_put_DevicePower_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtDevice_get_DevicePower_Proxy(IAMExtDevice *This,LONG *pPowerMode);")
cpp_quote("  void __RPC_STUB IAMExtDevice_get_DevicePower_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtDevice_Calibrate_Proxy(IAMExtDevice *This,HEVENT hEvent,LONG Mode,LONG *pStatus);")
cpp_quote("  void __RPC_STUB IAMExtDevice_Calibrate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtDevice_put_DevicePort_Proxy(IAMExtDevice *This,LONG DevicePort);")
cpp_quote("  void __RPC_STUB IAMExtDevice_put_DevicePort_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtDevice_get_DevicePort_Proxy(IAMExtDevice *This,LONG *pDevicePort);")
cpp_quote("  void __RPC_STUB IAMExtDevice_get_DevicePort_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef IAMExtDevice *PEXTDEVICE;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0339_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0339_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMExtTransport_INTERFACE_DEFINED__")
cpp_quote("#define __IAMExtTransport_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMExtTransport;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMExtTransport : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetCapability(LONG Capability,LONG *pValue,double *pdblValue) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_MediaState(LONG State) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_MediaState(LONG *pState) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_LocalControl(LONG State) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_LocalControl(LONG *pState) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetStatus(LONG StatusItem,LONG *pValue) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetTransportBasicParameters(LONG Param,LONG *pValue,LPOLESTR *ppszData) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetTransportBasicParameters(LONG Param,LONG Value,LPCOLESTR pszData) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetTransportVideoParameters(LONG Param,LONG *pValue) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetTransportVideoParameters(LONG Param,LONG Value) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetTransportAudioParameters(LONG Param,LONG *pValue) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetTransportAudioParameters(LONG Param,LONG Value) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Mode(LONG Mode) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Mode(LONG *pMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_Rate(double dblRate) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_Rate(double *pdblRate) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetChase(LONG *pEnabled,LONG *pOffset,HEVENT *phEvent) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetChase(LONG Enable,LONG Offset,HEVENT hEvent) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetBump(LONG *pSpeed,LONG *pDuration) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetBump(LONG Speed,LONG Duration) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_AntiClogControl(LONG *pEnabled) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_AntiClogControl(LONG Enable) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetEditPropertySet(LONG EditID,LONG *pState) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetEditPropertySet(LONG *pEditID,LONG State) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetEditProperty(LONG EditID,LONG Param,LONG *pValue) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetEditProperty(LONG EditID,LONG Param,LONG Value) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_EditStart(LONG *pValue) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_EditStart(LONG Value) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMExtTransportVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMExtTransport *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMExtTransport *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMExtTransport *This);")
cpp_quote("      HRESULT (WINAPI *GetCapability)(IAMExtTransport *This,LONG Capability,LONG *pValue,double *pdblValue);")
cpp_quote("      HRESULT (WINAPI *put_MediaState)(IAMExtTransport *This,LONG State);")
cpp_quote("      HRESULT (WINAPI *get_MediaState)(IAMExtTransport *This,LONG *pState);")
cpp_quote("      HRESULT (WINAPI *put_LocalControl)(IAMExtTransport *This,LONG State);")
cpp_quote("      HRESULT (WINAPI *get_LocalControl)(IAMExtTransport *This,LONG *pState);")
cpp_quote("      HRESULT (WINAPI *GetStatus)(IAMExtTransport *This,LONG StatusItem,LONG *pValue);")
cpp_quote("      HRESULT (WINAPI *GetTransportBasicParameters)(IAMExtTransport *This,LONG Param,LONG *pValue,LPOLESTR *ppszData);")
cpp_quote("      HRESULT (WINAPI *SetTransportBasicParameters)(IAMExtTransport *This,LONG Param,LONG Value,LPCOLESTR pszData);")
cpp_quote("      HRESULT (WINAPI *GetTransportVideoParameters)(IAMExtTransport *This,LONG Param,LONG *pValue);")
cpp_quote("      HRESULT (WINAPI *SetTransportVideoParameters)(IAMExtTransport *This,LONG Param,LONG Value);")
cpp_quote("      HRESULT (WINAPI *GetTransportAudioParameters)(IAMExtTransport *This,LONG Param,LONG *pValue);")
cpp_quote("      HRESULT (WINAPI *SetTransportAudioParameters)(IAMExtTransport *This,LONG Param,LONG Value);")
cpp_quote("      HRESULT (WINAPI *put_Mode)(IAMExtTransport *This,LONG Mode);")
cpp_quote("      HRESULT (WINAPI *get_Mode)(IAMExtTransport *This,LONG *pMode);")
cpp_quote("      HRESULT (WINAPI *put_Rate)(IAMExtTransport *This,double dblRate);")
cpp_quote("      HRESULT (WINAPI *get_Rate)(IAMExtTransport *This,double *pdblRate);")
cpp_quote("      HRESULT (WINAPI *GetChase)(IAMExtTransport *This,LONG *pEnabled,LONG *pOffset,HEVENT *phEvent);")
cpp_quote("      HRESULT (WINAPI *SetChase)(IAMExtTransport *This,LONG Enable,LONG Offset,HEVENT hEvent);")
cpp_quote("      HRESULT (WINAPI *GetBump)(IAMExtTransport *This,LONG *pSpeed,LONG *pDuration);")
cpp_quote("      HRESULT (WINAPI *SetBump)(IAMExtTransport *This,LONG Speed,LONG Duration);")
cpp_quote("      HRESULT (WINAPI *get_AntiClogControl)(IAMExtTransport *This,LONG *pEnabled);")
cpp_quote("      HRESULT (WINAPI *put_AntiClogControl)(IAMExtTransport *This,LONG Enable);")
cpp_quote("      HRESULT (WINAPI *GetEditPropertySet)(IAMExtTransport *This,LONG EditID,LONG *pState);")
cpp_quote("      HRESULT (WINAPI *SetEditPropertySet)(IAMExtTransport *This,LONG *pEditID,LONG State);")
cpp_quote("      HRESULT (WINAPI *GetEditProperty)(IAMExtTransport *This,LONG EditID,LONG Param,LONG *pValue);")
cpp_quote("      HRESULT (WINAPI *SetEditProperty)(IAMExtTransport *This,LONG EditID,LONG Param,LONG Value);")
cpp_quote("      HRESULT (WINAPI *get_EditStart)(IAMExtTransport *This,LONG *pValue);")
cpp_quote("      HRESULT (WINAPI *put_EditStart)(IAMExtTransport *This,LONG Value);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMExtTransportVtbl;")
cpp_quote("  struct IAMExtTransport {")
cpp_quote("    CONST_VTBL struct IAMExtTransportVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMExtTransport_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMExtTransport_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMExtTransport_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMExtTransport_GetCapability(This,Capability,pValue,pdblValue) (This)->lpVtbl->GetCapability(This,Capability,pValue,pdblValue)")
cpp_quote("#define IAMExtTransport_put_MediaState(This,State) (This)->lpVtbl->put_MediaState(This,State)")
cpp_quote("#define IAMExtTransport_get_MediaState(This,pState) (This)->lpVtbl->get_MediaState(This,pState)")
cpp_quote("#define IAMExtTransport_put_LocalControl(This,State) (This)->lpVtbl->put_LocalControl(This,State)")
cpp_quote("#define IAMExtTransport_get_LocalControl(This,pState) (This)->lpVtbl->get_LocalControl(This,pState)")
cpp_quote("#define IAMExtTransport_GetStatus(This,StatusItem,pValue) (This)->lpVtbl->GetStatus(This,StatusItem,pValue)")
cpp_quote("#define IAMExtTransport_GetTransportBasicParameters(This,Param,pValue,ppszData) (This)->lpVtbl->GetTransportBasicParameters(This,Param,pValue,ppszData)")
cpp_quote("#define IAMExtTransport_SetTransportBasicParameters(This,Param,Value,pszData) (This)->lpVtbl->SetTransportBasicParameters(This,Param,Value,pszData)")
cpp_quote("#define IAMExtTransport_GetTransportVideoParameters(This,Param,pValue) (This)->lpVtbl->GetTransportVideoParameters(This,Param,pValue)")
cpp_quote("#define IAMExtTransport_SetTransportVideoParameters(This,Param,Value) (This)->lpVtbl->SetTransportVideoParameters(This,Param,Value)")
cpp_quote("#define IAMExtTransport_GetTransportAudioParameters(This,Param,pValue) (This)->lpVtbl->GetTransportAudioParameters(This,Param,pValue)")
cpp_quote("#define IAMExtTransport_SetTransportAudioParameters(This,Param,Value) (This)->lpVtbl->SetTransportAudioParameters(This,Param,Value)")
cpp_quote("#define IAMExtTransport_put_Mode(This,Mode) (This)->lpVtbl->put_Mode(This,Mode)")
cpp_quote("#define IAMExtTransport_get_Mode(This,pMode) (This)->lpVtbl->get_Mode(This,pMode)")
cpp_quote("#define IAMExtTransport_put_Rate(This,dblRate) (This)->lpVtbl->put_Rate(This,dblRate)")
cpp_quote("#define IAMExtTransport_get_Rate(This,pdblRate) (This)->lpVtbl->get_Rate(This,pdblRate)")
cpp_quote("#define IAMExtTransport_GetChase(This,pEnabled,pOffset,phEvent) (This)->lpVtbl->GetChase(This,pEnabled,pOffset,phEvent)")
cpp_quote("#define IAMExtTransport_SetChase(This,Enable,Offset,hEvent) (This)->lpVtbl->SetChase(This,Enable,Offset,hEvent)")
cpp_quote("#define IAMExtTransport_GetBump(This,pSpeed,pDuration) (This)->lpVtbl->GetBump(This,pSpeed,pDuration)")
cpp_quote("#define IAMExtTransport_SetBump(This,Speed,Duration) (This)->lpVtbl->SetBump(This,Speed,Duration)")
cpp_quote("#define IAMExtTransport_get_AntiClogControl(This,pEnabled) (This)->lpVtbl->get_AntiClogControl(This,pEnabled)")
cpp_quote("#define IAMExtTransport_put_AntiClogControl(This,Enable) (This)->lpVtbl->put_AntiClogControl(This,Enable)")
cpp_quote("#define IAMExtTransport_GetEditPropertySet(This,EditID,pState) (This)->lpVtbl->GetEditPropertySet(This,EditID,pState)")
cpp_quote("#define IAMExtTransport_SetEditPropertySet(This,pEditID,State) (This)->lpVtbl->SetEditPropertySet(This,pEditID,State)")
cpp_quote("#define IAMExtTransport_GetEditProperty(This,EditID,Param,pValue) (This)->lpVtbl->GetEditProperty(This,EditID,Param,pValue)")
cpp_quote("#define IAMExtTransport_SetEditProperty(This,EditID,Param,Value) (This)->lpVtbl->SetEditProperty(This,EditID,Param,Value)")
cpp_quote("#define IAMExtTransport_get_EditStart(This,pValue) (This)->lpVtbl->get_EditStart(This,pValue)")
cpp_quote("#define IAMExtTransport_put_EditStart(This,Value) (This)->lpVtbl->put_EditStart(This,Value)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMExtTransport_GetCapability_Proxy(IAMExtTransport *This,LONG Capability,LONG *pValue,double *pdblValue);")
cpp_quote("  void __RPC_STUB IAMExtTransport_GetCapability_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_put_MediaState_Proxy(IAMExtTransport *This,LONG State);")
cpp_quote("  void __RPC_STUB IAMExtTransport_put_MediaState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_get_MediaState_Proxy(IAMExtTransport *This,LONG *pState);")
cpp_quote("  void __RPC_STUB IAMExtTransport_get_MediaState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_put_LocalControl_Proxy(IAMExtTransport *This,LONG State);")
cpp_quote("  void __RPC_STUB IAMExtTransport_put_LocalControl_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_get_LocalControl_Proxy(IAMExtTransport *This,LONG *pState);")
cpp_quote("  void __RPC_STUB IAMExtTransport_get_LocalControl_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_GetStatus_Proxy(IAMExtTransport *This,LONG StatusItem,LONG *pValue);")
cpp_quote("  void __RPC_STUB IAMExtTransport_GetStatus_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_GetTransportBasicParameters_Proxy(IAMExtTransport *This,LONG Param,LONG *pValue,LPOLESTR *ppszData);")
cpp_quote("  void __RPC_STUB IAMExtTransport_GetTransportBasicParameters_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_SetTransportBasicParameters_Proxy(IAMExtTransport *This,LONG Param,LONG Value,LPCOLESTR pszData);")
cpp_quote("  void __RPC_STUB IAMExtTransport_SetTransportBasicParameters_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_GetTransportVideoParameters_Proxy(IAMExtTransport *This,LONG Param,LONG *pValue);")
cpp_quote("  void __RPC_STUB IAMExtTransport_GetTransportVideoParameters_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_SetTransportVideoParameters_Proxy(IAMExtTransport *This,LONG Param,LONG Value);")
cpp_quote("  void __RPC_STUB IAMExtTransport_SetTransportVideoParameters_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_GetTransportAudioParameters_Proxy(IAMExtTransport *This,LONG Param,LONG *pValue);")
cpp_quote("  void __RPC_STUB IAMExtTransport_GetTransportAudioParameters_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_SetTransportAudioParameters_Proxy(IAMExtTransport *This,LONG Param,LONG Value);")
cpp_quote("  void __RPC_STUB IAMExtTransport_SetTransportAudioParameters_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_put_Mode_Proxy(IAMExtTransport *This,LONG Mode);")
cpp_quote("  void __RPC_STUB IAMExtTransport_put_Mode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_get_Mode_Proxy(IAMExtTransport *This,LONG *pMode);")
cpp_quote("  void __RPC_STUB IAMExtTransport_get_Mode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_put_Rate_Proxy(IAMExtTransport *This,double dblRate);")
cpp_quote("  void __RPC_STUB IAMExtTransport_put_Rate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_get_Rate_Proxy(IAMExtTransport *This,double *pdblRate);")
cpp_quote("  void __RPC_STUB IAMExtTransport_get_Rate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_GetChase_Proxy(IAMExtTransport *This,LONG *pEnabled,LONG *pOffset,HEVENT *phEvent);")
cpp_quote("  void __RPC_STUB IAMExtTransport_GetChase_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_SetChase_Proxy(IAMExtTransport *This,LONG Enable,LONG Offset,HEVENT hEvent);")
cpp_quote("  void __RPC_STUB IAMExtTransport_SetChase_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_GetBump_Proxy(IAMExtTransport *This,LONG *pSpeed,LONG *pDuration);")
cpp_quote("  void __RPC_STUB IAMExtTransport_GetBump_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_SetBump_Proxy(IAMExtTransport *This,LONG Speed,LONG Duration);")
cpp_quote("  void __RPC_STUB IAMExtTransport_SetBump_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_get_AntiClogControl_Proxy(IAMExtTransport *This,LONG *pEnabled);")
cpp_quote("  void __RPC_STUB IAMExtTransport_get_AntiClogControl_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_put_AntiClogControl_Proxy(IAMExtTransport *This,LONG Enable);")
cpp_quote("  void __RPC_STUB IAMExtTransport_put_AntiClogControl_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_GetEditPropertySet_Proxy(IAMExtTransport *This,LONG EditID,LONG *pState);")
cpp_quote("  void __RPC_STUB IAMExtTransport_GetEditPropertySet_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_SetEditPropertySet_Proxy(IAMExtTransport *This,LONG *pEditID,LONG State);")
cpp_quote("  void __RPC_STUB IAMExtTransport_SetEditPropertySet_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_GetEditProperty_Proxy(IAMExtTransport *This,LONG EditID,LONG Param,LONG *pValue);")
cpp_quote("  void __RPC_STUB IAMExtTransport_GetEditProperty_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_SetEditProperty_Proxy(IAMExtTransport *This,LONG EditID,LONG Param,LONG Value);")
cpp_quote("  void __RPC_STUB IAMExtTransport_SetEditProperty_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_get_EditStart_Proxy(IAMExtTransport *This,LONG *pValue);")
cpp_quote("  void __RPC_STUB IAMExtTransport_get_EditStart_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMExtTransport_put_EditStart_Proxy(IAMExtTransport *This,LONG Value);")
cpp_quote("  void __RPC_STUB IAMExtTransport_put_EditStart_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef IAMExtTransport *PIAMEXTTRANSPORT;")
cpp_quote("")
cpp_quote("#ifndef TIMECODE_DEFINED")
cpp_quote("#define TIMECODE_DEFINED")
cpp_quote("  typedef union _timecode {")
cpp_quote("    __C89_NAMELESS struct {")
cpp_quote("      WORD wFrameRate;")
cpp_quote("      WORD wFrameFract;")
cpp_quote("      DWORD dwFrames;")
cpp_quote("    };")
cpp_quote("    DWORDLONG qw;")
cpp_quote("  } TIMECODE;")
cpp_quote("")
cpp_quote("  typedef TIMECODE *PTIMECODE;")
cpp_quote("")
cpp_quote("  typedef struct tagTIMECODE_SAMPLE {")
cpp_quote("    LONGLONG qwTick;")
cpp_quote("    TIMECODE timecode;")
cpp_quote("    DWORD dwUser;")
cpp_quote("    DWORD dwFlags;")
cpp_quote("  } TIMECODE_SAMPLE;")
cpp_quote("")
cpp_quote("  typedef TIMECODE_SAMPLE *PTIMECODE_SAMPLE;")
cpp_quote("#endif /* TIMECODE_DEFINED */")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0340_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0340_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMTimecodeReader_INTERFACE_DEFINED__")
cpp_quote("#define __IAMTimecodeReader_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMTimecodeReader;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMTimecodeReader : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetTCRMode(LONG Param,LONG *pValue) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetTCRMode(LONG Param,LONG Value) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_VITCLine(LONG Line) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_VITCLine(LONG *pLine) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetTimecode(PTIMECODE_SAMPLE pTimecodeSample) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMTimecodeReaderVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMTimecodeReader *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMTimecodeReader *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMTimecodeReader *This);")
cpp_quote("      HRESULT (WINAPI *GetTCRMode)(IAMTimecodeReader *This,LONG Param,LONG *pValue);")
cpp_quote("      HRESULT (WINAPI *SetTCRMode)(IAMTimecodeReader *This,LONG Param,LONG Value);")
cpp_quote("      HRESULT (WINAPI *put_VITCLine)(IAMTimecodeReader *This,LONG Line);")
cpp_quote("      HRESULT (WINAPI *get_VITCLine)(IAMTimecodeReader *This,LONG *pLine);")
cpp_quote("      HRESULT (WINAPI *GetTimecode)(IAMTimecodeReader *This,PTIMECODE_SAMPLE pTimecodeSample);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMTimecodeReaderVtbl;")
cpp_quote("  struct IAMTimecodeReader {")
cpp_quote("    CONST_VTBL struct IAMTimecodeReaderVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMTimecodeReader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMTimecodeReader_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMTimecodeReader_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMTimecodeReader_GetTCRMode(This,Param,pValue) (This)->lpVtbl->GetTCRMode(This,Param,pValue)")
cpp_quote("#define IAMTimecodeReader_SetTCRMode(This,Param,Value) (This)->lpVtbl->SetTCRMode(This,Param,Value)")
cpp_quote("#define IAMTimecodeReader_put_VITCLine(This,Line) (This)->lpVtbl->put_VITCLine(This,Line)")
cpp_quote("#define IAMTimecodeReader_get_VITCLine(This,pLine) (This)->lpVtbl->get_VITCLine(This,pLine)")
cpp_quote("#define IAMTimecodeReader_GetTimecode(This,pTimecodeSample) (This)->lpVtbl->GetTimecode(This,pTimecodeSample)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMTimecodeReader_GetTCRMode_Proxy(IAMTimecodeReader *This,LONG Param,LONG *pValue);")
cpp_quote("  void __RPC_STUB IAMTimecodeReader_GetTCRMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTimecodeReader_SetTCRMode_Proxy(IAMTimecodeReader *This,LONG Param,LONG Value);")
cpp_quote("  void __RPC_STUB IAMTimecodeReader_SetTCRMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTimecodeReader_put_VITCLine_Proxy(IAMTimecodeReader *This,LONG Line);")
cpp_quote("  void __RPC_STUB IAMTimecodeReader_put_VITCLine_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTimecodeReader_get_VITCLine_Proxy(IAMTimecodeReader *This,LONG *pLine);")
cpp_quote("  void __RPC_STUB IAMTimecodeReader_get_VITCLine_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTimecodeReader_GetTimecode_Proxy(IAMTimecodeReader *This,PTIMECODE_SAMPLE pTimecodeSample);")
cpp_quote("  void __RPC_STUB IAMTimecodeReader_GetTimecode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef IAMTimecodeReader *PIAMTIMECODEREADER;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0341_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0341_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMTimecodeGenerator_INTERFACE_DEFINED__")
cpp_quote("#define __IAMTimecodeGenerator_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMTimecodeGenerator;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMTimecodeGenerator : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetTCGMode(LONG Param,LONG *pValue) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetTCGMode(LONG Param,LONG Value) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_VITCLine(LONG Line) = 0;")
cpp_quote("    virtual HRESULT WINAPI get_VITCLine(LONG *pLine) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetTimecode(PTIMECODE_SAMPLE pTimecodeSample) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetTimecode(PTIMECODE_SAMPLE pTimecodeSample) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMTimecodeGeneratorVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMTimecodeGenerator *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMTimecodeGenerator *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMTimecodeGenerator *This);")
cpp_quote("      HRESULT (WINAPI *GetTCGMode)(IAMTimecodeGenerator *This,LONG Param,LONG *pValue);")
cpp_quote("      HRESULT (WINAPI *SetTCGMode)(IAMTimecodeGenerator *This,LONG Param,LONG Value);")
cpp_quote("      HRESULT (WINAPI *put_VITCLine)(IAMTimecodeGenerator *This,LONG Line);")
cpp_quote("      HRESULT (WINAPI *get_VITCLine)(IAMTimecodeGenerator *This,LONG *pLine);")
cpp_quote("      HRESULT (WINAPI *SetTimecode)(IAMTimecodeGenerator *This,PTIMECODE_SAMPLE pTimecodeSample);")
cpp_quote("      HRESULT (WINAPI *GetTimecode)(IAMTimecodeGenerator *This,PTIMECODE_SAMPLE pTimecodeSample);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMTimecodeGeneratorVtbl;")
cpp_quote("  struct IAMTimecodeGenerator {")
cpp_quote("    CONST_VTBL struct IAMTimecodeGeneratorVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMTimecodeGenerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMTimecodeGenerator_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMTimecodeGenerator_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMTimecodeGenerator_GetTCGMode(This,Param,pValue) (This)->lpVtbl->GetTCGMode(This,Param,pValue)")
cpp_quote("#define IAMTimecodeGenerator_SetTCGMode(This,Param,Value) (This)->lpVtbl->SetTCGMode(This,Param,Value)")
cpp_quote("#define IAMTimecodeGenerator_put_VITCLine(This,Line) (This)->lpVtbl->put_VITCLine(This,Line)")
cpp_quote("#define IAMTimecodeGenerator_get_VITCLine(This,pLine) (This)->lpVtbl->get_VITCLine(This,pLine)")
cpp_quote("#define IAMTimecodeGenerator_SetTimecode(This,pTimecodeSample) (This)->lpVtbl->SetTimecode(This,pTimecodeSample)")
cpp_quote("#define IAMTimecodeGenerator_GetTimecode(This,pTimecodeSample) (This)->lpVtbl->GetTimecode(This,pTimecodeSample)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMTimecodeGenerator_GetTCGMode_Proxy(IAMTimecodeGenerator *This,LONG Param,LONG *pValue);")
cpp_quote("  void __RPC_STUB IAMTimecodeGenerator_GetTCGMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTimecodeGenerator_SetTCGMode_Proxy(IAMTimecodeGenerator *This,LONG Param,LONG Value);")
cpp_quote("  void __RPC_STUB IAMTimecodeGenerator_SetTCGMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTimecodeGenerator_put_VITCLine_Proxy(IAMTimecodeGenerator *This,LONG Line);")
cpp_quote("  void __RPC_STUB IAMTimecodeGenerator_put_VITCLine_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTimecodeGenerator_get_VITCLine_Proxy(IAMTimecodeGenerator *This,LONG *pLine);")
cpp_quote("  void __RPC_STUB IAMTimecodeGenerator_get_VITCLine_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTimecodeGenerator_SetTimecode_Proxy(IAMTimecodeGenerator *This,PTIMECODE_SAMPLE pTimecodeSample);")
cpp_quote("  void __RPC_STUB IAMTimecodeGenerator_SetTimecode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTimecodeGenerator_GetTimecode_Proxy(IAMTimecodeGenerator *This,PTIMECODE_SAMPLE pTimecodeSample);")
cpp_quote("  void __RPC_STUB IAMTimecodeGenerator_GetTimecode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef IAMTimecodeGenerator *PIAMTIMECODEGENERATOR;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0342_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0342_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMTimecodeDisplay_INTERFACE_DEFINED__")
cpp_quote("#define __IAMTimecodeDisplay_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMTimecodeDisplay;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMTimecodeDisplay : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetTCDisplayEnable(LONG *pState) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetTCDisplayEnable(LONG State) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetTCDisplay(LONG Param,LONG *pValue) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetTCDisplay(LONG Param,LONG Value) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMTimecodeDisplayVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMTimecodeDisplay *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMTimecodeDisplay *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMTimecodeDisplay *This);")
cpp_quote("      HRESULT (WINAPI *GetTCDisplayEnable)(IAMTimecodeDisplay *This,LONG *pState);")
cpp_quote("      HRESULT (WINAPI *SetTCDisplayEnable)(IAMTimecodeDisplay *This,LONG State);")
cpp_quote("      HRESULT (WINAPI *GetTCDisplay)(IAMTimecodeDisplay *This,LONG Param,LONG *pValue);")
cpp_quote("      HRESULT (WINAPI *SetTCDisplay)(IAMTimecodeDisplay *This,LONG Param,LONG Value);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMTimecodeDisplayVtbl;")
cpp_quote("  struct IAMTimecodeDisplay {")
cpp_quote("    CONST_VTBL struct IAMTimecodeDisplayVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMTimecodeDisplay_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMTimecodeDisplay_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMTimecodeDisplay_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMTimecodeDisplay_GetTCDisplayEnable(This,pState) (This)->lpVtbl->GetTCDisplayEnable(This,pState)")
cpp_quote("#define IAMTimecodeDisplay_SetTCDisplayEnable(This,State) (This)->lpVtbl->SetTCDisplayEnable(This,State)")
cpp_quote("#define IAMTimecodeDisplay_GetTCDisplay(This,Param,pValue) (This)->lpVtbl->GetTCDisplay(This,Param,pValue)")
cpp_quote("#define IAMTimecodeDisplay_SetTCDisplay(This,Param,Value) (This)->lpVtbl->SetTCDisplay(This,Param,Value)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMTimecodeDisplay_GetTCDisplayEnable_Proxy(IAMTimecodeDisplay *This,LONG *pState);")
cpp_quote("  void __RPC_STUB IAMTimecodeDisplay_GetTCDisplayEnable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTimecodeDisplay_SetTCDisplayEnable_Proxy(IAMTimecodeDisplay *This,LONG State);")
cpp_quote("  void __RPC_STUB IAMTimecodeDisplay_SetTCDisplayEnable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTimecodeDisplay_GetTCDisplay_Proxy(IAMTimecodeDisplay *This,LONG Param,LONG *pValue);")
cpp_quote("  void __RPC_STUB IAMTimecodeDisplay_GetTCDisplay_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMTimecodeDisplay_SetTCDisplay_Proxy(IAMTimecodeDisplay *This,LONG Param,LONG Value);")
cpp_quote("  void __RPC_STUB IAMTimecodeDisplay_SetTCDisplay_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef IAMTimecodeDisplay *PIAMTIMECODEDISPLAY;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0343_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0343_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMDevMemoryAllocator_INTERFACE_DEFINED__")
cpp_quote("#define __IAMDevMemoryAllocator_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMDevMemoryAllocator;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMDevMemoryAllocator : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetInfo(DWORD *pdwcbTotalFree,DWORD *pdwcbLargestFree,DWORD *pdwcbTotalMemory,DWORD *pdwcbMinimumChunk) = 0;")
cpp_quote("    virtual HRESULT WINAPI CheckMemory(const BYTE *pBuffer) = 0;")
cpp_quote("    virtual HRESULT WINAPI Alloc(BYTE **ppBuffer,DWORD *pdwcbBuffer) = 0;")
cpp_quote("    virtual HRESULT WINAPI Free(BYTE *pBuffer) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDevMemoryObject(IUnknown **ppUnkInnner,IUnknown *pUnkOuter) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMDevMemoryAllocatorVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMDevMemoryAllocator *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMDevMemoryAllocator *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMDevMemoryAllocator *This);")
cpp_quote("      HRESULT (WINAPI *GetInfo)(IAMDevMemoryAllocator *This,DWORD *pdwcbTotalFree,DWORD *pdwcbLargestFree,DWORD *pdwcbTotalMemory,DWORD *pdwcbMinimumChunk);")
cpp_quote("      HRESULT (WINAPI *CheckMemory)(IAMDevMemoryAllocator *This,const BYTE *pBuffer);")
cpp_quote("      HRESULT (WINAPI *Alloc)(IAMDevMemoryAllocator *This,BYTE **ppBuffer,DWORD *pdwcbBuffer);")
cpp_quote("      HRESULT (WINAPI *Free)(IAMDevMemoryAllocator *This,BYTE *pBuffer);")
cpp_quote("      HRESULT (WINAPI *GetDevMemoryObject)(IAMDevMemoryAllocator *This,IUnknown **ppUnkInnner,IUnknown *pUnkOuter);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMDevMemoryAllocatorVtbl;")
cpp_quote("  struct IAMDevMemoryAllocator {")
cpp_quote("    CONST_VTBL struct IAMDevMemoryAllocatorVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMDevMemoryAllocator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMDevMemoryAllocator_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMDevMemoryAllocator_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMDevMemoryAllocator_GetInfo(This,pdwcbTotalFree,pdwcbLargestFree,pdwcbTotalMemory,pdwcbMinimumChunk) (This)->lpVtbl->GetInfo(This,pdwcbTotalFree,pdwcbLargestFree,pdwcbTotalMemory,pdwcbMinimumChunk)")
cpp_quote("#define IAMDevMemoryAllocator_CheckMemory(This,pBuffer) (This)->lpVtbl->CheckMemory(This,pBuffer)")
cpp_quote("#define IAMDevMemoryAllocator_Alloc(This,ppBuffer,pdwcbBuffer) (This)->lpVtbl->Alloc(This,ppBuffer,pdwcbBuffer)")
cpp_quote("#define IAMDevMemoryAllocator_Free(This,pBuffer) (This)->lpVtbl->Free(This,pBuffer)")
cpp_quote("#define IAMDevMemoryAllocator_GetDevMemoryObject(This,ppUnkInnner,pUnkOuter) (This)->lpVtbl->GetDevMemoryObject(This,ppUnkInnner,pUnkOuter)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMDevMemoryAllocator_GetInfo_Proxy(IAMDevMemoryAllocator *This,DWORD *pdwcbTotalFree,DWORD *pdwcbLargestFree,DWORD *pdwcbTotalMemory,DWORD *pdwcbMinimumChunk);")
cpp_quote("  void __RPC_STUB IAMDevMemoryAllocator_GetInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMDevMemoryAllocator_CheckMemory_Proxy(IAMDevMemoryAllocator *This,const BYTE *pBuffer);")
cpp_quote("  void __RPC_STUB IAMDevMemoryAllocator_CheckMemory_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMDevMemoryAllocator_Alloc_Proxy(IAMDevMemoryAllocator *This,BYTE **ppBuffer,DWORD *pdwcbBuffer);")
cpp_quote("  void __RPC_STUB IAMDevMemoryAllocator_Alloc_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMDevMemoryAllocator_Free_Proxy(IAMDevMemoryAllocator *This,BYTE *pBuffer);")
cpp_quote("  void __RPC_STUB IAMDevMemoryAllocator_Free_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMDevMemoryAllocator_GetDevMemoryObject_Proxy(IAMDevMemoryAllocator *This,IUnknown **ppUnkInnner,IUnknown *pUnkOuter);")
cpp_quote("  void __RPC_STUB IAMDevMemoryAllocator_GetDevMemoryObject_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef IAMDevMemoryAllocator *PAMDEVMEMORYALLOCATOR;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0344_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0344_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMDevMemoryControl_INTERFACE_DEFINED__")
cpp_quote("#define __IAMDevMemoryControl_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMDevMemoryControl;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMDevMemoryControl : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI QueryWriteSync(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI WriteSync(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDevId(DWORD *pdwDevId) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMDevMemoryControlVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMDevMemoryControl *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMDevMemoryControl *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMDevMemoryControl *This);")
cpp_quote("      HRESULT (WINAPI *QueryWriteSync)(IAMDevMemoryControl *This);")
cpp_quote("      HRESULT (WINAPI *WriteSync)(IAMDevMemoryControl *This);")
cpp_quote("      HRESULT (WINAPI *GetDevId)(IAMDevMemoryControl *This,DWORD *pdwDevId);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMDevMemoryControlVtbl;")
cpp_quote("  struct IAMDevMemoryControl {")
cpp_quote("    CONST_VTBL struct IAMDevMemoryControlVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMDevMemoryControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMDevMemoryControl_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMDevMemoryControl_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMDevMemoryControl_QueryWriteSync(This) (This)->lpVtbl->QueryWriteSync(This)")
cpp_quote("#define IAMDevMemoryControl_WriteSync(This) (This)->lpVtbl->WriteSync(This)")
cpp_quote("#define IAMDevMemoryControl_GetDevId(This,pdwDevId) (This)->lpVtbl->GetDevId(This,pdwDevId)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMDevMemoryControl_QueryWriteSync_Proxy(IAMDevMemoryControl *This);")
cpp_quote("  void __RPC_STUB IAMDevMemoryControl_QueryWriteSync_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMDevMemoryControl_WriteSync_Proxy(IAMDevMemoryControl *This);")
cpp_quote("  void __RPC_STUB IAMDevMemoryControl_WriteSync_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMDevMemoryControl_GetDevId_Proxy(IAMDevMemoryControl *This,DWORD *pdwDevId);")
cpp_quote("  void __RPC_STUB IAMDevMemoryControl_GetDevId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef IAMDevMemoryControl *PAMDEVMEMORYCONTROL;")
cpp_quote("")
cpp_quote("  enum _AMSTREAMSELECTINFOFLAGS {")
cpp_quote("    AMSTREAMSELECTINFO_ENABLED = 0x1,AMSTREAMSELECTINFO_EXCLUSIVE = 0x2")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  enum _AMSTREAMSELECTENABLEFLAGS {")
cpp_quote("    AMSTREAMSELECTENABLE_ENABLE = 0x1,AMSTREAMSELECTENABLE_ENABLEALL = 0x2")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0345_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0345_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMStreamSelect_INTERFACE_DEFINED__")
cpp_quote("#define __IAMStreamSelect_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMStreamSelect;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMStreamSelect : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI Count(DWORD *pcStreams) = 0;")
cpp_quote("    virtual HRESULT WINAPI Info(LONG lIndex,AM_MEDIA_TYPE **ppmt,DWORD *pdwFlags,LCID *plcid,DWORD *pdwGroup,WCHAR **ppszName,IUnknown **ppObject,IUnknown **ppUnk) = 0;")
cpp_quote("    virtual HRESULT WINAPI Enable(LONG lIndex,DWORD dwFlags) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMStreamSelectVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMStreamSelect *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMStreamSelect *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMStreamSelect *This);")
cpp_quote("      HRESULT (WINAPI *Count)(IAMStreamSelect *This,DWORD *pcStreams);")
cpp_quote("      HRESULT (WINAPI *Info)(IAMStreamSelect *This,LONG lIndex,AM_MEDIA_TYPE **ppmt,DWORD *pdwFlags,LCID *plcid,DWORD *pdwGroup,WCHAR **ppszName,IUnknown **ppObject,IUnknown **ppUnk);")
cpp_quote("      HRESULT (WINAPI *Enable)(IAMStreamSelect *This,LONG lIndex,DWORD dwFlags);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMStreamSelectVtbl;")
cpp_quote("  struct IAMStreamSelect {")
cpp_quote("    CONST_VTBL struct IAMStreamSelectVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMStreamSelect_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMStreamSelect_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMStreamSelect_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMStreamSelect_Count(This,pcStreams) (This)->lpVtbl->Count(This,pcStreams)")
cpp_quote("#define IAMStreamSelect_Info(This,lIndex,ppmt,pdwFlags,plcid,pdwGroup,ppszName,ppObject,ppUnk) (This)->lpVtbl->Info(This,lIndex,ppmt,pdwFlags,plcid,pdwGroup,ppszName,ppObject,ppUnk)")
cpp_quote("#define IAMStreamSelect_Enable(This,lIndex,dwFlags) (This)->lpVtbl->Enable(This,lIndex,dwFlags)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMStreamSelect_Count_Proxy(IAMStreamSelect *This,DWORD *pcStreams);")
cpp_quote("  void __RPC_STUB IAMStreamSelect_Count_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMStreamSelect_Info_Proxy(IAMStreamSelect *This,LONG lIndex,AM_MEDIA_TYPE **ppmt,DWORD *pdwFlags,LCID *plcid,DWORD *pdwGroup,WCHAR **ppszName,IUnknown **ppObject,IUnknown **ppUnk);")
cpp_quote("  void __RPC_STUB IAMStreamSelect_Info_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMStreamSelect_Enable_Proxy(IAMStreamSelect *This,LONG lIndex,DWORD dwFlags);")
cpp_quote("  void __RPC_STUB IAMStreamSelect_Enable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef IAMStreamSelect *PAMSTREAMSELECT;")
cpp_quote("")
cpp_quote("  enum _AMRESCTL_RESERVEFLAGS {")
cpp_quote("    AMRESCTL_RESERVEFLAGS_RESERVE = 0,AMRESCTL_RESERVEFLAGS_UNRESERVE = 0x1")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0346_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0346_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMResourceControl_INTERFACE_DEFINED__")
cpp_quote("#define __IAMResourceControl_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMResourceControl;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMResourceControl : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI Reserve(DWORD dwFlags,PVOID pvReserved) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMResourceControlVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMResourceControl *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMResourceControl *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMResourceControl *This);")
cpp_quote("      HRESULT (WINAPI *Reserve)(IAMResourceControl *This,DWORD dwFlags,PVOID pvReserved);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMResourceControlVtbl;")
cpp_quote("  struct IAMResourceControl {")
cpp_quote("    CONST_VTBL struct IAMResourceControlVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMResourceControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMResourceControl_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMResourceControl_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMResourceControl_Reserve(This,dwFlags,pvReserved) (This)->lpVtbl->Reserve(This,dwFlags,pvReserved)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMResourceControl_Reserve_Proxy(IAMResourceControl *This,DWORD dwFlags,PVOID pvReserved);")
cpp_quote("  void __RPC_STUB IAMResourceControl_Reserve_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMClockAdjust_INTERFACE_DEFINED__")
cpp_quote("#define __IAMClockAdjust_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMClockAdjust;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMClockAdjust : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI SetClockDelta(REFERENCE_TIME rtDelta) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMClockAdjustVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMClockAdjust *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMClockAdjust *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMClockAdjust *This);")
cpp_quote("      HRESULT (WINAPI *SetClockDelta)(IAMClockAdjust *This,REFERENCE_TIME rtDelta);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMClockAdjustVtbl;")
cpp_quote("  struct IAMClockAdjust {")
cpp_quote("    CONST_VTBL struct IAMClockAdjustVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMClockAdjust_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMClockAdjust_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMClockAdjust_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMClockAdjust_SetClockDelta(This,rtDelta) (This)->lpVtbl->SetClockDelta(This,rtDelta)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMClockAdjust_SetClockDelta_Proxy(IAMClockAdjust *This,REFERENCE_TIME rtDelta);")
cpp_quote("  void __RPC_STUB IAMClockAdjust_SetClockDelta_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDrawVideoImage_INTERFACE_DEFINED__")
cpp_quote("#define __IDrawVideoImage_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDrawVideoImage;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDrawVideoImage : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI DrawVideoImageBegin(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI DrawVideoImageEnd(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI DrawVideoImageDraw(HDC hdc,LPRECT lprcSrc,LPRECT lprcDst) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDrawVideoImageVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDrawVideoImage *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDrawVideoImage *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDrawVideoImage *This);")
cpp_quote("      HRESULT (WINAPI *DrawVideoImageBegin)(IDrawVideoImage *This);")
cpp_quote("      HRESULT (WINAPI *DrawVideoImageEnd)(IDrawVideoImage *This);")
cpp_quote("      HRESULT (WINAPI *DrawVideoImageDraw)(IDrawVideoImage *This,HDC hdc,LPRECT lprcSrc,LPRECT lprcDst);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDrawVideoImageVtbl;")
cpp_quote("  struct IDrawVideoImage {")
cpp_quote("    CONST_VTBL struct IDrawVideoImageVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDrawVideoImage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDrawVideoImage_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDrawVideoImage_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDrawVideoImage_DrawVideoImageBegin(This) (This)->lpVtbl->DrawVideoImageBegin(This)")
cpp_quote("#define IDrawVideoImage_DrawVideoImageEnd(This) (This)->lpVtbl->DrawVideoImageEnd(This)")
cpp_quote("#define IDrawVideoImage_DrawVideoImageDraw(This,hdc,lprcSrc,lprcDst) (This)->lpVtbl->DrawVideoImageDraw(This,hdc,lprcSrc,lprcDst)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDrawVideoImage_DrawVideoImageBegin_Proxy(IDrawVideoImage *This);")
cpp_quote("  void __RPC_STUB IDrawVideoImage_DrawVideoImageBegin_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDrawVideoImage_DrawVideoImageEnd_Proxy(IDrawVideoImage *This);")
cpp_quote("  void __RPC_STUB IDrawVideoImage_DrawVideoImageEnd_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDrawVideoImage_DrawVideoImageDraw_Proxy(IDrawVideoImage *This,HDC hdc,LPRECT lprcSrc,LPRECT lprcDst);")
cpp_quote("  void __RPC_STUB IDrawVideoImage_DrawVideoImageDraw_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDecimateVideoImage_INTERFACE_DEFINED__")
cpp_quote("#define __IDecimateVideoImage_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDecimateVideoImage;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDecimateVideoImage : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI SetDecimationImageSize(LONG lWidth,LONG lHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI ResetDecimationImageSize(void) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDecimateVideoImageVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDecimateVideoImage *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDecimateVideoImage *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDecimateVideoImage *This);")
cpp_quote("      HRESULT (WINAPI *SetDecimationImageSize)(IDecimateVideoImage *This,LONG lWidth,LONG lHeight);")
cpp_quote("      HRESULT (WINAPI *ResetDecimationImageSize)(IDecimateVideoImage *This);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDecimateVideoImageVtbl;")
cpp_quote("  struct IDecimateVideoImage {")
cpp_quote("    CONST_VTBL struct IDecimateVideoImageVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDecimateVideoImage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDecimateVideoImage_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDecimateVideoImage_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDecimateVideoImage_SetDecimationImageSize(This,lWidth,lHeight) (This)->lpVtbl->SetDecimationImageSize(This,lWidth,lHeight)")
cpp_quote("#define IDecimateVideoImage_ResetDecimationImageSize(This) (This)->lpVtbl->ResetDecimationImageSize(This)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDecimateVideoImage_SetDecimationImageSize_Proxy(IDecimateVideoImage *This,LONG lWidth,LONG lHeight);")
cpp_quote("  void __RPC_STUB IDecimateVideoImage_SetDecimationImageSize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDecimateVideoImage_ResetDecimationImageSize_Proxy(IDecimateVideoImage *This);")
cpp_quote("  void __RPC_STUB IDecimateVideoImage_ResetDecimationImageSize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef enum _DECIMATION_USAGE {")
cpp_quote("    DECIMATION_LEGACY = 0,")
cpp_quote("    DECIMATION_USE_DECODER_ONLY,DECIMATION_USE_VIDEOPORT_ONLY,DECIMATION_USE_OVERLAY_ONLY,")
cpp_quote("    DECIMATION_DEFAULT")
cpp_quote("  } DECIMATION_USAGE;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0351_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0351_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMVideoDecimationProperties_INTERFACE_DEFINED__")
cpp_quote("#define __IAMVideoDecimationProperties_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMVideoDecimationProperties;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMVideoDecimationProperties : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI QueryDecimationUsage(DECIMATION_USAGE *lpUsage) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetDecimationUsage(DECIMATION_USAGE Usage) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMVideoDecimationPropertiesVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMVideoDecimationProperties *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMVideoDecimationProperties *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMVideoDecimationProperties *This);")
cpp_quote("      HRESULT (WINAPI *QueryDecimationUsage)(IAMVideoDecimationProperties *This,DECIMATION_USAGE *lpUsage);")
cpp_quote("      HRESULT (WINAPI *SetDecimationUsage)(IAMVideoDecimationProperties *This,DECIMATION_USAGE Usage);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMVideoDecimationPropertiesVtbl;")
cpp_quote("  struct IAMVideoDecimationProperties {")
cpp_quote("    CONST_VTBL struct IAMVideoDecimationPropertiesVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMVideoDecimationProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMVideoDecimationProperties_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMVideoDecimationProperties_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMVideoDecimationProperties_QueryDecimationUsage(This,lpUsage) (This)->lpVtbl->QueryDecimationUsage(This,lpUsage)")
cpp_quote("#define IAMVideoDecimationProperties_SetDecimationUsage(This,Usage) (This)->lpVtbl->SetDecimationUsage(This,Usage)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMVideoDecimationProperties_QueryDecimationUsage_Proxy(IAMVideoDecimationProperties *This,DECIMATION_USAGE *lpUsage);")
cpp_quote("  void __RPC_STUB IAMVideoDecimationProperties_QueryDecimationUsage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMVideoDecimationProperties_SetDecimationUsage_Proxy(IAMVideoDecimationProperties *This,DECIMATION_USAGE Usage);")
cpp_quote("  void __RPC_STUB IAMVideoDecimationProperties_SetDecimationUsage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  enum _AM_PUSHSOURCE_FLAGS {")
cpp_quote("    AM_PUSHSOURCECAPS_INTERNAL_RM = 0x1,AM_PUSHSOURCECAPS_NOT_LIVE = 0x2,AM_PUSHSOURCECAPS_PRIVATE_CLOCK = 0x4,")
cpp_quote("    AM_PUSHSOURCEREQS_USE_STREAM_CLOCK = 0x10000,AM_PUSHSOURCEREQS_USE_CLOCK_CHAIN = 0x20000")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0353_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0353_v0_0_s_ifspec;")
cpp_quote("")
cpp_quote("#ifndef __IAMDeviceRemoval_INTERFACE_DEFINED__")
cpp_quote("#define __IAMDeviceRemoval_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMDeviceRemoval;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMDeviceRemoval : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI DeviceInfo(CLSID *pclsidInterfaceClass,WCHAR **pwszSymbolicLink) = 0;")
cpp_quote("    virtual HRESULT WINAPI Reassociate(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI Disassociate(void) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMDeviceRemovalVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMDeviceRemoval *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMDeviceRemoval *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMDeviceRemoval *This);")
cpp_quote("      HRESULT (WINAPI *DeviceInfo)(IAMDeviceRemoval *This,CLSID *pclsidInterfaceClass,WCHAR **pwszSymbolicLink);")
cpp_quote("      HRESULT (WINAPI *Reassociate)(IAMDeviceRemoval *This);")
cpp_quote("      HRESULT (WINAPI *Disassociate)(IAMDeviceRemoval *This);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMDeviceRemovalVtbl;")
cpp_quote("  struct IAMDeviceRemoval {")
cpp_quote("    CONST_VTBL struct IAMDeviceRemovalVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMDeviceRemoval_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMDeviceRemoval_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMDeviceRemoval_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMDeviceRemoval_DeviceInfo(This,pclsidInterfaceClass,pwszSymbolicLink) (This)->lpVtbl->DeviceInfo(This,pclsidInterfaceClass,pwszSymbolicLink)")
cpp_quote("#define IAMDeviceRemoval_Reassociate(This) (This)->lpVtbl->Reassociate(This)")
cpp_quote("#define IAMDeviceRemoval_Disassociate(This) (This)->lpVtbl->Disassociate(This)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMDeviceRemoval_DeviceInfo_Proxy(IAMDeviceRemoval *This,CLSID *pclsidInterfaceClass,WCHAR **pwszSymbolicLink);")
cpp_quote("  void __RPC_STUB IAMDeviceRemoval_DeviceInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMDeviceRemoval_Reassociate_Proxy(IAMDeviceRemoval *This);")
cpp_quote("  void __RPC_STUB IAMDeviceRemoval_Reassociate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMDeviceRemoval_Disassociate_Proxy(IAMDeviceRemoval *This);")
cpp_quote("  void __RPC_STUB IAMDeviceRemoval_Disassociate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef struct __MIDL___MIDL_itf_strmif_0355_0001 {")
cpp_quote("    DWORD dwDVAAuxSrc;")
cpp_quote("    DWORD dwDVAAuxCtl;")
cpp_quote("    DWORD dwDVAAuxSrc1;")
cpp_quote("    DWORD dwDVAAuxCtl1;")
cpp_quote("    DWORD dwDVVAuxSrc;")
cpp_quote("    DWORD dwDVVAuxCtl;")
cpp_quote("    DWORD dwDVReserved[2];")
cpp_quote("  } DVINFO;")
cpp_quote("")
cpp_quote("  typedef struct __MIDL___MIDL_itf_strmif_0355_0001 *PDVINFO;")
cpp_quote("")
cpp_quote("  enum _DVENCODERRESOLUTION {")
cpp_quote("    DVENCODERRESOLUTION_720x480 = 2012,DVENCODERRESOLUTION_360x240 = 2013,DVENCODERRESOLUTION_180x120 = 2014,DVENCODERRESOLUTION_88x60 = 2015")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  enum _DVENCODERVIDEOFORMAT {")
cpp_quote("    DVENCODERVIDEOFORMAT_NTSC = 2000,DVENCODERVIDEOFORMAT_PAL = 2001")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  enum _DVENCODERFORMAT {")
cpp_quote("    DVENCODERFORMAT_DVSD = 2007,DVENCODERFORMAT_DVHD = 2008,DVENCODERFORMAT_DVSL = 2009")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0355_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0355_v0_0_s_ifspec;")
cpp_quote("#ifndef __IDVEnc_INTERFACE_DEFINED__")
cpp_quote("#define __IDVEnc_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDVEnc;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDVEnc : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI get_IFormatResolution(int *VideoFormat,int *DVFormat,int *Resolution,BYTE fDVInfo,DVINFO *sDVInfo) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_IFormatResolution(int VideoFormat,int DVFormat,int Resolution,BYTE fDVInfo,DVINFO *sDVInfo) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDVEncVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDVEnc *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDVEnc *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDVEnc *This);")
cpp_quote("      HRESULT (WINAPI *get_IFormatResolution)(IDVEnc *This,int *VideoFormat,int *DVFormat,int *Resolution,BYTE fDVInfo,DVINFO *sDVInfo);")
cpp_quote("      HRESULT (WINAPI *put_IFormatResolution)(IDVEnc *This,int VideoFormat,int DVFormat,int Resolution,BYTE fDVInfo,DVINFO *sDVInfo);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDVEncVtbl;")
cpp_quote("  struct IDVEnc {")
cpp_quote("    CONST_VTBL struct IDVEncVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDVEnc_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDVEnc_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDVEnc_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDVEnc_get_IFormatResolution(This,VideoFormat,DVFormat,Resolution,fDVInfo,sDVInfo) (This)->lpVtbl->get_IFormatResolution(This,VideoFormat,DVFormat,Resolution,fDVInfo,sDVInfo)")
cpp_quote("#define IDVEnc_put_IFormatResolution(This,VideoFormat,DVFormat,Resolution,fDVInfo,sDVInfo) (This)->lpVtbl->put_IFormatResolution(This,VideoFormat,DVFormat,Resolution,fDVInfo,sDVInfo)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDVEnc_get_IFormatResolution_Proxy(IDVEnc *This,int *VideoFormat,int *DVFormat,int *Resolution,BYTE fDVInfo,DVINFO *sDVInfo);")
cpp_quote("  void __RPC_STUB IDVEnc_get_IFormatResolution_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDVEnc_put_IFormatResolution_Proxy(IDVEnc *This,int VideoFormat,int DVFormat,int Resolution,BYTE fDVInfo,DVINFO *sDVInfo);")
cpp_quote("  void __RPC_STUB IDVEnc_put_IFormatResolution_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  enum _DVDECODERRESOLUTION {")
cpp_quote("    DVDECODERRESOLUTION_720x480 = 1000,DVDECODERRESOLUTION_360x240 = 1001,DVDECODERRESOLUTION_180x120 = 1002,DVDECODERRESOLUTION_88x60 = 1003")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  enum _DVRESOLUTION {")
cpp_quote("    DVRESOLUTION_FULL = 1000,DVRESOLUTION_HALF = 1001,DVRESOLUTION_QUARTER = 1002,DVRESOLUTION_DC = 1003")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0356_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0356_v0_0_s_ifspec;")
cpp_quote("#ifndef __IIPDVDec_INTERFACE_DEFINED__")
cpp_quote("#define __IIPDVDec_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IIPDVDec;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IIPDVDec : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI get_IPDisplay(int *displayPix) = 0;")
cpp_quote("    virtual HRESULT WINAPI put_IPDisplay(int displayPix) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IIPDVDecVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IIPDVDec *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IIPDVDec *This);")
cpp_quote("      ULONG (WINAPI *Release)(IIPDVDec *This);")
cpp_quote("      HRESULT (WINAPI *get_IPDisplay)(IIPDVDec *This,int *displayPix);")
cpp_quote("      HRESULT (WINAPI *put_IPDisplay)(IIPDVDec *This,int displayPix);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IIPDVDecVtbl;")
cpp_quote("  struct IIPDVDec {")
cpp_quote("    CONST_VTBL struct IIPDVDecVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IIPDVDec_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IIPDVDec_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IIPDVDec_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IIPDVDec_get_IPDisplay(This,displayPix) (This)->lpVtbl->get_IPDisplay(This,displayPix)")
cpp_quote("#define IIPDVDec_put_IPDisplay(This,displayPix) (This)->lpVtbl->put_IPDisplay(This,displayPix)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IIPDVDec_get_IPDisplay_Proxy(IIPDVDec *This,int *displayPix);")
cpp_quote("  void __RPC_STUB IIPDVDec_get_IPDisplay_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IIPDVDec_put_IPDisplay_Proxy(IIPDVDec *This,int displayPix);")
cpp_quote("  void __RPC_STUB IIPDVDec_put_IPDisplay_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDVRGB219_INTERFACE_DEFINED__")
cpp_quote("#define __IDVRGB219_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDVRGB219;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDVRGB219 : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI SetRGB219(WINBOOL bState) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDVRGB219Vtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDVRGB219 *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDVRGB219 *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDVRGB219 *This);")
cpp_quote("      HRESULT (WINAPI *SetRGB219)(IDVRGB219 *This,WINBOOL bState);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDVRGB219Vtbl;")
cpp_quote("  struct IDVRGB219 {")
cpp_quote("    CONST_VTBL struct IDVRGB219Vtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDVRGB219_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDVRGB219_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDVRGB219_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDVRGB219_SetRGB219(This,bState) (This)->lpVtbl->SetRGB219(This,bState)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDVRGB219_SetRGB219_Proxy(IDVRGB219 *This,WINBOOL bState);")
cpp_quote("  void __RPC_STUB IDVRGB219_SetRGB219_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDVSplitter_INTERFACE_DEFINED__")
cpp_quote("#define __IDVSplitter_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDVSplitter;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDVSplitter : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI DiscardAlternateVideoFrames(int nDiscard) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDVSplitterVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDVSplitter *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDVSplitter *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDVSplitter *This);")
cpp_quote("      HRESULT (WINAPI *DiscardAlternateVideoFrames)(IDVSplitter *This,int nDiscard);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDVSplitterVtbl;")
cpp_quote("  struct IDVSplitter {")
cpp_quote("    CONST_VTBL struct IDVSplitterVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDVSplitter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDVSplitter_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDVSplitter_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDVSplitter_DiscardAlternateVideoFrames(This,nDiscard) (This)->lpVtbl->DiscardAlternateVideoFrames(This,nDiscard)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDVSplitter_DiscardAlternateVideoFrames_Proxy(IDVSplitter *This,int nDiscard);")
cpp_quote("  void __RPC_STUB IDVSplitter_DiscardAlternateVideoFrames_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  enum _AM_AUDIO_RENDERER_STAT_PARAM {")
cpp_quote("    AM_AUDREND_STAT_PARAM_BREAK_COUNT = 1,")
cpp_quote("    AM_AUDREND_STAT_PARAM_SLAVE_MODE,AM_AUDREND_STAT_PARAM_SILENCE_DUR,")
cpp_quote("    AM_AUDREND_STAT_PARAM_LAST_BUFFER_DUR,AM_AUDREND_STAT_PARAM_DISCONTINUITIES,")
cpp_quote("    AM_AUDREND_STAT_PARAM_SLAVE_RATE,AM_AUDREND_STAT_PARAM_SLAVE_DROPWRITE_DUR,")
cpp_quote("    AM_AUDREND_STAT_PARAM_SLAVE_HIGHLOWERROR,AM_AUDREND_STAT_PARAM_SLAVE_LASTHIGHLOWERROR,")
cpp_quote("    AM_AUDREND_STAT_PARAM_SLAVE_ACCUMERROR,AM_AUDREND_STAT_PARAM_BUFFERFULLNESS,")
cpp_quote("    AM_AUDREND_STAT_PARAM_JITTER")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0359_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0359_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMAudioRendererStats_INTERFACE_DEFINED__")
cpp_quote("#define __IAMAudioRendererStats_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMAudioRendererStats;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMAudioRendererStats : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetStatParam(DWORD dwParam,DWORD *pdwParam1,DWORD *pdwParam2) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMAudioRendererStatsVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMAudioRendererStats *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMAudioRendererStats *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMAudioRendererStats *This);")
cpp_quote("      HRESULT (WINAPI *GetStatParam)(IAMAudioRendererStats *This,DWORD dwParam,DWORD *pdwParam1,DWORD *pdwParam2);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMAudioRendererStatsVtbl;")
cpp_quote("  struct IAMAudioRendererStats {")
cpp_quote("    CONST_VTBL struct IAMAudioRendererStatsVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMAudioRendererStats_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMAudioRendererStats_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMAudioRendererStats_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMAudioRendererStats_GetStatParam(This,dwParam,pdwParam1,pdwParam2) (This)->lpVtbl->GetStatParam(This,dwParam,pdwParam1,pdwParam2)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMAudioRendererStats_GetStatParam_Proxy(IAMAudioRendererStats *This,DWORD dwParam,DWORD *pdwParam1,DWORD *pdwParam2);")
cpp_quote("  void __RPC_STUB IAMAudioRendererStats_GetStatParam_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  enum _AM_INTF_SEARCH_FLAGS {")
cpp_quote("    AM_INTF_SEARCH_INPUT_PIN = 0x1,AM_INTF_SEARCH_OUTPUT_PIN = 0x2,AM_INTF_SEARCH_FILTER = 0x4")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0361_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0361_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMGraphStreams_INTERFACE_DEFINED__")
cpp_quote("#define __IAMGraphStreams_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMGraphStreams;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMGraphStreams : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI FindUpstreamInterface(IPin *pPin,REFIID riid,void **ppvInterface,DWORD dwFlags) = 0;")
cpp_quote("    virtual HRESULT WINAPI SyncUsingStreamOffset(WINBOOL bUseStreamOffset) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetMaxGraphLatency(REFERENCE_TIME rtMaxGraphLatency) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMGraphStreamsVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMGraphStreams *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMGraphStreams *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMGraphStreams *This);")
cpp_quote("      HRESULT (WINAPI *FindUpstreamInterface)(IAMGraphStreams *This,IPin *pPin,REFIID riid,void **ppvInterface,DWORD dwFlags);")
cpp_quote("      HRESULT (WINAPI *SyncUsingStreamOffset)(IAMGraphStreams *This,WINBOOL bUseStreamOffset);")
cpp_quote("      HRESULT (WINAPI *SetMaxGraphLatency)(IAMGraphStreams *This,REFERENCE_TIME rtMaxGraphLatency);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMGraphStreamsVtbl;")
cpp_quote("  struct IAMGraphStreams {")
cpp_quote("    CONST_VTBL struct IAMGraphStreamsVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMGraphStreams_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMGraphStreams_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMGraphStreams_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMGraphStreams_FindUpstreamInterface(This,pPin,riid,ppvInterface,dwFlags) (This)->lpVtbl->FindUpstreamInterface(This,pPin,riid,ppvInterface,dwFlags)")
cpp_quote("#define IAMGraphStreams_SyncUsingStreamOffset(This,bUseStreamOffset) (This)->lpVtbl->SyncUsingStreamOffset(This,bUseStreamOffset)")
cpp_quote("#define IAMGraphStreams_SetMaxGraphLatency(This,rtMaxGraphLatency) (This)->lpVtbl->SetMaxGraphLatency(This,rtMaxGraphLatency)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMGraphStreams_FindUpstreamInterface_Proxy(IAMGraphStreams *This,IPin *pPin,REFIID riid,void **ppvInterface,DWORD dwFlags);")
cpp_quote("  void __RPC_STUB IAMGraphStreams_FindUpstreamInterface_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMGraphStreams_SyncUsingStreamOffset_Proxy(IAMGraphStreams *This,WINBOOL bUseStreamOffset);")
cpp_quote("  void __RPC_STUB IAMGraphStreams_SyncUsingStreamOffset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMGraphStreams_SetMaxGraphLatency_Proxy(IAMGraphStreams *This,REFERENCE_TIME rtMaxGraphLatency);")
cpp_quote("  void __RPC_STUB IAMGraphStreams_SetMaxGraphLatency_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  enum AMOVERLAYFX {")
cpp_quote("    AMOVERFX_NOFX = 0,AMOVERFX_MIRRORLEFTRIGHT = 0x2,AMOVERFX_MIRRORUPDOWN = 0x4,AMOVERFX_DEINTERLACE = 0x8")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0362_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0362_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMOverlayFX_INTERFACE_DEFINED__")
cpp_quote("#define __IAMOverlayFX_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMOverlayFX;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMOverlayFX : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI QueryOverlayFXCaps(DWORD *lpdwOverlayFXCaps) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetOverlayFX(DWORD dwOverlayFX) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetOverlayFX(DWORD *lpdwOverlayFX) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMOverlayFXVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMOverlayFX *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMOverlayFX *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMOverlayFX *This);")
cpp_quote("      HRESULT (WINAPI *QueryOverlayFXCaps)(IAMOverlayFX *This,DWORD *lpdwOverlayFXCaps);")
cpp_quote("      HRESULT (WINAPI *SetOverlayFX)(IAMOverlayFX *This,DWORD dwOverlayFX);")
cpp_quote("      HRESULT (WINAPI *GetOverlayFX)(IAMOverlayFX *This,DWORD *lpdwOverlayFX);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMOverlayFXVtbl;")
cpp_quote("  struct IAMOverlayFX {")
cpp_quote("    CONST_VTBL struct IAMOverlayFXVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMOverlayFX_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMOverlayFX_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMOverlayFX_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMOverlayFX_QueryOverlayFXCaps(This,lpdwOverlayFXCaps) (This)->lpVtbl->QueryOverlayFXCaps(This,lpdwOverlayFXCaps)")
cpp_quote("#define IAMOverlayFX_SetOverlayFX(This,dwOverlayFX) (This)->lpVtbl->SetOverlayFX(This,dwOverlayFX)")
cpp_quote("#define IAMOverlayFX_GetOverlayFX(This,lpdwOverlayFX) (This)->lpVtbl->GetOverlayFX(This,lpdwOverlayFX)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMOverlayFX_QueryOverlayFXCaps_Proxy(IAMOverlayFX *This,DWORD *lpdwOverlayFXCaps);")
cpp_quote("  void __RPC_STUB IAMOverlayFX_QueryOverlayFXCaps_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMOverlayFX_SetOverlayFX_Proxy(IAMOverlayFX *This,DWORD dwOverlayFX);")
cpp_quote("  void __RPC_STUB IAMOverlayFX_SetOverlayFX_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMOverlayFX_GetOverlayFX_Proxy(IAMOverlayFX *This,DWORD *lpdwOverlayFX);")
cpp_quote("  void __RPC_STUB IAMOverlayFX_GetOverlayFX_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IAMOpenProgress_INTERFACE_DEFINED__")
cpp_quote("#define __IAMOpenProgress_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMOpenProgress;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMOpenProgress : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI QueryProgress(LONGLONG *pllTotal,LONGLONG *pllCurrent) = 0;")
cpp_quote("    virtual HRESULT WINAPI AbortOperation(void) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMOpenProgressVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMOpenProgress *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMOpenProgress *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMOpenProgress *This);")
cpp_quote("      HRESULT (WINAPI *QueryProgress)(IAMOpenProgress *This,LONGLONG *pllTotal,LONGLONG *pllCurrent);")
cpp_quote("      HRESULT (WINAPI *AbortOperation)(IAMOpenProgress *This);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMOpenProgressVtbl;")
cpp_quote("  struct IAMOpenProgress {")
cpp_quote("    CONST_VTBL struct IAMOpenProgressVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMOpenProgress_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMOpenProgress_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMOpenProgress_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMOpenProgress_QueryProgress(This,pllTotal,pllCurrent) (This)->lpVtbl->QueryProgress(This,pllTotal,pllCurrent)")
cpp_quote("#define IAMOpenProgress_AbortOperation(This) (This)->lpVtbl->AbortOperation(This)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMOpenProgress_QueryProgress_Proxy(IAMOpenProgress *This,LONGLONG *pllTotal,LONGLONG *pllCurrent);")
cpp_quote("  void __RPC_STUB IAMOpenProgress_QueryProgress_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IAMOpenProgress_AbortOperation_Proxy(IAMOpenProgress *This);")
cpp_quote("  void __RPC_STUB IAMOpenProgress_AbortOperation_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifdef __CRT_UUID_DECL")
cpp_quote("__CRT_UUID_DECL(IMpeg2Demultiplexer,0x436eee9c,0x264f,0x4242,0x90,0xe1,0x4e,0x33,0x0c,0x10,0x75,0x12);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IMpeg2Demultiplexer_INTERFACE_DEFINED__")
cpp_quote("#define __IMpeg2Demultiplexer_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IMpeg2Demultiplexer;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IMpeg2Demultiplexer : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI CreateOutputPin(AM_MEDIA_TYPE *pMediaType,LPWSTR pszPinName,IPin **ppIPin) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetOutputPinMediaType(LPWSTR pszPinName,AM_MEDIA_TYPE *pMediaType) = 0;")
cpp_quote("    virtual HRESULT WINAPI DeleteOutputPin(LPWSTR pszPinName) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IMpeg2DemultiplexerVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IMpeg2Demultiplexer *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IMpeg2Demultiplexer *This);")
cpp_quote("      ULONG (WINAPI *Release)(IMpeg2Demultiplexer *This);")
cpp_quote("      HRESULT (WINAPI *CreateOutputPin)(IMpeg2Demultiplexer *This,AM_MEDIA_TYPE *pMediaType,LPWSTR pszPinName,IPin **ppIPin);")
cpp_quote("      HRESULT (WINAPI *SetOutputPinMediaType)(IMpeg2Demultiplexer *This,LPWSTR pszPinName,AM_MEDIA_TYPE *pMediaType);")
cpp_quote("      HRESULT (WINAPI *DeleteOutputPin)(IMpeg2Demultiplexer *This,LPWSTR pszPinName);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IMpeg2DemultiplexerVtbl;")
cpp_quote("  struct IMpeg2Demultiplexer {")
cpp_quote("    CONST_VTBL struct IMpeg2DemultiplexerVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IMpeg2Demultiplexer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IMpeg2Demultiplexer_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IMpeg2Demultiplexer_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IMpeg2Demultiplexer_CreateOutputPin(This,pMediaType,pszPinName,ppIPin) (This)->lpVtbl->CreateOutputPin(This,pMediaType,pszPinName,ppIPin)")
cpp_quote("#define IMpeg2Demultiplexer_SetOutputPinMediaType(This,pszPinName,pMediaType) (This)->lpVtbl->SetOutputPinMediaType(This,pszPinName,pMediaType)")
cpp_quote("#define IMpeg2Demultiplexer_DeleteOutputPin(This,pszPinName) (This)->lpVtbl->DeleteOutputPin(This,pszPinName)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IMpeg2Demultiplexer_CreateOutputPin_Proxy(IMpeg2Demultiplexer *This,AM_MEDIA_TYPE *pMediaType,LPWSTR pszPinName,IPin **ppIPin);")
cpp_quote("  void __RPC_STUB IMpeg2Demultiplexer_CreateOutputPin_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMpeg2Demultiplexer_SetOutputPinMediaType_Proxy(IMpeg2Demultiplexer *This,LPWSTR pszPinName,AM_MEDIA_TYPE *pMediaType);")
cpp_quote("  void __RPC_STUB IMpeg2Demultiplexer_SetOutputPinMediaType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMpeg2Demultiplexer_DeleteOutputPin_Proxy(IMpeg2Demultiplexer *This,LPWSTR pszPinName);")
cpp_quote("  void __RPC_STUB IMpeg2Demultiplexer_DeleteOutputPin_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#define MPEG2_PROGRAM_STREAM_MAP 0x00000000")
cpp_quote("#define MPEG2_PROGRAM_ELEMENTARY_STREAM 0x00000001")
cpp_quote("#define MPEG2_PROGRAM_DIRECTORY_PES_PACKET 0x00000002")
cpp_quote("#define MPEG2_PROGRAM_PACK_HEADER 0x00000003")
cpp_quote("#define MPEG2_PROGRAM_PES_STREAM 0x00000004")
cpp_quote("#define MPEG2_PROGRAM_SYSTEM_HEADER 0x00000005")
cpp_quote("#define SUBSTREAM_FILTER_VAL_NONE 0x10000000")
cpp_quote("")
cpp_quote("  typedef struct __MIDL___MIDL_itf_strmif_0365_0001 {")
cpp_quote("    ULONG stream_id;")
cpp_quote("    DWORD dwMediaSampleContent;")
cpp_quote("    ULONG ulSubstreamFilterValue;")
cpp_quote("    int iDataOffset;")
cpp_quote("  } STREAM_ID_MAP;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0365_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0365_v0_0_s_ifspec;")
cpp_quote("#ifndef __IEnumStreamIdMap_INTERFACE_DEFINED__")
cpp_quote("#define __IEnumStreamIdMap_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IEnumStreamIdMap;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IEnumStreamIdMap : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI Next(ULONG cRequest,STREAM_ID_MAP *pStreamIdMap,ULONG *pcReceived) = 0;")
cpp_quote("    virtual HRESULT WINAPI Skip(ULONG cRecords) = 0;")
cpp_quote("    virtual HRESULT WINAPI Reset(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI Clone(IEnumStreamIdMap **ppIEnumStreamIdMap) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IEnumStreamIdMapVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IEnumStreamIdMap *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IEnumStreamIdMap *This);")
cpp_quote("      ULONG (WINAPI *Release)(IEnumStreamIdMap *This);")
cpp_quote("      HRESULT (WINAPI *Next)(IEnumStreamIdMap *This,ULONG cRequest,STREAM_ID_MAP *pStreamIdMap,ULONG *pcReceived);")
cpp_quote("      HRESULT (WINAPI *Skip)(IEnumStreamIdMap *This,ULONG cRecords);")
cpp_quote("      HRESULT (WINAPI *Reset)(IEnumStreamIdMap *This);")
cpp_quote("      HRESULT (WINAPI *Clone)(IEnumStreamIdMap *This,IEnumStreamIdMap **ppIEnumStreamIdMap);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IEnumStreamIdMapVtbl;")
cpp_quote("  struct IEnumStreamIdMap {")
cpp_quote("    CONST_VTBL struct IEnumStreamIdMapVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IEnumStreamIdMap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IEnumStreamIdMap_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IEnumStreamIdMap_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IEnumStreamIdMap_Next(This,cRequest,pStreamIdMap,pcReceived) (This)->lpVtbl->Next(This,cRequest,pStreamIdMap,pcReceived)")
cpp_quote("#define IEnumStreamIdMap_Skip(This,cRecords) (This)->lpVtbl->Skip(This,cRecords)")
cpp_quote("#define IEnumStreamIdMap_Reset(This) (This)->lpVtbl->Reset(This)")
cpp_quote("#define IEnumStreamIdMap_Clone(This,ppIEnumStreamIdMap) (This)->lpVtbl->Clone(This,ppIEnumStreamIdMap)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IEnumStreamIdMap_Next_Proxy(IEnumStreamIdMap *This,ULONG cRequest,STREAM_ID_MAP *pStreamIdMap,ULONG *pcReceived);")
cpp_quote("  void __RPC_STUB IEnumStreamIdMap_Next_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IEnumStreamIdMap_Skip_Proxy(IEnumStreamIdMap *This,ULONG cRecords);")
cpp_quote("  void __RPC_STUB IEnumStreamIdMap_Skip_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IEnumStreamIdMap_Reset_Proxy(IEnumStreamIdMap *This);")
cpp_quote("  void __RPC_STUB IEnumStreamIdMap_Reset_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IEnumStreamIdMap_Clone_Proxy(IEnumStreamIdMap *This,IEnumStreamIdMap **ppIEnumStreamIdMap);")
cpp_quote("  void __RPC_STUB IEnumStreamIdMap_Clone_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IMPEG2StreamIdMap_INTERFACE_DEFINED__")
cpp_quote("#define __IMPEG2StreamIdMap_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IMPEG2StreamIdMap;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IMPEG2StreamIdMap : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI MapStreamId(ULONG ulStreamId,DWORD MediaSampleContent,ULONG ulSubstreamFilterValue,int iDataOffset) = 0;")
cpp_quote("    virtual HRESULT WINAPI UnmapStreamId(ULONG culStreamId,ULONG *pulStreamId) = 0;")
cpp_quote("    virtual HRESULT WINAPI EnumStreamIdMap(IEnumStreamIdMap **ppIEnumStreamIdMap) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IMPEG2StreamIdMapVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IMPEG2StreamIdMap *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IMPEG2StreamIdMap *This);")
cpp_quote("      ULONG (WINAPI *Release)(IMPEG2StreamIdMap *This);")
cpp_quote("      HRESULT (WINAPI *MapStreamId)(IMPEG2StreamIdMap *This,ULONG ulStreamId,DWORD MediaSampleContent,ULONG ulSubstreamFilterValue,int iDataOffset);")
cpp_quote("      HRESULT (WINAPI *UnmapStreamId)(IMPEG2StreamIdMap *This,ULONG culStreamId,ULONG *pulStreamId);")
cpp_quote("      HRESULT (WINAPI *EnumStreamIdMap)(IMPEG2StreamIdMap *This,IEnumStreamIdMap **ppIEnumStreamIdMap);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IMPEG2StreamIdMapVtbl;")
cpp_quote("  struct IMPEG2StreamIdMap {")
cpp_quote("    CONST_VTBL struct IMPEG2StreamIdMapVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IMPEG2StreamIdMap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IMPEG2StreamIdMap_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IMPEG2StreamIdMap_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IMPEG2StreamIdMap_MapStreamId(This,ulStreamId,MediaSampleContent,ulSubstreamFilterValue,iDataOffset) (This)->lpVtbl->MapStreamId(This,ulStreamId,MediaSampleContent,ulSubstreamFilterValue,iDataOffset)")
cpp_quote("#define IMPEG2StreamIdMap_UnmapStreamId(This,culStreamId,pulStreamId) (This)->lpVtbl->UnmapStreamId(This,culStreamId,pulStreamId)")
cpp_quote("#define IMPEG2StreamIdMap_EnumStreamIdMap(This,ppIEnumStreamIdMap) (This)->lpVtbl->EnumStreamIdMap(This,ppIEnumStreamIdMap)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IMPEG2StreamIdMap_MapStreamId_Proxy(IMPEG2StreamIdMap *This,ULONG ulStreamId,DWORD MediaSampleContent,ULONG ulSubstreamFilterValue,int iDataOffset);")
cpp_quote("  void __RPC_STUB IMPEG2StreamIdMap_MapStreamId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMPEG2StreamIdMap_UnmapStreamId_Proxy(IMPEG2StreamIdMap *This,ULONG culStreamId,ULONG *pulStreamId);")
cpp_quote("  void __RPC_STUB IMPEG2StreamIdMap_UnmapStreamId_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IMPEG2StreamIdMap_EnumStreamIdMap_Proxy(IMPEG2StreamIdMap *This,IEnumStreamIdMap **ppIEnumStreamIdMap);")
cpp_quote("  void __RPC_STUB IMPEG2StreamIdMap_EnumStreamIdMap_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IRegisterServiceProvider_INTERFACE_DEFINED__")
cpp_quote("#define __IRegisterServiceProvider_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IRegisterServiceProvider;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IRegisterServiceProvider : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI RegisterService(REFGUID guidService,IUnknown *pUnkObject) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IRegisterServiceProviderVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IRegisterServiceProvider *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IRegisterServiceProvider *This);")
cpp_quote("      ULONG (WINAPI *Release)(IRegisterServiceProvider *This);")
cpp_quote("      HRESULT (WINAPI *RegisterService)(IRegisterServiceProvider *This,REFGUID guidService,IUnknown *pUnkObject);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IRegisterServiceProviderVtbl;")
cpp_quote("  struct IRegisterServiceProvider {")
cpp_quote("    CONST_VTBL struct IRegisterServiceProviderVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IRegisterServiceProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IRegisterServiceProvider_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IRegisterServiceProvider_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IRegisterServiceProvider_RegisterService(This,guidService,pUnkObject) (This)->lpVtbl->RegisterService(This,guidService,pUnkObject)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IRegisterServiceProvider_RegisterService_Proxy(IRegisterServiceProvider *This,REFGUID guidService,IUnknown *pUnkObject);")
cpp_quote("  void __RPC_STUB IRegisterServiceProvider_RegisterService_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifdef __cplusplus")
cpp_quote("#ifndef _IAMFilterGraphCallback_")
cpp_quote("#define _IAMFilterGraphCallback_")
cpp_quote("  EXTERN_GUID(IID_IAMFilterGraphCallback,0x56a868fd,0x0ad4,0x11ce,0xb0,0xa3,0x0,0x20,0xaf,0x0b,0xa7,0x70);")
cpp_quote("  struct IAMFilterGraphCallback : public IUnknown {")
cpp_quote("    virtual HRESULT UnableToRender(IPin *pPin) = 0;")
cpp_quote("  };")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  struct CodecAPIEventData {")
cpp_quote("    GUID guid;")
cpp_quote("    DWORD dataLength;")
cpp_quote("    DWORD reserved[3];")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0370_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0370_v0_0_s_ifspec;")
cpp_quote("#ifndef __ICodecAPI_INTERFACE_DEFINED__")
cpp_quote("#define __ICodecAPI_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_ICodecAPI;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct ICodecAPI : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI IsSupported(const GUID *Api) = 0;")
cpp_quote("    virtual HRESULT WINAPI IsModifiable(const GUID *Api) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetParameterRange(const GUID *Api,VARIANT *ValueMin,VARIANT *ValueMax,VARIANT *SteppingDelta) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetParameterValues(const GUID *Api,VARIANT **Values,ULONG *ValuesCount) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDefaultValue(const GUID *Api,VARIANT *Value) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetValue(const GUID *Api,VARIANT *Value) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetValue(const GUID *Api,VARIANT *Value) = 0;")
cpp_quote("    virtual HRESULT WINAPI RegisterForEvent(const GUID *Api,LONG_PTR userData) = 0;")
cpp_quote("    virtual HRESULT WINAPI UnregisterForEvent(const GUID *Api) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetAllDefaults(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetValueWithNotify(const GUID *Api,VARIANT *Value,GUID **ChangedParam,ULONG *ChangedParamCount) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetAllDefaultsWithNotify(GUID **ChangedParam,ULONG *ChangedParamCount) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetAllSettings(IStream *__MIDL_0016) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetAllSettings(IStream *__MIDL_0017) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetAllSettingsWithNotify(IStream *__MIDL_0018,GUID **ChangedParam,ULONG *ChangedParamCount) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct ICodecAPIVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(ICodecAPI *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(ICodecAPI *This);")
cpp_quote("      ULONG (WINAPI *Release)(ICodecAPI *This);")
cpp_quote("      HRESULT (WINAPI *IsSupported)(ICodecAPI *This,const GUID *Api);")
cpp_quote("      HRESULT (WINAPI *IsModifiable)(ICodecAPI *This,const GUID *Api);")
cpp_quote("      HRESULT (WINAPI *GetParameterRange)(ICodecAPI *This,const GUID *Api,VARIANT *ValueMin,VARIANT *ValueMax,VARIANT *SteppingDelta);")
cpp_quote("      HRESULT (WINAPI *GetParameterValues)(ICodecAPI *This,const GUID *Api,VARIANT **Values,ULONG *ValuesCount);")
cpp_quote("      HRESULT (WINAPI *GetDefaultValue)(ICodecAPI *This,const GUID *Api,VARIANT *Value);")
cpp_quote("      HRESULT (WINAPI *GetValue)(ICodecAPI *This,const GUID *Api,VARIANT *Value);")
cpp_quote("      HRESULT (WINAPI *SetValue)(ICodecAPI *This,const GUID *Api,VARIANT *Value);")
cpp_quote("      HRESULT (WINAPI *RegisterForEvent)(ICodecAPI *This,const GUID *Api,LONG_PTR userData);")
cpp_quote("      HRESULT (WINAPI *UnregisterForEvent)(ICodecAPI *This,const GUID *Api);")
cpp_quote("      HRESULT (WINAPI *SetAllDefaults)(ICodecAPI *This);")
cpp_quote("      HRESULT (WINAPI *SetValueWithNotify)(ICodecAPI *This,const GUID *Api,VARIANT *Value,GUID **ChangedParam,ULONG *ChangedParamCount);")
cpp_quote("      HRESULT (WINAPI *SetAllDefaultsWithNotify)(ICodecAPI *This,GUID **ChangedParam,ULONG *ChangedParamCount);")
cpp_quote("      HRESULT (WINAPI *GetAllSettings)(ICodecAPI *This,IStream *__MIDL_0016);")
cpp_quote("      HRESULT (WINAPI *SetAllSettings)(ICodecAPI *This,IStream *__MIDL_0017);")
cpp_quote("      HRESULT (WINAPI *SetAllSettingsWithNotify)(ICodecAPI *This,IStream *__MIDL_0018,GUID **ChangedParam,ULONG *ChangedParamCount);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } ICodecAPIVtbl;")
cpp_quote("  struct ICodecAPI {")
cpp_quote("    CONST_VTBL struct ICodecAPIVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define ICodecAPI_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define ICodecAPI_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define ICodecAPI_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define ICodecAPI_IsSupported(This,Api) (This)->lpVtbl->IsSupported(This,Api)")
cpp_quote("#define ICodecAPI_IsModifiable(This,Api) (This)->lpVtbl->IsModifiable(This,Api)")
cpp_quote("#define ICodecAPI_GetParameterRange(This,Api,ValueMin,ValueMax,SteppingDelta) (This)->lpVtbl->GetParameterRange(This,Api,ValueMin,ValueMax,SteppingDelta)")
cpp_quote("#define ICodecAPI_GetParameterValues(This,Api,Values,ValuesCount) (This)->lpVtbl->GetParameterValues(This,Api,Values,ValuesCount)")
cpp_quote("#define ICodecAPI_GetDefaultValue(This,Api,Value) (This)->lpVtbl->GetDefaultValue(This,Api,Value)")
cpp_quote("#define ICodecAPI_GetValue(This,Api,Value) (This)->lpVtbl->GetValue(This,Api,Value)")
cpp_quote("#define ICodecAPI_SetValue(This,Api,Value) (This)->lpVtbl->SetValue(This,Api,Value)")
cpp_quote("#define ICodecAPI_RegisterForEvent(This,Api,userData) (This)->lpVtbl->RegisterForEvent(This,Api,userData)")
cpp_quote("#define ICodecAPI_UnregisterForEvent(This,Api) (This)->lpVtbl->UnregisterForEvent(This,Api)")
cpp_quote("#define ICodecAPI_SetAllDefaults(This) (This)->lpVtbl->SetAllDefaults(This)")
cpp_quote("#define ICodecAPI_SetValueWithNotify(This,Api,Value,ChangedParam,ChangedParamCount) (This)->lpVtbl->SetValueWithNotify(This,Api,Value,ChangedParam,ChangedParamCount)")
cpp_quote("#define ICodecAPI_SetAllDefaultsWithNotify(This,ChangedParam,ChangedParamCount) (This)->lpVtbl->SetAllDefaultsWithNotify(This,ChangedParam,ChangedParamCount)")
cpp_quote("#define ICodecAPI_GetAllSettings(This,__MIDL_0016) (This)->lpVtbl->GetAllSettings(This,__MIDL_0016)")
cpp_quote("#define ICodecAPI_SetAllSettings(This,__MIDL_0017) (This)->lpVtbl->SetAllSettings(This,__MIDL_0017)")
cpp_quote("#define ICodecAPI_SetAllSettingsWithNotify(This,__MIDL_0018,ChangedParam,ChangedParamCount) (This)->lpVtbl->SetAllSettingsWithNotify(This,__MIDL_0018,ChangedParam,ChangedParamCount)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI ICodecAPI_IsSupported_Proxy(ICodecAPI *This,const GUID *Api);")
cpp_quote("  void __RPC_STUB ICodecAPI_IsSupported_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_IsModifiable_Proxy(ICodecAPI *This,const GUID *Api);")
cpp_quote("  void __RPC_STUB ICodecAPI_IsModifiable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_GetParameterRange_Proxy(ICodecAPI *This,const GUID *Api,VARIANT *ValueMin,VARIANT *ValueMax,VARIANT *SteppingDelta);")
cpp_quote("  void __RPC_STUB ICodecAPI_GetParameterRange_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_GetParameterValues_Proxy(ICodecAPI *This,const GUID *Api,VARIANT **Values,ULONG *ValuesCount);")
cpp_quote("  void __RPC_STUB ICodecAPI_GetParameterValues_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_GetDefaultValue_Proxy(ICodecAPI *This,const GUID *Api,VARIANT *Value);")
cpp_quote("  void __RPC_STUB ICodecAPI_GetDefaultValue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_GetValue_Proxy(ICodecAPI *This,const GUID *Api,VARIANT *Value);")
cpp_quote("  void __RPC_STUB ICodecAPI_GetValue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_SetValue_Proxy(ICodecAPI *This,const GUID *Api,VARIANT *Value);")
cpp_quote("  void __RPC_STUB ICodecAPI_SetValue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_RegisterForEvent_Proxy(ICodecAPI *This,const GUID *Api,LONG_PTR userData);")
cpp_quote("  void __RPC_STUB ICodecAPI_RegisterForEvent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_UnregisterForEvent_Proxy(ICodecAPI *This,const GUID *Api);")
cpp_quote("  void __RPC_STUB ICodecAPI_UnregisterForEvent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_SetAllDefaults_Proxy(ICodecAPI *This);")
cpp_quote("  void __RPC_STUB ICodecAPI_SetAllDefaults_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_SetValueWithNotify_Proxy(ICodecAPI *This,const GUID *Api,VARIANT *Value,GUID **ChangedParam,ULONG *ChangedParamCount);")
cpp_quote("  void __RPC_STUB ICodecAPI_SetValueWithNotify_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_SetAllDefaultsWithNotify_Proxy(ICodecAPI *This,GUID **ChangedParam,ULONG *ChangedParamCount);")
cpp_quote("  void __RPC_STUB ICodecAPI_SetAllDefaultsWithNotify_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_GetAllSettings_Proxy(ICodecAPI *This,IStream *__MIDL_0016);")
cpp_quote("  void __RPC_STUB ICodecAPI_GetAllSettings_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_SetAllSettings_Proxy(ICodecAPI *This,IStream *__MIDL_0017);")
cpp_quote("  void __RPC_STUB ICodecAPI_SetAllSettings_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI ICodecAPI_SetAllSettingsWithNotify_Proxy(ICodecAPI *This,IStream *__MIDL_0018,GUID **ChangedParam,ULONG *ChangedParamCount);")
cpp_quote("  void __RPC_STUB ICodecAPI_SetAllSettingsWithNotify_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IGetCapabilitiesKey_INTERFACE_DEFINED__")
cpp_quote("#define __IGetCapabilitiesKey_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IGetCapabilitiesKey;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IGetCapabilitiesKey : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetCapabilitiesKey(HKEY *pHKey) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IGetCapabilitiesKeyVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IGetCapabilitiesKey *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IGetCapabilitiesKey *This);")
cpp_quote("      ULONG (WINAPI *Release)(IGetCapabilitiesKey *This);")
cpp_quote("      HRESULT (WINAPI *GetCapabilitiesKey)(IGetCapabilitiesKey *This,HKEY *pHKey);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IGetCapabilitiesKeyVtbl;")
cpp_quote("  struct IGetCapabilitiesKey {")
cpp_quote("    CONST_VTBL struct IGetCapabilitiesKeyVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IGetCapabilitiesKey_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IGetCapabilitiesKey_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IGetCapabilitiesKey_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IGetCapabilitiesKey_GetCapabilitiesKey(This,pHKey) (This)->lpVtbl->GetCapabilitiesKey(This,pHKey)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IGetCapabilitiesKey_GetCapabilitiesKey_Proxy(IGetCapabilitiesKey *This,HKEY *pHKey);")
cpp_quote("  void __RPC_STUB IGetCapabilitiesKey_GetCapabilitiesKey_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IEncoderAPI_INTERFACE_DEFINED__")
cpp_quote("#define __IEncoderAPI_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IEncoderAPI;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IEncoderAPI : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI IsSupported(const GUID *Api) = 0;")
cpp_quote("    virtual HRESULT WINAPI IsAvailable(const GUID *Api) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetParameterRange(const GUID *Api,VARIANT *ValueMin,VARIANT *ValueMax,VARIANT *SteppingDelta) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetParameterValues(const GUID *Api,VARIANT **Values,ULONG *ValuesCount) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDefaultValue(const GUID *Api,VARIANT *Value) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetValue(const GUID *Api,VARIANT *Value) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetValue(const GUID *Api,VARIANT *Value) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IEncoderAPIVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IEncoderAPI *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IEncoderAPI *This);")
cpp_quote("      ULONG (WINAPI *Release)(IEncoderAPI *This);")
cpp_quote("      HRESULT (WINAPI *IsSupported)(IEncoderAPI *This,const GUID *Api);")
cpp_quote("      HRESULT (WINAPI *IsAvailable)(IEncoderAPI *This,const GUID *Api);")
cpp_quote("      HRESULT (WINAPI *GetParameterRange)(IEncoderAPI *This,const GUID *Api,VARIANT *ValueMin,VARIANT *ValueMax,VARIANT *SteppingDelta);")
cpp_quote("      HRESULT (WINAPI *GetParameterValues)(IEncoderAPI *This,const GUID *Api,VARIANT **Values,ULONG *ValuesCount);")
cpp_quote("      HRESULT (WINAPI *GetDefaultValue)(IEncoderAPI *This,const GUID *Api,VARIANT *Value);")
cpp_quote("      HRESULT (WINAPI *GetValue)(IEncoderAPI *This,const GUID *Api,VARIANT *Value);")
cpp_quote("      HRESULT (WINAPI *SetValue)(IEncoderAPI *This,const GUID *Api,VARIANT *Value);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IEncoderAPIVtbl;")
cpp_quote("  struct IEncoderAPI {")
cpp_quote("    CONST_VTBL struct IEncoderAPIVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IEncoderAPI_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IEncoderAPI_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IEncoderAPI_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IEncoderAPI_IsSupported(This,Api) (This)->lpVtbl->IsSupported(This,Api)")
cpp_quote("#define IEncoderAPI_IsAvailable(This,Api) (This)->lpVtbl->IsAvailable(This,Api)")
cpp_quote("#define IEncoderAPI_GetParameterRange(This,Api,ValueMin,ValueMax,SteppingDelta) (This)->lpVtbl->GetParameterRange(This,Api,ValueMin,ValueMax,SteppingDelta)")
cpp_quote("#define IEncoderAPI_GetParameterValues(This,Api,Values,ValuesCount) (This)->lpVtbl->GetParameterValues(This,Api,Values,ValuesCount)")
cpp_quote("#define IEncoderAPI_GetDefaultValue(This,Api,Value) (This)->lpVtbl->GetDefaultValue(This,Api,Value)")
cpp_quote("#define IEncoderAPI_GetValue(This,Api,Value) (This)->lpVtbl->GetValue(This,Api,Value)")
cpp_quote("#define IEncoderAPI_SetValue(This,Api,Value) (This)->lpVtbl->SetValue(This,Api,Value)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IEncoderAPI_IsSupported_Proxy(IEncoderAPI *This,const GUID *Api);")
cpp_quote("  void __RPC_STUB IEncoderAPI_IsSupported_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IEncoderAPI_IsAvailable_Proxy(IEncoderAPI *This,const GUID *Api);")
cpp_quote("  void __RPC_STUB IEncoderAPI_IsAvailable_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IEncoderAPI_GetParameterRange_Proxy(IEncoderAPI *This,const GUID *Api,VARIANT *ValueMin,VARIANT *ValueMax,VARIANT *SteppingDelta);")
cpp_quote("  void __RPC_STUB IEncoderAPI_GetParameterRange_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IEncoderAPI_GetParameterValues_Proxy(IEncoderAPI *This,const GUID *Api,VARIANT **Values,ULONG *ValuesCount);")
cpp_quote("  void __RPC_STUB IEncoderAPI_GetParameterValues_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IEncoderAPI_GetDefaultValue_Proxy(IEncoderAPI *This,const GUID *Api,VARIANT *Value);")
cpp_quote("  void __RPC_STUB IEncoderAPI_GetDefaultValue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IEncoderAPI_GetValue_Proxy(IEncoderAPI *This,const GUID *Api,VARIANT *Value);")
cpp_quote("  void __RPC_STUB IEncoderAPI_GetValue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IEncoderAPI_SetValue_Proxy(IEncoderAPI *This,const GUID *Api,VARIANT *Value);")
cpp_quote("  void __RPC_STUB IEncoderAPI_SetValue_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVideoEncoder_INTERFACE_DEFINED__")
cpp_quote("#define __IVideoEncoder_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVideoEncoder;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVideoEncoder : public IEncoderAPI {")
cpp_quote("  public:")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVideoEncoderVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVideoEncoder *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVideoEncoder *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVideoEncoder *This);")
cpp_quote("      HRESULT (WINAPI *IsSupported)(IVideoEncoder *This,const GUID *Api);")
cpp_quote("      HRESULT (WINAPI *IsAvailable)(IVideoEncoder *This,const GUID *Api);")
cpp_quote("      HRESULT (WINAPI *GetParameterRange)(IVideoEncoder *This,const GUID *Api,VARIANT *ValueMin,VARIANT *ValueMax,VARIANT *SteppingDelta);")
cpp_quote("      HRESULT (WINAPI *GetParameterValues)(IVideoEncoder *This,const GUID *Api,VARIANT **Values,ULONG *ValuesCount);")
cpp_quote("      HRESULT (WINAPI *GetDefaultValue)(IVideoEncoder *This,const GUID *Api,VARIANT *Value);")
cpp_quote("      HRESULT (WINAPI *GetValue)(IVideoEncoder *This,const GUID *Api,VARIANT *Value);")
cpp_quote("      HRESULT (WINAPI *SetValue)(IVideoEncoder *This,const GUID *Api,VARIANT *Value);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVideoEncoderVtbl;")
cpp_quote("  struct IVideoEncoder {")
cpp_quote("    CONST_VTBL struct IVideoEncoderVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVideoEncoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVideoEncoder_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVideoEncoder_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVideoEncoder_IsSupported(This,Api) (This)->lpVtbl->IsSupported(This,Api)")
cpp_quote("#define IVideoEncoder_IsAvailable(This,Api) (This)->lpVtbl->IsAvailable(This,Api)")
cpp_quote("#define IVideoEncoder_GetParameterRange(This,Api,ValueMin,ValueMax,SteppingDelta) (This)->lpVtbl->GetParameterRange(This,Api,ValueMin,ValueMax,SteppingDelta)")
cpp_quote("#define IVideoEncoder_GetParameterValues(This,Api,Values,ValuesCount) (This)->lpVtbl->GetParameterValues(This,Api,Values,ValuesCount)")
cpp_quote("#define IVideoEncoder_GetDefaultValue(This,Api,Value) (This)->lpVtbl->GetDefaultValue(This,Api,Value)")
cpp_quote("#define IVideoEncoder_GetValue(This,Api,Value) (This)->lpVtbl->GetValue(This,Api,Value)")
cpp_quote("#define IVideoEncoder_SetValue(This,Api,Value) (This)->lpVtbl->SetValue(This,Api,Value)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __ENCODER_API_DEFINES__")
cpp_quote("#define __ENCODER_API_DEFINES__")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0374_0001 {")
cpp_quote("    ConstantBitRate = 0,")
cpp_quote("    VariableBitRateAverage,VariableBitRatePeak")
cpp_quote("  } VIDEOENCODER_BITRATE_MODE;")
cpp_quote("#endif")
cpp_quote("#define AM_GETDECODERCAP_QUERY_VMR_SUPPORT 0x00000001")
cpp_quote("#define VMR_NOTSUPPORTED 0x00000000")
cpp_quote("#define VMR_SUPPORTED 0x00000001")
cpp_quote("#define AM_QUERY_DECODER_VMR_SUPPORT 0x00000001")
cpp_quote("#define AM_QUERY_DECODER_DXVA_1_SUPPORT 0x00000002")
cpp_quote("#define AM_QUERY_DECODER_DVD_SUPPORT 0x00000003")
cpp_quote("#define AM_QUERY_DECODER_ATSC_SD_SUPPORT 0x00000004")
cpp_quote("#define AM_QUERY_DECODER_ATSC_HD_SUPPORT 0x00000005")
cpp_quote("#define AM_GETDECODERCAP_QUERY_VMR9_SUPPORT 0x00000006")
cpp_quote("#define DECODER_CAP_NOTSUPPORTED 0x00000000")
cpp_quote("#define DECODER_CAP_SUPPORTED 0x00000001")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0374_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0374_v0_0_s_ifspec;")
cpp_quote("#ifndef __IAMDecoderCaps_INTERFACE_DEFINED__")
cpp_quote("#define __IAMDecoderCaps_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IAMDecoderCaps;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IAMDecoderCaps : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetDecoderCaps(DWORD dwCapIndex,DWORD *lpdwCap) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IAMDecoderCapsVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IAMDecoderCaps *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IAMDecoderCaps *This);")
cpp_quote("      ULONG (WINAPI *Release)(IAMDecoderCaps *This);")
cpp_quote("      HRESULT (WINAPI *GetDecoderCaps)(IAMDecoderCaps *This,DWORD dwCapIndex,DWORD *lpdwCap);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IAMDecoderCapsVtbl;")
cpp_quote("  struct IAMDecoderCaps {")
cpp_quote("    CONST_VTBL struct IAMDecoderCapsVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IAMDecoderCaps_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IAMDecoderCaps_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IAMDecoderCaps_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IAMDecoderCaps_GetDecoderCaps(This,dwCapIndex,lpdwCap) (This)->lpVtbl->GetDecoderCaps(This,dwCapIndex,lpdwCap)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IAMDecoderCaps_GetDecoderCaps_Proxy(IAMDecoderCaps *This,DWORD dwCapIndex,DWORD *lpdwCap);")
cpp_quote("  void __RPC_STUB IAMDecoderCaps_GetDecoderCaps_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#include <ddraw.h>")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_DOMAIN {")
cpp_quote("    DVD_DOMAIN_FirstPlay = 1,")
cpp_quote("    DVD_DOMAIN_VideoManagerMenu,DVD_DOMAIN_VideoTitleSetMenu,DVD_DOMAIN_Title,")
cpp_quote("    DVD_DOMAIN_Stop")
cpp_quote("  } DVD_DOMAIN;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_MENU_ID {")
cpp_quote("    DVD_MENU_Title = 2,DVD_MENU_Root = 3,DVD_MENU_Subpicture = 4,DVD_MENU_Audio = 5,")
cpp_quote("    DVD_MENU_Angle = 6,DVD_MENU_Chapter = 7")
cpp_quote("  } DVD_MENU_ID;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_DISC_SIDE {")
cpp_quote("    DVD_SIDE_A = 1,DVD_SIDE_B = 2")
cpp_quote("  } DVD_DISC_SIDE;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_PREFERRED_DISPLAY_MODE {")
cpp_quote("    DISPLAY_CONTENT_DEFAULT = 0,DISPLAY_16x9 = 1,DISPLAY_4x3_PANSCAN_PREFERRED = 2,DISPLAY_4x3_LETTERBOX_PREFERRED = 3")
cpp_quote("  } DVD_PREFERRED_DISPLAY_MODE;")
cpp_quote("")
cpp_quote("  typedef WORD DVD_REGISTER;")
cpp_quote("  typedef DVD_REGISTER GPRMARRAY[16];")
cpp_quote("  typedef DVD_REGISTER SPRMARRAY[24];")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_ATR {")
cpp_quote("    ULONG ulCAT;")
cpp_quote("    BYTE pbATRI[768];")
cpp_quote("  } DVD_ATR;")
cpp_quote("")
cpp_quote("  typedef BYTE DVD_VideoATR[2];")
cpp_quote("  typedef BYTE DVD_AudioATR[8];")
cpp_quote("  typedef BYTE DVD_SubpictureATR[6];")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_FRAMERATE {")
cpp_quote("    DVD_FPS_25 = 1,DVD_FPS_30NonDrop = 3")
cpp_quote("  } DVD_FRAMERATE;")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_TIMECODE {")
cpp_quote("    ULONG Hours1 :4;")
cpp_quote("    ULONG Hours10 :4;")
cpp_quote("    ULONG Minutes1 :4;")
cpp_quote("    ULONG Minutes10:4;")
cpp_quote("    ULONG Seconds1 :4;")
cpp_quote("    ULONG Seconds10:4;")
cpp_quote("    ULONG Frames1 :4;")
cpp_quote("    ULONG Frames10 :2;")
cpp_quote("    ULONG FrameRateCode: 2;")
cpp_quote("  } DVD_TIMECODE;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_TIMECODE_FLAGS {")
cpp_quote("    DVD_TC_FLAG_25fps = 0x1,DVD_TC_FLAG_30fps = 0x2,DVD_TC_FLAG_DropFrame = 0x4,DVD_TC_FLAG_Interpolated = 0x8")
cpp_quote("  } DVD_TIMECODE_FLAGS;")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_HMSF_TIMECODE {")
cpp_quote("    BYTE bHours;")
cpp_quote("    BYTE bMinutes;")
cpp_quote("    BYTE bSeconds;")
cpp_quote("    BYTE bFrames;")
cpp_quote("  } DVD_HMSF_TIMECODE;")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_PLAYBACK_LOCATION2 {")
cpp_quote("    ULONG TitleNum;")
cpp_quote("    ULONG ChapterNum;")
cpp_quote("    DVD_HMSF_TIMECODE TimeCode;")
cpp_quote("    ULONG TimeCodeFlags;")
cpp_quote("  } DVD_PLAYBACK_LOCATION2;")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_PLAYBACK_LOCATION {")
cpp_quote("    ULONG TitleNum;")
cpp_quote("    ULONG ChapterNum;")
cpp_quote("    ULONG TimeCode;")
cpp_quote("  } DVD_PLAYBACK_LOCATION;")
cpp_quote("")
cpp_quote("  typedef DWORD VALID_UOP_SOMTHING_OR_OTHER;")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0376_0001 {")
cpp_quote("    UOP_FLAG_Play_Title_Or_AtTime = 0x1,UOP_FLAG_Play_Chapter = 0x2,UOP_FLAG_Play_Title = 0x4,UOP_FLAG_Stop = 0x8,UOP_FLAG_ReturnFromSubMenu = 0x10,")
cpp_quote("    UOP_FLAG_Play_Chapter_Or_AtTime = 0x20,UOP_FLAG_PlayPrev_Or_Replay_Chapter = 0x40,UOP_FLAG_PlayNext_Chapter = 0x80,UOP_FLAG_Play_Forwards = 0x100,")
cpp_quote("    UOP_FLAG_Play_Backwards = 0x200,UOP_FLAG_ShowMenu_Title = 0x400,UOP_FLAG_ShowMenu_Root = 0x800,UOP_FLAG_ShowMenu_SubPic = 0x1000,")
cpp_quote("    UOP_FLAG_ShowMenu_Audio = 0x2000,UOP_FLAG_ShowMenu_Angle = 0x4000,UOP_FLAG_ShowMenu_Chapter = 0x8000,UOP_FLAG_Resume = 0x10000,")
cpp_quote("    UOP_FLAG_Select_Or_Activate_Button = 0x20000,UOP_FLAG_Still_Off = 0x40000,UOP_FLAG_Pause_On = 0x80000,UOP_FLAG_Select_Audio_Stream = 0x100000,")
cpp_quote("    UOP_FLAG_Select_SubPic_Stream = 0x200000,UOP_FLAG_Select_Angle = 0x400000,UOP_FLAG_Select_Karaoke_Audio_Presentation_Mode = 0x800000,")
cpp_quote("    UOP_FLAG_Select_Video_Mode_Preference = 0x1000000")
cpp_quote("  } VALID_UOP_FLAG;")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0376_0002 {")
cpp_quote("    DVD_CMD_FLAG_None = 0,DVD_CMD_FLAG_Flush = 0x1,DVD_CMD_FLAG_SendEvents = 0x2,DVD_CMD_FLAG_Block = 0x4,DVD_CMD_FLAG_StartWhenRendered = 0x8,")
cpp_quote("    DVD_CMD_FLAG_EndAfterRendered = 0x10")
cpp_quote("  } DVD_CMD_FLAGS;")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0376_0003 {")
cpp_quote("    DVD_ResetOnStop = 1,DVD_NotifyParentalLevelChange = 2,DVD_HMSF_TimeCodeEvents = 3,DVD_AudioDuringFFwdRew = 4")
cpp_quote("  } DVD_OPTION_FLAG;")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0376_0004 {")
cpp_quote("    DVD_Relative_Upper = 1,DVD_Relative_Lower = 2,DVD_Relative_Left = 3,DVD_Relative_Right = 4")
cpp_quote("  } DVD_RELATIVE_BUTTON;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_PARENTAL_LEVEL {")
cpp_quote("    DVD_PARENTAL_LEVEL_8 = 0x8000,DVD_PARENTAL_LEVEL_7 = 0x4000,DVD_PARENTAL_LEVEL_6 = 0x2000,DVD_PARENTAL_LEVEL_5 = 0x1000,")
cpp_quote("    DVD_PARENTAL_LEVEL_4 = 0x800,DVD_PARENTAL_LEVEL_3 = 0x400,DVD_PARENTAL_LEVEL_2 = 0x200,DVD_PARENTAL_LEVEL_1 = 0x100")
cpp_quote("  } DVD_PARENTAL_LEVEL;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_AUDIO_LANG_EXT {")
cpp_quote("    DVD_AUD_EXT_NotSpecified = 0,DVD_AUD_EXT_Captions = 1,DVD_AUD_EXT_VisuallyImpaired = 2,DVD_AUD_EXT_DirectorComments1 = 3,")
cpp_quote("    DVD_AUD_EXT_DirectorComments2 = 4")
cpp_quote("  } DVD_AUDIO_LANG_EXT;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_SUBPICTURE_LANG_EXT {")
cpp_quote("    DVD_SP_EXT_NotSpecified = 0,DVD_SP_EXT_Caption_Normal = 1,DVD_SP_EXT_Caption_Big = 2,DVD_SP_EXT_Caption_Children = 3,DVD_SP_EXT_CC_Normal = 5,")
cpp_quote("    DVD_SP_EXT_CC_Big = 6,DVD_SP_EXT_CC_Children = 7,DVD_SP_EXT_Forced = 9,DVD_SP_EXT_DirectorComments_Normal = 13,DVD_SP_EXT_DirectorComments_Big = 14,")
cpp_quote("    DVD_SP_EXT_DirectorComments_Children = 15")
cpp_quote("  } DVD_SUBPICTURE_LANG_EXT;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_AUDIO_APPMODE {")
cpp_quote("    DVD_AudioMode_None = 0,DVD_AudioMode_Karaoke = 1,DVD_AudioMode_Surround = 2,DVD_AudioMode_Other = 3")
cpp_quote("  } DVD_AUDIO_APPMODE;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_AUDIO_FORMAT {")
cpp_quote("    DVD_AudioFormat_AC3 = 0,DVD_AudioFormat_MPEG1 = 1,DVD_AudioFormat_MPEG1_DRC = 2,DVD_AudioFormat_MPEG2 = 3,DVD_AudioFormat_MPEG2_DRC = 4,")
cpp_quote("    DVD_AudioFormat_LPCM = 5,DVD_AudioFormat_DTS = 6,DVD_AudioFormat_SDDS = 7,DVD_AudioFormat_Other = 8")
cpp_quote("  } DVD_AUDIO_FORMAT;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_KARAOKE_DOWNMIX {")
cpp_quote("    DVD_Mix_0to0 = 0x1,DVD_Mix_1to0 = 0x2,DVD_Mix_2to0 = 0x4,DVD_Mix_3to0 = 0x8,DVD_Mix_4to0 = 0x10,DVD_Mix_Lto0 = 0x20,DVD_Mix_Rto0 = 0x40,")
cpp_quote("    DVD_Mix_0to1 = 0x100,DVD_Mix_1to1 = 0x200,DVD_Mix_2to1 = 0x400,DVD_Mix_3to1 = 0x800,DVD_Mix_4to1 = 0x1000,DVD_Mix_Lto1 = 0x2000,")
cpp_quote("    DVD_Mix_Rto1 = 0x4000")
cpp_quote("  } DVD_KARAOKE_DOWNMIX;")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_AudioAttributes {")
cpp_quote("    DVD_AUDIO_APPMODE AppMode;")
cpp_quote("    BYTE AppModeData;")
cpp_quote("    DVD_AUDIO_FORMAT AudioFormat;")
cpp_quote("    LCID Language;")
cpp_quote("    DVD_AUDIO_LANG_EXT LanguageExtension;")
cpp_quote("    WINBOOL fHasMultichannelInfo;")
cpp_quote("    DWORD dwFrequency;")
cpp_quote("    BYTE bQuantization;")
cpp_quote("    BYTE bNumberOfChannels;")
cpp_quote("    DWORD dwReserved[2];")
cpp_quote("  } DVD_AudioAttributes;")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_MUA_MixingInfo {")
cpp_quote("    WINBOOL fMixTo0;")
cpp_quote("    WINBOOL fMixTo1;")
cpp_quote("    WINBOOL fMix0InPhase;")
cpp_quote("    WINBOOL fMix1InPhase;")
cpp_quote("    DWORD dwSpeakerPosition;")
cpp_quote("  } DVD_MUA_MixingInfo;")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_MUA_Coeff {")
cpp_quote("    double log2_alpha;")
cpp_quote("    double log2_beta;")
cpp_quote("  } DVD_MUA_Coeff;")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_MultichannelAudioAttributes {")
cpp_quote("    DVD_MUA_MixingInfo Info[8];")
cpp_quote("    DVD_MUA_Coeff Coeff[8];")
cpp_quote("  } DVD_MultichannelAudioAttributes;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_KARAOKE_CONTENTS {")
cpp_quote("    DVD_Karaoke_GuideVocal1 = 0x1,DVD_Karaoke_GuideVocal2 = 0x2,DVD_Karaoke_GuideMelody1 = 0x4,DVD_Karaoke_GuideMelody2 = 0x8,")
cpp_quote("    DVD_Karaoke_GuideMelodyA = 0x10,DVD_Karaoke_GuideMelodyB = 0x20,DVD_Karaoke_SoundEffectA = 0x40,DVD_Karaoke_SoundEffectB = 0x80")
cpp_quote("  } DVD_KARAOKE_CONTENTS;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_KARAOKE_ASSIGNMENT {")
cpp_quote("    DVD_Assignment_reserved0 = 0,DVD_Assignment_reserved1 = 1,DVD_Assignment_LR = 2,DVD_Assignment_LRM = 3,DVD_Assignment_LR1 = 4,")
cpp_quote("    DVD_Assignment_LRM1 = 5,DVD_Assignment_LR12 = 6,DVD_Assignment_LRM12 = 7")
cpp_quote("  } DVD_KARAOKE_ASSIGNMENT;")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_KaraokeAttributes {")
cpp_quote("    BYTE bVersion;")
cpp_quote("    WINBOOL fMasterOfCeremoniesInGuideVocal1;")
cpp_quote("    WINBOOL fDuet;")
cpp_quote("    DVD_KARAOKE_ASSIGNMENT ChannelAssignment;")
cpp_quote("    WORD wChannelContents[8];")
cpp_quote("  } DVD_KaraokeAttributes;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_VIDEO_COMPRESSION {")
cpp_quote("    DVD_VideoCompression_Other = 0,DVD_VideoCompression_MPEG1 = 1,DVD_VideoCompression_MPEG2 = 2")
cpp_quote("  } DVD_VIDEO_COMPRESSION;")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_VideoAttributes {")
cpp_quote("    WINBOOL fPanscanPermitted;")
cpp_quote("    WINBOOL fLetterboxPermitted;")
cpp_quote("    ULONG ulAspectX;")
cpp_quote("    ULONG ulAspectY;")
cpp_quote("    ULONG ulFrameRate;")
cpp_quote("    ULONG ulFrameHeight;")
cpp_quote("    DVD_VIDEO_COMPRESSION Compression;")
cpp_quote("    WINBOOL fLine21Field1InGOP;")
cpp_quote("    WINBOOL fLine21Field2InGOP;")
cpp_quote("    ULONG ulSourceResolutionX;")
cpp_quote("    ULONG ulSourceResolutionY;")
cpp_quote("    WINBOOL fIsSourceLetterboxed;")
cpp_quote("    WINBOOL fIsFilmMode;")
cpp_quote("  } DVD_VideoAttributes;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_SUBPICTURE_TYPE {")
cpp_quote("    DVD_SPType_NotSpecified = 0,DVD_SPType_Language = 1,DVD_SPType_Other = 2")
cpp_quote("  } DVD_SUBPICTURE_TYPE;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_SUBPICTURE_CODING {")
cpp_quote("    DVD_SPCoding_RunLength = 0,DVD_SPCoding_Extended = 1,DVD_SPCoding_Other = 2")
cpp_quote("  } DVD_SUBPICTURE_CODING;")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_SubpictureAttributes {")
cpp_quote("    DVD_SUBPICTURE_TYPE Type;")
cpp_quote("    DVD_SUBPICTURE_CODING CodingMode;")
cpp_quote("    LCID Language;")
cpp_quote("    DVD_SUBPICTURE_LANG_EXT LanguageExtension;")
cpp_quote("  } DVD_SubpictureAttributes;")
cpp_quote("")
cpp_quote("  typedef enum tagDVD_TITLE_APPMODE {")
cpp_quote("    DVD_AppMode_Not_Specified = 0,DVD_AppMode_Karaoke = 1,DVD_AppMode_Other = 3")
cpp_quote("  } DVD_TITLE_APPMODE;")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_TitleMainAttributes {")
cpp_quote("    DVD_TITLE_APPMODE AppMode;")
cpp_quote("    DVD_VideoAttributes VideoAttributes;")
cpp_quote("    ULONG ulNumberOfAudioStreams;")
cpp_quote("    DVD_AudioAttributes AudioAttributes[8];")
cpp_quote("    DVD_MultichannelAudioAttributes MultichannelAudioAttributes[8];")
cpp_quote("    ULONG ulNumberOfSubpictureStreams;")
cpp_quote("    DVD_SubpictureAttributes SubpictureAttributes[32];")
cpp_quote("  } DVD_TitleAttributes;")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_MenuAttributes {")
cpp_quote("    WINBOOL fCompatibleRegion[8];")
cpp_quote("    DVD_VideoAttributes VideoAttributes;")
cpp_quote("    WINBOOL fAudioPresent;")
cpp_quote("    DVD_AudioAttributes AudioAttributes;")
cpp_quote("    WINBOOL fSubpicturePresent;")
cpp_quote("    DVD_SubpictureAttributes SubpictureAttributes;")
cpp_quote("  } DVD_MenuAttributes;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0376_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0376_v0_0_s_ifspec;")
cpp_quote("#ifndef __IDvdControl_INTERFACE_DEFINED__")
cpp_quote("#define __IDvdControl_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDvdControl;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDvdControl : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI TitlePlay(ULONG ulTitle) = 0;")
cpp_quote("    virtual HRESULT WINAPI ChapterPlay(ULONG ulTitle,ULONG ulChapter) = 0;")
cpp_quote("    virtual HRESULT WINAPI TimePlay(ULONG ulTitle,ULONG bcdTime) = 0;")
cpp_quote("    virtual HRESULT WINAPI StopForResume(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI GoUp(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI TimeSearch(ULONG bcdTime) = 0;")
cpp_quote("    virtual HRESULT WINAPI ChapterSearch(ULONG ulChapter) = 0;")
cpp_quote("    virtual HRESULT WINAPI PrevPGSearch(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI TopPGSearch(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI NextPGSearch(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI ForwardScan(double dwSpeed) = 0;")
cpp_quote("    virtual HRESULT WINAPI BackwardScan(double dwSpeed) = 0;")
cpp_quote("    virtual HRESULT WINAPI MenuCall(DVD_MENU_ID MenuID) = 0;")
cpp_quote("    virtual HRESULT WINAPI Resume(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI UpperButtonSelect(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI LowerButtonSelect(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI LeftButtonSelect(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI RightButtonSelect(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI ButtonActivate(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI ButtonSelectAndActivate(ULONG ulButton) = 0;")
cpp_quote("    virtual HRESULT WINAPI StillOff(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI PauseOn(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI PauseOff(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI MenuLanguageSelect(LCID Language) = 0;")
cpp_quote("    virtual HRESULT WINAPI AudioStreamChange(ULONG ulAudio) = 0;")
cpp_quote("    virtual HRESULT WINAPI SubpictureStreamChange(ULONG ulSubPicture,WINBOOL bDisplay) = 0;")
cpp_quote("    virtual HRESULT WINAPI AngleChange(ULONG ulAngle) = 0;")
cpp_quote("    virtual HRESULT WINAPI ParentalLevelSelect(ULONG ulParentalLevel) = 0;")
cpp_quote("    virtual HRESULT WINAPI ParentalCountrySelect(WORD wCountry) = 0;")
cpp_quote("    virtual HRESULT WINAPI KaraokeAudioPresentationModeChange(ULONG ulMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI VideoModePreferrence(ULONG ulPreferredDisplayMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetRoot(LPCWSTR pszPath) = 0;")
cpp_quote("    virtual HRESULT WINAPI MouseActivate(POINT point) = 0;")
cpp_quote("    virtual HRESULT WINAPI MouseSelect(POINT point) = 0;")
cpp_quote("    virtual HRESULT WINAPI ChapterPlayAutoStop(ULONG ulTitle,ULONG ulChapter,ULONG ulChaptersToPlay) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDvdControlVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDvdControl *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDvdControl *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *TitlePlay)(IDvdControl *This,ULONG ulTitle);")
cpp_quote("      HRESULT (WINAPI *ChapterPlay)(IDvdControl *This,ULONG ulTitle,ULONG ulChapter);")
cpp_quote("      HRESULT (WINAPI *TimePlay)(IDvdControl *This,ULONG ulTitle,ULONG bcdTime);")
cpp_quote("      HRESULT (WINAPI *StopForResume)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *GoUp)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *TimeSearch)(IDvdControl *This,ULONG bcdTime);")
cpp_quote("      HRESULT (WINAPI *ChapterSearch)(IDvdControl *This,ULONG ulChapter);")
cpp_quote("      HRESULT (WINAPI *PrevPGSearch)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *TopPGSearch)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *NextPGSearch)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *ForwardScan)(IDvdControl *This,double dwSpeed);")
cpp_quote("      HRESULT (WINAPI *BackwardScan)(IDvdControl *This,double dwSpeed);")
cpp_quote("      HRESULT (WINAPI *MenuCall)(IDvdControl *This,DVD_MENU_ID MenuID);")
cpp_quote("      HRESULT (WINAPI *Resume)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *UpperButtonSelect)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *LowerButtonSelect)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *LeftButtonSelect)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *RightButtonSelect)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *ButtonActivate)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *ButtonSelectAndActivate)(IDvdControl *This,ULONG ulButton);")
cpp_quote("      HRESULT (WINAPI *StillOff)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *PauseOn)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *PauseOff)(IDvdControl *This);")
cpp_quote("      HRESULT (WINAPI *MenuLanguageSelect)(IDvdControl *This,LCID Language);")
cpp_quote("      HRESULT (WINAPI *AudioStreamChange)(IDvdControl *This,ULONG ulAudio);")
cpp_quote("      HRESULT (WINAPI *SubpictureStreamChange)(IDvdControl *This,ULONG ulSubPicture,WINBOOL bDisplay);")
cpp_quote("      HRESULT (WINAPI *AngleChange)(IDvdControl *This,ULONG ulAngle);")
cpp_quote("      HRESULT (WINAPI *ParentalLevelSelect)(IDvdControl *This,ULONG ulParentalLevel);")
cpp_quote("      HRESULT (WINAPI *ParentalCountrySelect)(IDvdControl *This,WORD wCountry);")
cpp_quote("      HRESULT (WINAPI *KaraokeAudioPresentationModeChange)(IDvdControl *This,ULONG ulMode);")
cpp_quote("      HRESULT (WINAPI *VideoModePreferrence)(IDvdControl *This,ULONG ulPreferredDisplayMode);")
cpp_quote("      HRESULT (WINAPI *SetRoot)(IDvdControl *This,LPCWSTR pszPath);")
cpp_quote("      HRESULT (WINAPI *MouseActivate)(IDvdControl *This,POINT point);")
cpp_quote("      HRESULT (WINAPI *MouseSelect)(IDvdControl *This,POINT point);")
cpp_quote("      HRESULT (WINAPI *ChapterPlayAutoStop)(IDvdControl *This,ULONG ulTitle,ULONG ulChapter,ULONG ulChaptersToPlay);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDvdControlVtbl;")
cpp_quote("  struct IDvdControl {")
cpp_quote("    CONST_VTBL struct IDvdControlVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDvdControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDvdControl_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDvdControl_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDvdControl_TitlePlay(This,ulTitle) (This)->lpVtbl->TitlePlay(This,ulTitle)")
cpp_quote("#define IDvdControl_ChapterPlay(This,ulTitle,ulChapter) (This)->lpVtbl->ChapterPlay(This,ulTitle,ulChapter)")
cpp_quote("#define IDvdControl_TimePlay(This,ulTitle,bcdTime) (This)->lpVtbl->TimePlay(This,ulTitle,bcdTime)")
cpp_quote("#define IDvdControl_StopForResume(This) (This)->lpVtbl->StopForResume(This)")
cpp_quote("#define IDvdControl_GoUp(This) (This)->lpVtbl->GoUp(This)")
cpp_quote("#define IDvdControl_TimeSearch(This,bcdTime) (This)->lpVtbl->TimeSearch(This,bcdTime)")
cpp_quote("#define IDvdControl_ChapterSearch(This,ulChapter) (This)->lpVtbl->ChapterSearch(This,ulChapter)")
cpp_quote("#define IDvdControl_PrevPGSearch(This) (This)->lpVtbl->PrevPGSearch(This)")
cpp_quote("#define IDvdControl_TopPGSearch(This) (This)->lpVtbl->TopPGSearch(This)")
cpp_quote("#define IDvdControl_NextPGSearch(This) (This)->lpVtbl->NextPGSearch(This)")
cpp_quote("#define IDvdControl_ForwardScan(This,dwSpeed) (This)->lpVtbl->ForwardScan(This,dwSpeed)")
cpp_quote("#define IDvdControl_BackwardScan(This,dwSpeed) (This)->lpVtbl->BackwardScan(This,dwSpeed)")
cpp_quote("#define IDvdControl_MenuCall(This,MenuID) (This)->lpVtbl->MenuCall(This,MenuID)")
cpp_quote("#define IDvdControl_Resume(This) (This)->lpVtbl->Resume(This)")
cpp_quote("#define IDvdControl_UpperButtonSelect(This) (This)->lpVtbl->UpperButtonSelect(This)")
cpp_quote("#define IDvdControl_LowerButtonSelect(This) (This)->lpVtbl->LowerButtonSelect(This)")
cpp_quote("#define IDvdControl_LeftButtonSelect(This) (This)->lpVtbl->LeftButtonSelect(This)")
cpp_quote("#define IDvdControl_RightButtonSelect(This) (This)->lpVtbl->RightButtonSelect(This)")
cpp_quote("#define IDvdControl_ButtonActivate(This) (This)->lpVtbl->ButtonActivate(This)")
cpp_quote("#define IDvdControl_ButtonSelectAndActivate(This,ulButton) (This)->lpVtbl->ButtonSelectAndActivate(This,ulButton)")
cpp_quote("#define IDvdControl_StillOff(This) (This)->lpVtbl->StillOff(This)")
cpp_quote("#define IDvdControl_PauseOn(This) (This)->lpVtbl->PauseOn(This)")
cpp_quote("#define IDvdControl_PauseOff(This) (This)->lpVtbl->PauseOff(This)")
cpp_quote("#define IDvdControl_MenuLanguageSelect(This,Language) (This)->lpVtbl->MenuLanguageSelect(This,Language)")
cpp_quote("#define IDvdControl_AudioStreamChange(This,ulAudio) (This)->lpVtbl->AudioStreamChange(This,ulAudio)")
cpp_quote("#define IDvdControl_SubpictureStreamChange(This,ulSubPicture,bDisplay) (This)->lpVtbl->SubpictureStreamChange(This,ulSubPicture,bDisplay)")
cpp_quote("#define IDvdControl_AngleChange(This,ulAngle) (This)->lpVtbl->AngleChange(This,ulAngle)")
cpp_quote("#define IDvdControl_ParentalLevelSelect(This,ulParentalLevel) (This)->lpVtbl->ParentalLevelSelect(This,ulParentalLevel)")
cpp_quote("#define IDvdControl_ParentalCountrySelect(This,wCountry) (This)->lpVtbl->ParentalCountrySelect(This,wCountry)")
cpp_quote("#define IDvdControl_KaraokeAudioPresentationModeChange(This,ulMode) (This)->lpVtbl->KaraokeAudioPresentationModeChange(This,ulMode)")
cpp_quote("#define IDvdControl_VideoModePreferrence(This,ulPreferredDisplayMode) (This)->lpVtbl->VideoModePreferrence(This,ulPreferredDisplayMode)")
cpp_quote("#define IDvdControl_SetRoot(This,pszPath) (This)->lpVtbl->SetRoot(This,pszPath)")
cpp_quote("#define IDvdControl_MouseActivate(This,point) (This)->lpVtbl->MouseActivate(This,point)")
cpp_quote("#define IDvdControl_MouseSelect(This,point) (This)->lpVtbl->MouseSelect(This,point)")
cpp_quote("#define IDvdControl_ChapterPlayAutoStop(This,ulTitle,ulChapter,ulChaptersToPlay) (This)->lpVtbl->ChapterPlayAutoStop(This,ulTitle,ulChapter,ulChaptersToPlay)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDvdControl_TitlePlay_Proxy(IDvdControl *This,ULONG ulTitle);")
cpp_quote("  void __RPC_STUB IDvdControl_TitlePlay_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_ChapterPlay_Proxy(IDvdControl *This,ULONG ulTitle,ULONG ulChapter);")
cpp_quote("  void __RPC_STUB IDvdControl_ChapterPlay_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_TimePlay_Proxy(IDvdControl *This,ULONG ulTitle,ULONG bcdTime);")
cpp_quote("  void __RPC_STUB IDvdControl_TimePlay_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_StopForResume_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_StopForResume_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_GoUp_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_GoUp_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_TimeSearch_Proxy(IDvdControl *This,ULONG bcdTime);")
cpp_quote("  void __RPC_STUB IDvdControl_TimeSearch_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_ChapterSearch_Proxy(IDvdControl *This,ULONG ulChapter);")
cpp_quote("  void __RPC_STUB IDvdControl_ChapterSearch_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_PrevPGSearch_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_PrevPGSearch_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_TopPGSearch_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_TopPGSearch_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_NextPGSearch_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_NextPGSearch_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_ForwardScan_Proxy(IDvdControl *This,double dwSpeed);")
cpp_quote("  void __RPC_STUB IDvdControl_ForwardScan_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_BackwardScan_Proxy(IDvdControl *This,double dwSpeed);")
cpp_quote("  void __RPC_STUB IDvdControl_BackwardScan_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_MenuCall_Proxy(IDvdControl *This,DVD_MENU_ID MenuID);")
cpp_quote("  void __RPC_STUB IDvdControl_MenuCall_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_Resume_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_Resume_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_UpperButtonSelect_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_UpperButtonSelect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_LowerButtonSelect_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_LowerButtonSelect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_LeftButtonSelect_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_LeftButtonSelect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_RightButtonSelect_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_RightButtonSelect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_ButtonActivate_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_ButtonActivate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_ButtonSelectAndActivate_Proxy(IDvdControl *This,ULONG ulButton);")
cpp_quote("  void __RPC_STUB IDvdControl_ButtonSelectAndActivate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_StillOff_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_StillOff_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_PauseOn_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_PauseOn_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_PauseOff_Proxy(IDvdControl *This);")
cpp_quote("  void __RPC_STUB IDvdControl_PauseOff_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_MenuLanguageSelect_Proxy(IDvdControl *This,LCID Language);")
cpp_quote("  void __RPC_STUB IDvdControl_MenuLanguageSelect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_AudioStreamChange_Proxy(IDvdControl *This,ULONG ulAudio);")
cpp_quote("  void __RPC_STUB IDvdControl_AudioStreamChange_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_SubpictureStreamChange_Proxy(IDvdControl *This,ULONG ulSubPicture,WINBOOL bDisplay);")
cpp_quote("  void __RPC_STUB IDvdControl_SubpictureStreamChange_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_AngleChange_Proxy(IDvdControl *This,ULONG ulAngle);")
cpp_quote("  void __RPC_STUB IDvdControl_AngleChange_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_ParentalLevelSelect_Proxy(IDvdControl *This,ULONG ulParentalLevel);")
cpp_quote("  void __RPC_STUB IDvdControl_ParentalLevelSelect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_ParentalCountrySelect_Proxy(IDvdControl *This,WORD wCountry);")
cpp_quote("  void __RPC_STUB IDvdControl_ParentalCountrySelect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_KaraokeAudioPresentationModeChange_Proxy(IDvdControl *This,ULONG ulMode);")
cpp_quote("  void __RPC_STUB IDvdControl_KaraokeAudioPresentationModeChange_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_VideoModePreferrence_Proxy(IDvdControl *This,ULONG ulPreferredDisplayMode);")
cpp_quote("  void __RPC_STUB IDvdControl_VideoModePreferrence_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_SetRoot_Proxy(IDvdControl *This,LPCWSTR pszPath);")
cpp_quote("  void __RPC_STUB IDvdControl_SetRoot_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_MouseActivate_Proxy(IDvdControl *This,POINT point);")
cpp_quote("  void __RPC_STUB IDvdControl_MouseActivate_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_MouseSelect_Proxy(IDvdControl *This,POINT point);")
cpp_quote("  void __RPC_STUB IDvdControl_MouseSelect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl_ChapterPlayAutoStop_Proxy(IDvdControl *This,ULONG ulTitle,ULONG ulChapter,ULONG ulChaptersToPlay);")
cpp_quote("  void __RPC_STUB IDvdControl_ChapterPlayAutoStop_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDvdInfo_INTERFACE_DEFINED__")
cpp_quote("#define __IDvdInfo_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDvdInfo;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDvdInfo : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetCurrentDomain(DVD_DOMAIN *pDomain) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentLocation(DVD_PLAYBACK_LOCATION *pLocation) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetTotalTitleTime(ULONG *pulTotalTime) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentButton(ULONG *pulButtonsAvailable,ULONG *pulCurrentButton) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentAngle(ULONG *pulAnglesAvailable,ULONG *pulCurrentAngle) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentAudio(ULONG *pulStreamsAvailable,ULONG *pulCurrentStream) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentSubpicture(ULONG *pulStreamsAvailable,ULONG *pulCurrentStream,WINBOOL *pIsDisabled) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentUOPS(VALID_UOP_SOMTHING_OR_OTHER *pUOP) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetAllSPRMs(SPRMARRAY *pRegisterArray) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetAllGPRMs(GPRMARRAY *pRegisterArray) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetAudioLanguage(ULONG ulStream,LCID *pLanguage) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetSubpictureLanguage(ULONG ulStream,LCID *pLanguage) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetTitleAttributes(ULONG ulTitle,DVD_ATR *pATR) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetVMGAttributes(DVD_ATR *pATR) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentVideoAttributes(DVD_VideoATR *pATR) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentAudioAttributes(DVD_AudioATR *pATR) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentSubpictureAttributes(DVD_SubpictureATR *pATR) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentVolumeInfo(ULONG *pulNumOfVol,ULONG *pulThisVolNum,DVD_DISC_SIDE *pSide,ULONG *pulNumOfTitles) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDVDTextInfo(BYTE *pTextManager,ULONG ulBufSize,ULONG *pulActualSize) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetPlayerParentalLevel(ULONG *pulParentalLevel,ULONG *pulCountryCode) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetNumberOfChapters(ULONG ulTitle,ULONG *pulNumberOfChapters) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetTitleParentalLevels(ULONG ulTitle,ULONG *pulParentalLevels) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetRoot(LPSTR pRoot,ULONG ulBufSize,ULONG *pulActualSize) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDvdInfoVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDvdInfo *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDvdInfo *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDvdInfo *This);")
cpp_quote("      HRESULT (WINAPI *GetCurrentDomain)(IDvdInfo *This,DVD_DOMAIN *pDomain);")
cpp_quote("      HRESULT (WINAPI *GetCurrentLocation)(IDvdInfo *This,DVD_PLAYBACK_LOCATION *pLocation);")
cpp_quote("      HRESULT (WINAPI *GetTotalTitleTime)(IDvdInfo *This,ULONG *pulTotalTime);")
cpp_quote("      HRESULT (WINAPI *GetCurrentButton)(IDvdInfo *This,ULONG *pulButtonsAvailable,ULONG *pulCurrentButton);")
cpp_quote("      HRESULT (WINAPI *GetCurrentAngle)(IDvdInfo *This,ULONG *pulAnglesAvailable,ULONG *pulCurrentAngle);")
cpp_quote("      HRESULT (WINAPI *GetCurrentAudio)(IDvdInfo *This,ULONG *pulStreamsAvailable,ULONG *pulCurrentStream);")
cpp_quote("      HRESULT (WINAPI *GetCurrentSubpicture)(IDvdInfo *This,ULONG *pulStreamsAvailable,ULONG *pulCurrentStream,WINBOOL *pIsDisabled);")
cpp_quote("      HRESULT (WINAPI *GetCurrentUOPS)(IDvdInfo *This,VALID_UOP_SOMTHING_OR_OTHER *pUOP);")
cpp_quote("      HRESULT (WINAPI *GetAllSPRMs)(IDvdInfo *This,SPRMARRAY *pRegisterArray);")
cpp_quote("      HRESULT (WINAPI *GetAllGPRMs)(IDvdInfo *This,GPRMARRAY *pRegisterArray);")
cpp_quote("      HRESULT (WINAPI *GetAudioLanguage)(IDvdInfo *This,ULONG ulStream,LCID *pLanguage);")
cpp_quote("      HRESULT (WINAPI *GetSubpictureLanguage)(IDvdInfo *This,ULONG ulStream,LCID *pLanguage);")
cpp_quote("      HRESULT (WINAPI *GetTitleAttributes)(IDvdInfo *This,ULONG ulTitle,DVD_ATR *pATR);")
cpp_quote("      HRESULT (WINAPI *GetVMGAttributes)(IDvdInfo *This,DVD_ATR *pATR);")
cpp_quote("      HRESULT (WINAPI *GetCurrentVideoAttributes)(IDvdInfo *This,DVD_VideoATR *pATR);")
cpp_quote("      HRESULT (WINAPI *GetCurrentAudioAttributes)(IDvdInfo *This,DVD_AudioATR *pATR);")
cpp_quote("      HRESULT (WINAPI *GetCurrentSubpictureAttributes)(IDvdInfo *This,DVD_SubpictureATR *pATR);")
cpp_quote("      HRESULT (WINAPI *GetCurrentVolumeInfo)(IDvdInfo *This,ULONG *pulNumOfVol,ULONG *pulThisVolNum,DVD_DISC_SIDE *pSide,ULONG *pulNumOfTitles);")
cpp_quote("      HRESULT (WINAPI *GetDVDTextInfo)(IDvdInfo *This,BYTE *pTextManager,ULONG ulBufSize,ULONG *pulActualSize);")
cpp_quote("      HRESULT (WINAPI *GetPlayerParentalLevel)(IDvdInfo *This,ULONG *pulParentalLevel,ULONG *pulCountryCode);")
cpp_quote("      HRESULT (WINAPI *GetNumberOfChapters)(IDvdInfo *This,ULONG ulTitle,ULONG *pulNumberOfChapters);")
cpp_quote("      HRESULT (WINAPI *GetTitleParentalLevels)(IDvdInfo *This,ULONG ulTitle,ULONG *pulParentalLevels);")
cpp_quote("      HRESULT (WINAPI *GetRoot)(IDvdInfo *This,LPSTR pRoot,ULONG ulBufSize,ULONG *pulActualSize);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDvdInfoVtbl;")
cpp_quote("  struct IDvdInfo {")
cpp_quote("    CONST_VTBL struct IDvdInfoVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDvdInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDvdInfo_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDvdInfo_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDvdInfo_GetCurrentDomain(This,pDomain) (This)->lpVtbl->GetCurrentDomain(This,pDomain)")
cpp_quote("#define IDvdInfo_GetCurrentLocation(This,pLocation) (This)->lpVtbl->GetCurrentLocation(This,pLocation)")
cpp_quote("#define IDvdInfo_GetTotalTitleTime(This,pulTotalTime) (This)->lpVtbl->GetTotalTitleTime(This,pulTotalTime)")
cpp_quote("#define IDvdInfo_GetCurrentButton(This,pulButtonsAvailable,pulCurrentButton) (This)->lpVtbl->GetCurrentButton(This,pulButtonsAvailable,pulCurrentButton)")
cpp_quote("#define IDvdInfo_GetCurrentAngle(This,pulAnglesAvailable,pulCurrentAngle) (This)->lpVtbl->GetCurrentAngle(This,pulAnglesAvailable,pulCurrentAngle)")
cpp_quote("#define IDvdInfo_GetCurrentAudio(This,pulStreamsAvailable,pulCurrentStream) (This)->lpVtbl->GetCurrentAudio(This,pulStreamsAvailable,pulCurrentStream)")
cpp_quote("#define IDvdInfo_GetCurrentSubpicture(This,pulStreamsAvailable,pulCurrentStream,pIsDisabled) (This)->lpVtbl->GetCurrentSubpicture(This,pulStreamsAvailable,pulCurrentStream,pIsDisabled)")
cpp_quote("#define IDvdInfo_GetCurrentUOPS(This,pUOP) (This)->lpVtbl->GetCurrentUOPS(This,pUOP)")
cpp_quote("#define IDvdInfo_GetAllSPRMs(This,pRegisterArray) (This)->lpVtbl->GetAllSPRMs(This,pRegisterArray)")
cpp_quote("#define IDvdInfo_GetAllGPRMs(This,pRegisterArray) (This)->lpVtbl->GetAllGPRMs(This,pRegisterArray)")
cpp_quote("#define IDvdInfo_GetAudioLanguage(This,ulStream,pLanguage) (This)->lpVtbl->GetAudioLanguage(This,ulStream,pLanguage)")
cpp_quote("#define IDvdInfo_GetSubpictureLanguage(This,ulStream,pLanguage) (This)->lpVtbl->GetSubpictureLanguage(This,ulStream,pLanguage)")
cpp_quote("#define IDvdInfo_GetTitleAttributes(This,ulTitle,pATR) (This)->lpVtbl->GetTitleAttributes(This,ulTitle,pATR)")
cpp_quote("#define IDvdInfo_GetVMGAttributes(This,pATR) (This)->lpVtbl->GetVMGAttributes(This,pATR)")
cpp_quote("#define IDvdInfo_GetCurrentVideoAttributes(This,pATR) (This)->lpVtbl->GetCurrentVideoAttributes(This,pATR)")
cpp_quote("#define IDvdInfo_GetCurrentAudioAttributes(This,pATR) (This)->lpVtbl->GetCurrentAudioAttributes(This,pATR)")
cpp_quote("#define IDvdInfo_GetCurrentSubpictureAttributes(This,pATR) (This)->lpVtbl->GetCurrentSubpictureAttributes(This,pATR)")
cpp_quote("#define IDvdInfo_GetCurrentVolumeInfo(This,pulNumOfVol,pulThisVolNum,pSide,pulNumOfTitles) (This)->lpVtbl->GetCurrentVolumeInfo(This,pulNumOfVol,pulThisVolNum,pSide,pulNumOfTitles)")
cpp_quote("#define IDvdInfo_GetDVDTextInfo(This,pTextManager,ulBufSize,pulActualSize) (This)->lpVtbl->GetDVDTextInfo(This,pTextManager,ulBufSize,pulActualSize)")
cpp_quote("#define IDvdInfo_GetPlayerParentalLevel(This,pulParentalLevel,pulCountryCode) (This)->lpVtbl->GetPlayerParentalLevel(This,pulParentalLevel,pulCountryCode)")
cpp_quote("#define IDvdInfo_GetNumberOfChapters(This,ulTitle,pulNumberOfChapters) (This)->lpVtbl->GetNumberOfChapters(This,ulTitle,pulNumberOfChapters)")
cpp_quote("#define IDvdInfo_GetTitleParentalLevels(This,ulTitle,pulParentalLevels) (This)->lpVtbl->GetTitleParentalLevels(This,ulTitle,pulParentalLevels)")
cpp_quote("#define IDvdInfo_GetRoot(This,pRoot,ulBufSize,pulActualSize) (This)->lpVtbl->GetRoot(This,pRoot,ulBufSize,pulActualSize)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetCurrentDomain_Proxy(IDvdInfo *This,DVD_DOMAIN *pDomain);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetCurrentDomain_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetCurrentLocation_Proxy(IDvdInfo *This,DVD_PLAYBACK_LOCATION *pLocation);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetCurrentLocation_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetTotalTitleTime_Proxy(IDvdInfo *This,ULONG *pulTotalTime);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetTotalTitleTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetCurrentButton_Proxy(IDvdInfo *This,ULONG *pulButtonsAvailable,ULONG *pulCurrentButton);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetCurrentButton_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetCurrentAngle_Proxy(IDvdInfo *This,ULONG *pulAnglesAvailable,ULONG *pulCurrentAngle);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetCurrentAngle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetCurrentAudio_Proxy(IDvdInfo *This,ULONG *pulStreamsAvailable,ULONG *pulCurrentStream);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetCurrentAudio_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetCurrentSubpicture_Proxy(IDvdInfo *This,ULONG *pulStreamsAvailable,ULONG *pulCurrentStream,WINBOOL *pIsDisabled);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetCurrentSubpicture_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetCurrentUOPS_Proxy(IDvdInfo *This,VALID_UOP_SOMTHING_OR_OTHER *pUOP);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetCurrentUOPS_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetAllSPRMs_Proxy(IDvdInfo *This,SPRMARRAY *pRegisterArray);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetAllSPRMs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetAllGPRMs_Proxy(IDvdInfo *This,GPRMARRAY *pRegisterArray);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetAllGPRMs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetAudioLanguage_Proxy(IDvdInfo *This,ULONG ulStream,LCID *pLanguage);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetAudioLanguage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetSubpictureLanguage_Proxy(IDvdInfo *This,ULONG ulStream,LCID *pLanguage);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetSubpictureLanguage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetTitleAttributes_Proxy(IDvdInfo *This,ULONG ulTitle,DVD_ATR *pATR);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetTitleAttributes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetVMGAttributes_Proxy(IDvdInfo *This,DVD_ATR *pATR);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetVMGAttributes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetCurrentVideoAttributes_Proxy(IDvdInfo *This,DVD_VideoATR *pATR);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetCurrentVideoAttributes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetCurrentAudioAttributes_Proxy(IDvdInfo *This,DVD_AudioATR *pATR);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetCurrentAudioAttributes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetCurrentSubpictureAttributes_Proxy(IDvdInfo *This,DVD_SubpictureATR *pATR);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetCurrentSubpictureAttributes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetCurrentVolumeInfo_Proxy(IDvdInfo *This,ULONG *pulNumOfVol,ULONG *pulThisVolNum,DVD_DISC_SIDE *pSide,ULONG *pulNumOfTitles);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetCurrentVolumeInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetDVDTextInfo_Proxy(IDvdInfo *This,BYTE *pTextManager,ULONG ulBufSize,ULONG *pulActualSize);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetDVDTextInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetPlayerParentalLevel_Proxy(IDvdInfo *This,ULONG *pulParentalLevel,ULONG *pulCountryCode);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetPlayerParentalLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetNumberOfChapters_Proxy(IDvdInfo *This,ULONG ulTitle,ULONG *pulNumberOfChapters);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetNumberOfChapters_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetTitleParentalLevels_Proxy(IDvdInfo *This,ULONG ulTitle,ULONG *pulParentalLevels);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetTitleParentalLevels_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo_GetRoot_Proxy(IDvdInfo *This,LPSTR pRoot,ULONG ulBufSize,ULONG *pulActualSize);")
cpp_quote("  void __RPC_STUB IDvdInfo_GetRoot_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDvdCmd_INTERFACE_DEFINED__")
cpp_quote("#define __IDvdCmd_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDvdCmd;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDvdCmd : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI WaitForStart(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI WaitForEnd(void) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDvdCmdVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDvdCmd *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDvdCmd *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDvdCmd *This);")
cpp_quote("      HRESULT (WINAPI *WaitForStart)(IDvdCmd *This);")
cpp_quote("      HRESULT (WINAPI *WaitForEnd)(IDvdCmd *This);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDvdCmdVtbl;")
cpp_quote("  struct IDvdCmd {")
cpp_quote("    CONST_VTBL struct IDvdCmdVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDvdCmd_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDvdCmd_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDvdCmd_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDvdCmd_WaitForStart(This) (This)->lpVtbl->WaitForStart(This)")
cpp_quote("#define IDvdCmd_WaitForEnd(This) (This)->lpVtbl->WaitForEnd(This)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDvdCmd_WaitForStart_Proxy(IDvdCmd *This);")
cpp_quote("  void __RPC_STUB IDvdCmd_WaitForStart_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdCmd_WaitForEnd_Proxy(IDvdCmd *This);")
cpp_quote("  void __RPC_STUB IDvdCmd_WaitForEnd_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDvdState_INTERFACE_DEFINED__")
cpp_quote("#define __IDvdState_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDvdState;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDvdState : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetDiscID(ULONGLONG *pullUniqueID) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetParentalLevel(ULONG *pulParentalLevel) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDvdStateVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDvdState *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDvdState *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDvdState *This);")
cpp_quote("      HRESULT (WINAPI *GetDiscID)(IDvdState *This,ULONGLONG *pullUniqueID);")
cpp_quote("      HRESULT (WINAPI *GetParentalLevel)(IDvdState *This,ULONG *pulParentalLevel);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDvdStateVtbl;")
cpp_quote("  struct IDvdState {")
cpp_quote("    CONST_VTBL struct IDvdStateVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDvdState_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDvdState_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDvdState_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDvdState_GetDiscID(This,pullUniqueID) (This)->lpVtbl->GetDiscID(This,pullUniqueID)")
cpp_quote("#define IDvdState_GetParentalLevel(This,pulParentalLevel) (This)->lpVtbl->GetParentalLevel(This,pulParentalLevel)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDvdState_GetDiscID_Proxy(IDvdState *This,ULONGLONG *pullUniqueID);")
cpp_quote("  void __RPC_STUB IDvdState_GetDiscID_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdState_GetParentalLevel_Proxy(IDvdState *This,ULONG *pulParentalLevel);")
cpp_quote("  void __RPC_STUB IDvdState_GetParentalLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDvdControl2_INTERFACE_DEFINED__")
cpp_quote("#define __IDvdControl2_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDvdControl2;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDvdControl2 : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI PlayTitle(ULONG ulTitle,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI PlayChapterInTitle(ULONG ulTitle,ULONG ulChapter,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI PlayAtTimeInTitle(ULONG ulTitle,DVD_HMSF_TIMECODE *pStartTime,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI Stop(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI ReturnFromSubmenu(DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI PlayAtTime(DVD_HMSF_TIMECODE *pTime,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI PlayChapter(ULONG ulChapter,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI PlayPrevChapter(DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI ReplayChapter(DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI PlayNextChapter(DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI PlayForwards(double dSpeed,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI PlayBackwards(double dSpeed,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI ShowMenu(DVD_MENU_ID MenuID,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI Resume(DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectRelativeButton(DVD_RELATIVE_BUTTON buttonDir) = 0;")
cpp_quote("    virtual HRESULT WINAPI ActivateButton(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectButton(ULONG ulButton) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectAndActivateButton(ULONG ulButton) = 0;")
cpp_quote("    virtual HRESULT WINAPI StillOff(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI Pause(WINBOOL bState) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectAudioStream(ULONG ulAudio,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectSubpictureStream(ULONG ulSubPicture,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetSubpictureState(WINBOOL bState,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectAngle(ULONG ulAngle,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectParentalLevel(ULONG ulParentalLevel) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectParentalCountry(BYTE bCountry[2]) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectKaraokeAudioPresentationMode(ULONG ulMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectVideoModePreference(ULONG ulPreferredDisplayMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetDVDDirectory(LPCWSTR pszwPath) = 0;")
cpp_quote("    virtual HRESULT WINAPI ActivateAtPosition(POINT point) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectAtPosition(POINT point) = 0;")
cpp_quote("    virtual HRESULT WINAPI PlayChaptersAutoStop(ULONG ulTitle,ULONG ulChapter,ULONG ulChaptersToPlay,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI AcceptParentalLevelChange(WINBOOL bAccept) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetOption(DVD_OPTION_FLAG flag,WINBOOL fState) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetState(IDvdState *pState,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI PlayPeriodInTitleAutoStop(ULONG ulTitle,DVD_HMSF_TIMECODE *pStartTime,DVD_HMSF_TIMECODE *pEndTime,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetGPRM(ULONG ulIndex,WORD wValue,DWORD dwFlags,IDvdCmd **ppCmd) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectDefaultMenuLanguage(LCID Language) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectDefaultAudioLanguage(LCID Language,DVD_AUDIO_LANG_EXT audioExtension) = 0;")
cpp_quote("    virtual HRESULT WINAPI SelectDefaultSubpictureLanguage(LCID Language,DVD_SUBPICTURE_LANG_EXT subpictureExtension) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDvdControl2Vtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDvdControl2 *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDvdControl2 *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDvdControl2 *This);")
cpp_quote("      HRESULT (WINAPI *PlayTitle)(IDvdControl2 *This,ULONG ulTitle,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *PlayChapterInTitle)(IDvdControl2 *This,ULONG ulTitle,ULONG ulChapter,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *PlayAtTimeInTitle)(IDvdControl2 *This,ULONG ulTitle,DVD_HMSF_TIMECODE *pStartTime,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *Stop)(IDvdControl2 *This);")
cpp_quote("      HRESULT (WINAPI *ReturnFromSubmenu)(IDvdControl2 *This,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *PlayAtTime)(IDvdControl2 *This,DVD_HMSF_TIMECODE *pTime,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *PlayChapter)(IDvdControl2 *This,ULONG ulChapter,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *PlayPrevChapter)(IDvdControl2 *This,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *ReplayChapter)(IDvdControl2 *This,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *PlayNextChapter)(IDvdControl2 *This,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *PlayForwards)(IDvdControl2 *This,double dSpeed,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *PlayBackwards)(IDvdControl2 *This,double dSpeed,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *ShowMenu)(IDvdControl2 *This,DVD_MENU_ID MenuID,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *Resume)(IDvdControl2 *This,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *SelectRelativeButton)(IDvdControl2 *This,DVD_RELATIVE_BUTTON buttonDir);")
cpp_quote("      HRESULT (WINAPI *ActivateButton)(IDvdControl2 *This);")
cpp_quote("      HRESULT (WINAPI *SelectButton)(IDvdControl2 *This,ULONG ulButton);")
cpp_quote("      HRESULT (WINAPI *SelectAndActivateButton)(IDvdControl2 *This,ULONG ulButton);")
cpp_quote("      HRESULT (WINAPI *StillOff)(IDvdControl2 *This);")
cpp_quote("      HRESULT (WINAPI *Pause)(IDvdControl2 *This,WINBOOL bState);")
cpp_quote("      HRESULT (WINAPI *SelectAudioStream)(IDvdControl2 *This,ULONG ulAudio,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *SelectSubpictureStream)(IDvdControl2 *This,ULONG ulSubPicture,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *SetSubpictureState)(IDvdControl2 *This,WINBOOL bState,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *SelectAngle)(IDvdControl2 *This,ULONG ulAngle,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *SelectParentalLevel)(IDvdControl2 *This,ULONG ulParentalLevel);")
cpp_quote("      HRESULT (WINAPI *SelectParentalCountry)(IDvdControl2 *This,BYTE bCountry[2]);")
cpp_quote("      HRESULT (WINAPI *SelectKaraokeAudioPresentationMode)(IDvdControl2 *This,ULONG ulMode);")
cpp_quote("      HRESULT (WINAPI *SelectVideoModePreference)(IDvdControl2 *This,ULONG ulPreferredDisplayMode);")
cpp_quote("      HRESULT (WINAPI *SetDVDDirectory)(IDvdControl2 *This,LPCWSTR pszwPath);")
cpp_quote("      HRESULT (WINAPI *ActivateAtPosition)(IDvdControl2 *This,POINT point);")
cpp_quote("      HRESULT (WINAPI *SelectAtPosition)(IDvdControl2 *This,POINT point);")
cpp_quote("      HRESULT (WINAPI *PlayChaptersAutoStop)(IDvdControl2 *This,ULONG ulTitle,ULONG ulChapter,ULONG ulChaptersToPlay,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *AcceptParentalLevelChange)(IDvdControl2 *This,WINBOOL bAccept);")
cpp_quote("      HRESULT (WINAPI *SetOption)(IDvdControl2 *This,DVD_OPTION_FLAG flag,WINBOOL fState);")
cpp_quote("      HRESULT (WINAPI *SetState)(IDvdControl2 *This,IDvdState *pState,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *PlayPeriodInTitleAutoStop)(IDvdControl2 *This,ULONG ulTitle,DVD_HMSF_TIMECODE *pStartTime,DVD_HMSF_TIMECODE *pEndTime,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *SetGPRM)(IDvdControl2 *This,ULONG ulIndex,WORD wValue,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("      HRESULT (WINAPI *SelectDefaultMenuLanguage)(IDvdControl2 *This,LCID Language);")
cpp_quote("      HRESULT (WINAPI *SelectDefaultAudioLanguage)(IDvdControl2 *This,LCID Language,DVD_AUDIO_LANG_EXT audioExtension);")
cpp_quote("      HRESULT (WINAPI *SelectDefaultSubpictureLanguage)(IDvdControl2 *This,LCID Language,DVD_SUBPICTURE_LANG_EXT subpictureExtension);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDvdControl2Vtbl;")
cpp_quote("  struct IDvdControl2 {")
cpp_quote("    CONST_VTBL struct IDvdControl2Vtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDvdControl2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDvdControl2_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDvdControl2_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDvdControl2_PlayTitle(This,ulTitle,dwFlags,ppCmd) (This)->lpVtbl->PlayTitle(This,ulTitle,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_PlayChapterInTitle(This,ulTitle,ulChapter,dwFlags,ppCmd) (This)->lpVtbl->PlayChapterInTitle(This,ulTitle,ulChapter,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_PlayAtTimeInTitle(This,ulTitle,pStartTime,dwFlags,ppCmd) (This)->lpVtbl->PlayAtTimeInTitle(This,ulTitle,pStartTime,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_Stop(This) (This)->lpVtbl->Stop(This)")
cpp_quote("#define IDvdControl2_ReturnFromSubmenu(This,dwFlags,ppCmd) (This)->lpVtbl->ReturnFromSubmenu(This,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_PlayAtTime(This,pTime,dwFlags,ppCmd) (This)->lpVtbl->PlayAtTime(This,pTime,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_PlayChapter(This,ulChapter,dwFlags,ppCmd) (This)->lpVtbl->PlayChapter(This,ulChapter,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_PlayPrevChapter(This,dwFlags,ppCmd) (This)->lpVtbl->PlayPrevChapter(This,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_ReplayChapter(This,dwFlags,ppCmd) (This)->lpVtbl->ReplayChapter(This,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_PlayNextChapter(This,dwFlags,ppCmd) (This)->lpVtbl->PlayNextChapter(This,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_PlayForwards(This,dSpeed,dwFlags,ppCmd) (This)->lpVtbl->PlayForwards(This,dSpeed,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_PlayBackwards(This,dSpeed,dwFlags,ppCmd) (This)->lpVtbl->PlayBackwards(This,dSpeed,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_ShowMenu(This,MenuID,dwFlags,ppCmd) (This)->lpVtbl->ShowMenu(This,MenuID,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_Resume(This,dwFlags,ppCmd) (This)->lpVtbl->Resume(This,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_SelectRelativeButton(This,buttonDir) (This)->lpVtbl->SelectRelativeButton(This,buttonDir)")
cpp_quote("#define IDvdControl2_ActivateButton(This) (This)->lpVtbl->ActivateButton(This)")
cpp_quote("#define IDvdControl2_SelectButton(This,ulButton) (This)->lpVtbl->SelectButton(This,ulButton)")
cpp_quote("#define IDvdControl2_SelectAndActivateButton(This,ulButton) (This)->lpVtbl->SelectAndActivateButton(This,ulButton)")
cpp_quote("#define IDvdControl2_StillOff(This) (This)->lpVtbl->StillOff(This)")
cpp_quote("#define IDvdControl2_Pause(This,bState) (This)->lpVtbl->Pause(This,bState)")
cpp_quote("#define IDvdControl2_SelectAudioStream(This,ulAudio,dwFlags,ppCmd) (This)->lpVtbl->SelectAudioStream(This,ulAudio,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_SelectSubpictureStream(This,ulSubPicture,dwFlags,ppCmd) (This)->lpVtbl->SelectSubpictureStream(This,ulSubPicture,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_SetSubpictureState(This,bState,dwFlags,ppCmd) (This)->lpVtbl->SetSubpictureState(This,bState,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_SelectAngle(This,ulAngle,dwFlags,ppCmd) (This)->lpVtbl->SelectAngle(This,ulAngle,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_SelectParentalLevel(This,ulParentalLevel) (This)->lpVtbl->SelectParentalLevel(This,ulParentalLevel)")
cpp_quote("#define IDvdControl2_SelectParentalCountry(This,bCountry) (This)->lpVtbl->SelectParentalCountry(This,bCountry)")
cpp_quote("#define IDvdControl2_SelectKaraokeAudioPresentationMode(This,ulMode) (This)->lpVtbl->SelectKaraokeAudioPresentationMode(This,ulMode)")
cpp_quote("#define IDvdControl2_SelectVideoModePreference(This,ulPreferredDisplayMode) (This)->lpVtbl->SelectVideoModePreference(This,ulPreferredDisplayMode)")
cpp_quote("#define IDvdControl2_SetDVDDirectory(This,pszwPath) (This)->lpVtbl->SetDVDDirectory(This,pszwPath)")
cpp_quote("#define IDvdControl2_ActivateAtPosition(This,point) (This)->lpVtbl->ActivateAtPosition(This,point)")
cpp_quote("#define IDvdControl2_SelectAtPosition(This,point) (This)->lpVtbl->SelectAtPosition(This,point)")
cpp_quote("#define IDvdControl2_PlayChaptersAutoStop(This,ulTitle,ulChapter,ulChaptersToPlay,dwFlags,ppCmd) (This)->lpVtbl->PlayChaptersAutoStop(This,ulTitle,ulChapter,ulChaptersToPlay,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_AcceptParentalLevelChange(This,bAccept) (This)->lpVtbl->AcceptParentalLevelChange(This,bAccept)")
cpp_quote("#define IDvdControl2_SetOption(This,flag,fState) (This)->lpVtbl->SetOption(This,flag,fState)")
cpp_quote("#define IDvdControl2_SetState(This,pState,dwFlags,ppCmd) (This)->lpVtbl->SetState(This,pState,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_PlayPeriodInTitleAutoStop(This,ulTitle,pStartTime,pEndTime,dwFlags,ppCmd) (This)->lpVtbl->PlayPeriodInTitleAutoStop(This,ulTitle,pStartTime,pEndTime,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_SetGPRM(This,ulIndex,wValue,dwFlags,ppCmd) (This)->lpVtbl->SetGPRM(This,ulIndex,wValue,dwFlags,ppCmd)")
cpp_quote("#define IDvdControl2_SelectDefaultMenuLanguage(This,Language) (This)->lpVtbl->SelectDefaultMenuLanguage(This,Language)")
cpp_quote("#define IDvdControl2_SelectDefaultAudioLanguage(This,Language,audioExtension) (This)->lpVtbl->SelectDefaultAudioLanguage(This,Language,audioExtension)")
cpp_quote("#define IDvdControl2_SelectDefaultSubpictureLanguage(This,Language,subpictureExtension) (This)->lpVtbl->SelectDefaultSubpictureLanguage(This,Language,subpictureExtension)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDvdControl2_PlayTitle_Proxy(IDvdControl2 *This,ULONG ulTitle,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_PlayTitle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_PlayChapterInTitle_Proxy(IDvdControl2 *This,ULONG ulTitle,ULONG ulChapter,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_PlayChapterInTitle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_PlayAtTimeInTitle_Proxy(IDvdControl2 *This,ULONG ulTitle,DVD_HMSF_TIMECODE *pStartTime,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_PlayAtTimeInTitle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_Stop_Proxy(IDvdControl2 *This);")
cpp_quote("  void __RPC_STUB IDvdControl2_Stop_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_ReturnFromSubmenu_Proxy(IDvdControl2 *This,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_ReturnFromSubmenu_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_PlayAtTime_Proxy(IDvdControl2 *This,DVD_HMSF_TIMECODE *pTime,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_PlayAtTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_PlayChapter_Proxy(IDvdControl2 *This,ULONG ulChapter,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_PlayChapter_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_PlayPrevChapter_Proxy(IDvdControl2 *This,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_PlayPrevChapter_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_ReplayChapter_Proxy(IDvdControl2 *This,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_ReplayChapter_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_PlayNextChapter_Proxy(IDvdControl2 *This,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_PlayNextChapter_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_PlayForwards_Proxy(IDvdControl2 *This,double dSpeed,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_PlayForwards_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_PlayBackwards_Proxy(IDvdControl2 *This,double dSpeed,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_PlayBackwards_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_ShowMenu_Proxy(IDvdControl2 *This,DVD_MENU_ID MenuID,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_ShowMenu_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_Resume_Proxy(IDvdControl2 *This,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_Resume_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectRelativeButton_Proxy(IDvdControl2 *This,DVD_RELATIVE_BUTTON buttonDir);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectRelativeButton_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_ActivateButton_Proxy(IDvdControl2 *This);")
cpp_quote("  void __RPC_STUB IDvdControl2_ActivateButton_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectButton_Proxy(IDvdControl2 *This,ULONG ulButton);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectButton_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectAndActivateButton_Proxy(IDvdControl2 *This,ULONG ulButton);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectAndActivateButton_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_StillOff_Proxy(IDvdControl2 *This);")
cpp_quote("  void __RPC_STUB IDvdControl2_StillOff_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_Pause_Proxy(IDvdControl2 *This,WINBOOL bState);")
cpp_quote("  void __RPC_STUB IDvdControl2_Pause_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectAudioStream_Proxy(IDvdControl2 *This,ULONG ulAudio,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectAudioStream_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectSubpictureStream_Proxy(IDvdControl2 *This,ULONG ulSubPicture,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectSubpictureStream_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SetSubpictureState_Proxy(IDvdControl2 *This,WINBOOL bState,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_SetSubpictureState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectAngle_Proxy(IDvdControl2 *This,ULONG ulAngle,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectAngle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectParentalLevel_Proxy(IDvdControl2 *This,ULONG ulParentalLevel);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectParentalLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectParentalCountry_Proxy(IDvdControl2 *This,BYTE bCountry[2]);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectParentalCountry_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectKaraokeAudioPresentationMode_Proxy(IDvdControl2 *This,ULONG ulMode);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectKaraokeAudioPresentationMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectVideoModePreference_Proxy(IDvdControl2 *This,ULONG ulPreferredDisplayMode);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectVideoModePreference_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SetDVDDirectory_Proxy(IDvdControl2 *This,LPCWSTR pszwPath);")
cpp_quote("  void __RPC_STUB IDvdControl2_SetDVDDirectory_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_ActivateAtPosition_Proxy(IDvdControl2 *This,POINT point);")
cpp_quote("  void __RPC_STUB IDvdControl2_ActivateAtPosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectAtPosition_Proxy(IDvdControl2 *This,POINT point);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectAtPosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_PlayChaptersAutoStop_Proxy(IDvdControl2 *This,ULONG ulTitle,ULONG ulChapter,ULONG ulChaptersToPlay,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_PlayChaptersAutoStop_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_AcceptParentalLevelChange_Proxy(IDvdControl2 *This,WINBOOL bAccept);")
cpp_quote("  void __RPC_STUB IDvdControl2_AcceptParentalLevelChange_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SetOption_Proxy(IDvdControl2 *This,DVD_OPTION_FLAG flag,WINBOOL fState);")
cpp_quote("  void __RPC_STUB IDvdControl2_SetOption_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SetState_Proxy(IDvdControl2 *This,IDvdState *pState,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_SetState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_PlayPeriodInTitleAutoStop_Proxy(IDvdControl2 *This,ULONG ulTitle,DVD_HMSF_TIMECODE *pStartTime,DVD_HMSF_TIMECODE *pEndTime,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_PlayPeriodInTitleAutoStop_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SetGPRM_Proxy(IDvdControl2 *This,ULONG ulIndex,WORD wValue,DWORD dwFlags,IDvdCmd **ppCmd);")
cpp_quote("  void __RPC_STUB IDvdControl2_SetGPRM_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectDefaultMenuLanguage_Proxy(IDvdControl2 *This,LCID Language);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectDefaultMenuLanguage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectDefaultAudioLanguage_Proxy(IDvdControl2 *This,LCID Language,DVD_AUDIO_LANG_EXT audioExtension);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectDefaultAudioLanguage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdControl2_SelectDefaultSubpictureLanguage_Proxy(IDvdControl2 *This,LCID Language,DVD_SUBPICTURE_LANG_EXT subpictureExtension);")
cpp_quote("  void __RPC_STUB IDvdControl2_SelectDefaultSubpictureLanguage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  enum DVD_TextStringType {")
cpp_quote("    DVD_Struct_Volume = 0x1,DVD_Struct_Title = 0x2,DVD_Struct_ParentalID = 0x3,DVD_Struct_PartOfTitle = 0x4,DVD_Struct_Cell = 0x5,")
cpp_quote("    DVD_Stream_Audio = 0x10,DVD_Stream_Subpicture = 0x11,DVD_Stream_Angle = 0x12,DVD_Channel_Audio = 0x20,DVD_General_Name = 0x30,")
cpp_quote("    DVD_General_Comments = 0x31,DVD_Title_Series = 0x38,DVD_Title_Movie = 0x39,DVD_Title_Video = 0x3a,DVD_Title_Album = 0x3b,DVD_Title_Song = 0x3c,")
cpp_quote("    DVD_Title_Other = 0x3f,DVD_Title_Sub_Series = 0x40,DVD_Title_Sub_Movie = 0x41,DVD_Title_Sub_Video = 0x42,DVD_Title_Sub_Album = 0x43,")
cpp_quote("    DVD_Title_Sub_Song = 0x44,DVD_Title_Sub_Other = 0x47,DVD_Title_Orig_Series = 0x48,DVD_Title_Orig_Movie = 0x49,DVD_Title_Orig_Video = 0x4a,")
cpp_quote("    DVD_Title_Orig_Album = 0x4b,DVD_Title_Orig_Song = 0x4c,DVD_Title_Orig_Other = 0x4f,DVD_Other_Scene = 0x50,DVD_Other_Cut = 0x51,DVD_Other_Take = 0x52")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  enum DVD_TextCharSet {")
cpp_quote("    DVD_CharSet_Unicode = 0,DVD_CharSet_ISO646 = 1,DVD_CharSet_JIS_Roman_Kanji = 2,DVD_CharSet_ISO8859_1 = 3,")
cpp_quote("    DVD_CharSet_ShiftJIS_Kanji_Roman_Katakana = 4")
cpp_quote("  };")
cpp_quote("#define DVD_TITLE_MENU 0x000")
cpp_quote("#define DVD_STREAM_DATA_CURRENT 0x800")
cpp_quote("#define DVD_STREAM_DATA_VMGM 0x400")
cpp_quote("#define DVD_STREAM_DATA_VTSM 0x401")
cpp_quote("#define DVD_DEFAULT_AUDIO_STREAM 0x0f")
cpp_quote("")
cpp_quote("  typedef struct tagDVD_DECODER_CAPS {")
cpp_quote("    DWORD dwSize;")
cpp_quote("    DWORD dwAudioCaps;")
cpp_quote("    double dFwdMaxRateVideo;")
cpp_quote("    double dFwdMaxRateAudio;")
cpp_quote("    double dFwdMaxRateSP;")
cpp_quote("    double dBwdMaxRateVideo;")
cpp_quote("    double dBwdMaxRateAudio;")
cpp_quote("    double dBwdMaxRateSP;")
cpp_quote("    DWORD dwRes1;")
cpp_quote("    DWORD dwRes2;")
cpp_quote("    DWORD dwRes3;")
cpp_quote("    DWORD dwRes4;")
cpp_quote("  } DVD_DECODER_CAPS;")
cpp_quote("")
cpp_quote("#define DVD_AUDIO_CAPS_AC3 0x00000001")
cpp_quote("#define DVD_AUDIO_CAPS_MPEG2 0x00000002")
cpp_quote("#define DVD_AUDIO_CAPS_LPCM 0x00000004")
cpp_quote("#define DVD_AUDIO_CAPS_DTS 0x00000008")
cpp_quote("#define DVD_AUDIO_CAPS_SDDS 0x00000010")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0387_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0387_v0_0_s_ifspec;")
cpp_quote("#ifndef __IDvdInfo2_INTERFACE_DEFINED__")
cpp_quote("#define __IDvdInfo2_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDvdInfo2;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDvdInfo2 : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetCurrentDomain(DVD_DOMAIN *pDomain) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentLocation(DVD_PLAYBACK_LOCATION2 *pLocation) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetTotalTitleTime(DVD_HMSF_TIMECODE *pTotalTime,ULONG *ulTimeCodeFlags) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentButton(ULONG *pulButtonsAvailable,ULONG *pulCurrentButton) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentAngle(ULONG *pulAnglesAvailable,ULONG *pulCurrentAngle) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentAudio(ULONG *pulStreamsAvailable,ULONG *pulCurrentStream) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentSubpicture(ULONG *pulStreamsAvailable,ULONG *pulCurrentStream,WINBOOL *pbIsDisabled) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentUOPS(ULONG *pulUOPs) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetAllSPRMs(SPRMARRAY *pRegisterArray) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetAllGPRMs(GPRMARRAY *pRegisterArray) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetAudioLanguage(ULONG ulStream,LCID *pLanguage) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetSubpictureLanguage(ULONG ulStream,LCID *pLanguage) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetTitleAttributes(ULONG ulTitle,DVD_MenuAttributes *pMenu,DVD_TitleAttributes *pTitle) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetVMGAttributes(DVD_MenuAttributes *pATR) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentVideoAttributes(DVD_VideoAttributes *pATR) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetAudioAttributes(ULONG ulStream,DVD_AudioAttributes *pATR) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetKaraokeAttributes(ULONG ulStream,DVD_KaraokeAttributes *pAttributes) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetSubpictureAttributes(ULONG ulStream,DVD_SubpictureAttributes *pATR) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDVDVolumeInfo(ULONG *pulNumOfVolumes,ULONG *pulVolume,DVD_DISC_SIDE *pSide,ULONG *pulNumOfTitles) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDVDTextNumberOfLanguages(ULONG *pulNumOfLangs) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDVDTextLanguageInfo(ULONG ulLangIndex,ULONG *pulNumOfStrings,LCID *pLangCode,enum DVD_TextCharSet *pbCharacterSet) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDVDTextStringAsNative(ULONG ulLangIndex,ULONG ulStringIndex,BYTE *pbBuffer,ULONG ulMaxBufferSize,ULONG *pulActualSize,enum DVD_TextStringType *pType) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDVDTextStringAsUnicode(ULONG ulLangIndex,ULONG ulStringIndex,WCHAR *pchwBuffer,ULONG ulMaxBufferSize,ULONG *pulActualSize,enum DVD_TextStringType *pType) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetPlayerParentalLevel(ULONG *pulParentalLevel,BYTE pbCountryCode[2]) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetNumberOfChapters(ULONG ulTitle,ULONG *pulNumOfChapters) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetTitleParentalLevels(ULONG ulTitle,ULONG *pulParentalLevels) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDVDDirectory(LPWSTR pszwPath,ULONG ulMaxSize,ULONG *pulActualSize) = 0;")
cpp_quote("    virtual HRESULT WINAPI IsAudioStreamEnabled(ULONG ulStreamNum,WINBOOL *pbEnabled) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDiscID(LPCWSTR pszwPath,ULONGLONG *pullDiscID) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetState(IDvdState **pStateData) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetMenuLanguages(LCID *pLanguages,ULONG ulMaxLanguages,ULONG *pulActualLanguages) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetButtonAtPosition(POINT point,ULONG *pulButtonIndex) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCmdFromEvent(LONG_PTR lParam1,IDvdCmd **pCmdObj) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDefaultMenuLanguage(LCID *pLanguage) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDefaultAudioLanguage(LCID *pLanguage,DVD_AUDIO_LANG_EXT *pAudioExtension) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDefaultSubpictureLanguage(LCID *pLanguage,DVD_SUBPICTURE_LANG_EXT *pSubpictureExtension) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDecoderCaps(DVD_DECODER_CAPS *pCaps) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetButtonRect(ULONG ulButton,RECT *pRect) = 0;")
cpp_quote("    virtual HRESULT WINAPI IsSubpictureStreamEnabled(ULONG ulStreamNum,WINBOOL *pbEnabled) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDvdInfo2Vtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDvdInfo2 *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDvdInfo2 *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDvdInfo2 *This);")
cpp_quote("      HRESULT (WINAPI *GetCurrentDomain)(IDvdInfo2 *This,DVD_DOMAIN *pDomain);")
cpp_quote("      HRESULT (WINAPI *GetCurrentLocation)(IDvdInfo2 *This,DVD_PLAYBACK_LOCATION2 *pLocation);")
cpp_quote("      HRESULT (WINAPI *GetTotalTitleTime)(IDvdInfo2 *This,DVD_HMSF_TIMECODE *pTotalTime,ULONG *ulTimeCodeFlags);")
cpp_quote("      HRESULT (WINAPI *GetCurrentButton)(IDvdInfo2 *This,ULONG *pulButtonsAvailable,ULONG *pulCurrentButton);")
cpp_quote("      HRESULT (WINAPI *GetCurrentAngle)(IDvdInfo2 *This,ULONG *pulAnglesAvailable,ULONG *pulCurrentAngle);")
cpp_quote("      HRESULT (WINAPI *GetCurrentAudio)(IDvdInfo2 *This,ULONG *pulStreamsAvailable,ULONG *pulCurrentStream);")
cpp_quote("      HRESULT (WINAPI *GetCurrentSubpicture)(IDvdInfo2 *This,ULONG *pulStreamsAvailable,ULONG *pulCurrentStream,WINBOOL *pbIsDisabled);")
cpp_quote("      HRESULT (WINAPI *GetCurrentUOPS)(IDvdInfo2 *This,ULONG *pulUOPs);")
cpp_quote("      HRESULT (WINAPI *GetAllSPRMs)(IDvdInfo2 *This,SPRMARRAY *pRegisterArray);")
cpp_quote("      HRESULT (WINAPI *GetAllGPRMs)(IDvdInfo2 *This,GPRMARRAY *pRegisterArray);")
cpp_quote("      HRESULT (WINAPI *GetAudioLanguage)(IDvdInfo2 *This,ULONG ulStream,LCID *pLanguage);")
cpp_quote("      HRESULT (WINAPI *GetSubpictureLanguage)(IDvdInfo2 *This,ULONG ulStream,LCID *pLanguage);")
cpp_quote("      HRESULT (WINAPI *GetTitleAttributes)(IDvdInfo2 *This,ULONG ulTitle,DVD_MenuAttributes *pMenu,DVD_TitleAttributes *pTitle);")
cpp_quote("      HRESULT (WINAPI *GetVMGAttributes)(IDvdInfo2 *This,DVD_MenuAttributes *pATR);")
cpp_quote("      HRESULT (WINAPI *GetCurrentVideoAttributes)(IDvdInfo2 *This,DVD_VideoAttributes *pATR);")
cpp_quote("      HRESULT (WINAPI *GetAudioAttributes)(IDvdInfo2 *This,ULONG ulStream,DVD_AudioAttributes *pATR);")
cpp_quote("      HRESULT (WINAPI *GetKaraokeAttributes)(IDvdInfo2 *This,ULONG ulStream,DVD_KaraokeAttributes *pAttributes);")
cpp_quote("      HRESULT (WINAPI *GetSubpictureAttributes)(IDvdInfo2 *This,ULONG ulStream,DVD_SubpictureAttributes *pATR);")
cpp_quote("      HRESULT (WINAPI *GetDVDVolumeInfo)(IDvdInfo2 *This,ULONG *pulNumOfVolumes,ULONG *pulVolume,DVD_DISC_SIDE *pSide,ULONG *pulNumOfTitles);")
cpp_quote("      HRESULT (WINAPI *GetDVDTextNumberOfLanguages)(IDvdInfo2 *This,ULONG *pulNumOfLangs);")
cpp_quote("      HRESULT (WINAPI *GetDVDTextLanguageInfo)(IDvdInfo2 *This,ULONG ulLangIndex,ULONG *pulNumOfStrings,LCID *pLangCode,enum DVD_TextCharSet *pbCharacterSet);")
cpp_quote("      HRESULT (WINAPI *GetDVDTextStringAsNative)(IDvdInfo2 *This,ULONG ulLangIndex,ULONG ulStringIndex,BYTE *pbBuffer,ULONG ulMaxBufferSize,ULONG *pulActualSize,enum DVD_TextStringType *pType);")
cpp_quote("      HRESULT (WINAPI *GetDVDTextStringAsUnicode)(IDvdInfo2 *This,ULONG ulLangIndex,ULONG ulStringIndex,WCHAR *pchwBuffer,ULONG ulMaxBufferSize,ULONG *pulActualSize,enum DVD_TextStringType *pType);")
cpp_quote("      HRESULT (WINAPI *GetPlayerParentalLevel)(IDvdInfo2 *This,ULONG *pulParentalLevel,BYTE pbCountryCode[2]);")
cpp_quote("      HRESULT (WINAPI *GetNumberOfChapters)(IDvdInfo2 *This,ULONG ulTitle,ULONG *pulNumOfChapters);")
cpp_quote("      HRESULT (WINAPI *GetTitleParentalLevels)(IDvdInfo2 *This,ULONG ulTitle,ULONG *pulParentalLevels);")
cpp_quote("      HRESULT (WINAPI *GetDVDDirectory)(IDvdInfo2 *This,LPWSTR pszwPath,ULONG ulMaxSize,ULONG *pulActualSize);")
cpp_quote("      HRESULT (WINAPI *IsAudioStreamEnabled)(IDvdInfo2 *This,ULONG ulStreamNum,WINBOOL *pbEnabled);")
cpp_quote("      HRESULT (WINAPI *GetDiscID)(IDvdInfo2 *This,LPCWSTR pszwPath,ULONGLONG *pullDiscID);")
cpp_quote("      HRESULT (WINAPI *GetState)(IDvdInfo2 *This,IDvdState **pStateData);")
cpp_quote("      HRESULT (WINAPI *GetMenuLanguages)(IDvdInfo2 *This,LCID *pLanguages,ULONG ulMaxLanguages,ULONG *pulActualLanguages);")
cpp_quote("      HRESULT (WINAPI *GetButtonAtPosition)(IDvdInfo2 *This,POINT point,ULONG *pulButtonIndex);")
cpp_quote("      HRESULT (WINAPI *GetCmdFromEvent)(IDvdInfo2 *This,LONG_PTR lParam1,IDvdCmd **pCmdObj);")
cpp_quote("      HRESULT (WINAPI *GetDefaultMenuLanguage)(IDvdInfo2 *This,LCID *pLanguage);")
cpp_quote("      HRESULT (WINAPI *GetDefaultAudioLanguage)(IDvdInfo2 *This,LCID *pLanguage,DVD_AUDIO_LANG_EXT *pAudioExtension);")
cpp_quote("      HRESULT (WINAPI *GetDefaultSubpictureLanguage)(IDvdInfo2 *This,LCID *pLanguage,DVD_SUBPICTURE_LANG_EXT *pSubpictureExtension);")
cpp_quote("      HRESULT (WINAPI *GetDecoderCaps)(IDvdInfo2 *This,DVD_DECODER_CAPS *pCaps);")
cpp_quote("      HRESULT (WINAPI *GetButtonRect)(IDvdInfo2 *This,ULONG ulButton,RECT *pRect);")
cpp_quote("      HRESULT (WINAPI *IsSubpictureStreamEnabled)(IDvdInfo2 *This,ULONG ulStreamNum,WINBOOL *pbEnabled);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDvdInfo2Vtbl;")
cpp_quote("  struct IDvdInfo2 {")
cpp_quote("    CONST_VTBL struct IDvdInfo2Vtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDvdInfo2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDvdInfo2_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDvdInfo2_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDvdInfo2_GetCurrentDomain(This,pDomain) (This)->lpVtbl->GetCurrentDomain(This,pDomain)")
cpp_quote("#define IDvdInfo2_GetCurrentLocation(This,pLocation) (This)->lpVtbl->GetCurrentLocation(This,pLocation)")
cpp_quote("#define IDvdInfo2_GetTotalTitleTime(This,pTotalTime,ulTimeCodeFlags) (This)->lpVtbl->GetTotalTitleTime(This,pTotalTime,ulTimeCodeFlags)")
cpp_quote("#define IDvdInfo2_GetCurrentButton(This,pulButtonsAvailable,pulCurrentButton) (This)->lpVtbl->GetCurrentButton(This,pulButtonsAvailable,pulCurrentButton)")
cpp_quote("#define IDvdInfo2_GetCurrentAngle(This,pulAnglesAvailable,pulCurrentAngle) (This)->lpVtbl->GetCurrentAngle(This,pulAnglesAvailable,pulCurrentAngle)")
cpp_quote("#define IDvdInfo2_GetCurrentAudio(This,pulStreamsAvailable,pulCurrentStream) (This)->lpVtbl->GetCurrentAudio(This,pulStreamsAvailable,pulCurrentStream)")
cpp_quote("#define IDvdInfo2_GetCurrentSubpicture(This,pulStreamsAvailable,pulCurrentStream,pbIsDisabled) (This)->lpVtbl->GetCurrentSubpicture(This,pulStreamsAvailable,pulCurrentStream,pbIsDisabled)")
cpp_quote("#define IDvdInfo2_GetCurrentUOPS(This,pulUOPs) (This)->lpVtbl->GetCurrentUOPS(This,pulUOPs)")
cpp_quote("#define IDvdInfo2_GetAllSPRMs(This,pRegisterArray) (This)->lpVtbl->GetAllSPRMs(This,pRegisterArray)")
cpp_quote("#define IDvdInfo2_GetAllGPRMs(This,pRegisterArray) (This)->lpVtbl->GetAllGPRMs(This,pRegisterArray)")
cpp_quote("#define IDvdInfo2_GetAudioLanguage(This,ulStream,pLanguage) (This)->lpVtbl->GetAudioLanguage(This,ulStream,pLanguage)")
cpp_quote("#define IDvdInfo2_GetSubpictureLanguage(This,ulStream,pLanguage) (This)->lpVtbl->GetSubpictureLanguage(This,ulStream,pLanguage)")
cpp_quote("#define IDvdInfo2_GetTitleAttributes(This,ulTitle,pMenu,pTitle) (This)->lpVtbl->GetTitleAttributes(This,ulTitle,pMenu,pTitle)")
cpp_quote("#define IDvdInfo2_GetVMGAttributes(This,pATR) (This)->lpVtbl->GetVMGAttributes(This,pATR)")
cpp_quote("#define IDvdInfo2_GetCurrentVideoAttributes(This,pATR) (This)->lpVtbl->GetCurrentVideoAttributes(This,pATR)")
cpp_quote("#define IDvdInfo2_GetAudioAttributes(This,ulStream,pATR) (This)->lpVtbl->GetAudioAttributes(This,ulStream,pATR)")
cpp_quote("#define IDvdInfo2_GetKaraokeAttributes(This,ulStream,pAttributes) (This)->lpVtbl->GetKaraokeAttributes(This,ulStream,pAttributes)")
cpp_quote("#define IDvdInfo2_GetSubpictureAttributes(This,ulStream,pATR) (This)->lpVtbl->GetSubpictureAttributes(This,ulStream,pATR)")
cpp_quote("#define IDvdInfo2_GetDVDVolumeInfo(This,pulNumOfVolumes,pulVolume,pSide,pulNumOfTitles) (This)->lpVtbl->GetDVDVolumeInfo(This,pulNumOfVolumes,pulVolume,pSide,pulNumOfTitles)")
cpp_quote("#define IDvdInfo2_GetDVDTextNumberOfLanguages(This,pulNumOfLangs) (This)->lpVtbl->GetDVDTextNumberOfLanguages(This,pulNumOfLangs)")
cpp_quote("#define IDvdInfo2_GetDVDTextLanguageInfo(This,ulLangIndex,pulNumOfStrings,pLangCode,pbCharacterSet) (This)->lpVtbl->GetDVDTextLanguageInfo(This,ulLangIndex,pulNumOfStrings,pLangCode,pbCharacterSet)")
cpp_quote("#define IDvdInfo2_GetDVDTextStringAsNative(This,ulLangIndex,ulStringIndex,pbBuffer,ulMaxBufferSize,pulActualSize,pType) (This)->lpVtbl->GetDVDTextStringAsNative(This,ulLangIndex,ulStringIndex,pbBuffer,ulMaxBufferSize,pulActualSize,pType)")
cpp_quote("#define IDvdInfo2_GetDVDTextStringAsUnicode(This,ulLangIndex,ulStringIndex,pchwBuffer,ulMaxBufferSize,pulActualSize,pType) (This)->lpVtbl->GetDVDTextStringAsUnicode(This,ulLangIndex,ulStringIndex,pchwBuffer,ulMaxBufferSize,pulActualSize,pType)")
cpp_quote("#define IDvdInfo2_GetPlayerParentalLevel(This,pulParentalLevel,pbCountryCode) (This)->lpVtbl->GetPlayerParentalLevel(This,pulParentalLevel,pbCountryCode)")
cpp_quote("#define IDvdInfo2_GetNumberOfChapters(This,ulTitle,pulNumOfChapters) (This)->lpVtbl->GetNumberOfChapters(This,ulTitle,pulNumOfChapters)")
cpp_quote("#define IDvdInfo2_GetTitleParentalLevels(This,ulTitle,pulParentalLevels) (This)->lpVtbl->GetTitleParentalLevels(This,ulTitle,pulParentalLevels)")
cpp_quote("#define IDvdInfo2_GetDVDDirectory(This,pszwPath,ulMaxSize,pulActualSize) (This)->lpVtbl->GetDVDDirectory(This,pszwPath,ulMaxSize,pulActualSize)")
cpp_quote("#define IDvdInfo2_IsAudioStreamEnabled(This,ulStreamNum,pbEnabled) (This)->lpVtbl->IsAudioStreamEnabled(This,ulStreamNum,pbEnabled)")
cpp_quote("#define IDvdInfo2_GetDiscID(This,pszwPath,pullDiscID) (This)->lpVtbl->GetDiscID(This,pszwPath,pullDiscID)")
cpp_quote("#define IDvdInfo2_GetState(This,pStateData) (This)->lpVtbl->GetState(This,pStateData)")
cpp_quote("#define IDvdInfo2_GetMenuLanguages(This,pLanguages,ulMaxLanguages,pulActualLanguages) (This)->lpVtbl->GetMenuLanguages(This,pLanguages,ulMaxLanguages,pulActualLanguages)")
cpp_quote("#define IDvdInfo2_GetButtonAtPosition(This,point,pulButtonIndex) (This)->lpVtbl->GetButtonAtPosition(This,point,pulButtonIndex)")
cpp_quote("#define IDvdInfo2_GetCmdFromEvent(This,lParam1,pCmdObj) (This)->lpVtbl->GetCmdFromEvent(This,lParam1,pCmdObj)")
cpp_quote("#define IDvdInfo2_GetDefaultMenuLanguage(This,pLanguage) (This)->lpVtbl->GetDefaultMenuLanguage(This,pLanguage)")
cpp_quote("#define IDvdInfo2_GetDefaultAudioLanguage(This,pLanguage,pAudioExtension) (This)->lpVtbl->GetDefaultAudioLanguage(This,pLanguage,pAudioExtension)")
cpp_quote("#define IDvdInfo2_GetDefaultSubpictureLanguage(This,pLanguage,pSubpictureExtension) (This)->lpVtbl->GetDefaultSubpictureLanguage(This,pLanguage,pSubpictureExtension)")
cpp_quote("#define IDvdInfo2_GetDecoderCaps(This,pCaps) (This)->lpVtbl->GetDecoderCaps(This,pCaps)")
cpp_quote("#define IDvdInfo2_GetButtonRect(This,ulButton,pRect) (This)->lpVtbl->GetButtonRect(This,ulButton,pRect)")
cpp_quote("#define IDvdInfo2_IsSubpictureStreamEnabled(This,ulStreamNum,pbEnabled) (This)->lpVtbl->IsSubpictureStreamEnabled(This,ulStreamNum,pbEnabled)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetCurrentDomain_Proxy(IDvdInfo2 *This,DVD_DOMAIN *pDomain);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetCurrentDomain_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetCurrentLocation_Proxy(IDvdInfo2 *This,DVD_PLAYBACK_LOCATION2 *pLocation);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetCurrentLocation_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetTotalTitleTime_Proxy(IDvdInfo2 *This,DVD_HMSF_TIMECODE *pTotalTime,ULONG *ulTimeCodeFlags);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetTotalTitleTime_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetCurrentButton_Proxy(IDvdInfo2 *This,ULONG *pulButtonsAvailable,ULONG *pulCurrentButton);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetCurrentButton_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetCurrentAngle_Proxy(IDvdInfo2 *This,ULONG *pulAnglesAvailable,ULONG *pulCurrentAngle);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetCurrentAngle_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetCurrentAudio_Proxy(IDvdInfo2 *This,ULONG *pulStreamsAvailable,ULONG *pulCurrentStream);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetCurrentAudio_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetCurrentSubpicture_Proxy(IDvdInfo2 *This,ULONG *pulStreamsAvailable,ULONG *pulCurrentStream,WINBOOL *pbIsDisabled);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetCurrentSubpicture_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetCurrentUOPS_Proxy(IDvdInfo2 *This,ULONG *pulUOPs);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetCurrentUOPS_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetAllSPRMs_Proxy(IDvdInfo2 *This,SPRMARRAY *pRegisterArray);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetAllSPRMs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetAllGPRMs_Proxy(IDvdInfo2 *This,GPRMARRAY *pRegisterArray);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetAllGPRMs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetAudioLanguage_Proxy(IDvdInfo2 *This,ULONG ulStream,LCID *pLanguage);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetAudioLanguage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetSubpictureLanguage_Proxy(IDvdInfo2 *This,ULONG ulStream,LCID *pLanguage);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetSubpictureLanguage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetTitleAttributes_Proxy(IDvdInfo2 *This,ULONG ulTitle,DVD_MenuAttributes *pMenu,DVD_TitleAttributes *pTitle);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetTitleAttributes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetVMGAttributes_Proxy(IDvdInfo2 *This,DVD_MenuAttributes *pATR);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetVMGAttributes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetCurrentVideoAttributes_Proxy(IDvdInfo2 *This,DVD_VideoAttributes *pATR);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetCurrentVideoAttributes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetAudioAttributes_Proxy(IDvdInfo2 *This,ULONG ulStream,DVD_AudioAttributes *pATR);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetAudioAttributes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetKaraokeAttributes_Proxy(IDvdInfo2 *This,ULONG ulStream,DVD_KaraokeAttributes *pAttributes);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetKaraokeAttributes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetSubpictureAttributes_Proxy(IDvdInfo2 *This,ULONG ulStream,DVD_SubpictureAttributes *pATR);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetSubpictureAttributes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetDVDVolumeInfo_Proxy(IDvdInfo2 *This,ULONG *pulNumOfVolumes,ULONG *pulVolume,DVD_DISC_SIDE *pSide,ULONG *pulNumOfTitles);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetDVDVolumeInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetDVDTextNumberOfLanguages_Proxy(IDvdInfo2 *This,ULONG *pulNumOfLangs);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetDVDTextNumberOfLanguages_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetDVDTextLanguageInfo_Proxy(IDvdInfo2 *This,ULONG ulLangIndex,ULONG *pulNumOfStrings,LCID *pLangCode,enum DVD_TextCharSet *pbCharacterSet);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetDVDTextLanguageInfo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetDVDTextStringAsNative_Proxy(IDvdInfo2 *This,ULONG ulLangIndex,ULONG ulStringIndex,BYTE *pbBuffer,ULONG ulMaxBufferSize,ULONG *pulActualSize,enum DVD_TextStringType *pType);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetDVDTextStringAsNative_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetDVDTextStringAsUnicode_Proxy(IDvdInfo2 *This,ULONG ulLangIndex,ULONG ulStringIndex,WCHAR *pchwBuffer,ULONG ulMaxBufferSize,ULONG *pulActualSize,enum DVD_TextStringType *pType);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetDVDTextStringAsUnicode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetPlayerParentalLevel_Proxy(IDvdInfo2 *This,ULONG *pulParentalLevel,BYTE pbCountryCode[2]);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetPlayerParentalLevel_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetNumberOfChapters_Proxy(IDvdInfo2 *This,ULONG ulTitle,ULONG *pulNumOfChapters);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetNumberOfChapters_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetTitleParentalLevels_Proxy(IDvdInfo2 *This,ULONG ulTitle,ULONG *pulParentalLevels);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetTitleParentalLevels_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetDVDDirectory_Proxy(IDvdInfo2 *This,LPWSTR pszwPath,ULONG ulMaxSize,ULONG *pulActualSize);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetDVDDirectory_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_IsAudioStreamEnabled_Proxy(IDvdInfo2 *This,ULONG ulStreamNum,WINBOOL *pbEnabled);")
cpp_quote("  void __RPC_STUB IDvdInfo2_IsAudioStreamEnabled_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetDiscID_Proxy(IDvdInfo2 *This,LPCWSTR pszwPath,ULONGLONG *pullDiscID);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetDiscID_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetState_Proxy(IDvdInfo2 *This,IDvdState **pStateData);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetMenuLanguages_Proxy(IDvdInfo2 *This,LCID *pLanguages,ULONG ulMaxLanguages,ULONG *pulActualLanguages);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetMenuLanguages_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetButtonAtPosition_Proxy(IDvdInfo2 *This,POINT point,ULONG *pulButtonIndex);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetButtonAtPosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetCmdFromEvent_Proxy(IDvdInfo2 *This,LONG_PTR lParam1,IDvdCmd **pCmdObj);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetCmdFromEvent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetDefaultMenuLanguage_Proxy(IDvdInfo2 *This,LCID *pLanguage);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetDefaultMenuLanguage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetDefaultAudioLanguage_Proxy(IDvdInfo2 *This,LCID *pLanguage,DVD_AUDIO_LANG_EXT *pAudioExtension);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetDefaultAudioLanguage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetDefaultSubpictureLanguage_Proxy(IDvdInfo2 *This,LCID *pLanguage,DVD_SUBPICTURE_LANG_EXT *pSubpictureExtension);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetDefaultSubpictureLanguage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetDecoderCaps_Proxy(IDvdInfo2 *This,DVD_DECODER_CAPS *pCaps);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetDecoderCaps_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_GetButtonRect_Proxy(IDvdInfo2 *This,ULONG ulButton,RECT *pRect);")
cpp_quote("  void __RPC_STUB IDvdInfo2_GetButtonRect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdInfo2_IsSubpictureStreamEnabled_Proxy(IDvdInfo2 *This,ULONG ulStreamNum,WINBOOL *pbEnabled);")
cpp_quote("  void __RPC_STUB IDvdInfo2_IsSubpictureStreamEnabled_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef enum _AM_DVD_GRAPH_FLAGS {")
cpp_quote("    AM_DVD_HWDEC_PREFER = 0x1,AM_DVD_HWDEC_ONLY = 0x2,AM_DVD_SWDEC_PREFER = 0x4,AM_DVD_SWDEC_ONLY = 0x8,AM_DVD_NOVPE = 0x100,")
cpp_quote("    AM_DVD_VMR9_ONLY = 0x800")
cpp_quote("  } AM_DVD_GRAPH_FLAGS;")
cpp_quote("")
cpp_quote("  typedef enum _AM_DVD_STREAM_FLAGS {")
cpp_quote("    AM_DVD_STREAM_VIDEO = 0x1,AM_DVD_STREAM_AUDIO = 0x2,AM_DVD_STREAM_SUBPIC = 0x4")
cpp_quote("  } AM_DVD_STREAM_FLAGS;")
cpp_quote("")
cpp_quote("  typedef struct __MIDL___MIDL_itf_strmif_0389_0001 {")
cpp_quote("    HRESULT hrVPEStatus;")
cpp_quote("    WINBOOL bDvdVolInvalid;")
cpp_quote("    WINBOOL bDvdVolUnknown;")
cpp_quote("    WINBOOL bNoLine21In;")
cpp_quote("    WINBOOL bNoLine21Out;")
cpp_quote("    int iNumStreams;")
cpp_quote("    int iNumStreamsFailed;")
cpp_quote("    DWORD dwFailedStreamsFlag;")
cpp_quote("  } AM_DVD_RENDERSTATUS;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0389_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0389_v0_0_s_ifspec;")
cpp_quote("#ifndef __IDvdGraphBuilder_INTERFACE_DEFINED__")
cpp_quote("#define __IDvdGraphBuilder_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDvdGraphBuilder;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDvdGraphBuilder : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetFiltergraph(IGraphBuilder **ppGB) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDvdInterface(REFIID riid,void **ppvIF) = 0;")
cpp_quote("    virtual HRESULT WINAPI RenderDvdVideoVolume(LPCWSTR lpcwszPathName,DWORD dwFlags,AM_DVD_RENDERSTATUS *pStatus) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDvdGraphBuilderVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDvdGraphBuilder *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDvdGraphBuilder *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDvdGraphBuilder *This);")
cpp_quote("      HRESULT (WINAPI *GetFiltergraph)(IDvdGraphBuilder *This,IGraphBuilder **ppGB);")
cpp_quote("      HRESULT (WINAPI *GetDvdInterface)(IDvdGraphBuilder *This,REFIID riid,void **ppvIF);")
cpp_quote("      HRESULT (WINAPI *RenderDvdVideoVolume)(IDvdGraphBuilder *This,LPCWSTR lpcwszPathName,DWORD dwFlags,AM_DVD_RENDERSTATUS *pStatus);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDvdGraphBuilderVtbl;")
cpp_quote("  struct IDvdGraphBuilder {")
cpp_quote("    CONST_VTBL struct IDvdGraphBuilderVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDvdGraphBuilder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDvdGraphBuilder_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDvdGraphBuilder_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDvdGraphBuilder_GetFiltergraph(This,ppGB) (This)->lpVtbl->GetFiltergraph(This,ppGB)")
cpp_quote("#define IDvdGraphBuilder_GetDvdInterface(This,riid,ppvIF) (This)->lpVtbl->GetDvdInterface(This,riid,ppvIF)")
cpp_quote("#define IDvdGraphBuilder_RenderDvdVideoVolume(This,lpcwszPathName,dwFlags,pStatus) (This)->lpVtbl->RenderDvdVideoVolume(This,lpcwszPathName,dwFlags,pStatus)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDvdGraphBuilder_GetFiltergraph_Proxy(IDvdGraphBuilder *This,IGraphBuilder **ppGB);")
cpp_quote("  void __RPC_STUB IDvdGraphBuilder_GetFiltergraph_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdGraphBuilder_GetDvdInterface_Proxy(IDvdGraphBuilder *This,REFIID riid,void **ppvIF);")
cpp_quote("  void __RPC_STUB IDvdGraphBuilder_GetDvdInterface_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDvdGraphBuilder_RenderDvdVideoVolume_Proxy(IDvdGraphBuilder *This,LPCWSTR lpcwszPathName,DWORD dwFlags,AM_DVD_RENDERSTATUS *pStatus);")
cpp_quote("  void __RPC_STUB IDvdGraphBuilder_RenderDvdVideoVolume_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IDDrawExclModeVideo_INTERFACE_DEFINED__")
cpp_quote("#define __IDDrawExclModeVideo_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDDrawExclModeVideo;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDDrawExclModeVideo : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI SetDDrawObject(IDirectDraw *pDDrawObject) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDDrawObject(IDirectDraw **ppDDrawObject,WINBOOL *pbUsingExternal) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetDDrawSurface(IDirectDrawSurface *pDDrawSurface) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDDrawSurface(IDirectDrawSurface **ppDDrawSurface,WINBOOL *pbUsingExternal) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetDrawParameters(const RECT *prcSource,const RECT *prcTarget) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetNativeVideoProps(DWORD *pdwVideoWidth,DWORD *pdwVideoHeight,DWORD *pdwPictAspectRatioX,DWORD *pdwPictAspectRatioY) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetCallbackInterface(IDDrawExclModeVideoCallback *pCallback,DWORD dwFlags) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDDrawExclModeVideoVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDDrawExclModeVideo *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDDrawExclModeVideo *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDDrawExclModeVideo *This);")
cpp_quote("      HRESULT (WINAPI *SetDDrawObject)(IDDrawExclModeVideo *This,IDirectDraw *pDDrawObject);")
cpp_quote("      HRESULT (WINAPI *GetDDrawObject)(IDDrawExclModeVideo *This,IDirectDraw **ppDDrawObject,WINBOOL *pbUsingExternal);")
cpp_quote("      HRESULT (WINAPI *SetDDrawSurface)(IDDrawExclModeVideo *This,IDirectDrawSurface *pDDrawSurface);")
cpp_quote("      HRESULT (WINAPI *GetDDrawSurface)(IDDrawExclModeVideo *This,IDirectDrawSurface **ppDDrawSurface,WINBOOL *pbUsingExternal);")
cpp_quote("      HRESULT (WINAPI *SetDrawParameters)(IDDrawExclModeVideo *This,const RECT *prcSource,const RECT *prcTarget);")
cpp_quote("      HRESULT (WINAPI *GetNativeVideoProps)(IDDrawExclModeVideo *This,DWORD *pdwVideoWidth,DWORD *pdwVideoHeight,DWORD *pdwPictAspectRatioX,DWORD *pdwPictAspectRatioY);")
cpp_quote("      HRESULT (WINAPI *SetCallbackInterface)(IDDrawExclModeVideo *This,IDDrawExclModeVideoCallback *pCallback,DWORD dwFlags);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDDrawExclModeVideoVtbl;")
cpp_quote("  struct IDDrawExclModeVideo {")
cpp_quote("    CONST_VTBL struct IDDrawExclModeVideoVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDDrawExclModeVideo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDDrawExclModeVideo_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDDrawExclModeVideo_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDDrawExclModeVideo_SetDDrawObject(This,pDDrawObject) (This)->lpVtbl->SetDDrawObject(This,pDDrawObject)")
cpp_quote("#define IDDrawExclModeVideo_GetDDrawObject(This,ppDDrawObject,pbUsingExternal) (This)->lpVtbl->GetDDrawObject(This,ppDDrawObject,pbUsingExternal)")
cpp_quote("#define IDDrawExclModeVideo_SetDDrawSurface(This,pDDrawSurface) (This)->lpVtbl->SetDDrawSurface(This,pDDrawSurface)")
cpp_quote("#define IDDrawExclModeVideo_GetDDrawSurface(This,ppDDrawSurface,pbUsingExternal) (This)->lpVtbl->GetDDrawSurface(This,ppDDrawSurface,pbUsingExternal)")
cpp_quote("#define IDDrawExclModeVideo_SetDrawParameters(This,prcSource,prcTarget) (This)->lpVtbl->SetDrawParameters(This,prcSource,prcTarget)")
cpp_quote("#define IDDrawExclModeVideo_GetNativeVideoProps(This,pdwVideoWidth,pdwVideoHeight,pdwPictAspectRatioX,pdwPictAspectRatioY) (This)->lpVtbl->GetNativeVideoProps(This,pdwVideoWidth,pdwVideoHeight,pdwPictAspectRatioX,pdwPictAspectRatioY)")
cpp_quote("#define IDDrawExclModeVideo_SetCallbackInterface(This,pCallback,dwFlags) (This)->lpVtbl->SetCallbackInterface(This,pCallback,dwFlags)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDDrawExclModeVideo_SetDDrawObject_Proxy(IDDrawExclModeVideo *This,IDirectDraw *pDDrawObject);")
cpp_quote("  void __RPC_STUB IDDrawExclModeVideo_SetDDrawObject_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDDrawExclModeVideo_GetDDrawObject_Proxy(IDDrawExclModeVideo *This,IDirectDraw **ppDDrawObject,WINBOOL *pbUsingExternal);")
cpp_quote("  void __RPC_STUB IDDrawExclModeVideo_GetDDrawObject_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDDrawExclModeVideo_SetDDrawSurface_Proxy(IDDrawExclModeVideo *This,IDirectDrawSurface *pDDrawSurface);")
cpp_quote("  void __RPC_STUB IDDrawExclModeVideo_SetDDrawSurface_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDDrawExclModeVideo_GetDDrawSurface_Proxy(IDDrawExclModeVideo *This,IDirectDrawSurface **ppDDrawSurface,WINBOOL *pbUsingExternal);")
cpp_quote("  void __RPC_STUB IDDrawExclModeVideo_GetDDrawSurface_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDDrawExclModeVideo_SetDrawParameters_Proxy(IDDrawExclModeVideo *This,const RECT *prcSource,const RECT *prcTarget);")
cpp_quote("  void __RPC_STUB IDDrawExclModeVideo_SetDrawParameters_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDDrawExclModeVideo_GetNativeVideoProps_Proxy(IDDrawExclModeVideo *This,DWORD *pdwVideoWidth,DWORD *pdwVideoHeight,DWORD *pdwPictAspectRatioX,DWORD *pdwPictAspectRatioY);")
cpp_quote("  void __RPC_STUB IDDrawExclModeVideo_GetNativeVideoProps_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDDrawExclModeVideo_SetCallbackInterface_Proxy(IDDrawExclModeVideo *This,IDDrawExclModeVideoCallback *pCallback,DWORD dwFlags);")
cpp_quote("  void __RPC_STUB IDDrawExclModeVideo_SetCallbackInterface_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  enum _AM_OVERLAY_NOTIFY_FLAGS {")
cpp_quote("    AM_OVERLAY_NOTIFY_VISIBLE_CHANGE = 0x1,AM_OVERLAY_NOTIFY_SOURCE_CHANGE = 0x2,AM_OVERLAY_NOTIFY_DEST_CHANGE = 0x4")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0391_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0391_v0_0_s_ifspec;")
cpp_quote("#ifndef __IDDrawExclModeVideoCallback_INTERFACE_DEFINED__")
cpp_quote("#define __IDDrawExclModeVideoCallback_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IDDrawExclModeVideoCallback;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IDDrawExclModeVideoCallback : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI OnUpdateOverlay(WINBOOL bBefore,DWORD dwFlags,WINBOOL bOldVisible,const RECT *prcOldSrc,const RECT *prcOldDest,WINBOOL bNewVisible,const RECT *prcNewSrc,const RECT *prcNewDest) = 0;")
cpp_quote("    virtual HRESULT WINAPI OnUpdateColorKey(const COLORKEY *pKey,DWORD dwColor) = 0;")
cpp_quote("    virtual HRESULT WINAPI OnUpdateSize(DWORD dwWidth,DWORD dwHeight,DWORD dwARWidth,DWORD dwARHeight) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IDDrawExclModeVideoCallbackVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IDDrawExclModeVideoCallback *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IDDrawExclModeVideoCallback *This);")
cpp_quote("      ULONG (WINAPI *Release)(IDDrawExclModeVideoCallback *This);")
cpp_quote("      HRESULT (WINAPI *OnUpdateOverlay)(IDDrawExclModeVideoCallback *This,WINBOOL bBefore,DWORD dwFlags,WINBOOL bOldVisible,const RECT *prcOldSrc,const RECT *prcOldDest,WINBOOL bNewVisible,const RECT *prcNewSrc,const RECT *prcNewDest);")
cpp_quote("      HRESULT (WINAPI *OnUpdateColorKey)(IDDrawExclModeVideoCallback *This,const COLORKEY *pKey,DWORD dwColor);")
cpp_quote("      HRESULT (WINAPI *OnUpdateSize)(IDDrawExclModeVideoCallback *This,DWORD dwWidth,DWORD dwHeight,DWORD dwARWidth,DWORD dwARHeight);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IDDrawExclModeVideoCallbackVtbl;")
cpp_quote("  struct IDDrawExclModeVideoCallback {")
cpp_quote("    CONST_VTBL struct IDDrawExclModeVideoCallbackVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IDDrawExclModeVideoCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IDDrawExclModeVideoCallback_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IDDrawExclModeVideoCallback_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IDDrawExclModeVideoCallback_OnUpdateOverlay(This,bBefore,dwFlags,bOldVisible,prcOldSrc,prcOldDest,bNewVisible,prcNewSrc,prcNewDest) (This)->lpVtbl->OnUpdateOverlay(This,bBefore,dwFlags,bOldVisible,prcOldSrc,prcOldDest,bNewVisible,prcNewSrc,prcNewDest)")
cpp_quote("#define IDDrawExclModeVideoCallback_OnUpdateColorKey(This,pKey,dwColor) (This)->lpVtbl->OnUpdateColorKey(This,pKey,dwColor)")
cpp_quote("#define IDDrawExclModeVideoCallback_OnUpdateSize(This,dwWidth,dwHeight,dwARWidth,dwARHeight) (This)->lpVtbl->OnUpdateSize(This,dwWidth,dwHeight,dwARWidth,dwARHeight)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IDDrawExclModeVideoCallback_OnUpdateOverlay_Proxy(IDDrawExclModeVideoCallback *This,WINBOOL bBefore,DWORD dwFlags,WINBOOL bOldVisible,const RECT *prcOldSrc,const RECT *prcOldDest,WINBOOL bNewVisible,const RECT *prcNewSrc,const RECT *prcNewDest);")
cpp_quote("  void __RPC_STUB IDDrawExclModeVideoCallback_OnUpdateOverlay_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDDrawExclModeVideoCallback_OnUpdateColorKey_Proxy(IDDrawExclModeVideoCallback *This,const COLORKEY *pKey,DWORD dwColor);")
cpp_quote("  void __RPC_STUB IDDrawExclModeVideoCallback_OnUpdateColorKey_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IDDrawExclModeVideoCallback_OnUpdateSize_Proxy(IDDrawExclModeVideoCallback *This,DWORD dwWidth,DWORD dwHeight,DWORD dwARWidth,DWORD dwARHeight);")
cpp_quote("  void __RPC_STUB IDDrawExclModeVideoCallback_OnUpdateSize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0397_0002 {")
cpp_quote("    VMRSample_SyncPoint = 0x1,VMRSample_Preroll = 0x2,VMRSample_Discontinuity = 0x4,VMRSample_TimeValid = 0x8,VMRSample_SrcDstRectsValid = 0x10")
cpp_quote("  } VMRPresentationFlags;")
cpp_quote("")
cpp_quote("  typedef struct tagVMRPRESENTATIONINFO {")
cpp_quote("    DWORD dwFlags;")
cpp_quote("    LPDIRECTDRAWSURFACE7 lpSurf;")
cpp_quote("    REFERENCE_TIME rtStart;")
cpp_quote("    REFERENCE_TIME rtEnd;")
cpp_quote("    SIZE szAspectRatio;")
cpp_quote("    RECT rcSrc;")
cpp_quote("    RECT rcDst;")
cpp_quote("    DWORD dwTypeSpecificFlags;")
cpp_quote("    DWORD dwInterlaceFlags;")
cpp_quote("  } VMRPRESENTATIONINFO;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0397_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0397_v0_0_s_ifspec;")
cpp_quote("#ifndef __IVMRImagePresenter_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRImagePresenter_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRImagePresenter;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRImagePresenter : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI StartPresenting(DWORD_PTR dwUserID) = 0;")
cpp_quote("    virtual HRESULT WINAPI StopPresenting(DWORD_PTR dwUserID) = 0;")
cpp_quote("    virtual HRESULT WINAPI PresentImage(DWORD_PTR dwUserID,VMRPRESENTATIONINFO *lpPresInfo) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRImagePresenterVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRImagePresenter *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRImagePresenter *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRImagePresenter *This);")
cpp_quote("      HRESULT (WINAPI *StartPresenting)(IVMRImagePresenter *This,DWORD_PTR dwUserID);")
cpp_quote("      HRESULT (WINAPI *StopPresenting)(IVMRImagePresenter *This,DWORD_PTR dwUserID);")
cpp_quote("      HRESULT (WINAPI *PresentImage)(IVMRImagePresenter *This,DWORD_PTR dwUserID,VMRPRESENTATIONINFO *lpPresInfo);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRImagePresenterVtbl;")
cpp_quote("  struct IVMRImagePresenter {")
cpp_quote("    CONST_VTBL struct IVMRImagePresenterVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRImagePresenter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRImagePresenter_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRImagePresenter_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRImagePresenter_StartPresenting(This,dwUserID) (This)->lpVtbl->StartPresenting(This,dwUserID)")
cpp_quote("#define IVMRImagePresenter_StopPresenting(This,dwUserID) (This)->lpVtbl->StopPresenting(This,dwUserID)")
cpp_quote("#define IVMRImagePresenter_PresentImage(This,dwUserID,lpPresInfo) (This)->lpVtbl->PresentImage(This,dwUserID,lpPresInfo)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRImagePresenter_StartPresenting_Proxy(IVMRImagePresenter *This,DWORD_PTR dwUserID);")
cpp_quote("  void __RPC_STUB IVMRImagePresenter_StartPresenting_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRImagePresenter_StopPresenting_Proxy(IVMRImagePresenter *This,DWORD_PTR dwUserID);")
cpp_quote("  void __RPC_STUB IVMRImagePresenter_StopPresenting_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRImagePresenter_PresentImage_Proxy(IVMRImagePresenter *This,DWORD_PTR dwUserID,VMRPRESENTATIONINFO *lpPresInfo);")
cpp_quote("  void __RPC_STUB IVMRImagePresenter_PresentImage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0398_0001 {")
cpp_quote("    AMAP_PIXELFORMAT_VALID = 0x1,AMAP_3D_TARGET = 0x2,AMAP_ALLOW_SYSMEM = 0x4,AMAP_FORCE_SYSMEM = 0x8,AMAP_DIRECTED_FLIP = 0x10,AMAP_DXVA_TARGET = 0x20")
cpp_quote("  } VMRSurfaceAllocationFlags;")
cpp_quote("")
cpp_quote("  typedef struct tagVMRALLOCATIONINFO {")
cpp_quote("    DWORD dwFlags;")
cpp_quote("    LPBITMAPINFOHEADER lpHdr;")
cpp_quote("    LPDDPIXELFORMAT lpPixFmt;")
cpp_quote("    SIZE szAspectRatio;")
cpp_quote("    DWORD dwMinBuffers;")
cpp_quote("    DWORD dwMaxBuffers;")
cpp_quote("    DWORD dwInterlaceFlags;")
cpp_quote("    SIZE szNativeSize;")
cpp_quote("  } VMRALLOCATIONINFO;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0398_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0398_v0_0_s_ifspec;")
cpp_quote("#ifndef __IVMRSurfaceAllocator_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRSurfaceAllocator_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRSurfaceAllocator;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRSurfaceAllocator : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI AllocateSurface(DWORD_PTR dwUserID,VMRALLOCATIONINFO *lpAllocInfo,DWORD *lpdwActualBuffers,LPDIRECTDRAWSURFACE7 *lplpSurface) = 0;")
cpp_quote("    virtual HRESULT WINAPI FreeSurface(DWORD_PTR dwID) = 0;")
cpp_quote("    virtual HRESULT WINAPI PrepareSurface(DWORD_PTR dwUserID,LPDIRECTDRAWSURFACE7 lpSurface,DWORD dwSurfaceFlags) = 0;")
cpp_quote("    virtual HRESULT WINAPI AdviseNotify(IVMRSurfaceAllocatorNotify *lpIVMRSurfAllocNotify) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRSurfaceAllocatorVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRSurfaceAllocator *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRSurfaceAllocator *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRSurfaceAllocator *This);")
cpp_quote("      HRESULT (WINAPI *AllocateSurface)(IVMRSurfaceAllocator *This,DWORD_PTR dwUserID,VMRALLOCATIONINFO *lpAllocInfo,DWORD *lpdwActualBuffers,LPDIRECTDRAWSURFACE7 *lplpSurface);")
cpp_quote("      HRESULT (WINAPI *FreeSurface)(IVMRSurfaceAllocator *This,DWORD_PTR dwID);")
cpp_quote("      HRESULT (WINAPI *PrepareSurface)(IVMRSurfaceAllocator *This,DWORD_PTR dwUserID,LPDIRECTDRAWSURFACE7 lpSurface,DWORD dwSurfaceFlags);")
cpp_quote("      HRESULT (WINAPI *AdviseNotify)(IVMRSurfaceAllocator *This,IVMRSurfaceAllocatorNotify *lpIVMRSurfAllocNotify);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRSurfaceAllocatorVtbl;")
cpp_quote("  struct IVMRSurfaceAllocator {")
cpp_quote("    CONST_VTBL struct IVMRSurfaceAllocatorVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRSurfaceAllocator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRSurfaceAllocator_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRSurfaceAllocator_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRSurfaceAllocator_AllocateSurface(This,dwUserID,lpAllocInfo,lpdwActualBuffers,lplpSurface) (This)->lpVtbl->AllocateSurface(This,dwUserID,lpAllocInfo,lpdwActualBuffers,lplpSurface)")
cpp_quote("#define IVMRSurfaceAllocator_FreeSurface(This,dwID) (This)->lpVtbl->FreeSurface(This,dwID)")
cpp_quote("#define IVMRSurfaceAllocator_PrepareSurface(This,dwUserID,lpSurface,dwSurfaceFlags) (This)->lpVtbl->PrepareSurface(This,dwUserID,lpSurface,dwSurfaceFlags)")
cpp_quote("#define IVMRSurfaceAllocator_AdviseNotify(This,lpIVMRSurfAllocNotify) (This)->lpVtbl->AdviseNotify(This,lpIVMRSurfAllocNotify)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRSurfaceAllocator_AllocateSurface_Proxy(IVMRSurfaceAllocator *This,DWORD_PTR dwUserID,VMRALLOCATIONINFO *lpAllocInfo,DWORD *lpdwActualBuffers,LPDIRECTDRAWSURFACE7 *lplpSurface);")
cpp_quote("  void __RPC_STUB IVMRSurfaceAllocator_AllocateSurface_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRSurfaceAllocator_FreeSurface_Proxy(IVMRSurfaceAllocator *This,DWORD_PTR dwID);")
cpp_quote("  void __RPC_STUB IVMRSurfaceAllocator_FreeSurface_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRSurfaceAllocator_PrepareSurface_Proxy(IVMRSurfaceAllocator *This,DWORD_PTR dwUserID,LPDIRECTDRAWSURFACE7 lpSurface,DWORD dwSurfaceFlags);")
cpp_quote("  void __RPC_STUB IVMRSurfaceAllocator_PrepareSurface_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRSurfaceAllocator_AdviseNotify_Proxy(IVMRSurfaceAllocator *This,IVMRSurfaceAllocatorNotify *lpIVMRSurfAllocNotify);")
cpp_quote("  void __RPC_STUB IVMRSurfaceAllocator_AdviseNotify_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRSurfaceAllocatorNotify_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRSurfaceAllocatorNotify_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRSurfaceAllocatorNotify;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRSurfaceAllocatorNotify : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI AdviseSurfaceAllocator(DWORD_PTR dwUserID,IVMRSurfaceAllocator *lpIVRMSurfaceAllocator) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetDDrawDevice(LPDIRECTDRAW7 lpDDrawDevice,HMONITOR hMonitor) = 0;")
cpp_quote("    virtual HRESULT WINAPI ChangeDDrawDevice(LPDIRECTDRAW7 lpDDrawDevice,HMONITOR hMonitor) = 0;")
cpp_quote("    virtual HRESULT WINAPI RestoreDDrawSurfaces(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI NotifyEvent(LONG EventCode,LONG_PTR Param1,LONG_PTR Param2) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetBorderColor(COLORREF clrBorder) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRSurfaceAllocatorNotifyVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRSurfaceAllocatorNotify *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRSurfaceAllocatorNotify *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRSurfaceAllocatorNotify *This);")
cpp_quote("      HRESULT (WINAPI *AdviseSurfaceAllocator)(IVMRSurfaceAllocatorNotify *This,DWORD_PTR dwUserID,IVMRSurfaceAllocator *lpIVRMSurfaceAllocator);")
cpp_quote("      HRESULT (WINAPI *SetDDrawDevice)(IVMRSurfaceAllocatorNotify *This,LPDIRECTDRAW7 lpDDrawDevice,HMONITOR hMonitor);")
cpp_quote("      HRESULT (WINAPI *ChangeDDrawDevice)(IVMRSurfaceAllocatorNotify *This,LPDIRECTDRAW7 lpDDrawDevice,HMONITOR hMonitor);")
cpp_quote("      HRESULT (WINAPI *RestoreDDrawSurfaces)(IVMRSurfaceAllocatorNotify *This);")
cpp_quote("      HRESULT (WINAPI *NotifyEvent)(IVMRSurfaceAllocatorNotify *This,LONG EventCode,LONG_PTR Param1,LONG_PTR Param2);")
cpp_quote("      HRESULT (WINAPI *SetBorderColor)(IVMRSurfaceAllocatorNotify *This,COLORREF clrBorder);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRSurfaceAllocatorNotifyVtbl;")
cpp_quote("  struct IVMRSurfaceAllocatorNotify {")
cpp_quote("    CONST_VTBL struct IVMRSurfaceAllocatorNotifyVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRSurfaceAllocatorNotify_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRSurfaceAllocatorNotify_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRSurfaceAllocatorNotify_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRSurfaceAllocatorNotify_AdviseSurfaceAllocator(This,dwUserID,lpIVRMSurfaceAllocator) (This)->lpVtbl->AdviseSurfaceAllocator(This,dwUserID,lpIVRMSurfaceAllocator)")
cpp_quote("#define IVMRSurfaceAllocatorNotify_SetDDrawDevice(This,lpDDrawDevice,hMonitor) (This)->lpVtbl->SetDDrawDevice(This,lpDDrawDevice,hMonitor)")
cpp_quote("#define IVMRSurfaceAllocatorNotify_ChangeDDrawDevice(This,lpDDrawDevice,hMonitor) (This)->lpVtbl->ChangeDDrawDevice(This,lpDDrawDevice,hMonitor)")
cpp_quote("#define IVMRSurfaceAllocatorNotify_RestoreDDrawSurfaces(This) (This)->lpVtbl->RestoreDDrawSurfaces(This)")
cpp_quote("#define IVMRSurfaceAllocatorNotify_NotifyEvent(This,EventCode,Param1,Param2) (This)->lpVtbl->NotifyEvent(This,EventCode,Param1,Param2)")
cpp_quote("#define IVMRSurfaceAllocatorNotify_SetBorderColor(This,clrBorder) (This)->lpVtbl->SetBorderColor(This,clrBorder)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRSurfaceAllocatorNotify_AdviseSurfaceAllocator_Proxy(IVMRSurfaceAllocatorNotify *This,DWORD_PTR dwUserID,IVMRSurfaceAllocator *lpIVRMSurfaceAllocator);")
cpp_quote("  void __RPC_STUB IVMRSurfaceAllocatorNotify_AdviseSurfaceAllocator_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRSurfaceAllocatorNotify_SetDDrawDevice_Proxy(IVMRSurfaceAllocatorNotify *This,LPDIRECTDRAW7 lpDDrawDevice,HMONITOR hMonitor);")
cpp_quote("  void __RPC_STUB IVMRSurfaceAllocatorNotify_SetDDrawDevice_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRSurfaceAllocatorNotify_ChangeDDrawDevice_Proxy(IVMRSurfaceAllocatorNotify *This,LPDIRECTDRAW7 lpDDrawDevice,HMONITOR hMonitor);")
cpp_quote("  void __RPC_STUB IVMRSurfaceAllocatorNotify_ChangeDDrawDevice_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRSurfaceAllocatorNotify_RestoreDDrawSurfaces_Proxy(IVMRSurfaceAllocatorNotify *This);")
cpp_quote("  void __RPC_STUB IVMRSurfaceAllocatorNotify_RestoreDDrawSurfaces_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRSurfaceAllocatorNotify_NotifyEvent_Proxy(IVMRSurfaceAllocatorNotify *This,LONG EventCode,LONG_PTR Param1,LONG_PTR Param2);")
cpp_quote("  void __RPC_STUB IVMRSurfaceAllocatorNotify_NotifyEvent_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRSurfaceAllocatorNotify_SetBorderColor_Proxy(IVMRSurfaceAllocatorNotify *This,COLORREF clrBorder);")
cpp_quote("  void __RPC_STUB IVMRSurfaceAllocatorNotify_SetBorderColor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0400_0001 {")
cpp_quote("    VMR_ARMODE_NONE = 0,VMR_ARMODE_LETTER_BOX = VMR_ARMODE_NONE + 1")
cpp_quote("  } VMR_ASPECT_RATIO_MODE;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0400_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0400_v0_0_s_ifspec;")
cpp_quote("#ifndef __IVMRWindowlessControl_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRWindowlessControl_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRWindowlessControl;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRWindowlessControl : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetNativeVideoSize(LONG *lpWidth,LONG *lpHeight,LONG *lpARWidth,LONG *lpARHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetMinIdealVideoSize(LONG *lpWidth,LONG *lpHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetMaxIdealVideoSize(LONG *lpWidth,LONG *lpHeight) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetVideoPosition(const LPRECT lpSRCRect,const LPRECT lpDSTRect) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetVideoPosition(LPRECT lpSRCRect,LPRECT lpDSTRect) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetAspectRatioMode(DWORD *lpAspectRatioMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetAspectRatioMode(DWORD AspectRatioMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetVideoClippingWindow(HWND hwnd) = 0;")
cpp_quote("    virtual HRESULT WINAPI RepaintVideo(HWND hwnd,HDC hdc) = 0;")
cpp_quote("    virtual HRESULT WINAPI DisplayModeChanged(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetCurrentImage(BYTE **lpDib) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetBorderColor(COLORREF Clr) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetBorderColor(COLORREF *lpClr) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetColorKey(COLORREF Clr) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetColorKey(COLORREF *lpClr) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRWindowlessControlVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRWindowlessControl *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRWindowlessControl *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRWindowlessControl *This);")
cpp_quote("      HRESULT (WINAPI *GetNativeVideoSize)(IVMRWindowlessControl *This,LONG *lpWidth,LONG *lpHeight,LONG *lpARWidth,LONG *lpARHeight);")
cpp_quote("      HRESULT (WINAPI *GetMinIdealVideoSize)(IVMRWindowlessControl *This,LONG *lpWidth,LONG *lpHeight);")
cpp_quote("      HRESULT (WINAPI *GetMaxIdealVideoSize)(IVMRWindowlessControl *This,LONG *lpWidth,LONG *lpHeight);")
cpp_quote("      HRESULT (WINAPI *SetVideoPosition)(IVMRWindowlessControl *This,const LPRECT lpSRCRect,const LPRECT lpDSTRect);")
cpp_quote("      HRESULT (WINAPI *GetVideoPosition)(IVMRWindowlessControl *This,LPRECT lpSRCRect,LPRECT lpDSTRect);")
cpp_quote("      HRESULT (WINAPI *GetAspectRatioMode)(IVMRWindowlessControl *This,DWORD *lpAspectRatioMode);")
cpp_quote("      HRESULT (WINAPI *SetAspectRatioMode)(IVMRWindowlessControl *This,DWORD AspectRatioMode);")
cpp_quote("      HRESULT (WINAPI *SetVideoClippingWindow)(IVMRWindowlessControl *This,HWND hwnd);")
cpp_quote("      HRESULT (WINAPI *RepaintVideo)(IVMRWindowlessControl *This,HWND hwnd,HDC hdc);")
cpp_quote("      HRESULT (WINAPI *DisplayModeChanged)(IVMRWindowlessControl *This);")
cpp_quote("      HRESULT (WINAPI *GetCurrentImage)(IVMRWindowlessControl *This,BYTE **lpDib);")
cpp_quote("      HRESULT (WINAPI *SetBorderColor)(IVMRWindowlessControl *This,COLORREF Clr);")
cpp_quote("      HRESULT (WINAPI *GetBorderColor)(IVMRWindowlessControl *This,COLORREF *lpClr);")
cpp_quote("      HRESULT (WINAPI *SetColorKey)(IVMRWindowlessControl *This,COLORREF Clr);")
cpp_quote("      HRESULT (WINAPI *GetColorKey)(IVMRWindowlessControl *This,COLORREF *lpClr);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRWindowlessControlVtbl;")
cpp_quote("  struct IVMRWindowlessControl {")
cpp_quote("    CONST_VTBL struct IVMRWindowlessControlVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRWindowlessControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRWindowlessControl_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRWindowlessControl_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRWindowlessControl_GetNativeVideoSize(This,lpWidth,lpHeight,lpARWidth,lpARHeight) (This)->lpVtbl->GetNativeVideoSize(This,lpWidth,lpHeight,lpARWidth,lpARHeight)")
cpp_quote("#define IVMRWindowlessControl_GetMinIdealVideoSize(This,lpWidth,lpHeight) (This)->lpVtbl->GetMinIdealVideoSize(This,lpWidth,lpHeight)")
cpp_quote("#define IVMRWindowlessControl_GetMaxIdealVideoSize(This,lpWidth,lpHeight) (This)->lpVtbl->GetMaxIdealVideoSize(This,lpWidth,lpHeight)")
cpp_quote("#define IVMRWindowlessControl_SetVideoPosition(This,lpSRCRect,lpDSTRect) (This)->lpVtbl->SetVideoPosition(This,lpSRCRect,lpDSTRect)")
cpp_quote("#define IVMRWindowlessControl_GetVideoPosition(This,lpSRCRect,lpDSTRect) (This)->lpVtbl->GetVideoPosition(This,lpSRCRect,lpDSTRect)")
cpp_quote("#define IVMRWindowlessControl_GetAspectRatioMode(This,lpAspectRatioMode) (This)->lpVtbl->GetAspectRatioMode(This,lpAspectRatioMode)")
cpp_quote("#define IVMRWindowlessControl_SetAspectRatioMode(This,AspectRatioMode) (This)->lpVtbl->SetAspectRatioMode(This,AspectRatioMode)")
cpp_quote("#define IVMRWindowlessControl_SetVideoClippingWindow(This,hwnd) (This)->lpVtbl->SetVideoClippingWindow(This,hwnd)")
cpp_quote("#define IVMRWindowlessControl_RepaintVideo(This,hwnd,hdc) (This)->lpVtbl->RepaintVideo(This,hwnd,hdc)")
cpp_quote("#define IVMRWindowlessControl_DisplayModeChanged(This) (This)->lpVtbl->DisplayModeChanged(This)")
cpp_quote("#define IVMRWindowlessControl_GetCurrentImage(This,lpDib) (This)->lpVtbl->GetCurrentImage(This,lpDib)")
cpp_quote("#define IVMRWindowlessControl_SetBorderColor(This,Clr) (This)->lpVtbl->SetBorderColor(This,Clr)")
cpp_quote("#define IVMRWindowlessControl_GetBorderColor(This,lpClr) (This)->lpVtbl->GetBorderColor(This,lpClr)")
cpp_quote("#define IVMRWindowlessControl_SetColorKey(This,Clr) (This)->lpVtbl->SetColorKey(This,Clr)")
cpp_quote("#define IVMRWindowlessControl_GetColorKey(This,lpClr) (This)->lpVtbl->GetColorKey(This,lpClr)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_GetNativeVideoSize_Proxy(IVMRWindowlessControl *This,LONG *lpWidth,LONG *lpHeight,LONG *lpARWidth,LONG *lpARHeight);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_GetNativeVideoSize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_GetMinIdealVideoSize_Proxy(IVMRWindowlessControl *This,LONG *lpWidth,LONG *lpHeight);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_GetMinIdealVideoSize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_GetMaxIdealVideoSize_Proxy(IVMRWindowlessControl *This,LONG *lpWidth,LONG *lpHeight);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_GetMaxIdealVideoSize_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_SetVideoPosition_Proxy(IVMRWindowlessControl *This,const LPRECT lpSRCRect,const LPRECT lpDSTRect);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_SetVideoPosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_GetVideoPosition_Proxy(IVMRWindowlessControl *This,LPRECT lpSRCRect,LPRECT lpDSTRect);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_GetVideoPosition_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_GetAspectRatioMode_Proxy(IVMRWindowlessControl *This,DWORD *lpAspectRatioMode);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_GetAspectRatioMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_SetAspectRatioMode_Proxy(IVMRWindowlessControl *This,DWORD AspectRatioMode);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_SetAspectRatioMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_SetVideoClippingWindow_Proxy(IVMRWindowlessControl *This,HWND hwnd);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_SetVideoClippingWindow_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_RepaintVideo_Proxy(IVMRWindowlessControl *This,HWND hwnd,HDC hdc);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_RepaintVideo_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_DisplayModeChanged_Proxy(IVMRWindowlessControl *This);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_DisplayModeChanged_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_GetCurrentImage_Proxy(IVMRWindowlessControl *This,BYTE **lpDib);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_GetCurrentImage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_SetBorderColor_Proxy(IVMRWindowlessControl *This,COLORREF Clr);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_SetBorderColor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_GetBorderColor_Proxy(IVMRWindowlessControl *This,COLORREF *lpClr);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_GetBorderColor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_SetColorKey_Proxy(IVMRWindowlessControl *This,COLORREF Clr);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_SetColorKey_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRWindowlessControl_GetColorKey_Proxy(IVMRWindowlessControl *This,COLORREF *lpClr);")
cpp_quote("  void __RPC_STUB IVMRWindowlessControl_GetColorKey_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0401_0001 {")
cpp_quote("    MixerPref_NoDecimation = 0x1,MixerPref_DecimateOutput = 0x2,MixerPref_ARAdjustXorY = 0x4,MixerPref_DecimationReserved = 0x8,")
cpp_quote("    MixerPref_DecimateMask = 0xf,MixerPref_BiLinearFiltering = 0x10,MixerPref_PointFiltering = 0x20,MixerPref_FilteringMask = 0xf0,")
cpp_quote("    MixerPref_RenderTargetRGB = 0x100,MixerPref_RenderTargetYUV = 0x1000,MixerPref_RenderTargetYUV420 = 0x200,MixerPref_RenderTargetYUV422 = 0x400,")
cpp_quote("    MixerPref_RenderTargetYUV444 = 0x800,MixerPref_RenderTargetReserved = 0xe000,MixerPref_RenderTargetMask = 0xff00,")
cpp_quote("    MixerPref_DynamicSwitchToBOB = 0x10000,MixerPref_DynamicDecimateBy2 = 0x20000,MixerPref_DynamicReserved = 0xc0000,")
cpp_quote("    MixerPref_DynamicMask = 0xf0000")
cpp_quote("  } VMRMixerPrefs;")
cpp_quote("")
cpp_quote("  typedef struct _NORMALIZEDRECT {")
cpp_quote("    float left;")
cpp_quote("    float top;")
cpp_quote("    float right;")
cpp_quote("    float bottom;")
cpp_quote("  } NORMALIZEDRECT;")
cpp_quote("")
cpp_quote("  typedef struct _NORMALIZEDRECT *PNORMALIZEDRECT;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0401_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0401_v0_0_s_ifspec;")
cpp_quote("#ifndef __IVMRMixerControl_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRMixerControl_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRMixerControl;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRMixerControl : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI SetAlpha(DWORD dwStreamID,float Alpha) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetAlpha(DWORD dwStreamID,float *pAlpha) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetZOrder(DWORD dwStreamID,DWORD dwZ) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetZOrder(DWORD dwStreamID,DWORD *pZ) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetOutputRect(DWORD dwStreamID,const NORMALIZEDRECT *pRect) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetOutputRect(DWORD dwStreamID,NORMALIZEDRECT *pRect) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetBackgroundClr(COLORREF ClrBkg) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetBackgroundClr(COLORREF *lpClrBkg) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetMixingPrefs(DWORD dwMixerPrefs) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetMixingPrefs(DWORD *pdwMixerPrefs) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRMixerControlVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRMixerControl *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRMixerControl *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRMixerControl *This);")
cpp_quote("      HRESULT (WINAPI *SetAlpha)(IVMRMixerControl *This,DWORD dwStreamID,float Alpha);")
cpp_quote("      HRESULT (WINAPI *GetAlpha)(IVMRMixerControl *This,DWORD dwStreamID,float *pAlpha);")
cpp_quote("      HRESULT (WINAPI *SetZOrder)(IVMRMixerControl *This,DWORD dwStreamID,DWORD dwZ);")
cpp_quote("      HRESULT (WINAPI *GetZOrder)(IVMRMixerControl *This,DWORD dwStreamID,DWORD *pZ);")
cpp_quote("      HRESULT (WINAPI *SetOutputRect)(IVMRMixerControl *This,DWORD dwStreamID,const NORMALIZEDRECT *pRect);")
cpp_quote("      HRESULT (WINAPI *GetOutputRect)(IVMRMixerControl *This,DWORD dwStreamID,NORMALIZEDRECT *pRect);")
cpp_quote("      HRESULT (WINAPI *SetBackgroundClr)(IVMRMixerControl *This,COLORREF ClrBkg);")
cpp_quote("      HRESULT (WINAPI *GetBackgroundClr)(IVMRMixerControl *This,COLORREF *lpClrBkg);")
cpp_quote("      HRESULT (WINAPI *SetMixingPrefs)(IVMRMixerControl *This,DWORD dwMixerPrefs);")
cpp_quote("      HRESULT (WINAPI *GetMixingPrefs)(IVMRMixerControl *This,DWORD *pdwMixerPrefs);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRMixerControlVtbl;")
cpp_quote("  struct IVMRMixerControl {")
cpp_quote("    CONST_VTBL struct IVMRMixerControlVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRMixerControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRMixerControl_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRMixerControl_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRMixerControl_SetAlpha(This,dwStreamID,Alpha) (This)->lpVtbl->SetAlpha(This,dwStreamID,Alpha)")
cpp_quote("#define IVMRMixerControl_GetAlpha(This,dwStreamID,pAlpha) (This)->lpVtbl->GetAlpha(This,dwStreamID,pAlpha)")
cpp_quote("#define IVMRMixerControl_SetZOrder(This,dwStreamID,dwZ) (This)->lpVtbl->SetZOrder(This,dwStreamID,dwZ)")
cpp_quote("#define IVMRMixerControl_GetZOrder(This,dwStreamID,pZ) (This)->lpVtbl->GetZOrder(This,dwStreamID,pZ)")
cpp_quote("#define IVMRMixerControl_SetOutputRect(This,dwStreamID,pRect) (This)->lpVtbl->SetOutputRect(This,dwStreamID,pRect)")
cpp_quote("#define IVMRMixerControl_GetOutputRect(This,dwStreamID,pRect) (This)->lpVtbl->GetOutputRect(This,dwStreamID,pRect)")
cpp_quote("#define IVMRMixerControl_SetBackgroundClr(This,ClrBkg) (This)->lpVtbl->SetBackgroundClr(This,ClrBkg)")
cpp_quote("#define IVMRMixerControl_GetBackgroundClr(This,lpClrBkg) (This)->lpVtbl->GetBackgroundClr(This,lpClrBkg)")
cpp_quote("#define IVMRMixerControl_SetMixingPrefs(This,dwMixerPrefs) (This)->lpVtbl->SetMixingPrefs(This,dwMixerPrefs)")
cpp_quote("#define IVMRMixerControl_GetMixingPrefs(This,pdwMixerPrefs) (This)->lpVtbl->GetMixingPrefs(This,pdwMixerPrefs)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRMixerControl_SetAlpha_Proxy(IVMRMixerControl *This,DWORD dwStreamID,float Alpha);")
cpp_quote("  void __RPC_STUB IVMRMixerControl_SetAlpha_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMixerControl_GetAlpha_Proxy(IVMRMixerControl *This,DWORD dwStreamID,float *pAlpha);")
cpp_quote("  void __RPC_STUB IVMRMixerControl_GetAlpha_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMixerControl_SetZOrder_Proxy(IVMRMixerControl *This,DWORD dwStreamID,DWORD dwZ);")
cpp_quote("  void __RPC_STUB IVMRMixerControl_SetZOrder_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMixerControl_GetZOrder_Proxy(IVMRMixerControl *This,DWORD dwStreamID,DWORD *pZ);")
cpp_quote("  void __RPC_STUB IVMRMixerControl_GetZOrder_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMixerControl_SetOutputRect_Proxy(IVMRMixerControl *This,DWORD dwStreamID,const NORMALIZEDRECT *pRect);")
cpp_quote("  void __RPC_STUB IVMRMixerControl_SetOutputRect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMixerControl_GetOutputRect_Proxy(IVMRMixerControl *This,DWORD dwStreamID,NORMALIZEDRECT *pRect);")
cpp_quote("  void __RPC_STUB IVMRMixerControl_GetOutputRect_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMixerControl_SetBackgroundClr_Proxy(IVMRMixerControl *This,COLORREF ClrBkg);")
cpp_quote("  void __RPC_STUB IVMRMixerControl_SetBackgroundClr_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMixerControl_GetBackgroundClr_Proxy(IVMRMixerControl *This,COLORREF *lpClrBkg);")
cpp_quote("  void __RPC_STUB IVMRMixerControl_GetBackgroundClr_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMixerControl_SetMixingPrefs_Proxy(IVMRMixerControl *This,DWORD dwMixerPrefs);")
cpp_quote("  void __RPC_STUB IVMRMixerControl_SetMixingPrefs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMixerControl_GetMixingPrefs_Proxy(IVMRMixerControl *This,DWORD *pdwMixerPrefs);")
cpp_quote("")
cpp_quote("  void __RPC_STUB IVMRMixerControl_GetMixingPrefs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifdef __cplusplus")
cpp_quote("  typedef struct tagVMRGUID {")
cpp_quote("    ::GUID *pGUID;")
cpp_quote("    ::GUID GUID;")
cpp_quote("  } VMRGUID;")
cpp_quote("#else")
cpp_quote("  typedef struct tagVMRGUID {")
cpp_quote("    GUID *pGUID;")
cpp_quote("    GUID GUID;")
cpp_quote("  } VMRGUID;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef struct tagVMRMONITORINFO {")
cpp_quote("    VMRGUID guid;")
cpp_quote("    RECT rcMonitor;")
cpp_quote("    HMONITOR hMon;")
cpp_quote("    DWORD dwFlags;")
cpp_quote("    wchar_t szDevice[32];")
cpp_quote("    wchar_t szDescription[256];")
cpp_quote("    LARGE_INTEGER liDriverVersion;")
cpp_quote("    DWORD dwVendorId;")
cpp_quote("    DWORD dwDeviceId;")
cpp_quote("    DWORD dwSubSysId;")
cpp_quote("    DWORD dwRevision;")
cpp_quote("  } VMRMONITORINFO;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0402_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0402_v0_0_s_ifspec;")
cpp_quote("#ifndef __IVMRMonitorConfig_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRMonitorConfig_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRMonitorConfig;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRMonitorConfig : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI SetMonitor(const VMRGUID *pGUID) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetMonitor(VMRGUID *pGUID) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetDefaultMonitor(const VMRGUID *pGUID) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDefaultMonitor(VMRGUID *pGUID) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetAvailableMonitors(VMRMONITORINFO *pInfo,DWORD dwMaxInfoArraySize,DWORD *pdwNumDevices) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRMonitorConfigVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRMonitorConfig *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRMonitorConfig *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRMonitorConfig *This);")
cpp_quote("      HRESULT (WINAPI *SetMonitor)(IVMRMonitorConfig *This,const VMRGUID *pGUID);")
cpp_quote("      HRESULT (WINAPI *GetMonitor)(IVMRMonitorConfig *This,VMRGUID *pGUID);")
cpp_quote("      HRESULT (WINAPI *SetDefaultMonitor)(IVMRMonitorConfig *This,const VMRGUID *pGUID);")
cpp_quote("      HRESULT (WINAPI *GetDefaultMonitor)(IVMRMonitorConfig *This,VMRGUID *pGUID);")
cpp_quote("      HRESULT (WINAPI *GetAvailableMonitors)(IVMRMonitorConfig *This,VMRMONITORINFO *pInfo,DWORD dwMaxInfoArraySize,DWORD *pdwNumDevices);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRMonitorConfigVtbl;")
cpp_quote("  struct IVMRMonitorConfig {")
cpp_quote("    CONST_VTBL struct IVMRMonitorConfigVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRMonitorConfig_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRMonitorConfig_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRMonitorConfig_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRMonitorConfig_SetMonitor(This,pGUID) (This)->lpVtbl->SetMonitor(This,pGUID)")
cpp_quote("#define IVMRMonitorConfig_GetMonitor(This,pGUID) (This)->lpVtbl->GetMonitor(This,pGUID)")
cpp_quote("#define IVMRMonitorConfig_SetDefaultMonitor(This,pGUID) (This)->lpVtbl->SetDefaultMonitor(This,pGUID)")
cpp_quote("#define IVMRMonitorConfig_GetDefaultMonitor(This,pGUID) (This)->lpVtbl->GetDefaultMonitor(This,pGUID)")
cpp_quote("#define IVMRMonitorConfig_GetAvailableMonitors(This,pInfo,dwMaxInfoArraySize,pdwNumDevices) (This)->lpVtbl->GetAvailableMonitors(This,pInfo,dwMaxInfoArraySize,pdwNumDevices)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRMonitorConfig_SetMonitor_Proxy(IVMRMonitorConfig *This,const VMRGUID *pGUID);")
cpp_quote("  void __RPC_STUB IVMRMonitorConfig_SetMonitor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMonitorConfig_GetMonitor_Proxy(IVMRMonitorConfig *This,VMRGUID *pGUID);")
cpp_quote("  void __RPC_STUB IVMRMonitorConfig_GetMonitor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMonitorConfig_SetDefaultMonitor_Proxy(IVMRMonitorConfig *This,const VMRGUID *pGUID);")
cpp_quote("  void __RPC_STUB IVMRMonitorConfig_SetDefaultMonitor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMonitorConfig_GetDefaultMonitor_Proxy(IVMRMonitorConfig *This,VMRGUID *pGUID);")
cpp_quote("  void __RPC_STUB IVMRMonitorConfig_GetDefaultMonitor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMonitorConfig_GetAvailableMonitors_Proxy(IVMRMonitorConfig *This,VMRMONITORINFO *pInfo,DWORD dwMaxInfoArraySize,DWORD *pdwNumDevices);")
cpp_quote("  void __RPC_STUB IVMRMonitorConfig_GetAvailableMonitors_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0403_0001 {")
cpp_quote("    RenderPrefs_RestrictToInitialMonitor = 0,RenderPrefs_ForceOffscreen = 0x1,RenderPrefs_ForceOverlays = 0x2,RenderPrefs_AllowOverlays = 0,")
cpp_quote("    RenderPrefs_AllowOffscreen = 0,RenderPrefs_DoNotRenderColorKeyAndBorder = 0x8,RenderPrefs_Reserved = 0x10,RenderPrefs_PreferAGPMemWhenMixing = 0x20,")
cpp_quote("    RenderPrefs_Mask = 0x3f")
cpp_quote("  } VMRRenderPrefs;")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0403_0002 {")
cpp_quote("    VMRMode_Windowed = 0x1,VMRMode_Windowless = 0x2,VMRMode_Renderless = 0x4,VMRMode_Mask = 0x7")
cpp_quote("  } VMRMode;")
cpp_quote("")
cpp_quote("  enum __MIDL___MIDL_itf_strmif_0403_0003 {")
cpp_quote("    MAX_NUMBER_OF_STREAMS = 16")
cpp_quote("  };")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0403_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0403_v0_0_s_ifspec;")
cpp_quote("#ifndef __IVMRFilterConfig_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRFilterConfig_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRFilterConfig;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRFilterConfig : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI SetImageCompositor(IVMRImageCompositor *lpVMRImgCompositor) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetNumberOfStreams(DWORD dwMaxStreams) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetNumberOfStreams(DWORD *pdwMaxStreams) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetRenderingPrefs(DWORD dwRenderFlags) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetRenderingPrefs(DWORD *pdwRenderFlags) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetRenderingMode(DWORD Mode) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetRenderingMode(DWORD *pMode) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRFilterConfigVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRFilterConfig *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRFilterConfig *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRFilterConfig *This);")
cpp_quote("      HRESULT (WINAPI *SetImageCompositor)(IVMRFilterConfig *This,IVMRImageCompositor *lpVMRImgCompositor);")
cpp_quote("      HRESULT (WINAPI *SetNumberOfStreams)(IVMRFilterConfig *This,DWORD dwMaxStreams);")
cpp_quote("      HRESULT (WINAPI *GetNumberOfStreams)(IVMRFilterConfig *This,DWORD *pdwMaxStreams);")
cpp_quote("      HRESULT (WINAPI *SetRenderingPrefs)(IVMRFilterConfig *This,DWORD dwRenderFlags);")
cpp_quote("      HRESULT (WINAPI *GetRenderingPrefs)(IVMRFilterConfig *This,DWORD *pdwRenderFlags);")
cpp_quote("      HRESULT (WINAPI *SetRenderingMode)(IVMRFilterConfig *This,DWORD Mode);")
cpp_quote("      HRESULT (WINAPI *GetRenderingMode)(IVMRFilterConfig *This,DWORD *pMode);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRFilterConfigVtbl;")
cpp_quote("  struct IVMRFilterConfig {")
cpp_quote("    CONST_VTBL struct IVMRFilterConfigVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRFilterConfig_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRFilterConfig_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRFilterConfig_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRFilterConfig_SetImageCompositor(This,lpVMRImgCompositor) (This)->lpVtbl->SetImageCompositor(This,lpVMRImgCompositor)")
cpp_quote("#define IVMRFilterConfig_SetNumberOfStreams(This,dwMaxStreams) (This)->lpVtbl->SetNumberOfStreams(This,dwMaxStreams)")
cpp_quote("#define IVMRFilterConfig_GetNumberOfStreams(This,pdwMaxStreams) (This)->lpVtbl->GetNumberOfStreams(This,pdwMaxStreams)")
cpp_quote("#define IVMRFilterConfig_SetRenderingPrefs(This,dwRenderFlags) (This)->lpVtbl->SetRenderingPrefs(This,dwRenderFlags)")
cpp_quote("#define IVMRFilterConfig_GetRenderingPrefs(This,pdwRenderFlags) (This)->lpVtbl->GetRenderingPrefs(This,pdwRenderFlags)")
cpp_quote("#define IVMRFilterConfig_SetRenderingMode(This,Mode) (This)->lpVtbl->SetRenderingMode(This,Mode)")
cpp_quote("#define IVMRFilterConfig_GetRenderingMode(This,pMode) (This)->lpVtbl->GetRenderingMode(This,pMode)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRFilterConfig_SetImageCompositor_Proxy(IVMRFilterConfig *This,IVMRImageCompositor *lpVMRImgCompositor);")
cpp_quote("  void __RPC_STUB IVMRFilterConfig_SetImageCompositor_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRFilterConfig_SetNumberOfStreams_Proxy(IVMRFilterConfig *This,DWORD dwMaxStreams);")
cpp_quote("  void __RPC_STUB IVMRFilterConfig_SetNumberOfStreams_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRFilterConfig_GetNumberOfStreams_Proxy(IVMRFilterConfig *This,DWORD *pdwMaxStreams);")
cpp_quote("  void __RPC_STUB IVMRFilterConfig_GetNumberOfStreams_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRFilterConfig_SetRenderingPrefs_Proxy(IVMRFilterConfig *This,DWORD dwRenderFlags);")
cpp_quote("  void __RPC_STUB IVMRFilterConfig_SetRenderingPrefs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRFilterConfig_GetRenderingPrefs_Proxy(IVMRFilterConfig *This,DWORD *pdwRenderFlags);")
cpp_quote("  void __RPC_STUB IVMRFilterConfig_GetRenderingPrefs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRFilterConfig_SetRenderingMode_Proxy(IVMRFilterConfig *This,DWORD Mode);")
cpp_quote("  void __RPC_STUB IVMRFilterConfig_SetRenderingMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRFilterConfig_GetRenderingMode_Proxy(IVMRFilterConfig *This,DWORD *pMode);")
cpp_quote("  void __RPC_STUB IVMRFilterConfig_GetRenderingMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRAspectRatioControl_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRAspectRatioControl_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRAspectRatioControl;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRAspectRatioControl : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetAspectRatioMode(LPDWORD lpdwARMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetAspectRatioMode(DWORD dwARMode) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRAspectRatioControlVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRAspectRatioControl *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRAspectRatioControl *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRAspectRatioControl *This);")
cpp_quote("      HRESULT (WINAPI *GetAspectRatioMode)(IVMRAspectRatioControl *This,LPDWORD lpdwARMode);")
cpp_quote("      HRESULT (WINAPI *SetAspectRatioMode)(IVMRAspectRatioControl *This,DWORD dwARMode);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRAspectRatioControlVtbl;")
cpp_quote("  struct IVMRAspectRatioControl {")
cpp_quote("    CONST_VTBL struct IVMRAspectRatioControlVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRAspectRatioControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRAspectRatioControl_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRAspectRatioControl_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRAspectRatioControl_GetAspectRatioMode(This,lpdwARMode) (This)->lpVtbl->GetAspectRatioMode(This,lpdwARMode)")
cpp_quote("#define IVMRAspectRatioControl_SetAspectRatioMode(This,dwARMode) (This)->lpVtbl->SetAspectRatioMode(This,dwARMode)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRAspectRatioControl_GetAspectRatioMode_Proxy(IVMRAspectRatioControl *This,LPDWORD lpdwARMode);")
cpp_quote("  void __RPC_STUB IVMRAspectRatioControl_GetAspectRatioMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRAspectRatioControl_SetAspectRatioMode_Proxy(IVMRAspectRatioControl *This,DWORD dwARMode);")
cpp_quote("  void __RPC_STUB IVMRAspectRatioControl_SetAspectRatioMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0405_0001 {")
cpp_quote("    DeinterlacePref_NextBest = 0x1,DeinterlacePref_BOB = 0x2,DeinterlacePref_Weave = 0x4,DeinterlacePref_Mask = 0x7")
cpp_quote("  } VMRDeinterlacePrefs;")
cpp_quote("")
cpp_quote("  typedef enum __MIDL___MIDL_itf_strmif_0405_0002 {")
cpp_quote("    DeinterlaceTech_Unknown = 0,DeinterlaceTech_BOBLineReplicate = 0x1,DeinterlaceTech_BOBVerticalStretch = 0x2,DeinterlaceTech_MedianFiltering = 0x4,")
cpp_quote("    DeinterlaceTech_EdgeFiltering = 0x10,DeinterlaceTech_FieldAdaptive = 0x20,DeinterlaceTech_PixelAdaptive = 0x40,")
cpp_quote("    DeinterlaceTech_MotionVectorSteered = 0x80")
cpp_quote("  } VMRDeinterlaceTech;")
cpp_quote("")
cpp_quote("  typedef struct _VMRFrequency {")
cpp_quote("    DWORD dwNumerator;")
cpp_quote("    DWORD dwDenominator;")
cpp_quote("  } VMRFrequency;")
cpp_quote("")
cpp_quote("  typedef struct _VMRVideoDesc {")
cpp_quote("    DWORD dwSize;")
cpp_quote("    DWORD dwSampleWidth;")
cpp_quote("    DWORD dwSampleHeight;")
cpp_quote("    WINBOOL SingleFieldPerSample;")
cpp_quote("    DWORD dwFourCC;")
cpp_quote("    VMRFrequency InputSampleFreq;")
cpp_quote("    VMRFrequency OutputFrameFreq;")
cpp_quote("  } VMRVideoDesc;")
cpp_quote("")
cpp_quote("  typedef struct _VMRDeinterlaceCaps {")
cpp_quote("    DWORD dwSize;")
cpp_quote("    DWORD dwNumPreviousOutputFrames;")
cpp_quote("    DWORD dwNumForwardRefSamples;")
cpp_quote("    DWORD dwNumBackwardRefSamples;")
cpp_quote("    VMRDeinterlaceTech DeinterlaceTechnology;")
cpp_quote("  } VMRDeinterlaceCaps;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0405_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0405_v0_0_s_ifspec;")
cpp_quote("#ifndef __IVMRDeinterlaceControl_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRDeinterlaceControl_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRDeinterlaceControl;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRDeinterlaceControl : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI GetNumberOfDeinterlaceModes(VMRVideoDesc *lpVideoDescription,LPDWORD lpdwNumDeinterlaceModes,LPGUID lpDeinterlaceModes) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDeinterlaceModeCaps(LPGUID lpDeinterlaceMode,VMRVideoDesc *lpVideoDescription,VMRDeinterlaceCaps *lpDeinterlaceCaps) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDeinterlaceMode(DWORD dwStreamID,LPGUID lpDeinterlaceMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetDeinterlaceMode(DWORD dwStreamID,LPGUID lpDeinterlaceMode) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetDeinterlacePrefs(LPDWORD lpdwDeinterlacePrefs) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetDeinterlacePrefs(DWORD dwDeinterlacePrefs) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetActualDeinterlaceMode(DWORD dwStreamID,LPGUID lpDeinterlaceMode) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRDeinterlaceControlVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRDeinterlaceControl *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRDeinterlaceControl *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRDeinterlaceControl *This);")
cpp_quote("      HRESULT (WINAPI *GetNumberOfDeinterlaceModes)(IVMRDeinterlaceControl *This,VMRVideoDesc *lpVideoDescription,LPDWORD lpdwNumDeinterlaceModes,LPGUID lpDeinterlaceModes);")
cpp_quote("      HRESULT (WINAPI *GetDeinterlaceModeCaps)(IVMRDeinterlaceControl *This,LPGUID lpDeinterlaceMode,VMRVideoDesc *lpVideoDescription,VMRDeinterlaceCaps *lpDeinterlaceCaps);")
cpp_quote("      HRESULT (WINAPI *GetDeinterlaceMode)(IVMRDeinterlaceControl *This,DWORD dwStreamID,LPGUID lpDeinterlaceMode);")
cpp_quote("      HRESULT (WINAPI *SetDeinterlaceMode)(IVMRDeinterlaceControl *This,DWORD dwStreamID,LPGUID lpDeinterlaceMode);")
cpp_quote("      HRESULT (WINAPI *GetDeinterlacePrefs)(IVMRDeinterlaceControl *This,LPDWORD lpdwDeinterlacePrefs);")
cpp_quote("      HRESULT (WINAPI *SetDeinterlacePrefs)(IVMRDeinterlaceControl *This,DWORD dwDeinterlacePrefs);")
cpp_quote("      HRESULT (WINAPI *GetActualDeinterlaceMode)(IVMRDeinterlaceControl *This,DWORD dwStreamID,LPGUID lpDeinterlaceMode);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRDeinterlaceControlVtbl;")
cpp_quote("  struct IVMRDeinterlaceControl {")
cpp_quote("    CONST_VTBL struct IVMRDeinterlaceControlVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRDeinterlaceControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRDeinterlaceControl_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRDeinterlaceControl_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRDeinterlaceControl_GetNumberOfDeinterlaceModes(This,lpVideoDescription,lpdwNumDeinterlaceModes,lpDeinterlaceModes) (This)->lpVtbl->GetNumberOfDeinterlaceModes(This,lpVideoDescription,lpdwNumDeinterlaceModes,lpDeinterlaceModes)")
cpp_quote("#define IVMRDeinterlaceControl_GetDeinterlaceModeCaps(This,lpDeinterlaceMode,lpVideoDescription,lpDeinterlaceCaps) (This)->lpVtbl->GetDeinterlaceModeCaps(This,lpDeinterlaceMode,lpVideoDescription,lpDeinterlaceCaps)")
cpp_quote("#define IVMRDeinterlaceControl_GetDeinterlaceMode(This,dwStreamID,lpDeinterlaceMode) (This)->lpVtbl->GetDeinterlaceMode(This,dwStreamID,lpDeinterlaceMode)")
cpp_quote("#define IVMRDeinterlaceControl_SetDeinterlaceMode(This,dwStreamID,lpDeinterlaceMode) (This)->lpVtbl->SetDeinterlaceMode(This,dwStreamID,lpDeinterlaceMode)")
cpp_quote("#define IVMRDeinterlaceControl_GetDeinterlacePrefs(This,lpdwDeinterlacePrefs) (This)->lpVtbl->GetDeinterlacePrefs(This,lpdwDeinterlacePrefs)")
cpp_quote("#define IVMRDeinterlaceControl_SetDeinterlacePrefs(This,dwDeinterlacePrefs) (This)->lpVtbl->SetDeinterlacePrefs(This,dwDeinterlacePrefs)")
cpp_quote("#define IVMRDeinterlaceControl_GetActualDeinterlaceMode(This,dwStreamID,lpDeinterlaceMode) (This)->lpVtbl->GetActualDeinterlaceMode(This,dwStreamID,lpDeinterlaceMode)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRDeinterlaceControl_GetNumberOfDeinterlaceModes_Proxy(IVMRDeinterlaceControl *This,VMRVideoDesc *lpVideoDescription,LPDWORD lpdwNumDeinterlaceModes,LPGUID lpDeinterlaceModes);")
cpp_quote("  void __RPC_STUB IVMRDeinterlaceControl_GetNumberOfDeinterlaceModes_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRDeinterlaceControl_GetDeinterlaceModeCaps_Proxy(IVMRDeinterlaceControl *This,LPGUID lpDeinterlaceMode,VMRVideoDesc *lpVideoDescription,VMRDeinterlaceCaps *lpDeinterlaceCaps);")
cpp_quote("  void __RPC_STUB IVMRDeinterlaceControl_GetDeinterlaceModeCaps_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRDeinterlaceControl_GetDeinterlaceMode_Proxy(IVMRDeinterlaceControl *This,DWORD dwStreamID,LPGUID lpDeinterlaceMode);")
cpp_quote("  void __RPC_STUB IVMRDeinterlaceControl_GetDeinterlaceMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRDeinterlaceControl_SetDeinterlaceMode_Proxy(IVMRDeinterlaceControl *This,DWORD dwStreamID,LPGUID lpDeinterlaceMode);")
cpp_quote("  void __RPC_STUB IVMRDeinterlaceControl_SetDeinterlaceMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRDeinterlaceControl_GetDeinterlacePrefs_Proxy(IVMRDeinterlaceControl *This,LPDWORD lpdwDeinterlacePrefs);")
cpp_quote("  void __RPC_STUB IVMRDeinterlaceControl_GetDeinterlacePrefs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRDeinterlaceControl_SetDeinterlacePrefs_Proxy(IVMRDeinterlaceControl *This,DWORD dwDeinterlacePrefs);")
cpp_quote("  void __RPC_STUB IVMRDeinterlaceControl_SetDeinterlacePrefs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRDeinterlaceControl_GetActualDeinterlaceMode_Proxy(IVMRDeinterlaceControl *This,DWORD dwStreamID,LPGUID lpDeinterlaceMode);")
cpp_quote("  void __RPC_STUB IVMRDeinterlaceControl_GetActualDeinterlaceMode_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef struct _VMRALPHABITMAP {")
cpp_quote("    DWORD dwFlags;")
cpp_quote("    HDC hdc;")
cpp_quote("    LPDIRECTDRAWSURFACE7 pDDS;")
cpp_quote("    RECT rSrc;")
cpp_quote("    NORMALIZEDRECT rDest;")
cpp_quote("    FLOAT fAlpha;")
cpp_quote("    COLORREF clrSrcKey;")
cpp_quote("  } VMRALPHABITMAP;")
cpp_quote("")
cpp_quote("  typedef struct _VMRALPHABITMAP *PVMRALPHABITMAP;")
cpp_quote("")
cpp_quote("#define VMRBITMAP_DISABLE 0x00000001")
cpp_quote("#define VMRBITMAP_HDC 0x00000002")
cpp_quote("#define VMRBITMAP_ENTIREDDS 0x00000004")
cpp_quote("#define VMRBITMAP_SRCCOLORKEY 0x00000008")
cpp_quote("#define VMRBITMAP_SRCRECT 0x00000010")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0406_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0406_v0_0_s_ifspec;")
cpp_quote("#ifndef __IVMRMixerBitmap_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRMixerBitmap_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRMixerBitmap;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRMixerBitmap : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI SetAlphaBitmap(const VMRALPHABITMAP *pBmpParms) = 0;")
cpp_quote("    virtual HRESULT WINAPI UpdateAlphaBitmapParameters(PVMRALPHABITMAP pBmpParms) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetAlphaBitmapParameters(PVMRALPHABITMAP pBmpParms) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRMixerBitmapVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRMixerBitmap *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRMixerBitmap *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRMixerBitmap *This);")
cpp_quote("      HRESULT (WINAPI *SetAlphaBitmap)(IVMRMixerBitmap *This,const VMRALPHABITMAP *pBmpParms);")
cpp_quote("      HRESULT (WINAPI *UpdateAlphaBitmapParameters)(IVMRMixerBitmap *This,PVMRALPHABITMAP pBmpParms);")
cpp_quote("      HRESULT (WINAPI *GetAlphaBitmapParameters)(IVMRMixerBitmap *This,PVMRALPHABITMAP pBmpParms);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRMixerBitmapVtbl;")
cpp_quote("  struct IVMRMixerBitmap {")
cpp_quote("    CONST_VTBL struct IVMRMixerBitmapVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRMixerBitmap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRMixerBitmap_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRMixerBitmap_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRMixerBitmap_SetAlphaBitmap(This,pBmpParms) (This)->lpVtbl->SetAlphaBitmap(This,pBmpParms)")
cpp_quote("#define IVMRMixerBitmap_UpdateAlphaBitmapParameters(This,pBmpParms) (This)->lpVtbl->UpdateAlphaBitmapParameters(This,pBmpParms)")
cpp_quote("#define IVMRMixerBitmap_GetAlphaBitmapParameters(This,pBmpParms) (This)->lpVtbl->GetAlphaBitmapParameters(This,pBmpParms)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRMixerBitmap_SetAlphaBitmap_Proxy(IVMRMixerBitmap *This,const VMRALPHABITMAP *pBmpParms);")
cpp_quote("  void __RPC_STUB IVMRMixerBitmap_SetAlphaBitmap_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMixerBitmap_UpdateAlphaBitmapParameters_Proxy(IVMRMixerBitmap *This,PVMRALPHABITMAP pBmpParms);")
cpp_quote("  void __RPC_STUB IVMRMixerBitmap_UpdateAlphaBitmapParameters_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRMixerBitmap_GetAlphaBitmapParameters_Proxy(IVMRMixerBitmap *This,PVMRALPHABITMAP pBmpParms);")
cpp_quote("  void __RPC_STUB IVMRMixerBitmap_GetAlphaBitmapParameters_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  typedef struct _VMRVIDEOSTREAMINFO {")
cpp_quote("    LPDIRECTDRAWSURFACE7 pddsVideoSurface;")
cpp_quote("    DWORD dwWidth;")
cpp_quote("    DWORD dwHeight;")
cpp_quote("    DWORD dwStrmID;")
cpp_quote("    FLOAT fAlpha;")
cpp_quote("    DDCOLORKEY ddClrKey;")
cpp_quote("    NORMALIZEDRECT rNormal;")
cpp_quote("  } VMRVIDEOSTREAMINFO;")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0407_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0407_v0_0_s_ifspec;")
cpp_quote("#ifndef __IVMRImageCompositor_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRImageCompositor_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRImageCompositor;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRImageCompositor : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI InitCompositionTarget(IUnknown *pD3DDevice,LPDIRECTDRAWSURFACE7 pddsRenderTarget) = 0;")
cpp_quote("    virtual HRESULT WINAPI TermCompositionTarget(IUnknown *pD3DDevice,LPDIRECTDRAWSURFACE7 pddsRenderTarget) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetStreamMediaType(DWORD dwStrmID,AM_MEDIA_TYPE *pmt,WINBOOL fTexture) = 0;")
cpp_quote("    virtual HRESULT WINAPI CompositeImage(IUnknown *pD3DDevice,LPDIRECTDRAWSURFACE7 pddsRenderTarget,AM_MEDIA_TYPE *pmtRenderTarget,REFERENCE_TIME rtStart,REFERENCE_TIME rtEnd,DWORD dwClrBkGnd,VMRVIDEOSTREAMINFO *pVideoStreamInfo,UINT cStreams) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRImageCompositorVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRImageCompositor *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRImageCompositor *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRImageCompositor *This);")
cpp_quote("      HRESULT (WINAPI *InitCompositionTarget)(IVMRImageCompositor *This,IUnknown *pD3DDevice,LPDIRECTDRAWSURFACE7 pddsRenderTarget);")
cpp_quote("      HRESULT (WINAPI *TermCompositionTarget)(IVMRImageCompositor *This,IUnknown *pD3DDevice,LPDIRECTDRAWSURFACE7 pddsRenderTarget);")
cpp_quote("      HRESULT (WINAPI *SetStreamMediaType)(IVMRImageCompositor *This,DWORD dwStrmID,AM_MEDIA_TYPE *pmt,WINBOOL fTexture);")
cpp_quote("      HRESULT (WINAPI *CompositeImage)(IVMRImageCompositor *This,IUnknown *pD3DDevice,LPDIRECTDRAWSURFACE7 pddsRenderTarget,AM_MEDIA_TYPE *pmtRenderTarget,REFERENCE_TIME rtStart,REFERENCE_TIME rtEnd,DWORD dwClrBkGnd,VMRVIDEOSTREAMINFO *pVideoStreamInfo,UINT cStreams);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRImageCompositorVtbl;")
cpp_quote("  struct IVMRImageCompositor {")
cpp_quote("    CONST_VTBL struct IVMRImageCompositorVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRImageCompositor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRImageCompositor_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRImageCompositor_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRImageCompositor_InitCompositionTarget(This,pD3DDevice,pddsRenderTarget) (This)->lpVtbl->InitCompositionTarget(This,pD3DDevice,pddsRenderTarget)")
cpp_quote("#define IVMRImageCompositor_TermCompositionTarget(This,pD3DDevice,pddsRenderTarget) (This)->lpVtbl->TermCompositionTarget(This,pD3DDevice,pddsRenderTarget)")
cpp_quote("#define IVMRImageCompositor_SetStreamMediaType(This,dwStrmID,pmt,fTexture) (This)->lpVtbl->SetStreamMediaType(This,dwStrmID,pmt,fTexture)")
cpp_quote("#define IVMRImageCompositor_CompositeImage(This,pD3DDevice,pddsRenderTarget,pmtRenderTarget,rtStart,rtEnd,dwClrBkGnd,pVideoStreamInfo,cStreams) (This)->lpVtbl->CompositeImage(This,pD3DDevice,pddsRenderTarget,pmtRenderTarget,rtStart,rtEnd,dwClrBkGnd,pVideoStreamInfo,cStreams)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRImageCompositor_InitCompositionTarget_Proxy(IVMRImageCompositor *This,IUnknown *pD3DDevice,LPDIRECTDRAWSURFACE7 pddsRenderTarget);")
cpp_quote("  void __RPC_STUB IVMRImageCompositor_InitCompositionTarget_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRImageCompositor_TermCompositionTarget_Proxy(IVMRImageCompositor *This,IUnknown *pD3DDevice,LPDIRECTDRAWSURFACE7 pddsRenderTarget);")
cpp_quote("  void __RPC_STUB IVMRImageCompositor_TermCompositionTarget_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRImageCompositor_SetStreamMediaType_Proxy(IVMRImageCompositor *This,DWORD dwStrmID,AM_MEDIA_TYPE *pmt,WINBOOL fTexture);")
cpp_quote("  void __RPC_STUB IVMRImageCompositor_SetStreamMediaType_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRImageCompositor_CompositeImage_Proxy(IVMRImageCompositor *This,IUnknown *pD3DDevice,LPDIRECTDRAWSURFACE7 pddsRenderTarget,AM_MEDIA_TYPE *pmtRenderTarget,REFERENCE_TIME rtStart,REFERENCE_TIME rtEnd,DWORD dwClrBkGnd,VMRVIDEOSTREAMINFO *pVideoStreamInfo,UINT cStreams);")
cpp_quote("  void __RPC_STUB IVMRImageCompositor_CompositeImage_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRVideoStreamControl_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRVideoStreamControl_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRVideoStreamControl;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRVideoStreamControl : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI SetColorKey(LPDDCOLORKEY lpClrKey) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetColorKey(LPDDCOLORKEY lpClrKey) = 0;")
cpp_quote("    virtual HRESULT WINAPI SetStreamActiveState(WINBOOL fActive) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetStreamActiveState(WINBOOL *lpfActive) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRVideoStreamControlVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRVideoStreamControl *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRVideoStreamControl *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRVideoStreamControl *This);")
cpp_quote("      HRESULT (WINAPI *SetColorKey)(IVMRVideoStreamControl *This,LPDDCOLORKEY lpClrKey);")
cpp_quote("      HRESULT (WINAPI *GetColorKey)(IVMRVideoStreamControl *This,LPDDCOLORKEY lpClrKey);")
cpp_quote("      HRESULT (WINAPI *SetStreamActiveState)(IVMRVideoStreamControl *This,WINBOOL fActive);")
cpp_quote("      HRESULT (WINAPI *GetStreamActiveState)(IVMRVideoStreamControl *This,WINBOOL *lpfActive);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRVideoStreamControlVtbl;")
cpp_quote("  struct IVMRVideoStreamControl {")
cpp_quote("    CONST_VTBL struct IVMRVideoStreamControlVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRVideoStreamControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRVideoStreamControl_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRVideoStreamControl_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRVideoStreamControl_SetColorKey(This,lpClrKey) (This)->lpVtbl->SetColorKey(This,lpClrKey)")
cpp_quote("#define IVMRVideoStreamControl_GetColorKey(This,lpClrKey) (This)->lpVtbl->GetColorKey(This,lpClrKey)")
cpp_quote("#define IVMRVideoStreamControl_SetStreamActiveState(This,fActive) (This)->lpVtbl->SetStreamActiveState(This,fActive)")
cpp_quote("#define IVMRVideoStreamControl_GetStreamActiveState(This,lpfActive) (This)->lpVtbl->GetStreamActiveState(This,lpfActive)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRVideoStreamControl_SetColorKey_Proxy(IVMRVideoStreamControl *This,LPDDCOLORKEY lpClrKey);")
cpp_quote("  void __RPC_STUB IVMRVideoStreamControl_SetColorKey_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRVideoStreamControl_GetColorKey_Proxy(IVMRVideoStreamControl *This,LPDDCOLORKEY lpClrKey);")
cpp_quote("  void __RPC_STUB IVMRVideoStreamControl_GetColorKey_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRVideoStreamControl_SetStreamActiveState_Proxy(IVMRVideoStreamControl *This,WINBOOL fActive);")
cpp_quote("  void __RPC_STUB IVMRVideoStreamControl_SetStreamActiveState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRVideoStreamControl_GetStreamActiveState_Proxy(IVMRVideoStreamControl *This,WINBOOL *lpfActive);")
cpp_quote("  void __RPC_STUB IVMRVideoStreamControl_GetStreamActiveState_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRSurface_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRSurface_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRSurface;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRSurface : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI IsSurfaceLocked(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI LockSurface(BYTE **lpSurface) = 0;")
cpp_quote("    virtual HRESULT WINAPI UnlockSurface(void) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetSurface(LPDIRECTDRAWSURFACE7 *lplpSurface) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRSurfaceVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRSurface *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRSurface *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRSurface *This);")
cpp_quote("      HRESULT (WINAPI *IsSurfaceLocked)(IVMRSurface *This);")
cpp_quote("      HRESULT (WINAPI *LockSurface)(IVMRSurface *This,BYTE **lpSurface);")
cpp_quote("      HRESULT (WINAPI *UnlockSurface)(IVMRSurface *This);")
cpp_quote("      HRESULT (WINAPI *GetSurface)(IVMRSurface *This,LPDIRECTDRAWSURFACE7 *lplpSurface);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRSurfaceVtbl;")
cpp_quote("  struct IVMRSurface {")
cpp_quote("    CONST_VTBL struct IVMRSurfaceVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRSurface_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRSurface_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRSurface_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRSurface_IsSurfaceLocked(This) (This)->lpVtbl->IsSurfaceLocked(This)")
cpp_quote("#define IVMRSurface_LockSurface(This,lpSurface) (This)->lpVtbl->LockSurface(This,lpSurface)")
cpp_quote("#define IVMRSurface_UnlockSurface(This) (This)->lpVtbl->UnlockSurface(This)")
cpp_quote("#define IVMRSurface_GetSurface(This,lplpSurface) (This)->lpVtbl->GetSurface(This,lplpSurface)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRSurface_IsSurfaceLocked_Proxy(IVMRSurface *This);")
cpp_quote("  void __RPC_STUB IVMRSurface_IsSurfaceLocked_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRSurface_LockSurface_Proxy(IVMRSurface *This,BYTE **lpSurface);")
cpp_quote("  void __RPC_STUB IVMRSurface_LockSurface_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRSurface_UnlockSurface_Proxy(IVMRSurface *This);")
cpp_quote("  void __RPC_STUB IVMRSurface_UnlockSurface_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRSurface_GetSurface_Proxy(IVMRSurface *This,LPDIRECTDRAWSURFACE7 *lplpSurface);")
cpp_quote("  void __RPC_STUB IVMRSurface_GetSurface_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRImagePresenterConfig_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRImagePresenterConfig_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRImagePresenterConfig;")
cpp_quote("")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRImagePresenterConfig : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI SetRenderingPrefs(DWORD dwRenderFlags) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetRenderingPrefs(DWORD *dwRenderFlags) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRImagePresenterConfigVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRImagePresenterConfig *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRImagePresenterConfig *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRImagePresenterConfig *This);")
cpp_quote("      HRESULT (WINAPI *SetRenderingPrefs)(IVMRImagePresenterConfig *This,DWORD dwRenderFlags);")
cpp_quote("      HRESULT (WINAPI *GetRenderingPrefs)(IVMRImagePresenterConfig *This,DWORD *dwRenderFlags);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRImagePresenterConfigVtbl;")
cpp_quote("  struct IVMRImagePresenterConfig {")
cpp_quote("    CONST_VTBL struct IVMRImagePresenterConfigVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRImagePresenterConfig_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRImagePresenterConfig_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRImagePresenterConfig_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRImagePresenterConfig_SetRenderingPrefs(This,dwRenderFlags) (This)->lpVtbl->SetRenderingPrefs(This,dwRenderFlags)")
cpp_quote("#define IVMRImagePresenterConfig_GetRenderingPrefs(This,dwRenderFlags) (This)->lpVtbl->GetRenderingPrefs(This,dwRenderFlags)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRImagePresenterConfig_SetRenderingPrefs_Proxy(IVMRImagePresenterConfig *This,DWORD dwRenderFlags);")
cpp_quote("  void __RPC_STUB IVMRImagePresenterConfig_SetRenderingPrefs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRImagePresenterConfig_GetRenderingPrefs_Proxy(IVMRImagePresenterConfig *This,DWORD *dwRenderFlags);")
cpp_quote("  void __RPC_STUB IVMRImagePresenterConfig_GetRenderingPrefs_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVMRImagePresenterExclModeConfig_INTERFACE_DEFINED__")
cpp_quote("#define __IVMRImagePresenterExclModeConfig_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVMRImagePresenterExclModeConfig;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVMRImagePresenterExclModeConfig : public IVMRImagePresenterConfig {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI SetXlcModeDDObjAndPrimarySurface(LPDIRECTDRAW7 lpDDObj,LPDIRECTDRAWSURFACE7 lpPrimarySurf) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetXlcModeDDObjAndPrimarySurface(LPDIRECTDRAW7 *lpDDObj,LPDIRECTDRAWSURFACE7 *lpPrimarySurf) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVMRImagePresenterExclModeConfigVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVMRImagePresenterExclModeConfig *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVMRImagePresenterExclModeConfig *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVMRImagePresenterExclModeConfig *This);")
cpp_quote("      HRESULT (WINAPI *SetRenderingPrefs)(IVMRImagePresenterExclModeConfig *This,DWORD dwRenderFlags);")
cpp_quote("      HRESULT (WINAPI *GetRenderingPrefs)(IVMRImagePresenterExclModeConfig *This,DWORD *dwRenderFlags);")
cpp_quote("      HRESULT (WINAPI *SetXlcModeDDObjAndPrimarySurface)(IVMRImagePresenterExclModeConfig *This,LPDIRECTDRAW7 lpDDObj,LPDIRECTDRAWSURFACE7 lpPrimarySurf);")
cpp_quote("      HRESULT (WINAPI *GetXlcModeDDObjAndPrimarySurface)(IVMRImagePresenterExclModeConfig *This,LPDIRECTDRAW7 *lpDDObj,LPDIRECTDRAWSURFACE7 *lpPrimarySurf);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVMRImagePresenterExclModeConfigVtbl;")
cpp_quote("  struct IVMRImagePresenterExclModeConfig {")
cpp_quote("    CONST_VTBL struct IVMRImagePresenterExclModeConfigVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVMRImagePresenterExclModeConfig_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVMRImagePresenterExclModeConfig_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVMRImagePresenterExclModeConfig_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVMRImagePresenterExclModeConfig_SetRenderingPrefs(This,dwRenderFlags) (This)->lpVtbl->SetRenderingPrefs(This,dwRenderFlags)")
cpp_quote("#define IVMRImagePresenterExclModeConfig_GetRenderingPrefs(This,dwRenderFlags) (This)->lpVtbl->GetRenderingPrefs(This,dwRenderFlags)")
cpp_quote("#define IVMRImagePresenterExclModeConfig_SetXlcModeDDObjAndPrimarySurface(This,lpDDObj,lpPrimarySurf) (This)->lpVtbl->SetXlcModeDDObjAndPrimarySurface(This,lpDDObj,lpPrimarySurf)")
cpp_quote("#define IVMRImagePresenterExclModeConfig_GetXlcModeDDObjAndPrimarySurface(This,lpDDObj,lpPrimarySurf) (This)->lpVtbl->GetXlcModeDDObjAndPrimarySurface(This,lpDDObj,lpPrimarySurf)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVMRImagePresenterExclModeConfig_SetXlcModeDDObjAndPrimarySurface_Proxy(IVMRImagePresenterExclModeConfig *This,LPDIRECTDRAW7 lpDDObj,LPDIRECTDRAWSURFACE7 lpPrimarySurf);")
cpp_quote("  void __RPC_STUB IVMRImagePresenterExclModeConfig_SetXlcModeDDObjAndPrimarySurface_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVMRImagePresenterExclModeConfig_GetXlcModeDDObjAndPrimarySurface_Proxy(IVMRImagePresenterExclModeConfig *This,LPDIRECTDRAW7 *lpDDObj,LPDIRECTDRAWSURFACE7 *lpPrimarySurf);")
cpp_quote("  void __RPC_STUB IVMRImagePresenterExclModeConfig_GetXlcModeDDObjAndPrimarySurface_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef __IVPManager_INTERFACE_DEFINED__")
cpp_quote("#define __IVPManager_INTERFACE_DEFINED__")
cpp_quote("  EXTERN_C const IID IID_IVPManager;")
cpp_quote("#if defined(__cplusplus) && !defined(CINTERFACE)")
cpp_quote("  struct IVPManager : public IUnknown {")
cpp_quote("  public:")
cpp_quote("    virtual HRESULT WINAPI SetVideoPortIndex(DWORD dwVideoPortIndex) = 0;")
cpp_quote("    virtual HRESULT WINAPI GetVideoPortIndex(DWORD *pdwVideoPortIndex) = 0;")
cpp_quote("  };")
cpp_quote("#else")
cpp_quote("  typedef struct IVPManagerVtbl {")
cpp_quote("    BEGIN_INTERFACE")
cpp_quote("      HRESULT (WINAPI *QueryInterface)(IVPManager *This,REFIID riid,void **ppvObject);")
cpp_quote("      ULONG (WINAPI *AddRef)(IVPManager *This);")
cpp_quote("      ULONG (WINAPI *Release)(IVPManager *This);")
cpp_quote("      HRESULT (WINAPI *SetVideoPortIndex)(IVPManager *This,DWORD dwVideoPortIndex);")
cpp_quote("      HRESULT (WINAPI *GetVideoPortIndex)(IVPManager *This,DWORD *pdwVideoPortIndex);")
cpp_quote("    END_INTERFACE")
cpp_quote("  } IVPManagerVtbl;")
cpp_quote("  struct IVPManager {")
cpp_quote("    CONST_VTBL struct IVPManagerVtbl *lpVtbl;")
cpp_quote("  };")
cpp_quote("#ifdef COBJMACROS")
cpp_quote("#define IVPManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)")
cpp_quote("#define IVPManager_AddRef(This) (This)->lpVtbl->AddRef(This)")
cpp_quote("#define IVPManager_Release(This) (This)->lpVtbl->Release(This)")
cpp_quote("#define IVPManager_SetVideoPortIndex(This,dwVideoPortIndex) (This)->lpVtbl->SetVideoPortIndex(This,dwVideoPortIndex)")
cpp_quote("#define IVPManager_GetVideoPortIndex(This,pdwVideoPortIndex) (This)->lpVtbl->GetVideoPortIndex(This,pdwVideoPortIndex)")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("  HRESULT WINAPI IVPManager_SetVideoPortIndex_Proxy(IVPManager *This,DWORD dwVideoPortIndex);")
cpp_quote("  void __RPC_STUB IVPManager_SetVideoPortIndex_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("  HRESULT WINAPI IVPManager_GetVideoPortIndex_Proxy(IVPManager *This,DWORD *pdwVideoPortIndex);")
cpp_quote("  void __RPC_STUB IVPManager_GetVideoPortIndex_Stub(IRpcStubBuffer *This,IRpcChannelBuffer *_pRpcChannelBuffer,PRPC_MESSAGE _pRpcMessage,DWORD *_pdwStubPhase);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0413_v0_0_c_ifspec;")
cpp_quote("  extern RPC_IF_HANDLE __MIDL_itf_strmif_0413_v0_0_s_ifspec;")
