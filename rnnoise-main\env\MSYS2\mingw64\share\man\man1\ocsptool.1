.de1 NOP
.  it 1 an-trap
.  if \\n[.$] \,\\$*\/
..
.ie t \
.ds B-Font [CB]
.ds I-Font [CI]
.ds R-Font [CR]
.el \
.ds B-Font B
.ds I-Font I
.ds R-Font R
.TH ocsptool 1 "08 Feb 2025" "3.8.9" "User Commands"
.SH NAME
\f\*[B-Font]ocsptool\fP
\- GnuTLS OCSP tool
.SH SYNOPSIS
\f\*[B-Font]ocsptool\fP
.\" Mixture of short (flag) options and long options
[\f\*[B-Font]\-flags\f[]]
[\f\*[B-Font]\-flag\f[] [\f\*[I-Font]value\f[]]]
[\f\*[B-Font]\-\-option-name\f[][[=| ]\f\*[I-Font]value\f[]]]
.sp \n(Ppu
.ne 2

All arguments must be options.
.sp \n(Ppu
.ne 2
.SH "DESCRIPTION"
.br
\fBOn verification\fP
.br
Responses are typically signed/issued by designated certificates or
certificate authorities and thus this tool requires on verification
the certificate of the issuer or the full certificate chain in order to
determine the appropriate signing authority. The specified certificate
of the issuer is assumed trusted.
.sp
.sp
.SH "OPTIONS"
.TP
.NOP \f\*[B-Font]\-d\f[] \f\*[I-Font]num\f[], \f\*[B-Font]\-\-debug\f[]=\f\*[I-Font]num\f[]
Enable debugging.
This option takes an integer number as its argument.
The value of
\f\*[I-Font]num\f[]
is constrained to being:
.in +4
.nf
.na
in the range 0 through 9999
.fi
.in -4
.sp
Specifies the debug level.
.TP
.NOP \f\*[B-Font]\-V\f[], \f\*[B-Font]\-\-verbose\f[]
More verbose output.
.sp
.TP
.NOP \f\*[B-Font]\-\-infile\f[]=\f\*[I-Font]file\f[]
Input file.
.sp
.TP
.NOP \f\*[B-Font]\-\-outfile\f[]=\f\*[I-Font]str\f[]
Output file.
.sp
.TP
.NOP \f\*[B-Font]\-\-ask\f[]=\f\*[I-Font]server name|url\f[]
Ask an OCSP/HTTP server on a certificate validity.
.sp
Connects to the specified HTTP OCSP server and queries on the validity of the loaded certificate.
Its argument can be a URL or a plain server name. It can be combined with \-\-load\-chain, where it checks
all certificates in the provided chain, or with \-\-load\-cert and
\-\-load\-issuer options. The latter checks the provided certificate
against its specified issuer certificate.
.TP
.NOP \f\*[B-Font]\-e\f[], \f\*[B-Font]\-\-verify\-response\f[]
Verify response.
.sp
Verifies the provided OCSP response against the system trust
anchors (unless \-\-load\-trust is provided). It requires the \-\-load\-signer
or \-\-load\-chain options to obtain the signer of the OCSP response.
.TP
.NOP \f\*[B-Font]\-i\f[], \f\*[B-Font]\-\-request\-info\f[]
Print information on a OCSP request.
.sp
Display detailed information on the provided OCSP request.
.TP
.NOP \f\*[B-Font]\-j\f[], \f\*[B-Font]\-\-response\-info\f[]
Print information on a OCSP response.
.sp
Display detailed information on the provided OCSP response.
.TP
.NOP \f\*[B-Font]\-q\f[], \f\*[B-Font]\-\-generate\-request\f[]
Generates an OCSP request.
.sp
.TP
.NOP \f\*[B-Font]\-\-nonce\f[], \f\*[B-Font]\-\-no\-nonce\f[]
Use (or not) a nonce to OCSP request.
The \fIno\-nonce\fP form will disable the option.
.sp
.TP
.NOP \f\*[B-Font]\-\-load\-chain\f[]=\f\*[I-Font]file\f[]
Reads a set of certificates forming a chain from file.
.sp
.TP
.NOP \f\*[B-Font]\-\-load\-issuer\f[]=\f\*[I-Font]file\f[]
Reads issuer's certificate from file.
.sp
.TP
.NOP \f\*[B-Font]\-\-load\-cert\f[]=\f\*[I-Font]file\f[]
Reads the certificate to check from file.
.sp
.TP
.NOP \f\*[B-Font]\-\-load\-trust\f[]=\f\*[I-Font]file\f[]
Read OCSP trust anchors from file.
This option must not appear in combination with any of the following options:
load-signer.
.sp
When verifying an OCSP response read the trust anchors from the
provided file. When this is not provided, the system's trust anchors will be
used.
.TP
.NOP \f\*[B-Font]\-\-load\-signer\f[]=\f\*[I-Font]file\f[]
Reads the OCSP response signer from file.
This option must not appear in combination with any of the following options:
load-trust.
.sp
.TP
.NOP \f\*[B-Font]\-\-inder\f[], \f\*[B-Font]\-\-no\-inder\f[]
Use DER format for input certificates and private keys.
The \fIno\-inder\fP form will disable the option.
.sp
.TP
.NOP \f\*[B-Font]\-\-outder\f[]
Use DER format for output of responses (this is the default).
.sp
The output will be in DER encoded format. Unlike other GnuTLS tools, this is the default for this tool
.TP
.NOP \f\*[B-Font]\-\-outpem\f[]
Use PEM format for output of responses.
.sp
The output will be in PEM format.
.TP
.NOP \f\*[B-Font]\-Q\f[] \f\*[I-Font]file\f[], \f\*[B-Font]\-\-load\-request\f[]=\f\*[I-Font]file\f[]
Reads the DER encoded OCSP request from file.
.sp
.TP
.NOP \f\*[B-Font]\-S\f[] \f\*[I-Font]file\f[], \f\*[B-Font]\-\-load\-response\f[]=\f\*[I-Font]file\f[]
Reads the DER encoded OCSP response from file.
.sp
.TP
.NOP \f\*[B-Font]\-\-ignore\-errors\f[]
Ignore any verification errors.
.sp
.TP
.NOP \f\*[B-Font]\-\-verify\-allow\-broken\f[]
Allow broken algorithms, such as MD5 for verification.
.sp
This can be combined with \-\-verify\-response.
.TP
.NOP \f\*[B-Font]\-\-attime\f[]=\f\*[I-Font]timestamp\f[]
Perform validation at the timestamp instead of the system time.
.sp
timestamp is an instance in time encoded as Unix time or in a human
 readable timestring such as "29 Feb 2004", "2004\-02\-29".
Full documentation available at 
<https://www.gnu.org/software/coreutils/manual/html_node/Date\-input\-formats.html>
or locally via info '(coreutils) date invocation'.
.TP
.NOP \f\*[B-Font]\-v\f[] \f\*[I-Font]arg\f[], \f\*[B-Font]\-\-version\f[]=\f\*[I-Font]arg\f[]
Output version of program and exit.  The default mode is `v', a simple
version.  The `c' mode will print copyright information and `n' will
print the full copyright notice.
.TP
.NOP \f\*[B-Font]\-h\f[], \f\*[B-Font]\-\-help\f[]
Display usage information and exit.
.TP
.NOP \f\*[B-Font]\-!\f[], \f\*[B-Font]\-\-more\-help\f[]
Pass the extended usage information through a pager.

.sp
.SH EXAMPLES
.br
\fBPrint information about an OCSP request\fP
.br
.sp
To parse an OCSP request and print information about the content, the
\fB\-i\fP or \fB\-\-request\-info\fP parameter may be used as follows.
The \fB\-Q\fP parameter specify the name of the file containing the
OCSP request, and it should contain the OCSP request in binary DER
format.
.sp
.br
.in +4
.nf
$ ocsptool \-i \-Q ocsp\-request.der
.in -4
.fi
.sp
The input file may also be sent to standard input like this:
.sp
.br
.in +4
.nf
$ cat ocsp\-request.der | ocsptool \-\-request\-info
.in -4
.fi
.sp
.br
\fBPrint information about an OCSP response\fP
.br
.sp
Similar to parsing OCSP requests, OCSP responses can be parsed using
the \fB\-j\fP or \fB\-\-response\-info\fP as follows.
.sp
.br
.in +4
.nf
$ ocsptool \-j \-Q ocsp\-response.der
$ cat ocsp\-response.der | ocsptool \-\-response\-info
.in -4
.fi
.sp
.br
\fBGenerate an OCSP request\fP
.br
.sp
The \fB\-q\fP or \fB\-\-generate\-request\fP parameters are used to
generate an OCSP request.  By default the OCSP request is written to
standard output in binary DER format, but can be stored in a file
using \fB\-\-outfile\fP.  To generate an OCSP request the issuer of the
certificate to check needs to be specified with \fB\-\-load\-issuer\fP
and the certificate to check with \fB\-\-load\-cert\fP.  By default PEM
format is used for these files, although \fB\-\-inder\fP can be used to
specify that the input files are in DER format.
.sp
.br
.in +4
.nf
$ ocsptool \-q \-\-load\-issuer issuer.pem \-\-load\-cert client.pem \
           \-\-outfile ocsp\-request.der
.in -4
.fi
.sp
When generating OCSP requests, the tool will add an OCSP extension
containing a nonce.  This behaviour can be disabled by specifying
\fB\-\-no\-nonce\fP.
.sp
.br
\fBVerify signature in OCSP response\fP
.br
.sp
To verify the signature in an OCSP response the \fB\-e\fP or
\fB\-\-verify\-response\fP parameter is used.  The tool will read an
OCSP response in DER format from standard input, or from the file
specified by \fB\-\-load\-response\fP.  The OCSP response is verified
against a set of trust anchors, which are specified using
\fB\-\-load\-trust\fP.  The trust anchors are concatenated certificates
in PEM format.  The certificate that signed the OCSP response needs to
be in the set of trust anchors, or the issuer of the signer
certificate needs to be in the set of trust anchors and the OCSP
Extended Key Usage bit has to be asserted in the signer certificate.
.sp
.br
.in +4
.nf
$ ocsptool \-e \-\-load\-trust issuer.pem \
           \-\-load\-response ocsp\-response.der
.in -4
.fi
.sp
The tool will print status of verification.
.sp
.br
\fBVerify signature in OCSP response against given certificate\fP
.br
.sp
It is possible to override the normal trust logic if you know that a
certain certificate is supposed to have signed the OCSP response, and
you want to use it to check the signature.  This is achieved using
\fB\-\-load\-signer\fP instead of \fB\-\-load\-trust\fP.  This will load
one certificate and it will be used to verify the signature in the
OCSP response.  It will not check the Extended Key Usage bit.
.sp
.br
.in +4
.nf
$ ocsptool \-e \-\-load\-signer ocsp\-signer.pem \
           \-\-load\-response ocsp\-response.der
.in -4
.fi
.sp
This approach is normally only relevant in two situations.  The first
is when the OCSP response does not contain a copy of the signer
certificate, so the \fB\-\-load\-trust\fP code would fail.  The second
is if you want to avoid the indirect mode where the OCSP response
signer certificate is signed by a trust anchor.
.sp
.br
\fBReal\-world example\fP
.br
.sp
Here is an example of how to generate an OCSP request for a
certificate and to verify the response.  For illustration we'll use
the \fBblog.josefsson.org\fP host, which (as of writing) uses a
certificate from CACert.  First we'll use \fBgnutls\-cli\fP to get a
copy of the server certificate chain.  The server is not required to
send this information, but this particular one is configured to do so.
.sp
.br
.in +4
.nf
$ echo | gnutls\-cli \-p 443 blog.josefsson.org \-\-save\-cert chain.pem
.in -4
.fi
.sp
The saved certificates normally contain a pointer to where the OCSP
responder is located, in the Authority Information Access Information
extension.  For example, from \fBcerttool \-i < chain.pem\fP there is
this information:
.sp
.br
.in +4
.nf
		Authority Information Access Information (not critical):
			Access Method: *******.********.1 (id\-ad\-ocsp)
			Access Location URI: https://ocsp.CAcert.org/
.in -4
.fi
.sp
This means that ocsptool can discover the servers to contact over HTTP.
We can now request information on the chain certificates.
.sp
.br
.in +4
.nf
$ ocsptool \-\-ask \-\-load\-chain chain.pem
.in -4
.fi
.sp
The request is sent via HTTP to the OCSP server address found in
the certificates. It is possible to override the address of the
OCSP server as well as ask information on a particular certificate
using \-\-load\-cert and \-\-load\-issuer.
.sp
.br
.in +4
.nf
$ ocsptool \-\-ask https://ocsp.CAcert.org/ \-\-load\-chain chain.pem
.in -4
.fi
.SH "EXIT STATUS"
One of the following exit values will be returned:
.TP
.NOP 0 " (EXIT_SUCCESS)"
Successful program execution.
.TP
.NOP 1 " (EXIT_FAILURE)"
The operation failed or the command syntax was not valid.
.PP
.SH "SEE ALSO"
certtool (1)
.SH "AUTHORS"

.SH "COPYRIGHT"
Copyright (C) 2020-2023 Free Software Foundation, and others all rights reserved.
This program is released under the terms of
the GNU General Public License, version 3 or later
.
.SH "BUGS"
Please send bug reports to: <EMAIL>
