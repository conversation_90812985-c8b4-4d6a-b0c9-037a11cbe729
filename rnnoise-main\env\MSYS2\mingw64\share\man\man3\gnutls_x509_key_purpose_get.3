.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_key_purpose_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_key_purpose_get \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_key_purpose_get(gnutls_x509_key_purposes_t " p ", unsigned " idx ", gnutls_datum_t * " oid ");"
.SH ARGUMENTS
.IP "gnutls_x509_key_purposes_t p" 12
The key purposes
.IP "unsigned idx" 12
The index of the key purpose to retrieve
.IP "gnutls_datum_t * oid" 12
Will hold the object identifier of the key purpose (to be treated as constant)
.SH "DESCRIPTION"
This function will retrieve the specified by the index key purpose in the
purposes type. The object identifier will be a null terminated string.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
if the index is out of bounds, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
