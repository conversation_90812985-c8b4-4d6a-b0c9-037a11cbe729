.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_system_key_iter_get_info" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_system_key_iter_get_info \- API function
.SH SYNOPSIS
.B #include <gnutls/system-keys.h>
.sp
.BI "int gnutls_system_key_iter_get_info(gnutls_system_key_iter_t * " iter ", unsigned " cert_type ", char ** " cert_url ", char ** " key_url ", char ** " label ", gnutls_datum_t * " der ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_system_key_iter_t * iter" 12
an iterator of the system keys (must be set to \fBNULL\fP initially)
.IP "unsigned cert_type" 12
A value of gnutls_certificate_type_t which indicates the type of certificate to look for
.IP "char ** cert_url" 12
The certificate URL of the pair (may be \fBNULL\fP)
.IP "char ** key_url" 12
The key URL of the pair (may be \fBNULL\fP)
.IP "char ** label" 12
The friendly name (if any) of the pair (may be \fBNULL\fP)
.IP "gnutls_datum_t * der" 12
if non\-NULL the DER data of the certificate
.IP "unsigned int flags" 12
should be zero
.SH "DESCRIPTION"
This function will return on each call a certificate
and key pair URLs, as well as a label associated with them,
and the DER\-encoded certificate. When the iteration is complete it will
return \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP.

Typically  \fIcert_type\fP should be \fBGNUTLS_CRT_X509\fP.

All values set are allocated and must be cleared using \fBgnutls_free()\fP,
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
