/*
 * Copyright 2009 <PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "d3d10.idl";

const unsigned int D3D10_SHADER_DEBUG                           = 0x00001;
const unsigned int D3D10_SHADER_SKIP_VALIDATION                 = 0x00002;
const unsigned int D3D10_SHADER_SKIP_OPTIMIZATION               = 0x00004;
const unsigned int D3D10_SHADER_PACK_MATRIX_ROW_MAJOR           = 0x00008;
const unsigned int D3D10_SHADER_PACK_MATRIX_COLUMN_MAJOR        = 0x00010;
const unsigned int D3D10_SHADER_PARTIAL_PRECISION               = 0x00020;
const unsigned int D3D10_SHADER_FORCE_VS_SOFTWARE_NO_OPT        = 0x00040;
const unsigned int D3D10_SHADER_FORCE_PS_SOFTWARE_NO_OPT        = 0x00080;
const unsigned int D3D10_SHADER_NO_PRESHADER                    = 0x00100;
const unsigned int D3D10_SHADER_AVOID_FLOW_CONTROL              = 0x00200;
const unsigned int D3D10_SHADER_PREFER_FLOW_CONTROL             = 0x00400;
const unsigned int D3D10_SHADER_ENABLE_STRICTNESS               = 0x00800;
const unsigned int D3D10_SHADER_ENABLE_BACKWARDS_COMPATIBILITY  = 0x01000;
const unsigned int D3D10_SHADER_IEEE_STRICTNESS                 = 0x02000;
const unsigned int D3D10_SHADER_WARNINGS_ARE_ERRORS             = 0x40000;

const unsigned int D3D10_SHADER_OPTIMIZATION_LEVEL0             = 0x04000;
const unsigned int D3D10_SHADER_OPTIMIZATION_LEVEL1             = 0x00000;
const unsigned int D3D10_SHADER_OPTIMIZATION_LEVEL2             = 0x0c000;
const unsigned int D3D10_SHADER_OPTIMIZATION_LEVEL3             = 0x08000;

typedef D3D_SHADER_MACRO D3D10_SHADER_MACRO;
typedef D3D_SHADER_MACRO *LPD3D10_SHADER_MACRO;

typedef D3D_SHADER_VARIABLE_CLASS D3D10_SHADER_VARIABLE_CLASS;
typedef D3D_SHADER_VARIABLE_CLASS *LPD3D10_SHADER_VARIABLE_CLASS;

typedef D3D_CBUFFER_TYPE D3D10_CBUFFER_TYPE;
typedef D3D_CBUFFER_TYPE *LPD3D10_CBUFFER_TYPE;

typedef D3D_REGISTER_COMPONENT_TYPE D3D10_REGISTER_COMPONENT_TYPE;

typedef D3D_RESOURCE_RETURN_TYPE D3D10_RESOURCE_RETURN_TYPE;

typedef D3D_NAME D3D10_NAME;

typedef D3D_SHADER_INPUT_TYPE D3D10_SHADER_INPUT_TYPE;
typedef D3D_SHADER_INPUT_TYPE *LPD3D10_SHADER_INPUT_TYPE;

typedef D3D_SHADER_VARIABLE_TYPE D3D10_SHADER_VARIABLE_TYPE;
typedef D3D_SHADER_VARIABLE_TYPE *LPD3D10_SHADER_VARIABLE_TYPE;

typedef D3D_INCLUDE_TYPE D3D10_INCLUDE_TYPE;
typedef ID3DInclude ID3D10Include;
typedef ID3DInclude *LPD3D10INCLUDE;
cpp_quote("#define IID_ID3D10Include IID_ID3DInclude")

typedef struct _D3D10_SHADER_INPUT_BIND_DESC
{
    const char *Name;
    D3D10_SHADER_INPUT_TYPE Type;
    UINT BindPoint;
    UINT BindCount;
    UINT uFlags;
    D3D10_RESOURCE_RETURN_TYPE ReturnType;
    D3D10_SRV_DIMENSION Dimension;
    UINT NumSamples;
} D3D10_SHADER_INPUT_BIND_DESC;

typedef struct _D3D10_SIGNATURE_PARAMETER_DESC
{
    const char *SemanticName;
    UINT SemanticIndex;
    UINT Register;
    D3D10_NAME SystemValueType;
    D3D10_REGISTER_COMPONENT_TYPE ComponentType;
    BYTE Mask;
    BYTE ReadWriteMask;
} D3D10_SIGNATURE_PARAMETER_DESC;

typedef struct _D3D10_SHADER_DESC
{
    UINT Version;
    const char *Creator;
    UINT Flags;
    UINT ConstantBuffers;
    UINT BoundResources;
    UINT InputParameters;
    UINT OutputParameters;
    UINT InstructionCount;
    UINT TempRegisterCount;
    UINT TempArrayCount;
    UINT DefCount;
    UINT DclCount;
    UINT TextureNormalInstructions;
    UINT TextureLoadInstructions;
    UINT TextureCompInstructions;
    UINT TextureBiasInstructions;
    UINT TextureGradientInstructions;
    UINT FloatInstructionCount;
    UINT IntInstructionCount;
    UINT UintInstructionCount;
    UINT StaticFlowControlCount;
    UINT DynamicFlowControlCount;
    UINT MacroInstructionCount;
    UINT ArrayInstructionCount;
    UINT CutInstructionCount;
    UINT EmitInstructionCount;
    D3D10_PRIMITIVE_TOPOLOGY GSOutputTopology;
    UINT GSMaxOutputVertexCount;
} D3D10_SHADER_DESC;

typedef struct _D3D10_SHADER_BUFFER_DESC
{
    const char *Name;
    D3D10_CBUFFER_TYPE Type;
    UINT Variables;
    UINT Size;
    UINT uFlags;
} D3D10_SHADER_BUFFER_DESC;

typedef struct _D3D10_SHADER_VARIABLE_DESC
{
    const char *Name;
    UINT StartOffset;
    UINT Size;
    UINT uFlags;
    void *DefaultValue;
} D3D10_SHADER_VARIABLE_DESC;

typedef struct _D3D10_SHADER_TYPE_DESC
{
    D3D10_SHADER_VARIABLE_CLASS Class;
    D3D10_SHADER_VARIABLE_TYPE Type;
    UINT Rows;
    UINT Columns;
    UINT Elements;
    UINT Members;
    UINT Offset;
} D3D10_SHADER_TYPE_DESC;

[
    local,
    object,
    uuid(c530ad7d-9b16-4395-a979-ba2ecff83add),
]
interface ID3D10ShaderReflectionType
{
    HRESULT GetDesc(D3D10_SHADER_TYPE_DESC *desc);
    ID3D10ShaderReflectionType *GetMemberTypeByIndex(UINT index);
    ID3D10ShaderReflectionType *GetMemberTypeByName(const char *name);
    const char *GetMemberTypeName(UINT index);
};

[
    local,
    object,
    uuid(1bf63c95-2650-405d-99c1-3636bd1da0a1),
]
interface ID3D10ShaderReflectionVariable
{
    HRESULT GetDesc(D3D10_SHADER_VARIABLE_DESC *desc);
    ID3D10ShaderReflectionType *GetType();
};

[
    local,
    object,
    uuid(66c66a94-dddd-4b62-a66a-f0da33c2b4d0),
]
interface ID3D10ShaderReflectionConstantBuffer
{
    HRESULT GetDesc(D3D10_SHADER_BUFFER_DESC *desc);
    ID3D10ShaderReflectionVariable *GetVariableByIndex(UINT index);
    ID3D10ShaderReflectionVariable *GetVariableByName(const char *name);
};

[
    local,
    object,
    uuid(d40e20b6-f8f7-42ad-ab20-4baf8f15dfaa),
]
interface ID3D10ShaderReflection : IUnknown
{
    HRESULT GetDesc(D3D10_SHADER_DESC *desc);
    ID3D10ShaderReflectionConstantBuffer *GetConstantBufferByIndex(UINT index);
    ID3D10ShaderReflectionConstantBuffer *GetConstantBufferByName(const char *name);
    HRESULT GetResourceBindingDesc(UINT index, D3D10_SHADER_INPUT_BIND_DESC *desc);
    HRESULT GetInputParameterDesc(UINT index, D3D10_SIGNATURE_PARAMETER_DESC *desc);
    HRESULT GetOutputParameterDesc(UINT index, D3D10_SIGNATURE_PARAMETER_DESC *desc);
};


HRESULT __stdcall D3D10CompileShader(const char *data, SIZE_T data_size, const char *filename,
        const D3D10_SHADER_MACRO *defines, ID3D10Include *include, const char *entrypoint,
        const char *profile, UINT flags, ID3D10Blob **shader, ID3D10Blob **error_messages);
HRESULT __stdcall D3D10DisassembleShader(const void *data, SIZE_T data_size,
        BOOL color_code, const char *comments, ID3D10Blob **disassembly);
const char * __stdcall D3D10GetVertexShaderProfile(ID3D10Device *device);
const char * __stdcall D3D10GetGeometryShaderProfile(ID3D10Device *device);
const char * __stdcall D3D10GetPixelShaderProfile(ID3D10Device *device);

HRESULT __stdcall D3D10ReflectShader(const void *data, SIZE_T data_size, ID3D10ShaderReflection **reflector);
HRESULT __stdcall D3D10GetInputSignatureBlob(const void *data, SIZE_T data_size, ID3D10Blob **blob);
HRESULT __stdcall D3D10GetOutputSignatureBlob(const void *data, SIZE_T data_size, ID3D10Blob **blob);
HRESULT __stdcall D3D10GetInputAndOutputSignatureBlob(const void *data, SIZE_T data_size, ID3D10Blob **blob);
HRESULT __stdcall D3D10GetShaderDebugInfo(const void *data, SIZE_T data_size, ID3D10Blob **blob);
