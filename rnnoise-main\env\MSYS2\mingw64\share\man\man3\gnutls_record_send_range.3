.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_send_range" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_send_range \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "ssize_t gnutls_record_send_range(gnutls_session_t " session ", const void * " data ", size_t " data_size ", const gnutls_range_st * " range ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "const void * data" 12
contains the data to send.
.IP "size_t data_size" 12
is the length of the data.
.IP "const gnutls_range_st * range" 12
is the range of lengths in which the real data length must be hidden.
.SH "DESCRIPTION"
This function operates like \fBgnutls_record_send()\fP but, while
\fBgnutls_record_send()\fP adds minimal padding to each TLS record,
this function uses the TLS extra\-padding feature to conceal the real
data size within the range of lengths provided.
Some TLS sessions do not support extra padding (e.g. stream ciphers in standard
TLS or SSL3 sessions). To know whether the current session supports extra
padding, and hence length hiding, use the \fBgnutls_record_can_use_length_hiding()\fP
function.
.SH "NOTE"
This function currently is limited to blocking sockets.
.SH "RETURNS"
The number of bytes sent (that is data_size in a successful invocation),
or a negative error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
