<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/block.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('block_8h.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">block.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>.xz Block handling  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__block.html">lzma_block</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Options for the Block and Block Header encoders and decoders.  <a href="structlzma__block.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:a9263c5b314592e658772e5eb83ffdd78" id="r_a9263c5b314592e658772e5eb83ffdd78"><td class="memItemLeft" align="right" valign="top"><a id="a9263c5b314592e658772e5eb83ffdd78" name="a9263c5b314592e658772e5eb83ffdd78"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_BLOCK_HEADER_SIZE_MIN</b>&#160;&#160;&#160;8</td></tr>
<tr class="separator:a9263c5b314592e658772e5eb83ffdd78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7a39189d0721c55313a401b63811e49" id="r_af7a39189d0721c55313a401b63811e49"><td class="memItemLeft" align="right" valign="top"><a id="af7a39189d0721c55313a401b63811e49" name="af7a39189d0721c55313a401b63811e49"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_BLOCK_HEADER_SIZE_MAX</b>&#160;&#160;&#160;1024</td></tr>
<tr class="separator:af7a39189d0721c55313a401b63811e49"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac025c940683a70f4c7f956bad814fd5f" id="r_ac025c940683a70f4c7f956bad814fd5f"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac025c940683a70f4c7f956bad814fd5f">lzma_block_header_size_decode</a>(b)</td></tr>
<tr class="memdesc:ac025c940683a70f4c7f956bad814fd5f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decode the Block Header Size field.  <br /></td></tr>
<tr class="separator:ac025c940683a70f4c7f956bad814fd5f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ae9b47abc872d0b02c2da9d3fa5a7dacd" id="r_ae9b47abc872d0b02c2da9d3fa5a7dacd"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae9b47abc872d0b02c2da9d3fa5a7dacd">lzma_block_header_size</a> (<a class="el" href="structlzma__block.html">lzma_block</a> *block) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:ae9b47abc872d0b02c2da9d3fa5a7dacd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate Block Header Size.  <br /></td></tr>
<tr class="separator:ae9b47abc872d0b02c2da9d3fa5a7dacd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0eedbd6331d5708ea963260e6f2a92d0" id="r_a0eedbd6331d5708ea963260e6f2a92d0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0eedbd6331d5708ea963260e6f2a92d0">lzma_block_header_encode</a> (const <a class="el" href="structlzma__block.html">lzma_block</a> *block, uint8_t *out) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a0eedbd6331d5708ea963260e6f2a92d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Encode Block Header.  <br /></td></tr>
<tr class="separator:a0eedbd6331d5708ea963260e6f2a92d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f5487c21a7b36a8bd17be36074d43c9" id="r_a7f5487c21a7b36a8bd17be36074d43c9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7f5487c21a7b36a8bd17be36074d43c9">lzma_block_header_decode</a> (<a class="el" href="structlzma__block.html">lzma_block</a> *block, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator, const uint8_t *in) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a7f5487c21a7b36a8bd17be36074d43c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decode Block Header.  <br /></td></tr>
<tr class="separator:a7f5487c21a7b36a8bd17be36074d43c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c3e102d76db06a07126a569abc6e2bc" id="r_a6c3e102d76db06a07126a569abc6e2bc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6c3e102d76db06a07126a569abc6e2bc">lzma_block_compressed_size</a> (<a class="el" href="structlzma__block.html">lzma_block</a> *block, <a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> unpadded_size) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a6c3e102d76db06a07126a569abc6e2bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Validate and set Compressed Size according to Unpadded Size.  <br /></td></tr>
<tr class="separator:a6c3e102d76db06a07126a569abc6e2bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a412d5605280fa29befae1b89e344bf30" id="r_a412d5605280fa29befae1b89e344bf30"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a412d5605280fa29befae1b89e344bf30">lzma_block_unpadded_size</a> (const <a class="el" href="structlzma__block.html">lzma_block</a> *block) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a412d5605280fa29befae1b89e344bf30"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate Unpadded Size.  <br /></td></tr>
<tr class="separator:a412d5605280fa29befae1b89e344bf30"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a694424f9dfdd5151e01debac1c501fa9" id="r_a694424f9dfdd5151e01debac1c501fa9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a694424f9dfdd5151e01debac1c501fa9">lzma_block_total_size</a> (const <a class="el" href="structlzma__block.html">lzma_block</a> *block) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a694424f9dfdd5151e01debac1c501fa9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate the total encoded size of a Block.  <br /></td></tr>
<tr class="separator:a694424f9dfdd5151e01debac1c501fa9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2218a49025a0b44f9a6f9d6d24359359" id="r_a2218a49025a0b44f9a6f9d6d24359359"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2218a49025a0b44f9a6f9d6d24359359">lzma_block_encoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, <a class="el" href="structlzma__block.html">lzma_block</a> *block) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a2218a49025a0b44f9a6f9d6d24359359"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize .xz Block encoder.  <br /></td></tr>
<tr class="separator:a2218a49025a0b44f9a6f9d6d24359359"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa92c73b2a228efe921fa2376aa7adc92" id="r_aa92c73b2a228efe921fa2376aa7adc92"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa92c73b2a228efe921fa2376aa7adc92">lzma_block_decoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, <a class="el" href="structlzma__block.html">lzma_block</a> *block) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:aa92c73b2a228efe921fa2376aa7adc92"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize .xz Block decoder.  <br /></td></tr>
<tr class="separator:aa92c73b2a228efe921fa2376aa7adc92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a58ff73e2572b529f48cc590bfffe5b4f" id="r_a58ff73e2572b529f48cc590bfffe5b4f"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a58ff73e2572b529f48cc590bfffe5b4f">lzma_block_buffer_bound</a> (size_t uncompressed_size) lzma_nothrow</td></tr>
<tr class="memdesc:a58ff73e2572b529f48cc590bfffe5b4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate maximum output size for single-call Block encoding.  <br /></td></tr>
<tr class="separator:a58ff73e2572b529f48cc590bfffe5b4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af415fa5130ab64e8760e9c39e856fa54" id="r_af415fa5130ab64e8760e9c39e856fa54"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af415fa5130ab64e8760e9c39e856fa54">lzma_block_buffer_encode</a> (<a class="el" href="structlzma__block.html">lzma_block</a> *block, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator, const uint8_t *in, size_t in_size, uint8_t *out, size_t *out_pos, size_t out_size) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:af415fa5130ab64e8760e9c39e856fa54"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-call .xz Block encoder.  <br /></td></tr>
<tr class="separator:af415fa5130ab64e8760e9c39e856fa54"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a260f634ccd5f54fb98f570d8d92d8c" id="r_a5a260f634ccd5f54fb98f570d8d92d8c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5a260f634ccd5f54fb98f570d8d92d8c">lzma_block_uncomp_encode</a> (<a class="el" href="structlzma__block.html">lzma_block</a> *block, const uint8_t *in, size_t in_size, uint8_t *out, size_t *out_pos, size_t out_size) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a5a260f634ccd5f54fb98f570d8d92d8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-call uncompressed .xz Block encoder.  <br /></td></tr>
<tr class="separator:a5a260f634ccd5f54fb98f570d8d92d8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0c6eb869d91b08f68648b1aa7a32ee9f" id="r_a0c6eb869d91b08f68648b1aa7a32ee9f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0c6eb869d91b08f68648b1aa7a32ee9f">lzma_block_buffer_decode</a> (<a class="el" href="structlzma__block.html">lzma_block</a> *block, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator, const uint8_t *in, size_t *in_pos, size_t in_size, uint8_t *out, size_t *out_pos, size_t out_size) lzma_nothrow</td></tr>
<tr class="memdesc:a0c6eb869d91b08f68648b1aa7a32ee9f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-call .xz Block decoder.  <br /></td></tr>
<tr class="separator:a0c6eb869d91b08f68648b1aa7a32ee9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>.xz Block handling </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead. </dd></dl>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="ac025c940683a70f4c7f956bad814fd5f" name="ac025c940683a70f4c7f956bad814fd5f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac025c940683a70f4c7f956bad814fd5f">&#9670;&#160;</a></span>lzma_block_header_size_decode</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define lzma_block_header_size_decode</td>
          <td>(</td>
          <td class="paramtype"></td>          <td class="paramname"><span class="paramname"><em>b</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line">(((uint32_t)(b) + 1) * 4)</div>
</div><!-- fragment -->
<p>Decode the Block Header Size field. </p>
<p>To decode Block Header using <a class="el" href="#a7f5487c21a7b36a8bd17be36074d43c9" title="Decode Block Header.">lzma_block_header_decode()</a>, the size of the Block Header has to be known and stored into <a class="el" href="structlzma__block.html#a6689c4f7524b2c05772a2d6151138610" title="Size of the Block Header field in bytes.">lzma_block.header_size</a>. The size can be calculated from the first byte of a Block using this macro. Note that if the first byte is 0x00, it indicates beginning of Index; use this macro only when the byte is not 0x00.</p>
<p>There is no encoding macro because <a class="el" href="#ae9b47abc872d0b02c2da9d3fa5a7dacd" title="Calculate Block Header Size.">lzma_block_header_size()</a> and <a class="el" href="#a0eedbd6331d5708ea963260e6f2a92d0" title="Encode Block Header.">lzma_block_header_encode()</a> should be used. </p>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="ae9b47abc872d0b02c2da9d3fa5a7dacd" name="ae9b47abc872d0b02c2da9d3fa5a7dacd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae9b47abc872d0b02c2da9d3fa5a7dacd">&#9670;&#160;</a></span>lzma_block_header_size()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_block_header_size </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__block.html">lzma_block</a> *</td>          <td class="paramname"><span class="paramname"><em>block</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculate Block Header Size. </p>
<p>Calculate the minimum size needed for the Block Header field using the settings specified in the <a class="el" href="structlzma__block.html" title="Options for the Block and Block Header encoders and decoders.">lzma_block</a> structure. Note that it is OK to increase the calculated header_size value as long as it is a multiple of four and doesn't exceed LZMA_BLOCK_HEADER_SIZE_MAX. Increasing header_size just means that <a class="el" href="#a0eedbd6331d5708ea963260e6f2a92d0" title="Encode Block Header.">lzma_block_header_encode()</a> will add Header Padding.</p>
<dl class="section note"><dt>Note</dt><dd>This doesn't check that all the options are valid i.e. this may return LZMA_OK even if <a class="el" href="#a0eedbd6331d5708ea963260e6f2a92d0" title="Encode Block Header.">lzma_block_header_encode()</a> or <a class="el" href="#a2218a49025a0b44f9a6f9d6d24359359" title="Initialize .xz Block encoder.">lzma_block_encoder()</a> would fail. If you want to validate the filter chain, consider using lzma_memlimit_encoder() which as a side-effect validates the filter chain.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">block</td><td>Block options</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Size calculated successfully and stored to block-&gt;header_size.</li>
<li>LZMA_OPTIONS_ERROR: Unsupported version, filters or filter options.</li>
<li>LZMA_PROG_ERROR: Invalid values like compressed_size == 0. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a0eedbd6331d5708ea963260e6f2a92d0" name="a0eedbd6331d5708ea963260e6f2a92d0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0eedbd6331d5708ea963260e6f2a92d0">&#9670;&#160;</a></span>lzma_block_header_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_block_header_encode </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__block.html">lzma_block</a> *</td>          <td class="paramname"><span class="paramname"><em>block</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>out</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Encode Block Header. </p>
<p>The caller must have calculated the size of the Block Header already with <a class="el" href="#ae9b47abc872d0b02c2da9d3fa5a7dacd" title="Calculate Block Header Size.">lzma_block_header_size()</a>. If a value larger than the one calculated by <a class="el" href="#ae9b47abc872d0b02c2da9d3fa5a7dacd" title="Calculate Block Header Size.">lzma_block_header_size()</a> is used, the Block Header will be padded to the specified size.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">block</td><td>Block options to be encoded. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer. This must be at least block-&gt;header_size bytes.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Encoding was successful. block-&gt;header_size bytes were written to output buffer.</li>
<li>LZMA_OPTIONS_ERROR: Invalid or unsupported options.</li>
<li>LZMA_PROG_ERROR: Invalid arguments, for example block-&gt;header_size is invalid or block-&gt;filters is NULL. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a7f5487c21a7b36a8bd17be36074d43c9" name="a7f5487c21a7b36a8bd17be36074d43c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7f5487c21a7b36a8bd17be36074d43c9">&#9670;&#160;</a></span>lzma_block_header_decode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_block_header_decode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__block.html">lzma_block</a> *</td>          <td class="paramname"><span class="paramname"><em>block</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Decode Block Header. </p>
<p>block-&gt;version should (usually) be set to the highest value supported by the application. If the application sets block-&gt;version to a value higher than supported by the current liblzma version, this function will downgrade block-&gt;version to the highest value supported by it. Thus one should check the value of block-&gt;version after calling this function if block-&gt;version was set to a non-zero value and the application doesn't otherwise know that the liblzma version being used is new enough to support the specified block-&gt;version.</p>
<p>The size of the Block Header must have already been decoded with <a class="el" href="#ac025c940683a70f4c7f956bad814fd5f" title="Decode the Block Header Size field.">lzma_block_header_size_decode()</a> macro and stored to block-&gt;header_size.</p>
<p>The integrity check type from Stream Header must have been stored to block-&gt;check.</p>
<p>block-&gt;filters must have been allocated, but they don't need to be initialized (possible existing filter options are not freed).</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">block</td><td>Destination for Block options </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() (and also free() if an error occurs). </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer. This must be at least block-&gt;header_size bytes.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Decoding was successful. block-&gt;header_size bytes were read from the input buffer.</li>
<li>LZMA_OPTIONS_ERROR: The Block Header specifies some unsupported options such as unsupported filters. This can happen also if block-&gt;version was set to a too low value compared to what would be required to properly represent the information stored in the Block Header.</li>
<li>LZMA_DATA_ERROR: Block Header is corrupt, for example, the CRC32 doesn't match.</li>
<li>LZMA_PROG_ERROR: Invalid arguments, for example block-&gt;header_size is invalid or block-&gt;filters is NULL. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a6c3e102d76db06a07126a569abc6e2bc" name="a6c3e102d76db06a07126a569abc6e2bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6c3e102d76db06a07126a569abc6e2bc">&#9670;&#160;</a></span>lzma_block_compressed_size()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_block_compressed_size </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__block.html">lzma_block</a> *</td>          <td class="paramname"><span class="paramname"><em>block</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a></td>          <td class="paramname"><span class="paramname"><em>unpadded_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Validate and set Compressed Size according to Unpadded Size. </p>
<p>Block Header stores Compressed Size, but Index has Unpadded Size. If the application has already parsed the Index and is now decoding Blocks, it can calculate Compressed Size from Unpadded Size. This function does exactly that with error checking:</p>
<ul>
<li>Compressed Size calculated from Unpadded Size must be positive integer, that is, Unpadded Size must be big enough that after Block Header and Check fields there's still at least one byte for Compressed Size.</li>
<li>If Compressed Size was present in Block Header, the new value calculated from Unpadded Size is compared against the value from Block Header.</li>
</ul>
<dl class="section note"><dt>Note</dt><dd>This function must be called <em>after</em> decoding the Block Header field so that it can properly validate Compressed Size if it was present in Block Header.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">block</td><td>Block options: block-&gt;header_size must already be set with <a class="el" href="#ae9b47abc872d0b02c2da9d3fa5a7dacd" title="Calculate Block Header Size.">lzma_block_header_size()</a>. </td></tr>
    <tr><td class="paramname">unpadded_size</td><td>Unpadded Size from the Index field in bytes</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: block-&gt;compressed_size was set successfully.</li>
<li>LZMA_DATA_ERROR: unpadded_size is too small compared to block-&gt;header_size and lzma_check_size(block-&gt;check).</li>
<li>LZMA_PROG_ERROR: Some values are invalid. For example, block-&gt;header_size must be a multiple of four and between 8 and 1024 inclusive. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a412d5605280fa29befae1b89e344bf30" name="a412d5605280fa29befae1b89e344bf30"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a412d5605280fa29befae1b89e344bf30">&#9670;&#160;</a></span>lzma_block_unpadded_size()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_block_unpadded_size </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__block.html">lzma_block</a> *</td>          <td class="paramname"><span class="paramname"><em>block</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculate Unpadded Size. </p>
<p>The Index field stores Unpadded Size and Uncompressed Size. The latter can be taken directly from the <a class="el" href="structlzma__block.html" title="Options for the Block and Block Header encoders and decoders.">lzma_block</a> structure after coding a Block, but Unpadded Size needs to be calculated from Block Header Size, Compressed Size, and size of the Check field. This is where this function is needed.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">block</td><td>Block options: block-&gt;header_size must already be set with <a class="el" href="#ae9b47abc872d0b02c2da9d3fa5a7dacd" title="Calculate Block Header Size.">lzma_block_header_size()</a>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Unpadded Size on success, or zero on error. </dd></dl>

</div>
</div>
<a id="a694424f9dfdd5151e01debac1c501fa9" name="a694424f9dfdd5151e01debac1c501fa9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a694424f9dfdd5151e01debac1c501fa9">&#9670;&#160;</a></span>lzma_block_total_size()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> lzma_block_total_size </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__block.html">lzma_block</a> *</td>          <td class="paramname"><span class="paramname"><em>block</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculate the total encoded size of a Block. </p>
<p>This is equivalent to <a class="el" href="#a412d5605280fa29befae1b89e344bf30" title="Calculate Unpadded Size.">lzma_block_unpadded_size()</a> except that the returned value includes the size of the Block Padding field.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">block</td><td>Block options: block-&gt;header_size must already be set with <a class="el" href="#ae9b47abc872d0b02c2da9d3fa5a7dacd" title="Calculate Block Header Size.">lzma_block_header_size()</a>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>On success, total encoded size of the Block. On error, zero is returned. </dd></dl>

</div>
</div>
<a id="a2218a49025a0b44f9a6f9d6d24359359" name="a2218a49025a0b44f9a6f9d6d24359359"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2218a49025a0b44f9a6f9d6d24359359">&#9670;&#160;</a></span>lzma_block_encoder()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_block_encoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structlzma__block.html">lzma_block</a> *</td>          <td class="paramname"><span class="paramname"><em>block</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initialize .xz Block encoder. </p>
<p>Valid actions for <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> are LZMA_RUN, LZMA_SYNC_FLUSH (only if the filter chain supports it), and LZMA_FINISH.</p>
<p>The Block encoder encodes the Block Data, Block Padding, and Check value. It does NOT encode the Block Header which can be encoded with <a class="el" href="#a0eedbd6331d5708ea963260e6f2a92d0" title="Encode Block Header.">lzma_block_header_encode()</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">block</td><td>Block options: block-&gt;version, block-&gt;check, and block-&gt;filters must have been initialized.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: All good, continue with <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>.</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_UNSUPPORTED_CHECK: block-&gt;check specifies a Check ID that is not supported by this build of liblzma. Initializing the encoder failed.</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="aa92c73b2a228efe921fa2376aa7adc92" name="aa92c73b2a228efe921fa2376aa7adc92"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa92c73b2a228efe921fa2376aa7adc92">&#9670;&#160;</a></span>lzma_block_decoder()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_block_decoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structlzma__block.html">lzma_block</a> *</td>          <td class="paramname"><span class="paramname"><em>block</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initialize .xz Block decoder. </p>
<p>Valid actions for <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> are LZMA_RUN and LZMA_FINISH. Using LZMA_FINISH is not required. It is supported only for convenience.</p>
<p>The Block decoder decodes the Block Data, Block Padding, and Check value. It does NOT decode the Block Header which can be decoded with <a class="el" href="#a7f5487c21a7b36a8bd17be36074d43c9" title="Decode Block Header.">lzma_block_header_decode()</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">block</td><td>Block options</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: All good, continue with <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>.</li>
<li>LZMA_PROG_ERROR</li>
<li>LZMA_MEM_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a58ff73e2572b529f48cc590bfffe5b4f" name="a58ff73e2572b529f48cc590bfffe5b4f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a58ff73e2572b529f48cc590bfffe5b4f">&#9670;&#160;</a></span>lzma_block_buffer_bound()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t lzma_block_buffer_bound </td>
          <td>(</td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>uncompressed_size</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculate maximum output size for single-call Block encoding. </p>
<p>This is equivalent to <a class="el" href="container_8h.html#a66d4366a47b8332bff2a512f44f5c45e" title="Calculate output buffer size for single-call Stream encoder.">lzma_stream_buffer_bound()</a> but for .xz Blocks. See the documentation of <a class="el" href="container_8h.html#a66d4366a47b8332bff2a512f44f5c45e" title="Calculate output buffer size for single-call Stream encoder.">lzma_stream_buffer_bound()</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">uncompressed_size</td><td>Size of the data to be encoded with the single-call Block encoder.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Maximum output size in bytes for single-call Block encoding. </dd></dl>

</div>
</div>
<a id="af415fa5130ab64e8760e9c39e856fa54" name="af415fa5130ab64e8760e9c39e856fa54"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af415fa5130ab64e8760e9c39e856fa54">&#9670;&#160;</a></span>lzma_block_buffer_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_block_buffer_encode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__block.html">lzma_block</a> *</td>          <td class="paramname"><span class="paramname"><em>block</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>in_size</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>out</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>out_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>out_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Single-call .xz Block encoder. </p>
<p>In contrast to the multi-call encoder initialized with <a class="el" href="#a2218a49025a0b44f9a6f9d6d24359359" title="Initialize .xz Block encoder.">lzma_block_encoder()</a>, this function encodes also the Block Header. This is required to make it possible to write appropriate Block Header also in case the data isn't compressible, and different filter chain has to be used to encode the data in uncompressed form using uncompressed chunks of the LZMA2 filter.</p>
<p>When the data isn't compressible, header_size, compressed_size, and uncompressed_size are set just like when the data was compressible, but it is possible that header_size is too small to hold the filter chain specified in block-&gt;filters, because that isn't necessarily the filter chain that was actually used to encode the data. <a class="el" href="#a412d5605280fa29befae1b89e344bf30" title="Calculate Unpadded Size.">lzma_block_unpadded_size()</a> still works normally, because it doesn't read the filters array.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">block</td><td>Block options: block-&gt;version, block-&gt;check, and block-&gt;filters must have been initialized. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_size</td><td>Size of the input buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out_pos</td><td>The next byte will be written to out[*out_pos]. *out_pos is updated only if encoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">out_size</td><td>Size of the out buffer; the first byte into which no data is written to is out[out_size].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Encoding was successful.</li>
<li>LZMA_BUF_ERROR: Not enough output buffer space.</li>
<li>LZMA_UNSUPPORTED_CHECK</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_DATA_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a5a260f634ccd5f54fb98f570d8d92d8c" name="a5a260f634ccd5f54fb98f570d8d92d8c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5a260f634ccd5f54fb98f570d8d92d8c">&#9670;&#160;</a></span>lzma_block_uncomp_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_block_uncomp_encode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__block.html">lzma_block</a> *</td>          <td class="paramname"><span class="paramname"><em>block</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>in_size</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>out</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>out_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>out_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Single-call uncompressed .xz Block encoder. </p>
<p>This is like <a class="el" href="#af415fa5130ab64e8760e9c39e856fa54" title="Single-call .xz Block encoder.">lzma_block_buffer_encode()</a> except this doesn't try to compress the data and instead encodes the data using LZMA2 uncompressed chunks. The required output buffer size can be determined with <a class="el" href="#a58ff73e2572b529f48cc590bfffe5b4f" title="Calculate maximum output size for single-call Block encoding.">lzma_block_buffer_bound()</a>.</p>
<p>Since the data won't be compressed, this function ignores block-&gt;filters. This function doesn't take <a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> because this function doesn't allocate any memory from the heap.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">block</td><td>Block options: block-&gt;version, block-&gt;check, and block-&gt;filters must have been initialized. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_size</td><td>Size of the input buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out_pos</td><td>The next byte will be written to out[*out_pos]. *out_pos is updated only if encoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">out_size</td><td>Size of the out buffer; the first byte into which no data is written to is out[out_size].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Encoding was successful.</li>
<li>LZMA_BUF_ERROR: Not enough output buffer space.</li>
<li>LZMA_UNSUPPORTED_CHECK</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_DATA_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a0c6eb869d91b08f68648b1aa7a32ee9f" name="a0c6eb869d91b08f68648b1aa7a32ee9f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0c6eb869d91b08f68648b1aa7a32ee9f">&#9670;&#160;</a></span>lzma_block_buffer_decode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_block_buffer_decode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__block.html">lzma_block</a> *</td>          <td class="paramname"><span class="paramname"><em>block</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>in_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>in_size</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>out</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>out_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>out_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Single-call .xz Block decoder. </p>
<p>This is single-call equivalent of <a class="el" href="#aa92c73b2a228efe921fa2376aa7adc92" title="Initialize .xz Block decoder.">lzma_block_decoder()</a>, and requires that the caller has already decoded Block Header and checked its memory usage.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">block</td><td>Block options </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_pos</td><td>The next byte will be read from in[*in_pos]. *in_pos is updated only if decoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_size</td><td>Size of the input buffer; the first byte that won't be read is in[in_size]. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out_pos</td><td>The next byte will be written to out[*out_pos]. *out_pos is updated only if encoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">out_size</td><td>Size of the out buffer; the first byte into which no data is written to is out[out_size].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Decoding was successful.</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_DATA_ERROR</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_BUF_ERROR: Output buffer was too small.</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li><li class="navelem"><a class="el" href="block_8h.html">block.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
