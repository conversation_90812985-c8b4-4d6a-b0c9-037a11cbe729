<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>life_cycle-kdf</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#State-Transition-Diagram">State Transition Diagram</a></li>
      <li><a href="#Formal-State-Transitions">Formal State Transitions</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>life_cycle-kdf - The KDF algorithm life-cycle</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>All key derivation functions (KDFs) and pseudo random functions (PRFs) go through a number of stages in their life-cycle:</p>

<dl>

<dt id="start">start</dt>
<dd>

<p>This state represents the KDF/PRF before it has been allocated. It is the starting state for any life-cycle transitions.</p>

</dd>
<dt id="newed">newed</dt>
<dd>

<p>This state represents the KDF/PRF after it has been allocated.</p>

</dd>
<dt id="deriving">deriving</dt>
<dd>

<p>This state represents the KDF/PRF when it is set up and capable of generating output.</p>

</dd>
<dt id="freed">freed</dt>
<dd>

<p>This state is entered when the KDF/PRF is freed. It is the terminal state for all life-cycle transitions.</p>

</dd>
</dl>

<h2 id="State-Transition-Diagram">State Transition Diagram</h2>

<p>The usual life-cycle of a KDF/PRF is illustrated:</p>

<img src="img/kdf.png">

<h2 id="Formal-State-Transitions">Formal State Transitions</h2>

<p>This section defines all of the legal state transitions. This is the canonical list.</p>

<table style="border:1px solid; border-collapse:collapse">
<tr><th style="border:1px solid" align="left">Function Call</th>
    <th style="border:1px solid" colspan="4">Current State</th></tr>
<tr><th style="border:1px solid"></th>
    <th style="border:1px solid" align="center">start</th>
    <th style="border:1px solid" align="center">newed</th>
    <th style="border:1px solid" align="center">deriving</th>
    <th style="border:1px solid" align="center">freed</th></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_new</th>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid"></td>
    <td style="border:1px solid"></td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_derive</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">deriving</td>
    <td style="border:1px solid" align="center">deriving</td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_free</th>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_reset</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_get_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">deriving</td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_set_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">deriving</td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_gettable_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">deriving</td>
    <td style="border:1px solid"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_KDF_CTX_settable_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">deriving</td>
    <td style="border:1px solid"></td></tr>
</table>

<h1 id="NOTES">NOTES</h1>

<p>At some point the EVP layer will begin enforcing the transitions described herein.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider-kdf.html">provider-kdf(7)</a>, <a href="../man3/EVP_KDF.html">EVP_KDF(3)</a>.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The provider KDF interface was introduced in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


