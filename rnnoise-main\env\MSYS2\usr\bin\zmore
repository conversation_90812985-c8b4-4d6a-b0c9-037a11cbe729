#!/bin/sh

# Copyright (C) 2001-2002, 2007, 2010-2025 Free Software Foundation, Inc.
# Copyright (C) 1992, 1993 <PERSON><PERSON><PERSON><PERSON>

# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.

# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.

version="zmore (gzip) 1.14
Copyright (C) 2025 Free Software Foundation, Inc.
This is free software.  You may redistribute copies of it under the terms of
the GNU General Public License <https://www.gnu.org/licenses/gpl.html>.
There is NO WARRANTY, to the extent permitted by law.

Written by <PERSON><PERSON><PERSON><PERSON>."

usage="Usage: $0 [OPTION]... [FILE]...
Like 'more', but operate on the uncompressed contents of any compressed FILEs.

Report bugs to <<EMAIL>>."

case $1 in
  --h*) printf '%s\n' "$usage"   || exit 1; exit;;
  --v*) printf '%s\n' "$version" || exit 1; exit;;
  --) shift;;
  -?*) printf >&2 '%s\n' "$0: $1: unknown option; try '$0 --help' for help"
       exit 1;;
esac

if test $# = 0; then
    if test -t 0; then
        printf >&2 '%s\n' "$0: missing operands; try '$0 --help' for help"
        exit 1
    fi
    set -- -
fi

for FILE
do
  test $# -lt 2 ||
    printf '::::::::::::::\n%s\n::::::::::::::\n' "$FILE" || break
  gzip -cdfq -- "$FILE"
done 2>&1 | eval ${PAGER-more}
