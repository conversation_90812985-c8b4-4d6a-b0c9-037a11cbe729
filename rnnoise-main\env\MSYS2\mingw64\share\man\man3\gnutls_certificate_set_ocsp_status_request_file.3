.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_ocsp_status_request_file" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_ocsp_status_request_file \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_set_ocsp_status_request_file(gnutls_certificate_credentials_t " sc ", const char * " response_file ", unsigned " idx ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t sc" 12
is a credentials structure.
.IP "const char * response_file" 12
a filename of the OCSP response
.IP "unsigned idx" 12
is a certificate index as returned by \fBgnutls_certificate_set_key()\fP and friends
.SH "DESCRIPTION"
This function loads the provided OCSP response. It will be
sent to the client if requests an OCSP certificate status for
the certificate chain specified by  \fIidx\fP .
.SH "NOTE"
the ability to set multiple OCSP responses per credential
structure via the index  \fIidx\fP was added in version 3.5.6. To keep
backwards compatibility, it requires using \fBgnutls_certificate_set_flags()\fP
with the \fBGNUTLS_CERTIFICATE_API_V2\fP flag to make the set certificate
functions return an index usable by this function.

This function can be called multiple times since GnuTLS 3.6.3
when multiple responses which apply to the chain are available.
If the response provided does not match any certificates present
in the chain, the code \fBGNUTLS_E_OCSP_MISMATCH_WITH_CERTS\fP is returned.
To revert to the previous behavior set the flag \fBGNUTLS_CERTIFICATE_SKIP_OCSP_RESPONSE_CHECK\fP
in the certificate credentials structure. In that case, only the
end\-certificate's OCSP response can be set.
If the response is already expired at the time of loading the code
\fBGNUTLS_E_EXPIRED\fP is returned.

To revert to the previous behavior of this function which does not return
any errors, set the flag \fBGNUTLS_CERTIFICATE_SKIP_OCSP_RESPONSE_CHECK\fP
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "SINCE"
3.1.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
