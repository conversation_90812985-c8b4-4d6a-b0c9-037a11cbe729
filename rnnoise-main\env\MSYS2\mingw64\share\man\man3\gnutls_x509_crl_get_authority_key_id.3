.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crl_get_authority_key_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crl_get_authority_key_id \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crl_get_authority_key_id(gnutls_x509_crl_t " crl ", void * " id ", size_t * " id_size ", unsigned int * " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crl_t crl" 12
should contain a \fBgnutls_x509_crl_t\fP type
.IP "void * id" 12
The place where the identifier will be copied
.IP "size_t * id_size" 12
Holds the size of the result field.
.IP "unsigned int * critical" 12
will be non\-zero if the extension is marked as critical
(may be null)
.SH "DESCRIPTION"
This function will return the CRL authority's key identifier.  This
is obtained by the X.509 Authority Key identifier extension field
(*********).  Note that this function 
only returns the keyIdentifier field of the extension and
\fBGNUTLS_E_X509_UNSUPPORTED_EXTENSION\fP, if the extension contains
the name and serial number of the certificate. In that case
\fBgnutls_x509_crl_get_authority_key_gn_serial()\fP may be used.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error code in case of an error.
.SH "SINCE"
2.8.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
