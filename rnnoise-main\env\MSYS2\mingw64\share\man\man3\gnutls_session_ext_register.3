.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_ext_register" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_ext_register \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_session_ext_register(gnutls_session_t " session ", const char * " name ", int " id ", gnutls_ext_parse_type_t " parse_point ", gnutls_ext_recv_func " recv_func ", gnutls_ext_send_func " send_func ", gnutls_ext_deinit_data_func " deinit_func ", gnutls_ext_pack_func " pack_func ", gnutls_ext_unpack_func " unpack_func ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
the session for which this extension will be set
.IP "const char * name" 12
the name of the extension to register
.IP "int id" 12
the numeric id of the extension
.IP "gnutls_ext_parse_type_t parse_point" 12
the parse type of the extension (see gnutls_ext_parse_type_t)
.IP "gnutls_ext_recv_func recv_func" 12
a function to receive the data
.IP "gnutls_ext_send_func send_func" 12
a function to send the data
.IP "gnutls_ext_deinit_data_func deinit_func" 12
a function deinitialize any private data
.IP "gnutls_ext_pack_func pack_func" 12
a function which serializes the extension's private data (used on session packing for resumption)
.IP "gnutls_ext_unpack_func unpack_func" 12
a function which will deserialize the extension's private data
.IP "unsigned flags" 12
must be zero or flags from \fBgnutls_ext_flags_t\fP
.SH "DESCRIPTION"
This function will register a new extension type. The extension will be
only usable within the registered session. If the extension type
is already registered then \fBGNUTLS_E_ALREADY_REGISTERED\fP will be returned,
unless the flag \fBGNUTLS_EXT_FLAG_OVERRIDE_INTERNAL\fP is specified. The latter
flag when specified can be used to override certain extensions introduced
after 3.6.0. It is expected to be used by applications which handle
custom extensions that are not currently supported in GnuTLS, but direct
support for them may be added in the future.

Each registered extension can store temporary data into the gnutls_session_t
structure using \fBgnutls_ext_set_data()\fP, and they can be retrieved using
\fBgnutls_ext_get_data()\fP.

The validity of the extension registered can be given by the appropriate flags
of \fBgnutls_ext_flags_t\fP. If no validity is given, then the registered extension
will be valid for client and TLS1.2 server hello (or encrypted extensions for TLS1.3).
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.5.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
