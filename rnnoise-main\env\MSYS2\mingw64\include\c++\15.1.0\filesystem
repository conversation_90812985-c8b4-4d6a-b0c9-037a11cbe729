// <filesystem> -*- C++ -*-

// Copyright (C) 2014-2025 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file include/filesystem
 *  This is a Standard C++ Library header.
 *  @ingroup filesystem
 */

#ifndef _GLIBCXX_FILESYSTEM
#define _GLIBCXX_FILESYSTEM 1

#ifdef _GLIBCXX_SYSHDR
#pragma GCC system_header
#endif

#include <bits/requires_hosted.h>

#define __glibcxx_want_filesystem
#include <bits/version.h>

#ifdef __cpp_lib_filesystem // C++ >= 17 && HOSTED

/**
 * @defgroup filesystem File System
 *
 * Utilities for performing operations on file systems and their components,
 * such as paths, regular files, and directories.
 *
 * @since C++17
 */

#include <bits/fs_fwd.h>
#include <bits/fs_path.h>
#include <bits/fs_dir.h>
#include <bits/fs_ops.h>

#endif // __cpp_lib_filesystem

#endif // _GLIBCXX_FILESYSTEM
