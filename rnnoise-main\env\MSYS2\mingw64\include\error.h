/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#define NO_ERROR 0
#define ERROR_INVALID_FUNCTION 1
#define ERROR_FILE_NOT_FOUND 2
#define ERROR_PATH_NOT_FOUND 3
#define ERROR_TOO_MANY_OPEN_FILES 4
#define ERROR_ACCESS_DENIED 5
#define ERROR_INVALID_HANDLE 6
#define ERROR_ARENA_TRASHED 7
#define ERROR_NOT_ENOUGH_MEMORY 8
#define ERROR_INVALID_BLOCK 9
#define ERROR_BAD_ENVIRONMENT 10
#define ERROR_BAD_FORMAT 11
#define ERROR_INVALID_ACCESS 12
#define ERROR_INVALID_DATA 13
#define ERROR_INVALID_DRIVE 15
#define ERROR_CURRENT_DIRECTORY 16
#define ERROR_NOT_SAME_DEVICE 17
#define ERROR_NO_MORE_FILES 18
#define ERROR_WRITE_PROTECT 19
#define ERROR_BAD_UNIT 20
#define ERROR_NOT_READY 21
#define ERROR_BAD_COMMAND 22
#define ERROR_CRC 23
#define ERROR_BAD_LENGTH 24
#define ERROR_SEEK 25
#define ERROR_NOT_DOS_DISK 26
#define ERROR_SECTOR_NOT_FOUND 27
#define ERROR_OUT_OF_PAPER 28
#define ERROR_WRITE_FAULT 29
#define ERROR_READ_FAULT 30
#define ERROR_GEN_FAILURE 31
#define ERROR_SHARING_VIOLATION 32
#define ERROR_LOCK_VIOLATION 33
#define ERROR_WRONG_DISK 34
#define ERROR_FCB_UNAVAILABLE 35
#define ERROR_SHARING_BUFFER_EXCEEDED 36
#define ERROR_NOT_SUPPORTED 50
#define ERROR_FILE_EXISTS 80
#define ERROR_DUP_FCB 81
#define ERROR_CANNOT_MAKE 82
#define ERROR_FAIL_I24 83
#define ERROR_OUT_OF_STRUCTURES 84
#define ERROR_ALREADY_ASSIGNED 85
#define ERROR_INVALID_PASSWORD 86
#define ERROR_INVALID_PARAMETER 87
#define ERROR_NET_WRITE_FAULT 88
#define ERROR_NO_PROC_SLOTS 89
#define ERROR_NOT_FROZEN 90
#define ERR_TSTOVFL 91
#define ERR_TSTDUP 92
#define ERROR_NO_ITEMS 93
#define ERROR_INTERRUPT 95
#define ERROR_TOO_MANY_SEMAPHORES 100
#define ERROR_EXCL_SEM_ALREADY_OWNED 101
#define ERROR_SEM_IS_SET 102
#define ERROR_TOO_MANY_SEM_REQUESTS 103
#define ERROR_INVALID_AT_INTERRUPT_TIME 104
#define ERROR_SEM_OWNER_DIED 105
#define ERROR_SEM_USER_LIMIT 106
#define ERROR_DISK_CHANGE 107
#define ERROR_DRIVE_LOCKED 108
#define ERROR_BROKEN_PIPE 109
#define ERROR_OPEN_FAILED 110
#define ERROR_BUFFER_OVERFLOW 111
#define ERROR_DISK_FULL 112
#define ERROR_NO_MORE_SEARCH_HANDLES 113
#define ERROR_INVALID_TARGET_HANDLE 114
#define ERROR_PROTECTION_VIOLATION 115
#define ERROR_VIOKBD_REQUEST 116
#define ERROR_INVALID_CATEGORY 117
#define ERROR_INVALID_VERIFY_SWITCH 118
#define ERROR_BAD_DRIVER_LEVEL 119
#define ERROR_CALL_NOT_IMPLEMENTED 120
#define ERROR_SEM_TIMEOUT 121
#define ERROR_INSUFFICIENT_BUFFER 122
#define ERROR_INVALID_NAME 123
#define ERROR_INVALID_LEVEL 124
#define ERROR_NO_VOLUME_LABEL 125
#define ERROR_MOD_NOT_FOUND 126
#define ERROR_PROC_NOT_FOUND 127
#define ERROR_WAIT_NO_CHILDREN 128
#define ERROR_CHILD_NOT_COMPLETE 129
#define ERROR_DIRECT_ACCESS_HANDLE 130
#define ERROR_NEGATIVE_SEEK 131
#define ERROR_SEEK_ON_DEVICE 132
#define ERROR_IS_JOIN_TARGET 133
#define ERROR_IS_JOINED 134
#define ERROR_IS_SUBSTED 135
#define ERROR_NOT_JOINED 136
#define ERROR_NOT_SUBSTED 137
#define ERROR_JOIN_TO_JOIN 138
#define ERROR_SUBST_TO_SUBST 139
#define ERROR_JOIN_TO_SUBST 140
#define ERROR_SUBST_TO_JOIN 141
#define ERROR_BUSY_DRIVE 142
#define ERROR_SAME_DRIVE 143
#define ERROR_DIR_NOT_ROOT 144
#define ERROR_DIR_NOT_EMPTY 145
#define ERROR_IS_SUBST_PATH 146
#define ERROR_IS_JOIN_PATH 147
#define ERROR_PATH_BUSY 148
#define ERROR_IS_SUBST_TARGET 149
#define ERROR_SYSTEM_TRACE 150
#define ERROR_INVALID_EVENT_COUNT 151
#define ERROR_TOO_MANY_MUXWAITERS 152
#define ERROR_INVALID_LIST_FORMAT 153
#define ERROR_LABEL_TOO_LONG 154
#define ERROR_TOO_MANY_TCBS 155
#define ERROR_SIGNAL_REFUSED 156
#define ERROR_DISCARDED 157
#define ERROR_NOT_LOCKED 158
#define ERROR_BAD_THREADID_ADDR 159
#define ERROR_BAD_ARGUMENTS 160
#define ERROR_BAD_PATHNAME 161
#define ERROR_SIGNAL_PENDING 162
#define ERROR_UNCERTAIN_MEDIA 163
#define ERROR_MAX_THRDS_REACHED 164
#define ERROR_MONITORS_NOT_SUPPORTED 165
#define ERROR_INVALID_SEGMENT_NUMBER 180
#define ERROR_INVALID_CALLGATE 181
#define ERROR_INVALID_ORDINAL 182
#define ERROR_ALREADY_EXISTS 183
#define ERROR_NO_CHILD_PROCESS 184
#define ERROR_CHILD_ALIVE_NOWAIT 185
#define ERROR_INVALID_FLAG_NUMBER 186
#define ERROR_SEM_NOT_FOUND 187
#define ERROR_INVALID_STARTING_CODESEG 188
#define ERROR_INVALID_STACKSEG 189
#define ERROR_INVALID_MODULETYPE 190
#define ERROR_INVALID_EXE_SIGNATURE 191
#define ERROR_EXE_MARKED_INVALID 192
#define ERROR_BAD_EXE_FORMAT 193
#define ERROR_ITERATED_DATA_EXCEEDS_64k 194
#define ERROR_INVALID_MINALLOCSIZE 195
#define ERROR_DYNLINK_FROM_INVALID_RING 196
#define ERROR_IOPL_NOT_ENABLED 197
#define ERROR_INVALID_SEGDPL 198
#define ERROR_AUTODATASEG_EXCEEDS_64k 199
#define ERROR_RING2SEG_MUST_BE_MOVABLE 200
#define ERROR_RELOC_CHAIN_XEEDS_SEGLIM 201
#define ERROR_INFLOOP_IN_RELOC_CHAIN 202
#define ERROR_ENVVAR_NOT_FOUND 203
#define ERROR_NOT_CURRENT_CTRY 204
#define ERROR_NO_SIGNAL_SENT 205
#define ERROR_FILENAME_EXCED_RANGE 206
#define ERROR_RING2_STACK_IN_USE 207
#define ERROR_META_EXPANSION_TOO_LONG 208
#define ERROR_INVALID_SIGNAL_NUMBER 209
#define ERROR_THREAD_1_INACTIVE 210
#define ERROR_INFO_NOT_AVAIL 211
#define ERROR_LOCKED 212
#define ERROR_BAD_DYNALINK 213
#define ERROR_TOO_MANY_MODULES 214
#define ERROR_NESTING_NOT_ALLOWED 215
#define ERROR_INVALID_TOKEN 315
#define ERROR_USER_DEFINED_BASE 0xF000
#define ERROR_I24_WRITE_PROTECT 0
#define ERROR_I24_BAD_UNIT 1
#define ERROR_I24_NOT_READY 2
#define ERROR_I24_BAD_COMMAND 3
#define ERROR_I24_CRC 4
#define ERROR_I24_BAD_LENGTH 5
#define ERROR_I24_SEEK 6
#define ERROR_I24_NOT_DOS_DISK 7
#define ERROR_I24_SECTOR_NOT_FOUND 8
#define ERROR_I24_OUT_OF_PAPER 9
#define ERROR_I24_WRITE_FAULT 0x0A
#define ERROR_I24_READ_FAULT 0x0B
#define ERROR_I24_GEN_FAILURE 0x0C
#define ERROR_I24_DISK_CHANGE 0x0D
#define ERROR_I24_WRONG_DISK 0x0F
#define ERROR_I24_UNCERTAIN_MEDIA 0x10
#define ERROR_I24_CHAR_CALL_INTERRUPTED 0x11
#define ERROR_I24_NO_MONITOR_SUPPORT 0x12
#define ERROR_I24_INVALID_PARAMETER 0x13
#define ALLOWED_FAIL 0x0001
#define ALLOWED_ABORT 0x0002
#define ALLOWED_RETRY 0x0004
#define ALLOWED_IGNORE 0x0008
#define I24_OPERATION 0x1
#define I24_AREA 0x6
#define I24_CLASS 0x80
#define ERRCLASS_OUTRES 1
#define ERRCLASS_TEMPSIT 2
#define ERRCLASS_AUTH 3
#define ERRCLASS_INTRN 4
#define ERRCLASS_HRDFAIL 5
#define ERRCLASS_SYSFAIL 6
#define ERRCLASS_APPERR 7
#define ERRCLASS_NOTFND 8
#define ERRCLASS_BADFMT 9
#define ERRCLASS_LOCKED 10
#define ERRCLASS_MEDIA 11
#define ERRCLASS_ALREADY 12
#define ERRCLASS_UNK 13
#define ERRCLASS_CANT 14
#define ERRCLASS_TIME 15
#define ERRACT_RETRY 1
#define ERRACT_DLYRET 2
#define ERRACT_USER 3
#define ERRACT_ABORT 4
#define ERRACT_PANIC 5
#define ERRACT_IGNORE 6
#define ERRACT_INTRET 7
#define ERRLOC_UNK 1
#define ERRLOC_DISK 2
#define ERRLOC_NET 3
#define ERRLOC_SERDEV 4
#define ERRLOC_MEM 5
#define TC_NORMAL 0
#define TC_HARDERR 1
#define TC_GP_TRAP 2
#define TC_SIGNAL 3
