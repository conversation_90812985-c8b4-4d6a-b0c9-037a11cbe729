.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hkdf_extract" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hkdf_extract \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_hkdf_extract(gnutls_mac_algorithm_t " mac ", const gnutls_datum_t * " key ", const gnutls_datum_t * " salt ", void * " output ");"
.SH ARGUMENTS
.IP "gnutls_mac_algorithm_t mac" 12
the mac algorithm used internally
.IP "const gnutls_datum_t * key" 12
the initial keying material
.IP "const gnutls_datum_t * salt" 12
the optional salt
.IP "void * output" 12
the output value of the extract operation
.SH "DESCRIPTION"
This function will derive a fixed\-size key using the HKDF\-Extract
function as defined in RFC 5869.
.SH "RETURNS"
Zero or a negative error code on error.
.SH "SINCE"
3.6.13
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
