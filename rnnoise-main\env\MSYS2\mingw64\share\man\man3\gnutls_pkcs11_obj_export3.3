.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_obj_export3" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_obj_export3 \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_obj_export3(gnutls_pkcs11_obj_t " obj ", gnutls_x509_crt_fmt_t " fmt ", gnutls_datum_t * " out ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_obj_t obj" 12
Holds the object
.IP "gnutls_x509_crt_fmt_t fmt" 12
The format of the exported data
.IP "gnutls_datum_t * out" 12
will contain the object data
.SH "DESCRIPTION"
This function will export the PKCS11 object data.  It is normal for
data to be inaccessible and in that case \fBGNUTLS_E_INVALID_REQUEST\fP
will be returned.

The output buffer is allocated using \fBgnutls_malloc()\fP.
.SH "RETURNS"
In case of failure a negative error code will be
returned, and \fBGNUTLS_E_SUCCESS\fP (0) on success.
.SH "SINCE"
3.2.7
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
