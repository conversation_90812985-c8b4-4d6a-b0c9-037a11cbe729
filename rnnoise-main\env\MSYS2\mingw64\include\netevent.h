/*
 * netevent.h
 *
 * Network events
 *
 * This file is part of the ReactOS PSDK package.
 *
 * Contributors:
 *   Created by <PERSON> <<EMAIL>>
 *
 * THIS SOFTWARE IS NOT COPYRIGHTED
 *
 * This source code is offered for use in the public domain. You may
 * use, modify or distribute it freely.
 *
 * This code is distributed in the hope that it will be useful but
 * WITHOUT ANY WARRANTY. ALL WARRANTIES, EXPRESS OR IMPLIED ARE HEREBY
 * DISCLAIMED. This includes but is not limited to warranties of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
 *
 */

#ifndef __NETEVENT_H
#define __NETEVENT_H

#ifdef __cplusplus
extern "C" {
#endif

#define COULD_NOT_VERIFY_VOLUMES                                 __MSABI_LONG(0xC00037E8)

#define DFS_CONNECTION_FAILURE                                   __MSABI_LONG(0x40003842)
#define DFS_ERROR_ACTIVEDIRECTORY_OFFLINE                        __MSABI_LONG(0xC00038BB)
#define DFS_ERROR_CLUSTERINFO_FAILED                             __MSABI_LONG(0xC00038B5)
#define DFS_ERROR_COMPUTERINFO_FAILED                            __MSABI_LONG(0xC00038B4)
#define DFS_ERROR_CREATEEVENT_FAILED                             __MSABI_LONG(0xC00038B3)
#define DFS_ERROR_CREATE_REPARSEPOINT_FAILURE                    __MSABI_LONG(0xC00038A7)
#define DFS_ERROR_CREATE_REPARSEPOINT_SUCCESS                    __MSABI_LONG(0x400038D2)
#define DFS_ERROR_CROSS_FOREST_TRUST_INFO_FAILED                 __MSABI_LONG(0xC00038D6)
#define DFS_ERROR_DCINFO_FAILED                                  __MSABI_LONG(0xC00038B6)
#define DFS_ERROR_DSCONNECT_FAILED                               __MSABI_LONG(0x800038BE)
#define DFS_ERROR_DUPLICATE_LINK                                 __MSABI_LONG(0xC00038D3)
#define DFS_ERROR_HANDLENAMESPACE_FAILED                         __MSABI_LONG(0xC00038B8)
#define DFS_ERROR_LINKS_OVERLAP                                  __MSABI_LONG(0xC00038D0)
#define DFS_ERROR_LINK_OVERLAP                                   __MSABI_LONG(0xC00038D1)
#define DFS_ERROR_MUTLIPLE_ROOTS_NOT_SUPPORTED                   __MSABI_LONG(0xC00038C7)
#define DFS_ERROR_NO_DFS_DATA                                    __MSABI_LONG(0xC00038C2)
#define DFS_ERROR_ON_ROOT                                        __MSABI_LONG(0x800038C6)
#define DFS_ERROR_OVERLAPPING_DIRECTORIES                        __MSABI_LONG(0xC00038A9)
#define DFS_ERROR_PREFIXTABLE_FAILED                             __MSABI_LONG(0xC00038B7)
#define DFS_ERROR_REFLECTIONENGINE_FAILED                        __MSABI_LONG(0xC00038BA)
#define DFS_ERROR_REGISTERSTORE_FAILED                           __MSABI_LONG(0xC00038B9)
#define DFS_ERROR_REMOVE_LINK_FAILED                             __MSABI_LONG(0xC00038CC)
#define DFS_ERROR_RESYNCHRONIZE_FAILED                           __MSABI_LONG(0xC00038CB)
#define DFS_ERROR_ROOTSYNCINIT_FAILED                            __MSABI_LONG(0xC00038B2)
#define DFS_ERROR_SECURITYINIT_FAILED                            __MSABI_LONG(0xC00038AF)
#define DFS_ERROR_SITECACHEINIT_FAILED                           __MSABI_LONG(0xC00038B1)
#define DFS_ERROR_SITESUPPOR_FAILED                              __MSABI_LONG(0xC00038BC)
#define DFS_ERROR_TARGET_LIST_INCORRECT                          __MSABI_LONG(0xC00038CF)
#define DFS_ERROR_THREADINIT_FAILED                              __MSABI_LONG(0xC00038B0)
#define DFS_ERROR_TOO_MANY_ERRORS                                __MSABI_LONG(0xC00038AD)
#define DFS_ERROR_TRUSTED_DOMAIN_INFO_FAILED                     __MSABI_LONG(0xC00038D4)
#define DFS_ERROR_UNSUPPORTED_FILESYSTEM                         __MSABI_LONG(0xC00038A8)
#define DFS_ERROR_WINSOCKINIT_FAILED                             __MSABI_LONG(0xC00038AE)
#define DFS_INFO_ACTIVEDIRECTORY_ONLINE                          __MSABI_LONG(0x400038AC)
#define DFS_INFO_CROSS_FOREST_TRUST_INFO_SUCCESS                 __MSABI_LONG(0x400038D7)
#define DFS_INFO_DOMAIN_REFERRAL_MIN_OVERFLOW                    __MSABI_LONG(0x400038C9)
#define DFS_INFO_DS_RECONNECTED                                  __MSABI_LONG(0x400038C1)
#define DFS_INFO_FINISH_BUILDING_NAMESPACE                       __MSABI_LONG(0x400038C5)
#define DFS_INFO_FINISH_INIT                                     __MSABI_LONG(0x400038C3)
#define DFS_INFO_RECONNECT_DATA                                  __MSABI_LONG(0x400038C4)
#define DFS_INFO_TRUSTED_DOMAIN_INFO_SUCCESS                     __MSABI_LONG(0x400038D5)
#define DFS_MAX_DNR_ATTEMPTS                                     __MSABI_LONG(0x40003845)
#define DFS_OPEN_FAILURE                                         __MSABI_LONG(0x40003847)
#define DFS_REFERRAL_FAILURE                                     __MSABI_LONG(0x40003843)
#define DFS_REFERRAL_REQUEST                                     __MSABI_LONG(0x400037EE)
#define DFS_REFERRAL_SUCCESS                                     __MSABI_LONG(0x40003844)
#define DFS_SPECIAL_REFERRAL_FAILURE                             __MSABI_LONG(0x40003846)
#define DFS_WARN_DOMAIN_REFERRAL_OVERFLOW                        __MSABI_LONG(0x800038C8)
#define DFS_WARN_INCOMPLETE_MOVE                                 __MSABI_LONG(0x800038CA)
#define DFS_WARN_METADATA_LINK_INFO_INVALID                      __MSABI_LONG(0x800038CE)
#define DFS_WARN_METADATA_LINK_TYPE_INCORRECT                    __MSABI_LONG(0x800038CD)

#define EVENT_BAD_ACCOUNT_NAME                                   __MSABI_LONG(0xC0001B60)
#define EVENT_BAD_SERVICE_STATE                                  __MSABI_LONG(0xC0001B68)
#define EVENT_BOOT_SYSTEM_DRIVERS_FAILED                         __MSABI_LONG(0xC0001B72)
#define EVENT_BOWSER_CANT_READ_REGISTRY                          __MSABI_LONG(0x40001F5D)
#define EVENT_BOWSER_ELECTION_RECEIVED                           __MSABI_LONG(0x00001F4C)
#define EVENT_BOWSER_ELECTION_SENT_FIND_MASTER_FAILED            __MSABI_LONG(0x40001F4E)
#define EVENT_BOWSER_ELECTION_SENT_GETBLIST_FAILED               __MSABI_LONG(0x40001F4D)
#define EVENT_BOWSER_GETBROWSERLIST_THRESHOLD_EXCEEDED           __MSABI_LONG(0x40001F5F)
#define EVENT_BOWSER_ILLEGAL_DATAGRAM                            __MSABI_LONG(0x80001F46)
#define EVENT_BOWSER_ILLEGAL_DATAGRAM_THRESHOLD                  __MSABI_LONG(0xC0001F50)
#define EVENT_BOWSER_MAILSLOT_DATAGRAM_THRESHOLD_EXCEEDED        __MSABI_LONG(0x40001F5E)
#define EVENT_BOWSER_NAME_CONVERSION_FAILED                      __MSABI_LONG(0xC0001F4A)
#define EVENT_BOWSER_NON_MASTER_MASTER_ANNOUNCE                  __MSABI_LONG(0x80001F45)
#define EVENT_BOWSER_NON_PDC_WON_ELECTION                        __MSABI_LONG(0x40001F5C)
#define EVENT_BOWSER_OLD_BACKUP_FOUND                            __MSABI_LONG(0x40001F58)
#define EVENT_BOWSER_OTHER_MASTER_ON_NET                         __MSABI_LONG(0xC0001F43)
#define EVENT_BOWSER_PDC_LOST_ELECTION                           __MSABI_LONG(0x40001F5B)
#define EVENT_BOWSER_PROMOTED_WHILE_ALREADY_MASTER               __MSABI_LONG(0x80001F44)
#define EVENT_BRIDGE_ADAPTER_BIND_FAILED                         __MSABI_LONG(0xC0003970)
#define EVENT_BRIDGE_ADAPTER_FILTER_FAILED                       __MSABI_LONG(0xC000396E)
#define EVENT_BRIDGE_ADAPTER_LINK_SPEED_QUERY_FAILED             __MSABI_LONG(0xC000396C)
#define EVENT_BRIDGE_ADAPTER_MAC_ADDR_QUERY_FAILED               __MSABI_LONG(0xC000396D)
#define EVENT_BRIDGE_ADAPTER_NAME_QUERY_FAILED                   __MSABI_LONG(0xC000396F)
#define EVENT_BRIDGE_BUFFER_POOL_CREATION_FAILED                 __MSABI_LONG(0xC0003912)
#define EVENT_BRIDGE_DEVICE_CREATION_FAILED                      __MSABI_LONG(0xC000390B)
#define EVENT_BRIDGE_ETHERNET_NOT_OFFERED                        __MSABI_LONG(0xC000390E)
#define EVENT_BRIDGE_INIT_MALLOC_FAILED                          __MSABI_LONG(0xC0003913)
#define EVENT_BRIDGE_MINIPORT_INIT_FAILED                        __MSABI_LONG(0xC000390D)
#define EVENT_BRIDGE_MINIPORT_REGISTER_FAILED                    __MSABI_LONG(0xC000390A)
#define EVENT_BRIDGE_MINIPROT_DEVNAME_MISSING                    __MSABI_LONG(0xC0003909)
#define EVENT_BRIDGE_NO_BRIDGE_MAC_ADDR                          __MSABI_LONG(0xC000390C)
#define EVENT_BRIDGE_PACKET_POOL_CREATION_FAILED                 __MSABI_LONG(0xC0003911)
#define EVENT_BRIDGE_PROTOCOL_REGISTER_FAILED                    __MSABI_LONG(0xC0003908)
#define EVENT_BRIDGE_THREAD_CREATION_FAILED                      __MSABI_LONG(0xC000390F)
#define EVENT_BRIDGE_THREAD_REF_FAILED                           __MSABI_LONG(0xC0003910)
#define EVENT_BROWSER_BACKUP_STOPPED                             __MSABI_LONG(0xC0001F60)
#define EVENT_BROWSER_DEPENDANT_SERVICE_FAILED                   __MSABI_LONG(0xC0001F51)
#define EVENT_BROWSER_DOMAIN_LIST_FAILED                         __MSABI_LONG(0x80001F56)
#define EVENT_BROWSER_DOMAIN_LIST_RETRIEVED                      __MSABI_LONG(0x00001F5A)
#define EVENT_BROWSER_ELECTION_SENT_LANMAN_NT_STARTED            __MSABI_LONG(0x40001F4F)
#define EVENT_BROWSER_ELECTION_SENT_LANMAN_NT_STOPPED            __MSABI_LONG(0x40001F61)
#define EVENT_BROWSER_ELECTION_SENT_ROLE_CHANGED                 __MSABI_LONG(0x40001F63)
#define EVENT_BROWSER_GETBLIST_RECEIVED_NOT_MASTER               __MSABI_LONG(0xC0001F62)
#define EVENT_BROWSER_ILLEGAL_CONFIG                             __MSABI_LONG(0x80001F57)
#define EVENT_BROWSER_MASTER_PROMOTION_FAILED                    __MSABI_LONG(0xC0001F49)
#define EVENT_BROWSER_MASTER_PROMOTION_FAILED_NO_MASTER          __MSABI_LONG(0xC0001F54)
#define EVENT_BROWSER_MASTER_PROMOTION_FAILED_STOPPING           __MSABI_LONG(0xC0001F53)
#define EVENT_BROWSER_NOT_STARTED_IPX_CONFIG_MISMATCH            __MSABI_LONG(0xC0001F64)
#define EVENT_BROWSER_OTHERDOMAIN_ADD_FAILED                     __MSABI_LONG(0xC0001F4B)
#define EVENT_BROWSER_ROLE_CHANGE_FAILED                         __MSABI_LONG(0xC0001F48)
#define EVENT_BROWSER_SERVER_LIST_FAILED                         __MSABI_LONG(0x80001F55)
#define EVENT_BROWSER_SERVER_LIST_RETRIEVED                      __MSABI_LONG(0x00001F59)
#define EVENT_BROWSER_STATUS_BITS_UPDATE_FAILED                  __MSABI_LONG(0xC0001F47)
#define EVENT_CALL_TO_FUNCTION_FAILED                            __MSABI_LONG(0xC0001B5D)
#define EVENT_CALL_TO_FUNCTION_FAILED_II                         __MSABI_LONG(0xC0001B5E)
#define EVENT_CIRCULAR_DEPENDENCY_AUTO                           __MSABI_LONG(0xC0001B6A)
#define EVENT_CIRCULAR_DEPENDENCY_DEMAND                         __MSABI_LONG(0xC0001B69)
#define EVENT_COMMAND_NOT_INTERACTIVE                            __MSABI_LONG(0xC0001EDC)
#define EVENT_COMMAND_START_FAILED                               __MSABI_LONG(0xC0001EDD)
#define EVENT_CONNECTION_TIMEOUT                                 __MSABI_LONG(0xC0001B61)
#define EVENT_ComputerNameChange                                 __MSABI_LONG(0x8000177B)
#define EVENT_DAV_REDIR_DELAYED_WRITE_FAILED                     __MSABI_LONG(0x800039D0)
#define EVENT_DCOM_ASSERTION_FAILURE                             __MSABI_LONG(0xC000271C)
#define EVENT_DCOM_COMPLUS_DISABLED                              __MSABI_LONG(0xC000271E)
#define EVENT_DCOM_INVALID_ENDPOINT_DATA                         __MSABI_LONG(0xC000271D)
#define EVENT_DEPEND_ON_LATER_GROUP                              __MSABI_LONG(0xC0001B6C)
#define EVENT_DEPEND_ON_LATER_SERVICE                            __MSABI_LONG(0xC0001B6B)
#define EVENT_DNSAPI_DEREGISTRATION_FAILED_NOTSUPP               __MSABI_LONG(0x80002BAE)
#define EVENT_DNSAPI_DEREGISTRATION_FAILED_NOTSUPP_PRIMARY_DN    __MSABI_LONG(0x80002BBA)
#define EVENT_DNSAPI_DEREGISTRATION_FAILED_OTHER                 __MSABI_LONG(0x80002BB1)
#define EVENT_DNSAPI_DEREGISTRATION_FAILED_OTHER_PRIMARY_DN      __MSABI_LONG(0x80002BBD)
#define EVENT_DNSAPI_DEREGISTRATION_FAILED_REFUSED               __MSABI_LONG(0x80002BAF)
#define EVENT_DNSAPI_DEREGISTRATION_FAILED_REFUSED_PRIMARY_DN    __MSABI_LONG(0x80002BBB)
#define EVENT_DNSAPI_DEREGISTRATION_FAILED_SECURITY              __MSABI_LONG(0x80002BB0)
#define EVENT_DNSAPI_DEREGISTRATION_FAILED_SECURITY_PRIMARY_DN   __MSABI_LONG(0x80002BBC)
#define EVENT_DNSAPI_DEREGISTRATION_FAILED_SERVERFAIL            __MSABI_LONG(0x80002BAD)
#define EVENT_DNSAPI_DEREGISTRATION_FAILED_SERVERFAIL_PRIMARY_DN __MSABI_LONG(0x80002BB9)
#define EVENT_DNSAPI_DEREGISTRATION_FAILED_TIMEOUT               __MSABI_LONG(0x80002BAC)
#define EVENT_DNSAPI_DEREGISTRATION_FAILED_TIMEOUT_PRIMARY_DN    __MSABI_LONG(0x80002BB8)
#define EVENT_DNSAPI_PTR_DEREGISTRATION_FAILED_NOTSUPP           __MSABI_LONG(0x80002BB4)
#define EVENT_DNSAPI_PTR_DEREGISTRATION_FAILED_OTHER             __MSABI_LONG(0x80002BB7)
#define EVENT_DNSAPI_PTR_DEREGISTRATION_FAILED_REFUSED           __MSABI_LONG(0x80002BB5)
#define EVENT_DNSAPI_PTR_DEREGISTRATION_FAILED_SECURITY          __MSABI_LONG(0x80002BB6)
#define EVENT_DNSAPI_PTR_DEREGISTRATION_FAILED_SERVERFAIL        __MSABI_LONG(0x80002BB3)
#define EVENT_DNSAPI_PTR_DEREGISTRATION_FAILED_TIMEOUT           __MSABI_LONG(0x80002BB2)
#define EVENT_DNSAPI_PTR_REGISTRATION_FAILED_NOTSUPP             __MSABI_LONG(0x80002B96)
#define EVENT_DNSAPI_PTR_REGISTRATION_FAILED_OTHER               __MSABI_LONG(0x80002B99)
#define EVENT_DNSAPI_PTR_REGISTRATION_FAILED_REFUSED             __MSABI_LONG(0x80002B97)
#define EVENT_DNSAPI_PTR_REGISTRATION_FAILED_SECURITY            __MSABI_LONG(0x80002B98)
#define EVENT_DNSAPI_PTR_REGISTRATION_FAILED_SERVERFAIL          __MSABI_LONG(0x80002B95)
#define EVENT_DNSAPI_PTR_REGISTRATION_FAILED_TIMEOUT             __MSABI_LONG(0x80002B94)
#define EVENT_DNSAPI_REGISTERED_ADAPTER                          __MSABI_LONG(0x40002BC0)
#define EVENT_DNSAPI_REGISTERED_ADAPTER_PRIMARY_DN               __MSABI_LONG(0x40002BC2)
#define EVENT_DNSAPI_REGISTERED_PTR                              __MSABI_LONG(0x40002BC1)
#define EVENT_DNSAPI_REGISTRATION_FAILED_NOTSUPP                 __MSABI_LONG(0x80002B90)
#define EVENT_DNSAPI_REGISTRATION_FAILED_NOTSUPP_PRIMARY_DN      __MSABI_LONG(0x80002B9C)
#define EVENT_DNSAPI_REGISTRATION_FAILED_OTHER                   __MSABI_LONG(0x80002B93)
#define EVENT_DNSAPI_REGISTRATION_FAILED_OTHER_PRIMARY_DN        __MSABI_LONG(0x80002B9F)
#define EVENT_DNSAPI_REGISTRATION_FAILED_REFUSED                 __MSABI_LONG(0x80002B91)
#define EVENT_DNSAPI_REGISTRATION_FAILED_REFUSED_PRIMARY_DN      __MSABI_LONG(0x80002B9D)
#define EVENT_DNSAPI_REGISTRATION_FAILED_SECURITY                __MSABI_LONG(0x80002B92)
#define EVENT_DNSAPI_REGISTRATION_FAILED_SECURITY_PRIMARY_DN     __MSABI_LONG(0x80002B9E)
#define EVENT_DNSAPI_REGISTRATION_FAILED_SERVERFAIL              __MSABI_LONG(0x80002B8F)
#define EVENT_DNSAPI_REGISTRATION_FAILED_SERVERFAIL_PRIMARY_DN   __MSABI_LONG(0x80002B9B)
#define EVENT_DNSAPI_REGISTRATION_FAILED_TIMEOUT                 __MSABI_LONG(0x80002B8E)
#define EVENT_DNSAPI_REGISTRATION_FAILED_TIMEOUT_PRIMARY_DN      __MSABI_LONG(0x80002B9A)
#define EVENT_DNSDomainNameChange                                __MSABI_LONG(0x8000177C)
#define EVENT_DNS_CACHE_NETWORK_PERF_WARNING                     __MSABI_LONG(0x80002B2A)
#define EVENT_DNS_CACHE_START_FAILURE_LOW_MEMORY                 __MSABI_LONG(0xC0002AFF)
#define EVENT_DNS_CACHE_START_FAILURE_NO_CONTROL                 __MSABI_LONG(0xC0002AFA)
#define EVENT_DNS_CACHE_START_FAILURE_NO_DLL                     __MSABI_LONG(0xC0002AF8)
#define EVENT_DNS_CACHE_START_FAILURE_NO_DONE_EVENT              __MSABI_LONG(0xC0002AFB)
#define EVENT_DNS_CACHE_START_FAILURE_NO_ENTRY                   __MSABI_LONG(0xC0002AF9)
#define EVENT_DNS_CACHE_START_FAILURE_NO_RPC                     __MSABI_LONG(0xC0002AFC)
#define EVENT_DNS_CACHE_START_FAILURE_NO_SHUTDOWN_NOTIFY         __MSABI_LONG(0xC0002AFD)
#define EVENT_DNS_CACHE_START_FAILURE_NO_UPDATE                  __MSABI_LONG(0xC0002AFE)
#define EVENT_DNS_CACHE_UNABLE_TO_REACH_SERVER_WARNING           __MSABI_LONG(0x80002B2B)
#define EVENT_EQOS_ERROR_MACHINE_POLICY_KEYNAME_SIZE_ZERO        __MSABI_LONG(0xC0004142)
#define EVENT_EQOS_ERROR_MACHINE_POLICY_KEYNAME_TOO_LONG         __MSABI_LONG(0xC0004140)
#define EVENT_EQOS_ERROR_MACHINE_POLICY_REFERESH                 __MSABI_LONG(0xC000413C)
#define EVENT_EQOS_ERROR_OPENING_MACHINE_POLICY_ROOT_KEY         __MSABI_LONG(0xC000413E)
#define EVENT_EQOS_ERROR_OPENING_MACHINE_POLICY_SUBKEY           __MSABI_LONG(0xC0004144)
#define EVENT_EQOS_ERROR_OPENING_USER_POLICY_ROOT_KEY            __MSABI_LONG(0xC000413F)
#define EVENT_EQOS_ERROR_OPENING_USER_POLICY_SUBKEY              __MSABI_LONG(0xC0004145)
#define EVENT_EQOS_ERROR_PROCESSING_MACHINE_POLICY_FIELD         __MSABI_LONG(0xC0004146)
#define EVENT_EQOS_ERROR_PROCESSING_USER_POLICY_FIELD            __MSABI_LONG(0xC0004147)
#define EVENT_EQOS_ERROR_SETTING_APP_MARKING                     __MSABI_LONG(0xC0004149)
#define EVENT_EQOS_ERROR_SETTING_TCP_AUTOTUNING                  __MSABI_LONG(0xC0004148)
#define EVENT_EQOS_ERROR_USER_POLICY_KEYNAME_SIZE_ZERO           __MSABI_LONG(0xC0004143)
#define EVENT_EQOS_ERROR_USER_POLICY_KEYNAME_TOO_LONG            __MSABI_LONG(0xC0004141)
#define EVENT_EQOS_ERROR_USER_POLICY_REFERESH                    __MSABI_LONG(0xC000413D)
#define EVENT_EQOS_INFO_APP_MARKING_ALLOWED                      __MSABI_LONG(0x4000407F)
#define EVENT_EQOS_INFO_APP_MARKING_IGNORED                      __MSABI_LONG(0x4000407E)
#define EVENT_EQOS_INFO_APP_MARKING_NOT_CONFIGURED               __MSABI_LONG(0x4000407D)
#define EVENT_EQOS_INFO_LOCAL_SETTING_DONT_USE_NLA               __MSABI_LONG(0x40004080)
#define EVENT_EQOS_INFO_MACHINE_POLICY_REFRESH_NO_CHANGE         __MSABI_LONG(0x40004074)
#define EVENT_EQOS_INFO_MACHINE_POLICY_REFRESH_WITH_CHANGE       __MSABI_LONG(0x40004075)
#define EVENT_EQOS_INFO_TCP_AUTOTUNING_HIGHLY_RESTRICTED         __MSABI_LONG(0x4000407A)
#define EVENT_EQOS_INFO_TCP_AUTOTUNING_NORMAL                    __MSABI_LONG(0x4000407C)
#define EVENT_EQOS_INFO_TCP_AUTOTUNING_NOT_CONFIGURED            __MSABI_LONG(0x40004078)
#define EVENT_EQOS_INFO_TCP_AUTOTUNING_OFF                       __MSABI_LONG(0x40004079)
#define EVENT_EQOS_INFO_TCP_AUTOTUNING_RESTRICTED                __MSABI_LONG(0x4000407B)
#define EVENT_EQOS_INFO_USER_POLICY_REFRESH_NO_CHANGE            __MSABI_LONG(0x40004076)
#define EVENT_EQOS_INFO_USER_POLICY_REFRESH_WITH_CHANGE          __MSABI_LONG(0x40004077)
#define EVENT_EQOS_URL_QOS_APPLICATION_CONFLICT                  __MSABI_LONG(0x40004081)
#define EVENT_EQOS_WARNING_MACHINE_POLICY_CONFLICT               __MSABI_LONG(0x800040E0)
#define EVENT_EQOS_WARNING_MACHINE_POLICY_NO_FULLPATH_APPNAME    __MSABI_LONG(0x800040E2)
#define EVENT_EQOS_WARNING_MACHINE_POLICY_PROFILE_NOT_SPECIFIED  __MSABI_LONG(0x800040DC)
#define EVENT_EQOS_WARNING_MACHINE_POLICY_QUOTA_EXCEEDED         __MSABI_LONG(0x800040DE)
#define EVENT_EQOS_WARNING_MACHINE_POLICY_VERSION                __MSABI_LONG(0x800040DA)
#define EVENT_EQOS_WARNING_TEST_1                                __MSABI_LONG(0x800040D8)
#define EVENT_EQOS_WARNING_TEST_2                                __MSABI_LONG(0x800040D9)
#define EVENT_EQOS_WARNING_USER_POLICY_CONFLICT                  __MSABI_LONG(0x800040E1)
#define EVENT_EQOS_WARNING_USER_POLICY_NO_FULLPATH_APPNAME       __MSABI_LONG(0x800040E3)
#define EVENT_EQOS_WARNING_USER_POLICY_PROFILE_NOT_SPECIFIED     __MSABI_LONG(0x800040DD)
#define EVENT_EQOS_WARNING_USER_POLICY_QUOTA_EXCEEDED            __MSABI_LONG(0x800040DF)
#define EVENT_EQOS_WARNING_USER_POLICY_VERSION                   __MSABI_LONG(0x800040DB)
#define EVENT_EventLogProductInfo                                __MSABI_LONG(0x80001779)
#define EVENT_EventlogAbnormalShutdown                           __MSABI_LONG(0x80001778)
#define EVENT_EventlogStarted                                    __MSABI_LONG(0x80001775)
#define EVENT_EventlogStopped                                    __MSABI_LONG(0x80001776)
#define EVENT_EventlogUptime                                     __MSABI_LONG(0x8000177D)
#define EVENT_FIRST_LOGON_FAILED                                 __MSABI_LONG(0xC0001B65)
#define EVENT_FIRST_LOGON_FAILED_II                              __MSABI_LONG(0xC0001B7E)
#define EVENT_FRS_ACCESS_CHECKS_DISABLED                         __MSABI_LONG(0x800034CD)
#define EVENT_FRS_ACCESS_CHECKS_FAILED_UNKNOWN                   __MSABI_LONG(0xC00034CF)
#define EVENT_FRS_ACCESS_CHECKS_FAILED_USER                      __MSABI_LONG(0x800034CE)
#define EVENT_FRS_ASSERT                                         __MSABI_LONG(0xC00034C2)
#define EVENT_FRS_BAD_REG_DATA                                   __MSABI_LONG(0x800034EB)
#define EVENT_FRS_CANNOT_COMMUNICATE                             __MSABI_LONG(0xC00034C6)
#define EVENT_FRS_CANNOT_CREATE_UUID                             __MSABI_LONG(0xC00034D4)
#define EVENT_FRS_CANNOT_START_BACKUP_RESTORE_IN_PROGRESS        __MSABI_LONG(0xC00034D1)
#define EVENT_FRS_CANT_OPEN_PREINSTALL                           __MSABI_LONG(0xC00034EF)
#define EVENT_FRS_CANT_OPEN_STAGE                                __MSABI_LONG(0xC00034EE)
#define EVENT_FRS_DATABASE_SPACE                                 __MSABI_LONG(0xC00034C7)
#define EVENT_FRS_DISK_WRITE_CACHE_ENABLED                       __MSABI_LONG(0x800034C8)
#define EVENT_FRS_DS_POLL_ERROR_SUMMARY                          __MSABI_LONG(0x800034FA)
#define EVENT_FRS_DUPLICATE_IN_CXTION                            __MSABI_LONG(0xC00034F6)
#define EVENT_FRS_DUPLICATE_IN_CXTION_SYSVOL                     __MSABI_LONG(0xC00034F5)
#define EVENT_FRS_ERROR                                          __MSABI_LONG(0xC00034BC)
#define EVENT_FRS_ERROR_REPLICA_SET_DELETED                      __MSABI_LONG(0x800034F8)
#define EVENT_FRS_HUGE_FILE                                      __MSABI_LONG(0x800034D3)
#define EVENT_FRS_IN_ERROR_STATE                                 __MSABI_LONG(0xC00034F3)
#define EVENT_FRS_JET_1414                                       __MSABI_LONG(0xC00034C9)
#define EVENT_FRS_JOIN_FAIL_TIME_SKEW                            __MSABI_LONG(0xC00034EC)
#define EVENT_FRS_LONG_JOIN                                      __MSABI_LONG(0x800034C4)
#define EVENT_FRS_LONG_JOIN_DONE                                 __MSABI_LONG(0x800034C5)
#define EVENT_FRS_MOVED_PREEXISTING                              __MSABI_LONG(0x800034D0)
#define EVENT_FRS_NO_DNS_ATTRIBUTE                               __MSABI_LONG(0x800034D5)
#define EVENT_FRS_NO_SID                                         __MSABI_LONG(0xC00034D6)
#define EVENT_FRS_OVERLAPS_LOGGING                               __MSABI_LONG(0xC00034E5)
#define EVENT_FRS_OVERLAPS_OTHER_STAGE                           __MSABI_LONG(0xC00034E9)
#define EVENT_FRS_OVERLAPS_ROOT                                  __MSABI_LONG(0xC00034E8)
#define EVENT_FRS_OVERLAPS_STAGE                                 __MSABI_LONG(0xC00034E7)
#define EVENT_FRS_OVERLAPS_WORKING                               __MSABI_LONG(0xC00034E6)
#define EVENT_FRS_PREPARE_ROOT_FAILED                            __MSABI_LONG(0xC00034EA)
#define EVENT_FRS_REPLICA_IN_JRNL_WRAP_ERROR                     __MSABI_LONG(0xC00034F9)
#define EVENT_FRS_REPLICA_NO_ROOT_CHANGE                         __MSABI_LONG(0xC00034F4)
#define EVENT_FRS_REPLICA_SET_CREATE_FAIL                        __MSABI_LONG(0xC00034F0)
#define EVENT_FRS_REPLICA_SET_CREATE_OK                          __MSABI_LONG(0x400034F1)
#define EVENT_FRS_REPLICA_SET_CXTIONS                            __MSABI_LONG(0x400034F2)
#define EVENT_FRS_RMTCO_TIME_SKEW                                __MSABI_LONG(0xC00034ED)
#define EVENT_FRS_ROOT_HAS_MOVED                                 __MSABI_LONG(0xC00034F7)
#define EVENT_FRS_ROOT_NOT_VALID                                 __MSABI_LONG(0xC00034E3)
#define EVENT_FRS_STAGE_NOT_VALID                                __MSABI_LONG(0xC00034E4)
#define EVENT_FRS_STAGING_AREA_FULL                              __MSABI_LONG(0x800034D2)
#define EVENT_FRS_STARTING                                       __MSABI_LONG(0x400034BD)
#define EVENT_FRS_STOPPED                                        __MSABI_LONG(0x400034BF)
#define EVENT_FRS_STOPPED_ASSERT                                 __MSABI_LONG(0xC00034C1)
#define EVENT_FRS_STOPPED_FORCE                                  __MSABI_LONG(0xC00034C0)
#define EVENT_FRS_STOPPING                                       __MSABI_LONG(0x400034BE)
#define EVENT_FRS_SYSVOL_NOT_READY                               __MSABI_LONG(0x800034CA)
#define EVENT_FRS_SYSVOL_NOT_READY_PRIMARY                       __MSABI_LONG(0x800034CB)
#define EVENT_FRS_SYSVOL_READY                                   __MSABI_LONG(0x400034CC)
#define EVENT_FRS_VOLUME_NOT_SUPPORTED                           __MSABI_LONG(0xC00034C3)
#define EVENT_INVALID_DRIVER_DEPENDENCY                          __MSABI_LONG(0xC0001B67)
#define EVENT_IPX_CREATE_DEVICE                                  __MSABI_LONG(0xC0002522)
#define EVENT_IPX_ILLEGAL_CONFIG                                 __MSABI_LONG(0x8000251F)
#define EVENT_IPX_INTERNAL_NET_INVALID                           __MSABI_LONG(0xC0002520)
#define EVENT_IPX_NEW_DEFAULT_TYPE                               __MSABI_LONG(0x4000251D)
#define EVENT_IPX_NO_ADAPTERS                                    __MSABI_LONG(0xC0002523)
#define EVENT_IPX_NO_FRAME_TYPES                                 __MSABI_LONG(0xC0002521)
#define EVENT_IPX_SAP_ANNOUNCE                                   __MSABI_LONG(0x8000251E)
#define EVENT_NBT_BAD_BACKUP_WINS_ADDR                           __MSABI_LONG(0x800010D0)
#define EVENT_NBT_BAD_PRIMARY_WINS_ADDR                          __MSABI_LONG(0x800010D1)
#define EVENT_NBT_CREATE_ADDRESS                                 __MSABI_LONG(0xC00010D3)
#define EVENT_NBT_CREATE_CONNECTION                              __MSABI_LONG(0xC00010D4)
#define EVENT_NBT_CREATE_DEVICE                                  __MSABI_LONG(0xC00010D7)
#define EVENT_NBT_CREATE_DRIVER                                  __MSABI_LONG(0xC00010CC)
#define EVENT_NBT_DUPLICATE_NAME                                 __MSABI_LONG(0xC00010DF)
#define EVENT_NBT_DUPLICATE_NAME_ERROR                           __MSABI_LONG(0xC00010E1)
#define EVENT_NBT_NAME_RELEASE                                   __MSABI_LONG(0xC00010E0)
#define EVENT_NBT_NAME_SERVER_ADDRS                              __MSABI_LONG(0xC00010D2)
#define EVENT_NBT_NON_OS_INIT                                    __MSABI_LONG(0xC00010D5)
#define EVENT_NBT_NO_BACKUP_WINS                                 __MSABI_LONG(0x800010CE)
#define EVENT_NBT_NO_DEVICES                                     __MSABI_LONG(0x800010D8)
#define EVENT_NBT_NO_RESOURCES                                   __MSABI_LONG(0xC00010E2)
#define EVENT_NBT_NO_WINS                                        __MSABI_LONG(0x800010CF)
#define EVENT_NBT_OPEN_REG_LINKAGE                               __MSABI_LONG(0xC00010D9)
#define EVENT_NBT_OPEN_REG_NAMESERVER                            __MSABI_LONG(0x800010DC)
#define EVENT_NBT_OPEN_REG_PARAMS                                __MSABI_LONG(0xC00010CD)
#define EVENT_NBT_READ_BIND                                      __MSABI_LONG(0xC00010DA)
#define EVENT_NBT_READ_EXPORT                                    __MSABI_LONG(0xC00010DB)
#define EVENT_NBT_TIMERS                                         __MSABI_LONG(0xC00010D6)
#define EVENT_NDIS_ADAPTER_CHECK_ERROR                           __MSABI_LONG(0xC00013A7)
#define EVENT_NDIS_ADAPTER_DISABLED                              __MSABI_LONG(0x80001396)
#define EVENT_NDIS_ADAPTER_NOT_FOUND                             __MSABI_LONG(0xC000138B)
#define EVENT_NDIS_BAD_IO_BASE_ADDRESS                           __MSABI_LONG(0xC0001394)
#define EVENT_NDIS_BAD_VERSION                                   __MSABI_LONG(0xC000138E)
#define EVENT_NDIS_CABLE_DISCONNECTED_ERROR                      __MSABI_LONG(0x800013A9)
#define EVENT_NDIS_DMA_CONFLICT                                  __MSABI_LONG(0x8000139B)
#define EVENT_NDIS_DRIVER_FAILURE                                __MSABI_LONG(0xC000138D)
#define EVENT_NDIS_HARDWARE_FAILURE                              __MSABI_LONG(0xC000138A)
#define EVENT_NDIS_INTERRUPT_CONFLICT                            __MSABI_LONG(0x8000139A)
#define EVENT_NDIS_INTERRUPT_CONNECT                             __MSABI_LONG(0xC000138C)
#define EVENT_NDIS_INVALID_DOWNLOAD_FILE_ERROR                   __MSABI_LONG(0xC000139C)
#define EVENT_NDIS_INVALID_VALUE_FROM_ADAPTER                    __MSABI_LONG(0xC0001392)
#define EVENT_NDIS_IO_PORT_CONFLICT                              __MSABI_LONG(0x80001397)
#define EVENT_NDIS_LOBE_FAILUE_ERROR                             __MSABI_LONG(0x800013A3)
#define EVENT_NDIS_MAXFRAMESIZE_ERROR                            __MSABI_LONG(0x8000139F)
#define EVENT_NDIS_MAXINTERNALBUFS_ERROR                         __MSABI_LONG(0x800013A0)
#define EVENT_NDIS_MAXMULTICAST_ERROR                            __MSABI_LONG(0x800013A1)
#define EVENT_NDIS_MAXRECEIVES_ERROR                             __MSABI_LONG(0x8000139D)
#define EVENT_NDIS_MAXTRANSMITS_ERROR                            __MSABI_LONG(0x8000139E)
#define EVENT_NDIS_MEMORY_CONFLICT                               __MSABI_LONG(0x80001399)
#define EVENT_NDIS_MISSING_CONFIGURATION_PARAMETER               __MSABI_LONG(0xC0001393)
#define EVENT_NDIS_NETWORK_ADDRESS                               __MSABI_LONG(0xC0001390)
#define EVENT_NDIS_OUT_OF_RESOURCE                               __MSABI_LONG(0xC0001389)
#define EVENT_NDIS_PORT_OR_DMA_CONFLICT                          __MSABI_LONG(0x80001398)
#define EVENT_NDIS_PRODUCTID_ERROR                               __MSABI_LONG(0x800013A2)
#define EVENT_NDIS_RECEIVE_SPACE_SMALL                           __MSABI_LONG(0x40001395)
#define EVENT_NDIS_REMOVE_RECEIVED_ERROR                         __MSABI_LONG(0x800013A5)
#define EVENT_NDIS_RESET_FAILURE_CORRECTION                      __MSABI_LONG(0x800013AA)
#define EVENT_NDIS_RESET_FAILURE_ERROR                           __MSABI_LONG(0x800013A8)
#define EVENT_NDIS_RESOURCE_CONFLICT                             __MSABI_LONG(0xC0001388)
#define EVENT_NDIS_SIGNAL_LOSS_ERROR                             __MSABI_LONG(0x800013A4)
#define EVENT_NDIS_TIMEOUT                                       __MSABI_LONG(0x8000138F)
#define EVENT_NDIS_TOKEN_RING_CORRECTION                         __MSABI_LONG(0x400013A6)
#define EVENT_NDIS_UNSUPPORTED_CONFIGURATION                     __MSABI_LONG(0xC0001391)
#define EVENT_PS_ADMISSIONCONTROL_OVERFLOW                       __MSABI_LONG(0x8000371F)
#define EVENT_PS_BAD_BESTEFFORT_LIMIT                            __MSABI_LONG(0x80003714)
#define EVENT_PS_BINDING_FAILED                                  __MSABI_LONG(0xC0003718)
#define EVENT_PS_GPC_REGISTER_FAILED                             __MSABI_LONG(0xC00036B0)
#define EVENT_PS_INIT_DEVICE_FAILED                              __MSABI_LONG(0xC000371B)
#define EVENT_PS_MISSING_ADAPTER_REGISTRY_DATA                   __MSABI_LONG(0xC0003719)
#define EVENT_PS_NETWORK_ADDRESS_FAIL                            __MSABI_LONG(0xC0003720)
#define EVENT_PS_NO_RESOURCES_FOR_INIT                           __MSABI_LONG(0xC00036B1)
#define EVENT_PS_QUERY_OID_GEN_LINK_SPEED                        __MSABI_LONG(0xC0003717)
#define EVENT_PS_QUERY_OID_GEN_MAXIMUM_FRAME_SIZE                __MSABI_LONG(0xC0003715)
#define EVENT_PS_QUERY_OID_GEN_MAXIMUM_TOTAL_SIZE                __MSABI_LONG(0xC0003716)
#define EVENT_PS_REGISTER_ADDRESS_FAMILY_FAILED                  __MSABI_LONG(0xC000371A)
#define EVENT_PS_REGISTER_MINIPORT_FAILED                        __MSABI_LONG(0xC00036B3)
#define EVENT_PS_REGISTER_PROTOCOL_FAILED                        __MSABI_LONG(0xC00036B2)
#define EVENT_PS_RESOURCE_POOL                                   __MSABI_LONG(0xC000371E)
#define EVENT_PS_WAN_LIMITED_BESTEFFORT                          __MSABI_LONG(0x8000371D)
#define EVENT_PS_WMI_INSTANCE_NAME_FAILED                        __MSABI_LONG(0xC000371C)
#define EVENT_RDR_AT_THREAD_MAX                                  __MSABI_LONG(0x80000BD2)
#define EVENT_RDR_CANT_BIND_TRANSPORT                            __MSABI_LONG(0x80000BD8)
#define EVENT_RDR_CANT_BUILD_SMB_HEADER                          __MSABI_LONG(0x80000BDB)
#define EVENT_RDR_CANT_CREATE_DEVICE                             __MSABI_LONG(0x80000BBA)
#define EVENT_RDR_CANT_CREATE_THREAD                             __MSABI_LONG(0x80000BBB)
#define EVENT_RDR_CANT_GET_SECURITY_CONTEXT                      __MSABI_LONG(0x80000BDA)
#define EVENT_RDR_CANT_READ_REGISTRY                             __MSABI_LONG(0x80000BD3)
#define EVENT_RDR_CANT_REGISTER_ADDRESS                          __MSABI_LONG(0x80000BD9)
#define EVENT_RDR_CANT_SET_THREAD                                __MSABI_LONG(0x80000BBC)
#define EVENT_RDR_CLOSE_BEHIND                                   __MSABI_LONG(0x80000BC3)
#define EVENT_RDR_CONNECTION                                     __MSABI_LONG(0x80000BCB)
#define EVENT_RDR_CONNECTION_REFERENCE                           __MSABI_LONG(0x80000BC7)
#define EVENT_RDR_CONTEXTS                                       __MSABI_LONG(0x80000BD0)
#define EVENT_RDR_DELAYED_SET_ATTRIBUTES_FAILED                  __MSABI_LONG(0x80000BD6)
#define EVENT_RDR_DELETEONCLOSE_FAILED                           __MSABI_LONG(0x80000BD7)
#define EVENT_RDR_DISPOSITION                                    __MSABI_LONG(0x80000BCF)
#define EVENT_RDR_ENCRYPT                                        __MSABI_LONG(0x80000BCA)
#define EVENT_RDR_FAILED_UNLOCK                                  __MSABI_LONG(0x80000BC1)
#define EVENT_RDR_INVALID_LOCK_REPLY                             __MSABI_LONG(0x80000BBF)
#define EVENT_RDR_INVALID_OPLOCK                                 __MSABI_LONG(0x80000BC6)
#define EVENT_RDR_INVALID_REPLY                                  __MSABI_LONG(0x80000BBD)
#define EVENT_RDR_INVALID_SMB                                    __MSABI_LONG(0x80000BBE)
#define EVENT_RDR_MAXCMDS                                        __MSABI_LONG(0x80000BCD)
#define EVENT_RDR_OPLOCK_SMB                                     __MSABI_LONG(0x80000BCE)
#define EVENT_RDR_PRIMARY_TRANSPORT_CONNECT_FAILED               __MSABI_LONG(0x80000BD5)
#define EVENT_RDR_RESOURCE_SHORTAGE                              __MSABI_LONG(0x80000BB9)
#define EVENT_RDR_SECURITY_SIGNATURE_MISMATCH                    __MSABI_LONG(0x80000BDC)
#define EVENT_RDR_SERVER_REFERENCE                               __MSABI_LONG(0x80000BC8)
#define EVENT_RDR_SMB_REFERENCE                                  __MSABI_LONG(0x80000BC9)
#define EVENT_RDR_TIMEOUT                                        __MSABI_LONG(0x80000BC5)
#define EVENT_RDR_TIMEZONE_BIAS_TOO_LARGE                        __MSABI_LONG(0x80000BD4)
#define EVENT_RDR_UNEXPECTED_ERROR                               __MSABI_LONG(0x80000BC4)
#define EVENT_RDR_WRITE_BEHIND_FLUSH_FAILED                      __MSABI_LONG(0x80000BD1)
#define EVENT_READFILE_TIMEOUT                                   __MSABI_LONG(0xC0001B62)
#define EVENT_REVERTED_TO_LASTKNOWNGOOD                          __MSABI_LONG(0xC0001B5F)
#define EVENT_RPCSS_ACTIVATION_ERROR                             __MSABI_LONG(0xC0002717)
#define EVENT_RPCSS_CREATEPROCESS_FAILURE                        __MSABI_LONG(0xC0002710)
#define EVENT_RPCSS_DEFAULT_LAUNCH_ACCESS_DENIED                 __MSABI_LONG(0xC0002713)
#define EVENT_RPCSS_LAUNCH_ACCESS_DENIED                         __MSABI_LONG(0xC0002712)
#define EVENT_RPCSS_REMOTE_SIDE_ERROR                            __MSABI_LONG(0xC0002716)
#define EVENT_RPCSS_REMOTE_SIDE_ERROR_WITH_FILE                  __MSABI_LONG(0xC0002718)
#define EVENT_RPCSS_REMOTE_SIDE_UNAVAILABLE                      __MSABI_LONG(0xC0002719)
#define EVENT_RPCSS_RUNAS_CANT_LOGIN                             __MSABI_LONG(0xC0002714)
#define EVENT_RPCSS_RUNAS_CREATEPROCESS_FAILURE                  __MSABI_LONG(0xC0002711)
#define EVENT_RPCSS_SERVER_NOT_RESPONDING                        __MSABI_LONG(0xC000271B)
#define EVENT_RPCSS_SERVER_START_TIMEOUT                         __MSABI_LONG(0xC000271A)
#define EVENT_RPCSS_START_SERVICE_FAILURE                        __MSABI_LONG(0xC0002715)
#define EVENT_RUNNING_LASTKNOWNGOOD                              __MSABI_LONG(0xC0001B73)
#define EVENT_SCOPE_LABEL_TOO_LONG                               __MSABI_LONG(0x800010DD)
#define EVENT_SCOPE_TOO_LONG                                     __MSABI_LONG(0x800010DE)
#define EVENT_SECOND_LOGON_FAILED                                __MSABI_LONG(0xC0001B66)
#define EVENT_SERVICE_CONFIG_BACKOUT_FAILED                      __MSABI_LONG(0xC0001B7D)
#define EVENT_SERVICE_CONTROL_SUCCESS                            __MSABI_LONG(0x40001B7B)
#define EVENT_SERVICE_CRASH                                      __MSABI_LONG(0xC0001B77)
#define EVENT_SERVICE_CRASH_NO_ACTION                            __MSABI_LONG(0xC0001B7A)
#define EVENT_SERVICE_DIFFERENT_PID_CONNECTED                    __MSABI_LONG(0x80001B7F)
#define EVENT_SERVICE_EXIT_FAILED                                __MSABI_LONG(0xC0001B6F)
#define EVENT_SERVICE_EXIT_FAILED_SPECIFIC                       __MSABI_LONG(0xC0001B70)
#define EVENT_SERVICE_LOGON_TYPE_NOT_GRANTED                     __MSABI_LONG(0xC0001B81)
#define EVENT_SERVICE_NOT_INTERACTIVE                            __MSABI_LONG(0xC0001B76)
#define EVENT_SERVICE_RECOVERY_FAILED                            __MSABI_LONG(0xC0001B78)
#define EVENT_SERVICE_SCESRV_FAILED                              __MSABI_LONG(0xC0001B79)
#define EVENT_SERVICE_SHUTDOWN_FAILED                            __MSABI_LONG(0xC0001B83)
#define EVENT_SERVICE_START_AT_BOOT_FAILED                       __MSABI_LONG(0xC0001B71)
#define EVENT_SERVICE_START_FAILED                               __MSABI_LONG(0xC0001B58)
#define EVENT_SERVICE_START_FAILED_GROUP                         __MSABI_LONG(0xC0001B5A)
#define EVENT_SERVICE_START_FAILED_II                            __MSABI_LONG(0xC0001B59)
#define EVENT_SERVICE_START_FAILED_NONE                          __MSABI_LONG(0xC0001B5B)
#define EVENT_SERVICE_START_HUNG                                 __MSABI_LONG(0xC0001B6E)
#define EVENT_SERVICE_START_TYPE_CHANGED                         __MSABI_LONG(0x40001B80)
#define EVENT_SERVICE_STATUS_SUCCESS                             __MSABI_LONG(0x40001B7C)
#define EVENT_SERVICE_STOP_SUCCESS_WITH_REASON                   __MSABI_LONG(0x40001B82)
#define EVENT_SEVERE_SERVICE_FAILED                              __MSABI_LONG(0xC0001B6D)
#define EVENT_SRV_CANT_BIND_DUP_NAME                             __MSABI_LONG(0xC00009C9)
#define EVENT_SRV_CANT_BIND_TO_TRANSPORT                         __MSABI_LONG(0x800009C8)
#define EVENT_SRV_CANT_CHANGE_DOMAIN_NAME                        __MSABI_LONG(0x800009D0)
#define EVENT_SRV_CANT_CREATE_DEVICE                             __MSABI_LONG(0xC00007D2)
#define EVENT_SRV_CANT_CREATE_PROCESS                            __MSABI_LONG(0xC00007D3)
#define EVENT_SRV_CANT_CREATE_THREAD                             __MSABI_LONG(0xC00007D4)
#define EVENT_SRV_CANT_GROW_TABLE                                __MSABI_LONG(0x800007D9)
#define EVENT_SRV_CANT_LOAD_DRIVER                               __MSABI_LONG(0x800009CC)
#define EVENT_SRV_CANT_MAP_ERROR                                 __MSABI_LONG(0x800009CE)
#define EVENT_SRV_CANT_OPEN_NPFS                                 __MSABI_LONG(0xC00007D7)
#define EVENT_SRV_CANT_RECREATE_SHARE                            __MSABI_LONG(0x800009CF)
#define EVENT_SRV_CANT_START_SCAVENGER                           __MSABI_LONG(0xC00007DA)
#define EVENT_SRV_CANT_UNLOAD_DRIVER                             __MSABI_LONG(0x800009CD)
#define EVENT_SRV_DISK_FULL                                      __MSABI_LONG(0x800007DD)
#define EVENT_SRV_DOS_ATTACK_DETECTED                            __MSABI_LONG(0x800007E9)
#define EVENT_SRV_INVALID_REGISTRY_VALUE                         __MSABI_LONG(0x800009CA)
#define EVENT_SRV_INVALID_REQUEST                                __MSABI_LONG(0xC00007D6)
#define EVENT_SRV_INVALID_SD                                     __MSABI_LONG(0x800009CB)
#define EVENT_SRV_IRP_STACK_SIZE                                 __MSABI_LONG(0xC00007DB)
#define EVENT_SRV_KEY_NOT_CREATED                                __MSABI_LONG(0xC00009C6)
#define EVENT_SRV_KEY_NOT_FOUND                                  __MSABI_LONG(0xC00009C5)
#define EVENT_SRV_NETWORK_ERROR                                  __MSABI_LONG(0x800007DC)
#define EVENT_SRV_NONPAGED_POOL_LIMIT                            __MSABI_LONG(0xC00007E1)
#define EVENT_SRV_NO_BLOCKING_IO                                 __MSABI_LONG(0x800007E8)
#define EVENT_SRV_NO_FREE_CONNECTIONS                            __MSABI_LONG(0x800007E6)
#define EVENT_SRV_NO_FREE_RAW_WORK_ITEM                          __MSABI_LONG(0x800007E7)
#define EVENT_SRV_NO_NONPAGED_POOL                               __MSABI_LONG(0xC00007E3)
#define EVENT_SRV_NO_PAGED_POOL                                  __MSABI_LONG(0xC00007E4)
#define EVENT_SRV_NO_TRANSPORTS_BOUND                            __MSABI_LONG(0xC00009C7)
#define EVENT_SRV_NO_VIRTUAL_MEMORY                              __MSABI_LONG(0xC00007E0)
#define EVENT_SRV_NO_WORK_ITEM                                   __MSABI_LONG(0x800007E5)
#define EVENT_SRV_OUT_OF_WORK_ITEM_DOS                           __MSABI_LONG(0x800007EB)
#define EVENT_SRV_PAGED_POOL_LIMIT                               __MSABI_LONG(0xC00007E2)
#define EVENT_SRV_RESOURCE_SHORTAGE                              __MSABI_LONG(0xC00007D1)
#define EVENT_SRV_SERVICE_FAILED                                 __MSABI_LONG(0xC00007D0)
#define EVENT_SRV_TOO_MANY_DOS                                   __MSABI_LONG(0x800007EA)
#define EVENT_SRV_TXF_INIT_FAILED                                __MSABI_LONG(0x800009D1)
#define EVENT_SRV_UNEXPECTED_DISC                                __MSABI_LONG(0xC00007D5)
#define EVENT_STREAMS_ALLOCB_FAILURE                             __MSABI_LONG(0x80000FA1)
#define EVENT_STREAMS_ALLOCB_FAILURE_CNT                         __MSABI_LONG(0x80000FA2)
#define EVENT_STREAMS_ESBALLOC_FAILURE                           __MSABI_LONG(0x80000FA3)
#define EVENT_STREAMS_ESBALLOC_FAILURE_CNT                       __MSABI_LONG(0x80000FA4)
#define EVENT_STREAMS_STRLOG                                     __MSABI_LONG(0xC0000FA0)
#define EVENT_TAKE_OWNERSHIP                                     __MSABI_LONG(0xC0001B74)
#define EVENT_TCPIP6_STARTED                                     __MSABI_LONG(0x40000C1C)
#define EVENT_TCPIP_ADAPTER_REG_FAILURE                          __MSABI_LONG(0xC000105F)
#define EVENT_TCPIP_ADDRESS_CONFLICT1                            __MSABI_LONG(0xC0001066)
#define EVENT_TCPIP_ADDRESS_CONFLICT2                            __MSABI_LONG(0xC0001067)
#define EVENT_TCPIP_CREATE_DEVICE_FAILED                         __MSABI_LONG(0xC0001004)
#define EVENT_TCPIP_DHCP_INIT_FAILED                             __MSABI_LONG(0x8000105E)
#define EVENT_TCPIP_INVALID_ADDRESS                              __MSABI_LONG(0xC000105B)
#define EVENT_TCPIP_INVALID_DEFAULT_GATEWAY                      __MSABI_LONG(0x80001060)
#define EVENT_TCPIP_INVALID_MASK                                 __MSABI_LONG(0xC000105C)
#define EVENT_TCPIP_IPV4_UNINSTALLED                             __MSABI_LONG(0x4000106B)
#define EVENT_TCPIP_IP_INIT_FAILED                               __MSABI_LONG(0xC0001064)
#define EVENT_TCPIP_MEDIA_CONNECT                                __MSABI_LONG(0x40001069)
#define EVENT_TCPIP_MEDIA_DISCONNECT                             __MSABI_LONG(0x4000106A)
#define EVENT_TCPIP_NO_ADAPTER_RESOURCES                         __MSABI_LONG(0xC000105D)
#define EVENT_TCPIP_NO_ADDRESS_LIST                              __MSABI_LONG(0xC0001061)
#define EVENT_TCPIP_NO_BINDINGS                                  __MSABI_LONG(0xC0001063)
#define EVENT_TCPIP_NO_MASK                                      __MSABI_LONG(0xC000105A)
#define EVENT_TCPIP_NO_MASK_LIST                                 __MSABI_LONG(0xC0001062)
#define EVENT_TCPIP_NO_RESOURCES_FOR_INIT                        __MSABI_LONG(0xC0001005)
#define EVENT_TCPIP_NTE_CONTEXT_LIST_FAILURE                     __MSABI_LONG(0xC0001068)
#define EVENT_TCPIP_TCP_CONNECT_LIMIT_REACHED                    __MSABI_LONG(0x80001082)
#define EVENT_TCPIP_TCP_INIT_FAILED                              __MSABI_LONG(0xC0001081)
#define EVENT_TCPIP_TCP_MPP_ATTACKS_DETECTED                     __MSABI_LONG(0x80001085)
#define EVENT_TCPIP_TCP_TIME_WAIT_COLLISION                      __MSABI_LONG(0x80001083)
#define EVENT_TCPIP_TCP_WSD_WS_RESTRICTED                        __MSABI_LONG(0x80001084)
#define EVENT_TCPIP_TOO_MANY_GATEWAYS                            __MSABI_LONG(0x80001065)
#define EVENT_TCPIP_TOO_MANY_NETS                                __MSABI_LONG(0xC0001059)
#define EVENT_TCPIP_UDP_LIMIT_REACHED                            __MSABI_LONG(0x800010A9)
#define EVENT_TRANSACT_INVALID                                   __MSABI_LONG(0xC0001B64)
#define EVENT_TRANSACT_TIMEOUT                                   __MSABI_LONG(0xC0001B63)
#define EVENT_TRANSPORT_ADAPTER_NOT_FOUND                        __MSABI_LONG(0xC000232E)
#define EVENT_TRANSPORT_BAD_PROTOCOL                             __MSABI_LONG(0x40002333)
#define EVENT_TRANSPORT_BINDING_FAILED                           __MSABI_LONG(0xC000232D)
#define EVENT_TRANSPORT_QUERY_OID_FAILED                         __MSABI_LONG(0xC0002330)
#define EVENT_TRANSPORT_REGISTER_FAILED                          __MSABI_LONG(0xC000232C)
#define EVENT_TRANSPORT_RESOURCE_LIMIT                           __MSABI_LONG(0x8000232A)
#define EVENT_TRANSPORT_RESOURCE_POOL                            __MSABI_LONG(0x80002329)
#define EVENT_TRANSPORT_RESOURCE_SPECIFIC                        __MSABI_LONG(0x8000232B)
#define EVENT_TRANSPORT_SET_OID_FAILED                           __MSABI_LONG(0xC000232F)
#define EVENT_TRANSPORT_TOO_MANY_LINKS                           __MSABI_LONG(0x40002332)
#define EVENT_TRANSPORT_TRANSFER_DATA                            __MSABI_LONG(0x40002331)
#define EVENT_TRK_INTERNAL_ERROR                                 __MSABI_LONG(0xC00030D4)
#define EVENT_TRK_SERVICE_CORRUPT_LOG                            __MSABI_LONG(0xC00030D7)
#define EVENT_TRK_SERVICE_DUPLICATE_VOLIDS                       __MSABI_LONG(0x400030DB)
#define EVENT_TRK_SERVICE_MOVE_QUOTA_EXCEEDED                    __MSABI_LONG(0x800030DC)
#define EVENT_TRK_SERVICE_START_FAILURE                          __MSABI_LONG(0xC00030D6)
#define EVENT_TRK_SERVICE_START_SUCCESS                          __MSABI_LONG(0x400030D5)
#define EVENT_TRK_SERVICE_VOLUME_CLAIM                           __MSABI_LONG(0x400030DA)
#define EVENT_TRK_SERVICE_VOLUME_CREATE                          __MSABI_LONG(0x400030D9)
#define EVENT_TRK_SERVICE_VOL_QUOTA_EXCEEDED                     __MSABI_LONG(0x800030D8)
#define EVENT_UP_DRIVER_ON_MP                                    __MSABI_LONG(0xC00017D4)
#define EVENT_WEBCLIENT_CLOSE_DELETE_FAILED                      __MSABI_LONG(0x80003A36)
#define EVENT_WEBCLIENT_CLOSE_PROPPATCH_FAILED                   __MSABI_LONG(0x80003A37)
#define EVENT_WEBCLIENT_CLOSE_PUT_FAILED                         __MSABI_LONG(0x80003A35)
#define EVENT_WEBCLIENT_SETINFO_PROPPATCH_FAILED                 __MSABI_LONG(0x80003A38)
#define EVENT_WMI_CANT_GET_EVENT_DATA                            __MSABI_LONG(0x80002F49)
#define EVENT_WMI_CANT_OPEN_DEVICE                               __MSABI_LONG(0xC0002EE0)
#define EVENT_WMI_CANT_RESOLVE_INSTANCE                          __MSABI_LONG(0x80002F48)
#define EVENT_WMI_INVALID_MOF                                    __MSABI_LONG(0x80002F44)
#define EVENT_WMI_INVALID_REGINFO                                __MSABI_LONG(0x80002F46)
#define EVENT_WMI_INVALID_REGPATH                                __MSABI_LONG(0x80002F47)
#define EVENT_WMI_MOF_LOAD_FAILURE                               __MSABI_LONG(0x80002F45)
#define EVENT_WSK_OWNINGTHREAD_PARAMETER_IGNORED                 __MSABI_LONG(0xC0003E80)

#define EXTRA_EXIT_POINT                                         __MSABI_LONG(0xC00037DC)
#define EXTRA_EXIT_POINT_DELETED                                 __MSABI_LONG(0xC00037E0)
#define EXTRA_EXIT_POINT_NOT_DELETED                             __MSABI_LONG(0xC00037E1)
#define EXTRA_VOLUME                                             __MSABI_LONG(0xC00037DF)
#define EXTRA_VOLUME_DELETED                                     __MSABI_LONG(0xC00037E6)
#define EXTRA_VOLUME_NOT_DELETED                                 __MSABI_LONG(0xC00037E7)

#define KNOWLEDGE_INCONSISTENCY_DETECTED                         __MSABI_LONG(0xC00037E9)

#define LM_REDIR_FAILURE                                         __MSABI_LONG(0x40003841)

#define MACHINE_UNJOINED                                         __MSABI_LONG(0xC00037ED)

#define MISSING_EXIT_POINT                                       __MSABI_LONG(0xC00037DD)
#define MISSING_EXIT_POINT_CREATED                               __MSABI_LONG(0xC00037E2)
#define MISSING_EXIT_POINT_NOT_CREATED                           __MSABI_LONG(0xC00037E3)
#define MISSING_VOLUME                                           __MSABI_LONG(0xC00037DE)
#define MISSING_VOLUME_CREATED                                   __MSABI_LONG(0xC00037E4)
#define MISSING_VOLUME_NOT_CREATED                               __MSABI_LONG(0xC00037E5)

#define NET_DFS_ENUM                                             __MSABI_LONG(0x400038A4)
#define NET_DFS_ENUMEX                                           __MSABI_LONG(0x400038A5)

#define NOT_A_DFS_PATH                                           __MSABI_LONG(0x40003840)

#define NTFRSPRF_COLLECT_RPC_BINDING_ERROR_CONN                  __MSABI_LONG(0xC00034DC)
#define NTFRSPRF_COLLECT_RPC_BINDING_ERROR_SET                   __MSABI_LONG(0xC00034DB)
#define NTFRSPRF_COLLECT_RPC_CALL_ERROR_CONN                     __MSABI_LONG(0xC00034DE)
#define NTFRSPRF_COLLECT_RPC_CALL_ERROR_SET                      __MSABI_LONG(0xC00034DD)
#define NTFRSPRF_OPEN_RPC_BINDING_ERROR_CONN                     __MSABI_LONG(0xC00034D8)
#define NTFRSPRF_OPEN_RPC_BINDING_ERROR_SET                      __MSABI_LONG(0xC00034D7)
#define NTFRSPRF_OPEN_RPC_CALL_ERROR_CONN                        __MSABI_LONG(0xC00034DA)
#define NTFRSPRF_OPEN_RPC_CALL_ERROR_SET                         __MSABI_LONG(0xC00034D9)
#define NTFRSPRF_REGISTRY_ERROR_CONN                             __MSABI_LONG(0xC00034E2)
#define NTFRSPRF_REGISTRY_ERROR_SET                              __MSABI_LONG(0xC00034E1)
#define NTFRSPRF_VIRTUALALLOC_ERROR_CONN                         __MSABI_LONG(0xC00034E0)
#define NTFRSPRF_VIRTUALALLOC_ERROR_SET                          __MSABI_LONG(0xC00034DF)

#define NWSAP_EVENT_BADWANFILTER_VALUE                           __MSABI_LONG(0xC000214A)
#define NWSAP_EVENT_BIND_FAILED                                  __MSABI_LONG(0xC0002138)
#define NWSAP_EVENT_CARDLISTEVENT_FAIL                           __MSABI_LONG(0xC000214B)
#define NWSAP_EVENT_CARDMALLOC_FAILED                            __MSABI_LONG(0xC000213C)
#define NWSAP_EVENT_CREATELPCEVENT_ERROR                         __MSABI_LONG(0xC0002147)
#define NWSAP_EVENT_CREATELPCPORT_ERROR                          __MSABI_LONG(0xC0002146)
#define NWSAP_EVENT_GETSOCKNAME_FAILED                           __MSABI_LONG(0xC0002139)
#define NWSAP_EVENT_HASHTABLE_MALLOC_FAILED                      __MSABI_LONG(0xC0002144)
#define NWSAP_EVENT_INVALID_FILTERNAME                           __MSABI_LONG(0x8000214D)
#define NWSAP_EVENT_KEY_NOT_FOUND                                __MSABI_LONG(0xC0002134)
#define NWSAP_EVENT_LPCHANDLEMEMORY_ERROR                        __MSABI_LONG(0xC0002149)
#define NWSAP_EVENT_LPCLISTENMEMORY_ERROR                        __MSABI_LONG(0xC0002148)
#define NWSAP_EVENT_NOCARDS                                      __MSABI_LONG(0xC000213D)
#define NWSAP_EVENT_OPTBCASTINADDR_FAILED                        __MSABI_LONG(0xC000213B)
#define NWSAP_EVENT_OPTEXTENDEDADDR_FAILED                       __MSABI_LONG(0xC000213A)
#define NWSAP_EVENT_OPTMAXADAPTERNUM_ERROR                       __MSABI_LONG(0xC0002153)
#define NWSAP_EVENT_RECVSEM_FAIL                                 __MSABI_LONG(0xC000213F)
#define NWSAP_EVENT_SDMDEVENT_FAIL                               __MSABI_LONG(0xC000214C)
#define NWSAP_EVENT_SENDEVENT_FAIL                               __MSABI_LONG(0xC0002140)
#define NWSAP_EVENT_SETOPTBCAST_FAILED                           __MSABI_LONG(0xC0002137)
#define NWSAP_EVENT_SOCKET_FAILED                                __MSABI_LONG(0xC0002136)
#define NWSAP_EVENT_STARTLPCWORKER_ERROR                         __MSABI_LONG(0xC0002145)
#define NWSAP_EVENT_STARTRECEIVE_ERROR                           __MSABI_LONG(0xC0002141)
#define NWSAP_EVENT_STARTWANCHECK_ERROR                          __MSABI_LONG(0xC0002152)
#define NWSAP_EVENT_STARTWANWORKER_ERROR                         __MSABI_LONG(0xC0002151)
#define NWSAP_EVENT_STARTWORKER_ERROR                            __MSABI_LONG(0xC0002142)
#define NWSAP_EVENT_TABLE_MALLOC_FAILED                          __MSABI_LONG(0xC0002143)
#define NWSAP_EVENT_THREADEVENT_FAIL                             __MSABI_LONG(0xC000213E)
#define NWSAP_EVENT_WANBIND_FAILED                               __MSABI_LONG(0xC0002150)
#define NWSAP_EVENT_WANEVENT_ERROR                               __MSABI_LONG(0xC0002155)
#define NWSAP_EVENT_WANHANDLEMEMORY_ERROR                        __MSABI_LONG(0xC0002154)
#define NWSAP_EVENT_WANSEM_FAIL                                  __MSABI_LONG(0xC000214E)
#define NWSAP_EVENT_WANSOCKET_FAILED                             __MSABI_LONG(0xC000214F)
#define NWSAP_EVENT_WSASTARTUP_FAILED                            __MSABI_LONG(0xC0002135)

#define PREFIX_MISMATCH                                          __MSABI_LONG(0xC00037EA)
#define PREFIX_MISMATCH_FIXED                                    __MSABI_LONG(0xC00037EB)
#define PREFIX_MISMATCH_NOT_FIXED                                __MSABI_LONG(0xC00037EC)

#define STATUS_SEVERITY_ERROR                                    0x3
#define STATUS_SEVERITY_INFORMATIONAL                            0x1
#define STATUS_SEVERITY_SUCCESS                                  0x0
#define STATUS_SEVERITY_WARNING                                  0x2

#define TITLE_SC_MESSAGE_BOX                                     __MSABI_LONG(0xC0001B75)

#ifdef __cplusplus
}
#endif

#endif /* __NETEVENT_H */
