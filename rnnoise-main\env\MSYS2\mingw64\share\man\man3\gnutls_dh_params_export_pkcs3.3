.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dh_params_export_pkcs3" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dh_params_export_pkcs3 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_dh_params_export_pkcs3(gnutls_dh_params_t " params ", gnutls_x509_crt_fmt_t " format ", unsigned char * " params_data ", size_t * " params_data_size ");"
.SH ARGUMENTS
.IP "gnutls_dh_params_t params" 12
Holds the DH parameters
.IP "gnutls_x509_crt_fmt_t format" 12
the format of output params. One of PEM or DER.
.IP "unsigned char * params_data" 12
will contain a PKCS3 DHParams structure PEM or DER encoded
.IP "size_t * params_data_size" 12
holds the size of params_data (and will be replaced by the actual size of parameters)
.SH "DESCRIPTION"
This function will export the given dh parameters to a PKCS3
DHParams structure. This is the format generated by "openssl dhparam" tool.
If the buffer provided is not long enough to hold the output, then
GNUTLS_E_SHORT_MEMORY_BUFFER will be returned.

If the structure is PEM encoded, it will have a header
of "BEGIN DH PARAMETERS".
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
