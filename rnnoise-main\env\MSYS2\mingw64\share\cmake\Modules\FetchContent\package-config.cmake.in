# Automatically generated by CMake's FetchContent module.
# Do not edit this file, it will be regenerated every time CMake runs.

# Projects or the dependencies themselves can provide the following files.
# The files should define any additional commands or variables that the
# dependency would normally provide but which won't be available globally
# if the dependency is brought into the build via FetchContent instead.
# For dependencies that only provide imported targets and no commands,
# these typically won't be needed.
include("${CMAKE_CURRENT_LIST_DIR}/@<EMAIL>" OPTIONAL)
include("${CMAKE_CURRENT_LIST_DIR}/@<EMAIL>" OPTIONAL)
