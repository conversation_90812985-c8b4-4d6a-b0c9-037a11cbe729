# This is the list of root certificates used for qualified
# certificates.  They are defined as certificates capable of creating
# legally binding signatures in the same way as a handwritten
# signatures are.  Comments like this one and empty lines are allowed
# Lines do have a length limit but this is not a serious limitation as
# the format of the entries is fixed and checked by gpgsm: A
# non-comment line starts with optional whitespaces, followed by
# exactly 40 hex character, whitespace and a lowercased 2 letter
# country code.  Additional data delimited with by a whitespace is
# current ignored but might late be used for other purposes.
#
# Note: The subversion copy of this file carries a gpg:signature
# property with its OpenPGP signature.  Check this signature before
# adding entries:
#  svn pg gpg:signature qualified.txt | gpg --verify - qualified.txt
# to create a new signature:
#  f=qualified.txt; gpg -sba $f && svn ps gpg:signature -F $f.asc $f

#*******************************************
#
#                 Belgium
#
# Need to figure out a reliable source.
#*******************************************



#*******************************************
#
#                 Germany
#
# The information for Germany is available
# at http://www.bundesnetzagentur.de
#*******************************************

#Serial number: 32D18D
#       Issuer: /CN=6R-Ca 1:PN/NameDistinguisher=1/O=RegulierungsbehÈorde
#               fÈur Telekommunikation und Post/C=DE
#      Subject: /CN=6R-Ca 1:PN/NameDistinguisher=1/O=RegulierungsbehÈorde
#               fÈur Telekommunikation und Post/C=DE
#     validity: 2001-02-01 09:52:17 through 2005-06-01 09:52:17
#     key type: 1024 bit RSA
#    key usage: certSign crlSign
#[checked: 2005-11-14]
EA:8D:99:DD:36:AA:2D:07:1A:3C:7B:69:00:9E:51:B9:4A:2E:E7:60  de


#Serial number: 00C48C8D
#       Issuer: /CN=7R-CA 1:PN/NameDistinguisher=1/O=RegulierungsbehÈorde
#               fÈur Telekommunikation und Post/C=DE
#      Subject: /CN=7R-CA 1:PN/NameDistinguisher=1/O=RegulierungsbehÈorde
#               fÈur Telekommunikation und Post/C=DE
#     validity: 2001-10-15 11:15:15 through 2006-02-15 11:15:15
#     key type: 1024 bit RSA
#    key usage: certSign crlSign
#[checked: 2005-11-14]
DB:45:3D:1B:B0:1A:F3:23:10:6B:DE:D0:09:61:57:AA:F4:25:E0:5B  de


#Serial number: 01
#       Issuer: /CN=8R-CA 1:PN/O=Regulierungsbehörde für
#               Telekommunikation und Post/C=DE
#      Subject: /CN=8R-CA 1:PN/O=Regulierungsbehörde für
#               Telekommunikation und Post/C=DE
#     validity: 2004-11-25 14:10:37 through 2007-12-31 14:04:03
#     key type: 1024 bit RSA
#    key usage: certSign
#     policies: 1.3.36.8.1.1:N:
# chain length: unlimited
#[checked: 2005-11-14]
42:6A:F6:78:30:E9:CE:24:5B:EF:41:A2:C1:A8:51:DA:C5:0A:6D:F5  de


#Serial number: 02
#       Issuer: /CN=9R-CA 1:PN/O=Regulierungsbehörde für
#               Telekommunikation und Post/C=DE
#      Subject: /CN=9R-CA 1:PN/O=Regulierungsbehörde für
#               Telekommunikation und Post/C=DE
#     validity: 2004-11-25 14:59:11 through 2007-12-31 14:56:59
#     key type: 1024 bit RSA
#    key usage: certSign
#     policies: 1.3.36.8.1.1:N:
# chain length: unlimited
#[checked: 2005-11-14]
75:9A:4A:CE:7C:DA:7E:89:1B:B2:72:4B:E3:76:EA:47:3A:96:97:24  de


#Serial number: 2A
#       Issuer: /CN=10R-CA 1:PN/O=Bundesnetzagentur/C=DE
#      Subject: /CN=10R-CA 1:PN/O=Bundesnetzagentur/C=DE
#     validity: 2005-08-03 15:30:36 through 2007-12-31 15:09:23
#     key type: 1024 bit RSA
#    key usage: certSign
#     policies: 1.3.36.8.1.1:N:
# chain length: unlimited
#[checked: 2005-11-14]
31:C9:D2:E6:31:4D:0B:CC:2C:1A:45:00:A6:6B:97:98:27:18:8E:CD  de


#Serial number: 2D
#       Issuer: /CN=11R-CA 1:PN/O=Bundesnetzagentur/C=DE
#      Subject: /CN=11R-CA 1:PN/O=Bundesnetzagentur/C=DE
#     validity: 2005-08-03 18:09:49 through 2007-12-31 18:04:28
#     key type: 1024 bit RSA
#    key usage: certSign
#     policies: 1.3.36.8.1.1:N:
# chain length: unlimited
#[checked: 2005-11-14]
A0:8B:DF:3B:AA:EE:3F:9D:64:6C:47:81:23:21:D4:A6:18:81:67:1D  de


#           ID: 0x5B4757B0
#          S/N: 0139
#       Issuer: /CN=12R-CA 1:PN/O=Bundesnetzagentur/C=DE
#      Subject: /CN=12R-CA 1:PN/O=Bundesnetzagentur/C=DE
#     validity: 2007-05-25 11:01:44 through 2012-05-25 10:56:07
#     key type: 2048 bit RSA
#    key usage: certSign
#     policies: 1.3.36.8.1.1:N:
# chain length: unlimited
# [checked: 2008-06-25]
44:7E:D4:E3:9A:D7:92:E2:07:FA:53:1A:2E:F5:B8:02:5B:47:57:B0  de

#           ID: 0x46A2CC8A
#          S/N: 013C
#       Issuer: /CN=13R-CA 1:PN/O=Bundesnetzagentur/C=DE
#      Subject: /CN=13R-CA 1:PN/O=Bundesnetzagentur/C=DE
#     validity: 2007-05-29 11:02:37 through 2012-05-29 10:55:54
#     key type: 2048 bit RSA
#    key usage: certSign
#     policies: 1.3.36.8.1.1:N:
# chain length: unlimited
# [checked: 2008-06-25]
AC:A7:BE:45:1F:A6:BF:09:F2:D1:3F:08:7B:BC:EB:7F:46:A2:CC:8A  de


#
# D-Trust root certificates.  Probably by shifting a lot of Euros to
# laywer companies, German CAs achieved to get the permission to
# create their own legally binding root certificates - independent of
# the Bundesnetzagentur.  The main problem with this is that it is
# hard to figure out what qualified root certificates are actually
# active.  There is now no way to be sure whether a signature is a
# qualified one.  A pettifogger's way of validating certificates.
#

#Serial number: 00B95F
#       Issuer: /CN=D-TRUST Qualified Root CA 1 2006:PN/O=D-Trust GmbH/C=DE
#      Subject: /CN=D-TRUST Qualified Root CA 1 2006:PN/O=D-Trust GmbH/C=DE
#          aka: <EMAIL>
#          aka: (uri http://www.d-trust.net)
#     validity: 2006-04-27 12:40:54 through 2011-04-27 12:40:54
#     key type: 2048 bit RSA
#    key usage: certSign crlSign
#     policies: 1.3.6.1.4.1.4788.2.30.1:N:
# chain length: unlimited
#[checked: 2007-01-31  by phone 030-259391-0 and callback by Mrs. Enke]
E0:BF:1B:91:91:6B:88:E4:F1:15:92:22:CE:37:23:96:B1:4A:2E:5C  de


#Serial number: 00B960
#       Issuer: /CN=D-TRUST Qualified Root CA 2 2006:PN/O=D-Trust GmbH/C=DE
#      Subject: /CN=D-TRUST Qualified Root CA 2 2006:PN/O=D-Trust GmbH/C=DE
#          aka: <EMAIL>
#          aka: (uri http://www.d-trust.net)
#     validity: 2006-04-27 12:40:54 through 2011-04-27 12:40:54
#     key type: 2048 bit RSA
#    key usage: certSign crlSign
#     policies: 1.3.6.1.4.1.4788.2.30.1:N:
# chain length: unlimited
#[checked: 2007-01-31  by phone 030-259391-0 and callback by Mrs. Enke]
98:2A:75:67:0F:F8:28:4A:94:E0:9D:23:D8:E7:62:C8:BD:A4:54:04  de


#
# S-Trust root certificates.
#

#Serial number: 00DF749F80AA51F0EDC0CB1FC183E97EE2
#       Issuer: /CN=S-TRUST Qualified Root CA 2006-001:PN
#               /O=Deutscher Sparkassen Verlag GmbH/L=Stuttgart
#               /ST=Baden-Wuerttemberg (BW)/C=DE
#      Subject: /CN=S-TRUST Qualified Root CA 2006-001:PN
#               /O=Deutscher Sparkassen Verlag GmbH/L=Stuttgart
#               /ST=Baden-Wuerttemberg (BW)/C=DE
#     validity: 2006-01-01 00:00:00 through 2010-12-30 23:59:59
#     key type: 2048 bit RSA
#    key usage: certSign crlSign
# chain length: 1
#[checked: 2007-01-31 by phone 0711-782-0 Mr. Brommer]
7D:DC:76:1C:FD:AF:4C:E0:3A:B5:3A:DD:C9:FA:13:35:19:A3:DE:C9  de

#Serial number: 00BC098E0402E92956B8D7DE74977E26F7
#       Issuer: /CN=S-TRUST Qualified Root CA 2007-001:PN
#               /O=Deutscher Sparkassen Verlag GmbH/L=Stuttgart
#               /ST=Baden-Wuerttemberg (BW)/C=DE
#      Subject: /CN=S-TRUST Qualified Root CA 2007-001:PN
#               /O=Deutscher Sparkassen Verlag GmbH/L=Stuttgart
#               /ST=Baden-Wuerttemberg (BW)/C=DE
#     validity: 2007-01-01 00:00:00 through 2011-12-30 23:59:59
#     key type: 2048 bit RSA
#    key usage: certSign crlSign
# chain length: 1
#[checked: 2007-01-31 by phone 0711-782-0 Mr. Brommer]
7A:3C:1B:60:2E:BD:A4:A1:E0:EB:AD:7A:BA:4F:D1:43:69:A9:39:FC  de


#           ID: 0xA8FEA3CA
#          S/N: 00B3963E0E6C2D65125853E970665402E5
#       Issuer: /CN=S-TRUST Qualified Root CA 2008-001:PN
#               /O=Deutscher Sparkassen Verlag GmbH/L=Stuttgart/C=DE
#      Subject: /CN=S-TRUST Qualified Root CA 2008-001:PN
#               /O=Deutscher Sparkassen Verlag GmbH/L=Stuttgart/C=DE
#     validity: 2008-01-01 00:00:00 through 2012-12-30 23:59:59
#     key type: 2048 bit RSA
#    key usage: certSign crlSign
# chain length: 1
#[checked: 2007-12-13 via received ZIP file with qualified signature from
#         /CN=Dr. Matthias Stehle/O=Deutscher Sparkassenverlag
#         /C=DE/SerialNumber=DSV0000000008/SN=Stehle/GN=Matthias Georg]
C9:2F:E6:50:DB:32:59:E0:CE:65:55:F3:8C:76:E0:B8:A8:FE:A3:CA  de

#           ID: 0x3A7D979B
#          S/N: 00C4216083F35C54F67B09A80C3C55FE7D
#       Issuer: /CN=S-TRUST Qualified Root CA 2008-002:PN
#               /O=Deutscher Sparkassen Verlag GmbH/L=Stuttgart/C=DE
#      Subject: /CN=S-TRUST Qualified Root CA 2008-002:PN
#               /O=Deutscher Sparkassen Verlag GmbH/L=Stuttgart/C=DE
#     validity: 2008-01-01 00:00:00 through 2012-12-30 23:59:59
#     key type: 2048 bit RSA
#    key usage: certSign crlSign
# chain length: 1
#[checked: 2007-12-13 via received ZIP file with qualified signature from
#         /CN=Dr. Matthias Stehle/O=Deutscher Sparkassenverlag
#         /C=DE/SerialNumber=DSV0000000008/SN=Stehle/GN=Matthias Georg"]
D5:C7:50:F2:FE:4E:EE:D7:C7:B1:E4:13:7B:FB:54:84:3A:7D:97:9B  de


#*******************************************
#
#                 End of file
#
#*******************************************
