## Syntax highlighting for Tcl and Expect scripts.

## Original author:  <PERSON>
## License:  GPL version 3 or newer

syntax tcl "\.(tcl|exp)$"
magic "Tcl(/Tk)? script"
comment "#"

# Standard Tcl [info commands]:
color green "\<(after|append|array|auto_(execok|import|load(_index)?|qualify)|binary|break)\>"
color green "\<(case|catch|cd|clock|close|concat|continue|encoding|eof|error|eval|exec|exit|expr)\>"
color green "\<(fblocked|fconfigure|fcopy|file(event)?|flush|for|foreach|format|gets|glob|global|history)\>"
color green "\<(if|incr|info|interp|join|lappend|lindex|linsert|list|llength|load|lrange|lreplace|lsearch|lset|lsort)\>"
color green "\<(namespace|open|package|pid|puts|pwd|read|regexp|regsub|rename|return)\>"
color green "\<(scan|seek|set|socket|source|split|string|subst|switch|tclLog|tell|time|trace)\>"
color green "\<(unknown|unset|update|uplevel|upvar|variable|vwait|while)\>"

# Basic Tcl subcommands:
color green "\<array (anymore|donesearch|exists|get|names|nextelement|set|size|startsearch|statistics|unset)\>"
color green "\<string (compare|equal|first|index|is|last|(byte)?length|map|match|range|repeat|replace|to(lower|title|upper)?|trim(left|right)?|will|word(end|start))\>"

# Extended TclX [info commands]:
color green "\<(alarm|auto_load_pkg|bsearch|cat(close|gets|open)|ccollate|cconcat|cequal|chgrp|chmod|chown|chroot)\>"
color green "\<(cindex|clength|cmdtrace|commandloop|crange|csubstr|ctoken|ctype|dup|echo|execl)\>"
color green "\<(fcntl|flock|fork|fstat|ftruncate|funlock|host_info|id|infox|keyl(del|get|keys|set)|kill)\>"
color green "\<(lassign|lcontain|lempty|lgets|link|lmatch|loadlibindex|loop|lvar(cat|pop|push)|max|min|nice)\>"
color green "\<(pipe|profile|random|readdir|replicate|scan(context|file|match)|select|server_(accept|create)|signal)\>"
color green "\<(sleep|sync|system|tclx_(findinit|fork|load_tndxs|sleep|system|wait)|times|translit|try_eval|umask|wait)\>"

# Syntax:
color green "[!$&();<=>\`|]"
color brightblue "\<proc[[:blank:]]|\{|\}"
color brightyellow ""([^"\]|\\.)*"|'([^'\]|\\.)*'"
color brightred "\$\{?[[:alnum:]_!@#$*?-]+\}?"

# Trailing whitespace:
color ,green "[[:space:]]+$"

# Comments:
color cyan "^[[:blank:]]*#.*"
