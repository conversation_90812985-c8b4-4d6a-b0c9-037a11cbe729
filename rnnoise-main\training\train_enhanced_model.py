#!/usr/bin/env python3
"""
RNN噪声抑制与人声增强模型训练脚本
使用clean_voice和wind_noise_voice数据
"""

import tensorflow as tf
import numpy as np
import h5py
import os
from datetime import datetime

# 设置CPU运行
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

def create_enhanced_model():
    """创建增强的RNN模型"""
    print("构建增强RNN模型...")
    
    # 输入层 (68维)
    inputs = tf.keras.Input(shape=(None, 68), name='main_input')
    
    # 特征分离
    noise_features = inputs[:, :, :38]  # 前38维用于噪声抑制
    voice_features = inputs[:, :, 38:]  # 后30维用于人声增强
    
    # 共享特征处理层
    shared_dense = tf.keras.layers.Dense(32, activation='tanh', name='shared_dense')(inputs)
    
    # VAD分支 (基于噪声抑制特征)
    vad_input = tf.keras.layers.concatenate([shared_dense, noise_features])
    vad_gru = tf.keras.layers.GRU(24, activation='tanh', return_sequences=True, name='vad_gru')(vad_input)
    vad_output = tf.keras.layers.Dense(1, activation='sigmoid', name='vad_output')(vad_gru)
    
    # 噪声抑制分支
    noise_input = tf.keras.layers.concatenate([shared_dense, vad_gru, noise_features])
    noise_gru = tf.keras.layers.GRU(48, activation='relu', return_sequences=True, name='noise_gru')(noise_input)
    
    # 人声增强分支
    voice_input = tf.keras.layers.concatenate([shared_dense, vad_gru, voice_features])
    voice_gru = tf.keras.layers.GRU(48, activation='tanh', return_sequences=True, name='voice_gru')(voice_input)
    
    # 特征融合层
    fused_input = tf.keras.layers.concatenate([vad_gru, noise_gru, voice_gru, inputs])
    final_gru = tf.keras.layers.GRU(96, activation='tanh', return_sequences=True, name='final_gru')(fused_input)
    
    # 双重输出
    noise_suppression_output = tf.keras.layers.Dense(18, activation='sigmoid', name='noise_suppression_output')(final_gru)
    voice_enhancement_output = tf.keras.layers.Dense(18, activation='sigmoid', name='voice_enhancement_output')(final_gru)
    
    # 构建模型
    model = tf.keras.Model(inputs=inputs, outputs=[noise_suppression_output, voice_enhancement_output, vad_output])
    
    return model

def noise_suppression_loss(y_true, y_pred):
    """噪声抑制损失函数"""
    # 创建mask，-1表示无效数据
    mask = tf.cast(tf.greater(y_true, -0.5), tf.float32)
    
    # 确保数值稳定性
    y_pred_safe = tf.maximum(y_pred, 1e-8)
    y_true_safe = tf.maximum(tf.maximum(y_true, 0.0), 1e-8)
    
    # MSE损失
    sqrt_diff = tf.sqrt(y_pred_safe) - tf.sqrt(y_true_safe)
    mse_loss = tf.square(sqrt_diff)
    
    # 四次方损失（更强的惩罚）
    quartic_loss = tf.square(mse_loss)
    
    # 组合损失
    total_loss = mask * (10.0 * quartic_loss + mse_loss)
    
    # 计算平均值，只考虑有效数据
    valid_count = tf.maximum(tf.reduce_sum(mask, axis=-1), 1.0)
    return tf.reduce_sum(total_loss, axis=-1) / valid_count

def voice_enhancement_loss(y_true, y_pred):
    """人声增强损失函数"""
    # 创建mask
    mask = tf.cast(tf.greater(y_true, -0.5), tf.float32)
    
    # 确保数值稳定性
    y_pred_safe = tf.maximum(y_pred, 1e-8)
    y_true_safe = tf.maximum(tf.maximum(y_true, 0.0), 1e-8)
    
    # 基础MSE损失
    sqrt_diff = tf.sqrt(y_pred_safe) - tf.sqrt(y_true_safe)
    mse_loss = tf.square(sqrt_diff)
    
    # 高频增强权重
    freq_weights = tf.constant([1.0, 1.0, 1.1, 1.1, 1.2, 1.2, 1.3, 1.3, 1.4, 
                               1.4, 1.5, 1.5, 1.6, 1.6, 1.7, 1.7, 1.8, 1.8])
    freq_weights = tf.reshape(freq_weights, [1, 1, 18])
    
    # 加权MSE损失
    weighted_mse = mse_loss * freq_weights
    
    # 谐波保持损失
    harmonic_loss = tf.square(y_pred - y_true) * 0.1
    
    # 组合损失
    total_loss = mask * (weighted_mse + harmonic_loss)
    
    # 计算平均值
    valid_count = tf.maximum(tf.reduce_sum(mask, axis=-1), 1.0)
    return tf.reduce_sum(total_loss, axis=-1) / valid_count

def vad_loss(y_true, y_pred):
    """VAD损失函数"""
    return tf.keras.losses.binary_crossentropy(y_true, y_pred)

def load_training_data():
    """加载训练数据"""
    print("加载训练数据...")
    
    # 检查数据文件
    data_file = 'training_data.h5'
    if not os.path.exists(data_file):
        print(f"错误: 找不到训练数据文件 {data_file}")
        print("请确保已经生成了训练数据")
        return None, None, None, None
    
    # 加载HDF5数据
    with h5py.File(data_file, 'r') as hf:
        all_data = hf['data'][:]
    
    print(f"原始数据形状: {all_data.shape}")
    
    # 数据预处理
    window_size = 2000
    nb_sequences = len(all_data) // window_size
    
    print(f"训练序列数量: {nb_sequences}")
    print(f"每个序列长度: {window_size}")
    print(f"总训练样本: {nb_sequences * window_size}")
    
    # 输入特征 (68维)
    x_train = all_data[:nb_sequences*window_size, :68]
    x_train = np.reshape(x_train, (nb_sequences, window_size, 68))
    
    # 噪声抑制目标 (18维)
    noise_train = all_data[:nb_sequences*window_size, 68:86]
    noise_train = np.reshape(noise_train, (nb_sequences, window_size, 18))
    
    # 人声增强目标 (18维)
    voice_train = all_data[:nb_sequences*window_size, 86:104]
    voice_train = np.reshape(voice_train, (nb_sequences, window_size, 18))
    
    # VAD目标 (1维)
    vad_train = all_data[:nb_sequences*window_size, 104:105]
    vad_train = np.reshape(vad_train, (nb_sequences, window_size, 1))
    
    print("训练数据形状:")
    print(f"  输入特征: {x_train.shape}")
    print(f"  噪声抑制目标: {noise_train.shape}")
    print(f"  人声增强目标: {voice_train.shape}")
    print(f"  VAD目标: {vad_train.shape}")
    
    return x_train, noise_train, voice_train, vad_train

def train_model():
    """训练模型"""
    print("=" * 60)
    print("RNN噪声抑制与人声增强模型训练")
    print("数据源: clean_voice + wind_noise_voice")
    print("=" * 60)
    
    # 加载数据
    x_train, noise_train, voice_train, vad_train = load_training_data()
    if x_train is None:
        return
    
    # 创建模型
    model = create_enhanced_model()
    
    # 显示模型结构
    print("\n模型结构:")
    model.summary()
    
    # 编译模型
    print("\n编译模型...")
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss={
            'noise_suppression_output': noise_suppression_loss,
            'voice_enhancement_output': voice_enhancement_loss,
            'vad_output': vad_loss
        },
        loss_weights={
            'noise_suppression_output': 10.0,
            'voice_enhancement_output': 8.0,
            'vad_output': 0.5
        },
        metrics={
            'noise_suppression_output': ['mae'],
            'voice_enhancement_output': ['mae'],
            'vad_output': ['accuracy']
        }
    )
    
    # 设置回调
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_dir = f"../models_enhanced/{timestamp}"
    os.makedirs(model_dir, exist_ok=True)
    
    callbacks = [
        tf.keras.callbacks.ModelCheckpoint(
            filepath=f"{model_dir}/enhanced_model_epoch_{{epoch:02d}}_loss_{{loss:.4f}}.keras",
            monitor='loss',
            save_best_only=True,
            verbose=1
        ),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=10,
            restore_best_weights=True,
            verbose=1
        ),
        tf.keras.callbacks.ReduceLROnPlateau(
            monitor='loss',
            factor=0.5,
            patience=5,
            min_lr=1e-6,
            verbose=1
        )
    ]
    
    # 开始训练
    print(f"\n开始训练... 模型将保存到: {model_dir}")
    print("训练参数:")
    print("  - 批次大小: 16")
    print("  - 最大轮数: 100")
    print("  - 验证集比例: 10%")
    print("  - 早停机制: 10轮无改善")
    
    history = model.fit(
        x_train,
        {
            'noise_suppression_output': noise_train,
            'voice_enhancement_output': voice_train,
            'vad_output': vad_train
        },
        batch_size=16,
        epochs=100,
        validation_split=0.1,
        callbacks=callbacks,
        verbose=1
    )
    
    # 保存最终模型
    final_model_path = f"{model_dir}/enhanced_rnnoise_final.keras"
    model.save(final_model_path)
    
    # 保存训练历史
    import pickle
    with open(f"{model_dir}/training_history.pkl", 'wb') as f:
        pickle.dump(history.history, f)
    
    print(f"\n训练完成!")
    print(f"最终模型保存在: {final_model_path}")
    print(f"训练历史保存在: {model_dir}/training_history.pkl")
    
    # 显示训练结果摘要
    print("\n训练结果摘要:")
    final_loss = history.history['loss'][-1]
    final_val_loss = history.history['val_loss'][-1]
    print(f"  最终训练损失: {final_loss:.4f}")
    print(f"  最终验证损失: {final_val_loss:.4f}")
    
    return model, history

if __name__ == '__main__':
    try:
        model, history = train_model()
        print("\n🎉 训练成功完成!")
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
