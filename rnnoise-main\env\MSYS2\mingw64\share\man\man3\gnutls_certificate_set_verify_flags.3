.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_verify_flags" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_verify_flags \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_certificate_set_verify_flags(gnutls_certificate_credentials_t " res ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t res" 12
is a gnutls_certificate_credentials_t type
.IP "unsigned int flags" 12
are the flags
.SH "DESCRIPTION"
This function will set the flags to be used for verification
of certificates and override any defaults.  The provided flags must be an OR of the
\fBgnutls_certificate_verify_flags\fP enumerations.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
