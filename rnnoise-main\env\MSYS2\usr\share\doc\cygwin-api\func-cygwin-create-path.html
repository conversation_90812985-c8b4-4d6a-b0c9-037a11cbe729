<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>cygwin_create_path</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="cygwin-functions.html#func-cygwin-path" title="Path conversion functions"><link rel="prev" href="func-cygwin-conv-path-list.html" title="cygwin_conv_path_list"><link rel="next" href="func-cygwin-posix-path-list-p.html" title="cygwin_posix_path_list_p"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">cygwin_create_path</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="func-cygwin-conv-path-list.html">Prev</a>&#160;</td><th width="60%" align="center">Path conversion functions</th><td width="20%" align="right">&#160;<a accesskey="n" href="func-cygwin-posix-path-list-p.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="func-cygwin-create-path"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>cygwin_create_path</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="funcsynopsis"><pre class="funcsynopsisinfo">
#include &lt;sys/cygwin.h&gt;
</pre><p><code class="funcdef">void *
<b class="fsfunc">cygwin_create_path</b>(</code>cygwin_conv_path_t <var class="pdparam">what</var>, const void * <var class="pdparam">from</var><code>)</code>;</p></div></div><div class="refsect1"><a name="func-cygwin-create-path-desc"></a><h2>Description</h2><p>This is equivalent to the <code class="function">cygwin_conv_path</code>, except
that <code class="function">cygwin_create_path</code> does not take a buffer pointer
for the result of the conversion as input.  Rather it allocates the buffer
itself using <code class="function">malloc</code>(3) and returns a pointer to this
buffer.  In case of error it returns NULL and sets errno to one of the
values defined for <code class="function">cygwin_conv_path</code>.  Additionally
errno can be set to the below value.</p><pre class="programlisting">
    ENOMEM        Insufficient memory was available.
</pre><p>When you don't need the returned buffer anymore, use
<code class="function">free</code>(3) to deallocate it.</p></div><div class="refsect1"><a name="func-cygwin-create-path-also"></a><h2>See also</h2><p>See also <a class="link" href="func-cygwin-conv-path.html" title="cygwin_conv_path">cygwin_conv_path</a></p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="func-cygwin-conv-path-list.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="cygwin-functions.html#func-cygwin-path">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="func-cygwin-posix-path-list-p.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">cygwin_conv_path_list&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;cygwin_posix_path_list_p</td></tr></table></div></body></html>
