.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_x509_crl_mem" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_x509_crl_mem \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_set_x509_crl_mem(gnutls_certificate_credentials_t " res ", const gnutls_datum_t * " CRL ", gnutls_x509_crt_fmt_t " type ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t res" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "const gnutls_datum_t * CRL" 12
is a list of trusted CRLs. They should have been verified before.
.IP "gnutls_x509_crt_fmt_t type" 12
is DER or PEM
.SH "DESCRIPTION"
This function adds the trusted CRLs in order to verify client or
server certificates.  In case of a client this is not required to
be called if the certificates are not verified using
\fBgnutls_certificate_verify_peers2()\fP.  This function may be called
multiple times.
.SH "RETURNS"
number of CRLs processed, or a negative error code on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
