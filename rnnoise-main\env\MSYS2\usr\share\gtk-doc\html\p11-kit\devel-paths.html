<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Packaging PKCS#11 module configs: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="devel.html" title="Building, Packaging, and Contributing to p11-kit">
<link rel="prev" href="devel.html" title="Building, Packaging, and Contributing to p11-kit">
<link rel="next" href="devel-commands.html" title="Customizing installed commands">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts"></td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="devel.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="devel.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="devel-commands.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="devel-paths"></a>Packaging PKCS#11 module configs</h2></div></div></div>
<p>Developers or packagers of PKCS#11 modules need to install various
		files into specific locations so that p11-kit will recognize and load the
		module correctly.</p>
<p>You should use <code class="literal">pkg-config</code> as described below
		to determine configuration paths. p11-kit installs a
		<code class="literal">pkg-config</code> file called <code class="literal">p11-kit-1.pc</code>.
		This file contains all the information about the various paths that p11-kit
		looks for files at.</p>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="devel-paths-config"></a>Path to place module configuration</h3></div></div></div>
<p>As described in the <a class="link" href="pkcs11-conf.html#config-module" title="Module Configuration">module configuration</a>
			documentation, each PKCS#11 module should install a config file describing
			that module. These config files should be installed to a specific directory which
			can be determined by running:</p>
<pre class="programlisting">
$ <span class="command"><strong>pkg-config p11-kit-1 --variable p11_module_configs</strong></span>
/usr/share/p11-kit/modules</pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="devel-paths-modules"></a>Default path for modules with relative paths</h3></div></div></div>
<p>If a <a class="link" href="pkcs11-conf.html#config-module" title="Module Configuration">module configuration</a>
			contains a relative path in its <code class="literal">module:</code> setting,
			then that module will be loaded from the default module path. This
			path can be determined by running:</p>
<pre class="programlisting">
$ <span class="command"><strong>pkg-config p11-kit-1 --variable p11_module_path</strong></span>
/usr/lib64/pkcs11</pre>
</div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>