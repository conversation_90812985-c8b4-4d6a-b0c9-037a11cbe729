# RNN噪声抑制模型训练详细指南

## 项目概述
本文档详细介绍如何使用自定义数据集训练RNN噪声抑制模型，包括数据准备、训练流程、模型导出和集成到C程序的完整过程。

## 环境要求

### 系统环境
- Windows 10/11 + MSYS2环境
- Python 3.x (建议3.7+)
- 足够的存储空间（训练数据可能需要数GB）

### Python依赖包
```bash
# 激活Python环境
cd rnnoise-main/env
Scripts/activate

# 安装必要的包
pip install tensorflow==2.x
pip install keras
pip install h5py
pip install numpy
```

### 编译工具
- GCC编译器（通过MSYS2安装）
- CMake（用于重新编译训练数据生成程序）

## 数据集要求

### 数据格式要求
- **音频格式**: 16位PCM WAV文件
- **采样率**: 16kHz
- **声道**: 单声道
- **文件组织**: 干净语音和噪声分别存放在不同文件夹

### 推荐的数据集结构
```
your_dataset/
├── clean_speech/          # 干净语音文件夹
│   ├── speech_001.wav
│   ├── speech_002.wav
│   └── ...
└── noise/                 # 噪声文件夹
    ├── noise_001.wav
    ├── noise_002.wav
    └── ...
```

### 数据质量建议
- **语音数据**: 至少2-3小时的干净语音
- **噪声数据**: 多样化的噪声类型（环境噪声、白噪声、粉噪声等）
- **数据平衡**: 确保语音和噪声数据量相当
- **质量控制**: 确保音频文件无损坏，音量适中

## 完整训练流程

### 第一步：编译训练数据生成程序

#### 1.1 启动MSYS2环境
```bash
# 从项目根目录启动
rnnoise-main\env\MSYS2\msys2_shell.cmd -defterm -here -no-start -mingw64
```

#### 1.2 编译denoise_training_gao程序
```bash
cd rnnoise-main/src

# 使用提供的编译脚本
gcc -DTRAINING=1 -Wall -W -O3 -g -Wunused-variable -I../include \
    denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c \
    -o denoise_training_gao -lm
```

**注意**: 如果编译失败，可能需要调整源码路径或使用备份版本：
```bash
# 使用备份版本
cd bakcup2
bash compile_gao.sh
```

### 第二步：生成训练数据

#### 2.1 准备数据路径
```bash
# 创建训练目录
cd ../training

# 设置数据路径变量（根据您的实际路径修改）
SPEECH_DIR="/path/to/your/clean_speech"
NOISE_DIR="/path/to/your/noise"
```

#### 2.2 生成特征数据
```bash
# 运行训练数据生成程序
../src/denoise_training_gao $SPEECH_DIR $NOISE_DIR mixed.wav > training_16k_v3.f32
```

**程序参数说明**:
- `$SPEECH_DIR`: 干净语音文件夹路径
- `$NOISE_DIR`: 噪声文件夹路径  
- `mixed.wav`: 输出的混合音频文件（可选）
- `training_16k_v3.f32`: 输出的训练特征文件

**数据生成过程**:
1. 程序随机选择语音和噪声文件
2. 按随机SNR（0-20dB）混合音频
3. 提取38维输入特征和18维目标特征
4. 生成VAD（语音活动检测）标签
5. 输出75维特征向量（38+18+18+1）

#### 2.3 转换为HDF5格式
```bash
# 将二进制特征文件转换为HDF5格式
python bin2hdf5.py training_16k_v3.f32 80000000 75 training_16k_v3.h5
```

**参数说明**:
- `training_16k_v3.f32`: 输入的二进制特征文件
- `80000000`: 预期的数据点数量（根据实际调整）
- `75`: 每个数据点的特征维度
- `training_16k_v3.h5`: 输出的HDF5文件

### 第三步：模型训练

#### 3.1 修改训练脚本（可选）
根据您的数据集大小和需求，可能需要调整训练参数：

```python
# 编辑 rnn_train_16k.py
batch_size = 64          # 批次大小，根据GPU内存调整
epochs = 120             # 训练轮数
validation_split = 0.1   # 验证集比例
window_size = 2000       # 序列长度
```

#### 3.2 开始训练
```bash
# 使用CPU训练
python rnn_train_16k.py

# 或使用GPU训练（如果有GPU）
python rnn_train_16k_gpu.py
```

**训练过程监控**:
- 训练会自动保存最佳模型到`models/`文件夹
- 文件名格式：`weights-improvement-{epoch:02d}-{loss:.5f}.hdf5`
- 监控`denoise_output_loss`指标，越小越好

#### 3.3 训练输出示例
```
Loading data...
done.
40000 sequences x shape = (40000, 2000, 38) y shape =  (40000, 2000, 18)
Train...
Epoch 1/120
625/625 [==============================] - 45s - loss: 0.1234 - denoise_output_loss: 0.0987
...
Epoch 119/120
625/625 [==============================] - 42s - loss: 0.0345 - denoise_output_loss: 0.0267
```

### 第四步：模型导出和集成

#### 4.1 选择最佳模型
```bash
# 查看训练结果，选择loss最小的模型
ls -la models/weights-improvement-*

# 例如选择第119轮的模型
cp models/weights-improvement-119-0.02670.hdf5 best_model.hdf5
```

#### 4.2 导出为C代码
```bash
# 将Keras模型转换为C代码
python dump_rnn.py best_model.hdf5 rnn_data_new.c rnn_data_new.h model_name
```

**输出文件**:
- `rnn_data_new.c`: 包含模型权重的C源文件
- `rnn_data_new.h`: 对应的头文件

#### 4.3 集成到主程序
```bash
# 备份原始模型文件
cp ../src/rnn_data.c ../src/rnn_data_backup.c
cp ../src/rnn_data.h ../src/rnn_data_backup.h

# 替换为新训练的模型
cp rnn_data_new.c ../src/rnn_data.c
cp rnn_data_new.h ../src/rnn_data.h
```

#### 4.4 重新编译主程序
```bash
# 返回主目录重新编译
cd ..
ninja

# 测试新模型
cd bin
./rnn_gao_new.exe test_noisy.wav test_output.wav
```

## 训练参数调优

### 模型架构参数
```python
# 在 rnn_train_16k.py 中可调整的参数
main_input = Input(shape=(None, 38))           # 输入特征维度
Dense(24, activation='tanh')                   # 输入层神经元数
GRU(24, activation='tanh')                     # VAD GRU层大小
GRU(48, activation='relu')                     # 噪声估计GRU层大小  
GRU(96, activation='tanh')                     # 去噪GRU层大小
Dense(18, activation='sigmoid')                # 输出层（频带增益）
```

### 损失函数权重
```python
loss_weights=[10, 0.5]  # [去噪损失权重, VAD损失权重]
```

### 训练策略
- **学习率**: 使用Adam优化器的默认学习率
- **正则化**: L2正则化系数 `reg = 0.000001`
- **权重约束**: 权重裁剪到[-0.499, 0.499]范围

## 数据增强技巧

### 动态SNR混合
程序自动实现以下数据增强：
- 随机SNR范围：0-20dB
- 随机语音/噪声增益调整
- 随机滤波器响应
- VAD标签自动生成

### 扩展数据集
```bash
# 可以多次运行数据生成，累积更多训练数据
../src/denoise_training_gao $SPEECH_DIR1 $NOISE_DIR1 mixed1.wav >> training_16k_v3.f32
../src/denoise_training_gao $SPEECH_DIR2 $NOISE_DIR2 mixed2.wav >> training_16k_v3.f32
```

## 性能评估

### 客观指标
- **训练损失**: 监控`denoise_output_loss`
- **验证损失**: 确保没有过拟合
- **PESQ**: 使用标准PESQ工具评估
- **STOI**: 短时客观可懂度指标

### 主观评估
- 准备测试音频集
- 对比原始噪声、处理后音频
- 多人主观评分

## 故障排除

### 常见问题

1. **内存不足**
   ```python
   # 减小批次大小
   batch_size = 32  # 从64减少到32
   
   # 减小序列长度
   window_size = 1000  # 从2000减少到1000
   ```

2. **训练数据不足**
   ```bash
   # 检查生成的数据量
   ls -lh training_16k_v3.f32
   
   # 应该至少有几百MB
   ```

3. **模型不收敛**
   - 检查学习率设置
   - 增加训练数据
   - 调整模型架构

4. **编译错误**
   ```bash
   # 确保所有依赖文件存在
   ls ../include/rnnoise.h
   ls kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c
   ```

## 高级技巧

### 迁移学习
```python
# 加载预训练模型继续训练
model = load_model('pretrained_model.hdf5', custom_objects={...})
# 继续训练...
```

### 多GPU训练
```python
# 使用多GPU并行训练
from keras.utils import multi_gpu_model
parallel_model = multi_gpu_model(model, gpus=2)
```

### 自定义损失函数
```python
# 根据具体需求调整损失函数权重
def custom_loss(y_true, y_pred):
    # 自定义损失计算
    return loss_value
```

## 针对400对数据集的具体建议

### 数据预处理脚本
为了更好地处理您的400对数据，建议创建预处理脚本：

```python
# data_preprocessor.py
import os
import librosa
import soundfile as sf
import numpy as np

def preprocess_audio_files(input_dir, output_dir, target_sr=16000):
    """
    预处理音频文件：统一采样率、格式、音量
    """
    os.makedirs(output_dir, exist_ok=True)

    for filename in os.listdir(input_dir):
        if filename.endswith(('.wav', '.mp3', '.flac')):
            # 读取音频
            audio, sr = librosa.load(os.path.join(input_dir, filename), sr=target_sr)

            # 音量归一化
            audio = audio / np.max(np.abs(audio)) * 0.8

            # 保存为16位WAV
            output_path = os.path.join(output_dir, filename.replace('.mp3', '.wav').replace('.flac', '.wav'))
            sf.write(output_path, audio, target_sr, subtype='PCM_16')

    print(f"预处理完成，输出到: {output_dir}")

# 使用示例
preprocess_audio_files("原始语音文件夹", "clean_speech")
preprocess_audio_files("原始噪声文件夹", "noise")
```

### 数据量估算
对于400对数据（假设每个文件5-10秒）：
- 总时长：约33-67分钟
- 建议增加数据增强以达到2-3小时训练数据
- 可以通过调整`denoise_training_gao`的循环次数增加数据

### 训练时间预估
- **CPU训练**: 约6-12小时（120 epochs）
- **GPU训练**: 约1-3小时（120 epochs）
- **推荐配置**: 至少8GB RAM，GPU显存4GB+

## 快速开始脚本

创建一个自动化训练脚本：

```bash
#!/bin/bash
# auto_train.sh - 自动化训练脚本

echo "=== RNN噪声抑制模型自动训练脚本 ==="

# 1. 检查环境
echo "检查Python环境..."
python -c "import tensorflow, keras, h5py, numpy" || {
    echo "错误: 缺少必要的Python包"
    exit 1
}

# 2. 编译训练程序
echo "编译训练数据生成程序..."
cd rnnoise-main/src
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include \
    denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c \
    -o denoise_training_gao -lm

# 3. 生成训练数据
echo "生成训练数据..."
cd ../training
../src/denoise_training_gao /path/to/clean_speech /path/to/noise mixed.wav > training_16k_v3.f32

# 4. 转换数据格式
echo "转换为HDF5格式..."
python bin2hdf5.py training_16k_v3.f32 80000000 75 training_16k_v3.h5

# 5. 开始训练
echo "开始模型训练..."
python rnn_train_16k.py

# 6. 导出最佳模型
echo "导出最佳模型..."
BEST_MODEL=$(ls models/weights-improvement-*.hdf5 | sort -V | tail -1)
python dump_rnn.py $BEST_MODEL rnn_data_new.c rnn_data_new.h custom_model

# 7. 集成新模型
echo "集成新模型到主程序..."
cp rnn_data_new.c ../src/rnn_data.c
cp rnn_data_new.h ../src/rnn_data.h

# 8. 重新编译
echo "重新编译主程序..."
cd ..
ninja

echo "=== 训练完成！新模型已集成到 bin/rnn_gao_new.exe ==="
```

## 模型性能监控

### 训练过程可视化
```python
# training_monitor.py
import matplotlib.pyplot as plt
import re

def plot_training_history(log_file):
    """解析训练日志并绘制损失曲线"""
    epochs = []
    losses = []

    with open(log_file, 'r') as f:
        for line in f:
            if 'denoise_output_loss:' in line:
                epoch = int(re.search(r'Epoch (\d+)/', line).group(1))
                loss = float(re.search(r'denoise_output_loss: ([\d.]+)', line).group(1))
                epochs.append(epoch)
                losses.append(loss)

    plt.figure(figsize=(10, 6))
    plt.plot(epochs, losses)
    plt.title('Training Loss Over Time')
    plt.xlabel('Epoch')
    plt.ylabel('Denoise Output Loss')
    plt.grid(True)
    plt.savefig('training_history.png')
    plt.show()

# 使用方法：将训练输出重定向到文件
# python rnn_train_16k.py > training.log 2>&1
# python training_monitor.py
```

### 实时性能测试
```python
# performance_test.py
import time
import numpy as np
from your_model import rnnoise_process_frame

def test_realtime_performance():
    """测试实时处理性能"""
    frame_size = 160
    sample_rate = 16000
    frame_duration = frame_size / sample_rate  # 10ms

    # 模拟音频帧
    test_frame = np.random.randn(frame_size).astype(np.float32)

    # 性能测试
    times = []
    for i in range(1000):
        start_time = time.time()
        output = rnnoise_process_frame(test_frame)
        end_time = time.time()
        times.append(end_time - start_time)

    avg_time = np.mean(times)
    max_time = np.max(times)

    print(f"平均处理时间: {avg_time*1000:.2f}ms")
    print(f"最大处理时间: {max_time*1000:.2f}ms")
    print(f"帧时长: {frame_duration*1000:.2f}ms")
    print(f"实时性能: {'通过' if avg_time < frame_duration else '不通过'}")

test_realtime_performance()
```

## 总结

通过以上详细指南，您可以使用自己的400对噪声数据集训练出定制化的RNN噪声抑制模型。关键要点：

1. **数据质量至关重要** - 确保音频质量和多样性
2. **充分的训练数据** - 至少需要数小时的音频数据
3. **合适的参数调优** - 根据数据特点调整模型参数
4. **充分的训练时间** - 通常需要数十到上百个epoch
5. **验证和测试** - 使用独立测试集验证模型性能

### 针对400对数据的特别建议：
- 使用数据增强技术扩充训练集
- 考虑迁移学习从预训练模型开始
- 重点关注过拟合问题，适当使用正则化
- 准备充足的验证数据评估模型泛化能力

训练完成后，新模型将集成到C程序中，可以实现针对您特定噪声环境的优化降噪效果。
