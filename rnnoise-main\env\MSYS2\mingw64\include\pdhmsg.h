/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _PDH_MSG_H_
#define _PDH_MSG_H_

#define STATUS_SEVERITY_WARNING 0x2
#define STATUS_SEVERITY_SUCCESS 0x0
#define STATUS_SEVERITY_INFORMATIONAL 0x1
#define STATUS_SEVERITY_ERROR 0x3

#define PDH_CSTATUS_VALID_DATA ((DWORD)0x00000000)
#define PDH_CSTATUS_NEW_DATA ((DWORD)0x00000001)
#define PDH_CSTATUS_NO_MACHINE ((DWORD)0x800007D0)
#define PDH_CSTATUS_NO_INSTANCE ((DWORD)0x800007D1)
#define PDH_MORE_DATA ((DWORD)0x800007D2)
#define PDH_CSTATUS_ITEM_NOT_VALIDATED ((DWORD)0x800007D3)
#define PDH_RETRY ((DWORD)0x800007D4)
#define PDH_NO_DATA ((DWORD)0x800007D5)
#define PDH_CALC_NEGATIVE_DENOMINATOR ((DWORD)0x800007D6)
#define PDH_CALC_NEGATIVE_TIMEBASE ((DWORD)0x800007D7)
#define PDH_CALC_NEGATIVE_VALUE ((DWORD)0x800007D8)
#define PDH_DIALOG_CANCELLED ((DWORD)0x800007D9)
#define PDH_END_OF_LOG_FILE ((DWORD)0x800007DA)
#define PDH_ASYNC_QUERY_TIMEOUT ((DWORD)0x800007DB)
#define PDH_CANNOT_SET_DEFAULT_REALTIME_DATASOURCE ((DWORD)0x800007DC)
#define PDH_CSTATUS_NO_OBJECT ((DWORD)0xC0000BB8)
#define PDH_CSTATUS_NO_COUNTER ((DWORD)0xC0000BB9)
#define PDH_CSTATUS_INVALID_DATA ((DWORD)0xC0000BBA)
#define PDH_MEMORY_ALLOCATION_FAILURE ((DWORD)0xC0000BBB)
#define PDH_INVALID_HANDLE ((DWORD)0xC0000BBC)
#define PDH_INVALID_ARGUMENT ((DWORD)0xC0000BBD)
#define PDH_FUNCTION_NOT_FOUND ((DWORD)0xC0000BBE)
#define PDH_CSTATUS_NO_COUNTERNAME ((DWORD)0xC0000BBF)
#define PDH_CSTATUS_BAD_COUNTERNAME ((DWORD)0xC0000BC0)
#define PDH_INVALID_BUFFER ((DWORD)0xC0000BC1)
#define PDH_INSUFFICIENT_BUFFER ((DWORD)0xC0000BC2)
#define PDH_CANNOT_CONNECT_MACHINE ((DWORD)0xC0000BC3)
#define PDH_INVALID_PATH ((DWORD)0xC0000BC4)
#define PDH_INVALID_INSTANCE ((DWORD)0xC0000BC5)
#define PDH_INVALID_DATA ((DWORD)0xC0000BC6)
#define PDH_NO_DIALOG_DATA ((DWORD)0xC0000BC7)
#define PDH_CANNOT_READ_NAME_STRINGS ((DWORD)0xC0000BC8)
#define PDH_LOG_FILE_CREATE_ERROR ((DWORD)0xC0000BC9)
#define PDH_LOG_FILE_OPEN_ERROR ((DWORD)0xC0000BCA)
#define PDH_LOG_TYPE_NOT_FOUND ((DWORD)0xC0000BCB)
#define PDH_NO_MORE_DATA ((DWORD)0xC0000BCC)
#define PDH_ENTRY_NOT_IN_LOG_FILE ((DWORD)0xC0000BCD)
#define PDH_DATA_SOURCE_IS_LOG_FILE ((DWORD)0xC0000BCE)
#define PDH_DATA_SOURCE_IS_REAL_TIME ((DWORD)0xC0000BCF)
#define PDH_UNABLE_READ_LOG_HEADER ((DWORD)0xC0000BD0)
#define PDH_FILE_NOT_FOUND ((DWORD)0xC0000BD1)
#define PDH_FILE_ALREADY_EXISTS ((DWORD)0xC0000BD2)
#define PDH_NOT_IMPLEMENTED ((DWORD)0xC0000BD3)
#define PDH_STRING_NOT_FOUND ((DWORD)0xC0000BD4)
#define PDH_UNABLE_MAP_NAME_FILES ((DWORD)0x80000BD5)
#define PDH_UNKNOWN_LOG_FORMAT ((DWORD)0xC0000BD6)
#define PDH_UNKNOWN_LOGSVC_COMMAND ((DWORD)0xC0000BD7)
#define PDH_LOGSVC_QUERY_NOT_FOUND ((DWORD)0xC0000BD8)
#define PDH_LOGSVC_NOT_OPENED ((DWORD)0xC0000BD9)
#define PDH_WBEM_ERROR ((DWORD)0xC0000BDA)
#define PDH_ACCESS_DENIED ((DWORD)0xC0000BDB)
#define PDH_LOG_FILE_TOO_SMALL ((DWORD)0xC0000BDC)
#define PDH_INVALID_DATASOURCE ((DWORD)0xC0000BDD)
#define PDH_INVALID_SQLDB ((DWORD)0xC0000BDE)
#define PDH_NO_COUNTERS ((DWORD)0xC0000BDF)
#define PDH_SQL_ALLOC_FAILED ((DWORD)0xC0000BE0)
#define PDH_SQL_ALLOCCON_FAILED ((DWORD)0xC0000BE1)
#define PDH_SQL_EXEC_DIRECT_FAILED ((DWORD)0xC0000BE2)
#define PDH_SQL_FETCH_FAILED ((DWORD)0xC0000BE3)
#define PDH_SQL_ROWCOUNT_FAILED ((DWORD)0xC0000BE4)
#define PDH_SQL_MORE_RESULTS_FAILED ((DWORD)0xC0000BE5)
#define PDH_SQL_CONNECT_FAILED ((DWORD)0xC0000BE6)
#define PDH_SQL_BIND_FAILED ((DWORD)0xC0000BE7)
#define PDH_CANNOT_CONNECT_WMI_SERVER ((DWORD)0xC0000BE8)
#define PDH_PLA_COLLECTION_ALREADY_RUNNING ((DWORD)0xC0000BE9)
#define PDH_PLA_ERROR_SCHEDULE_OVERLAP ((DWORD)0xC0000BEA)
#define PDH_PLA_COLLECTION_NOT_FOUND ((DWORD)0xC0000BEB)
#define PDH_PLA_ERROR_SCHEDULE_ELAPSED ((DWORD)0xC0000BEC)
#define PDH_PLA_ERROR_NOSTART ((DWORD)0xC0000BED)
#define PDH_PLA_ERROR_ALREADY_EXISTS ((DWORD)0xC0000BEE)
#define PDH_PLA_ERROR_TYPE_MISMATCH ((DWORD)0xC0000BEF)
#define PDH_PLA_ERROR_FILEPATH ((DWORD)0xC0000BF0)
#define PDH_PLA_SERVICE_ERROR ((DWORD)0xC0000BF1)
#define PDH_PLA_VALIDATION_ERROR ((DWORD)0xC0000BF2)
#define PDH_PLA_VALIDATION_WARNING ((DWORD)0x80000BF3)
#define PDH_PLA_ERROR_NAME_TOO_LONG ((DWORD)0xC0000BF4)
#define PDH_INVALID_SQL_LOG_FORMAT ((DWORD)0xC0000BF5)
#define PDH_COUNTER_ALREADY_IN_QUERY ((DWORD)0xC0000BF6)
#define PDH_BINARY_LOG_CORRUPT ((DWORD)0xC0000BF7)
#define PDH_LOG_SAMPLE_TOO_SMALL ((DWORD)0xC0000BF8)
#define PDH_OS_LATER_VERSION ((DWORD)0xC0000BF9)
#define PDH_OS_EARLIER_VERSION ((DWORD)0xC0000BFA)
#define PDH_INCORRECT_APPEND_TIME ((DWORD)0xC0000BFB)
#define PDH_UNMATCHED_APPEND_COUNTER ((DWORD)0xC0000BFC)
#define PDH_SQL_ALTER_DETAIL_FAILED ((DWORD)0xC0000BFD)
#define PDH_QUERY_PERF_DATA_TIMEOUT ((DWORD)0xC0000BFE)

#endif
