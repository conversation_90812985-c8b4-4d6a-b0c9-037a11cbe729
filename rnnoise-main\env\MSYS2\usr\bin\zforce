#!/bin/sh
# zforce: force a gz extension on all gzip files so that gzip will not
# compress them twice.
#
# This can be useful for files with names truncated after a file transfer.
# 12345678901234 is renamed to 12345678901.gz


# Copyright (C) 2002, 2007, 2010-2025 Free Software Foundation, Inc.
# Copyright (C) 1993 <PERSON><PERSON><PERSON><PERSON><PERSON>

# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.

# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.

version="zforce (gzip) 1.14
Copyright (C) 2025 Free Software Foundation, Inc.
This is free software.  You may redistribute copies of it under the terms of
the GNU General Public License <https://www.gnu.org/licenses/gpl.html>.
There is NO WARRANTY, to the extent permitted by law.

Written by <PERSON><PERSON><PERSON><PERSON><PERSON>."

usage="Usage: $0 [FILE]...
Force a .gz extension on all compressed FILEs so that gzip will
not compress them twice.

Report bugs to <<EMAIL>>."

if test $# = 0; then
  printf >&2 '%s\n' "$0: invalid number of operands; try \`$0 --help' for help"
  exit 1
fi

res=0
for i do
  case "$i" in
  --h*) printf '%s\n' "$usage"   || exit 1; exit;;
  --v*) printf '%s\n' "$version" || exit 1; exit;;
  *[-.]z | *[-.]gz | *.t[ag]z) continue;;
  esac

  if test ! -f "$i" ; then
    printf '%s\n' "zforce: $i not a file"
    res=1
    continue
  fi

  if gzip -lv < "$i" 2>/dev/null | grep '^defl' > /dev/null; then

    new="$i.gz"
    mv "$i" "$new" && printf '%s\n' "$i -- replaced with $new" || res=1
  fi
done
exit $res
