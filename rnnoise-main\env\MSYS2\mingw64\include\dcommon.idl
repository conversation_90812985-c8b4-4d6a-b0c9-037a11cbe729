/*
 * Copyright 2012 Nikolay Sivov for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "dxgiformat.idl";

cpp_quote("#if 0")
typedef struct
{
    long x, y;
} POINT;

typedef struct
{
    long left, top, right, bottom;
} RECT;

typedef unsigned int UINT32;
cpp_quote("#endif")

typedef enum DWRITE_MEASURING_MODE
{
    DWRITE_MEASURING_MODE_NATURAL,
    DWRITE_MEASURING_MODE_GDI_CLASSIC,
    DWRITE_MEASURING_MODE_GDI_NATURAL
} DWRITE_MEASURING_MODE;

typedef enum DWRITE_GLYPH_IMAGE_FORMATS
{
    DWRITE_GLYPH_IMAGE_FORMATS_NONE                   = 0,
    DWRITE_GLYPH_IMAGE_FORMATS_TRUETYPE               = 1 << 0,
    DWRITE_GLYPH_IMAGE_FORMATS_CFF                    = 1 << 1,
    DWRITE_GLYPH_IMAGE_FORMATS_COLR                   = 1 << 2,
    DWRITE_GLYPH_IMAGE_FORMATS_SVG                    = 1 << 3,
    DWRITE_GLYPH_IMAGE_FORMATS_PNG                    = 1 << 4,
    DWRITE_GLYPH_IMAGE_FORMATS_JPEG                   = 1 << 5,
    DWRITE_GLYPH_IMAGE_FORMATS_TIFF                   = 1 << 6,
    DWRITE_GLYPH_IMAGE_FORMATS_PREMULTIPLIED_B8G8R8A8 = 1 << 7,
    DWRITE_GLYPH_IMAGE_FORMATS_COLR_PAINT_TREE        = 1 << 8,
} DWRITE_GLYPH_IMAGE_FORMATS;

cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(DWRITE_GLYPH_IMAGE_FORMATS)")
cpp_quote("#define DWRITE_GLYPH_IMAGE_FORMATS_COLR_PAINT_TREE_DEFINED")

typedef enum D2D1_ALPHA_MODE
{
    D2D1_ALPHA_MODE_UNKNOWN = 0,
    D2D1_ALPHA_MODE_PREMULTIPLIED = 1,
    D2D1_ALPHA_MODE_STRAIGHT = 2,
    D2D1_ALPHA_MODE_IGNORE = 3,
    D2D1_ALPHA_MODE_FORCE_DWORD = 0xffffffff
} D2D1_ALPHA_MODE;

typedef struct D2D1_PIXEL_FORMAT
{
    DXGI_FORMAT format;
    D2D1_ALPHA_MODE alphaMode;
} D2D1_PIXEL_FORMAT;

typedef struct D2D_POINT_2F
{
    float x;
    float y;
} D2D_POINT_2F, D2D1_POINT_2F;

typedef POINT D2D_POINT_2L, D2D1_POINT_2L;

typedef struct D2D_VECTOR_2F
{
    float x;
    float y;
} D2D_VECTOR_2F;

typedef struct D2D_VECTOR_3F
{
    float x;
    float y;
    float z;
} D2D_VECTOR_3F;

typedef struct D2D_VECTOR_4F
{
    float x;
    float y;
    float z;
    float w;
} D2D_VECTOR_4F;

typedef struct D2D_RECT_F
{
    float left;
    float top;
    float right;
    float bottom;
} D2D_RECT_F;

typedef RECT D2D_RECT_L, D2D1_RECT_L;

typedef struct D2D_SIZE_U
{
    UINT32 width;
    UINT32 height;
} D2D_SIZE_U, D2D1_SIZE_U;

typedef struct D2D_MATRIX_3X2_F
{
    union
    {
        struct
        {
            float m11;
            float m12;
            float m21;
            float m22;
            float dx;
            float dy;
        };

        struct
        {
            float _11, _12;
            float _21, _22;
            float _31, _32;
        };

        float m[3][2];
    };
} D2D_MATRIX_3X2_F;

typedef struct D2D_MATRIX_4X3_F
{
    union
    {
        struct
        {
            float _11, _12, _13;
            float _21, _22, _23;
            float _31, _32, _33;
            float _41, _42, _43;
        };

        float m[4][3];
    };
} D2D_MATRIX_4X3_F;

typedef struct D2D_MATRIX_4X4_F
{
    union
    {
        struct
        {
            float _11, _12, _13, _14;
            float _21, _22, _23, _24;
            float _31, _32, _33, _34;
            float _41, _42, _43, _44;
        };

        float m[4][4];
    };
} D2D_MATRIX_4X4_F;

typedef struct D2D_MATRIX_5X4_F
{
    union
    {
        struct
        {
            float _11, _12, _13, _14;
            float _21, _22, _23, _24;
            float _31, _32, _33, _34;
            float _41, _42, _43, _44;
            float _51, _52, _53, _54;
        };

        float m[5][4];
    };
} D2D_MATRIX_5X4_F;
