.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_transport_set_int2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_transport_set_int2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_transport_set_int2(gnutls_session_t " session ", int " recv_fd ", int " send_fd ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "int recv_fd" 12
is socket descriptor for the pull function
.IP "int send_fd" 12
is socket descriptor for the push function
.SH "DESCRIPTION"
This function sets the first argument of the transport functions,
such as \fBsend()\fP and \fBrecv()\fP for the default callbacks using the
system's socket API. With this function you can set two different
descriptors for receiving and sending.

This function is equivalent to calling \fBgnutls_transport_set_ptr2()\fP
with the descriptors, but requires no casts.
.SH "SINCE"
3.1.9
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
