.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_set_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_set_id \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_session_set_id(gnutls_session_t " session ", const gnutls_datum_t * " sid ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "const gnutls_datum_t * sid" 12
the session identifier
.SH "DESCRIPTION"
This function sets the session ID to be used in a client hello.
This is a function intended for exceptional uses. Do not use this
function unless you are implementing a custom protocol.

To set session resumption parameters use \fBgnutls_session_set_data()\fP instead.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "SINCE"
3.2.1
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
