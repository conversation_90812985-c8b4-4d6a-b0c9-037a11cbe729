/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#include <colordlg.h>

ChooseColor DIALOG LOADONCALL MOVEABLE DISCARDABLE 2,0,298,184
STYLE WS_BORDER | DS_MODALFRAME | WS_CAPTION | WS_POPUP | WS_SYSMENU |
      DS_3DLOOK | DS_CONTEXTHELP
CAPTION "Color"
FONT 8 "MS Shell Dlg"
BEGIN
    LTEXT "&Basic colors:",-1,4,4,140,9
    CONTROL "",COLOR_BOX1,"static",SS_SIMPLE | WS_CHILD | WS_TABSTOP | WS_GROUP,4,14,140,86

    LTEXT "&Custom colors:",-1,4,106,140,9
    CONTROL "",COLOR_CUSTOM1,"static",SS_SIMPLE | WS_CHILD | WS_TABSTOP | WS_GROUP,4,116,140,28

    PUSHBUTTON "&Define Custom Colors >>" COLOR_MIX,4,150,140,14,WS_TABSTOP | WS_GROUP

    DEFPUSHBUTTON "OK",IDOK,4,166,44,14,WS_GROUP | WS_TABSTOP
    PUSHBUTTON "Cancel",IDCANCEL,52,166,44,14,WS_GROUP | WS_TABSTOP
    PUSHBUTTON "&Help",pshHelp,100,166,44,14,WS_GROUP | WS_TABSTOP

    CONTROL "",COLOR_RAINBOW,"static",SS_SUNKEN | SS_SIMPLE | WS_CHILD,152,4,118,116

    CONTROL "",COLOR_LUMSCROLL,"static",SS_SUNKEN | SS_SIMPLE | WS_CHILD,280,4,8,116

    CONTROL "",COLOR_CURRENT,"static",SS_SUNKEN | SS_SIMPLE | WS_CHILD,152,124,40,26

    PUSHBUTTON "&o",COLOR_SOLID,300,200,4,14,WS_GROUP
    RTEXT "Color",COLOR_SOLID_LEFT,152,151,20,9
    LTEXT "|S&olid",COLOR_SOLID_RIGHT,172,151,20,9

    RTEXT "Hu&e:",COLOR_HUEACCEL,194,126,20,9
    EDITTEXT,COLOR_HUE,216,124,18,12,WS_GROUP | WS_TABSTOP

    RTEXT "&Sat:",COLOR_SATACCEL,194,140,20,9
    EDITTEXT,COLOR_SAT,216,138,18,12,WS_GROUP | WS_TABSTOP

    RTEXT "&Lum:",COLOR_LUMACCEL,194,154,20,9
    EDITTEXT,COLOR_LUM,216,152,18,12,WS_GROUP | WS_TABSTOP

    RTEXT "&Red:",COLOR_REDACCEL,243,126,24,9
    EDITTEXT,COLOR_RED,269,124,18,12,WS_GROUP | WS_TABSTOP

    RTEXT "&Green:",COLOR_GREENACCEL,243,140,24,9
    EDITTEXT,COLOR_GREEN,269,138,18,12,WS_GROUP | WS_TABSTOP

    RTEXT "Bl&ue:",COLOR_BLUEACCEL,243,154,24,9
    EDITTEXT,COLOR_BLUE,269,152,18,12,WS_GROUP | WS_TABSTOP

    PUSHBUTTON "&Add to Custom Colors",COLOR_ADD,152,166,142,14,WS_GROUP | WS_TABSTOP
END
