# Tcl autoload index file, version 2.0
# This file is generated by the "auto_mkindex" command
# and sourced to set up indexing information for one or
# more commands.  Typically each line is a command that
# sets an element in the auto_index array, where the
# element name is the name of a command and the value is
# a script that loads the command.

set auto_index(::tk::dialog::error::Return) [list source -encoding utf-8 [file join $dir bgerror.tcl]]
set auto_index(::tk::dialog::error::Details) [list source -encoding utf-8 [file join $dir bgerror.tcl]]
set auto_index(::tk::dialog::error::SaveToLog) [list source -encoding utf-8 [file join $dir bgerror.tcl]]
set auto_index(::tk::dialog::error::Destroy) [list source -encoding utf-8 [file join $dir bgerror.tcl]]
set auto_index(::tk::dialog::error::bgerror) [list source -encoding utf-8 [file join $dir bgerror.tcl]]
set auto_index(bgerror) [list source -encoding utf-8 [file join $dir bgerror.tcl]]
set auto_index(::tk::ButtonInvoke) [list source -encoding utf-8 [file join $dir button.tcl]]
set auto_index(::tk::ButtonAutoInvoke) [list source -encoding utf-8 [file join $dir button.tcl]]
set auto_index(::tk::CheckRadioInvoke) [list source -encoding utf-8 [file join $dir button.tcl]]
set auto_index(::tk::dialog::file::chooseDir::) [list source -encoding utf-8 [file join $dir choosedir.tcl]]
set auto_index(::tk::dialog::file::chooseDir::Config) [list source -encoding utf-8 [file join $dir choosedir.tcl]]
set auto_index(::tk::dialog::file::chooseDir::OkCmd) [list source -encoding utf-8 [file join $dir choosedir.tcl]]
set auto_index(::tk::dialog::file::chooseDir::DblClick) [list source -encoding utf-8 [file join $dir choosedir.tcl]]
set auto_index(::tk::dialog::file::chooseDir::ListBrowse) [list source -encoding utf-8 [file join $dir choosedir.tcl]]
set auto_index(::tk::dialog::file::chooseDir::Done) [list source -encoding utf-8 [file join $dir choosedir.tcl]]
set auto_index(::tk::dialog::color::) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::InitValues) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::Config) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::BuildDialog) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::SetRGBValue) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::XToRgb) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::RgbToX) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::DrawColorScale) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::CreateSelector) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::RedrawFinalColor) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::RedrawColorBars) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::StartMove) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::MoveSelector) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::ReleaseMouse) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::ResizeColorBars) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::HandleSelEntry) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::HandleRGBEntry) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::EnterColorBar) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::LeaveColorBar) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::OkCmd) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(::tk::dialog::color::CancelCmd) [list source -encoding utf-8 [file join $dir clrpick.tcl]]
set auto_index(tclParseConfigSpec) [list source -encoding utf-8 [file join $dir comdlg.tcl]]
set auto_index(tclListValidFlags) [list source -encoding utf-8 [file join $dir comdlg.tcl]]
set auto_index(::tk::FocusGroup_Create) [list source -encoding utf-8 [file join $dir comdlg.tcl]]
set auto_index(::tk::FocusGroup_BindIn) [list source -encoding utf-8 [file join $dir comdlg.tcl]]
set auto_index(::tk::FocusGroup_BindOut) [list source -encoding utf-8 [file join $dir comdlg.tcl]]
set auto_index(::tk::FocusGroup_Destroy) [list source -encoding utf-8 [file join $dir comdlg.tcl]]
set auto_index(::tk::FocusGroup_In) [list source -encoding utf-8 [file join $dir comdlg.tcl]]
set auto_index(::tk::FocusGroup_Out) [list source -encoding utf-8 [file join $dir comdlg.tcl]]
set auto_index(::tk::FDGetFileTypes) [list source -encoding utf-8 [file join $dir comdlg.tcl]]
set auto_index(::tk::ConsoleInit) [list source -encoding utf-8 [file join $dir console.tcl]]
set auto_index(::tk::ConsoleSource) [list source -encoding utf-8 [file join $dir console.tcl]]
set auto_index(::tk::ConsoleInvoke) [list source -encoding utf-8 [file join $dir console.tcl]]
set auto_index(::tk::ConsoleHistory) [list source -encoding utf-8 [file join $dir console.tcl]]
set auto_index(::tk::ConsolePrompt) [list source -encoding utf-8 [file join $dir console.tcl]]
set auto_index(::tk::ConsoleBind) [list source -encoding utf-8 [file join $dir console.tcl]]
set auto_index(::tk::ConsoleInsert) [list source -encoding utf-8 [file join $dir console.tcl]]
set auto_index(::tk::ConsoleOutput) [list source -encoding utf-8 [file join $dir console.tcl]]
set auto_index(::tk::ConsoleExit) [list source -encoding utf-8 [file join $dir console.tcl]]
set auto_index(::tk::ConsoleAbout) [list source -encoding utf-8 [file join $dir console.tcl]]
set auto_index(tk_dialog) [list source -encoding utf-8 [file join $dir dialog.tcl]]
set auto_index(::tk::EntryClosestGap) [list source -encoding utf-8 [file join $dir entry.tcl]]
set auto_index(::tk::EntryButton1) [list source -encoding utf-8 [file join $dir entry.tcl]]
set auto_index(::tk::EntryMouseSelect) [list source -encoding utf-8 [file join $dir entry.tcl]]
set auto_index(::tk::EntryPaste) [list source -encoding utf-8 [file join $dir entry.tcl]]
set auto_index(::tk::EntryAutoScan) [list source -encoding utf-8 [file join $dir entry.tcl]]
set auto_index(::tk::EntryKeySelect) [list source -encoding utf-8 [file join $dir entry.tcl]]
set auto_index(::tk::EntryInsert) [list source -encoding utf-8 [file join $dir entry.tcl]]
set auto_index(::tk::EntryBackspace) [list source -encoding utf-8 [file join $dir entry.tcl]]
set auto_index(::tk::EntrySeeInsert) [list source -encoding utf-8 [file join $dir entry.tcl]]
set auto_index(::tk::EntrySetCursor) [list source -encoding utf-8 [file join $dir entry.tcl]]
set auto_index(::tk::EntryTranspose) [list source -encoding utf-8 [file join $dir entry.tcl]]
set auto_index(::tk::EntryPreviousWord) [list source -encoding utf-8 [file join $dir entry.tcl]]
set auto_index(::tk::EntryGetSelection) [list source -encoding utf-8 [file join $dir entry.tcl]]
set auto_index(tk_focusNext) [list source -encoding utf-8 [file join $dir focus.tcl]]
set auto_index(tk_focusPrev) [list source -encoding utf-8 [file join $dir focus.tcl]]
set auto_index(::tk::FocusOK) [list source -encoding utf-8 [file join $dir focus.tcl]]
set auto_index(tk_focusFollowsMouse) [list source -encoding utf-8 [file join $dir focus.tcl]]
set auto_index(::tk::IconList) [list source -encoding utf-8 [file join $dir iconlist.tcl]]
set auto_index(::tk::ListboxBeginSelect) [list source -encoding utf-8 [file join $dir listbox.tcl]]
set auto_index(::tk::ListboxMotion) [list source -encoding utf-8 [file join $dir listbox.tcl]]
set auto_index(::tk::ListboxBeginExtend) [list source -encoding utf-8 [file join $dir listbox.tcl]]
set auto_index(::tk::ListboxBeginToggle) [list source -encoding utf-8 [file join $dir listbox.tcl]]
set auto_index(::tk::ListboxAutoScan) [list source -encoding utf-8 [file join $dir listbox.tcl]]
set auto_index(::tk::ListboxUpDown) [list source -encoding utf-8 [file join $dir listbox.tcl]]
set auto_index(::tk::ListboxExtendUpDown) [list source -encoding utf-8 [file join $dir listbox.tcl]]
set auto_index(::tk::ListboxDataExtend) [list source -encoding utf-8 [file join $dir listbox.tcl]]
set auto_index(::tk::ListboxCancel) [list source -encoding utf-8 [file join $dir listbox.tcl]]
set auto_index(::tk::ListboxSelectAll) [list source -encoding utf-8 [file join $dir listbox.tcl]]
set auto_index(::tk::Megawidget) [list source -encoding utf-8 [file join $dir megawidget.tcl]]
set auto_index(::tk::MbEnter) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MbLeave) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MbPost) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuUnpost) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MbMotion) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MbButtonUp) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuMotion) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuButtonDown) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuLeave) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuInvoke) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuEscape) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuUpArrow) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuDownArrow) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuLeftArrow) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuRightArrow) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuNextMenu) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuNextEntry) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuFind) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::TraverseToMenu) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::FirstMenu) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::TraverseWithinMenu) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuFirstEntry) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::MenuFindName) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::PostOverPoint) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::SaveGrabInfo) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::RestoreOldGrab) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(tk_menuSetFocus) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::GenerateMenuSelect) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(tk_popup) [list source -encoding utf-8 [file join $dir menu.tcl]]
set auto_index(::tk::ensure_psenc_is_loaded) [list source -encoding utf-8 [file join $dir mkpsenc.tcl]]
set auto_index(::tk::MessageBox) [list source -encoding utf-8 [file join $dir msgbox.tcl]]
set auto_index(tk_menuBar) [list source -encoding utf-8 [file join $dir obsolete.tcl]]
set auto_index(tk_bindForTraversal) [list source -encoding utf-8 [file join $dir obsolete.tcl]]
set auto_index(::tk::classic::restore) [list source -encoding utf-8 [file join $dir obsolete.tcl]]
set auto_index(tk_optionMenu) [list source -encoding utf-8 [file join $dir optMenu.tcl]]
set auto_index(tk_setPalette) [list source -encoding utf-8 [file join $dir palette.tcl]]
set auto_index(::tk::RecolorTree) [list source -encoding utf-8 [file join $dir palette.tcl]]
set auto_index(::tk::Darken) [list source -encoding utf-8 [file join $dir palette.tcl]]
set auto_index(tk_bisque) [list source -encoding utf-8 [file join $dir palette.tcl]]
set auto_index(::safe::tkInterpInit) [list source -encoding utf-8 [file join $dir safetk.tcl]]
set auto_index(::safe::loadTk) [list source -encoding utf-8 [file join $dir safetk.tcl]]
set auto_index(::safe::TkInit) [list source -encoding utf-8 [file join $dir safetk.tcl]]
set auto_index(::safe::allowTk) [list source -encoding utf-8 [file join $dir safetk.tcl]]
set auto_index(::safe::disallowTk) [list source -encoding utf-8 [file join $dir safetk.tcl]]
set auto_index(::safe::tkDelete) [list source -encoding utf-8 [file join $dir safetk.tcl]]
set auto_index(::safe::tkTopLevel) [list source -encoding utf-8 [file join $dir safetk.tcl]]
set auto_index(::tk::ScaleActivate) [list source -encoding utf-8 [file join $dir scale.tcl]]
set auto_index(::tk::ScaleButtonDown) [list source -encoding utf-8 [file join $dir scale.tcl]]
set auto_index(::tk::ScaleDrag) [list source -encoding utf-8 [file join $dir scale.tcl]]
set auto_index(::tk::ScaleEndDrag) [list source -encoding utf-8 [file join $dir scale.tcl]]
set auto_index(::tk::ScaleIncrement) [list source -encoding utf-8 [file join $dir scale.tcl]]
set auto_index(::tk::ScaleControlPress) [list source -encoding utf-8 [file join $dir scale.tcl]]
set auto_index(::tk::ScaleButton2Down) [list source -encoding utf-8 [file join $dir scale.tcl]]
set auto_index(::tk::ScrollButtonDown) [list source -encoding utf-8 [file join $dir scrlbar.tcl]]
set auto_index(::tk::ScrollButtonUp) [list source -encoding utf-8 [file join $dir scrlbar.tcl]]
set auto_index(::tk::ScrollSelect) [list source -encoding utf-8 [file join $dir scrlbar.tcl]]
set auto_index(::tk::ScrollStartDrag) [list source -encoding utf-8 [file join $dir scrlbar.tcl]]
set auto_index(::tk::ScrollDrag) [list source -encoding utf-8 [file join $dir scrlbar.tcl]]
set auto_index(::tk::ScrollEndDrag) [list source -encoding utf-8 [file join $dir scrlbar.tcl]]
set auto_index(::tk::ScrollByUnits) [list source -encoding utf-8 [file join $dir scrlbar.tcl]]
set auto_index(::tk::ScrollByPages) [list source -encoding utf-8 [file join $dir scrlbar.tcl]]
set auto_index(::tk::ScrollToPos) [list source -encoding utf-8 [file join $dir scrlbar.tcl]]
set auto_index(::tk::ScrollTopBottom) [list source -encoding utf-8 [file join $dir scrlbar.tcl]]
set auto_index(::tk::ScrollButton2Down) [list source -encoding utf-8 [file join $dir scrlbar.tcl]]
set auto_index(::tk::spinbox::Invoke) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::ClosestGap) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::ButtonDown) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::ButtonUp) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::MouseSelect) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::Paste) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::Motion) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::AutoScan) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::KeySelect) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::Insert) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::Backspace) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::SeeInsert) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::SetCursor) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::Transpose) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::PreviousWord) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::spinbox::GetSelection) [list source -encoding utf-8 [file join $dir spinbox.tcl]]
set auto_index(::tk::TearOffMenu) [list source -encoding utf-8 [file join $dir tearoff.tcl]]
set auto_index(::tk::MenuDup) [list source -encoding utf-8 [file join $dir tearoff.tcl]]
set auto_index(::tk::TextClosestGap) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextButton1) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextSelectTo) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextKeyExtend) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextPaste) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextAutoScan) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextSetCursor) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextKeySelect) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextResetAnchor) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextInsert) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextUpDownLine) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextPrevPara) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextNextPara) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextScrollPages) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextTranspose) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(tk_textCopy) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(tk_textCut) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(tk_textPaste) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextNextPos) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::TextPrevPos) [list source -encoding utf-8 [file join $dir text.tcl]]
set auto_index(::tk::PlaceWindow) [list source -encoding utf-8 [file join $dir tk.tcl]]
set auto_index(::tk::SetFocusGrab) [list source -encoding utf-8 [file join $dir tk.tcl]]
set auto_index(::tk::RestoreFocusGrab) [list source -encoding utf-8 [file join $dir tk.tcl]]
set auto_index(::tk::ScreenChanged) [list source -encoding utf-8 [file join $dir tk.tcl]]
set auto_index(::tk::EventMotifBindings) [list source -encoding utf-8 [file join $dir tk.tcl]]
set auto_index(::tk::CancelRepeat) [list source -encoding utf-8 [file join $dir tk.tcl]]
set auto_index(::tk::TabToWindow) [list source -encoding utf-8 [file join $dir tk.tcl]]
set auto_index(::tk::dialog::file::) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::Config) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::Create) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::SetSelectMode) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::UpdateWhenIdle) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::Update) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::SetPathSilently) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::SetPath) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::SetFilter) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::ResolveFile) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::EntFocusIn) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::EntFocusOut) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::ActivateEnt) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::VerifyFileName) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::InvokeBtn) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::UpDirCmd) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::JoinFile) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::OkCmd) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::CancelCmd) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::ListBrowse) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::ListInvoke) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::dialog::file::Done) [list source -encoding utf-8 [file join $dir tkfbox.tcl]]
set auto_index(::tk::MotifFDialog) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_Create) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_FileTypes) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_SetFilter) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_Config) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_BuildUI) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_SetListMode) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_MakeSList) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_InterpFilter) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_Update) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_LoadFiles) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_BrowseDList) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_ActivateDList) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_BrowseFList) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_ActivateFList) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_ActivateFEnt) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_ActivateSEnt) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_OkCmd) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_FilterCmd) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::MotifFDialog_CancelCmd) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::ListBoxKeyAccel_Set) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::ListBoxKeyAccel_Unset) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::ListBoxKeyAccel_Key) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::ListBoxKeyAccel_Goto) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::ListBoxKeyAccel_Reset) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(tk_getFileType) [list source -encoding utf-8 [file join $dir xmfbox.tcl]]
set auto_index(::tk::unsupported::ExposePrivateCommand) [list source -encoding utf-8 [file join $dir unsupported.tcl]]
set auto_index(::tk::unsupported::ExposePrivateVariable) [list source -encoding utf-8 [file join $dir unsupported.tcl]]
set auto_index(::tk::fontchooser) [list source -encoding utf-8 [file join $dir fontchooser.tcl]]
