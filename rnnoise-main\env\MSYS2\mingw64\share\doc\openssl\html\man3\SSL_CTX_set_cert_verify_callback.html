<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CTX_set_cert_verify_callback</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#WARNINGS">WARNINGS</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_CTX_set_cert_verify_callback - set peer certificate verification procedure</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

void SSL_CTX_set_cert_verify_callback(SSL_CTX *ctx,
                                      int (*callback)(X509_STORE_CTX *, void *),
                                      void *arg);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_CTX_set_cert_verify_callback() sets the verification callback function for <i>ctx</i>. SSL objects that are created from <i>ctx</i> inherit the setting valid at the time when <a href="../man3/SSL_new.html">SSL_new(3)</a> is called.</p>

<h1 id="NOTES">NOTES</h1>

<p>When a peer certificate has been received during an SSL/TLS handshake, a verification function is called regardless of the verification mode. If the application does not explicitly specify a verification callback function, the built-in verification function is used. If a verification callback <i>callback</i> is specified via SSL_CTX_set_cert_verify_callback(), the supplied callback function is called instead with the arguments callback(X509_STORE_CTX *x509_store_ctx, void *arg). The argument <i>arg</i> is specified by the application when setting <i>callback</i>. By setting <i>callback</i> to NULL, the default behaviour is restored.</p>

<p><i>callback</i> should return 1 to indicate verification success and 0 to indicate verification failure. In server mode, a return value of 0 leads to handshake failure. In client mode, the behaviour is as follows. All values, including 0, are ignored if the verification mode is <b>SSL_VERIFY_NONE</b>. Otherwise, when the return value is less than or equal to 0, the handshake will fail.</p>

<p>In client mode <i>callback</i> may also call the <a href="../man3/SSL_set_retry_verify.html">SSL_set_retry_verify(3)</a> function on the <b>SSL</b> object set in the <i>x509_store_ctx</i> ex data (see <a href="../man3/SSL_get_ex_data_X509_STORE_CTX_idx.html">SSL_get_ex_data_X509_STORE_CTX_idx(3)</a>) and return 1. This would be typically done in case the certificate verification was not yet able to succeed. This makes the handshake suspend and return control to the calling application with <b>SSL_ERROR_WANT_RETRY_VERIFY</b>. The app can for instance fetch further certificates or cert status information needed for the verification. Calling <a href="../man3/SSL_connect.html">SSL_connect(3)</a> again resumes the connection attempt by retrying the server certificate verification step. This process may even be repeated if need be.</p>

<p>In any case a viable verification result value must be reflected in the <b>error</b> member of <i>x509_store_ctx</i>, which can be done using <a href="../man3/X509_STORE_CTX_set_error.html">X509_STORE_CTX_set_error(3)</a>. This is particularly important in case the <i>callback</i> allows the connection to continue (by returning 1). Note that the verification status in the store context is a possibly durable indication of the chain&#39;s validity! This gets recorded in the SSL session (and thus also in session tickets) and the validity of the originally presented chain is then visible on resumption, even though no chain is presented int that case. Moreover, the calling application will be informed about the detailed result of the verification procedure and may elect to base further decisions on it.</p>

<p>Within <i>x509_store_ctx</i>, <i>callback</i> has access to the <i>verify_callback</i> function set using <a href="../man3/SSL_CTX_set_verify.html">SSL_CTX_set_verify(3)</a>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_CTX_set_cert_verify_callback() does not return a value.</p>

<h1 id="WARNINGS">WARNINGS</h1>

<p>Do not mix the verification callback described in this function with the <b>verify_callback</b> function called during the verification process. The latter is set using the <a href="../man3/SSL_CTX_set_verify.html">SSL_CTX_set_verify(3)</a> family of functions.</p>

<p>Providing a complete verification procedure including certificate purpose settings etc is a complex task. The built-in procedure is quite powerful and in most cases it should be sufficient to modify its behaviour using the <b>verify_callback</b> function.</p>

<h1 id="BUGS">BUGS</h1>

<p>SSL_CTX_set_cert_verify_callback() does not provide diagnostic information.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_CTX_set_verify.html">SSL_CTX_set_verify(3)</a>, <a href="../man3/X509_STORE_CTX_set_error.html">X509_STORE_CTX_set_error(3)</a>, <a href="../man3/SSL_get_verify_result.html">SSL_get_verify_result(3)</a>, <a href="../man3/SSL_set_retry_verify.html">SSL_set_retry_verify(3)</a>, <a href="../man3/SSL_CTX_load_verify_locations.html">SSL_CTX_load_verify_locations(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2001-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


