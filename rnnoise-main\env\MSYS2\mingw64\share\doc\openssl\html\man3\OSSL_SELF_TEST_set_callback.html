<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_SELF_TEST_set_callback</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_SELF_TEST_set_callback, OSSL_SELF_TEST_get_callback - specify a callback for processing self tests</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/self_test.h&gt;

void OSSL_SELF_TEST_set_callback(OSSL_LIB_CTX *ctx, OSSL_CALLBACK *cb, void *cbarg);
void OSSL_SELF_TEST_get_callback(OSSL_LIB_CTX *ctx, OSSL_CALLBACK **cb, void **cbarg);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Set or gets the optional application callback (and the callback argument) that is called during self testing. The application callback <a href="../man3/OSSL_CALLBACK.html">OSSL_CALLBACK(3)</a> is associated with a <b>OSSL_LIB_CTX</b>. The application callback function receives information about a running self test, and may return a result to the calling self test. See <a href="../man7/openssl-core.h.html">openssl-core.h(7)</a> for further information on the callback.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_SELF_TEST_get_callback() returns the callback and callback argument that has been set via OSSL_SELF_TEST_set_callback() for the given library context <i>ctx</i>. These returned parameters will be NULL if OSSL_SELF_TEST_set_callback() has not been called.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/openssl-core.h.html">openssl-core.h(7)</a>, <a href="../man7/OSSL_PROVIDER-FIPS.html">OSSL_PROVIDER-FIPS(7)</a> <a href="../man3/OSSL_SELF_TEST_new.html">OSSL_SELF_TEST_new(3)</a> <a href="../man3/OSSL_LIB_CTX.html">OSSL_LIB_CTX(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The functions described here were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


