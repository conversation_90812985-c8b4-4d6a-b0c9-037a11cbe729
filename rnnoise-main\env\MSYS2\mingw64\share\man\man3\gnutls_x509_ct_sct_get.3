.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_ct_sct_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_ct_sct_get \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_ct_sct_get(const gnutls_x509_ct_scts_t " scts ", unsigned " idx ", time_t * " timestamp ", gnutls_datum_t * " logid ", gnutls_sign_algorithm_t * " sigalg ", gnutls_datum_t * " signature ");"
.SH ARGUMENTS
.IP "const gnutls_x509_ct_scts_t scts" 12
A list of SCTs
.IP "unsigned idx" 12
The index of the target SCT in the list
.IP "time_t * timestamp" 12
The timestamp of the SCT
.IP "gnutls_datum_t * logid" 12
The LogID field of the SCT; must be freed with \fBgnutls_free()\fP
.IP "gnutls_sign_algorithm_t * sigalg" 12
The signature algorithm
.IP "gnutls_datum_t * signature" 12
The signature of the SCT; must be freed with \fBgnutls_free()\fP
.SH "DESCRIPTION"
This function will return a specific SCT (Signed Certificate Timestamp)
stored in the SCT list  \fIscts\fP .

The datums holding the SCT's LogId and signature will be allocated
using \fBgnutls_malloc()\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) will be returned on success,
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP if  \fIidx\fP exceeds the number of SCTs in the list
or a negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
