diff_cmd () {
	"$merge_tool_path" "$LOCAL" "$REMOTE"
}

diff_cmd_help () {
	echo "Use <PERSON><PERSON><PERSON>'s Diff Tool (requires a graphical session)"
}

merge_cmd () {
	if $base_present
	then
		"$merge_tool_path" -s "$LOCAL" \
		"$REMOTE" "$BASE" "$MERGED"
	else
		"$merge_tool_path" -m "$LOCAL" \
		"$REMOTE" "$MERGED"
	fi
}

merge_cmd_help () {
	echo "Use <PERSON><PERSON><PERSON>'s Diff Tool (requires a graphical session)"
}

exit_code_trustable () {
	true
}
