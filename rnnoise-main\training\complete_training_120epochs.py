#!/usr/bin/env python3
"""
完整的RNN噪声抑制与人声增强模型训练脚本 - 120轮训练
详细说明特征来源和训练过程
"""

import tensorflow as tf
import numpy as np
import h5py
import os
from datetime import datetime
import pickle

# 设置CPU运行
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

def print_feature_explanation():
    """详细说明特征的来源和构成"""
    print("=" * 80)
    print("特征详细说明")
    print("=" * 80)
    
    print("\n📊 输入特征构成 (68维):")
    print("├── 原始RNNoise特征 (38维)")
    print("│   ├── 频带能量特征 (18维)")
    print("│   │   └── 基于Bark频带的能量分布，覆盖0-8kHz")
    print("│   ├── 倒谱特征 (18维)")
    print("│   │   └── DCT变换后的频谱特征，用于语音识别")
    print("│   ├── 基频特征 (1维)")
    print("│   │   └── 音调周期信息，用于语音/非语音判断")
    print("│   └── 频谱变异性 (1维)")
    print("│       └── 频谱稳定性指标，辅助VAD检测")
    print("│")
    print("└── 人声增强特征 (30维)")
    print("    ├── 基频相关特征 (8维)")
    print("    │   ├── 基频值 (归一化到0-1)")
    print("    │   ├── 基频稳定性 (连续帧间基频变化)")
    print("    │   ├── 基频强度 (谐波能量占比)")
    print("    │   ├── 基频置信度 (基频检测可靠性)")
    print("    │   ├── 基频轨迹平滑度")
    print("    │   ├── 谐波存在性指标")
    print("    │   ├── 基频调制深度")
    print("    │   └── 基频颤音特征")
    print("    │")
    print("    ├── 共振峰特征 (6维)")
    print("    │   ├── F1频率位置 (第一共振峰)")
    print("    │   ├── F2频率位置 (第二共振峰)")
    print("    │   ├── F3频率位置 (第三共振峰)")
    print("    │   ├── F1带宽")
    print("    │   ├── F2带宽")
    print("    │   └── F3带宽")
    print("    │")
    print("    ├── 谐波结构特征 (8维)")
    print("    │   ├── 谐波-噪声比 (HNR)")
    print("    │   ├── 谐波规律性指标")
    print("    │   ├── 第1谐波能量")
    print("    │   ├── 第2谐波能量")
    print("    │   ├── 第3谐波能量")
    print("    │   ├── 第4谐波能量")
    print("    │   ├── 谐波失真度量")
    print("    │   └── 谐波稳定性")
    print("    │")
    print("    └── 语音清晰度特征 (8维)")
    print("        ├── 频谱重心 (能量集中频率)")
    print("        ├── 频谱扩散度 (能量分布宽度)")
    print("        ├── 总能量")
    print("        ├── 动态范围指标")
    print("        ├── 调制频谱特征")
    print("        ├── 语音清晰度指数")
    print("        ├── 辅音/元音比")
    print("        └── 语音自然度指标")
    
    print("\n🎯 输出目标构成 (37维):")
    print("├── 噪声抑制目标 (18维)")
    print("│   └── 每个频带的增益值 [0,1]，1表示保持，0表示完全抑制")
    print("├── 人声增强目标 (18维)")
    print("│   └── 每个频带的增强值 [0,1.5]，>1表示增强，<1表示衰减")
    print("└── VAD目标 (1维)")
    print("    └── 语音活动概率 [0,1]，1表示有语音，0表示静音")
    
    print("\n🔬 特征提取过程:")
    print("1. 音频预处理: 16kHz采样，160样本/帧 (10ms)")
    print("2. FFT变换: 512点FFT，获得频域信息")
    print("3. Bark频带分析: 18个频带，模拟人耳感知")
    print("4. 基频检测: 自相关法检测音调周期")
    print("5. 共振峰分析: LPC分析提取共振峰")
    print("6. 谐波分析: 基于基频的谐波结构分析")
    print("7. 特征归一化: 所有特征归一化到合理范围")

def create_enhanced_model():
    """创建增强的RNN模型"""
    print("\n🏗️ 构建增强RNN模型...")
    
    # 输入层 (68维)
    inputs = tf.keras.Input(shape=(None, 68), name='main_input')
    
    # 特征分离
    noise_features = inputs[:, :, :38]  # 前38维用于噪声抑制
    voice_features = inputs[:, :, 38:]  # 后30维用于人声增强
    
    # 共享特征处理层
    shared_dense = tf.keras.layers.Dense(32, activation='tanh', name='shared_dense')(inputs)
    
    # VAD分支 (基于噪声抑制特征)
    vad_input = tf.keras.layers.concatenate([shared_dense, noise_features])
    vad_gru = tf.keras.layers.GRU(24, activation='tanh', return_sequences=True, name='vad_gru')(vad_input)
    vad_output = tf.keras.layers.Dense(1, activation='sigmoid', name='vad_output')(vad_gru)
    
    # 噪声抑制分支
    noise_input = tf.keras.layers.concatenate([shared_dense, vad_gru, noise_features])
    noise_gru = tf.keras.layers.GRU(48, activation='relu', return_sequences=True, name='noise_gru')(noise_input)
    
    # 人声增强分支
    voice_input = tf.keras.layers.concatenate([shared_dense, vad_gru, voice_features])
    voice_gru = tf.keras.layers.GRU(48, activation='tanh', return_sequences=True, name='voice_gru')(voice_input)
    
    # 特征融合层
    fused_input = tf.keras.layers.concatenate([vad_gru, noise_gru, voice_gru, inputs])
    final_gru = tf.keras.layers.GRU(96, activation='tanh', return_sequences=True, name='final_gru')(fused_input)
    
    # 双重输出
    noise_suppression_output = tf.keras.layers.Dense(18, activation='sigmoid', name='noise_suppression_output')(final_gru)
    voice_enhancement_output = tf.keras.layers.Dense(18, activation='sigmoid', name='voice_enhancement_output')(final_gru)
    
    # 构建模型
    model = tf.keras.Model(inputs=inputs, outputs=[noise_suppression_output, voice_enhancement_output, vad_output])
    
    return model

def stable_noise_loss(y_true, y_pred):
    """稳定的噪声抑制损失函数"""
    # 创建mask，处理-1标记的无效数据
    mask = tf.cast(tf.greater(y_true, -0.5), tf.float32)
    
    # 确保数值稳定性
    y_pred_safe = tf.clip_by_value(y_pred, 1e-7, 1.0 - 1e-7)
    y_true_safe = tf.maximum(y_true, 0.0)
    
    # MSE损失
    mse_loss = tf.square(y_pred_safe - y_true_safe)
    
    # 应用mask
    masked_loss = mask * mse_loss
    
    # 计算平均值
    sum_mask = tf.reduce_sum(mask, axis=-1)
    sum_mask = tf.maximum(sum_mask, 1.0)
    
    return tf.reduce_sum(masked_loss, axis=-1) / sum_mask

def stable_voice_loss(y_true, y_pred):
    """稳定的人声增强损失函数"""
    # 创建mask
    mask = tf.cast(tf.greater(y_true, -0.5), tf.float32)
    
    # 确保数值稳定性
    y_pred_safe = tf.clip_by_value(y_pred, 1e-7, 1.5)
    y_true_safe = tf.maximum(y_true, 0.0)
    
    # MSE损失
    mse_loss = tf.square(y_pred_safe - y_true_safe)
    
    # 高频增强权重
    freq_weights = tf.constant([1.0, 1.0, 1.1, 1.1, 1.2, 1.2, 1.3, 1.3, 1.4, 
                               1.4, 1.5, 1.5, 1.6, 1.6, 1.7, 1.7, 1.8, 1.8])
    freq_weights = tf.reshape(freq_weights, [1, 1, 18])
    
    # 加权损失
    weighted_loss = mse_loss * freq_weights
    masked_loss = mask * weighted_loss
    
    # 计算平均值
    sum_mask = tf.reduce_sum(mask, axis=-1)
    sum_mask = tf.maximum(sum_mask, 1.0)
    
    return tf.reduce_sum(masked_loss, axis=-1) / sum_mask

def load_training_data():
    """加载训练数据"""
    print("\n📂 加载训练数据...")
    
    data_file = 'training_data.h5'
    if not os.path.exists(data_file):
        print(f"❌ 找不到训练数据文件: {data_file}")
        return None, None, None, None
    
    with h5py.File(data_file, 'r') as hf:
        all_data = hf['data'][:]
    
    print(f"原始数据形状: {all_data.shape}")
    
    # 数据预处理
    window_size = 2000
    nb_sequences = len(all_data) // window_size
    
    print(f"训练序列数量: {nb_sequences}")
    print(f"每个序列长度: {window_size}")
    print(f"总训练样本: {nb_sequences * window_size}")
    
    # 输入特征 (68维)
    x_train = all_data[:nb_sequences*window_size, :68]
    x_train = np.reshape(x_train, (nb_sequences, window_size, 68))
    
    # 噪声抑制目标 (18维)
    noise_train = all_data[:nb_sequences*window_size, 68:86]
    noise_train = np.reshape(noise_train, (nb_sequences, window_size, 18))
    
    # 人声增强目标 (18维)
    voice_train = all_data[:nb_sequences*window_size, 86:104]
    voice_train = np.reshape(voice_train, (nb_sequences, window_size, 18))
    
    # VAD目标 (1维)
    vad_train = all_data[:nb_sequences*window_size, 104:105]
    vad_train = np.reshape(vad_train, (nb_sequences, window_size, 1))
    
    print("训练数据形状:")
    print(f"  输入特征: {x_train.shape}")
    print(f"  噪声抑制目标: {noise_train.shape}")
    print(f"  人声增强目标: {voice_train.shape}")
    print(f"  VAD目标: {vad_train.shape}")
    
    return x_train, noise_train, voice_train, vad_train

def train_complete_model():
    """完整的120轮训练"""
    print("=" * 80)
    print("RNN噪声抑制与人声增强模型 - 完整120轮训练")
    print("数据源: clean_voice + wind_noise_voice (400对音频文件)")
    print("=" * 80)
    
    # 显示特征说明
    print_feature_explanation()
    
    # 加载数据
    x_train, noise_train, voice_train, vad_train = load_training_data()
    if x_train is None:
        return
    
    # 创建模型
    model = create_enhanced_model()
    
    # 显示模型结构
    print("\n📋 模型结构:")
    model.summary()
    
    # 编译模型
    print("\n⚙️ 编译模型...")
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=5e-5, clipnorm=0.5),
        loss={
            'noise_suppression_output': stable_noise_loss,
            'voice_enhancement_output': stable_voice_loss,
            'vad_output': 'binary_crossentropy'
        },
        loss_weights={
            'noise_suppression_output': 5.0,
            'voice_enhancement_output': 3.0,
            'vad_output': 0.2
        },
        metrics={
            'noise_suppression_output': ['mae'],
            'voice_enhancement_output': ['mae'],
            'vad_output': ['accuracy']
        }
    )
    
    # 设置回调
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_dir = f"../models_enhanced/complete_120epochs_{timestamp}"
    os.makedirs(model_dir, exist_ok=True)
    
    callbacks = [
        tf.keras.callbacks.ModelCheckpoint(
            filepath=f"{model_dir}/model_epoch_{{epoch:03d}}_loss_{{loss:.4f}}.keras",
            monitor='loss',
            save_best_only=False,  # 保存所有epoch
            save_freq='epoch',
            verbose=1
        ),
        tf.keras.callbacks.ModelCheckpoint(
            filepath=f"{model_dir}/best_model.keras",
            monitor='val_loss',
            save_best_only=True,
            verbose=1
        ),
        tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.8,
            patience=10,
            min_lr=1e-6,
            verbose=1
        ),
        tf.keras.callbacks.CSVLogger(
            f"{model_dir}/training_log.csv",
            append=True
        )
    ]
    
    # 开始训练
    print(f"\n🚀 开始120轮训练...")
    print(f"模型保存目录: {model_dir}")
    print("训练配置:")
    print("  - 批次大小: 16")
    print("  - 训练轮数: 120")
    print("  - 验证集比例: 10%")
    print("  - 学习率: 5e-5")
    print("  - 梯度裁剪: 0.5")
    
    history = model.fit(
        x_train,
        {
            'noise_suppression_output': noise_train,
            'voice_enhancement_output': voice_train,
            'vad_output': vad_train
        },
        batch_size=16,
        epochs=120,
        validation_split=0.1,
        callbacks=callbacks,
        verbose=1
    )
    
    # 保存最终模型和训练历史
    final_model_path = f"{model_dir}/final_model_120epochs.keras"
    model.save(final_model_path)
    
    with open(f"{model_dir}/training_history.pkl", 'wb') as f:
        pickle.dump(history.history, f)
    
    print(f"\n🎉 120轮训练完成!")
    print(f"最终模型: {final_model_path}")
    print(f"最佳模型: {model_dir}/best_model.keras")
    print(f"训练日志: {model_dir}/training_log.csv")
    print(f"所有epoch模型保存在: {model_dir}/")
    
    return model, history

if __name__ == '__main__':
    try:
        model, history = train_complete_model()
        print("\n✅ 训练成功完成!")
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
