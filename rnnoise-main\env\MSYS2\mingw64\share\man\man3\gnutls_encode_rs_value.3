.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_encode_rs_value" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_encode_rs_value \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_encode_rs_value(gnutls_datum_t * " sig_value ", const gnutls_datum_t * " r ", const gnutls_datum_t * " s ");"
.SH ARGUMENTS
.IP "gnutls_datum_t * sig_value" 12
will hold a Dss\-Sig\-Value DER encoded structure
.IP "const gnutls_datum_t * r" 12
must contain the r value
.IP "const gnutls_datum_t * s" 12
must contain the s value
.SH "DESCRIPTION"
This function will encode the provided r and s values, 
into a Dss\-Sig\-Value structure, used for DSA and ECDSA
signatures.

The output value should be deallocated using \fBgnutls_free()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "SINCE"
3.6.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
