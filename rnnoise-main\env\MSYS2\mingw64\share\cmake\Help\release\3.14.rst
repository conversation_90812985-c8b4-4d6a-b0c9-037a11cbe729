CMake 3.14 Release Notes
************************

.. only:: html

  .. contents::

Changes made since CMake 3.13 include the following.

New Features
============

Generators
----------

* The :generator:`Visual Studio 16 2019` generator was added.  This is
  experimental and based on "Visual Studio 2019 Preview 4" because this
  version of VS has not been released.

  The VS 2019 generator differs from generators for earlier versions
  in that it does not provide variants that specify the target platform
  in the generator name.  Instead :variable:`CMAKE_GENERATOR_PLATFORM`
  must be used, e.g. through the ``-A`` command-line option.  Furthermore,
  the default target platform (architecture) is now based on the *host*
  platform.  The VS host toolset selection is now based on the host
  architecture as well.

* The :generator:`Green Hills MULTI` generator has been updated:

  * Now supports :ref:`Object Libraries`.

  * Now warns on unsupported project types such as shared libraries.

  * Now generates a top-level ``<PROJECT-NAME>.top.gpj`` for each directory
    calling the :command:`project` command.  The top-level project file
    ``default.gpj`` is no longer created.

  * Now honors target renaming and destination output control properties
    such as :prop_tgt:`RUNTIME_OUTPUT_DIRECTORY` and :prop_tgt:`OUTPUT_NAME`.
    This also fixes support for installation rules generated by
    :command:`install`.

  * Now honors source file properties :prop_sf:`INCLUDE_DIRECTORIES`,
    :prop_sf:`COMPILE_DEFINITIONS`, and :prop_sf:`COMPILE_OPTIONS`.

  * Now supports Dynamic Download Integrity Applications which did not include
    Integrate Files via :prop_tgt:`GHS_INTEGRITY_APP` and setting a target
    link flag of ``-dynamic``.

  * The contents of project files now sorts sources groups and files by name.
    Set the :prop_tgt:`GHS_NO_SOURCE_GROUP_FILE` target property to ``ON`` to
    generate a single project file for the target instead of a project file for
    each source group.  Set the :variable:`CMAKE_GHS_NO_SOURCE_GROUP_FILE`
    variable to enable this for all targets.

File-Based API
--------------

* A file-based api for clients to get semantic buildsystem information
  has been added.  See the :manual:`cmake-file-api(7)` manual.
  This is intended to replace the :manual:`cmake-server(7)` mode for IDEs.

Platforms
---------

* CMake now supports
  :ref:`Cross Compiling for iOS, tvOS, or watchOS <Cross Compiling for iOS, tvOS, visionOS, or watchOS>`
  using simple toolchain files.

Command-Line
------------

* The :manual:`cmake(1)` :ref:`Build Tool Mode <Build Tool Mode>`
  (``cmake --build``) gained ``--verbose`` and ``-v`` options to
  specify verbose build output. Some generators such as Xcode don't
  support this option currently.

* The :manual:`cmake(1)` ``-E compare_files`` command learned a new
  ``--ignore-eol`` option to specify that end-of-line differences
  (e.g. LF vs CRLF) should be ignored when comparing files.

* The :manual:`cmake-gui(1)` dialog gained new ``-S`` and ``-B`` arguments to
  explicitly specify source and build directories.

Commands
--------

* The :command:`file` command learned a new sub-command, ``CREATE_LINK``,
  which can be used to create hard or symbolic links.

* The :command:`file` command learned a new sub-command, ``READ_SYMLINK``,
  which can be used to determine the path that a symlink points to.

* The :command:`file` command gained a ``SIZE`` mode to get the size
  of a file on disk.

* The :command:`find_package` command learned to optionally resolve
  symbolic links in the paths to package configuration files.
  See the :variable:`CMAKE_FIND_PACKAGE_RESOLVE_SYMLINKS` variable.

* The :command:`get_filename_component` command gained new
  ``LAST_EXT`` and ``NAME_WLE`` variants to work with the
  extension after the last ``.`` in the name.

* The :command:`if` command gained support for checking if cache variables
  are defined with the  ``DEFINED CACHE{VAR}`` syntax.

* The :command:`install(CODE)` and :command:`install(SCRIPT)` commands
  learned to support generator expressions.  See policy :policy:`CMP0087`.

* The :command:`install(TARGETS)` command learned how to install to an
  appropriate default directory for a given target type, based on
  variables from the :module:`GNUInstallDirs` module and built-in defaults,
  in lieu of a ``DESTINATION`` argument.

* The :command:`install(FILES)` and :command:`install(DIRECTORY)` commands
  learned a new set of parameters for installing files as a file type,
  setting the destination based on the appropriate variables from
  :module:`GNUInstallDirs` and built-in defaults, in lieu of a
  ``DESTINATION`` argument.

* The :command:`list` operations ``REMOVE_ITEM``, ``REMOVE_DUPLICATES``,
  ``SORT``, ``REVERSE``, and ``FILTER`` all now accept a non-existent variable
  as the list since these operations on empty lists is also the empty list.

* The :command:`list` operation ``REMOVE_AT`` now indicates that the given
  indices are invalid for a non-existent variable or empty list.

* The :command:`try_compile` and :command:`try_run` commands gained a new
  ``LINK_OPTIONS`` option.

Variables
---------

* A :variable:`CMAKE_BUILD_RPATH_USE_ORIGIN` variable and corresponding
  :prop_tgt:`BUILD_RPATH_USE_ORIGIN` target property were added to
  enable use of relative runtime paths (RPATHs). This helps achieving
  relocatable and reproducible builds that are invariant of the build
  directory.

* A :variable:`CMAKE_VS_PLATFORM_NAME_DEFAULT` variable was added for
  :ref:`Visual Studio Generators` to report their default platform used
  when :variable:`CMAKE_GENERATOR_PLATFORM` is not set explicitly.

Properties
----------

* A :prop_gbl:`CMAKE_ROLE` global property was added to allow scripts to
  determine whether they're running in project mode, script mode,
  find-package mode, CTest, or CPack.

* The :prop_tgt:`CUDA_RESOLVE_DEVICE_SYMBOLS` target property is now supported
  on shared library, module library, and executable targets.  Previously it was
  only honored on static libraries.

* The :prop_tgt:`EXCLUDE_FROM_ALL` target property was created to override
  the setting of its directory. A target will now be built as part of "all"
  if its :prop_tgt:`EXCLUDE_FROM_ALL` property is set to ``OFF``, even if its
  containing directory is marked as :prop_dir:`EXCLUDE_FROM_ALL`.

* :prop_tgt:`INTERFACE_POSITION_INDEPENDENT_CODE` target property gains the
  support of :manual:`generator expressions <cmake-generator-expressions(7)>`.

Modules
-------

* The family of modules to check capabilities (like
  :module:`CheckCSourceCompiles`) gain capability to manage ``LINK_OPTIONS``.

* A :module:`CheckFortranSourceRuns` module was added to provide a
  :command:`check_fortran_source_runs` command to check if a Fortran
  source snippet compiles and runs.

* The :module:`CMakePackageConfigHelpers` module's
  :command:`write_basic_package_version_file` command gained a new
  ``ARCH_INDEPENDENT`` option for supporting architecture-independent
  packages.

* The :module:`ExternalProject` module :command:`ExternalProject_Add` command
  gained ``LOG_DIR`` and ``LOG_MERGED_STDOUTERR`` options to control logging.

* The :module:`ExternalProject` module :command:`ExternalProject_Add` command
  gained ``LOG_PATCH`` to optionally log the patch step.

* The :module:`ExternalProject` module :command:`ExternalProject_Add` command
  learned to apply ``SOURCE_SUBDIR`` when ``BUILD_IN_SOURCE`` is also used.
  The ``BUILD_COMMAND`` is run in the given ``SOURCE_SUBDIR`` of the
  ``SOURCE_DIR``.

* The :module:`FetchContent` module gained a new
  :command:`FetchContent_MakeAvailable` command.  It accepts a list of
  dependency names, which it then iterates over, populating and adding
  each one to the main build using the canonical pattern.  This
  significantly reduces the amount of boilerplate needed in a project.

* The :module:`FindBISON` module's ``BISON_TARGET`` command now runs ``bison``
  with :variable:`CMAKE_CURRENT_BINARY_DIR` as the working directory.
  See policy :policy:`CMP0088`.

* The :module:`FindCURL` module gained support for requesting
  protocols as package components.

* The :module:`FindFontconfig` module was added to find `fontconfig`_.

* The :module:`FindGDAL` module now provides imported targets.

* The :module:`FindGIF` module now provides imported targets.

* The :module:`FindGit` module now provides an imported target for the
  Git executable.

* The :module:`FindIce` module learned to find ``slice2confluence``
  and ``slice2matlab``.

* The :module:`FindLibinput` module was added to find `libinput`_.

* The :module:`FindLibLZMA` module now provides imported targets.

* The :module:`FindMatlab` module gained new options ``R2017b`` and
  ``R2018a`` to specify the MEX API version to use; these options
  mirror the new options to the ``mex`` command in MATLAB R2018a.
  The option ``MX_LIBRARY`` is no longer needed.

* The :module:`FindPostgreSQL` module now provides imported targets.

* The :module:`FindPython`, :module:`FindPython2`, and :module:`FindPython3`
  modules gained support for ``NumPy`` component.

* The :module:`FindPython2`, :module:`FindPython3`, and :module:`FindPython`
  modules now support running in script mode by skipping the creation of
  imported targets and helper functions.

* The :module:`FindSQLite3` module was added to find the SQLite v3.x library.

* The :module:`FindX11` had the following variables renamed in order to match
  their library names rather than header names. The old variables are provided
  for compatibility:

  - ``X11_Xxf86misc_INCLUDE_PATH`` instead of ``X11_xf86misc_INCLUDE_PATH``
  - ``X11_Xxf86misc_LIB`` instead of ``X11_xf86misc_LIB``
  - ``X11_Xxf86misc_FOUND`` instead of ``X11_xf86misc_FOUND``
  - ``X11_Xxf86vm_INCLUDE_PATH`` instead of ``X11_xf86vmode_INCLUDE_PATH``
  - ``X11_Xxf86vm_LIB`` instead of ``X11_xf86vmode_LIB``
  - ``X11_Xxf86vm_FOUND`` instead of ``X11_xf86vmode_FOUND``
  - ``X11_xkbfile_INCLUDE_PATH`` instead of ``X11_Xkbfile_INCLUDE_PATH``
  - ``X11_xkbfile_LIB`` instead of ``X11_Xkbfile_LIB``
  - ``X11_xkbfile_FOUND`` instead of ``X11_Xkbfile_FOUND``
  - ``X11_Xtst_INCLUDE_PATH`` instead of ``X11_XTest_INCLUDE_PATH``
  - ``X11_Xtst_LIB`` instead of ``X11_XTest_LIB``
  - ``X11_Xtst_FOUND`` instead of ``X11_XTest_FOUND``
  - ``X11_Xss_INCLUDE_PATH`` instead of ``X11_Xscreensaver_INCLUDE_PATH``
  - ``X11_Xss_LIB`` instead of ``X11_Xscreensaver_LIB``
  - ``X11_Xss_FOUND`` instead of ``X11_Xscreensaver_FOUND``

  The following variables are deprecated completely since they were
  essentially duplicates:

  - ``X11_Xinput_INCLUDE_PATH`` (use ``X11_Xi_INCLUDE_PATH``)
  - ``X11_Xinput_LIB`` (use ``X11_Xi_LIB``)
  - ``X11_Xinput_FOUND`` (use ``X11_Xi_FOUND``)

* The :module:`FindX11` now provides ``X11_Xext_INCLUDE_PATH``.

* The :module:`FindX11` now provides imported targets.

* The :module:`UseSWIG` module learned to pass ``-module <module_name>`` to
  the ``SWIG`` compiler if the file property ``SWIG_MODULE_NAME`` is defined.
  See policy :policy:`CMP0086`.

* The :module:`UseSWIG` module gained an option to specify
  ``SWIG`` source file extensions.

.. _`fontconfig`: https://www.freedesktop.org/wiki/Software/fontconfig/
.. _`libinput`: https://www.freedesktop.org/wiki/Software/libinput/

Generator Expressions
---------------------

* The ``$<Fortran_COMPILER_ID:...>`` and ``$<Fortran_COMPILER_VERSION:...>``
  :manual:`generator expressions <cmake-generator-expressions(7)>` were added.

* The ``$<IN_LIST:...>`` generator expression now correctly handles an
  empty argument. See :policy:`CMP0085` for details.

Autogen
-------

* The :prop_tgt:`AUTOMOC_EXECUTABLE`, :prop_tgt:`AUTORCC_EXECUTABLE`, and
  :prop_tgt:`AUTOUIC_EXECUTABLE` target properties were added.  They all
  take a path to an executable and force automoc/autorcc/autouic to use
  this executable.

  Setting these will also prevent the configure time testing for these
  executables. This is mainly useful when you build these tools yourself.

* The new variables :variable:`CMAKE_GLOBAL_AUTOGEN_TARGET`,
  :variable:`CMAKE_GLOBAL_AUTOGEN_TARGET_NAME`,
  :variable:`CMAKE_GLOBAL_AUTORCC_TARGET` and
  :variable:`CMAKE_GLOBAL_AUTORCC_TARGET_NAME` control the generation
  of global ``autogen`` and ``autorcc`` targets.

* A new :variable:`CMAKE_AUTOGEN_ORIGIN_DEPENDS` variable and
  :prop_tgt:`AUTOGEN_ORIGIN_DEPENDS` target property may be set to enable or
  disable forwarding of the origin target dependencies to the corresponding
  :ref:`<ORIGIN>_autogen` target.

CTest
-----

* :manual:`ctest(1)` gained a ``--show-only=json-v1`` option to show the
  list of tests in a machine-readable JSON format.
  See the :ref:`Show as JSON Object Model` section of the manual.

* The :command:`ctest_submit` command learned a new ``Done`` part that can be used
  to inform CDash that a build is complete and that no more parts will be uploaded.

* CTest learned to accept the dashboard server submission URL from a single
  variable.  See the ``SubmitURL`` setting in :manual:`ctest(1)`,
  the :variable:`CTEST_SUBMIT_URL` variable, and the ``SUBMIT_URL``
  argument of the :command:`ctest_submit` command.

Deprecated and Removed Features
===============================

* An explicit deprecation diagnostic was added for policies ``CMP0064``
  and ``CMP0065`` (``CMP0063`` and below were already deprecated).
  The :manual:`cmake-policies(7)` manual explains that the OLD behaviors
  of all policies are deprecated and that projects should port to the
  NEW behaviors.

* The :generator:`Xcode` generator deprecated support for Xcode
  versions prior to Xcode 5.  Support for those will be dropped in a
  future version of CMake.

* The :module:`FindQt` module is no longer used by the :command:`find_package`
  command as a find module.  This allows the Qt Project upstream to optionally
  provide its own ``QtConfig.cmake`` package configuration file and have
  applications use it via ``find_package(Qt)`` rather than
  ``find_package(Qt CONFIG)``.  See policy :policy:`CMP0084`.

* Support for running CMake on Windows XP and Windows Vista has been dropped.
  The precompiled Windows binaries provided on ``cmake.org`` now require
  Windows 7 or higher.

* CTest no longer supports submissions via ``ftp``, ``scp``, ``cp``, and
  ``xmlrpc``.  CDash is the only maintained testing dashboard for CTest,
  and it only supports submissions over ``http`` and ``https``.

Other Changes
=============

* Object library linking has been fixed to propagate private link libraries
  of object libraries to consuming targets.

* Install rules under :command:`add_subdirectory` now interleave with those in
  the calling directory. See policy :policy:`CMP0082` for details.

* CMake now imposes a maximum recursion limit to prevent a stack overflow on
  scripts that recurse infinitely. The limit can be adjusted at runtime with
  :variable:`CMAKE_MAXIMUM_RECURSION_DEPTH`.

* When using cppcheck via the :variable:`CMAKE_<LANG>_CPPCHECK` variable
  or :prop_tgt:`<LANG>_CPPCHECK` property, the build will now fail if
  ``cppcheck`` returns non-zero as configured by its command-line options.

* Required link options to manage Position Independent Executable are now
  added when :prop_tgt:`POSITION_INDEPENDENT_CODE` is set.  The project is
  responsible for using the :module:`CheckPIESupported` module to check for
  ``PIE`` support to ensure that the :prop_tgt:`POSITION_INDEPENDENT_CODE`
  target property will be honored at link time for executables.  This behavior
  is controlled by policy :policy:`CMP0083`.

* :ref:`Visual Studio Generators` for VS 2010 and above learned
  to support the ``VS_DEBUGGER_*`` properties on targets created
  via :command:`add_custom_target`.

* The :module:`CPack` module no longer defaults to the ``paxr`` value in the
  :variable:`CPACK_DEBIAN_ARCHIVE_TYPE` variable, because ``dpkg`` has
  never supported the PAX tar format. The ``paxr`` value will be mapped
  to ``gnutar`` and a deprecation message emitted.

* CMake no longer issues a warning if a target listed in an
  :command:`install(TARGETS)` command has its :prop_tgt:`EXCLUDE_FROM_ALL`
  property set to true.

Updates
=======

Changes made since CMake 3.14.0 include the following.

3.14.1
------

* The :module:`FindFontconfig` module added by 3.14.0 accidentally
  used uppercase ``FONTCONFIG_*`` variable names that do not match
  our conventions.  3.14.1 revises the module to use ``Fontconfig_*``
  variable names.  This is incompatible with 3.14.0 but since the
  module is new in the 3.14 series usage should not yet be widespread.

3.14.3
------

* The :variable:`CMAKE_VS_PLATFORM_NAME_DEFAULT` variable was added
  to help toolchain files work with the :generator:`Visual Studio 16 2019`
  generator where the default platform now depends on the host platform.

3.14.4
------

* In CMake 3.14.0 through 3.14.3, calling :command:`target_link_libraries`
  to add ``PRIVATE`` dependencies to a static library created in another
  directory (under policy :policy:`CMP0079` ``NEW`` behavior) would
  incorrectly propagate usage requirements of those dependencies to
  dependents that link the static library.  This has been fixed.
  The bug also existed in 3.13.0 through 3.13.4 and is fixed in 3.13.5.

3.14.5
------

* Entries of the ``CPATH`` environment variable are no longer excluded
  from explicit use via :command:`include_directories` and
  :command:`target_include_directories` as they were in CMake 3.14.0
  through 3.14.4.

3.14.6
------

* In CMake 3.14.0 through 3.14.5, the :module:`FindBISON` module
  policy :policy:`CMP0088` ``NEW`` behavior accidentally interpreted
  a relative path to the ``.y`` input as relative to the build tree
  directory instead of the source tree directory.  This has been fixed.

3.14.7
------

* In CMake 3.14.0 through 3.14.6, the :prop_dir:`EXCLUDE_FROM_ALL`
  directory property was regressed from pre-3.14 behavior and caused
  targets within the directory to be excluded even from its own "all".
  This has been fixed.
