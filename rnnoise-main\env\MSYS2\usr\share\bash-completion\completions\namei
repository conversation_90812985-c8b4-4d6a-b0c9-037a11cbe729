_namei_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="--help --version --mountpoints --modes --owners --long --nosymlinks --vertical"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	local IFS=$'\n'
	compopt -o filenames
	COMPREPLY=( $(compgen -f -- $cur) )
	return 0
}
complete -F _namei_module namei
