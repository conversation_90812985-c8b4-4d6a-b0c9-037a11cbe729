.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_issuer_alt_name2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_issuer_alt_name2 \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_issuer_alt_name2(gnutls_x509_crt_t " cert ", unsigned int " seq ", void * " ian ", size_t * " ian_size ", unsigned int * " ian_type ", unsigned int * " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "unsigned int seq" 12
specifies the sequence number of the alt name (0 for the first one, 1 for the second etc.)
.IP "void * ian" 12
is the place where the alternative name will be copied to
.IP "size_t * ian_size" 12
holds the size of ret.
.IP "unsigned int * ian_type" 12
holds the type of the alternative name (one of gnutls_x509_subject_alt_name_t).
.IP "unsigned int * critical" 12
will be non\-zero if the extension is marked as critical (may be null)
.SH "DESCRIPTION"
This function will return the alternative names, contained in the
given certificate. It is the same as
\fBgnutls_x509_crt_get_issuer_alt_name()\fP except for the fact that it
will return the type of the alternative name in  \fIian_type\fP even if
the function fails for some reason (i.e.  the buffer provided is
not enough).
.SH "RETURNS"
the alternative issuer name type on success, one of the
enumerated \fBgnutls_x509_subject_alt_name_t\fP.  It will return
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP if  \fIian_size\fP is not large enough
to hold the value.  In that case  \fIian_size\fP will be updated with
the required size.  If the certificate does not have an
Alternative name with the specified sequence number then
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP is returned.
.SH "SINCE"
2.10.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
