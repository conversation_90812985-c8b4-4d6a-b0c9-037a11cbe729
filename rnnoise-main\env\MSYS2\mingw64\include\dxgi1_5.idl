/*
 * Copyright 2017 <PERSON><PERSON><PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "dxgi1_4.idl";

typedef enum DXGI_OUTDUPL_FLAG
{
    DXGI_OUTDUPL_COMPOSITED_UI_CAPTURE_ONLY = 0x1,
} DXGI_OUTDUPL_FLAG;

typedef enum DXGI_HDR_METADATA_TYPE
{
    DXGI_HDR_METADATA_TYPE_NONE = 0x0,
    DX<PERSON>_HDR_METADATA_TYPE_HDR10 = 0x1,
    DX<PERSON>_HDR_METADATA_TYPE_HDR10PLUS = 0x2,
} DXGI_HDR_METADATA_TYPE;

typedef enum _DXGI_OFFER_RESOURCE_FLAGS
{
    DXGI_OFFER_RESOURCE_FLAG_ALLOW_DECOMMIT = 0x1,
} DXGI_OFFER_RESOURCE_FLAGS;

typedef enum _DXGI_RECLAIM_RESOURCE_RESULTS
{
    DXGI_RECLAIM_RESOURCE_RESULT_OK = 0x0,
    DXGI_RECLAIM_RESOURCE_RESULT_DISCARDED = 0x1,
    DXGI_RECLAIM_RESOURCE_RESULT_NOT_COMMITTED = 0x2,
} DXGI_RECLAIM_RESOURCE_RESULTS;

typedef enum DXGI_FEATURE
{
       DXGI_FEATURE_PRESENT_ALLOW_TEARING = 0x0,
} DXGI_FEATURE;

typedef struct DXGI_HDR_METADATA_HDR10
{
    UINT16 RedPrimary[2];
    UINT16 GreenPrimary[2];
    UINT16 BluePrimary[2];
    UINT16 WhitePoint[2];
    UINT MaxMasteringLuminance;
    UINT MinMasteringLuminance;
    UINT16 MaxContentLightLevel;
    UINT16 MaxFrameAverageLightLevel;
} DXGI_HDR_METADATA_HDR10;

typedef struct DXGI_HDR_METADATA_HDR10PLUS
{
    BYTE Data[72];
} DXGI_HDR_METADATA_HDR10PLUS;

[
    object,
    uuid(80a07424-ab52-42eb-833c-0c42fd282d98),
    local,
    pointer_default(unique)
]
interface IDXGIOutput5 : IDXGIOutput4
{
    HRESULT DuplicateOutput1(
        [in] IUnknown *device,
        [in] UINT flags,
        [in] UINT format_count,
        [in] const DXGI_FORMAT *formats,
        [out] IDXGIOutputDuplication **duplication
    );
}

[
    object,
    uuid(3d585d5a-bd4a-489e-b1f4-3dbcb6452ffb),
    local,
    pointer_default(unique)
]
interface IDXGISwapChain4 : IDXGISwapChain3
{
    HRESULT SetHDRMetaData(
        [in] DXGI_HDR_METADATA_TYPE type,
        [in] UINT size,
        [in] void *metadata
    );
}

[
    object,
    uuid(95b4f95f-d8da-4ca4-9ee6-3b76d5968a10),
    local,
    pointer_default(unique)
]
interface IDXGIDevice4 : IDXGIDevice3
{
    HRESULT OfferResources1(
        [in] UINT resource_count,
        [in] IDXGIResource *const *resources,
        [in] DXGI_OFFER_RESOURCE_PRIORITY priority,
        [in] UINT flags
    );
    HRESULT ReclaimResources1(
        [in] UINT resource_count,
        [in] IDXGIResource *const *resources,
        [out] DXGI_RECLAIM_RESOURCE_RESULTS *results
    );
}

[
    object,
    uuid(7632e1f5-ee65-4dca-87fd-84cd75f8838d),
    local,
    pointer_default(unique)
]
interface IDXGIFactory5 : IDXGIFactory4
{
    HRESULT CheckFeatureSupport(
        DXGI_FEATURE feature,
        [in, out] void *support_data,
        UINT support_data_size
    );
}
