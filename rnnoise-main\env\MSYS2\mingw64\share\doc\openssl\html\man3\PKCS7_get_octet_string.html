<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>PKCS7_get_octet_string</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>PKCS7_get_octet_string - return octet string from a PKCS#7 envelopedData structure</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/pkcs7.h&gt;

ASN1_OCTET_STRING *PKCS7_get_octet_string(PKCS7 *p7);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>PKCS7_get_octet_string() returns a pointer to an ASN1 octet string from a PKCS#7 envelopedData structure or <b>NULL</b> if the structure cannot be parsed.</p>

<h1 id="NOTES">NOTES</h1>

<p>As the <b>0</b> implies, PKCS7_get_octet_string() returns internal pointers which should not be freed by the caller.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>PKCS7_get_octet_string() returns an ASN1_OCTET_STRING pointer.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/PKCS7_type_is_data.html">PKCS7_type_is_data(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2002-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


