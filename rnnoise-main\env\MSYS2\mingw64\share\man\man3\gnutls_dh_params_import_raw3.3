.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dh_params_import_raw3" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dh_params_import_raw3 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_dh_params_import_raw3(gnutls_dh_params_t " dh_params ", const gnutls_datum_t * " prime ", const gnutls_datum_t * " q ", const gnutls_datum_t * " generator ");"
.SH ARGUMENTS
.IP "gnutls_dh_params_t dh_params" 12
The parameters
.IP "const gnutls_datum_t * prime" 12
holds the new prime
.IP "const gnutls_datum_t * q" 12
holds the subgroup if available, otherwise NULL
.IP "const gnutls_datum_t * generator" 12
holds the new generator
.SH "DESCRIPTION"
This function will replace the pair of prime and generator for use
in the <PERSON><PERSON><PERSON>\-<PERSON><PERSON> key exchange.  The new parameters should be
stored in the appropriate gnutls_datum.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
