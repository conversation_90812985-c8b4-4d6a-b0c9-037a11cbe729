.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_sign_set_secure_for_certs" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_sign_set_secure_for_certs \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_sign_set_secure_for_certs(gnutls_sign_algorithm_t " sign ", unsigned int " secure ");"
.SH ARGUMENTS
.IP "gnutls_sign_algorithm_t sign" 12
the sign algorithm
.IP "unsigned int secure" 12
whether to mark the sign algorithm secure for certificates
.SH "DESCRIPTION"
Modify the previous system wide setting that marked  \fIsign\fP as secure
or insecure for the use in certificates.  Calling this function is allowed
only if allowlisting mode is set in the configuration file,
and only if the system\-wide TLS priority string
has not been initialized yet.
The intended usage is to provide applications with a way
to expressly deviate from the distribution or site defaults
inherited from the configuration file.
The modification is composable with further modifications
performed through the priority string mechanism.

This function is not thread\-safe and is intended to be called
in the main thread at the beginning of the process execution.
When  \fIsecure\fP is true,  \fIsign\fP is marked as secure for any use unlike
\fBgnutls_sign_set_secure()\fP.  Otherwise, it is marked as insecure only
for the use in certificates.  Use \fBgnutls_sign_set_secure()\fP to mark
it insecure for any uses.
.SH "RETURNS"
0 on success or negative error code otherwise.
.SH "SINCE"
3.7.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
