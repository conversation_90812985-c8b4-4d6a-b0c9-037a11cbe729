<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>RC4_set_key</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTE">NOTE</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>RC4_set_key, RC4 - RC4 encryption</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/rc4.h&gt;</code></pre>

<p>The following functions have been deprecated since OpenSSL 3.0, and can be hidden entirely by defining <b>OPENSSL_API_COMPAT</b> with a suitable version value, see <a href="../man7/openssl_user_macros.html">openssl_user_macros(7)</a>:</p>

<pre><code>void RC4_set_key(RC4_KEY *key, int len, const unsigned char *data);

void RC4(RC4_KEY *key, unsigned long len, const unsigned char *indata,
         unsigned char *outdata);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>All of the functions described on this page are deprecated. Applications should instead use <a href="../man3/EVP_EncryptInit_ex.html">EVP_EncryptInit_ex(3)</a>, <a href="../man3/EVP_EncryptUpdate.html">EVP_EncryptUpdate(3)</a> and <a href="../man3/EVP_EncryptFinal_ex.html">EVP_EncryptFinal_ex(3)</a> or the equivalently named decrypt functions.</p>

<p>This library implements the Alleged RC4 cipher, which is described for example in <i>Applied Cryptography</i>. It is believed to be compatible with RC4[TM], a proprietary cipher of RSA Security Inc.</p>

<p>RC4 is a stream cipher with variable key length. Typically, 128 bit (16 byte) keys are used for strong encryption, but shorter insecure key sizes have been widely used due to export restrictions.</p>

<p>RC4 consists of a key setup phase and the actual encryption or decryption phase.</p>

<p>RC4_set_key() sets up the <b>RC4_KEY</b> <b>key</b> using the <b>len</b> bytes long key at <b>data</b>.</p>

<p>RC4() encrypts or decrypts the <b>len</b> bytes of data at <b>indata</b> using <b>key</b> and places the result at <b>outdata</b>. Repeated RC4() calls with the same <b>key</b> yield a continuous key stream.</p>

<p>Since RC4 is a stream cipher (the input is XORed with a pseudo-random key stream to produce the output), decryption uses the same function calls as encryption.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>RC4_set_key() and RC4() do not return values.</p>

<h1 id="NOTE">NOTE</h1>

<p>Applications should use the higher level functions <a href="../man3/EVP_EncryptInit.html">EVP_EncryptInit(3)</a> etc. instead of calling these functions directly.</p>

<p>It is difficult to securely use stream ciphers. For example, do not perform multiple encryptions using the same key stream.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_EncryptInit.html">EVP_EncryptInit(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>All of these functions were deprecated in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


