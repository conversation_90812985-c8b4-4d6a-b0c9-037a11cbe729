## Syntax highlighting for the packet-filtering rules of Netfilter.

## Original author:  <PERSON>
## License:  GPL version 3 or newer

syntax nftables "\.(nft|nftables)$"
header "^#!.*(nft|nftables)"
comment "#"

# Objects and operations
color green "\<(chain|hook|policy|priority|ruleset|set|table|type|v?map)\>"
color green "\<(define|include)\>"
color red "\<(add|delete|flush|insert|remove|replace)\>"

# Families
color yellow "\<(arp|bridge|inet|ingress|ip6?|netdev)\>"

# Terminal statements
color red "\<(drop|reject)\>"
color brightblue "\<(accept|continue|(d|s)nat|goto|jump|masquerade|return)\>"

# Comments
color cyan "(^|[[:blank:]])#.*"

# Trailing whitespace
color ,green "[[:space:]]+$"

# Strings
color yellow ""([^"\]|\\.)*"|'([^'\]|\\.)*'"

# Syntactic symbols
color green "[][{}():;|`$<>!=&\]"

# Basic variable names
color brightred "(\$|@)[[:alpha:]_-][[:alnum:]_.-]*"
