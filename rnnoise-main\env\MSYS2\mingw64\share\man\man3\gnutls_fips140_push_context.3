.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_fips140_push_context" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_fips140_push_context \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_fips140_push_context(gnutls_fips140_context_t " context ");"
.SH ARGUMENTS
.IP "gnutls_fips140_context_t context" 12
a \fBgnutls_fips140_context_t\fP
.SH "DESCRIPTION"
Associate the FIPS  \fIcontext\fP to the current thread, diverting the
currently active context. If a cryptographic operation is ongoing
in the current thread, e.g., \fBgnutls_aead_cipher_init()\fP is called
but \fBgnutls_aead_cipher_deinit()\fP is not yet called, it returns an
error \fBGNUTLS_E_INVALID_REQUEST\fP.

The operation state of  \fIcontext\fP will be reset to
\fBGNUTLS_FIPS140_OP_INITIAL\fP.

This function is no\-op if FIPS140 is not compiled in nor enabled
at run\-time.
.SH "RETURNS"
0 upon success, a negative error code otherwise
.SH "SINCE"
3.7.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
