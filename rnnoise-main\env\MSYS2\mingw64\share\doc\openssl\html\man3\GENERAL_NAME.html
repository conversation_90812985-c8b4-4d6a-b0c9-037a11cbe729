<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>GENERAL_NAME</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>GENERAL_NAME, GENERAL_NAME_set1_X509_NAME - GENERAL_NAME method routines</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509v3.h&gt;

typedef struct GENERAL_NAME_st GENERAL_NAME;

int GENERAL_NAME_set1_X509_NAME(GENERAL_NAME **tgt, const X509_NAME *src);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>GENERAL_NAME_set1_X509_NAME() creates a new GENERAL_NAME of type GEN_DIRNAME and populates it based on provided X509_NAME <i>src</i> which can be NULL. <i>tgt</i> must not be NULL. If successful, <i>*tgt</i> will be set to point to the newly created GENERAL_NAME.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>GENERAL_NAME_set1_X509_NAME() return 1 on success, 0 on error.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>GENERAL_NAME_set1_X509_NAME() was added in OpenSSL 3.4.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2007-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


