.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_aki_set_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_aki_set_id \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_aki_set_id(gnutls_x509_aki_t " aki ", const gnutls_datum_t * " id ");"
.SH ARGUMENTS
.IP "gnutls_x509_aki_t aki" 12
The authority key ID
.IP "const gnutls_datum_t * id" 12
the key identifier
.SH "DESCRIPTION"
This function will set the keyIdentifier to be stored in the  \fIaki\fP type.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
