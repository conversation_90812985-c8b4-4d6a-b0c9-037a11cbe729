#!/bin/sh
# Compare the uncompressed contents of compressed files, byte by byte.

# Copyright (C) 2007, 2010-2025 Free Software Foundation, Inc.

# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.

# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.

version="zcmp (gzip) 1.14
Copyright (C) 2025 Free Software Foundation, Inc.
This is free software.  You may redistribute copies of it under the terms of
the GNU General Public License <https://www.gnu.org/licenses/gpl.html>.
There is NO WARRANTY, to the extent permitted by law.

Written by <PERSON>."

usage="Usage: $0 [OPTION]... FILE1 [FILE2]
Compare FILE1 to FILE2 byte by byte, using their uncompressed contents
if they are compressed.  If FILE2 is omitted, compare FILE1 to the
uncompressed contents of FILE1.gz.  Do comparisons like 'cmp' does.

Options are the same as for 'cmp'.

If a FILE is '-' or missing, read standard input.

Report bugs to <<EMAIL>>."

case $1 in
--help)    printf '%s\n' "$usage"   || exit 2; exit;;
--version) printf '%s\n' "$version" || exit 2; exit;;
esac

exec zdiff --__cmp "$@"
