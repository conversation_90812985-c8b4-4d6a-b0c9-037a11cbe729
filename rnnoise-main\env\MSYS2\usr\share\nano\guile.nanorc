## Syntax highlighting for Guile Scheme.

## Original author:  <PERSON>

syntax guile "\.scm$"
header "^#!.*guile"
comment ";"

# Basic scheme functions
color green "\<(do|if|lambda|let(rec)?|map|unless|when)\>"
# Defining things
color brightcyan "\<define(-macro|-module|-public|-syntax)?\>"
# Quoted symbols
color brightyellow "'\<(\w|-)+\>"
# Chars
color brightmagenta "#\\(.|\w+)"
# Booleans
color brightred "(#t|#f)\>"
# Keywords
color blue "#?:(\w|[?-])+"
# Strings
color yellow start="^[[:blank:]]+"" end="[^\]""
color yellow ""([^"\]|\\.)*""
# Comments
color cyan "(^|[[:blank:]]);.*"
