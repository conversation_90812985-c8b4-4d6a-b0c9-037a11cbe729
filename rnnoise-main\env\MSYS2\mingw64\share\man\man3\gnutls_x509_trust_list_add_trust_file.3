.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_add_trust_file" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_add_trust_file \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_trust_list_add_trust_file(gnutls_x509_trust_list_t " list ", const char * " ca_file ", const char * " crl_file ", gnutls_x509_crt_fmt_t " type ", unsigned int " tl_flags ", unsigned int " tl_vflags ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The list
.IP "const char * ca_file" 12
A file containing a list of CAs (optional)
.IP "const char * crl_file" 12
A file containing a list of CRLs (optional)
.IP "gnutls_x509_crt_fmt_t type" 12
The format of the certificates
.IP "unsigned int tl_flags" 12
flags from \fBgnutls_trust_list_flags_t\fP
.IP "unsigned int tl_vflags" 12
gnutls_certificate_verify_flags if flags specifies GNUTLS_TL_VERIFY_CRL
.SH "DESCRIPTION"
This function will add the given certificate authorities
to the trusted list. PKCS \fB11\fP URLs are also accepted, instead
of files, by this function. A PKCS \fB11\fP URL implies a trust
database (a specially marked module in p11\-kit); the URL "pkcs11:"
implies all trust databases in the system. Only a single URL specifying
trust databases can be set; they cannot be stacked with multiple calls.
.SH "RETURNS"
The number of added elements is returned.
.SH "SINCE"
3.1
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
