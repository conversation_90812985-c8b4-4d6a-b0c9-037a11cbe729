add_dependencies
----------------

Add a dependency between top-level targets.

.. code-block:: cmake

  add_dependencies(<target> <target-dependency>...)

Makes a top-level ``<target>`` depend on other top-level targets to
ensure that they build before ``<target>`` does.  A top-level target
is one created by one of the :command:`add_executable`,
:command:`add_library`, or :command:`add_custom_target` commands
(but not targets generated by CMake like ``install``).
At least one ``<target-dependency>`` must be given.

Dependencies added to an :ref:`imported target <Imported Targets>`
or an :ref:`interface library <Interface Libraries>` are followed
transitively in its place since the target itself does not build.

.. versionadded:: 3.3
  Allow adding dependencies to interface libraries.

.. versionadded:: 3.8
  Dependencies will populate the :prop_tgt:`MANUALLY_ADDED_DEPENDENCIES`
  property of ``<target>``.

.. versionchanged:: 3.9
  The :ref:`Ninja Generators` use weaker ordering than
  other generators in order to improve available concurrency.
  They only guarantee that the dependencies' custom commands are
  finished before sources in ``<target>`` start compiling; this
  ensures generated sources are available.

See Also
^^^^^^^^

* The ``DEPENDS`` option of :command:`add_custom_target` and
  :command:`add_custom_command` commands for adding file-level
  dependencies in custom rules.

* The :prop_sf:`OBJECT_DEPENDS` source file property to add
  file-level dependencies to object files.
