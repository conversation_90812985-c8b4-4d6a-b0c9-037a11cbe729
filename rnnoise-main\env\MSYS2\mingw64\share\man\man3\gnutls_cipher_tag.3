.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_cipher_tag" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_cipher_tag \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_cipher_tag(gnutls_cipher_hd_t " handle ", void * " tag ", size_t " tag_size ");"
.SH ARGUMENTS
.IP "gnutls_cipher_hd_t handle" 12
is a \fBgnutls_cipher_hd_t\fP type
.IP "void * tag" 12
will hold the tag
.IP "size_t tag_size" 12
the length of the tag to return
.SH "DESCRIPTION"
This function operates on authenticated encryption with
associated data (AEAD) ciphers and will return the
output tag.
.SH "RETURNS"
Zero or a negative error code on error.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
