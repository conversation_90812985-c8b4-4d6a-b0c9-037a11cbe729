.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs12_simple_parse" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs12_simple_parse \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs12.h>
.sp
.BI "int gnutls_pkcs12_simple_parse(gnutls_pkcs12_t " p12 ", const char * " password ", gnutls_x509_privkey_t * " key ", gnutls_x509_crt_t ** " chain ", unsigned int * " chain_len ", gnutls_x509_crt_t ** " extra_certs ", unsigned int * " extra_certs_len ", gnutls_x509_crl_t * " crl ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs12_t p12" 12
A pkcs12 type
.IP "const char * password" 12
optional password used to decrypt the structure, bags and keys.
.IP "gnutls_x509_privkey_t * key" 12
a structure to store the parsed private key.
.IP "gnutls_x509_crt_t ** chain" 12
the corresponding to key certificate chain (may be \fBNULL\fP)
.IP "unsigned int * chain_len" 12
will be updated with the number of additional (may be \fBNULL\fP)
.IP "gnutls_x509_crt_t ** extra_certs" 12
optional pointer to receive an array of additional
certificates found in the PKCS12 structure (may be \fBNULL\fP).
.IP "unsigned int * extra_certs_len" 12
will be updated with the number of additional
certs (may be \fBNULL\fP).
.IP "gnutls_x509_crl_t * crl" 12
an optional structure to store the parsed CRL (may be \fBNULL\fP).
.IP "unsigned int flags" 12
should be zero or one of GNUTLS_PKCS12_SP_*
.SH "DESCRIPTION"
This function parses a PKCS12 structure in  \fIpkcs12\fP and extracts the
private key, the corresponding certificate chain, any additional
certificates and a CRL. The structures in  \fIkey\fP ,  \fIchain\fP  \fIcrl\fP , and  \fIextra_certs\fP must not be initialized.

The  \fIextra_certs\fP and  \fIextra_certs_len\fP parameters are optional
and both may be set to \fBNULL\fP. If either is non\-\fBNULL\fP, then both must
be set. The value for  \fIextra_certs\fP is allocated
using \fBgnutls_malloc()\fP.

Encrypted PKCS12 bags and PKCS8 private keys are supported, but
only with password based security and the same password for all
operations.

Note that a PKCS12 structure may contain many keys and/or certificates,
and there is no way to identify which key/certificate pair you want.
For this reason this function is useful for PKCS12 files that contain 
only one key/certificate pair and/or one CRL.

If the provided structure has encrypted fields but no password
is provided then this function returns \fBGNUTLS_E_DECRYPTION_FAILED\fP.

Note that normally the chain constructed does not include self signed
certificates, to comply with TLS' requirements. If, however, the flag 
\fBGNUTLS_PKCS12_SP_INCLUDE_SELF_SIGNED\fP is specified then
self signed certificates will be included in the chain.

Prior to using this function the PKCS \fB12\fP structure integrity must
be verified using \fBgnutls_pkcs12_verify_mac()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.1.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
