# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V368
166
167
169
170
174
175
176
177
1154
1155
1421
1423
1550
1552
1758
1759
1769
1770
1789
1791
2038
2039
2554
2555
2928
2929
3059
3065
3066
3067
3199
3200
3407
3408
3449
3450
3841
3844
3859
3860
3861
3864
3866
3872
3892
3893
3894
3895
3896
3897
4030
4038
4039
4045
4046
4048
4053
4057
4254
4256
5008
5018
5741
5742
6464
6465
6622
6656
7009
7019
7028
7037
8448
8450
8451
8455
8456
8458
8468
8469
8470
8472
8478
8484
8485
8486
8487
8488
8489
8490
8494
8495
8506
8508
8522
8523
8524
8526
8527
8528
8586
8588
8597
8602
8604
8608
8609
8611
8612
8614
8615
8622
8623
8654
8656
8658
8659
8660
8661
8692
8960
8968
8972
8992
8994
9001
9003
9084
9085
9115
9140
9180
9186
9255
9280
9291
9372
9450
9472
9655
9656
9665
9666
9720
9728
9839
9840
10088
10132
10176
10240
10496
11008
11056
11077
11079
11085
11124
11126
11158
11159
11264
11493
11499
11856
11858
11904
11930
11931
12020
12032
12246
12272
12284
12292
12293
12306
12308
12320
12321
12342
12344
12350
12352
12688
12690
12694
12704
12736
12772
12800
12831
12842
12872
12880
12881
12896
12928
12938
12977
12992
13312
19904
19968
42128
42183
43048
43052
43062
43064
43065
43066
43639
43642
64832
64848
64975
64976
65021
65024
65508
65509
65512
65513
65517
65519
65532
65534
65847
65856
65913
65930
65932
65935
65936
65949
65952
65953
66000
66045
67703
67705
68296
68297
71487
71488
73685
73693
73697
73714
92988
92992
92997
92998
113820
113821
118608
118724
118784
119030
119040
119079
119081
119141
119146
119149
119171
119173
119180
119210
119214
119275
119296
119362
119365
119366
119552
119639
120832
121344
121399
121403
121453
121461
121462
121476
121477
121479
123215
123216
126124
126125
126254
126255
126976
127020
127024
127124
127136
127151
127153
127168
127169
127184
127185
127222
127245
127406
127462
127491
127504
127548
127552
127561
127568
127570
127584
127590
127744
127995
128000
128728
128732
128749
128752
128765
128768
128887
128891
128986
128992
129004
129008
129009
129024
129036
129040
129096
129104
129114
129120
129160
129168
129198
129200
129202
129280
129620
129632
129646
129648
129661
129664
129673
129680
129726
129727
129734
129742
129756
129760
129769
129776
129785
129792
129939
129940
129995
END
