<?xml version="1.0"?>
<!DOCTYPE syscalls_info SYSTEM "gdb-syscalls.dtd">
<!-- Copyright (C) 2011-2024 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->
<!-- This file was generated using the following file:

     arch/mips/kernel/syscalls/syscall_n64.tbl

     The file mentioned above belongs to the Linux Kernel.  -->
<syscalls_info>
  <syscall name="read" number="5000" groups="descriptor"/>
  <syscall name="write" number="5001" groups="descriptor"/>
  <syscall name="open" number="5002" groups="descriptor,file"/>
  <syscall name="close" number="5003" groups="descriptor"/>
  <syscall name="stat" number="5004" groups="file"/>
  <syscall name="fstat" number="5005" groups="descriptor"/>
  <syscall name="lstat" number="5006" groups="file"/>
  <syscall name="poll" number="5007" groups="descriptor"/>
  <syscall name="lseek" number="5008" groups="descriptor"/>
  <syscall name="mmap" number="5009" groups="descriptor,memory"/>
  <syscall name="mprotect" number="5010" groups="memory"/>
  <syscall name="munmap" number="5011" groups="memory"/>
  <syscall name="brk" number="5012" groups="memory"/>
  <syscall name="rt_sigaction" number="5013" groups="signal"/>
  <syscall name="rt_sigprocmask" number="5014" groups="signal"/>
  <syscall name="ioctl" number="5015" groups="descriptor"/>
  <syscall name="pread64" number="5016" groups="descriptor"/>
  <syscall name="pwrite64" number="5017" groups="descriptor"/>
  <syscall name="readv" number="5018" groups="descriptor"/>
  <syscall name="writev" number="5019" groups="descriptor"/>
  <syscall name="access" number="5020" groups="file"/>
  <syscall name="pipe" number="5021" groups="descriptor"/>
  <syscall name="_newselect" number="5022" groups="descriptor"/>
  <syscall name="sched_yield" number="5023"/>
  <syscall name="mremap" number="5024" groups="memory"/>
  <syscall name="msync" number="5025" groups="memory"/>
  <syscall name="mincore" number="5026" groups="memory"/>
  <syscall name="madvise" number="5027" groups="memory"/>
  <syscall name="shmget" number="5028" groups="ipc"/>
  <syscall name="shmat" number="5029" groups="ipc,memory"/>
  <syscall name="shmctl" number="5030" groups="ipc"/>
  <syscall name="dup" number="5031" groups="descriptor"/>
  <syscall name="dup2" number="5032" groups="descriptor"/>
  <syscall name="pause" number="5033" groups="signal"/>
  <syscall name="nanosleep" number="5034"/>
  <syscall name="getitimer" number="5035"/>
  <syscall name="setitimer" number="5036"/>
  <syscall name="alarm" number="5037"/>
  <syscall name="getpid" number="5038"/>
  <syscall name="sendfile" number="5039" groups="descriptor,network"/>
  <syscall name="socket" number="5040" groups="network"/>
  <syscall name="connect" number="5041" groups="network"/>
  <syscall name="accept" number="5042" groups="network"/>
  <syscall name="sendto" number="5043" groups="network"/>
  <syscall name="recvfrom" number="5044" groups="network"/>
  <syscall name="sendmsg" number="5045" groups="network"/>
  <syscall name="recvmsg" number="5046" groups="network"/>
  <syscall name="shutdown" number="5047" groups="network"/>
  <syscall name="bind" number="5048" groups="network"/>
  <syscall name="listen" number="5049" groups="network"/>
  <syscall name="getsockname" number="5050" groups="network"/>
  <syscall name="getpeername" number="5051" groups="network"/>
  <syscall name="socketpair" number="5052" groups="network"/>
  <syscall name="setsockopt" number="5053" groups="network"/>
  <syscall name="getsockopt" number="5054" groups="network"/>
  <syscall name="clone" number="5055" groups="process"/>
  <syscall name="fork" number="5056" groups="process"/>
  <syscall name="execve" number="5057" groups="file,process"/>
  <syscall name="exit" number="5058" groups="process"/>
  <syscall name="wait4" number="5059" groups="process"/>
  <syscall name="kill" number="5060" groups="signal,process"/>
  <syscall name="uname" number="5061"/>
  <syscall name="semget" number="5062" groups="ipc"/>
  <syscall name="semop" number="5063" groups="ipc"/>
  <syscall name="semctl" number="5064" groups="ipc"/>
  <syscall name="shmdt" number="5065" groups="ipc,memory"/>
  <syscall name="msgget" number="5066" groups="ipc"/>
  <syscall name="msgsnd" number="5067" groups="ipc"/>
  <syscall name="msgrcv" number="5068" groups="ipc"/>
  <syscall name="msgctl" number="5069" groups="ipc"/>
  <syscall name="fcntl" number="5070" groups="descriptor"/>
  <syscall name="flock" number="5071" groups="descriptor"/>
  <syscall name="fsync" number="5072" groups="descriptor"/>
  <syscall name="fdatasync" number="5073" groups="descriptor"/>
  <syscall name="truncate" number="5074" groups="file"/>
  <syscall name="ftruncate" number="5075" groups="descriptor"/>
  <syscall name="getdents" number="5076" groups="descriptor"/>
  <syscall name="getcwd" number="5077" groups="file"/>
  <syscall name="chdir" number="5078" groups="file"/>
  <syscall name="fchdir" number="5079" groups="descriptor"/>
  <syscall name="rename" number="5080" groups="file"/>
  <syscall name="mkdir" number="5081" groups="file"/>
  <syscall name="rmdir" number="5082" groups="file"/>
  <syscall name="creat" number="5083" groups="descriptor,file"/>
  <syscall name="link" number="5084" groups="file"/>
  <syscall name="unlink" number="5085" groups="file"/>
  <syscall name="symlink" number="5086" groups="file"/>
  <syscall name="readlink" number="5087" groups="file"/>
  <syscall name="chmod" number="5088" groups="file"/>
  <syscall name="fchmod" number="5089" groups="descriptor"/>
  <syscall name="chown" number="5090" groups="file"/>
  <syscall name="fchown" number="5091" groups="descriptor"/>
  <syscall name="lchown" number="5092" groups="file"/>
  <syscall name="umask" number="5093"/>
  <syscall name="gettimeofday" number="5094"/>
  <syscall name="getrlimit" number="5095"/>
  <syscall name="getrusage" number="5096"/>
  <syscall name="sysinfo" number="5097"/>
  <syscall name="times" number="5098"/>
  <syscall name="ptrace" number="5099"/>
  <syscall name="getuid" number="5100"/>
  <syscall name="syslog" number="5101"/>
  <syscall name="getgid" number="5102"/>
  <syscall name="setuid" number="5103"/>
  <syscall name="setgid" number="5104"/>
  <syscall name="geteuid" number="5105"/>
  <syscall name="getegid" number="5106"/>
  <syscall name="setpgid" number="5107"/>
  <syscall name="getppid" number="5108"/>
  <syscall name="getpgrp" number="5109"/>
  <syscall name="setsid" number="5110"/>
  <syscall name="setreuid" number="5111"/>
  <syscall name="setregid" number="5112"/>
  <syscall name="getgroups" number="5113"/>
  <syscall name="setgroups" number="5114"/>
  <syscall name="setresuid" number="5115"/>
  <syscall name="getresuid" number="5116"/>
  <syscall name="setresgid" number="5117"/>
  <syscall name="getresgid" number="5118"/>
  <syscall name="getpgid" number="5119"/>
  <syscall name="setfsuid" number="5120"/>
  <syscall name="setfsgid" number="5121"/>
  <syscall name="getsid" number="5122"/>
  <syscall name="capget" number="5123"/>
  <syscall name="capset" number="5124"/>
  <syscall name="rt_sigpending" number="5125" groups="signal"/>
  <syscall name="rt_sigtimedwait" number="5126" groups="signal"/>
  <syscall name="rt_sigqueueinfo" number="5127" groups="signal,process"/>
  <syscall name="rt_sigsuspend" number="5128" groups="signal"/>
  <syscall name="sigaltstack" number="5129" groups="signal"/>
  <syscall name="utime" number="5130" groups="file"/>
  <syscall name="mknod" number="5131" groups="file"/>
  <syscall name="personality" number="5132"/>
  <syscall name="ustat" number="5133"/>
  <syscall name="statfs" number="5134" groups="file"/>
  <syscall name="fstatfs" number="5135" groups="descriptor"/>
  <syscall name="sysfs" number="5136"/>
  <syscall name="getpriority" number="5137"/>
  <syscall name="setpriority" number="5138"/>
  <syscall name="sched_setparam" number="5139"/>
  <syscall name="sched_getparam" number="5140"/>
  <syscall name="sched_setscheduler" number="5141"/>
  <syscall name="sched_getscheduler" number="5142"/>
  <syscall name="sched_get_priority_max" number="5143"/>
  <syscall name="sched_get_priority_min" number="5144"/>
  <syscall name="sched_rr_get_interval" number="5145"/>
  <syscall name="mlock" number="5146" groups="memory"/>
  <syscall name="munlock" number="5147" groups="memory"/>
  <syscall name="mlockall" number="5148" groups="memory"/>
  <syscall name="munlockall" number="5149" groups="memory"/>
  <syscall name="vhangup" number="5150"/>
  <syscall name="pivot_root" number="5151" groups="file"/>
  <syscall name="_sysctl" number="5152"/>
  <syscall name="prctl" number="5153"/>
  <syscall name="adjtimex" number="5154"/>
  <syscall name="setrlimit" number="5155"/>
  <syscall name="chroot" number="5156" groups="file"/>
  <syscall name="sync" number="5157"/>
  <syscall name="acct" number="5158" groups="file"/>
  <syscall name="settimeofday" number="5159"/>
  <syscall name="mount" number="5160" groups="file"/>
  <syscall name="umount2" number="5161" groups="file"/>
  <syscall name="swapon" number="5162" groups="file"/>
  <syscall name="swapoff" number="5163" groups="file"/>
  <syscall name="reboot" number="5164"/>
  <syscall name="sethostname" number="5165"/>
  <syscall name="setdomainname" number="5166"/>
  <syscall name="create_module" number="5167"/>
  <syscall name="init_module" number="5168"/>
  <syscall name="delete_module" number="5169"/>
  <syscall name="get_kernel_syms" number="5170"/>
  <syscall name="query_module" number="5171"/>
  <syscall name="quotactl" number="5172" groups="file"/>
  <syscall name="nfsservctl" number="5173"/>
  <syscall name="getpmsg" number="5174" groups="network"/>
  <syscall name="putpmsg" number="5175" groups="network"/>
  <syscall name="afs_syscall" number="5176"/>
  <syscall name="gettid" number="5178"/>
  <syscall name="readahead" number="5179" groups="descriptor"/>
  <syscall name="setxattr" number="5180" groups="file"/>
  <syscall name="lsetxattr" number="5181" groups="file"/>
  <syscall name="fsetxattr" number="5182" groups="descriptor"/>
  <syscall name="getxattr" number="5183" groups="file"/>
  <syscall name="lgetxattr" number="5184" groups="file"/>
  <syscall name="fgetxattr" number="5185" groups="descriptor"/>
  <syscall name="listxattr" number="5186" groups="file"/>
  <syscall name="llistxattr" number="5187" groups="file"/>
  <syscall name="flistxattr" number="5188" groups="descriptor"/>
  <syscall name="removexattr" number="5189" groups="file"/>
  <syscall name="lremovexattr" number="5190" groups="file"/>
  <syscall name="fremovexattr" number="5191" groups="descriptor"/>
  <syscall name="tkill" number="5192" groups="signal,process"/>
  <syscall name="futex" number="5194"/>
  <syscall name="sched_setaffinity" number="5195"/>
  <syscall name="sched_getaffinity" number="5196"/>
  <syscall name="cacheflush" number="5197" groups="memory"/>
  <syscall name="cachectl" number="5198"/>
  <syscall name="sysmips" number="5199"/>
  <syscall name="io_setup" number="5200" groups="memory"/>
  <syscall name="io_destroy" number="5201" groups="memory"/>
  <syscall name="io_getevents" number="5202"/>
  <syscall name="io_submit" number="5203"/>
  <syscall name="io_cancel" number="5204"/>
  <syscall name="exit_group" number="5205" groups="process"/>
  <syscall name="lookup_dcookie" number="5206"/>
  <syscall name="epoll_create" number="5207" groups="descriptor"/>
  <syscall name="epoll_ctl" number="5208" groups="descriptor"/>
  <syscall name="epoll_wait" number="5209" groups="descriptor"/>
  <syscall name="remap_file_pages" number="5210" groups="memory"/>
  <syscall name="rt_sigreturn" number="5211" groups="signal"/>
  <syscall name="set_tid_address" number="5212"/>
  <syscall name="restart_syscall" number="5213"/>
  <syscall name="semtimedop" number="5214" groups="ipc"/>
  <syscall name="fadvise64" number="5215" groups="descriptor"/>
  <syscall name="timer_create" number="5216"/>
  <syscall name="timer_settime" number="5217"/>
  <syscall name="timer_gettime" number="5218"/>
  <syscall name="timer_getoverrun" number="5219"/>
  <syscall name="timer_delete" number="5220"/>
  <syscall name="clock_settime" number="5221"/>
  <syscall name="clock_gettime" number="5222"/>
  <syscall name="clock_getres" number="5223"/>
  <syscall name="clock_nanosleep" number="5224"/>
  <syscall name="tgkill" number="5225" groups="signal,process"/>
  <syscall name="utimes" number="5226" groups="file"/>
  <syscall name="mbind" number="5227" groups="memory"/>
  <syscall name="get_mempolicy" number="5228" groups="memory"/>
  <syscall name="set_mempolicy" number="5229" groups="memory"/>
  <syscall name="mq_open" number="5230" groups="descriptor"/>
  <syscall name="mq_unlink" number="5231"/>
  <syscall name="mq_timedsend" number="5232" groups="descriptor"/>
  <syscall name="mq_timedreceive" number="5233" groups="descriptor"/>
  <syscall name="mq_notify" number="5234" groups="descriptor"/>
  <syscall name="mq_getsetattr" number="5235" groups="descriptor"/>
  <syscall name="vserver" number="5236"/>
  <syscall name="waitid" number="5237" groups="process"/>
  <syscall name="add_key" number="5239"/>
  <syscall name="request_key" number="5240"/>
  <syscall name="keyctl" number="5241"/>
  <syscall name="set_thread_area" number="5242"/>
  <syscall name="inotify_init" number="5243" groups="descriptor"/>
  <syscall name="inotify_add_watch" number="5244" groups="descriptor,file"/>
  <syscall name="inotify_rm_watch" number="5245" groups="descriptor"/>
  <syscall name="migrate_pages" number="5246" groups="memory"/>
  <syscall name="openat" number="5247" groups="descriptor,file"/>
  <syscall name="mkdirat" number="5248" groups="descriptor,file"/>
  <syscall name="mknodat" number="5249" groups="descriptor,file"/>
  <syscall name="fchownat" number="5250" groups="descriptor,file"/>
  <syscall name="futimesat" number="5251" groups="descriptor,file"/>
  <syscall name="newfstatat" number="5252" groups="descriptor,file"/>
  <syscall name="unlinkat" number="5253" groups="descriptor,file"/>
  <syscall name="renameat" number="5254" groups="descriptor,file"/>
  <syscall name="linkat" number="5255" groups="descriptor,file"/>
  <syscall name="symlinkat" number="5256" groups="descriptor,file"/>
  <syscall name="readlinkat" number="5257" groups="descriptor,file"/>
  <syscall name="fchmodat" number="5258" groups="descriptor,file"/>
  <syscall name="faccessat" number="5259" groups="descriptor,file"/>
  <syscall name="pselect6" number="5260" groups="descriptor"/>
  <syscall name="ppoll" number="5261" groups="descriptor"/>
  <syscall name="unshare" number="5262"/>
  <syscall name="splice" number="5263" groups="descriptor"/>
  <syscall name="sync_file_range" number="5264" groups="descriptor"/>
  <syscall name="tee" number="5265" groups="descriptor"/>
  <syscall name="vmsplice" number="5266" groups="descriptor"/>
  <syscall name="move_pages" number="5267" groups="memory"/>
  <syscall name="set_robust_list" number="5268"/>
  <syscall name="get_robust_list" number="5269"/>
  <syscall name="kexec_load" number="5270"/>
  <syscall name="getcpu" number="5271"/>
  <syscall name="epoll_pwait" number="5272" groups="descriptor"/>
  <syscall name="ioprio_set" number="5273"/>
  <syscall name="ioprio_get" number="5274"/>
  <syscall name="utimensat" number="5275" groups="descriptor,file"/>
  <syscall name="signalfd" number="5276" groups="descriptor,signal"/>
  <syscall name="timerfd" number="5277" groups="descriptor"/>
  <syscall name="eventfd" number="5278" groups="descriptor"/>
  <syscall name="fallocate" number="5279" groups="descriptor"/>
  <syscall name="timerfd_create" number="5280" groups="descriptor"/>
  <syscall name="timerfd_gettime" number="5281" groups="descriptor"/>
  <syscall name="timerfd_settime" number="5282" groups="descriptor"/>
  <syscall name="signalfd4" number="5283" groups="descriptor,signal"/>
  <syscall name="eventfd2" number="5284" groups="descriptor"/>
  <syscall name="epoll_create1" number="5285" groups="descriptor"/>
  <syscall name="dup3" number="5286" groups="descriptor"/>
  <syscall name="pipe2" number="5287" groups="descriptor"/>
  <syscall name="inotify_init1" number="5288" groups="descriptor"/>
  <syscall name="preadv" number="5289" groups="descriptor"/>
  <syscall name="pwritev" number="5290" groups="descriptor"/>
  <syscall name="rt_tgsigqueueinfo" number="5291" groups="process,signal"/>
  <syscall name="perf_event_open" number="5292" groups="descriptor"/>
  <syscall name="accept4" number="5293" groups="network"/>
  <syscall name="recvmmsg" number="5294" groups="network"/>
  <syscall name="fanotify_init" number="5295" groups="descriptor"/>
  <syscall name="fanotify_mark" number="5296" groups="descriptor,file"/>
  <syscall name="prlimit64" number="5297"/>
  <syscall name="name_to_handle_at" number="5298" groups="descriptor,file"/>
  <syscall name="open_by_handle_at" number="5299" groups="descriptor"/>
  <syscall name="clock_adjtime" number="5300"/>
  <syscall name="syncfs" number="5301" groups="descriptor"/>
  <syscall name="sendmmsg" number="5302" groups="network"/>
  <syscall name="setns" number="5303" groups="descriptor"/>
  <syscall name="process_vm_readv" number="5304"/>
  <syscall name="process_vm_writev" number="5305"/>
  <syscall name="kcmp" number="5306"/>
  <syscall name="finit_module" number="5307" groups="descriptor"/>
  <syscall name="getdents64" number="5308" groups="descriptor"/>
  <syscall name="sched_setattr" number="5309"/>
  <syscall name="sched_getattr" number="5310"/>
  <syscall name="renameat2" number="5311" groups="descriptor,file"/>
  <syscall name="seccomp" number="5312"/>
  <syscall name="getrandom" number="5313"/>
  <syscall name="memfd_create" number="5314" groups="descriptor"/>
  <syscall name="bpf" number="5315" groups="descriptor"/>
  <syscall name="execveat" number="5316" groups="descriptor,file,process"/>
  <syscall name="userfaultfd" number="5317" groups="descriptor"/>
  <syscall name="membarrier" number="5318"/>
  <syscall name="mlock2" number="5319" groups="memory"/>
  <syscall name="copy_file_range" number="5320" groups="descriptor"/>
  <syscall name="preadv2" number="5321" groups="descriptor"/>
  <syscall name="pwritev2" number="5322" groups="descriptor"/>
  <syscall name="pkey_mprotect" number="5323" groups="memory"/>
  <syscall name="pkey_alloc" number="5324"/>
  <syscall name="pkey_free" number="5325"/>
  <syscall name="statx" number="5326" groups="descriptor,file"/>
  <syscall name="rseq" number="5327"/>
  <syscall name="io_pgetevents" number="5328"/>
  <syscall name="pidfd_send_signal" number="5424" groups="descriptor,signal,process"/>
  <syscall name="io_uring_setup" number="5425" groups="descriptor"/>
  <syscall name="io_uring_enter" number="5426" groups="descriptor,signal"/>
  <syscall name="io_uring_register" number="5427" groups="descriptor,memory"/>
  <syscall name="open_tree" number="5428" groups="descriptor,file"/>
  <syscall name="move_mount" number="5429" groups="descriptor,file"/>
  <syscall name="fsopen" number="5430" groups="descriptor"/>
  <syscall name="fsconfig" number="5431" groups="descriptor,file"/>
  <syscall name="fsmount" number="5432" groups="descriptor"/>
  <syscall name="fspick" number="5433" groups="descriptor,file"/>
  <syscall name="pidfd_open" number="5434" groups="descriptor"/>
  <syscall name="clone3" number="5435" groups="process"/>
  <syscall name="close_range" number="5436"/>
  <syscall name="openat2" number="5437" groups="descriptor,file"/>
  <syscall name="pidfd_getfd" number="5438" groups="descriptor"/>
  <syscall name="faccessat2" number="5439" groups="descriptor,file"/>
  <syscall name="process_madvise" number="5440" groups="descriptor"/>
  <syscall name="epoll_pwait2" number="5441" groups="descriptor"/>
  <syscall name="mount_setattr" number="5442" groups="descriptor,file"/>
  <syscall name="quotactl_fd" number="5443" groups="descriptor"/>
  <syscall name="landlock_create_ruleset" number="5444" groups="descriptor"/>
  <syscall name="landlock_add_rule" number="5445" groups="descriptor"/>
  <syscall name="landlock_restrict_self" number="5446" groups="descriptor"/>
  <syscall name="process_mrelease" number="5448" groups="descriptor"/>
  <syscall name="futex_waitv" number="5449"/>
  <syscall name="cachestat" number="5451" groups="descriptor"/>
  <syscall name="fchmodat2" number="5452" groups="descriptor,file"/>
  <syscall name="map_shadow_stack" number="5453" groups="memory"/>
  <syscall name="futex_wake" number="5454"/>
  <syscall name="futex_wait" number="5455"/>
  <syscall name="futex_requeue" number="5456"/>
  <syscall name="statmount" number="5457"/>
  <syscall name="listmount" number="5458"/>
  <syscall name="lsm_get_self_attr" number="5459"/>
  <syscall name="lsm_set_self_attr" number="5460"/>
  <syscall name="lsm_list_modules" number="5461"/>
  <syscall name="mseal" number="5462" groups="memory"/>
  <syscall name="setxattrat" number="5463"/>
  <syscall name="getxattrat" number="5464"/>
  <syscall name="listxattrat" number="5465"/>
  <syscall name="removexattrat" number="5466"/>
</syscalls_info>
