## Syntax highlighting for Makefiles.

syntax makefile "(^|/)((GNU)?m|M)akefile[^/]*$|\.(make|mk)$"
magic "makefile script"

tabgives "	"
comment "#"

# Assignments.
color red " (:?:|\+|\?)?= "

# Keywords.
color magenta "^(if|ifn?def|ifn?eq|else|endif|(-|s)?include)\>"
color magenta "^((override +)?(un)?define|endef|(un)?export|private|vpath)\>"

# Variable expansions.
color blue "\$+[{(][[:alnum:]_-]+[})]"

# Targets.
color brightblue "^[^	].*:"

# Comments.
color green "(^|[[:blank:]]+)#.*"

# Trailing whitespace.
color ,green "[[:space:]]+$"
