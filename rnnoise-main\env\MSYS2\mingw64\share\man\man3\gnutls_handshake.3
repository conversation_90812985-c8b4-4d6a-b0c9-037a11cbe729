.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_handshake" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_handshake \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_handshake(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
This function performs the handshake of the TLS/SSL protocol, and
initializes the TLS session parameters.

The non\-fatal errors expected by this function are:
\fBGNUTLS_E_INTERRUPTED\fP, \fBGNUTLS_E_AGAIN\fP,
\fBGNUTLS_E_WARNING_ALERT_RECEIVED\fP. When this function is called
for re\-handshake under TLS 1.2 or earlier, the non\-fatal error code
\fBGNUTLS_E_GOT_APPLICATION_DATA\fP may also be returned.

The former two interrupt the handshake procedure due to the transport
layer being interrupted, and the latter because of a "warning" alert that
was sent by the peer (it is always a good idea to check any
received alerts). On these non\-fatal errors call this function again,
until it returns 0; cf.  \fBgnutls_record_get_direction()\fP and
\fBgnutls_error_is_fatal()\fP. In DTLS sessions the non\-fatal error
\fBGNUTLS_E_LARGE_PACKET\fP is also possible, and indicates that
the MTU should be adjusted.

When this function is called by a server after a rehandshake request
under TLS 1.2 or earlier the \fBGNUTLS_E_GOT_APPLICATION_DATA\fP error code indicates
that some data were pending prior to peer initiating the handshake.
Under TLS 1.3 this function when called after a successful handshake, is a no\-op
and always succeeds in server side; in client side this function is
equivalent to \fBgnutls_session_key_update()\fP with \fBGNUTLS_KU_PEER\fP flag.

This function handles both full and abbreviated TLS handshakes (resumption).
For abbreviated handshakes, in client side, the \fBgnutls_session_set_data()\fP
should be called prior to this function to set parameters from a previous session.
In server side, resumption is handled by either setting a DB back\-end, or setting
up keys for session tickets.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on a successful handshake, otherwise a negative error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
