.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_priority_set" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_priority_set \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_priority_set(gnutls_session_t " session ", gnutls_priority_t " priority ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_priority_t priority" 12
is a \fBgnutls_priority_t\fP type.
.SH "DESCRIPTION"
Sets the priorities to use on the ciphers, key exchange methods,
and macs. Note that this function is expected to be called once
per session; when called multiple times (e.g., before a re\-handshake,
the caller should make sure that any new settings are not incompatible
with the original session).
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, or an error code on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
