.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_verify_function" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_verify_function \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_certificate_set_verify_function(gnutls_certificate_credentials_t " cred ", gnutls_certificate_verify_function * " func ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t cred" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "gnutls_certificate_verify_function * func" 12
is the callback function
.SH "DESCRIPTION"
This function sets a callback to be called when peer's certificate
has been received in order to verify it on receipt rather than
doing after the handshake is completed.

The callback's function prototype is:
int (*callback)(gnutls_session_t);

If the callback function is provided then gnut<PERSON> will call it, in the
handshake, just after the certificate message has been received.
To verify or obtain the certificate the \fBgnutls_certificate_verify_peers2()\fP,
\fBgnutls_certificate_type_get()\fP, \fBgnutls_certificate_get_peers()\fP functions
can be used.

The callback function should return 0 for the handshake to continue
or non\-zero to terminate.
.SH "SINCE"
2.10.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
