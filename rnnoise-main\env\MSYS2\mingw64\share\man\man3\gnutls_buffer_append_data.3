.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_buffer_append_data" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_buffer_append_data \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_buffer_append_data(gnutls_buffer_t " dest ", const void * " data ", size_t " data_size ");"
.SH ARGUMENTS
.IP "gnutls_buffer_t dest" 12
the buffer to append to
.IP "const void * data" 12
the data
.IP "size_t data_size" 12
the size of  \fIdata\fP 
.SH "DESCRIPTION"
Appends the provided  \fIdata\fP to the destination buffer.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
