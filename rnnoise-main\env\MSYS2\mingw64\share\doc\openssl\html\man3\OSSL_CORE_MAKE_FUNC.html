<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_CORE_MAKE_FUNC</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_CORE_MAKE_FUNC, SSL_OP_BIT, EXT_UTF8STRING - OpenSSL reserved symbols</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/core_dispatch.h&gt;

#define OSSL_CORE_MAKE_FUNC(type,name,args)
#define SSL_OP_BIT(n)
#define EXT_UTF8STRING(nid)</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>There are certain macros that may appear in OpenSSL header files that are reserved for internal use. They should not be used by applications or assumed to exist.</p>

<p>All the macros listed in the synopsis above are reserved.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>Not applicable.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The macros described here were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


