#!/usr/bin/bash
#
# paclog-pkglist - Parse a log file into a list of currently installed packages
#
# Copyright (C) 2011 <PERSON> <<EMAIL>>
#
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License
# as published by the Free Software Foundation; either version 2
# of the License, or (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.

declare -r myname='paclog-pkglist'
declare -r myver='1.10.6'

export TEXTDOMAIN='pacman'
export TEXTDOMAINDIR='/usr/share/locale'
declare logfile=${1:-/var/log/pacman.log}

usage() {
	cat <<EOF
${myname} v${myver}

Parse a log file into a list of currently installed packages.

Usage: ${myname} [path to pacman log]

Options:
  -h, --help     show this help message and exit
  -V, --version  display version information and exit

Example: ${myname} /var/log/pacman.log

Defaults to: /var/log/pacman.log
EOF
}

version() {
	printf "%s %s\n" "$myname" "$myver"
	echo 'Copyright (C) 2011 Dave Reisner <<EMAIL>>'
}

if [[ $1 ]]; then
	if [[ $1 = -@(h|-help) ]]; then
		usage
		exit 0
	elif [[ $1 = -@(V|-version) ]]; then
		version
		exit 0
	elif [[ ! -e $logfile ]]; then
		printf $"target not found: %s\n" "$1"
		exit 1
	fi
fi

<"$logfile" awk '
{
	if ($3 ~ /^\[.*\]$/) {
		# new style with caller name
		action  = $4
		pkgname = $5
		pkgver  = $6
		upgver  = $8
		nfields = NF
	} else {
		action  = $3
		pkgname = $4
		pkgver  = $5
		upgver  = $7
		nfields = (NF + 1)    # compensate for missing caller field
	}
}

nfields == 6 && action == "installed" {
	gsub(/[()]/, "", pkgver)
	pkg[pkgname] = pkgver
	next
}

nfields == 8 && (action == "upgraded" || action == "downgraded") {
	sub(/\)/, "", upgver)
	pkg[pkgname] = upgver
	next
}

nfields == 6 && action == "removed" {
	pkg[pkgname] = -1
}

END {
	for (i in pkg) {
		if (pkg[i] != -1) {
			printf "%s %s\n",i,pkg[i]
		}
	}
}' | sort
