include(Compiler/GNU)
__compiler_gnu(OBJCXX)
__compiler_gnu_cxx_standards(OBJCXX)

if((NOT DEFINED CMAKE_DEPENDS_USE_COMPILER OR CMAKE_DEPENDS_USE_COMPILER)
    AND CMAKE_GENERATOR MATCHES "Makefiles|WMake"
    AND CMAKE_DEPFILE_FLAGS_OBJCXX)
  # dependencies are computed by the compiler itself
  set(CMAKE_OBJCXX_DEPFILE_FORMAT gcc)
  set(CMAKE_OBJCXX_DEPENDS_USE_COMPILER TRUE)
endif()


if(NOT CMAKE_OBJCXX_COMPILER_VERSION VERSION_LESS 4.2)
  set(CMAKE_OBJCXX_COMPILE_OPTIONS_VISIBILITY_INLINES_HIDDEN "-fvisibility-inlines-hidden")
endif()

if(NOT CMAKE_OBJCXX_LINK_FLAGS)
  set(CMAKE_OBCXX_LINK_FLAGS "-lstdc++")
endif()
