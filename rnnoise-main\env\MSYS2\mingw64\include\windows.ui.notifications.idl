/*
 * Copyright (C) 2024 Biswa<PERSON><PERSON>yo <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.applicationmodel.idl";
import "windows.data.xml.dom.idl";
import "windows.system.idl";

namespace Windows.UI.Notifications {
    typedef enum AdaptiveNotificationContentKind AdaptiveNotificationContentKind;
    typedef enum BadgeTemplateType BadgeTemplateType;
    typedef enum NotificationKinds NotificationKinds;
    typedef enum NotificationMirroring NotificationMirroring;
    typedef enum NotificationSetting NotificationSetting;
    typedef enum NotificationUpdateResult NotificationUpdateResult;
    typedef enum PeriodicUpdateRecurrence PeriodicUpdateRecurrence;
    typedef enum TileFlyoutTemplateType TileFlyoutTemplateType;
    typedef enum TileTemplateType TileTemplateType;
    typedef enum ToastDismissalReason ToastDismissalReason;
    typedef enum ToastHistoryChangedType ToastHistoryChangedType;
    typedef enum ToastNotificationMode ToastNotificationMode;
    typedef enum ToastNotificationPriority ToastNotificationPriority;
    typedef enum ToastTemplateType ToastTemplateType;
    typedef enum UserNotificationChangedKind UserNotificationChangedKind;

    interface IAdaptiveNotificationContent;
    interface IAdaptiveNotificationText;
    interface IBadgeNotification;
    interface IBadgeNotificationFactory;
    interface IBadgeUpdateManagerForUser;
    interface IBadgeUpdateManagerStatics;
    interface IBadgeUpdateManagerStatics2;
    interface IBadgeUpdater;
    interface IKnownAdaptiveNotificationHintsStatics;
    interface IKnownAdaptiveNotificationTextStylesStatics;
    interface IKnownNotificationBindingsStatics;
    interface INotification;
    interface INotificationBinding;
    interface INotificationData;
    interface INotificationDataFactory;
    interface INotificationVisual;
    interface IScheduledTileNotification;
    interface IScheduledTileNotificationFactory;
    interface IScheduledToastNotification;
    interface IScheduledToastNotification2;
    interface IScheduledToastNotification3;
    interface IScheduledToastNotification4;
    interface IScheduledToastNotificationFactory;
    interface IScheduledToastNotificationShowingEventArgs;
    interface IShownTileNotification;
    interface ITileFlyoutNotification;
    interface ITileFlyoutNotificationFactory;
    interface ITileFlyoutUpdateManagerStatics;
    interface ITileFlyoutUpdater;
    interface ITileNotification;
    interface ITileNotificationFactory;
    interface ITileUpdateManagerForUser;
    interface ITileUpdateManagerStatics;
    interface ITileUpdateManagerStatics2;
    interface ITileUpdater;
    interface ITileUpdater2;
    interface IToastActivatedEventArgs;
    interface IToastActivatedEventArgs2;
    interface IToastCollection;
    interface IToastCollectionFactory;
    interface IToastCollectionManager;
    interface IToastDismissedEventArgs;
    interface IToastFailedEventArgs;
    interface IToastNotification;
    interface IToastNotification2;
    interface IToastNotification3;
    interface IToastNotification4;
    interface IToastNotification6;
    interface IToastNotificationActionTriggerDetail;
    interface IToastNotificationFactory;
    interface IToastNotificationHistory;
    interface IToastNotificationHistory2;
    interface IToastNotificationHistoryChangedTriggerDetail;
    interface IToastNotificationHistoryChangedTriggerDetail2;
    interface IToastNotificationManagerForUser;
    interface IToastNotificationManagerForUser2;
    interface IToastNotificationManagerForUser3;
    interface IToastNotificationManagerStatics;
    interface IToastNotificationManagerStatics2;
    interface IToastNotificationManagerStatics4;
    interface IToastNotificationManagerStatics5;
    interface IToastNotifier;
    interface IToastNotifier2;
    interface IToastNotifier3;
    interface IUserNotification;
    interface IUserNotificationChangedEventArgs;

    runtimeclass AdaptiveNotificationText;
    runtimeclass BadgeNotification;
    runtimeclass BadgeUpdateManager;
    runtimeclass BadgeUpdateManagerForUser;
    runtimeclass BadgeUpdater;
    runtimeclass KnownAdaptiveNotificationHints;
    runtimeclass KnownAdaptiveNotificationTextStyles;
    runtimeclass KnownNotificationBindings;
    runtimeclass Notification;
    runtimeclass NotificationBinding;
    runtimeclass NotificationData;
    runtimeclass NotificationVisual;
    runtimeclass ScheduledTileNotification;
    runtimeclass ScheduledToastNotification;
    runtimeclass ScheduledToastNotificationShowingEventArgs;
    runtimeclass ShownTileNotification;
    runtimeclass TileFlyoutNotification;
    runtimeclass TileFlyoutUpdateManager;
    runtimeclass TileFlyoutUpdater;
    runtimeclass TileNotification;
    runtimeclass TileUpdateManager;
    runtimeclass TileUpdateManagerForUser;
    runtimeclass TileUpdater;
    runtimeclass ToastActivatedEventArgs;
    runtimeclass ToastCollection;
    runtimeclass ToastCollectionManager;
    runtimeclass ToastDismissedEventArgs;
    runtimeclass ToastFailedEventArgs;
    runtimeclass ToastNotification;
    runtimeclass ToastNotificationActionTriggerDetail;
    runtimeclass ToastNotificationHistory;
    runtimeclass ToastNotificationHistoryChangedTriggerDetail;
    runtimeclass ToastNotificationManager;
    runtimeclass ToastNotificationManagerForUser;
    runtimeclass ToastNotifier;
    runtimeclass UserNotification;
    runtimeclass UserNotificationChangedEventArgs;

    declare {
        interface Windows.Foundation.Collections.IIterable<Windows.UI.Notifications.AdaptiveNotificationText *>;
        interface Windows.Foundation.Collections.IIterable<Windows.UI.Notifications.NotificationBinding *>;
        interface Windows.Foundation.Collections.IIterable<Windows.UI.Notifications.ScheduledTileNotification *>;
        interface Windows.Foundation.Collections.IIterable<Windows.UI.Notifications.ScheduledToastNotification *>;
        interface Windows.Foundation.Collections.IIterable<Windows.UI.Notifications.ShownTileNotification *>;
        interface Windows.Foundation.Collections.IIterable<Windows.UI.Notifications.ToastCollection *>;
        interface Windows.Foundation.Collections.IIterable<Windows.UI.Notifications.ToastNotification *>;
        interface Windows.Foundation.Collections.IIterable<Windows.UI.Notifications.UserNotification *>;
        interface Windows.Foundation.Collections.IIterator<Windows.UI.Notifications.AdaptiveNotificationText *>;
        interface Windows.Foundation.Collections.IIterator<Windows.UI.Notifications.NotificationBinding *>;
        interface Windows.Foundation.Collections.IIterator<Windows.UI.Notifications.ScheduledTileNotification *>;
        interface Windows.Foundation.Collections.IIterator<Windows.UI.Notifications.ScheduledToastNotification *>;
        interface Windows.Foundation.Collections.IIterator<Windows.UI.Notifications.ShownTileNotification *>;
        interface Windows.Foundation.Collections.IIterator<Windows.UI.Notifications.ToastCollection *>;
        interface Windows.Foundation.Collections.IIterator<Windows.UI.Notifications.ToastNotification *>;
        interface Windows.Foundation.Collections.IIterator<Windows.UI.Notifications.UserNotification *>;
        interface Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.AdaptiveNotificationText *>;
        interface Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.NotificationBinding *>;
        interface Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.ScheduledTileNotification *>;
        interface Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.ScheduledToastNotification *>;
        interface Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.ShownTileNotification *>;
        interface Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.ToastCollection *>;
        interface Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.ToastNotification *>;
        interface Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.UserNotification *>;
        interface Windows.Foundation.Collections.IVector<Windows.UI.Notifications.NotificationBinding *>;
        interface Windows.Foundation.IAsyncOperation<Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.ToastCollection *> *>;
        interface Windows.Foundation.IAsyncOperation<Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.UserNotification *> *>;
        interface Windows.Foundation.IAsyncOperation<Windows.UI.Notifications.ToastCollection *>;
        interface Windows.Foundation.IAsyncOperation<Windows.UI.Notifications.ToastNotificationHistory *>;
        interface Windows.Foundation.IAsyncOperation<Windows.UI.Notifications.ToastNotifier *>;
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.ToastCollection *> *>;
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.UserNotification *> *>;
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.UI.Notifications.ToastCollection *>;
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.UI.Notifications.ToastNotificationHistory *>;
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.UI.Notifications.ToastNotifier *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Notifications.ToastNotification *, IInspectable *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Notifications.ToastNotification *, Windows.UI.Notifications.ToastDismissedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Notifications.ToastNotification *, Windows.UI.Notifications.ToastFailedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Notifications.ToastNotificationManagerForUser *, IInspectable *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Notifications.ToastNotifier *, Windows.UI.Notifications.ScheduledToastNotificationShowingEventArgs *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0)
    ]
    enum AdaptiveNotificationContentKind
    {
        Text = 0,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum BadgeTemplateType
    {
        BadgeGlyph  = 0,
        BadgeNumber = 1,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        flags
    ]
    enum NotificationKinds
    {
        Unknown = 0x0,
        Toast   = 0x1,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0)
    ]
    enum NotificationMirroring
    {
        Allowed  = 0,
        Disabled = 1,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum NotificationSetting
    {
        Enabled                = 0,
        DisabledForApplication = 1,
        DisabledForUser        = 2,
        DisabledByGroupPolicy  = 3,
        DisabledByManifest     = 4,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0)
    ]
    enum NotificationUpdateResult
    {
        Succeeded            = 0,
        Failed               = 1,
        NotificationNotFound = 2,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum PeriodicUpdateRecurrence
    {
        HalfHour    = 0,
        Hour        = 1,
        SixHours    = 2,
        TwelveHours = 3,
        Daily       = 4,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum TileFlyoutTemplateType
    {
        TileFlyoutTemplate01 = 0,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum TileTemplateType
    {
        TileSquareImage                           = 0,
        TileSquareBlock                           = 1,
        TileSquareText01                          = 2,
        TileSquareText02                          = 3,
        TileSquareText03                          = 4,
        TileSquareText04                          = 5,
        TileSquarePeekImageAndText01              = 6,
        TileSquarePeekImageAndText02              = 7,
        TileSquarePeekImageAndText03              = 8,
        TileSquarePeekImageAndText04              = 9,
        TileWideImage                             = 10,
        TileWideImageCollection                   = 11,
        TileWideImageAndText01                    = 12,
        TileWideImageAndText02                    = 13,
        TileWideBlockAndText01                    = 14,
        TileWideBlockAndText02                    = 15,
        TileWidePeekImageCollection01             = 16,
        TileWidePeekImageCollection02             = 17,
        TileWidePeekImageCollection03             = 18,
        TileWidePeekImageCollection04             = 19,
        TileWidePeekImageCollection05             = 20,
        TileWidePeekImageCollection06             = 21,
        TileWidePeekImageAndText01                = 22,
        TileWidePeekImageAndText02                = 23,
        TileWidePeekImage01                       = 24,
        TileWidePeekImage02                       = 25,
        TileWidePeekImage03                       = 26,
        TileWidePeekImage04                       = 27,
        TileWidePeekImage05                       = 28,
        TileWidePeekImage06                       = 29,
        TileWideSmallImageAndText01               = 30,
        TileWideSmallImageAndText02               = 31,
        TileWideSmallImageAndText03               = 32,
        TileWideSmallImageAndText04               = 33,
        TileWideSmallImageAndText05               = 34,
        TileWideText01                            = 35,
        TileWideText02                            = 36,
        TileWideText03                            = 37,
        TileWideText04                            = 38,
        TileWideText05                            = 39,
        TileWideText06                            = 40,
        TileWideText07                            = 41,
        TileWideText08                            = 42,
        TileWideText09                            = 43,
        TileWideText10                            = 44,
        TileWideText11                            = 45,
        TileSquare150x150Image                    = 0,
        TileSquare150x150Block                    = 1,
        TileSquare150x150Text01                   = 2,
        TileSquare150x150Text02                   = 3,
        TileSquare150x150Text03                   = 4,
        TileSquare150x150Text04                   = 5,
        TileSquare150x150PeekImageAndText01       = 6,
        TileSquare150x150PeekImageAndText02       = 7,
        TileSquare150x150PeekImageAndText03       = 8,
        TileSquare150x150PeekImageAndText04       = 9,
        TileWide310x150Image                      = 10,
        TileWide310x150ImageCollection            = 11,
        TileWide310x150ImageAndText01             = 12,
        TileWide310x150ImageAndText02             = 13,
        TileWide310x150BlockAndText01             = 14,
        TileWide310x150BlockAndText02             = 15,
        TileWide310x150PeekImageCollection01      = 16,
        TileWide310x150PeekImageCollection02      = 17,
        TileWide310x150PeekImageCollection03      = 18,
        TileWide310x150PeekImageCollection04      = 19,
        TileWide310x150PeekImageCollection05      = 20,
        TileWide310x150PeekImageCollection06      = 21,
        TileWide310x150PeekImageAndText01         = 22,
        TileWide310x150PeekImageAndText02         = 23,
        TileWide310x150PeekImage01                = 24,
        TileWide310x150PeekImage02                = 25,
        TileWide310x150PeekImage03                = 26,
        TileWide310x150PeekImage04                = 27,
        TileWide310x150PeekImage05                = 28,
        TileWide310x150PeekImage06                = 29,
        TileWide310x150SmallImageAndText01        = 30,
        TileWide310x150SmallImageAndText02        = 31,
        TileWide310x150SmallImageAndText03        = 32,
        TileWide310x150SmallImageAndText04        = 33,
        TileWide310x150SmallImageAndText05        = 34,
        TileWide310x150Text01                     = 35,
        TileWide310x150Text02                     = 36,
        TileWide310x150Text03                     = 37,
        TileWide310x150Text04                     = 38,
        TileWide310x150Text05                     = 39,
        TileWide310x150Text06                     = 40,
        TileWide310x150Text07                     = 41,
        TileWide310x150Text08                     = 42,
        TileWide310x150Text09                     = 43,
        TileWide310x150Text10                     = 44,
        TileWide310x150Text11                     = 45,
        TileSquare310x310BlockAndText01           = 46,
        TileSquare310x310BlockAndText02           = 47,
        TileSquare310x310Image                    = 48,
        TileSquare310x310ImageAndText01           = 49,
        TileSquare310x310ImageAndText02           = 50,
        TileSquare310x310ImageAndTextOverlay01    = 51,
        TileSquare310x310ImageAndTextOverlay02    = 52,
        TileSquare310x310ImageAndTextOverlay03    = 53,
        TileSquare310x310ImageCollectionAndText01 = 54,
        TileSquare310x310ImageCollectionAndText02 = 55,
        TileSquare310x310ImageCollection          = 56,
        TileSquare310x310SmallImagesAndTextList01 = 57,
        TileSquare310x310SmallImagesAndTextList02 = 58,
        TileSquare310x310SmallImagesAndTextList03 = 59,
        TileSquare310x310SmallImagesAndTextList04 = 60,
        TileSquare310x310Text01                   = 61,
        TileSquare310x310Text02                   = 62,
        TileSquare310x310Text03                   = 63,
        TileSquare310x310Text04                   = 64,
        TileSquare310x310Text05                   = 65,
        TileSquare310x310Text06                   = 66,
        TileSquare310x310Text07                   = 67,
        TileSquare310x310Text08                   = 68,
        TileSquare310x310TextList01               = 69,
        TileSquare310x310TextList02               = 70,
        TileSquare310x310TextList03               = 71,
        TileSquare310x310SmallImageAndText01      = 72,
        TileSquare310x310SmallImagesAndTextList05 = 73,
        TileSquare310x310Text09                   = 74,
        TileSquare71x71IconWithBadge              = 75,
        TileSquare150x150IconWithBadge            = 76,
        TileWide310x150IconWithBadgeAndText       = 77,
        TileSquare71x71Image                      = 78,
        TileTall150x310Image                      = 79,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum ToastDismissalReason
    {
        UserCanceled      = 0,
        ApplicationHidden = 1,
        TimedOut          = 2,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum ToastHistoryChangedType
    {
        Cleared = 0,
        Removed = 1,
        Expired = 2,
        Added   = 3,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 15.0)
    ]
    enum ToastNotificationMode
    {
        Unrestricted = 0,
        PriorityOnly = 1,
        AlarmsOnly   = 2,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0)
    ]
    enum ToastNotificationPriority
    {
        Default = 0,
        High    = 1,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum ToastTemplateType
    {
        ToastImageAndText01 = 0,
        ToastImageAndText02 = 1,
        ToastImageAndText03 = 2,
        ToastImageAndText04 = 3,
        ToastText01         = 4,
        ToastText02         = 5,
        ToastText03         = 6,
        ToastText04         = 7,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0)
    ]
    enum UserNotificationChangedKind
    {
        Added   = 0,
        Removed = 1,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        uuid(eb0dbe66-7448-448d-9db8-d78acd2abba9)
    ]
    interface IAdaptiveNotificationContent : IInspectable
    {
        [propget] HRESULT Kind([out, retval] Windows.UI.Notifications.AdaptiveNotificationContentKind *value);
        [propget] HRESULT Hints([out, retval] Windows.Foundation.Collections.IMap<HSTRING, HSTRING> **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.AdaptiveNotificationText),
        uuid(46d4a3be-609a-4326-a40b-bfde872034a3)
    ]
    interface IAdaptiveNotificationText : IInspectable
    {
        [propget] HRESULT Text([out, retval] HSTRING *value);
        [propput] HRESULT Text([in] HSTRING value);
        [propget] HRESULT Language([out, retval] HSTRING *value);
        [propput] HRESULT Language([in] HSTRING value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.BadgeNotification),
        uuid(075cb4ca-d08a-4e2f-9233-7e289c1f7722)
    ]
    interface IBadgeNotification : IInspectable
    {
        [propget] HRESULT Content([out, retval] Windows.Data.Xml.Dom.XmlDocument **value);
        [propput] HRESULT ExpirationTime([in] Windows.Foundation.IReference<Windows.Foundation.DateTime> *value);
        [propget] HRESULT ExpirationTime([out, retval] Windows.Foundation.IReference<Windows.Foundation.DateTime> **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.BadgeNotification),
        uuid(edf255ce-0618-4d59-948a-5a61040c52f9)
    ]
    interface IBadgeNotificationFactory : IInspectable
    {
        HRESULT CreateBadgeNotification(
            [in] Windows.Data.Xml.Dom.XmlDocument *content,
            [out, retval] Windows.UI.Notifications.BadgeNotification **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.BadgeUpdateManagerForUser),
        uuid(996b21bc-0386-44e5-ba8d-0c1077a62e92)
    ]
    interface IBadgeUpdateManagerForUser : IInspectable
    {
        [overload("CreateBadgeUpdaterForApplication")] HRESULT CreateBadgeUpdaterForApplication([out, retval] Windows.UI.Notifications.BadgeUpdater **result);
        [overload("CreateBadgeUpdaterForApplication")] HRESULT CreateBadgeUpdaterForApplicationWithId([in] HSTRING application_id, [out, retval] Windows.UI.Notifications.BadgeUpdater **result);
        HRESULT CreateBadgeUpdaterForSecondaryTile([in] HSTRING tile_id, [out, retval] Windows.UI.Notifications.BadgeUpdater **result);
        [propget] HRESULT User([out, retval] Windows.System.User **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.BadgeUpdateManager),
        uuid(33400faa-6dd5-4105-aebc-9b50fca492da)
    ]
    interface IBadgeUpdateManagerStatics : IInspectable
    {
        [overload("CreateBadgeUpdaterForApplication")] HRESULT CreateBadgeUpdaterForApplication([out, retval] Windows.UI.Notifications.BadgeUpdater **result);
        [overload("CreateBadgeUpdaterForApplication")] HRESULT CreateBadgeUpdaterForApplicationWithId([in] HSTRING application_id, [out, retval] Windows.UI.Notifications.BadgeUpdater **result);
        HRESULT CreateBadgeUpdaterForSecondaryTile([in] HSTRING tile_id, [out, retval] Windows.UI.Notifications.BadgeUpdater **result);
        HRESULT GetTemplateContent([in] Windows.UI.Notifications.BadgeTemplateType type, [out, retval] Windows.Data.Xml.Dom.XmlDocument **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.BadgeUpdateManager),
        uuid(979a35ce-f940-48bf-94e8-ca244d400b41)
    ]
    interface IBadgeUpdateManagerStatics2 : IInspectable
    {
        HRESULT GetForUser([in] Windows.System.User *user, [out, retval] Windows.UI.Notifications.BadgeUpdateManagerForUser **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.BadgeUpdater),
        uuid(b5fa1fd4-7562-4f6c-bfa3-1b6ed2e57f2f)
    ]
    interface IBadgeUpdater : IInspectable
    {
        HRESULT Update([in] Windows.UI.Notifications.BadgeNotification *notification);
        HRESULT Clear();
        [overload("StartPeriodicUpdate")] HRESULT StartPeriodicUpdate(
            [in] Windows.Foundation.Uri *badge_content,
            [in] Windows.UI.Notifications.PeriodicUpdateRecurrence requested_interval);
        [overload("StartPeriodicUpdate")] HRESULT StartPeriodicUpdateAtTime(
            [in] Windows.Foundation.Uri *badge_content,
            [in] Windows.Foundation.DateTime start_time,
            [in] Windows.UI.Notifications.PeriodicUpdateRecurrence requested_interval);
        HRESULT StopPeriodicUpdate();
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.KnownAdaptiveNotificationHints),
        uuid(06206598-d496-497d-8692-4f7d7c2770df)
    ]
    interface IKnownAdaptiveNotificationHintsStatics : IInspectable
    {
        [propget] HRESULT Style([out, retval] HSTRING *value);
        [propget] HRESULT Wrap([out, retval] HSTRING *value);
        [propget] HRESULT MaxLines([out, retval] HSTRING *value);
        [propget] HRESULT MinLines([out, retval] HSTRING *value);
        [propget] HRESULT TextStacking([out, retval] HSTRING *value);
        [propget] HRESULT Align([out, retval] HSTRING *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.KnownAdaptiveNotificationTextStyles),
        uuid(202192d7-8996-45aa-8ba1-d461d72c2a1b)
    ]
    interface IKnownAdaptiveNotificationTextStylesStatics : IInspectable
    {
        [propget] HRESULT Caption([out, retval] HSTRING *value);
        [propget] HRESULT Body([out, retval] HSTRING *value);
        [propget] HRESULT Base([out, retval] HSTRING *value);
        [propget] HRESULT Subtitle([out, retval] HSTRING *value);
        [propget] HRESULT Title([out, retval] HSTRING *value);
        [propget] HRESULT Subheader([out, retval] HSTRING *value);
        [propget] HRESULT Header([out, retval] HSTRING *value);
        [propget] HRESULT TitleNumeral([out, retval] HSTRING *value);
        [propget] HRESULT SubheaderNumeral([out, retval] HSTRING *value);
        [propget] HRESULT HeaderNumeral([out, retval] HSTRING *value);
        [propget] HRESULT CaptionSubtle([out, retval] HSTRING *value);
        [propget] HRESULT BodySubtle([out, retval] HSTRING *value);
        [propget] HRESULT BaseSubtle([out, retval] HSTRING *value);
        [propget] HRESULT SubtitleSubtle([out, retval] HSTRING *value);
        [propget] HRESULT TitleSubtle([out, retval] HSTRING *value);
        [propget] HRESULT SubheaderSubtle([out, retval] HSTRING *value);
        [propget] HRESULT SubheaderNumeralSubtle([out, retval] HSTRING *value);
        [propget] HRESULT HeaderSubtle([out, retval] HSTRING *value);
        [propget] HRESULT HeaderNumeralSubtle([out, retval] HSTRING *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.KnownNotificationBindings),
        uuid(79427bae-a8b7-4d58-89ea-76a7b7bccded)
    ]
    interface IKnownNotificationBindingsStatics : IInspectable
    {
        [propget] HRESULT ToastGeneric([out, retval] HSTRING *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.Notification),
        uuid(108037fe-eb76-4f82-97bc-da07530a2e20)
    ]
    interface INotification : IInspectable
    {
        [propget] HRESULT ExpirationTime([out, retval] Windows.Foundation.IReference<Windows.Foundation.DateTime> **value);
        [propput] HRESULT ExpirationTime([in] Windows.Foundation.IReference<Windows.Foundation.DateTime> *value);
        [propget] HRESULT Visual([out, retval] Windows.UI.Notifications.NotificationVisual **value);
        [propput] HRESULT Visual([in] Windows.UI.Notifications.NotificationVisual *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.NotificationBinding),
        uuid(F29E4B85-0370-4AD3-B4EA-DA9E35E7EABF)
    ]
    interface INotificationBinding : IInspectable
    {
        [propget] HRESULT Template([out, retval] HSTRING *value);
        [propput] HRESULT Template([in] HSTRING value);
        [propget] HRESULT Language([out, retval] HSTRING *value);
        [propput] HRESULT Language([in] HSTRING value);
        [propget] HRESULT Hints([out, retval] Windows.Foundation.Collections.IMap<HSTRING, HSTRING> **value);
        HRESULT GetTextElements([out, retval] Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.AdaptiveNotificationText *> **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.Notifications.NotificationData),
        uuid(9ffd2312-9d6a-4aaf-b6ac-ff17f0c1f280)
    ]
    interface INotificationData : IInspectable
    {
        [propget] HRESULT Values([out, retval] Windows.Foundation.Collections.IMap<HSTRING, HSTRING> **value);
        [propget] HRESULT SequenceNumber([out, retval] UINT32 *value);
        [propput] HRESULT SequenceNumber([in] UINT32 value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.Notifications.NotificationData),
        uuid(23c1e33a-1c10-46fb-8040-dec384621cf8)
    ]
    interface INotificationDataFactory : IInspectable
    {
        [overload("CreateNotificationData")] HRESULT CreateNotificationDataWithValuesAndSequenceNumber(
            [in] Windows.Foundation.Collections.IIterable<Windows.Foundation.Collections.IKeyValuePair<HSTRING, HSTRING> *> *initial_values,
            [in] UINT32 sequence_number,
            [out, retval] Windows.UI.Notifications.NotificationData **value);
        [overload("CreateNotificationData")] HRESULT CreateNotificationDataWithValues(
            [in] Windows.Foundation.Collections.IIterable<Windows.Foundation.Collections.IKeyValuePair<HSTRING, HSTRING> *> *initial_values,
            [out, retval] Windows.UI.Notifications.NotificationData **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.NotificationVisual),
        uuid(68835b8e-aa56-4e11-86d3-5f9a6957bc5b)
    ]
    interface INotificationVisual : IInspectable
    {
        [propget] HRESULT Language([out, retval] HSTRING *value);
        [propput] HRESULT Language([in] HSTRING value);
        [propget] HRESULT Bindings([out, retval] Windows.Foundation.Collections.IVector<Windows.UI.Notifications.NotificationBinding *> **value);
        HRESULT GetBinding([in] HSTRING template_name, [out, retval] Windows.UI.Notifications.NotificationBinding **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ScheduledTileNotification),
        uuid(0abca6d5-99dc-4c78-a11c-c9e7f86d7ef7)
    ]
    interface IScheduledTileNotification : IInspectable
    {
        [propget] HRESULT Content([out, retval] Windows.Data.Xml.Dom.XmlDocument **value);
        [propget] HRESULT DeliveryTime([out, retval] Windows.Foundation.DateTime *value);
        [propput] HRESULT ExpirationTime([in] Windows.Foundation.IReference<Windows.Foundation.DateTime> *value);
        [propget] HRESULT ExpirationTime([out, retval] Windows.Foundation.IReference<Windows.Foundation.DateTime> **value);
        [propput] HRESULT Tag([in] HSTRING value);
        [propget] HRESULT Tag([out, retval] HSTRING *value);
        [propput] HRESULT Id([in] HSTRING value);
        [propget] HRESULT Id([out, retval] HSTRING *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ScheduledTileNotification),
        uuid(3383138a-98c0-4c3b-bbd6-4a633c7cfc29)
    ]
    interface IScheduledTileNotificationFactory : IInspectable
    {
        HRESULT CreateScheduledTileNotification(
            [in] Windows.Data.Xml.Dom.XmlDocument *content,
            [in] Windows.Foundation.DateTime delivery_time,
            [out, retval] Windows.UI.Notifications.ScheduledTileNotification **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ScheduledToastNotification),
        uuid(79F577F8-0DE7-48CD-9740-9B370490C838)
    ]
    interface IScheduledToastNotification : IInspectable
    {
        [propget] HRESULT Content([out, retval] Windows.Data.Xml.Dom.XmlDocument **value);
        [propget] HRESULT DeliveryTime([out, retval] Windows.Foundation.DateTime *value);
        [propget] HRESULT SnoozeInterval([out, retval] Windows.Foundation.IReference<Windows.Foundation.TimeSpan> **value);
        [propget] HRESULT MaximumSnoozeCount([out, retval] UINT32 *value);
        [propput] HRESULT Id([in] HSTRING value);
        [propget] HRESULT Id([out, retval] HSTRING *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ScheduledToastNotification),
        uuid(a66ea09c-31b4-43b0-b5dd-7a40e85363b1)
    ]
    interface IScheduledToastNotification2 : IInspectable
    {
        [propput] HRESULT Tag([in] HSTRING value);
        [propget] HRESULT Tag([out, retval] HSTRING *value);
        [propput] HRESULT Group([in] HSTRING value);
        [propget] HRESULT Group([out, retval] HSTRING *value);
        [propput] HRESULT SuppressPopup([in] boolean value);
        [propget] HRESULT SuppressPopup([out, retval] boolean *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.ScheduledToastNotification),
        uuid(98429e8b-bd32-4a3b-9d15-22aea49462a1)
    ]
    interface IScheduledToastNotification3 : IInspectable
    {
        [propget] HRESULT NotificationMirroring([out, retval] Windows.UI.Notifications.NotificationMirroring *value);
        [propput] HRESULT NotificationMirroring([in] Windows.UI.Notifications.NotificationMirroring value);
        [propget] HRESULT RemoteId([out, retval] HSTRING *value);
        [propput] HRESULT RemoteId([in] HSTRING value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 6.0),
        exclusiveto(Windows.UI.Notifications.ScheduledToastNotification),
        uuid(1d4761fd-bdef-4e4a-96be-0101369b58d2)
    ]
    interface IScheduledToastNotification4 : IInspectable
    {
        [propget] HRESULT ExpirationTime([out, retval] Windows.Foundation.IReference<Windows.Foundation.DateTime> **value);
        [propput] HRESULT ExpirationTime([in] Windows.Foundation.IReference<Windows.Foundation.DateTime> *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ScheduledToastNotification),
        uuid(e7bed191-0bb9-4189-8394-31761b476fd7)
    ]
    interface IScheduledToastNotificationFactory : IInspectable
    {
        HRESULT CreateScheduledToastNotification(
            [in] Windows.Data.Xml.Dom.XmlDocument *content,
            [in] Windows.Foundation.DateTime delivery_time,
            [out, retval] Windows.UI.Notifications.ScheduledToastNotification **value);
        HRESULT CreateScheduledToastNotificationRecurring(
            [in] Windows.Data.Xml.Dom.XmlDocument *content,
            [in] Windows.Foundation.DateTime delivery_time,
            [in] Windows.Foundation.TimeSpan snooze_interval,
            [in] UINT32 maximum_snooze_count,
            [out, retval] Windows.UI.Notifications.ScheduledToastNotification **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 7.0),
        exclusiveto(Windows.UI.Notifications.ScheduledToastNotificationShowingEventArgs),
        uuid(6173f6b4-412a-5e2c-a6ed-a0209aef9a09)
    ]
    interface IScheduledToastNotificationShowingEventArgs : IInspectable
    {
        [propget] HRESULT Cancel([out, retval] boolean *value);
        [propput] HRESULT Cancel([in] boolean value);
        [propget] HRESULT ScheduledToastNotification([out, retval] Windows.UI.Notifications.ScheduledToastNotification **value);
        HRESULT GetDeferral([out, retval] Windows.Foundation.Deferral **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.ShownTileNotification),
        uuid(342d8988-5af2-481a-a6a3-f2fdc78de88e)
    ]
    interface IShownTileNotification : IInspectable
    {
        [propget] HRESULT Arguments([out, retval] HSTRING *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.TileFlyoutNotification),
        uuid(9a53b261-c70c-42be-b2f3-f42aa97d34e5)
    ]
    interface ITileFlyoutNotification : IInspectable
    {
        [propget] HRESULT Content([out, retval] Windows.Data.Xml.Dom.XmlDocument **value);
        [propput] HRESULT ExpirationTime([in] Windows.Foundation.IReference<Windows.Foundation.DateTime> *value);
        [propget] HRESULT ExpirationTime([out, retval] Windows.Foundation.IReference<Windows.Foundation.DateTime> **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.TileFlyoutNotification),
        uuid(ef556ff5-5226-4f2b-b278-88a35dfe569f)
    ]
    interface ITileFlyoutNotificationFactory : IInspectable
    {
        HRESULT CreateTileFlyoutNotification(
            [in] Windows.Data.Xml.Dom.XmlDocument *content,
            [out, retval] Windows.UI.Notifications.TileFlyoutNotification **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.TileFlyoutUpdateManager),
        uuid(04363b0b-1ac0-4b99-88e7-ada83e953d48)
    ]
    interface ITileFlyoutUpdateManagerStatics : IInspectable
    {
        [overload("CreateTileFlyoutUpdaterForApplication")] HRESULT CreateTileFlyoutUpdaterForApplication(
            [out, retval] Windows.UI.Notifications.TileFlyoutUpdater **result);
        [overload("CreateTileFlyoutUpdaterForApplication")] HRESULT CreateTileFlyoutUpdaterForApplicationWithId(
            [in] HSTRING application_id,
            [out, retval] Windows.UI.Notifications.TileFlyoutUpdater **result);
        HRESULT CreateTileFlyoutUpdaterForSecondaryTile(
            [in] HSTRING tile_id,
            [out, retval] Windows.UI.Notifications.TileFlyoutUpdater **result);
        HRESULT GetTemplateContent(
            [in] Windows.UI.Notifications.TileFlyoutTemplateType type,
            [out, retval] Windows.Data.Xml.Dom.XmlDocument **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.TileFlyoutUpdater),
        uuid(8d40c76a-c465-4052-a740-5c2654c1a089)
    ]
    interface ITileFlyoutUpdater : IInspectable
    {
        HRESULT Update([in] Windows.UI.Notifications.TileFlyoutNotification *notification);
        HRESULT Clear();
        [overload("StartPeriodicUpdate")] HRESULT StartPeriodicUpdate(
            [in] Windows.Foundation.Uri *tile_flyout_content,
            [in] Windows.UI.Notifications.PeriodicUpdateRecurrence requested_interval);
        [overload("StartPeriodicUpdate")] HRESULT StartPeriodicUpdateAtTime(
            [in] Windows.Foundation.Uri *tile_flyout_content,
            [in] Windows.Foundation.DateTime start_time,
            [in] Windows.UI.Notifications.PeriodicUpdateRecurrence requested_interval);
        HRESULT StopPeriodicUpdate();
        [propget] HRESULT Setting([out, retval] Windows.UI.Notifications.NotificationSetting *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.TileNotification),
        uuid(ebaec8fa-50ec-4c18-b4d0-3af02e5540ab)
    ]
    interface ITileNotification : IInspectable
    {
        [propget] HRESULT Content([out, retval] Windows.Data.Xml.Dom.XmlDocument **value);
        [propput] HRESULT ExpirationTime([in] Windows.Foundation.IReference<Windows.Foundation.DateTime> *value);
        [propget] HRESULT ExpirationTime([out, retval] Windows.Foundation.IReference<Windows.Foundation.DateTime> **value);
        [propput] HRESULT Tag([in] HSTRING value);
        [propget] HRESULT Tag([out, retval] HSTRING *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.TileNotification),
        uuid(c6abdd6e-4928-46c8-bdbf-81a047dea0d4)
    ]
    interface ITileNotificationFactory : IInspectable
    {
        HRESULT CreateTileNotification([in] Windows.Data.Xml.Dom.XmlDocument *content, [out, retval] Windows.UI.Notifications.TileNotification **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.TileUpdateManagerForUser),
        uuid(55141348-2ee2-4e2d-9cc1-216a20decc9f)
    ]
    interface ITileUpdateManagerForUser : IInspectable
    {
        [overload("CreateTileUpdaterForApplicationForUser")] HRESULT CreateTileUpdaterForApplication([out, retval] Windows.UI.Notifications.TileUpdater **result);
        [overload("CreateTileUpdaterForApplication")] HRESULT CreateTileUpdaterForApplicationWithId([in] HSTRING application_id, [out, retval] Windows.UI.Notifications.TileUpdater **result);
        HRESULT CreateTileUpdaterForSecondaryTile([in] HSTRING tile_id, [out, retval] Windows.UI.Notifications.TileUpdater **result);
        [propget] HRESULT User([out, retval] Windows.System.User **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.TileUpdateManager),
        uuid(da159e5d-3ea9-4986-8d84-b09d5e12276d)
    ]
    interface ITileUpdateManagerStatics : IInspectable
    {
        [overload("CreateTileUpdaterForApplication")] HRESULT CreateTileUpdaterForApplication([out, retval] Windows.UI.Notifications.TileUpdater **result);
        [overload("CreateTileUpdaterForApplication")] HRESULT CreateTileUpdaterForApplicationWithId([in] HSTRING application_id, [out, retval] Windows.UI.Notifications.TileUpdater **result);
        HRESULT CreateTileUpdaterForSecondaryTile([in] HSTRING tileId, [out, retval] Windows.UI.Notifications.TileUpdater **result);
        HRESULT GetTemplateContent([in] Windows.UI.Notifications.TileTemplateType type, [out, retval] Windows.Data.Xml.Dom.XmlDocument **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.TileUpdateManager),
        uuid(731c1ddc-8e14-4b7c-a34b-9d22de76c84d)
    ]
    interface ITileUpdateManagerStatics2 : IInspectable
    {
        HRESULT GetForUser([in] Windows.System.User *user, [out, retval] Windows.UI.Notifications.TileUpdateManagerForUser **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.TileUpdater),
        uuid(0942a48b-1d91-44ec-9243-c1e821c29a20)
    ]
    interface ITileUpdater : IInspectable
    {
        HRESULT Update([in] Windows.UI.Notifications.TileNotification *notification);
        HRESULT Clear();
        HRESULT EnableNotificationQueue([in] boolean enable);
        [propget] HRESULT Setting([out, retval] Windows.UI.Notifications.NotificationSetting *value);
        HRESULT AddToSchedule([in] Windows.UI.Notifications.ScheduledTileNotification *scheduled_tile);
        HRESULT RemoveFromSchedule([in] Windows.UI.Notifications.ScheduledTileNotification *scheduled_tile);
        HRESULT GetScheduledTileNotifications([out, retval] Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.ScheduledTileNotification *> **result);
        [overload("StartPeriodicUpdate")] HRESULT StartPeriodicUpdate(
            [in] Windows.Foundation.Uri *tile_content,
            [in] Windows.UI.Notifications.PeriodicUpdateRecurrence requested_interval);
        [overload("StartPeriodicUpdate")] HRESULT StartPeriodicUpdateAtTime(
            [in] Windows.Foundation.Uri *tile_content,
            [in] Windows.Foundation.DateTime start_time,
            [in] Windows.UI.Notifications.PeriodicUpdateRecurrence requested_interval);
        HRESULT StopPeriodicUpdate();
        [overload("StartPeriodicUpdateBatch")] HRESULT StartPeriodicUpdateBatch(
            [in] Windows.Foundation.Collections.IIterable<Windows.Foundation.Uri *> *tile_contents,
            [in] Windows.UI.Notifications.PeriodicUpdateRecurrence requested_interval);
        [overload("StartPeriodicUpdateBatch")] HRESULT StartPeriodicUpdateBatchAtTime(
            [in] Windows.Foundation.Collections.IIterable<Windows.Foundation.Uri *> *tile_contents,
            [in] Windows.Foundation.DateTime start_time,
            [in] Windows.UI.Notifications.PeriodicUpdateRecurrence requested_interval);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.TileUpdater),
        uuid(a2266e12-15ee-43ed-83f5-65b352bb1a84)
    ]
    interface ITileUpdater2 : IInspectable
    {
        HRESULT EnableNotificationQueueForSquare150x150([in] boolean enable);
        HRESULT EnableNotificationQueueForWide310x150([in] boolean enable);
        HRESULT EnableNotificationQueueForSquare310x310([in] boolean enable);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ToastActivatedEventArgs),
        uuid(e3bf92f3-c197-436f-8265-0625824f8dac)
    ]
    interface IToastActivatedEventArgs : IInspectable
    {
        [propget] HRESULT Arguments([out, retval] HSTRING *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 8.0),
        exclusiveto(Windows.UI.Notifications.ToastActivatedEventArgs),
        uuid(ab7da512-cc61-568e-81be-304ac31038fa)
    ]
    interface IToastActivatedEventArgs2 : IInspectable
    {
        [propget] HRESULT UserInput([out, retval] Windows.Foundation.Collections.ValueSet **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.Notifications.ToastCollection),
        uuid(0a8bc3b0-e0be-4858-bc2a-89dfe0b32863)
    ]
    interface IToastCollection : IInspectable
    {
        [propget] HRESULT Id([out, retval] HSTRING *value);
        [propget] HRESULT DisplayName([out, retval] HSTRING *value);
        [propput] HRESULT DisplayName([in] HSTRING value);
        [propget] HRESULT LaunchArgs([out, retval] HSTRING *value);
        [propput] HRESULT LaunchArgs([in] HSTRING value);
        [propget] HRESULT Icon([out, retval] Windows.Foundation.Uri **value);
        [propput] HRESULT Icon([in] Windows.Foundation.Uri *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.Notifications.ToastCollection),
        uuid(164dd3d7-73c4-44f7-b4ff-fb6d4bf1f4c6)
    ]
    interface IToastCollectionFactory : IInspectable
    {
        HRESULT CreateInstance(
            [in] HSTRING collection_id,
            [in] HSTRING display_name,
            [in] HSTRING launch_args,
            [in] Windows.Foundation.Uri *icon_uri,
            [out, retval] Windows.UI.Notifications.ToastCollection **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.Notifications.ToastCollectionManager),
        uuid(2a1821fe-179d-49bc-b79d-a527920d3665)
    ]
    interface IToastCollectionManager : IInspectable
    {
        HRESULT SaveToastCollectionAsync([in] Windows.UI.Notifications.ToastCollection *collection, [out, retval] Windows.Foundation.IAsyncAction **operation);
        HRESULT FindAllToastCollectionsAsync([out, retval] Windows.Foundation.IAsyncOperation<Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.ToastCollection *> *> **operation);
        HRESULT GetToastCollectionAsync([in] HSTRING collection_id, [out, retval] Windows.Foundation.IAsyncOperation<Windows.UI.Notifications.ToastCollection *> **operation);
        HRESULT RemoveToastCollectionAsync([in] HSTRING collection_id, [out, retval] Windows.Foundation.IAsyncAction **operation);
        HRESULT RemoveAllToastCollectionsAsync([out, retval] Windows.Foundation.IAsyncAction **operation);
        [propget] HRESULT User([out, retval] Windows.System.User **value);
        [propget] HRESULT AppId([out, retval] HSTRING *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ToastDismissedEventArgs),
        uuid(3f89d935-d9cb-4538-a0f0-ffe7659938f8)
    ]
    interface IToastDismissedEventArgs : IInspectable
    {
        [propget] HRESULT Reason([out, retval] Windows.UI.Notifications.ToastDismissalReason *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ToastFailedEventArgs),
        uuid(35176862-cfd4-44f8-ad64-f500fd896c3b)
    ]
    interface IToastFailedEventArgs : IInspectable
    {
        [propget] HRESULT ErrorCode([out, retval] HRESULT *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ToastNotification),
        uuid(997e2675-059e-4e60-8b06-1760917c8b80)
    ]
    interface IToastNotification : IInspectable
    {
        [propget] HRESULT Content([out, retval] Windows.Data.Xml.Dom.XmlDocument **value);
        [propput] HRESULT ExpirationTime([in] Windows.Foundation.IReference<Windows.Foundation.DateTime> *value);
        [propget] HRESULT ExpirationTime([out, retval] Windows.Foundation.IReference<Windows.Foundation.DateTime> **value);
        [eventadd] HRESULT Dismissed(
            [in] Windows.Foundation.TypedEventHandler<Windows.UI.Notifications.ToastNotification *, Windows.UI.Notifications.ToastDismissedEventArgs *> *handler,
            [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT Dismissed([in] EventRegistrationToken token);
        [eventadd] HRESULT Activated(
            [in] Windows.Foundation.TypedEventHandler<Windows.UI.Notifications.ToastNotification *, IInspectable *> *handler,
            [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT Activated([in] EventRegistrationToken token);
        [eventadd] HRESULT Failed(
            [in] Windows.Foundation.TypedEventHandler<Windows.UI.Notifications.ToastNotification *, Windows.UI.Notifications.ToastFailedEventArgs *> *handler,
            [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT Failed([in] EventRegistrationToken token);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ToastNotification),
        uuid(9dfb9fd1-143a-490e-90bf-b9fba7132de7)
    ]
    interface IToastNotification2 : IInspectable
    {
        [propput] HRESULT Tag([in] HSTRING value);
        [propget] HRESULT Tag([out, retval] HSTRING *value);
        [propput] HRESULT Group([in] HSTRING value);
        [propget] HRESULT Group([out, retval] HSTRING *value);
        [propput] HRESULT SuppressPopup([in] boolean value);
        [propget] HRESULT SuppressPopup([out, retval] boolean *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.ToastNotification),
        uuid(31e8aed8-8141-4f99-bc0a-c4ed21297d77)
    ]
    interface IToastNotification3 : IInspectable
    {
        [propget] HRESULT NotificationMirroring([out, retval] Windows.UI.Notifications.NotificationMirroring *value);
        [propput] HRESULT NotificationMirroring([in] Windows.UI.Notifications.NotificationMirroring value);
        [propget] HRESULT RemoteId([out, retval] HSTRING *value);
        [propput] HRESULT RemoteId([in] HSTRING value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.Notifications.ToastNotification),
        uuid(15154935-28ea-4727-88e9-c58680e2d118)
    ]
    interface IToastNotification4 : IInspectable
    {
        [propget] HRESULT Data([out, retval] Windows.UI.Notifications.NotificationData **value);
        [propput] HRESULT Data([in] Windows.UI.Notifications.NotificationData *value);
        [propget] HRESULT Priority([out, retval] Windows.UI.Notifications.ToastNotificationPriority *value);
        [propput] HRESULT Priority([in] Windows.UI.Notifications.ToastNotificationPriority value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 8.0),
        exclusiveto(Windows.UI.Notifications.ToastNotification),
        uuid(43ebfe53-89ae-5c1e-a279-3aecfe9b6f54)
    ]
    interface IToastNotification6 : IInspectable
    {
        [propget] HRESULT ExpiresOnReboot([out, retval] boolean *value);
        [propput] HRESULT ExpiresOnReboot([in] boolean value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ToastNotificationActionTriggerDetail),
        uuid(9445135a-38f3-42f6-96aa-7955b0f03da2)
    ]
    interface IToastNotificationActionTriggerDetail : IInspectable
    {
        [propget] HRESULT Argument([out, retval] HSTRING *value);
        [propget] HRESULT UserInput([out, retval] Windows.Foundation.Collections.ValueSet **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ToastNotification),
        uuid(04124b20-82c6-4229-b109-fd9ed4662b53)
    ]
    interface IToastNotificationFactory : IInspectable
    {
        HRESULT CreateToastNotification([in] Windows.Data.Xml.Dom.XmlDocument *content, [out, retval] Windows.UI.Notifications.ToastNotification **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ToastNotificationHistory),
        uuid(5caddc63-01d3-4c97-986f-0533483fee14)
    ]
    interface IToastNotificationHistory : IInspectable
    {
        [overload("RemoveGroup")] HRESULT RemoveGroup([in] HSTRING group);
        [overload("RemoveGroup")] HRESULT RemoveGroupWithId([in] HSTRING group, [in] HSTRING application_id);
        [overload("Remove")] HRESULT RemoveGroupedTagWithId([in] HSTRING tag, [in] HSTRING group, [in] HSTRING application_id);
        [overload("Remove")] HRESULT RemoveGroupedTag([in] HSTRING tag, [in] HSTRING group);
        [overload("Remove")] HRESULT Remove([in] HSTRING tag);
        [overload("Clear")] HRESULT Clear();
        [overload("Clear")] HRESULT ClearWithId([in] HSTRING application_id);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ToastNotificationHistory),
        uuid(3bc3d253-2f31-4092-9129-8ad5abf067da)
    ]
    interface IToastNotificationHistory2 : IInspectable
    {
        [overload("GetHistory")] HRESULT GetHistory(
            [out, retval] Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.ToastNotification *> **result);
        [overload("GetHistory")] HRESULT GetHistoryWithId(
            [in] HSTRING application_id,
            [out, retval] Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.ToastNotification *> **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ToastNotificationHistoryChangedTriggerDetail),
        uuid(db037ffa-0068-412c-9c83-267c37f65670)
    ]
    interface IToastNotificationHistoryChangedTriggerDetail : IInspectable
    {
        [propget] HRESULT ChangeType([out, retval] Windows.UI.Notifications.ToastHistoryChangedType *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.Notifications.ToastNotificationHistoryChangedTriggerDetail),
        uuid(0b36e982-c871-49fb-babb-25bdbc4cc45b)
    ]
    interface IToastNotificationHistoryChangedTriggerDetail2 : IInspectable
    {
        [propget] HRESULT CollectionId([out, retval] HSTRING *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.ToastNotificationManagerForUser),
        uuid(79ab57f6-43fe-487b-8a7f-99567200ae94)
    ]
    interface IToastNotificationManagerForUser : IInspectable
    {
        [overload("CreateToastNotifier")] HRESULT CreateToastNotifier([out, retval] Windows.UI.Notifications.ToastNotifier **result);
        [overload("CreateToastNotifier")] HRESULT CreateToastNotifierWithId([in] HSTRING application_id, [out, retval] Windows.UI.Notifications.ToastNotifier **result);
        [propget] HRESULT History([out, retval] Windows.UI.Notifications.ToastNotificationHistory **value);
        [propget] HRESULT User([out, retval] Windows.System.User **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.Notifications.ToastNotificationManagerForUser),
        uuid(679c64b7-81ab-42c2-8819-c958767753f4)
    ]
    interface IToastNotificationManagerForUser2 : IInspectable
    {
        HRESULT GetToastNotifierForToastCollectionIdAsync([in] HSTRING collection_id, [out, retval] Windows.Foundation.IAsyncOperation<Windows.UI.Notifications.ToastNotifier *> **operation);
        HRESULT GetHistoryForToastCollectionIdAsync([in] HSTRING collection_id, [out, retval] Windows.Foundation.IAsyncOperation<Windows.UI.Notifications.ToastNotificationHistory *> **operation);
        [overload("GetToastCollectionManager")] HRESULT GetToastCollectionManager([out, retval] Windows.UI.Notifications.ToastCollectionManager **result);
        [overload("GetToastCollectionManager")] HRESULT GetToastCollectionManagerWithAppId([in] HSTRING app_id, [out, retval] Windows.UI.Notifications.ToastCollectionManager **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 15.0),
        exclusiveto(Windows.UI.Notifications.ToastNotificationManagerForUser),
        uuid(3efcb176-6cc1-56dc-973b-251f7aacb1c5)
    ]
    interface IToastNotificationManagerForUser3 : IInspectable
    {
        [propget] HRESULT NotificationMode([out, retval] Windows.UI.Notifications.ToastNotificationMode *value);
        [eventadd] HRESULT NotificationModeChanged(
            [in] Windows.Foundation.TypedEventHandler<Windows.UI.Notifications.ToastNotificationManagerForUser *, IInspectable *> *handler,
            [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT NotificationModeChanged([in] EventRegistrationToken token);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ToastNotificationManager),
        uuid(50ac103f-d235-4598-bbef-98fe4d1a3ad4)
    ]
    interface IToastNotificationManagerStatics : IInspectable
    {
        [overload("CreateToastNotifier")] HRESULT CreateToastNotifier([out, retval] Windows.UI.Notifications.ToastNotifier **result);
        [overload("CreateToastNotifier")] HRESULT CreateToastNotifierWithId([in] HSTRING application_id, [out, retval] Windows.UI.Notifications.ToastNotifier **result);
        HRESULT GetTemplateContent([in] Windows.UI.Notifications.ToastTemplateType type, [out, retval] Windows.Data.Xml.Dom.XmlDocument **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ToastNotificationManager),
        uuid(7ab93c52-0e48-4750-ba9d-1a4113981847)
    ]
    interface IToastNotificationManagerStatics2 : IInspectable
    {
        [propget] HRESULT History([out, retval] Windows.UI.Notifications.ToastNotificationHistory **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.ToastNotificationManager),
        uuid(8F993FD3-E516-45FB-8130-398E93FA52C3)
    ]
    interface IToastNotificationManagerStatics4 : IInspectable
    {
        HRESULT GetForUser([in] Windows.System.User *user, [out, retval] Windows.UI.Notifications.ToastNotificationManagerForUser **result);
        HRESULT ConfigureNotificationMirroring([in] Windows.UI.Notifications.NotificationMirroring value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.Notifications.ToastNotificationManager),
        uuid(d6f5f569-d40d-407c-8989-88cab42cfd14)
    ]
    interface IToastNotificationManagerStatics5 : IInspectable
    {
        HRESULT GetDefault([out, retval] Windows.UI.Notifications.ToastNotificationManagerForUser **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Notifications.ToastNotifier),
        uuid(75927b93-03f3-41ec-91d3-6e5bac1b38e7)
    ]
    interface IToastNotifier : IInspectable
    {
        HRESULT Show([in] Windows.UI.Notifications.ToastNotification *notification);
        HRESULT Hide([in] Windows.UI.Notifications.ToastNotification *notification);
        [propget] HRESULT Setting([out, retval] Windows.UI.Notifications.NotificationSetting *value);
        HRESULT AddToSchedule([in] Windows.UI.Notifications.ScheduledToastNotification *scheduled_toast);
        HRESULT RemoveFromSchedule([in] Windows.UI.Notifications.ScheduledToastNotification *scheduled_toast);
        HRESULT GetScheduledToastNotifications([out, retval] Windows.Foundation.Collections.IVectorView<Windows.UI.Notifications.ScheduledToastNotification *> **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.Notifications.ToastNotifier),
        uuid(354389c6-7c01-4bd5-9c20-604340cd2b74)
    ]
    interface IToastNotifier2 : IInspectable
    {
        [overload("Update")] HRESULT UpdateWithTagAndGroup([in] Windows.UI.Notifications.NotificationData *data, [in] HSTRING tag, [in] HSTRING group, [out, retval] Windows.UI.Notifications.NotificationUpdateResult *result);
        [overload("Update")] HRESULT UpdateWithTag([in] Windows.UI.Notifications.NotificationData *data, [in] HSTRING tag, [out, retval] Windows.UI.Notifications.NotificationUpdateResult *result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 7.0),
        exclusiveto(Windows.UI.Notifications.ToastNotifier),
        uuid(ae75a04a-3b0c-51ad-b7e8-b08ab6052549)
    ]
    interface IToastNotifier3 : IInspectable
    {
        [eventadd] HRESULT ScheduledToastNotificationShowing(
            [in] Windows.Foundation.TypedEventHandler<Windows.UI.Notifications.ToastNotifier *, Windows.UI.Notifications.ScheduledToastNotificationShowingEventArgs *> *handler,
            [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT ScheduledToastNotificationShowing([in] EventRegistrationToken token);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.UserNotification),
        uuid(adf7e52f-4e53-42d5-9c33-eb5ea515b23e)
    ]
    interface IUserNotification : IInspectable
    {
        [propget] HRESULT Notification([out, retval] Windows.UI.Notifications.Notification **value);
        [propget] HRESULT AppInfo([out, retval] Windows.ApplicationModel.AppInfo **value);
        [propget] HRESULT Id([out, retval] UINT32 *value);
        [propget] HRESULT CreationTime([out, retval] Windows.Foundation.DateTime *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Notifications.UserNotificationChangedEventArgs),
        uuid(b6bd6839-79cf-4b25-82c0-0ce1eef81f8c)
    ]
    interface IUserNotificationChangedEventArgs : IInspectable
    {
        [propget] HRESULT ChangeKind([out, retval] Windows.UI.Notifications.UserNotificationChangedKind *value);
        [propget] HRESULT UserNotificationId([out, retval] UINT32 *value);
    }

    [
        activatable(Windows.Foundation.UniversalApiContract, 3.0),
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass AdaptiveNotificationText
    {
        [default] interface Windows.UI.Notifications.IAdaptiveNotificationText;
        interface Windows.UI.Notifications.IAdaptiveNotificationContent;
    }

    [
        activatable(Windows.UI.Notifications.IBadgeNotificationFactory, Windows.Foundation.UniversalApiContract, 1.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        threading(mta)
    ]
    runtimeclass BadgeNotification
    {
        [default] interface Windows.UI.Notifications.IBadgeNotification;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        static(Windows.UI.Notifications.IBadgeUpdateManagerStatics, Windows.Foundation.UniversalApiContract, 1.0),
        static(Windows.UI.Notifications.IBadgeUpdateManagerStatics2, Windows.Foundation.UniversalApiContract, 3.0),
        threading(mta)
    ]
    runtimeclass BadgeUpdateManager
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile)
    ]
    runtimeclass BadgeUpdateManagerForUser
    {
        [default] interface Windows.UI.Notifications.IBadgeUpdateManagerForUser;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass BadgeUpdater
    {
        [default] interface Windows.UI.Notifications.IBadgeUpdater;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile),
        static(Windows.UI.Notifications.IKnownAdaptiveNotificationHintsStatics, Windows.Foundation.UniversalApiContract, 3.0),
        threading(both)
    ]
    runtimeclass KnownAdaptiveNotificationHints
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile),
        static(Windows.UI.Notifications.IKnownAdaptiveNotificationTextStylesStatics, Windows.Foundation.UniversalApiContract, 3.0),
        threading(both)
    ]
    runtimeclass KnownAdaptiveNotificationTextStyles
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile),
        static(Windows.UI.Notifications.IKnownNotificationBindingsStatics, Windows.Foundation.UniversalApiContract, 3.0),
        threading(both)
    ]
    runtimeclass KnownNotificationBindings
    {
    }

    [
        activatable(Windows.Foundation.UniversalApiContract, 3.0),
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass Notification
    {
        [default] interface Windows.UI.Notifications.INotification;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile)
    ]
    runtimeclass NotificationBinding
    {
        [default] interface Windows.UI.Notifications.INotificationBinding;
    }

    [
        activatable(Windows.Foundation.UniversalApiContract, 4.0),
        activatable(Windows.UI.Notifications.INotificationDataFactory, Windows.Foundation.UniversalApiContract, 4.0),
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass NotificationData
    {
        [default] interface Windows.UI.Notifications.INotificationData;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile)
    ]
    runtimeclass NotificationVisual
    {
        [default] interface Windows.UI.Notifications.INotificationVisual;
    }

    [
        activatable(Windows.UI.Notifications.IScheduledTileNotificationFactory, Windows.Foundation.UniversalApiContract, 1.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        threading(mta)
    ]
    runtimeclass ScheduledTileNotification
    {
        [default] interface Windows.UI.Notifications.IScheduledTileNotification;
    }

    [
        activatable(Windows.UI.Notifications.IScheduledToastNotificationFactory, Windows.Foundation.UniversalApiContract, 1.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        threading(mta)
    ]
    runtimeclass ScheduledToastNotification
    {
        [default] interface Windows.UI.Notifications.IScheduledToastNotification;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.UI.Notifications.IScheduledToastNotification2;
        [contract(Windows.Foundation.UniversalApiContract, 3.0)] interface Windows.UI.Notifications.IScheduledToastNotification3;
        [contract(Windows.Foundation.UniversalApiContract, 6.0)] interface Windows.UI.Notifications.IScheduledToastNotification4;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 7.0),
        marshaling_behavior(agile)
    ]
    runtimeclass ScheduledToastNotificationShowingEventArgs
    {
        [default] interface Windows.UI.Notifications.IScheduledToastNotificationShowingEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile)
    ]
    runtimeclass ShownTileNotification
    {
        [default] interface Windows.UI.Notifications.IShownTileNotification;
    }

    [
        activatable(Windows.UI.Notifications.ITileFlyoutNotificationFactory, Windows.Foundation.UniversalApiContract, 1.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        threading(mta)
    ]
    runtimeclass TileFlyoutNotification
    {
        [default] interface Windows.UI.Notifications.ITileFlyoutNotification;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        static(Windows.UI.Notifications.ITileFlyoutUpdateManagerStatics, Windows.Foundation.UniversalApiContract, 1.0),
        threading(mta)
    ]
    runtimeclass TileFlyoutUpdateManager
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass TileFlyoutUpdater
    {
        [default] interface Windows.UI.Notifications.ITileFlyoutUpdater;
    }

    [
        activatable(Windows.UI.Notifications.ITileNotificationFactory, Windows.Foundation.UniversalApiContract, 1.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        threading(mta)
    ]
    runtimeclass TileNotification
    {
        [default] interface Windows.UI.Notifications.ITileNotification;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        static(Windows.UI.Notifications.ITileUpdateManagerStatics, Windows.Foundation.UniversalApiContract, 1.0),
        static(Windows.UI.Notifications.ITileUpdateManagerStatics2, Windows.Foundation.UniversalApiContract, 3.0),
        threading(mta)
    ]
    runtimeclass TileUpdateManager
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile)
    ]
    runtimeclass TileUpdateManagerForUser
    {
        [default] interface Windows.UI.Notifications.ITileUpdateManagerForUser;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass TileUpdater
    {
        [default] interface Windows.UI.Notifications.ITileUpdater;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.UI.Notifications.ITileUpdater2;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass ToastActivatedEventArgs
    {
        [default] interface Windows.UI.Notifications.IToastActivatedEventArgs;
        [contract(Windows.Foundation.UniversalApiContract, 8.0)] interface Windows.UI.Notifications.IToastActivatedEventArgs2;
    }

    [
        activatable(Windows.UI.Notifications.IToastCollectionFactory, Windows.Foundation.UniversalApiContract, 4.0),
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        marshaling_behavior(agile),
        threading(mta)
    ]
    runtimeclass ToastCollection
    {
        [default] interface Windows.UI.Notifications.IToastCollection;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        marshaling_behavior(agile)
    ]
    runtimeclass ToastCollectionManager
    {
        [default] interface Windows.UI.Notifications.IToastCollectionManager;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass ToastDismissedEventArgs
    {
        [default] interface Windows.UI.Notifications.IToastDismissedEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass ToastFailedEventArgs
    {
        [default] interface Windows.UI.Notifications.IToastFailedEventArgs;
    }

    [
        activatable(Windows.UI.Notifications.IToastNotificationFactory, Windows.Foundation.UniversalApiContract, 1.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        threading(mta)
    ]
    runtimeclass ToastNotification
    {
        [default] interface Windows.UI.Notifications.IToastNotification;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.UI.Notifications.IToastNotification2;
        [contract(Windows.Foundation.UniversalApiContract, 3.0)] interface Windows.UI.Notifications.IToastNotification3;
        [contract(Windows.Foundation.UniversalApiContract, 4.0)] interface Windows.UI.Notifications.IToastNotification4;
        [contract(Windows.Foundation.UniversalApiContract, 8.0)] interface Windows.UI.Notifications.IToastNotification6;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass ToastNotificationActionTriggerDetail
    {
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] [default] interface Windows.UI.Notifications.IToastNotificationActionTriggerDetail;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass ToastNotificationHistory
    {
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.UI.Notifications.IToastNotificationHistory2;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] [default] interface Windows.UI.Notifications.IToastNotificationHistory;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass ToastNotificationHistoryChangedTriggerDetail
    {
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] [default] interface Windows.UI.Notifications.IToastNotificationHistoryChangedTriggerDetail;
        [contract(Windows.Foundation.UniversalApiContract, 4.0)] interface Windows.UI.Notifications.IToastNotificationHistoryChangedTriggerDetail2;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        static(Windows.UI.Notifications.IToastNotificationManagerStatics, Windows.Foundation.UniversalApiContract, 1.0),
        static(Windows.UI.Notifications.IToastNotificationManagerStatics2, Windows.Foundation.UniversalApiContract, 1.0),
        static(Windows.UI.Notifications.IToastNotificationManagerStatics4, Windows.Foundation.UniversalApiContract, 3.0),
        static(Windows.UI.Notifications.IToastNotificationManagerStatics5, Windows.Foundation.UniversalApiContract, 4.0),
        threading(mta)
    ]
    runtimeclass ToastNotificationManager
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile)
    ]
    runtimeclass ToastNotificationManagerForUser
    {
        [default] interface Windows.UI.Notifications.IToastNotificationManagerForUser;
        [contract(Windows.Foundation.UniversalApiContract, 4.0)] interface Windows.UI.Notifications.IToastNotificationManagerForUser2;
        [contract(Windows.Foundation.UniversalApiContract, 15.0)] interface Windows.UI.Notifications.IToastNotificationManagerForUser3;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass ToastNotifier
    {
        [default] interface Windows.UI.Notifications.IToastNotifier;
        [contract(Windows.Foundation.UniversalApiContract, 4.0)] interface Windows.UI.Notifications.IToastNotifier2;
        [contract(Windows.Foundation.UniversalApiContract, 7.0)] interface Windows.UI.Notifications.IToastNotifier3;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile)
    ]
    runtimeclass UserNotification
    {
        [default] interface Windows.UI.Notifications.IUserNotification;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile)
    ]
    runtimeclass UserNotificationChangedEventArgs
    {
        [default] interface Windows.UI.Notifications.IUserNotificationChangedEventArgs;
    }
}
