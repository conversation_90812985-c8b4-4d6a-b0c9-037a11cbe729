<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_PARAM_print_to_bio</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_PARAM_print_to_bio - OSSL_PARAM interrogation utilities</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/params.h&gt;

int OSSL_PARAM_print_to_bio(const OSSL_PARAM *p, BIO *bio,
                            int print_values);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>OSSL_PARAM_print_to_bio() formats each parameter contained in the passed in array of <b>OSSL_PARAM</b> values <i>p</i>, and prints both the key, and optionally its value, to a provided <b>BIO</b>. <i>p</i> must be a non-null array of OSSL_PARAM values, terminated with a value containing a null <i>key</i> member. <i>print_values</i> is a control parameter, indicating that key values should be printed, in addition to key names.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_PARAM_print_to_bio() returns 1 on success, and 0 on failure</p>

<h1 id="HISTORY">HISTORY</h1>

<p>OSSL_PARAM_print_to_bio() was added in OpenSSL 3.5</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


