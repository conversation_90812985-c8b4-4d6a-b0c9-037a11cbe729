_hardlink_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-x'|'--exclude')
			COMPREPLY=( $(compgen -W "regex" -- $cur) )
			return 0
			;;
		'-i'|'--include')
			COMPREPLY=( $(compgen -W "regex" -- $cur) )
			return 0
			;;
		'-s'|'--minimum-size')
			COMPREPLY=( $(compgen -W "number" -- $cur) )
			return 0
			;;
		'-S'|'--maximum-size')
			COMPREPLY=( $(compgen -W "number" -- $cur) )
			return 0
			;;
		'-b'|'--io-size')
			COMPREPLY=( $(compgen -W "number" -- $cur) )
			return 0
			;;
		'-r'|'--cache-size')
			COMPREPLY=( $(compgen -W "number" -- $cur) )
			return 0
			;;
		'-y'|'--method')
			COMPREPLY=( $(compgen -W "sha256 sha1 crc32c memcmp" -- $cur) )
			return 0
			;;
		'--reflink')
			COMPREPLY=( $(compgen -W "never always auto" -- $cur) )
			return 0
			;;
		'-H'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
		OPTS="
			--content
			--respect-dir
			--respect-name
			--maximize
			--minimize
			--dry-run
			--ignore-owner
			--keep-oldest
			--ignore-mode
			--quiet
			--ignore-time
			--verbose
			--respect-xattrs
			--skip-reflinks
			--version
			--help
		"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	local IFS=$'\n'
	compopt -o filenames
	COMPREPLY=( $(compgen -d -- $cur) )
	return 0
}
complete -F _hardlink_module hardlink
