.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_srtp_get_profile_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_srtp_get_profile_id \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_srtp_get_profile_id(const char * " name ", gnutls_srtp_profile_t * " profile ");"
.SH ARGUMENTS
.IP "const char * name" 12
The name of the profile to look up
.IP "gnutls_srtp_profile_t * profile" 12
Will hold the profile id
.SH "DESCRIPTION"
This function allows you to look up a profile based on a string.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.

Since 3.1.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
