#!/bin/sh

# Copyright (C) 1998, 2002, 2004, 2007, 2010-2025 Free Software Foundation,
# Inc.
# Copyright (C) 1993 <PERSON><PERSON><PERSON><PERSON> Gail<PERSON>

# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.

# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.

LC_ALL=C
export LC_ALL

version="znew (gzip) 1.14
Copyright (C) 2025 Free Software Foundation, Inc.
This is free software.  You may redistribute copies of it under the terms of
the GNU General Public License <https://www.gnu.org/licenses/gpl.html>.
There is NO WARRANTY, to the extent permitted by law.

Written by <PERSON><PERSON><PERSON><PERSON>."

usage="Usage: $0 [OPTION]... [FILE]...
Recompress files from .Z (compress) format to .gz (gzip) format.

Options:

  -f     Force recompression even if a .gz file already exists.
  -t     Test the new files before deleting originals.
  -v     Verbose; display name and statistics for each file compressed.
  -9     Use the slowest compression method (optimal compression).
  -P     Use pipes for the conversion to reduce disk space usage.
  -K     Keep a .Z file when it is smaller than the .gz file; implies -t.
      --help     display this help and exit
      --version  output version information and exit

Report bugs to <<EMAIL>>."

check=0
pipe=0
opt=
files=
keep=0
res=0
old=0
new=0
block=1024
# block is the disk block size (best guess, need not be exact)

# Beware -s or --suffix in $GZIP, which could cause misbehavior
# in gzip 1.13 and earlier.
unset GZIP
ext=.gz

for arg
do
  case "$arg" in
  --help)      printf '%s\n' "$usage"   || exit 1; exit;;
  --version)   printf '%s\n' "$version" || exit 1; exit;;
  -*)     opt="$opt $arg"; shift;;
   *)     break;;
  esac
done

if test $# -eq 0; then
  echo >&2 "$0: invalid number of operands; try \`$0 --help' for help"
  exit 1
fi

opt=`printf '%s\n' "$opt" | sed -e 's/ //g' -e 's/-//g'`
case "$opt" in
  *t*) check=1; opt=`printf '%s\n' "$opt" | sed 's/t//g'`
esac
case "$opt" in
  *K*) keep=1; check=1; opt=`printf '%s\n' "$opt" | sed 's/K//g'`
esac
case "$opt" in
  *P*) pipe=1; opt=`printf '%s\n' "$opt" | sed 's/P//g'`
esac
if test -n "$opt"; then
  opt="-$opt"
fi

for i do
  n=`printf '%s\n' "$i" | sed 's/.Z$//'`
  if test ! -f "$n.Z" ; then
    printf '%s\n' "$n.Z not found"
    res=1; continue
  fi
  test $keep -eq 1 && old=`wc -c < "$n.Z"`
  if test $pipe -eq 1; then
    if gzip -d < "$n.Z" | gzip $opt > "$n$ext"; then
      # Copy file attributes from old file to new one, if possible.
      touch -r"$n.Z" -- "$n$ext" 2>/dev/null
      chmod --reference="$n.Z" -- "$n$ext" 2>/dev/null
    else
      printf '%s\n' "error while recompressing $n.Z"
      res=1; continue
    fi
  else
    if test $check -eq 1; then
      if cp -p "$n.Z" "$n.$$"; then
        :
      else
        printf '%s\n' "cannot backup $n.Z"
        res=1; continue
      fi
    fi
    if gzip -d "$n.Z"; then
      :
    else
      test $check -eq 1 && mv "$n.$$" "$n.Z"
      printf '%s\n' "error while uncompressing $n.Z"
      res=1; continue
    fi
    if gzip $opt "$n"; then
      :
    else
      if test $check -eq 1; then
        mv "$n.$$" "$n.Z" && rm -f "$n"
        printf '%s\n' "error while recompressing $n"
      else
        # compress $n  (might be dangerous if disk full)
        printf '%s\n' "error while recompressing $n, left uncompressed"
      fi
      res=1; continue
    fi
  fi
  test $keep -eq 1 && new=`wc -c < "$n$ext"`
  if test $keep -eq 1 && test `expr \( $old + $block - 1 \) / $block` -lt \
                              `expr \( $new + $block - 1 \) / $block`; then
    if test $pipe -eq 1; then
      rm -f "$n$ext"
    else
      mv "$n.$$" "$n.Z" && rm -f "$n$ext"
    fi
    printf '%s\n' "$n.Z smaller than $n$ext -- unchanged"

  elif test $check -eq 1; then
    if gzip -t "$n$ext" ; then
      rm -f "$n.$$" "$n.Z"
    else
      test $pipe -eq 0 && mv "$n.$$" "$n.Z"
      rm -f "$n$ext"
      printf '%s\n' "error while testing $n$ext, $n.Z unchanged"
      res=1; continue
    fi
  elif test $pipe -eq 1; then
    rm -f "$n.Z"
  fi
done
exit $res
