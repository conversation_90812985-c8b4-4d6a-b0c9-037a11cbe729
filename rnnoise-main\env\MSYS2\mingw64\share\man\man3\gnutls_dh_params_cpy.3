.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dh_params_cpy" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dh_params_cpy \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_dh_params_cpy(gnutls_dh_params_t " dst ", gnutls_dh_params_t " src ");"
.SH ARGUMENTS
.IP "gnutls_dh_params_t dst" 12
Is the destination parameters, which should be initialized.
.IP "gnutls_dh_params_t src" 12
Is the source parameters
.SH "DESCRIPTION"
This function will copy the DH parameters structure from source
to destination. The destination should be already initialized.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
