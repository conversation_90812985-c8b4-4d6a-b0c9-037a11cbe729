<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>mkpasswd</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-ug-net.html" title="Cygwin User's Guide"><link rel="up" href="using-utils.html" title="Cygwin Utilities"><link rel="prev" href="mkgroup.html" title="mkgroup"><link rel="next" href="mount.html" title="mount"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">mkpasswd</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="mkgroup.html">Prev</a>&#160;</td><th width="60%" align="center">Cygwin Utilities</th><td width="20%" align="right">&#160;<a accesskey="n" href="mount.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="mkpasswd"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>mkpasswd &#8212; Write /etc/passwd-like output to stdout</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="cmdsynopsis"><p><code class="command">mkpasswd</code>  [
	    -l  |   -L  
	   [<em class="replaceable"><code>MACHINE</code></em>]
	] [-d [<em class="replaceable"><code>DOMAIN</code></em>]] [-c] [-S <em class="replaceable"><code>CHAR</code></em>] [-o <em class="replaceable"><code>OFFSET</code></em>] [-u <em class="replaceable"><code>USERNAME</code></em>] [-b] [-U  <em class="replaceable"><code>USERLIST</code></em>]</p></div><div class="cmdsynopsis"><p><code class="command">mkpassword</code>    -h  |   -V  </p></div></div><div class="refsect1"><a name="mkpasswd-options"></a><h2>Options</h2><pre class="screen">
   -l,--local [machine]    Print local user accounts of \"machine\",
                           from local machine if no machine specified.
                           Automatically adding machine prefix for local
                           machine depends on settings in /etc/nsswitch.conf.
   -L,--Local [machine]    Ditto, but generate username with machine prefix.
   -d,--domain [domain]    Print domain accounts,
                           from current domain if no domain specified.
   -c,--current            Print current user.
   -S,--separator char     For -L use character char as domain\\user
                           separator in username instead of the default '+'.
   -o,--id-offset offset   Change the default offset (0x10000) added to uids
                           of foreign local machine accounts.  Use with -l/-L.
   -u,--username username  Only return information for the specified user.
                           One of -l, -d must be specified, too
   -b,--no-builtin         Don't print BUILTIN users.
   -p,--path-to-home path  Use specified path instead of user account home dir
                           or /home prefix.
   -U,--unix userlist      Print UNIX users when using -l on a UNIX Samba
                           server.  Userlist is a comma-separated list of
                           usernames or uid ranges (root,-25,50-100).
                           Enumerating large ranges can take a long time!
   -h,--help               Displays this message.
   -V,--version            Version information and exit.

Default is to print local accounts on stand-alone machines, domain accounts
on domain controllers and domain member machines.
</pre></div><div class="refsect1"><a name="mkpasswd-desc"></a><h2>Description</h2><p>Don't use this command to generate a local /etc/passwd file, unless you
    really need one.  See the Cygwin User's Guide for more information.</p><p>The <span class="command"><strong>mkpasswd</strong></span> program can be used to create a
      <code class="filename">/etc/passwd</code> file.  Cygwin doesn't need this file,
      because it reads user information from the Windows account databases,
      but you can add an <code class="filename">/etc/passwd</code> file, for instance
      if your machine is often disconnected from its domain controller.</p><p>Note that this information is static, in contrast to the information
      automatically gathered by Cygwin from the Windows account databases. If
      you change the user information on your system, you'll need to regenerate
      the passwd file for it to have the new information.</p><p>By default, the information generated by <span class="command"><strong>mkpasswd</strong></span>
      is equivalent to the information generated by Cygwin itself.  The
      <code class="literal">-d</code> and <code class="literal">-l/-L</code> options allow you to
      specify where the information comes from, some domain, or the local SAM
      of a machine.  Note that you can only enumerate accounts from trusted
      domains.  Any non-trusted domain will be ignored.  Access-restrictions
      of your current account apply.  The <code class="literal">-l/-L</code> when used
      with a machine name, tries to contact that machine to enumerate local
      groups of other machines, typically outside of domains.  This scenario
      cannot be covered by Cygwin's account automatism.  If you want to use
      the <code class="literal">-L</code> option, but you don't like the default
      domain/group separator from <code class="filename">/etc/nsswitch.conf</code>,
      you can specify another separator using the <code class="literal">-S</code> option,
      analog to <span class="command"><strong>mkgroup</strong></span>.</p><p>For very simple needs, an entry for the current user can be created
      by using the option <code class="literal">-c</code>.</p><p>The <code class="literal">-o</code> option allows for special cases (such as
      multiple domains) where the UIDs might match otherwise. The
      <code class="literal">-p</code> option causes <span class="command"><strong>mkpasswd</strong></span> to use
      the specified prefix instead of the account home dir or <code class="literal">/home/
      </code>. For example, this command:
    </p><div class="example"><a name="utils-althome-ex"></a><p class="title"><b>Example&#160;3.10.&#160;Using an alternate home root</b></p><div class="example-contents"><pre class="screen">
<code class="prompt">$</code> <strong class="userinput"><code>mkpasswd -l -p "$(cygpath -H)" &gt; /etc/passwd</code></strong>
</pre></div></div><br class="example-break"><p>
      would put local users' home directories in the Windows
      'Profiles' directory. The <code class="literal">-u</code> option creates just an
      entry for the specified user. The <code class="literal">-U</code> option allows you
      to enumerate the standard UNIX users on a Samba machine. It's used
      together with <code class="literal">-l samba-server</code> or <code class="literal">-L
      samba-server</code>. The normal UNIX users are usually not enumerated,
      but they can show up as file owners in <span class="command"><strong>ls -l</strong></span> output.
    </p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="mkgroup.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="using-utils.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="mount.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">mkgroup&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-ug-net.html">Home</a></td><td width="40%" align="right" valign="top">&#160;mount</td></tr></table></div></body></html>
