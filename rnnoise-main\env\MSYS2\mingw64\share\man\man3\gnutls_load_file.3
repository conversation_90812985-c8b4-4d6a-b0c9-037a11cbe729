.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_load_file" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_load_file \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_load_file(const char * " filename ", gnutls_datum_t * " data ");"
.SH ARGUMENTS
.IP "const char * filename" 12
the name of the file to load
.IP "gnutls_datum_t * data" 12
Where the file will be stored
.SH "DESCRIPTION"
This function will load a file into a datum. The data are
zero terminated but the terminating null is not included in length.
The returned data are allocated using \fBgnutls_malloc()\fP.

Note that this function is not designed for reading sensitive materials,
such as private keys, on practical applications. When the reading fails
in the middle, the partially loaded content might remain on memory.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.

Since 3.1.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
