.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crl_set_next_update" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crl_set_next_update \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crl_set_next_update(gnutls_x509_crl_t " crl ", time_t " exp_time ");"
.SH ARGUMENTS
.IP "gnutls_x509_crl_t crl" 12
should contain a gnutls_x509_crl_t type
.IP "time_t exp_time" 12
The actual time
.SH "DESCRIPTION"
This function will set the time this CRL will be updated.
This is an optional value to be set on a CRL and this call
can be omitted when generating a CRL.

Prior to GnuTLS 3.5.7, setting a nextUpdate field was required
in order to generate a CRL.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
