# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
# file LICENSE.rst or https://cmake.org/licensing for details.

cmake_minimum_required(VERSION ${CMAKE_VERSION}) # this file comes with cmake

# If CMAKE_DISABLE_SOURCE_CHANGES is set to true and the source directory is an
# existing directory in our source tree, calling file(MAKE_DIRECTORY) on it
# would cause a fatal error, even though it would be a no-op.
if(NOT EXISTS "@source_dir@")
  file(MAKE_DIRECTORY "@source_dir@")
endif()
file(MAKE_DIRECTORY
  "@binary_dir@"
  "@install_dir@"
  "@tmp_dir@"
  "@stamp_dir@"
  "@download_dir@"
  "@log_dir@"
)

set(configSubDirs @CMAKE_CONFIGURATION_TYPES@)
foreach(subDir IN LISTS configSubDirs)
    file(MAKE_DIRECTORY "@stamp_dir@/${subDir}")
endforeach()
if(cfgdir)
  file(MAKE_DIRECTORY "@stamp_dir@${cfgdir}") # cfgdir has leading slash
endif()
