[externally-managed]
Error=To install Python packages system-wide, try 'pacman -S
 $MINGW_PACKAGE_PREFIX-python-xyz', where xyz is the package you
 are trying to install.

 If you wish to install a non-MSYS2-packaged Python package,
 create a virtual environment using 'python -m venv path/to/venv'.
 Then use path/to/venv/bin/python and path/to/venv/bin/pip.

 If you wish to install a non-MSYS2 packaged Python application,
 it may be easiest to use 'pipx install xyz', which will manage a
 virtual environment for you. Make sure you have $MINGW_PACKAGE_PREFIX-python-pipx
 installed via pacman.


