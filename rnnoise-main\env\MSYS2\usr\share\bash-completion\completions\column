_column_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-c'|'--output-width'|'-l'|'--table-columns-limit')
			COMPREPLY=( $(compgen -W "number" -- $cur) )
			return 0
			;;
		'-s'|'--separator'|'-o'|'--output-separator'|'-n'|'--table-name'|'-O')
			COMPREPLY=( $(compgen -W "string" -- $cur) )
			return 0
			;;
		'-O'|'--table-order'|'-N'|'--table-columns'|'-E'|'--table-noextreme'|'-H'|'--table-hide'|'-R'|'--table-right'|'-T'|'--table-truncate'|'-W'|'--table-wrap')
			COMPREPLY=( $(compgen -W "string" -- $cur) )
			return 0
			;;
		'-r'|'--tree'|'-i'|'--tree-id'|'-p'|'--tree-parent')
			COMPREPLY=( $(compgen -W "string" -- $cur) )
			return 0
			;;
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="--columns
				--table
				--table-name
				--table-order
				--table-columns
				--table-columns-limit
				--table-noextreme
				--table-noheadings
				--table-maxout
				--table-header-repeat
				--table-hide
				--table-right
				--table-truncate
				--table-wrap
				--keep-empty-lines
				--json
				--tree
				--tree-id
				--tree-parent
				--output-width
				--separator
				--output-separator
				--fillrows
				--help
				--version"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	local IFS=$'\n'
	compopt -o filenames
	COMPREPLY=( $(compgen -f -- $cur) )
	return 0
}
complete -F _column_module column
