This directory contains sound files for deployment as mintty bell sounds.

### Mintty bell sounds ###

The BOING sound
is available on [YouTube, BOING.WAV](http://kunalsdatabase.com/ebooks/computer_ebooks/Hacking%20eBooks%20Collection/Hacking/Hacking%20Tools/Hacking%20Tools/HO2K%20v0.1.8.1/HO2K%20v0.1.8.1/).

Other nice boing sounds could be extracted from
[YouTube, Boing 0](https://www.youtube.com/watch?v=iew9op9aPLQ)
or
[YouTube, Boing 2](https://www.youtube.com/watch?v=d7vfbyFl5kc).

Further sounds are selected from a contribution of “naknak”, with some instructions 
[how to make bell sounds](https://github.com/mintty/mintty/issues/711#issuecomment-483074839) 
from sound sources that may be useful to others.
The sounds are provided under the creative commons license.

Suitable Windows sounds may be `Windows Default.wav`, `Windows Ding.wav`, 
`Windows Critical Stop.wav`, from the Windows subfolder `Media`.

To deploy a sound file to be listed in the Options dialog, 
simply copy a .wav file into the config subfolder `sounds` 
(e.g. in `$HOME/.config/mintty` or `$APPDATA/mintty`).
