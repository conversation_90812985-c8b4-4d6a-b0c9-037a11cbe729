<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>provider-skeymgmt</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Key-Objects">Key Objects</a></li>
      <li><a href="#Destructing-Function">Destructing Function</a></li>
      <li><a href="#Key-Object-Import-and-Export-Functions">Key Object Import and Export Functions</a></li>
      <li><a href="#Key-Object-Generation-Functions">Key Object Generation Functions</a></li>
      <li><a href="#Key-Object-Information-functions">Key Object Information functions</a></li>
      <li><a href="#Common-Import-and-Export-Parameters">Common Import and Export Parameters</a></li>
    </ul>
  </li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>provider-skeymgmt - The SKEYMGMT library &lt;-&gt; provider functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/core_dispatch.h&gt;

/*
 * None of these are actual functions, but are displayed like this for
 * the function signatures for functions that are offered as function
 * pointers in OSSL_DISPATCH arrays.
 */

/* Key object destruction */
void OSSL_FUNC_skeymgmt_free(void *keydata);

/* Key object import and export functions */
void *OSSL_FUNC_skeymgmt_import(void *provctx, int selection,
                                const OSSL_PARAM params[]);
int OSSL_FUNC_skeymgmt_export(void *keydata, int selection,
                              OSSL_CALLBACK *param_cb, void *cbarg);
void *OSSL_FUNC_skeymgmt_generate(void *provctx,
                                  const OSSL_PARAM params[]);
const OSSL_PARAM *OSSL_FUNC_skeymgmt_gen_settable_params(void *provctx);
const OSSL_PARAM *OSSL_FUNC_skeymgmt_imp_settable_params(void *provctx);
const char *OSSL_FUNC_skeymgmt_get_key_id(void *keydata);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The SKEYMGMT operation doesn&#39;t have much public visibility in the OpenSSL libraries, rather it is an internal operation that is designed to work with operations that use opaque symmetric keys objects.</p>

<p>The SKEYMGMT operation shares knowledge with the operations it works with, therefore the SKEYMGMT and the algorithms which use it must belong to the same provider. The OpenSSL libraries will ensure that they do.</p>

<p>The primary responsibility of the SKEYMGMT operation is to hold the provider side key data for the OpenSSL library EVP_SKEY structure.</p>

<p>All &quot;functions&quot; mentioned here are passed as function pointers between <i>libcrypto</i> and the provider in <a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> arrays via <a href="../man3/OSSL_ALGORITHM.html">OSSL_ALGORITHM(3)</a> arrays that are returned by the provider&#39;s provider_query_operation() function (see <a href="../man7/provider-base.html">&quot;Provider Functions&quot; in provider-base(7)</a>).</p>

<p>All these &quot;functions&quot; have a corresponding function type definition named <b>OSSL_FUNC_{name}_fn</b>, and a helper function to retrieve the function pointer from a <a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> element named <b>OSSL_FUNC_{name}</b>.</p>

<p><a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> arrays are indexed by numbers that are provided as macros in <a href="../man7/openssl-core_dispatch.h.html">openssl-core_dispatch.h(7)</a>, as follows:</p>

<pre><code>OSSL_FUNC_skeymgmt_free                 OSSL_FUNC_SKEYMGMT_FREE

OSSL_FUNC_skeymgmt_import               OSSL_FUNC_SKEYMGMT_IMPORT
OSSL_FUNC_skeymgmt_export               OSSL_FUNC_SKEYMGMT_EXPORT

OSSL_FUNC_skeymgmt_generate             OSSL_FUNC_SKEYMGMT_GENERATE

OSSL_FUNC_skeymgmt_get_key_id           OSSL_FUNC_SKEYMGMT_GET_KEY_ID
OSSL_FUNC_skeymgmt_imp_settable_params  OSSL_FUNC_SKEYMGMT_IMP_SETTABLE_PARAMS
OSSL_FUNC_skeymgmt_gen_settable_params  OSSL_FUNC_SKEYMGMT_GEN_SETTABLE_PARAMS</code></pre>

<p>The SKEYMGMT management is inspired by KEYMGMT but is simpler.</p>

<h2 id="Key-Objects">Key Objects</h2>

<p>A key object is a collection of data for an symmetric key, and is represented as <i>keydata</i> in this manual.</p>

<p>The exact contents of a key object are defined by the provider, and it is assumed that different operations in one and the same provider use the exact same structure to represent this collection of data, so that for example, a key object that has been created using the SKEYMGMT interface can be passed as is to other algorithms from the same provider operations, such as OSSL_FUNC_mac_init_opaque() (see <a href="../man7/provider-mac.html">provider-mac(7)</a>).</p>

<p>With the export SKEYMGMT function, it&#39;s possible to select a specific subset of data to handle, governed by the bits in a <i>selection</i> indicator. The bits are:</p>

<dl>

<dt id="OSSL_SKEYMGMT_SELECT_SECRET_KEY"><b>OSSL_SKEYMGMT_SELECT_SECRET_KEY</b></dt>
<dd>

<p>Indicating that the secret key raw bytes in a key object should be included.</p>

</dd>
<dt id="OSSL_SKEYMGMT_SELECT_PARAMETERS"><b>OSSL_SKEYMGMT_SELECT_PARAMETERS</b></dt>
<dd>

<p>Indicating that the parameters in a key object should be included.</p>

</dd>
</dl>

<p>Combined selector bits are also defined for easier use:</p>

<dl>

<dt id="OSSL_SKEYMGMT_SELECT_ALL"><b>OSSL_SKEYMGMT_SELECT_ALL</b></dt>
<dd>

<p>Indicating that everything in a key object should be included.</p>

</dd>
</dl>

<p>The exact interpretation of those bits or how they combine is left to each function where you can specify a selector.</p>

<h2 id="Destructing-Function">Destructing Function</h2>

<p>OSSL_FUNC_skeymgmt_free() should free the passed <i>keydata</i>.</p>

<h2 id="Key-Object-Import-and-Export-Functions">Key Object Import and Export Functions</h2>

<p>OSSL_FUNC_skeymgmt_import() should import data into <i>keydata</i> with values taken from the <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array <i>params</i>. It allocates the <i>keydata</i> object (there is no separate allocation function).</p>

<p>OSSL_FUNC_skeymgmt_imp_settable_params() returns a list of parameters that can be provided to the OSSL_FUNC_skeymgmt_import() function.</p>

<p>OSSL_FUNC_skeymgmt_export() should extract values indicated by <i>selection</i> from <i>keydata</i>, create an <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array with them and call <i>param_cb</i> with that array as well as the given <i>cbarg</i>. The passed <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array is transient and is freed upon the return from <i>param_cb</i>.</p>

<h2 id="Key-Object-Generation-Functions">Key Object Generation Functions</h2>

<p>OSSL_FUNC_skeymgmt_generate() creates a new key according to the values taken from the <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array <i>params</i>. It allocates the <i>keydata</i> object.</p>

<p>OSSL_FUNC_skeymgmt_gen_settable_params() returns a list of parameters that can be provided to the OSSL_FUNC_skeymgmt_generate() function.</p>

<h2 id="Key-Object-Information-functions">Key Object Information functions</h2>

<p>OSSL_FUNC_skeymgmt_get_key_id() returns a NUL-terminated string identifying the particular key. The returned string will be freed by a call to EVP_SKEY_free() so callers need to copy it themselves if they want to preserve the value past the key lifetime. The purpose of this function is providing a printable string that can help users to access the specific key. The content of this string is provider-specific.</p>

<h2 id="Common-Import-and-Export-Parameters">Common Import and Export Parameters</h2>

<p>See <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> for further details on the parameters structure.</p>

<p>Common information parameters currently recognised by built-in skeymgmt algorithms are as follows:</p>

<dl>

<dt id="raw-bytes-SKEY_PARAM_RAW_BYTES-octet-string">&quot;raw-bytes&quot; (<b>SKEY_PARAM_RAW_BYTES</b>) &lt;octet string&gt;</dt>
<dd>

<p>The value represents symmetric key as a byte array.</p>

</dd>
<dt id="key-length-SKEY_PARAM_KEY_LENGTH-integer">&quot;key-length&quot; (<b>SKEY_PARAM_KEY_LENGTH</b>) &lt;integer&gt;</dt>
<dd>

<p>The value is the byte length of the given key.</p>

</dd>
</dl>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_FUNC_skeymgmt_import() and OSSL_FUNC_skeymgmt_generate() return a pointer to an allocated object on success and NULL on error.</p>

<p>OSSL_FUNC_skeymgmt_export() returns 1 for success or 0 on error.</p>

<p>OSSL_FUNC_skeymgmt_get_key_id() returns a pointer to a 0-terminated string or NULL.</p>

<p>OSSL_FUNC_skeymgmt_gen_settable_params() and OSSL_FUNC_skeymgmt_imp_settable_params() return references to an array of <b>OSSL_PARAM</b> which can be NULL if there are no settable parameters.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider.html">provider(7)</a>, <a href="../man3/EVP_SKEY.html">EVP_SKEY(3)</a>, <a href="../man3/EVP_KEYMGMT.html">EVP_KEYMGMT(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The SKEYMGMT interface was introduced in OpenSSL 3.5.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2024-2025 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


