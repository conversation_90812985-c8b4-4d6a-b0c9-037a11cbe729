/*
 * Copyright 2021 Jace<PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "unknwn.idl";
import "oaidl.idl";
import "ocidl.idl";

#ifndef __WIDL__
#define threading(model)
#endif

typedef struct _EVENT_DESCRIPTOR
{
    USHORT     Id;
    UCHAR      Version;
    UCHAR      Channel;
    UCHAR      Level;
    UCHAR      Opcode;
    USHORT     Task;
    ULONGLONG  Keyword;
} EVENT_DESCRIPTOR, *PEVENT_DESCRIPTOR;
typedef const EVENT_DESCRIPTOR *PCEVENT_DESCRIPTOR;

typedef struct _EVENT_HEADER
{
    USHORT            Size;
    USHORT            HeaderType;
    USHORT            Flags;
    USHORT            EventProperty;
    ULONG             ThreadId;
    ULONG             ProcessId;
    LARGE_INTEGER     TimeStamp;
    GUID              ProviderId;
    EVENT_DESCRIPTOR  EventDescriptor;
    union {
        struct {
            ULONG     KernelTime;
            ULONG     UserTime;
        } DUMMYSTRUCTNAME;
        ULONG64       ProcessorTime;
    } DUMMYUNIONNAME;
    GUID              ActivityId;
} EVENT_HEADER, *PEVENT_HEADER;

typedef struct _ETW_BUFFER_CONTEXT
{
    union {
        struct {
            UCHAR ProcessorNumber;
            UCHAR Alignment;
        } DUMMYSTRUCTNAME;
        USHORT ProcessorIndex;
    } DUMMYUNIONNAME;
    USHORT LoggerId;
} ETW_BUFFER_CONTEXT, *PETW_BUFFER_CONTEXT;

typedef struct _EVENT_HEADER_EXTENDED_DATA_ITEM
{
    USHORT     Reserved1;
    USHORT     ExtType;
    USHORT     Linkage;
    USHORT     DataSize;
    ULONGLONG  DataPtr;
} EVENT_HEADER_EXTENDED_DATA_ITEM, *PEVENT_HEADER_EXTENDED_DATA_ITEM;

typedef struct _EVENT_RECORD
{
    EVENT_HEADER                      EventHeader;
    ETW_BUFFER_CONTEXT                BufferContext;
    USHORT                            ExtendedDataCount;
    USHORT                            UserDataLength;
    EVENT_HEADER_EXTENDED_DATA_ITEM  *ExtendedData;
    void                             *UserData;
    void                             *UserContext;
} EVENT_RECORD, *PEVENT_RECORD;

typedef ULONG64 TRACEHANDLE, *PTRACEHANDLE;

[
    object,
    uuid(8cc97f40-9028-4ff3-9b62-7d1f79ca7bcb),
    local
]
interface ITraceEvent : IUnknown
{
    HRESULT Clone([out, retval] ITraceEvent **NewEvent);

    HRESULT GetUserContext([out, retval] void **UserContext);

    HRESULT GetEventRecord([out, retval] PEVENT_RECORD *EventRecord);

    HRESULT SetPayload([in, size_is(PayloadSize)] BYTE *Payload,
                       [in] ULONG PayloadSize);

    HRESULT SetEventDescriptor([in] PCEVENT_DESCRIPTOR EventDescriptor);

    HRESULT SetProcessId([in] ULONG ProcessId);

    HRESULT SetProcessorIndex([in] ULONG ProcessorIndex);

    HRESULT SetThreadId([in] ULONG ThreadId);

    HRESULT SetThreadTimes([in] ULONG KernelTime,
                           [in] ULONG UserTime);

    HRESULT SetActivityId([in] LPCGUID ActivityId);

    HRESULT SetTimeStamp([in] LARGE_INTEGER *TimeStamp);

    HRESULT SetProviderId([in] LPCGUID ProviderId);
}

interface ITraceRelogger;

[
    object,
    uuid(3ed25501-593f-43e9-8f38-3ab46f5a4a52)
]
interface ITraceEventCallback : IUnknown
{
    HRESULT OnBeginProcessTrace([in] ITraceEvent *HeaderEvent,
                                [in] ITraceRelogger *Relogger);

    HRESULT OnFinalizeProcessTrace([in] ITraceRelogger *Relogger);

    HRESULT OnEvent([in] ITraceEvent *Event,
                    [in] ITraceRelogger *Relogger);
}

[
    object,
    uuid(f754ad43-3bcc-4286-8009-9c5da214e84e),
    local
]
interface ITraceRelogger : IUnknown
{
    HRESULT AddLogfileTraceStream([in] BSTR LogfileName,
                                  [in] void *UserContext,
                                  [out, retval] TRACEHANDLE *TraceHandle);

    HRESULT AddRealtimeTraceStream([in] BSTR LoggerName,
                                   [in] void *UserContext,
                                   [out, retval] TRACEHANDLE *TraceHandle);

    HRESULT RegisterCallback([in] ITraceEventCallback *Callback);

    HRESULT Inject([in] ITraceEvent *Event);

    HRESULT CreateEventInstance([in] TRACEHANDLE TraceHandle,
                                [in] ULONG Flags,
                                [out, retval] ITraceEvent **Event);

    HRESULT ProcessTrace();

    HRESULT SetOutputFilename([in] BSTR LogfileName);

    HRESULT SetCompressionMode([in] BOOLEAN CompressionMode);

    HRESULT Cancel();
}

[
    uuid(7b40792d-05ff-44c4-9058-f440c71f17d4),
    threading(both)
]
coclass CTraceRelogger
{
    [default] interface ITraceRelogger;
}
