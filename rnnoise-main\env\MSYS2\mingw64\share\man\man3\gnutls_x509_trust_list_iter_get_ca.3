.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_iter_get_ca" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_iter_get_ca \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_trust_list_iter_get_ca(gnutls_x509_trust_list_t " list ", gnutls_x509_trust_list_iter_t * " iter ", gnutls_x509_crt_t * " crt ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The list
.IP "gnutls_x509_trust_list_iter_t * iter" 12
A pointer to an iterator (initially the iterator should be \fBNULL\fP)
.IP "gnutls_x509_crt_t * crt" 12
where the certificate will be copied
.SH "DESCRIPTION"
This function obtains a certificate in the trust list and advances the
iterator to the next certificate. The certificate returned in  \fIcrt\fP must be
deallocated with \fBgnutls_x509_crt_deinit()\fP.

When past the last element is accessed \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
is returned and the iterator is reset.

The iterator is deinitialized and reset to \fBNULL\fP automatically by this
function after iterating through all elements until
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP is returned. If the iteration is
aborted early, it must be manually deinitialized using
\fBgnutls_x509_trust_list_iter_deinit()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
