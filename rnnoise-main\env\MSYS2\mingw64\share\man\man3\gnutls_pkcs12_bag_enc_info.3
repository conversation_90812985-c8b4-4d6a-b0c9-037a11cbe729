.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs12_bag_enc_info" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs12_bag_enc_info \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs12.h>
.sp
.BI "int gnutls_pkcs12_bag_enc_info(gnutls_pkcs12_bag_t " bag ", unsigned int * " schema ", unsigned int * " cipher ", void * " salt ", unsigned int * " salt_size ", unsigned int * " iter_count ", char ** " oid ");"
.SH ARGUMENTS
.IP "gnutls_pkcs12_bag_t bag" 12
The bag
.IP "unsigned int * schema" 12
indicate the schema as one of \fBgnutls_pkcs_encrypt_flags_t\fP
.IP "unsigned int * cipher" 12
the cipher used as \fBgnutls_cipher_algorithm_t\fP
.IP "void * salt" 12
PBKDF2 salt (if non\-NULL then  \fIsalt_size\fP initially holds its size)
.IP "unsigned int * salt_size" 12
PBKDF2 salt size
.IP "unsigned int * iter_count" 12
PBKDF2 iteration count
.IP "char ** oid" 12
if non\-NULL it will contain an allocated null\-terminated variable with the OID
.SH "DESCRIPTION"
This function will provide information on the encryption algorithms used
in an encrypted bag. If the structure algorithms
are unknown the code \fBGNUTLS_E_UNKNOWN_CIPHER_TYPE\fP will be returned,
and only  \fIoid\fP , will be set. That is,  \fIoid\fP will be set on encrypted bags
whether supported or not. It must be deinitialized using \fBgnutls_free()\fP.
The other variables are only set on supported structures.
.SH "RETURNS"
\fBGNUTLS_E_INVALID_REQUEST\fP if the provided bag isn't encrypted,
\fBGNUTLS_E_UNKNOWN_CIPHER_TYPE\fP if the structure's encryption isn't supported, or
another negative error code in case of a failure. Zero on success.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
