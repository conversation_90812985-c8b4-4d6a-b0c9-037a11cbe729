------------------------------------------------------------------------
-- dqMinus.decTest -- decQuad 0-x                                     --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- All operands and results are decQuads.
extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- Sanity check
dqmns001 minus       +7.50  -> -7.50

-- Infinities
dqmns011 minus  Infinity    -> -Infinity
dqmns012 minus  -Infinity   -> Infinity

-- NaNs, 0 payload
dqmns021 minus         NaN  -> NaN
dqmns022 minus        -NaN  -> -NaN
dqmns023 minus        sNaN  -> NaN  Invalid_operation
dqmns024 minus       -sNaN  -> -NaN Invalid_operation

-- NaNs, non-0 payload
dqmns031 minus       NaN13  -> NaN13
dqmns032 minus      -NaN13  -> -NaN13
dqmns033 minus      sNaN13  -> NaN13   Invalid_operation
dqmns034 minus     -sNaN13  -> -NaN13  Invalid_operation
dqmns035 minus       NaN70  -> NaN70
dqmns036 minus      -NaN70  -> -NaN70
dqmns037 minus      sNaN101 -> NaN101  Invalid_operation
dqmns038 minus     -sNaN101 -> -NaN101 Invalid_operation

-- finites
dqmns101 minus          7   -> -7
dqmns102 minus         -7   -> 7
dqmns103 minus         75   -> -75
dqmns104 minus        -75   -> 75
dqmns105 minus       7.50   -> -7.50
dqmns106 minus      -7.50   -> 7.50
dqmns107 minus       7.500  -> -7.500
dqmns108 minus      -7.500  -> 7.500

-- zeros
dqmns111 minus          0   -> 0
dqmns112 minus         -0   -> 0
dqmns113 minus       0E+4   -> 0E+4
dqmns114 minus      -0E+4   -> 0E+4
dqmns115 minus     0.0000   -> 0.0000
dqmns116 minus    -0.0000   -> 0.0000
dqmns117 minus      0E-141  -> 0E-141
dqmns118 minus     -0E-141  -> 0E-141

-- full coefficients, alternating bits
dqmns121 minus   2682682682682682682682682682682682    -> -2682682682682682682682682682682682
dqmns122 minus  -2682682682682682682682682682682682    ->  2682682682682682682682682682682682
dqmns123 minus   1341341341341341341341341341341341    -> -1341341341341341341341341341341341
dqmns124 minus  -1341341341341341341341341341341341    ->  1341341341341341341341341341341341

-- Nmax, Nmin, Ntiny
dqmns131 minus  9.999999999999999999999999999999999E+6144   -> -9.999999999999999999999999999999999E+6144
dqmns132 minus  1E-6143                                     -> -1E-6143
dqmns133 minus  1.000000000000000000000000000000000E-6143   -> -1.000000000000000000000000000000000E-6143
dqmns134 minus  1E-6176                                     -> -1E-6176 Subnormal

dqmns135 minus  -1E-6176                                    ->  1E-6176 Subnormal
dqmns136 minus  -1.000000000000000000000000000000000E-6143  ->  1.000000000000000000000000000000000E-6143
dqmns137 minus  -1E-6143                                    ->  1E-6143
dqmns138 minus  -9.999999999999999999999999999999999E+6144  ->  9.999999999999999999999999999999999E+6144
