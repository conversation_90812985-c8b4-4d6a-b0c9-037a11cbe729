<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CTX_set_srp_password</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_CTX_set_srp_username, SSL_CTX_set_srp_password, SSL_CTX_set_srp_strength, SSL_CTX_set_srp_cb_arg, SSL_CTX_set_srp_username_callback, SSL_CTX_set_srp_client_pwd_callback, SSL_CTX_set_srp_verify_param_callback, SSL_set_srp_server_param, SSL_set_srp_server_param_pw, SSL_get_srp_g, SSL_get_srp_N, SSL_get_srp_username, SSL_get_srp_userinfo - SRP control operations</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;</code></pre>

<p>The following functions have been deprecated since OpenSSL 3.0, and can be hidden entirely by defining <b>OPENSSL_API_COMPAT</b> with a suitable version value, see <a href="../man7/openssl_user_macros.html">openssl_user_macros(7)</a>:</p>

<pre><code>int SSL_CTX_set_srp_username(SSL_CTX *ctx, char *name);
int SSL_CTX_set_srp_password(SSL_CTX *ctx, char *password);
int SSL_CTX_set_srp_strength(SSL_CTX *ctx, int strength);
int SSL_CTX_set_srp_cb_arg(SSL_CTX *ctx, void *arg);
int SSL_CTX_set_srp_username_callback(SSL_CTX *ctx,
                                      int (*cb) (SSL *s, int *ad, void *arg));
int SSL_CTX_set_srp_client_pwd_callback(SSL_CTX *ctx,
                                        char *(*cb) (SSL *s, void *arg));
int SSL_CTX_set_srp_verify_param_callback(SSL_CTX *ctx,
                                          int (*cb) (SSL *s, void *arg));

int SSL_set_srp_server_param(SSL *s, const BIGNUM *N, const BIGNUM *g,
                             BIGNUM *sa, BIGNUM *v, char *info);
int SSL_set_srp_server_param_pw(SSL *s, const char *user, const char *pass,
                                const char *grp);

BIGNUM *SSL_get_srp_g(SSL *s);
BIGNUM *SSL_get_srp_N(SSL *s);

char *SSL_get_srp_username(SSL *s);
char *SSL_get_srp_userinfo(SSL *s);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>All of the functions described on this page are deprecated. There are no available replacement functions at this time.</p>

<p>These functions provide access to SRP (Secure Remote Password) parameters, an alternate authentication mechanism for TLS. SRP allows the use of usernames and passwords over unencrypted channels without revealing the password to an eavesdropper. SRP also supplies a shared secret at the end of the authentication sequence that can be used to generate encryption keys.</p>

<p>The SRP protocol, version 3 is specified in RFC 2945. SRP version 6 is described in RFC 5054 with applications to TLS authentication.</p>

<p>The SSL_CTX_set_srp_username() function sets the SRP username for <b>ctx</b>. This should be called on the client prior to creating a connection to the server. The length of <b>name</b> must be shorter or equal to 255 characters.</p>

<p>The SSL_CTX_set_srp_password() function sets the SRP password for <b>ctx</b>. This may be called on the client prior to creating a connection to the server. This overrides the effect of SSL_CTX_set_srp_client_pwd_callback().</p>

<p>The SSL_CTX_set_srp_strength() function sets the SRP strength for <b>ctx</b>. This is the minimal length of the SRP prime in bits. If not specified 1024 is used. If not satisfied by the server key exchange the connection will be rejected.</p>

<p>The SSL_CTX_set_srp_cb_arg() function sets an extra parameter that will be passed to all following callbacks as <b>arg</b>.</p>

<p>The SSL_CTX_set_srp_username_callback() function sets the server side callback that is invoked when an SRP username is found in a ClientHello. The callback parameters are the SSL connection <b>s</b>, a writable error flag <b>ad</b> and the extra argument <b>arg</b> set by SSL_CTX_set_srp_cb_arg(). This callback should setup the server for the key exchange by calling SSL_set_srp_server_param() with the appropriate parameters for the received username. The username can be obtained by calling SSL_get_srp_username(). See <a href="../man3/SRP_VBASE_init.html">SRP_VBASE_init(3)</a> to parse the verifier file created by <a href="../man1/openssl-srp.html">openssl-srp(1)</a> or <a href="../man3/SRP_create_verifier.html">SRP_create_verifier(3)</a> to generate it. The callback should return <b>SSL_ERROR_NONE</b> to proceed with the server key exchange, <b>SSL3_AL_FATAL</b> for a fatal error or any value &lt; 0 for a retryable error. In the event of a <b>SSL3_AL_FATAL</b> the alert flag given by <b>*al</b> will be sent back. By default this will be <b>SSL_AD_UNKNOWN_PSK_IDENTITY</b>.</p>

<p>The SSL_CTX_set_srp_client_pwd_callback() function sets the client password callback on the client. The callback parameters are the SSL connection <b>s</b> and the extra argument <b>arg</b> set by SSL_CTX_set_srp_cb_arg(). The callback will be called as part of the generation of the client secrets. It should return the client password in text form or NULL to abort the connection. The resulting memory will be freed by the library as part of the callback resolution. This overrides the effect of SSL_CTX_set_srp_password().</p>

<p>The SSL_CTX_set_srp_verify_param_callback() sets the SRP gN parameter verification callback on the client. This allows the client to perform custom verification when receiving the server SRP proposed parameters. The callback parameters are the SSL connection <b>s</b> and the extra argument <b>arg</b> set by SSL_CTX_set_srp_cb_arg(). The callback should return a positive value to accept the server parameters. Returning 0 or a negative value will abort the connection. The server parameters can be obtained by calling SSL_get_srp_N() and SSL_get_srp_g(). Sanity checks are already performed by the library after the handshake (B % N non zero, check against the strength parameter) and are not necessary. If no callback is set the g and N parameters will be checked against known RFC 5054 values.</p>

<p>The SSL_set_srp_server_param() function sets all SRP parameters for the connection <b>s</b>. <b>N</b> and <b>g</b> are the SRP group parameters, <b>sa</b> is the user salt, <b>v</b> the password verifier and <b>info</b> is the optional user info.</p>

<p>The SSL_set_srp_server_param_pw() function sets all SRP parameters for the connection <b>s</b> by generating a random salt and a password verifier. <b>user</b> is the username, <b>pass</b> the password and <b>grp</b> the SRP group parameters identifier for <a href="../man3/SRP_get_default_gN.html">SRP_get_default_gN(3)</a>.</p>

<p>The SSL_get_srp_g() function returns the SRP group generator for <b>s</b>, or from the underlying SSL_CTX if it is NULL.</p>

<p>The SSL_get_srp_N() function returns the SRP prime for <b>s</b>, or from the underlying SSL_CTX if it is NULL.</p>

<p>The SSL_get_srp_username() function returns the SRP username for <b>s</b>, or from the underlying SSL_CTX if it is NULL.</p>

<p>The SSL_get_srp_userinfo() function returns the SRP user info for <b>s</b>, or from the underlying SSL_CTX if it is NULL.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>All SSL_CTX_set_* functions return 1 on success and 0 on failure.</p>

<p>SSL_set_srp_server_param() returns 1 on success and -1 on failure.</p>

<p>The SSL_get_SRP_* functions return a pointer to the requested data, the memory is owned by the library and should not be freed by the caller.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Setup SRP parameters on the client:</p>

<pre><code>#include &lt;openssl/ssl.h&gt;

const char *username = &quot;username&quot;;
const char *password = &quot;password&quot;;

SSL_CTX *ctx = SSL_CTX_new(TLS_client_method());
if (!ctx)
    /* Error */
if (!SSL_CTX_set_srp_username(ctx, username))
    /* Error */
if (!SSL_CTX_set_srp_password(ctx, password))
    /* Error */</code></pre>

<p>Setup SRP server with verifier file:</p>

<pre><code>#include &lt;openssl/srp.h&gt;
#include &lt;openssl/ssl.h&gt;

const char *srpvfile = &quot;password.srpv&quot;;

int srpServerCallback(SSL *s, int *ad, void *arg)
{
    SRP_VBASE *srpData = (SRP_VBASE*) arg;
    char *username = SSL_get_srp_username(s);

    SRP_user_pwd *user_pwd = SRP_VBASE_get1_by_user(srpData, username);
    if (!user_pwd)
        /* Error */
        return SSL3_AL_FATAL;

    if (SSL_set_srp_server_param(s, user_pwd-&gt;N, user_pwd-&gt;g,
        user_pwd-&gt;s, user_pwd-&gt;v, user_pwd-&gt;info) &lt; 0)
        /* Error */

    SRP_user_pwd_free(user_pwd);
    return SSL_ERROR_NONE;
}

SSL_CTX *ctx = SSL_CTX_new(TLS_server_method());
if (!ctx)
    /* Error */

/*
 * seedKey should contain a NUL terminated sequence
 * of random non NUL bytes
 */
const char *seedKey;

SRP_VBASE *srpData = SRP_VBASE_new(seedKey);
if (SRP_VBASE_init(srpData, (char*) srpvfile) != SRP_NO_ERROR)
   /* Error */

SSL_CTX_set_srp_cb_arg(ctx, srpData);
SSL_CTX_set_srp_username_callback(ctx, srpServerCallback);</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man1/openssl-srp.html">openssl-srp(1)</a>, <a href="../man3/SRP_VBASE_new.html">SRP_VBASE_new(3)</a>, <a href="../man3/SRP_create_verifier.html">SRP_create_verifier(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 1.0.1 and deprecated in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2018-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


