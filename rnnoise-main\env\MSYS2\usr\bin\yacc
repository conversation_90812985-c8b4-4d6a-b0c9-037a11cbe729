#! /bin/sh

# The functions in this file provide support for relocatability of
# shell scripts.  They should be included near the beginning of each
# shell script in a relocatable program, by adding @relocatable_sh@
# and causing the script to be expanded with AC_CONFIG_FILES.  A
# small amount of additional code must be added and adapted to the
# package by hand; see doc/relocatable-maint.texi (in Gnulib) for
# details.
#
# Copyright (C) 2003-2021 Free Software Foundation, Inc.
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

# Support for relocatability.
func_find_curr_installdir ()
{
  # Determine curr_installdir, even taking into account symlinks.
  curr_executable="$0"
  case "$curr_executable" in
    */* | *\\*) ;;
    *) # Need to look in the PATH.
      if test "${PATH_SEPARATOR+set}" != set; then
        # Determine PATH_SEPARATOR by trying to find /bin/sh in a PATH which
        # contains only /bin. Note that ksh looks also at the FPATH variable,
        # so we have to set that as well for the test.
        PATH_SEPARATOR=:
        (PATH='/bin;/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 \
          && { (PATH='/bin:/bin'; FPATH=$PATH; sh -c :) >/dev/null 2>&1 \
                 || PATH_SEPARATOR=';'
             }
      fi
      save_IFS="$IFS"; IFS="$PATH_SEPARATOR"
      for dir in $PATH; do
        IFS="$save_IFS"
        test -z "$dir" && dir=.
        for exec_ext in ''; do
          if test -f "$dir/$curr_executable$exec_ext"; then
            curr_executable="$dir/$curr_executable$exec_ext"
            break 2
          fi
        done
      done
      IFS="$save_IFS"
      ;;
  esac
  # Make absolute.
  case "$curr_executable" in
    /* | ?:/* | ?:\\*) ;;
    *) curr_executable=`pwd`/"$curr_executable" ;;
  esac
  # Resolve symlinks.
  sed_dirname='s,/[^/]*$,,'
  sed_linkdest='s,^.* -> \(.*\),\1,p'
  while : ; do
    lsline=`LC_ALL=C ls -l "$curr_executable"`
    case "$lsline" in
      *" -> "*)
        linkdest=`echo "$lsline" | sed -n -e "$sed_linkdest"`
        case "$linkdest" in
          /* | ?:/* | ?:\\*) curr_executable="$linkdest" ;;
          *) curr_executable=`echo "$curr_executable" | sed -e "$sed_dirname"`/"$linkdest" ;;
        esac ;;
      *) break ;;
    esac
  done
  curr_installdir=`echo "$curr_executable" | sed -e 's,/[^/]*$,,'`
  # Canonicalize.
  curr_installdir=`cd "$curr_installdir" && pwd`
}
func_find_prefixes ()
{
  # Compute the original/current installation prefixes by stripping the
  # trailing directories off the original/current installation directories.
  orig_installprefix="$orig_installdir"
  curr_installprefix="$curr_installdir"
  while true; do
    orig_last=`echo "$orig_installprefix" | sed -n -e 's,^.*/\([^/]*\)$,\1,p'`
    curr_last=`echo "$curr_installprefix" | sed -n -e 's,^.*/\([^/]*\)$,\1,p'`
    if test -z "$orig_last" || test -z "$curr_last"; then
      break
    fi
    if test "$orig_last" != "$curr_last"; then
      break
    fi
    orig_installprefix=`echo "$orig_installprefix" | sed -e 's,/[^/]*$,,'`
    curr_installprefix=`echo "$curr_installprefix" | sed -e 's,/[^/]*$,,'`
  done
}
if test "no" = yes; then
  prefix="/usr"
  exec_prefix="${prefix}"
  bindir="${exec_prefix}/bin"
  orig_installdir="$bindir" # see Makefile.am's *_SCRIPTS variables
  func_find_curr_installdir # determine curr_installdir
  func_find_prefixes
  relocate () {
    echo "$1/" \
    | sed -e "s%^${orig_installprefix}/%${curr_installprefix}/%" \
    | sed -e 's,/$,,'
  }
else
  relocate () {
    echo "$1"
  }
fi

prefix=/usr
exec_prefix=${prefix}
bindir=`relocate "${exec_prefix}/bin"`
exec "$bindir/bison" -y "$@"
