.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs7_print_signature_info" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs7_print_signature_info \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs7.h>
.sp
.BI "int gnutls_pkcs7_print_signature_info(gnutls_pkcs7_signature_info_st * " info ", gnutls_certificate_print_formats_t " format ", gnutls_datum_t * " out ");"
.SH ARGUMENTS
.IP "gnutls_pkcs7_signature_info_st * info" 12
The PKCS7 signature info struct to be printed
.IP "gnutls_certificate_print_formats_t format" 12
Indicate the format to use
.IP "gnutls_datum_t * out" 12
Newly allocated datum with null terminated string.
.SH "DESCRIPTION"
This function will pretty print a PKCS \fB7\fP signature info structure, suitable
for display to a human.

Currently the supported formats are \fBGNUTLS_CRT_PRINT_FULL\fP and
\fBGNUTLS_CRT_PRINT_COMPACT\fP.

The output  \fIout\fP needs to be deallocated using \fBgnutls_free()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.6.14
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
