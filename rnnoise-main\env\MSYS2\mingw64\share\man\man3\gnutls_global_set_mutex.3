.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_global_set_mutex" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_global_set_mutex \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_global_set_mutex(mutex_init_func " init ", mutex_deinit_func " deinit ", mutex_lock_func " lock ", mutex_unlock_func " unlock ");"
.SH ARGUMENTS
.IP "mutex_init_func init" 12
mutex initialization function
.IP "mutex_deinit_func deinit" 12
mutex deinitialization function
.IP "mutex_lock_func lock" 12
mutex locking function
.IP "mutex_unlock_func unlock" 12
mutex unlocking function
.SH "DESCRIPTION"
With this function you are allowed to override the default mutex
locks used in some parts of gnutls and dependent libraries. This function
should be used if you have complete control of your program and libraries.
Do not call this function from a library, or preferably from any application
unless really needed to. GnuTLS will use the appropriate locks for the running
system.

This function must be called prior to any other GnuTLS function; otherwise
the behavior is undefined.
.SH "DEPRECATED"
This function is discouraged on GnuTLS 3.7.3 or later.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
