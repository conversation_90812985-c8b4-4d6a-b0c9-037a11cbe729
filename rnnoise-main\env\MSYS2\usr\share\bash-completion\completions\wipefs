_wipefs_module()
{
	local cur prev OPTS
	COMPREPLY=()
	cur="${COMP_WORDS[COMP_CWORD]}"
	prev="${COMP_WORDS[COMP_CWORD-1]}"
	case $prev in
		'-b'|'--backup')
			COMPREPLY=( $(compgen -o dirnames -- ${cur:-"/"}) )
			return 0
			;;
		'-O'|'--output')
			local prefix realcur OUTPUT_ALL OUTPUT
			realcur="${cur##*,}"
			prefix="${cur%$realcur}"
			OUTPUT_ALL="UUID LABEL LENGTH TYPE OFFSET USAGE DEVICE"
			for WORD in $OUTPUT_ALL; do
				if ! [[ $prefix == *"$WORD"* ]]; then
					OUTPUT="$WORD ${OUTPUT:-""}"
				fi
			done
			compopt -o nospace
			COMPREPLY=( $(compgen -P "$prefix" -W "$OUTPUT" -S ',' -- $realcur) )
			return 0
			;;
		'-o'|'--offset')
			COMPREPLY=( $(compgen -W "offset" -- $cur) )
			return 0
			;;
		'-t'|'--types')
			local TYPES
			TYPES="$(blkid -k)"
			COMPREPLY=( $(compgen -W "$TYPES" -- $cur) )
			return 0
			;;
		'-h'|'--help'|'-V'|'--version')
			return 0
			;;
	esac
	case $cur in
		-*)
			OPTS="
				--all
				--backup
				--force
				--noheadings
				--json
				--lock
				--no-act
				--offset
				--output
				--parsable
				--quiet
				--types
				--help
				--version
			"
			COMPREPLY=( $(compgen -W "${OPTS[*]}" -- $cur) )
			return 0
			;;
	esac
	compopt -o bashdefault -o default
	COMPREPLY=( $(compgen -W "$(lsblk -pnro name)" -- $cur) )
	return 0
}
complete -F _wipefs_module wipefs
