.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ext_get_current_msg" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ext_get_current_msg \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned gnutls_ext_get_current_msg(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
a \fBgnutls_session_t\fP opaque pointer
.SH "DESCRIPTION"
This function allows an extension handler to obtain the message
this extension is being called from. The returned value is a single
entry of the \fBgnutls_ext_flags_t\fP enumeration. That is, if an
extension was registered with the \fBGNUTLS_EXT_FLAG_HRR\fP and
\fBGNUTLS_EXT_FLAG_EE\fP flags, the value when called during parsing of the
encrypted extensions message will be \fBGNUTLS_EXT_FLAG_EE\fP.

If not called under an extension handler, its value is undefined.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
