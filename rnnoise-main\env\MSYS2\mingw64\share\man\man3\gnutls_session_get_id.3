.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_get_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_get_id \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_session_get_id(gnutls_session_t " session ", void * " session_id ", size_t * " session_id_size ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "void * session_id" 12
is a pointer to space to hold the session id.
.IP "size_t * session_id_size" 12
initially should contain the maximum  \fIsession_id\fP size and will be updated.
.SH "DESCRIPTION"
Returns the TLS session identifier. The session ID is selected by the
server, and in older versions of TLS was a unique identifier shared
between client and server which was persistent across resumption.
In the latest version of TLS (1.3) or TLS with session tickets, the
notion of session identifiers is undefined and cannot be relied for uniquely
identifying sessions across client and server.

In client side this function returns the identifier returned by the
server, and cannot be assumed to have any relation to session resumption.
In server side this function is guaranteed to return a persistent
identifier of the session since GnuTLS 3.6.4, which may not necessarily
map into the TLS session ID value. Prior to that version the value
could only be considered a persistent identifier, under TLS1.2 or earlier
and when no session tickets were in use.

The session identifier value returned is always less than
\fBGNUTLS_MAX_SESSION_ID_SIZE\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
