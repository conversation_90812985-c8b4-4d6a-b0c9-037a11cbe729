.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_system_key_add_x509" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_system_key_add_x509 \- API function
.SH SYNOPSIS
.B #include <gnutls/system-keys.h>
.sp
.BI "int gnutls_system_key_add_x509(gnutls_x509_crt_t " crt ", gnutls_x509_privkey_t " privkey ", const char * " label ", char ** " cert_url ", char ** " key_url ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
the certificate to be added
.IP "gnutls_x509_privkey_t privkey" 12
the key to be added
.IP "const char * label" 12
the friendly name to describe the key
.IP "char ** cert_url" 12
if non\-NULL it will contain an allocated value with the certificate URL
.IP "char ** key_url" 12
if non\-NULL it will contain an allocated value with the key URL
.SH "DESCRIPTION"
This function will added the given key and certificate pair,
to the system list.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
