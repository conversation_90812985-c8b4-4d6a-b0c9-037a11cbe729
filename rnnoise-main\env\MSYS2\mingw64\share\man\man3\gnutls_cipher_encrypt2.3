.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_cipher_encrypt2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_cipher_encrypt2 \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_cipher_encrypt2(gnutls_cipher_hd_t " handle ", const void * " ptext ", size_t " ptext_len ", void * " ctext ", size_t " ctext_len ");"
.SH ARGUMENTS
.IP "gnutls_cipher_hd_t handle" 12
is a \fBgnutls_cipher_hd_t\fP type
.IP "const void * ptext" 12
the data to encrypt
.IP "size_t ptext_len" 12
the length of data to encrypt
.IP "void * ctext" 12
the encrypted data
.IP "size_t ctext_len" 12
the available length for encrypted data
.SH "DESCRIPTION"
This function will encrypt the given data using the algorithm
specified by the context. For block ciphers the  \fIptext_len\fP must be
a multiple of the block size. For the supported ciphers the encrypted
data length will equal the plaintext size.
.SH "RETURNS"
Zero or a negative error code on error.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
