/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifdef __cplusplus
extern "C" {
#endif

  DEFINE_GUID(GUID_QOS_REMAINING_BANDWIDTH,0xc4c51720,0x40ec,0x11d1,0x2c,0x91,0x00,0xaa,0x00,0x57,0x49,0x15);
  DEFINE_GUID(GUID_QOS_BESTEFFORT_BANDWIDTH,0xed885290,0x40ec,0x11d1,0x2c,0x91,0x00,0xaa,0x00,0x57,0x49,0x15);
  DEFINE_GUID(GUID_QOS_LATENCY,0xfc408ef0,0x40ec,0x11d1,0x2c,0x91,0x00,0xaa,0x00,0x57,0x49,0x15);
  DEFINE_GUID(GUID_QOS_FLOW_COUNT,0x1147f880,0x40ed,0x11d1,0x2c,0x91,0x00,0xaa,0x00,0x57,0x49,0x15);
  DEFINE_GUID(GUID_QOS_NON_BESTEFFORT_LIMIT,0x185c44e0,0x40ed,0x11d1,0x2c,0x91,0x00,0xaa,0x00,0x57,0x49,0x15);
  DEFINE_GUID(GUID_QOS_MAX_OUTSTANDING_SENDS,0x161ffa86,0x6120,0x11d1,0x2c,0x91,0x00,0xaa,0x00,0x57,0x49,0x15);
  DEFINE_GUID(GUID_QOS_STATISTICS_BUFFER,0xbb2c0980,0xe900,0x11d1,0xb0,0x7e,0x00,0x80,0xc7,0x13,0x82,0xbf);
  DEFINE_GUID(GUID_QOS_FLOW_MODE,0x5c82290a,0x515a,0x11d2,0x8e,0x58,0x00,0xc0,0x4f,0xc9,0xbf,0xcb);
  DEFINE_GUID(GUID_QOS_ISSLOW_FLOW,0xabf273a4,0xee07,0x11d2,0xbe,0x1b,0x00,0xa0,0xc9,0x9e,0xe6,0x3b);
  DEFINE_GUID(GUID_QOS_TIMER_RESOLUTION,0xba10cc88,0xf13e,0x11d2,0xbe,0x1b,0x00,0xa0,0xc9,0x9e,0xe6,0x3b);
  DEFINE_GUID(GUID_QOS_FLOW_IP_CONFORMING,0x07f99a8b,0xfcd2,0x11d2,0xbe,0x1e,0x00,0xa0,0xc9,0x9e,0xe6,0x3b);
  DEFINE_GUID(GUID_QOS_FLOW_IP_NONCONFORMING,0x087a5987,0xfcd2,0x11d2,0xbe,0x1e,0x00,0xa0,0xc9,0x9e,0xe6,0x3b);
  DEFINE_GUID(GUID_QOS_FLOW_8021P_CONFORMING,0x08c1e013,0xfcd2,0x11d2,0xbe,0x1e,0x00,0xa0,0xc9,0x9e,0xe6,0x3b);
  DEFINE_GUID(GUID_QOS_FLOW_8021P_NONCONFORMING,0x09023f91,0xfcd2,0x11d2,0xbe,0x1e,0x00,0xa0,0xc9,0x9e,0xe6,0x3b);
  DEFINE_GUID(GUID_QOS_ENABLE_AVG_STATS,0xbafb6d11,0x27c4,0x4801,0xa4,0x6f,0xef,0x80,0x80,0xc1,0x88,0xc8);
  DEFINE_GUID(GUID_QOS_ENABLE_WINDOW_ADJUSTMENT,0xaa966725,0xd3e9,0x4c55,0xb3,0x35,0x2a,0x0,0x27,0x9a,0x1e,0x64);

#ifdef __cplusplus
}
#endif
