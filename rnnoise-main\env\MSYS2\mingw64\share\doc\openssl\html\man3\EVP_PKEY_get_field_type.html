<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY_get_field_type</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY_get_field_type, EVP_PKEY_get_ec_point_conv_form - get field type or point conversion form of a key</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

int EVP_PKEY_get_field_type(const EVP_PKEY *pkey);
int EVP_PKEY_get_ec_point_conv_form(const EVP_PKEY *pkey);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>EVP_PKEY_get_field_type() returns the field type NID of the <i>pkey</i>, if <i>pkey</i>&#39;s key type supports it. The types currently supported by the built-in OpenSSL providers are either <b>NID_X9_62_prime_field</b> for prime curves or <b>NID_X9_62_characteristic_two_field</b> for binary curves; these values are defined in the <i>&lt;openssl/obj_mac.h&gt;</i> header file.</p>

<p>EVP_PKEY_get_ec_point_conv_form() returns the point conversion format of the <i>pkey</i>, if <i>pkey</i>&#39;s key type supports it.</p>

<h1 id="NOTES">NOTES</h1>

<p>Among the standard OpenSSL key types, this is only supported for EC and SM2 keys. Other providers may support this for additional key types.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_PKEY_get_field_type() returns the field type NID or 0 on error.</p>

<p>EVP_PKEY_get_ec_point_conv_form() returns the point conversion format number (see <a href="../man3/EC_GROUP_copy.html">EC_GROUP_copy(3)</a>) or 0 on error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EC_GROUP_copy.html">EC_GROUP_copy(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


