include(Compiler/NVIDIA)
__compiler_nvidia_cxx_standards(HIP)
__compiler_nvidia_cuda_flags(HIP)

# The ROCm hip-lang cmake package's device runtime library is not needed for NVIDIA GPUs.
set(_CMAKE_HIP_DEVICE_RUNTIME_TARGET "")

set(CMAKE_HIP_STANDARD_INCLUDE_DIRECTORIES "${CMAKE_HIP_COMPILER_ROCM_ROOT}/include")

set(CMAKE_HIP_LINK_EXECUTABLE
  "<CMAKE_HIP_HOST_LINK_LAUNCHER> <LINK_FLAGS> <OBJECTS> -o <TARGET> <LINK_LIBRARIES>${__IMPLICIT_LINKS}")
set(CMAKE_HIP_CREATE_SHARED_LIBRARY
  "<CMAKE_HIP_HOST_LINK_LAUNCHER> <CMAKE_SHARED_LIBRARY_HIP_FLAGS> <LINK_FLAGS> <CMAKE_SHARED_LIBRARY_CREATE_HIP_FLAGS> <SONAME_FLAG><TARGET_SONAME> -o <TARGET> <OBJECTS> <LINK_LIBRARIES>${__IMPLICIT_LINKS}")
set(CMAKE_HIP_CREATE_SHARED_MODULE "${CMAKE_HIP_CREATE_SHARED_LIBRARY}")
