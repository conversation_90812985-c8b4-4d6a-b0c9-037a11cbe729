<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Deprecated: p11-kit</title>
<meta name="generator" content="DocBook XSL Stylesheets Vsnapshot">
<link rel="home" href="index.html" title="p11-kit">
<link rel="up" href="reference.html" title="API Reference">
<link rel="prev" href="p11-kit-Future.html" title="Future">
<link rel="next" href="devel.html" title="Building, Packaging, and Contributing to p11-kit">
<meta name="generator" content="GTK-Doc V1.34.0 (XML mode)">
<link rel="stylesheet" href="style.css" type="text/css">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table class="navigation" id="top" width="100%" summary="Navigation header" cellpadding="2" cellspacing="5"><tr valign="middle">
<td width="100%" align="left" class="shortcuts">
<a href="#" class="shortcut">Top</a><span id="nav_description">  <span class="dim">|</span> 
                  <a href="#p11-kit-Deprecated.description" class="shortcut">Description</a></span>
</td>
<td><a accesskey="h" href="index.html"><img src="home.png" width="16" height="16" border="0" alt="Home"></a></td>
<td><a accesskey="u" href="reference.html"><img src="up.png" width="16" height="16" border="0" alt="Up"></a></td>
<td><a accesskey="p" href="p11-kit-Future.html"><img src="left.png" width="16" height="16" border="0" alt="Prev"></a></td>
<td><a accesskey="n" href="devel.html"><img src="right.png" width="16" height="16" border="0" alt="Next"></a></td>
</tr></table>
<div class="refentry">
<a name="p11-kit-Deprecated"></a><div class="titlepage"></div>
<div class="refnamediv"><table width="100%"><tr>
<td valign="top">
<h2><span class="refentrytitle"><a name="p11-kit-Deprecated.top_of_page"></a>Deprecated</span></h2>
<p>Deprecated — Deprecated functions</p>
</td>
<td class="gallery_image" valign="top" align="right"></td>
</tr></table></div>
<div class="refsect1">
<a name="p11-kit-Deprecated.functions"></a><h2>Functions</h2>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="functions_proto_type">
<col class="functions_proto_name">
</colgroup>
<tbody>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Deprecated.html#p11-kit-initialize-registered" title="p11_kit_initialize_registered ()">p11_kit_initialize_registered</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Deprecated.html#p11-kit-finalize-registered" title="p11_kit_finalize_registered ()">p11_kit_finalize_registered</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_FUNCTION_LIST_PTR</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Deprecated.html#p11-kit-registered-modules" title="p11_kit_registered_modules ()">p11_kit_registered_modules</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Deprecated.html#p11-kit-registered-module-to-name" title="p11_kit_registered_module_to_name ()">p11_kit_registered_module_to_name</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_FUNCTION_LIST_PTR</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Deprecated.html#p11-kit-registered-name-to-module" title="p11_kit_registered_name_to_module ()">p11_kit_registered_name_to_module</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">char</span> *
</td>
<td class="function_name">
<a class="link" href="p11-kit-Deprecated.html#p11-kit-registered-option" title="p11_kit_registered_option ()">p11_kit_registered_option</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Deprecated.html#p11-kit-initialize-module" title="p11_kit_initialize_module ()">p11_kit_initialize_module</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Deprecated.html#p11-kit-load-initialize-module" title="p11_kit_load_initialize_module ()">p11_kit_load_initialize_module</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="function_type">
<span class="returnvalue">CK_RV</span>
</td>
<td class="function_name">
<a class="link" href="p11-kit-Deprecated.html#p11-kit-finalize-module" title="p11_kit_finalize_module ()">p11_kit_finalize_module</a> <span class="c_punctuation">()</span>
</td>
</tr>
<tr>
<td class="define_keyword">#define</td>
<td class="function_name">
<a class="link" href="p11-kit-Deprecated.html#P11-KIT-DEPRECATED-FOR:CAPS" title="P11_KIT_DEPRECATED_FOR()">P11_KIT_DEPRECATED_FOR</a><span class="c_punctuation">()</span>
</td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect1">
<a name="p11-kit-Deprecated.description"></a><h2>Description</h2>
<p>These functions have been deprecated from p11-kit and are not recommended for
general usage. In large part they were deprecated because they did not adequately
insulate multiple callers of a PKCS#11 module from another, and could not
support the 'managed' mode needed to do this.</p>
</div>
<div class="refsect1">
<a name="p11-kit-Deprecated.functions_details"></a><h2>Functions</h2>
<div class="refsect2">
<a name="p11-kit-initialize-registered"></a><h3>p11_kit_initialize_registered ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
p11_kit_initialize_registered (<em class="parameter"><code><span class="type">void</span></code></em>);</pre>
<div class="warning">
<p><code class="literal">p11_kit_initialize_registered</code> is deprecated and should not be used in newly-written code.</p>
<p>Since: 0.19.0: Use <a class="link" href="p11-kit-Modules.html#p11-kit-modules-load" title="p11_kit_modules_load ()"><code class="function">p11_kit_modules_load()</code></a> instead.</p>
</div>
<p>Initialize all the registered PKCS#11 modules.</p>
<p>If this is the first time this function is called multiple times
consecutively within a single process, then it merely increments an
initialization reference count for each of these modules.</p>
<p>Use <a class="link" href="p11-kit-Deprecated.html#p11-kit-finalize-registered" title="p11_kit_finalize_registered ()"><code class="function">p11_kit_finalize_registered()</code></a> to finalize these registered modules once
the caller is done with them.</p>
<p>If this function fails, then an error message will be available via the
<a class="link" href="p11-kit-Utilities.html#p11-kit-message" title="p11_kit_message ()"><code class="function">p11_kit_message()</code></a> function.</p>
<div class="refsect3">
<a name="p11-kit-initialize-registered.returns"></a><h4>Returns</h4>
<p> CKR_OK if the initialization succeeded, or an error code.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-finalize-registered"></a><h3>p11_kit_finalize_registered ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
p11_kit_finalize_registered (<em class="parameter"><code><span class="type">void</span></code></em>);</pre>
<div class="warning">
<p><code class="literal">p11_kit_finalize_registered</code> is deprecated and should not be used in newly-written code.</p>
<p>Since 0.19.0: Use <a class="link" href="p11-kit-Modules.html#p11-kit-modules-release" title="p11_kit_modules_release ()"><code class="function">p11_kit_modules_release()</code></a> instead.</p>
</div>
<p>Finalize all the registered PKCS#11 modules. These should have been
initialized with <a class="link" href="p11-kit-Deprecated.html#p11-kit-initialize-registered" title="p11_kit_initialize_registered ()"><code class="function">p11_kit_initialize_registered()</code></a>.</p>
<p>If <a class="link" href="p11-kit-Deprecated.html#p11-kit-initialize-registered" title="p11_kit_initialize_registered ()"><code class="function">p11_kit_initialize_registered()</code></a> has been called more than once in this
process, then this function must be called the same number of times before
actual finalization will occur.</p>
<p>If this function fails, then an error message will be available via the
<a class="link" href="p11-kit-Utilities.html#p11-kit-message" title="p11_kit_message ()"><code class="function">p11_kit_message()</code></a> function.</p>
<div class="refsect3">
<a name="p11-kit-finalize-registered.returns"></a><h4>Returns</h4>
<p> CKR_OK if the finalization succeeded, or an error code.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-registered-modules"></a><h3>p11_kit_registered_modules ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_FUNCTION_LIST_PTR</span> *
p11_kit_registered_modules (<em class="parameter"><code><span class="type">void</span></code></em>);</pre>
<div class="warning">
<p><code class="literal">p11_kit_registered_modules</code> is deprecated and should not be used in newly-written code.</p>
<p>Since 0.19.0: Use <a class="link" href="p11-kit-Modules.html#p11-kit-modules-load" title="p11_kit_modules_load ()"><code class="function">p11_kit_modules_load()</code></a> instead.</p>
</div>
<p>Get a list of all the registered PKCS#11 modules. This list will be valid
once the <a class="link" href="p11-kit-Deprecated.html#p11-kit-initialize-registered" title="p11_kit_initialize_registered ()"><code class="function">p11_kit_initialize_registered()</code></a> function has been called.</p>
<p>The returned value is a <code class="code">NULL</code> terminated array of</p>
<code class="code">CK_FUNCTION_LIST_PTR</code> pointers.
<p>The returned modules are unmanaged.</p>
<div class="refsect3">
<a name="p11-kit-registered-modules.returns"></a><h4>Returns</h4>
<p> A list of all the registered modules. Use the <code class="function">free()</code> function to
free the list.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-registered-module-to-name"></a><h3>p11_kit_registered_module_to_name ()</h3>
<pre class="programlisting"><span class="returnvalue">char</span> *
p11_kit_registered_module_to_name (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST_PTR</span> module</code></em>);</pre>
<div class="warning">
<p><code class="literal">p11_kit_registered_module_to_name</code> is deprecated and should not be used in newly-written code.</p>
<p>Since 0.19.0: Use <a class="link" href="p11-kit-Modules.html#p11-kit-module-get-name" title="p11_kit_module_get_name ()"><code class="function">p11_kit_module_get_name()</code></a> instead.</p>
</div>
<p>Get the name of a registered PKCS#11 module.</p>
<p>You can use <a class="link" href="p11-kit-Deprecated.html#p11-kit-registered-modules" title="p11_kit_registered_modules ()"><code class="function">p11_kit_registered_modules()</code></a> to get a list of all the registered
modules. This name is specified by the registered module configuration.</p>
<div class="refsect3">
<a name="p11-kit-registered-module-to-name.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>pointer to a registered module</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-registered-module-to-name.returns"></a><h4>Returns</h4>
<p> A newly allocated string containing the module name, or</p>
<code class="code">NULL</code> if no such registered module exists. Use <code class="function">free()</code> to
<p>    free this string.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-registered-name-to-module"></a><h3>p11_kit_registered_name_to_module ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_FUNCTION_LIST_PTR</span>
p11_kit_registered_name_to_module (<em class="parameter"><code>const <span class="type">char</span> *name</code></em>);</pre>
<div class="warning">
<p><code class="literal">p11_kit_registered_name_to_module</code> is deprecated and should not be used in newly-written code.</p>
<p>Since 0.19.0: Use <a class="link" href="p11-kit-Modules.html#p11-kit-module-for-name" title="p11_kit_module_for_name ()"><code class="function">p11_kit_module_for_name()</code></a> instead.</p>
</div>
<p>Lookup a registered PKCS#11 module by its name. This name is specified by
the registered module configuration.</p>
<div class="refsect3">
<a name="p11-kit-registered-name-to-module.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>name</p></td>
<td class="parameter_description"><p>name of a registered module</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-registered-name-to-module.returns"></a><h4>Returns</h4>
<p> a pointer to a PKCS#11 module, or <code class="code">NULL</code> if this name was
not found.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-registered-option"></a><h3>p11_kit_registered_option ()</h3>
<pre class="programlisting"><span class="returnvalue">char</span> *
p11_kit_registered_option (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST_PTR</span> module</code></em>,
                           <em class="parameter"><code>const <span class="type">char</span> *field</code></em>);</pre>
<div class="warning">
<p><code class="literal">p11_kit_registered_option</code> is deprecated and should not be used in newly-written code.</p>
<p>Since 0.19.0: Use <a class="link" href="p11-kit-Modules.html#p11-kit-config-option" title="p11_kit_config_option ()"><code class="function">p11_kit_config_option()</code></a> instead.</p>
</div>
<p>Lookup a configured option for a registered PKCS#11 module. If a</p>
<code class="code">NULL</code> module argument is specified, then this will lookup
<p>the configuration option in the global config file.</p>
<div class="refsect3">
<a name="p11-kit-registered-option.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>a pointer to a registered module</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>field</p></td>
<td class="parameter_description"><p>the name of the option to lookup.</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-registered-option.returns"></a><h4>Returns</h4>
<p> A newly allocated string containing the option value, or</p>
<code class="code">NULL</code> if the registered module or the option were not found.
<p>    Use <code class="function">free()</code> to free the returned string.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-initialize-module"></a><h3>p11_kit_initialize_module ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
p11_kit_initialize_module (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST_PTR</span> module</code></em>);</pre>
<div class="warning">
<p><code class="literal">p11_kit_initialize_module</code> is deprecated and should not be used in newly-written code.</p>
<p>Since 0.19.0: Use <a class="link" href="p11-kit-Modules.html#p11-kit-module-initialize" title="p11_kit_module_initialize ()"><code class="function">p11_kit_module_initialize()</code></a> instead.</p>
</div>
<p>Initialize an arbitrary PKCS#11 module. Normally using the
<a class="link" href="p11-kit-Deprecated.html#p11-kit-initialize-registered" title="p11_kit_initialize_registered ()"><code class="function">p11_kit_initialize_registered()</code></a> is preferred.</p>
<p>Using this function to initialize modules allows coordination between
multiple users of the same module in a single process. It should be called
on modules that have been loaded (with <code class="function">dlopen()</code> for example) but not yet
initialized. The caller should not yet have called the module's</p>
<code class="code">C_Initialize</code> method. This function will call
<code class="code">C_Initialize</code> as necessary.
<p>Subsequent calls to this function for the same module will result in an
initialization count being incremented for the module. It is safe (although
usually unnecessary) to use this function on registered modules.</p>
<p>The module must be finalized with <a class="link" href="p11-kit-Deprecated.html#p11-kit-finalize-module" title="p11_kit_finalize_module ()"><code class="function">p11_kit_finalize_module()</code></a> instead of
calling its <code class="code">C_Finalize</code> method directly.</p>
<p>This function does not accept a <code class="code">CK_C_INITIALIZE_ARGS</code> argument.
Custom initialization arguments cannot be supported when multiple consumers
load the same module.</p>
<p>If this function fails, then an error message will be available via the
<a class="link" href="p11-kit-Utilities.html#p11-kit-message" title="p11_kit_message ()"><code class="function">p11_kit_message()</code></a> function.</p>
<div class="refsect3">
<a name="p11-kit-initialize-module.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>loaded module to initialize.</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-initialize-module.returns"></a><h4>Returns</h4>
<p> CKR_OK if the initialization was successful.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-load-initialize-module"></a><h3>p11_kit_load_initialize_module ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
p11_kit_load_initialize_module (<em class="parameter"><code>const <span class="type">char</span> *module_path</code></em>,
                                <em class="parameter"><code><span class="type">CK_FUNCTION_LIST_PTR</span> *module</code></em>);</pre>
<div class="warning">
<p><code class="literal">p11_kit_load_initialize_module</code> is deprecated and should not be used in newly-written code.</p>
<p>Since 0.19.0: Use <a class="link" href="p11-kit-Modules.html#p11-kit-module-load" title="p11_kit_module_load ()"><code class="function">p11_kit_module_load()</code></a> instead.</p>
</div>
<p>Load an arbitrary PKCS#11 module from a dynamic library file, and
initialize it. Normally using the <a class="link" href="p11-kit-Deprecated.html#p11-kit-initialize-registered" title="p11_kit_initialize_registered ()"><code class="function">p11_kit_initialize_registered()</code></a> function
is preferred.</p>
<p>Using this function to load and initialize modules allows coordination between
multiple users of the same module in a single process. The caller should not
call the module's <code class="code">C_Initialize</code> method. This function will call</p>
<code class="code">C_Initialize</code> as necessary.
<p>If a module has already been loaded, then use of this function is unnecesasry.
Instead use the <a class="link" href="p11-kit-Deprecated.html#p11-kit-initialize-module" title="p11_kit_initialize_module ()"><code class="function">p11_kit_initialize_module()</code></a> function to initialize it.</p>
<p>Subsequent calls to this function for the same module will result in an
initialization count being incremented for the module. It is safe (although
usually unnecessary) to use this function on registered modules.</p>
<p>The module must be finalized with <a class="link" href="p11-kit-Deprecated.html#p11-kit-finalize-module" title="p11_kit_finalize_module ()"><code class="function">p11_kit_finalize_module()</code></a> instead of
calling its <code class="code">C_Finalize</code> method directly.</p>
<p>This function does not accept a <code class="code">CK_C_INITIALIZE_ARGS</code> argument.
Custom initialization arguments cannot be supported when multiple consumers
load the same module.</p>
<p>If this function fails, then an error message will be available via the
<a class="link" href="p11-kit-Utilities.html#p11-kit-message" title="p11_kit_message ()"><code class="function">p11_kit_message()</code></a> function.</p>
<div class="refsect3">
<a name="p11-kit-load-initialize-module.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody>
<tr>
<td class="parameter_name"><p>module_path</p></td>
<td class="parameter_description"><p>full file path of module library</p></td>
<td class="parameter_annotations"> </td>
</tr>
<tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>location to place loaded module pointer</p></td>
<td class="parameter_annotations"> </td>
</tr>
</tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-load-initialize-module.returns"></a><h4>Returns</h4>
<p> CKR_OK if the initialization was successful.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="p11-kit-finalize-module"></a><h3>p11_kit_finalize_module ()</h3>
<pre class="programlisting"><span class="returnvalue">CK_RV</span>
p11_kit_finalize_module (<em class="parameter"><code><span class="type">CK_FUNCTION_LIST_PTR</span> module</code></em>);</pre>
<div class="warning">
<p><code class="literal">p11_kit_finalize_module</code> is deprecated and should not be used in newly-written code.</p>
<p>Since 0.19.0: Use <a class="link" href="p11-kit-Modules.html#p11-kit-module-finalize" title="p11_kit_module_finalize ()"><code class="function">p11_kit_module_finalize()</code></a> and
	<a class="link" href="p11-kit-Modules.html#p11-kit-module-release" title="p11_kit_module_release ()"><code class="function">p11_kit_module_release()</code></a> instead.</p>
</div>
<p>Finalize an arbitrary PKCS#11 module. The module must have been initialized
using <a class="link" href="p11-kit-Deprecated.html#p11-kit-initialize-module" title="p11_kit_initialize_module ()"><code class="function">p11_kit_initialize_module()</code></a>. In most cases callers will want to use
<a class="link" href="p11-kit-Deprecated.html#p11-kit-finalize-registered" title="p11_kit_finalize_registered ()"><code class="function">p11_kit_finalize_registered()</code></a> instead of this function.</p>
<p>Using this function to finalize modules allows coordination between
multiple users of the same module in a single process. The caller should not
call the module's <code class="code">C_Finalize</code> method. This function will call</p>
<code class="code">C_Finalize</code> as necessary.
<p>If the module was initialized more than once, then this function will
decrement an initialization count for the module. When the count reaches zero
the module will be truly finalized. It is safe (although usually unnecessary)
to use this function on registered modules if (and only if) they were
initialized using <a class="link" href="p11-kit-Deprecated.html#p11-kit-initialize-module" title="p11_kit_initialize_module ()"><code class="function">p11_kit_initialize_module()</code></a> for some reason.</p>
<p>If this function fails, then an error message will be available via the
<a class="link" href="p11-kit-Utilities.html#p11-kit-message" title="p11_kit_message ()"><code class="function">p11_kit_message()</code></a> function.</p>
<div class="refsect3">
<a name="p11-kit-finalize-module.parameters"></a><h4>Parameters</h4>
<div class="informaltable"><table class="informaltable" width="100%" border="0">
<colgroup>
<col width="150px" class="parameters_name">
<col class="parameters_description">
<col width="200px" class="parameters_annotations">
</colgroup>
<tbody><tr>
<td class="parameter_name"><p>module</p></td>
<td class="parameter_description"><p>loaded module to finalize.</p></td>
<td class="parameter_annotations"> </td>
</tr></tbody>
</table></div>
</div>
<div class="refsect3">
<a name="p11-kit-finalize-module.returns"></a><h4>Returns</h4>
<p> CKR_OK if the finalization was successful.</p>
</div>
</div>
<hr>
<div class="refsect2">
<a name="P11-KIT-DEPRECATED-FOR:CAPS"></a><h3>P11_KIT_DEPRECATED_FOR()</h3>
<pre class="programlisting">#define P11_KIT_DEPRECATED_FOR(f) __attribute__((deprecated("Use " #f " instead")))
</pre>
</div>
</div>
</div>
<div class="footer">
<hr>Generated by GTK-Doc V1.34.0</div>
</body>
</html>