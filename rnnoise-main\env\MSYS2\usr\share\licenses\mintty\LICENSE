mintty is copyright 2008-23 <PERSON>, 2015-23 <PERSON>.

Licensed under the terms of the GNU General Public License version 3 or later,
amended with the bundling clause to clarify ambiguous interpretation.

The bundling clause can be found in the accompanying file LICENSE.bundling.

The GPL license text can be found in the accompanying file LICENSE.GPL, 
at /usr/share/doc/common-licenses/GPL-3.0 on Cygwin installs, 
or on the GNU website at http://www.gnu.org/licenses/gpl.html.

Sources are available from the project page at http://mintty.github.io/.

Based on PuTTY version 0.60 by <PERSON> and contributors.
Big thanks to everyone involved for their work on PuTTY.
See LICENSE.PuTTY for PuTTY's copyright notice, contributors, and license.
The sources of PuTTY 0.60 can be downloaded from
ftp://ftp.chiark.greenend.org.uk/users/sgtatham/putty-0.60.

The minibidi algorithm is under MIT license as quoted in the source file.

Sixel code (sixel.c) is relicensed under GPL like mintty with the 
permission of its author (kmiya@culti); Sixel colour conversion code 
(sixel_hls.c) is licensed by its author <PERSON> under the license 
quoted in the source file.

The program icon is the apps/utilities-terminal icon from KDE's Oxygen theme,
retrieved from http://websvn.kde.org/trunk/KDE/kdebase/runtime/pics/oxygen. 
Thanks to the KDE artists for their sleek design. The Oxygen icons are licensed
under the terms of the LGPLv3; see LICENSE.Oxygen for details.

The colour schemes / theme files bundled with mintty are included 
under various licenses. The source and license or permission are 
quoted in the respective theme files.

Bell sounds files are included, mostly under the creative commons license 
(https://creativecommons.org/publicdomain/zero/1.0/), see also the README 
in the sounds subdirectory.

