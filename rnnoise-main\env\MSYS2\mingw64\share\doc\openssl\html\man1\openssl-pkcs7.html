<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-pkcs7</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-pkcs7 - PKCS#7 command</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>pkcs7</b> [<b>-help</b>] [<b>-inform</b> <b>DER</b>|<b>PEM</b>] [<b>-outform</b> <b>DER</b>|<b>PEM</b>] [<b>-in</b> <i>filename</i>] [<b>-out</b> <i>filename</i>] [<b>-print</b>] [<b>-print_certs</b>] [<b>-quiet</b>] [<b>-text</b>] [<b>-noout</b>] [<b>-engine</b> <i>id</i>] [<b>-provider</b> <i>name</i>] [<b>-provider-path</b> <i>path</i>] [<b>-provparam</b> <i>[name:]key=value</i>] [<b>-propquery</b> <i>propq</i>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This command processes PKCS#7 files. Note that it only understands PKCS#7 v 1.5 as specified in IETF RFC 2315. It cannot currently parse CMS as described in IETF RFC 2630.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="inform-DER-PEM--outform-DER-PEM"><b>-inform</b> <b>DER</b>|<b>PEM</b>, <b>-outform</b> <b>DER</b>|<b>PEM</b></dt>
<dd>

<p>The input and formats; the default is <b>PEM</b>. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

<p>The data is a PKCS#7 Version 1.5 structure.</p>

</dd>
<dt id="in-filename"><b>-in</b> <i>filename</i></dt>
<dd>

<p>This specifies the input filename to read from or standard input if this option is not specified.</p>

</dd>
<dt id="out-filename"><b>-out</b> <i>filename</i></dt>
<dd>

<p>Specifies the output filename to write to or standard output by default.</p>

</dd>
<dt id="print"><b>-print</b></dt>
<dd>

<p>Print out the full PKCS7 object.</p>

</dd>
<dt id="print_certs"><b>-print_certs</b></dt>
<dd>

<p>Prints out any certificates or CRLs contained in the file. They are preceded by their subject and issuer names in one line format.</p>

</dd>
<dt id="quiet"><b>-quiet</b></dt>
<dd>

<p>When used with -print_certs, prints out just the PEM-encoded certificates without any other output.</p>

</dd>
<dt id="text"><b>-text</b></dt>
<dd>

<p>Prints out certificate details in full rather than just subject and issuer names.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>Don&#39;t output the encoded version of the PKCS#7 structure (or certificates if <b>-print_certs</b> is set).</p>

</dd>
<dt id="engine-id"><b>-engine</b> <i>id</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Engine Options&quot; in openssl(1)</a>. This option is deprecated.</p>

</dd>
<dt id="provider-name"><b>-provider</b> <i>name</i></dt>
<dd>

</dd>
<dt id="provider-path-path"><b>-provider-path</b> <i>path</i></dt>
<dd>

</dd>
<dt id="provparam-name:-key-value"><b>-provparam</b> <i>[name:]key=value</i></dt>
<dd>

</dd>
<dt id="propquery-propq"><b>-propquery</b> <i>propq</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Provider Options&quot; in openssl(1)</a>, <a href="../man7/provider.html">provider(7)</a>, and <a href="../man7/property.html">property(7)</a>.</p>

</dd>
</dl>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Convert a PKCS#7 file from PEM to DER:</p>

<pre><code>openssl pkcs7 -in file.pem -outform DER -out file.der</code></pre>

<p>Output all certificates in a file:</p>

<pre><code>openssl pkcs7 -in file.pem -print_certs -out certs.pem</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/openssl.html">openssl(1)</a>, <a href="../man1/openssl-crl2pkcs7.html">openssl-crl2pkcs7(1)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The <b>-engine</b> option was deprecated in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


