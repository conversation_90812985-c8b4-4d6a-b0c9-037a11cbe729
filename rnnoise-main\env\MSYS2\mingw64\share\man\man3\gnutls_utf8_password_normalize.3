.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_utf8_password_normalize" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_utf8_password_normalize \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_utf8_password_normalize(const unsigned char * " password ", unsigned " plen ", gnutls_datum_t * " out ", unsigned " flags ");"
.SH ARGUMENTS
.IP "const unsigned char * password" 12
contain the UTF\-8 formatted password
.IP "unsigned plen" 12
the length of the provided password
.IP "gnutls_datum_t * out" 12
the result in an null\-terminated allocated string
.IP "unsigned flags" 12
should be zero
.SH "DESCRIPTION"
This function will convert the provided UTF\-8 password according
to the normalization rules in RFC7613.

If the flag \fBGNUTLS_UTF8_IGNORE_ERRS\fP is specified, any UTF\-8 encoding
errors will be ignored, and in that case the output will be a copy of the input.
.SH "RETURNS"
\fBGNUTLS_E_INVALID_UTF8_STRING\fP on invalid UTF\-8 data, or 0 on success.
.SH "SINCE"
3.5.7
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
