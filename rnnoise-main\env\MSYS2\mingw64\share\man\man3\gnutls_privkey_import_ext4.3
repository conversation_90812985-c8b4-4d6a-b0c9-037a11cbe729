.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_import_ext4" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_import_ext4 \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_import_ext4(gnutls_privkey_t " pkey ", void * " userdata ", gnutls_privkey_sign_data_func " sign_data_fn ", gnutls_privkey_sign_hash_func " sign_hash_fn ", gnutls_privkey_decrypt_func " decrypt_fn ", gnutls_privkey_deinit_func " deinit_fn ", gnutls_privkey_info_func " info_fn ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t pkey" 12
The private key
.IP "void * userdata" 12
private data to be provided to the callbacks
.IP "gnutls_privkey_sign_data_func sign_data_fn" 12
callback for signature operations (may be \fBNULL\fP)
.IP "gnutls_privkey_sign_hash_func sign_hash_fn" 12
callback for signature operations (may be \fBNULL\fP)
.IP "gnutls_privkey_decrypt_func decrypt_fn" 12
callback for decryption operations (may be \fBNULL\fP)
.IP "gnutls_privkey_deinit_func deinit_fn" 12
a deinitialization function
.IP "gnutls_privkey_info_func info_fn" 12
returns info about the public key algorithm (should not be \fBNULL\fP)
.IP "unsigned int flags" 12
Flags for the import
.SH "DESCRIPTION"
This function will associate the given callbacks with the
\fBgnutls_privkey_t\fP type. At least one of the callbacks
must be non\-null. If a deinitialization function is provided
then flags is assumed to contain \fBGNUTLS_PRIVKEY_IMPORT_AUTO_RELEASE\fP.

Note that in contrast with the signing function of
\fBgnutls_privkey_import_ext3()\fP, the signing functions provided to this
function take explicitly the signature algorithm as parameter and
different functions are provided to sign the data and hashes.

The  \fIsign_hash_fn\fP is to be called to sign pre\-hashed data. The input
to the callback is the output of the hash (such as SHA256) corresponding
to the signature algorithm. For RSA PKCS\fB1\fP signatures, the signature
algorithm can be set to \fBGNUTLS_SIGN_RSA_RAW\fP, and in that case the data
should be handled as if they were an RSA PKCS\fB1\fP DigestInfo structure.

The  \fIsign_data_fn\fP is to be called to sign data. The input data will be
he data to be signed (and hashed), with the provided signature
algorithm. This function is to be used for signature algorithms like
Ed25519 which cannot take pre\-hashed data as input.

When both  \fIsign_data_fn\fP and  \fIsign_hash_fn\fP functions are provided they
must be able to operate on all the supported signature algorithms,
unless prohibited by the type of the algorithm (e.g., as with Ed25519).

The  \fIinfo_fn\fP must provide information on the signature algorithms supported by
this private key, and should support the flags \fBGNUTLS_PRIVKEY_INFO_PK_ALGO\fP,
\fBGNUTLS_PRIVKEY_INFO_HAVE_SIGN_ALGO\fP and \fBGNUTLS_PRIVKEY_INFO_PK_ALGO_BITS\fP.
It must return \-1 on unknown flags.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.6.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
