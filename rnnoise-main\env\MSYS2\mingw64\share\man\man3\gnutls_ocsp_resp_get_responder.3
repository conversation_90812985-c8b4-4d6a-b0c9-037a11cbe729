.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_resp_get_responder" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_resp_get_responder \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_resp_get_responder(gnutls_ocsp_resp_const_t " resp ", gnutls_datum_t * " dn ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_resp_const_t resp" 12
should contain a \fBgnutls_ocsp_resp_t\fP type
.IP "gnutls_datum_t * dn" 12
newly allocated buffer with name
.SH "DESCRIPTION"
This function will extract the name of the Basic OCSP Response in
the provided buffer. The name will be in the form
"C=xxxx,O=yyyy,CN=zzzz" as described in RFC2253. The output string
will be ASCII or UTF\-8 encoded, depending on the certificate data.

If the responder ID is not a name but a hash, this function
will return zero and the  \fIdn\fP elements will be set to \fBNULL\fP.

The caller needs to deallocate memory by calling \fBgnutls_free()\fP on
 \fIdn\fP \->data.

This function does not output a fully RFC4514 compliant string, if
that is required see \fBgnutls_ocsp_resp_get_responder2()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error code is returned. When no data exist it will
return success and set  \fIdn\fP elements to zero.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
