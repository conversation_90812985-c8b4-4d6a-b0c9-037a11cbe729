.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs7_set_crt_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs7_set_crt_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs7.h>
.sp
.BI "int gnutls_pkcs7_set_crt_raw(gnutls_pkcs7_t " pkcs7 ", const gnutls_datum_t * " crt ");"
.SH ARGUMENTS
.IP "gnutls_pkcs7_t pkcs7" 12
The pkcs7 type
.IP "const gnutls_datum_t * crt" 12
the DER encoded certificate to be added
.SH "DESCRIPTION"
This function will add a certificate to the PKCS7 or RFC2630
certificate set.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
