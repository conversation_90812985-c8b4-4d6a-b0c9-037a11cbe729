.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_pk_algorithm" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_pk_algorithm \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_pk_algorithm(gnutls_x509_crq_t " crq ", unsigned int * " bits ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "unsigned int * bits" 12
if bits is non\-\fBNULL\fP it will hold the size of the parameters' in bits
.SH "DESCRIPTION"
This function will return the public key algorithm of a PKCS\fB10\fP
certificate request.

If bits is non\-\fBNULL\fP, it should have enough size to hold the
parameters size in bits.  For RSA the bits returned is the modulus.
For DSA the bits returned are of the public exponent.
.SH "RETURNS"
a member of the \fBgnutls_pk_algorithm_t\fP enumeration on
success, or a negative error code on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
