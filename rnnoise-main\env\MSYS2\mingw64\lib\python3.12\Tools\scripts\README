This directory contains a collection of executable Python scripts that are
useful while building, extending or managing Python.

2to3                      Main script for running the 2to3 conversion tool
checkpip.py               Checks the version of the projects bundled in ensurepip
                          are the latest available
combinerefs.py            A helper for analyzing PYTHONDUMPREFS output
divmod_threshold.py       Determine threshold for switching from longobject.c
                          divmod to _pylong.int_divmod()
idle3                     Main program to start IDLE
pydoc3                    Python documentation browser
run_tests.py              Run the test suite with more sensible default options
summarize_stats.py        Summarize specialization stats for all files in the
                          default stats folders
var_access_benchmark.py   Show relative speeds of local, nonlocal, global,
                          and built-in access
