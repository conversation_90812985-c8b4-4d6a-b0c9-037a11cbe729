set ::msgcat::header "Project-Id-Version: fr\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2010-02-02 12:59+0100\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: French\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nX-Generator: KBabel 1.11.4\nPlural-Forms:  nplurals=2; plural=(n > 1);\n"
::msgcat::mcset fr "git-gui: fatal error" "git-gui: erreur fatale"
::msgcat::mcset fr "Invalid font specified in %s:" "Police invalide sp\u00e9cifi\u00e9e dans %s :"
::msgcat::mcset fr "Main Font" "Police principale"
::msgcat::mcset fr "Diff/Console Font" "Police diff/console"
::msgcat::mcset fr "Cannot find git in PATH." "Impossible de trouver git dans PATH."
::msgcat::mcset fr "Cannot parse Git version string:" "Impossible de parser la version de Git :"
::msgcat::mcset fr "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "Impossible de d\u00e9terminer la version de Git.\n\n%s affirme qu'il s'agit de la version '%s'.\n\n%s n\u00e9cessite au moins Git 1.5.0.\n\nPeut-on consid\u00e9rer que '%s' est en version 1.5.0 ?\n"
::msgcat::mcset fr "Git directory not found:" "Impossible de trouver le r\u00e9pertoire git :"
::msgcat::mcset fr "Cannot move to top of working directory:" "Impossible d'aller \u00e0 la racine du r\u00e9pertoire de travail :"
::msgcat::mcset fr "Cannot use bare repository:" "Impossible d'utiliser un d\u00e9p\u00f4t nu (bare) :"
::msgcat::mcset fr "No working directory" "Aucun r\u00e9pertoire de travail"
::msgcat::mcset fr "Refreshing file status..." "Rafra\u00eechissement du statut des fichiers..."
::msgcat::mcset fr "Scanning for modified files ..." "Recherche de fichiers modifi\u00e9s..."
::msgcat::mcset fr "Calling prepare-commit-msg hook..." "Lancement de l'action de pr\u00e9paration du message de commit..."
::msgcat::mcset fr "Commit declined by prepare-commit-msg hook." "Commit refus\u00e9 par l'action de pr\u00e9paration du message de commit."
::msgcat::mcset fr "Ready." "Pr\u00eat."
::msgcat::mcset fr "Displaying only %s of %s files." "Affiche seulement %s fichiers sur %s."
::msgcat::mcset fr "Unmodified" "Non modifi\u00e9"
::msgcat::mcset fr "Modified, not staged" "Modifi\u00e9, pas index\u00e9"
::msgcat::mcset fr "Staged for commit" "Index\u00e9"
::msgcat::mcset fr "Portions staged for commit" "Portions index\u00e9es"
::msgcat::mcset fr "Staged for commit, missing" "Index\u00e9s, manquant"
::msgcat::mcset fr "File type changed, not staged" "Le type de fichier a chang\u00e9, non index\u00e9"
::msgcat::mcset fr "File type changed, staged" "Le type de fichier a chang\u00e9, index\u00e9"
::msgcat::mcset fr "Untracked, not staged" "Non versionn\u00e9, non index\u00e9"
::msgcat::mcset fr "Missing" "Manquant"
::msgcat::mcset fr "Staged for removal" "Index\u00e9 pour suppression"
::msgcat::mcset fr "Staged for removal, still present" "Index\u00e9 pour suppression, toujours pr\u00e9sent"
::msgcat::mcset fr "Requires merge resolution" "N\u00e9cessite la r\u00e9solution d'une fusion"
::msgcat::mcset fr "Starting gitk... please wait..." "Lancement de gitk... un instant..."
::msgcat::mcset fr "Couldn't find gitk in PATH" "Impossible de trouver gitk dans PATH."
::msgcat::mcset fr "Couldn't find git gui in PATH" "Impossible de trouver git gui dans PATH"
::msgcat::mcset fr "Repository" "D\u00e9p\u00f4t"
::msgcat::mcset fr "Edit" "\u00c9dition"
::msgcat::mcset fr "Branch" "Branche"
::msgcat::mcset fr "Commit@@noun" "Commit"
::msgcat::mcset fr "Merge" "Fusionner"
::msgcat::mcset fr "Remote" "D\u00e9p\u00f4t distant"
::msgcat::mcset fr "Tools" "Outils"
::msgcat::mcset fr "Explore Working Copy" "Explorer la copie de travail"
::msgcat::mcset fr "Browse Current Branch's Files" "Naviguer dans la branche courante"
::msgcat::mcset fr "Browse Branch Files..." "Naviguer dans la branche..."
::msgcat::mcset fr "Visualize Current Branch's History" "Visualiser l'historique de la branche courante"
::msgcat::mcset fr "Visualize All Branch History" "Voir l'historique de toutes les branches"
::msgcat::mcset fr "Browse %s's Files" "Parcourir l'arborescence de %s"
::msgcat::mcset fr "Visualize %s's History" "Voir l'historique de la branche : %s"
::msgcat::mcset fr "Database Statistics" "Statistiques du d\u00e9p\u00f4t"
::msgcat::mcset fr "Compress Database" "Comprimer le d\u00e9p\u00f4t"
::msgcat::mcset fr "Verify Database" "V\u00e9rifier le d\u00e9p\u00f4t"
::msgcat::mcset fr "Create Desktop Icon" "Cr\u00e9er une ic\u00f4ne sur le bureau"
::msgcat::mcset fr "Quit" "Quitter"
::msgcat::mcset fr "Undo" "D\u00e9faire"
::msgcat::mcset fr "Redo" "Refaire"
::msgcat::mcset fr "Cut" "Couper"
::msgcat::mcset fr "Copy" "Copier"
::msgcat::mcset fr "Paste" "Coller"
::msgcat::mcset fr "Delete" "Supprimer"
::msgcat::mcset fr "Select All" "Tout s\u00e9lectionner"
::msgcat::mcset fr "Create..." "Cr\u00e9er..."
::msgcat::mcset fr "Checkout..." "Charger (checkout)..."
::msgcat::mcset fr "Rename..." "Renommer..."
::msgcat::mcset fr "Delete..." "Supprimer..."
::msgcat::mcset fr "Reset..." "R\u00e9initialiser..."
::msgcat::mcset fr "Done" "Effectu\u00e9"
::msgcat::mcset fr "Commit@@verb" "Commiter@@verb"
::msgcat::mcset fr "New Commit" "Nouveau commit"
::msgcat::mcset fr "Amend Last Commit" "Corriger dernier commit"
::msgcat::mcset fr "Rescan" "Recharger modifs."
::msgcat::mcset fr "Stage To Commit" "Indexer"
::msgcat::mcset fr "Stage Changed Files To Commit" "Indexer toutes modifications"
::msgcat::mcset fr "Unstage From Commit" "D\u00e9sindexer"
::msgcat::mcset fr "Revert Changes" "Annuler les modifications"
::msgcat::mcset fr "Show Less Context" "Montrer moins de contexte"
::msgcat::mcset fr "Show More Context" "Montrer plus de contexte"
::msgcat::mcset fr "Sign Off" "Signer"
::msgcat::mcset fr "Local Merge..." "Fusion locale..."
::msgcat::mcset fr "Abort Merge..." "Abandonner fusion..."
::msgcat::mcset fr "Add..." "Ajouter..."
::msgcat::mcset fr "Push..." "Pousser..."
::msgcat::mcset fr "Delete Branch..." "Supprimer branche..."
::msgcat::mcset fr "Options..." "Options..."
::msgcat::mcset fr "Remove..." "Supprimer..."
::msgcat::mcset fr "Help" "Aide"
::msgcat::mcset fr "About %s" "\u00c0 propos de %s"
::msgcat::mcset fr "Online Documentation" "Documentation en ligne"
::msgcat::mcset fr "Show SSH Key" "Montrer la cl\u00e9 SSH"
::msgcat::mcset fr "fatal: cannot stat path %s: No such file or directory" "erreur fatale : pas d'infos sur le chemin %s : Fichier ou r\u00e9pertoire inexistant"
::msgcat::mcset fr "Current Branch:" "Branche courante :"
::msgcat::mcset fr "Staged Changes (Will Commit)" "Modifs. index\u00e9es (pour commit)"
::msgcat::mcset fr "Unstaged Changes" "Modifs. non index\u00e9es"
::msgcat::mcset fr "Stage Changed" "Indexer modifs."
::msgcat::mcset fr "Push" "Pousser"
::msgcat::mcset fr "Initial Commit Message:" "Message de commit initial :"
::msgcat::mcset fr "Amended Commit Message:" "Message de commit corrig\u00e9 :"
::msgcat::mcset fr "Amended Initial Commit Message:" "Message de commit initial corrig\u00e9 :"
::msgcat::mcset fr "Amended Merge Commit Message:" "Message de commit de fusion corrig\u00e9 :"
::msgcat::mcset fr "Merge Commit Message:" "Message de commit de fusion :"
::msgcat::mcset fr "Commit Message:" "Message de commit :"
::msgcat::mcset fr "Copy All" "Copier tout"
::msgcat::mcset fr "File:" "Fichier :"
::msgcat::mcset fr "Refresh" "Rafra\u00eechir"
::msgcat::mcset fr "Decrease Font Size" "Diminuer la police"
::msgcat::mcset fr "Increase Font Size" "Agrandir la police"
::msgcat::mcset fr "Encoding" "Codage des caract\u00e8res"
::msgcat::mcset fr "Apply/Reverse Hunk" "Appliquer/Inverser section"
::msgcat::mcset fr "Apply/Reverse Line" "Appliquer/Inverser la ligne"
::msgcat::mcset fr "Run Merge Tool" "Lancer l'outil de fusion"
::msgcat::mcset fr "Use Remote Version" "Utiliser la version distante"
::msgcat::mcset fr "Use Local Version" "Utiliser la version locale"
::msgcat::mcset fr "Revert To Base" "Revenir \u00e0 la version de base"
::msgcat::mcset fr "Visualize These Changes In The Submodule" "Voir les changments dans le sous-module"
::msgcat::mcset fr "Visualize Current Branch History In The Submodule" "Voir l'historique de la branche courante du sous-module"
::msgcat::mcset fr "Visualize All Branch History In The Submodule" "Voir l'historique de toutes les branches du sous-module"
::msgcat::mcset fr "Start git gui In The Submodule" "D\u00e9marrer git gui dans le sous-module"
::msgcat::mcset fr "Unstage Hunk From Commit" "D\u00e9sindexer la section"
::msgcat::mcset fr "Unstage Lines From Commit" "D\u00e9sindexer la ligne du commit"
::msgcat::mcset fr "Unstage Line From Commit" "D\u00e9sindexer la ligne"
::msgcat::mcset fr "Stage Hunk For Commit" "Indexer la section"
::msgcat::mcset fr "Stage Lines For Commit" "Indexer les lignes"
::msgcat::mcset fr "Stage Line For Commit" "Indexer la ligne"
::msgcat::mcset fr "Initializing..." "Initialisation..."
::msgcat::mcset fr "Possible environment issues exist.\n\nThe following environment variables are probably\ngoing to be ignored by any Git subprocess run\nby %s:\n\n" "Des probl\u00e8mes d'environnement sont possibles.\n\nLes variables d'environnement suivantes seront\nprobablement ignor\u00e9es par tous les\nsous-processus de Git lanc\u00e9s par %s\n\n"
::msgcat::mcset fr "\nThis is due to a known issue with the\nTcl binary distributed by Cygwin." "\nCeci est d\u00fb \u00e0 un probl\u00e8me connu avec\nle binaire Tcl distribu\u00e9 par Cygwin."
::msgcat::mcset fr "\n\nA good replacement for %s\nis placing values for the user.name and\nuser.email settings into your personal\n~/.gitconfig file.\n" "\n\nUn bon remplacement pour %s\nest de mettre les valeurs pour 'user.name' (nom\nde l'utilisateur) et 'user.email' (addresse email\nde l'utilisateur) dans votre fichier '~/.gitconfig'.\n"
::msgcat::mcset fr "git-gui - a graphical user interface for Git." "git-gui - une interface graphique utilisateur pour Git"
::msgcat::mcset fr "File Viewer" "Visionneur de fichier"
::msgcat::mcset fr "Commit:" "Commit :"
::msgcat::mcset fr "Copy Commit" "Copier commit"
::msgcat::mcset fr "Find Text..." "Chercher texte..."
::msgcat::mcset fr "Do Full Copy Detection" "Lancer la d\u00e9tection approfondie des copies"
::msgcat::mcset fr "Show History Context" "Montrer l'historique"
::msgcat::mcset fr "Blame Parent Commit" "Bl\u00e2mer le commit parent"
::msgcat::mcset fr "Reading %s..." "Lecture de %s..."
::msgcat::mcset fr "Loading copy/move tracking annotations..." "Chargement des annotations de suivi des copies/d\u00e9placements..."
::msgcat::mcset fr "lines annotated" "lignes annot\u00e9es"
::msgcat::mcset fr "Loading original location annotations..." "Chargement des annotations d'emplacement original"
::msgcat::mcset fr "Annotation complete." "Annotation termin\u00e9e."
::msgcat::mcset fr "Busy" "Occup\u00e9"
::msgcat::mcset fr "Annotation process is already running." "Annotation en cours d'ex\u00e9cution."
::msgcat::mcset fr "Running thorough copy detection..." "Recherche de copie approfondie en cours..."
::msgcat::mcset fr "Loading annotation..." "Chargement des annotations..."
::msgcat::mcset fr "Author:" "Auteur :"
::msgcat::mcset fr "Committer:" "Commiteur :"
::msgcat::mcset fr "Original File:" "Fichier original :"
::msgcat::mcset fr "Cannot find HEAD commit:" "Impossible de trouver le commit HEAD :"
::msgcat::mcset fr "Cannot find parent commit:" "Impossible de trouver le commit parent :"
::msgcat::mcset fr "Unable to display parent" "Impossible d'afficher le parent"
::msgcat::mcset fr "Error loading diff:" "Erreur lors du chargement des diff\u00e9rences :"
::msgcat::mcset fr "Originally By:" "\u00c0 l'origine par :"
::msgcat::mcset fr "In File:" "Dans le fichier :"
::msgcat::mcset fr "Copied Or Moved Here By:" "Copi\u00e9 ou d\u00e9plac\u00e9 ici par :"
::msgcat::mcset fr "Checkout Branch" "Charger la branche (checkout)"
::msgcat::mcset fr "Checkout" "Charger (checkout)"
::msgcat::mcset fr "Cancel" "Annuler"
::msgcat::mcset fr "Revision" "R\u00e9vision"
::msgcat::mcset fr "Options" "Options"
::msgcat::mcset fr "Fetch Tracking Branch" "R\u00e9cup\u00e9rer la branche de suivi"
::msgcat::mcset fr "Detach From Local Branch" "D\u00e9tacher de la branche locale"
::msgcat::mcset fr "Create Branch" "Cr\u00e9er une branche"
::msgcat::mcset fr "Create New Branch" "Cr\u00e9er une nouvelle branche"
::msgcat::mcset fr "Create" "Cr\u00e9er"
::msgcat::mcset fr "Branch Name" "Nom de branche"
::msgcat::mcset fr "Name:" "Nom :"
::msgcat::mcset fr "Match Tracking Branch Name" "Trouver nom de branche de suivi"
::msgcat::mcset fr "Starting Revision" "R\u00e9vision initiale"
::msgcat::mcset fr "Update Existing Branch:" "Mettre \u00e0 jour une branche existante :"
::msgcat::mcset fr "No" "Non"
::msgcat::mcset fr "Fast Forward Only" "Mise \u00e0 jour rectiligne seulement (fast-forward)"
::msgcat::mcset fr "Reset" "R\u00e9initialiser"
::msgcat::mcset fr "Checkout After Creation" "Charger (checkout) apr\u00e8s cr\u00e9ation"
::msgcat::mcset fr "Please select a tracking branch." "Choisissez une branche de suivi"
::msgcat::mcset fr "Tracking branch %s is not a branch in the remote repository." "La branche de suivi %s n'est pas une branche dans le d\u00e9p\u00f4t distant."
::msgcat::mcset fr "Please supply a branch name." "Fournissez un nom de branche."
::msgcat::mcset fr "'%s' is not an acceptable branch name." "'%s' n'est pas un nom de branche acceptable."
::msgcat::mcset fr "Delete Branch" "Supprimer branche"
::msgcat::mcset fr "Delete Local Branch" "Supprimer branche locale"
::msgcat::mcset fr "Local Branches" "Branches locales"
::msgcat::mcset fr "Delete Only If Merged Into" "Supprimer seulement si fusionn\u00e9e dans :"
::msgcat::mcset fr "Always (Do not perform merge checks)" "Toujours (ne pas v\u00e9rifier les fusions)"
::msgcat::mcset fr "The following branches are not completely merged into %s:" "Les branches suivantes ne sont pas compl\u00e8tement fusionn\u00e9es dans %s :"
::msgcat::mcset fr "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "Il est difficile de r\u00e9cup\u00e9rer des branches supprim\u00e9es.\n\nSupprimer les branches s\u00e9lectionn\u00e9es ?"
::msgcat::mcset fr "Failed to delete branches:\n%s" "La suppression des branches suivantes a \u00e9chou\u00e9 :\n%s"
::msgcat::mcset fr "Rename Branch" "Renommer branche"
::msgcat::mcset fr "Rename" "Renommer"
::msgcat::mcset fr "Branch:" "Branche :"
::msgcat::mcset fr "New Name:" "Nouveau nom :"
::msgcat::mcset fr "Please select a branch to rename." "Merci de s\u00e9lectionner une branche \u00e0 renommer."
::msgcat::mcset fr "Branch '%s' already exists." "La branche '%s' existe d\u00e9j\u00e0."
::msgcat::mcset fr "Failed to rename '%s'." "\u00c9chec pour renommer '%s'."
::msgcat::mcset fr "Starting..." "Lancement..."
::msgcat::mcset fr "File Browser" "Visionneur de fichier"
::msgcat::mcset fr "Loading %s..." "Chargement de %s..."
::msgcat::mcset fr "\[Up To Parent\]" "\[Jusqu'au parent\]"
::msgcat::mcset fr "Browse Branch Files" "Naviguer dans les fichiers de le branche"
::msgcat::mcset fr "Browse" "Naviguer"
::msgcat::mcset fr "Fetching %s from %s" "R\u00e9cup\u00e9ration de %s \u00e0 partir de %s"
::msgcat::mcset fr "fatal: Cannot resolve %s" "erreur fatale : Impossible de r\u00e9soudre %s"
::msgcat::mcset fr "Close" "Fermer"
::msgcat::mcset fr "Branch '%s' does not exist." "La branche '%s' n'existe pas."
::msgcat::mcset fr "Failed to configure simplified git-pull for '%s'." "\u00c9chec de la configuration simplifi\u00e9e de git-pull pour '%s'."
::msgcat::mcset fr "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "La branche '%s' existe d\u00e9j\u00e0.\n\nImpossible de faire une avance rapide (fast forward) vers %s.\nUne fusion est n\u00e9cessaire."
::msgcat::mcset fr "Merge strategy '%s' not supported." "La strat\u00e9gie de fusion '%s' n'est pas support\u00e9e."
::msgcat::mcset fr "Failed to update '%s'." "La mise \u00e0 jour de '%s' a \u00e9chou\u00e9."
::msgcat::mcset fr "Staging area (index) is already locked." "L'index (staging area) est d\u00e9j\u00e0 verrouill\u00e9."
::msgcat::mcset fr "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before the current branch can be changed.\n\nThe rescan will be automatically started now.\n" "L'\u00e9tat lors de la derni\u00e8re synchronisation ne correspond plus \u00e0 l'\u00e9tat du d\u00e9p\u00f4t.\n\nUn autre programme Git a modifi\u00e9 ce d\u00e9p\u00f4t depuis la derni\u00e8re synchronisation. Une resynchronisation doit \u00eatre effectu\u00e9e avant de pouvoir modifier la branche courante.\n\nCela va \u00eatre fait tout de suite automatiquement.\n"
::msgcat::mcset fr "Updating working directory to '%s'..." "Mise \u00e0 jour du r\u00e9pertoire courant avec '%s'..."
::msgcat::mcset fr "files checked out" "fichiers charg\u00e9s"
::msgcat::mcset fr "Aborted checkout of '%s' (file level merging is required)." "Chargement de '%s' abandonn\u00e9 (il est n\u00e9cessaire de fusionner des fichiers)."
::msgcat::mcset fr "File level merge required." "Il est n\u00e9cessaire de fusionner des fichiers."
::msgcat::mcset fr "Staying on branch '%s'." "Le r\u00e9pertoire de travail reste sur la branche '%s'."
::msgcat::mcset fr "You are no longer on a local branch.\n\nIf you wanted to be on a branch, create one now starting from 'This Detached Checkout'." "Vous n'\u00eates plus sur une branche locale.\n\nSi vous vouliez \u00eatre sur une branche, cr\u00e9ez-en une maintenant en partant de 'Cet emprunt d\u00e9tach\u00e9'."
::msgcat::mcset fr "Checked out '%s'." "'%s' charg\u00e9."
::msgcat::mcset fr "Resetting '%s' to '%s' will lose the following commits:" "R\u00e9initialiser '%s' \u00e0 '%s' va faire perdre les commits suivants :"
::msgcat::mcset fr "Recovering lost commits may not be easy." "R\u00e9cup\u00e9rer les commits perdus ne sera peut \u00eatre pas facile."
::msgcat::mcset fr "Reset '%s'?" "R\u00e9initialiser '%s' ?"
::msgcat::mcset fr "Visualize" "Visualiser"
::msgcat::mcset fr "Failed to set current branch.\n\nThis working directory is only partially switched.  We successfully updated your files, but failed to update an internal Git file.\n\nThis should not have occurred.  %s will now close and give up." "Le changement de la branche courante a \u00e9chou\u00e9.\n\nLe r\u00e9pertoire courant n'est que partiellement modifi\u00e9. Les fichiers ont \u00e9t\u00e9 mis \u00e0 jour avec succ\u00e8s, mais la mise \u00e0 jour d'un fichier interne \u00e0 Git a \u00e9chou\u00e9e.\n\nCela n'aurait pas d\u00fb se produire. %s va abandonner et se terminer."
::msgcat::mcset fr "Select" "S\u00e9lectionner"
::msgcat::mcset fr "Font Family" "Familles de polices"
::msgcat::mcset fr "Font Size" "Taille de police"
::msgcat::mcset fr "Font Example" "Exemple de police"
::msgcat::mcset fr "This is example text.\nIf you like this text, it can be your font." "Ceci est un texte d'exemple.\nSi vous aimez ce texte, vous pouvez choisir cette police."
::msgcat::mcset fr "Git Gui" "Git Gui"
::msgcat::mcset fr "Create New Repository" "Cr\u00e9er nouveau d\u00e9p\u00f4t"
::msgcat::mcset fr "New..." "Nouveau..."
::msgcat::mcset fr "Clone Existing Repository" "Cloner un d\u00e9p\u00f4t existant"
::msgcat::mcset fr "Clone..." "Cloner..."
::msgcat::mcset fr "Open Existing Repository" "Ouvrir un d\u00e9p\u00f4t existant"
::msgcat::mcset fr "Open..." "Ouvrir..."
::msgcat::mcset fr "Recent Repositories" "D\u00e9p\u00f4ts r\u00e9cemment utilis\u00e9s"
::msgcat::mcset fr "Open Recent Repository:" "Ouvrir un d\u00e9p\u00f4t r\u00e9cent :"
::msgcat::mcset fr "Failed to create repository %s:" "La cr\u00e9ation du d\u00e9p\u00f4t %s a \u00e9chou\u00e9 :"
::msgcat::mcset fr "Directory:" "R\u00e9pertoire :"
::msgcat::mcset fr "Git Repository" "D\u00e9p\u00f4t Git"
::msgcat::mcset fr "Directory %s already exists." "Le r\u00e9pertoire %s existe d\u00e9j\u00e0."
::msgcat::mcset fr "File %s already exists." "Le fichier %s existe d\u00e9j\u00e0."
::msgcat::mcset fr "Clone" "Cloner"
::msgcat::mcset fr "Source Location:" "Emplacement source :"
::msgcat::mcset fr "Target Directory:" "R\u00e9pertoire cible :"
::msgcat::mcset fr "Clone Type:" "Type de clonage :"
::msgcat::mcset fr "Standard (Fast, Semi-Redundant, Hardlinks)" "Standard (rapide, semi-redondant, liens durs)"
::msgcat::mcset fr "Full Copy (Slower, Redundant Backup)" "Copie compl\u00e8te (plus lent, sauvegarde redondante)"
::msgcat::mcset fr "Shared (Fastest, Not Recommended, No Backup)" "Partag\u00e9 (le plus rapide, non recommand\u00e9, pas de sauvegarde)"
::msgcat::mcset fr "Not a Git repository: %s" "'%s' n'est pas un d\u00e9p\u00f4t Git."
::msgcat::mcset fr "Standard only available for local repository." "Standard n'est disponible que pour un d\u00e9p\u00f4t local."
::msgcat::mcset fr "Shared only available for local repository." "Partag\u00e9 n'est disponible que pour un d\u00e9p\u00f4t local."
::msgcat::mcset fr "Location %s already exists." "L'emplacement %s existe d\u00e9j\u00e0."
::msgcat::mcset fr "Failed to configure origin" "La configuration de l'origine a \u00e9chou\u00e9."
::msgcat::mcset fr "Counting objects" "D\u00e9compte des objets"
::msgcat::mcset fr "buckets" "paniers"
::msgcat::mcset fr "Unable to copy objects/info/alternates: %s" "Impossible de copier 'objects/info/alternates' : %s"
::msgcat::mcset fr "Nothing to clone from %s." "Il n'y a rien \u00e0 cloner depuis %s."
::msgcat::mcset fr "The 'master' branch has not been initialized." "La branche 'master' n'a pas \u00e9t\u00e9 initialis\u00e9e."
::msgcat::mcset fr "Hardlinks are unavailable.  Falling back to copying." "Les liens durs ne sont pas support\u00e9s. Une copie sera effectu\u00e9e \u00e0 la place."
::msgcat::mcset fr "Cloning from %s" "Clonage depuis %s"
::msgcat::mcset fr "Copying objects" "Copie des objets"
::msgcat::mcset fr "KiB" "KiB"
::msgcat::mcset fr "Unable to copy object: %s" "Impossible de copier l'objet : %s"
::msgcat::mcset fr "Linking objects" "Liaison des objets"
::msgcat::mcset fr "objects" "objets"
::msgcat::mcset fr "Unable to hardlink object: %s" "Impossible cr\u00e9er un lien dur pour l'objet : %s"
::msgcat::mcset fr "Cannot fetch branches and objects.  See console output for details." "Impossible de r\u00e9cup\u00e9rer les branches et objets. Voir la sortie console pour plus de d\u00e9tails."
::msgcat::mcset fr "Cannot fetch tags.  See console output for details." "Impossible de r\u00e9cup\u00e9rer les marques (tags). Voir la sortie console pour plus de d\u00e9tails."
::msgcat::mcset fr "Cannot determine HEAD.  See console output for details." "Impossible de d\u00e9terminer HEAD. Voir la sortie console pour plus de d\u00e9tails."
::msgcat::mcset fr "Unable to cleanup %s" "Impossible de nettoyer %s"
::msgcat::mcset fr "Clone failed." "Le clonage a \u00e9chou\u00e9."
::msgcat::mcset fr "No default branch obtained." "Aucune branche par d\u00e9faut n'a \u00e9t\u00e9 obtenue."
::msgcat::mcset fr "Cannot resolve %s as a commit." "Impossible de r\u00e9soudre %s comme commit."
::msgcat::mcset fr "Creating working directory" "Cr\u00e9ation du r\u00e9pertoire de travail"
::msgcat::mcset fr "files" "fichiers"
::msgcat::mcset fr "Initial file checkout failed." "Le chargement initial du fichier a \u00e9chou\u00e9."
::msgcat::mcset fr "Open" "Ouvrir"
::msgcat::mcset fr "Repository:" "D\u00e9p\u00f4t :"
::msgcat::mcset fr "Failed to open repository %s:" "Impossible d'ouvrir le d\u00e9p\u00f4t %s :"
::msgcat::mcset fr "This Detached Checkout" "Cet emprunt d\u00e9tach\u00e9"
::msgcat::mcset fr "Revision Expression:" "Expression de r\u00e9vision :"
::msgcat::mcset fr "Local Branch" "Branche locale"
::msgcat::mcset fr "Tracking Branch" "Branche de suivi"
::msgcat::mcset fr "Tag" "Marque (tag)"
::msgcat::mcset fr "Invalid revision: %s" "R\u00e9vision invalide : %s"
::msgcat::mcset fr "No revision selected." "Pas de r\u00e9vision s\u00e9lectionn\u00e9e."
::msgcat::mcset fr "Revision expression is empty." "L'expression de r\u00e9vision est vide."
::msgcat::mcset fr "Updated" "Mise \u00e0 jour:"
::msgcat::mcset fr "URL" "URL"
::msgcat::mcset fr "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "Il n'y a rien \u00e0 corriger.\n\nVous allez cr\u00e9er le commit initial. Il n'y a pas de commit avant celui-ci \u00e0 corriger.\n"
::msgcat::mcset fr "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "Impossible de corriger pendant une fusion.\n\nVous \u00eates actuellement au milieu d'une fusion qui n'a pas \u00e9t\u00e9 compl\u00e8tement termin\u00e9e. Vous ne pouvez pas corriger le commit pr\u00e9c\u00e9dent sauf si vous abandonnez la fusion courante.\n"
::msgcat::mcset fr "Error loading commit data for amend:" "Erreur lors du chargement des donn\u00e9es de commit pour correction :"
::msgcat::mcset fr "Unable to obtain your identity:" "Impossible d'obtenir votre identit\u00e9 :"
::msgcat::mcset fr "Invalid GIT_COMMITTER_IDENT:" "GIT_COMMITTER_IDENT invalide :"
::msgcat::mcset fr "warning: Tcl does not support encoding '%s'." "attention : Tcl ne supporte pas le codage '%s'."
::msgcat::mcset fr "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before another commit can be created.\n\nThe rescan will be automatically started now.\n" "L'\u00e9tat lors de la derni\u00e8re synchronisation ne correspond plus \u00e0 l'\u00e9tat du d\u00e9p\u00f4t.\n\nUn autre programme Git a modifi\u00e9 ce d\u00e9p\u00f4t depuis la derni\u00e8re synchronisation. Une resynshronisation doit \u00eatre effectu\u00e9e avant de pouvoir cr\u00e9er un nouveau commit.\n\nCela va \u00eatre fait tout de suite automatiquement.\n"
::msgcat::mcset fr "Unmerged files cannot be committed.\n\nFile %s has merge conflicts.  You must resolve them and stage the file before committing.\n" "Des fichiers non fusionn\u00e9s ne peuvent \u00eatre commit\u00e9s.\n\nLe fichier %s a des conflicts de fusion. Vous devez les r\u00e9soudre et pr\u00e9-commiter le fichier avant de pouvoir commiter.\n"
::msgcat::mcset fr "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "Un \u00e9tat de fichier inconnu %s a \u00e9t\u00e9 d\u00e9tect\u00e9.\n\nLe fichier %s ne peut pas \u00eatre commit\u00e9 par ce programme.\n"
::msgcat::mcset fr "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "Pas de modification \u00e0 commiter.\n\nVous devez indexer au moins 1 fichier avant de pouvoir commiter.\n"
::msgcat::mcset fr "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "Merci de fournir un message de commit.\n\nUn bon message de commit a le format suivant :\n\n- Premi\u00e8re ligne : d\u00e9crire en une phrase ce que vous avez fait.\n- Deuxi\u00e8me ligne : rien.\n- Lignes suivantes : D\u00e9crire pourquoi ces modifications sont bonnes.\n"
::msgcat::mcset fr "Calling pre-commit hook..." "Lancement de l'action d'avant-commit..."
::msgcat::mcset fr "Commit declined by pre-commit hook." "Commit refus\u00e9 par l'action d'avant-commit."
::msgcat::mcset fr "Calling commit-msg hook..." "Lancement de l'action \"message de commit\"..."
::msgcat::mcset fr "Commit declined by commit-msg hook." "Commit refus\u00e9 par l'action \"message de commit\"."
::msgcat::mcset fr "Committing changes..." "Commit des modifications..."
::msgcat::mcset fr "write-tree failed:" "write-tree a \u00e9chou\u00e9 :"
::msgcat::mcset fr "Commit failed." "Le commit a \u00e9chou\u00e9."
::msgcat::mcset fr "Commit %s appears to be corrupt" "Le commit %s semble \u00eatre corrompu"
::msgcat::mcset fr "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "Pas de modification \u00e0 commiter.\n\nAucun fichier n'a \u00e9t\u00e9 modifi\u00e9 par ce commit et il ne s'agit pas d'un commit de fusion.\n\nUne resynchronisation va \u00eatre lanc\u00e9e tout de suite automatiquement.\n"
::msgcat::mcset fr "No changes to commit." "Pas de modifications \u00e0 commiter."
::msgcat::mcset fr "commit-tree failed:" "commit-tree a \u00e9chou\u00e9 :"
::msgcat::mcset fr "update-ref failed:" "update-ref a \u00e9chou\u00e9 :"
::msgcat::mcset fr "Created commit %s: %s" "Commit %s cr\u00e9\u00e9 : %s"
::msgcat::mcset fr "Working... please wait..." "Travail en cours... merci de patienter..."
::msgcat::mcset fr "Success" "Succ\u00e8s"
::msgcat::mcset fr "Error: Command Failed" "Erreur : \u00e9chec de la commande"
::msgcat::mcset fr "Number of loose objects" "Nombre d'objets en fichier particulier"
::msgcat::mcset fr "Disk space used by loose objects" "Espace disque utilis\u00e9 par les fichiers particuliers"
::msgcat::mcset fr "Number of packed objects" "Nombre d'objets empaquet\u00e9s"
::msgcat::mcset fr "Number of packs" "Nombre de paquets d'objets"
::msgcat::mcset fr "Disk space used by packed objects" "Espace disque utilis\u00e9 par les objets empaquet\u00e9s"
::msgcat::mcset fr "Packed objects waiting for pruning" "Objets empaquet\u00e9s attendant d'\u00eatre supprim\u00e9s"
::msgcat::mcset fr "Garbage files" "Fichiers poubelle"
::msgcat::mcset fr "Compressing the object database" "Compression de la base des objets"
::msgcat::mcset fr "Verifying the object database with fsck-objects" "V\u00e9rification de la base des objets avec fsck-objects"
::msgcat::mcset fr "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database.\n\nCompress the database now?" "Ce d\u00e9p\u00f4t comprend actuellement environ %i objets ayant leur fichier particulier.\n\nPour conserver une performance optimale, il est fortement recommand\u00e9 de comprimer la base de donn\u00e9e.\n\nComprimer la base maintenant ?"
::msgcat::mcset fr "Invalid date from Git: %s" "Date invalide de Git : %s"
::msgcat::mcset fr "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "Aucune diff\u00e9rence d\u00e9tect\u00e9e.\n\n%s ne comporte aucune modification.\n\nLa date de modification de ce fichier a \u00e9t\u00e9 mise \u00e0 jour par une autre application, mais le contenu du fichier n'a pas chang\u00e9.\n\nUne resynchronisation va \u00eatre lanc\u00e9e automatiquement pour trouver d'autres fichiers qui pourraient se trouver dans le m\u00eame \u00e9tat."
::msgcat::mcset fr "Loading diff of %s..." "Chargement des diff\u00e9rences de %s..."
::msgcat::mcset fr "LOCAL: deleted\nREMOTE:\n" "LOCAL : supprim\u00e9\nDISTANT :\n"
::msgcat::mcset fr "REMOTE: deleted\nLOCAL:\n" "DISTANT : supprim\u00e9\nLOCAL :\n"
::msgcat::mcset fr "LOCAL:\n" "LOCAL :\n"
::msgcat::mcset fr "REMOTE:\n" "DISTANT :\n"
::msgcat::mcset fr "Unable to display %s" "Impossible d'afficher %s"
::msgcat::mcset fr "Error loading file:" "Erreur lors du chargement du fichier :"
::msgcat::mcset fr "Git Repository (subproject)" "D\u00e9p\u00f4t Git (sous projet)"
::msgcat::mcset fr "* Binary file (not showing content)." "* Fichier binaire (pas d'aper\u00e7u du contenu)."
::msgcat::mcset fr "* Untracked file is %d bytes.\n* Showing only first %d bytes.\n" "* Le fichier non suivi fait %d octets.\n* Seuls les %d premiers octets sont montr\u00e9s.\n"
::msgcat::mcset fr "\n* Untracked file clipped here by %s.\n* To see the entire file, use an external editor.\n" "\n* Fichier suivi raccourcis ici de %s.\n* Pour voir le fichier entier, utilisez un \u00e9diteur externe.\n"
::msgcat::mcset fr "Failed to unstage selected hunk." "\u00c9chec lors de la d\u00e9sindexation de la section s\u00e9lectionn\u00e9e."
::msgcat::mcset fr "Failed to stage selected hunk." "\u00c9chec lors de l'indexation de la section."
::msgcat::mcset fr "Failed to unstage selected line." "\u00c9chec lors de la d\u00e9sindexation de la ligne s\u00e9lectionn\u00e9e."
::msgcat::mcset fr "Failed to stage selected line." "\u00c9chec lors de l'indexation de la ligne."
::msgcat::mcset fr "Default" "D\u00e9faut"
::msgcat::mcset fr "System (%s)" "Syst\u00e8me (%s)"
::msgcat::mcset fr "Other" "Autre"
::msgcat::mcset fr "error" "erreur"
::msgcat::mcset fr "warning" "attention"
::msgcat::mcset fr "You must correct the above errors before committing." "Vous devez corriger les erreurs suivantes avant de pouvoir commiter."
::msgcat::mcset fr "Unable to unlock the index." "Impossible de d\u00e9verrouiller l'index."
::msgcat::mcset fr "Index Error" "Erreur de l'index"
::msgcat::mcset fr "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "\u00c9chec de la mise \u00e0 jour de l'index. Une resynchronisation va \u00eatre lanc\u00e9e automatiquement."
::msgcat::mcset fr "Continue" "Continuer"
::msgcat::mcset fr "Unlock Index" "D\u00e9verrouiller l'index"
::msgcat::mcset fr "Unstaging %s from commit" "D\u00e9sindexation de : %s"
::msgcat::mcset fr "Ready to commit." "Pr\u00eat \u00e0 \u00eatre commit\u00e9."
::msgcat::mcset fr "Adding %s" "Ajout de %s"
::msgcat::mcset fr "Revert changes in file %s?" "Annuler les modifications dans le fichier %s ? "
::msgcat::mcset fr "Revert changes in these %i files?" "Annuler les modifications dans ces %i fichiers ?"
::msgcat::mcset fr "Any unstaged changes will be permanently lost by the revert." "Toutes les modifications non-index\u00e9es seront d\u00e9finitivement perdues par l'annulation."
::msgcat::mcset fr "Do Nothing" "Ne rien faire"
::msgcat::mcset fr "Reverting selected files" "Annuler modifications dans fichiers selectionn\u00e9s"
::msgcat::mcset fr "Reverting %s" "Annulation des modifications dans %s"
::msgcat::mcset fr "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "Impossible de fusionner pendant une correction.\n\nVous devez finir de corriger ce commit avant de lancer une quelconque fusion.\n"
::msgcat::mcset fr "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before a merge can be performed.\n\nThe rescan will be automatically started now.\n" "L'\u00e9tat lors de la derni\u00e8re synchronisation ne correspond plus \u00e0 l'\u00e9tat du d\u00e9p\u00f4t.\n\nUn autre programme Git a modifi\u00e9 ce d\u00e9p\u00f4t depuis la derni\u00e8re synchronisation. Une resynchronisation doit \u00eatre effectu\u00e9e avant de pouvoir fusionner de nouveau.\n\nCela va \u00eatre fait tout de suite automatiquement\n"
::msgcat::mcset fr "You are in the middle of a conflicted merge.\n\nFile %s has merge conflicts.\n\nYou must resolve them, stage the file, and commit to complete the current merge.  Only then can you begin another merge.\n" "Vous \u00eates au milieu d'une fusion conflictuelle.\n\nLe fichier %s a des conflicts de fusion.\n\nVous devez les r\u00e9soudre, puis indexer le fichier, et enfin commiter pour terminer la fusion courante. Seulement \u00e0 ce moment l\u00e0 sera-t-il possible d'effectuer une nouvelle fusion.\n"
::msgcat::mcset fr "You are in the middle of a change.\n\nFile %s is modified.\n\nYou should complete the current commit before starting a merge.  Doing so will help you abort a failed merge, should the need arise.\n" "Vous \u00eates au milieu d'une modification.\n\nLe fichier %s a \u00e9t\u00e9 modifi\u00e9.\n\nVous devriez terminer le commit courant avant de lancer une fusion. En faisait comme cela, vous \u00e9viterez de devoir \u00e9ventuellement abandonner une fusion ayant \u00e9chou\u00e9.\n"
::msgcat::mcset fr "%s of %s" "%s de %s"
::msgcat::mcset fr "Merging %s and %s..." "Fusion de %s et %s..."
::msgcat::mcset fr "Merge completed successfully." "La fusion s'est faite avec succ\u00e8s."
::msgcat::mcset fr "Merge failed.  Conflict resolution is required." "La fusion a echou\u00e9. Il est n\u00e9cessaire de r\u00e9soudre les conflits."
::msgcat::mcset fr "Merge Into %s" "Fusion dans %s"
::msgcat::mcset fr "Revision To Merge" "R\u00e9vision \u00e0 fusionner"
::msgcat::mcset fr "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "Impossible d'abandonner en cours de correction.\n\nVous devez finir de corriger ce commit.\n"
::msgcat::mcset fr "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "Abandonner la fusion ?\n\nAbandonner la fusion courante entrainera la perte de TOUTES les modifications non commit\u00e9es.\n\nAbandonner quand m\u00eame la fusion courante ?"
::msgcat::mcset fr "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "R\u00e9initialiser les modifications ?\n\nR\u00e9initialiser les modifications va faire perdre TOUTES les modifications non commit\u00e9es.\n\nR\u00e9initialiser quand m\u00eame les modifications courantes ?"
::msgcat::mcset fr "Aborting" "Abandon"
::msgcat::mcset fr "files reset" "fichiers r\u00e9initialis\u00e9s"
::msgcat::mcset fr "Abort failed." "L'abandon a \u00e9chou\u00e9."
::msgcat::mcset fr "Abort completed.  Ready." "Abandon temin\u00e9. Pr\u00eat."
::msgcat::mcset fr "Force resolution to the base version?" "Forcer la r\u00e9solution \u00e0 la version de base ?"
::msgcat::mcset fr "Force resolution to this branch?" "Forcer la r\u00e9solution \u00e0 cette branche ?"
::msgcat::mcset fr "Force resolution to the other branch?" "Forcer la r\u00e9solution \u00e0 l'autre branche ?"
::msgcat::mcset fr "Note that the diff shows only conflicting changes.\n\n%s will be overwritten.\n\nThis operation can be undone only by restarting the merge." "Noter que le diff ne montre que les modifications en conflit.\n\n%s sera \u00e9cras\u00e9.\n\nCette op\u00e9ration ne peut \u00eatre invers\u00e9e qu'en relan\u00e7ant la fusion."
::msgcat::mcset fr "File %s seems to have unresolved conflicts, still stage?" "Le fichier %s semble avoir des conflits non r\u00e9solus, indexer quand m\u00eame ?"
::msgcat::mcset fr "Adding resolution for %s" "Ajouter une r\u00e9solution pour %s"
::msgcat::mcset fr "Cannot resolve deletion or link conflicts using a tool" "Impossible de r\u00e9soudre la suppression ou de relier des conflits en utilisant un outil"
::msgcat::mcset fr "Conflict file does not exist" "Le fichier en conflit n'existe pas."
::msgcat::mcset fr "Not a GUI merge tool: '%s'" "'%s' n'est pas un outil graphique pour fusionner des fichiers."
::msgcat::mcset fr "Unsupported merge tool '%s'" "Outil de fusion '%s' non support\u00e9"
::msgcat::mcset fr "Merge tool is already running, terminate it?" "L'outil de fusion tourne d\u00e9j\u00e0, faut-il le terminer ?"
::msgcat::mcset fr "Error retrieving versions:\n%s" "Erreur lors de la r\u00e9cup\u00e9ration des versions :\n%s"
::msgcat::mcset fr "Could not start the merge tool:\n\n%s" "Impossible de lancer l'outil de fusion :\n\n%s"
::msgcat::mcset fr "Running merge tool..." "Lancement de l'outil de fusion..."
::msgcat::mcset fr "Merge tool failed." "L'outil de fusion a \u00e9chou\u00e9."
::msgcat::mcset fr "Invalid global encoding '%s'" "Codage global '%s' invalide"
::msgcat::mcset fr "Invalid repo encoding '%s'" "Codage de d\u00e9p\u00f4t '%s' invalide"
::msgcat::mcset fr "Restore Defaults" "Remettre les valeurs par d\u00e9faut"
::msgcat::mcset fr "Save" "Sauvegarder"
::msgcat::mcset fr "%s Repository" "D\u00e9p\u00f4t : %s"
::msgcat::mcset fr "Global (All Repositories)" "Globales (tous les d\u00e9p\u00f4ts)"
::msgcat::mcset fr "User Name" "Nom d'utilisateur"
::msgcat::mcset fr "Email Address" "Adresse email"
::msgcat::mcset fr "Summarize Merge Commits" "R\u00e9sumer les commits de fusion"
::msgcat::mcset fr "Merge Verbosity" "Fusion bavarde"
::msgcat::mcset fr "Show Diffstat After Merge" "Montrer statistiques de diff apr\u00e8s fusion"
::msgcat::mcset fr "Use Merge Tool" "Utiliser outil de fusion"
::msgcat::mcset fr "Trust File Modification Timestamps" "Faire confiance aux dates de modification de fichiers "
::msgcat::mcset fr "Prune Tracking Branches During Fetch" "Purger les branches de suivi pendant la r\u00e9cup\u00e9ration"
::msgcat::mcset fr "Match Tracking Branches" "Faire correspondre les branches de suivi"
::msgcat::mcset fr "Blame Copy Only On Changed Files" "Annoter les copies seulement sur fichiers modifi\u00e9s"
::msgcat::mcset fr "Minimum Letters To Blame Copy On" "Minimum de carat\u00e8res pour annoter une copie"
::msgcat::mcset fr "Blame History Context Radius (days)" "Distance de bl\u00e2me dans l'historique (jours)"
::msgcat::mcset fr "Number of Diff Context Lines" "Nombre de lignes de contexte dans les diffs"
::msgcat::mcset fr "Commit Message Text Width" "Largeur du texte de message de commit"
::msgcat::mcset fr "New Branch Name Template" "Nouveau mod\u00e8le de nom de branche"
::msgcat::mcset fr "Default File Contents Encoding" "Codage du contenu des fichiers par d\u00e9faut"
::msgcat::mcset fr "Change" "Modifier"
::msgcat::mcset fr "Spelling Dictionary:" "Dictionnaire d'orthographe :"
::msgcat::mcset fr "Change Font" "Modifier les polices"
::msgcat::mcset fr "Choose %s" "Choisir %s"
::msgcat::mcset fr "pt." "pt."
::msgcat::mcset fr "Preferences" "Pr\u00e9f\u00e9rences"
::msgcat::mcset fr "Failed to completely save options:" "La sauvegarde compl\u00e8te des options a \u00e9chou\u00e9 :"
::msgcat::mcset fr "Remove Remote" "Supprimer un d\u00e9p\u00f4t distant"
::msgcat::mcset fr "Prune from" "Purger de"
::msgcat::mcset fr "Fetch from" "R\u00e9cup\u00e9rer de"
::msgcat::mcset fr "Push to" "Pousser vers"
::msgcat::mcset fr "Add Remote" "Ajouter un d\u00e9p\u00f4t distant"
::msgcat::mcset fr "Add New Remote" "Ajouter un nouveau d\u00e9p\u00f4t distant"
::msgcat::mcset fr "Add" "Ajouter"
::msgcat::mcset fr "Remote Details" "D\u00e9tails des d\u00e9p\u00f4ts distants"
::msgcat::mcset fr "Location:" "Emplacement :"
::msgcat::mcset fr "Further Action" "Action suppl\u00e9mentaire"
::msgcat::mcset fr "Fetch Immediately" "R\u00e9cup\u00e9rer imm\u00e9diatement"
::msgcat::mcset fr "Initialize Remote Repository and Push" "Initialiser un d\u00e9p\u00f4t distant et pousser"
::msgcat::mcset fr "Do Nothing Else Now" "Ne rien faire d'autre maintenant"
::msgcat::mcset fr "Please supply a remote name." "Merci de fournir un nom de d\u00e9p\u00f4t distant."
::msgcat::mcset fr "'%s' is not an acceptable remote name." "'%s' n'est pas un nom de d\u00e9p\u00f4t distant acceptable."
::msgcat::mcset fr "Failed to add remote '%s' of location '%s'." "\u00c9chec de l'ajout du d\u00e9p\u00f4t distant '%s' \u00e0 l'emplacement '%s'."
::msgcat::mcset fr "fetch %s" "r\u00e9cup\u00e9rer %s"
::msgcat::mcset fr "Fetching the %s" "R\u00e9cup\u00e9ration de %s"
::msgcat::mcset fr "Do not know how to initialize repository at location '%s'." "Pas de m\u00e9thode connue pour initialiser le d\u00e9p\u00f4t \u00e0 l'emplacement '%s'."
::msgcat::mcset fr "push %s" "pousser %s"
::msgcat::mcset fr "Setting up the %s (at %s)" "Mise en place de %s (\u00e0 %s)"
::msgcat::mcset fr "Delete Branch Remotely" "Supprimer une branche \u00e0 distance"
::msgcat::mcset fr "From Repository" "D\u00e9p\u00f4t source"
::msgcat::mcset fr "Remote:" "Branche distante :"
::msgcat::mcset fr "Arbitrary Location:" "Emplacement arbitraire :"
::msgcat::mcset fr "Branches" "Branches"
::msgcat::mcset fr "Delete Only If" "Supprimer seulement si"
::msgcat::mcset fr "Merged Into:" "Fusionn\u00e9 dans :"
::msgcat::mcset fr "A branch is required for 'Merged Into'." "Une branche est n\u00e9cessaire pour 'Fusionn\u00e9 dans'."
::msgcat::mcset fr "The following branches are not completely merged into %s:\n\n - %s" "Les branches suivantes ne sont pas compl\u00e8tement fusionn\u00e9es dans %s :\n\n - %s"
::msgcat::mcset fr "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "Un ou plusieurs des tests de fusion ont \u00e9chou\u00e9 parce que vous n'avez pas r\u00e9cup\u00e9r\u00e9 les commits n\u00e9cessaires. Essayez de r\u00e9cup\u00e9rer \u00e0 partir de %s d'abord."
::msgcat::mcset fr "Please select one or more branches to delete." "Merci de s\u00e9lectionner une ou plusieurs branches \u00e0 supprimer."
::msgcat::mcset fr "Deleting branches from %s" "Suppression des branches de %s"
::msgcat::mcset fr "No repository selected." "Aucun d\u00e9p\u00f4t n'est s\u00e9lectionn\u00e9."
::msgcat::mcset fr "Scanning %s..." "Synchronisation de %s..."
::msgcat::mcset fr "Find:" "Chercher :"
::msgcat::mcset fr "Next" "Suivant"
::msgcat::mcset fr "Prev" "Pr\u00e9c\u00e9dent"
::msgcat::mcset fr "Case-Sensitive" "Sensible \u00e0 la casse"
::msgcat::mcset fr "Cannot write shortcut:" "Impossible d'\u00e9crire le raccourci :"
::msgcat::mcset fr "Cannot write icon:" "Impossible d'\u00e9crire l'ic\u00f4ne :"
::msgcat::mcset fr "Unsupported spell checker" "V\u00e9rificateur d'orthographe non support\u00e9"
::msgcat::mcset fr "Spell checking is unavailable" "La v\u00e9rification d'orthographe n'est pas disponible"
::msgcat::mcset fr "Invalid spell checking configuration" "Configuration de v\u00e9rification d'orthographe invalide"
::msgcat::mcset fr "Reverting dictionary to %s." "R\u00e9initialisation du dictionnaire \u00e0 %s."
::msgcat::mcset fr "Spell checker silently failed on startup" "La v\u00e9rification d'orthographe a \u00e9chou\u00e9 silencieusement au d\u00e9marrage"
::msgcat::mcset fr "Unrecognized spell checker" "V\u00e9rificateur d'orthographe non reconnu"
::msgcat::mcset fr "No Suggestions" "Aucune suggestion"
::msgcat::mcset fr "Unexpected EOF from spell checker" "EOF inattendue envoy\u00e9e par le v\u00e9rificateur d'orthographe"
::msgcat::mcset fr "Spell Checker Failed" "Le v\u00e9rificateur d'orthographe a \u00e9chou\u00e9"
::msgcat::mcset fr "No keys found." "Aucune cl\u00e9 trouv\u00e9e."
::msgcat::mcset fr "Found a public key in: %s" "Cl\u00e9 publique trouv\u00e9e dans : %s"
::msgcat::mcset fr "Generate Key" "G\u00e9n\u00e9rer une cl\u00e9"
::msgcat::mcset fr "Copy To Clipboard" "Copier dans le presse-papier"
::msgcat::mcset fr "Your OpenSSH Public Key" "Votre cl\u00e9 publique OpenSSH"
::msgcat::mcset fr "Generating..." "G\u00e9n\u00e9ration..."
::msgcat::mcset fr "Could not start ssh-keygen:\n\n%s" "Impossible de lancer ssh-keygen :\n\n%s"
::msgcat::mcset fr "Generation failed." "La g\u00e9n\u00e9ration a \u00e9chou\u00e9."
::msgcat::mcset fr "Generation succeeded, but no keys found." "La g\u00e9n\u00e9ration a r\u00e9ussi, mais aucune cl\u00e9 n'a \u00e9t\u00e9 trouv\u00e9e."
::msgcat::mcset fr "Your key is in: %s" "Votre cl\u00e9 est dans : %s"
::msgcat::mcset fr "%s ... %*i of %*i %s (%3i%%)" "%s ... %*i de %*i %s (%3i%%)"
::msgcat::mcset fr "Running %s requires a selected file." "Lancer %s n\u00e9cessite qu'un fichier soit s\u00e9lectionn\u00e9."
::msgcat::mcset fr "Are you sure you want to run %s?" "\u00cates-vous s\u00fbr de vouloir lancer %s ?"
::msgcat::mcset fr "Tool: %s" "Outil : %s"
::msgcat::mcset fr "Running: %s" "Lancement de : %s"
::msgcat::mcset fr "Tool completed successfully: %s" "L'outil a termin\u00e9 avec succ\u00e8s : %s"
::msgcat::mcset fr "Tool failed: %s" "L'outil a \u00e9chou\u00e9 : %s"
::msgcat::mcset fr "Add Tool" "Ajouter un outil"
::msgcat::mcset fr "Add New Tool Command" "Ajouter une nouvelle commande d'outil"
::msgcat::mcset fr "Add globally" "Ajouter globalement"
::msgcat::mcset fr "Tool Details" "D\u00e9tails sur l'outil"
::msgcat::mcset fr "Use '/' separators to create a submenu tree:" "Utiliser les s\u00e9parateurs '/' pour cr\u00e9er un arbre de sous-menus :"
::msgcat::mcset fr "Command:" "Commande :"
::msgcat::mcset fr "Show a dialog before running" "Montrer une bo\u00eete de dialogue avant le lancement"
::msgcat::mcset fr "Ask the user to select a revision (sets \$REVISION)" "Demander \u00e0 l'utilisateur de s\u00e9lectionner une r\u00e9vision (change \$REVISION)"
::msgcat::mcset fr "Ask the user for additional arguments (sets \$ARGS)" "Demander \u00e0 l'utilisateur des arguments suppl\u00e9mentaires (change \$ARGS)"
::msgcat::mcset fr "Don't show the command output window" "Ne pas montrer la fen\u00eatre de sortie des commandes"
::msgcat::mcset fr "Run only if a diff is selected (\$FILENAME not empty)" "Lancer seulement si un diff est s\u00e9lectionn\u00e9 (\$FILENAME non vide)"
::msgcat::mcset fr "Please supply a name for the tool." "Merci de fournir un nom pour l'outil."
::msgcat::mcset fr "Tool '%s' already exists." "L'outil '%s' existe d\u00e9j\u00e0."
::msgcat::mcset fr "Could not add tool:\n%s" "Impossible d'ajouter l'outil :\n%s"
::msgcat::mcset fr "Remove Tool" "Supprimer l'outil"
::msgcat::mcset fr "Remove Tool Commands" "Supprimer des commandes d'outil"
::msgcat::mcset fr "Remove" "Supprimer"
::msgcat::mcset fr "(Blue denotes repository-local tools)" "(Le bleu indique des outils locaux au d\u00e9p\u00f4t)"
::msgcat::mcset fr "Run Command: %s" "Lancer commande : %s"
::msgcat::mcset fr "Arguments" "Arguments"
::msgcat::mcset fr "OK" "OK"
::msgcat::mcset fr "Fetching new changes from %s" "R\u00e9cup\u00e9ration des derni\u00e8res modifications de %s"
::msgcat::mcset fr "remote prune %s" "purger \u00e0 distance %s"
::msgcat::mcset fr "Pruning tracking branches deleted from %s" "Nettoyer les branches de suivi supprim\u00e9es de %s"
::msgcat::mcset fr "Pushing changes to %s" "Les modifications sont pouss\u00e9es vers %s"
::msgcat::mcset fr "Mirroring to %s" "Dupliquer dans %s"
::msgcat::mcset fr "Pushing %s %s to %s" "Pousse %s %s vers %s"
::msgcat::mcset fr "Push Branches" "Pousser branches"
::msgcat::mcset fr "Source Branches" "Branches source"
::msgcat::mcset fr "Destination Repository" "D\u00e9p\u00f4t de destination"
::msgcat::mcset fr "Transfer Options" "Options de transfert"
::msgcat::mcset fr "Force overwrite existing branch (may discard changes)" "Forcer l'\u00e9crasement d'une branche existante (peut supprimer des modifications)"
::msgcat::mcset fr "Use thin pack (for slow network connections)" "Utiliser des petits paquets (pour les connexions lentes)"
::msgcat::mcset fr "Include tags" "Inclure les marques (tags)"
