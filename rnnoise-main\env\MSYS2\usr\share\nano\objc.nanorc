## Syntax highlighting for C/C++/Obj-C files.

## Original author:  <PERSON>

syntax m "\.m$"
magic "Objective-C source"
comment "//"

# Stuffs.
color brightwhite "\<[[:upper:]_][[:upper:][:digit:]_]+\>"
color green "\<(float|double|BOOL|bool|char|int|short|long|id|sizeof|enum|void|static|const|struct|union|typedef|extern|(un)?signed|inline)\>"
color green "\<[[:alpha:]_][[:alnum:]_]*_t\>"
color green "\<(class|namespace|template|public|protected|private|typename|this|friend|virtual|using|mutable|volatile|register|explicit)\>"
color brightgreen "\<(for|if|while|do|else|case|default|switch)\>"
color brightgreen "\<(try|throw|catch|operator|new|delete)\>"
color brightgreen "\<(goto|continue|break|return)\>"
color brightgreen "@(encode|end|implementation|interface)|selector)\>"

# GCC builtins.
color cyan "__attribute__[[:blank:]]*\(\([^)]*\)\)|__(aligned|asm|builtin|hidden|inline|packed|restrict|section|typeof|weak)__"

# Selector/method.
color brightmagenta "(^|[[:blank:]])\[.*[[:blank:]].*\]"
color white ":[[:alnum:]]*"
color magenta "[[:alnum:]]*:"
color white "\[[^][:blank:]]*\]"

# Strings.
color brightblack "'([^'\]|\\(["'\abfnrtv]|x[[:xdigit:]]{1,2}|[0-3]?[0-7]{1,2}))'"
color brightblack "<[^= 	]*>|"([^"\]|\\.)*""
color brightblue "@"([^"\]|\\.)*""

# Preprocessor commands.
color brightblue "^[[:blank:]]*#[[:blank:]]*(define|include|import|(un|ifn?)def|endif|el(if|se)|if|warning|error)"

# Comments.
color yellow "//.*"
color yellow start="/\*" end="\*/"

# Trailing whitespace.
color ,green "[[:space:]]+$"
