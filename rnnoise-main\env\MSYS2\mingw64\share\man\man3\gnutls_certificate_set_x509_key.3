.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_set_x509_key" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_set_x509_key \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_set_x509_key(gnutls_certificate_credentials_t " res ", gnutls_x509_crt_t * " cert_list ", int " cert_list_size ", gnutls_x509_privkey_t " key ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t res" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "gnutls_x509_crt_t * cert_list" 12
contains a certificate list (path) for the specified private key
.IP "int cert_list_size" 12
holds the size of the certificate list
.IP "gnutls_x509_privkey_t key" 12
is a \fBgnutls_x509_privkey_t\fP key
.SH "DESCRIPTION"
This function sets a certificate/private key pair in the
gnutls_certificate_credentials_t type.  This function may be
called more than once, in case multiple keys/certificates exist for
the server.  For clients that wants to send more than their own end
entity certificate (e.g., also an intermediate CA cert) then put
the certificate chain in  \fIcert_list\fP .

Note that the certificates and keys provided, can be safely deinitialized
after this function is called.

If that function fails to load the  \fIres\fP type is at an undefined state, it must
not be reused to load other keys or certificates.

Note that, this function by default returns zero on success and a negative value on error.
Since 3.5.6, when the flag \fBGNUTLS_CERTIFICATE_API_V2\fP is set using \fBgnutls_certificate_set_flags()\fP
it returns an index (greater or equal to zero). That index can be used to other functions to refer to the added key\-pair.
.SH "RETURNS"
On success this functions returns zero, and otherwise a negative value on error (see above for modifying that behavior).
.SH "SINCE"
2.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
