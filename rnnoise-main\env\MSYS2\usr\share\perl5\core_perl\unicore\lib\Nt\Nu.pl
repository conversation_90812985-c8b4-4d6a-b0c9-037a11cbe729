# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V304
188
191
2548
2554
2930
2936
3056
3059
3192
3199
3416
3423
3440
3449
3882
3892
4978
4989
5870
5873
6128
6138
8528
8579
8581
8586
9321
9332
9341
9352
9361
9372
9451
9461
9470
9471
10111
10112
10121
10122
10131
10132
11517
11518
12295
12296
12321
12330
12344
12347
12690
12694
12832
12842
12872
12880
12881
12896
12928
12938
12977
12992
13317
13318
13443
13444
14378
14379
15181
15182
19968
19969
19971
19972
19975
19976
19977
19978
20061
20062
20108
20109
20116
20117
20118
20119
20159
20161
20191
20192
20200
20201
20237
20238
20336
20337
20740
20741
20806
20807
20841
20842
20843
20844
20845
20846
21313
21314
21315
21318
21324
21325
21441
21445
22235
22236
22769
22770
22777
22778
24186
24187
24318
24320
24332
24335
24336
24337
25342
25343
25420
25421
26578
26579
28422
28423
29590
29591
30334
30335
32902
32903
33836
33837
36014
36015
36019
36020
36144
36145
38433
38434
38470
38471
38476
38477
38520
38521
38646
38647
42726
42736
43056
43062
63851
63852
63859
63860
63864
63865
63922
63923
63953
63954
63955
63956
63997
63998
65799
65844
65856
65913
65930
65932
66273
66300
66336
66340
66369
66370
66378
66379
66513
66518
67672
67680
67705
67712
67751
67760
67835
67840
67862
67868
68028
68030
68032
68048
68050
68096
68164
68169
68221
68223
68253
68256
68331
68336
68440
68448
68472
68480
68521
68528
68858
68864
69225
69247
69405
69415
69457
69461
69573
69580
69723
69734
70113
70133
71482
71484
71914
71923
72794
72813
73664
73685
74752
74863
93019
93026
93824
93847
119488
119508
119520
119540
119648
119673
125127
125136
126065
126124
126125
126128
126129
126133
126209
126254
126255
126270
127243
127245
131073
131074
131172
131173
131298
131299
131361
131362
133418
133419
133507
133508
133516
133517
133532
133533
133866
133867
133885
133886
133913
133914
140176
140177
141720
141721
146203
146204
156269
156270
194704
194705
END
