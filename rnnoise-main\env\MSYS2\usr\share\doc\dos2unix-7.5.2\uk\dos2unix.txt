НАЗВА
    dos2unix - програма для перетворення даних у текстовому форматі DOS/Mac
    у формат Unix, і навпаки

КОРОТКИЙ ОПИС
        dos2unix [параметри] [ФАЙЛ ...] [-n ВХІДНИЙ_ФАЙЛ ВИХІДНИЙ_ФАЙЛ ...]
        unix2dos [параметри] [ФАЙЛ ...] [-n ВХІДНИЙ_ФАЙЛ ВИХІДНИЙ_ФАЙЛ ...]

ОПИС
    До складу пакунка Dos2unix включено програми "dos2unix" та "unix2dos",
    призначені для перетворення звичайних текстових даних у форматі DOS або
    Mac на дані у форматі Unix, і навпаки.

    У текстових файлах DOS/Windows розрив рядка або перехід на новий рядок
    здійснюється за допомогою комбінації двох символів: повернення каретки
    (CR) і переведення рядка (LF). У текстових файлах Unix за перехід на
    новий рядок відповідає один символ: переведення рядка (LF). У текстових
    файлах Mac, до Mac OS X, за розрив рядка відповідав один символ:
    повернення каретки (CR). У сучасних версіях Mac OS використовується
    типовий для Unix розрив рядка (LF).

    Окрім символів розриву рядка, програма Dos2unix здатна виконувати
    перетворення кодування файлів. Можна перетворити дані у декількох
    кодуваннях DOS на файли у кодуванні Latin-1 Unix. Також можна
    перетворити дані у файлах Windows Unicode (UTF-16) на дані у кодуванні
    Unix Unicode (UTF-8).

    Під час перетворення програма пропускатиме двійкові файли, якщо ви не
    накажете їй виконати перетворення таких файлів безпосередньо.

    Програма автоматично пропускатиме файли, які не є звичайними файлами,
    зокрема каталоги та канали FIFO.

    Типово, програма не вноситиме змін до символічних посилань та об’єктів
    посилань. Якщо потрібно, програма може замінити символічні посилання або
    записати перетворені дані до файла-призначення символічного посилання. У
    Windows запису до об’єкта символічного посилання не передбачено.

    Програму dos2unix було створено за зразком програми dos2unix для
    SunOS/Solaris. Втім, існує одна важлива відмінність від оригінальної
    версії для SunOS/Solaris. Ця версія типово виконує заміну файлів під час
    перетворення (старий режим обробки файлів), а у оригінальній версії для
    SunOS/Solaris передбачено підтримку лише парного перетворення (новий
    режим обробки файлів). Див. також параметри "-o" та "-n". Ще однією
    відмінністю є те, що у версії для SunOS/Solaris типово використовувався
    режим перетворення *iso*, а у цій версії типовим є режим перетворення
    *ascii*.

ПАРАМЕТРИ
    --  Вважати усі наступні параметри назвами файлів. Цим параметром слід
        користуватися, якщо вам потрібно виконати перетворення файлів, чиї
        назви містять дефіси. Наприклад, щоб виконати перетворення файла
        «-foo», скористайтеся такою командою:

            dos2unix -- -foo

        Або у новому режимі файлів:

            dos2unix -n -- -foo out.txt

    --allow-chown
        Дозволити зміну власника файла у старому режимі файлів.

        Якщо використано цей параметр, перетворення не перериватиметься,
        якщо у старому режимі файлів не вдасться зберегти параметри
        належності файла до певного користувача і/або групи. Перетворення
        продовжуватиметься, а перетворений файл матиме нові параметри
        власника, такі, наче його перетворено у новому режимі файлів. Див.
        також параметри "-o" і "-n". Цим параметром можна скористатися, лише
        якщо у dos2unix передбачено підтримку збереження параметрів
        належності файлів певним користувачам і групам.

    -ascii
        Типовий режим перетворення. Див. також розділ щодо режимів
        перетворення.

    -iso
        Виконати перетворення з кодування DOS на кодування ISO-8859-1. Див.
        розділ щодо режимів перетворення.

    -1252
        Використати кодову таблицю 1252 Windows (західноєвропейські мови).

    -437
        Використовувати кодову сторінку DOS 437 (США). Це типова кодова
        сторінка для перетворення ISO.

    -850
        Використовувати кодову сторінку DOS 850 (західноєвропейські мови).

    -860
        Використовувати сторінку DOS 860 (португальська).

    -863
        Використовувати сторінку DOS 863 (канадська французька).

    -865
        Використовувати сторінку DOS 865 (скандинавські мови).

    -7  Перетворювати 8-бітові символи на 7-бітові.

    -b, --keep-bom
        Зберегти позначку порядку байтів (BOM). Якщо у файлі вхідних даних є
        BOM, записати BOM до файла результатів. Це типова поведінка під час
        перетворення у формат із символами розриву рядків DOS. Див. також
        параметр "-r".

    -c, --convmode РЕЖИМ
        Встановити режим перетворення. Значенням аргументу РЕЖИМ може бути
        один з таких рядків: *ascii*, *7bit*, *iso*, *mac*. Типовим є режим
        ascii.

    -D, --display-enc КОДУВАННЯ
        Встановити кодування показаного тексту. Можливі варіанти значень
        параметра КОДУВАННЯ: *ansi*, *unicode*, *unicodebom*, *utf8*,
        *utf8bom*, типовим є ansi.

        Цей параметр доступний лише у dos2unix для Windows з підтримкою назв
        файлів у Unicode. Цей параметр не впливає на справжнє читання та
        запис назв файлів, лише на те, як буде показано ці назви.

        Існує декілька способів показу тексту у консолі Windows, заснованих
        на кодуванні тексту. Усі ці способи мають свої переваги і недоліки.

        ansi
            Типовим способом для dos2unix є кодування тексту у форматі ANSI.
            Перевагою є зворотна сумісність. Цей варіант працює з растровими
            шрифтами та шрифтами TrueType. У деяких регіонах, ймовірно, вам
            доведеться змінити активну кодову сторінку DOS OEM на системну
            кодову сторінку ANSI Windows за допомогою команди "chcp",
            оскільки dos2unix використовує системну кодову сторінку Windows.

            Недоліком ansi є те, що назви файлів із символами, яких немає у
            типовому системному кодуванні, буде показано неправильно.
            Замість цих символів ви побачите знак питання або не той символ.
            Якщо у вашій системі немає файлів із назвами, які містять
            нетипові символи, можна скористатися цим варіантом.

        unicode, unicodebom
            Перевагою кодування unicode (назва у Windows кодування UTF-16) є
            те, що зазвичай текст буде показано правильно. Змінювати активну
            кодову сторінку не потрібно. Ймовірно, вам потрібно встановити
            шрифт консолі TrueType для належного показу нестандартних
            символів. Якщо символ не передбачено у шрифті TrueType, зазвичай
            ви побачите невеличкий квадратик замість символу, іноді із
            знаком питання у ньому.

            Якщо ви користуєтеся консоллю ConEmu, весь текст буде показано
            належним чином, оскільки ConEmu автоматично вибирає належний
            шрифт.

            Недоліком unicode є те, що це кодування несумісне з ASCII.
            Обробка виведених даних є непростою, якщо ви передаватимете ці
            дані до іншої програми або файла.

            Якщо використовується метод "unicodebom", текст у кодуванні
            Unicode буде оброблено з урахуванням BOM (позначки порядку
            байтів). BOM потрібна для правильного переспрямовування або
            тунелювання даних у PowerShell.

        utf8, utf8bom
            Перевагою utf8 є те, що це кодування сумісне з ASCII. Вам слід
            встановити шрифт консолі TrueType. Якщо використано шрифт
            TrueType, текст буде показано подібно до того, як його показано,
            якщо визначено кодування "unicode".

            Недоліком є те, що якщо ви скористаєтеся типовим растровим
            шрифтом, усі символи поза ASCII буде показано неправильно. Не
            лише назви файлів у unicode, а й перекладені повідомлення
            стануть непридатними до читання. У Windows, налаштованому на
            роботі у східно-азійському регіоні, ви можете помітити значне
            блимання під час показу повідомлень.

            У консолі ConEmu добре працює спосіб кодування utf8.

            Якщо використовується метод "utf8bom", текст у кодуванні UTF-8
            буде оброблено з урахуванням BOM (позначки порядку байтів). BOM
            потрібна для правильного переспрямовування або тунелювання даних
            у PowerShell.

        Типове кодування можна змінити за допомогою змінної середовища
        DOS2UNIX_DISPLAY_ENC встановленням для неї значення "unicode",
        "unicodebom", "utf8" або "utf8bom".

    -e, --add-eol
        Додати розрив рядка до останнього рядка, якщо його там немає. Працює
        для будь-яких перетворень.

        У файлі, який перетворено з формату DOS до формату Unix може не
        вистачати розриву рядка в останньому рядку. Існують текстові
        редактори, які записують текстові файли без розриву рядка в
        останньому рядку. Деякі програми Unix мають проблеми з обробкою
        таких файлів, оскільки за стандартом POSIX кожен рядок текстового
        файла має завершуватися символом розриву рядка. Наприклад,
        об'єднання файлів може дати не зовсім очікуваний результат.

    -f, --force
        Примусове перетворення двійкових файлів.

    -gb, --gb18030
        У Windows файли в UTF-16 типово перетворюються на файли в UTF-8,
        незалежно від встановленої локалі. За допомогою цього параметра ви
        можете наказати програмі перетворювати файли в UTF-16 на файли у
        GB18030. Цим параметром можна скористатися лише у Windows. Див.
        також розділ, присвячений GB18030.

    -h, --help
        Показати довідкові дані і завершити роботу.

    -i[ПРАПОРЦІ], --info[=ПРАПОРЦІ] ФАЙЛ ...
        Вивести дані щодо файла. Не виконувати перетворення.

        Буде виведено такі дані, у вказаному порядку: кількість розривів
        рядків у форматі DOS, кількість розривів рядків у форматі Unix,
        кількість розривів рядків у форматі Mac, позначка порядку байтів,
        текстовим чи бінарним є файл та назву файла.

        Приклад результатів:

             6       0       0  no_bom    text    dos.txt
             0       6       0  no_bom    text    unix.txt
             0       0       6  no_bom    text    mac.txt
             6       6       6  no_bom    text    mixed.txt
            50       0       0  UTF-16LE  text    utf16le.txt
             0      50       0  no_bom    text    utf8unix.txt
            50       0       0  UTF-8     text    utf8dos.txt
             2     418     219  no_bom    binary  dos2unix.exe

        Зауважте, що іноді бінарні файли помилково розпізнаються як
        текстові. Див. також параметр "-s".

        Якщо використано додатковий параметр "-e" або "--add-eol", буде
        також виведено дані щодо типу розриву рядків для останнього рядка
        або "noeol", якщо такого немає.

        Приклад результатів:

             6       0       0  no_bom    text   dos     dos.txt
             0       6       0  no_bom    text   unix    unix.txt
             0       0       6  no_bom    text   mac     mac.txt
             1       0       0  no_bom    text   noeol   noeol_dos.txt

        Крім того, можна вказати додаткові прапорці для внесення змін у
        виведені дані. Можна використовувати один або декілька таких
        прапорців.

        0   Виводити рядки даних щодо файла із завершенням на нульовий
            символ, а не символ розриву рядка. Це уможливлює правильну
            інтерпретацію назв файлів, що містять пробіли або символи лапок,
            якщо використано прапорець «c». Скористайтеся цим прапорцем у
            поєднанні із параметром -0 або "--null" xargs(1).

        d   Вивести кількість символів розривів рядка DOS.

        u   Вивести кількість символів розривів рядка Unix.

        m   Вивести кількість символів розривів рядка Mac.

        b   Вивести позначку порядку байтів.

        t   Вивести дані щодо того, є файл текстовим чи бінарним.

        e   Вивести тип розриву рядка в останньому рядку або "noeol", якщо
            останній рядок не містить розриву рядка.

        c   Вивести дані лише тих файлів, які було б перетворено.

            Із прапорцем "c" dos2unix виведе лише назви файлів, у яких
            містяться розриви рядків DOS. unix2dos виведе лише назви файлів,
            у яких містяться розриви рядків Unix.

            Якщо використано додатковий параметр "-e" або "--add-eol", буде
            також виведено список файлів, які не містять символу розриву
            рядка в останньому рядку.

        h   Вивести заголовок.

        p   Показувати назви файлів без шляхів.

        Приклади:

        Вивести дані щодо усіх файлів *.txt:

            dos2unix -i *.txt

        Вивести кількість розривів рядків у форматі DOS і розривів рядків у
        форматі Unix:

            dos2unix -idu *.txt

        Вивести лише позначку порядку байтів:

            dos2unix --info=b *.txt

        Вивести список файлів, у яких є символи розриву рядків DOS:

            dos2unix -ic *.txt

        Вивести список файлів, у яких використано символи розриву рядків
        Unix:

            unix2dos -ic *.txt

        Вивести список файлів, у яких є символи розриву рядків DOS або якы
        не містять символу розриву рядка в останньому рядку:

            dos2unix -e -ic *.txt

        Перетворити лише файли із розривами рядків DOS і не чіпати інших
        файлів:

            dos2unix -ic0 *.txt | xargs -0 dos2unix

        Знайти текстові файли і розривами рядків DOS:

            find -name '*.txt' -print0 | xargs -0 dos2unix -ic

    -k, --keepdate
        Зберегти часову позначку файла вхідних даних у файлі результатів
        перетворення.

    -L, --license
        Вивести умови ліцензування програми.

    -l, --newline
        Вставити додатковий символ розриву рядка.

        dos2unix: перетворення на два символи розриву рядків Unix
        відбуватиметься лише для комбінацій розриву рядків DOS. У режимі Mac
        виконуватиметься перетворення на два розриви рядків Unix лише
        символів розриву рядків Mac.

        unix2dos: перетворення на дві комбінації розриву рядків DOS
        відбуватиметься лише для символів розриву рядків DOS. У режимі Mac
        виконуватиметься перетворення на два розриви рядків Mac лише
        символів розриву рядків Unix.

    -m, --add-bom
        Записати до файла результатів позначку порядку байтів (BOM). Типово
        буде записано позначку порядку байтів UTF-8.

        Якщо дані початкового файла закодовано у UTF-16 і використано
        параметр "-u", буде дописано позначку порядку байтів UTF-16.

        Не використовуйте цей параметр для кодувань результатів, відмінних
        від UTF-8, UTF-16 або GB18030. Див. також розділ щодо UNICODE.

    -n, --newfile ВХІДНИЙ_ФАЙЛ ВИХІДНИЙ_ФАЙЛ ...
        Новий режим обробки файлів. Перетворити дані з файла ВХІДНИЙ_ФАЙЛ і
        записати результати до файла ВИХІДНИЙ_ФАЙЛ. Назви файлів слід
        вказувати парами, *не слід* використовувати шаблони заміни, інакше
        вміст файлів *буде втрачено*.

        Власником перетвореного файла буде призначено користувача, яким було
        розпочато перетворення у режимі нового файла (парному режимі). Права
        доступу на читання або запис нового файла буде визначено на основі
        прав доступу до початкового файла мінус umask(1) для користувача,
        яким було розпочато перетворення.

    --no-allow-chown
        Не дозволяти зміну власника файла у старому режимі файлів (типова
        поведінка).

        Переривати перетворення, якщо у старому режимі файлів не вдасться
        зберегти параметри належності файла до певного користувача і/або
        групи. Див. також параметри "-o" і "-n". Цим параметром можна
        скористатися, лише якщо у dos2unix передбачено підтримку збереження
        параметрів належності файлів певним користувачам і групам.

    --no-add-eol
        Не додавати розрив рядка до останнього рядка, якщо його там немає.

    -O, --to-stdout
        Записати дані до стандартного виведення, подібного до фільтра Unix.
        Скористайтеся параметром "-o", щоб повернутися до старого режиму
        файла (на місці).

        У поєднанні із параметром "-e" файли можна належним чином розрізати.
        Усередині об'єднаного файла не буде об'єднання останнього і першого
        рядків і позначок порядку байтів Unicode. Приклад:

            dos2unix -e -O файл1.txt файл2.txt > результат.txt

    -o, --oldfile ФАЙЛ ...
        Застарілий режим обробки. Виконати перетворення файла ФАЙЛ і
        перезаписати його вміст. Типово, програма працює у цьому режимі.
        Можна використовувати шаблони заміни.

        У застарілому режимі (режимі заміщення) перетворений файл належатиме
        тому самому власнику і групі і матиме ті самі права доступу на
        читання або запис, що і початковий файл. Крім того, якщо
        перетворення файла виконується іншим користувачем, який має права
        доступу на запис до файла (наприклад користувачем root),
        перетворення буде перервано, якщо зберегти початкові значення не
        вдасться. Зміна власника може означати неможливість читання файла
        для його початкового власника. Зміна групи може призвести до проблем
        із безпекою, оскільки файл може стати доступним для читання
        користувачам, які не повинні мати такі права доступу. Можливість
        збереження прав власності та прав доступу до файла передбачено лише
        у Unix.

        Щоб перевірити, чи передбачено у dos2unix підтримку збереження
        параметрів власника і групи файлів, віддайте команду "dos2unix -V".

        Перетворення завжди виконується з використанням тимчасового файла.
        Якщо під час перетворення станеться помилка, тимчасовий файл буде
        вилучено, а початковий файл залишиться незмінним. Якщо перетворення
        буде виконано успішно, початковий файл буде замінено на тимчасовий
        файл. Може так статися, що у вас будуть права на перезапис
        початкового файла, але не буде прав для встановлення тих самих
        параметрів власника для тимчасового файла, який замінить собою
        початковий файл. Це означає, що ви не зможете зберегти параметри
        належності файла певному користувачу і/або групі. У цьому випадку ви
        можете скористатися параметром "--allow-chown", щоб програма могла
        продовжити обробку даних:

            dos2unix --allow-chown якийсь.txt

        Іншим варіантом дій є використання нового режиму файлів:

            dos2unix -n якийсь.txt якийсь.txt

        Перевагою використання параметра "--allow-chown" є те, що ви можете
        користуватися символами-замінниками, а параметри власників буде
        збережено, якщо можливо.

    -q, --quiet
        Режим без виведення повідомлень. Програма не виводитиме жодних
        попереджень або повідомлень про помилки. Повернутим значенням завжди
        буде нуль, якщо вказано правильні параметри командного рядка.

    -r, --remove-bom
        Вилучити позначку порядку байтів (BOM). Не записувати BOM до файла
        результатів. Це типова поведінка під час перетворення файлів з
        форматом розриву рядків Unix. Див. також параметр "-b".

    -s, --safe
        Пропускати двійкові файли (типово).

        Пропускання бінарних файлів реалізовано для того, щоб уникнути
        випадкових помилок. Майте на увазі, що визначення бінарних файлів не
        є 100% точним. Програма просто шукає у файлах бінарні символи, які
        типово не трапляються у текстових файлах. Може так статися, що у
        бінарному файлі містяться лише звичайні текстові символи. Такий
        бінарний файл буде помилково сприйнято програмою як текстовий.

    -u, --keep-utf16
        Зберегти початкове кодування UTF-16. Файл результатів буде записано
        у тому самому кодуванні UTF-16, із прямим або зворотним порядком
        байтів, що і початковий файл. Таким чином можна запобігти
        перетворенню даних у кодування UTF-8. До файла буде записано
        відповідну позначку порядку байтів UTF-16. Вимкнути цей параметр
        можна за допомогою параметра "-ascii".

    -ul, --assume-utf16le
        Припускати, що кодуванням вхідних файлів є UTF-16LE.

        Якщо у початковому файлі є позначка порядку байтів (BOM), її буде
        використано у файлі-результаті, незалежно від використання цього
        параметра.

        Якщо вами було зроблено помилкове припущення щодо формату файла
        (файл вхідних даних насправді не є файлом у форматі UTF-16LE), і
        дані вдасться успішно перетворити, ви отримаєте файл у кодуванні
        UTF-8 з помилковим вмістом. Скасувати таке помилкове перетворення
        можна за допомогою зворотного перетворення iconv(1) з даних у
        форматі UTF-8 на дані у форматі UTF-16LE. Таким чином ви повернетеся
        до початкового кодування даних у файлі.

        Припущення щодо форматування UTF-16LE працює як визначення *режиму
        перетворення*. Перемиканням на типовий режим *ascii* можна вимкнути
        припущення щодо форматування UTF-16LE.

    -ub, --assume-utf16be
        Припускати, що вхідним форматом є UTF-16BE.

        Цей параметр працює у спосіб, тотожний до параметра "-ul".

    -v, --verbose
        Виводити докладні повідомлення. Буде показано додаткові дані щодо
        позначок порядку байтів та кількості перетворених символів розриву
        рядків.

    -F, --follow-symlink
        Переходити за символічними посиланням і перетворювати файли, на які
        вони вказують.

    -R, --replace-symlink
        Замінити символічні посилання перетвореними файлами (початкові
        файли, на які вони вказують, змінено не буде).

    -S, --skip-symlink
        Не змінювати символічні посилання та файли, на які вони посилаються
        (типово).

    -V, --version
        Вивести дані щодо версії і завершити роботу.

РЕЖИМ MAC
    Типово, розриви рядків DOS перетворюються на розриви рядків Unix, і
    навпаки. Розриви рядків Mac перетворенню не підлягають.

    У режимі Mac розриви рядків Mac перетворюються на розриви рядків Unix, і
    навпаки. Розриви рядків DOS перетворенню не підлягають.

    Щоб запустити програму у режимі перетворення Mac, скористайтеся
    параметром командного рядка "-c mac" або програмами "mac2unix" та
    "unix2mac".

РЕЖИМИ ПЕРЕТВОРЕННЯ
    ascii
        Це типовий режим перетворення. Цей режим призначено для перетворення
        файлів у кодуванні ASCII або сумісному з ASCII кодуванні, зокрема
        UTF-8. Вмикання режиму ascii вимикає режим 7bit і iso.

        Якщо у dos2unix передбачено підтримку UTF-16, файли у кодуванні
        UTF-16 буде перетворено до поточного кодування символів локалі у
        системах POSIX та до UTF-8 у Windows. Вмикання режиму ascii вимикає
        параметр збереження кодування UTF-16 ("-u") і параметр, які надають
        змогу припускати, що вхідні дані закодовано в UTF-16 ("-ul" і
        "-ub"). Щоб визначити, чи передбачено у dos2unix підтримку UTF-16,
        введіть команду "dos2unix -V". Див. також розділ UNICODE.

    7bit
        У цьому режимі усі 8-бітові символи, які не є частиною ASCII, (з
        номерами від 128 до 255) буде перетворено на відповідні 7-бітові
        символи.

    iso Перетворення символів буде виконано з кодування (кодової сторінки)
        DOS до кодування ISO-8859-1 (Latin-1) у Unix. Символи DOS, які не
        мають еквівалентів у ISO-8859-1 і перетворення яких неможливе, буде
        перетворено на символ крапки. Те саме стосується символів
        ISO-8859-1, які не мають еквівалентів у DOS.

        Якщо буде використано лише параметр "-iso", програма dos2unix
        спробує визначити активне кодування. Якщо це виявиться неможливим,
        dos2unix використає типове кодування CP437, яке здебільшого
        використовується у США. Щоб примусово визначити кодування,
        скористайтеся параметром -437 (США), -850 (західноєвропейські мови),
        -860 (португальська), -863 (канадська французька) або -865
        (скандинавські мови). Використати кодування Windows CP1252
        (західноєвропейські мови) можна за допомогою параметра -1252. Для
        інших кодувань використовуйте поєднання dos2unix з iconv(1).
        Програма iconv здатна виконувати перетворення даних у доволі
        широкому спектрі кодувань символів.

        Ніколи не користуйтеся перетворенням ISO для текстових файлів у
        форматі Unicode. Використання подібного перетворення призведе до
        ушкодження вмісту файлів у кодуванні UTF-8.

        Декілька прикладів:

        Перетворити дані у типовому кодуванні DOS на дані у кодуванні
        Latin-1 Unix:

            dos2unix -iso -n in.txt out.txt

        Перетворити дані у кодуванні DOS CP850 на дані у кодуванні Latin-1
        Unix:

            dos2unix -850 -n in.txt out.txt

        Перетворити дані у кодуванні CP1252 Windows на дані у кодуванні
        Latin-1 Unix:

            dos2unix -1252 -n in.txt out.txt

        Перетворити дані у кодуванні CP252 Windows на дані у кодуванні UTF-8
        Unix (Unicode):

            iconv -f CP1252 -t UTF-8 in.txt | dos2unix > out.txt

        Перетворити дані у кодуванні Latin-1 Unix на дані у типовому
        кодуванні DOS:

            unix2dos -iso -n in.txt out.txt

        Перетворити дані у кодуванні Latin-1 Unix на дані у кодуванні DOS
        CP850:

            unix2dos -850 -n in.txt out.txt

        Перетворити дані у кодуванні Latin-1 Unix на дані у кодуванні
        Windows CP1252:

            unix2dos -1252 -n in.txt out.txt

        Перетворити дані у кодуванні UTF-8 Unix (Unicode) на дані у
        кодуванні Windows CP1252:

            unix2dos < in.txt | iconv -f UTF-8 -t CP1252 > out.txt

        Див. також <http://czyborra.com/charsets/codepages.html> та
        <http://czyborra.com/charsets/iso8859.html>.

UNICODE
  Кодування
    Існує декілька різних кодувань Unicode. У Unix та Linux у файлах Unicode
    здебільшого використовується кодування UTF-8. У Windows для текстових
    файлів Unicode може бути використано кодування UTF-8, UTF-16 або UTF-16
    зі зворотним порядком байтів. Втім, здебільшого, використовується формат
    UTF-16.

  Перетворення
    У текстових файлах Unicode, як і у текстових файлах ASCII, може бути
    використано розриви рядків DOS, Unix або Mac.

    Усі версії dos2unix та unix2dos здатні виконувати перетворення у
    кодуванні UTF-8, оскільки UTF-8 було розроблено так, що зворотну
    сумісність з ASCII збережено.

    Програми dos2unix та unix2dos, зібрані з підтримкою Unicode UTF-16,
    можуть читати текстові файли у кодуванні UTF-16 з прямим та зворотним
    порядком байтів. Щоб дізнатися про те, чи було dos2unix зібрано з
    підтримкою UTF-16, віддайте команду "dos2unix -V".

    У Unix/Linux файли у кодуванні UTF-16 перетворюються на файли у
    кодуванні локалі. Для визначення поточного кодування символів локалі
    скористайтеся командою locale(1). Якщо перетворення виявиться
    неможливим, програма повідомить про помилку перетворення і пропустить
    відповідний файл.

    У Windows файли UTF-16 типово буде перетворено на файли UTF-8. Обидва
    типи систем, Windows та Unix/Linux, мають непогані можливості з
    підтримки файлів у форматуванні UTF-8.

    Кодування UTF-16 та UTF-8 є повністю сумісними. Під час перетворення не
    буде втрачено жодної інформації. Якщо під час перетворення даних у
    кодуванні UTF-16 на дані у кодуванні UTF-8 трапиться помилка, наприклад,
    якщо у вхідному файлі UTF-16 міститиметься якась помилка, файл буде
    пропущено.

    Якщо використано параметр "-u", файл результатів буде записано у тому
    самому кодуванні UTF-16, що і початковий файл. Використання параметра
    Option "-u" запобігає перетворенню даних у кодування UTF-8.

    У dos2unix та unix2dos не передбачено параметра для перетворення даних у
    кодуванні UTF-8 на дані у кодуванні UTF-16.

    Режим перетворення ISO та 7-бітовий режим не працюють для файлів UTF-16.

  Позначка порядку байтів
    У Windows до текстових файлів у кодуванні Unicode типово дописується
    позначка порядку байтів (BOM), оскільки багато програм Windows (зокрема
    Notepad) додають таку позначку автоматично. Див. також
    <https://en.wikipedia.org/wiki/Byte_order_mark>.

    У Unix файли у кодуванні Unicode типово не містять позначки порядку
    байтів. Вважається, що кодуванням текстових файлів є те кодування, яке
    визначається поточною локаллю.

    Програма dos2unix може визначити, чи є кодуванням файла UTF-16, лише
    якщо у файлі міститься позначка порядку байтів. Якщо у файлі, де
    використано кодування UTF-16, немає такої позначки, dos2unix вважатиме
    такий файл двійковим (бінарним).

    Для перетворення файлів UTF-16 без позначки порядку байтів скористайтеся
    параметром "-ul" або "-ub".

    Типово dos2unix не записує до файлів результатів перетворення позначки
    порядку байтів (BOM). Якщо використано параметр "-b", dos2unix запише до
    файла результатів BOM, якщо BOM була у файлі початкових даних.

    Типово unix2dos записує позначку порядку байтів (BOM) до файла
    результатів, якщо BOM є у початковому файлі. Скористайтеся параметром
    "-r", щоб вилучити BOM.

    Dos2unix та unix2dos завжди записують до файла результатів позначку
    порядку байтів (BOM), якщо використано параметр "-m".

  Назви файлів у Unicode у Windows
    У dos2unix передбачено підтримку читання і запису назв файлів Unicode у
    командному рядку Windows. Це означає, що dos2unix може відкривати файли,
    у назвах яких є символи, які не є частиною типової системної кодової
    сторінки ANSI. Щоб визначити, чи зібрано dos2unix для Windows з
    підтримкою назв файлів у кодуванні Unicode, скористайтеся командою
    "dos2unix -V".

    Із показом назв файлів у кодуванні Unicode у консолі Windows пов’язано
    декілька проблем. Див. параметр "-D", "--display-enc". Назви файлів може
    бути некоректно показано у консолі, але запис цих назв відбуватиметься
    належним чином.

  Приклади для Unicode
    Перетворити дані з Windows UTF-16 (з позначкою порядку байтів (BOM)) у
    формат Unix UTF-8:

        dos2unix -n in.txt out.txt

    Перетворити дані у форматі Windows UTF-16LE (без BOM) на дані у форматі
    UTF-8 Unix:

        dos2unix -ul -n in.txt out.txt

    Перетворити дані у кодуванні UTF-8 Unix на дані у кодуванні Windows
    UTF-8 без BOM:

        unix2dos -m -n in.txt out.txt

    Перетворити дані у кодуванні UTF-8 Unix на дані у кодуванні Windows
    UTF-16:

        unix2dos < in.txt | iconv -f UTF-8 -t UTF-16 > out.txt

GB18030
    GB18030 є китайським урядовим стандартом. Підтримка обов’язкової
    підмножини стандарту GB18030 є неодмінною вимогою до будь-яких
    програмних продуктів, які продаються у Китаї. Див. також
    <https://en.wikipedia.org/wiki/GB_18030>.

    Кодування GB18030 є повністю сумісним із Unicode. Його можна розглядати
    як формат перетворення unicode. Подібно до UTF-8, GB18030 є сумісним із
    ASCII. GB18030 також є сумісним із кодовою сторінкою Windows 936, яку ще
    називають GBK.

    У Unix/Linux файли UTF-16 буде перетворено до кодування GB18030, якщо
    кодуванням локалі є GB18030. Зауважте, що це спрацює, лише якщо
    підтримку локалі передбачено у системі. Щоб отримати список
    підтримуваних локалей, скористайтеся командою "locale -a".

    У Windows для перетворення файлів UTF-16 на файли GB18030 слід вказати
    параметр "-gb".

    У файлах у кодуванні GB18030 може міститися позначка порядку байтів, так
    само, як у файлах у кодуванні Unicode.

ПРИКЛАДИ
    Прочитати вхідні дані зі стандартного джерела (stdin) і записати
    результат до стандартного виведення (stdout):

        dos2unix < a.txt
        cat a.txt | dos2unix

    Перетворити дані у a.txt і замістити цей файл. Перетворити дані у b.txt
    і замістити цей файл:

        dos2unix a.txt b.txt
        dos2unix -o a.txt b.txt

    Перетворити дані у a.txt і замістити файл у режимі перетворення ascii:

        dos2unix a.txt

    Перетворити дані у a.txt і замістити файл у режимі перетворення ascii.
    Перетворити дані у b.txt і замістити цей файл у режимі 7-бітового
    перетворення:

        dos2unix a.txt -c 7bit b.txt
        dos2unix -c ascii a.txt -c 7bit b.txt
        dos2unix -ascii a.txt -7 b.txt

    Перетворити файл a.txt з формату Mac на формат Unix:

        dos2unix -c mac a.txt
        mac2unix a.txt

    Перетворити файл a.txt з формату Unix на формат Mac:

        unix2dos -c mac a.txt
        unix2mac a.txt

    Перетворити дані у a.txt, замістити цей файл і зберегти часову позначку
    початкового файла:

        dos2unix -k a.txt
        dos2unix -k -o a.txt

    Перетворити дані у файлі a.txt і записати результати до файла e.txt:

        dos2unix -n a.txt e.txt

    Перетворити дані у файлі a.txt і записати результати до файла e.txt.
    Скопіювати часову позначку файла a.txt для файла e.txt:

        dos2unix -k -n a.txt e.txt

    Перетворити дані у a.txt і замістити цей файл. Перетворити дані у b.txt
    і записати результат до e.txt:

        dos2unix a.txt -n b.txt e.txt
        dos2unix -o a.txt -n b.txt e.txt

    Перетворити дані у c.txt і записати результати до e.txt. Перетворити
    дані у a.txt і замістити ними цей файл. Перетворити дані у b.txt і
    замістити ними цей файл. Перетворити дані у d.txt і записати результати
    до f.txt:

        dos2unix -n c.txt e.txt -o a.txt b.txt -n d.txt f.txt

РЕКУРСИВНЕ ПЕРЕТВОРЕННЯ
    У оболонці UNIX можна скористатися командами find(1) і xargs(1) для
    запуску dos2unix рекурсивно для усіх текстових файлів у ієрархії
    каталогів. Наприклад, щоб виконати перетворення усіх файлів .txt у
    структурі підкаталогів поточного каталогу, віддайте таку команду:

        find . -name '*.txt' -print0 |xargs -0 dos2unix

    Параметр find(1) "-print0" і відповідний параметр xargs(1) -0 потрібні,
    якщо у назва файлів є пробіли або лапки. Інакше, ці параметри можна
    пропустити. Іншим варіантом дій є використання find(1) з параметром
    "-exec":

        find . -name '*.txt' -exec dos2unix {} \;

    У командному рядку Windows можна скористатися такою командою:

        for /R %G in (*.txt) do dos2unix "%G"

    Користувачі PowerShell можуть скористатися такою командою у Windows
    PowerShell:

        get-childitem -path . -filter '*.txt' -recurse | foreach-object {dos2unix $_.Fullname}

ЛОКАЛІЗАЦІЯ
    LANG
        Основна мова визначається за допомогою змінної середовища LANG.
        Значення змінної LANG складається з декількох частин. Перша частина
        записується малими літерами і визначає код мови. Друга частина є
        необов’язковою, визначає код країни і записується прописними
        літерами, відокремлюється від першої частини символом
        підкреслювання. Передбачено також необов’язкову третю частину:
        кодування. Ця частина відокремлюється від другої частини крапкою.
        Ось декілька прикладів для командних оболонок стандартного типу
        POSIX:

            export LANG=uk               українська
            export LANG=uk_UA            українська, Україна
            export LANG=ru_UA            російська, Україна
            export LANG=es_ES            іспанська, Іспанія
            export LANG=es_MX            іспанська, Мексика
            export LANG=en_US.iso88591   англійська, США, кодування Latin-1
            export LANG=en_GB.UTF-8      англійська, Великобританія, кодування UTF-8

        Повний список мов та кодів країн наведено у підручнику з gettext:
        <https://www.gnu.org/software/gettext/manual/html_node/Usual-Languag
        e-Codes.html>

        У системах Unix для отримання даних щодо локалі можна скористатися
        командою locale(1).

    LANGUAGE
        За допомогою змінної середовища LANGUAGE ви можете вказати список
        пріоритетності мов. Записи у списку слід відокремлювати двокрапками.
        Програма dos2unix надає перевагу LANGUAGE над LANG. Наприклад, перша
        голландська, далі німецька: "LANGUAGE=nl:de". Спочатку вам слід
        увімкнути локалізацію, встановивши для змінної LANG (або LC_ALL)
        значення, відмінне від «C». Далі ви зможете використовувати список
        пріоритетності мов за допомогою змінної LANGUAGE. Додаткові
        відомості можна знайти у підручнику з gettext:
        <https://www.gnu.org/software/gettext/manual/html_node/The-LANGUAGE-
        variable.html>

        Якщо вами буде вибрано мову, перекладу якою немає, буде показано
        стандартні повідомлення англійською мовою.

    DOS2UNIX_LOCALEDIR
        Змінну LOCALEDIR, встановлену під час збирання програми, можна
        змінити за допомогою змінної середовища DOS2UNIX_LOCALEDIR.
        LOCALEDIR використовується для пошуку файлів перекладів. Типовим
        значенням у системах GNU є "/usr/local/share/locale". Переглянути
        поточне значення змінної LOCALEDIR можна переглянути за допомогою
        параметра --version.

        Приклад (командна оболонка POSIX):

            export DOS2UNIX_LOCALEDIR=$HOME/share/locale

ПОВЕРНУТЕ ЗНАЧЕННЯ
    Якщо завдання вдасться успішно виконати, програма поверне нульовий код
    виходу. Якщо станеться системна помилка, буде повернуто код цієї
    помилки. Якщо станеться якась інша помилка, буде повернуто код 1.

    У режимі без повідомлень повернутим значенням завжди буде нуль, якщо
    вказано правильні параметри командного рядка.

СТАНДАРТИ
    <https://en.wikipedia.org/wiki/Text_file>

    <https://uk.wikipedia.org/wiki/Carriage_return>

    <https://uk.wikipedia.org/wiki/Newline>

    <https://uk.wikipedia.org/wiki/Unicode>

АВТОРИ
    Benjamin Lin - <<EMAIL>>, Bernd Johannes Wuebben (режим
    mac2unix) - <<EMAIL>>, Christian Wurll (додатковий новий рядок)
    - <<EMAIL>>, Erwin Waterlander - <<EMAIL>>
    (супровідник)

    Сторінка проєкту: <https://waterlan.home.xs4all.nl/dos2unix.html>

    Сторінка на SourceForge: <https://sourceforge.net/projects/dos2unix/>

ТАКОЖ ПЕРЕГЛЯНЬТЕ
    file(1) find(1) iconv(1) locale(1) xargs(1)

