.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_get_master_secret" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_get_master_secret \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_session_get_master_secret(gnutls_session_t " session ", gnutls_datum_t * " secret ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_datum_t * secret" 12
the session's master secret
.SH "DESCRIPTION"
This function returns pointers to the master secret
used in the TLS session. The pointers are not to be modified or deallocated.

This function is only applicable under TLS 1.2 or earlier versions.
.SH "SINCE"
3.5.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
