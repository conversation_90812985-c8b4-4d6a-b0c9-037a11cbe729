#!/usr/bin/env bash
#
#   repo-add - add a package to a given repo database file
#   repo-remove - remove a package entry from a given repo database file
#
# <AUTHOR> <EMAIL>
#
#   This program is free software; you can redistribute it and/or modify
#   it under the terms of the GNU General Public License as published by
#   the Free Software Foundation; either version 2 of the License, or
#   (at your option) any later version.
#
#   This program is distributed in the hope that it will be useful,
#   but WITHOUT ANY WARRANTY; without even the implied warranty of
#   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#   GNU General Public License for more details.
#
#   You should have received a copy of the GNU General Public License
#   along with this program.  If not, see <http://www.gnu.org/licenses/>.

shopt -s extglob

# gettext initialization
export TEXTDOMAIN='pacman-scripts'
export TEXTDOMAINDIR='/usr/share/locale'

declare -r myver='6.1.0'
declare -r confdir='/etc'

MAKEPKG_LIBRARY=${MAKEPKG_LIBRARY:-'/usr/share/makepkg'}

QUIET=0
ONLYADDNEW=0
RMEXISTING=0
SIGN=0
KEY=0
VERIFY=0
REPO_DB_FILE=
REPO_DB_PREFIX=
REPO_DB_SUFFIX=
LOCKFILE=
CLEAN_LOCK=0
USE_COLOR='y'
PREVENT_DOWNGRADE=0
INCLUDE_SIGS=0
DB_MODIFIED=0
DB_MISSING=0

# Import libmakepkg
source "$MAKEPKG_LIBRARY"/util/compress.sh
source "$MAKEPKG_LIBRARY"/util/message.sh
source "$MAKEPKG_LIBRARY"/util/parseopts.sh
source "$MAKEPKG_LIBRARY"/utils_fixed_path.sh

# ensure we have a sane umask set
umask 0022

# print usage instructions
usage() {
	cmd=${0##*/}
	printf -- "%s (pacman) %s\n\n" "$cmd" "$myver"
	if [[ $cmd == "repo-add" ]] ; then
		printf -- "$(gettext "Usage: repo-add [options] <path-to-db> <package> ...\n")"
		printf -- "\n"
		printf -- "$(gettext "\
repo-add will update a package database by reading a package file.\n\
Multiple packages to add can be specified on the command line.\n")"
		printf -- "\n"
		printf -- "$(gettext "Options:\n")"
		printf -- "$(gettext "  -n, --new         only add packages that are not already in the database\n")"
		printf -- "$(gettext "  -R, --remove      remove old package file from disk after updating database\n")"
		printf -- "$(gettext "  -p, --prevent-downgrade  do not add package to database if a newer version is already present\n")"
	elif [[ $cmd == "repo-remove" ]] ; then
		printf -- "$(gettext "Usage: repo-remove [options] <path-to-db> <packagename> ...\n")"
		printf -- "\n"
		printf -- "$(gettext "\
repo-remove will update a package database by removing the package name\n\
specified on the command line from the given repo database. Multiple\n\
packages to remove can be specified on the command line.\n")"
		printf -- "\n"
		printf -- "$(gettext "Options:\n")"
	else
		printf -- "$(gettext "Please move along, there is nothing to see here.\n")"
		return
	fi
	printf -- "$(gettext "  --nocolor         turn off color in output\n")"
	printf -- "$(gettext "  -q, --quiet       minimize output\n")"
	printf -- "$(gettext "  -s, --sign        sign database with GnuPG after update\n")"
	printf -- "$(gettext "  -k, --key <key>   use the specified key to sign the database\n")"
	printf -- "$(gettext "  -v, --verify      verify database's signature before update\n")"
	printf -- "$(gettext "\n\
See %s(8) for more details and descriptions of the available options.\n")" $cmd
	printf "\n"
	if [[ $cmd == "repo-add" ]] ; then
		printf -- "$(gettext "Example:  repo-add /path/to/repo.db.tar.gz pacman-3.0.0-1-i686.pkg.tar.gz\n")"
	elif [[ $cmd == "repo-remove" ]] ; then
		printf -- "$(gettext "Example:  repo-remove /path/to/repo.db.tar.gz kernel26\n")"
	fi
}

version() {
	cmd=${0##*/}
	printf "%s (pacman) %s\n\n" "$cmd" "$myver"
	printf -- "Copyright (c) 2006-2024 Pacman Development Team <<EMAIL>>.\n"
	printf '\n'
	printf -- "$(gettext "\
This is free software; see the source for copying conditions.\n\
There is NO WARRANTY, to the extent permitted by law.\n")"
}


# format a metadata entry
#		arg1 - Entry name
#		...  - value(s)
format_entry() {
	local field=$1; shift

	if [[ $1 ]]; then
		printf '%%%s%%\n' "$field"
		printf '%s\n' "$@"
		printf '\n'
	fi
}

find_pkgentry() {
	local pkgname=$1
	local pkgentry

	for pkgentry in "$tmpdir/db/$pkgname"*; do
		name=${pkgentry##*/}
		if [[ ${name%-*-*} = "$pkgname" ]]; then
			echo $pkgentry
			return 0
		fi
	done
	return 1
}

check_gpg() {
	if ! type -p gpg >/dev/null; then
		error "$(gettext "Cannot find the gpg binary! Is GnuPG installed?")"
		exit 1 # $E_MISSING_PROGRAM
	fi

	if (( ! VERIFY )); then
		if ! gpg --list-secret-key ${GPGKEY:+"$GPGKEY"} &>/dev/null; then
			if [[ ! -z $GPGKEY ]]; then
				error "$(gettext "The key ${GPGKEY} does not exist in your keyring.")"
			elif (( ! KEY )); then
				error "$(gettext "There is no key in your keyring.")"
			fi
			exit 1
		fi
	fi
}

# sign the package database once repackaged
create_signature() {
	(( ! SIGN )) && return
	local dbfile=$1
	local ret=0
	msg "$(gettext "Signing database '%s'...")" "${dbfile##*/.tmp.}"

	local SIGNWITHKEY=()
	if [[ -n $GPGKEY ]]; then
		SIGNWITHKEY=(-u "${GPGKEY}")
	fi
	gpg --detach-sign --use-agent --no-armor "${SIGNWITHKEY[@]}" "$dbfile" &>/dev/null || ret=$?

	if (( ! ret )); then
		msg2 "$(gettext "Created signature file '%s'")" "${dbfile##*/.tmp.}.sig"
	else
		warning "$(gettext "Failed to sign package database file '%s'")" "${dbfile##*/.tmp.}"
	fi
}

# verify the existing package database signature
verify_signature() {
	(( ! VERIFY )) && return
	local dbfile=$1
	local ret=0
	msg "$(gettext "Verifying database signature...")"

	if [[ ! -f $dbfile.sig ]]; then
		warning "$(gettext "No existing signature found, skipping verification.")"
		return
	fi
	gpg --verify "$dbfile.sig" || ret=$?
	if (( ! ret )); then
		msg2 "$(gettext "Database signature file verified.")"
	else
		error "$(gettext "Database signature was NOT valid!")"
		exit 1
	fi
}

verify_repo_extension() {
	local junk=()
	if [[ $1 = *.db.tar* ]] && get_compression_command "$1" junk; then
		return 0
	fi

	error "$(gettext "'%s' does not have a valid database archive extension.")" "$1"
	exit 1
}

# write an entry to the pacman database
#   arg1 - path to package
db_write_entry() {
	# blank out all variables
	local pkgfile=$1
	local -a _groups _licenses _replaces _depends _conflicts _provides \
		_optdepends _makedepends _checkdepends
	local pkgname pkgbase pkgver pkgdesc csize size url arch builddate packager \
		sha256sum pgpsig pgpsigsize

	# read info from the zipped package
	local line var val
	while read -r line; do
		[[ ${line:0:1} = '#' ]] && continue
		IFS=' =' read -r var val < <(printf '%s\n' "$line")

		# normalize whitespace with an extglob
		declare "$var=${val//+([[:space:]])/ }"
		case $var in
			group) _groups+=("$group") ;;
			license) _licenses+=("$license") ;;
			replaces) _replaces+=("$replaces") ;;
			depend) _depends+=("$depend") ;;
			conflict) _conflicts+=("$conflict") ;;
			provides) _provides+=("$provides") ;;
			optdepend) _optdepends+=("$optdepend") ;;
			makedepend) _makedepends+=("$makedepend") ;;
			checkdepend) _checkdepends+=("$checkdepend") ;;
		esac
	done< <(bsdtar -xOqf "$pkgfile" .PKGINFO)

	# ensure $pkgname and $pkgver variables were found
	if [[ -z $pkgname || -z $pkgver ]]; then
		error "$(gettext "Invalid package file '%s'.")" "$pkgfile"
		return 1
	fi

	if [[ -d $tmpdir/db/$pkgname-$pkgver ]]; then
		warning "$(gettext "An entry for '%s' already existed")" "$pkgname-$pkgver"
		if (( ONLYADDNEW )); then
			return 0
		fi
	else
		pkgentry=$(find_pkgentry "$pkgname")
		if [[ -n $pkgentry ]]; then

			local version=$(sed -n '/^%VERSION%$/ {n;p;q}' "$pkgentry/desc")
			if (( $(vercmp "$version" "$pkgver") > 0 )); then
				warning "$(gettext "A newer version for '%s' is already present in database")" "$pkgname"
				if (( PREVENT_DOWNGRADE )); then
					return 0
				fi
			fi
			if (( RMEXISTING )); then
				local oldfilename="$(sed -n '/^%FILENAME%$/ {n;p;q;}' "$pkgentry/desc")"
				local oldfile="$(dirname "$1")/$oldfilename"
			fi
		fi
	fi

	# compute base64'd PGP signature
	if (( INCLUDE_SIGS )) && [[ -f "$pkgfile.sig" ]]; then
		if grep -q 'BEGIN PGP SIGNATURE' "$pkgfile.sig"; then
			error "$(gettext "Cannot use armored signatures for packages: %s")" "$pkgfile.sig"
			return 1
		fi
		pgpsigsize=$(wc -c < "$pkgfile.sig")
		if (( pgpsigsize > 16384 )); then
			error "$(gettext "Invalid package signature file '%s'.")" "$pkgfile.sig"
			return 1
		fi
		msg2 "$(gettext "Adding package signature...")"
		pgpsig=$(base64 "$pkgfile.sig" | tr -d '\n')
	fi

	csize=$(wc -c < "$pkgfile")

	# compute checksums
	msg2 "$(gettext "Computing checksums...")"
	sha256sum=$(sha256sum "$pkgfile")
	sha256sum=${sha256sum%% *}

	# remove an existing entry if it exists, ignore failures
	db_remove_entry "$pkgname"

	# create package directory
	pushd "$tmpdir/db" >/dev/null
	mkdir "$pkgname-$pkgver"
	pushd "$pkgname-$pkgver" >/dev/null

	# create desc entry
	msg2 "$(gettext "Creating '%s' db entry...")" 'desc'
	{
		format_entry "FILENAME"  "${1##*/}"
		format_entry "NAME"      "$pkgname"
		format_entry "BASE"      "$pkgbase"
		format_entry "VERSION"   "$pkgver"
		format_entry "DESC"      "$pkgdesc"
		format_entry "GROUPS"    "${_groups[@]}"
		format_entry "CSIZE"     "$csize"
		format_entry "ISIZE"     "$size"

		# add checksums
		format_entry "SHA256SUM" "$sha256sum"

		# add PGP sig
		format_entry "PGPSIG"    "$pgpsig"

		format_entry "URL"       "$url"
		format_entry "LICENSE"   "${_licenses[@]}"
		format_entry "ARCH"      "$arch"
		format_entry "BUILDDATE" "$builddate"
		format_entry "PACKAGER"  "$packager"
		format_entry "REPLACES"  "${_replaces[@]}"
		format_entry "CONFLICTS" "${_conflicts[@]}"
		format_entry "PROVIDES"  "${_provides[@]}"

		format_entry "DEPENDS" "${_depends[@]}"
		format_entry "OPTDEPENDS" "${_optdepends[@]}"
		format_entry "MAKEDEPENDS" "${_makedepends[@]}"
		format_entry "CHECKDEPENDS" "${_checkdepends[@]}"
	} >'desc'

	popd >/dev/null
	popd >/dev/null

	# copy updated package entry into "files" database
	cp -a "$tmpdir/db/$pkgname-$pkgver" "$tmpdir/files/$pkgname-$pkgver"

	# create files file
	msg2 "$(gettext "Creating '%s' db entry...")" 'files'
	local files_path="$tmpdir/files/$pkgname-$pkgver/files"
	echo "%FILES%" >"$files_path"
	bsdtar --exclude='^.*' -tf "$pkgfile" | LC_ALL=C sort -u >>"$files_path"

	if (( RMEXISTING )); then
		msg2 "$(gettext "Removing old package file '%s'")" "$oldfilename"
		rm -f ${oldfile} ${oldfile}.sig
	fi

	DB_MODIFIED=1

	return 0
} # end db_write_entry

# remove existing entries from the DB
#   arg1 - package name
db_remove_entry() {
	local pkgname=$1
	local notfound=1
	local pkgentry=$(find_pkgentry "$pkgname")
	while [[ -n $pkgentry ]]; do
		notfound=0

		msg2 "$(gettext "Removing existing entry '%s'...")" \
		"${pkgentry##*/}"
		rm -rf "$pkgentry"

		# remove entries in "files" database
		local filesentry=$(echo "$pkgentry" | sed 's/\(.*\)\/db\//\1\/files\//')
		rm -rf "$filesentry"

		DB_MODIFIED=1

		pkgentry=$(find_pkgentry "$pkgname")
	done
	return $notfound
} # end db_remove_entry

elephant() {
	case $(( RANDOM % 2 )) in
		0) printf '%s\n' "H4sIAL3qBE4CAyWLwQ3AMAgD/0xh5UPzYiFUMgjq7LUJsk7yIQNAQTAikFUDnqkr" \
		                 "OQFOUm0Wd9pHCi13ONjBpVdqcWx+EdXVX4vXvGv5cgztB9+fJxZ7AAAA"
		;;

		1) printf '%s\n' "H4sIAJVWBU4CA21RMQ7DIBDbeYWrDgQJ7rZ+IA/IB05l69alcx5fc0ASVXUk4jOO" \
		                 "7yAAUWtorygwJ4hlMii0YkJKKRKGvsMsiykl1SalvrMD1gUXyXRkGZPx5OPft81K" \
		                 "tNAiAjyGjYO47h1JjizPkJrCWbK/4C+uLkT7bzpGc7CT9bmOzNSW5WLSO5vexjmH" \
		                 "ZL9JFFZeAa0a2+lKjL2anpYfV+0Zx9LJ+/MC8nRayuDlSNy2rfAPibOzsiWHL0jL" \
		                 "SsjFAQAA"
		;;
	esac | base64 -d | gzip -d
}

prepare_repo_db() {
	local repodir dbfile

	# ensure the path to the DB exists; $LOCKFILE is always an absolute path
	repodir=${LOCKFILE%/*}/

	if [[ ! -d $repodir ]]; then
		error "$(gettext "%s does not exist or is not a directory.")" "$repodir"
		exit 1
	fi

	# check lock file
	if ( set -o noclobber; echo "$$" > "$LOCKFILE") 2> /dev/null; then
		CLEAN_LOCK=1
	else
		error "$(gettext "Failed to acquire lockfile: %s.")" "$LOCKFILE"
		[[ -f $LOCKFILE ]] && error "$(gettext "Held by process %s")" "$(cat "$LOCKFILE")"
		exit 1
	fi

	for repo in "db" "files"; do
		dbfile=${repodir}/$REPO_DB_PREFIX.$repo.$REPO_DB_SUFFIX

		if [[ -f $dbfile ]]; then
			# there are two situations we can have here:
			# a DB with some entries, or a DB with no contents at all.
			if ! bsdtar -tqf "$dbfile" '*/desc' >/dev/null 2>&1; then
				# check empty case
				if [[ -n $(bsdtar -tqf "$dbfile" '*' 2>/dev/null) ]]; then
					error "$(gettext "Repository file '%s' is not a proper pacman database.")" "$dbfile"
					exit 1
				fi
			fi
			verify_signature "$dbfile"
			msg "$(gettext "Extracting %s to a temporary location...")" "${dbfile##*/}"
			bsdtar -xf "$dbfile" -C "$tmpdir/$repo"
		else
			case $cmd in
				repo-remove)
					# only a missing "db" database is currently an error
					# TODO: remove if statement
					if [[ $repo == "db" ]]; then
						error "$(gettext "Repository file '%s' was not found.")" "$dbfile"
						exit 1
					fi
					;;
				repo-add)
					# check if the file can be created (write permission, directory existence, etc)
					if ! touch "$dbfile"; then
						error "$(gettext "Repository file '%s' could not be created.")" "$dbfile"
						exit 1
					fi
					rm -f "$dbfile"
					# mark the db for unconditional update
					DB_MISSING=1
					;;
			esac
		fi
	done
}

add() {
	if [[ ! -f $1 ]]; then
		error "$(gettext "File '%s' not found.")" "$1"
		return 1
	fi

	pkgfile=$1
	if ! bsdtar -tqf "$pkgfile" .PKGINFO >/dev/null 2>&1; then
		error "$(gettext "'%s' is not a package file, skipping")" "$pkgfile"
		return 1
	fi

	msg "$(gettext "Adding package '%s'")" "$pkgfile"

	db_write_entry "$pkgfile"
}

remove() {
	pkgname=$1
	msg "$(gettext "Searching for package '%s'...")" "$pkgname"

	if ! db_remove_entry "$pkgname"; then
		error "$(gettext "Package matching '%s' not found.")" "$pkgname"
		return 1
	fi

	return 0
}

rotate_db() {
	dirname=${LOCKFILE%/*}

	pushd "$dirname" >/dev/null

	for repo in "db" "files"; do
		filename=${REPO_DB_PREFIX}.${repo}.${REPO_DB_SUFFIX}
		tempname=$dirname/.tmp.$filename

		# hardlink or move the previous version of the database and signature to .old
		# extension as a backup measure
		if [[ -f $filename ]]; then
			ln -f "$filename" "$filename.old" 2>/dev/null || \
				mv -f "$filename" "$filename.old"

			if [[ -f $filename.sig ]]; then
				ln -f "$filename.sig" "$filename.old.sig" 2>/dev/null || \
					mv -f "$filename.sig" "$filename.old.sig"
			else
				rm -f "$filename.old.sig"
			fi
		fi

		# rotate the newly-created database and signature into place
		mv "$tempname" "$filename"
		if [[ -f $tempname.sig ]]; then
			mv "$tempname.sig" "$filename.sig"
		fi

		dblink=${filename%.tar*}
		rm -f "$dblink" "$dblink.sig"
		ln -s "$filename" "$dblink" 2>/dev/null || \
			ln "$filename" "$dblink" 2>/dev/null || \
			cp "$filename" "$dblink"
		if [[ -f "$filename.sig" ]]; then
			ln -s "$filename.sig" "$dblink.sig" 2>/dev/null || \
				ln "$filename.sig" "$dblink.sig" 2>/dev/null || \
				cp "$filename.sig" "$dblink.sig"
		fi
	done

	popd >/dev/null
}

create_db() {
	# $LOCKFILE is already guaranteed to be absolute so this is safe
	dirname=${LOCKFILE%/*}

	for repo in "db" "files"; do
		filename=${REPO_DB_PREFIX}.${repo}.${REPO_DB_SUFFIX}
		# this ensures we create it on the same filesystem, making moves atomic
		tempname=$dirname/.tmp.$filename

		pushd "$tmpdir/$repo" >/dev/null
		local files=(*)
		if [[ ${files[*]} = '*' ]]; then
			# we have no packages remaining? zip up some emptyness
			warning "$(gettext "No packages remain, creating empty database.")"
			files=(-T /dev/null)
		fi
		bsdtar --uid 1 --uname root --gid 1 --gname root -cf - "${files[@]}" | compress_as "$filename" > "$tempname"
		popd >/dev/null

		create_signature "$tempname"
	done
}

trap_exit() {
	# unhook all traps to avoid race conditions
	trap '' EXIT TERM HUP QUIT INT ERR

	echo
	error "$@"
	clean_up 1
}

clean_up() {
	local exit_code=${1:-$?}

	# unhook all traps to avoid race conditions
	trap '' EXIT TERM HUP QUIT INT ERR

	[[ -d $tmpdir ]] && rm -rf "$tmpdir"
	(( CLEAN_LOCK )) && [[ -f $LOCKFILE ]] && rm -f "$LOCKFILE"

	exit $exit_code
}


# PROGRAM START

# determine whether we have gettext; make it a no-op if we do not
if ! type -p gettext &>/dev/null; then
	gettext() {
		printf "%s\n" "$@"
	}
else
	gettext() {
		/usr/bin/gettext "$@"
	}
fi

case $1 in
	-h|--help) usage; exit 0;;
	-V|--version) version; exit 0;;
esac

# figure out what program we are
cmd=${0##*/}
if [[ $cmd == "repo-elephant" ]]; then
	elephant
	exit 0
fi

if [[ $cmd != "repo-add" && $cmd != "repo-remove" ]]; then
	error "$(gettext "Invalid command name '%s' specified.")" "$cmd"
	exit 1
fi

tmpdir=$(mktemp -d "${TMPDIR:-/tmp}/repo-tools.XXXXXXXXXX") || {
	error "$(gettext "Cannot create temp directory for database building.")";
	exit 1; }

for repo in "db" "files"; do
	mkdir "$tmpdir/$repo"
done

trap 'clean_up' EXIT
for signal in TERM HUP QUIT; do
	trap "trap_exit \"$(gettext "%s signal caught. Exiting...")\" \"$signal\"" "$signal"
done
trap 'trap_exit "$(gettext "Aborted by user! Exiting...")"' INT
trap 'trap_exit "$(gettext "An unknown error has occurred. Exiting...")"' ERR


OPT_SHORT="knpqRsv"
OPT_LONG=('include-sigs' 'key' 'new' 'nocolor' 'quiet' 'prevent-downgrade' 'remove'
          'sign' 'verify')
if ! parseopts "$OPT_SHORT" "${OPT_LONG[@]}" -- "$@"; then
	exit 1 # E_INVALID_OPTION
fi
set -- "${OPTRET[@]}"
unset OPT_SHORT OPT_LONG OPTRET

declare -a args
# parse arguments
while true; do
	case $1 in
		-q|--quiet) QUIET=1;;
		-n|--new) ONLYADDNEW=1;;
		-R|--remove) RMEXISTING=1;;
		--nocolor) USE_COLOR='n';;
		-s|--sign)
			SIGN=1
			;;
		-k|--key)
			KEY=1
			shift
			GPGKEY=$1
			;;
		-v|--verify)
			VERIFY=1
			;;
		-p|--prevent-downgrade)
			PREVENT_DOWNGRADE=1
			;;
		--include-sigs)
			INCLUDE_SIGS=1
			;;
		--)
			shift
			break
			;;
	esac
	shift
done

args=("$@")

# check if messages are to be printed using color
if [[ -t 2 && $USE_COLOR != "n" ]]; then
	colorize
else
	unset ALL_OFF BOLD BLUE GREEN RED YELLOW
fi

REPO_DB_FILE=${args[0]}
if [[ -z $REPO_DB_FILE ]]; then
	usage
	exit 1
fi

if [[ $REPO_DB_FILE == /* ]]; then
	LOCKFILE=$REPO_DB_FILE.lck
else
	LOCKFILE=$PWD/$REPO_DB_FILE.lck
fi

verify_repo_extension "$REPO_DB_FILE"

REPO_DB_PREFIX=${REPO_DB_FILE##*/}
REPO_DB_PREFIX=${REPO_DB_PREFIX%.db.*}
REPO_DB_SUFFIX=${REPO_DB_FILE##*.db.}

if (( SIGN || VERIFY )); then
	check_gpg
fi

if (( VERIFY && ${#args[@]} == 1 )); then
	for repo in "db" "files"; do
		dbfile=${repodir}/$REPO_DB_PREFIX.$repo.$REPO_DB_SUFFIX

		if [[ -f $dbfile ]]; then
			verify_signature "$dbfile"
		fi
	done
	exit 0
fi

prepare_repo_db

fail=0
for arg in "${args[@]:1}"; do
	case $cmd in
		repo-add) add "$arg" ;;
		repo-remove) remove "$arg" ;;
	esac || fail=1
done

# if the whole operation was a success, re-zip and rotate databases
if (( fail )); then
	msg "$(gettext "Package database was not modified due to errors.")"
	exit 1
fi

if (( DB_MODIFIED || DB_MISSING )); then
	msg "$(gettext "Creating updated database file '%s'")" "$REPO_DB_FILE"
	create_db
	rotate_db
else
	msg "$(gettext "No changes made to package database.")"
fi

exit 0
