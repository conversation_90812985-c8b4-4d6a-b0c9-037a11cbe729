# Time-stamp: "2014-01-24 23:59:56 MST <EMAIL>"
$Text::Unidecode::Char[0x4e] = [
'Yi ', 'Ding ', '<PERSON><PERSON> ', '<PERSON> ', 'Shang ', '<PERSON><PERSON> ', qq{[?] }, '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ',
'<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', 'Qi<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON>g ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ',
'Qiu ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', 'Ji<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', 'Ji<PERSON> ',
'<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ',
'<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', 'Ji<PERSON> ', 'Ji<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', qq{[?] }, '<PERSON>hi ', '<PERSON> ', '<PERSON>ha ', '<PERSON> ', '<PERSON>a ',
'Le ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON>g ', '<PERSON>ao ', '<PERSON> ', '<PERSON>uai ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', qq{[?] }, '<PERSON>e ', '<PERSON>u ', '<PERSON> ', '<PERSON> ',
'<PERSON> ', '<PERSON>ang ', '<PERSON>ai ', '<PERSON>u ', '<PERSON> ', qq{[?] }, 'Shu ', 'Twul ', 'Shi ', 'Ji ', 'Nang ', 'Jia ', 'Kel ', 'Shi ', qq{[?] }, 'Ol ',
'Mai ', 'Luan ', 'Cal ', 'Ru ', 'Xue ', 'Yan ', 'Fu ', 'Sha ', 'Na ', 'Gan ', 'Sol ', 'El ', 'Cwul ', qq{[?] }, 'Gan ', 'Chi ',
'Gui ', 'Gan ', 'Luan ', 'Lin ', 'Yi ', 'Jue ', 'Liao ', 'Ma ', 'Yu ', 'Zheng ', 'Shi ', 'Shi ', 'Er ', 'Chu ', 'Yu ', 'Yu ',
'Yu ', 'Yun ', 'Hu ', 'Qi ', 'Wu ', 'Jing ', 'Si ', 'Sui ', 'Gen ', 'Gen ', 'Ya ', 'Xie ', 'Ya ', 'Qi ', 'Ya ', 'Ji ',
'Tou ', 'Wang ', 'Kang ', 'Ta ', 'Jiao ', 'Hai ', 'Yi ', 'Chan ', 'Heng ', 'Mu ', qq{[?] }, 'Xiang ', 'Jing ', 'Ting ', 'Liang ', 'Xiang ',
'Jing ', 'Ye ', 'Qin ', 'Bo ', 'You ', 'Xie ', 'Dan ', 'Lian ', 'Duo ', 'Wei ', 'Ren ', 'Ren ', 'Ji ', 'La ', 'Wang ', 'Yi ',
'Shi ', 'Ren ', 'Le ', 'Ding ', 'Ze ', 'Jin ', 'Pu ', 'Chou ', 'Ba ', 'Zhang ', 'Jin ', 'Jie ', 'Bing ', 'Reng ', 'Cong ', 'Fo ',
'San ', 'Lun ', 'Sya ', 'Cang ', 'Zi ', 'Shi ', 'Ta ', 'Zhang ', 'Fu ', 'Xian ', 'Xian ', 'Tuo ', 'Hong ', 'Tong ', 'Ren ', 'Qian ',
'Gan ', 'Yi ', 'Di ', 'Dai ', 'Ling ', 'Yi ', 'Chao ', 'Chang ', 'Sa ', qq{[?] }, 'Yi ', 'Mu ', 'Men ', 'Ren ', 'Jia ', 'Chao ',
'Yang ', 'Qian ', 'Zhong ', 'Pi ', 'Wan ', 'Wu ', 'Jian ', 'Jie ', 'Yao ', 'Feng ', 'Cang ', 'Ren ', 'Wang ', 'Fen ', 'Di ', 'Fang ',
];
1;
