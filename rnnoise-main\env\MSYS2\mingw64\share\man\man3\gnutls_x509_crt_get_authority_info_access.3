.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_authority_info_access" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_authority_info_access \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_authority_info_access(gnutls_x509_crt_t " crt ", unsigned int " seq ", int " what ", gnutls_datum_t * " data ", unsigned int * " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
Holds the certificate
.IP "unsigned int seq" 12
specifies the sequence number of the access descriptor (0 for the first one, 1 for the second etc.)
.IP "int what" 12
what data to get, a \fBgnutls_info_access_what_t\fP type.
.IP "gnutls_datum_t * data" 12
output data to be freed with \fBgnutls_free()\fP.
.IP "unsigned int * critical" 12
pointer to output integer that is set to non\-zero if the extension is marked as critical (may be \fBNULL\fP)
.SH "DESCRIPTION"
Note that a simpler API to access the authority info data is provided
by \fBgnutls_x509_aia_get()\fP and \fBgnutls_x509_ext_import_aia()\fP.

This function extracts the Authority Information Access (AIA)
extension, see RFC 5280 section ******* for more information.  The
AIA extension holds a sequence of AccessDescription (AD) data.

The  \fIseq\fP input parameter is used to indicate which member of the
sequence the caller is interested in.  The first member is 0, the
second member 1 and so on.  When the  \fIseq\fP value is out of bounds,
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP is returned.

The type of data returned in  \fIdata\fP is specified via  \fIwhat\fP which
should be \fBgnutls_info_access_what_t\fP values.

If  \fIwhat\fP is \fBGNUTLS_IA_ACCESSMETHOD_OID\fP then  \fIdata\fP will hold the
accessMethod OID (e.g., "*******.********.1").

If  \fIwhat\fP is \fBGNUTLS_IA_ACCESSLOCATION_GENERALNAME_TYPE\fP,  \fIdata\fP will
hold the accessLocation GeneralName type (e.g.,
"uniformResourceIdentifier").

If  \fIwhat\fP is \fBGNUTLS_IA_URI\fP,  \fIdata\fP will hold the accessLocation URI
data.  Requesting this  \fIwhat\fP value leads to an error if the
accessLocation is not of the "uniformResourceIdentifier" type.

If  \fIwhat\fP is \fBGNUTLS_IA_OCSP_URI\fP,  \fIdata\fP will hold the OCSP URI.
Requesting this  \fIwhat\fP value leads to an error if the accessMethod
is not *******.********.1 aka OCSP, or if accessLocation is not of
the "uniformResourceIdentifier" type. In that case \fBGNUTLS_E_UNKNOWN_ALGORITHM\fP
will be returned, and  \fIseq\fP should be increased and this function
called again.

If  \fIwhat\fP is \fBGNUTLS_IA_CAISSUERS_URI\fP,  \fIdata\fP will hold the caIssuers
URI.  Requesting this  \fIwhat\fP value leads to an error if the
accessMethod is not *******.********.2 aka caIssuers, or if
accessLocation is not of the "uniformResourceIdentifier" type.
In that case handle as in \fBGNUTLS_IA_OCSP_URI\fP.

More  \fIwhat\fP values may be allocated in the future as needed.

If  \fIdata\fP is NULL, the function does the same without storing the
output data, that is, it will set  \fIcritical\fP and do error checking
as usual.

The value of the critical flag is returned in * \fIcritical\fP .  Supply a
NULL  \fIcritical\fP if you want the function to make sure the extension
is non\-critical, as required by RFC 5280.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, \fBGNUTLS_E_INVALID_REQUEST\fP on
invalid  \fIcrt\fP , \fBGNUTLS_E_CONSTRAINT_ERROR\fP if the extension is
incorrectly marked as critical (use a non\-NULL  \fIcritical\fP to
override), \fBGNUTLS_E_UNKNOWN_ALGORITHM\fP if the requested OID does
not match (e.g., when using \fBGNUTLS_IA_OCSP_URI\fP), otherwise a
negative error code.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
