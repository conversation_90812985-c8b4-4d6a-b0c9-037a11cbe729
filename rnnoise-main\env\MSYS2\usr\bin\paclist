#!/usr/bin/bash
# paclist - List all packages installed from a given repo
#
# Copyright (C) 2008 <PERSON> <<EMAIL>>
# Copyright (C) 2011 <PERSON> <<EMAIL>>
#
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License
# as published by the Free Software Foundation; either version 2
# of the License, or (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.

declare -r myname='paclist'
declare -r myver='1.10.6'

usage() {
	cat <<EOF
${myname} v${myver}

List all packages installed from a given repository.

Usage: ${myname} <repository ...>

Options:
  -h, --help     show this help message and exit
  -V, --version  display version information and exit

Example: ${myname} core-testing
EOF
}

version() {
	printf "%s %s\n" "$myname" "$myver"
	echo 'Copyright (C) 2008 Dan <PERSON> <<EMAIL>>'
	echo 'Copyright (C) 2011 Dave Reisner <<EMAIL>>'
}

if [[ -z $1 ]]; then
	usage
	exit 1
fi

if [[ $1 = -@(h|-help) ]]; then
	usage
	exit 0
elif [[ $1 = -@(V|-version) ]]; then
	version
	exit 0
fi

pacman -Sl "$@" | awk '/\[.*[[:alpha:]]+]$/ {print $2,$3};
                     /\[.*[[:digit:]]+]$/ {print $2,substr($NF, 1, length($NF) - 1)}'

# exit with pacman's return value, not awk's
exit "${PIPESTATUS[0]}"
