.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_priority_set_direct" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_priority_set_direct \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_priority_set_direct(gnutls_session_t " session ", const char * " priorities ", const char ** " err_pos ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "const char * priorities" 12
is a string describing priorities
.IP "const char ** err_pos" 12
In case of an error this will have the position in the string the error occurred
.SH "DESCRIPTION"
Sets the priorities to use on the ciphers, key exchange methods,
and macs.  This function avoids keeping a
priority cache and is used to directly set string priorities to a
TLS session.  For documentation check the \fBgnutls_priority_init()\fP.

To use a reasonable default, consider using \fBgnutls_set_default_priority()\fP,
or \fBgnutls_set_default_priority_append()\fP instead of this function.
.SH "RETURNS"
On syntax error \fBGNUTLS_E_INVALID_REQUEST\fP is returned,
\fBGNUTLS_E_SUCCESS\fP on success, or an error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
