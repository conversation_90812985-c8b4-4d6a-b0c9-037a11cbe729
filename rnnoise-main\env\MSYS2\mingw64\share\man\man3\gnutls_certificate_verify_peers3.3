.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_verify_peers3" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_verify_peers3 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_verify_peers3(gnutls_session_t " session ", const char * " hostname ", unsigned int * " status ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.IP "const char * hostname" 12
is the expected name of the peer; may be \fBNULL\fP
.IP "unsigned int * status" 12
is the output of the verification
.SH "DESCRIPTION"
This function will verify the peer's certificate and store the
the status in the  \fIstatus\fP variable as a bitwise OR of gnutls_certificate_status_t
values or zero if the certificate is trusted. Note that value in  \fIstatus\fP is set only when the return value of this function is success (i.e, failure
to trust a certificate does not imply a negative return value).
The default verification flags used by this function can be overridden
using \fBgnutls_certificate_set_verify_flags()\fP. See the documentation
of \fBgnutls_certificate_verify_peers2()\fP for details in the verification process.

This function will take into account the stapled OCSP responses sent by the server,
as well as the following X.509 certificate extensions: Name Constraints,
Key Usage, and Basic Constraints (pathlen).

If the  \fIhostname\fP provided is non\-NULL then this function will compare
the hostname in the certificate against it. The comparison will follow
the RFC6125 recommendations. If names do not match the
\fBGNUTLS_CERT_UNEXPECTED_OWNER\fP status flag will be set.

In order to verify the purpose of the end\-certificate (by checking the extended
key usage), use \fBgnutls_certificate_verify_peers()\fP.

To avoid denial of service attacks some
default upper limits regarding the certificate key size and chain
size are set. To override them use \fBgnutls_certificate_set_verify_limits()\fP.

Note that when using raw public\-keys verification will not work because there is
no corresponding certificate body belonging to the raw key that can be verified. In that
case this function will return \fBGNUTLS_E_INVALID_REQUEST\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) when the validation is performed, or a negative error code otherwise.
A successful error code means that the  \fIstatus\fP parameter must be checked to obtain the validation status.
.SH "SINCE"
3.1.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
