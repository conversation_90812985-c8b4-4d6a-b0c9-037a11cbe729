/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> within this package.
 */
#ifndef _APISETHANDLE_
#define _APISETHANDLE_

#include <apiset.h>
#include <apisetcconv.h>
#include <minwindef.h>

#ifdef __cplusplus
extern "C" {
#endif

#define INVALID_HANDLE_VALUE ((HANDLE) (LONG_PTR)-1)

#if WINAPI_FAMILY_PARTITION (WINAPI_PARTITION_APP)
  WINBASEAPI WINBOOL WINAPI CloseHandle (HANDLE hObject);
  WIN<PERSON><PERSON><PERSON><PERSON> WINBOOL WINAPI DuplicateHandle (HAN<PERSON><PERSON> hSourceProcessHandle, HAND<PERSON> hSourceHandle, HANDLE hTargetProcessHandle, <PERSON><PERSON><PERSON><PERSON> lpTargetHandle, DWORD dwDesiredAccess, WIN<PERSON><PERSON> bInheritHandle, DWORD dwOptions);
#if _WIN32_WINNT >= _WIN32_WINNT_WIN10
  WINBASEAPI WINBOOL WINAPI CompareObjectHandles (HANDLE hFirstObjectHandle, HANDLE hSecondObjectHandle);
#endif
#endif

#if WINAPI_FAMILY_PARTITION (WINAPI_PARTITION_DESKTOP) || NTDDI_VERSION >= NTDDI_WIN10_19H1
  WINBASEAPI WINBOOL WINAPI GetHandleInformation (HANDLE hObject, LPDWORD lpdwFlags);
  WINBASEAPI WINBOOL WINAPI SetHandleInformation (HANDLE hObject, DWORD dwMask, DWORD dwFlags);
#endif

#ifdef __cplusplus
}
#endif
#endif
