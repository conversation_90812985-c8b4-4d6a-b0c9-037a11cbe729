/*
 * Background Intelligent Transfer Service (BITS) 3.0 interface
 *
 * Copyright 2013 Nikolay Sivov for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 *
 */

#ifndef DO_NO_IMPORTS
import "bits.idl";
import "bits2_0.idl";
#endif

[
    uuid(659cdeac-489e-11d9-a9cd-000d56965251),
    odl
]
interface IBackgroundCopyCallback2 : IBackgroundCopyCallback
{
    HRESULT FileTransferred([in] IBackgroundCopyJob *job,
                            [in] IBackgroundCopyFile *file);
}

[
    uuid(659cdeae-489e-11d9-a9cd-000d56965251),
    odl
]
interface IBackgroundCopyJob4 : IBackgroundCopyJob3
{
cpp_quote("#define BG_JOB_ENABLE_PEERCACHING_CLIENT 0x0001")
cpp_quote("#define BG_JOB_ENABLE_PEERCACHING_SERVER 0x0002")
cpp_quote("#define BG_JOB_DISABLE_BRANCH_CACHE      0x0004")

    HRESULT SetPeerCachingFlags(DWORD flags);
    HRESULT GetPeerCachingFlags([out, ref] DWORD *flags);
    HRESULT GetOwnerIntegrityLevel([out, ref] ULONG *level);
    HRESULT GetOwnerElevationState([out, ref] BOOL *elevated);
    HRESULT SetMaximumDownloadTime(ULONG timeout);
    HRESULT GetMaximumDownloadTime([out,ref] ULONG *timeout);
}

[
    uuid(659cdea6-489e-11d9-a9cd-000d56965251),
    lcid(0x0000),
    version(1.0)
]
library BackgroundCopyManager3_0
{
    [
        uuid(659cdea7-489e-11d9-a9cd-000d56965251)
    ]
    coclass BackgroundCopyManager3_0
    {
        [default] interface IBackgroundCopyManager;
    };

    interface IBackgroundCopyJob4;
}
