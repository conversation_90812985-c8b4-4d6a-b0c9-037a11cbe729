<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): Globals</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('globals.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="contents">
<div class="textblock">Here is a list of all documented functions, variables, defines, enums, and typedefs with links to the documentation:</div>

<h3><a id="index_l" name="index_l"></a>- l -</h3><ul>
<li>lzma_action&#160;:&#160;<a class="el" href="base_8h.html#aa92efcbf3cecfcac79c81fc645fce77e">base.h</a></li>
<li>lzma_alone_decoder()&#160;:&#160;<a class="el" href="container_8h.html#a5f43c3a1035e5a226dcd298f4162b861">container.h</a></li>
<li>lzma_alone_encoder()&#160;:&#160;<a class="el" href="container_8h.html#a26fcc5bccdf3f862caa4c992d01e1a72">container.h</a></li>
<li>lzma_auto_decoder()&#160;:&#160;<a class="el" href="container_8h.html#a21cbebf2771617bb1e956385cfb353e3">container.h</a></li>
<li>LZMA_BACKWARD_SIZE_MAX&#160;:&#160;<a class="el" href="stream__flags_8h.html#a2e5e09010880f8caa6cd6539c7341239">stream_flags.h</a></li>
<li>LZMA_BACKWARD_SIZE_MIN&#160;:&#160;<a class="el" href="stream__flags_8h.html#ae8da8190f1396f66332073946bc45634">stream_flags.h</a></li>
<li>lzma_bcj_arm64_decode()&#160;:&#160;<a class="el" href="bcj_8h.html#a1989d14c1abfb1cab1e6281b78b12250">bcj.h</a></li>
<li>lzma_bcj_arm64_encode()&#160;:&#160;<a class="el" href="bcj_8h.html#a864bc77639e1e70a2b50f3f03424e482">bcj.h</a></li>
<li>lzma_bcj_riscv_decode()&#160;:&#160;<a class="el" href="bcj_8h.html#a7379d9357c8c7fc100a55d0c53a29021">bcj.h</a></li>
<li>lzma_bcj_riscv_encode()&#160;:&#160;<a class="el" href="bcj_8h.html#a3c11efb1d448aaabd95595fb1c68ab01">bcj.h</a></li>
<li>lzma_bcj_x86_decode()&#160;:&#160;<a class="el" href="bcj_8h.html#a24814feed2515ef61d30f61c8ae19500">bcj.h</a></li>
<li>lzma_bcj_x86_encode()&#160;:&#160;<a class="el" href="bcj_8h.html#ab17e110b27302635b7f97cf549b4b83d">bcj.h</a></li>
<li>lzma_block_buffer_bound()&#160;:&#160;<a class="el" href="block_8h.html#a58ff73e2572b529f48cc590bfffe5b4f">block.h</a></li>
<li>lzma_block_buffer_decode()&#160;:&#160;<a class="el" href="block_8h.html#a0c6eb869d91b08f68648b1aa7a32ee9f">block.h</a></li>
<li>lzma_block_buffer_encode()&#160;:&#160;<a class="el" href="block_8h.html#af415fa5130ab64e8760e9c39e856fa54">block.h</a></li>
<li>lzma_block_compressed_size()&#160;:&#160;<a class="el" href="block_8h.html#a6c3e102d76db06a07126a569abc6e2bc">block.h</a></li>
<li>lzma_block_decoder()&#160;:&#160;<a class="el" href="block_8h.html#aa92c73b2a228efe921fa2376aa7adc92">block.h</a></li>
<li>lzma_block_encoder()&#160;:&#160;<a class="el" href="block_8h.html#a2218a49025a0b44f9a6f9d6d24359359">block.h</a></li>
<li>lzma_block_header_decode()&#160;:&#160;<a class="el" href="block_8h.html#a7f5487c21a7b36a8bd17be36074d43c9">block.h</a></li>
<li>lzma_block_header_encode()&#160;:&#160;<a class="el" href="block_8h.html#a0eedbd6331d5708ea963260e6f2a92d0">block.h</a></li>
<li>lzma_block_header_size()&#160;:&#160;<a class="el" href="block_8h.html#ae9b47abc872d0b02c2da9d3fa5a7dacd">block.h</a></li>
<li>lzma_block_header_size_decode&#160;:&#160;<a class="el" href="block_8h.html#ac025c940683a70f4c7f956bad814fd5f">block.h</a></li>
<li>lzma_block_total_size()&#160;:&#160;<a class="el" href="block_8h.html#a694424f9dfdd5151e01debac1c501fa9">block.h</a></li>
<li>lzma_block_uncomp_encode()&#160;:&#160;<a class="el" href="block_8h.html#a5a260f634ccd5f54fb98f570d8d92d8c">block.h</a></li>
<li>lzma_block_unpadded_size()&#160;:&#160;<a class="el" href="block_8h.html#a412d5605280fa29befae1b89e344bf30">block.h</a></li>
<li>lzma_bool&#160;:&#160;<a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">base.h</a></li>
<li>LZMA_BUF_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea9ca0ecb62459bdc84d6af47d16b23ae5">base.h</a></li>
<li>lzma_check&#160;:&#160;<a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">check.h</a></li>
<li>LZMA_CHECK_CRC32&#160;:&#160;<a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3fa0be65014a40b5cb4ab32252b3709bef7">check.h</a></li>
<li>LZMA_CHECK_CRC64&#160;:&#160;<a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3fa87b4b0697a1e1ccb6766dd5c2fa24afc">check.h</a></li>
<li>LZMA_CHECK_ID_MAX&#160;:&#160;<a class="el" href="check_8h.html#acd221ababe30230d9647aab469ad80cb">check.h</a></li>
<li>lzma_check_is_supported()&#160;:&#160;<a class="el" href="check_8h.html#ae9391ed2acfad0ce9357b68c608f07d8">check.h</a></li>
<li>LZMA_CHECK_NONE&#160;:&#160;<a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3fa7b9851d75abfabc08d7fc5b4aaeb6f20">check.h</a></li>
<li>LZMA_CHECK_SHA256&#160;:&#160;<a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3faf26a55ddd204a50ae87ec3432e7bc309">check.h</a></li>
<li>lzma_check_size()&#160;:&#160;<a class="el" href="check_8h.html#afd3fda19575d9d4f864c626c02b7cb48">check.h</a></li>
<li>LZMA_CHECK_SIZE_MAX&#160;:&#160;<a class="el" href="check_8h.html#a379e931cf86351ab1d97896cda9abbe0">check.h</a></li>
<li>lzma_code()&#160;:&#160;<a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957">base.h</a></li>
<li>LZMA_CONCATENATED&#160;:&#160;<a class="el" href="container_8h.html#a563c84b5f368b3dd00d92ea903c5c33d">container.h</a></li>
<li>lzma_cputhreads()&#160;:&#160;<a class="el" href="hardware_8h.html#a22f7a882b7a4b741a226abf62bdf46ca">hardware.h</a></li>
<li>lzma_crc32()&#160;:&#160;<a class="el" href="check_8h.html#a760b569cce91bdd01e4ce9d78823c96d">check.h</a></li>
<li>lzma_crc64()&#160;:&#160;<a class="el" href="check_8h.html#aff2e74ce671b9f82a96adb549c68cea2">check.h</a></li>
<li>LZMA_DATA_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea3aa72729a844790e39b4e1101a731dfb">base.h</a></li>
<li>LZMA_DELTA_DIST_MAX&#160;:&#160;<a class="el" href="delta_8h.html#afdf8a5ce184ddf9f8070de637775da94">delta.h</a></li>
<li>LZMA_DELTA_DIST_MIN&#160;:&#160;<a class="el" href="delta_8h.html#a466886d9d01392f61bdf267687a4f96e">delta.h</a></li>
<li>lzma_delta_type&#160;:&#160;<a class="el" href="delta_8h.html#a04d84d7fa6cefdc219b6e2e96ff36fe1">delta.h</a></li>
<li>lzma_easy_buffer_encode()&#160;:&#160;<a class="el" href="container_8h.html#ac5e71f2711b57391186671967435faf2">container.h</a></li>
<li>lzma_easy_decoder_memusage()&#160;:&#160;<a class="el" href="container_8h.html#a3562055d26c18fad067a7c7516eaddf5">container.h</a></li>
<li>lzma_easy_encoder()&#160;:&#160;<a class="el" href="container_8h.html#acbdad999c544872f0f5d242f0d1a4ed4">container.h</a></li>
<li>lzma_easy_encoder_memusage()&#160;:&#160;<a class="el" href="container_8h.html#a62c853cf7dbf008bdbd97b2685c3eabf">container.h</a></li>
<li>lzma_end()&#160;:&#160;<a class="el" href="base_8h.html#a854ff37464ae1225febf14db1af43308">base.h</a></li>
<li>LZMA_FAIL_FAST&#160;:&#160;<a class="el" href="container_8h.html#aa1f469ed3d4b2eaf12f8081657efc9d5">container.h</a></li>
<li>lzma_file_info_decoder()&#160;:&#160;<a class="el" href="index_8h.html#a7c5d77cf8532d95977d4571a1eb0a222">index.h</a></li>
<li>LZMA_FILTER_ARM&#160;:&#160;<a class="el" href="bcj_8h.html#a495a58f63ebc7a8b756099efba492f8b">bcj.h</a></li>
<li>LZMA_FILTER_ARM64&#160;:&#160;<a class="el" href="bcj_8h.html#a01765158cd31cac21b272b180628fc4b">bcj.h</a></li>
<li>LZMA_FILTER_ARMTHUMB&#160;:&#160;<a class="el" href="bcj_8h.html#a5ec62e7e5e7df3d9af5b2ea3f857689a">bcj.h</a></li>
<li>lzma_filter_decoder_is_supported()&#160;:&#160;<a class="el" href="filter_8h.html#acab0c67bf5b3a76f2b474c8e1da98938">filter.h</a></li>
<li>LZMA_FILTER_DELTA&#160;:&#160;<a class="el" href="delta_8h.html#a7ced67235ad7a01ae31d32ecf1e634cb">delta.h</a></li>
<li>lzma_filter_encoder_is_supported()&#160;:&#160;<a class="el" href="filter_8h.html#a3db3c36cd6e57658a74c53e4daa2bef6">filter.h</a></li>
<li>lzma_filter_flags_decode()&#160;:&#160;<a class="el" href="filter_8h.html#a4cba9a4c658cce0ff01fd102b31ea1a7">filter.h</a></li>
<li>lzma_filter_flags_encode()&#160;:&#160;<a class="el" href="filter_8h.html#a96f23309bc21398fece18c00ebe7db98">filter.h</a></li>
<li>lzma_filter_flags_size()&#160;:&#160;<a class="el" href="filter_8h.html#a996c9c21840ed54e37bd1f664a79d940">filter.h</a></li>
<li>LZMA_FILTER_IA64&#160;:&#160;<a class="el" href="bcj_8h.html#a2fe36218a38f400e1ce40820758f7427">bcj.h</a></li>
<li>LZMA_FILTER_LZMA1&#160;:&#160;<a class="el" href="lzma12_8h.html#accedd16abcb758e7f748bac1102abda9">lzma12.h</a></li>
<li>LZMA_FILTER_LZMA1EXT&#160;:&#160;<a class="el" href="lzma12_8h.html#a98a7fd42aa78a273a6b138629e46772d">lzma12.h</a></li>
<li>LZMA_FILTER_LZMA2&#160;:&#160;<a class="el" href="lzma12_8h.html#a04f9d9a018a47cc99491e6e94e92f96b">lzma12.h</a></li>
<li>LZMA_FILTER_POWERPC&#160;:&#160;<a class="el" href="bcj_8h.html#ab7f667d4a5d319f227f23163cbea086f">bcj.h</a></li>
<li>LZMA_FILTER_RISCV&#160;:&#160;<a class="el" href="bcj_8h.html#a932e9d66e945f5601b8fad7445a9b40c">bcj.h</a></li>
<li>LZMA_FILTER_SPARC&#160;:&#160;<a class="el" href="bcj_8h.html#a50941088e93ef659c6b000bbcaf58143">bcj.h</a></li>
<li>LZMA_FILTER_X86&#160;:&#160;<a class="el" href="bcj_8h.html#aa9eac1f580ddde3309518cd153d596b1">bcj.h</a></li>
<li>lzma_filters_copy()&#160;:&#160;<a class="el" href="filter_8h.html#a611fe1176eeeda187b1bd8aef45040aa">filter.h</a></li>
<li>lzma_filters_free()&#160;:&#160;<a class="el" href="filter_8h.html#ae06979d219897f5f4c29cbc7a96a8892">filter.h</a></li>
<li>LZMA_FILTERS_MAX&#160;:&#160;<a class="el" href="filter_8h.html#ab33c0cc1728bf390e5b84f8bce1928ba">filter.h</a></li>
<li>lzma_filters_update()&#160;:&#160;<a class="el" href="filter_8h.html#a4a8fd969df001e449ebe4421ab33bba5">filter.h</a></li>
<li>LZMA_FINISH&#160;:&#160;<a class="el" href="base_8h.html#aa92efcbf3cecfcac79c81fc645fce77ea7d24fb3c6c144d13bcb091195b8ebec1">base.h</a></li>
<li>LZMA_FORMAT_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea63b7a58949854eb9307f8e351358d56c">base.h</a></li>
<li>LZMA_FULL_BARRIER&#160;:&#160;<a class="el" href="base_8h.html#aa92efcbf3cecfcac79c81fc645fce77eaf7bf60e3555a4d10ffad3ecc3d2e01f1">base.h</a></li>
<li>LZMA_FULL_FLUSH&#160;:&#160;<a class="el" href="base_8h.html#aa92efcbf3cecfcac79c81fc645fce77eaab46f0d7c721f1ec377e9575eab2586f">base.h</a></li>
<li>LZMA_GET_CHECK&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6eaa5b648c18da0f584f621cfdf7fef1bdb">base.h</a></li>
<li>lzma_get_check()&#160;:&#160;<a class="el" href="check_8h.html#a8d7c3ffabfd024485f03fa209536c746">check.h</a></li>
<li>lzma_get_progress()&#160;:&#160;<a class="el" href="base_8h.html#ab6447cd68eeecbd6b88f21daeb8ce751">base.h</a></li>
<li>LZMA_IGNORE_CHECK&#160;:&#160;<a class="el" href="container_8h.html#a1289925ae1c63a8e86f69f3657118a4d">container.h</a></li>
<li>lzma_index&#160;:&#160;<a class="el" href="index_8h.html#afc18c1443b3b9aa0d146b44e8755b62e">index.h</a></li>
<li>lzma_index_append()&#160;:&#160;<a class="el" href="index_8h.html#ac347747eb933c7c408e6c801b33becc3">index.h</a></li>
<li>lzma_index_block_count()&#160;:&#160;<a class="el" href="index_8h.html#add1a8c506f67dbc19cae6747107e3bec">index.h</a></li>
<li>lzma_index_buffer_decode()&#160;:&#160;<a class="el" href="index_8h.html#a028b8b8d59a413f9682eea1269a6ae8b">index.h</a></li>
<li>lzma_index_buffer_encode()&#160;:&#160;<a class="el" href="index_8h.html#add1ef06dec8a26d08ae8651cff0fd8d6">index.h</a></li>
<li>lzma_index_cat()&#160;:&#160;<a class="el" href="index_8h.html#abc4db36b4bd67af01819be9dd045c34a">index.h</a></li>
<li>LZMA_INDEX_CHECK_MASK_CRC32&#160;:&#160;<a class="el" href="index_8h.html#a6812319b2f335df98fcf5e97c144e4ac">index.h</a></li>
<li>LZMA_INDEX_CHECK_MASK_CRC64&#160;:&#160;<a class="el" href="index_8h.html#a062a47783c17f64728d98b831411c5c3">index.h</a></li>
<li>LZMA_INDEX_CHECK_MASK_NONE&#160;:&#160;<a class="el" href="index_8h.html#a2643014196abf129ddbb51ec2bd956d9">index.h</a></li>
<li>LZMA_INDEX_CHECK_MASK_SHA256&#160;:&#160;<a class="el" href="index_8h.html#ad6540f1ecf60f186f7c0492937482169">index.h</a></li>
<li>lzma_index_checks()&#160;:&#160;<a class="el" href="index_8h.html#af8d6528a04241841bd0a4322b0c57eaa">index.h</a></li>
<li>lzma_index_decoder()&#160;:&#160;<a class="el" href="index_8h.html#abb56fd1d5914f8900ece7b88b78e5e23">index.h</a></li>
<li>lzma_index_dup()&#160;:&#160;<a class="el" href="index_8h.html#a5161e3f67156577882e1d95dcb57e33e">index.h</a></li>
<li>lzma_index_encoder()&#160;:&#160;<a class="el" href="index_8h.html#a6800d70f3b2afca085496460cd03211d">index.h</a></li>
<li>lzma_index_end()&#160;:&#160;<a class="el" href="index_8h.html#a0c2d0009f07fc315d5ac89e4bcd25abd">index.h</a></li>
<li>lzma_index_file_size()&#160;:&#160;<a class="el" href="index_8h.html#ac875ed47d35385e5dac461b25c5ea1c9">index.h</a></li>
<li>lzma_index_hash&#160;:&#160;<a class="el" href="index__hash_8h.html#a2db9f438838c8ff72a8a6fd3fc856f8c">index_hash.h</a></li>
<li>lzma_index_hash_append()&#160;:&#160;<a class="el" href="index__hash_8h.html#a2bdbe4f0b5fa2fadb7528447feaaa97f">index_hash.h</a></li>
<li>lzma_index_hash_decode()&#160;:&#160;<a class="el" href="index__hash_8h.html#a891eb955284c9117155f92eb0ddba44c">index_hash.h</a></li>
<li>lzma_index_hash_end()&#160;:&#160;<a class="el" href="index__hash_8h.html#a7dacb41b9ec1c8df5d33dfdae97743b3">index_hash.h</a></li>
<li>lzma_index_hash_init()&#160;:&#160;<a class="el" href="index__hash_8h.html#aaafae4967a4a266d97dc34a98bfcabfb">index_hash.h</a></li>
<li>lzma_index_hash_size()&#160;:&#160;<a class="el" href="index__hash_8h.html#a0f8ab3b57b117f9547866156755c917f">index_hash.h</a></li>
<li>lzma_index_init()&#160;:&#160;<a class="el" href="index_8h.html#a0850627d011111326d4278a3e2edec25">index.h</a></li>
<li>LZMA_INDEX_ITER_ANY&#160;:&#160;<a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8af46f6e5c414471c7c96586f380e48315">index.h</a></li>
<li>LZMA_INDEX_ITER_BLOCK&#160;:&#160;<a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8a2702617d60d6fc15138a749e06ef3414">index.h</a></li>
<li>lzma_index_iter_init()&#160;:&#160;<a class="el" href="index_8h.html#aa78f02f18ed29d289a6ef37b8ea98a21">index.h</a></li>
<li>lzma_index_iter_locate()&#160;:&#160;<a class="el" href="index_8h.html#ac4f56df9d210712e5d7add5502c9eb93">index.h</a></li>
<li>lzma_index_iter_mode&#160;:&#160;<a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8">index.h</a></li>
<li>lzma_index_iter_next()&#160;:&#160;<a class="el" href="index_8h.html#af428522e1b3eef137c65c5a01f766e0e">index.h</a></li>
<li>LZMA_INDEX_ITER_NONEMPTY_BLOCK&#160;:&#160;<a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8aa49bf4d561d8f2c61d300edbb6c282c7">index.h</a></li>
<li>lzma_index_iter_rewind()&#160;:&#160;<a class="el" href="index_8h.html#ae81438be8deff4894b104e65d8acdd24">index.h</a></li>
<li>LZMA_INDEX_ITER_STREAM&#160;:&#160;<a class="el" href="index_8h.html#a712b43192d944bf2f767711343cd9ca8a5b31d985de1c823151acdd7e4a966fc9">index.h</a></li>
<li>lzma_index_memusage()&#160;:&#160;<a class="el" href="index_8h.html#a880def3727ecdd7f242807083d228fc5">index.h</a></li>
<li>lzma_index_memused()&#160;:&#160;<a class="el" href="index_8h.html#a4118805ac7be6618aca6d6d7e5e4dde7">index.h</a></li>
<li>lzma_index_size()&#160;:&#160;<a class="el" href="index_8h.html#a669ed1a82b1941217cfbb07e7826afc2">index.h</a></li>
<li>lzma_index_stream_count()&#160;:&#160;<a class="el" href="index_8h.html#afd159a765b09b0cf79186069a848d07e">index.h</a></li>
<li>lzma_index_stream_flags()&#160;:&#160;<a class="el" href="index_8h.html#a79a19669237f19f0b11c9f3be80a62b4">index.h</a></li>
<li>lzma_index_stream_padding()&#160;:&#160;<a class="el" href="index_8h.html#a3ed82f96c688f3c953f6509b6f4e2ef3">index.h</a></li>
<li>lzma_index_stream_size()&#160;:&#160;<a class="el" href="index_8h.html#af3630369b43c9ccc906065d759b49663">index.h</a></li>
<li>lzma_index_total_size()&#160;:&#160;<a class="el" href="index_8h.html#a7829942b83ee1fa5b6443cc248b81041">index.h</a></li>
<li>lzma_index_uncompressed_size()&#160;:&#160;<a class="el" href="index_8h.html#a620fe6317f1f9d7af9cc27c748bf07d6">index.h</a></li>
<li>lzma_internal&#160;:&#160;<a class="el" href="base_8h.html#ab1a60127c640135687a5bcc232cec906">base.h</a></li>
<li>lzma_lzip_decoder()&#160;:&#160;<a class="el" href="container_8h.html#a97689f5709e0db1e2dac450f5ce4e5eb">container.h</a></li>
<li>lzma_lzma_preset()&#160;:&#160;<a class="el" href="lzma12_8h.html#aa62c28944fe3575653a4c25780400d77">lzma12.h</a></li>
<li>lzma_match_finder&#160;:&#160;<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80">lzma12.h</a></li>
<li>LZMA_MEM_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea567e1464feca03900a5425fb45b2f5b6">base.h</a></li>
<li>LZMA_MEMLIMIT_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6eaa1d705effe6026f32c0fe9756b6326bc">base.h</a></li>
<li>lzma_memlimit_get()&#160;:&#160;<a class="el" href="base_8h.html#ac871bc2ead5d482c6d6b3d51bfec365c">base.h</a></li>
<li>lzma_memlimit_set()&#160;:&#160;<a class="el" href="base_8h.html#afc49d4cf75b73128a167df3407505f7b">base.h</a></li>
<li>lzma_memusage()&#160;:&#160;<a class="el" href="base_8h.html#a418b210cf206782a73cd9de7dc27f670">base.h</a></li>
<li>LZMA_MF_BT2&#160;:&#160;<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a7ab212446c3f6520f5c33ccfa4b3386a">lzma12.h</a></li>
<li>LZMA_MF_BT3&#160;:&#160;<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a983ecc59bf3e07a7c43fea551ea11865">lzma12.h</a></li>
<li>LZMA_MF_BT4&#160;:&#160;<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a468c32cdea9861d1ff98478364e6c547">lzma12.h</a></li>
<li>LZMA_MF_HC3&#160;:&#160;<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a6eb38f634021a192cada8a978b5de93b">lzma12.h</a></li>
<li>LZMA_MF_HC4&#160;:&#160;<a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80a0944620f4949289c2ebde613cae12b04">lzma12.h</a></li>
<li>lzma_mf_is_supported()&#160;:&#160;<a class="el" href="lzma12_8h.html#aefba1f7214ddcf8cd408a0702e8642b5">lzma12.h</a></li>
<li>lzma_microlzma_decoder()&#160;:&#160;<a class="el" href="container_8h.html#aa8372dae3e7c907c36f7bb5426aeacdf">container.h</a></li>
<li>lzma_microlzma_encoder()&#160;:&#160;<a class="el" href="container_8h.html#abfc8f11acf837b167aa94b7071b54c30">container.h</a></li>
<li>lzma_mode&#160;:&#160;<a class="el" href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866">lzma12.h</a></li>
<li>LZMA_MODE_FAST&#160;:&#160;<a class="el" href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866ac8c0926a91b4f756e11121efd30648cc">lzma12.h</a></li>
<li>lzma_mode_is_supported()&#160;:&#160;<a class="el" href="lzma12_8h.html#ad1add1c2600fdbb3d737e4fb3465dfcb">lzma12.h</a></li>
<li>LZMA_MODE_NORMAL&#160;:&#160;<a class="el" href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866ad37225f30d5cd21fc8bb3eaba283bbf9">lzma12.h</a></li>
<li>lzma_mt_block_size()&#160;:&#160;<a class="el" href="container_8h.html#a7eaeda1cb811ac1f5a6403691df8a894">container.h</a></li>
<li>LZMA_NO_CHECK&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6eaa720d30092d504d7d138a320db1905ef">base.h</a></li>
<li>LZMA_OK&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6eac003781ccb81bbd5578e29abed8a8cfe">base.h</a></li>
<li>LZMA_OPTIONS_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6eaa9ff6dfee36b7aba4fae60706d37425f">base.h</a></li>
<li>lzma_physmem()&#160;:&#160;<a class="el" href="hardware_8h.html#a85363e453b34272a9f26c9fdffb041ee">hardware.h</a></li>
<li>LZMA_PRESET_DEFAULT&#160;:&#160;<a class="el" href="container_8h.html#af3ca20ff228b363a82515c1aee9e27bc">container.h</a></li>
<li>LZMA_PRESET_EXTREME&#160;:&#160;<a class="el" href="container_8h.html#af524fe9af5737820fdadcd40a2c26deb">container.h</a></li>
<li>LZMA_PRESET_LEVEL_MASK&#160;:&#160;<a class="el" href="container_8h.html#a97e40265e355a21bd2465aaa5b85f03d">container.h</a></li>
<li>LZMA_PROG_ERROR&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea2dac8d451cb38da8550653d0d7be4ec2">base.h</a></li>
<li>lzma_properties_decode()&#160;:&#160;<a class="el" href="filter_8h.html#a88d2e864b2039ac82802cc202278d478">filter.h</a></li>
<li>lzma_properties_encode()&#160;:&#160;<a class="el" href="filter_8h.html#a8e00887086df5a44084ac22e48415de3">filter.h</a></li>
<li>lzma_properties_size()&#160;:&#160;<a class="el" href="filter_8h.html#aee038818cf7bbe044c3f7a7c86998c1b">filter.h</a></li>
<li>lzma_raw_buffer_decode()&#160;:&#160;<a class="el" href="filter_8h.html#a3b942df507e4f9a6d7525e5a4c6864e5">filter.h</a></li>
<li>lzma_raw_buffer_encode()&#160;:&#160;<a class="el" href="filter_8h.html#a226724ab3391b410281fdf656cc7c432">filter.h</a></li>
<li>lzma_raw_decoder()&#160;:&#160;<a class="el" href="filter_8h.html#ae77b3b6c5eccd9d77bbafef0a8a203c1">filter.h</a></li>
<li>lzma_raw_decoder_memusage()&#160;:&#160;<a class="el" href="filter_8h.html#a58511249ae9206d7de7c5d1f05842297">filter.h</a></li>
<li>lzma_raw_encoder()&#160;:&#160;<a class="el" href="filter_8h.html#a2368e4129032345eb0738b0c6e085703">filter.h</a></li>
<li>lzma_raw_encoder_memusage()&#160;:&#160;<a class="el" href="filter_8h.html#a730f9391e85a5979bcd1b32643ae7176">filter.h</a></li>
<li>lzma_reserved_enum&#160;:&#160;<a class="el" href="base_8h.html#a05805a07754b2aa22f7d443eb7ece41a">base.h</a></li>
<li>lzma_ret&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">base.h</a></li>
<li>LZMA_RUN&#160;:&#160;<a class="el" href="base_8h.html#aa92efcbf3cecfcac79c81fc645fce77ea868472b76492afcaef54020a481890b1">base.h</a></li>
<li>LZMA_SEEK_NEEDED&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea6cf28e5345851f13bd798a4eab8cc939">base.h</a></li>
<li>lzma_set_ext_size&#160;:&#160;<a class="el" href="lzma12_8h.html#a73ed0293db4e59d73a702d66fef537c3">lzma12.h</a></li>
<li>LZMA_STR_ALL_FILTERS&#160;:&#160;<a class="el" href="filter_8h.html#a41aa51eeb53190404439c31d8e9c97cd">filter.h</a></li>
<li>LZMA_STR_DECODER&#160;:&#160;<a class="el" href="filter_8h.html#a8a0f3fc03bdb84a294cdd53a98783104">filter.h</a></li>
<li>LZMA_STR_ENCODER&#160;:&#160;<a class="el" href="filter_8h.html#a09a775f6a78d28ca136acfb51ad5fa02">filter.h</a></li>
<li>lzma_str_from_filters()&#160;:&#160;<a class="el" href="filter_8h.html#a7deeb86ef59a9111b8033681290e0fb0">filter.h</a></li>
<li>LZMA_STR_GETOPT_LONG&#160;:&#160;<a class="el" href="filter_8h.html#a87e9ac4ae5829b092262223256141a29">filter.h</a></li>
<li>lzma_str_list_filters()&#160;:&#160;<a class="el" href="filter_8h.html#ab51585b68796ce0270f87e615b923809">filter.h</a></li>
<li>LZMA_STR_NO_SPACES&#160;:&#160;<a class="el" href="filter_8h.html#ac0113c47caf98a735db2297936c5e857">filter.h</a></li>
<li>LZMA_STR_NO_VALIDATION&#160;:&#160;<a class="el" href="filter_8h.html#adc33f4c0c7b5d3ae36acc0437a904339">filter.h</a></li>
<li>lzma_str_to_filters()&#160;:&#160;<a class="el" href="filter_8h.html#aa042cf11749bc2183b27de1c3142da30">filter.h</a></li>
<li>lzma_stream_buffer_bound()&#160;:&#160;<a class="el" href="container_8h.html#a66d4366a47b8332bff2a512f44f5c45e">container.h</a></li>
<li>lzma_stream_buffer_decode()&#160;:&#160;<a class="el" href="container_8h.html#aa58f237f6cea97ef0eb9bf5c37a3008d">container.h</a></li>
<li>lzma_stream_buffer_encode()&#160;:&#160;<a class="el" href="container_8h.html#a6e645ccaeace3b13a6981e03c6e190ad">container.h</a></li>
<li>lzma_stream_decoder()&#160;:&#160;<a class="el" href="container_8h.html#a02b7683ef98d8049788961370a8b28c0">container.h</a></li>
<li>lzma_stream_decoder_mt()&#160;:&#160;<a class="el" href="container_8h.html#a7179d178e6430c10e2006a467921e98e">container.h</a></li>
<li>lzma_stream_encoder()&#160;:&#160;<a class="el" href="container_8h.html#a1a97aec94c9fedd7646cfa51c4f4cd52">container.h</a></li>
<li>lzma_stream_encoder_mt()&#160;:&#160;<a class="el" href="container_8h.html#a3f8793518711ee84d1abf12ea3aaba42">container.h</a></li>
<li>lzma_stream_encoder_mt_memusage()&#160;:&#160;<a class="el" href="container_8h.html#ad7cf41496d77f4d346e006b26ed8e101">container.h</a></li>
<li>LZMA_STREAM_END&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea91ecc6fab14c13ad36224afbcb4e55c4">base.h</a></li>
<li>lzma_stream_flags_compare()&#160;:&#160;<a class="el" href="stream__flags_8h.html#a3e25ca4205021302882a696283d45263">stream_flags.h</a></li>
<li>lzma_stream_footer_decode()&#160;:&#160;<a class="el" href="stream__flags_8h.html#aa92a383f85753bb79ee23227fa68186c">stream_flags.h</a></li>
<li>lzma_stream_footer_encode()&#160;:&#160;<a class="el" href="stream__flags_8h.html#a438249a75ea8da952a7474b92bfe7b7a">stream_flags.h</a></li>
<li>lzma_stream_header_decode()&#160;:&#160;<a class="el" href="stream__flags_8h.html#ae03198e464f0d296e601ff841e100805">stream_flags.h</a></li>
<li>lzma_stream_header_encode()&#160;:&#160;<a class="el" href="stream__flags_8h.html#a2ebb8d6dff23daeb3de398913b845eff">stream_flags.h</a></li>
<li>LZMA_STREAM_HEADER_SIZE&#160;:&#160;<a class="el" href="stream__flags_8h.html#ada7e0a4f5e7146f547962cb9e9ef08ee">stream_flags.h</a></li>
<li>LZMA_STREAM_INIT&#160;:&#160;<a class="el" href="base_8h.html#af31f0c8b6f14359cd082b9559f7f3e01">base.h</a></li>
<li>LZMA_SYNC_FLUSH&#160;:&#160;<a class="el" href="base_8h.html#aa92efcbf3cecfcac79c81fc645fce77ea14d75152afcda85d215e877fdd9c4170">base.h</a></li>
<li>LZMA_TELL_ANY_CHECK&#160;:&#160;<a class="el" href="container_8h.html#a0bdde702a77ff42b90a99c0bf4147b6b">container.h</a></li>
<li>LZMA_TELL_NO_CHECK&#160;:&#160;<a class="el" href="container_8h.html#ada9cd20febb28b5ed6656de9184a86e9">container.h</a></li>
<li>LZMA_TELL_UNSUPPORTED_CHECK&#160;:&#160;<a class="el" href="container_8h.html#ae21fb746037c82735d40d428c462e078">container.h</a></li>
<li>LZMA_UNSUPPORTED_CHECK&#160;:&#160;<a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6ea989f393a1772d85bf545a9da48fc7ac2">base.h</a></li>
<li>LZMA_VERSION&#160;:&#160;<a class="el" href="version_8h.html#a156c47ff34aa0c2b726d0daf799f10a0">version.h</a></li>
<li>LZMA_VERSION_COMMIT&#160;:&#160;<a class="el" href="version_8h.html#a7fd6169ff15ac7f01f94970359a331ea">version.h</a></li>
<li>LZMA_VERSION_MAJOR&#160;:&#160;<a class="el" href="version_8h.html#aa0f450c9d3b0ff5f88b55888ed55701f">version.h</a></li>
<li>LZMA_VERSION_MINOR&#160;:&#160;<a class="el" href="version_8h.html#af8fd295cf8aa349b0731423ad7a56134">version.h</a></li>
<li>lzma_version_number()&#160;:&#160;<a class="el" href="version_8h.html#a72f929c9b9e8e730b790b3f8c80c3c80">version.h</a></li>
<li>LZMA_VERSION_PATCH&#160;:&#160;<a class="el" href="version_8h.html#a8b550373cbff381f15d4308b852a3c2a">version.h</a></li>
<li>LZMA_VERSION_STABILITY&#160;:&#160;<a class="el" href="version_8h.html#ae289abe5dcc203c7cda9f6a9a2f36b3a">version.h</a></li>
<li>LZMA_VERSION_STRING&#160;:&#160;<a class="el" href="version_8h.html#a57bb143c993c305a53e9aade831a546c">version.h</a></li>
<li>lzma_version_string()&#160;:&#160;<a class="el" href="version_8h.html#a8998c1d8b4b5c2c1218bdfd58fdb1baa">version.h</a></li>
<li>lzma_vli&#160;:&#160;<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">vli.h</a></li>
<li>LZMA_VLI_BYTES_MAX&#160;:&#160;<a class="el" href="vli_8h.html#a063ecff4133aa2f8899b9fa3fdefd310">vli.h</a></li>
<li>LZMA_VLI_C&#160;:&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">vli.h</a></li>
<li>lzma_vli_decode()&#160;:&#160;<a class="el" href="vli_8h.html#a7b7d50e1074e0e2bcd81c29a5f7461c7">vli.h</a></li>
<li>lzma_vli_encode()&#160;:&#160;<a class="el" href="vli_8h.html#a50bbb77e9ec3b72c25586aa700c20970">vli.h</a></li>
<li>lzma_vli_is_valid&#160;:&#160;<a class="el" href="vli_8h.html#a4f67ed698215d865a2b87a95ab1320dd">vli.h</a></li>
<li>LZMA_VLI_MAX&#160;:&#160;<a class="el" href="vli_8h.html#a7b782528bd1934db7c020adbedb20ec9">vli.h</a></li>
<li>lzma_vli_size()&#160;:&#160;<a class="el" href="vli_8h.html#a8d53e0b69934b43da8721fa6f1e8cc4f">vli.h</a></li>
<li>LZMA_VLI_UNKNOWN&#160;:&#160;<a class="el" href="vli_8h.html#a5a4b28254a30c859018b896ed371d69a">vli.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
