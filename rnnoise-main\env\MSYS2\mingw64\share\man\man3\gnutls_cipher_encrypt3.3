.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_cipher_encrypt3" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_cipher_encrypt3 \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_cipher_encrypt3(gnutls_cipher_hd_t " handle ", const void * " ptext ", size_t " ptext_len ", void * " ctext ", size_t * " ctext_len ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_cipher_hd_t handle" 12
is a \fBgnutls_cipher_hd_t\fP type
.IP "const void * ptext" 12
the data to encrypt
.IP "size_t ptext_len" 12
the length of data to encrypt
.IP "void * ctext" 12
the encrypted data
.IP "size_t * ctext_len" 12
the length of encrypted data (initially must hold the maximum available size)
.IP "unsigned flags" 12
flags for padding
.SH "DESCRIPTION"
This function will encrypt the given data using the algorithm
specified by the context. For block ciphers,  \fIptext_len\fP is
typically a multiple of the block size. If not, the caller can
instruct the function to pad the last block according to  \fIflags\fP .
Currently, the only available padding scheme is
\fBGNUTLS_CIPHER_PADDING_PKCS7\fP.

If  \fIctext\fP is not \fBNULL\fP, it must hold enough space to store
resulting cipher text. To check the required size, this function
can be called with  \fIctext\fP set to \fBNULL\fP. Then  \fIctext_len\fP will be
updated without performing actual encryption.
.SH "RETURNS"
Zero or a negative error code on error.
.SH "SINCE"
3.7.7
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
