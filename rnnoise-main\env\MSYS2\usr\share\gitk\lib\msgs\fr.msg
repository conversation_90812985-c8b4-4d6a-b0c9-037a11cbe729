set ::msgcat::header "Project-Id-Version: gitk\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2016-01-22 23:28+0100\nLast-Translator: <PERSON><PERSON><PERSON>\u00ebl <PERSON> <jn.a<PERSON>@free.fr>\nLanguage-Team: *******************\nLanguage: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nX-Poedit-Language: French\nX-Poedit-Country: FRANCE\n"
::msgcat::mcset fr "Couldn't get list of unmerged files:" "Impossible de r\u00e9cup\u00e9rer la liste des fichiers non fusionn\u00e9s :"
::msgcat::mcset fr "Color words" "Colorier les mots diff\u00e9rents"
::msgcat::mcset fr "Markup words" "Marquer les mots diff\u00e9rents"
::msgcat::mcset fr "Error parsing revisions:" "Erreur lors du parcours des r\u00e9visions :"
::msgcat::mcset fr "Error executing --argscmd command:" "Erreur \u00e0 l'ex\u00e9cution de la commande --argscmd :"
::msgcat::mcset fr "No files selected: --merge specified but no files are unmerged." "Aucun fichier s\u00e9lectionn\u00e9 : --merge pr\u00e9cis\u00e9, mais tous les fichiers sont fusionn\u00e9s."
::msgcat::mcset fr "No files selected: --merge specified but no unmerged files are within file limit." "Aucun fichier s\u00e9lectionn\u00e9 : --merge pr\u00e9cis\u00e9 mais aucun fichier non fusionn\u00e9 n'est dans la limite des fichiers."
::msgcat::mcset fr "Error executing git log:" "Erreur \u00e0 l'ex\u00e9cution de git log :"
::msgcat::mcset fr "Reading" "Lecture en cours"
::msgcat::mcset fr "Reading commits..." "Lecture des commits..."
::msgcat::mcset fr "No commits selected" "Aucun commit s\u00e9lectionn\u00e9"
::msgcat::mcset fr "Command line" "Ligne de commande"
::msgcat::mcset fr "Can't parse git log output:" "Impossible de lire la sortie de git log :"
::msgcat::mcset fr "No commit information available" "Aucune information disponible sur le commit"
::msgcat::mcset fr "OK" "OK"
::msgcat::mcset fr "Cancel" "Annuler"
::msgcat::mcset fr "&Update" "Mise \u00e0 jour"
::msgcat::mcset fr "&Reload" "&Recharger"
::msgcat::mcset fr "Reread re&ferences" "Relire les r\u00e9&f\u00e9rences"
::msgcat::mcset fr "&List references" "&Lister les r\u00e9f\u00e9rences"
::msgcat::mcset fr "Start git &gui" "D\u00e9marrer git &gui"
::msgcat::mcset fr "&Quit" "&Quitter"
::msgcat::mcset fr "&File" "&Fichier"
::msgcat::mcset fr "&Preferences" "Pr\u00e9f\u00e9rences"
::msgcat::mcset fr "&Edit" "&\u00c9diter"
::msgcat::mcset fr "&New view..." "&Nouvelle vue..."
::msgcat::mcset fr "&Edit view..." "&\u00c9diter la vue..."
::msgcat::mcset fr "&Delete view" "Supprimer la vue"
::msgcat::mcset fr "&All files" "Tous les fichiers"
::msgcat::mcset fr "&View" "&Vue"
::msgcat::mcset fr "&About gitk" "\u00c0 propos de gitk"
::msgcat::mcset fr "&Key bindings" "Raccourcis clavier"
::msgcat::mcset fr "&Help" "Aide"
::msgcat::mcset fr "SHA1 ID:" "Id SHA1 :"
::msgcat::mcset fr "Row" "Colonne"
::msgcat::mcset fr "Find" "Recherche"
::msgcat::mcset fr "commit" "commit"
::msgcat::mcset fr "containing:" "contient :"
::msgcat::mcset fr "touching paths:" "chemins modifi\u00e9s :"
::msgcat::mcset fr "adding/removing string:" "ajoute/supprime la cha\u00eene :"
::msgcat::mcset fr "changing lines matching:" "modifie les lignes v\u00e9rifiant\u00a0:"
::msgcat::mcset fr "Exact" "Exact"
::msgcat::mcset fr "IgnCase" "Ignorer la casse"
::msgcat::mcset fr "Regexp" "Expression r\u00e9guli\u00e8re"
::msgcat::mcset fr "All fields" "Tous les champs"
::msgcat::mcset fr "Headline" "Titre"
::msgcat::mcset fr "Comments" "Commentaires"
::msgcat::mcset fr "Author" "Auteur"
::msgcat::mcset fr "Committer" "Validateur"
::msgcat::mcset fr "Search" "Rechercher"
::msgcat::mcset fr "Diff" "Diff"
::msgcat::mcset fr "Old version" "Ancienne version"
::msgcat::mcset fr "New version" "Nouvelle version"
::msgcat::mcset fr "Lines of context" "Lignes de contexte"
::msgcat::mcset fr "Ignore space change" "Ignorer les modifications d'espace"
::msgcat::mcset fr "Line diff" "diff\u00e9rence par ligne"
::msgcat::mcset fr "Patch" "Patch"
::msgcat::mcset fr "Tree" "Arbre"
::msgcat::mcset fr "Diff this -> selected" "Diff ceci -> la s\u00e9lection"
::msgcat::mcset fr "Diff selected -> this" "Diff s\u00e9lection -> ceci"
::msgcat::mcset fr "Make patch" "Cr\u00e9er patch"
::msgcat::mcset fr "Create tag" "Cr\u00e9er \u00e9tiquette"
::msgcat::mcset fr "Copy commit summary" "Copi\u00e9 le r\u00e9sum\u00e9 du commit"
::msgcat::mcset fr "Write commit to file" "\u00c9crire le commit dans un fichier"
::msgcat::mcset fr "Create new branch" "Cr\u00e9er une nouvelle branche"
::msgcat::mcset fr "Cherry-pick this commit" "Cueillir (cherry-pick) ce commit"
::msgcat::mcset fr "Reset HEAD branch to here" "R\u00e9initialiser la branche HEAD vers cet \u00e9tat"
::msgcat::mcset fr "Mark this commit" "Marquer ce commit"
::msgcat::mcset fr "Return to mark" "Retourner \u00e0 la marque"
::msgcat::mcset fr "Find descendant of this and mark" "Chercher le descendant de ceci et le marquer"
::msgcat::mcset fr "Compare with marked commit" "Comparer avec le commit marqu\u00e9"
::msgcat::mcset fr "Diff this -> marked commit" "Diff ceci -> s\u00e9lection"
::msgcat::mcset fr "Diff marked commit -> this" "Diff entre s\u00e9lection -> ceci"
::msgcat::mcset fr "Revert this commit" "D\u00e9faire ce commit"
::msgcat::mcset fr "Check out this branch" "R\u00e9cup\u00e9rer cette branche"
::msgcat::mcset fr "Remove this branch" "Supprimer cette branche"
::msgcat::mcset fr "Copy branch name" "Copier la nom de la branche"
::msgcat::mcset fr "Highlight this too" "Surligner \u00e9galement ceci"
::msgcat::mcset fr "Highlight this only" "Surligner seulement ceci"
::msgcat::mcset fr "External diff" "Diff externe"
::msgcat::mcset fr "Blame parent commit" "Bl\u00e2mer le commit parent"
::msgcat::mcset fr "Copy path" "Copier le chemin"
::msgcat::mcset fr "Show origin of this line" "Montrer l'origine de cette ligne"
::msgcat::mcset fr "Run git gui blame on this line" "Ex\u00e9cuter git gui blame sur cette ligne"
::msgcat::mcset fr "About gitk" "\u00c0 propos de gitk"
::msgcat::mcset fr "\nGitk - a commit viewer for git\n\nCopyright \u00a9 2005-2016 Paul Mackerras\n\nUse and redistribute under the terms of the GNU General Public License" "\nGitk - visualisateur de commit pour git\n\nCopyright \\u00a9 2005-2016 Paul Mackerras\n\nUtilisation et redistribution soumises aux termes de la GNU General Public License"
::msgcat::mcset fr "Close" "Fermer"
::msgcat::mcset fr "Gitk key bindings" "Raccourcis clavier de Gitk"
::msgcat::mcset fr "Gitk key bindings:" "Raccourcis clavier de Gitk :"
::msgcat::mcset fr "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009Quitter"
::msgcat::mcset fr "<%s-W>\u0009\u0009Close window" "<%s-W>\u0009\u0009Fermer la fen\u00eatre"
::msgcat::mcset fr "<Home>\u0009\u0009Move to first commit" "<D\u00e9but>\u0009\u0009Aller au premier commit"
::msgcat::mcset fr "<End>\u0009\u0009Move to last commit" "<Fin>\u0009\u0009Aller au dernier commit"
::msgcat::mcset fr "<Up>, p, k\u0009Move up one commit" "<Haut>, p, k\u0009 Aller au commit pr\u00e9c\u00e9dent"
::msgcat::mcset fr "<Down>, n, j\u0009Move down one commit" "<Bas>, n, j\u0009 Aller au commit suivant"
::msgcat::mcset fr "<Left>, z, h\u0009Go back in history list" "<Gauche>, z, h\u0009Reculer dans l'historique"
::msgcat::mcset fr "<Right>, x, l\u0009Go forward in history list" "<Droite>, x, l\u0009Avancer dans l'historique"
::msgcat::mcset fr "<%s-n>\u0009Go to n-th parent of current commit in history list" "<%s-n>\u0009Aller sur le n-i\u00e8me parent du commit dans l'historique"
::msgcat::mcset fr "<PageUp>\u0009Move up one page in commit list" "<PageUp>\u0009Monter d'une page dans la liste des commits"
::msgcat::mcset fr "<PageDown>\u0009Move down one page in commit list" "<PageDown>\u0009Descendre d'une page dans la liste des commits"
::msgcat::mcset fr "<%s-Home>\u0009Scroll to top of commit list" "<%s-D\u00e9but>\u0009Aller en haut de la liste des commits"
::msgcat::mcset fr "<%s-End>\u0009Scroll to bottom of commit list" "<%s-End>\u0009Aller en bas de la liste des commits"
::msgcat::mcset fr "<%s-Up>\u0009Scroll commit list up one line" "<%s-Up>\u0009Monter d'une ligne dans la liste des commits"
::msgcat::mcset fr "<%s-Down>\u0009Scroll commit list down one line" "<%s-Down>\u0009Descendre d'une ligne dans la liste des commits"
::msgcat::mcset fr "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-PageUp>\u0009Monter d'une page dans la liste des commits"
::msgcat::mcset fr "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-PageDown>\u0009Descendre d'une page dans la liste des commits"
::msgcat::mcset fr "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Shift-Up>\u0009Recherche en arri\u00e8re (vers l'avant, commits les plus anciens)"
::msgcat::mcset fr "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Shift-Down>\u0009Recherche en avant (vers l'arri\u00e8re, commit les plus r\u00e9cents)"
::msgcat::mcset fr "<Delete>, b\u0009Scroll diff view up one page" "<Supprimer>, b\u0009Monter d'une page dans la vue des diff"
::msgcat::mcset fr "<Backspace>\u0009Scroll diff view up one page" "<Backspace>\u0009Monter d'une page dans la vue des diff"
::msgcat::mcset fr "<Space>\u0009\u0009Scroll diff view down one page" "<Espace>\u0009\u0009Descendre d'une page dans la vue des diff"
::msgcat::mcset fr "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u0009Monter de 18 lignes dans la vue des diff"
::msgcat::mcset fr "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u0009Descendre de 18 lignes dans la vue des diff"
::msgcat::mcset fr "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009Rechercher"
::msgcat::mcset fr "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009Aller au r\u00e9sultat de recherche suivant"
::msgcat::mcset fr "<Return>\u0009Move to next find hit" "<Return>\u0009\u0009Aller au r\u00e9sultat de recherche suivant"
::msgcat::mcset fr "g\u0009\u0009Go to commit" "g\u0009\u0009Aller au commit"
::msgcat::mcset fr "/\u0009\u0009Focus the search box" "/\u0009\u0009Focus sur la zone de recherche"
::msgcat::mcset fr "?\u0009\u0009Move to previous find hit" "?\u0009\u0009Aller au r\u00e9sultat de recherche pr\u00e9c\u00e9dent"
::msgcat::mcset fr "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009Aller au prochain fichier dans la vue des diff"
::msgcat::mcset fr "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009Aller au r\u00e9sultat suivant dans la vue des diff"
::msgcat::mcset fr "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009Aller au r\u00e9sultat pr\u00e9c\u00e9dent dans la vue des diff"
::msgcat::mcset fr "<%s-KP+>\u0009Increase font size" "<%s-KP+>\u0009Augmenter la taille de la police"
::msgcat::mcset fr "<%s-plus>\u0009Increase font size" "<%s-plus>\u0009Augmenter la taille de la police"
::msgcat::mcset fr "<%s-KP->\u0009Decrease font size" "<%s-KP->\u0009Diminuer la taille de la police"
::msgcat::mcset fr "<%s-minus>\u0009Decrease font size" "<%s-minus>\u0009Diminuer la taille de la police"
::msgcat::mcset fr "<F5>\u0009\u0009Update" "<F5>\u0009\u0009Mise \u00e0 jour"
::msgcat::mcset fr "Error creating temporary directory %s:" "Erreur lors de la cr\u00e9ation du r\u00e9pertoire temporaire %s :"
::msgcat::mcset fr "Error getting \"%s\" from %s:" "Erreur en obtenant \"%s\" de %s:"
::msgcat::mcset fr "command failed:" "\u00e9chec de la commande :"
::msgcat::mcset fr "No such commit" "Commit inexistant"
::msgcat::mcset fr "git gui blame: command failed:" "git gui blame : \u00e9chec de la commande :"
::msgcat::mcset fr "Couldn't read merge head: %s" "Impossible de lire le head de la fusion : %s"
::msgcat::mcset fr "Error reading index: %s" "Erreur \u00e0 la lecture de l'index : %s"
::msgcat::mcset fr "Couldn't start git blame: %s" "Impossible de d\u00e9marrer git blame : %s"
::msgcat::mcset fr "Searching" "Recherche en cours"
::msgcat::mcset fr "Error running git blame: %s" "Erreur \u00e0 l'ex\u00e9cution de git blame : %s"
::msgcat::mcset fr "That line comes from commit %s,  which is not in this view" "Cette ligne est issue du commit %s, qui n'est pas dans cette vue"
::msgcat::mcset fr "External diff viewer failed:" "\u00c9chec de l'outil externe de visualisation des diff\u00a0:"
::msgcat::mcset fr "All files" "Tous les fichiers"
::msgcat::mcset fr "View" "Vue"
::msgcat::mcset fr "Gitk view definition" "D\u00e9finition des vues de Gitk"
::msgcat::mcset fr "Remember this view" "Se souvenir de cette vue"
::msgcat::mcset fr "References (space separated list):" "R\u00e9f\u00e9rences (liste d'\u00e9l\u00e9ments s\u00e9par\u00e9s par des espaces) :"
::msgcat::mcset fr "Branches & tags:" "Branches & \u00e9tiquettes :"
::msgcat::mcset fr "All refs" "Toutes les r\u00e9f\u00e9rences"
::msgcat::mcset fr "All (local) branches" "Toutes les branches (locales)"
::msgcat::mcset fr "All tags" "Toutes les \u00e9tiquettes"
::msgcat::mcset fr "All remote-tracking branches" "Toutes les branches de suivi \u00e0 distance"
::msgcat::mcset fr "Commit Info (regular expressions):" "Info sur les commits (expressions r\u00e9guli\u00e8res) :"
::msgcat::mcset fr "Author:" "Auteur :"
::msgcat::mcset fr "Committer:" "Validateur :"
::msgcat::mcset fr "Commit Message:" "Message de commit :"
::msgcat::mcset fr "Matches all Commit Info criteria" "Correspond \u00e0 tous les crit\u00e8res d'Info sur les commits"
::msgcat::mcset fr "Matches no Commit Info criteria" "Ne correspond \u00e0 aucun des crit\u00e8res d'Info sur les commits"
::msgcat::mcset fr "Changes to Files:" "Changements des fichiers :"
::msgcat::mcset fr "Fixed String" "Cha\u00eene Fig\u00e9e"
::msgcat::mcset fr "Regular Expression" "Expression R\u00e9guli\u00e8re"
::msgcat::mcset fr "Search string:" "Recherche de la cha\u00eene :"
::msgcat::mcset fr "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "Dates des commits (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\") :"
::msgcat::mcset fr "Since:" "Depuis :"
::msgcat::mcset fr "Until:" "Jusqu'au :"
::msgcat::mcset fr "Limit and/or skip a number of revisions (positive integer):" "Limiter et/ou sauter un certain nombre (entier positif) de r\u00e9visions :"
::msgcat::mcset fr "Number to show:" "Nombre \u00e0 afficher :"
::msgcat::mcset fr "Number to skip:" "Nombre \u00e0 sauter :"
::msgcat::mcset fr "Miscellaneous options:" "Options diverses :"
::msgcat::mcset fr "Strictly sort by date" "Trier par date"
::msgcat::mcset fr "Mark branch sides" "Indiquer les c\u00f4t\u00e9s des branches"
::msgcat::mcset fr "Limit to first parent" "Limiter au premier anc\u00eatre"
::msgcat::mcset fr "Simple history" "Historique simple"
::msgcat::mcset fr "Additional arguments to git log:" "Arguments suppl\u00e9mentaires de git log :"
::msgcat::mcset fr "Enter files and directories to include, one per line:" "Saisir les fichiers et r\u00e9pertoires \u00e0 inclure, un par ligne :"
::msgcat::mcset fr "Command to generate more commits to include:" "Commande pour g\u00e9n\u00e9rer plus de commits \u00e0 inclure :"
::msgcat::mcset fr "Gitk: edit view" "Gitk : \u00e9diter la vue"
::msgcat::mcset fr "-- criteria for selecting revisions" "-- crit\u00e8re pour la s\u00e9lection des r\u00e9visions"
::msgcat::mcset fr "View Name" "Nom de la vue"
::msgcat::mcset fr "Apply (F5)" "Appliquer (F5)"
::msgcat::mcset fr "Error in commit selection arguments:" "Erreur dans les arguments de s\u00e9lection des commits :"
::msgcat::mcset fr "None" "Aucun"
::msgcat::mcset fr "Descendant" "Descendant"
::msgcat::mcset fr "Not descendant" "Pas un descendant"
::msgcat::mcset fr "Ancestor" "Anc\u00eatre"
::msgcat::mcset fr "Not ancestor" "Pas un anc\u00eatre"
::msgcat::mcset fr "Local changes checked in to index but not committed" "Modifications locales enregistr\u00e9es dans l'index mais non valid\u00e9es"
::msgcat::mcset fr "Local uncommitted changes, not checked in to index" "Modifications locales non enregistr\u00e9es dans l'index et non valid\u00e9es"
::msgcat::mcset fr "and many more" "et beaucoup plus"
::msgcat::mcset fr "many" "nombreux"
::msgcat::mcset fr "Tags:" "\u00c9tiquettes :"
::msgcat::mcset fr "Parent" "Parent"
::msgcat::mcset fr "Child" "Enfant"
::msgcat::mcset fr "Branch" "Branche"
::msgcat::mcset fr "Follows" "Suit"
::msgcat::mcset fr "Precedes" "Pr\u00e9c\u00e8de"
::msgcat::mcset fr "Error getting diffs: %s" "Erreur lors de la r\u00e9cup\u00e9ration des diff : %s"
::msgcat::mcset fr "Goto:" "Aller \u00e0 :"
::msgcat::mcset fr "Short SHA1 id %s is ambiguous" "L'id SHA1 court %s est ambigu"
::msgcat::mcset fr "Revision %s is not known" "La r\u00e9vision %s est inconnu"
::msgcat::mcset fr "SHA1 id %s is not known" "L'id SHA1 %s est inconnu"
::msgcat::mcset fr "Revision %s is not in the current view" "La r\u00e9vision %s n'est pas dans la vue courante"
::msgcat::mcset fr "Date" "Date"
::msgcat::mcset fr "Children" "Enfants"
::msgcat::mcset fr "Reset %s branch to here" "R\u00e9initialiser la branche %s vers cet \u00e9tat"
::msgcat::mcset fr "Detached head: can't reset" "Head d\u00e9tach\u00e9 : impossible de r\u00e9initialiser"
::msgcat::mcset fr "Skipping merge commit " "\u00c9viter le commit de la fusion "
::msgcat::mcset fr "Error getting patch ID for " "Erreur \u00e0 l'obtention de l'ID du patch pour "
::msgcat::mcset fr " - stopping\n" " - arr\u00eat en cours\n"
::msgcat::mcset fr "Commit " "Commit "
::msgcat::mcset fr " is the same patch as\n       " "est le m\u00eame patch que \n       "
::msgcat::mcset fr " differs from\n       " " diff\u00e8re de\n       "
::msgcat::mcset fr "Diff of commits:\n\n" "Diff des commits\u00a0:\n\n"
::msgcat::mcset fr " has %s children - stopping\n" " a %s enfants - arr\u00eat en cours\n"
::msgcat::mcset fr "Error writing commit to file: %s" "Erreur \u00e0 l'\u00e9criture du commit dans le fichier : %s"
::msgcat::mcset fr "Error diffing commits: %s" "Erreur \u00e0 la diff\u00e9rence des commits : %s"
::msgcat::mcset fr "Top" "Haut"
::msgcat::mcset fr "From" "De"
::msgcat::mcset fr "To" "\u00c0"
::msgcat::mcset fr "Generate patch" "G\u00e9n\u00e9rer le patch"
::msgcat::mcset fr "From:" "De :"
::msgcat::mcset fr "To:" "\u00c0 :"
::msgcat::mcset fr "Reverse" "Inverser"
::msgcat::mcset fr "Output file:" "Fichier de sortie :"
::msgcat::mcset fr "Generate" "G\u00e9n\u00e9rer"
::msgcat::mcset fr "Error creating patch:" "Erreur \u00e0 la cr\u00e9ation du patch :"
::msgcat::mcset fr "ID:" "ID :"
::msgcat::mcset fr "Tag name:" "Nom de l'\u00e9tiquette :"
::msgcat::mcset fr "Tag message is optional" "Le message d'\u00e9tiquette est optionnel"
::msgcat::mcset fr "Tag message:" "Message d'\u00e9tiquette :"
::msgcat::mcset fr "Create" "Cr\u00e9er"
::msgcat::mcset fr "No tag name specified" "Aucun nom d'\u00e9tiquette sp\u00e9cifi\u00e9"
::msgcat::mcset fr "Tag \"%s\" already exists" "L'\u00e9tiquette \"%s\" existe d\u00e9j\u00e0"
::msgcat::mcset fr "Error creating tag:" "Erreur \u00e0 la cr\u00e9ation de l'\u00e9tiquette :"
::msgcat::mcset fr "Command:" "Commande :"
::msgcat::mcset fr "Write" "\u00c9crire"
::msgcat::mcset fr "Error writing commit:" "Erreur \u00e0 l'ecriture du commit :"
::msgcat::mcset fr "Name:" "Nom :"
::msgcat::mcset fr "Please specify a name for the new branch" "Veuillez sp\u00e9cifier un nom pour la nouvelle branche"
::msgcat::mcset fr "Branch '%s' already exists. Overwrite?" "La branche '%s' existe d\u00e9j\u00e0. \u00c9craser?"
::msgcat::mcset fr "Commit %s is already included in branch %s -- really re-apply it?" "Le Commit %s est d\u00e9j\u00e0 inclus dans la branche %s -- le r\u00e9-appliquer malgr\u00e9 tout?"
::msgcat::mcset fr "Cherry-picking" "Picorer (Cherry-picking)"
::msgcat::mcset fr "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "Le picorage (cherry-pick) a \u00e9chou\u00e9e \u00e0 cause de modifications locales du fichier '%s'.\nVeuillez commiter, r\u00e9initialiser ou stasher vos changements et essayer de nouveau."
::msgcat::mcset fr "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "Le picorage (cherry-pick) a \u00e9chou\u00e9e \u00e0 cause d'un conflit lors d'une fusion.\nSouhaitez-vous ex\u00e9cuter git citool pour le r\u00e9soudre ?"
::msgcat::mcset fr "No changes committed" "Aucune modification valid\u00e9e"
::msgcat::mcset fr "Commit %s is not included in branch %s -- really revert it?" "Le Commit %s n'est pas inclus dans la branche %s -- le d\u00e9faire malgr\u00e9 tout?"
::msgcat::mcset fr "Reverting" "Commit d\u00e9fait"
::msgcat::mcset fr "Revert failed because of local changes to the following files:%s Please commit, reset or stash  your changes and try again." "\u00c9chec en tentant de d\u00e9faire le commit \u00e0 cause de modifications locales des fichiers\u00a0: %s. Veuillez valider, r\u00e9initialiser ou remiser vos modifications et essayer de nouveau."
::msgcat::mcset fr "Revert failed because of merge conflict.\n Do you wish to run git citool to resolve it?" "\u00c9chec en tentant de d\u00e9faire \u00e0 cause d'un conflit de fusion.\nSouhaitez-vous ex\u00e9cuter git citool pour le r\u00e9soudre ?"
::msgcat::mcset fr "Confirm reset" "Confirmer la r\u00e9initialisation"
::msgcat::mcset fr "Reset branch %s to %s?" "R\u00e9initialiser la branche %s \u00e0 %s?"
::msgcat::mcset fr "Reset type:" "Type de r\u00e9initialisation :"
::msgcat::mcset fr "Soft: Leave working tree and index untouched" "Douce : Laisse le r\u00e9pertoire de travail et l'index intacts"
::msgcat::mcset fr "Mixed: Leave working tree untouched, reset index" "Hybride : Laisse le r\u00e9pertoire de travail dans son \u00e9tat courant, r\u00e9initialise l'index"
::msgcat::mcset fr "Hard: Reset working tree and index\n(discard ALL local changes)" "Dure : R\u00e9initialise le r\u00e9pertoire de travail et l'index\n(abandonne TOUTES les modifications locale)"
::msgcat::mcset fr "Resetting" "R\u00e9initialisation"
::msgcat::mcset fr "Checking out" "Extraction"
::msgcat::mcset fr "Cannot delete the currently checked-out branch" "Impossible de supprimer la branche extraite"
::msgcat::mcset fr "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "Les commits de la branche %s ne sont dans aucune autre branche.\nVoulez-vous vraiment supprimer cette branche %s ?"
::msgcat::mcset fr "Tags and heads: %s" "\u00c9tiquettes et heads : %s"
::msgcat::mcset fr "Filter" "Filtrer"
::msgcat::mcset fr "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "Erreur \u00e0 la lecture des informations sur la topologie des commits, les informations sur les branches et les tags pr\u00e9c\u00e9dents/suivants seront incompl\u00e8tes."
::msgcat::mcset fr "Tag" "\u00c9tiquette"
::msgcat::mcset fr "Id" "Id"
::msgcat::mcset fr "Gitk font chooser" "S\u00e9lecteur de police de Gitk"
::msgcat::mcset fr "B" "B"
::msgcat::mcset fr "I" "I"
::msgcat::mcset fr "Commit list display options" "Options d'affichage de la liste des commits"
::msgcat::mcset fr "Maximum graph width (lines)" "Longueur maximum du graphe (lignes)"
::msgcat::mcset fr "Maximum graph width (% of pane)" "Largeur maximum du graphe (% du panneau)"
::msgcat::mcset fr "Show local changes" "Montrer les modifications locales"
::msgcat::mcset fr "Auto-select SHA1 (length)" "S\u00e9lection auto. du SHA1 (longueur)"
::msgcat::mcset fr "Hide remote refs" "Cacher les refs distantes"
::msgcat::mcset fr "Diff display options" "Options d'affichage des diff"
::msgcat::mcset fr "Tab spacing" "Taille des tabulations"
::msgcat::mcset fr "Display nearby tags/heads" "Afficher les tags les plus proches"
::msgcat::mcset fr "Maximum # tags/heads to show" "Nombre maximum d'\u00e9tiquettes/heads \u00e0 afficher"
::msgcat::mcset fr "Limit diffs to listed paths" "Limiter les diff\u00e9rences aux chemins list\u00e9s"
::msgcat::mcset fr "Support per-file encodings" "Support pour un encodage des caract\u00e8res par fichier"
::msgcat::mcset fr "External diff tool" "Outil diff externe"
::msgcat::mcset fr "Choose..." "Choisir..."
::msgcat::mcset fr "General options" "Options g\u00e9n\u00e9rales"
::msgcat::mcset fr "Use themed widgets" "Utiliser des widgets en th\u00e8me"
::msgcat::mcset fr "(change requires restart)" "(la modification n\u00e9cessite un red\u00e9marrage)"
::msgcat::mcset fr "(currently unavailable)" "(non disponible actuellement)"
::msgcat::mcset fr "Colors: press to choose" "Couleurs : cliquer pour choisir"
::msgcat::mcset fr "Interface" "Interface"
::msgcat::mcset fr "interface" "interface"
::msgcat::mcset fr "Background" "Arri\u00e8re-plan"
::msgcat::mcset fr "background" "arri\u00e8re-plan"
::msgcat::mcset fr "Foreground" "Premier plan"
::msgcat::mcset fr "foreground" "premier plan"
::msgcat::mcset fr "Diff: old lines" "Diff : anciennes lignes"
::msgcat::mcset fr "diff old lines" "diff anciennes lignes"
::msgcat::mcset fr "Diff: new lines" "Diff : nouvelles lignes"
::msgcat::mcset fr "diff new lines" "diff nouvelles lignes"
::msgcat::mcset fr "Diff: hunk header" "Diff : ent\u00eate du hunk"
::msgcat::mcset fr "diff hunk header" "diff : ent\u00eate du hunk"
::msgcat::mcset fr "Marked line bg" "Fond de la ligne marqu\u00e9e"
::msgcat::mcset fr "marked line background" "Fond de la ligne marqu\u00e9e"
::msgcat::mcset fr "Select bg" "S\u00e9lectionner le fond"
::msgcat::mcset fr "Fonts: press to choose" "Polices : cliquer pour choisir"
::msgcat::mcset fr "Main font" "Police principale"
::msgcat::mcset fr "Diff display font" "Police d'affichage des diff"
::msgcat::mcset fr "User interface font" "Police de l'interface utilisateur"
::msgcat::mcset fr "Gitk preferences" "Pr\u00e9f\u00e9rences de Gitk"
::msgcat::mcset fr "General" "G\u00e9n\u00e9ral"
::msgcat::mcset fr "Colors" "Couleurs"
::msgcat::mcset fr "Fonts" "Polices"
::msgcat::mcset fr "Gitk: choose color for %s" "Gitk : choisir la couleur de %s"
::msgcat::mcset fr "Sorry, gitk cannot run with this version of Tcl/Tk.\n Gitk requires at least Tcl/Tk 8.4." "D\u00e9sol\u00e9, gitk ne peut \u00eatre ex\u00e9cut\u00e9 avec cette version de Tcl/Tk.\n Gitk requiert Tcl/Tk version 8.4 ou sup\u00e9rieur."
::msgcat::mcset fr "Cannot find a git repository here." "Impossible de trouver un d\u00e9p\u00f4t git ici."
::msgcat::mcset fr "Ambiguous argument '%s': both revision and filename" "Argument '%s' ambigu : \u00e0 la fois une r\u00e9vision et un nom de fichier"
::msgcat::mcset fr "Bad arguments to gitk:" "Arguments invalides pour gitk :"
