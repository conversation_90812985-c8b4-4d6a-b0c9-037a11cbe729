# help.fi.txt - fi GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


.gpg.edit_ownertrust.value
Tämän arvon määrittäminen on sinun tehtäväsi, tätä arvoa ei koskaan 
kerrota kolmansille osapuolille. Tarvitsemme sitä toteuttamaan 
luottamusverkko eikä sillä ei ole mitään tekemistä (epäsuorasti luotujen) 
varmenneverkkojen kanssa.
.

.gpg.edit_ownertrust.set_ultimate.okay
Rakentaakseen luottamusverkon, GnuPG:n täytyy tietää mihin avaimiin 
luotetaan ehdottomasti - nämä ovat tavallisesti ne avaimet, joiden salainen 
pari on sinulla.  Vastaa "kyllä" luottaaksesi tähän avaimeen ehdoitta

.

.gpg.untrusted_key.override
Vastaa "kyllä" jos haluat kaikesta huolimatta käyttää tätä epäluotettavaa
avainta.
.

.gpg.pklist.user_id.enter
Syötä vastaanottajan, jolle haluat lähettää viestin, käyttäjätunnus.
.

.#gpg.keygen.algo
# fixme: Please translate and remove the hash mark from the key line.
Select the algorithm to use.

DSA (aka DSS) is the Digital Signature Algorithm and can only be used
for signatures.

Elgamal is an encrypt-only algorithm.

RSA may be used for signatures or encryption.

The first (primary) key must always be a key which is capable of signing.
.

.gpg.keygen.algo.rsa_se
Yleensä ei ole järkevää käyttää samaa avainta allekirjoitukseen
ja salaamiseen. Tätä algorimiä tulisi käyttää vain määrätyissä ympäristöissä.
Ole hyvä ja kysy tietoturva-asiantuntijaltasi ensin
.

.gpg.keygen.size
Syötä avaimen koko
.

.gpg.keygen.size.huge.okay
Vastaa "kyllä" tai " ei"
.

.gpg.keygen.size.large.okay
Vastaa "kyllä" tai " ei"
.

.gpg.keygen.valid
Syötä pyydetty arvo kuten näkyy kehotteessa.
On mahdollista syöttää ISO-muotoinen päivä (VVVV-KK-PP),
mutta sen seurauksena et saa kunnollista virheilmoitusta 
vaan järjestelmä yrittää tulkita arvon aikajaksona.
.

.gpg.keygen.valid.okay
Vastaa "kyllä" tai " ei"
.

.gpg.keygen.name
Anna avaimen haltijan nimi
.

.gpg.keygen.email
anna vapaaehtoinen, mutta erittäin suositeltava sähköpostiosoite
.

.gpg.keygen.comment
Kirjoita vapaaehtoinen huomautus
.

.gpg.keygen.userid.cmd
N   muuta nimeä
C   muuta kommenttia
E   muuta sähköpostiosoitetta
O   jatka avaimen luomista
L   lopeta
.

.gpg.keygen.sub.okay
Vastaa "kyllä" (tai vain "k") jos haluat luoda aliavaimen.
.

.gpg.sign_uid.okay
Vastaa "kyllä" tai " ei"
.

.gpg.sign_uid.class
Allekirjoittaessasi avaimen käyttäjätunnuksen sinun tulisi varmista, että 
avain todella kuuluu henkilölle, joka mainitaan käyttäjätunnuksessa.  Muiden 
on hyvä tietää kuinka huolellisesti olet varmistanut tämän. 

"0" tarkoittaa, että et väitä mitään siitä, kuinka huolellisesti olet
    varmistanut avaimen.

"1" tarkoittaa, että uskot avaimen kuuluvan henkilölle, joka väittää 
    hallitsevan sitä, mutta et voinut varmistaa tai et varmistanut avainta 
    lainkaan.  Tämä on hyödyllinen "persoonan" varmistamiseen, jossa 
    allekirjoitat pseudonyymin käyttäjän avaimen.

"2" tarkoittaa arkista varmistusta.  Esimerkiksi olet varmistanut 
    avaimen sormenjäljen ja tarkistanut käyttäjätunnuksen ja 
    valokuvatunnisteen täsmäävän.

"3" tarkoittaa syvällistä henkilöllisyyden varmistamista.  Esimerkiksi 
    tämä voi tarkoittaa avaimen sormenjäljen tarkistamista avaimen haltijan 
    kanssa henkilökohtaisesti, ja että tarkistit nimen avaimessa täsmäävän 
    vaikeasti väärennettävän kuvallisen henkilöllisyystodistuksen (kuten 
    passi) kanssa, ja lopuksi varmistit (sähköpostin vaihtamisella), että 
    sähköpostiosoite kuuluu avaimen haltijalle.

Huomaa, että yllä annetut esimerkit tasoille 2 ja 3 ovat todellakin *vain* 
esimerkkejä.  Lopullisesti se on sinun päätöksesi mitä "arkinen" ja 
"syvällinen" tarkoittaa allekirjoittaessasi muita avaimia.

Jos et tiedä mikä olisi sopiva vastaus, vastaa "0".
.

.gpg.change_passwd.empty.okay
Vastaa "kyllä" tai " ei"
.

.gpg.keyedit.save.okay
Vastaa "kyllä" tai " ei"
.

.gpg.keyedit.cancel.okay
Vastaa "kyllä" tai " ei"
.

.#gpg.keyedit.sign_all.okay
# fixme: Please translate and remove the hash mark from the key line.
Answer "yes" if you want to sign ALL the user IDs
.

.gpg.keyedit.remove.uid.okay
Vastaa "kyllä", jos haluat poistaa tämän käyttäjätunnuksen.
Menetät samalla kaikki siihen liittyvät varmenteet!
.

.gpg.keyedit.remove.subkey.okay
Vastaa "kyllä", jos aliavaimen voi poistaa
.

.gpg.keyedit.delsig.valid
Tämä on voimassa oleva allekirjoitus tälle avaimelle, tavallisesti ei 
kannata poistaa tätä allekirjoitusta koska se saattaa olla tarpeen
luottamussuhteen luomiseksi avaimeen tai johonkin toiseen tämän avaimen
varmentamaan avaimeen.
.

.gpg.keyedit.delsig.unknown
Allekirjoitusta ei voida tarkistaa koska sinulla ei ole 
siihen liittyvää avainta. Lykkää sen poistamista kunnes
 tiedät mitä avainta on käytetty, koska allekirjoitus 
avain saattaa luoda luottamusketjun toisen, jo ennalta 
varmennetun avaimen kautta.
.

.gpg.keyedit.delsig.invalid
Allekirjoitus ei ole pätevä.  Järkevintä olisi poistaa se 
avainrenkaastasi.
.

.gpg.keyedit.delsig.selfsig
Tämä allekirjoitus takaa avaimen haltijan henkilöllisyyden. 
Tällaisen allekirjoituksen poistaminen on tavallisesti huono 
ajatus.  GnuPG ei kenties voi käyttää avainta enää.  Poista 
allekirjoitus vain, jos se ei ole jostain syystä pätevä, ja 
avaimella on jo toinen allekirjoitus.
.

.gpg.keyedit.updpref.okay
Muuta valinnat kaikille käyttäjätunnuksille (tai vain valituille)
nykyiseen luetteloon valinnoista.  Kaikkien muutettujen
oma-allekirjoitusten aikaleima siirretään yhdellä sekunnilla eteenpäin.

.

.gpg.passphrase.enter
Ole hyvä ja syötä salasana, tämän on salainen lause 

.

.gpg.passphrase.repeat
Toista edellinen salasanasi varmistuaksesi siitä, mitä kirjoitit.
.

.gpg.detached_signature.filename
Anna allekirjoitetun tiedoston nimi
.

.gpg.openfile.overwrite.okay
Vastaa "kyllä", jos tiedoston voi ylikirjoittaa
.

.gpg.openfile.askoutname
Syötä uusi tiedostonimi. Jos painat vain RETURN, käytetään
oletustiedostoa (joka näkyy sulkeissa).
.

.gpg.ask_revocation_reason.code
Sinun tulisi määrittää syy varmenteelle. Riippuen asiayhteydestä
voit valita tästä listasta:
  "Avain on paljastunut"
      Käytä tätä, jos sinulla on syytä uskoa, että luvattomat henkilöt 
      ovat saaneet salaisen avaimesi käsiinsä.
  "Avain on korvattu"
      Käytä tätä, jos olet korvannut tämän uudemmalla avaimella.
  "Avain ei ole enää käytössä"
      Käytä tätä, jost ole lopettanut tämän avaimen käytön.
  "Käyttäjätunnus ei ole enää voimassa"
      Käytä tätä ilmoittamaan, että käyttäjätunnusta ei pitäisi käyttää;
      tätä normaalisti käytetään merkitsemään sähköpostiosoite vanhenneeksi.

.

.gpg.ask_revocation_reason.text
Halutessasi voit kirjoittaa tähän kuvauksen miksi julkaiset tämän
mitätöintivarmenteen.  Kirjoita lyhyesti.
Tyhjä rivi päättää tekstin.

.



# Local variables:
# mode: fundamental
# coding: utf-8
# End:
