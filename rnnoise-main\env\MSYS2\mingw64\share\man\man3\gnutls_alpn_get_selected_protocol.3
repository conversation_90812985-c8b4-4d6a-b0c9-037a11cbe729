.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_alpn_get_selected_protocol" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_alpn_get_selected_protocol \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_alpn_get_selected_protocol(gnutls_session_t " session ", gnutls_datum_t * " protocol ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "gnutls_datum_t * protocol" 12
will hold the protocol name
.SH "DESCRIPTION"
This function allows you to get the negotiated protocol name. The
returned protocol should be treated as opaque, constant value and
only valid during the session life.

The selected protocol is the first supported by the list sent
by the client.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned,
otherwise a negative error code is returned.

Since 3.2.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
