.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_kx_get_name" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_kx_get_name \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const char * gnutls_kx_get_name(gnutls_kx_algorithm_t " algorithm ");"
.SH ARGUMENTS
.IP "gnutls_kx_algorithm_t algorithm" 12
is a key exchange algorithm
.SH "DESCRIPTION"
Convert a \fBgnutls_kx_algorithm_t\fP value to a string.
.SH "RETURNS"
a pointer to a string that contains the name of the
specified key exchange algorithm, or \fBNULL\fP.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
