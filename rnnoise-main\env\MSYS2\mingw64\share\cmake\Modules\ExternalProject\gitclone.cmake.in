# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
# file LICENSE.rst or https://cmake.org/licensing for details.

cmake_minimum_required(VERSION ${CMAKE_VERSION}) # this file comes with cmake

if(EXISTS "@gitclone_stampfile@" AND EXISTS "@gitclone_infofile@" AND
  "@gitclone_stampfile@" IS_NEWER_THAN "@gitclone_infofile@")
  message(VERBOSE
    "Avoiding repeated git clone, stamp file is up to date: "
    "'@gitclone_stampfile@'"
  )
  return()
endif()

# Even at VERBOSE level, we don't want to see the commands executed, but
# enabling them to be shown for DEBUG may be useful to help diagnose problems.
cmake_language(GET_MESSAGE_LOG_LEVEL active_log_level)
if(active_log_level MATCHES "DEBUG|TRACE")
  set(maybe_show_command COMMAND_ECHO STDOUT)
else()
  set(maybe_show_command "")
endif()

execute_process(
  COMMAND ${CMAKE_COMMAND} -E rm -rf "@source_dir@"
  RESULT_VARIABLE error_code
  ${maybe_show_command}
)
if(error_code)
  message(FATAL_ERROR "Failed to remove directory: '@source_dir@'")
endif()

# try the clone 3 times in case there is an odd git clone issue
set(error_code 1)
set(number_of_tries 0)
while(error_code AND number_of_tries LESS 3)
  execute_process(
    COMMAND "@git_EXECUTABLE@"
            clone @git_clone_options@ "@git_repository@" "@src_name@"
    WORKING_DIRECTORY "@work_dir@"
    RESULT_VARIABLE error_code
    ${maybe_show_command}
  )
  math(EXPR number_of_tries "${number_of_tries} + 1")
endwhile()
if(number_of_tries GREATER 1)
  message(NOTICE "Had to git clone more than once: ${number_of_tries} times.")
endif()
if(error_code)
  message(FATAL_ERROR "Failed to clone repository: '@git_repository@'")
endif()

execute_process(
  COMMAND "@git_EXECUTABLE@"
          checkout "@git_tag@" @git_checkout_explicit--@
  WORKING_DIRECTORY "@work_dir@/@src_name@"
  RESULT_VARIABLE error_code
  ${maybe_show_command}
)
if(error_code)
  message(FATAL_ERROR "Failed to checkout tag: '@git_tag@'")
endif()

set(init_submodules @init_submodules@)
if(init_submodules)
  execute_process(
    COMMAND "@git_EXECUTABLE@" @git_submodules_config_options@
            submodule update @git_submodules_recurse@ --init @git_submodules@
    WORKING_DIRECTORY "@work_dir@/@src_name@"
    RESULT_VARIABLE error_code
    ${maybe_show_command}
  )
endif()
if(error_code)
  message(FATAL_ERROR "Failed to update submodules in: '@work_dir@/@src_name@'")
endif()

# Complete success, update the script-last-run stamp file:
#
execute_process(
  COMMAND ${CMAKE_COMMAND} -E copy "@gitclone_infofile@" "@gitclone_stampfile@"
  RESULT_VARIABLE error_code
  ${maybe_show_command}
)
if(error_code)
  message(FATAL_ERROR "Failed to copy script-last-run stamp file: '@gitclone_stampfile@'")
endif()
