cmake_minimum_required(VERSION 3.5)
project(EnhancedRNNoise C)

# 设置编译选项
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -W -O3 -g -ffast-math")

# 包含目录
include_directories(include)

# 源文件
set(CORE_SOURCES
    src/kiss_fft.c
    src/celt_lpc.c
    src/pitch.c
    src/rnn.c
    src/rnn_data.c
    src/denoise.c
)

# 增强RNN权重文件
set(ENHANCED_SOURCES
    src/enhanced_rnn_weights.c
)

# 可执行文件目标

# 1. 增强RNN推理测试程序
add_executable(enhanced_inference_test
    ${ENHANCED_SOURCES}
    src/simple_model_test.c
)
target_link_libraries(enhanced_inference_test m)

# 设置输出目录
set_target_properties(enhanced_inference_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 安装目标
install(TARGETS enhanced_inference_test
    RUNTIME DESTINATION bin
)

# 打印信息
message(STATUS "Enhanced RNNoise build configuration:")
message(STATUS "  - Enhanced inference test: enhanced_inference_test")
message(STATUS "  - Output directory: ${CMAKE_BINARY_DIR}/bin")
