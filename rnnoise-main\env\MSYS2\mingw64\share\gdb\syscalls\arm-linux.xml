<?xml version="1.0"?>
<!DOCTYPE syscalls_info SYSTEM "gdb-syscalls.dtd">
<!-- Copyright (C) 2009-2024 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->
<!-- This file was generated using the following files:

     arch/arm/tools/syscall.tbl
     arch/arm/include/uapi/asm/unistd.h

     The files mentioned above belong to the Linux Kernel.  -->
<syscalls_info>
  <syscall name="restart_syscall" number="0"/>
  <syscall name="exit" number="1" groups="process"/>
  <syscall name="fork" number="2" groups="process"/>
  <syscall name="read" number="3" groups="descriptor"/>
  <syscall name="write" number="4" groups="descriptor"/>
  <syscall name="open" number="5" groups="descriptor,file"/>
  <syscall name="close" number="6" groups="descriptor"/>
  <syscall name="waitpid" number="7" groups="process"/> <!-- removed -->
  <syscall name="creat" number="8" groups="descriptor,file"/>
  <syscall name="link" number="9" groups="file"/>
  <syscall name="unlink" number="10" groups="file"/>
  <syscall name="execve" number="11" groups="file,process"/>
  <syscall name="chdir" number="12" groups="file"/>
  <syscall name="time" number="13"/>
  <syscall name="mknod" number="14" groups="file"/>
  <syscall name="chmod" number="15" groups="file"/>
  <syscall name="lchown" number="16" groups="file"/>
  <syscall name="break" number="17" groups="memory"/> <!-- removed -->
  <syscall name="oldstat" number="18" groups="file"/> <!-- removed -->
  <syscall name="lseek" number="19" groups="descriptor"/>
  <syscall name="getpid" number="20"/>
  <syscall name="mount" number="21" groups="file"/>
  <syscall name="umount" number="22" groups="file"/>
  <syscall name="setuid" number="23"/>
  <syscall name="getuid" number="24"/>
  <syscall name="stime" number="25"/>
  <syscall name="ptrace" number="26"/>
  <syscall name="alarm" number="27"/>
  <syscall name="oldfstat" number="28" groups="descriptor"/> <!-- removed -->
  <syscall name="pause" number="29" groups="signal"/>
  <syscall name="utime" number="30" groups="file"/>
  <syscall name="stty" number="31"/> <!-- removed -->
  <syscall name="gtty" number="32"/> <!-- removed -->
  <syscall name="access" number="33" groups="file"/>
  <syscall name="nice" number="34"/>
  <syscall name="ftime" number="35"/> <!-- removed -->
  <syscall name="sync" number="36"/>
  <syscall name="kill" number="37" groups="signal,process"/>
  <syscall name="rename" number="38" groups="file"/>
  <syscall name="mkdir" number="39" groups="file"/>
  <syscall name="rmdir" number="40" groups="file"/>
  <syscall name="dup" number="41" groups="descriptor"/>
  <syscall name="pipe" number="42" groups="descriptor"/>
  <syscall name="times" number="43"/>
  <syscall name="prof" number="44"/> <!-- removed -->
  <syscall name="brk" number="45" groups="memory"/>
  <syscall name="setgid" number="46"/>
  <syscall name="getgid" number="47"/>
  <syscall name="signal" number="48" groups="signal"/> <!-- removed -->
  <syscall name="geteuid" number="49"/>
  <syscall name="getegid" number="50"/>
  <syscall name="acct" number="51" groups="file"/>
  <syscall name="umount2" number="52" groups="file"/>
  <syscall name="lock" number="53"/> <!-- removed -->
  <syscall name="ioctl" number="54" groups="descriptor"/>
  <syscall name="fcntl" number="55" groups="descriptor"/>
  <syscall name="mpx" number="56"/> <!-- removed -->
  <syscall name="setpgid" number="57"/>
  <syscall name="ulimit" number="58"/> <!-- removed -->
  <syscall name="olduname" number="59"/> <!-- removed -->
  <syscall name="umask" number="60"/>
  <syscall name="chroot" number="61" groups="file"/>
  <syscall name="ustat" number="62"/>
  <syscall name="dup2" number="63" groups="descriptor"/>
  <syscall name="getppid" number="64"/>
  <syscall name="getpgrp" number="65"/>
  <syscall name="setsid" number="66"/>
  <syscall name="sigaction" number="67" groups="signal"/>
  <syscall name="sgetmask" number="68" groups="signal"/> <!-- removed -->
  <syscall name="ssetmask" number="69" groups="signal"/> <!-- removed -->
  <syscall name="setreuid" number="70"/>
  <syscall name="setregid" number="71"/>
  <syscall name="sigsuspend" number="72" groups="signal"/>
  <syscall name="sigpending" number="73" groups="signal"/>
  <syscall name="sethostname" number="74"/>
  <syscall name="setrlimit" number="75"/>
  <syscall name="getrlimit" number="76"/>
  <syscall name="getrusage" number="77"/>
  <syscall name="gettimeofday" number="78"/>
  <syscall name="settimeofday" number="79"/>
  <syscall name="getgroups" number="80"/>
  <syscall name="setgroups" number="81"/>
  <syscall name="select" number="82" groups="descriptor"/>
  <syscall name="symlink" number="83" groups="file"/>
  <syscall name="oldlstat" number="84" groups="file"/> <!-- removed -->
  <syscall name="readlink" number="85" groups="file"/>
  <syscall name="uselib" number="86" groups="file"/>
  <syscall name="swapon" number="87" groups="file"/>
  <syscall name="reboot" number="88"/>
  <syscall name="readdir" number="89" groups="descriptor"/>
  <syscall name="mmap" number="90" groups="descriptor,memory"/>
  <syscall name="munmap" number="91" groups="memory"/>
  <syscall name="truncate" number="92" groups="file"/>
  <syscall name="ftruncate" number="93" groups="descriptor"/>
  <syscall name="fchmod" number="94" groups="descriptor"/>
  <syscall name="fchown" number="95" groups="descriptor"/>
  <syscall name="getpriority" number="96"/>
  <syscall name="setpriority" number="97"/>
  <syscall name="profil" number="98"/> <!-- removed -->
  <syscall name="statfs" number="99" groups="file"/>
  <syscall name="fstatfs" number="100" groups="descriptor"/>
  <syscall name="ioperm" number="101"/> <!-- removed -->
  <syscall name="socketcall" number="102" groups="descriptor"/>
  <syscall name="syslog" number="103"/>
  <syscall name="setitimer" number="104"/>
  <syscall name="getitimer" number="105"/>
  <syscall name="stat" number="106" groups="file"/>
  <syscall name="lstat" number="107" groups="file"/>
  <syscall name="fstat" number="108" groups="descriptor"/>
  <syscall name="olduname" number="109"/> <!-- removed -->
  <syscall name="iopl" number="110"/> <!-- removed -->
  <syscall name="vhangup" number="111"/>
  <syscall name="idle" number="112"/> <!-- removed -->
  <syscall name="syscall" number="113"/>
  <syscall name="wait4" number="114" groups="process"/>
  <syscall name="swapoff" number="115" groups="file"/>
  <syscall name="sysinfo" number="116"/>
  <syscall name="ipc" number="117" groups="ipc"/>
  <syscall name="fsync" number="118" groups="descriptor"/>
  <syscall name="sigreturn" number="119" groups="signal"/>
  <syscall name="clone" number="120" groups="process"/>
  <syscall name="setdomainname" number="121"/>
  <syscall name="uname" number="122"/>
  <syscall name="modify_ldt" number="123"/> <!-- removed -->
  <syscall name="adjtimex" number="124"/>
  <syscall name="mprotect" number="125" groups="memory"/>
  <syscall name="sigprocmask" number="126" groups="signal"/>
  <syscall name="create_module" number="127"/> <!-- removed -->
  <syscall name="init_module" number="128"/>
  <syscall name="delete_module" number="129"/>
  <syscall name="get_kernel_syms" number="130"/> <!-- removed -->
  <syscall name="quotactl" number="131" groups="file"/>
  <syscall name="getpgid" number="132"/>
  <syscall name="fchdir" number="133" groups="descriptor"/>
  <syscall name="bdflush" number="134"/>
  <syscall name="sysfs" number="135"/>
  <syscall name="personality" number="136"/>
  <syscall name="afs_syscall" number="137"/> <!-- removed -->
  <syscall name="setfsuid" number="138"/>
  <syscall name="setfsgid" number="139"/>
  <syscall name="_llseek" number="140" groups="descriptor"/>
  <syscall name="getdents" number="141" groups="descriptor"/>
  <syscall name="_newselect" number="142" groups="descriptor"/>
  <syscall name="flock" number="143" groups="descriptor"/>
  <syscall name="msync" number="144" groups="memory"/>
  <syscall name="readv" number="145" groups="descriptor"/>
  <syscall name="writev" number="146" groups="descriptor"/>
  <syscall name="getsid" number="147"/>
  <syscall name="fdatasync" number="148" groups="descriptor"/>
  <syscall name="_sysctl" number="149"/>
  <syscall name="mlock" number="150" groups="memory"/>
  <syscall name="munlock" number="151" groups="memory"/>
  <syscall name="mlockall" number="152" groups="memory"/>
  <syscall name="munlockall" number="153" groups="memory"/>
  <syscall name="sched_setparam" number="154"/>
  <syscall name="sched_getparam" number="155"/>
  <syscall name="sched_setscheduler" number="156"/>
  <syscall name="sched_getscheduler" number="157"/>
  <syscall name="sched_yield" number="158"/>
  <syscall name="sched_get_priority_max" number="159"/>
  <syscall name="sched_get_priority_min" number="160"/>
  <syscall name="sched_rr_get_interval" number="161"/>
  <syscall name="nanosleep" number="162"/>
  <syscall name="mremap" number="163" groups="memory"/>
  <syscall name="setresuid" number="164"/>
  <syscall name="getresuid" number="165"/>
  <syscall name="vm86" number="166"/> <!-- removed -->
  <syscall name="query_module" number="167"/> <!-- removed -->
  <syscall name="poll" number="168" groups="descriptor"/>
  <syscall name="nfsservctl" number="169"/>
  <syscall name="setresgid" number="170"/>
  <syscall name="getresgid" number="171"/>
  <syscall name="prctl" number="172"/>
  <syscall name="rt_sigreturn" number="173" groups="signal"/>
  <syscall name="rt_sigaction" number="174" groups="signal"/>
  <syscall name="rt_sigprocmask" number="175" groups="signal"/>
  <syscall name="rt_sigpending" number="176" groups="signal"/>
  <syscall name="rt_sigtimedwait" number="177" groups="signal"/>
  <syscall name="rt_sigqueueinfo" number="178" groups="signal,process"/>
  <syscall name="rt_sigsuspend" number="179" groups="signal"/>
  <syscall name="pread64" number="180" groups="descriptor"/>
  <syscall name="pwrite64" number="181" groups="descriptor"/>
  <syscall name="chown" number="182" groups="file"/>
  <syscall name="getcwd" number="183" groups="file"/>
  <syscall name="capget" number="184"/>
  <syscall name="capset" number="185"/>
  <syscall name="sigaltstack" number="186" groups="signal"/>
  <syscall name="sendfile" number="187" groups="descriptor,network"/>
  <syscall name="vfork" number="190" groups="process"/>
  <syscall name="ugetrlimit" number="191"/>
  <syscall name="mmap2" number="192" groups="descriptor,memory"/>
  <syscall name="truncate64" number="193" groups="file"/>
  <syscall name="ftruncate64" number="194" groups="descriptor"/>
  <syscall name="stat64" number="195" groups="file"/>
  <syscall name="lstat64" number="196" groups="file"/>
  <syscall name="fstat64" number="197" groups="descriptor"/>
  <syscall name="lchown32" number="198" groups="file"/>
  <syscall name="getuid32" number="199"/>
  <syscall name="getgid32" number="200"/>
  <syscall name="geteuid32" number="201"/>
  <syscall name="getegid32" number="202"/>
  <syscall name="setreuid32" number="203"/>
  <syscall name="setregid32" number="204"/>
  <syscall name="getgroups32" number="205"/>
  <syscall name="setgroups32" number="206"/>
  <syscall name="fchown32" number="207" groups="descriptor"/>
  <syscall name="setresuid32" number="208"/>
  <syscall name="getresuid32" number="209"/>
  <syscall name="setresgid32" number="210"/>
  <syscall name="getresgid32" number="211"/>
  <syscall name="chown32" number="212" groups="file"/>
  <syscall name="setuid32" number="213"/>
  <syscall name="setgid32" number="214"/>
  <syscall name="setfsuid32" number="215"/>
  <syscall name="setfsgid32" number="216"/>
  <syscall name="getdents64" number="217" groups="descriptor"/>
  <syscall name="pivot_root" number="218" groups="file"/>
  <syscall name="mincore" number="219" groups="memory"/>
  <syscall name="madvise" number="220" groups="memory"/>
  <syscall name="fcntl64" number="221" groups="descriptor"/>
  <syscall name="gettid" number="224"/>
  <syscall name="readahead" number="225" groups="descriptor"/>
  <syscall name="setxattr" number="226" groups="file"/>
  <syscall name="lsetxattr" number="227" groups="file"/>
  <syscall name="fsetxattr" number="228" groups="descriptor"/>
  <syscall name="getxattr" number="229" groups="file"/>
  <syscall name="lgetxattr" number="230" groups="file"/>
  <syscall name="fgetxattr" number="231" groups="descriptor"/>
  <syscall name="listxattr" number="232" groups="file"/>
  <syscall name="llistxattr" number="233" groups="file"/>
  <syscall name="flistxattr" number="234" groups="descriptor"/>
  <syscall name="removexattr" number="235" groups="file"/>
  <syscall name="lremovexattr" number="236" groups="file"/>
  <syscall name="fremovexattr" number="237" groups="descriptor"/>
  <syscall name="tkill" number="238" groups="signal,process"/>
  <syscall name="sendfile64" number="239" groups="descriptor,network"/>
  <syscall name="futex" number="240"/>
  <syscall name="sched_setaffinity" number="241"/>
  <syscall name="sched_getaffinity" number="242"/>
  <syscall name="io_setup" number="243" groups="memory"/>
  <syscall name="io_destroy" number="244" groups="memory"/>
  <syscall name="io_getevents" number="245"/>
  <syscall name="io_submit" number="246"/>
  <syscall name="io_cancel" number="247"/>
  <syscall name="exit_group" number="248" groups="process"/>
  <syscall name="lookup_dcookie" number="249"/>
  <syscall name="epoll_create" number="250" groups="descriptor"/>
  <syscall name="epoll_ctl" number="251" groups="descriptor"/>
  <syscall name="epoll_wait" number="252" groups="descriptor"/>
  <syscall name="remap_file_pages" number="253" groups="memory"/>
  <syscall name="set_tid_address" number="256"/>
  <syscall name="timer_create" number="257"/>
  <syscall name="timer_settime" number="258"/>
  <syscall name="timer_gettime" number="259"/>
  <syscall name="timer_getoverrun" number="260"/>
  <syscall name="timer_delete" number="261"/>
  <syscall name="clock_settime" number="262"/>
  <syscall name="clock_gettime" number="263"/>
  <syscall name="clock_getres" number="264"/>
  <syscall name="clock_nanosleep" number="265"/>
  <syscall name="statfs64" number="266" groups="file"/>
  <syscall name="fstatfs64" number="267" groups="descriptor"/>
  <syscall name="tgkill" number="268" groups="signal,process"/>
  <syscall name="utimes" number="269" groups="file"/>
  <syscall name="arm_fadvise64_64" number="270"/>
  <syscall name="pciconfig_iobase" number="271"/>
  <syscall name="pciconfig_read" number="272"/>
  <syscall name="pciconfig_write" number="273"/>
  <syscall name="mq_open" number="274" groups="descriptor"/>
  <syscall name="mq_unlink" number="275"/>
  <syscall name="mq_timedsend" number="276" groups="descriptor"/>
  <syscall name="mq_timedreceive" number="277" groups="descriptor"/>
  <syscall name="mq_notify" number="278" groups="descriptor"/>
  <syscall name="mq_getsetattr" number="279" groups="descriptor"/>
  <syscall name="waitid" number="280" groups="process"/>
  <syscall name="socket" number="281" groups="network"/>
  <syscall name="bind" number="282" groups="network"/>
  <syscall name="connect" number="283" groups="network"/>
  <syscall name="listen" number="284" groups="network"/>
  <syscall name="accept" number="285" groups="network"/>
  <syscall name="getsockname" number="286" groups="network"/>
  <syscall name="getpeername" number="287" groups="network"/>
  <syscall name="socketpair" number="288" groups="network"/>
  <syscall name="send" number="289" groups="network"/>
  <syscall name="sendto" number="290" groups="network"/>
  <syscall name="recv" number="291" groups="network"/>
  <syscall name="recvfrom" number="292" groups="network"/>
  <syscall name="shutdown" number="293" groups="network"/>
  <syscall name="setsockopt" number="294" groups="network"/>
  <syscall name="getsockopt" number="295" groups="network"/>
  <syscall name="sendmsg" number="296" groups="network"/>
  <syscall name="recvmsg" number="297" groups="network"/>
  <syscall name="semop" number="298" groups="ipc"/>
  <syscall name="semget" number="299" groups="ipc"/>
  <syscall name="semctl" number="300" groups="ipc"/>
  <syscall name="msgsnd" number="301" groups="ipc"/>
  <syscall name="msgrcv" number="302" groups="ipc"/>
  <syscall name="msgget" number="303" groups="ipc"/>
  <syscall name="msgctl" number="304" groups="ipc"/>
  <syscall name="shmat" number="305" groups="ipc,memory"/>
  <syscall name="shmdt" number="306" groups="ipc,memory"/>
  <syscall name="shmget" number="307" groups="ipc"/>
  <syscall name="shmctl" number="308" groups="ipc"/>
  <syscall name="add_key" number="309"/>
  <syscall name="request_key" number="310"/>
  <syscall name="keyctl" number="311"/>
  <syscall name="semtimedop" number="312" groups="ipc"/>
  <syscall name="vserver" number="313"/>
  <syscall name="ioprio_set" number="314"/>
  <syscall name="ioprio_get" number="315"/>
  <syscall name="inotify_init" number="316" groups="descriptor"/>
  <syscall name="inotify_add_watch" number="317" groups="descriptor,file"/>
  <syscall name="inotify_rm_watch" number="318" groups="descriptor"/>
  <syscall name="mbind" number="319" groups="memory"/>
  <syscall name="get_mempolicy" number="320" groups="memory"/>
  <syscall name="set_mempolicy" number="321" groups="memory"/>
  <syscall name="openat" number="322" groups="descriptor,file"/>
  <syscall name="mkdirat" number="323" groups="descriptor,file"/>
  <syscall name="mknodat" number="324" groups="descriptor,file"/>
  <syscall name="fchownat" number="325" groups="descriptor,file"/>
  <syscall name="futimesat" number="326" groups="descriptor,file"/>
  <syscall name="fstatat64" number="327" groups="descriptor,file"/>
  <syscall name="unlinkat" number="328" groups="descriptor,file"/>
  <syscall name="renameat" number="329" groups="descriptor,file"/>
  <syscall name="linkat" number="330" groups="descriptor,file"/>
  <syscall name="symlinkat" number="331" groups="descriptor,file"/>
  <syscall name="readlinkat" number="332" groups="descriptor,file"/>
  <syscall name="fchmodat" number="333" groups="descriptor,file"/>
  <syscall name="faccessat" number="334" groups="descriptor,file"/>
  <syscall name="pselect6" number="335" groups="descriptor"/>
  <syscall name="ppoll" number="336" groups="descriptor"/>
  <syscall name="unshare" number="337"/>
  <syscall name="set_robust_list" number="338"/>
  <syscall name="get_robust_list" number="339"/>
  <syscall name="splice" number="340" groups="descriptor"/>
  <syscall name="arm_sync_file_range" number="341"/>
  <syscall name="tee" number="342" groups="descriptor"/>
  <syscall name="vmsplice" number="343" groups="descriptor"/>
  <syscall name="move_pages" number="344" groups="memory"/>
  <syscall name="getcpu" number="345"/>
  <syscall name="epoll_pwait" number="346" groups="descriptor"/>
  <syscall name="kexec_load" number="347"/>
  <syscall name="utimensat" number="348" groups="descriptor,file"/>
  <syscall name="signalfd" number="349" groups="descriptor,signal"/>
  <syscall name="timerfd_create" number="350" groups="descriptor"/>
  <syscall name="eventfd" number="351" groups="descriptor"/>
  <syscall name="fallocate" number="352" groups="descriptor"/>
  <syscall name="timerfd_settime" number="353" groups="descriptor"/>
  <syscall name="timerfd_gettime" number="354" groups="descriptor"/>
  <syscall name="signalfd4" number="355" groups="descriptor,signal"/>
  <syscall name="eventfd2" number="356" groups="descriptor"/>
  <syscall name="epoll_create1" number="357" groups="descriptor"/>
  <syscall name="dup3" number="358" groups="descriptor"/>
  <syscall name="pipe2" number="359" groups="descriptor"/>
  <syscall name="inotify_init1" number="360" groups="descriptor"/>
  <syscall name="preadv" number="361" groups="descriptor"/>
  <syscall name="pwritev" number="362" groups="descriptor"/>
  <syscall name="rt_tgsigqueueinfo" number="363" groups="process,signal"/>
  <syscall name="perf_event_open" number="364" groups="descriptor"/>
  <syscall name="recvmmsg" number="365" groups="network"/>
  <syscall name="accept4" number="366" groups="network"/>
  <syscall name="fanotify_init" number="367" groups="descriptor"/>
  <syscall name="fanotify_mark" number="368" groups="descriptor,file"/>
  <syscall name="prlimit64" number="369"/>
  <syscall name="name_to_handle_at" number="370" groups="descriptor,file"/>
  <syscall name="open_by_handle_at" number="371" groups="descriptor"/>
  <syscall name="clock_adjtime" number="372"/>
  <syscall name="syncfs" number="373" groups="descriptor"/>
  <syscall name="sendmmsg" number="374" groups="network"/>
  <syscall name="setns" number="375" groups="descriptor"/>
  <syscall name="process_vm_readv" number="376"/>
  <syscall name="process_vm_writev" number="377"/>
  <syscall name="kcmp" number="378"/>
  <syscall name="finit_module" number="379" groups="descriptor"/>
  <syscall name="sched_setattr" number="380"/>
  <syscall name="sched_getattr" number="381"/>
  <syscall name="renameat2" number="382" groups="descriptor,file"/>
  <syscall name="seccomp" number="383"/>
  <syscall name="getrandom" number="384"/>
  <syscall name="memfd_create" number="385" groups="descriptor"/>
  <syscall name="bpf" number="386" groups="descriptor"/>
  <syscall name="execveat" number="387" groups="descriptor,file,process"/>
  <syscall name="userfaultfd" number="388" groups="descriptor"/>
  <syscall name="membarrier" number="389"/>
  <syscall name="mlock2" number="390" groups="memory"/>
  <syscall name="copy_file_range" number="391" groups="descriptor"/>
  <syscall name="preadv2" number="392" groups="descriptor"/>
  <syscall name="pwritev2" number="393" groups="descriptor"/>
  <syscall name="pkey_mprotect" number="394" groups="memory"/>
  <syscall name="pkey_alloc" number="395"/>
  <syscall name="pkey_free" number="396"/>
  <syscall name="statx" number="397" groups="descriptor,file"/>
  <syscall name="rseq" number="398"/>
  <syscall name="io_pgetevents" number="399"/>
  <syscall name="migrate_pages" number="400" groups="memory"/>
  <syscall name="kexec_file_load" number="401" groups="descriptor"/>
  <syscall name="clock_gettime64" number="403"/>
  <syscall name="clock_settime64" number="404"/>
  <syscall name="clock_adjtime64" number="405"/>
  <syscall name="clock_getres_time64" number="406"/>
  <syscall name="clock_nanosleep_time64" number="407"/>
  <syscall name="timer_gettime64" number="408"/>
  <syscall name="timer_settime64" number="409"/>
  <syscall name="timerfd_gettime64" number="410" groups="descriptor"/>
  <syscall name="timerfd_settime64" number="411" groups="descriptor"/>
  <syscall name="utimensat_time64" number="412" groups="descriptor,file"/>
  <syscall name="pselect6_time64" number="413" groups="descriptor"/>
  <syscall name="ppoll_time64" number="414" groups="descriptor"/>
  <syscall name="io_pgetevents_time64" number="416"/>
  <syscall name="recvmmsg_time64" number="417" groups="network"/>
  <syscall name="mq_timedsend_time64" number="418" groups="descriptor"/>
  <syscall name="mq_timedreceive_time64" number="419" groups="descriptor"/>
  <syscall name="semtimedop_time64" number="420" groups="ipc"/>
  <syscall name="rt_sigtimedwait_time64" number="421" groups="signal"/>
  <syscall name="futex_time64" number="422"/>
  <syscall name="sched_rr_get_interval_time64" number="423"/>
  <syscall name="pidfd_send_signal" number="424" groups="descriptor,signal,process"/>
  <syscall name="io_uring_setup" number="425" groups="descriptor"/>
  <syscall name="io_uring_enter" number="426" groups="descriptor,signal"/>
  <syscall name="io_uring_register" number="427" groups="descriptor,memory"/>
  <syscall name="open_tree" number="428" groups="descriptor,file"/>
  <syscall name="move_mount" number="429" groups="descriptor,file"/>
  <syscall name="fsopen" number="430" groups="descriptor"/>
  <syscall name="fsconfig" number="431" groups="descriptor,file"/>
  <syscall name="fsmount" number="432" groups="descriptor"/>
  <syscall name="fspick" number="433" groups="descriptor,file"/>
  <syscall name="pidfd_open" number="434" groups="descriptor"/>
  <syscall name="clone3" number="435" groups="process"/>
  <syscall name="close_range" number="436"/>
  <syscall name="openat2" number="437" groups="descriptor,file"/>
  <syscall name="pidfd_getfd" number="438" groups="descriptor"/>
  <syscall name="faccessat2" number="439" groups="descriptor,file"/>
  <syscall name="process_madvise" number="440" groups="descriptor"/>
  <syscall name="epoll_pwait2" number="441" groups="descriptor"/>
  <syscall name="mount_setattr" number="442" groups="descriptor,file"/>
  <syscall name="quotactl_fd" number="443" groups="descriptor"/>
  <syscall name="landlock_create_ruleset" number="444" groups="descriptor"/>
  <syscall name="landlock_add_rule" number="445" groups="descriptor"/>
  <syscall name="landlock_restrict_self" number="446" groups="descriptor"/>
  <syscall name="process_mrelease" number="448" groups="descriptor"/>
  <syscall name="futex_waitv" number="449"/>
  <syscall name="set_mempolicy_home_node" number="450" groups="memory"/>
  <syscall name="cachestat" number="451" groups="descriptor"/>
  <syscall name="fchmodat2" number="452" groups="descriptor,file"/>
  <syscall name="map_shadow_stack" number="453" groups="memory"/>
  <syscall name="futex_wake" number="454"/>
  <syscall name="futex_wait" number="455"/>
  <syscall name="futex_requeue" number="456"/>
  <syscall name="statmount" number="457"/>
  <syscall name="listmount" number="458"/>
  <syscall name="lsm_get_self_attr" number="459"/>
  <syscall name="lsm_set_self_attr" number="460"/>
  <syscall name="lsm_list_modules" number="461"/>
  <syscall name="mseal" number="462" groups="memory"/>
  <syscall name="setxattrat" number="463"/>
  <syscall name="getxattrat" number="464"/>
  <syscall name="listxattrat" number="465"/>
  <syscall name="removexattrat" number="466"/>
  <syscall name="ARM_breakpoint" number="983041"/>
  <syscall name="ARM_cacheflush" number="983042"/>
  <syscall name="ARM_usr26" number="983043"/>
  <syscall name="ARM_usr32" number="983044"/>
  <syscall name="ARM_set_tls" number="983045"/>
  <syscall name="ARM_get_tls" number="983046"/>
</syscalls_info>
