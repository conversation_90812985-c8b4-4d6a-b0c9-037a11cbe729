#!/usr/bin/env python3
"""
增强RNN模型验证脚本
使用训练数据验证模型性能
"""

import tensorflow as tf
import numpy as np
import h5py
import os
from datetime import datetime
import matplotlib.pyplot as plt

# 设置CPU运行
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

def load_model_safely():
    """安全加载训练好的模型"""
    model_path = '../models_enhanced/stable_20250802_124711/enhanced_rnnoise_stable.keras'
    
    if not os.path.exists(model_path):
        print(f"❌ 找不到模型文件: {model_path}")
        return None
    
    try:
        # 定义自定义损失函数
        def stable_mse_loss(y_true, y_pred):
            return tf.reduce_mean(tf.square(y_true - y_pred))
        
        # 加载模型
        model = tf.keras.models.load_model(model_path, custom_objects={
            'stable_mse_loss': stable_mse_loss
        })
        
        print(f"✅ 模型加载成功: {model_path}")
        return model
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None

def load_validation_data():
    """加载验证数据"""
    data_file = 'training_data.h5'
    
    if not os.path.exists(data_file):
        print(f"❌ 找不到训练数据文件: {data_file}")
        return None, None, None, None
    
    print("加载验证数据...")
    with h5py.File(data_file, 'r') as hf:
        all_data = hf['data'][:]
    
    # 使用最后10%的数据作为验证集
    total_samples = len(all_data)
    val_start = int(total_samples * 0.9)
    val_data = all_data[val_start:]
    
    print(f"验证数据: {len(val_data)} 样本")
    
    # 分离特征和目标
    x_val = val_data[:, :68]  # 输入特征
    noise_targets = val_data[:, 68:86]  # 噪声抑制目标
    voice_targets = val_data[:, 86:104]  # 人声增强目标
    vad_targets = val_data[:, 104:105]  # VAD目标

    # 重塑为序列格式 (samples, sequence_length, features)
    # 将每个样本作为长度为1的序列
    x_val = np.expand_dims(x_val, axis=1)  # (samples, 1, 68)
    noise_targets = np.expand_dims(noise_targets, axis=1)  # (samples, 1, 18)
    voice_targets = np.expand_dims(voice_targets, axis=1)  # (samples, 1, 18)
    vad_targets = np.expand_dims(vad_targets, axis=1)  # (samples, 1, 1)

    print(f"重塑后的数据形状:")
    print(f"  输入: {x_val.shape}")
    print(f"  噪声目标: {noise_targets.shape}")
    print(f"  人声目标: {voice_targets.shape}")
    print(f"  VAD目标: {vad_targets.shape}")

    return x_val, noise_targets, voice_targets, vad_targets

def evaluate_model_performance(model, x_val, noise_targets, voice_targets, vad_targets):
    """评估模型性能"""
    print("\n=== 模型性能评估 ===")
    
    # 进行预测
    print("运行模型预测...")
    predictions = model.predict(x_val, batch_size=32, verbose=1)
    
    noise_pred, voice_pred, vad_pred = predictions
    
    # 计算各项指标
    print("\n性能指标:")
    
    # 1. 噪声抑制性能
    # 只计算有效目标的MSE (目标值 >= 0)
    noise_mask = noise_targets >= 0
    if np.any(noise_mask):
        noise_mse = np.mean((noise_pred[noise_mask] - noise_targets[noise_mask]) ** 2)
        noise_mae = np.mean(np.abs(noise_pred[noise_mask] - noise_targets[noise_mask]))
        print(f"  噪声抑制 MSE: {noise_mse:.6f}")
        print(f"  噪声抑制 MAE: {noise_mae:.6f}")
    
    # 2. 人声增强性能
    voice_mask = voice_targets >= 0
    if np.any(voice_mask):
        voice_mse = np.mean((voice_pred[voice_mask] - voice_targets[voice_mask]) ** 2)
        voice_mae = np.mean(np.abs(voice_pred[voice_mask] - voice_targets[voice_mask]))
        print(f"  人声增强 MSE: {voice_mse:.6f}")
        print(f"  人声增强 MAE: {voice_mae:.6f}")
    
    # 3. VAD性能
    vad_mse = np.mean((vad_pred - vad_targets) ** 2)
    vad_accuracy = np.mean((vad_pred > 0.5) == (vad_targets > 0.5))
    print(f"  VAD MSE: {vad_mse:.6f}")
    print(f"  VAD 准确率: {vad_accuracy:.4f}")
    
    # 4. 输出范围检查
    print(f"\n输出范围检查:")
    print(f"  噪声抑制输出: [{np.min(noise_pred):.3f}, {np.max(noise_pred):.3f}]")
    print(f"  人声增强输出: [{np.min(voice_pred):.3f}, {np.max(voice_pred):.3f}]")
    print(f"  VAD输出: [{np.min(vad_pred):.3f}, {np.max(vad_pred):.3f}]")
    
    return {
        'noise_mse': noise_mse if np.any(noise_mask) else float('inf'),
        'voice_mse': voice_mse if np.any(voice_mask) else float('inf'),
        'vad_accuracy': vad_accuracy,
        'predictions': predictions
    }

def visualize_results(x_val, targets, predictions, num_samples=5):
    """可视化结果"""
    print(f"\n=== 结果可视化 (前{num_samples}个样本) ===")
    
    noise_targets, voice_targets, vad_targets = targets
    noise_pred, voice_pred, vad_pred = predictions
    
    for i in range(min(num_samples, len(x_val))):
        print(f"\n样本 {i+1}:")
        
        # 显示输入特征统计
        print(f"  输入特征范围: [{np.min(x_val[i]):.3f}, {np.max(x_val[i]):.3f}]")
        
        # 噪声抑制对比
        if noise_targets[i, 0] >= 0:  # 有效目标
            print(f"  噪声抑制 - 目标: {noise_targets[i, :5]}")
            print(f"  噪声抑制 - 预测: {noise_pred[i, :5]}")
        
        # 人声增强对比
        if voice_targets[i, 0] >= 0:  # 有效目标
            print(f"  人声增强 - 目标: {voice_targets[i, :5]}")
            print(f"  人声增强 - 预测: {voice_pred[i, :5]}")
        
        # VAD对比
        print(f"  VAD - 目标: {vad_targets[i, 0]:.3f}, 预测: {vad_pred[i, 0]:.3f}")

def test_model_robustness(model):
    """测试模型鲁棒性"""
    print("\n=== 鲁棒性测试 ===")
    
    # 测试不同输入情况 (需要3维输入)
    test_cases = [
        ("零输入", np.zeros((1, 1, 68))),
        ("随机输入", np.random.randn(1, 1, 68) * 0.1),
        ("极值输入", np.ones((1, 1, 68))),
        ("负值输入", -np.ones((1, 1, 68)) * 0.5)
    ]
    
    for name, test_input in test_cases:
        try:
            pred = model.predict(test_input, verbose=0)
            noise_out, voice_out, vad_out = pred
            
            print(f"  {name}:")
            print(f"    噪声抑制: [{np.min(noise_out):.3f}, {np.max(noise_out):.3f}]")
            print(f"    人声增强: [{np.min(voice_out):.3f}, {np.max(voice_out):.3f}]")
            print(f"    VAD: {vad_out[0, 0]:.3f}")
            
        except Exception as e:
            print(f"  {name}: ❌ 错误 - {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("增强RNN模型验证程序")
    print("基于clean_voice + wind_noise_voice训练数据")
    print("=" * 60)
    
    # 加载模型
    model = load_model_safely()
    if model is None:
        return
    
    # 显示模型信息
    print("\n=== 模型信息 ===")
    model.summary()
    
    # 加载验证数据
    x_val, noise_targets, voice_targets, vad_targets = load_validation_data()
    if x_val is None:
        return
    
    # 评估性能
    results = evaluate_model_performance(model, x_val, noise_targets, voice_targets, vad_targets)
    
    # 可视化结果
    visualize_results(x_val, (noise_targets, voice_targets, vad_targets), results['predictions'])
    
    # 鲁棒性测试
    test_model_robustness(model)
    
    # 总结
    print("\n" + "=" * 60)
    print("验证总结:")
    print(f"✅ 模型加载成功")
    print(f"✅ 验证数据处理完成")
    print(f"✅ 性能评估完成")
    print(f"✅ 鲁棒性测试完成")
    
    if results['noise_mse'] < 0.1 and results['voice_mse'] < 0.1 and results['vad_accuracy'] > 0.6:
        print("🎉 模型性能良好！")
    else:
        print("⚠️ 模型性能需要改进")
    
    print("\n模型已准备好用于实际应用！")
    print("下一步可以:")
    print("1. 导出为C代码集成到RNNoise")
    print("2. 进行更多的音频测试")
    print("3. 优化模型参数")

if __name__ == '__main__':
    main()
