diff_cmd () {
	"$merge_tool_path" "$LOCAL" "$REMOTE"
}

diff_cmd_help () {
	echo "Use Meld (requires a graphical session)"
}

merge_cmd () {
	check_meld_for_features

	option_auto_merge=
	if test "$meld_use_auto_merge_option" = true
	then
		option_auto_merge="--auto-merge"
	fi

	if test "$meld_has_output_option" = true
	then
		"$merge_tool_path" $option_auto_merge --output="$MERGED" \
			"$LOCAL" "$BASE" "$REMOTE"
	else
		"$merge_tool_path" $option_auto_merge "$LOCAL" "$MERGED" "$REMOTE"
	fi
}

merge_cmd_help () {
	echo "Use Meld (requires a graphical session) with optional \`auto merge\` (see \`git help mergetool\`'s \`CONFIGURATION\` section)"
}

# Get meld help message
init_meld_help_msg () {
	if test -z "$meld_help_msg"
	then
		meld_path="$(git config mergetool.meld.path || echo meld)"
		meld_help_msg=$("$meld_path" --help 2>&1)
	fi
}

# Check the features and set flags
check_meld_for_features () {
	# Check whether we should use 'meld --output <file>'
	if test -z "$meld_has_output_option"
	then
		meld_has_output_option=$(git config --bool mergetool.meld.hasOutput)
		case "$meld_has_output_option" in
		true | false)
			: use configured value
			;;
		*)
			: empty or invalid configured value, detecting "--output" automatically
			init_meld_help_msg

			case "$meld_help_msg" in
			*"--output="* | *'[OPTION...]'*)
				# All version that has [OPTION...] supports --output
				meld_has_output_option=true
				;;
			*)
				meld_has_output_option=false
				;;
			esac
			;;
		esac
	fi
	# Check whether we should use 'meld --auto-merge ...'
	if test -z "$meld_use_auto_merge_option"
	then
		meld_use_auto_merge_option=$(
			git config --bool-or-str mergetool.meld.useAutoMerge
		)
		case "$meld_use_auto_merge_option" in
		true | false)
			: use well formatted boolean value
			;;
		auto)
			# testing the "--auto-merge" option only if config is "auto"
			init_meld_help_msg

			case "$meld_help_msg" in
			*"--auto-merge"* | *'[OPTION...]'*)
				meld_use_auto_merge_option=true
				;;
			*)
				meld_use_auto_merge_option=false
				;;
			esac
			;;
		"")
			meld_use_auto_merge_option=false
			;;
		*)
			die "unknown mergetool.meld.useAutoMerge: $meld_use_auto_merge_option"
			;;
		esac
	fi
}
