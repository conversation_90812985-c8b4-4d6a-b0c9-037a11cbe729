# RNN人声增强系统 - 训练状态与问题解决方案

## 🎯 当前完成状态

### ✅ 已完成的工作

#### 1. 代码修改完成
- **特征提取扩展**: 从38维扩展到68维 (新增30维人声增强特征)
- **双重目标生成**: 18维噪声抑制 + 18维人声增强目标
- **训练数据格式**: 从75维扩展到105维特征向量
- **模型架构升级**: 支持双重输出的增强RNN模型
- **损失函数设计**: 专门的人声增强损失函数

#### 2. 数据生成成功
- **训练数据大小**: 126MB (enhanced_training_data.f32)
- **数据转换**: 成功转换为HDF5格式
- **训练序列**: 58个序列，每个2000帧
- **总样本数**: 116,000个训练样本
- **特征维度**: 105维 (68输入 + 36输出 + 1VAD)

#### 3. 系统集成完成
- **编译成功**: 增强的训练数据生成程序正常工作
- **数据流通**: 从音频文件到训练数据的完整流程
- **模型定义**: 增强RNN模型架构已实现

### ⚠️ 当前遇到的问题

#### 训练脚本维度兼容性问题
在模型训练阶段遇到了TensorFlow/Keras的维度匹配问题：

```
ValueError: Dimensions must be equal, but are 18 and 2000 for 
'{{node compile_loss/mycost/add_2}} = AddV2[T=DT_FLOAT]'
```

**问题原因**: 损失函数中的张量维度不匹配，特别是在处理序列数据时。

## 🔧 问题解决方案

### 方案一：修复现有训练脚本 (推荐)

#### 1. 简化损失函数
```python
def mycost(y_true, y_pred):
    """简化的噪声抑制损失函数"""
    mask = tf.minimum(y_true + 1., 1.)
    
    # 确保数值稳定性
    y_pred_safe = tf.maximum(y_pred, 1e-8)
    y_true_safe = tf.maximum(y_true, 1e-8)
    
    # MSE损失
    mse_loss = tf.square(tf.sqrt(y_pred_safe) - tf.sqrt(y_true_safe))
    
    # 应用mask并计算平均值
    masked_loss = mask * mse_loss
    return tf.reduce_mean(masked_loss)

def voice_enhancement_loss(y_true, y_pred):
    """简化的人声增强损失函数"""
    mask = tf.minimum(y_true + 1., 1.)
    
    # 基础MSE损失
    y_pred_safe = tf.maximum(y_pred, 1e-8)
    y_true_safe = tf.maximum(y_true, 1e-8)
    mse_loss = tf.square(tf.sqrt(y_pred_safe) - tf.sqrt(y_true_safe))
    
    # 高频增强权重
    freq_weights = tf.constant([1.0, 1.0, 1.1, 1.1, 1.2, 1.2, 1.3, 1.3, 1.4, 
                               1.4, 1.5, 1.5, 1.6, 1.6, 1.7, 1.7, 1.8, 1.8])
    freq_weights = tf.reshape(freq_weights, [1, 1, 18])
    
    weighted_loss = mse_loss * freq_weights
    masked_loss = mask * weighted_loss
    return tf.reduce_mean(masked_loss)
```

#### 2. 调整模型编译
```python
model.compile(
    loss={
        'noise_suppression_output': mycost,
        'voice_enhancement_output': voice_enhancement_loss,
        'vad_output': 'binary_crossentropy'  # 使用标准损失函数
    },
    loss_weights={
        'noise_suppression_output': 10.0,
        'voice_enhancement_output': 8.0,
        'vad_output': 0.5
    },
    optimizer='adam'
)
```

### 方案二：创建简化训练脚本

创建一个专门的简化训练脚本：

```python
#!/usr/bin/env python3
"""
简化的RNN人声增强训练脚本
"""

import tensorflow as tf
import numpy as np
import h5py
import os

# 设置CPU运行
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

def create_enhanced_model():
    """创建简化的增强模型"""
    inputs = tf.keras.Input(shape=(None, 68), name='inputs')
    
    # 简化的网络结构
    x = tf.keras.layers.Dense(48, activation='tanh')(inputs)
    x = tf.keras.layers.GRU(64, return_sequences=True)(x)
    
    # 双重输出
    noise_output = tf.keras.layers.Dense(18, activation='sigmoid', name='noise_output')(x)
    voice_output = tf.keras.layers.Dense(18, activation='sigmoid', name='voice_output')(x)
    vad_output = tf.keras.layers.Dense(1, activation='sigmoid', name='vad_output')(x)
    
    model = tf.keras.Model(inputs=inputs, outputs=[noise_output, voice_output, vad_output])
    return model

def train_model():
    """训练模型"""
    print("创建模型...")
    model = create_enhanced_model()
    
    # 编译模型
    model.compile(
        optimizer='adam',
        loss=['mse', 'mse', 'binary_crossentropy'],
        loss_weights=[10.0, 8.0, 0.5],
        metrics=['mae']
    )
    
    print("加载数据...")
    with h5py.File('training_data.h5', 'r') as hf:
        all_data = hf['data'][:]
    
    # 数据预处理
    window_size = 2000
    nb_sequences = len(all_data) // window_size
    
    x_train = all_data[:nb_sequences*window_size, :68]
    x_train = np.reshape(x_train, (nb_sequences, window_size, 68))
    
    noise_train = all_data[:nb_sequences*window_size, 68:86]
    noise_train = np.reshape(noise_train, (nb_sequences, window_size, 18))
    
    voice_train = all_data[:nb_sequences*window_size, 86:104]
    voice_train = np.reshape(voice_train, (nb_sequences, window_size, 18))
    
    vad_train = all_data[:nb_sequences*window_size, 104:105]
    vad_train = np.reshape(vad_train, (nb_sequences, window_size, 1))
    
    print(f"训练数据形状: {x_train.shape}")
    print(f"开始训练...")
    
    # 训练模型
    history = model.fit(
        x_train,
        [noise_train, voice_train, vad_train],
        batch_size=16,
        epochs=50,
        validation_split=0.1,
        verbose=1
    )
    
    # 保存模型
    model.save('enhanced_model_simple.keras')
    print("训练完成！")
    
    return model, history

if __name__ == '__main__':
    train_model()
```

### 方案三：分阶段训练策略

#### 阶段1：基础噪声抑制训练
```python
# 先训练噪声抑制部分，使用原有的38维特征
# 确保基础功能正常工作
```

#### 阶段2：人声增强特征训练
```python
# 在基础模型基础上，添加人声增强分支
# 使用迁移学习的方式
```

#### 阶段3：联合优化
```python
# 同时优化噪声抑制和人声增强
# 使用较小的学习率进行微调
```

## 📋 立即可执行的解决步骤

### 步骤1：创建简化训练脚本
```bash
cd rnnoise-main/training
cat > simple_enhanced_train.py << 'EOF'
# [插入上面的简化训练脚本代码]
EOF
```

### 步骤2：运行简化训练
```bash
python simple_enhanced_train.py
```

### 步骤3：验证训练结果
```bash
# 检查生成的模型文件
ls -la enhanced_model_simple.keras

# 验证模型结构
python -c "
import tensorflow as tf
model = tf.keras.models.load_model('enhanced_model_simple.keras')
model.summary()
"
```

## 🎯 预期训练结果

### 成功指标
- **模型收敛**: 损失函数稳定下降
- **验证性能**: 验证损失不出现过拟合
- **模型大小**: 约10-20MB的.keras文件
- **训练时间**: 1-2小时 (CPU环境)

### 性能预期
基于58个训练序列（116,000样本）：
- **噪声抑制**: 应该达到基础RNNoise性能的80-90%
- **人声增强**: 在谐波频率上有明显改善
- **实时性**: 保持原有的实时处理能力

## 🔄 后续优化方向

### 数据增强
- 增加更多的SNR变化范围
- 添加不同类型的噪声
- 使用数据增强技术扩充训练集

### 模型优化
- 调整网络架构参数
- 尝试不同的激活函数
- 优化损失函数权重

### 评估改进
- 实现客观评价指标 (PESQ, STOI)
- 进行主观听觉测试
- 与原始RNNoise对比

---

**总结**: 我们已经成功实现了人声增强功能的核心代码，数据生成正常，主要问题在于训练脚本的维度兼容性。通过上述解决方案，可以快速解决训练问题并获得可用的增强模型。
