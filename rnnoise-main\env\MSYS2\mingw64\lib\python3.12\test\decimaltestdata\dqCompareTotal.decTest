------------------------------------------------------------------------
-- dqCompareTotal.decTest -- decQuad comparison using total ordering  --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- Note that we cannot assume add/subtract tests cover paths adequately,
-- here, because the code might be quite different (comparison cannot
-- overflow or underflow, so actual subtractions are not necessary).
-- Similarly, comparetotal will have some radically different paths
-- than compare.

-- All operands and results are decQuads.
extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- sanity checks
dqcot001 comparetotal  -2  -2  -> 0
dqcot002 comparetotal  -2  -1  -> -1
dqcot003 comparetotal  -2   0  -> -1
dqcot004 comparetotal  -2   1  -> -1
dqcot005 comparetotal  -2   2  -> -1
dqcot006 comparetotal  -1  -2  -> 1
dqcot007 comparetotal  -1  -1  -> 0
dqcot008 comparetotal  -1   0  -> -1
dqcot009 comparetotal  -1   1  -> -1
dqcot010 comparetotal  -1   2  -> -1
dqcot011 comparetotal   0  -2  -> 1
dqcot012 comparetotal   0  -1  -> 1
dqcot013 comparetotal   0   0  -> 0
dqcot014 comparetotal   0   1  -> -1
dqcot015 comparetotal   0   2  -> -1
dqcot016 comparetotal   1  -2  -> 1
dqcot017 comparetotal   1  -1  -> 1
dqcot018 comparetotal   1   0  -> 1
dqcot019 comparetotal   1   1  -> 0
dqcot020 comparetotal   1   2  -> -1
dqcot021 comparetotal   2  -2  -> 1
dqcot022 comparetotal   2  -1  -> 1
dqcot023 comparetotal   2   0  -> 1
dqcot025 comparetotal   2   1  -> 1
dqcot026 comparetotal   2   2  -> 0

dqcot031 comparetotal  -20  -20  -> 0
dqcot032 comparetotal  -20  -10  -> -1
dqcot033 comparetotal  -20   00  -> -1
dqcot034 comparetotal  -20   10  -> -1
dqcot035 comparetotal  -20   20  -> -1
dqcot036 comparetotal  -10  -20  -> 1
dqcot037 comparetotal  -10  -10  -> 0
dqcot038 comparetotal  -10   00  -> -1
dqcot039 comparetotal  -10   10  -> -1
dqcot040 comparetotal  -10   20  -> -1
dqcot041 comparetotal   00  -20  -> 1
dqcot042 comparetotal   00  -10  -> 1
dqcot043 comparetotal   00   00  -> 0
dqcot044 comparetotal   00   10  -> -1
dqcot045 comparetotal   00   20  -> -1
dqcot046 comparetotal   10  -20  -> 1
dqcot047 comparetotal   10  -10  -> 1
dqcot048 comparetotal   10   00  -> 1
dqcot049 comparetotal   10   10  -> 0
dqcot050 comparetotal   10   20  -> -1
dqcot051 comparetotal   20  -20  -> 1
dqcot052 comparetotal   20  -10  -> 1
dqcot053 comparetotal   20   00  -> 1
dqcot055 comparetotal   20   10  -> 1
dqcot056 comparetotal   20   20  -> 0

dqcot061 comparetotal  -2.0  -2.0  -> 0
dqcot062 comparetotal  -2.0  -1.0  -> -1
dqcot063 comparetotal  -2.0   0.0  -> -1
dqcot064 comparetotal  -2.0   1.0  -> -1
dqcot065 comparetotal  -2.0   2.0  -> -1
dqcot066 comparetotal  -1.0  -2.0  -> 1
dqcot067 comparetotal  -1.0  -1.0  -> 0
dqcot068 comparetotal  -1.0   0.0  -> -1
dqcot069 comparetotal  -1.0   1.0  -> -1
dqcot070 comparetotal  -1.0   2.0  -> -1
dqcot071 comparetotal   0.0  -2.0  -> 1
dqcot072 comparetotal   0.0  -1.0  -> 1
dqcot073 comparetotal   0.0   0.0  -> 0
dqcot074 comparetotal   0.0   1.0  -> -1
dqcot075 comparetotal   0.0   2.0  -> -1
dqcot076 comparetotal   1.0  -2.0  -> 1
dqcot077 comparetotal   1.0  -1.0  -> 1
dqcot078 comparetotal   1.0   0.0  -> 1
dqcot079 comparetotal   1.0   1.0  -> 0
dqcot080 comparetotal   1.0   2.0  -> -1
dqcot081 comparetotal   2.0  -2.0  -> 1
dqcot082 comparetotal   2.0  -1.0  -> 1
dqcot083 comparetotal   2.0   0.0  -> 1
dqcot085 comparetotal   2.0   1.0  -> 1
dqcot086 comparetotal   2.0   2.0  -> 0

-- now some cases which might overflow if subtract were used
dqcot090 comparetotal  9.99999999999999999999999999999E+6144 9.99999999999999999999999999999E+6144  -> 0
dqcot091 comparetotal -9.99999999999999999999999999999E+6144 9.99999999999999999999999999999E+6144  -> -1
dqcot092 comparetotal  9.99999999999999999999999999999E+6144 -9.99999999999999999999999999999E+6144 -> 1
dqcot093 comparetotal -9.99999999999999999999999999999E+6144 -9.99999999999999999999999999999E+6144 -> 0

-- some differing length/exponent cases
-- in this first group, compare would compare all equal
dqcot100 comparetotal   7.0    7.0    -> 0
dqcot101 comparetotal   7.0    7      -> -1
dqcot102 comparetotal   7      7.0    -> 1
dqcot103 comparetotal   7E+0   7.0    -> 1
dqcot104 comparetotal   70E-1  7.0    -> 0
dqcot105 comparetotal   0.7E+1 7      -> 0
dqcot106 comparetotal   70E-1  7      -> -1
dqcot107 comparetotal   7.0    7E+0   -> -1
dqcot108 comparetotal   7.0    70E-1  -> 0
dqcot109 comparetotal   7      0.7E+1 -> 0
dqcot110 comparetotal   7      70E-1  -> 1

dqcot120 comparetotal   8.0    7.0    -> 1
dqcot121 comparetotal   8.0    7      -> 1
dqcot122 comparetotal   8      7.0    -> 1
dqcot123 comparetotal   8E+0   7.0    -> 1
dqcot124 comparetotal   80E-1  7.0    -> 1
dqcot125 comparetotal   0.8E+1 7      -> 1
dqcot126 comparetotal   80E-1  7      -> 1
dqcot127 comparetotal   8.0    7E+0   -> 1
dqcot128 comparetotal   8.0    70E-1  -> 1
dqcot129 comparetotal   8      0.7E+1  -> 1
dqcot130 comparetotal   8      70E-1  -> 1

dqcot140 comparetotal   8.0    9.0    -> -1
dqcot141 comparetotal   8.0    9      -> -1
dqcot142 comparetotal   8      9.0    -> -1
dqcot143 comparetotal   8E+0   9.0    -> -1
dqcot144 comparetotal   80E-1  9.0    -> -1
dqcot145 comparetotal   0.8E+1 9      -> -1
dqcot146 comparetotal   80E-1  9      -> -1
dqcot147 comparetotal   8.0    9E+0   -> -1
dqcot148 comparetotal   8.0    90E-1  -> -1
dqcot149 comparetotal   8      0.9E+1 -> -1
dqcot150 comparetotal   8      90E-1  -> -1

-- and again, with sign changes -+ ..
dqcot200 comparetotal  -7.0    7.0    -> -1
dqcot201 comparetotal  -7.0    7      -> -1
dqcot202 comparetotal  -7      7.0    -> -1
dqcot203 comparetotal  -7E+0   7.0    -> -1
dqcot204 comparetotal  -70E-1  7.0    -> -1
dqcot205 comparetotal  -0.7E+1 7      -> -1
dqcot206 comparetotal  -70E-1  7      -> -1
dqcot207 comparetotal  -7.0    7E+0   -> -1
dqcot208 comparetotal  -7.0    70E-1  -> -1
dqcot209 comparetotal  -7      0.7E+1 -> -1
dqcot210 comparetotal  -7      70E-1  -> -1

dqcot220 comparetotal  -8.0    7.0    -> -1
dqcot221 comparetotal  -8.0    7      -> -1
dqcot222 comparetotal  -8      7.0    -> -1
dqcot223 comparetotal  -8E+0   7.0    -> -1
dqcot224 comparetotal  -80E-1  7.0    -> -1
dqcot225 comparetotal  -0.8E+1 7      -> -1
dqcot226 comparetotal  -80E-1  7      -> -1
dqcot227 comparetotal  -8.0    7E+0   -> -1
dqcot228 comparetotal  -8.0    70E-1  -> -1
dqcot229 comparetotal  -8      0.7E+1 -> -1
dqcot230 comparetotal  -8      70E-1  -> -1

dqcot240 comparetotal  -8.0    9.0    -> -1
dqcot241 comparetotal  -8.0    9      -> -1
dqcot242 comparetotal  -8      9.0    -> -1
dqcot243 comparetotal  -8E+0   9.0    -> -1
dqcot244 comparetotal  -80E-1  9.0    -> -1
dqcot245 comparetotal  -0.8E+1 9      -> -1
dqcot246 comparetotal  -80E-1  9      -> -1
dqcot247 comparetotal  -8.0    9E+0   -> -1
dqcot248 comparetotal  -8.0    90E-1  -> -1
dqcot249 comparetotal  -8      0.9E+1 -> -1
dqcot250 comparetotal  -8      90E-1  -> -1

-- and again, with sign changes +- ..
dqcot300 comparetotal   7.0    -7.0    -> 1
dqcot301 comparetotal   7.0    -7      -> 1
dqcot302 comparetotal   7      -7.0    -> 1
dqcot303 comparetotal   7E+0   -7.0    -> 1
dqcot304 comparetotal   70E-1  -7.0    -> 1
dqcot305 comparetotal   .7E+1  -7      -> 1
dqcot306 comparetotal   70E-1  -7      -> 1
dqcot307 comparetotal   7.0    -7E+0   -> 1
dqcot308 comparetotal   7.0    -70E-1  -> 1
dqcot309 comparetotal   7      -.7E+1  -> 1
dqcot310 comparetotal   7      -70E-1  -> 1

dqcot320 comparetotal   8.0    -7.0    -> 1
dqcot321 comparetotal   8.0    -7      -> 1
dqcot322 comparetotal   8      -7.0    -> 1
dqcot323 comparetotal   8E+0   -7.0    -> 1
dqcot324 comparetotal   80E-1  -7.0    -> 1
dqcot325 comparetotal   .8E+1  -7      -> 1
dqcot326 comparetotal   80E-1  -7      -> 1
dqcot327 comparetotal   8.0    -7E+0   -> 1
dqcot328 comparetotal   8.0    -70E-1  -> 1
dqcot329 comparetotal   8      -.7E+1  -> 1
dqcot330 comparetotal   8      -70E-1  -> 1

dqcot340 comparetotal   8.0    -9.0    -> 1
dqcot341 comparetotal   8.0    -9      -> 1
dqcot342 comparetotal   8      -9.0    -> 1
dqcot343 comparetotal   8E+0   -9.0    -> 1
dqcot344 comparetotal   80E-1  -9.0    -> 1
dqcot345 comparetotal   .8E+1  -9      -> 1
dqcot346 comparetotal   80E-1  -9      -> 1
dqcot347 comparetotal   8.0    -9E+0   -> 1
dqcot348 comparetotal   8.0    -90E-1  -> 1
dqcot349 comparetotal   8      -.9E+1  -> 1
dqcot350 comparetotal   8      -90E-1  -> 1

-- and again, with sign changes -- ..
dqcot400 comparetotal   -7.0    -7.0    -> 0
dqcot401 comparetotal   -7.0    -7      -> 1
dqcot402 comparetotal   -7      -7.0    -> -1
dqcot403 comparetotal   -7E+0   -7.0    -> -1
dqcot404 comparetotal   -70E-1  -7.0    -> 0
dqcot405 comparetotal   -.7E+1  -7      -> 0
dqcot406 comparetotal   -70E-1  -7      -> 1
dqcot407 comparetotal   -7.0    -7E+0   -> 1
dqcot408 comparetotal   -7.0    -70E-1  -> 0
dqcot409 comparetotal   -7      -.7E+1  -> 0
dqcot410 comparetotal   -7      -70E-1  -> -1

dqcot420 comparetotal   -8.0    -7.0    -> -1
dqcot421 comparetotal   -8.0    -7      -> -1
dqcot422 comparetotal   -8      -7.0    -> -1
dqcot423 comparetotal   -8E+0   -7.0    -> -1
dqcot424 comparetotal   -80E-1  -7.0    -> -1
dqcot425 comparetotal   -.8E+1  -7      -> -1
dqcot426 comparetotal   -80E-1  -7      -> -1
dqcot427 comparetotal   -8.0    -7E+0   -> -1
dqcot428 comparetotal   -8.0    -70E-1  -> -1
dqcot429 comparetotal   -8      -.7E+1  -> -1
dqcot430 comparetotal   -8      -70E-1  -> -1

dqcot440 comparetotal   -8.0    -9.0    -> 1
dqcot441 comparetotal   -8.0    -9      -> 1
dqcot442 comparetotal   -8      -9.0    -> 1
dqcot443 comparetotal   -8E+0   -9.0    -> 1
dqcot444 comparetotal   -80E-1  -9.0    -> 1
dqcot445 comparetotal   -.8E+1  -9      -> 1
dqcot446 comparetotal   -80E-1  -9      -> 1
dqcot447 comparetotal   -8.0    -9E+0   -> 1
dqcot448 comparetotal   -8.0    -90E-1  -> 1
dqcot449 comparetotal   -8      -.9E+1  -> 1
dqcot450 comparetotal   -8      -90E-1  -> 1


-- testcases that subtract to lots of zeros at boundaries [pgr]
dqcot473 comparetotal 123.4560000000000E-89 123.456E-89 -> -1
dqcot474 comparetotal 123.456000000000E+89 123.456E+89 -> -1
dqcot475 comparetotal 123.45600000000E-89 123.456E-89 -> -1
dqcot476 comparetotal 123.4560000000E+89 123.456E+89 -> -1
dqcot477 comparetotal 123.456000000E-89 123.456E-89 -> -1
dqcot478 comparetotal 123.45600000E+89 123.456E+89 -> -1
dqcot479 comparetotal 123.4560000E-89 123.456E-89 -> -1
dqcot480 comparetotal 123.456000E+89 123.456E+89 -> -1
dqcot481 comparetotal 123.45600E-89 123.456E-89 -> -1
dqcot482 comparetotal 123.4560E+89 123.456E+89 -> -1
dqcot483 comparetotal 123.456E-89 123.456E-89 -> 0
dqcot487 comparetotal 123.456E+89 123.4560000000000E+89 -> 1
dqcot488 comparetotal 123.456E-89 123.456000000000E-89 -> 1
dqcot489 comparetotal 123.456E+89 123.45600000000E+89 -> 1
dqcot490 comparetotal 123.456E-89 123.4560000000E-89 -> 1
dqcot491 comparetotal 123.456E+89 123.456000000E+89 -> 1
dqcot492 comparetotal 123.456E-89 123.45600000E-89 -> 1
dqcot493 comparetotal 123.456E+89 123.4560000E+89 -> 1
dqcot494 comparetotal 123.456E-89 123.456000E-89 -> 1
dqcot495 comparetotal 123.456E+89 123.45600E+89 -> 1
dqcot496 comparetotal 123.456E-89 123.4560E-89 -> 1
dqcot497 comparetotal 123.456E+89 123.456E+89 -> 0

-- wide-ranging, around precision; signs equal
dqcot498 comparetotal    1     1E-17    -> 1
dqcot499 comparetotal    1     1E-16    -> 1
dqcot500 comparetotal    1     1E-15    -> 1
dqcot501 comparetotal    1     1E-14    -> 1
dqcot502 comparetotal    1     1E-13    -> 1
dqcot503 comparetotal    1     1E-12    -> 1
dqcot504 comparetotal    1     1E-11    -> 1
dqcot505 comparetotal    1     1E-10    -> 1
dqcot506 comparetotal    1     1E-9     -> 1
dqcot507 comparetotal    1     1E-8     -> 1
dqcot508 comparetotal    1     1E-7     -> 1
dqcot509 comparetotal    1     1E-6     -> 1
dqcot510 comparetotal    1     1E-5     -> 1
dqcot511 comparetotal    1     1E-4     -> 1
dqcot512 comparetotal    1     1E-3     -> 1
dqcot513 comparetotal    1     1E-2     -> 1
dqcot514 comparetotal    1     1E-1     -> 1
dqcot515 comparetotal    1     1E-0     -> 0
dqcot516 comparetotal    1     1E+1     -> -1
dqcot517 comparetotal    1     1E+2     -> -1
dqcot518 comparetotal    1     1E+3     -> -1
dqcot519 comparetotal    1     1E+4     -> -1
dqcot521 comparetotal    1     1E+5     -> -1
dqcot522 comparetotal    1     1E+6     -> -1
dqcot523 comparetotal    1     1E+7     -> -1
dqcot524 comparetotal    1     1E+8     -> -1
dqcot525 comparetotal    1     1E+9     -> -1
dqcot526 comparetotal    1     1E+10    -> -1
dqcot527 comparetotal    1     1E+11    -> -1
dqcot528 comparetotal    1     1E+12    -> -1
dqcot529 comparetotal    1     1E+13    -> -1
dqcot530 comparetotal    1     1E+14    -> -1
dqcot531 comparetotal    1     1E+15    -> -1
dqcot532 comparetotal    1     1E+16    -> -1
dqcot533 comparetotal    1     1E+17    -> -1
-- LR swap
dqcot538 comparetotal    1E-17  1       -> -1
dqcot539 comparetotal    1E-16  1       -> -1
dqcot540 comparetotal    1E-15  1       -> -1
dqcot541 comparetotal    1E-14  1       -> -1
dqcot542 comparetotal    1E-13  1       -> -1
dqcot543 comparetotal    1E-12  1       -> -1
dqcot544 comparetotal    1E-11  1       -> -1
dqcot545 comparetotal    1E-10  1       -> -1
dqcot546 comparetotal    1E-9   1       -> -1
dqcot547 comparetotal    1E-8   1       -> -1
dqcot548 comparetotal    1E-7   1       -> -1
dqcot549 comparetotal    1E-6   1       -> -1
dqcot550 comparetotal    1E-5   1       -> -1
dqcot551 comparetotal    1E-4   1       -> -1
dqcot552 comparetotal    1E-3   1       -> -1
dqcot553 comparetotal    1E-2   1       -> -1
dqcot554 comparetotal    1E-1   1       -> -1
dqcot555 comparetotal    1E-0   1       ->  0
dqcot556 comparetotal    1E+1   1       ->  1
dqcot557 comparetotal    1E+2   1       ->  1
dqcot558 comparetotal    1E+3   1       ->  1
dqcot559 comparetotal    1E+4   1       ->  1
dqcot561 comparetotal    1E+5   1       ->  1
dqcot562 comparetotal    1E+6   1       ->  1
dqcot563 comparetotal    1E+7   1       ->  1
dqcot564 comparetotal    1E+8   1       ->  1
dqcot565 comparetotal    1E+9   1       ->  1
dqcot566 comparetotal    1E+10  1       ->  1
dqcot567 comparetotal    1E+11  1       ->  1
dqcot568 comparetotal    1E+12  1       ->  1
dqcot569 comparetotal    1E+13  1       ->  1
dqcot570 comparetotal    1E+14  1       ->  1
dqcot571 comparetotal    1E+15  1       ->  1
dqcot572 comparetotal    1E+16  1       ->  1
dqcot573 comparetotal    1E+17  1       ->  1
-- similar with a useful coefficient, one side only
dqcot578 comparetotal  0.000000987654321     1E-17    -> 1
dqcot579 comparetotal  0.000000987654321     1E-16    -> 1
dqcot580 comparetotal  0.000000987654321     1E-15    -> 1
dqcot581 comparetotal  0.000000987654321     1E-14    -> 1
dqcot582 comparetotal  0.000000987654321     1E-13    -> 1
dqcot583 comparetotal  0.000000987654321     1E-12    -> 1
dqcot584 comparetotal  0.000000987654321     1E-11    -> 1
dqcot585 comparetotal  0.000000987654321     1E-10    -> 1
dqcot586 comparetotal  0.000000987654321     1E-9     -> 1
dqcot587 comparetotal  0.000000987654321     1E-8     -> 1
dqcot588 comparetotal  0.000000987654321     1E-7     -> 1
dqcot589 comparetotal  0.000000987654321     1E-6     -> -1
dqcot590 comparetotal  0.000000987654321     1E-5     -> -1
dqcot591 comparetotal  0.000000987654321     1E-4     -> -1
dqcot592 comparetotal  0.000000987654321     1E-3     -> -1
dqcot593 comparetotal  0.000000987654321     1E-2     -> -1
dqcot594 comparetotal  0.000000987654321     1E-1     -> -1
dqcot595 comparetotal  0.000000987654321     1E-0     -> -1
dqcot596 comparetotal  0.000000987654321     1E+1     -> -1
dqcot597 comparetotal  0.000000987654321     1E+2     -> -1
dqcot598 comparetotal  0.000000987654321     1E+3     -> -1
dqcot599 comparetotal  0.000000987654321     1E+4     -> -1

-- check some unit-y traps
dqcot600 comparetotal   12            12.2345 -> -1
dqcot601 comparetotal   12.0          12.2345 -> -1
dqcot602 comparetotal   12.00         12.2345 -> -1
dqcot603 comparetotal   12.000        12.2345 -> -1
dqcot604 comparetotal   12.0000       12.2345 -> -1
dqcot605 comparetotal   12.00000      12.2345 -> -1
dqcot606 comparetotal   12.000000     12.2345 -> -1
dqcot607 comparetotal   12.0000000    12.2345 -> -1
dqcot608 comparetotal   12.00000000   12.2345 -> -1
dqcot609 comparetotal   12.000000000  12.2345 -> -1
dqcot610 comparetotal   12.1234 12            ->  1
dqcot611 comparetotal   12.1234 12.0          ->  1
dqcot612 comparetotal   12.1234 12.00         ->  1
dqcot613 comparetotal   12.1234 12.000        ->  1
dqcot614 comparetotal   12.1234 12.0000       ->  1
dqcot615 comparetotal   12.1234 12.00000      ->  1
dqcot616 comparetotal   12.1234 12.000000     ->  1
dqcot617 comparetotal   12.1234 12.0000000    ->  1
dqcot618 comparetotal   12.1234 12.00000000   ->  1
dqcot619 comparetotal   12.1234 12.000000000  ->  1
dqcot620 comparetotal  -12           -12.2345 ->  1
dqcot621 comparetotal  -12.0         -12.2345 ->  1
dqcot622 comparetotal  -12.00        -12.2345 ->  1
dqcot623 comparetotal  -12.000       -12.2345 ->  1
dqcot624 comparetotal  -12.0000      -12.2345 ->  1
dqcot625 comparetotal  -12.00000     -12.2345 ->  1
dqcot626 comparetotal  -12.000000    -12.2345 ->  1
dqcot627 comparetotal  -12.0000000   -12.2345 ->  1
dqcot628 comparetotal  -12.00000000  -12.2345 ->  1
dqcot629 comparetotal  -12.000000000 -12.2345 ->  1
dqcot630 comparetotal  -12.1234 -12           -> -1
dqcot631 comparetotal  -12.1234 -12.0         -> -1
dqcot632 comparetotal  -12.1234 -12.00        -> -1
dqcot633 comparetotal  -12.1234 -12.000       -> -1
dqcot634 comparetotal  -12.1234 -12.0000      -> -1
dqcot635 comparetotal  -12.1234 -12.00000     -> -1
dqcot636 comparetotal  -12.1234 -12.000000    -> -1
dqcot637 comparetotal  -12.1234 -12.0000000   -> -1
dqcot638 comparetotal  -12.1234 -12.00000000  -> -1
dqcot639 comparetotal  -12.1234 -12.000000000 -> -1

-- extended zeros
dqcot640 comparetotal   0     0   -> 0
dqcot641 comparetotal   0    -0   -> 1
dqcot642 comparetotal   0    -0.0 -> 1
dqcot643 comparetotal   0     0.0 -> 1
dqcot644 comparetotal  -0     0   -> -1
dqcot645 comparetotal  -0    -0   -> 0
dqcot646 comparetotal  -0    -0.0 -> -1
dqcot647 comparetotal  -0     0.0 -> -1
dqcot648 comparetotal   0.0   0   -> -1
dqcot649 comparetotal   0.0  -0   -> 1
dqcot650 comparetotal   0.0  -0.0 -> 1
dqcot651 comparetotal   0.0   0.0 -> 0
dqcot652 comparetotal  -0.0   0   -> -1
dqcot653 comparetotal  -0.0  -0   -> 1
dqcot654 comparetotal  -0.0  -0.0 -> 0
dqcot655 comparetotal  -0.0   0.0 -> -1

dqcot656 comparetotal  -0E1   0.0 -> -1
dqcot657 comparetotal  -0E2   0.0 -> -1
dqcot658 comparetotal   0E1   0.0 -> 1
dqcot659 comparetotal   0E2   0.0 -> 1
dqcot660 comparetotal  -0E1   0   -> -1
dqcot661 comparetotal  -0E2   0   -> -1
dqcot662 comparetotal   0E1   0   -> 1
dqcot663 comparetotal   0E2   0   -> 1
dqcot664 comparetotal  -0E1  -0E1 -> 0
dqcot665 comparetotal  -0E2  -0E1 -> -1
dqcot666 comparetotal   0E1  -0E1 -> 1
dqcot667 comparetotal   0E2  -0E1 -> 1
dqcot668 comparetotal  -0E1  -0E2 -> 1
dqcot669 comparetotal  -0E2  -0E2 -> 0
dqcot670 comparetotal   0E1  -0E2 -> 1
dqcot671 comparetotal   0E2  -0E2 -> 1
dqcot672 comparetotal  -0E1   0E1 -> -1
dqcot673 comparetotal  -0E2   0E1 -> -1
dqcot674 comparetotal   0E1   0E1 -> 0
dqcot675 comparetotal   0E2   0E1 -> 1
dqcot676 comparetotal  -0E1   0E2 -> -1
dqcot677 comparetotal  -0E2   0E2 -> -1
dqcot678 comparetotal   0E1   0E2 -> -1
dqcot679 comparetotal   0E2   0E2 -> 0

-- trailing zeros; unit-y
dqcot680 comparetotal   12    12           -> 0
dqcot681 comparetotal   12    12.0         -> 1
dqcot682 comparetotal   12    12.00        -> 1
dqcot683 comparetotal   12    12.000       -> 1
dqcot684 comparetotal   12    12.0000      -> 1
dqcot685 comparetotal   12    12.00000     -> 1
dqcot686 comparetotal   12    12.000000    -> 1
dqcot687 comparetotal   12    12.0000000   -> 1
dqcot688 comparetotal   12    12.00000000  -> 1
dqcot689 comparetotal   12    12.000000000 -> 1
dqcot690 comparetotal   12              12 -> 0
dqcot691 comparetotal   12.0            12 -> -1
dqcot692 comparetotal   12.00           12 -> -1
dqcot693 comparetotal   12.000          12 -> -1
dqcot694 comparetotal   12.0000         12 -> -1
dqcot695 comparetotal   12.00000        12 -> -1
dqcot696 comparetotal   12.000000       12 -> -1
dqcot697 comparetotal   12.0000000      12 -> -1
dqcot698 comparetotal   12.00000000     12 -> -1
dqcot699 comparetotal   12.000000000    12 -> -1

-- old long operand checks
dqcot701 comparetotal 12345678000  1 ->  1
dqcot702 comparetotal 1 12345678000  -> -1
dqcot703 comparetotal 1234567800   1 ->  1
dqcot704 comparetotal 1 1234567800   -> -1
dqcot705 comparetotal 1234567890   1 ->  1
dqcot706 comparetotal 1 1234567890   -> -1
dqcot707 comparetotal 1234567891   1 ->  1
dqcot708 comparetotal 1 1234567891   -> -1
dqcot709 comparetotal 12345678901  1 ->  1
dqcot710 comparetotal 1 12345678901  -> -1
dqcot711 comparetotal 1234567896   1 ->  1
dqcot712 comparetotal 1 1234567896   -> -1
dqcot713 comparetotal -1234567891  1 -> -1
dqcot714 comparetotal 1 -1234567891  ->  1
dqcot715 comparetotal -12345678901 1 -> -1
dqcot716 comparetotal 1 -12345678901 ->  1
dqcot717 comparetotal -1234567896  1 -> -1
dqcot718 comparetotal 1 -1234567896  ->  1

-- old residue cases
dqcot740 comparetotal  1  0.9999999  -> 1
dqcot741 comparetotal  1  0.999999   -> 1
dqcot742 comparetotal  1  0.99999    -> 1
dqcot743 comparetotal  1  1.0000     -> 1
dqcot744 comparetotal  1  1.00001    -> -1
dqcot745 comparetotal  1  1.000001   -> -1
dqcot746 comparetotal  1  1.0000001  -> -1
dqcot750 comparetotal  0.9999999  1  -> -1
dqcot751 comparetotal  0.999999   1  -> -1
dqcot752 comparetotal  0.99999    1  -> -1
dqcot753 comparetotal  1.0000     1  -> -1
dqcot754 comparetotal  1.00001    1  -> 1
dqcot755 comparetotal  1.000001   1  -> 1
dqcot756 comparetotal  1.0000001  1  -> 1

-- Specials
dqcot780 comparetotal  Inf  -Inf   ->  1
dqcot781 comparetotal  Inf  -1000  ->  1
dqcot782 comparetotal  Inf  -1     ->  1
dqcot783 comparetotal  Inf  -0     ->  1
dqcot784 comparetotal  Inf   0     ->  1
dqcot785 comparetotal  Inf   1     ->  1
dqcot786 comparetotal  Inf   1000  ->  1
dqcot787 comparetotal  Inf   Inf   ->  0
dqcot788 comparetotal -1000  Inf   -> -1
dqcot789 comparetotal -Inf   Inf   -> -1
dqcot790 comparetotal -1     Inf   -> -1
dqcot791 comparetotal -0     Inf   -> -1
dqcot792 comparetotal  0     Inf   -> -1
dqcot793 comparetotal  1     Inf   -> -1
dqcot794 comparetotal  1000  Inf   -> -1
dqcot795 comparetotal  Inf   Inf   ->  0

dqcot800 comparetotal -Inf  -Inf   ->  0
dqcot801 comparetotal -Inf  -1000  -> -1
dqcot802 comparetotal -Inf  -1     -> -1
dqcot803 comparetotal -Inf  -0     -> -1
dqcot804 comparetotal -Inf   0     -> -1
dqcot805 comparetotal -Inf   1     -> -1
dqcot806 comparetotal -Inf   1000  -> -1
dqcot807 comparetotal -Inf   Inf   -> -1
dqcot808 comparetotal -Inf  -Inf   ->  0
dqcot809 comparetotal -1000 -Inf   ->  1
dqcot810 comparetotal -1    -Inf   ->  1
dqcot811 comparetotal -0    -Inf   ->  1
dqcot812 comparetotal  0    -Inf   ->  1
dqcot813 comparetotal  1    -Inf   ->  1
dqcot814 comparetotal  1000 -Inf   ->  1
dqcot815 comparetotal  Inf  -Inf   ->  1

dqcot821 comparetotal  NaN -Inf    ->  1
dqcot822 comparetotal  NaN -1000   ->  1
dqcot823 comparetotal  NaN -1      ->  1
dqcot824 comparetotal  NaN -0      ->  1
dqcot825 comparetotal  NaN  0      ->  1
dqcot826 comparetotal  NaN  1      ->  1
dqcot827 comparetotal  NaN  1000   ->  1
dqcot828 comparetotal  NaN  Inf    ->  1
dqcot829 comparetotal  NaN  NaN    ->  0
dqcot830 comparetotal -Inf  NaN    ->  -1
dqcot831 comparetotal -1000 NaN    ->  -1
dqcot832 comparetotal -1    NaN    ->  -1
dqcot833 comparetotal -0    NaN    ->  -1
dqcot834 comparetotal  0    NaN    ->  -1
dqcot835 comparetotal  1    NaN    ->  -1
dqcot836 comparetotal  1000 NaN    ->  -1
dqcot837 comparetotal  Inf  NaN    ->  -1
dqcot838 comparetotal -NaN -NaN    ->  0
dqcot839 comparetotal +NaN -NaN    ->  1
dqcot840 comparetotal -NaN +NaN    ->  -1

dqcot841 comparetotal  sNaN -sNaN  ->  1
dqcot842 comparetotal  sNaN -NaN   ->  1
dqcot843 comparetotal  sNaN -Inf   ->  1
dqcot844 comparetotal  sNaN -1000  ->  1
dqcot845 comparetotal  sNaN -1     ->  1
dqcot846 comparetotal  sNaN -0     ->  1
dqcot847 comparetotal  sNaN  0     ->  1
dqcot848 comparetotal  sNaN  1     ->  1
dqcot849 comparetotal  sNaN  1000  ->  1
dqcot850 comparetotal  sNaN  NaN   ->  -1
dqcot851 comparetotal  sNaN sNaN   ->  0

dqcot852 comparetotal -sNaN sNaN   ->  -1
dqcot853 comparetotal -NaN  sNaN   ->  -1
dqcot854 comparetotal -Inf  sNaN   ->  -1
dqcot855 comparetotal -1000 sNaN   ->  -1
dqcot856 comparetotal -1    sNaN   ->  -1
dqcot857 comparetotal -0    sNaN   ->  -1
dqcot858 comparetotal  0    sNaN   ->  -1
dqcot859 comparetotal  1    sNaN   ->  -1
dqcot860 comparetotal  1000 sNaN   ->  -1
dqcot861 comparetotal  Inf  sNaN   ->  -1
dqcot862 comparetotal  NaN  sNaN   ->  1
dqcot863 comparetotal  sNaN sNaN   ->  0

dqcot871 comparetotal  -sNaN -sNaN  ->  0
dqcot872 comparetotal  -sNaN -NaN   ->  1
dqcot873 comparetotal  -sNaN -Inf   ->  -1
dqcot874 comparetotal  -sNaN -1000  ->  -1
dqcot875 comparetotal  -sNaN -1     ->  -1
dqcot876 comparetotal  -sNaN -0     ->  -1
dqcot877 comparetotal  -sNaN  0     ->  -1
dqcot878 comparetotal  -sNaN  1     ->  -1
dqcot879 comparetotal  -sNaN  1000  ->  -1
dqcot880 comparetotal  -sNaN  NaN   ->  -1
dqcot881 comparetotal  -sNaN sNaN   ->  -1

dqcot882 comparetotal -sNaN -sNaN   ->  0
dqcot883 comparetotal -NaN  -sNaN   ->  -1
dqcot884 comparetotal -Inf  -sNaN   ->  1
dqcot885 comparetotal -1000 -sNaN   ->  1
dqcot886 comparetotal -1    -sNaN   ->  1
dqcot887 comparetotal -0    -sNaN   ->  1
dqcot888 comparetotal  0    -sNaN   ->  1
dqcot889 comparetotal  1    -sNaN   ->  1
dqcot890 comparetotal  1000 -sNaN   ->  1
dqcot891 comparetotal  Inf  -sNaN   ->  1
dqcot892 comparetotal  NaN  -sNaN   ->  1
dqcot893 comparetotal  sNaN -sNaN   ->  1

-- NaNs with payload
dqcot960 comparetotal  NaN9 -Inf   ->  1
dqcot961 comparetotal  NaN8  999   ->  1
dqcot962 comparetotal  NaN77 Inf   ->  1
dqcot963 comparetotal -NaN67 NaN5  ->  -1
dqcot964 comparetotal -Inf  -NaN4  ->  1
dqcot965 comparetotal -999  -NaN33 ->  1
dqcot966 comparetotal  Inf   NaN2  ->  -1

dqcot970 comparetotal -NaN41 -NaN42 -> 1
dqcot971 comparetotal +NaN41 -NaN42 -> 1
dqcot972 comparetotal -NaN41 +NaN42 -> -1
dqcot973 comparetotal +NaN41 +NaN42 -> -1
dqcot974 comparetotal -NaN42 -NaN01 -> -1
dqcot975 comparetotal +NaN42 -NaN01 ->  1
dqcot976 comparetotal -NaN42 +NaN01 -> -1
dqcot977 comparetotal +NaN42 +NaN01 ->  1

dqcot980 comparetotal -sNaN771 -sNaN772 -> 1
dqcot981 comparetotal +sNaN771 -sNaN772 -> 1
dqcot982 comparetotal -sNaN771 +sNaN772 -> -1
dqcot983 comparetotal +sNaN771 +sNaN772 -> -1
dqcot984 comparetotal -sNaN772 -sNaN771 -> -1
dqcot985 comparetotal +sNaN772 -sNaN771 ->  1
dqcot986 comparetotal -sNaN772 +sNaN771 -> -1
dqcot987 comparetotal +sNaN772 +sNaN771 ->  1

dqcot991 comparetotal -sNaN99 -Inf    -> -1
dqcot992 comparetotal  sNaN98 -11     ->  1
dqcot993 comparetotal  sNaN97  NaN    -> -1
dqcot994 comparetotal  sNaN16 sNaN94  -> -1
dqcot995 comparetotal  NaN85  sNaN83  ->  1
dqcot996 comparetotal -Inf    sNaN92  -> -1
dqcot997 comparetotal  088    sNaN81  -> -1
dqcot998 comparetotal  Inf    sNaN90  -> -1
dqcot999 comparetotal  NaN   -sNaN89  ->  1

-- spread zeros
dqcot1110 comparetotal   0E-6143  0       -> -1
dqcot1111 comparetotal   0E-6143 -0       ->  1
dqcot1112 comparetotal  -0E-6143  0       -> -1
dqcot1113 comparetotal  -0E-6143 -0       ->  1
dqcot1114 comparetotal   0E-6143  0E+6144  -> -1
dqcot1115 comparetotal   0E-6143 -0E+6144  ->  1
dqcot1116 comparetotal  -0E-6143  0E+6144  -> -1
dqcot1117 comparetotal  -0E-6143 -0E+6144  ->  1
dqcot1118 comparetotal   0       0E+6144  -> -1
dqcot1119 comparetotal   0      -0E+6144  ->  1
dqcot1120 comparetotal  -0       0E+6144  -> -1
dqcot1121 comparetotal  -0      -0E+6144  ->  1

dqcot1130 comparetotal   0E+6144  0       ->  1
dqcot1131 comparetotal   0E+6144 -0       ->  1
dqcot1132 comparetotal  -0E+6144  0       -> -1
dqcot1133 comparetotal  -0E+6144 -0       -> -1
dqcot1134 comparetotal   0E+6144  0E-6143  ->  1
dqcot1135 comparetotal   0E+6144 -0E-6143  ->  1
dqcot1136 comparetotal  -0E+6144  0E-6143  -> -1
dqcot1137 comparetotal  -0E+6144 -0E-6143  -> -1
dqcot1138 comparetotal   0       0E-6143  ->  1
dqcot1139 comparetotal   0      -0E-6143  ->  1
dqcot1140 comparetotal  -0       0E-6143  -> -1
dqcot1141 comparetotal  -0      -0E-6143  -> -1

-- Null tests
dqcot9990 comparetotal 10  # -> NaN Invalid_operation
dqcot9991 comparetotal  # 10 -> NaN Invalid_operation
