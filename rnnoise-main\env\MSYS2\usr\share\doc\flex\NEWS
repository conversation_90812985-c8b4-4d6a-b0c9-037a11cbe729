flex NEWS

* Noteworthy changes in release 2.6.4 (2017-05-06) [stable]

** build

*** The indent target now knows about flex's new (as of 2.6.0)
    layout. The indent rules it would apply are not correct and do
    need to be fixed.

*** The files included in the flex distribution are now built by the
    version of flex that is included in the distribution.

*** The configure script has a better idea of which headers are
    required to build flex. It will also error when missing functions
    are detected.

*** We have lowered the versions of automake and gettext that
    configure.ac lists as required for building flex. In autogen.sh,
    we now check for how to call libtoolize and use what we find in
    the rest of the script.

*** Since files in lib/ are picked up as needed by src/, we no longer
    generate a Makefile for that directory.

*** Flex can be cross compiled.

** documentation

*** Some typos were removed from the manual.

** scanner

*** Some minor performance enhancements.

*** We honor user defined yy_* macros again. We are also more careful
    to not leak macro definitions into header files.

*** A number of portability fixes were introduced so building flex is
    more reliable on more platforms. Additionally, outdated function
    calls were removed.

*** When building the flex executable itself, %# comments from
    flex.skl are removed when generating the C source code array. This
    reduces the size of flex.

** test suite

*** All scripts in the test suite are now run by $(SHELL) and the
    needed portability fixes have been included.

*** Test suite dependencies are handled much better. This only matters
    if you are actively developing flex or its test suite.

*** Tests that depend on platform dependent features now properly skip
    when those platforms are not present.

*** When running "make check", you can now pas V=0 to silence more of
    the build. This is useful when you're less connncerned about the
    details of building and linking the test programs themselves.

* Noteworthy changes in release 2.6.3 (2016-12-30) [stable]

** scanner

*** several bug fixes resolved problems introduced in recent flex
    versions regarding processing of comments, literals and various
    quoting scenarios.

*** If the path to m4 was sufficiently long, a buffer overflow could
    occur. This has been resolved. The fix also removes dependence on
    the constant PATH_MAX.

** build

*** A new configure option --disable-bootstrap changes the behavior of
    the build system when building flex. The default
    "--enable-bootstrap" behavior is to build flex, then to use that
    flex to build flex again. With --disable-bootstrap, the scanner is
    simply built by sedding the scanner source. This is friendlier to
    cross compilation.

*** The compatibility functions in lib/ are no longer built as a
    library. Instead, they are built as $(LIBOBJ) objects. This is
    simpler and friendlier to cross compilation.

*** It is now possible to build flex without building the accompanying
    libfl. This is friendlier to cross compilation. See the
    --disable-libfl option to configure. Resolves #99.

*** the PIC version of libfl was not correctly built. It is no longer
    included in the build/installation targets of flex since it was
    unused.

*** the distributed man page is only rebuilt when the relevant source
    files change or when the binary doesn't exist. In particular, this
    is friendlier to cross compilation. Resolves #108

** test

*** the shell scripts in the test suite are more portable across different shell implementations.

* version 2.6.2 released 2016-10-24

** flex internals

*** a segfalt involving yyrestart(NULL) has been fixed

*** flex should now handle quoting when mixed with m4 processing correctly

*** flex handles `[[' and `]]' correctly

*** flex no longer generates non-ANSI code

*** more compilation warnings were squashed in generated scanners

*** prevented a buffer overflow that could occur when input buffers were the exact wrong size

** test suite

*** input filenames on MSWindows are now calculated correctly

*** general code cleanups in a number of tests now make the test suite compile much more cleanly

** build system

*** the xz archive has been replaced with an lzip archive

*** a new option to configure --enable-warnings to encapsulate passing
    of warning-related flags which is useful in testing flex

*** make indent now works for out of source builds

*** Portability warnings when generating Makefile.in files are now suppressed; they were just noise and the use of GNU extensions in Makefile.{am,in,} was intentional and well known.

** bugs

*** resolved gh#67

** new sv translation from the translation project

* version 2.6.1 released 2016-03-01

** flex resources

*** The flex project is now hosted at github. Consider this a "period of transition". In particular, you should start at https://github.com/westes/flex for the flex codebase, issue tracking and pull requests.

*** New releases of flex are to be found at https://github.com/westes/flex/releases.

** flex internals

*** Flex now uses more modern and more standard names for variable types. There's more work to be done on that front yet, though.

*** A number of compiler warnings have been remedied.

*** Line directives should now  work as expected and be absent when that is expected.

** test suite

*** When running the test suite, c++ files are compiled with the c++ header inside the flex distribution, rather than relying on the build system's flex header , which might not be installed yet or which might be out of date with respect to what flex tests expect.

*** Some portability fixes in the test suite such as opening files for reading in binary mode

** Building flex

*** The file src/scan.c asdistributed  with flex source is now built with the current version of flex. Occasionally this had to be done manually to pick up new flex features. It's now just a part of flex's build system.

*** The pdf version of the manual is no longer distributed with flex, although if you have the texinfo package installed, you can still build it.

*** lots of general build system cleanup

*** the build system tries a bit harder to find libtoolize and texi2dvi.

*** When help2man and texi2dvi are missing, the error messages are now much more helpful.

** bug fixes

*** resolved github issues #53, #54, #55, #61.

*** Resolved sf bugs #128, #129, #155, #160, #184, #187, #195.

* version 2.6.0 released 2015-11-17

** User Visible Changes

*** C++ scanners now use references instead of pointers. See the manual for details.

*** A number of compiler warnings were addressed, so flex generated scanners should be quieter under compiler warning scenarios.

*** Allow error reporting routines to accept varying number of arguments

*** Removed deprecated 'register' storage class specifier

*** Changeed output formats from octal to hexadecimal

*** check limits before using array index cclp; resolves sf-166

*** Suppress clang warning about empty @param paragraph; resolves sf#158

*** Fixed malloc/realloc replacement, resolves sf bug#151.

*** Adjusted buffer sizes on ia64.

*** various documentation and code clean up fixes: resolves sf bugs #167, #168, among other patches.

** Flex Internals

*** flex is now organized into subdirectories. This keeps the tree neater at the top level and puts like things near each other and unlike things away from each other.

*** The test suite has been reorganized and is now run with the parallel test suite harness from automake.

*** Cleaned up the automake parts of the build system to better reflect what automake does on its own. Also added a call to libtoolize in autogen.sh because autoreconf gets confused without a prior run of libtoolize.

*** po/Makefile now includes a rule to fetch the latest translations from the translation project. "make -f po/Makefile getpo" from the top level of the flex tree will fetch the files.

*** New da translation from the translation project

* flex version  2.5.39 released 2014-03-26

** no user visible changes in this release

* version 2.5.38 released 2014-02-14

** internationalization

*** add sr translation from the translation project

*** update da, es, ko, nl, pt_BR, ro, ru, sv, tr, vi, zh_CN translations from the translation project

*** rename zh_tw to its proper zh_TW name

* version 2.5.37 released 2012-08-03

** Import flex into git. See
   git://flex.git.sourceforge.net/gitroot/flex/flex.

** Fix make install target to not fail when the flex++ program is
   already installed

** New translations from the translation project: de, fi, pl, vi

* version 2.5.36 released 2012-07-20

** various portability fixes that quiet compiler warnings on 64-bit
   hosts

** various manual fixes, including correcting the name of a %option and
   updating some simple examples to use ANSI C syntax

** various bug fixes that prevent certain error conditions from
   persisting when they should not persist

** improvements to the test suite so it behaves better when linking
   compiled files

** new translations from the translation project: ca, da, es, fi, fr,
   ga, ko, pt_br, ro, ru, sv, tr, zh_cn

** the flex distribution is now built with automake 1.10.1 and automake
   2.61

* version 2.5.35 released 2008-02-26

** fixed bug that prevented flex from accepting certain comments in the
  scanner file (resolves bugs #1849809 and #1849805)

** fix bug that prevented headers for all functions from being generated
  (resolves bug #1628314)

** change yy_size_t to be size_t (resolves bug #1849812)

** new de, nl, pl, pt_br, vi translations from the translation project

* version 2.5.34 released 2007-12-12

** introduce yylex_init_extra; see the manual for details

** introduce %option extra-type="your_type *" (resolves bug #1744505)

** The flex program now parses multiple short concatenated options (resolves bug
  #1619820). Thanks to Petr Machata of Red Hat on this issue.

** better checking after yyalloc/yyrealloc (resolves bug #1595967)

** flex now provides for a libfl_pic.a compiled with position
   independent code. Particularly useful when including a flex scanner
   in a shared library and with more recent versions of gcc. Thanks to the Debian project for the idea.

** SourceForge feature request #1658379: Expose YY_BUF_SIZE in the
	header file.

** flex better escapes filenames with special characters in them
   (resolves bug #1623600)

** a memory leak was plugged(resolves bug #1601111)

** pattern language expanded; see the manual for details on the below
   highlights

*** pattern options added to specify patterns as case-insensitive or
    case-sensitive

*** pattern options to specify whether the "." character should match
    the newline character

*** pattern options added to allow ignoring of whitespace in patterns

*** POSIX character classes may be negated in patterns

*** patterns may now use set difference, union operators

** the manual now contains an appendix listing various common patterns
   which may be useful when writing scanners

** some memory leaks were removed from the C++ scanner (but the C++
  scanner is still experimental and may change radically without
  notice)

** c++ scanners can now use yywrap

** added new unit test for c++ and yywrap

** portability fixes to some unit tests

** flex man page and flex manual in pdf now distributed in the flex
distribution

** new ca, vi, ga, nl translations from the translation project

** flex no longer comes with an rpm spec file

** flex development now happens with automake 1.9.6

* version 2.5.33 released 2006-2-20

** all flex resources are now to be found from the website at
   http://flex.sourceforge.net/

** there was no release 2.5.32 published

** numerous bug and security fixes

** new nl, vi, sv, ro, po, ga, ca, fr, tr translations from the translation project

** upgrade to use gettext 0.12 (this now makes the "pdf" and "ps"
   targets in the build system able to be run successfully)

* version 2.5.31 released 2003-4-1

** remove --enable-maintainer-mode configure option; none of the
   Makefiles were using it and it can be unduely confusing

* version 2.5.30 released 2003-4-1

** yylineno is per-buffer in reentrant scanners

** added %top directive for placing code at the top of the generated
   scanner; see manual for details

** flex now uses m4 to generate scanners; while this means that
   scanners are more readable, it means that flex requires m4 to be
   installed; see manual for details

* version 2.5.29 released 2003-3-5

** Automatic stack management for multiple input buffers in C and C++ scanners

** moved the flex documentation to a new doc/ subdirectory

** cleanups to the yy namespace

* version 2.5.28 released 2003-2-12

** flex is now hosted at sourceforge

** Fixed trailing slash bug in YY_INPUT macro def

** Flex now warns if always-interactive is specified with fast or full

* version 2.5.27 released 2003-1-21

** flex now works with recent bison versions

** new pt_br translation from the translation project

* version 2.5.26 released 2003-1-14

** Fixed table deserialization bug on big-endian archs. Patch sent from Bryce Nichols <<EMAIL>>

** yyleng has proper declarations now; this caused flex to generate
   unusable scanners for some programs

** the flex distribution now includes a spec file suitable for use
   with rpm

** some more c++ fixes

** new es translation from the translation project

** slight tweeks to the flex_int*_t types

** flex now warns about pattern ranges that might be ambiguous when
   generating a case-insensitive scanner


* version 2.5.25 released 2002-12-2

** flex now uses flex_int*_t types. For C99 systems, they are just the
   int*_t types; for non-C99 systems, we just make some typedefs

** new pt_br translation from the translation project

* version 2.5.24 released 2002-11-25

* more portability fixes

** the manual continues to be updated and edited, but it's still got a
   ways to go

** it is possible to have multiple c++ scanners in the same program again

** new turkish translation from the translation project

* version 2.5.23 released 2002-10-21

** more portability fixes

** the manual includes a title page and a table-of-contents when printed

** the test suite can be run with "make check" from the top-level
   directory

** configure now accepts the --enable-maintainer-mode option

** gettext functionality is now only available externally

** the constant FLEX_BETA is defined if flex is a beta release

** the script create-test was not included in the distribution and it
   should have been

* version 2.5.22 released 2002-10-10

** more portability fixes around how we get ahold of the integral
   types; there is a constant FLEX_NEED_INTEGRAL_TYPE_DEFINITIONS
   which you should define if you don't have the <inttypes.h> header
   file (after you complain to your C vendor for not providing a
   reasonable C environment)

** more test suite cleanups; in particular, the test suite should run
   correctly when build from a different directory

** upgraded automake to 1.7 and consequently autoconf to 2.54; this
   means, among other things, that there is some support for
formatting the manual in postscript and pdf in the distributed
   Makefile.in (and therefore in the Makefile built by configure)

** the flex.1 manpage is generated by help2man; (this has been true
   for quite a while but was not listed here)

** flex now includes three defined constants to indicate which version
  of flex generated a scanner (YY_FLEX_{MAJOR,MINOR,SUBMINOR}_VERSION)

** flex tries its best to output only the relevant portions of the
   skeleton when generating a scanner, thus avoiding as much
   conditional compilation as possible

* version 2.5.21 released 2002-9-17

** one of the tests in the test suite broke the dist target

* version 2.5.20 released 2002-9-16

** A flex scanner has the ability to save the DFA tables to a file,
   and load them at runtime when needed; see the manual for details

** Added %option bison-bridge (--bison-bridge)

** Removed %option reentrant-bison/--reentrant-bison/-Rb

** yylineno is present in all scanners; Modified nasty performance
   penalty warning with yylineno in documentation

** test-table-opts is now run last in the test suite because it's so fat

** flex can, to some extent, diagnose where internal problems occur

** new translations from the translation project: fr, ca, de, ru, sv

**Flex generates C99 defs now; see YY_TRADITIONAL_FUNC_DEFS in the
  manual if that's not a good thing for you

* version 2.5.19 released 2002-9-5

** prevent segfault on input lines which are longer than the allocated
   space (problem report from Manoj Srivastava
   <<EMAIL>>)

** Changed option 'header' to 'header-file'

* version 2.5.18 released 2002-9-4

** portability fixes for integer constants and in the way the test
   suite reports its results

** the test for bison was reporting bison missing when it was, in
   fact, found

** if we don't find GNU indent, we're more careful when we're not
   finding it

* version 2.5.17 released 2002-8-29

** more portability fixes

** updated config.sub and config.guess

** flex is indented by GNU indent (this was done earlier but not
   explicitly documented)

* version 2.5.16 released 2002-8-28

** c++ scanners compile again

** there is now an indent target in the top-level Makefile; configure
   checks for GNU indent which is required for proper operation of the
   indent target

** some more portability fixes were made

** %options and invocation sections of manual merged

** a c++ test was added to the test suite

** we're trying to clean up more files in the test suite's make clean
   targets

* version 2.5.15 released 2002-8-21

** reject-state buffer is now dynamically allocated and REJECT buffer
   variables are reentrant-safe

** manual now discusses memory usage

** skeleton now processed by m4 before mkskel.sh; (this only matters
   if you want to change the skeleton or if you're doing flex development)

** zh_cn translation added from translation project

** a bug that caused a segfault has now been fixed

** the test suite now respects the usual CFLAGS, etc. variables

** removed some warnings which some tests trigggered with the -s option

** the flex-generated header file now tries to be smarter about
   conditionally including start conditions

** tables code omitted from generated scanner when not used

* version 2.5.14 released 2002-8-15

** the tests using the reentrant c scanner as c++ were reworked
   slightly to be sure that the c++ was enforced

** de translation now included in the distribution

** various portability fixes regarding nls support, c++ include
   headers, etc.

* version 2.5.13 released 2002-8-15

** the header file output with %option header is now much smaller

** Fixed type mismatch in printf in scanner skeleton

** yylex_init now reports errors

* version 2.5.12 released 2002-8-8

** updated gettext support to 0.11.5

** new fr translation from the translation project

** bison is no longer needed to build flex; If you are building flex
   from a release (i.e., not from a cvs snapshot), then you don't need
   to have a pre-built lex around either (unless you modify scan.l, of
   course); (This has been true for some time, but was not mentioned
   here.)

* version 2.5.11 released 2002-7-31

** Fixed bug where yyless did not consider yylineno

** the yylineno performance hit is now gone

** fixed some typos in the manual and we now include texinfo.tex in
   the distribution

** traditional prototypes output for C scanners, controlled by a
   preprocessor symbol; see documentation for details

* version 2.5.10 released 2002-7-24

** yy_globals renamed to yyscanner and yy_globals_t renamed to
   yy_guts_t

** added dist-bzip2 option to Makefile.am so we now produce a bzip2'd
   archive in addition to the standard gzip archive

*  version 2.5.9

** new tests in test suite: test-mem-{nr,r}, test-posix,
   test-posixly-correct, test-debug-{nr,r}

** made changes to work with gcc-3.2 development code

** ability to choose which memory functions are used in flex

** new yylex_destroy() function for the non-reentrant scanner

** new handling of POSIXLY_CORRECT environment variable

** the test suite now has its copyrights explicitly described

** new ca, de, fr, ru, sv, tr translations

* version 2.5.8

** a new --posix option generates scanners with posix-style abc{1,3}
   compatible parsing, see manual for the screwy details

* version 2.5.7

** configure.in now includes a call to AC_PREREQ to enforce the
   requirement for autoconf at least 2.50 (This only effects you if
   you're doing flex development.)

** configure now uses autoconf's versioning information and configure
   --help reports the bug-reporting address for flex

** test suite now only reports success versus failure; reporting
   skipped is problematic under the current setup

** compilation with --disable-nls now works

** flex can now be built in a separate directory

* version 2.5.6

** gettext support added (from gettext 0.11)

*** translations for ca, da, de, es, fr, ko, ru, sv, tr included

** distribution now built under automake 1.6 and autoconf 2.53

** command-line option parsing happens differently now:

*** Added long option parsing

*** Options -n and -c, previously deprecated, now simply do nothing

*** Options are now parsed left to right

** added a number of new options

*** All positive %options are now accessible from the command line

*** Added option -D, to define a preprocessor symbol

*** Added option --header=FILE to specify a C .h file to generate

*** added option --yywrap to call yywrap on EOF

*** added option --yylineno to track line count in yylineno

*** --yyclass=NAME name of C++ class when generating c++ scanners

*** for long option names which are associated with existing short
options, see accompanying documentation

*** new %option nounistd or command-line --nounistd added to prevent
    flex from generating #include <unistd.h> on systems that don't
    have that include file

** Support for reentrant C scanners has been added

*** Updated the manual with the new reentrant API

*** Two new options %option reentrant (-R) and 
%option reentrant-bison (-Rb)

*** All globals optionally placed into struct yyglobals_t

*** All access to globals replaced by macro invocations

*** All functions optionally take one additional
argument, yy_globals

*** New style for invoking reentrant scanner:
yylex_init(void** scanner );
yylex( scanner );
yylex_destroy( scanner );

*** Added get/set functions for members of struct yy_globals_t
e.g.,  yyget_text, yyget_leng, etc

*** Prefix substitution added for new functions

*** Macro shortcuts to the lengthy get/set functions
provided for use in actions, e.g.,  yytext, yyleng, etc

*** Arbitrary, user-defined data, "yyextra", may be added to scanner

** %option nomain no longer implies %option yywrap
But the inverse is still true

** Developer test suite added

*** TESTS/ directory has been added. Users can 
'make test' in the TESTS directory to execute the test suite

** Support for bison variables yylval and yylloc added

** automake support for the build process

** manual is now in texinfo/info format

*** flex.1 removed from distribution

** flex no longer generates C-language scanners with C++-style
   comments

** flex now generates scanners in c++ which are compatible with
   recent c++ compilers

** flex input scanner now recognizes '\r' as an EOL character

See the file ONEWS for changes in earlier releases.

See the file COPYING for copying conditions.

Local Variables:
mode: text
mode: outline-minor
end:
