# Time-stamp: "2016-07-25 07:11:15 MDT"
$Text::Unidecode::Char[0x33] = [
'apartment', 'alpha', 'ampere', 'are', 'inning', 'inch', 'won', 'escudo', 'acre', 'ounce', 'ohm', qq{kai-ri}, 'carat', 'calorie', 'gallon', 'gamma',
'giga', 'guinea', 'curie', 'guilder', 'kilo', 'kilogram', 'kilometer', 'kilowatt', 'gram', 'gram ton', 'cruzeiro', 'krone', 'case', 'koruna', qq{co-op}, 'cycle',
'centime', 'shilling', 'centi', 'cent', 'dozen', 'desi', 'dollar', 'ton', 'nano', 'knot', 'heights', 'percent', 'parts', 'barrel', 'piaster', 'picul',
'pico', 'building', 'farad', 'feet', 'bushel', 'franc', 'hectare', 'peso', 'pfennig', 'hertz', 'pence', 'page', 'beta', 'point', 'volt', 'hon',
'pound', 'hall', 'horn', 'micro', 'mile', 'mach', 'mark', 'mansion', 'micron', 'milli', 'millibar', 'mega', 'megaton', 'meter', 'yard', 'yard',
'yuan', 'liter', 'lira', 'rupee', 'ruble', 'rem', 'roentgen', 'watt', '0h', '1h', '2h', '3h', '4h', '5h', '6h', '7h',
'8h', '9h', '10h', '11h', '12h', '13h', '14h', '15h', '16h', '17h', '18h', '19h', '20h', '21h', '22h', '23h',
'24h', 'HPA', 'da', 'AU', 'bar', 'oV', 'pc', '[?]', '[?]', '[?]', '[?]', 'Heisei', 'Syouwa', 'Taisyou', 'Meiji', qq{Inc.},
'pA', 'nA', 'microamp', 'mA', 'kA', 'kB', 'MB', 'GB', 'cal', 'kcal', 'pF', 'nF', 'microFarad', 'microgram', 'mg', 'kg',
'Hz', 'kHz', 'MHz', 'GHz', 'THz', 'microliter', 'ml', 'dl', 'kl', 'fm', 'nm', 'micrometer', 'mm', 'cm', 'km', qq{mm^2},
qq{cm^2}, qq{m^2}, qq{km^2}, qq{mm^4}, qq{cm^3}, qq{m^3}, qq{km^3}, qq{m/s}, qq{m/s^2}, 'Pa', 'kPa', 'MPa', 'GPa', 'rad', qq{rad/s}, qq{rad/s^2},
'ps', 'ns', 'microsecond', 'ms', 'pV', 'nV', 'microvolt', 'mV', 'kV', 'MV', 'pW', 'nW', 'microwatt', 'mW', 'kW', 'MW',
'kOhm', 'MOhm', qq{a.m.}, 'Bq', 'cc', 'cd', qq{C/kg}, qq{Co.}, 'dB', 'Gy', 'ha', 'HP', 'in', qq{K.K.}, 'KM', 'kt',
'lm', 'ln', 'log', 'lx', 'mb', 'mil', 'mol', 'pH', qq{p.m.}, 'PPM', 'PR', 'sr', 'Sv', 'Wb', '[?]', '[?]',
'1d', '2d', '3d', '4d', '5d', '6d', '7d', '8d', '9d', '10d', '11d', '12d', '13d', '14d', '15d', '16d',
'17d', '18d', '19d', '20d', '21d', '22d', '23d', '24d', '25d', '26d', '27d', '28d', '29d', '30d', '31d', 'gal',
];
1;
