.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_subject_alt_name" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_subject_alt_name \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_subject_alt_name(gnutls_x509_crt_t " cert ", unsigned int " seq ", void * " san ", size_t * " san_size ", unsigned int * " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "unsigned int seq" 12
specifies the sequence number of the alt name (0 for the first one, 1 for the second etc.)
.IP "void * san" 12
is the place where the alternative name will be copied to
.IP "size_t * san_size" 12
holds the size of san.
.IP "unsigned int * critical" 12
will be non\-zero if the extension is marked as critical (may be null)
.SH "DESCRIPTION"
This function retrieves the Alternative Name (*********), contained
in the given certificate in the X509v3 Certificate Extensions.

When the SAN type is otherName, it will extract the data in the
otherName's value field, and \fBGNUTLS_SAN_OTHERNAME\fP is returned.
You may use \fBgnutls_x509_crt_get_subject_alt_othername_oid()\fP to get
the corresponding OID and the "virtual" SAN types (e.g.,
\fBGNUTLS_SAN_OTHERNAME_XMPP\fP).

If an otherName OID is known, the data will be decoded.  Otherwise
the returned data will be DER encoded, and you will have to decode
it yourself.  Currently, only the RFC 3920 id\-on\-xmppAddr SAN is
recognized.
.SH "RETURNS"
the alternative subject name type on success, one of the
enumerated \fBgnutls_x509_subject_alt_name_t\fP.  It will return
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP if  \fIsan_size\fP is not large enough to
hold the value.  In that case  \fIsan_size\fP will be updated with the
required size.  If the certificate does not have an Alternative
name with the specified sequence number then
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
