.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dtls_prestate_set" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dtls_prestate_set \- API function
.SH SYNOPSIS
.B #include <gnutls/dtls.h>
.sp
.BI "void gnutls_dtls_prestate_set(gnutls_session_t " session ", gnutls_dtls_prestate_st * " prestate ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
a new session
.IP "gnutls_dtls_prestate_st * prestate" 12
contains the client's prestate
.SH "DESCRIPTION"
This function will associate the prestate acquired by
the cookie authentication with the client, with the newly 
established session.

This functions must be called after a successful \fBgnutls_dtls_cookie_verify()\fP
and should be succeeded by the actual DTLS handshake using \fBgnutls_handshake()\fP.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
