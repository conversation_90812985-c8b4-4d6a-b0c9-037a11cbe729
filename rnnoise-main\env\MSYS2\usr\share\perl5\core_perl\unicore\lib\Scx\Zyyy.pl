# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V294
0
65
91
97
123
170
171
186
187
192
215
216
247
248
697
736
741
746
748
768
884
885
894
895
901
902
903
904
1541
1542
1757
1758
2274
2275
3647
3648
4053
4057
5867
5870
8192
8204
8206
8239
8240
8293
8294
8305
8308
8319
8320
8335
8352
8385
8448
8486
8487
8490
8492
8498
8499
8526
8527
8544
8585
8588
8592
9255
9280
9291
9312
10240
10496
11124
11126
11158
11159
11264
11776
11843
11844
11870
12272
12284
12288
12289
12292
12293
12306
12307
12320
12321
12342
12343
12872
12896
12927
12928
12977
12992
13004
13008
13169
13179
13184
13280
13311
13312
19904
19968
42760
42786
42888
42891
43867
43868
43882
43884
65040
65050
65072
65093
65095
65107
65108
65127
65128
65132
65279
65280
65281
65313
65339
65345
65371
65377
65504
65511
65512
65519
65529
65534
65936
65949
66000
66045
118608
118724
118784
119030
119040
119079
119081
119143
119146
119163
119171
119173
119180
119210
119214
119275
119488
119508
119520
119540
119552
119639
119666
119673
119808
119893
119894
119965
119966
119968
119970
119971
119973
119975
119977
119981
119982
119994
119995
119996
119997
120004
120005
120070
120071
120075
120077
120085
120086
120093
120094
120122
120123
120127
120128
120133
120134
120135
120138
120145
120146
120486
120488
120780
120782
120832
126065
126133
126209
126270
126976
127020
127024
127124
127136
127151
127153
127168
127169
127184
127185
127222
127232
127406
127462
127488
127489
127491
127504
127548
127552
127561
127584
127590
127744
128728
128732
128749
128752
128765
128768
128887
128891
128986
128992
129004
129008
129009
129024
129036
129040
129096
129104
129114
129120
129160
129168
129198
129200
129202
129280
129620
129632
129646
129648
129661
129664
129673
129680
129726
129727
129734
129742
129756
129760
129769
129776
129785
129792
129939
129940
129995
130032
130042
917505
917506
917536
917632
END
