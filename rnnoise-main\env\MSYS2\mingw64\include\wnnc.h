/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#ifndef _WNNC_
#define _WNNC_

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WIN<PERSON><PERSON>_PARTITION_DESKTOP)

#define WNNC_NET_MSNET		0x00010000
#define WNNC_NET_LANMAN		WNNC_NET_SMB
#define WNNC_NET_SMB		0x00020000
#define WNNC_NET_NETWARE	0x00030000
#define WNNC_NET_VINES		0x00040000
#define WNNC_NET_10NET		0x00050000
#define WNNC_NET_LOCUS		0x00060000
#define WNNC_NET_SUN_PC_NFS	0x00070000
#define WNNC_NET_LANSTEP	0x00080000
#define WNNC_NET_9TILES		0x00090000
#define WNNC_NET_LANTASTIC	0x000a0000
#define WNNC_NET_AS400		0x000b0000
#define WNNC_NET_FTP_NFS	0x000c0000
#define WNNC_NET_PATHWORKS	0x000d0000
#define WNNC_NET_LIFENET	0x000e0000
#define WNNC_NET_POWERLAN	0x000f0000
#define WNNC_NET_BWNFS		0x00100000
#define WNNC_NET_COGENT		0x00110000
#define WNNC_NET_FARALLON	0x00120000
#define WNNC_NET_APPLETALK	0x00130000
#define WNNC_NET_INTERGRAPH	0x00140000
#define WNNC_NET_SYMFONET	0x00150000
#define WNNC_NET_CLEARCASE	0x00160000
#define WNNC_NET_FRONTIER	0x00170000
#define WNNC_NET_BMC		0x00180000
#define WNNC_NET_DCE		0x00190000
#define WNNC_NET_AVID		0x001a0000
#define WNNC_NET_DOCUSPACE	0x001b0000
#define WNNC_NET_MANGOSOFT	0x001c0000
#define WNNC_NET_SERNET		0x001d0000
#define WNNC_NET_RIVERFRONT1	0x001e0000
#define WNNC_NET_RIVERFRONT2	0x001f0000
#define WNNC_NET_DECORB		0x00200000
#define WNNC_NET_PROTSTOR	0x00210000
#define WNNC_NET_FJ_REDIR	0x00220000
#define WNNC_NET_DISTINCT	0x00230000
#define WNNC_NET_TWINS		0x00240000
#define WNNC_NET_RDR2SAMPLE	0x00250000
#define WNNC_NET_CSC		0x00260000
#define WNNC_NET_3IN1		0x00270000
#define WNNC_NET_EXTENDNET	0x00290000
#define WNNC_NET_STAC		0x002a0000
#define WNNC_NET_FOXBAT		0x002b0000
#define WNNC_NET_YAHOO		0x002c0000
#define WNNC_NET_EXIFS		0x002d0000
#define WNNC_NET_DAV		0x002e0000
#define WNNC_NET_KNOWARE	0x002f0000
#define WNNC_NET_OBJECT_DIRE	0x00300000
#define WNNC_NET_MASFAX		0x00310000
#define WNNC_NET_HOB_NFS	0x00320000
#define WNNC_NET_SHIVA		0x00330000
#define WNNC_NET_IBMAL		0x00340000
#define WNNC_NET_LOCK		0x00350000
#define WNNC_NET_TERMSRV	0x00360000
#define WNNC_NET_SRT		0x00370000
#define WNNC_NET_QUINCY		0x00380000
#define WNNC_NET_OPENAFS	0x00390000
#define WNNC_NET_AVID1		0x003a0000
#define WNNC_NET_DFS		0x003b0000
#define WNNC_NET_KWNP		0x003c0000
#define WNNC_NET_ZENWORKS	0x003d0000
#define WNNC_NET_DRIVEONWEB	0x003e0000
#define WNNC_NET_VMWARE		0x003f0000
#define WNNC_NET_RSFX		0x00400000
#define WNNC_NET_MFILES		0x00410000
#define WNNC_NET_MS_NFS		0x00420000
#define WNNC_NET_GOOGLE		0x00430000
#define WNNC_NET_NDFS		0x00440000

#define WNNC_CRED_MANAGER   0xffff0000

#endif /* WINAPI_PARTITION_DESKTOP.  */

#endif
