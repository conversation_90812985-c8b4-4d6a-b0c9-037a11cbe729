.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_export_dsa_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_export_dsa_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_export_dsa_raw(gnutls_privkey_t " key ", gnutls_datum_t * " p ", gnutls_datum_t * " q ", gnutls_datum_t * " g ", gnutls_datum_t * " y ", gnutls_datum_t * " x ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t key" 12
Holds the public key
.IP "gnutls_datum_t * p" 12
will hold the p
.IP "gnutls_datum_t * q" 12
will hold the q
.IP "gnutls_datum_t * g" 12
will hold the g
.IP "gnutls_datum_t * y" 12
will hold the y
.IP "gnutls_datum_t * x" 12
will hold the x
.SH "DESCRIPTION"
This function will export the DSA private key's parameters found
in the given structure. The new parameters will be allocated using
\fBgnutls_malloc()\fP and will be stored in the appropriate datum.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
