/*
 * Copyright (C) 2005 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#if 0
#pragma makedep install
#endif

#include <xmldomdid.h>
#include <idispids.h>

#if !defined(progid) && !defined(__WIDL__)
#define threading(model)
#define progid(str)
#define vi_progid(str)
#endif

interface IXMLDOMImplementation;
interface IXMLDOMNode;
interface IXMLDOMDocumentFragment;
interface IXMLDOMDocument;
interface IXMLDOMNodeList;
interface IXMLDOMNamedNodeMap;
interface IXMLDOMCharacterData;
interface IXMLDOMAttribute;
interface IXMLDOMElement;
interface IXMLDOMText;
interface IXMLDOMComment;
interface IXMLDOMProcessingInstruction;
interface IXMLDOMCDATASection;
interface IXMLDOMDocumentType;
interface IXMLDOMNotation;
interface IXMLDOMEntity;
interface IXMLDOMEntityReference;
interface IXMLDOMParseError;

cpp_quote("#ifndef __MSXML_DOMNODETYPE_DEFINED")
cpp_quote("#define __MSXML_DOMNODETYPE_DEFINED")
typedef enum tagDOMNodeType {
    NODE_INVALID,
    NODE_ELEMENT,
    NODE_ATTRIBUTE,
    NODE_TEXT,
    NODE_CDATA_SECTION,
    NODE_ENTITY_REFERENCE,
    NODE_ENTITY,
    NODE_PROCESSING_INSTRUCTION,
    NODE_COMMENT,
    NODE_DOCUMENT,
    NODE_DOCUMENT_TYPE,
    NODE_DOCUMENT_FRAGMENT,
    NODE_NOTATION
} DOMNodeType;
cpp_quote("#endif")

[
local,
object,
odl,
dual,
oleautomation,
/*nonextensible,*/
uuid(2933bf80-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMNode : IDispatch
{
    [propget, id(DISPID_DOM_NODE_NODENAME)]
    HRESULT nodeName( [out,retval] BSTR *name );

    [propget, id(DISPID_DOM_NODE_NODEVALUE)]
    HRESULT nodeValue( [out,retval] VARIANT *value );

    [propput, id(DISPID_DOM_NODE_NODEVALUE)]
    HRESULT nodeValue( [in] VARIANT value );

    [propget, id(DISPID_DOM_NODE_NODETYPE)]
    HRESULT nodeType( [out,retval] DOMNodeType *type );

    [propget, id(DISPID_DOM_NODE_PARENTNODE)]
    HRESULT parentNode( [out,retval] IXMLDOMNode **parent );

    [propget, id(DISPID_DOM_NODE_CHILDNODES)]
    HRESULT childNodes( [out,retval] IXMLDOMNodeList **childList );

    [propget, id(DISPID_DOM_NODE_FIRSTCHILD)]
    HRESULT firstChild( [out,retval] IXMLDOMNode **firstChild );

    [propget, id(DISPID_DOM_NODE_LASTCHILD)]
    HRESULT lastChild( [out,retval] IXMLDOMNode **lastChild );

    [propget, id(DISPID_DOM_NODE_PREVIOUSSIBLING)]
    HRESULT previousSibling( [out,retval] IXMLDOMNode **previousSibling );

    [propget, id(DISPID_DOM_NODE_NEXTSIBLING)]
    HRESULT nextSibling( [out,retval] IXMLDOMNode **nextSibling );

    [propget, id(DISPID_DOM_NODE_ATTRIBUTES)]
    HRESULT attributes( [out,retval] IXMLDOMNamedNodeMap **attributeMap );

    [id(DISPID_DOM_NODE_INSERTBEFORE)]
    HRESULT insertBefore( [in] IXMLDOMNode * newChild,
                          [in] VARIANT refChild,
                          [out,retval] IXMLDOMNode **outNewChild );

    [id(DISPID_DOM_NODE_REPLACECHILD)]
    HRESULT replaceChild( [in] IXMLDOMNode *newChild,
                          [in] IXMLDOMNode *oldChild,
                          [out,retval] IXMLDOMNode **outOldChild );

    [id(DISPID_DOM_NODE_REMOVECHILD)]
    HRESULT removeChild( [in] IXMLDOMNode *childNode,
                         [out,retval] IXMLDOMNode **oldChild );

    [id(DISPID_DOM_NODE_APPENDCHILD)]
    HRESULT appendChild( [in] IXMLDOMNode *newChild,
                         [out,retval] IXMLDOMNode **outNewChild );

    [id(DISPID_DOM_NODE_HASCHILDNODES)]
    HRESULT hasChildNodes( [out,retval] VARIANT_BOOL *hasChild );

    [propget, id(DISPID_DOM_NODE_OWNERDOC)]
    HRESULT ownerDocument( [out,retval] IXMLDOMDocument **DOMDocument );

    [id(DISPID_DOM_NODE_CLONENODE)]
    HRESULT cloneNode( [in] VARIANT_BOOL deep,
                       [out,retval] IXMLDOMNode **cloneRoot );

    [propget, id(DISPID_XMLDOM_NODE_STRINGTYPE)]
    HRESULT nodeTypeString( [out,retval] BSTR *nodeType );

    [propget, id(DISPID_XMLDOM_NODE_TEXT)]
    HRESULT text( [out,retval] BSTR *text );

    [propput, id(DISPID_XMLDOM_NODE_TEXT)]
    HRESULT text( [in] BSTR text );

    [propget, id(DISPID_XMLDOM_NODE_SPECIFIED)]
    HRESULT specified( [out,retval] VARIANT_BOOL *isSpecified );

    [propget, id(DISPID_XMLDOM_NODE_DEFINITION)]
    HRESULT definition( [out,retval] IXMLDOMNode **definitionNode );

    [propget, id(DISPID_XMLDOM_NODE_NODETYPEDVALUE)]
    HRESULT nodeTypedValue( [out,retval] VARIANT *typedValue );

    [propput, id(DISPID_XMLDOM_NODE_NODETYPEDVALUE)]
    HRESULT nodeTypedValue( [in] VARIANT typedValue );

    [propget, id(DISPID_XMLDOM_NODE_DATATYPE)]
    HRESULT dataType( [out,retval] VARIANT *dataTypeName );

    [propput, id(DISPID_XMLDOM_NODE_DATATYPE)]
    HRESULT dataType( [in] BSTR dataTypeName );

    [propget, id(DISPID_XMLDOM_NODE_XML)]
    HRESULT xml( [out,retval] BSTR *xmlString );

    [id(DISPID_XMLDOM_NODE_TRANSFORMNODE)]
    HRESULT transformNode( [in] IXMLDOMNode *styleSheet,
                           [out,retval] BSTR *xmlString );

    [id(DISPID_XMLDOM_NODE_SELECTNODES)]
    HRESULT selectNodes( [in] BSTR queryString,
                         [out,retval] IXMLDOMNodeList **resultList );

    [id(DISPID_XMLDOM_NODE_SELECTSINGLENODE)]
    HRESULT selectSingleNode( [in] BSTR queryString,
                              [out,retval] IXMLDOMNode **resultNode );

    [propget, id(DISPID_XMLDOM_NODE_PARSED)]
    HRESULT parsed( [out,retval] VARIANT_BOOL *isParsed );

    [propget, id(DISPID_XMLDOM_NODE_NAMESPACE)]
    HRESULT namespaceURI( [out,retval] BSTR *namespaceURI );

    [propget, id(DISPID_XMLDOM_NODE_PREFIX)]
    HRESULT prefix( [out,retval] BSTR *prefixString );

    [propget, id(DISPID_XMLDOM_NODE_BASENAME)]
    HRESULT baseName( [out,retval] BSTR *nameString );

    [id(DISPID_XMLDOM_NODE_TRANSFORMNODETOOBJECT)]
    HRESULT transformNodeToObject( [in] IXMLDOMNode *stylesheet,
                                   [in] VARIANT outputObject );
}

[
local,
object,
odl,
dual,
/*nonextensible,*/
oleautomation,
uuid(2933bf81-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMDocument : IXMLDOMNode
{
    [propget, id(DISPID_DOM_DOCUMENT_DOCTYPE)]
    HRESULT doctype( [out,retval] IXMLDOMDocumentType **documentType );

    [propget, id(DISPID_DOM_DOCUMENT_IMPLEMENTATION)]
    HRESULT implementation( [out,retval] IXMLDOMImplementation **impl );

    [propget, id(DISPID_DOM_DOCUMENT_DOCUMENTELEMENT)]
    HRESULT documentElement( [out,retval] IXMLDOMElement **DOMElement );

    [propputref, id(DISPID_DOM_DOCUMENT_DOCUMENTELEMENT)]
    HRESULT documentElement( [in] IXMLDOMElement *DOMElement );

    [id(DISPID_DOM_DOCUMENT_CREATEELEMENT)]
    HRESULT createElement( [in] BSTR tagname,
                           [out,retval] IXMLDOMElement **element );

    [id(DISPID_DOM_DOCUMENT_CREATEDOCUMENTFRAGMENT)]
    HRESULT createDocumentFragment( [out,retval] IXMLDOMDocumentFragment **docFrag );

    [id(DISPID_DOM_DOCUMENT_CREATETEXTNODE)]
    HRESULT createTextNode( [in] BSTR data,
                            [out,retval] IXMLDOMText **text );

    [id(DISPID_DOM_DOCUMENT_CREATECOMMENT)]
    HRESULT createComment( [in] BSTR data,
                           [out,retval] IXMLDOMComment **comment );

    [id(DISPID_DOM_DOCUMENT_CREATECDATASECTION)]
    HRESULT createCDATASection( [in] BSTR data,
                                [out,retval] IXMLDOMCDATASection **cdata );

    [id(DISPID_DOM_DOCUMENT_CREATEPROCESSINGINSTRUCTION)]
    HRESULT createProcessingInstruction( [in] BSTR target,
                                         [in] BSTR data,
                                         [out,retval] IXMLDOMProcessingInstruction **pi );

    [id(DISPID_DOM_DOCUMENT_CREATEATTRIBUTE)]
    HRESULT createAttribute( [in] BSTR name,
                             [out,retval] IXMLDOMAttribute **attribute );

    [id(DISPID_DOM_DOCUMENT_CREATEENTITYREFERENCE)]
    HRESULT createEntityReference( [in] BSTR name,
                                   [out,retval] IXMLDOMEntityReference **entityRef );

    [id(DISPID_DOM_DOCUMENT_GETELEMENTSBYTAGNAME)]
    HRESULT getElementsByTagName( [in] BSTR tagName,
                                  [out,retval] IXMLDOMNodeList **resultList );

    [id(DISPID_XMLDOM_DOCUMENT_CREATENODE)]
    HRESULT createNode( [in] VARIANT Type,
                        [in] BSTR name,
                        [in] BSTR namespaceURI,
                        [out,retval] IXMLDOMNode **node );

    [id(DISPID_XMLDOM_DOCUMENT_NODEFROMID)]
    HRESULT nodeFromID( [in] BSTR idString,
                        [out,retval] IXMLDOMNode **node );

    [id(DISPID_XMLDOM_DOCUMENT_LOAD)]
    HRESULT load( [in] VARIANT xmlSource,
                  [out,retval] VARIANT_BOOL *isSuccessful );

    [propget, id(DISPID_READYSTATE)]
    HRESULT readyState( [out,retval] LONG *value );

    [propget, id(DISPID_XMLDOM_DOCUMENT_PARSEERROR)]
    HRESULT parseError( [out,retval] IXMLDOMParseError **errorObj );

    [propget, id(DISPID_XMLDOM_DOCUMENT_URL)]
    HRESULT url( [out,retval] BSTR *urlString );

    /* FIXME: these is meant to be async, but widl parses that as a keyword */
    [propget, id(DISPID_XMLDOM_DOCUMENT_ASYNC)]
    HRESULT async( [out,retval] VARIANT_BOOL *isAsync );

    [propput, id(DISPID_XMLDOM_DOCUMENT_ASYNC)]
    HRESULT async( [in] VARIANT_BOOL isAsync );

    [id(DISPID_XMLDOM_DOCUMENT_ABORT)]
    HRESULT abort();

    [id(DISPID_XMLDOM_DOCUMENT_LOADXML)]
    HRESULT loadXML( [in] BSTR bstrXML,
                     [out,retval] VARIANT_BOOL *isSuccessful );

    [id(DISPID_XMLDOM_DOCUMENT_SAVE)]
    HRESULT save( [in] VARIANT destination );

    [propget, id(DISPID_XMLDOM_DOCUMENT_VALIDATE)]
    HRESULT validateOnParse( [out,retval] VARIANT_BOOL *isValidating );

    [propput, id(DISPID_XMLDOM_DOCUMENT_VALIDATE)]
    HRESULT validateOnParse( [in] VARIANT_BOOL isValidating );

    [propget, id(DISPID_XMLDOM_DOCUMENT_RESOLVENAMESPACE)]
    HRESULT resolveExternals( [out,retval] VARIANT_BOOL *isResolving );

    [propput, id(DISPID_XMLDOM_DOCUMENT_RESOLVENAMESPACE)]
    HRESULT resolveExternals( [in] VARIANT_BOOL isValidating );

    [propget, id(DISPID_XMLDOM_DOCUMENT_PRESERVEWHITESPACE)]
    HRESULT preserveWhiteSpace( [out,retval] VARIANT_BOOL *isPreserving );

    [propput, id(DISPID_XMLDOM_DOCUMENT_PRESERVEWHITESPACE)]
    HRESULT preserveWhiteSpace( [in] VARIANT_BOOL isPreserving );

    [propput, id(DISPID_XMLDOM_DOCUMENT_ONREADYSTATECHANGE)]
    HRESULT onreadystatechange( [in] VARIANT readystatechangeSink );

    [propput, id(DISPID_XMLDOM_DOCUMENT_ONDATAAVAILABLE)]
    HRESULT ondataavailable( [in] VARIANT ondataavailableSink );

    [propput, id(DISPID_XMLDOM_DOCUMENT_ONTRANSFORMNODE)]
    HRESULT ontransformnode( [in] VARIANT ontransformnodeSink );
}

[
local,
object,
odl,
dual,
/*nonextensible,*/
oleautomation,
uuid(2933bf82-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMNodeList : IDispatch
{
    [propget, id(DISPID_VALUE)]
    HRESULT item( [in] LONG index,
                  [out,retval] IXMLDOMNode **listItem );

    [propget, id(DISPID_DOM_NODELIST_LENGTH)]
    HRESULT length( [out,retval] LONG *listLength );

    [id(DISPID_XMLDOM_NODELIST_NEXTNODE)]
    HRESULT nextNode( [out,retval] IXMLDOMNode **nextItem );

    [id(DISPID_XMLDOM_NODELIST_RESET)]
    HRESULT reset();

    [id(DISPID_NEWENUM)]
    HRESULT _newEnum( [out,retval] IUnknown **ppUnk );
}

[
local,
object,
odl,
dual,
/*nonextensible,*/
oleautomation,
uuid(2933bf83-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMNamedNodeMap : IDispatch
{
    [id(DISPID_DOM_NAMEDNODEMAP_GETNAMEDITEM)]
    HRESULT getNamedItem( [in] BSTR name,
                          [out,retval] IXMLDOMNode **namedItem );

    [id(DISPID_DOM_NAMEDNODEMAP_SETNAMEDITEM)]
    HRESULT setNamedItem( [in] IXMLDOMNode *newItem,
                          [out,retval] IXMLDOMNode **namedItem );

    [id(DISPID_DOM_NAMEDNODEMAP_REMOVENAMEDITEM)]
    HRESULT removeNamedItem( [in] BSTR name,
                             [out,retval] IXMLDOMNode **namedItem );

    [propget, id(DISPID_VALUE)]
    HRESULT item( [in] LONG index,
                  [out,retval] IXMLDOMNode **listItem );

    [propget, id(DISPID_DOM_NODELIST_LENGTH)]
    HRESULT length( [out,retval] LONG *listLength );

    [id(DISPID_XMLDOM_NAMEDNODEMAP_GETQUALIFIEDITEM)]
    HRESULT getQualifiedItem( [in] BSTR baseName,
                              [in] BSTR namespaceURI,
                              [out,retval] IXMLDOMNode **qualifiedItem );

    [id(DISPID_XMLDOM_NAMEDNODEMAP_REMOVEQUALIFIEDITEM)]
    HRESULT removeQualifiedItem( [in] BSTR baseName,
                                 [in] BSTR namespaceURI,
                                 [out,retval] IXMLDOMNode **qualifiedItem );

    [id(DISPID_XMLDOM_NAMEDNODEMAP_NEXTNODE)]
    HRESULT nextNode( [out,retval] IXMLDOMNode **nextItem );

    [id(DISPID_XMLDOM_NAMEDNODEMAP_RESET)]
    HRESULT reset();

    [id(DISPID_NEWENUM)]
    HRESULT _newEnum( [out,retval] IUnknown **ppUnk );
}

[
local,
object,
odl,
dual,
/*nonextensible,*/
oleautomation,
uuid(3efaa413-272f-11d2-836f-0000f87a7782),
pointer_default(unique)
]
interface IXMLDOMDocumentFragment : IXMLDOMNode
{
    /* empty */
}

[
local,
object,
odl,
dual,
/*nonextensible,*/
oleautomation,
uuid(2933bf84-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMCharacterData : IXMLDOMNode
{
    [propget, id(DISPID_DOM_DATA_DATA)]
    HRESULT data( [out,retval] BSTR *data );

    [propput, id(DISPID_DOM_DATA_DATA)]
    HRESULT data( [in] BSTR data );

    [propget, id(DISPID_DOM_DATA_LENGTH)]
    HRESULT length( [out,retval] LONG *dataLength );

    [id(DISPID_DOM_DATA_SUBSTRING)]
    HRESULT substringData( [in] LONG offset,
                           [in] LONG count,
                           [out,retval] BSTR *data );

    [id(DISPID_DOM_DATA_APPEND)]
    HRESULT appendData( [in] BSTR data );

    [id(DISPID_DOM_DATA_INSERT)]
    HRESULT insertData( [in] LONG offset,
                        [in] BSTR data );

    [id(DISPID_DOM_DATA_DELETE)]
    HRESULT deleteData( [in] LONG offset,
                        [in] LONG count );

    [id(DISPID_DOM_DATA_REPLACE)]
    HRESULT replaceData( [in] LONG offset,
                         [in] LONG count,
                         [in] BSTR data );
}

[
local,
object,
odl,
dual,
/*nonextensible,*/
oleautomation,
uuid(2933bf85-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMAttribute : IXMLDOMNode
{
    [propget,id(DISPID_DOM_ATTRIBUTE_GETNAME)]
    HRESULT name( [out,retval] BSTR *attributeName );

    [propget,id(DISPID_DOM_ATTRIBUTE_VALUE)]
    HRESULT value( [out,retval] VARIANT *attributeValue );

    [propput,id(DISPID_DOM_ATTRIBUTE_VALUE)]
    HRESULT value( [in] VARIANT attributeValue );
}

[
local,
object,
odl,
dual,
/*nonextensible,*/
oleautomation,
uuid(2933bf86-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMElement : IXMLDOMNode
{
    [propget, id(DISPID_DOM_ELEMENT_GETTAGNAME)]
    HRESULT tagName( [out,retval] BSTR *tagName );

    [id(DISPID_DOM_ELEMENT_GETATTRIBUTE)]
    HRESULT getAttribute( [in] BSTR name,
                          [out,retval] VARIANT * value );

    [id(DISPID_DOM_ELEMENT_SETATTRIBUTE)]
    HRESULT setAttribute( [in] BSTR name,
                          [in] VARIANT value );

    [id(DISPID_DOM_ELEMENT_REMOVEATTRIBUTE)]
    HRESULT removeAttribute( [in] BSTR name );

    [id(DISPID_DOM_ELEMENT_GETATTRIBUTENODE)]
    HRESULT getAttributeNode( [in] BSTR name,
                              [out, retval] IXMLDOMAttribute ** attributeNode );

    [id(DISPID_DOM_ELEMENT_SETATTRIBUTENODE)]
    HRESULT setAttributeNode( [in] IXMLDOMAttribute *DOMAttribute,
                              [out, retval] IXMLDOMAttribute ** attributeNode );

    [id(DISPID_DOM_ELEMENT_REMOVEATTRIBUTENODE)]
    HRESULT removeAttributeNode( [in] IXMLDOMAttribute *DOMAttribute,
                              [out, retval] IXMLDOMAttribute ** attributeNode );

    [id(DISPID_DOM_ELEMENT_GETELEMENTSBYTAGNAME)]
    HRESULT getElementsByTagName( [in] BSTR tagName,
                              [out, retval] IXMLDOMNodeList ** resultList );

    [id(DISPID_DOM_ELEMENT_NORMALIZE)]
    HRESULT normalize();
}

[
local,
object,
odl,
dual,
/*nonextensible,*/
oleautomation,
uuid(2933bf87-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMText : IXMLDOMCharacterData
{
    [id(DISPID_DOM_TEXT_SPLITTEXT)]
    HRESULT splitText( [in] LONG offset,
                       [out,retval] IXMLDOMText **rightHandTextNode );
}

[
local,
object,
odl,
dual,
/*nonextensible,*/
oleautomation,
uuid(2933bf88-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMComment : IXMLDOMCharacterData
{
    /* empty */
}

[
local,
object,
odl,
dual,
/*nonextensible,*/
oleautomation,
uuid(2933bf89-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMProcessingInstruction : IXMLDOMNode
{
    [propget, id(DISPID_DOM_PI_TARGET)]
    HRESULT target( [out, retval] BSTR *name );

    [propget, id(DISPID_DOM_PI_DATA)]
    HRESULT data( [out, retval] BSTR *value );

    [propput, id(DISPID_DOM_PI_DATA)]
    HRESULT data( [in] BSTR value );
}

[
local,
object,
odl,
dual,
/*nonextensible,*/
oleautomation,
uuid(2933bf8a-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMCDATASection : IXMLDOMText
{
    /* empty */
}

[
local,
object,
odl,
dual,
/*nonextensible,*/
oleautomation,
uuid(2933bf8b-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMDocumentType : IXMLDOMNode
{
    [propget, id(DISPID_DOM_DOCUMENTTYPE_NAME)]
    HRESULT name( [out,retval] BSTR *rootName );

    [propget, id(DISPID_DOM_DOCUMENTTYPE_ENTITIES)]
    HRESULT entities( [out,retval] IXMLDOMNamedNodeMap **entityMap );

    [propget, id(DISPID_DOM_DOCUMENTTYPE_NOTATIONS)]
    HRESULT notations( [out,retval] IXMLDOMNamedNodeMap **notationMap );
}

[
local,
object,
odl,
dual,
nonextensible,
oleautomation,
uuid(2933bf8c-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMNotation : IXMLDOMNode
{
    [propget, id(DISPID_DOM_NOTATION_PUBLICID)]
    HRESULT publicId([out, retval] VARIANT *publicId);

    [propget, id(DISPID_DOM_NOTATION_SYSTEMID)]
    HRESULT systemId([out, retval] VARIANT *systemId);
}

[
local,
object,
odl,
dual,
nonextensible,
oleautomation,
uuid(2933bf8d-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMEntity : IXMLDOMNode
{
    [propget, id(DISPID_DOM_ENTITY_PUBLICID)]
    HRESULT publicId([out, retval] VARIANT *publicId);

    [propget, id(DISPID_DOM_ENTITY_SYSTEMID)]
    HRESULT systemId([out, retval] VARIANT *systemId);

    [propget, id(DISPID_DOM_ENTITY_NOTATIONNAME)]
    HRESULT notationName([out, retval] BSTR *name);
}

[
local,
object,
odl,
dual,
nonextensible,
oleautomation,
uuid(2933bf8e-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMEntityReference : IXMLDOMNode
{
}

[
local,
object,
odl,
dual,
nonextensible,
oleautomation,
uuid(2933bf8f-7b36-11d2-b20e-00c04f983e60),
pointer_default(unique)
]
interface IXMLDOMImplementation : IDispatch
{
    [id(DISPID_DOM_IMPLEMENTATION_HASFEATURE)]
    HRESULT hasFeature([in] BSTR feature, [in] BSTR version, [out, retval] VARIANT_BOOL *pbool);
}

[
local,
object,
odl,
dual,
oleautomation,
/*nonextensible,*/
helpstring("structure for reporting parse errors"),
pointer_default(unique),
uuid (3efaa426-272f-11d2-836f-0000f87a7782)
]
interface IXMLDOMParseError : IDispatch
{
    [propget, id(DISPID_VALUE)]
    HRESULT errorCode([retval, out] LONG *errCode);

    [propget, id(DISPID_DOM_ERROR_URL)]
    HRESULT url([retval, out] BSTR *p);

    [propget, id(DISPID_DOM_ERROR_REASON)]
    HRESULT reason([retval, out] BSTR *p);

    [propget, id(DISPID_DOM_ERROR_SRCTEXT)]
    HRESULT srcText([retval, out] BSTR *p);

    [propget, id(DISPID_DOM_ERROR_LINE)]
    HRESULT line([retval, out] LONG *lineNo);

    [propget, id(DISPID_DOM_ERROR_LINEPOS)]
    HRESULT linepos([retval, out] LONG * linePos);

    [propget, id(DISPID_DOM_ERROR_FILEPOS)]
    HRESULT filepos([retval, out] LONG * filePos);
}

[
    hidden,
    uuid(3efaa427-272f-11d2-836f-0000f87a7782)
]
dispinterface XMLDOMDocumentEvents
{
    properties:
    methods:
    [id(DISPID_XMLDOMEVENT_ONDATAAVAILABLE)]
    HRESULT ondataavailable();

    [id(DISPID_XMLDOMEVENT_ONREADYSTATECHANGE)]
    HRESULT onreadystatechange();
}

[
    helpstring("XML DOM Document"),
    progid("Microsoft.XMLDOM.1.0"),
    vi_progid("Microsoft.XMLDOM"),
    threading(both),
    version(1.0),
    uuid(2933bf90-7b36-11d2-b20e-00c04f983e60)
]
coclass DOMDocument
{
    [default] interface IXMLDOMDocument;
    [default, source] dispinterface XMLDOMDocumentEvents;
}

[
    helpstring("Free Threaded XML DOM Document"),
    progid("Microsoft.FreeThreadedXMLDOM.1.0"),
    vi_progid("Microsoft.FreeThreadedXMLDOM"),
    threading(both),
    version(1.0),
    uuid(2933bf91-7b36-11d2-b20e-00c04f983e60)
]
coclass DOMFreeThreadedDocument
{
    [default] interface IXMLDOMDocument;
    [default, source] dispinterface XMLDOMDocumentEvents;
}

[
object,
uuid(ed8c108d-4349-11d2-91a4-00c04f7969e8),
odl,
dual,
oleautomation,
pointer_default(unique)
]
interface IXMLHttpRequest : IDispatch
{
    [id(1)]
    HRESULT open([in] BSTR bstrMethod, [in] BSTR bstrUrl,
                 [in, optional] VARIANT varAsync,
                 [in, optional] VARIANT varUser,
                 [in, optional] VARIANT varPassword);
    [id(2)]
    HRESULT setRequestHeader([in] BSTR bstrHeader, [in] BSTR bstrValue);

    [id(3)]
    HRESULT getResponseHeader([in] BSTR bstrHeader, [out, retval] BSTR *pbstrValue);

    [id(4)]
    HRESULT getAllResponseHeaders([out, retval] BSTR *pbstrHeaders);

    [id(5)]
    HRESULT send([in, optional] VARIANT varBody);

    [id(6)]
    HRESULT abort();

    [propget, id(7)]
    HRESULT status([out, retval] LONG *plStatus);

    [propget, id(8)]
    HRESULT statusText([out, retval] BSTR *bstrStatus);

    [propget, id(9)]
    HRESULT responseXML([out, retval] IDispatch **ppBody);

    [propget, id(10)]
    HRESULT responseText([out, retval] BSTR *pbstrBody);

    [propget, id(11)]
    HRESULT responseBody([out, retval] VARIANT *pvarBody);

    [propget, id(12)]
    HRESULT responseStream([out, retval] VARIANT *pvarBody);

    [propget, id(13)]
    HRESULT readyState([out, retval] LONG *plState);

    [propput, id(14)]
    HRESULT onreadystatechange([in] IDispatch *pReadyStateSink);
}

[
    helpstring("XML HTTP Request"),
    progid("Microsoft.XMLHTTP.1.0"),
    vi_progid("Microsoft.XMLHTTP"),
    threading(apartment),
    version(1.0),
    uuid(ed8c108e-4349-11d2-91a4-00c04f7969e8)
]
coclass XMLHTTPRequest
{
    [default] interface IXMLHttpRequest;
}
