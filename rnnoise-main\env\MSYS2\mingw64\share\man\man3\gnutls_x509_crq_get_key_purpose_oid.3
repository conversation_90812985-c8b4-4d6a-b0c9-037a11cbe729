.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_key_purpose_oid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_key_purpose_oid \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_key_purpose_oid(gnutls_x509_crq_t " crq ", unsigned " indx ", void * " oid ", size_t * " sizeof_oid ", unsigned int * " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "unsigned indx" 12
This specifies which OID to return, use (0) to get the first one
.IP "void * oid" 12
a pointer to store the OID (may be \fBNULL\fP)
.IP "size_t * sizeof_oid" 12
initially holds the size of  \fIoid\fP 
.IP "unsigned int * critical" 12
output variable with critical flag, may be \fBNULL\fP.
.SH "DESCRIPTION"
This function will extract the key purpose OIDs of the Certificate
specified by the given index.  These are stored in the Extended Key
Usage extension (*********).  See the GNUTLS_KP_* definitions for
human readable names.
.SH "RETURNS"
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP if the provided buffer is
not long enough, and in that case the * \fIsizeof_oid\fP will be
updated with the required size.  On success 0 is returned.
.SH "SINCE"
2.8.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
