cpp_quote("/**")
cpp_quote(" * This file is part of the mingw-w64 runtime package.")
cpp_quote(" * No warranty is given; refer to the file DISCLAIMER within this package.")
cpp_quote(" */")
cpp_quote("")

#ifndef DO_NO_IMPORTS
import "unknwn.idl";
#endif

cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
interface IEnumGUID;
cpp_quote("#endif")

cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
interface IEnumCATEGORYINFO;
interface ICatRegister;
interface ICatInformation;
cpp_quote("")
cpp_quote("EXTERN_C const CLSID CLSID_StdComponentCategoriesMgr;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
typedef GUID CATID;
typedef REFGUID REFCATID;
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("#define IID_IEnumCLSID IID_IEnumGUID")
cpp_quote("#define IEnumCLSID IEnumGUID")
cpp_quote("#define LPENUMCLSID LPENUMGUID")
cpp_quote("")

#define IEnumCLSID IEnumGUID

cpp_quote("#define CATID_NULL GUID_NULL")
cpp_quote("#define IsEqualCATID(rcatid1, rcatid2) IsEqualGUID(rcatid1, rcatid2)")
cpp_quote("#define IID_IEnumCATID IID_IEnumGUID")
cpp_quote("#define IEnumCATID IEnumGUID")
cpp_quote("")

#define IEnumCATID IEnumGUID

cpp_quote("")
cpp_quote("EXTERN_C const CATID CATID_Insertable;")
cpp_quote("EXTERN_C const CATID CATID_Control;")
cpp_quote("EXTERN_C const CATID CATID_Programmable;")
cpp_quote("EXTERN_C const CATID CATID_IsShortcut;")
cpp_quote("EXTERN_C const CATID CATID_NeverShowExt;")
cpp_quote("EXTERN_C const CATID CATID_DocObject;")
cpp_quote("EXTERN_C const CATID CATID_Printable;")
cpp_quote("EXTERN_C const CATID CATID_RequiresDataPathHost;")
cpp_quote("EXTERN_C const CATID CATID_PersistsToMoniker;")
cpp_quote("EXTERN_C const CATID CATID_PersistsToStorage;")
cpp_quote("EXTERN_C const CATID CATID_PersistsToStreamInit;")
cpp_quote("EXTERN_C const CATID CATID_PersistsToStream;")
cpp_quote("EXTERN_C const CATID CATID_PersistsToMemory;")
cpp_quote("EXTERN_C const CATID CATID_PersistsToFile;")
cpp_quote("EXTERN_C const CATID CATID_PersistsToPropertyBag;")
cpp_quote("EXTERN_C const CATID CATID_InternetAware;")
cpp_quote("EXTERN_C const CATID CATID_DesignTimeUIActivatableControl;")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
cpp_quote("#ifndef _LPENUMGUID_DEFINED")
cpp_quote("#define _LPENUMGUID_DEFINED")
[object, uuid (0002e000-0000-0000-C000-000000000046), pointer_default (unique)]
interface IEnumGUID : IUnknown {
  typedef [unique] IEnumGUID *LPENUMGUID;
  [local] HRESULT Next ([in] ULONG celt,[out, size_is (celt), length_is (*pceltFetched)] GUID *rgelt,[out] ULONG *pceltFetched);
  [call_as (Next)] HRESULT RemoteNext ([in] ULONG celt,[out, size_is (celt), length_is (*pceltFetched)] GUID *rgelt,[out] ULONG *pceltFetched);
  HRESULT Skip ([in] ULONG celt);
  HRESULT Reset ();
  HRESULT Clone ([out] IEnumGUID **ppenum);
}
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("#ifndef _LPENUMCATEGORYINFO_DEFINED")
cpp_quote("#define _LPENUMCATEGORYINFO_DEFINED")
[object, uuid (0002e011-0000-0000-C000-000000000046), pointer_default (unique)]
interface IEnumCATEGORYINFO : IUnknown {
  typedef [unique] IEnumCATEGORYINFO *LPENUMCATEGORYINFO;

#define CATDESC_MAX 128

cpp_quote("")
  typedef struct tagCATEGORYINFO {
    CATID catid;
    LCID lcid;
    OLECHAR szDescription[CATDESC_MAX];
  } CATEGORYINFO,*LPCATEGORYINFO;
cpp_quote("")
  HRESULT Next ([in] ULONG celt,[out, size_is (celt), length_is (*pceltFetched)] CATEGORYINFO *rgelt,[out] ULONG *pceltFetched);
  HRESULT Skip ([in] ULONG celt);
  HRESULT Reset ();
  HRESULT Clone ([out] IEnumCATEGORYINFO **ppenum);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef _LPCATREGISTER_DEFINED")
cpp_quote("#define _LPCATREGISTER_DEFINED")
[object, uuid (0002e012-0000-0000-C000-000000000046), pointer_default (unique)]
interface ICatRegister : IUnknown {
  typedef [unique] ICatRegister *LPCATREGISTER;
cpp_quote("")
  HRESULT RegisterCategories ([in] ULONG cCategories,[in, size_is (cCategories)] CATEGORYINFO rgCategoryInfo[]);
  HRESULT UnRegisterCategories ([in] ULONG cCategories,[in, size_is (cCategories)] CATID rgcatid[]);
  HRESULT RegisterClassImplCategories ([in] REFCLSID rclsid,[in] ULONG cCategories,[in, size_is (cCategories)] CATID rgcatid[]);
  HRESULT UnRegisterClassImplCategories ([in] REFCLSID rclsid,[in] ULONG cCategories,[in, size_is (cCategories)] CATID rgcatid[]);
  HRESULT RegisterClassReqCategories ([in] REFCLSID rclsid,[in] ULONG cCategories,[in, size_is (cCategories)] CATID rgcatid[]);
  HRESULT UnRegisterClassReqCategories ([in] REFCLSID rclsid,[in] ULONG cCategories,[in, size_is (cCategories)] CATID rgcatid[]);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#ifndef _LPCATINFORMATION_DEFINED")
cpp_quote("#define _LPCATINFORMATION_DEFINED")
[object, uuid (0002e013-0000-0000-C000-000000000046), pointer_default (unique)]
interface ICatInformation : IUnknown {
  typedef [unique] ICatInformation *LPCATINFORMATION;
cpp_quote("")
  HRESULT EnumCategories ([in] LCID lcid,[out] IEnumCATEGORYINFO **ppenumCategoryInfo);
  HRESULT GetCategoryDesc ([in] REFCATID rcatid,[in] LCID lcid,[out] LPWSTR *pszDesc);
  [local] HRESULT EnumClassesOfCategories ([in] ULONG cImplemented,[in, size_is (cImplemented)] const CATID rgcatidImpl[],[in] ULONG cRequired,[in, size_is (cRequired)] const CATID rgcatidReq[],[out] IEnumCLSID **ppenumClsid);
  [call_as (EnumClassesOfCategories)] HRESULT RemoteEnumClassesOfCategories ([in] ULONG cImplemented,[in, unique, size_is (cImplemented)] const CATID rgcatidImpl[],[in] ULONG cRequired,[in, unique, size_is (cRequired)] const CATID rgcatidReq[],[out] IEnumCLSID **ppenumClsid);
  [local] HRESULT IsClassOfCategories ([in] REFCLSID rclsid,[in] ULONG cImplemented,[in, size_is (cImplemented)] const CATID rgcatidImpl[],[in] ULONG cRequired,[in, size_is (cRequired)] const CATID rgcatidReq[]);
  [call_as (IsClassOfCategories)] HRESULT RemoteIsClassOfCategories ([in] REFCLSID rclsid,[in] ULONG cImplemented,[in, unique, size_is (cImplemented)] const CATID rgcatidImpl[],[in] ULONG cRequired,[in, unique, size_is (cRequired)] const CATID rgcatidReq[]);
  HRESULT EnumImplCategoriesOfClass ([in] REFCLSID rclsid,[out] IEnumCATID **ppenumCatid);
  HRESULT EnumReqCategoriesOfClass ([in] REFCLSID rclsid,[out] IEnumCATID **ppenumCatid);
}
cpp_quote("#endif")
cpp_quote("#endif")
