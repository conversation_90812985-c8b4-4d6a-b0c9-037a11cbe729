.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_sign_algorithm_get_requested" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_sign_algorithm_get_requested \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_sign_algorithm_get_requested(gnutls_session_t " session ", size_t " indx ", gnutls_sign_algorithm_t * " algo ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "size_t indx" 12
is an index of the signature algorithm to return
.IP "gnutls_sign_algorithm_t * algo" 12
the returned certificate type will be stored there
.SH "DESCRIPTION"
Returns the signature algorithm specified by index that was
requested by the peer. If the specified index has no data available
this function returns \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP.  If
the negotiated TLS version does not support signature algorithms
then \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP will be returned even
for the first index.  The first index is 0.

This function is useful in the certificate callback functions
to assist in selecting the correct certificate.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "SINCE"
2.10.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
