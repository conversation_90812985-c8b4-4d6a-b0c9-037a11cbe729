.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_export_dh_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_export_dh_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_export_dh_raw(gnutls_privkey_t " key ", gnutls_dh_params_t " params ", gnutls_datum_t * " y ", gnutls_datum_t * " x ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t key" 12
Holds the private key
.IP "gnutls_dh_params_t params" 12
will hold the Diffie\-Hellman parameters (optional), must be initialized
.IP "gnutls_datum_t * y" 12
will hold the y (optional)
.IP "gnutls_datum_t * x" 12
will hold the x
.IP "unsigned int flags" 12
flags from \fBgnutls_abstract_export_flags_t\fP
.SH "DESCRIPTION"
This function will export the Diffie\-Hellman private key parameter
found in the given \fBgnutls_privkey_t\fP structure. The new parameter
will be allocated using \fBgnutls_malloc()\fP and will be stored in the
appropriate datum.

To retrieve other parameters common in both public key and private
key, use \fBgnutls_dh_params_export_raw()\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.8.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
