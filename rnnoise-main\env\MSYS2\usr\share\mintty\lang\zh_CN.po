# Simplified Chinese translations for mintty package
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the mintty package.
# <PERSON> <<EMAIL>>, 2017.
# GeekDuan<PERSON>ian <<EMAIL>>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: mintty\n"
"Report-Msgid-Bugs-To: https://github.com/mintty/mintty/issues/700\n"
"POT-Creation-Date: 2025-03-22 07:33+0100\n"
"PO-Revision-Date: 2024-06-28 23:51+0200\n"
"Last-Translator: Geek<PERSON>uan<PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.0.3\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: charset.c:228 charset.c:239 winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "(Default)"
msgstr "(默认)"

#: charset.c:250
msgid "(OEM codepage)"
msgstr "(OEM 代码页)"

#: charset.c:254
msgid "(ANSI codepage)"
msgstr "(ANSI 代码页)"

#: child.c:96
msgid "There are no available terminals"
msgstr "没有可用的终端"

#: child.c:171
msgid "Error: Could not open log file"
msgstr "错误：无法打开日志文件"

#: child.c:334
msgid "Error: Could not fork child process"
msgstr "错误：无法创建子进程"

#: child.c:336
msgid "DLL rebasing may be required; see 'rebaseall / rebase --help'"
msgstr ""
"可能需要 DLL 地址重定位，运行此命令查看帮助 'rebaseall / rebase --help'"

#. __ %1$s: client command (e.g. shell) to be run; %2$s: error message
#: child.c:426
msgid "Failed to run '%s': %s"
msgstr "运行 '%s' 失败：%s"

#. __ %1$s: client command (e.g. shell) terminated, %2$i: exit code
#: child.c:578
msgid "%s: Exit %i"
msgstr "%s：退出 %i"

#. __ default inline notification if ExitWrite=yes
#: child.c:585
msgid "TERMINATED"
msgstr "已终止"

#: child.c:1232
msgid "Error: Could not fork child daemon"
msgstr "错误：无法创建子进程"

#. __ Setting false for Boolean options (localization optional)
#: config.c:655
msgid "no"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:657
msgid "yes"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:659
msgid "false"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:661
msgid "true"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:663
msgid "off"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:665
msgid "on"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:694
msgid "stretch"
msgstr "拉伸"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:696
msgid "align"
msgstr "适应"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:698
msgid "middle"
msgstr "居中"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:700
msgid "full"
msgstr "填充"

#. __ %s: unknown option name
#: config.c:866
msgid "Ignoring unknown option '%s'"
msgstr "忽略未知选项 '%s'"

#: config.c:914 config.c:943
msgid "Internal error: too many options"
msgstr "内部错误：过多选项"

#: config.c:930
msgid "Internal error: too many options/comments"
msgstr "内部错误：过多选项/注释"

#. __ %2$s: option name, %1$s: invalid value
#. report errors only during initialisation
#: config.c:1100
msgid "Ignoring invalid value '%s' for option '%s'"
msgstr "忽略 '%s' 无效值，属于该选项 '%s'"

#. __ %s: option name
#: config.c:1112
msgid "Ignoring option '%s' with missing value"
msgstr "忽略 '%s' 选项，因为缺少值"

#. __ %1$s: config file name, %2$s: error message
#: config.c:1791
msgid ""
"Could not save options to '%s':\n"
"%s."
msgstr ""
"无法将选项保存到 '%s'：\n"
"%s。"

#: config.c:2172
msgid "◇ None (printing disabled) ◇"
msgstr "◇ 无 (禁用打印) ◇"

#: config.c:2174
msgid "◆ Default printer ◆"
msgstr "◆ 默认打印机 ◆"

#. __ UI localization disabled
#: config.c:2283
msgid "– None –"
msgstr "– 无 –"

#. __ UI localization: use Windows desktop setting
#: config.c:2285
msgid "@ Windows language @"
msgstr "@ Windows 语言 @"

#. __ UI localization: use environment variable setting (LANGUAGE, LC_*)
#: config.c:2287
msgid "* Locale environm. *"
msgstr "* Locale 环境变量 *"

#. __ UI localization: use mintty configuration setting (Text - Locale)
#: config.c:2289
msgid "= cfg. Text Locale ="
msgstr "= 已配置的 Locale ="

#: config.c:2394
msgid "simple beep"
msgstr "简单的嘟声"

#: config.c:2395
msgid "no beep"
msgstr "无提示音"

#: config.c:2396
msgid "Default Beep"
msgstr "默认的嘟声"

#: config.c:2397
msgid "Critical Stop"
msgstr "错误提示音"

#: config.c:2398
msgid "Question"
msgstr "疑问提示音"

#: config.c:2399
msgid "Exclamation"
msgstr "惊叹提示音"

#: config.c:2400
msgid "Asterisk"
msgstr "星号提示音"

#: config.c:2443
msgid "◇ None (system sound) ◇"
msgstr "◇ 无 (系统声音) ◇"

#. __ terminal theme / colour scheme
#. __ emojis style
#: config.c:2874 config.c:3425
msgid "◇ None ◇"
msgstr "◇ 无 ◇"

#. __ indicator of unsaved downloaded colour scheme
#: config.c:2877
msgid "downloaded / give me a name!"
msgstr "已下载 / 给我起名！"

#: config.c:2983
msgid "Could not load web theme"
msgstr "无法加载网络主题"

#: config.c:3040
msgid "Cannot write theme file"
msgstr "无法写入主题文件"

#: config.c:3045
msgid "Cannot store theme file"
msgstr "无法储存主题文件"

#. __ Options - Text:
#: config.c:3502 config.c:3840 config.c:3939
msgid "as font"
msgstr "字形"

#. __ Options - Text:
#: config.c:3503 config.c:3845 config.c:3944
msgid "as colour"
msgstr "颜色"

#: config.c:3504
msgid "as font & as colour"
msgstr "字形 & 颜色"

#. __ Options - Text:
#: config.c:3505 config.c:3850 config.c:3949
msgid "xterm"
msgstr ""

#. __ Dialog button - show About text
#: config.c:3653
msgid "About..."
msgstr "关于..."

#. __ Dialog button - save changes
#: config.c:3656
msgid "Save"
msgstr "保存"

#. __ Dialog button - cancel
#: config.c:3660 winctrls.c:1277 windialog.c:895
msgid "Cancel"
msgstr "取消"

#. __ Dialog button - apply changes
#: config.c:3664
msgid "Apply"
msgstr "应用"

#. __ Dialog button - take notice
#: config.c:3668 windialog.c:892
msgid "I see"
msgstr "我了解了"

#. __ Dialog button - confirm action
#: config.c:3670 winctrls.c:1276 windialog.c:894
msgid "OK"
msgstr "确定"

#. __ Options - Looks: treeview label
#: config.c:3677 config.c:3708 config.c:3767
msgid "Looks"
msgstr "外观"

#. __ Options - Looks: panel title
#: config.c:3679
msgid "Looks in Terminal"
msgstr "终端外观"

#. __ Options - Looks: section title
#: config.c:3681
msgid "Colours"
msgstr "颜色"

#. __ Options - Looks:
#: config.c:3685
msgid "&Foreground..."
msgstr "前景(&F)..."

#. __ Options - Looks:
#: config.c:3689
msgid "&Background..."
msgstr "背景(&B)..."

#. __ Options - Looks:
#: config.c:3693
msgid "&Cursor..."
msgstr "光标(&C)..."

#. __ Options - Looks:
#: config.c:3697
msgid "&Theme"
msgstr "主题(&T)"

#. __ Options - Looks: name of web service
#: config.c:3702
msgid "Color Scheme Designer"
msgstr "颜色样式设计工具"

#. __ Options - Looks: store colour scheme
#: config.c:3705 winctrls.c:484
msgid "Store"
msgstr "保存"

#. __ Options - Looks: section title
#: config.c:3710
msgid "Transparency"
msgstr "透明度"

#. __ Options - Looks: transparency
#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Window:
#: config.c:3716 config.c:4098 config.c:4261 config.c:4459
msgid "&Off"
msgstr "关(&O)"

#. __ Options - Looks: transparency
#: config.c:3718
msgid "&Low"
msgstr "低(&L)"

#. __ Options - Looks: transparency, short form of radio button label "Medium"
#: config.c:3720
msgid "&Med."
msgstr "中(&M)"

#. __ Options - Looks: transparency
#: config.c:3722
msgid "&Medium"
msgstr "中(&M)"

#. __ Options - Looks: transparency
#: config.c:3724
msgid "&High"
msgstr "高(&H)"

#. __ Options - Looks: transparency
#: config.c:3726
msgid "Gla&ss"
msgstr "玻璃"

#. __ Options - Looks: transparency
#: config.c:3733 config.c:3745 config.c:3752
msgid "Opa&que when focused"
msgstr "获得焦点后变为不透明(&Q)"

#. __ Options - Looks: transparency
#: config.c:3738
msgid "Blu&r"
msgstr "模糊(&R)"

#: config.c:3759
msgid "◄"
msgstr ""

#: config.c:3762
msgid "►"
msgstr ""

#. __ Options - Looks: section title
#: config.c:3769
msgid "Cursor"
msgstr "光标"

#. __ Options - Looks: cursor type
#: config.c:3774
msgid "Li&ne"
msgstr "竖线"

#. __ Options - Looks: cursor type
#: config.c:3776
msgid "Bloc&k"
msgstr "方块"

#. __ Options - Looks: cursor type
#: config.c:3779
msgid "Bo&x"
msgstr ""

#. __ Options - Looks: cursor type
#: config.c:3782
msgid "&Underscore"
msgstr "下划线"

#. __ Options - Looks: cursor feature
#: config.c:3787
msgid "Blinkin&g"
msgstr "闪烁(&G)"

#. __ Options - Text: treeview label
#: config.c:3794 config.c:3819 config.c:3834 config.c:3883 config.c:3933
#: config.c:3958 config.c:3980 config.c:3993 config.c:4001
msgid "Text"
msgstr "文本"

#. __ Options - Text: panel title
#: config.c:3796
msgid "Text and Font properties"
msgstr "文本和字体属性"

#. __ Options - Text: section title
#: config.c:3798
msgid "Font"
msgstr "字体"

#. __ Options - Text:
#. __ Font chooser:
#: config.c:3806 winctrls.c:1287
msgid "Font st&yle:"
msgstr "字体样式(&Y)"

#. __ Font chooser:
#: config.c:3811 winctrls.c:1289
msgid "&Size:"
msgstr "大小(&S)"

#. __ Options - Text:
#: config.c:3823 config.c:3902
msgid "Sho&w bold as font"
msgstr "显示粗体字形(&W)"

#. __ Options - Text:
#: config.c:3828 config.c:3907
msgid "Show &bold as colour"
msgstr "显示粗体颜色(&B)"

#. __ Options - Text:
#: config.c:3836 config.c:3857 config.c:3935 config.c:3962
msgid "Show bold"
msgstr "显示粗体的"

#. __ Options - Text:
#: config.c:3864 config.c:3912 config.c:3968
msgid "&Allow blinking"
msgstr "允许闪烁(&A)"

#. __ Options - Text:
#: config.c:3869 config.c:3973
msgid "Show dim as font"
msgstr "显示半隐字形(&W)"

#. __ Options - Text:
#: config.c:3887 config.c:3920 config.c:3955
msgid "Font smoothing"
msgstr "字体平滑"

#. __ Options - Text:
#: config.c:3890 config.c:3923 config.c:4149 config.c:4188 config.c:4342
#: config.c:4355
msgid "&Default"
msgstr "默认(&D)"

#. __ Options - Text:
#. __ Options - Window: scrollbar
#: config.c:3892 config.c:3925 config.c:4147 config.c:4186 config.c:4340
#: config.c:4353 config.c:4440
msgid "&None"
msgstr "无(&N)"

#. __ Options - Text:
#: config.c:3894 config.c:3927 config.c:4148 config.c:4187 config.c:4341
#: config.c:4354
msgid "&Partial"
msgstr "部分(&P)"

#. __ Options - Text:
#: config.c:3896 config.c:3929 config.c:4150 config.c:4189 config.c:4343
#: config.c:4356
msgid "&Full"
msgstr "全部(&F)"

#: config.c:3983
msgid "&Locale"
msgstr "本地 &Locale"

#: config.c:3986
msgid "&Character set"
msgstr "字符集(&C)"

#. __ Options - Text - Emojis:
#. __ Options - Text:
#: config.c:3997 config.c:4003
msgid "Emojis"
msgstr "Emoji"

#. __ Options - Text - Emojis:
#: config.c:4007
msgid "Style"
msgstr "风格"

#. __ Options - Text - Emojis:
#: config.c:4012
msgid "Placement"
msgstr "位置"

#. __ Options - Keys: treeview label
#: config.c:4020 config.c:4050 config.c:4085 config.c:4103
msgid "Keys"
msgstr "按键"

#. __ Options - Keys: panel title
#: config.c:4022
msgid "Keyboard features"
msgstr "键盘特性"

#. __ Options - Keys:
#: config.c:4026
msgid "&Backarrow sends ^H"
msgstr "退格键发送 ^H (&B)"

#. __ Options - Keys:
#: config.c:4031
msgid "&Delete sends DEL"
msgstr "删除键发送 DEL (&D)"

#. __ Options - Keys:
#: config.c:4036
msgid "Ctrl+LeftAlt is Alt&Gr"
msgstr "将 Ctrl+左Alt 设置为 AltGr (&G)"

#. __ Options - Keys:
#: config.c:4041
msgid "AltGr is also Alt"
msgstr "将 AltGr 也视为 Alt"

#. __ Options - Keys:
#: config.c:4046
msgid "&Esc/Enter reset IME to alphanumeric"
msgstr "Esc/Enter 重置输入法为英文(&E)"

#. __ Options - Keys: section title
#: config.c:4052
msgid "Shortcuts"
msgstr "快捷键"

#. __ Options - Keys:
#: config.c:4055
msgid "Cop&y and Paste (Ctrl/Shift+Ins)"
msgstr "复制和粘贴(&Y) (Ctrl/Shift+Ins)"

#. __ Options - Keys:
#: config.c:4060
msgid "&Menu and Full Screen (Alt+Space/Enter)"
msgstr "显示菜单和全屏(&M) (Alt+Space/Enter)"

#. __ Options - Keys:
#: config.c:4065
msgid "&Switch window (Ctrl+[Shift+]Tab)"
msgstr "切换窗口(&S) (Ctrl+[Shift+]Tab)"

#. __ Options - Keys:
#: config.c:4070
msgid "&Zoom (Ctrl+plus/minus/zero)"
msgstr "缩放(&Z) (Ctrl + +/-/0)"

#. __ Options - Keys:
#: config.c:4075
msgid "&Alt+Fn shortcuts"
msgstr "Alt+Fn 快捷键(&A)"

#. __ Options - Keys:
#: config.c:4080
msgid "&Ctrl+Shift+letter shortcuts"
msgstr "Ctrl+Shift+字母快捷键(&C)"

#. __ Options - Keys: section title
#: config.c:4087 config.c:4105
msgid "Compose key"
msgstr "组合按键"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Shift:
#. __ Options - Window:
#. __ Options - Modifier - Shift:
#: config.c:4092 config.c:4253 config.c:4272 config.c:4451 config.c:4470
msgid "&Shift"
msgstr ""

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Control:
#. __ Options - Window:
#. __ Options - Modifier - Control:
#: config.c:4094 config.c:4255 config.c:4280 config.c:4453 config.c:4478
msgid "&Ctrl"
msgstr ""

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Alt:
#. __ Options - Window:
#. __ Options - Modifier - Alt:
#: config.c:4096 config.c:4257 config.c:4276 config.c:4455 config.c:4474
msgid "&Alt"
msgstr ""

#. __ Options - Mouse: treeview label
#: config.c:4112 config.c:4201 config.c:4233
msgid "Mouse"
msgstr "鼠标"

#. __ Options - Mouse: panel title
#: config.c:4114
msgid "Mouse functions"
msgstr "鼠标功能"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4122 config.c:4155 config.c:4171 config.c:4319
msgid "Cop&y on select"
msgstr "选中后立即复制(&Y)"

#. __ Options - Mouse:
#. __ Options - Selection:
#. __ Context menu:
#: config.c:4127 config.c:4160 config.c:4324 wininput.c:685
msgid "Copy with TABs"
msgstr "以纯文本复制（包括 &Tab）"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4132 config.c:4165 config.c:4177 config.c:4329
msgid "Copy as &rich text"
msgstr "以富文本复制(&R)"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4138 config.c:4145 config.c:4184 config.c:4338 config.c:4351
msgid "Copy as &HTML"
msgstr "以 HTML 复制"

#. __ Options - Mouse:
#: config.c:4197
msgid "Clic&ks place command line cursor"
msgstr "使用鼠标点击来定位光标(&K)"

#. __ Options - Mouse: section title
#: config.c:4203
msgid "Click actions"
msgstr "单击动作"

#. __ Options - Mouse:
#: config.c:4206
msgid "Right mouse button"
msgstr "鼠标右键"

#. __ Options - Mouse:
#: config.c:4209 config.c:4223
msgid "&Paste"
msgstr "粘贴(&P)"

#. __ Options - Mouse:
#: config.c:4211 config.c:4225
msgid "E&xtend"
msgstr "扩展(&E)"

#. __ Options - Mouse:
#: config.c:4213
msgid "&Menu"
msgstr "菜单(&M)"

#. __ Options - Mouse:
#: config.c:4215 config.c:4229
msgid "Ente&r"
msgstr "回车(&E)"

#. __ Options - Mouse:
#: config.c:4220
msgid "Middle mouse button"
msgstr "鼠标中键"

#. __ Options - Mouse:
#: config.c:4227
msgid "&Nothing"
msgstr "无(&N)"

#. __ Options - Mouse: section title
#: config.c:4235
msgid "Application mouse mode"
msgstr "应用程序鼠标模式"

#. __ Options - Mouse:
#: config.c:4238
msgid "Default click target"
msgstr "默认点击目标"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4241
msgid "&Window"
msgstr "窗口(&W)"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4243
msgid "&Application"
msgstr "应用程序(&A)"

#. __ Options - Mouse:
#: config.c:4250 config.c:4268
msgid "Modifier for overriding default"
msgstr "覆盖默认配置的修饰键"

#. __ Options - Window:
#. __ Options - Modifier - Win:
#. __ Options - Window:
#. __ Options - Modifier - Win:
#: config.c:4259 config.c:4284 config.c:4457 config.c:4482
msgid "&Win"
msgstr ""

#. __ Options - Modifier - Super:
#: config.c:4288 config.c:4486
msgid "&Sup"
msgstr ""

#. __ Options - Modifier - Hyper:
#: config.c:4292 config.c:4490
msgid "&Hyp"
msgstr ""

#. __ Options - Selection: treeview label
#: config.c:4302 config.c:4313 config.c:4377
msgid "Selection"
msgstr "选中"

#. __ Options - Selection: panel title
#: config.c:4304
msgid "Selection and clipboard"
msgstr "选中字符和剪切板功能"

#. __ Options - Selection:
#: config.c:4308
msgid "Clear selection on input"
msgstr "输入后取消选中"

#. __ Options - Selection: section title
#: config.c:4315
msgid "Clipboard"
msgstr "剪切板"

#. __ Options - Selection:
#: config.c:4365
msgid "Trim space from selection"
msgstr "移除不必要的空格"

#. __ Options - Selection:
#: config.c:4371
msgid "Allow setting selection"
msgstr "允许使用 OSC 52 转义字符设置剪切板内容"

#. __ Options - Selection: section title
#. __ Options - Window: treeview label
#: config.c:4379 config.c:4401 config.c:4426 config.c:4499
msgid "Window"
msgstr "窗口"

#. __ Options - Selection: clock position of info popup for text size
#: config.c:4384
msgid "Show size while selecting (0..12)"
msgstr "选择时显示尺寸 (0:关闭/1-12:方位)"

#. __ Options - Selection:
#: config.c:4391
msgid "Suspend output while selecting"
msgstr "选择时暂停屏幕输出"

#. __ Options - Window: panel title
#: config.c:4403
msgid "Window properties"
msgstr "窗口属性"

#. __ Options - Window: section title
#: config.c:4405
msgid "Default size"
msgstr "默认大小"

#. __ Options - Window:
#: config.c:4409
msgid "Colu&mns"
msgstr "列数"

#. __ Options - Window:
#: config.c:4413
msgid "Ro&ws"
msgstr "行数"

#. __ Options - Window:
#: config.c:4417
msgid "C&urrent size"
msgstr "当前大小(&U)"

#. __ Options - Window:
#: config.c:4422
msgid "Re&wrap on resize"
msgstr "调整大小时重新折行(&W)"

#. __ Options - Window:
#: config.c:4430
msgid "Scroll&back lines"
msgstr "保存历史行数"

#. __ Options - Window:
#: config.c:4435
msgid "Scrollbar"
msgstr "滚动条"

#. __ Options - Window: scrollbar
#: config.c:4438
msgid "&Left"
msgstr "左(&L)"

#. __ Options - Window: scrollbar
#: config.c:4442
msgid "&Right"
msgstr "右(&R)"

#. __ Options - Window:
#: config.c:4448 config.c:4466
msgid "Modifier for scrolling"
msgstr "用于滚动的修饰键"

#. __ Options - Window:
#: config.c:4495
msgid "&PgUp and PgDn scroll without modifier"
msgstr "即使不按修饰键，也用 PgUp 和 PgDn 滚动"

#. __ Options - Window: section title
#: config.c:4501
msgid "UI language"
msgstr "界面语言"

#. __ Options - Terminal: treeview label
#: config.c:4511 config.c:4524 config.c:4585 config.c:4599
msgid "Terminal"
msgstr "终端"

#. __ Options - Terminal: panel title
#: config.c:4513
msgid "Terminal features"
msgstr "终端特性"

#. __ Options - Terminal:
#: config.c:4517
msgid "&Type"
msgstr "终端类型(&T)"

#. __ Options - Terminal: answerback string for ^E request
#: config.c:4521
msgid "&Answerback"
msgstr "应答字符串(&A)"

#. __ Options - Terminal: section title
#: config.c:4526
msgid "Bell"
msgstr "响铃"

#. __ Options - Terminal: bell
#: config.c:4533
msgid "► &Play"
msgstr "► 播放(&P)"

#. __ Options - Terminal: bell
#: config.c:4539
msgid "&Wave"
msgstr "声音"

#. __ Options - Terminal: bell
#: config.c:4561 config.c:4574
msgid "&Flash"
msgstr "闪屏(&F)"

#. __ Options - Terminal: bell
#: config.c:4563 config.c:4578
msgid "&Highlight in taskbar"
msgstr "任务栏高亮(&H)"

#. __ Options - Terminal: bell
#: config.c:4565 config.c:4582
msgid "&Popup"
msgstr "弹出(&P)"

#. __ Options - Terminal: section title
#: config.c:4587
msgid "Printer"
msgstr "打印机"

#. __ Options - Terminal:
#: config.c:4602
msgid "Prompt about running processes on &close"
msgstr "退出时提示还在运行的进程(&C)"

#. __ Options - Terminal:
#. __ Context menu:
#: config.c:4607 wininput.c:581
msgid "Status Line"
msgstr "状态栏"

#: textprint.c:44 textprint.c:127
msgid "[Printing...] "
msgstr "[打印中...] "

#. __ Options - Text: font chooser activation button
#: winctrls.c:935
msgid "&Select..."
msgstr "选择(&S)..."

#. __ Font chooser: title bar label
#: winctrls.c:1281
msgid "Font "
msgstr "字体 "

#. __ Font chooser: button
#: winctrls.c:1283
msgid "&Apply"
msgstr "应用(&A)"

#. __ Font chooser:
#: winctrls.c:1285
msgid "&Font:"
msgstr "字体(&F)："

#. __ Font chooser:
#: winctrls.c:1291
msgid "Sample"
msgstr "实例"

#. __ Font chooser: text sample ("AaBbYyZz" by default)
#: winctrls.c:1295 winctrls.c:1554 winctrls.c:1719
msgid "Ferqœm’4€"
msgstr "ABC abc 文字实例"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1312
msgid "Sc&ript:"
msgstr "字符集："

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1314
msgid "<A>Show more fonts</A>"
msgstr "<A>显示更多字体</A>"

#. __ Colour chooser: title bar label
#: winctrls.c:1319
msgid "Colour "
msgstr "颜色 "

#. __ Colour chooser:
#: winctrls.c:1332 winctrls.c:1344
msgid "B&asic colours:"
msgstr "基本颜色(&B)："

#. __ Colour chooser:
#: winctrls.c:1353
msgid "&Custom colours:"
msgstr "自定义颜色(&C)："

#. __ Colour chooser:
#: winctrls.c:1360
msgid "De&fine Custom Colours >>"
msgstr "定义当前颜色(&F)>>"

#. __ Colour chooser:
#: winctrls.c:1363
msgid "Colour"
msgstr "颜色"

#. __ Colour chooser:
#: winctrls.c:1365
msgid "|S&olid"
msgstr "|纯色(&O)"

#. __ Colour chooser:
#: winctrls.c:1367
msgid "&Hue:"
msgstr "色调(&H)"

#. __ Colour chooser:
#: winctrls.c:1370
msgid "&Sat:"
msgstr "饱和度(&S)"

#. __ Colour chooser:
#: winctrls.c:1372
msgid "&Lum:"
msgstr "亮度(&L)"

#. __ Colour chooser:
#: winctrls.c:1374
msgid "&Red:"
msgstr "红色(&R)："

#. __ Colour chooser:
#: winctrls.c:1376
msgid "&Green:"
msgstr "绿色(&G)："

#. __ Colour chooser:
#: winctrls.c:1378
msgid "&Blue:"
msgstr "蓝色(&B)："

#. __ Colour chooser:
#: winctrls.c:1381
msgid "A&dd to Custom Colours"
msgstr "添加到自定义颜色(&D)"

#. __ Options: dialog title
#: windialog.c:266 windialog.c:839
msgid "Options"
msgstr "选项"

#. __ Options: dialog title: "mintty <release> available (for download)"
#: windialog.c:268
msgid "available"
msgstr "可用"

#. __ Options: dialog width scale factor (80...200)
#: windialog.c:783
msgid "100"
msgstr "105"

#: windialog.c:924 windialog.c:951
msgid "Error"
msgstr "错误"

#. __ Context menu, session switcher ("virtual tabs") menu label
#: wininput.c:300
msgid "Session switcher"
msgstr "切换会话"

#. __ Context menu, session launcher ("virtual tabs") menu label
#: wininput.c:320
msgid "Session launcher"
msgstr "启动会话"

#: wininput.c:429 wininput.c:435
msgid "Ctrl+"
msgstr ""

#: wininput.c:430 wininput.c:436
msgid "Alt+"
msgstr ""

#: wininput.c:431 wininput.c:437
msgid "Shift+"
msgstr ""

#. __ System menu:
#: wininput.c:462
msgid "&Restore"
msgstr "恢复(&R)"

#. __ System menu:
#: wininput.c:464
msgid "&Move"
msgstr "移动(&M)"

#. __ System menu:
#: wininput.c:466
msgid "&Size"
msgstr "大小(&S)"

#. __ System menu:
#: wininput.c:468
msgid "Mi&nimize"
msgstr "最小化(&N)"

#. __ System menu:
#: wininput.c:470
msgid "Ma&ximize"
msgstr "最大化(&X)"

#. __ System menu:
#: wininput.c:472
msgid "&Close"
msgstr "关闭(&C)"

#. __ System menu:
#: wininput.c:477
msgid "New &Window"
msgstr "新建窗口(&W)"

#. __ System menu:
#: wininput.c:483
msgid "New &Tab"
msgstr "新建标签(&T)"

#. __ Context menu:
#: wininput.c:490
msgid "&Copy"
msgstr "复制(&C)"

#. __ Context menu:
#: wininput.c:509
msgid "&Paste "
msgstr "粘贴(&P) "

#. __ Context menu:
#: wininput.c:514
msgid "Copy → Paste"
msgstr "复制 → 粘贴"

#. __ Context menu:
#: wininput.c:519
msgid "S&earch"
msgstr "搜索(&E)"

#. __ Context menu:
#: wininput.c:526
msgid "&Log to File"
msgstr "写入到日志文件(&L)"

#. __ Context menu:
#: wininput.c:532
msgid "Character &Info"
msgstr "字符详情(&I)"

#. __ Context menu:
#: wininput.c:538
msgid "VT220 Keyboard"
msgstr "VT200 键盘"

#. __ Context menu:
#: wininput.c:543
msgid "&Reset"
msgstr "重置(&R)"

#. __ Context menu:
#: wininput.c:551
msgid "&Default Size"
msgstr "默认大小(&D)"

#. __ Context menu:
#: wininput.c:561
msgid "Scroll&bar"
msgstr "滚动条"

#. __ Context menu:
#: wininput.c:567
msgid "&Full Screen"
msgstr "全屏(&F)"

#. __ Context menu:
#: wininput.c:573
msgid "Flip &Screen"
msgstr "备用屏幕(&S)"

#. __ System menu:
#: wininput.c:591 wininput.c:763
msgid "Copy &Title"
msgstr "复制标题(&T)"

#. __ System menu:
#. __ Context menu:
#. __ System menu:
#: wininput.c:593 wininput.c:747 wininput.c:765
msgid "&Options..."
msgstr "选项(&O)..."

#. __ Context menu:
#: wininput.c:678
msgid "Ope&n"
msgstr "打开(&O)"

#. __ Context menu:
#: wininput.c:683
msgid "Copy as text"
msgstr "以纯文本复制"

#. __ Context menu:
#: wininput.c:687
msgid "Copy as RTF"
msgstr "以 RTF 复制"

#. __ Context menu:
#: wininput.c:689
msgid "Copy as HTML text"
msgstr "以 HTML 文本复制"

#. __ Context menu:
#: wininput.c:691
msgid "Copy as HTML"
msgstr "以 HTML 复制"

#. __ Context menu:
#: wininput.c:693
msgid "Copy as HTML full"
msgstr "以 HTML 全部复制"

#. __ Context menu:
#: wininput.c:700
msgid "Select &All"
msgstr "全选(&A)"

#. __ Context menu:
#: wininput.c:702
msgid "Save as &Image"
msgstr "保存截图(&I)"

#. __ Context menu: write terminal window contents as HTML file
#: wininput.c:714
msgid "HTML Screen Dump"
msgstr "HTML 屏幕转储"

#. __ Context menu: clear scrollback buffer (lines scrolled off the window)
#: wininput.c:722
msgid "Clear Scrollback"
msgstr "清除滚动历史"

#. __ Context menu: generate a TTY BRK condition (tty line interrupt)
#: wininput.c:733
msgid "Send Break"
msgstr "发送 Break"

#. __ Context menu, user commands
#: wininput.c:835
msgid "User commands"
msgstr "用户命令"

#: wininput.c:1503 wininput.c:1524 wininput.c:1526 wininput.c:1528
#: wininput.c:1565
msgid "[NO SCROLL] "
msgstr "［暂停滚屏］"

#: wininput.c:1516 wininput.c:1525 wininput.c:1530 wininput.c:1586
msgid "[SCROLL MODE] "
msgstr "［滚屏模式］"

#: winmain.c:3845
msgid "Processes are running in session:"
msgstr "在当前会话中还运行着进程："

#: winmain.c:3846
msgid "Close anyway?"
msgstr "确认关闭？"

#: winmain.c:3870
msgid "Reset terminal?"
msgstr "重置 终端？"

#: winmain.c:4100
msgid "Try '--help' for more information"
msgstr "使用 --help 参数运行来了解如何使用"

#: winmain.c:4108
msgid "Could not load icon"
msgstr "无法加载图标"

#: winmain.c:6402
msgid "Usage:"
msgstr "用法："

#: winmain.c:6403
msgid "[OPTION]... [ PROGRAM [ARG]... | - ]"
msgstr "[选项]... [ 程序名 [参数]... | - ]"

#. __ help text (output of -H / --help), after initial line ("synopsis")
#: winmain.c:6406
msgid ""
"Start a new terminal session running the specified program or the user's "
"shell.\n"
"If a dash is given instead of a program, invoke the shell as a login shell.\n"
"\n"
"Options:\n"
"  -c, --config FILE     Load specified config file (cf. -C or -o ThemeFile)\n"
"  -e, --exec ...        Treat remaining arguments as the command to execute\n"
"  -h, --hold never|start|error|always  Keep window open after command "
"finishes\n"
"  -p, --position X,Y    Open window at specified coordinates\n"
"  -p, --position center|left|right|top|bottom  Open window at special "
"position\n"
"  -p, --position @N     Open window on monitor N\n"
"  -s, --size COLS,ROWS  Set screen size in characters (also COLSxROWS)\n"
"  -s, --size maxwidth|maxheight  Set max screen size in given dimension\n"
"  -t, --title TITLE     Set window title (default: the invoked command) (cf. "
"-T)\n"
"  -w, --window normal|min|max|full|hide  Set initial window state\n"
"  -i, --icon FILE[,IX]  Load window icon from file, optionally with index\n"
"  -l, --log FILE|-      Log output to file or stdout\n"
"      --nobidi|--nortl  Disable bidi (right-to-left support)\n"
"  -o, --option OPT=VAL  Set/Override config file option with given value\n"
"  -B, --Border frame|void  Use thin/no window border\n"
"  -R, --Report s|o      Report window position (short/long) after exit\n"
"      --nopin           Make this instance not pinnable to taskbar\n"
"  -D, --daemon          Start new instance with Windows shortcut key\n"
"  -H, --help            Display help and exit\n"
"  -V, --version         Print version information and exit\n"
"See manual page for further command line options and configuration.\n"
msgstr ""
"用于新终端会话来运行用户 shell 的指定程序。\n"
"如果指定的程序是 dash，那么将它作为一个登陆 shell 来运行。\n"
"\n"
"选项：\n"
"  -c, --config FILE     加载特定的配置文件（相关选项 -C 或者 -o 主题文件）\n"
"  -e, --exec ...        将剩余参数作为命令来运行\n"
"  -h, --hold never|start|error|always  命令运行结束后维持窗口开启状态\n"
"  -p, --position X,Y    在指定屏幕坐标打开窗口\n"
"  -p, --position center|left|right|top|bottom  在特定位置打开窗口\n"
"  -p, --position @N     在第 N 个显示器打开窗口\n"
"  -s, --size COLS,ROWS  设置屏幕大小（也可以用 COLSxROWS）\n"
"  -s, --size maxwidth|maxheight  设置屏幕的最大宽度或高度\n"
"  -t, --title TITLE     设置窗口标题（默认是运行过的命令（相关选项  -T）\n"
"  -w, --window normal|min|max|full|hide  设置窗口的初始状态\n"
"  -i, --icon FILE[,IX]  从文件加载图标，图标序号可选\n"
"  -l, --log FILE|-      输出日志到文件或者标准输出\n"
"      --nobidi|--nortl  禁用 bidi（从右向左的支持）\n"
"  -o, --option OPT=VAL  用指定的值覆盖配置文件中的值\n"
"  -B, --Border frame|void  使用窗口窄边框/无边框\n"
"  -R, --Report s|o      退出后报告窗口位置信息（短/长）\n"
"      --nopin           让这个实例不能被固定在任务栏上\n"
"  -D, --daemon          使用 Win 快捷键来启动新实例\n"
"  -H, --help            显示帮助信息然后退出\n"
"  -V, --version         打印版本信息然后退出\n"
"请查阅 man 手册了解更多命令行选项和配置相关帮助内容。\n"

#: winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "WSL distribution '%s' not found"
msgstr "找不到 '%s' WSL 发行版"

#: winmain.c:6731
msgid "Duplicate option '%s'"
msgstr "重复的选项 %s"

#: winmain.c:6739 winmain.c:6830
msgid "Unknown option '%s'"
msgstr "未知的选项 %s"

#: winmain.c:6741
msgid "Option '%s' requires an argument"
msgstr "选项 %s 需要一个参数"

#: winmain.c:6768
msgid "Syntax error in position argument '%s'"
msgstr "position 选项的参数 '%s' 有语法错误"

#: winmain.c:6779
msgid "Syntax error in size argument '%s'"
msgstr "size 选项的参数 '%s' 有语法错误"

#: winmain.c:6939
msgid "Syntax error in geometry argument '%s'"
msgstr "geometry 选项的参数 '%s' 有语法错误"

#: winmain.c:7038
msgid "Mintty could not detach from caller, starting anyway"
msgstr "Mintty 不能从调用者分离，但继续启动"

#: winmain.c:7387
msgid "Using default title due to invalid characters in program name"
msgstr "使用默认的标题栏文字，因为程序名中有无效字符"

#: winsearch.c:232
msgid "◀"
msgstr ""

#: winsearch.c:233
msgid "▶"
msgstr ""

#: winsearch.c:234
msgid "X"
msgstr ""

#. __ Options - Text: font properties information: "Leading": total line padding (see option RowSpacing), Bold/Underline modes (font or manual, see options BoldAsFont/UnderlineManual/UnderlineColour)
#: wintext.c:165
msgid "Leading: %d, Bold: %s, Underline: %s"
msgstr "行间距：%d，粗体：%s，下划线：%s"

#. __ Options - Text: font properties: value taken from font
#: wintext.c:167
msgid "font"
msgstr "字体"

#. __ Options - Text: font properties: value affected by option
#: wintext.c:169
msgid "manual"
msgstr "手动"

#: wintext.c:544
msgid "Font not found, using system substitute"
msgstr "找不到字体，使用系统字体替代"

#: wintext.c:559
msgid "Font has limited support for character ranges"
msgstr "字体对字符范围支持有限"

#: wintext.c:702
msgid "Font installation corrupt, using system substitute"
msgstr "字体安装错误，使用系统字体替代"

#: wintext.c:756
msgid "Font does not support system locale"
msgstr "字体不支持系统语言环境"

#: appinfo.h:64
msgid "There is no warranty, to the extent permitted by law."
msgstr "在法律许可的情况下特此明示，本软件不提供任何担保。"

#. __ %s: WEBSITE (URL)
#: appinfo.h:69
msgid ""
"Please report bugs or request enhancements through the issue tracker on the "
"mintty project page located at\n"
"%s.\n"
"See also the Wiki there for further hints, thanks and credits."
msgstr ""
"在 mintty 项目主页的 Issues 版块反馈问题或者提交功能改进\n"
"地址：%s\n"
"Wiki 页面中有更多帮助、鸣谢和许可等信息。"
