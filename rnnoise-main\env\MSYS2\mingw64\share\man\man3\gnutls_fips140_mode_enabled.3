.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_fips140_mode_enabled" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_fips140_mode_enabled \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned gnutls_fips140_mode_enabled( " void ");"
.SH ARGUMENTS
.IP " void" 12
.SH "DESCRIPTION"

Checks whether this library is in FIPS140 mode. The returned
value corresponds to the library mode as set with
\fBgnutls_fips140_set_mode()\fP.

If \fBgnutls_fips140_set_mode()\fP was called with \fBGNUTLS_FIPS140_SET_MODE_THREAD\fP
then this function will return the current thread's FIPS140 mode, otherwise
the global value is returned.
.SH "RETURNS"
return non\-zero if true or zero if false.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
