.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_obj_import_url" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_obj_import_url \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_obj_import_url(gnutls_pkcs11_obj_t " obj ", const char * " url ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_obj_t obj" 12
The structure to store the object
.IP "const char * url" 12
a PKCS 11 url identifying the key
.IP "unsigned int flags" 12
Or sequence of GNUTLS_PKCS11_OBJ_* flags
.SH "DESCRIPTION"
This function will "import" a PKCS 11 URL identifying an object (e.g. certificate)
to the \fBgnutls_pkcs11_obj_t\fP type. This does not involve any
parsing (such as X.509 or OpenPGP) since the \fBgnutls_pkcs11_obj_t\fP is
format agnostic. Only data are transferred.

If the flag \fBGNUTLS_PKCS11_OBJ_FLAG_OVERWRITE_TRUSTMOD_EXT\fP is specified
any certificate read, will have its extensions overwritten by any
stapled extensions in the trust module.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
