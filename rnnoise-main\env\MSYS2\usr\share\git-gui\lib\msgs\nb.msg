set ::msgcat::header "Project-Id-Version: nb\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2008-12-03 16:05+0100\nLast-Translator: <PERSON><PERSON> <<EMAIL>>\nLanguage-Team: Norwegian Bokm\u00e5l\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\n"
::msgcat::mcset nb "git-gui: fatal error" "git-gui: Kritisk feil"
::msgcat::mcset nb "Invalid font specified in %s:" "Ugyldig font spesifisert i %s:"
::msgcat::mcset nb "Main Font" "Hovedskrifttype"
::msgcat::mcset nb "Diff/Console Font" "Diff-/Konsollskrifttype"
::msgcat::mcset nb "Cannot find git in PATH." "Kan ikke finne git i PATH"
::msgcat::mcset nb "Cannot parse Git version string:" "Kan ikke tyde Git's oppgitte versjon:"
::msgcat::mcset nb "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "Kan ikke avgj\u00f8re hvilken Git-versjon du har.\n\n%s sier versjonen er '%s'.\n\n%s krever Git versjon 1.5.0 eller nyere.\n\nAnta at '%s' er versjon 1.5.0?\n"
::msgcat::mcset nb "Git directory not found:" "Git-katalog ikke funnet:"
::msgcat::mcset nb "Cannot move to top of working directory:" "Kan ikke g\u00e5 til toppen av arbeidskatalogen:"
::msgcat::mcset nb "No working directory" "Ingen arbeidskatalog"
::msgcat::mcset nb "Refreshing file status..." "Oppdaterer filstatus..."
::msgcat::mcset nb "Scanning for modified files ..." "S\u00f8ker etter endrede filer..."
::msgcat::mcset nb "Ready." "Klar."
::msgcat::mcset nb "Unmodified" "Uendret"
::msgcat::mcset nb "Modified, not staged" "Endret, ikke k\u00f8et"
::msgcat::mcset nb "Staged for commit" "K\u00f8et for innsjekking"
::msgcat::mcset nb "Portions staged for commit" "Delvis k\u00f8et for innsjekking"
::msgcat::mcset nb "Staged for commit, missing" "Klar for innsjekking, frav\u00e6rende"
::msgcat::mcset nb "File type changed, not staged" "Filtype endret, ikke k\u00f8et"
::msgcat::mcset nb "File type changed, staged" "Filtype endret, k\u00f8et"
::msgcat::mcset nb "Untracked, not staged" "Usporet, ikke k\u00f8et"
::msgcat::mcset nb "Missing" "Frav\u00e6rende"
::msgcat::mcset nb "Staged for removal" "K\u00f8et for fjerning"
::msgcat::mcset nb "Staged for removal, still present" "K\u00f8et for fjerning, fortsatt tilstede"
::msgcat::mcset nb "Requires merge resolution" "Sammensl\u00e5ingen krever konflikth\u00e5ndtering"
::msgcat::mcset nb "Starting gitk... please wait..." "Starter gitk... Vennligst vent..."
::msgcat::mcset nb "Couldn't find gitk in PATH" "Kunne ikke finne gitk i PATH"
::msgcat::mcset nb "Repository" "Arkiv"
::msgcat::mcset nb "Edit" "Redigere"
::msgcat::mcset nb "Branch" "Gren"
::msgcat::mcset nb "Commit@@noun" "Innsjekking"
::msgcat::mcset nb "Merge" "Sammensl\u00e5ing"
::msgcat::mcset nb "Remote" "Fjernarkiv"
::msgcat::mcset nb "Tools" "Verkt\u00f8y"
::msgcat::mcset nb "Explore Working Copy" "Utforsk arbeidskopien"
::msgcat::mcset nb "Browse Current Branch's Files" "Utforsk denne grens filer"
::msgcat::mcset nb "Browse Branch Files..." "Bla igjennom filer p\u00e5 gren..."
::msgcat::mcset nb "Visualize Current Branch's History" "Visualiser denne grens historikk"
::msgcat::mcset nb "Visualize All Branch History" "Visualiser alle greners historikk"
::msgcat::mcset nb "Browse %s's Files" "Bla i filene til %s"
::msgcat::mcset nb "Visualize %s's History" "Visualiser historien til %s"
::msgcat::mcset nb "Database Statistics" "Databasestatistikk"
::msgcat::mcset nb "Compress Database" "Kompress databasen"
::msgcat::mcset nb "Verify Database" "Verifiser databasen"
::msgcat::mcset nb "Create Desktop Icon" "Lag skrivebordsikon"
::msgcat::mcset nb "Quit" "Avslutt"
::msgcat::mcset nb "Undo" "Angre"
::msgcat::mcset nb "Redo" "Gj\u00f8r om"
::msgcat::mcset nb "Cut" "Klipp ut"
::msgcat::mcset nb "Copy" "Kopier"
::msgcat::mcset nb "Paste" "Lim inn"
::msgcat::mcset nb "Delete" "Slett"
::msgcat::mcset nb "Select All" "Velg alle"
::msgcat::mcset nb "Create..." "Opprett..."
::msgcat::mcset nb "Checkout..." "Sjekk ut..."
::msgcat::mcset nb "Rename..." "Endre navn..."
::msgcat::mcset nb "Delete..." "Slett..."
::msgcat::mcset nb "Reset..." "Tilbakestill..."
::msgcat::mcset nb "Done" "Ferdig"
::msgcat::mcset nb "Commit@@verb" "Sjekk inn"
::msgcat::mcset nb "New Commit" "Ny innsjekking"
::msgcat::mcset nb "Amend Last Commit" "Legg til forrige innsjekking"
::msgcat::mcset nb "Rescan" "S\u00f8k p\u00e5 ny"
::msgcat::mcset nb "Stage To Commit" "Legg til i innsjekkingsk\u00f8en"
::msgcat::mcset nb "Stage Changed Files To Commit" "Legg til endrede filer i innsjekkingsk\u00f8en"
::msgcat::mcset nb "Unstage From Commit" "Fjern fra innsjekkingsk\u00f8en"
::msgcat::mcset nb "Revert Changes" "Tilbakestill endringer"
::msgcat::mcset nb "Show Less Context" "Vis mindre innhold"
::msgcat::mcset nb "Show More Context" "Vis mer innhold"
::msgcat::mcset nb "Sign Off" "Sign\u00e9r"
::msgcat::mcset nb "Local Merge..." "Lokal sammensl\u00e5ing..."
::msgcat::mcset nb "Abort Merge..." "Avbryt sammensl\u00e5ing..."
::msgcat::mcset nb "Add..." "Legg til..."
::msgcat::mcset nb "Push..." "Send..."
::msgcat::mcset nb "Delete Branch..." "Fjern gren..."
::msgcat::mcset nb "About %s" "Om %s"
::msgcat::mcset nb "Preferences..." "Innstillinger..."
::msgcat::mcset nb "Options..." "Alternativer..."
::msgcat::mcset nb "Remove..." "Fjern..."
::msgcat::mcset nb "Help" "Hjelp"
::msgcat::mcset nb "Online Documentation" "Online dokumentasjon"
::msgcat::mcset nb "Show SSH Key" "Vis SSH-n\u00f8kkel"
::msgcat::mcset nb "fatal: cannot stat path %s: No such file or directory" "kritisk: kunne ikke finne status for sti %s: Ingen slik fil eller katalog"
::msgcat::mcset nb "Current Branch:" "N\u00e5v\u00e6rende gren:"
::msgcat::mcset nb "Staged Changes (Will Commit)" "K\u00f8ede endringer (til innsjekking)"
::msgcat::mcset nb "Unstaged Changes" "Uk\u00f8ede endringer"
::msgcat::mcset nb "Stage Changed" "K\u00f8 endret"
::msgcat::mcset nb "Push" "Send"
::msgcat::mcset nb "Initial Commit Message:" "Innledende innsjekkingsmelding:"
::msgcat::mcset nb "Amended Commit Message:" "Utdypt innsjekkingsmelding"
::msgcat::mcset nb "Amended Initial Commit Message:" "Utdypt innledende innsjekkingsmelding:"
::msgcat::mcset nb "Amended Merge Commit Message:" "Utdypt innsjekkingsmelding for sammensl\u00e5ing:"
::msgcat::mcset nb "Merge Commit Message:" "Revisjonsmelding for sammensl\u00e5ing:"
::msgcat::mcset nb "Commit Message:" "Revisjonsmelding:"
::msgcat::mcset nb "Copy All" "Kopier alle"
::msgcat::mcset nb "File:" "Fil:"
::msgcat::mcset nb "Refresh" "Oppdater"
::msgcat::mcset nb "Decrease Font Size" "Gj\u00f8r teksten mindre"
::msgcat::mcset nb "Increase Font Size" "Gj\u00f8r teksten st\u00f8rre"
::msgcat::mcset nb "Encoding" "Tekstkoding"
::msgcat::mcset nb "Apply/Reverse Hunk" "Bruk/tilbakestill del"
::msgcat::mcset nb "Apply/Reverse Line" "Bruk/tilbakestill linje"
::msgcat::mcset nb "Run Merge Tool" "Start sammensl\u00e5ingsprosess"
::msgcat::mcset nb "Use Remote Version" "Bruk versjon fra fjernarkiv"
::msgcat::mcset nb "Use Local Version" "Bruk lokal versjon"
::msgcat::mcset nb "Revert To Base" "Tilbakestill til baseversjonen"
::msgcat::mcset nb "Unstage Hunk From Commit" "Fjern delen fra innsjekkingsk\u00f8en"
::msgcat::mcset nb "Unstage Line From Commit" "Fjern linjen fra innsjekkingsk\u00f8en"
::msgcat::mcset nb "Stage Hunk For Commit" "Legg del i innsjekkingsk\u00f8en"
::msgcat::mcset nb "Stage Line For Commit" "Legg til linje i innsjekkingsk\u00f8en"
::msgcat::mcset nb "Initializing..." "Initsialiserer..."
::msgcat::mcset nb "git-gui - a graphical user interface for Git." "git-gui - Et grafisk brukergrensesnitt for Git."
::msgcat::mcset nb "File Viewer" "Filviser"
::msgcat::mcset nb "Commit:" "Innsjekking:"
::msgcat::mcset nb "Copy Commit" "Kopier innsjekking"
::msgcat::mcset nb "Find Text..." "S\u00f8k etter tekst..."
::msgcat::mcset nb "Do Full Copy Detection" "Gjennomf\u00f8r full deteksjon av kopieringer"
::msgcat::mcset nb "Show History Context" "Vis historikkens innhold"
::msgcat::mcset nb "Reading %s..." "Leser %s..."
::msgcat::mcset nb "Busy" "Opptatt"
::msgcat::mcset nb "Running thorough copy detection..." "Kj\u00f8rer kopidetektering..."
::msgcat::mcset nb "Author:" "Forfatter:"
::msgcat::mcset nb "Committer:" "Innsjekker:"
::msgcat::mcset nb "Original File:" "Opprinnelig fil:"
::msgcat::mcset nb "Cannot find HEAD commit:" "Finner ikke HEAD's innsjekking:"
::msgcat::mcset nb "Cannot find parent commit:" "Kan ikke finne innsjekkingens forelder:"
::msgcat::mcset nb "Unable to display parent" "Kan ikke vise forelder"
::msgcat::mcset nb "Error loading diff:" "Feil ved innlasting av forskjell:"
::msgcat::mcset nb "Originally By:" "Opprinnelig av:"
::msgcat::mcset nb "In File:" "I fil:"
::msgcat::mcset nb "Copied Or Moved Here By:" "Kopiert eller flyttet hit av:"
::msgcat::mcset nb "Checkout Branch" "Sjekk ut gren"
::msgcat::mcset nb "Checkout" "Utsjekking"
::msgcat::mcset nb "Cancel" "Avbryt"
::msgcat::mcset nb "Revision" "Revisjon"
::msgcat::mcset nb "Options" "Valg"
::msgcat::mcset nb "Fetch Tracking Branch" "Hent sporet gren"
::msgcat::mcset nb "Detach From Local Branch" "Koble bort lokal gren"
::msgcat::mcset nb "Create Branch" "Opprett gren"
::msgcat::mcset nb "Create New Branch" "Opprett ny gren"
::msgcat::mcset nb "Create" "Opprett"
::msgcat::mcset nb "Branch Name" "Navn p\u00e5 gren"
::msgcat::mcset nb "Name:" "Navn:"
::msgcat::mcset nb "Match Tracking Branch Name" "Bruk navn p\u00e5 sporet gren"
::msgcat::mcset nb "Starting Revision" "Starter revisjon"
::msgcat::mcset nb "Update Existing Branch:" "Oppdater eksisterende gren:"
::msgcat::mcset nb "No" "Nei"
::msgcat::mcset nb "Fast Forward Only" "Kun hurtigfremspoling"
::msgcat::mcset nb "Reset" "Tilbakestill"
::msgcat::mcset nb "Checkout After Creation" "Sjekk ut etter oppretting"
::msgcat::mcset nb "Please select a tracking branch." "Velg en gren som skal f\u00f8lges."
::msgcat::mcset nb "Tracking branch %s is not a branch in the remote repository." "Den fulgte grenen %s er ikke en gren i fjernarkivet."
::msgcat::mcset nb "Please supply a branch name." "Angi et navn for grenen."
::msgcat::mcset nb "'%s' is not an acceptable branch name." "'%s' kan ikke brukes som navn p\u00e5 en gren."
::msgcat::mcset nb "Delete Branch" "Fjern gren"
::msgcat::mcset nb "Delete Local Branch" "Fjern lokal gren"
::msgcat::mcset nb "Local Branches" "Lokale grener"
::msgcat::mcset nb "Delete Only If Merged Into" "Fjern kun ved sammensl\u00e5ing"
::msgcat::mcset nb "Always (Do not perform merge test.)" "Alltid (Ikke utf\u00f8r sammensl\u00e5ingstest.)"
::msgcat::mcset nb "The following branches are not completely merged into %s:" "F\u00f8lgende grener er ikke fullstendig sl\u00e5tt sammen med %s:"
::msgcat::mcset nb "Failed to delete branches:\n%s" "Kunne ikke fjerne grener:\n%s"
::msgcat::mcset nb "Rename Branch" "Gi gren nytt navn"
::msgcat::mcset nb "Rename" "Endre navn"
::msgcat::mcset nb "Branch:" "Gren:"
::msgcat::mcset nb "New Name:" "Nytt navn:"
::msgcat::mcset nb "Please select a branch to rename." "Vennligst velg grenen du vil endre navn p\u00e5."
::msgcat::mcset nb "Branch '%s' already exists." "Grenen '%s' eksisterer allerede."
::msgcat::mcset nb "Failed to rename '%s'." "Kunne ikke endre navnet '%s'."
::msgcat::mcset nb "Starting..." "Starter..."
::msgcat::mcset nb "File Browser" "Utforsker"
::msgcat::mcset nb "Loading %s..." "Laster %s..."
::msgcat::mcset nb "\[Up To Parent\]" "\[Opp til forelder\]"
::msgcat::mcset nb "Browse Branch Files" "Bla igjennom grenens filer"
::msgcat::mcset nb "Browse" "Bla igjennom"
::msgcat::mcset nb "Fetching %s from %s" "Henter %s fra %s"
::msgcat::mcset nb "fatal: Cannot resolve %s" "kritisk: Kan ikke \u00e5pne %s"
::msgcat::mcset nb "Close" "Lukk"
::msgcat::mcset nb "Branch '%s' does not exist." "Grenen '%s' eksisterer ikke."
::msgcat::mcset nb "Failed to configure simplified git-pull for '%s'." "Kunne ikke konfigurere forenklet git-pull for '%s'."
::msgcat::mcset nb "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "Grenen '%s' eksisterer allerede.\n\nDen kan ikke hurtigfremspoles til %s.\nEn sammensl\u00e5ing er p\u00e5krevd."
::msgcat::mcset nb "Merge strategy '%s' not supported." "Sammensl\u00e5ingsstrategien '%s' er ikke st\u00f8ttet."
::msgcat::mcset nb "Failed to update '%s'." "Kunne ikke oppdatere '%s'."
::msgcat::mcset nb "Staging area (index) is already locked." "K\u00f8omr\u00e5det (index) er allerede l\u00e5st."
::msgcat::mcset nb "Updating working directory to '%s'..." "Oppdaterer arbeidskatalogen til '%s'..."
::msgcat::mcset nb "files checked out" "filer sjekket ut"
::msgcat::mcset nb "Aborted checkout of '%s' (file level merging is required)." "Avbr\u00f8t utsjekkingen av '%s' (sammensl\u00e5ing p\u00e5 filniv\u00e5 kreves)."
::msgcat::mcset nb "File level merge required." "Sammensl\u00e5ing p\u00e5 filniv\u00e5 kreves"
::msgcat::mcset nb "Staying on branch '%s'." "Blir st\u00e5ende p\u00e5 grenen '%s'."
::msgcat::mcset nb "Checked out '%s'." "Sjekket ut '%s'."
::msgcat::mcset nb "Resetting '%s' to '%s' will lose the following commits:" "Tilbakestilling av '%s' til '%s' vil medf\u00f8re tap av f\u00f8lgende innsjekkinger:"
::msgcat::mcset nb "Recovering lost commits may not be easy." "Det vil kanskje ikke v\u00e6re s\u00e5 enkelt \u00e5 gjenopprette en tapt innsjekking."
::msgcat::mcset nb "Reset '%s'?" "Tilbakestill '%s'?"
::msgcat::mcset nb "Visualize" "Visualiser"
::msgcat::mcset nb "Select" "Velg"
::msgcat::mcset nb "Font Family" "Skrifttype-familie"
::msgcat::mcset nb "Font Size" "Skriftst\u00f8rrelse"
::msgcat::mcset nb "Font Example" "Skrifteksempel"
::msgcat::mcset nb "This is example text.\nIf you like this text, it can be your font." "Dette er en eksempeltekst.\nHvis du liker hvordan teksten ser ut, kan du velge dette som din skrifttype."
::msgcat::mcset nb "Git Gui" "Git Gui"
::msgcat::mcset nb "Create New Repository" "Opprett nytt arkiv"
::msgcat::mcset nb "New..." "Ny..."
::msgcat::mcset nb "Clone Existing Repository" "Klon eksistererende arkiv"
::msgcat::mcset nb "Clone..." "Klon..."
::msgcat::mcset nb "Open Existing Repository" "\u00c5pne eksistererende arkiv"
::msgcat::mcset nb "Open..." "\u00c5pne..."
::msgcat::mcset nb "Recent Repositories" "Nylig brukte arkiv"
::msgcat::mcset nb "Open Recent Repository:" "\u00c5pne nylig brukt arkiv:"
::msgcat::mcset nb "Failed to create repository %s:" "Kunne ikke opprette arkivet %s:"
::msgcat::mcset nb "Directory:" "Mappe:"
::msgcat::mcset nb "Git Repository" "Git arkiv"
::msgcat::mcset nb "Directory %s already exists." "Mappen %s eksisterer allerede."
::msgcat::mcset nb "File %s already exists." "Filen %s eksisterer allerede."
::msgcat::mcset nb "Clone" "Klon"
::msgcat::mcset nb "Source Location:" "Kildeplassering:"
::msgcat::mcset nb "Target Directory:" "Destinasjonsmappe:"
::msgcat::mcset nb "Clone Type:" "Klontype:"
::msgcat::mcset nb "Standard (Fast, Semi-Redundant, Hardlinks)" "Standard (rask, delvis redundant, hardlinker)"
::msgcat::mcset nb "Full Copy (Slower, Redundant Backup)" "Full kopi (tregere, redundant sikkerhetskopi)"
::msgcat::mcset nb "Shared (Fastest, Not Recommended, No Backup)" "Delt (raskest, ikke anbefalt, ingen sikkerhetskopiering)"
::msgcat::mcset nb "Not a Git repository: %s" "Ikke et Git-arkiv: %s"
::msgcat::mcset nb "Standard only available for local repository." "Standard er kun tilgjengelig for lokalt arkiv."
::msgcat::mcset nb "Shared only available for local repository." "Delt er kun tilgjengelig for lokalt arkiv."
::msgcat::mcset nb "Location %s already exists." "Stedet %s eksisterer allerede."
::msgcat::mcset nb "Failed to configure origin" "Kunne ikke konfigurere kildeoppf\u00f8ring"
::msgcat::mcset nb "Counting objects" "Teller objekter"
::msgcat::mcset nb "buckets" "b\u00f8tter"
::msgcat::mcset nb "Unable to copy objects/info/alternates: %s" "Kunne ikke kopiere objekter/informasjon/alternativt: %s"
::msgcat::mcset nb "Nothing to clone from %s." "Ingenting \u00e5 klone fra %s."
::msgcat::mcset nb "The 'master' branch has not been initialized." "Grenen 'master' har ikke blitt initsialisert."
::msgcat::mcset nb "Hardlinks are unavailable.  Falling back to copying." "Harde linker er utilgjengelig. G\u00e5r tilbake til kopiering."
::msgcat::mcset nb "Cloning from %s" "Kloner fra %s"
::msgcat::mcset nb "Copying objects" "Kopierer objekter"
::msgcat::mcset nb "KiB" "kB"
::msgcat::mcset nb "Unable to copy object: %s" "Kunne ikke kopiere objekt: %s"
::msgcat::mcset nb "Linking objects" "Lenker objekter"
::msgcat::mcset nb "objects" "objekter"
::msgcat::mcset nb "Unable to hardlink object: %s" "Kunne ikke opprette hardlink med objektet: %s"
::msgcat::mcset nb "Cannot fetch branches and objects.  See console output for details." "Kunne ikke hente grener og objekter. Se utdata i konsoll for detaljer."
::msgcat::mcset nb "Cannot fetch tags.  See console output for details." "Kunne ikke hente tagger. Se utdata i konsoll for detaljer."
::msgcat::mcset nb "Cannot determine HEAD.  See console output for details." "Kan ikke bestemme HEAD. Se utdata i konsoll for detaljer."
::msgcat::mcset nb "Unable to cleanup %s" "Kunne ikke rydde opp %s"
::msgcat::mcset nb "Clone failed." "Kloning feilet."
::msgcat::mcset nb "No default branch obtained." "Ingen standardgren hentet."
::msgcat::mcset nb "Cannot resolve %s as a commit." "Kan ikke finne %s som en innsjekking."
::msgcat::mcset nb "Creating working directory" "Oppretter arbeidskatalog"
::msgcat::mcset nb "files" "filer"
::msgcat::mcset nb "Initial file checkout failed." "Initsialiserende utsjekking feilet."
::msgcat::mcset nb "Open" "\u00c5pne"
::msgcat::mcset nb "Repository:" "Arkiv:"
::msgcat::mcset nb "Failed to open repository %s:" "Kunne ikke \u00e5pne arkivet %s:"
::msgcat::mcset nb "This Detached Checkout" "Denne frakoblede utsjekkingen"
::msgcat::mcset nb "Revision Expression:" "Revisjonsuttrykk:"
::msgcat::mcset nb "Local Branch" "Lokal gren"
::msgcat::mcset nb "Tracking Branch" "Sporet gren"
::msgcat::mcset nb "Tag" "Tag"
::msgcat::mcset nb "Invalid revision: %s" "Ugyldig revisjon: %s"
::msgcat::mcset nb "No revision selected." "Ingen revisjoner valgt."
::msgcat::mcset nb "Revision expression is empty." "Revisjonsuttrykk er tomt."
::msgcat::mcset nb "Updated" "Oppdatert"
::msgcat::mcset nb "URL" "URL"
::msgcat::mcset nb "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "Det er ingenting \u00e5 legge til.\n\nDu er i ferd med \u00e5 lage den initsialiserende revisjonen. Det er ingen tidligere revisjoner \u00e5 tilf\u00f8ye.\n"
::msgcat::mcset nb "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "Kan ikke tilf\u00f8ye under sammensl\u00e5ing.\n\nDu er for \u00f8yeblikket under en p\u00e5g\u00e5ende sammensl\u00e5ing som ikke er fullf\u00f8rt. Du kan ikke tilf\u00f8ye en tidligere revisjon med mindre du f\u00f8rst avbryter denne sammensl\u00e5ingen.\n"
::msgcat::mcset nb "Error loading commit data for amend:" "Feil ved innhenting av revisjonsdata for tilf\u00f8ying:"
::msgcat::mcset nb "Unable to obtain your identity:" "Kunne ikke avgj\u00f8re din identitet:"
::msgcat::mcset nb "Invalid GIT_COMMITTER_IDENT:" "Ugyldig GIT_COMMITTER_IDENT:"
::msgcat::mcset nb "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "Ukjent filstatus %s er funnet.\n\nFilen %s kan ikke sjekkes inn av dette programmet.\n"
::msgcat::mcset nb "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "Ingen endringer \u00e5 sjekke inn.\n\nDu m\u00e5 k\u00f8e minst en fil f\u00f8r du kan sjekke inn noe.\n"
::msgcat::mcset nb "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "Vennligst angi en revisjonsmelding.\n\nEn god melding har f\u00f8lgende format:\n\n- F\u00f8rste linje: En beskrivelse av hva du har gjort i \u00e9n setning.\n- Andre linje: Blank\n- Resterende linjer: Forklar hvorfor denne endringen er bra.\n"
::msgcat::mcset nb "warning: Tcl does not support encoding '%s'." "advarsel: Tcl st\u00f8tter ikke denne tegnkodingen '%s'."
::msgcat::mcset nb "Committing changes..." "Sjekker inn endringer..."
::msgcat::mcset nb "write-tree failed:" "Skriving til tre feilet:"
::msgcat::mcset nb "Commit failed." "Innsjekking feilet."
::msgcat::mcset nb "Commit %s appears to be corrupt" "Revisjon %s ser ut til \u00e5 v\u00e6re korrupt"
::msgcat::mcset nb "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "Ingen endringer til innsjekking.\n\nIngen filer ble endret av denne revisjonen, og det var ikke en revisjon fra en sammensl\u00e5ing.\n\nEt nytt s\u00f8k vil bli startet automatisk.\n"
::msgcat::mcset nb "No changes to commit." "Ingen endringer til innsekking."
::msgcat::mcset nb "commit-tree failed:" "commit-tree feilet:"
::msgcat::mcset nb "update-ref failed:" "update-ref feilet:"
::msgcat::mcset nb "Created commit %s: %s" "Opprettet innsjekking %s: %s"
::msgcat::mcset nb "Working... please wait..." "Jobber... Vennligst vent..."
::msgcat::mcset nb "Success" "Suksess"
::msgcat::mcset nb "Error: Command Failed" "Feil: Kommandoen feilet"
::msgcat::mcset nb "Number of loose objects" "Antall l\u00f8se objekter"
::msgcat::mcset nb "Disk space used by loose objects" "Diskplass brukt av l\u00f8se objekter"
::msgcat::mcset nb "Number of packed objects" "Antall pakkede objekter"
::msgcat::mcset nb "Number of packs" "Antall pakker"
::msgcat::mcset nb "Disk space used by packed objects" "Diskplass brukt av pakkede objekter"
::msgcat::mcset nb "Packed objects waiting for pruning" "Pakkede objekter som avventer fjerning"
::msgcat::mcset nb "Garbage files" "Avfallsfiler"
::msgcat::mcset nb "Compressing the object database" "Komprimerer objektdatabasen"
::msgcat::mcset nb "Verifying the object database with fsck-objects" "Verifiserer objektdatabasen med fsck-objects"
::msgcat::mcset nb "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database when more than %i loose objects exist.\n\nCompress the database now?" "Dette arkivet inneholder omtrent %i 'l\u00f8se' objekter.\n\nFor \u00e5 sikre en optimal ytelse er det sterkt anbefalt at du komprimerer databasen n\u00e5r det er flere enn %i 'l\u00f8se' objekter i den.\n\nKomprimere databasen n\u00e5?"
::msgcat::mcset nb "Invalid date from Git: %s" "Ugyldig dato fra Git: %s"
::msgcat::mcset nb "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "Ingen forandringer funnet.\n\n%s har ingen endringer.\n\nTidsstempelet for endring p\u00e5 denne filen ble oppdatert av en annen  applikasjon, men innholdet er uendret.\n\nEn gjennoms\u00f8king vil n\u00e5 starte automatisk for \u00e5 se om andre filer har status."
::msgcat::mcset nb "Loading diff of %s..." "Laster inn forskjellene av %s..."
::msgcat::mcset nb "LOCAL: deleted\nREMOTE:\n" "LOKAL: slettet\nFJERN:\n"
::msgcat::mcset nb "REMOTE: deleted\nLOCAL:\n" "FJERN: slettet\nLOKAL:\n"
::msgcat::mcset nb "LOCAL:\n" "LOKAL:\n"
::msgcat::mcset nb "REMOTE:\n" "FJERN:\n"
::msgcat::mcset nb "Unable to display %s" "Kan ikke vise %s"
::msgcat::mcset nb "Error loading file:" "Feil ved lesing av fil: %s"
::msgcat::mcset nb "Git Repository (subproject)" "Git-arkiv (underprosjekt)"
::msgcat::mcset nb "* Binary file (not showing content)." "* Bin\u00e6rfil (viser ikke innhold)"
::msgcat::mcset nb "* Untracked file is %d bytes.\n* Showing only first %d bytes.\n" "* Usporet fil er %d bytes.\n* Viser bare %d f\u00f8rste bytes.\n"
::msgcat::mcset nb "\n* Untracked file clipped here by %s.\n* To see the entire file, use an external editor.\n" "\n* Usporede filer klippet her av %s.\n* For \u00e5 se hele filen, bruk et eksternt redigeringsverkt\u00f8y.\n"
::msgcat::mcset nb "Failed to unstage selected hunk." "Kunne ikke fjerne den valgte delen fra innsjekkingsk\u00f8en."
::msgcat::mcset nb "Failed to stage selected hunk." "Kunne ikke legge til den valgte delen i innsjekkingsk\u00f8en."
::msgcat::mcset nb "Failed to unstage selected line." "Kunne ikke fjerne den valgte linjen fra innsjekkingsk\u00f8en."
::msgcat::mcset nb "Failed to stage selected line." "Kunne ikke legge til den valgte linjen i innsjekkingsk\u00f8en."
::msgcat::mcset nb "Default" "Standard"
::msgcat::mcset nb "System (%s)" "Systemets (%s)"
::msgcat::mcset nb "Other" "Andre"
::msgcat::mcset nb "error" "feil"
::msgcat::mcset nb "warning" "advarsel"
::msgcat::mcset nb "You must correct the above errors before committing." "Du m\u00e5 rette de ovenst\u00e5ende feilene f\u00f8r innsjekking."
::msgcat::mcset nb "Unable to unlock the index." "Kunne ikke l\u00e5se opp indexen."
::msgcat::mcset nb "Index Error" "Feil p\u00e5 index"
::msgcat::mcset nb "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "Oppdatering av Git's index mislyktes. Et nytt s\u00f8k vil bli startet for \u00e5 resynkronisere git-gui."
::msgcat::mcset nb "Continue" "Fortsett"
::msgcat::mcset nb "Unlock Index" "L\u00e5s opp index"
::msgcat::mcset nb "Unstaging %s from commit" "Fjerner %s fra innsjekkingsk\u00f8en"
::msgcat::mcset nb "Ready to commit." "Klar til innsjekking."
::msgcat::mcset nb "Adding %s" "Legger til %s"
::msgcat::mcset nb "Revert changes in file %s?" "Reverter endringene i filen %s?"
::msgcat::mcset nb "Revert changes in these %i files?" "Reverter endringene i disse %i filene?"
::msgcat::mcset nb "Any unstaged changes will be permanently lost by the revert." "Endringer som ikke ligger i innsjekkingsk\u00f8en vil bli tapt av denne reverteringen"
::msgcat::mcset nb "Do Nothing" "Ikke gj\u00f8r noe"
::msgcat::mcset nb "Reverting selected files" "Reverterer valgte filer"
::msgcat::mcset nb "Reverting %s" "Reverterer %s"
::msgcat::mcset nb "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "Kunne ikke sl\u00e5 sammen under utvidelse.\n\nDu m\u00e5 f\u00f8rst fullf\u00f8re utvidelsen av denne revisjonen f\u00f8r du kan starte en sammensl\u00e5ing.\n"
::msgcat::mcset nb "%s of %s" "%s av %s"
::msgcat::mcset nb "Merging %s and %s..." "Sl\u00e5r sammen %s og %s"
::msgcat::mcset nb "Merge completed successfully." "Vellykket sammensl\u00e5ing fullf\u00f8rt."
::msgcat::mcset nb "Merge failed.  Conflict resolution is required." "Sammensl\u00e5ing feilet. H\u00e5ndtering av konflikten kreves."
::msgcat::mcset nb "Merge Into %s" "Sl\u00e5 sammen inn i %s"
::msgcat::mcset nb "Revision To Merge" "Revisjon til sammensl\u00e5ing"
::msgcat::mcset nb "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "Kan ikke avbryte under utvidelse av revisjon.\n\nDu m\u00e5 fullf\u00f8re utvidelsen av denne revisjonen.\n"
::msgcat::mcset nb "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "Avbryt sammensl\u00e5ing?\n\nAvbryting av p\u00e5g\u00e5ende sammensl\u00e5ing vil f\u00f8re til at *alle* endringer som ikke  er sjekket inn, vil g\u00e5 tapt.\n\nFortsette med \u00e5 avbryte den p\u00e5g\u00e5ende sammensl\u00e5ingen?"
::msgcat::mcset nb "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "Nullstill endringer?\n\nNullstilling av endringer vil f\u00f8re til at *alle* endringer som ikke er sjekket inn g\u00e5r tapt.\n\nFortsette med nullstilling av endringer?"
::msgcat::mcset nb "Aborting" "Avbryter"
::msgcat::mcset nb "files reset" "filer tilbakestilt"
::msgcat::mcset nb "Abort failed." "Avbryting feilet."
::msgcat::mcset nb "Abort completed.  Ready." "Avbryting fullf\u00f8rt. Klar."
::msgcat::mcset nb "Force resolution to the base version?" "Tving h\u00e5ndtering til opprinnelig versjon?"
::msgcat::mcset nb "Force resolution to this branch?" "Tving h\u00e5ndtering i denne grenen?"
::msgcat::mcset nb "Force resolution to the other branch?" "Tving h\u00e5ndtering i den andre grenen?"
::msgcat::mcset nb "Note that the diff shows only conflicting changes.\n\n%s will be overwritten.\n\nThis operation can be undone only by restarting the merge." "Merk deg at endringsvisningen kun viser motstridende endringer.\n\n%s vil bli overskrevet.\n\nDenne operasjonen kan kun bli angret ved \u00e5 starte sammensl\u00e5ingen p\u00e5 ny."
::msgcat::mcset nb "File %s seems to have unresolved conflicts, still stage?" "Filen %s ser ut til \u00e5 ha ul\u00f8ste konflikter, skal filen likevel k\u00f8es?"
::msgcat::mcset nb "Adding resolution for %s" "Legger til l\u00f8sninge p\u00e5 konflikt for %s"
::msgcat::mcset nb "Conflict file does not exist" "Konfliktfil eksisterer ikke"
::msgcat::mcset nb "Error retrieving versions:\n%s" "Kunne ikke hente versjoner:\n%s"
::msgcat::mcset nb "Restore Defaults" "Gjennopprett standardverdier"
::msgcat::mcset nb "Save" "Lagre"
::msgcat::mcset nb "%s Repository" "%s arkiv"
::msgcat::mcset nb "Global (All Repositories)" "Globalt (alle arkiv)"
::msgcat::mcset nb "User Name" "Navn"
::msgcat::mcset nb "Email Address" "Epost-adresse"
::msgcat::mcset nb "Summarize Merge Commits" "Oppsummer innsjekkinger fra sammensl\u00e5inger"
::msgcat::mcset nb "Merge Verbosity" "Detaljeniv\u00e5 p\u00e5 sammensl\u00e5ing"
::msgcat::mcset nb "Show Diffstat After Merge" "Vis endringsstatistikk etter sammensl\u00e5ing"
::msgcat::mcset nb "Use Merge Tool" "Bruk sammensl\u00e5ingsverkt\u00f8y"
::msgcat::mcset nb "Trust File Modification Timestamps" "Stol p\u00e5 filers tid for endring"
::msgcat::mcset nb "Number of Diff Context Lines" "Antall linjer sammenhengende endringer"
::msgcat::mcset nb "Commit Message Text Width" "Tekstbredde for vindu til innsjekkingsmeldinger"
::msgcat::mcset nb "New Branch Name Template" "Mal for navn p\u00e5 nye grener"
::msgcat::mcset nb "Default File Contents Encoding" "Standard tekstenkoding for innhold i filer"
::msgcat::mcset nb "Change" "Endre"
::msgcat::mcset nb "Spelling Dictionary:" "Stavebokordlister:"
::msgcat::mcset nb "Change Font" "Endre skrifttype"
::msgcat::mcset nb "Choose %s" "Velg %s"
::msgcat::mcset nb "pt." "pt."
::msgcat::mcset nb "Preferences" "Egenskaper"
::msgcat::mcset nb "Failed to completely save options:" "Kunne ikke lagre alternativ:"
::msgcat::mcset nb "Remove Remote" "Fjern fjernarkiv"
::msgcat::mcset nb "Prune from" "Fjern fra"
::msgcat::mcset nb "Fetch from" "Hent fra"
::msgcat::mcset nb "Push to" "Send til"
::msgcat::mcset nb "Add Remote" "Legg til fjernarkiv"
::msgcat::mcset nb "Add New Remote" "Legg til nytt fjernarkiv"
::msgcat::mcset nb "Add" "Legg til"
::msgcat::mcset nb "Remote Details" "Detaljer for fjernarkiv"
::msgcat::mcset nb "Location:" "Lokasjon:"
::msgcat::mcset nb "Further Action" "Videre handling"
::msgcat::mcset nb "Fetch Immediately" "Hent umiddelbart"
::msgcat::mcset nb "Initialize Remote Repository and Push" "Initsialiser og send til fjernarkiv"
::msgcat::mcset nb "Do Nothing Else Now" "Ikke gj\u00f8r mer n\u00e5"
::msgcat::mcset nb "Please supply a remote name." "Vennligst angi et navn for fjernarkivet."
::msgcat::mcset nb "'%s' is not an acceptable remote name." "'%s' er ikke et tillatt navn for et fjernarkiv."
::msgcat::mcset nb "Failed to add remote '%s' of location '%s'." "Kunne ikke legge til fjernarkivet '%s' p\u00e5 '%s'."
::msgcat::mcset nb "fetch %s" "hent %s"
::msgcat::mcset nb "Fetching the %s" "Henter %s"
::msgcat::mcset nb "Do not know how to initialize repository at location '%s'." "Vet ikke hvordan arkiv p\u00e5 '%s' skal opprettes."
::msgcat::mcset nb "push %s" "send %s"
::msgcat::mcset nb "Setting up the %s (at %s)" "Initsialiserer %s (p\u00e5 %s)"
::msgcat::mcset nb "Delete Branch Remotely" "Fjern gren fra fjernarkiv"
::msgcat::mcset nb "From Repository" "Fra arkiv"
::msgcat::mcset nb "Remote:" "Fjernarkiv:"
::msgcat::mcset nb "Arbitrary Location:" "Vilk\u00e5rlig lokasjon:"
::msgcat::mcset nb "Branches" "Grener"
::msgcat::mcset nb "Delete Only If" "Slett kun hvis"
::msgcat::mcset nb "Merged Into:" "Sl\u00e5tt sammen i:"
::msgcat::mcset nb "Always (Do not perform merge checks)" "Alltid (Ikke utf\u00f8r sammensl\u00e5ingskontroll)"
::msgcat::mcset nb "A branch is required for 'Merged Into'." "En gren kreves for 'sammensl\u00e5ing i'."
::msgcat::mcset nb "The following branches are not completely merged into %s:\n\n - %s" "F\u00f8lgende grener er ikke fullestendig sammensl\u00e5tt med %s:\n\n - %s"
::msgcat::mcset nb "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "En eller flere av testene som blir kj\u00f8rt under sammensl\u00e5ing feilet fordi duikke har hentet inn de n\u00f8dvendige innsjekkingene. Pr\u00f8v \u00e5 hent disse fra %sf\u00f8rst"
::msgcat::mcset nb "Please select one or more branches to delete." "Velg en eller flere grener som skal fjernes."
::msgcat::mcset nb "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "Gjenoppretting av fjernede grener er vanskelig.\n\nFjern den merkede grenen?"
::msgcat::mcset nb "Deleting branches from %s" "Fjerner grenene fra %s"
::msgcat::mcset nb "No repository selected." "Ingen arkiv valgt."
::msgcat::mcset nb "Scanning %s..." "S\u00f8ker %s..."
::msgcat::mcset nb "Find:" "Finn:"
::msgcat::mcset nb "Next" "Neste"
::msgcat::mcset nb "Prev" "Forrige"
::msgcat::mcset nb "Case-Sensitive" "Skiller p\u00e5 store og sm\u00e5 bokstaver"
::msgcat::mcset nb "Cannot write shortcut:" "Kan ikke opprette snarvei:"
::msgcat::mcset nb "Cannot write icon:" "Kan ikke opprette ikon:"
::msgcat::mcset nb "Unsupported spell checker" "Stavekontrolleren er ikke st\u00f8ttet"
::msgcat::mcset nb "Spell checking is unavailable" "Stavekontroll er ikke tilgjengelig"
::msgcat::mcset nb "Invalid spell checking configuration" "Ugyldig stavekontroll-konfigurasjon"
::msgcat::mcset nb "Reverting dictionary to %s." "Reverterer ordbok til %s."
::msgcat::mcset nb "Spell checker silently failed on startup" "Stavekontrollen feilet stille under oppstart"
::msgcat::mcset nb "Unrecognized spell checker" "Stavekontrolleren er ukjent"
::msgcat::mcset nb "No Suggestions" "Ingen forslag"
::msgcat::mcset nb "Unexpected EOF from spell checker" "Uventet slutt p\u00e5 filen fra stavekontrollen"
::msgcat::mcset nb "Spell Checker Failed" "Stavekontroll mislyktes"
::msgcat::mcset nb "No keys found." "Ingen n\u00f8kler funnet."
::msgcat::mcset nb "Found a public key in: %s" "Funnet en offentlig n\u00f8kkel i: %s"
::msgcat::mcset nb "Generate Key" "Generer n\u00f8kkel"
::msgcat::mcset nb "Copy To Clipboard" "Kopier til utklippstavlen"
::msgcat::mcset nb "Your OpenSSH Public Key" "Din offentlige OpenSSH-n\u00f8kkel"
::msgcat::mcset nb "Generating..." "Genererer..."
::msgcat::mcset nb "Could not start ssh-keygen:\n\n%s" "Kunne ikke starte ssh-keygen:\n\n%s"
::msgcat::mcset nb "Generation failed." "Generering feilet."
::msgcat::mcset nb "Generation succeeded, but no keys found." "Generering vellykket, men ingen n\u00f8kler er funnet."
::msgcat::mcset nb "Your key is in: %s" "N\u00f8kkelen din ligger i: %s"
::msgcat::mcset nb "%s ... %*i of %*i %s (%3i%%)" "%s ... %*i av %*i %s (%3i%%)"
::msgcat::mcset nb "Running %s requires a selected file." "\u00c5 kj\u00f8re %s krever at en fil er valgt"
::msgcat::mcset nb "Are you sure you want to run %s?" "Er du sikker p\u00e5 at du vil kj\u00f8re %s?"
::msgcat::mcset nb "Tool: %s" "Verkt\u00f8y: %s"
::msgcat::mcset nb "Running: %s" "Kj\u00f8rer: %s"
::msgcat::mcset nb "Tool completed successfully: %s" "Verkt\u00f8yet ble fullf\u00f8rt med suksess: %s"
::msgcat::mcset nb "Tool failed: %s" "Verkt\u00f8y feilet: %s"
::msgcat::mcset nb "Add Tool" "Legg til verkt\u00f8y"
::msgcat::mcset nb "Add New Tool Command" "Legg til ny verkt\u00f8ykommando"
::msgcat::mcset nb "Add globally" "Legg til globalt"
::msgcat::mcset nb "Tool Details" "Verkt\u00f8ydetaljer"
::msgcat::mcset nb "Use '/' separators to create a submenu tree:" "Bruk '/'-separator for \u00e5 lage undermenyer:"
::msgcat::mcset nb "Command:" "Kommando:"
::msgcat::mcset nb "Show a dialog before running" "Vis en dialog f\u00f8r start"
::msgcat::mcset nb "Ask the user to select a revision (sets \$REVISION)" "Sp\u00f8r brukeren om \u00e5 velge en revisjon (setter \$REVISION)"
::msgcat::mcset nb "Ask the user for additional arguments (sets \$ARGS)" "Sp\u00f8r brukeren for ytterligere paramtere (setter \$ARGS)"
::msgcat::mcset nb "Don't show the command output window" "Ikke vis kommandoens utdata i vinduet"
::msgcat::mcset nb "Run only if a diff is selected (\$FILENAME not empty)" "Kj\u00f8r kun om forskjellene er markert (\$FILENAME er ikke tom)"
::msgcat::mcset nb "Please supply a name for the tool." "Vennligst angi et navn for dette verkt\u00f8yet."
::msgcat::mcset nb "Tool '%s' already exists." "Verkt\u00f8yet '%s' eksisterer allerede."
::msgcat::mcset nb "Could not add tool:\n%s" "Kunne ikke legge til verkt\u00f8yet:\n%s"
::msgcat::mcset nb "Remove Tool" "Fjern verkt\u00f8yet"
::msgcat::mcset nb "Remove Tool Commands" "Fjern verkt\u00f8yskommandoen"
::msgcat::mcset nb "Remove" "Fjern"
::msgcat::mcset nb "(Blue denotes repository-local tools)" "(Blue angir lokale verkt\u00f8y til arkivet)"
::msgcat::mcset nb "Run Command: %s" "Kj\u00f8r kommando: %s"
::msgcat::mcset nb "Arguments" "Argumenter"
::msgcat::mcset nb "OK" "OK"
::msgcat::mcset nb "Fetching new changes from %s" "Henter nye endringer fra %s"
::msgcat::mcset nb "remote prune %s" "slett fjernarkiv %s"
::msgcat::mcset nb "Pruning tracking branches deleted from %s" "Fjrner sporing av grener slettet fra %s"
::msgcat::mcset nb "Pushing changes to %s" "Sender endringer til %s"
::msgcat::mcset nb "Pushing %s %s to %s" "Sender %s %s til %s"
::msgcat::mcset nb "Push Branches" "Send grener"
::msgcat::mcset nb "Source Branches" "Kildegrener"
::msgcat::mcset nb "Destination Repository" "Destinasjonsarkiv"
::msgcat::mcset nb "Transfer Options" "Overf\u00f8ringsalternativer"
::msgcat::mcset nb "Force overwrite existing branch (may discard changes)" "Tving overskrivning av eksisterende gren (kan forkaste endringer)"
::msgcat::mcset nb "Use thin pack (for slow network connections)" "Bruk tynne pakker (for tregere nettverkstilkoblinger)"
::msgcat::mcset nb "Include tags" "Inkluder tagger"
