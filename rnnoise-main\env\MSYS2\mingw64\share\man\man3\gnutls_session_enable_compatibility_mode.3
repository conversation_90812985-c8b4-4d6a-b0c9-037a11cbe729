.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_enable_compatibility_mode" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_enable_compatibility_mode \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_session_enable_compatibility_mode(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
This function can be used to disable certain (security) features in
TLS in order to maintain maximum compatibility with buggy
clients. Because several trade\-offs with security are enabled,
if required they will be reported through the audit subsystem.

Normally only servers that require maximum compatibility with
everything out there, need to call this function.

Note that this function must be called after any call to gnutls_priority
functions.
.SH "SINCE"
2.1.4
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
