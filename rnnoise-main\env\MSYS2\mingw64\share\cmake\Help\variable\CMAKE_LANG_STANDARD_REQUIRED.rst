CMAKE_<LANG>_STANDARD_REQUIRED
------------------------------

The variations are:

* :variable:`CMAKE_C_STANDARD_REQUIRED`
* :variable:`CMAKE_CXX_STANDARD_REQUIRED`
* :variable:`CMAKE_CUDA_STANDARD_REQUIRED`
* :variable:`CMAKE_HIP_STANDARD_REQUIRED`
* :variable:`CMAKE_OBJC_STANDARD_REQUIRED`
* :variable:`CMAKE_OBJCXX_STANDARD_REQUIRED`

Default values for :prop_tgt:`<LANG>_STANDARD_REQUIRED` target properties if
set when a target is created.

For supported CMake versions see the respective pages.

See the :manual:`cmake-compile-features(7)` manual for information on
compile features and a list of supported compilers.
