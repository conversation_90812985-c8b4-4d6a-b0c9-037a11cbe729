.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_handshake_get_last_out" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_handshake_get_last_out \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_handshake_description_t gnutls_handshake_get_last_out(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
This function is only useful to check where the last performed
handshake failed.  If the previous handshake succeed or was not
performed at all then no meaningful value will be returned.

Check \fBgnutls_handshake_description_t\fP in gnutls.h for the
available handshake descriptions.
.SH "RETURNS"
the last handshake message type sent, a
\fBgnutls_handshake_description_t\fP.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
