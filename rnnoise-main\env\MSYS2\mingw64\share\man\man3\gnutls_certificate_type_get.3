.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_type_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_type_get \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_certificate_type_t gnutls_certificate_type_get(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
This function returns the type of the certificate that is negotiated
for this side to send to the peer. The certificate type is by default
X.509, unless an alternative certificate type is enabled by
\fBgnutls_init()\fP and negotiated during the session.

Resumed sessions will return the certificate type that was negotiated
and used in the original session.

As of version 3.6.4 it is recommended to use
\fBgnutls_certificate_type_get2()\fP which is more fine\-grained.
.SH "RETURNS"
the currently used \fBgnutls_certificate_type_t\fP certificate
type as negotiated for 'our' side of the connection.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
