/*
 * Copyright 2020 Nikolay Sivov for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "unknwn.idl";
import "audioclient.idl";
import "propsys.idl";

typedef [v1_enum] enum AudioObjectType
{
    AudioObjectType_None             = 0,
    AudioObjectType_Dynamic          = 0x00000001,
    AudioObjectType_FrontLeft        = 0x00000002,
    AudioObjectType_FrontRight       = 0x00000004,
    AudioObjectType_FrontCenter      = 0x00000008,
    AudioObjectType_LowFrequency     = 0x00000010,
    AudioObjectType_SideLeft         = 0x00000020,
    AudioObjectType_SideRight        = 0x00000040,
    AudioObjectType_BackLeft         = 0x00000080,
    AudioObjectType_BackRight        = 0x00000100,
    AudioObjectType_TopFrontLeft     = 0x00000200,
    AudioObjectType_TopFrontRight    = 0x00000400,
    AudioObjectType_TopBackLeft      = 0x00000800,
    AudioObjectType_TopBackRight     = 0x00001000,
    AudioObjectType_BottomFrontLeft  = 0x00002000,
    AudioObjectType_BottomFrontRight = 0x00004000,
    AudioObjectType_BottomBackLeft   = 0x00008000,
    AudioObjectType_BottomBackRight  = 0x00010000,
    AudioObjectType_BackCenter       = 0x00020000,
} AudioObjectType;

cpp_quote("#define SPTLAUDCLNT_E_DESTROYED                     AUDCLNT_ERR(0x100)")
cpp_quote("#define SPTLAUDCLNT_E_OUT_OF_ORDER                  AUDCLNT_ERR(0x101)")
cpp_quote("#define SPTLAUDCLNT_E_RESOURCES_INVALIDATED         AUDCLNT_ERR(0x102)")
cpp_quote("#define SPTLAUDCLNT_E_NO_MORE_OBJECTS               AUDCLNT_ERR(0x103)")
cpp_quote("#define SPTLAUDCLNT_E_PROPERTY_NOT_SUPPORTED        AUDCLNT_ERR(0x104)")
cpp_quote("#define SPTLAUDCLNT_E_ERRORS_IN_OBJECT_CALLS        AUDCLNT_ERR(0x105)")
cpp_quote("#define SPTLAUDCLNT_E_METADATA_FORMAT_NOT_SUPPORTED AUDCLNT_ERR(0x106)")
cpp_quote("#define SPTLAUDCLNT_E_STREAM_NOT_AVAILABLE          AUDCLNT_ERR(0x107)")
cpp_quote("#define SPTLAUDCLNT_E_INVALID_LICENSE               AUDCLNT_ERR(0x108)")
cpp_quote("#define SPTLAUDCLNT_E_STREAM_NOT_STOPPED            AUDCLNT_ERR(0x10a)")
cpp_quote("#define SPTLAUDCLNT_E_STATIC_OBJECT_NOT_AVAILABLE   AUDCLNT_ERR(0x10b)")
cpp_quote("#define SPTLAUDCLNT_E_OBJECT_ALREADY_ACTIVE         AUDCLNT_ERR(0x10c)")
cpp_quote("#define SPTLAUDCLNT_E_INTERNAL                      AUDCLNT_ERR(0x10d)")

interface ISpatialAudioObjectRenderStreamBase;

[
    object,
    uuid(dddf83e6-68d7-4c70-883f-a1836afb4a50),
    pointer_default(unique),
    local
]
interface ISpatialAudioObjectRenderStreamNotify : IUnknown
{
    HRESULT OnAvailableDynamicObjectCountChange(
        [in] ISpatialAudioObjectRenderStreamBase *stream,
        [in] LONGLONG deadline,
        [in] UINT32 object_count);
}

typedef struct tagSpatialAudioObjectRenderStreamActivationParams
{
    const WAVEFORMATEX *ObjectFormat;
    AudioObjectType StaticObjectTypeMask;
    UINT32 MinDynamicObjectCount;
    UINT32 MaxDynamicObjectCount;
    AUDIO_STREAM_CATEGORY Category;
    HANDLE EventHandle;
    ISpatialAudioObjectRenderStreamNotify *NotifyObject;
} SpatialAudioObjectRenderStreamActivationParams;

[
    object,
    uuid(dcdaa858-895a-4a22-a5eb-67bda506096d),
    pointer_default(unique),
    local
]
interface IAudioFormatEnumerator : IUnknown
{
    HRESULT GetCount(
        [out] UINT32 *count);

    HRESULT GetFormat(
        [in] UINT32 index,
        [out] WAVEFORMATEX **format);
}

[
    object,
    uuid(bbf8e066-aaaa-49be-9a4d-fd2a858ea27f),
    pointer_default(unique),
    local
]
interface ISpatialAudioClient : IUnknown
{
    HRESULT GetStaticObjectPosition(
        [in] AudioObjectType type,
        [out] float *x,
        [out] float *y,
        [out] float *z);

    HRESULT GetNativeStaticObjectTypeMask(
        [out] AudioObjectType *mask);

    HRESULT GetMaxDynamicObjectCount(
        [out] UINT32 *value);

    HRESULT GetSupportedAudioObjectFormatEnumerator(
        [out] IAudioFormatEnumerator **enumerator);

    HRESULT GetMaxFrameCount(
        [in] WAVEFORMATEX const *format,
        [out] UINT32 *count);

    HRESULT IsAudioObjectFormatSupported(
        [in] WAVEFORMATEX const *format);

    HRESULT IsSpatialAudioStreamAvailable(
        [in] REFIID stream_uuid,
        [in] PROPVARIANT const *info);

    HRESULT ActivateSpatialAudioStream(
        [in] PROPVARIANT const *params,
        [in] REFIID riid,
        [out, iid_is(riid)] void **stream);
}

[
    object,
    uuid(cce0b8f2-8d4d-4efb-a8cf-3d6ecf1c30e0),
    pointer_default(unique),
    local
]
interface ISpatialAudioObjectBase : IUnknown
{
    HRESULT GetBuffer(
        [out] BYTE **buffer,
        [out] UINT32 *bytes);

    HRESULT SetEndOfStream(
        [in] UINT32 frames);

    HRESULT IsActive(
        [out] BOOL *active);

    HRESULT GetAudioObjectType(
        [out] AudioObjectType *type);
}

[
    object,
    uuid(dde28967-521b-46e5-8f00-bd6f2bc8ab1d),
    pointer_default(unique),
    local
]
interface ISpatialAudioObject : ISpatialAudioObjectBase
{
    HRESULT SetPosition(
        [in] float x,
        [in] float y,
        [in] float z);

    HRESULT SetVolume(
        [in] float vol);
}

[
    object,
    uuid(feaaf403-c1d8-450d-aa05-e0ccee7502a8),
    pointer_default(unique),
    local
]
interface ISpatialAudioObjectRenderStreamBase : IUnknown
{
    HRESULT GetAvailableDynamicObjectCount(
        [out] UINT32 *count);

    HRESULT GetService(
        [in] REFIID riid,
        [out] void **service);

    HRESULT Start();

    HRESULT Stop();

    HRESULT Reset();

    HRESULT BeginUpdatingAudioObjects(
        [out] UINT32 *count,
        [out] UINT32 *frames);

    HRESULT EndUpdatingAudioObjects();
}

[
    object,
    uuid(bab5f473-b423-477b-85f5-b5a332a04153),
    pointer_default(unique),
    local
]
interface ISpatialAudioObjectRenderStream : ISpatialAudioObjectRenderStreamBase
{
    HRESULT ActivateSpatialAudioObject(
        [in] AudioObjectType type,
        [out] ISpatialAudioObject **object);
}
