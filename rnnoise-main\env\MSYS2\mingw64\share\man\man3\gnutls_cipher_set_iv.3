.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_cipher_set_iv" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_cipher_set_iv \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "void gnutls_cipher_set_iv(gnutls_cipher_hd_t " handle ", void * " iv ", size_t " ivlen ");"
.SH ARGUMENTS
.IP "gnutls_cipher_hd_t handle" 12
is a \fBgnutls_cipher_hd_t\fP type
.IP "void * iv" 12
the IV to set
.IP "size_t ivlen" 12
the length of the IV
.SH "DESCRIPTION"
This function will set the IV to be used for the next
encryption block.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
