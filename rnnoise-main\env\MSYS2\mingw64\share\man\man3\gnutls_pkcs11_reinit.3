.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_reinit" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_reinit \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_reinit( " void ");"
.SH ARGUMENTS
.IP " void" 12
.SH "DESCRIPTION"

This function will reinitialize the PKCS 11 subsystem in gnutls. 
This is required by PKCS 11 when an application uses \fBfork()\fP. The
reinitialization function must be called on the child.

Note that since GnuTLS 3.3.0, the reinitialization of the PKCS \fB11\fP
subsystem occurs automatically after fork.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
