.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_send2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_send2 \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "ssize_t gnutls_record_send2(gnutls_session_t " session ", const void * " data ", size_t " data_size ", size_t " pad ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "const void * data" 12
contains the data to send
.IP "size_t data_size" 12
is the length of the data
.IP "size_t pad" 12
padding to be added to the record
.IP "unsigned flags" 12
must be zero
.SH "DESCRIPTION"
This function is identical to \fBgnutls_record_send()\fP except that it
takes an extra argument to specify padding to be added the record.
To determine the maximum size of padding, use
\fBgnutls_record_get_max_size()\fP and \fBgnutls_record_overhead_size()\fP.

Note that in order for GnuTLS to provide constant time processing
of padding and data in TLS1.3, the flag \fBGNUTLS_SAFE_PADDING_CHECK\fP
must be used in \fBgnutls_init()\fP.
.SH "RETURNS"
The number of bytes sent, or a negative error code.  The
number of bytes sent might be less than  \fIdata_size\fP .  The maximum
number of bytes this function can send in a single call depends
on the negotiated maximum record size.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
