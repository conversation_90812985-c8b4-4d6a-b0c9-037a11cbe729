/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

cpp_quote("#include <winapifamily.h>")

cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

#ifndef DO_NO_IMPORTS
import "unknwn.idl";
import "wtypes.idl";
import "prsht.idl";
#endif

cpp_quote("")
cpp_quote("#define NETCFG_E_ALREADY_INITIALIZED MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa020)")
cpp_quote("#define NETCFG_E_NOT_INITIALIZED MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa021)")
cpp_quote("#define NETCFG_E_IN_USE MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa022)")
cpp_quote("#define NETCFG_E_NO_WRITE_LOCK MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa024)")
cpp_quote("#define NETCFG_E_NEED_REBOOT MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa025)")
cpp_quote("#define NETCFG_E_ACTIVE_RAS_CONNECTIONS MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa026)")
cpp_quote("#define NETCFG_E_ADAPTER_NOT_FOUND MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa027)")
cpp_quote("#define NETCFG_E_COMPONENT_REMOVED_PENDING_REBOOT MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa028)")
cpp_quote("#define NETCFG_E_MAX_FILTER_LIMIT MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa029)")
cpp_quote("#define NETCFG_E_VMSWITCH_ACTIVE_OVER_ADAPTER MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa02a)")
cpp_quote("#define NETCFG_E_DUPLICATE_INSTANCEID MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa02b)")

cpp_quote("")
cpp_quote("#define NETCFG_S_REBOOT MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0xa020)")
cpp_quote("#define NETCFG_S_DISABLE_QUERY MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0xa022)")
cpp_quote("#define NETCFG_S_STILL_REFERENCED MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0xa023)")
cpp_quote("#define NETCFG_S_CAUSED_SETUP_CHANGE MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0xa024)")
cpp_quote("#define NETCFG_S_COMMIT_NOW MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0xa025)")

cpp_quote("")
cpp_quote("#define NETCFG_CLIENT_CID_MS_MSClient TEXT(\"ms_msclient\")")
cpp_quote("#define NETCFG_SERVICE_CID_MS_SERVER TEXT(\"ms_server\")")
cpp_quote("#define NETCFG_SERVICE_CID_MS_NETBIOS TEXT(\"ms_netbios\")")
cpp_quote("#define NETCFG_SERVICE_CID_MS_PSCHED TEXT(\"ms_pschedpc\")")
cpp_quote("#define NETCFG_SERVICE_CID_MS_WLBS TEXT(\"ms_wlbs\")")
cpp_quote("#define NETCFG_TRANS_CID_MS_APPLETALK TEXT(\"ms_appletalk\")")
cpp_quote("#define NETCFG_TRANS_CID_MS_NETBEUI TEXT(\"ms_netbeui\")")
cpp_quote("#define NETCFG_TRANS_CID_MS_NETMON TEXT(\"ms_netmon\")")
cpp_quote("#define NETCFG_TRANS_CID_MS_NWIPX TEXT(\"ms_nwipx\")")
cpp_quote("#define NETCFG_TRANS_CID_MS_NWSPX TEXT(\"ms_nwspx\")")
cpp_quote("#define NETCFG_TRANS_CID_MS_TCPIP TEXT(\"ms_tcpip\")")

cpp_quote("")
interface IEnumNetCfgBindingInterface;
interface IEnumNetCfgBindingPath;
interface IEnumNetCfgComponent;
interface INetCfg;
interface INetCfgProperties;
interface INetCfgLock;
interface INetCfgBindingInterface;
interface INetCfgBindingPath;
interface INetCfgComponentBindings;
interface INetCfgBindingPath;
interface INetCfgClass;
interface INetCfgComponent;
interface INetCfgIdentification;
interface INetCfgClassSetup;
interface INetCfgClassSetup2;

cpp_quote("")
[local, object, uuid(c0e8ae90-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface IEnumNetCfgBindingInterface : IUnknown {
  HRESULT Next ([in] ULONG celt, [out, size_is(celt), length_is(*pceltFetched)] INetCfgBindingInterface **rgelt, [out] ULONG *pceltFetched);
  HRESULT Skip ([in] ULONG celt);
  HRESULT Reset ();
  HRESULT Clone ([out] IEnumNetCfgBindingInterface **ppenum);
};

cpp_quote("")
[local, object, uuid(c0e8ae91-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface IEnumNetCfgBindingPath : IUnknown {
  HRESULT Next ([in] ULONG celt, [out, size_is(celt), length_is(*pceltFetched)] INetCfgBindingPath **rgelt, [out] ULONG *pceltFetched);
  HRESULT Skip ([in] ULONG celt);
  HRESULT Reset ();
  HRESULT Clone ([out] IEnumNetCfgBindingPath **ppenum);
};

cpp_quote("")
[local, object, uuid(c0e8ae92-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface IEnumNetCfgComponent : IUnknown {
  HRESULT Next ([in] ULONG celt, [out, size_is(celt), length_is(*pceltFetched)] INetCfgComponent **rgelt, [out] ULONG *pceltFetched);
  HRESULT Skip ([in] ULONG celt);
  HRESULT Reset ();
  HRESULT Clone ([out] IEnumNetCfgComponent **ppenum);
};

cpp_quote("")
[local, object, uuid(c0e8ae93-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface INetCfg : IUnknown {
  HRESULT Initialize ([in] PVOID pvReserved);
  HRESULT Uninitialize ();
  HRESULT Apply ();
  HRESULT Cancel ();
  HRESULT EnumComponents ([in] const GUID *pguidClass, [out] IEnumNetCfgComponent **ppenumComponent);
  HRESULT FindComponent ([in, string] LPCWSTR pszwInfId, [out] INetCfgComponent **pComponent);
  HRESULT QueryNetCfgClass ([in] const GUID *pguidClass, [in] REFIID riid, [out, iid_is(riid)] void **ppvObject);
};

cpp_quote("")
[threading(both), uuid(5b035261-40f9-11d1-aaec-00805fc1270e)]
coclass CNetCfg {
  [default] interface INetCfg;
}

cpp_quote("")
[local, object, uuid(c0e8ae9f-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface INetCfgLock : IUnknown {
  HRESULT AcquireWriteLock ([in] DWORD cmsTimeout, [in, string] LPCWSTR pszwClientDescription, [out, string] LPWSTR *ppszwClientDescription);
  HRESULT ReleaseWriteLock ();
  HRESULT IsWriteLocked ([out, string] LPWSTR *ppszwClientDescription);
};

cpp_quote("")
[local, object, uuid(c0e8ae94-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface INetCfgBindingInterface : IUnknown {
  HRESULT GetName ([out, string] LPWSTR *ppszwInterfaceName);
  HRESULT GetUpperComponent ([out] INetCfgComponent **ppnccItem);
  HRESULT GetLowerComponent ([out] INetCfgComponent **ppnccItem);
};

cpp_quote("")
[local, object, uuid(c0e8ae96-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface INetCfgBindingPath : IUnknown {
  HRESULT IsSamePathAs ([in] INetCfgBindingPath *pPath);
  HRESULT IsSubPathOf ([in] INetCfgBindingPath *pPath);
  HRESULT IsEnabled ();
  HRESULT Enable ([in] WINBOOL fEnable);
  HRESULT GetPathToken([out, string] LPWSTR *ppszwPathToken);
  HRESULT GetOwner ([out] INetCfgComponent **ppComponent);
  HRESULT GetDepth ([out] ULONG *pcInterfaces);
  HRESULT EnumBindingInterfaces ([out] IEnumNetCfgBindingInterface **ppenumInterface);
};

cpp_quote("")
[local, object, uuid(c0e8ae97-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface INetCfgClass : IUnknown {
  HRESULT FindComponent ([in, string] LPCWSTR pszwInfId, [out] INetCfgComponent **ppnccItem);
  HRESULT EnumComponents ([out] IEnumNetCfgComponent **ppenumComponent);
};

cpp_quote("")
[local, object, uuid(c0e8ae9d-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface INetCfgClassSetup : IUnknown {
  typedef enum tagOBO_TOKEN_TYPE {
    OBO_USER = 1,
    OBO_COMPONENT = 2,
    OBO_SOFTWARE = 3
  } OBO_TOKEN_TYPE;

  typedef struct tagOBO_TOKEN {
    OBO_TOKEN_TYPE Type;
    INetCfgComponent *pncc;
    LPCWSTR pszwManufacturer;
    LPCWSTR pszwProduct;
    LPCWSTR pszwDisplayName;
    WINBOOL fRegistered;
  } OBO_TOKEN;

  HRESULT SelectAndInstall ([in] HWND hwndParent, [in] OBO_TOKEN *pOboToken, [out] INetCfgComponent **ppnccItem);
  HRESULT Install ([in, string] LPCWSTR pszwInfId, [in] OBO_TOKEN *pOboToken, [in] DWORD dwSetupFlags, [in] DWORD dwUpgradeFromBuildNo, [in, string] LPCWSTR pszwAnswerFile, [in, string] LPCWSTR pszwAnswerSections, [out] INetCfgComponent **ppnccItem);
  HRESULT DeInstall ([in] INetCfgComponent *pComponent, [in] OBO_TOKEN *pOboToken, [out] LPWSTR *pmszwRefs);
};

cpp_quote("")
[local, object, uuid(c0e8aea0-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface INetCfgClassSetup2: INetCfgClassSetup {
  HRESULT UpdateNonEnumeratedComponent ([in] INetCfgComponent *pIComp, [in] DWORD dwSetupFlags, [in] DWORD dwUpgradeFromBuildNo);
};

cpp_quote("")
[local, object, uuid(c0e8ae99-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface INetCfgComponent : IUnknown {
  typedef enum tagCOMPONENT_CHARACTERISTICS {
    NCF_VIRTUAL = 0x00000001,
    NCF_SOFTWARE_ENUMERATED = 0x00000002,
    NCF_PHYSICAL = 0x00000004,
    NCF_HIDDEN = 0x00000008,
    NCF_NO_SERVICE = 0x00000010,
    NCF_NOT_USER_REMOVABLE = 0x00000020,
    NCF_MULTIPORT_INSTANCED_ADAPTER = 0x00000040,
    NCF_HAS_UI = 0x00000080,
    NCF_SINGLE_INSTANCE = 0x00000100,
    NCF_FILTER = 0x00000400,
    NCF_DONTEXPOSELOWER = 0x00001000,
    NCF_HIDE_BINDING = 0x00002000,
    NCF_NDIS_PROTOCOL = 0x00004000,
    NCF_FIXED_BINDING = 0x00020000,
    NCF_LW_FILTER = 0x00040000
  } COMPONENT_CHARACTERISTICS;

  typedef enum tagNCRP_FLAGS {
    NCRP_QUERY_PROPERTY_UI = 0x00000001,
    NCRP_SHOW_PROPERTY_UI = 0x00000002
  } NCRP_FLAGS;

  HRESULT GetDisplayName ([out, string] LPWSTR *ppszwDisplayName);
  HRESULT SetDisplayName ([in, string] LPCWSTR pszwDisplayName);
  HRESULT GetHelpText ([out, string] LPWSTR *pszwHelpText);
  HRESULT GetId ([out, string] LPWSTR *ppszwId);
  HRESULT GetCharacteristics ([out] LPDWORD pdwCharacteristics);
  HRESULT GetInstanceGuid ([out] GUID *pGuid);
  HRESULT GetPnpDevNodeId ([out, string] LPWSTR *ppszwDevNodeId);
  HRESULT GetClassGuid ([out] GUID *pGuid);
  HRESULT GetBindName ([out, string] LPWSTR *ppszwBindName);
  HRESULT GetDeviceStatus ([out] ULONG *pulStatus);
  HRESULT OpenParamKey ([out] HKEY *phkey);
  HRESULT RaisePropertyUi ([in] HWND hwndParent, [in] DWORD dwFlags, [in] IUnknown *punkContext);
};

cpp_quote("")
[local, object, uuid(c0e8ae9e-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface INetCfgComponentBindings : IUnknown {
  typedef enum tagSUPPORTS_BINDING_INTERFACE_FLAGS {
    NCF_LOWER = 0x01,
    NCF_UPPER = 0x02
  } SUPPORTS_BINDING_INTERFACE_FLAGS;

  typedef enum tagENUM_BINDING_PATHS_FLAGS {
    EBP_ABOVE = 0x01,
    EBP_BELOW = 0x02
  } ENUM_BINDING_PATHS_FLAGS;

  HRESULT BindTo ([in] INetCfgComponent *pnccItem);
  HRESULT UnbindFrom ([in] INetCfgComponent *pnccItem);
  HRESULT SupportsBindingInterface ([in] DWORD dwFlags, [in] LPCWSTR pszwInterfaceName);
  HRESULT IsBoundTo ([in] INetCfgComponent *pnccItem);
  HRESULT IsBindableTo ([in] INetCfgComponent *pnccItem);
  HRESULT EnumBindingPaths ([in] DWORD dwFlags, [out] IEnumNetCfgBindingPath **ppIEnum);
  HRESULT MoveBefore ([in] INetCfgBindingPath *pncbItemSrc, [in] INetCfgBindingPath *pncbItemDest);
  HRESULT MoveAfter ([in] INetCfgBindingPath *pncbItemSrc, [in] INetCfgBindingPath *pncbItemDest);
};

cpp_quote("")
[local, object, uuid(c0e8ae98-306e-11d1-aacf-00805fc1270e), pointer_default(unique)]
interface INetCfgSysPrep : IUnknown {
  HRESULT HrSetupSetFirstDword([in, string] LPCWSTR pwszSection, [in, string] LPCWSTR pwszKey, [in] DWORD dwValue);
  HRESULT HrSetupSetFirstString([in, string] LPCWSTR pwszSection, [in, string] LPCWSTR pwszKey, [in, string] LPCWSTR pwszValue);
  HRESULT HrSetupSetFirstStringAsBool([in, string] LPCWSTR pwszSection, [in, string] LPCWSTR pwszKey, [in] WINBOOL fValue);
  HRESULT HrSetupSetFirstMultiSzField([in, string] LPCWSTR pwszSection, [in, string] LPCWSTR pwszKey, [in, string] LPCWSTR pmszValue);
};

cpp_quote("#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */")
