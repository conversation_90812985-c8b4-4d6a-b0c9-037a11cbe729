.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crl_list_import" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crl_list_import \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crl_list_import(gnutls_x509_crl_t * " crls ", unsigned int * " crl_max ", const gnutls_datum_t * " data ", gnutls_x509_crt_fmt_t " format ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crl_t * crls" 12
Indicates where the parsed CRLs will be copied to. Must not be initialized.
.IP "unsigned int * crl_max" 12
Initially must hold the maximum number of crls. It will be updated with the number of crls available.
.IP "const gnutls_datum_t * data" 12
The CRL data
.IP "gnutls_x509_crt_fmt_t format" 12
One of DER or PEM.
.IP "unsigned int flags" 12
must be (0) or an OR'd sequence of gnutls_certificate_import_flags.
.SH "DESCRIPTION"
This function will convert the given CRL list
to the native gnutls_x509_crl_t format. The output will be stored
in  \fIcrls\fP .  They will be automatically initialized.

If the Certificate is PEM encoded it should have a header of "X509 CRL".
.SH "RETURNS"
the number of certificates read or a negative error value.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
