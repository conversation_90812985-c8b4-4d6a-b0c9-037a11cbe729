.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_check_hostname2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_check_hostname2 \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "unsigned gnutls_x509_crt_check_hostname2(gnutls_x509_crt_t " cert ", const char * " hostname ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain an gnutls_x509_crt_t type
.IP "const char * hostname" 12
A null terminated string that contains a DNS name
.IP "unsigned int flags" 12
gnutls_certificate_verify_flags
.SH "DESCRIPTION"
This function will check if the given certificate's subject matches
the given hostname.  This is a basic implementation of the matching
described in RFC6125, and takes into account wildcards,
and the DNSName/IPAddress subject alternative name PKIX extension.

IPv4 addresses are accepted by this function in the dotted\-decimal
format (e.g, ddd.ddd.ddd.ddd), and IPv6 addresses in the hexadecimal
x:x:x:x:x:x:x:x format. For them the IPAddress subject alternative
name extension is consulted. Previous versions to 3.6.0 of GnuTLS
in case of a non\-match would consult (in a non\-standard extension)
the DNSname and CN fields. This is no longer the case.

When the flag \fBGNUTLS_VERIFY_DO_NOT_ALLOW_WILDCARDS\fP is specified no
wildcards are considered. Otherwise they are only considered if the
domain name consists of three components or more, and the wildcard
starts at the leftmost position.
When the flag \fBGNUTLS_VERIFY_DO_NOT_ALLOW_IP_MATCHES\fP is specified,
the input will be treated as a DNS name, and matching of textual IP addresses
against the IPAddress part of the alternative name will not be allowed.

The function \fBgnutls_x509_crt_check_ip()\fP is available for matching
IP addresses.
.SH "RETURNS"
non\-zero for a successful match, and zero on failure.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
