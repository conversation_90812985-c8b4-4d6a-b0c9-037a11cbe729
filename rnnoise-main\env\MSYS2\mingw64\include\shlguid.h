/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCL<PERSON>IM<PERSON> within this package.
 */
#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION (WINAPI_PARTITION_DESKTOP)

#include <_mingw_unicode.h>

#ifndef _WIN32_IE
#define _WIN32_IE 0x0501
#endif

#define STR_MYDOCS_CLSID "{450D8FBA-AD25-11D0-98A8-0800361B1103}"

#define PSGUID_INTERNETSHORTCUT {0x000214a0, 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46}
#define PSGUID_INTERNETSITE {__MSABI_LONG(0x000214a1), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46}

#define IDefViewID IUnknown

#define IID_IShellCopyHookA IID_ICopyHookA
#define IID_IShellCopyHookW IID_ICopyHookW

#define SID_LinkSite IID_IShellLinkW
#define SID_ShellFolderViewCB IID_IShellFolderViewCB
#define SID_SShellBrowser IID_IShellBrowser
#define SID_SShellDesktop CLSID_ShellDesktop

DEFINE_GUID (CLSID_NetworkDomain, 0x46e06680, 0x4bf0, 0x11d1, 0x83, 0xee, 0x00, 0xa0, 0xc9, 0x0d, 0xc8, 0x49);
DEFINE_GUID (CLSID_NetworkServer, 0xc0542a90, 0x4bf0, 0x11d1, 0x83, 0xee, 0x00, 0xa0, 0xc9, 0x0d, 0xc8, 0x49);
DEFINE_GUID (CLSID_NetworkShare, 0x54a754c0, 0x4bf0, 0x11d1, 0x83, 0xee, 0x00, 0xa0, 0xc9, 0x0d, 0xc8, 0x49);
DEFINE_GUID (CLSID_MyComputer, 0x20d04fe0, 0x3aea, 0x1069, 0xa2, 0xd8, 0x08, 0x00, 0x2b, 0x30, 0x30, 0x9d);
DEFINE_GUID (CLSID_Internet, 0x871c5380, 0x42a0, 0x1069, 0xa2, 0xea, 0x08, 0x00, 0x2b, 0x30, 0x30, 0x9d);
DEFINE_GUID (CLSID_RecycleBin, 0x645ff040, 0x5081, 0x101b, 0x9f, 0x08, 0x00, 0xaa, 0x00, 0x2f, 0x95, 0x4e);
DEFINE_GUID (CLSID_ControlPanel, 0x21ec2020, 0x3aea, 0x1069, 0xa2, 0xdd, 0x08, 0x00, 0x2b, 0x30, 0x30, 0x9d);
DEFINE_GUID (CLSID_Printers, 0x2227a280, 0x3aea, 0x1069, 0xa2, 0xde, 0x08, 0x00, 0x2b, 0x30, 0x30, 0x9d);
DEFINE_GUID (CLSID_MyDocuments, 0x450d8fba, 0xad25, 0x11d0, 0x98, 0xa8, 0x08, 0x00, 0x36, 0x1b, 0x11, 0x03);
DEFINE_GUID (CATID_BrowsableShellExt, __MSABI_LONG(0x00021490), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (CATID_BrowseInPlace, __MSABI_LONG(0x00021491), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (CATID_DeskBand, __MSABI_LONG(0x00021492), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (CATID_InfoBand, __MSABI_LONG(0x00021493), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (CATID_CommBand, __MSABI_LONG(0x00021494), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (FMTID_Intshcut, __MSABI_LONG(0x000214a0), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (FMTID_InternetSite, __MSABI_LONG(0x000214a1), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (CGID_Explorer, __MSABI_LONG(0x000214d0), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (CGID_ShellDocView, __MSABI_LONG(0x000214d1), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (CGID_ShellServiceObject, __MSABI_LONG(0x000214d2), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (CGID_ExplorerBarDoc, __MSABI_LONG(0x000214d3), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_INewShortcutHookA, __MSABI_LONG(0x000214e1), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_IExtractIconA, __MSABI_LONG(0x000214eb), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_IShellDetails, __MSABI_LONG(0x000214ec), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_ICopyHookA, __MSABI_LONG(0x000214ef), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_IFileViewerA, __MSABI_LONG(0x000214f0), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_IFileViewerSite, __MSABI_LONG(0x000214f3), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_IShellExecuteHookA, __MSABI_LONG(0x000214f5), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_IPropSheetPage, __MSABI_LONG(0x000214f6), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_INewShortcutHookW, __MSABI_LONG(0x000214f7), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_IFileViewerW, __MSABI_LONG(0x000214f8), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_IExtractIconW, __MSABI_LONG(0x000214fa), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_IShellExecuteHookW, __MSABI_LONG(0x000214fb), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_ICopyHookW, __MSABI_LONG(0x000214fc), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_IQueryInfo, __MSABI_LONG(0x00021500), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
DEFINE_GUID (IID_IBriefcaseStg, __MSABI_LONG(0x8bce1fa1), 0x0921, 0x101b, 0xb1, 0xff, 0x00, 0xdd, 0x01, 0x0c, 0xcc, 0x48);
DEFINE_GUID (IID_IURLSearchHook, __MSABI_LONG(0xac60f6a0), 0x0fd9, 0x11d0, 0x99, 0xcb, 0x00, 0xc0, 0x4f, 0xd6, 0x44, 0x97);
DEFINE_GUID (IID_ISearchContext, __MSABI_LONG(0x09f656a2), 0x41af, 0x480c, 0x88, 0xf7, 0x16, 0xcc, 0x0d, 0x16, 0x46, 0x15);
DEFINE_GUID (IID_IURLSearchHook2, __MSABI_LONG(0x5ee44da4), 0x6d32, 0x46e3, 0x86, 0xbc, 0x07, 0x54, 0x0d, 0xed, 0xd0, 0xe0);
DEFINE_GUID (IID_IDefViewID, __MSABI_LONG(0x985f64f0), 0xd410, 0x4e02, 0xbe, 0x22, 0xda, 0x07, 0xf2, 0xb5, 0xc5, 0xe1);
DEFINE_GUID (CLSID_FolderShortcut, __MSABI_LONG(0x0afaced1), 0xe828, 0x11d1, 0x91, 0x87, 0xb5, 0x32, 0xf1, 0xe9, 0x57, 0x5d);
DEFINE_GUID (IID_IDockingWindowSite, 0x2a342fc2, 0x7b26, 0x11d0, 0x8c, 0xa9, 0x0, 0xa0, 0xc9, 0x2d, 0xbf, 0xe8);
DEFINE_GUID (IID_IDockingWindowFrame, 0x47d2657a, 0x7b27, 0x11d0, 0x8c, 0xa9, 0x0, 0xa0, 0xc9, 0x2d, 0xbf, 0xe8);
DEFINE_GUID (IID_IShellIconOverlay, __MSABI_LONG(0x7d688a70), 0xc613, 0x11d0, 0x99, 0x9b, 0x00, 0xc0, 0x4f, 0xd6, 0x55, 0xe1);
DEFINE_GUID (IID_IShellIconOverlayIdentifier, __MSABI_LONG(0x0c6c4200), 0xc589, 0x11d0, 0x99, 0x9a, 0x00, 0xc0, 0x4f, 0xd6, 0x55, 0xe1);
DEFINE_GUID (IID_IShellFolderViewCB, __MSABI_LONG(0x2047e320), 0xf2a9, 0x11ce, 0xae, 0x65, 0x08, 0x00, 0x2b, 0x2e, 0x12, 0x62);
DEFINE_GUID (CLSID_CFSIconOverlayManager, __MSABI_LONG(0x63b51f81), 0xc868, 0x11d0, 0x99, 0x9c, 0x00, 0xc0, 0x4f, 0xd6, 0x55, 0xe1);
DEFINE_GUID (IID_IShellIconOverlayManager, __MSABI_LONG(0xf10b5e34), 0xdd3b, 0x42a7, 0xaa, 0x7d, 0x2f, 0x4e, 0xc5, 0x4b, 0xb0, 0x9b);
DEFINE_GUID (IID_IThumbnailCapture, 0x4ea39266, 0x7211, 0x409f, 0xb6, 0x22, 0xf6, 0x3d, 0xbd, 0x16, 0xc5, 0x33);
DEFINE_GUID (IID_IShellImageStore, 0x48c8118c, 0xb924, 0x11d1, 0x98, 0xd5, 0x0, 0xc0, 0x4f, 0xb6, 0x87, 0xda);
#if NTDDI_VERSION < 0x06000000
DEFINE_GUID (CLSID_ShellThumbnailDiskCache, 0x1ebdcf80, 0xa200, 0x11d0, 0xa3, 0xa4, 0x0, 0xc0, 0x4f, 0xd7, 0x6, 0xec);
#endif
DEFINE_GUID (SID_DefView, 0x6d12fe80, 0x7911, 0x11cf, 0x95, 0x34, 0x00, 0x00, 0xc0, 0x5b, 0xae, 0x0b);
DEFINE_GUID (CGID_DefView, 0x4af07f10, 0xd231, 0x11d0, 0xb9, 0x42, 0x0, 0xa0, 0xc9, 0x3, 0x12, 0xe1);
DEFINE_GUID (CLSID_MenuBand, 0x5b4dae26, 0xb807, 0x11d0, 0x98, 0x15, 0x0, 0xc0, 0x4f, 0xd9, 0x19, 0x72);
DEFINE_GUID (IID_IShellFolderBand, 0x7fe80cc8, 0xc247, 0x11d0, 0xb9, 0x3a, 0x0, 0xa0, 0xc9, 0x3, 0x12, 0xe1);
DEFINE_GUID (IID_IDefViewFrame, __MSABI_LONG(0x710eb7a0), 0x45ed, 0x11d0, 0x92, 0x4a, 0x00, 0x20, 0xaf, 0xc7, 0xac, 0x4d);
DEFINE_GUID (VID_LargeIcons, __MSABI_LONG(0x0057d0e0), 0x3573, 0x11cf, 0xae, 0x69, 0x08, 0x00, 0x2b, 0x2e, 0x12, 0x62);
DEFINE_GUID (VID_SmallIcons, __MSABI_LONG(0x089000c0), 0x3573, 0x11cf, 0xae, 0x69, 0x08, 0x00, 0x2b, 0x2e, 0x12, 0x62);
DEFINE_GUID (VID_List, __MSABI_LONG(0x0e1fa5e0), 0x3573, 0x11cf, 0xae, 0x69, 0x08, 0x00, 0x2b, 0x2e, 0x12, 0x62);
DEFINE_GUID (VID_Details, __MSABI_LONG(0x137e7700), 0x3573, 0x11cf, 0xae, 0x69, 0x08, 0x00, 0x2b, 0x2e, 0x12, 0x62);
DEFINE_GUID (VID_Tile, __MSABI_LONG(0x65f125e5), 0x7be1, 0x4810, 0xba, 0x9d, 0xd2, 0x71, 0xc8, 0x43, 0x2c, 0xe3);
DEFINE_GUID (VID_Content, __MSABI_LONG(0x30c2c434), 0x0889, 0x4c8d, 0x98, 0x5d, 0xa9, 0xf7, 0x18, 0x30, 0xb0, 0xa9);
DEFINE_GUID (VID_Thumbnails, 0x8bebb290, 0x52d0, 0x11d0, 0xb7, 0xf4, 0x0, 0xc0, 0x4f, 0xd7, 0x6, 0xec);
DEFINE_GUID (VID_ThumbStrip, 0x8eefa624, 0xd1e9, 0x445b, 0x94, 0xb7, 0x74, 0xfb, 0xce, 0x2e, 0xa1, 0x1a);
DEFINE_GUID (SID_SInPlaceBrowser, 0x1d2ae02b, 0x3655, 0x46cc, 0xb6, 0x3a, 0x28, 0x59, 0x88, 0x15, 0x3b, 0xca);
#if NTDDI_VERSION >= 0x06010000
DEFINE_GUID (SID_SSearchBoxInfo, 0x142daa61, 0x516b, 0x4713, 0xb4, 0x9c, 0xfb, 0x98, 0x5e, 0xf8, 0x29, 0x98);
DEFINE_GUID (SID_CommandsPropertyBag, 0x6e043250, 0x4416, 0x485c, 0xb1, 0x43, 0xe6, 0x2a, 0x76, 0x0d, 0x9f, 0xe5);
#endif
DEFINE_GUID (IID_IDiscardableBrowserProperty, 0x49c3de7c, 0xd329, 0x11d0, 0xab, 0x73, 0x00, 0xc0, 0x4f, 0xc3, 0x3e, 0x80);
DEFINE_GUID (IID_IShellChangeNotify, __MSABI_LONG(0xd82be2b1), 0x5764, 0x11d0, 0xa9, 0x6e, 0x00, 0xc0, 0x4f, 0xd7, 0x05, 0xa2);

#define IID_IFileViewer __MINGW_NAME_AW(IID_IFileViewer)
#define IID_IShellLink __MINGW_NAME_AW(IID_IShellLink)
#define IID_IExtractIcon __MINGW_NAME_AW(IID_IExtractIcon)
#define IID_IShellCopyHook __MINGW_NAME_AW(IID_IShellCopyHook)
#define IID_IShellExecuteHook __MINGW_NAME_AW(IID_IShellExecuteHook)
#define IID_INewShortcutHook __MINGW_NAME_AW(IID_INewShortcutHook)

#ifndef NO_INTSHCUT_GUIDS
#include <isguids.h>
#endif

#ifndef NO_SHDOCVW_GUIDS

#ifndef GUID_DEFS_ONLY
#include <exdisp.h>
#include <shldisp.h>
#endif

#define SID_SInternetExplorer IID_IWebBrowserApp
#define SID_SProgressUI CLSID_ProgressDialog
#define SID_SUrlHistory CLSID_CUrlHistory
#define SID_SWebBrowserApp IID_IWebBrowserApp
#define SID_SWebBrowserEventsService IID_IWebBrowserEventsService

DEFINE_GUID (CLSID_CUrlHistory, __MSABI_LONG(0x3c374a40), 0xbae4, 0x11cf, 0xbf, 0x7d, 0x00, 0xaa, 0x00, 0x69, 0x46, 0xee);
DEFINE_GUID (CLSID_CURLSearchHook, __MSABI_LONG(0xcfbfae00), 0x17a6, 0x11d0, 0x99, 0xcb, 0x00, 0xc0, 0x4f, 0xd6, 0x44, 0x97);
DEFINE_GUID (IID_IObjMgr, __MSABI_LONG(0x00bb2761), 0x6a77, 0x11d0, 0xa5, 0x35, 0x00, 0xc0, 0x4f, 0xd7, 0xd0, 0x62);
DEFINE_GUID (IID_IACList, __MSABI_LONG(0x77a130b0), 0x94fd, 0x11d0, 0xa5, 0x44, 0x00, 0xc0, 0x4f, 0xd7, 0xd0, 0x62);
DEFINE_GUID (IID_IACList2, __MSABI_LONG(0x470141a0), 0x5186, 0x11d2, 0xbb, 0xb6, 0x00, 0x60, 0x97, 0x7b, 0x46, 0x4c);
DEFINE_GUID (IID_ICurrentWorkingDirectory, __MSABI_LONG(0x91956d21), 0x9276, 0x11d1, 0x92, 0x1a, 0x00, 0x60, 0x97, 0xdf, 0x5b, 0xd4);
DEFINE_GUID (CLSID_AutoComplete, __MSABI_LONG(0x00bb2763), 0x6a77, 0x11d0, 0xa5, 0x35, 0x00, 0xc0, 0x4f, 0xd7, 0xd0, 0x62);
DEFINE_GUID (CLSID_ACLHistory, __MSABI_LONG(0x00bb2764), 0x6a77, 0x11d0, 0xa5, 0x35, 0x00, 0xc0, 0x4f, 0xd7, 0xd0, 0x62);
DEFINE_GUID (CLSID_ACListISF, __MSABI_LONG(0x03c036f1), 0xa186, 0x11d0, 0x82, 0x4a, 0x00, 0xaa, 0x00, 0x5b, 0x43, 0x83);
DEFINE_GUID (CLSID_ACLMRU, __MSABI_LONG(0x6756a641), 0xde71, 0x11d0, 0x83, 0x1b, 0x0, 0xaa, 0x0, 0x5b, 0x43, 0x83);
DEFINE_GUID (CLSID_ACLMulti, __MSABI_LONG(0x00bb2765), 0x6a77, 0x11d0, 0xa5, 0x35, 0x00, 0xc0, 0x4f, 0xd7, 0xd0, 0x62);
#if _WIN32_IE >= 0x0600
DEFINE_GUID (CLSID_ACLCustomMRU, 0x6935db93, 0x21e8, 0x4ccc, 0xbe, 0xb9, 0x9f, 0xe3, 0xc7, 0x7a, 0x29, 0x7a);
#endif
DEFINE_GUID (CLSID_ProgressDialog, 0xf8383852, 0xfcd3, 0x11d1, 0xa6, 0xb9, 0x0, 0x60, 0x97, 0xdf, 0x5b, 0xd4);
DEFINE_GUID (IID_IProgressDialog, 0xebbc7c04, 0x315e, 0x11d2, 0xb6, 0x2f, 0x0, 0x60, 0x97, 0xdf, 0x5b, 0xd4);
DEFINE_GUID (SID_STopLevelBrowser, __MSABI_LONG(0x4c96be40), 0x915c, 0x11cf, 0x99, 0xd3, 0x00, 0xaa, 0x00, 0x4a, 0xe8, 0x37);
#endif

#define PSGUID_SHELLDETAILS {0x28636aa6, 0x953d, 0x11d2, 0xb5, 0xd6, 0x0, 0xc0, 0x4f, 0xd9, 0x18, 0xd0}

#define PID_FINDDATA 0
#define PID_NETRESOURCE 1
#define PID_DESCRIPTIONID 2
#define PID_WHICHFOLDER 3
#define PID_NETWORKLOCATION 4
#define PID_COMPUTERNAME 5


#define PID_DISPLAY_PROPERTIES 0
#define PID_INTROTEXT 1
#define PID_SYNC_COPY_IN 2

#define PIDSI_ARTIST 2
#define PIDSI_SONGTITLE 3
#define PIDSI_ALBUM 4
#define PIDSI_YEAR 5
#define PIDSI_COMMENT 6
#define PIDSI_TRACK 7
#define PIDSI_GENRE 11
#define PIDSI_LYRICS 12

#define PID_MISC_STATUS 2
#define PID_MISC_ACCESSCOUNT 3
#define PID_MISC_OWNER 4
#define PID_HTMLINFOTIPFILE 5
#define PID_MISC_PICS 6

#define PIDDRSI_PROTECTED 2
#define PIDDRSI_DESCRIPTION 3
#define PIDDRSI_PLAYCOUNT 4
#define PIDDRSI_PLAYSTARTS 5
#define PIDDRSI_PLAYEXPIRES 6

#define PID_DISPLACED_FROM 2
#define PID_DISPLACED_DATE 3

#define PSGUID_BRIEFCASE {0x328d8b21, 0x7729, 0x4bfc, 0x95, 0x4c, 0x90, 0x2b, 0x32, 0x9d, 0x56, 0xb0}
#define PSGUID_MISC {0x9b174b34, 0x40ff, 0x11d2, 0xa2, 0x7e, 0x0, 0xc0, 0x4f, 0xc3, 0x8, 0x71}
#define PSGUID_WEBVIEW {0xf2275480, 0xf782, 0x4291, 0xbd, 0x94, 0xf1, 0x36, 0x93, 0x51, 0x3a, 0xec}
#define PSGUID_MUSIC {0x56a3372e, 0xce9c, 0x11d2, 0x9f, 0xe, 0x0, 0x60, 0x97, 0xc6, 0x86, 0xf6}
#define PSGUID_DRM {0xaeac19e4, 0x89ae, 0x4508, 0xb9, 0xb7, 0xbb, 0x86, 0x7a, 0xbe, 0xe2, 0xed}
#define PSGUID_VIDEO {0x64440491, 0x4c8b, 0x11d1, 0x8b, 0x70, 0x8, 0x0, 0x36, 0xb1, 0x1a, 0x3}
#define PSGUID_IMAGEPROPERTIES {0x14b81da1, 0x0135, 0x4d31, 0x96, 0xd9, 0x6c, 0xbf, 0xc9, 0x67, 0x1a, 0x99}
#define PSGUID_CUSTOMIMAGEPROPERTIES {0x7ecd8b0e, 0xc136, 0x4a9b, 0x94, 0x11, 0x4e, 0xbd, 0x66, 0x73, 0xcc, 0xc3}
#define PSGUID_LIBRARYPROPERTIES {0x5d76b67f, 0x9b3d, 0x44bb, 0xb6, 0xae, 0x25, 0xda, 0x4f, 0x63, 0x8a, 0x67}
#define PSGUID_DISPLACED {0x9b174b33, 0x40ff, 0x11d2, 0xa2, 0x7e, 0x0, 0xc0, 0x4f, 0xc3, 0x8, 0x71}

DEFINE_GUID (CLSID_FileTypes, 0xb091e540, 0x83e3, 0x11cf, 0xa7, 0x13, 0x00, 0x20, 0xaf, 0xd7, 0x97, 0x62);
DEFINE_GUID (CLSID_ActiveDesktop, __MSABI_LONG(0x75048700), 0xef1f, 0x11d0, 0x98, 0x88, 0x00, 0x60, 0x97, 0xde, 0xac, 0xf9);
DEFINE_GUID (IID_IActiveDesktop, __MSABI_LONG(0xf490eb00), 0x1240, 0x11d1, 0x98, 0x88, 0x00, 0x60, 0x97, 0xde, 0xac, 0xf9);
DEFINE_GUID (IID_IActiveDesktopP, __MSABI_LONG(0x52502ee0), 0xec80, 0x11d0, 0x89, 0xab, 0x00, 0xc0, 0x4f, 0xc2, 0x97, 0x2d);
DEFINE_GUID (IID_IADesktopP2, 0xb22754e2, 0x4574, 0x11d1, 0x98, 0x88, 0x0, 0x60, 0x97, 0xde, 0xac, 0xf9);
DEFINE_GUID (IID_ISynchronizedCallBack, 0x74c26041, 0x70d1, 0x11d1, 0xb7, 0x5a, 0x0, 0xa0, 0xc9, 0x5, 0x64, 0xfe);
DEFINE_GUID (IID_IQueryAssociations, 0xc46ca590, 0x3c3f, 0x11d2, 0xbe, 0xe6, 0x00, 0x00, 0xf8, 0x05, 0xca, 0x57);
DEFINE_GUID (CLSID_QueryAssociations, 0xa07034fd, 0x6caa, 0x4954, 0xac, 0x3f, 0x97, 0xa2, 0x72, 0x16, 0xf9, 0x8a);
DEFINE_GUID (IID_IColumnProvider, 0xe8025004, 0x1c42, 0x11d2, 0xbe, 0x2c, 0x0, 0xa0, 0xc9, 0xa8, 0x3d, 0xa1);
DEFINE_GUID (CLSID_LinkColumnProvider, 0x24f14f02, 0x7b1c, 0x11d1, 0x83, 0x8f, 0x0, 0x0, 0xf8, 0x4, 0x61, 0xcf);
DEFINE_GUID (CGID_ShortCut, 0x93a68750, 0x951a, 0x11d1, 0x94, 0x6f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0);
DEFINE_GUID (IID_INamedPropertyBag, 0xfb700430, 0x952c, 0x11d1, 0x94, 0x6f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0);
DEFINE_GUID (CLSID_InternetButtons, __MSABI_LONG(0x1e796980), 0x9cc5, 0x11d1, 0xa8, 0x3f, 0x00, 0xc0, 0x4f, 0xc9, 0x9d, 0x61);
DEFINE_GUID (CLSID_MSOButtons, 0x178f34b8, 0xa282, 0x11d2, 0x86, 0xc5, 0x0, 0xc0, 0x4f, 0x8e, 0xea, 0x99);
DEFINE_GUID (CLSID_ToolbarExtButtons, 0x2ce4b5d8, 0xa28f, 0x11d2, 0x86, 0xc5, 0x0, 0xc0, 0x4f, 0x8e, 0xea, 0x99);
DEFINE_GUID (CLSID_DarwinAppPublisher, __MSABI_LONG(0xcfccc7a0), 0xa282, 0x11d1, 0x90, 0x82, 0x00, 0x60, 0x08, 0x05, 0x93, 0x82);
DEFINE_GUID (CLSID_DocHostUIHandler, 0x7057e952, 0xbd1b, 0x11d1, 0x89, 0x19, 0x0, 0xc0, 0x4f, 0xc2, 0xc8, 0x36);
DEFINE_GUID (FMTID_ShellDetails, 0x28636aa6, 0x953d, 0x11d2, 0xb5, 0xd6, 0x0, 0xc0, 0x4f, 0xd9, 0x18, 0xd0);
DEFINE_GUID (FMTID_Storage, 0xb725f130, 0x47ef, 0x101a, 0xa5, 0xf1, 0x02, 0x60, 0x8c, 0x9e, 0xeb, 0xac);
DEFINE_GUID (FMTID_ImageProperties, 0x14b81da1, 0x0135, 0x4d31, 0x96, 0xd9, 0x6c, 0xbf, 0xc9, 0x67, 0x1a, 0x99);
DEFINE_GUID (FMTID_CustomImageProperties, 0x7ecd8b0e, 0xc136, 0x4a9b, 0x94, 0x11, 0x4e, 0xbd, 0x66, 0x73, 0xcc, 0xc3);
DEFINE_GUID (FMTID_LibraryProperties, 0x5d76b67f, 0x9b3d, 0x44bb, 0xb6, 0xae, 0x25, 0xda, 0x4f, 0x63, 0x8a, 0x67);
DEFINE_GUID (FMTID_Displaced, 0x9b174b33, 0x40ff, 0x11d2, 0xa2, 0x7e, 0x0, 0xc0, 0x4f, 0xc3, 0x8, 0x71);
DEFINE_GUID (FMTID_Briefcase, 0x328d8b21, 0x7729, 0x4bfc, 0x95, 0x4c, 0x90, 0x2b, 0x32, 0x9d, 0x56, 0xb0);
DEFINE_GUID (FMTID_Misc, 0x9b174b34, 0x40ff, 0x11d2, 0xa2, 0x7e, 0x0, 0xc0, 0x4f, 0xc3, 0x8, 0x71);
DEFINE_GUID (FMTID_WebView, 0xf2275480, 0xf782, 0x4291, 0xbd, 0x94, 0xf1, 0x36, 0x93, 0x51, 0x3a, 0xec);
DEFINE_GUID (FMTID_MUSIC, 0x56a3372e, 0xce9c, 0x11d2, 0x9f, 0xe, 0x0, 0x60, 0x97, 0xc6, 0x86, 0xf6);
DEFINE_GUID (FMTID_DRM, 0xaeac19e4, 0x89ae, 0x4508, 0xb9, 0xb7, 0xbb, 0x86, 0x7a, 0xbe, 0xe2, 0xed);

#define PIDVSI_STREAM_NAME 0x00000002
#define PIDVSI_FRAME_WIDTH 0x00000003
#define PIDVSI_FRAME_HEIGHT 0x00000004
#define PIDVSI_TIMELENGTH 0x00000007
#define PIDVSI_FRAME_COUNT 0x00000005
#define PIDVSI_FRAME_RATE 0x00000006
#define PIDVSI_DATA_RATE 0x00000008
#define PIDVSI_SAMPLE_SIZE 0x00000009
#define PIDVSI_COMPRESSION 0x0000000a
#define PIDVSI_STREAM_NUMBER 0x0000000b

#define PSGUID_AUDIO {0x64440490, 0x4c8b, 0x11d1, 0x8b, 0x70, 0x8, 0x0, 0x36, 0xb1, 0x1a, 0x3}
DEFINE_GUID (FMTID_AudioSummaryInformation, 0x64440490, 0x4c8b, 0x11d1, 0x8b, 0x70, 0x8, 0x0, 0x36, 0xb1, 0x1a, 0x3);

#define PIDASI_FORMAT 0x00000002
#define PIDASI_TIMELENGTH 0x00000003
#define PIDASI_AVG_DATA_RATE 0x00000004
#define PIDASI_SAMPLE_RATE 0x00000005
#define PIDASI_SAMPLE_SIZE 0x00000006
#define PIDASI_CHANNEL_COUNT 0x00000007
#define PIDASI_STREAM_NUMBER 0x00000008
#define PIDASI_STREAM_NAME 0x00000009
#define PIDASI_COMPRESSION 0x0000000a

#define PSGUID_CONTROLPANEL {0x305ca226, 0xd286, 0x468e, 0xb8, 0x48, 0x2b, 0x2e, 0x8e, 0x69, 0x7b, 0x74}
#define PID_CONTROLPANEL_CATEGORY 2

#define PSGUID_VOLUME {0x9b174b35, 0x40ff, 0x11d2, 0xa2, 0x7e, 0x0, 0xc0, 0x4f, 0xc3, 0x8, 0x71}
#define PSGUID_SHARE {0xd8c3986f, 0x813b, 0x449c, 0x84, 0x5d, 0x87, 0xb9, 0x5d, 0x67, 0x4a, 0xde}
#define PSGUID_LINK {0xb9b4b3fc, 0x2b51, 0x4a42, 0xb5, 0xd8, 0x32, 0x41, 0x46, 0xaf, 0xcf, 0x25}
#define PSGUID_QUERY_D {0x49691c90, 0x7e17, 0x101a, 0xa9, 0x1c, 0x08, 0x00, 0x2b, 0x2e, 0xcd, 0xa9}
#define PSGUID_SUMMARYINFORMATION {__MSABI_LONG(0xf29f85e0), 0x4ff9, 0x1068, 0xab, 0x91, 0x08, 0x00, 0x2b, 0x27, 0xb3, 0xd9}
#define PSGUID_DOCUMENTSUMMARYINFORMATION {__MSABI_LONG(0xd5cdd502), 0x2e9c, 0x101b, 0x93, 0x97, 0x08, 0x00, 0x2b, 0x2c, 0xf9, 0xae}
#define PSGUID_MEDIAFILESUMMARYINFORMATION {__MSABI_LONG(0x64440492), 0x4c8b, 0x11d1, 0x8b, 0x70, 0x08, 0x00, 0x36, 0xb1, 0x1a, 0x03}
#define PSGUID_IMAGESUMMARYINFORMATION {__MSABI_LONG(0x6444048f), 0x4c8b, 0x11d1, 0x8b, 0x70, 0x8, 0x00, 0x36, 0xb1, 0x1a, 0x03}

#define PID_VOLUME_FREE 2
#define PID_VOLUME_CAPACITY 3
#define PID_VOLUME_FILESYSTEM 4

#define PID_SHARE_CSC_STATUS 2

#define PID_LINK_TARGET 2
#define PID_LINK_TARGET_TYPE 3

#define PID_QUERY_RANK 2

DEFINE_GUID (FMTID_Volume, 0x9b174b35, 0x40ff, 0x11d2, 0xa2, 0x7e, 0x0, 0xc0, 0x4f, 0xc3, 0x8, 0x71);
DEFINE_GUID (FMTID_Query, 0x49691c90, 0x7e17, 0x101a, 0xa9, 0x1c, 0x08, 0x00, 0x2b, 0x2e, 0xcd, 0xa9);
DEFINE_GUID (FMTID_MediaFileSummaryInformation, 0x64440492, 0x4c8b, 0x11d1, 0x8b, 0x70, 0x08, 0x00, 0x36, 0xb1, 0x1a, 0x03);
DEFINE_GUID (FMTID_ImageSummaryInformation, 0x6444048f, 0x4c8b, 0x11d1, 0x8b, 0x70, 0x8, 0x00, 0x36, 0xb1, 0x1a, 0x03);
DEFINE_GUID (CLSID_HWShellExecute, 0xffb8655f, 0x81b9, 0x4fce, 0xb8, 0x9c, 0x9a, 0x6b, 0xa7, 0x6d, 0x13, 0xe7);
DEFINE_GUID (CLSID_DragDropHelper, 0x4657278a, 0x411b, 0x11d2, 0x83, 0x9a, 0x0, 0xc0, 0x4f, 0xd9, 0x18, 0xd0);
DEFINE_GUID (CLSID_CAnchorBrowsePropertyPage, 0x3050f3bb, 0x98b5, 0x11cf, 0xbb, 0x82, 0x00, 0xaa, 0x00, 0xbd, 0xce, 0x0b);
DEFINE_GUID (CLSID_CImageBrowsePropertyPage, 0x3050f3b3, 0x98b5, 0x11cf, 0xbb, 0x82, 0x00, 0xaa, 0x00, 0xbd, 0xce, 0x0b);
DEFINE_GUID (CLSID_CDocBrowsePropertyPage, 0x3050f3b4, 0x98b5, 0x11cf, 0xbb, 0x82, 0x00, 0xaa, 0x00, 0xbd, 0xce, 0x0b);
/*DEFINE_GUID (IID_IFileSystemBindData, 0x1e18d10, 0x4d8b, 0x11d2, 0x85, 0x5d, 0x0, 0x60, 0x8, 0x5, 0x93, 0x67);
*/
DEFINE_GUID (SID_STopWindow, 0x49e1b500, 0x4636, 0x11d3, 0x97, 0xf7, 0x0, 0xc0, 0x4f, 0x45, 0xd0, 0xb3);
DEFINE_GUID (SID_SGetViewFromViewDual, 0x889a935d, 0x971e, 0x4b12, 0xb9, 0x0c, 0x24, 0xdf, 0xc9, 0xe1, 0xe5, 0xe8);
DEFINE_GUID (CLSID_FolderItem, 0xfef10fa2, 0x355e, 0x4e06, 0x93, 0x81, 0x9b, 0x24, 0xd7, 0xf7, 0xcc, 0x88);
DEFINE_GUID (CLSID_FolderItemsMultiLevel, 0x53c74826, 0xab99, 0x4d33, 0xac, 0xa4, 0x31, 0x17, 0xf5, 0x1d, 0x37, 0x88);
DEFINE_GUID (CLSID_NewMenu, 0xd969a300, 0xe7ff, 0x11d0, 0xa9, 0x3b, 0x0, 0xa0, 0xc9, 0xf, 0x27, 0x19);
DEFINE_GUID (BHID_SFObject, 0x3981e224, 0xf559, 0x11d3, 0x8e, 0x3a, 0x00, 0xc0, 0x4f, 0x68, 0x37, 0xd5);
DEFINE_GUID (BHID_SFUIObject, 0x3981e225, 0xf559, 0x11d3, 0x8e, 0x3a, 0x00, 0xc0, 0x4f, 0x68, 0x37, 0xd5);
DEFINE_GUID (BHID_SFViewObject, 0x3981e226, 0xf559, 0x11d3, 0x8e, 0x3a, 0x00, 0xc0, 0x4f, 0x68, 0x37, 0xd5);
DEFINE_GUID (BHID_Storage, 0x3981e227, 0xf559, 0x11d3, 0x8e, 0x3a, 0x00, 0xc0, 0x4f, 0x68, 0x37, 0xd5);
DEFINE_GUID (BHID_Stream, 0x1cebb3ab, 0x7c10, 0x499a, 0xa4, 0x17, 0x92, 0xca, 0x16, 0xc4, 0xcb, 0x83);
DEFINE_GUID (BHID_RandomAccessStream, 0xf16fc93b, 0x77ae, 0x4cfe, 0xbd, 0xa7, 0xa8, 0x66, 0xee, 0xa6, 0x87, 0x8d);
DEFINE_GUID (BHID_LinkTargetItem, 0x3981e228, 0xf559, 0x11d3, 0x8e, 0x3a, 0x00, 0xc0, 0x4f, 0x68, 0x37, 0xd5);
DEFINE_GUID (BHID_StorageEnum, 0x4621a4e3, 0xf0d6, 0x4773, 0x8a, 0x9c, 0x46, 0xe7, 0x7b, 0x17, 0x48, 0x40);
DEFINE_GUID (BHID_Transfer, 0xd5e346a1, 0xf753, 0x4932, 0xb4, 0x3, 0x45, 0x74, 0x80, 0xe, 0x24, 0x98);
DEFINE_GUID (BHID_PropertyStore, 0x0384e1a4, 0x1523, 0x439c, 0xa4, 0xc8, 0xab, 0x91, 0x10, 0x52, 0xf5, 0x86);
DEFINE_GUID (BHID_ThumbnailHandler, 0x7b2e650a, 0x8e20, 0x4f4a, 0xb0, 0x9e, 0x65, 0x97, 0xaf, 0xc7, 0x2f, 0xb0);
DEFINE_GUID (BHID_EnumItems, 0x94f60519, 0x2850, 0x4924, 0xaa, 0x5a, 0xd1, 0x5e, 0x84, 0x86, 0x80, 0x39);
DEFINE_GUID (BHID_DataObject, 0xb8c0bd9f, 0xed24, 0x455c, 0x83, 0xe6, 0xd5, 0x39, 0xc, 0x4f, 0xe8, 0xc4);
DEFINE_GUID (BHID_AssociationArray, 0xbea9ef17, 0x82f1, 0x4f60, 0x92, 0x84, 0x4f, 0x8d, 0xb7, 0x5c, 0x3b, 0xe9);
DEFINE_GUID (BHID_Filter, 0x38d08778, 0xf557, 0x4690, 0x9e, 0xbf, 0xba, 0x54, 0x70, 0x6a, 0xd8, 0xf7);
DEFINE_GUID (BHID_EnumAssocHandlers, 0xb8ab0b9c, 0xc2ec, 0x4f7a, 0x91, 0x8d, 0x31, 0x49, 0x00, 0xe6, 0x28, 0x0a);
#if _WIN32_IE >= 0x0600
DEFINE_GUID (SID_CtxQueryAssociations, 0xfaadfc40, 0xb777, 0x4b69, 0xaa, 0x81, 0x77, 0x03, 0x5e, 0xf0, 0xe6, 0xe8);
#endif
DEFINE_GUID (IID_IDocViewSite, 0x87d605e0, 0xc511, 0x11cf, 0x89, 0xa9, 0x00, 0xa0, 0xc9, 0x05, 0x41, 0x29);
DEFINE_GUID (CLSID_QuickLinks, 0xe5cbf21, 0xd15f, 0x11d0, 0x83, 0x1, 0x0, 0xaa, 0x0, 0x5b, 0x43, 0x83);
DEFINE_GUID (CLSID_ISFBand, __MSABI_LONG(0xd82be2b0), 0x5764, 0x11d0, 0xa9, 0x6e, 0x00, 0xc0, 0x4f, 0xd7, 0x05, 0xa2);
DEFINE_GUID (IID_CDefView, 0x4434ff80, 0xef4c, 0x11ce, 0xae, 0x65, 0x08, 0x00, 0x2b, 0x2e, 0x12, 0x62);
DEFINE_GUID (CLSID_ShellFldSetExt, 0x6d5313c0, 0x8c62, 0x11d1, 0xb2, 0xcd, 0x00, 0x60, 0x97, 0xdf, 0x8c, 0x11);
DEFINE_GUID (SID_SMenuBandChild, 0xed9cc020, 0x8b9, 0x11d1, 0x98, 0x23, 0x0, 0xc0, 0x4f, 0xd9, 0x19, 0x72);
DEFINE_GUID (SID_SMenuBandParent, 0x8c278eec, 0x3eab, 0x11d1, 0x8c, 0xb0, 0x0, 0xc0, 0x4f, 0xd9, 0x18, 0xd0);
DEFINE_GUID (SID_SMenuPopup, 0xd1e7afeb, 0x6a2e, 0x11d0, 0x8c, 0x78, 0x0, 0xc0, 0x4f, 0xd9, 0x18, 0xb4);
DEFINE_GUID (SID_SMenuBandBottomSelected, 0x165ebaf4, 0x6d51, 0x11d2, 0x83, 0xad, 0x0, 0xc0, 0x4f, 0xd9, 0x18, 0xd0);
DEFINE_GUID (SID_SMenuBandBottom, 0x743ca664, 0xdeb, 0x11d1, 0x98, 0x25, 0x0, 0xc0, 0x4f, 0xd9, 0x19, 0x72);
DEFINE_GUID (SID_MenuShellFolder, 0xa6c17eb4, 0x2d65, 0x11d2, 0x83, 0x8f, 0x0, 0xc0, 0x4f, 0xd9, 0x18, 0xd0);
DEFINE_GUID (SID_SMenuBandContextMenuModifier, 0x39545874, 0x7162, 0x465e, 0xb7, 0x83, 0x2a, 0xa1, 0x87, 0x4f, 0xef, 0x81);
DEFINE_GUID (SID_SMenuBandBKContextMenu, 0x164bbd86, 0x1d0d, 0x4de0, 0x9a, 0x3b, 0xd9, 0x72, 0x96, 0x47, 0xc2, 0xb8);
DEFINE_GUID (CGID_MENUDESKBAR, 0x5c9f0a12, 0x959e, 0x11d0, 0xa3, 0xa4, 0x0, 0xa0, 0xc9, 0x8, 0x26, 0x36);
DEFINE_GUID (SID_SMenuBandTop, 0x9493a810, 0xec38, 0x11d0, 0xbc, 0x46, 0x0, 0xaa, 0x0, 0x6c, 0xe2, 0xf5);
DEFINE_GUID (CLSID_MenuToolbarBase, 0x40b96610, 0xb522, 0x11d1, 0xb3, 0xb4, 0x0, 0xaa, 0x0, 0x6e, 0xfd, 0xe7);
DEFINE_GUID (IID_IBanneredBar, 0x596a9a94, 0x13e, 0x11d1, 0x8d, 0x34, 0x0, 0xa0, 0xc9, 0xf, 0x27, 0x19);
DEFINE_GUID (CLSID_MenuBandSite, 0xe13ef4e4, 0xd2f2, 0x11d0, 0x98, 0x16, 0x0, 0xc0, 0x4f, 0xd9, 0x19, 0x72);
DEFINE_GUID (SID_SCommDlgBrowser, 0x80f30233, 0xb7df, 0x11d2, 0xa3, 0x3b, 0x0, 0x60, 0x97, 0xdf, 0x5b, 0xd4);
DEFINE_GUID (CPFG_LOGON_USERNAME, 0xda15bbe8, 0x954d, 0x4fd3, 0xb0, 0xf4, 0x1f, 0xb5, 0xb9, 0x0b, 0x17, 0x4b);
DEFINE_GUID (CPFG_LOGON_PASSWORD, 0x60624cfa, 0xa477, 0x47b1, 0x8a, 0x8e, 0x3a, 0x4a, 0x19, 0x98, 0x18, 0x27);
DEFINE_GUID (CPFG_SMARTCARD_USERNAME, 0x3e1ecf69, 0x568c, 0x4d96, 0x9d, 0x59, 0x46, 0x44, 0x41, 0x74, 0xe2, 0xd6);
DEFINE_GUID (CPFG_SMARTCARD_PIN, 0x4fe5263b, 0x9181, 0x46c1, 0xb0, 0xa4, 0x9d, 0xed, 0xd4, 0xdb, 0x7d, 0xea);
DEFINE_GUID (CPFG_CREDENTIAL_PROVIDER_LOGO, 0x2d837775, 0xf6cd, 0x464e, 0xa7, 0x45, 0x48, 0x2f, 0xd0, 0xb4, 0x74, 0x93);
DEFINE_GUID (CPFG_CREDENTIAL_PROVIDER_LABEL, 0x286bbff3, 0xbad4, 0x438f, 0xb0, 0x7, 0x79, 0xb7, 0x26, 0x7c, 0x3d, 0x48);

#include <knownfolders.h>

DEFINE_GUID (FOLDERTYPEID_Invalid, 0x57807898, 0x8c4f, 0x4462, 0xbb, 0x63, 0x71, 0x04, 0x23, 0x80, 0xb1, 0x09);
DEFINE_GUID (FOLDERTYPEID_Generic, 0x5c4f28b5, 0xf869, 0x4e84, 0x8e, 0x60, 0xf1, 0x1d, 0xb9, 0x7c, 0x5c, 0xc7);
DEFINE_GUID (FOLDERTYPEID_GenericSearchResults, 0x7fde1a1e, 0x8b31, 0x49a5, 0x93, 0xb8, 0x6b, 0xe1, 0x4c, 0xfa, 0x49, 0x43);
DEFINE_GUID (FOLDERTYPEID_GenericLibrary, 0x5f4eab9a, 0x6833, 0x4f61, 0x89, 0x9d, 0x31, 0xcf, 0x46, 0x97, 0x9d, 0x49);
DEFINE_GUID (FOLDERTYPEID_Documents, 0x7d49d726, 0x3c21, 0x4f05, 0x99, 0xaa, 0xfd, 0xc2, 0xc9, 0x47, 0x46, 0x56);
DEFINE_GUID (FOLDERTYPEID_Pictures, 0xb3690e58, 0xe961, 0x423b, 0xb6, 0x87, 0x38, 0x6e, 0xbf, 0xd8, 0x32, 0x39);
DEFINE_GUID (FOLDERTYPEID_Music, 0x94d6ddcc, 0x4a68, 0x4175, 0xa3, 0x74, 0xbd, 0x58, 0x4a, 0x51, 0x0b, 0x78);
DEFINE_GUID (FOLDERTYPEID_Videos, 0x5fa96407, 0x7e77, 0x483c, 0xac, 0x93, 0x69, 0x1d, 0x05, 0x85, 0x0d, 0xe8);
DEFINE_GUID (FOLDERTYPEID_UserFiles, 0xcd0fc69b, 0x71e2, 0x46e5, 0x96, 0x90, 0x5b, 0xcd, 0x9f, 0x57, 0xaa, 0xb3);
DEFINE_GUID (FOLDERTYPEID_UsersLibraries, 0xc4d98f09, 0x6124, 0x4fe0, 0x99, 0x42, 0x82, 0x64, 0x16, 0x8, 0x2d, 0xa9);
DEFINE_GUID (FOLDERTYPEID_OtherUsers, 0xb337fd00, 0x9dd5, 0x4635, 0xa6, 0xd4, 0xda, 0x33, 0xfd, 0x10, 0x2b, 0x7a);
DEFINE_GUID (FOLDERTYPEID_PublishedItems, 0x7f2f5b96, 0xff74, 0x41da, 0xaf, 0xd8, 0x1c, 0x78, 0xa5, 0xf3, 0xae, 0xa2);
DEFINE_GUID (FOLDERTYPEID_Communications, 0x91475fe5, 0x586b, 0x4eba, 0x8d, 0x75, 0xd1, 0x74, 0x34, 0xb8, 0xcd, 0xf6);
DEFINE_GUID (FOLDERTYPEID_Contacts, 0xde2b70ec, 0x9bf7, 0x4a93, 0xbd, 0x3d, 0x24, 0x3f, 0x78, 0x81, 0xd4, 0x92);
DEFINE_GUID (FOLDERTYPEID_StartMenu, 0xef87b4cb, 0xf2ce, 0x4785, 0x86, 0x58, 0x4c, 0xa6, 0xc6, 0x3e, 0x38, 0xc6);
DEFINE_GUID (FOLDERTYPEID_RecordedTV, 0x5557a28f, 0x5da6, 0x4f83, 0x88, 0x09, 0xc2, 0xc9, 0x8a, 0x11, 0xa6, 0xfa);
DEFINE_GUID (FOLDERTYPEID_SavedGames, 0xd0363307, 0x28cb, 0x4106, 0x9f, 0x23, 0x29, 0x56, 0xe3, 0xe5, 0xe0, 0xe7);
DEFINE_GUID (FOLDERTYPEID_OpenSearch, 0x8faf9629, 0x1980, 0x46ff, 0x80, 0x23, 0x9d, 0xce, 0xab, 0x9c, 0x3e, 0xe3);
DEFINE_GUID (FOLDERTYPEID_SearchConnector, 0x982725ee, 0x6f47, 0x479e, 0xb4, 0x47, 0x81, 0x2b, 0xfa, 0x7d, 0x2e, 0x8f);
DEFINE_GUID (FOLDERTYPEID_AccountPictures, 0xdb2a5d8f, 0x06e6, 0x4007, 0xab, 0xa6, 0xaf, 0x87, 0x7d, 0x52, 0x6e, 0xa6);
DEFINE_GUID (FOLDERTYPEID_Games, 0xb689b0d0, 0x76d3, 0x4cbb, 0x87, 0xf7, 0x58, 0x5d, 0x0e, 0x0c, 0xe0, 0x70);
DEFINE_GUID (FOLDERTYPEID_ControlPanelCategory, 0xde4f0660, 0xfa10, 0x4b8f, 0xa4, 0x94, 0x06, 0x8b, 0x20, 0xb2, 0x23, 0x07);
DEFINE_GUID (FOLDERTYPEID_ControlPanelClassic, 0x0c3794f3, 0xb545, 0x43aa, 0xa3, 0x29, 0xc3, 0x74, 0x30, 0xc5, 0x8d, 0x2a);
DEFINE_GUID (FOLDERTYPEID_Printers, 0x2c7bbec6, 0xc844, 0x4a0a, 0x91, 0xfa, 0xce, 0xf6, 0xf5, 0x9c, 0xfd, 0xa1);
DEFINE_GUID (FOLDERTYPEID_RecycleBin, 0xd6d9e004, 0xcd87, 0x442b, 0x9d, 0x57, 0x5e, 0x0a, 0xeb, 0x4f, 0x6f, 0x72);
DEFINE_GUID (FOLDERTYPEID_SoftwareExplorer, 0xd674391b, 0x52d9, 0x4e07, 0x83, 0x4e, 0x67, 0xc9, 0x86, 0x10, 0xf3, 0x9d);
DEFINE_GUID (FOLDERTYPEID_CompressedFolder, 0x80213e82, 0xbcfd, 0x4c4f, 0x88, 0x17, 0xbb, 0x27, 0x60, 0x12, 0x67, 0xa9);
DEFINE_GUID (FOLDERTYPEID_NetworkExplorer, 0x25cc242b, 0x9a7c, 0x4f51, 0x80, 0xe0, 0x7a, 0x29, 0x28, 0xfe, 0xbe, 0x42);
DEFINE_GUID (FOLDERTYPEID_Searches, 0x0b0ba2e3, 0x405f, 0x415e, 0xa6, 0xee, 0xca, 0xd6, 0x25, 0x20, 0x78, 0x53);
DEFINE_GUID (FOLDERTYPEID_SearchHome, 0x834d8a44, 0x0974, 0x4ed6, 0x86, 0x6e, 0xf2, 0x03, 0xd8, 0x0b, 0x38, 0x10);
DEFINE_GUID (SYNCMGR_OBJECTID_Icon, 0x6dbc85c3, 0x5d07, 0x4c72, 0xa7, 0x77, 0x7f, 0xec, 0x78, 0x7, 0x2c, 0x6);
DEFINE_GUID (SYNCMGR_OBJECTID_EventStore, 0x4bef34b9, 0xa786, 0x4075, 0xba, 0x88, 0xc, 0x2b, 0x9d, 0x89, 0xa9, 0x8f);
DEFINE_GUID (SYNCMGR_OBJECTID_ConflictStore, 0xd78181f4, 0x2389, 0x47e4, 0xa9, 0x60, 0x60, 0xbc, 0xc2, 0xed, 0x93, 0xb);
DEFINE_GUID (SYNCMGR_OBJECTID_BrowseContent, 0x57cbb584, 0xe9b4, 0x47ae, 0xa1, 0x20, 0xc4, 0xdf, 0x33, 0x35, 0xde, 0xe2);
DEFINE_GUID (SYNCMGR_OBJECTID_ShowSchedule, 0xedc6f3e3, 0x8441, 0x4109, 0xad, 0xf3, 0x6c, 0x1c, 0xa0, 0xb7, 0xde, 0x47);
DEFINE_GUID (SYNCMGR_OBJECTID_QueryBeforeActivate, 0xd882d80b, 0xe7aa, 0x49ed, 0x86, 0xb7, 0xe6, 0xe1, 0xf7, 0x14, 0xcd, 0xfe);
DEFINE_GUID (SYNCMGR_OBJECTID_QueryBeforeDeactivate, 0xa0efc282, 0x60e0, 0x460e, 0x93, 0x74, 0xea, 0x88, 0x51, 0x3c, 0xfc, 0x80);
DEFINE_GUID (SYNCMGR_OBJECTID_QueryBeforeEnable, 0x4cbf7f0, 0x5beb, 0x4de1, 0xbc, 0x90, 0x90, 0x83, 0x45, 0xc4, 0x80, 0xf6);
DEFINE_GUID (SYNCMGR_OBJECTID_QueryBeforeDisable, 0xbb5f64aa, 0xf004, 0x4eb5, 0x8e, 0x4d, 0x26, 0x75, 0x19, 0x66, 0x34, 0x4c);
DEFINE_GUID (SYNCMGR_OBJECTID_QueryBeforeDelete, 0xf76c3397, 0xafb3, 0x45d7, 0xa5, 0x9f, 0x5a, 0x49, 0xe9, 0x5, 0x43, 0x7e);
DEFINE_GUID (SYNCMGR_OBJECTID_EventLinkClick, 0x2203bdc1, 0x1af1, 0x4082, 0x8c, 0x30, 0x28, 0x39, 0x9f, 0x41, 0x38, 0x4c);
DEFINE_GUID (EP_NavPane, 0xcb316b22, 0x25f7, 0x42b8, 0x8a, 0x09, 0x54, 0x0d, 0x23, 0xa4, 0x3c, 0x2f);
DEFINE_GUID (EP_Commands, 0xd9745868, 0xca5f, 0x4a76, 0x91, 0xcd, 0xf5, 0xa1, 0x29, 0xfb, 0xb0, 0x76);
DEFINE_GUID (EP_Commands_Organize, 0x72e81700, 0xe3ec, 0x4660, 0xbf, 0x24, 0x3c, 0x3b, 0x7b, 0x64, 0x88, 0x06);
DEFINE_GUID (EP_Commands_View, 0x21f7c32d, 0xeeaa, 0x439b, 0xbb, 0x51, 0x37, 0xb9, 0x6f, 0xd6, 0xa9, 0x43);
DEFINE_GUID (EP_DetailsPane, 0x43abf98b, 0x89b8, 0x472d, 0xb9, 0xce, 0xe6, 0x9b, 0x82, 0x29, 0xf0, 0x19);
DEFINE_GUID (EP_PreviewPane, 0x893c63d1, 0x45c8, 0x4d17, 0xbe, 0x19, 0x22, 0x3b, 0xe7, 0x1b, 0xe3, 0x65);
DEFINE_GUID (EP_QueryPane, 0x65bcde4f, 0x4f07, 0x4f27, 0x83, 0xa7, 0x1a, 0xfc, 0xa4, 0xdf, 0x7d, 0xdd);
DEFINE_GUID (EP_AdvQueryPane, 0xb4e9db8b, 0x34ba, 0x4c39, 0xb5, 0xcc, 0x16, 0xa1, 0xbd, 0x2c, 0x41, 0x1c);
DEFINE_GUID (EP_StatusBar, 0x65fe56ce, 0x5cfe, 0x4bc4, 0xad, 0x8a, 0x7a, 0xe3, 0xfe, 0x7e, 0x8f, 0x7c);
DEFINE_GUID (EP_Ribbon, 0xd27524a8, 0xc9f2, 0x4834, 0xa1, 0x6, 0xdf, 0x88, 0x89, 0xfd, 0x4f, 0x37);
DEFINE_GUID (CATID_LocationFactory, 0x965c4d51, 0x8b76, 0x4e57, 0x80, 0xb7, 0x56, 0x4d, 0x2e, 0xa4, 0xb5, 0x5e);
DEFINE_GUID (CATID_LocationProvider, 0x1b3ca474, 0x2614, 0x414b, 0xb8, 0x13, 0x1a, 0xce, 0xca, 0x3e, 0x3d, 0xd8);
DEFINE_GUID (ItemCount_Property_GUID, 0xabbf5c45, 0x5ccc, 0x47b7, 0xbb, 0x4e, 0x87, 0xcb, 0x87, 0xbb, 0xd1, 0x62);
DEFINE_GUID (SelectedItemCount_Property_GUID, 0x8fe316d2, 0xe52, 0x460a, 0x9c, 0x1e, 0x48, 0xf2, 0x73, 0xd4, 0x70, 0xa3);
DEFINE_GUID (ItemIndex_Property_GUID, 0x92a053da, 0x2969, 0x4021, 0xbf, 0x27, 0x51, 0x4c, 0xfc, 0x2e, 0x4a, 0x69);
DEFINE_GUID (CATID_SearchableApplication, 0x366c292a, 0xd9b3, 0x4dbf, 0xbb, 0x70, 0xe6, 0x2e, 0xc3, 0xd0, 0xbb, 0xbf);

/* Commented out to ...
  DEFINE_GUID (IID_IShellBrowser, __MSABI_LONG(0x000214e2), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_IShellView, __MSABI_LONG(0x000214e3), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_IContextMenu, __MSABI_LONG(0x000214e4), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_IShellIcon, __MSABI_LONG(0x000214e5), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_IShellFolder, __MSABI_LONG(0x000214e6), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_IShellExtInit, __MSABI_LONG(0x000214e8), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_IShellPropSheetExt, __MSABI_LONG(0x000214e9), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_IPersistFolder, __MSABI_LONG(0x000214ea), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_IShellLinkA, __MSABI_LONG(0x000214ee), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_ICommDlgBrowser, __MSABI_LONG(0x000214f1), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_IEnumIDList, __MSABI_LONG(0x000214f2), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_IContextMenu2, __MSABI_LONG(0x000214f4), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_IShellLinkW, __MSABI_LONG(0x000214f9), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_IRemoteComputer, __MSABI_LONG(0x000214fe), 0, 0, 0xc0, 0, 0, 0, 0, 0, 0, 0x46);
  DEFINE_GUID (IID_IShellView2, __MSABI_LONG(0x88e39e80), 0x3578, 0x11cf, 0xae, 0x69, 0x08, 0x00, 0x2b, 0x2e, 0x12, 0x62);
  DEFINE_GUID (IID_ICommDlgBrowser2, 0x10339516, 0x2894, 0x11d2, 0x90, 0x39, 0x0, 0xc0, 0x4f, 0x8e, 0xeb, 0x3e);
  DEFINE_GUID (IID_IContextMenu3, 0xbcfce0a0, 0xec17, 0x11d0, 0x8d, 0x10, 0x0, 0xa0, 0xc9, 0xf, 0x27, 0x19);
  DEFINE_GUID (IID_IShellFolder2, 0x93f2f68c, 0x1d1b, 0x11d3, 0xa3, 0xe, 0x0, 0xc0, 0x4f, 0x79, 0xab, 0xd1);
  DEFINE_GUID (IID_IEnumExtraSearch, 0xe700be1, 0x9db6, 0x11d1, 0xa1, 0xce, 0x0, 0xc0, 0x4f, 0xd7, 0x5d, 0x13);

  ... avoid double definitions. See shobjidl.h header.  */

#endif
