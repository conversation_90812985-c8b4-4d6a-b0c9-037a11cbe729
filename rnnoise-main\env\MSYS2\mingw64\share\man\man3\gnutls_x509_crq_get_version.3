.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_version" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_version \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_version(gnutls_x509_crq_t " crq ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.SH "DESCRIPTION"
This function will return the version of the specified Certificate
request.
.SH "RETURNS"
version of certificate request, or a negative error code on
error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
