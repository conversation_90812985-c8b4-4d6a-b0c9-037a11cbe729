// <experimental/map> -*- C++ -*-

// Copyright (C) 2015-2025 Free Software Foundation, Inc.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file experimental/map
 *  This is a TS C++ Library header.
 *  @ingroup libfund-ts
 */

#ifndef _GLIBCXX_EXPERIMENTAL_MAP
#define _GLIBCXX_EXPERIMENTAL_MAP 1

#ifdef _GLIBCXX_SYSHDR
#pragma GCC system_header
#endif

#include <bits/requires_hosted.h> // experimental is currently omitted

#if __cplusplus >= 201402L

#include <map>
#include <bits/erase_if.h>
#include <experimental/memory_resource>

namespace std _GLIBCXX_VISIBILITY(default)
{
_GLIBCXX_BEGIN_NAMESPACE_VERSION

namespace experimental
{
inline namespace fundamentals_v2
{
  template<typename _Key, typename _Tp, typename _Compare, typename _Alloc,
	   typename _Predicate>
    inline void
    erase_if(map<_Key, _Tp, _Compare, _Alloc>& __cont, _Predicate __pred)
    {
      _GLIBCXX_STD_C::map<_Key, _Tp, _Compare, _Alloc>& __ucont = __cont;
      std::__detail::__erase_nodes_if(__cont, __ucont, __pred);
    }

  template<typename _Key, typename _Tp, typename _Compare, typename _Alloc,
	   typename _Predicate>
    inline void
    erase_if(multimap<_Key, _Tp, _Compare, _Alloc>& __cont, _Predicate __pred)
    {
      _GLIBCXX_STD_C::multimap<_Key, _Tp, _Compare, _Alloc>& __ucont = __cont;
      std::__detail::__erase_nodes_if(__cont, __ucont, __pred);
    }

  namespace pmr {
    template<typename _Key, typename _Tp, typename _Compare = less<_Key>>
      using map
      = std::map<_Key, _Tp, _Compare,
		 polymorphic_allocator<pair<const _Key, _Tp>>>;

    template<typename _Key, typename _Tp, typename _Compare = less<_Key>>
      using multimap
      = std::multimap<_Key, _Tp, _Compare,
		      polymorphic_allocator<pair<const _Key, _Tp>>>;
  } // namespace pmr
} // namespace fundamentals_v2
} // namespace experimental

_GLIBCXX_END_NAMESPACE_VERSION
} // namespace std

#endif // C++14

#endif // _GLIBCXX_EXPERIMENTAL_MAP
