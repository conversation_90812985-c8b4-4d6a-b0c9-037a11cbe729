------------------------------------------------------------------------
-- dqCopyAbs.decTest -- quiet decQuad copy and set sign to zero       --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- All operands and results are decQuads.
extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- Sanity check
dqcpa001 copyabs       +7.50  -> 7.50

-- Infinities
dqcpa011 copyabs  Infinity    -> Infinity
dqcpa012 copyabs  -Infinity   -> Infinity

-- NaNs, 0 payload
dqcpa021 copyabs         NaN  -> NaN
dqcpa022 copyabs        -NaN  -> NaN
dqcpa023 copyabs        sNaN  -> sNaN
dqcpa024 copyabs       -sNaN  -> sNaN

-- NaNs, non-0 payload
dqcpa031 copyabs       NaN10  -> NaN10
dqcpa032 copyabs      -NaN15  -> NaN15
dqcpa033 copyabs      sNaN15  -> sNaN15
dqcpa034 copyabs     -sNaN10  -> sNaN10
dqcpa035 copyabs       NaN7   -> NaN7
dqcpa036 copyabs      -NaN7   -> NaN7
dqcpa037 copyabs      sNaN101 -> sNaN101
dqcpa038 copyabs     -sNaN101 -> sNaN101

-- finites
dqcpa101 copyabs          7   -> 7
dqcpa102 copyabs         -7   -> 7
dqcpa103 copyabs         75   -> 75
dqcpa104 copyabs        -75   -> 75
dqcpa105 copyabs       7.10   -> 7.10
dqcpa106 copyabs      -7.10   -> 7.10
dqcpa107 copyabs       7.500  -> 7.500
dqcpa108 copyabs      -7.500  -> 7.500

-- zeros
dqcpa111 copyabs          0   -> 0
dqcpa112 copyabs         -0   -> 0
dqcpa113 copyabs       0E+6   -> 0E+6
dqcpa114 copyabs      -0E+6   -> 0E+6
dqcpa115 copyabs     0.0000   -> 0.0000
dqcpa116 copyabs    -0.0000   -> 0.0000
dqcpa117 copyabs      0E-141  -> 0E-141
dqcpa118 copyabs     -0E-141  -> 0E-141

-- full coefficients, alternating bits
dqcpa121 copyabs   2682682682682682682682682682682682    ->  2682682682682682682682682682682682
dqcpa122 copyabs  -2682682682682682682682682682682682    ->  2682682682682682682682682682682682
dqcpa123 copyabs   1341341341341341341341341341341341    ->  1341341341341341341341341341341341
dqcpa124 copyabs  -1341341341341341341341341341341341    ->  1341341341341341341341341341341341

-- Nmax, Nmin, Ntiny
dqcpa131 copyabs  9.999999999999999999999999999999999E+6144   ->  9.999999999999999999999999999999999E+6144
dqcpa132 copyabs  1E-6143                                     ->  1E-6143
dqcpa133 copyabs  1.000000000000000000000000000000000E-6143   ->  1.000000000000000000000000000000000E-6143
dqcpa134 copyabs  1E-6176                                     ->  1E-6176

dqcpa135 copyabs  -1E-6176                                    ->  1E-6176
dqcpa136 copyabs  -1.000000000000000000000000000000000E-6143  ->  1.000000000000000000000000000000000E-6143
dqcpa137 copyabs  -1E-6143                                    ->  1E-6143
dqcpa138 copyabs  -9.999999999999999999999999999999999E+6144  ->  9.999999999999999999999999999999999E+6144
