.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_verify" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_verify \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_verify(gnutls_x509_crq_t " crq ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
is the crq to be verified
.IP "unsigned int flags" 12
Flags that may be used to change the verification algorithm. Use OR of the gnutls_certificate_verify_flags enumerations.
.SH "DESCRIPTION"
This function will verify self signature in the certificate
request and return its status.
.SH "RETURNS"
In case of a verification failure \fBGNUTLS_E_PK_SIG_VERIFY_FAILED\fP 
is returned, and zero or positive code on success.

Since 2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
