.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_handshake_set_private_extensions" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_handshake_set_private_extensions \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_handshake_set_private_extensions(gnutls_session_t " session ", int " allow ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "int allow" 12
is an integer (0 or 1)
.SH "DESCRIPTION"
This function will enable or disable the use of private cipher
suites (the ones that start with 0xFF).  By default or if  \fIallow\fP is 0 then these cipher suites will not be advertised nor used.

Currently GnuTLS does not include such cipher\-suites or
compression algorithms.

Enabling the private ciphersuites when talking to other than
gnutls servers and clients may cause interoperability problems.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
