<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/filter.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('filter_8h.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">filter.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Common filter related types and functions.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__filter.html">lzma_filter</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter options.  <a href="structlzma__filter.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:ab33c0cc1728bf390e5b84f8bce1928ba" id="r_ab33c0cc1728bf390e5b84f8bce1928ba"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab33c0cc1728bf390e5b84f8bce1928ba">LZMA_FILTERS_MAX</a>&#160;&#160;&#160;4</td></tr>
<tr class="memdesc:ab33c0cc1728bf390e5b84f8bce1928ba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum number of filters in a chain.  <br /></td></tr>
<tr class="separator:ab33c0cc1728bf390e5b84f8bce1928ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41aa51eeb53190404439c31d8e9c97cd" id="r_a41aa51eeb53190404439c31d8e9c97cd"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a41aa51eeb53190404439c31d8e9c97cd">LZMA_STR_ALL_FILTERS</a>&#160;&#160;&#160;UINT32_C(0x01)</td></tr>
<tr class="memdesc:a41aa51eeb53190404439c31d8e9c97cd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Allow or show all filters.  <br /></td></tr>
<tr class="separator:a41aa51eeb53190404439c31d8e9c97cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc33f4c0c7b5d3ae36acc0437a904339" id="r_adc33f4c0c7b5d3ae36acc0437a904339"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adc33f4c0c7b5d3ae36acc0437a904339">LZMA_STR_NO_VALIDATION</a>&#160;&#160;&#160;UINT32_C(0x02)</td></tr>
<tr class="memdesc:adc33f4c0c7b5d3ae36acc0437a904339"><td class="mdescLeft">&#160;</td><td class="mdescRight">Do not validate the filter chain in <a class="el" href="#aa042cf11749bc2183b27de1c3142da30" title="Convert a string to a filter chain.">lzma_str_to_filters()</a>  <br /></td></tr>
<tr class="separator:adc33f4c0c7b5d3ae36acc0437a904339"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09a775f6a78d28ca136acfb51ad5fa02" id="r_a09a775f6a78d28ca136acfb51ad5fa02"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a09a775f6a78d28ca136acfb51ad5fa02">LZMA_STR_ENCODER</a>&#160;&#160;&#160;UINT32_C(0x10)</td></tr>
<tr class="memdesc:a09a775f6a78d28ca136acfb51ad5fa02"><td class="mdescLeft">&#160;</td><td class="mdescRight">Stringify encoder options.  <br /></td></tr>
<tr class="separator:a09a775f6a78d28ca136acfb51ad5fa02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a0f3fc03bdb84a294cdd53a98783104" id="r_a8a0f3fc03bdb84a294cdd53a98783104"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8a0f3fc03bdb84a294cdd53a98783104">LZMA_STR_DECODER</a>&#160;&#160;&#160;UINT32_C(0x20)</td></tr>
<tr class="memdesc:a8a0f3fc03bdb84a294cdd53a98783104"><td class="mdescLeft">&#160;</td><td class="mdescRight">Stringify decoder options.  <br /></td></tr>
<tr class="separator:a8a0f3fc03bdb84a294cdd53a98783104"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a87e9ac4ae5829b092262223256141a29" id="r_a87e9ac4ae5829b092262223256141a29"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a87e9ac4ae5829b092262223256141a29">LZMA_STR_GETOPT_LONG</a>&#160;&#160;&#160;UINT32_C(0x40)</td></tr>
<tr class="memdesc:a87e9ac4ae5829b092262223256141a29"><td class="mdescLeft">&#160;</td><td class="mdescRight">Produce xz-compatible getopt_long() syntax.  <br /></td></tr>
<tr class="separator:a87e9ac4ae5829b092262223256141a29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac0113c47caf98a735db2297936c5e857" id="r_ac0113c47caf98a735db2297936c5e857"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac0113c47caf98a735db2297936c5e857">LZMA_STR_NO_SPACES</a>&#160;&#160;&#160;UINT32_C(0x80)</td></tr>
<tr class="memdesc:ac0113c47caf98a735db2297936c5e857"><td class="mdescLeft">&#160;</td><td class="mdescRight">Use two dashes "--" instead of a space to separate filters.  <br /></td></tr>
<tr class="separator:ac0113c47caf98a735db2297936c5e857"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a3db3c36cd6e57658a74c53e4daa2bef6" id="r_a3db3c36cd6e57658a74c53e4daa2bef6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3db3c36cd6e57658a74c53e4daa2bef6">lzma_filter_encoder_is_supported</a> (<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> id) lzma_nothrow lzma_attr_const</td></tr>
<tr class="memdesc:a3db3c36cd6e57658a74c53e4daa2bef6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Test if the given Filter ID is supported for encoding.  <br /></td></tr>
<tr class="separator:a3db3c36cd6e57658a74c53e4daa2bef6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acab0c67bf5b3a76f2b474c8e1da98938" id="r_acab0c67bf5b3a76f2b474c8e1da98938"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acab0c67bf5b3a76f2b474c8e1da98938">lzma_filter_decoder_is_supported</a> (<a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> id) lzma_nothrow lzma_attr_const</td></tr>
<tr class="memdesc:acab0c67bf5b3a76f2b474c8e1da98938"><td class="mdescLeft">&#160;</td><td class="mdescRight">Test if the given Filter ID is supported for decoding.  <br /></td></tr>
<tr class="separator:acab0c67bf5b3a76f2b474c8e1da98938"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a611fe1176eeeda187b1bd8aef45040aa" id="r_a611fe1176eeeda187b1bd8aef45040aa"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a611fe1176eeeda187b1bd8aef45040aa">lzma_filters_copy</a> (const <a class="el" href="structlzma__filter.html">lzma_filter</a> *src, <a class="el" href="structlzma__filter.html">lzma_filter</a> *dest, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a611fe1176eeeda187b1bd8aef45040aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy the filters array.  <br /></td></tr>
<tr class="separator:a611fe1176eeeda187b1bd8aef45040aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae06979d219897f5f4c29cbc7a96a8892" id="r_ae06979d219897f5f4c29cbc7a96a8892"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae06979d219897f5f4c29cbc7a96a8892">lzma_filters_free</a> (<a class="el" href="structlzma__filter.html">lzma_filter</a> *filters, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator) lzma_nothrow</td></tr>
<tr class="memdesc:ae06979d219897f5f4c29cbc7a96a8892"><td class="mdescLeft">&#160;</td><td class="mdescRight">Free the options in the array of <a class="el" href="structlzma__filter.html" title="Filter options.">lzma_filter</a> structures.  <br /></td></tr>
<tr class="separator:ae06979d219897f5f4c29cbc7a96a8892"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a730f9391e85a5979bcd1b32643ae7176" id="r_a730f9391e85a5979bcd1b32643ae7176"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a730f9391e85a5979bcd1b32643ae7176">lzma_raw_encoder_memusage</a> (const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filters) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a730f9391e85a5979bcd1b32643ae7176"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate approximate memory requirements for raw encoder.  <br /></td></tr>
<tr class="separator:a730f9391e85a5979bcd1b32643ae7176"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a58511249ae9206d7de7c5d1f05842297" id="r_a58511249ae9206d7de7c5d1f05842297"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a58511249ae9206d7de7c5d1f05842297">lzma_raw_decoder_memusage</a> (const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filters) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a58511249ae9206d7de7c5d1f05842297"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate approximate memory requirements for raw decoder.  <br /></td></tr>
<tr class="separator:a58511249ae9206d7de7c5d1f05842297"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2368e4129032345eb0738b0c6e085703" id="r_a2368e4129032345eb0738b0c6e085703"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2368e4129032345eb0738b0c6e085703">lzma_raw_encoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filters) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a2368e4129032345eb0738b0c6e085703"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize raw encoder.  <br /></td></tr>
<tr class="separator:a2368e4129032345eb0738b0c6e085703"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae77b3b6c5eccd9d77bbafef0a8a203c1" id="r_ae77b3b6c5eccd9d77bbafef0a8a203c1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae77b3b6c5eccd9d77bbafef0a8a203c1">lzma_raw_decoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filters) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:ae77b3b6c5eccd9d77bbafef0a8a203c1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize raw decoder.  <br /></td></tr>
<tr class="separator:ae77b3b6c5eccd9d77bbafef0a8a203c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4a8fd969df001e449ebe4421ab33bba5" id="r_a4a8fd969df001e449ebe4421ab33bba5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4a8fd969df001e449ebe4421ab33bba5">lzma_filters_update</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filters) lzma_nothrow</td></tr>
<tr class="memdesc:a4a8fd969df001e449ebe4421ab33bba5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Update the filter chain in the encoder.  <br /></td></tr>
<tr class="separator:a4a8fd969df001e449ebe4421ab33bba5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a226724ab3391b410281fdf656cc7c432" id="r_a226724ab3391b410281fdf656cc7c432"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a226724ab3391b410281fdf656cc7c432">lzma_raw_buffer_encode</a> (const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filters, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator, const uint8_t *in, size_t in_size, uint8_t *out, size_t *out_pos, size_t out_size) lzma_nothrow</td></tr>
<tr class="memdesc:a226724ab3391b410281fdf656cc7c432"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-call raw encoder.  <br /></td></tr>
<tr class="separator:a226724ab3391b410281fdf656cc7c432"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b942df507e4f9a6d7525e5a4c6864e5" id="r_a3b942df507e4f9a6d7525e5a4c6864e5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3b942df507e4f9a6d7525e5a4c6864e5">lzma_raw_buffer_decode</a> (const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filters, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator, const uint8_t *in, size_t *in_pos, size_t in_size, uint8_t *out, size_t *out_pos, size_t out_size) lzma_nothrow</td></tr>
<tr class="memdesc:a3b942df507e4f9a6d7525e5a4c6864e5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-call raw decoder.  <br /></td></tr>
<tr class="separator:a3b942df507e4f9a6d7525e5a4c6864e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aee038818cf7bbe044c3f7a7c86998c1b" id="r_aee038818cf7bbe044c3f7a7c86998c1b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aee038818cf7bbe044c3f7a7c86998c1b">lzma_properties_size</a> (uint32_t *size, const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filter) lzma_nothrow</td></tr>
<tr class="memdesc:aee038818cf7bbe044c3f7a7c86998c1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the size of the Filter Properties field.  <br /></td></tr>
<tr class="separator:aee038818cf7bbe044c3f7a7c86998c1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e00887086df5a44084ac22e48415de3" id="r_a8e00887086df5a44084ac22e48415de3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8e00887086df5a44084ac22e48415de3">lzma_properties_encode</a> (const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filter, uint8_t *props) lzma_nothrow</td></tr>
<tr class="memdesc:a8e00887086df5a44084ac22e48415de3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Encode the Filter Properties field.  <br /></td></tr>
<tr class="separator:a8e00887086df5a44084ac22e48415de3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88d2e864b2039ac82802cc202278d478" id="r_a88d2e864b2039ac82802cc202278d478"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a88d2e864b2039ac82802cc202278d478">lzma_properties_decode</a> (<a class="el" href="structlzma__filter.html">lzma_filter</a> *filter, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator, const uint8_t *props, size_t props_size) lzma_nothrow</td></tr>
<tr class="memdesc:a88d2e864b2039ac82802cc202278d478"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decode the Filter Properties field.  <br /></td></tr>
<tr class="separator:a88d2e864b2039ac82802cc202278d478"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a996c9c21840ed54e37bd1f664a79d940" id="r_a996c9c21840ed54e37bd1f664a79d940"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a996c9c21840ed54e37bd1f664a79d940">lzma_filter_flags_size</a> (uint32_t *size, const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filter) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a996c9c21840ed54e37bd1f664a79d940"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate encoded size of a Filter Flags field.  <br /></td></tr>
<tr class="separator:a996c9c21840ed54e37bd1f664a79d940"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a96f23309bc21398fece18c00ebe7db98" id="r_a96f23309bc21398fece18c00ebe7db98"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a96f23309bc21398fece18c00ebe7db98">lzma_filter_flags_encode</a> (const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filter, uint8_t *out, size_t *out_pos, size_t out_size) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a96f23309bc21398fece18c00ebe7db98"><td class="mdescLeft">&#160;</td><td class="mdescRight">Encode Filter Flags into given buffer.  <br /></td></tr>
<tr class="separator:a96f23309bc21398fece18c00ebe7db98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4cba9a4c658cce0ff01fd102b31ea1a7" id="r_a4cba9a4c658cce0ff01fd102b31ea1a7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4cba9a4c658cce0ff01fd102b31ea1a7">lzma_filter_flags_decode</a> (<a class="el" href="structlzma__filter.html">lzma_filter</a> *filter, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator, const uint8_t *in, size_t *in_pos, size_t in_size) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a4cba9a4c658cce0ff01fd102b31ea1a7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decode Filter Flags from given buffer.  <br /></td></tr>
<tr class="separator:a4cba9a4c658cce0ff01fd102b31ea1a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa042cf11749bc2183b27de1c3142da30" id="r_aa042cf11749bc2183b27de1c3142da30"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa042cf11749bc2183b27de1c3142da30">lzma_str_to_filters</a> (const char *str, int *error_pos, <a class="el" href="structlzma__filter.html">lzma_filter</a> *filters, uint32_t flags, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:aa042cf11749bc2183b27de1c3142da30"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a string to a filter chain.  <br /></td></tr>
<tr class="separator:aa042cf11749bc2183b27de1c3142da30"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7deeb86ef59a9111b8033681290e0fb0" id="r_a7deeb86ef59a9111b8033681290e0fb0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7deeb86ef59a9111b8033681290e0fb0">lzma_str_from_filters</a> (char **str, const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filters, uint32_t flags, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a7deeb86ef59a9111b8033681290e0fb0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a filter chain to a string.  <br /></td></tr>
<tr class="separator:a7deeb86ef59a9111b8033681290e0fb0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab51585b68796ce0270f87e615b923809" id="r_ab51585b68796ce0270f87e615b923809"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab51585b68796ce0270f87e615b923809">lzma_str_list_filters</a> (char **str, <a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a> filter_id, uint32_t flags, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:ab51585b68796ce0270f87e615b923809"><td class="mdescLeft">&#160;</td><td class="mdescRight">List available filters and/or their options (for help message)  <br /></td></tr>
<tr class="separator:ab51585b68796ce0270f87e615b923809"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Common filter related types and functions. </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead. </dd></dl>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="ab33c0cc1728bf390e5b84f8bce1928ba" name="ab33c0cc1728bf390e5b84f8bce1928ba"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab33c0cc1728bf390e5b84f8bce1928ba">&#9670;&#160;</a></span>LZMA_FILTERS_MAX</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_FILTERS_MAX&#160;&#160;&#160;4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum number of filters in a chain. </p>
<p>A filter chain can have 1-4 filters, of which three are allowed to change the size of the data. Usually only one or two filters are needed. </p>

</div>
</div>
<a id="a41aa51eeb53190404439c31d8e9c97cd" name="a41aa51eeb53190404439c31d8e9c97cd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a41aa51eeb53190404439c31d8e9c97cd">&#9670;&#160;</a></span>LZMA_STR_ALL_FILTERS</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_STR_ALL_FILTERS&#160;&#160;&#160;UINT32_C(0x01)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Allow or show all filters. </p>
<p>By default only the filters supported in the .xz format are accept by <a class="el" href="#aa042cf11749bc2183b27de1c3142da30" title="Convert a string to a filter chain.">lzma_str_to_filters()</a> or shown by <a class="el" href="#ab51585b68796ce0270f87e615b923809" title="List available filters and/or their options (for help message)">lzma_str_list_filters()</a>. </p>

</div>
</div>
<a id="adc33f4c0c7b5d3ae36acc0437a904339" name="adc33f4c0c7b5d3ae36acc0437a904339"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adc33f4c0c7b5d3ae36acc0437a904339">&#9670;&#160;</a></span>LZMA_STR_NO_VALIDATION</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_STR_NO_VALIDATION&#160;&#160;&#160;UINT32_C(0x02)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Do not validate the filter chain in <a class="el" href="#aa042cf11749bc2183b27de1c3142da30" title="Convert a string to a filter chain.">lzma_str_to_filters()</a> </p>
<p>By default <a class="el" href="#aa042cf11749bc2183b27de1c3142da30" title="Convert a string to a filter chain.">lzma_str_to_filters()</a> can return an error if the filter chain as a whole isn't usable in the .xz format or in the raw encoder or decoder. With this flag, this validation is skipped. This flag doesn't affect the handling of the individual filter options. To allow non-.xz filters also LZMA_STR_ALL_FILTERS is needed. </p>

</div>
</div>
<a id="a09a775f6a78d28ca136acfb51ad5fa02" name="a09a775f6a78d28ca136acfb51ad5fa02"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a09a775f6a78d28ca136acfb51ad5fa02">&#9670;&#160;</a></span>LZMA_STR_ENCODER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_STR_ENCODER&#160;&#160;&#160;UINT32_C(0x10)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Stringify encoder options. </p>
<p>Show the filter-specific options that the encoder will use. This may be useful for verbose diagnostic messages.</p>
<p>Note that if options were decoded from .xz headers then the encoder options may be undefined. This flag shouldn't be used in such a situation. </p>

</div>
</div>
<a id="a8a0f3fc03bdb84a294cdd53a98783104" name="a8a0f3fc03bdb84a294cdd53a98783104"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8a0f3fc03bdb84a294cdd53a98783104">&#9670;&#160;</a></span>LZMA_STR_DECODER</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_STR_DECODER&#160;&#160;&#160;UINT32_C(0x20)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Stringify decoder options. </p>
<p>Show the filter-specific options that the decoder will use. This may be useful for showing what filter options were decoded from file headers. </p>

</div>
</div>
<a id="a87e9ac4ae5829b092262223256141a29" name="a87e9ac4ae5829b092262223256141a29"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a87e9ac4ae5829b092262223256141a29">&#9670;&#160;</a></span>LZMA_STR_GETOPT_LONG</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_STR_GETOPT_LONG&#160;&#160;&#160;UINT32_C(0x40)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Produce xz-compatible getopt_long() syntax. </p>
<p>That is, "delta:dist=2 lzma2:dict=4MiB,pb=1,lp=1" becomes "--delta=dist=2 --lzma2=dict=4MiB,pb=1,lp=1".</p>
<p>This syntax is compatible with xz 5.0.0 as long as the filters and their options are supported too. </p>

</div>
</div>
<a id="ac0113c47caf98a735db2297936c5e857" name="ac0113c47caf98a735db2297936c5e857"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac0113c47caf98a735db2297936c5e857">&#9670;&#160;</a></span>LZMA_STR_NO_SPACES</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_STR_NO_SPACES&#160;&#160;&#160;UINT32_C(0x80)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Use two dashes "--" instead of a space to separate filters. </p>
<p>That is, "delta:dist=2 lzma2:pb=1,lp=1" becomes "delta:dist=2--lzma2:pb=1,lp=1". This looks slightly odd but this kind of strings should be usable on the command line without quoting. However, it is possible that future versions with new filter options might produce strings that require shell quoting anyway as the exact set of possible characters isn't frozen for now.</p>
<p>It is guaranteed that the single quote (') will never be used in filter chain strings (even if LZMA_STR_NO_SPACES isn't used). </p>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="a3db3c36cd6e57658a74c53e4daa2bef6" name="a3db3c36cd6e57658a74c53e4daa2bef6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3db3c36cd6e57658a74c53e4daa2bef6">&#9670;&#160;</a></span>lzma_filter_encoder_is_supported()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a> lzma_filter_encoder_is_supported </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a></td>          <td class="paramname"><span class="paramname"><em>id</em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Test if the given Filter ID is supported for encoding. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">id</td><td>Filter ID</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59" title="Boolean.">lzma_bool</a>:<ul>
<li>true if the Filter ID is supported for encoding by this liblzma build.</li>
<li>false otherwise. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="acab0c67bf5b3a76f2b474c8e1da98938" name="acab0c67bf5b3a76f2b474c8e1da98938"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acab0c67bf5b3a76f2b474c8e1da98938">&#9670;&#160;</a></span>lzma_filter_decoder_is_supported()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a> lzma_filter_decoder_is_supported </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a></td>          <td class="paramname"><span class="paramname"><em>id</em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Test if the given Filter ID is supported for decoding. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">id</td><td>Filter ID</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59" title="Boolean.">lzma_bool</a>:<ul>
<li>true if the Filter ID is supported for decoding by this liblzma build.</li>
<li>false otherwise. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a611fe1176eeeda187b1bd8aef45040aa" name="a611fe1176eeeda187b1bd8aef45040aa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a611fe1176eeeda187b1bd8aef45040aa">&#9670;&#160;</a></span>lzma_filters_copy()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_filters_copy </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>src</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>dest</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Copy the filters array. </p>
<p>Copy the Filter IDs and filter-specific options from src to dest. Up to LZMA_FILTERS_MAX filters are copied, plus the terminating .id == LZMA_VLI_UNKNOWN. Thus, dest should have at least LZMA_FILTERS_MAX + 1 elements space unless the caller knows that src is smaller than that.</p>
<p>Unless the filter-specific options is NULL, the Filter ID has to be supported by liblzma, because liblzma needs to know the size of every filter-specific options structure. The filter-specific options are not validated. If options is NULL, any unsupported Filter IDs are copied without returning an error.</p>
<p>Old filter-specific options in dest are not freed, so dest doesn't need to be initialized by the caller in any way.</p>
<p>If an error occurs, memory possibly already allocated by this function is always freed. liblzma versions older than 5.2.7 may modify the dest array and leave its contents in an undefined state if an error occurs. liblzma 5.2.7 and newer only modify the dest array when returning LZMA_OK.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">src</td><td>Array of filters terminated with .id == LZMA_VLI_UNKNOWN. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">dest</td><td>Destination filter array </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free().</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_OPTIONS_ERROR: Unsupported Filter ID and its options is not NULL.</li>
<li>LZMA_PROG_ERROR: src or dest is NULL. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="ae06979d219897f5f4c29cbc7a96a8892" name="ae06979d219897f5f4c29cbc7a96a8892"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae06979d219897f5f4c29cbc7a96a8892">&#9670;&#160;</a></span>lzma_filters_free()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void lzma_filters_free </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filters</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Free the options in the array of <a class="el" href="structlzma__filter.html" title="Filter options.">lzma_filter</a> structures. </p>
<p>This frees the filter chain options. The filters array itself is not freed.</p>
<p>The filters array must have at most LZMA_FILTERS_MAX + 1 elements including the terminating element which must have .id = LZMA_VLI_UNKNOWN. For all elements before the terminating element:</p><ul>
<li>options will be freed using the given <a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> or, if allocator is NULL, using free().</li>
<li>options will be set to NULL.</li>
<li>id will be set to LZMA_VLI_UNKNOWN.</li>
</ul>
<p>If filters is NULL, this does nothing. Again, this never frees the filters array itself.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">filters</td><td>Array of filters terminated with .id == LZMA_VLI_UNKNOWN. </td></tr>
    <tr><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a730f9391e85a5979bcd1b32643ae7176" name="a730f9391e85a5979bcd1b32643ae7176"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a730f9391e85a5979bcd1b32643ae7176">&#9670;&#160;</a></span>lzma_raw_encoder_memusage()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_raw_encoder_memusage </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filters</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculate approximate memory requirements for raw encoder. </p>
<p>This function can be used to calculate the memory requirements for Block and Stream encoders too because Block and Stream encoders don't need significantly more memory than raw encoder.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">filters</td><td>Array of filters terminated with .id == LZMA_VLI_UNKNOWN.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Number of bytes of memory required for the given filter chain when encoding or UINT64_MAX on error. </dd></dl>

</div>
</div>
<a id="a58511249ae9206d7de7c5d1f05842297" name="a58511249ae9206d7de7c5d1f05842297"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a58511249ae9206d7de7c5d1f05842297">&#9670;&#160;</a></span>lzma_raw_decoder_memusage()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_raw_decoder_memusage </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filters</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculate approximate memory requirements for raw decoder. </p>
<p>This function can be used to calculate the memory requirements for Block and Stream decoders too because Block and Stream decoders don't need significantly more memory than raw decoder.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">filters</td><td>Array of filters terminated with .id == LZMA_VLI_UNKNOWN.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Number of bytes of memory required for the given filter chain when decoding or UINT64_MAX on error. </dd></dl>

</div>
</div>
<a id="a2368e4129032345eb0738b0c6e085703" name="a2368e4129032345eb0738b0c6e085703"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2368e4129032345eb0738b0c6e085703">&#9670;&#160;</a></span>lzma_raw_encoder()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_raw_encoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filters</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initialize raw encoder. </p>
<p>This function may be useful when implementing custom file formats.</p>
<p>The 'action' with <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> can be LZMA_RUN, LZMA_SYNC_FLUSH (if the filter chain supports it), or LZMA_FINISH.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">filters</td><td>Array of filters terminated with .id == LZMA_VLI_UNKNOWN.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="ae77b3b6c5eccd9d77bbafef0a8a203c1" name="ae77b3b6c5eccd9d77bbafef0a8a203c1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae77b3b6c5eccd9d77bbafef0a8a203c1">&#9670;&#160;</a></span>lzma_raw_decoder()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_raw_decoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filters</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initialize raw decoder. </p>
<p>The initialization of raw decoder goes similarly to raw encoder.</p>
<p>The 'action' with <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> can be LZMA_RUN or LZMA_FINISH. Using LZMA_FINISH is not required, it is supported just for convenience.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">filters</td><td>Array of filters terminated with .id == LZMA_VLI_UNKNOWN.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a4a8fd969df001e449ebe4421ab33bba5" name="a4a8fd969df001e449ebe4421ab33bba5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4a8fd969df001e449ebe4421ab33bba5">&#9670;&#160;</a></span>lzma_filters_update()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_filters_update </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filters</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Update the filter chain in the encoder. </p>
<p>This function may be called after <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> has returned LZMA_STREAM_END when LZMA_FULL_BARRIER, LZMA_FULL_FLUSH, or LZMA_SYNC_FLUSH was used:</p>
<ul>
<li>After LZMA_FULL_BARRIER or LZMA_FULL_FLUSH: Single-threaded .xz Stream encoder (<a class="el" href="container_8h.html#a1a97aec94c9fedd7646cfa51c4f4cd52" title="Initialize .xz Stream encoder using a custom filter chain.">lzma_stream_encoder()</a>) and (since liblzma 5.4.0) multi-threaded Stream encoder (<a class="el" href="container_8h.html#a3f8793518711ee84d1abf12ea3aaba42" title="Initialize multithreaded .xz Stream encoder.">lzma_stream_encoder_mt()</a>) allow setting a new filter chain to be used for the next Block(s).</li>
<li>After LZMA_SYNC_FLUSH: Raw encoder (<a class="el" href="#a2368e4129032345eb0738b0c6e085703" title="Initialize raw encoder.">lzma_raw_encoder()</a>), Block encoder (<a class="el" href="block_8h.html#a2218a49025a0b44f9a6f9d6d24359359" title="Initialize .xz Block encoder.">lzma_block_encoder()</a>), and single-threaded .xz Stream encoder (<a class="el" href="container_8h.html#a1a97aec94c9fedd7646cfa51c4f4cd52" title="Initialize .xz Stream encoder using a custom filter chain.">lzma_stream_encoder()</a>) allow changing certain filter-specific options in the middle of encoding. The actual filters in the chain (Filter IDs) must not be changed! Currently only the lc, lp, and pb options of LZMA2 (not LZMA1) can be changed this way.</li>
<li>In the future some filters might allow changing some of their options without any barrier or flushing but currently such filters don't exist.</li>
</ul>
<p>This function may also be called when no data has been compressed yet although this is rarely useful. In that case, this function will behave as if LZMA_FULL_FLUSH (Stream encoders) or LZMA_SYNC_FLUSH (Raw or Block encoder) had been used right before calling this function.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">filters</td><td>Array of filters terminated with .id == LZMA_VLI_UNKNOWN.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_MEMLIMIT_ERROR</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a226724ab3391b410281fdf656cc7c432" name="a226724ab3391b410281fdf656cc7c432"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a226724ab3391b410281fdf656cc7c432">&#9670;&#160;</a></span>lzma_raw_buffer_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_raw_buffer_encode </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filters</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>in_size</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>out</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>out_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>out_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Single-call raw encoder. </p>
<dl class="section note"><dt>Note</dt><dd>There is no function to calculate how big output buffer would surely be big enough. (<a class="el" href="container_8h.html#a66d4366a47b8332bff2a512f44f5c45e" title="Calculate output buffer size for single-call Stream encoder.">lzma_stream_buffer_bound()</a> works only for <a class="el" href="container_8h.html#a6e645ccaeace3b13a6981e03c6e190ad" title="Single-call .xz Stream encoder.">lzma_stream_buffer_encode()</a>; raw encoder won't necessarily meet that bound.)</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">filters</td><td>Array of filters terminated with .id == LZMA_VLI_UNKNOWN. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_size</td><td>Size of the input buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out_pos</td><td>The next byte will be written to out[*out_pos]. *out_pos is updated only if encoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">out_size</td><td>Size of the out buffer; the first byte into which no data is written to is out[out_size].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Encoding was successful.</li>
<li>LZMA_BUF_ERROR: Not enough output buffer space.</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_DATA_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a3b942df507e4f9a6d7525e5a4c6864e5" name="a3b942df507e4f9a6d7525e5a4c6864e5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3b942df507e4f9a6d7525e5a4c6864e5">&#9670;&#160;</a></span>lzma_raw_buffer_decode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_raw_buffer_decode </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filters</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>in_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>in_size</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>out</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>out_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>out_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Single-call raw decoder. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">filters</td><td>Array of filters terminated with .id == LZMA_VLI_UNKNOWN. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_pos</td><td>The next byte will be read from in[*in_pos]. *in_pos is updated only if decoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_size</td><td>Size of the input buffer; the first byte that won't be read is in[in_size]. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out_pos</td><td>The next byte will be written to out[*out_pos]. *out_pos is updated only if encoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">out_size</td><td>Size of the out buffer; the first byte into which no data is written to is out[out_size].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Decoding was successful.</li>
<li>LZMA_BUF_ERROR: Not enough output buffer space.</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_DATA_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="aee038818cf7bbe044c3f7a7c86998c1b" name="aee038818cf7bbe044c3f7a7c86998c1b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aee038818cf7bbe044c3f7a7c86998c1b">&#9670;&#160;</a></span>lzma_properties_size()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_properties_size </td>
          <td>(</td>
          <td class="paramtype">uint32_t *</td>          <td class="paramname"><span class="paramname"><em>size</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filter</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the size of the Filter Properties field. </p>
<p>This function may be useful when implementing custom file formats using the raw encoder and decoder.</p>
<dl class="section note"><dt>Note</dt><dd>This function validates the Filter ID, but does not necessarily validate the options. Thus, it is possible that this returns LZMA_OK while the following call to <a class="el" href="#a8e00887086df5a44084ac22e48415de3" title="Encode the Filter Properties field.">lzma_properties_encode()</a> returns LZMA_OPTIONS_ERROR.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">size</td><td>Pointer to uint32_t to hold the size of the properties </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">filter</td><td>Filter ID and options (the size of the properties may vary depending on the options)</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a8e00887086df5a44084ac22e48415de3" name="a8e00887086df5a44084ac22e48415de3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e00887086df5a44084ac22e48415de3">&#9670;&#160;</a></span>lzma_properties_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_properties_encode </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filter</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>props</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Encode the Filter Properties field. </p>
<dl class="section note"><dt>Note</dt><dd>Even this function won't validate more options than actually necessary. Thus, it is possible that encoding the properties succeeds but using the same options to initialize the encoder will fail.</dd>
<dd>
If <a class="el" href="#aee038818cf7bbe044c3f7a7c86998c1b" title="Get the size of the Filter Properties field.">lzma_properties_size()</a> indicated that the size of the Filter Properties field is zero, calling <a class="el" href="#a8e00887086df5a44084ac22e48415de3" title="Encode the Filter Properties field.">lzma_properties_encode()</a> is not required, but it won't do any harm either.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">filter</td><td>Filter ID and options </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">props</td><td>Buffer to hold the encoded options. The size of the buffer must have been already determined with <a class="el" href="#aee038818cf7bbe044c3f7a7c86998c1b" title="Get the size of the Filter Properties field.">lzma_properties_size()</a>.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a88d2e864b2039ac82802cc202278d478" name="a88d2e864b2039ac82802cc202278d478"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a88d2e864b2039ac82802cc202278d478">&#9670;&#160;</a></span>lzma_properties_decode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_properties_decode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filter</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>props</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>props_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Decode the Filter Properties field. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">filter</td><td>filter-&gt;id must have been set to the correct Filter ID. filter-&gt;options doesn't need to be initialized (it's not freed by this function). The decoded options will be stored in filter-&gt;options; it's application's responsibility to free it when appropriate. filter-&gt;options is set to NULL if there are no properties or if an error occurs. </td></tr>
    <tr><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). and in case of an error, also free(). </td></tr>
    <tr><td class="paramname">props</td><td>Input buffer containing the properties. </td></tr>
    <tr><td class="paramname">props_size</td><td>Size of the properties. This must be the exact size; giving too much or too little input will return LZMA_OPTIONS_ERROR.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_MEM_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a996c9c21840ed54e37bd1f664a79d940" name="a996c9c21840ed54e37bd1f664a79d940"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a996c9c21840ed54e37bd1f664a79d940">&#9670;&#160;</a></span>lzma_filter_flags_size()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_filter_flags_size </td>
          <td>(</td>
          <td class="paramtype">uint32_t *</td>          <td class="paramname"><span class="paramname"><em>size</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filter</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculate encoded size of a Filter Flags field. </p>
<p>Knowing the size of Filter Flags is useful to know when allocating memory to hold the encoded Filter Flags.</p>
<dl class="section note"><dt>Note</dt><dd>If you need to calculate size of List of Filter Flags, you need to loop over every <a class="el" href="structlzma__filter.html" title="Filter options.">lzma_filter</a> entry.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">size</td><td>Pointer to integer to hold the calculated size </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">filter</td><td>Filter ID and associated options whose encoded size is to be calculated</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: *size set successfully. Note that this doesn't guarantee that filter-&gt;options is valid, thus <a class="el" href="#a96f23309bc21398fece18c00ebe7db98" title="Encode Filter Flags into given buffer.">lzma_filter_flags_encode()</a> may still fail.</li>
<li>LZMA_OPTIONS_ERROR: Unknown Filter ID or unsupported options.</li>
<li>LZMA_PROG_ERROR: Invalid options </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a96f23309bc21398fece18c00ebe7db98" name="a96f23309bc21398fece18c00ebe7db98"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a96f23309bc21398fece18c00ebe7db98">&#9670;&#160;</a></span>lzma_filter_flags_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_filter_flags_encode </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filter</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>out</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>out_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>out_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Encode Filter Flags into given buffer. </p>
<p>In contrast to some functions, this doesn't allocate the needed buffer. This is due to how this function is used internally by liblzma.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">filter</td><td>Filter ID and options to be encoded </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out_pos</td><td>out[*out_pos] is the next write position. This is updated by the encoder. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">out_size</td><td>out[out_size] is the first byte to not write.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Encoding was successful.</li>
<li>LZMA_OPTIONS_ERROR: Invalid or unsupported options.</li>
<li>LZMA_PROG_ERROR: Invalid options or not enough output buffer space (you should have checked it with <a class="el" href="#a996c9c21840ed54e37bd1f664a79d940" title="Calculate encoded size of a Filter Flags field.">lzma_filter_flags_size()</a>). </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a4cba9a4c658cce0ff01fd102b31ea1a7" name="a4cba9a4c658cce0ff01fd102b31ea1a7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4cba9a4c658cce0ff01fd102b31ea1a7">&#9670;&#160;</a></span>lzma_filter_flags_decode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_filter_flags_decode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filter</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>in_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>in_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Decode Filter Flags from given buffer. </p>
<p>The decoded result is stored into *filter. The old value of filter-&gt;options is not free()d. If anything other than LZMA_OK is returned, filter-&gt;options is set to NULL.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">filter</td><td>Destination filter. The decoded Filter ID will be stored in filter-&gt;id. If options are needed they will be allocated and the pointer will be stored in filter-&gt;options. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">in_pos</td><td>The next byte will be read from in[*in_pos]. *in_pos is updated only if decoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_size</td><td>Size of the input buffer; the first byte that won't be read is in[in_size].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_DATA_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="aa042cf11749bc2183b27de1c3142da30" name="aa042cf11749bc2183b27de1c3142da30"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa042cf11749bc2183b27de1c3142da30">&#9670;&#160;</a></span>lzma_str_to_filters()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char * lzma_str_to_filters </td>
          <td>(</td>
          <td class="paramtype">const char *</td>          <td class="paramname"><span class="paramname"><em>str</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *</td>          <td class="paramname"><span class="paramname"><em>error_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filters</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>flags</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Convert a string to a filter chain. </p>
<p>This tries to make it easier to write applications that allow users to set custom compression options. This only handles the filter configuration (including presets) but not the number of threads, block size, check type, or memory limits.</p>
<p>The input string can be either a preset or a filter chain. Presets begin with a digit 0-9 and may be followed by zero or more flags which are lower-case letters. Currently only "e" is supported, matching LZMA_PRESET_EXTREME. For partial xz command line syntax compatibility, a preset string may start with a single dash "-".</p>
<p>A filter chain consists of one or more "filtername:opt1=value1,opt2=value2" strings separated by one or more spaces. Leading and trailing spaces are ignored. All names and values must be lower-case. Extra commas in the option list are ignored. The order of filters is significant: when encoding, the uncompressed input data goes to the leftmost filter first. Normally "lzma2" is the last filter in the chain.</p>
<p>If one wishes to avoid spaces, for example, to avoid shell quoting, it is possible to use two dashes "--" instead of spaces to separate the filters.</p>
<p>For xz command line compatibility, each filter may be prefixed with two dashes "--" and the colon ":" separating the filter name from the options may be replaced with an equals sign "=".</p>
<p>By default, only filters that can be used in the .xz format are accepted. To allow all filters (LZMA1) use the flag LZMA_STR_ALL_FILTERS.</p>
<p>By default, very basic validation is done for the filter chain as a whole, for example, that LZMA2 is only used as the last filter in the chain. The validation isn't perfect though and it's possible that this function succeeds but using the filter chain for encoding or decoding will still result in LZMA_OPTIONS_ERROR. To disable this validation, use the flag LZMA_STR_NO_VALIDATION.</p>
<p>The available filter names and their options are available via <a class="el" href="#ab51585b68796ce0270f87e615b923809" title="List available filters and/or their options (for help message)">lzma_str_list_filters()</a>. See the xz man page for the description of filter names and options.</p>
<p>For command line applications, below is an example how an error message can be displayed. Note the use of an empty string for the field width. If "^" was used there it would create an off-by-one error except at the very beginning of the line.</p>
<div class="fragment"><div class="line"><span class="keyword">const</span> <span class="keywordtype">char</span> *str = ...; <span class="comment">// From user</span></div>
<div class="line"><a class="code hl_struct" href="structlzma__filter.html" title="Filter options.">lzma_filter</a> filters[<a class="code hl_define" href="#ab33c0cc1728bf390e5b84f8bce1928ba" title="Maximum number of filters in a chain.">LZMA_FILTERS_MAX</a> + 1];</div>
<div class="line"><span class="keywordtype">int</span> pos;</div>
<div class="line"><span class="keyword">const</span> <span class="keywordtype">char</span> *msg = <a class="code hl_function" href="#aa042cf11749bc2183b27de1c3142da30" title="Convert a string to a filter chain.">lzma_str_to_filters</a>(str, &amp;pos, filters, 0, NULL);</div>
<div class="line"><span class="keywordflow">if</span> (msg != NULL) {</div>
<div class="line">    printf(<span class="stringliteral">&quot;%s: Error in XZ compression options:\n&quot;</span>, argv[0]);</div>
<div class="line">    printf(<span class="stringliteral">&quot;%s: %s\n&quot;</span>, argv[0], str);</div>
<div class="line">    printf(<span class="stringliteral">&quot;%s: %*s^\n&quot;</span>, argv[0], errpos, <span class="stringliteral">&quot;&quot;</span>);</div>
<div class="line">    printf(<span class="stringliteral">&quot;%s: %s\n&quot;</span>, argv[0], msg);</div>
<div class="line">}</div>
</div><!-- fragment --><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">str</td><td>User-supplied string describing a preset or a filter chain. If a default value is needed and you don't know what would be good, use "6" since that is the default preset in xz too. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">error_pos</td><td>If this isn't NULL, this value will be set on both success and on all errors. This tells the location of the error in the string. This is an int to make it straightforward to use this as printf() field width. The value is guaranteed to be in the range [0, INT_MAX] even if strlen(str) somehow was greater than INT_MAX. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">filters</td><td>An array of <a class="el" href="structlzma__filter.html" title="Filter options.">lzma_filter</a> structures. There must be LZMA_FILTERS_MAX + 1 (that is, five) elements in the array. The old contents are ignored so it doesn't need to be initialized. This array is modified only if this function returns NULL. Once the allocated filter options are no longer needed, <a class="el" href="#ae06979d219897f5f4c29cbc7a96a8892" title="Free the options in the array of lzma_filter structures.">lzma_filters_free()</a> can be used to free the options (it doesn't free the filters array itself). </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">flags</td><td>Bitwise-or of zero or more of the flags LZMA_STR_ALL_FILTERS and LZMA_STR_NO_VALIDATION. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free().</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>On success, NULL is returned. On error, a statically-allocated error message is returned which together with the error_pos should give some idea what is wrong. </dd></dl>

</div>
</div>
<a id="a7deeb86ef59a9111b8033681290e0fb0" name="a7deeb86ef59a9111b8033681290e0fb0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7deeb86ef59a9111b8033681290e0fb0">&#9670;&#160;</a></span>lzma_str_from_filters()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_str_from_filters </td>
          <td>(</td>
          <td class="paramtype">char **</td>          <td class="paramname"><span class="paramname"><em>str</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filters</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>flags</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Convert a filter chain to a string. </p>
<p>Use cases:</p>
<ul>
<li>Verbose output showing the full encoder options to the user (use LZMA_STR_ENCODER in flags)</li>
<li>Showing the filters and options that are required to decode a file (use LZMA_STR_DECODER in flags)</li>
<li>Showing the filter names without any options in informational messages where the technical details aren't important (no flags). In this case the .options in the filters array are ignored and may be NULL even if a filter has a mandatory options structure.</li>
</ul>
<p>Note that even if the filter chain was specified using a preset, the resulting filter chain isn't reversed to a preset. So if you specify "6" to <a class="el" href="#aa042cf11749bc2183b27de1c3142da30" title="Convert a string to a filter chain.">lzma_str_to_filters()</a> then <a class="el" href="#a7deeb86ef59a9111b8033681290e0fb0" title="Convert a filter chain to a string.">lzma_str_from_filters()</a> will produce a string containing "lzma2".</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[out]</td><td class="paramname">str</td><td>On success *str will be set to point to an allocated string describing the given filter chain. Old value is ignored. On error *str is always set to NULL. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">filters</td><td>Array of filters terminated with .id == LZMA_VLI_UNKNOWN. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">flags</td><td>Bitwise-or of zero or more of the flags LZMA_STR_ENCODER, LZMA_STR_DECODER, LZMA_STR_GETOPT_LONG, and LZMA_STR_NO_SPACES. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free().</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_OPTIONS_ERROR: Empty filter chain (filters[0].id == LZMA_VLI_UNKNOWN) or the filter chain includes a Filter ID that is not supported by this function.</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="ab51585b68796ce0270f87e615b923809" name="ab51585b68796ce0270f87e615b923809"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab51585b68796ce0270f87e615b923809">&#9670;&#160;</a></span>lzma_str_list_filters()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_str_list_filters </td>
          <td>(</td>
          <td class="paramtype">char **</td>          <td class="paramname"><span class="paramname"><em>str</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="vli_8h.html#a1dbc0ffc3e72748f64df8f7f71898272">lzma_vli</a></td>          <td class="paramname"><span class="paramname"><em>filter_id</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>flags</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>List available filters and/or their options (for help message) </p>
<p>If a filter_id is given then only one line is created which contains the filter name. If LZMA_STR_ENCODER or LZMA_STR_DECODER is used then the options read by the encoder or decoder are printed on the same line.</p>
<p>If filter_id is LZMA_VLI_UNKNOWN then all supported .xz-compatible filters are listed:</p>
<ul>
<li>If neither LZMA_STR_ENCODER nor LZMA_STR_DECODER is used then the supported filter names are listed on a single line separated by spaces.</li>
<li>If LZMA_STR_ENCODER or LZMA_STR_DECODER is used then filters and the supported options are listed one filter per line. There won't be a newline after the last filter.</li>
<li>If LZMA_STR_ALL_FILTERS is used then the list will include also those filters that cannot be used in the .xz format (LZMA1).</li>
</ul>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">str</td><td>On success *str will be set to point to an allocated string listing the filters and options. Old value is ignored. On error *str is always set to NULL. </td></tr>
    <tr><td class="paramname">filter_id</td><td>Filter ID or LZMA_VLI_UNKNOWN. </td></tr>
    <tr><td class="paramname">flags</td><td>Bitwise-or of zero or more of the flags LZMA_STR_ALL_FILTERS, LZMA_STR_ENCODER, LZMA_STR_DECODER, and LZMA_STR_GETOPT_LONG. </td></tr>
    <tr><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free().</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_OPTIONS_ERROR: Unsupported filter_id or flags</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li><li class="navelem"><a class="el" href="filter_8h.html">filter.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
