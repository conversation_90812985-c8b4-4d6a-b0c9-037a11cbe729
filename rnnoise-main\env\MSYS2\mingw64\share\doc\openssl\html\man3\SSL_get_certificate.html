<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_get_certificate</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_get_certificate, SSL_get_privatekey - retrieve TLS/SSL certificate and private key</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

X509 *SSL_get_certificate(const SSL *s);
EVP_PKEY *SSL_get_privatekey(const SSL *s);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_get_certificate() returns a pointer to an <b>X509</b> object representing a certificate used as the local peer&#39;s identity.</p>

<p>Multiple certificates can be configured; for example, a server might have both RSA and ECDSA certificates. The certificate which is returned by SSL_get_certificate() is determined as follows:</p>

<ul>

<li><p>If it is called before certificate selection has occurred, it returns the most recently added certificate, or NULL if no certificate has been added.</p>

</li>
<li><p>After certificate selection has occurred, it returns the certificate which was selected during the handshake, or NULL if no certificate was selected (for example, on a client where no client certificate is in use).</p>

</li>
</ul>

<p>Certificate selection occurs during the handshake; therefore, the value returned by SSL_get_certificate() during any callback made during the handshake process will depend on whether that callback is made before or after certificate selection occurs.</p>

<p>A specific use for SSL_get_certificate() is inside a callback set via a call to <a href="../man3/SSL_CTX_set_tlsext_status_cb.html">SSL_CTX_set_tlsext_status_cb(3)</a>. This callback occurs after certificate selection, where it can be used to examine a server&#39;s chosen certificate, for example for the purpose of identifying a certificate&#39;s OCSP responder URL so that an OCSP response can be obtained.</p>

<p>SSL_get_privatekey() returns a pointer to the <b>EVP_PKEY</b> object corresponding to the certificate returned by SSL_get_certificate(), if any.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>These functions return pointers to their respective objects, or NULL if no such object is available. Returned objects are owned by the SSL object and should not be freed by users of these functions.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_CTX_set_tlsext_status_cb.html">SSL_CTX_set_tlsext_status_cb(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2001-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


