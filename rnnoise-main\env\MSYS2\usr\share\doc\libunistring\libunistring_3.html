<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html401/loose.dtd">
<html>
<!-- Created on October, 16 2024 by texi2html 1.78a -->
<!--
Written by: <PERSON> <<EMAIL>> (original author)
            <PERSON>  <<EMAIL>>
            <PERSON> <o<PERSON><EMAIL>>
            and many others.
Maintained by: Many creative people.
Send bugs and suggestions to <<EMAIL>>

-->
<head>
<title>GNU libunistring: 3. Elementary types &lt;unitypes.h&gt;</title>

<meta name="description" content="GNU libunistring: 3. Elementary types &lt;unitypes.h&gt;">
<meta name="keywords" content="GNU libunistring: 3. Elementary types &lt;unitypes.h&gt;">
<meta name="resource-type" content="document">
<meta name="distribution" content="global">
<meta name="Generator" content="texi2html 1.78a">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style type="text/css">
<!--
a.summary-letter {text-decoration: none}
pre.display {font-family: serif}
pre.format {font-family: serif}
pre.menu-comment {font-family: serif}
pre.menu-preformatted {font-family: serif}
pre.smalldisplay {font-family: serif; font-size: smaller}
pre.smallexample {font-size: smaller}
pre.smallformat {font-family: serif; font-size: smaller}
pre.smalllisp {font-size: smaller}
span.roman {font-family:serif; font-weight:normal;}
span.sansserif {font-family:sans-serif; font-weight:normal;}
ul.toc {list-style: none}
-->
</style>


</head>

<body lang="en" bgcolor="#FFFFFF" text="#000000" link="#0000FF" vlink="#800080" alink="#FF0000">

<table cellpadding="1" cellspacing="1" border="0">
<tr><td valign="middle" align="left">[<a href="libunistring_2.html#SEC8" title="Beginning of this chapter or previous chapter"> &lt;&lt; </a>]</td>
<td valign="middle" align="left">[<a href="libunistring_4.html#SEC10" title="Next chapter"> &gt;&gt; </a>]</td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Top" title="Cover (top) of document">Top</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Contents" title="Table of contents">Contents</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_21.html#SEC94" title="Index">Index</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_abt.html#SEC_About" title="About (help)"> ? </a>]</td>
</tr></table>

<hr size="2">
<a name="unitypes_002eh"></a>
<a name="SEC9"></a>
<h1 class="chapter"> <a href="libunistring_toc.html#TOC9">3. Elementary types <code>&lt;unitypes.h&gt;</code></a> </h1>

<p>The include file <code>&lt;unitypes.h&gt;</code> provides the following basic types.
</p>
<dl>
<dt><u>Type:</u> <b>uint8_t</b>
<a name="IDX16"></a>
</dt>
<dt><u>Type:</u> <b>uint16_t</b>
<a name="IDX17"></a>
</dt>
<dt><u>Type:</u> <b>uint32_t</b>
<a name="IDX18"></a>
</dt>
<dd><p>These are the storage units of UTF-8/16/32 strings, respectively.  The definitions are
taken from <code>&lt;stdint.h&gt;</code>, on platforms where this include file is present.
</p></dd></dl>

<dl>
<dt><u>Type:</u> <b>ucs4_t</b>
<a name="IDX19"></a>
</dt>
<dd><p>This type represents a single Unicode character, outside of an UTF-32 string.
</p></dd></dl>

<p>The types <code>ucs4_t</code> and <code>uint32_t</code> happen to be identical.  They differ
in use and intent, however:
</p><ul>
<li>
Use <code>uint32_t *</code> to designate an UTF-32 string.  Use <code>ucs4_t</code> to
designate a single Unicode character, outside of an UTF-32 string.
</li><li>
Conversions functions that take an UTF-32 string as input will usually perform
a range-check on the <code>uint32_t</code> values.  Whereas functions that are
declared to take <code>ucs4_t</code> arguments will not perform such a range-check.
</li></ul>
<hr size="6">
<table cellpadding="1" cellspacing="1" border="0">
<tr><td valign="middle" align="left">[<a href="libunistring_2.html#SEC8" title="Beginning of this chapter or previous chapter"> &lt;&lt; </a>]</td>
<td valign="middle" align="left">[<a href="libunistring_4.html#SEC10" title="Next chapter"> &gt;&gt; </a>]</td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left"> &nbsp; </td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Top" title="Cover (top) of document">Top</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_toc.html#SEC_Contents" title="Table of contents">Contents</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_21.html#SEC94" title="Index">Index</a>]</td>
<td valign="middle" align="left">[<a href="libunistring_abt.html#SEC_About" title="About (help)"> ? </a>]</td>
</tr></table>
<p>
 <font size="-1">
  This document was generated by <em>Bruno Haible</em> on <em>October, 16 2024</em> using <a href="https://www.nongnu.org/texi2html/"><em>texi2html 1.78a</em></a>.
 </font>
 <br>

</p>
</body>
</html>
