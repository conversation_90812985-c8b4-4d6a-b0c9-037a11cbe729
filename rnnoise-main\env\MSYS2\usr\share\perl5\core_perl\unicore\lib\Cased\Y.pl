# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V314
65
91
97
123
170
171
181
182
186
187
192
215
216
247
248
443
444
448
452
660
661
697
704
706
736
741
837
838
880
884
886
888
890
894
895
896
902
903
904
907
908
909
910
930
931
1014
1015
1154
1162
1328
1329
1367
1376
1417
4256
4294
4295
4296
4301
4302
4304
4347
4348
4352
5024
5110
5112
5118
7296
7305
7312
7355
7357
7360
7424
7616
7680
7958
7960
7966
7968
8006
8008
8014
8016
8024
8025
8026
8027
8028
8029
8030
8031
8062
8064
8117
8118
8125
8126
8127
8130
8133
8134
8141
8144
8148
8150
8156
8160
8173
8178
8181
8182
8189
8305
8306
8319
8320
8336
8349
8450
8451
8455
8456
8458
8468
8469
8470
8473
8478
8484
8485
8486
8487
8488
8489
8490
8494
8495
8501
8505
8506
8508
8512
8517
8522
8526
8527
8544
8576
8579
8581
9398
9450
11264
11493
11499
11503
11506
11508
11520
11558
11559
11560
11565
11566
42560
42606
42624
42654
42786
42888
42891
42895
42896
42955
42960
42962
42963
42964
42965
42970
42994
42999
43000
43003
43824
43867
43868
43882
43888
43968
64256
64263
64275
64280
65313
65339
65345
65371
66560
66640
66736
66772
66776
66812
66928
66939
66940
66955
66956
66963
66964
66966
66967
66978
66979
66994
66995
67002
67003
67005
67456
67457
67459
67462
67463
67505
67506
67515
68736
68787
68800
68851
71840
71904
93760
93824
119808
119893
119894
119965
119966
119968
119970
119971
119973
119975
119977
119981
119982
119994
119995
119996
119997
120004
120005
120070
120071
120075
120077
120085
120086
120093
120094
120122
120123
120127
120128
120133
120134
120135
120138
120145
120146
120486
120488
120513
120514
120539
120540
120571
120572
120597
120598
120629
120630
120655
120656
120687
120688
120713
120714
120745
120746
120771
120772
120780
122624
122634
122635
122655
122661
122667
122928
122990
125184
125252
127280
127306
127312
127338
127344
127370
END
