include(Platform/Generic-ADSP-Common)

set(CMAKE_CXX_OUTPUT_EXTENSION ".doj")

string(APPEND CMAKE_CXX_FLAGS_DEBUG_INIT " -g")
string(APPEND CMAKE_CXX_FLAGS_MINSIZEREL_INIT " ")
string(APPEND CMAKE_CXX_FLAGS_RELEASE_INIT " ")
string(APPEND CMAKE_CXX_FLAGS_RELWITHDEBINFO_INIT " ")

set(CMAKE_CXX_LINKER_WRAPPER_FLAG "-flags-link" " ")
set(CMAKE_CXX_LINKER_WRAPPER_FLAG_SEP ",")

set(CMAKE_CXX_CREATE_STATIC_LIBRARY
     "<CMAKE_CXX_COMPILER> -build-lib -proc ${ADSP_PROCESSOR} -si-revision ${ADSP_PROCESSOR_SILICIUM_REVISION} -o <TARGET> <CMAKE_CXX_LINK_FLAGS> <OBJECTS>")

set(CMAKE_CXX_LINK_EXECUTABLE
     "<CMAKE_CXX_COMPILER> <FLAGS> <CMAKE_CXX_LINK_FLAGS> <LINK_FLAGS> <OBJECTS> -o <TARGET> <LINK_LIBRARIES>")

set(CMAKE_CXX_CREATE_SHARED_LIBRARY)
set(CMAKE_CXX_CREATE_MODULE_LIBRARY)
