# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   IT IS DEPRECATED TO USE THIS FILE   !!!!!!!

# This file is for internal use by core Perl only.  It is retained for
# backwards compatibility with applications that may have come to rely on it,
# but its format and even its name or existence are subject to change without
# notice in a future Perl version.  Don't use it directly.  Instead, its
# contents are now retrievable through a stable API in the Unicode::UCD
# module: Unicode::UCD::prop_invmap('NFKC_Casefold') (Values for individual
# code points can be retrieved via Unicode::UCD::charprop());



# The name this table is to be known by, with the format of the mappings in
# the main body of the table, and what all code points missing from this file
# map to.
$Unicode::UCD::SwashInfo{'ToNFKCCF'}{'format'} = 'x'; # non-negative hex whole number; a code point
$Unicode::UCD::SwashInfo{'ToNFKCCF'}{'specials_name'} = 'Unicode::UCD::ToSpecNFKCCF'; # Name of hash of special mappings
$Unicode::UCD::SwashInfo{'ToNFKCCF'}{'missing'} = '<code point>'; # code point maps to itself

# Some code points require special handling because their mappings are each to
# multiple code points.  These do not appear in the main body, but are defined
# in the hash below.

# Each key is the string of N bytes that together make up the UTF-8 encoding
# for the code point.  (i.e. the same as looking at the code point's UTF-8
# under "use bytes").  Each value is the UTF-8 of the translation, for speed.
%Unicode::UCD::ToSpecNFKCCF = (
"\xC2\xA8" => "\x{0020}\x{0308}",             # U+00A8 => 0020 0308
"\xC2\xAD" => "",                             # U+00AD => 
"\xC2\xAF" => "\x{0020}\x{0304}",             # U+00AF => 0020 0304
"\xC2\xB4" => "\x{0020}\x{0301}",             # U+00B4 => 0020 0301
"\xC2\xB8" => "\x{0020}\x{0327}",             # U+00B8 => 0020 0327
"\xC2\xBC" => "\x{0031}\x{2044}\x{0034}",     # U+00BC => 0031 2044 0034
"\xC2\xBD" => "\x{0031}\x{2044}\x{0032}",     # U+00BD => 0031 2044 0032
"\xC2\xBE" => "\x{0033}\x{2044}\x{0034}",     # U+00BE => 0033 2044 0034
"\xC3\x9F" => "\x{0073}\x{0073}",             # U+00DF => 0073 0073
"\xC4\xB0" => "\x{0069}\x{0307}",             # U+0130 => 0069 0307
"\xC4\xB2" => "\x{0069}\x{006A}",             # U+0132 => 0069 006A
"\xC4\xB3" => "\x{0069}\x{006A}",             # U+0133 => 0069 006A
"\xC4\xBF" => "\x{006C}\x{00B7}",             # U+013F => 006C 00B7
"\xC5\x80" => "\x{006C}\x{00B7}",             # U+0140 => 006C 00B7
"\xC5\x89" => "\x{02BC}\x{006E}",             # U+0149 => 02BC 006E
"\xC7\x84" => "\x{0064}\x{017E}",             # U+01C4 => 0064 017E
"\xC7\x85" => "\x{0064}\x{017E}",             # U+01C5 => 0064 017E
"\xC7\x86" => "\x{0064}\x{017E}",             # U+01C6 => 0064 017E
"\xC7\x87" => "\x{006C}\x{006A}",             # U+01C7 => 006C 006A
"\xC7\x88" => "\x{006C}\x{006A}",             # U+01C8 => 006C 006A
"\xC7\x89" => "\x{006C}\x{006A}",             # U+01C9 => 006C 006A
"\xC7\x8A" => "\x{006E}\x{006A}",             # U+01CA => 006E 006A
"\xC7\x8B" => "\x{006E}\x{006A}",             # U+01CB => 006E 006A
"\xC7\x8C" => "\x{006E}\x{006A}",             # U+01CC => 006E 006A
"\xC7\xB1" => "\x{0064}\x{007A}",             # U+01F1 => 0064 007A
"\xC7\xB2" => "\x{0064}\x{007A}",             # U+01F2 => 0064 007A
"\xC7\xB3" => "\x{0064}\x{007A}",             # U+01F3 => 0064 007A
"\xCB\x98" => "\x{0020}\x{0306}",             # U+02D8 => 0020 0306
"\xCB\x99" => "\x{0020}\x{0307}",             # U+02D9 => 0020 0307
"\xCB\x9A" => "\x{0020}\x{030A}",             # U+02DA => 0020 030A
"\xCB\x9B" => "\x{0020}\x{0328}",             # U+02DB => 0020 0328
"\xCB\x9C" => "\x{0020}\x{0303}",             # U+02DC => 0020 0303
"\xCB\x9D" => "\x{0020}\x{030B}",             # U+02DD => 0020 030B
"\xCD\x84" => "\x{0308}\x{0301}",             # U+0344 => 0308 0301
"\xCD\x8F" => "",                             # U+034F => 
"\xCD\xBA" => "\x{0020}\x{03B9}",             # U+037A => 0020 03B9
"\xCE\x84" => "\x{0020}\x{0301}",             # U+0384 => 0020 0301
"\xCE\x85" => "\x{0020}\x{0308}\x{0301}",     # U+0385 => 0020 0308 0301
"\xD6\x87" => "\x{0565}\x{0582}",             # U+0587 => 0565 0582
"\xD8\x9C" => "",                             # U+061C => 
"\xD9\xB5" => "\x{0627}\x{0674}",             # U+0675 => 0627 0674
"\xD9\xB6" => "\x{0648}\x{0674}",             # U+0676 => 0648 0674
"\xD9\xB7" => "\x{06C7}\x{0674}",             # U+0677 => 06C7 0674
"\xD9\xB8" => "\x{064A}\x{0674}",             # U+0678 => 064A 0674
"\xE0\xA5\x98" => "\x{0915}\x{093C}",         # U+0958 => 0915 093C
"\xE0\xA5\x99" => "\x{0916}\x{093C}",         # U+0959 => 0916 093C
"\xE0\xA5\x9A" => "\x{0917}\x{093C}",         # U+095A => 0917 093C
"\xE0\xA5\x9B" => "\x{091C}\x{093C}",         # U+095B => 091C 093C
"\xE0\xA5\x9C" => "\x{0921}\x{093C}",         # U+095C => 0921 093C
"\xE0\xA5\x9D" => "\x{0922}\x{093C}",         # U+095D => 0922 093C
"\xE0\xA5\x9E" => "\x{092B}\x{093C}",         # U+095E => 092B 093C
"\xE0\xA5\x9F" => "\x{092F}\x{093C}",         # U+095F => 092F 093C
"\xE0\xA7\x9C" => "\x{09A1}\x{09BC}",         # U+09DC => 09A1 09BC
"\xE0\xA7\x9D" => "\x{09A2}\x{09BC}",         # U+09DD => 09A2 09BC
"\xE0\xA7\x9F" => "\x{09AF}\x{09BC}",         # U+09DF => 09AF 09BC
"\xE0\xA8\xB3" => "\x{0A32}\x{0A3C}",         # U+0A33 => 0A32 0A3C
"\xE0\xA8\xB6" => "\x{0A38}\x{0A3C}",         # U+0A36 => 0A38 0A3C
"\xE0\xA9\x99" => "\x{0A16}\x{0A3C}",         # U+0A59 => 0A16 0A3C
"\xE0\xA9\x9A" => "\x{0A17}\x{0A3C}",         # U+0A5A => 0A17 0A3C
"\xE0\xA9\x9B" => "\x{0A1C}\x{0A3C}",         # U+0A5B => 0A1C 0A3C
"\xE0\xA9\x9E" => "\x{0A2B}\x{0A3C}",         # U+0A5E => 0A2B 0A3C
"\xE0\xAD\x9C" => "\x{0B21}\x{0B3C}",         # U+0B5C => 0B21 0B3C
"\xE0\xAD\x9D" => "\x{0B22}\x{0B3C}",         # U+0B5D => 0B22 0B3C
"\xE0\xB8\xB3" => "\x{0E4D}\x{0E32}",         # U+0E33 => 0E4D 0E32
"\xE0\xBA\xB3" => "\x{0ECD}\x{0EB2}",         # U+0EB3 => 0ECD 0EB2
"\xE0\xBB\x9C" => "\x{0EAB}\x{0E99}",         # U+0EDC => 0EAB 0E99
"\xE0\xBB\x9D" => "\x{0EAB}\x{0EA1}",         # U+0EDD => 0EAB 0EA1
"\xE0\xBD\x83" => "\x{0F42}\x{0FB7}",         # U+0F43 => 0F42 0FB7
"\xE0\xBD\x8D" => "\x{0F4C}\x{0FB7}",         # U+0F4D => 0F4C 0FB7
"\xE0\xBD\x92" => "\x{0F51}\x{0FB7}",         # U+0F52 => 0F51 0FB7
"\xE0\xBD\x97" => "\x{0F56}\x{0FB7}",         # U+0F57 => 0F56 0FB7
"\xE0\xBD\x9C" => "\x{0F5B}\x{0FB7}",         # U+0F5C => 0F5B 0FB7
"\xE0\xBD\xA9" => "\x{0F40}\x{0FB5}",         # U+0F69 => 0F40 0FB5
"\xE0\xBD\xB3" => "\x{0F71}\x{0F72}",         # U+0F73 => 0F71 0F72
"\xE0\xBD\xB5" => "\x{0F71}\x{0F74}",         # U+0F75 => 0F71 0F74
"\xE0\xBD\xB6" => "\x{0FB2}\x{0F80}",         # U+0F76 => 0FB2 0F80
"\xE0\xBD\xB7" => "\x{0FB2}\x{0F71}\x{0F80}", # U+0F77 => 0FB2 0F71 0F80
"\xE0\xBD\xB8" => "\x{0FB3}\x{0F80}",         # U+0F78 => 0FB3 0F80
"\xE0\xBD\xB9" => "\x{0FB3}\x{0F71}\x{0F80}", # U+0F79 => 0FB3 0F71 0F80
"\xE0\xBE\x81" => "\x{0F71}\x{0F80}",         # U+0F81 => 0F71 0F80
"\xE0\xBE\x93" => "\x{0F92}\x{0FB7}",         # U+0F93 => 0F92 0FB7
"\xE0\xBE\x9D" => "\x{0F9C}\x{0FB7}",         # U+0F9D => 0F9C 0FB7
"\xE0\xBE\xA2" => "\x{0FA1}\x{0FB7}",         # U+0FA2 => 0FA1 0FB7
"\xE0\xBE\xA7" => "\x{0FA6}\x{0FB7}",         # U+0FA7 => 0FA6 0FB7
"\xE0\xBE\xAC" => "\x{0FAB}\x{0FB7}",         # U+0FAC => 0FAB 0FB7
"\xE0\xBE\xB9" => "\x{0F90}\x{0FB5}",         # U+0FB9 => 0F90 0FB5
"\xE1\x85\x9F" => "",                         # U+115F => 
"\xE1\x85\xA0" => "",                         # U+1160 => 
"\xE1\x9E\xB4" => "",                         # U+17B4 => 
"\xE1\x9E\xB5" => "",                         # U+17B5 => 
"\xE1\xA0\x8B" => "",                         # U+180B => 
"\xE1\xA0\x8C" => "",                         # U+180C => 
"\xE1\xA0\x8D" => "",                         # U+180D => 
"\xE1\xA0\x8E" => "",                         # U+180E => 
"\xE1\xA0\x8F" => "",                         # U+180F => 
"\xE1\xBA\x9A" => "\x{0061}\x{02BE}",         # U+1E9A => 0061 02BE
"\xE1\xBA\x9E" => "\x{0073}\x{0073}",         # U+1E9E => 0073 0073
"\xE1\xBE\x80" => "\x{1F00}\x{03B9}",         # U+1F80 => 1F00 03B9
"\xE1\xBE\x81" => "\x{1F01}\x{03B9}",         # U+1F81 => 1F01 03B9
"\xE1\xBE\x82" => "\x{1F02}\x{03B9}",         # U+1F82 => 1F02 03B9
"\xE1\xBE\x83" => "\x{1F03}\x{03B9}",         # U+1F83 => 1F03 03B9
"\xE1\xBE\x84" => "\x{1F04}\x{03B9}",         # U+1F84 => 1F04 03B9
"\xE1\xBE\x85" => "\x{1F05}\x{03B9}",         # U+1F85 => 1F05 03B9
"\xE1\xBE\x86" => "\x{1F06}\x{03B9}",         # U+1F86 => 1F06 03B9
"\xE1\xBE\x87" => "\x{1F07}\x{03B9}",         # U+1F87 => 1F07 03B9
"\xE1\xBE\x88" => "\x{1F00}\x{03B9}",         # U+1F88 => 1F00 03B9
"\xE1\xBE\x89" => "\x{1F01}\x{03B9}",         # U+1F89 => 1F01 03B9
"\xE1\xBE\x8A" => "\x{1F02}\x{03B9}",         # U+1F8A => 1F02 03B9
"\xE1\xBE\x8B" => "\x{1F03}\x{03B9}",         # U+1F8B => 1F03 03B9
"\xE1\xBE\x8C" => "\x{1F04}\x{03B9}",         # U+1F8C => 1F04 03B9
"\xE1\xBE\x8D" => "\x{1F05}\x{03B9}",         # U+1F8D => 1F05 03B9
"\xE1\xBE\x8E" => "\x{1F06}\x{03B9}",         # U+1F8E => 1F06 03B9
"\xE1\xBE\x8F" => "\x{1F07}\x{03B9}",         # U+1F8F => 1F07 03B9
"\xE1\xBE\x90" => "\x{1F20}\x{03B9}",         # U+1F90 => 1F20 03B9
"\xE1\xBE\x91" => "\x{1F21}\x{03B9}",         # U+1F91 => 1F21 03B9
"\xE1\xBE\x92" => "\x{1F22}\x{03B9}",         # U+1F92 => 1F22 03B9
"\xE1\xBE\x93" => "\x{1F23}\x{03B9}",         # U+1F93 => 1F23 03B9
"\xE1\xBE\x94" => "\x{1F24}\x{03B9}",         # U+1F94 => 1F24 03B9
"\xE1\xBE\x95" => "\x{1F25}\x{03B9}",         # U+1F95 => 1F25 03B9
"\xE1\xBE\x96" => "\x{1F26}\x{03B9}",         # U+1F96 => 1F26 03B9
"\xE1\xBE\x97" => "\x{1F27}\x{03B9}",         # U+1F97 => 1F27 03B9
"\xE1\xBE\x98" => "\x{1F20}\x{03B9}",         # U+1F98 => 1F20 03B9
"\xE1\xBE\x99" => "\x{1F21}\x{03B9}",         # U+1F99 => 1F21 03B9
"\xE1\xBE\x9A" => "\x{1F22}\x{03B9}",         # U+1F9A => 1F22 03B9
"\xE1\xBE\x9B" => "\x{1F23}\x{03B9}",         # U+1F9B => 1F23 03B9
"\xE1\xBE\x9C" => "\x{1F24}\x{03B9}",         # U+1F9C => 1F24 03B9
"\xE1\xBE\x9D" => "\x{1F25}\x{03B9}",         # U+1F9D => 1F25 03B9
"\xE1\xBE\x9E" => "\x{1F26}\x{03B9}",         # U+1F9E => 1F26 03B9
"\xE1\xBE\x9F" => "\x{1F27}\x{03B9}",         # U+1F9F => 1F27 03B9
"\xE1\xBE\xA0" => "\x{1F60}\x{03B9}",         # U+1FA0 => 1F60 03B9
"\xE1\xBE\xA1" => "\x{1F61}\x{03B9}",         # U+1FA1 => 1F61 03B9
"\xE1\xBE\xA2" => "\x{1F62}\x{03B9}",         # U+1FA2 => 1F62 03B9
"\xE1\xBE\xA3" => "\x{1F63}\x{03B9}",         # U+1FA3 => 1F63 03B9
"\xE1\xBE\xA4" => "\x{1F64}\x{03B9}",         # U+1FA4 => 1F64 03B9
"\xE1\xBE\xA5" => "\x{1F65}\x{03B9}",         # U+1FA5 => 1F65 03B9
"\xE1\xBE\xA6" => "\x{1F66}\x{03B9}",         # U+1FA6 => 1F66 03B9
"\xE1\xBE\xA7" => "\x{1F67}\x{03B9}",         # U+1FA7 => 1F67 03B9
"\xE1\xBE\xA8" => "\x{1F60}\x{03B9}",         # U+1FA8 => 1F60 03B9
"\xE1\xBE\xA9" => "\x{1F61}\x{03B9}",         # U+1FA9 => 1F61 03B9
"\xE1\xBE\xAA" => "\x{1F62}\x{03B9}",         # U+1FAA => 1F62 03B9
"\xE1\xBE\xAB" => "\x{1F63}\x{03B9}",         # U+1FAB => 1F63 03B9
"\xE1\xBE\xAC" => "\x{1F64}\x{03B9}",         # U+1FAC => 1F64 03B9
"\xE1\xBE\xAD" => "\x{1F65}\x{03B9}",         # U+1FAD => 1F65 03B9
"\xE1\xBE\xAE" => "\x{1F66}\x{03B9}",         # U+1FAE => 1F66 03B9
"\xE1\xBE\xAF" => "\x{1F67}\x{03B9}",         # U+1FAF => 1F67 03B9
"\xE1\xBE\xB2" => "\x{1F70}\x{03B9}",         # U+1FB2 => 1F70 03B9
"\xE1\xBE\xB3" => "\x{03B1}\x{03B9}",         # U+1FB3 => 03B1 03B9
"\xE1\xBE\xB4" => "\x{03AC}\x{03B9}",         # U+1FB4 => 03AC 03B9
"\xE1\xBE\xB7" => "\x{1FB6}\x{03B9}",         # U+1FB7 => 1FB6 03B9
"\xE1\xBE\xBC" => "\x{03B1}\x{03B9}",         # U+1FBC => 03B1 03B9
"\xE1\xBE\xBD" => "\x{0020}\x{0313}",         # U+1FBD => 0020 0313
"\xE1\xBE\xBF" => "\x{0020}\x{0313}",         # U+1FBF => 0020 0313
"\xE1\xBF\x80" => "\x{0020}\x{0342}",         # U+1FC0 => 0020 0342
"\xE1\xBF\x81" => "\x{0020}\x{0308}\x{0342}", # U+1FC1 => 0020 0308 0342
"\xE1\xBF\x82" => "\x{1F74}\x{03B9}",         # U+1FC2 => 1F74 03B9
"\xE1\xBF\x83" => "\x{03B7}\x{03B9}",         # U+1FC3 => 03B7 03B9
"\xE1\xBF\x84" => "\x{03AE}\x{03B9}",         # U+1FC4 => 03AE 03B9
"\xE1\xBF\x87" => "\x{1FC6}\x{03B9}",         # U+1FC7 => 1FC6 03B9
"\xE1\xBF\x8C" => "\x{03B7}\x{03B9}",         # U+1FCC => 03B7 03B9
"\xE1\xBF\x8D" => "\x{0020}\x{0313}\x{0300}", # U+1FCD => 0020 0313 0300
"\xE1\xBF\x8E" => "\x{0020}\x{0313}\x{0301}", # U+1FCE => 0020 0313 0301
"\xE1\xBF\x8F" => "\x{0020}\x{0313}\x{0342}", # U+1FCF => 0020 0313 0342
"\xE1\xBF\x9D" => "\x{0020}\x{0314}\x{0300}", # U+1FDD => 0020 0314 0300
"\xE1\xBF\x9E" => "\x{0020}\x{0314}\x{0301}", # U+1FDE => 0020 0314 0301
"\xE1\xBF\x9F" => "\x{0020}\x{0314}\x{0342}", # U+1FDF => 0020 0314 0342
"\xE1\xBF\xAD" => "\x{0020}\x{0308}\x{0300}", # U+1FED => 0020 0308 0300
"\xE1\xBF\xAE" => "\x{0020}\x{0308}\x{0301}", # U+1FEE => 0020 0308 0301
"\xE1\xBF\xB2" => "\x{1F7C}\x{03B9}",         # U+1FF2 => 1F7C 03B9
"\xE1\xBF\xB3" => "\x{03C9}\x{03B9}",         # U+1FF3 => 03C9 03B9
"\xE1\xBF\xB4" => "\x{03CE}\x{03B9}",         # U+1FF4 => 03CE 03B9
"\xE1\xBF\xB7" => "\x{1FF6}\x{03B9}",         # U+1FF7 => 1FF6 03B9
"\xE1\xBF\xBC" => "\x{03C9}\x{03B9}",         # U+1FFC => 03C9 03B9
"\xE1\xBF\xBD" => "\x{0020}\x{0301}",         # U+1FFD => 0020 0301
"\xE1\xBF\xBE" => "\x{0020}\x{0314}",         # U+1FFE => 0020 0314
"\xE2\x80\x8B" => "",                         # U+200B => 
"\xE2\x80\x8C" => "",                         # U+200C => 
"\xE2\x80\x8D" => "",                         # U+200D => 
"\xE2\x80\x8E" => "",                         # U+200E => 
"\xE2\x80\x8F" => "",                         # U+200F => 
"\xE2\x80\x97" => "\x{0020}\x{0333}",         # U+2017 => 0020 0333
"\xE2\x80\xA5" => "\x{002E}\x{002E}",         # U+2025 => 002E 002E
"\xE2\x80\xA6" => "\x{002E}\x{002E}\x{002E}", # U+2026 => 002E 002E 002E
"\xE2\x80\xAA" => "",                         # U+202A => 
"\xE2\x80\xAB" => "",                         # U+202B => 
"\xE2\x80\xAC" => "",                         # U+202C => 
"\xE2\x80\xAD" => "",                         # U+202D => 
"\xE2\x80\xAE" => "",                         # U+202E => 
"\xE2\x80\xB3" => "\x{2032}\x{2032}",         # U+2033 => 2032 2032
"\xE2\x80\xB4" => "\x{2032}\x{2032}\x{2032}", # U+2034 => 2032 2032 2032
"\xE2\x80\xB6" => "\x{2035}\x{2035}",         # U+2036 => 2035 2035
"\xE2\x80\xB7" => "\x{2035}\x{2035}\x{2035}", # U+2037 => 2035 2035 2035
"\xE2\x80\xBC" => "\x{0021}\x{0021}",         # U+203C => 0021 0021
"\xE2\x80\xBE" => "\x{0020}\x{0305}",         # U+203E => 0020 0305
"\xE2\x81\x87" => "\x{003F}\x{003F}",         # U+2047 => 003F 003F
"\xE2\x81\x88" => "\x{003F}\x{0021}",         # U+2048 => 003F 0021
"\xE2\x81\x89" => "\x{0021}\x{003F}",         # U+2049 => 0021 003F
"\xE2\x81\x97" => "\x{2032}\x{2032}\x{2032}\x{2032}", # U+2057 => 2032 2032 2032 2032
"\xE2\x81\xA0" => "",                         # U+2060 => 
"\xE2\x81\xA1" => "",                         # U+2061 => 
"\xE2\x81\xA2" => "",                         # U+2062 => 
"\xE2\x81\xA3" => "",                         # U+2063 => 
"\xE2\x81\xA4" => "",                         # U+2064 => 
"\xE2\x81\xA5" => "",                         # U+2065 => 
"\xE2\x81\xA6" => "",                         # U+2066 => 
"\xE2\x81\xA7" => "",                         # U+2067 => 
"\xE2\x81\xA8" => "",                         # U+2068 => 
"\xE2\x81\xA9" => "",                         # U+2069 => 
"\xE2\x81\xAA" => "",                         # U+206A => 
"\xE2\x81\xAB" => "",                         # U+206B => 
"\xE2\x81\xAC" => "",                         # U+206C => 
"\xE2\x81\xAD" => "",                         # U+206D => 
"\xE2\x81\xAE" => "",                         # U+206E => 
"\xE2\x81\xAF" => "",                         # U+206F => 
"\xE2\x82\xA8" => "\x{0072}\x{0073}",         # U+20A8 => 0072 0073
"\xE2\x84\x80" => "\x{0061}\x{002F}\x{0063}", # U+2100 => 0061 002F 0063
"\xE2\x84\x81" => "\x{0061}\x{002F}\x{0073}", # U+2101 => 0061 002F 0073
"\xE2\x84\x83" => "\x{00B0}\x{0063}",         # U+2103 => 00B0 0063
"\xE2\x84\x85" => "\x{0063}\x{002F}\x{006F}", # U+2105 => 0063 002F 006F
"\xE2\x84\x86" => "\x{0063}\x{002F}\x{0075}", # U+2106 => 0063 002F 0075
"\xE2\x84\x89" => "\x{00B0}\x{0066}",         # U+2109 => 00B0 0066
"\xE2\x84\x96" => "\x{006E}\x{006F}",         # U+2116 => 006E 006F
"\xE2\x84\xA0" => "\x{0073}\x{006D}",         # U+2120 => 0073 006D
"\xE2\x84\xA1" => "\x{0074}\x{0065}\x{006C}", # U+2121 => 0074 0065 006C
"\xE2\x84\xA2" => "\x{0074}\x{006D}",         # U+2122 => 0074 006D
"\xE2\x84\xBB" => "\x{0066}\x{0061}\x{0078}", # U+213B => 0066 0061 0078
"\xE2\x85\x90" => "\x{0031}\x{2044}\x{0037}", # U+2150 => 0031 2044 0037
"\xE2\x85\x91" => "\x{0031}\x{2044}\x{0039}", # U+2151 => 0031 2044 0039
"\xE2\x85\x92" => "\x{0031}\x{2044}\x{0031}\x{0030}", # U+2152 => 0031 2044 0031 0030
"\xE2\x85\x93" => "\x{0031}\x{2044}\x{0033}", # U+2153 => 0031 2044 0033
"\xE2\x85\x94" => "\x{0032}\x{2044}\x{0033}", # U+2154 => 0032 2044 0033
"\xE2\x85\x95" => "\x{0031}\x{2044}\x{0035}", # U+2155 => 0031 2044 0035
"\xE2\x85\x96" => "\x{0032}\x{2044}\x{0035}", # U+2156 => 0032 2044 0035
"\xE2\x85\x97" => "\x{0033}\x{2044}\x{0035}", # U+2157 => 0033 2044 0035
"\xE2\x85\x98" => "\x{0034}\x{2044}\x{0035}", # U+2158 => 0034 2044 0035
"\xE2\x85\x99" => "\x{0031}\x{2044}\x{0036}", # U+2159 => 0031 2044 0036
"\xE2\x85\x9A" => "\x{0035}\x{2044}\x{0036}", # U+215A => 0035 2044 0036
"\xE2\x85\x9B" => "\x{0031}\x{2044}\x{0038}", # U+215B => 0031 2044 0038
"\xE2\x85\x9C" => "\x{0033}\x{2044}\x{0038}", # U+215C => 0033 2044 0038
"\xE2\x85\x9D" => "\x{0035}\x{2044}\x{0038}", # U+215D => 0035 2044 0038
"\xE2\x85\x9E" => "\x{0037}\x{2044}\x{0038}", # U+215E => 0037 2044 0038
"\xE2\x85\x9F" => "\x{0031}\x{2044}",         # U+215F => 0031 2044
"\xE2\x85\xA1" => "\x{0069}\x{0069}",         # U+2161 => 0069 0069
"\xE2\x85\xA2" => "\x{0069}\x{0069}\x{0069}", # U+2162 => 0069 0069 0069
"\xE2\x85\xA3" => "\x{0069}\x{0076}",         # U+2163 => 0069 0076
"\xE2\x85\xA5" => "\x{0076}\x{0069}",         # U+2165 => 0076 0069
"\xE2\x85\xA6" => "\x{0076}\x{0069}\x{0069}", # U+2166 => 0076 0069 0069
"\xE2\x85\xA7" => "\x{0076}\x{0069}\x{0069}\x{0069}", # U+2167 => 0076 0069 0069 0069
"\xE2\x85\xA8" => "\x{0069}\x{0078}",         # U+2168 => 0069 0078
"\xE2\x85\xAA" => "\x{0078}\x{0069}",         # U+216A => 0078 0069
"\xE2\x85\xAB" => "\x{0078}\x{0069}\x{0069}", # U+216B => 0078 0069 0069
"\xE2\x85\xB1" => "\x{0069}\x{0069}",         # U+2171 => 0069 0069
"\xE2\x85\xB2" => "\x{0069}\x{0069}\x{0069}", # U+2172 => 0069 0069 0069
"\xE2\x85\xB3" => "\x{0069}\x{0076}",         # U+2173 => 0069 0076
"\xE2\x85\xB5" => "\x{0076}\x{0069}",         # U+2175 => 0076 0069
"\xE2\x85\xB6" => "\x{0076}\x{0069}\x{0069}", # U+2176 => 0076 0069 0069
"\xE2\x85\xB7" => "\x{0076}\x{0069}\x{0069}\x{0069}", # U+2177 => 0076 0069 0069 0069
"\xE2\x85\xB8" => "\x{0069}\x{0078}",         # U+2178 => 0069 0078
"\xE2\x85\xBA" => "\x{0078}\x{0069}",         # U+217A => 0078 0069
"\xE2\x85\xBB" => "\x{0078}\x{0069}\x{0069}", # U+217B => 0078 0069 0069
"\xE2\x86\x89" => "\x{0030}\x{2044}\x{0033}", # U+2189 => 0030 2044 0033
"\xE2\x88\xAC" => "\x{222B}\x{222B}",         # U+222C => 222B 222B
"\xE2\x88\xAD" => "\x{222B}\x{222B}\x{222B}", # U+222D => 222B 222B 222B
"\xE2\x88\xAF" => "\x{222E}\x{222E}",         # U+222F => 222E 222E
"\xE2\x88\xB0" => "\x{222E}\x{222E}\x{222E}", # U+2230 => 222E 222E 222E
"\xE2\x91\xA9" => "\x{0031}\x{0030}",         # U+2469 => 0031 0030
"\xE2\x91\xAA" => "\x{0031}\x{0031}",         # U+246A => 0031 0031
"\xE2\x91\xAB" => "\x{0031}\x{0032}",         # U+246B => 0031 0032
"\xE2\x91\xAC" => "\x{0031}\x{0033}",         # U+246C => 0031 0033
"\xE2\x91\xAD" => "\x{0031}\x{0034}",         # U+246D => 0031 0034
"\xE2\x91\xAE" => "\x{0031}\x{0035}",         # U+246E => 0031 0035
"\xE2\x91\xAF" => "\x{0031}\x{0036}",         # U+246F => 0031 0036
"\xE2\x91\xB0" => "\x{0031}\x{0037}",         # U+2470 => 0031 0037
"\xE2\x91\xB1" => "\x{0031}\x{0038}",         # U+2471 => 0031 0038
"\xE2\x91\xB2" => "\x{0031}\x{0039}",         # U+2472 => 0031 0039
"\xE2\x91\xB3" => "\x{0032}\x{0030}",         # U+2473 => 0032 0030
"\xE2\x91\xB4" => "\x{0028}\x{0031}\x{0029}", # U+2474 => 0028 0031 0029
"\xE2\x91\xB5" => "\x{0028}\x{0032}\x{0029}", # U+2475 => 0028 0032 0029
"\xE2\x91\xB6" => "\x{0028}\x{0033}\x{0029}", # U+2476 => 0028 0033 0029
"\xE2\x91\xB7" => "\x{0028}\x{0034}\x{0029}", # U+2477 => 0028 0034 0029
"\xE2\x91\xB8" => "\x{0028}\x{0035}\x{0029}", # U+2478 => 0028 0035 0029
"\xE2\x91\xB9" => "\x{0028}\x{0036}\x{0029}", # U+2479 => 0028 0036 0029
"\xE2\x91\xBA" => "\x{0028}\x{0037}\x{0029}", # U+247A => 0028 0037 0029
"\xE2\x91\xBB" => "\x{0028}\x{0038}\x{0029}", # U+247B => 0028 0038 0029
"\xE2\x91\xBC" => "\x{0028}\x{0039}\x{0029}", # U+247C => 0028 0039 0029
"\xE2\x91\xBD" => "\x{0028}\x{0031}\x{0030}\x{0029}", # U+247D => 0028 0031 0030 0029
"\xE2\x91\xBE" => "\x{0028}\x{0031}\x{0031}\x{0029}", # U+247E => 0028 0031 0031 0029
"\xE2\x91\xBF" => "\x{0028}\x{0031}\x{0032}\x{0029}", # U+247F => 0028 0031 0032 0029
"\xE2\x92\x80" => "\x{0028}\x{0031}\x{0033}\x{0029}", # U+2480 => 0028 0031 0033 0029
"\xE2\x92\x81" => "\x{0028}\x{0031}\x{0034}\x{0029}", # U+2481 => 0028 0031 0034 0029
"\xE2\x92\x82" => "\x{0028}\x{0031}\x{0035}\x{0029}", # U+2482 => 0028 0031 0035 0029
"\xE2\x92\x83" => "\x{0028}\x{0031}\x{0036}\x{0029}", # U+2483 => 0028 0031 0036 0029
"\xE2\x92\x84" => "\x{0028}\x{0031}\x{0037}\x{0029}", # U+2484 => 0028 0031 0037 0029
"\xE2\x92\x85" => "\x{0028}\x{0031}\x{0038}\x{0029}", # U+2485 => 0028 0031 0038 0029
"\xE2\x92\x86" => "\x{0028}\x{0031}\x{0039}\x{0029}", # U+2486 => 0028 0031 0039 0029
"\xE2\x92\x87" => "\x{0028}\x{0032}\x{0030}\x{0029}", # U+2487 => 0028 0032 0030 0029
"\xE2\x92\x88" => "\x{0031}\x{002E}",         # U+2488 => 0031 002E
"\xE2\x92\x89" => "\x{0032}\x{002E}",         # U+2489 => 0032 002E
"\xE2\x92\x8A" => "\x{0033}\x{002E}",         # U+248A => 0033 002E
"\xE2\x92\x8B" => "\x{0034}\x{002E}",         # U+248B => 0034 002E
"\xE2\x92\x8C" => "\x{0035}\x{002E}",         # U+248C => 0035 002E
"\xE2\x92\x8D" => "\x{0036}\x{002E}",         # U+248D => 0036 002E
"\xE2\x92\x8E" => "\x{0037}\x{002E}",         # U+248E => 0037 002E
"\xE2\x92\x8F" => "\x{0038}\x{002E}",         # U+248F => 0038 002E
"\xE2\x92\x90" => "\x{0039}\x{002E}",         # U+2490 => 0039 002E
"\xE2\x92\x91" => "\x{0031}\x{0030}\x{002E}", # U+2491 => 0031 0030 002E
"\xE2\x92\x92" => "\x{0031}\x{0031}\x{002E}", # U+2492 => 0031 0031 002E
"\xE2\x92\x93" => "\x{0031}\x{0032}\x{002E}", # U+2493 => 0031 0032 002E
"\xE2\x92\x94" => "\x{0031}\x{0033}\x{002E}", # U+2494 => 0031 0033 002E
"\xE2\x92\x95" => "\x{0031}\x{0034}\x{002E}", # U+2495 => 0031 0034 002E
"\xE2\x92\x96" => "\x{0031}\x{0035}\x{002E}", # U+2496 => 0031 0035 002E
"\xE2\x92\x97" => "\x{0031}\x{0036}\x{002E}", # U+2497 => 0031 0036 002E
"\xE2\x92\x98" => "\x{0031}\x{0037}\x{002E}", # U+2498 => 0031 0037 002E
"\xE2\x92\x99" => "\x{0031}\x{0038}\x{002E}", # U+2499 => 0031 0038 002E
"\xE2\x92\x9A" => "\x{0031}\x{0039}\x{002E}", # U+249A => 0031 0039 002E
"\xE2\x92\x9B" => "\x{0032}\x{0030}\x{002E}", # U+249B => 0032 0030 002E
"\xE2\x92\x9C" => "\x{0028}\x{0061}\x{0029}", # U+249C => 0028 0061 0029
"\xE2\x92\x9D" => "\x{0028}\x{0062}\x{0029}", # U+249D => 0028 0062 0029
"\xE2\x92\x9E" => "\x{0028}\x{0063}\x{0029}", # U+249E => 0028 0063 0029
"\xE2\x92\x9F" => "\x{0028}\x{0064}\x{0029}", # U+249F => 0028 0064 0029
"\xE2\x92\xA0" => "\x{0028}\x{0065}\x{0029}", # U+24A0 => 0028 0065 0029
"\xE2\x92\xA1" => "\x{0028}\x{0066}\x{0029}", # U+24A1 => 0028 0066 0029
"\xE2\x92\xA2" => "\x{0028}\x{0067}\x{0029}", # U+24A2 => 0028 0067 0029
"\xE2\x92\xA3" => "\x{0028}\x{0068}\x{0029}", # U+24A3 => 0028 0068 0029
"\xE2\x92\xA4" => "\x{0028}\x{0069}\x{0029}", # U+24A4 => 0028 0069 0029
"\xE2\x92\xA5" => "\x{0028}\x{006A}\x{0029}", # U+24A5 => 0028 006A 0029
"\xE2\x92\xA6" => "\x{0028}\x{006B}\x{0029}", # U+24A6 => 0028 006B 0029
"\xE2\x92\xA7" => "\x{0028}\x{006C}\x{0029}", # U+24A7 => 0028 006C 0029
"\xE2\x92\xA8" => "\x{0028}\x{006D}\x{0029}", # U+24A8 => 0028 006D 0029
"\xE2\x92\xA9" => "\x{0028}\x{006E}\x{0029}", # U+24A9 => 0028 006E 0029
"\xE2\x92\xAA" => "\x{0028}\x{006F}\x{0029}", # U+24AA => 0028 006F 0029
"\xE2\x92\xAB" => "\x{0028}\x{0070}\x{0029}", # U+24AB => 0028 0070 0029
"\xE2\x92\xAC" => "\x{0028}\x{0071}\x{0029}", # U+24AC => 0028 0071 0029
"\xE2\x92\xAD" => "\x{0028}\x{0072}\x{0029}", # U+24AD => 0028 0072 0029
"\xE2\x92\xAE" => "\x{0028}\x{0073}\x{0029}", # U+24AE => 0028 0073 0029
"\xE2\x92\xAF" => "\x{0028}\x{0074}\x{0029}", # U+24AF => 0028 0074 0029
"\xE2\x92\xB0" => "\x{0028}\x{0075}\x{0029}", # U+24B0 => 0028 0075 0029
"\xE2\x92\xB1" => "\x{0028}\x{0076}\x{0029}", # U+24B1 => 0028 0076 0029
"\xE2\x92\xB2" => "\x{0028}\x{0077}\x{0029}", # U+24B2 => 0028 0077 0029
"\xE2\x92\xB3" => "\x{0028}\x{0078}\x{0029}", # U+24B3 => 0028 0078 0029
"\xE2\x92\xB4" => "\x{0028}\x{0079}\x{0029}", # U+24B4 => 0028 0079 0029
"\xE2\x92\xB5" => "\x{0028}\x{007A}\x{0029}", # U+24B5 => 0028 007A 0029
"\xE2\xA8\x8C" => "\x{222B}\x{222B}\x{222B}\x{222B}", # U+2A0C => 222B 222B 222B 222B
"\xE2\xA9\xB4" => "\x{003A}\x{003A}\x{003D}", # U+2A74 => 003A 003A 003D
"\xE2\xA9\xB5" => "\x{003D}\x{003D}",         # U+2A75 => 003D 003D
"\xE2\xA9\xB6" => "\x{003D}\x{003D}\x{003D}", # U+2A76 => 003D 003D 003D
"\xE2\xAB\x9C" => "\x{2ADD}\x{0338}",         # U+2ADC => 2ADD 0338
"\xE3\x82\x9B" => "\x{0020}\x{3099}",         # U+309B => 0020 3099
"\xE3\x82\x9C" => "\x{0020}\x{309A}",         # U+309C => 0020 309A
"\xE3\x82\x9F" => "\x{3088}\x{308A}",         # U+309F => 3088 308A
"\xE3\x83\xBF" => "\x{30B3}\x{30C8}",         # U+30FF => 30B3 30C8
"\xE3\x85\xA4" => "",                         # U+3164 => 
"\xE3\x88\x80" => "\x{0028}\x{1100}\x{0029}", # U+3200 => 0028 1100 0029
"\xE3\x88\x81" => "\x{0028}\x{1102}\x{0029}", # U+3201 => 0028 1102 0029
"\xE3\x88\x82" => "\x{0028}\x{1103}\x{0029}", # U+3202 => 0028 1103 0029
"\xE3\x88\x83" => "\x{0028}\x{1105}\x{0029}", # U+3203 => 0028 1105 0029
"\xE3\x88\x84" => "\x{0028}\x{1106}\x{0029}", # U+3204 => 0028 1106 0029
"\xE3\x88\x85" => "\x{0028}\x{1107}\x{0029}", # U+3205 => 0028 1107 0029
"\xE3\x88\x86" => "\x{0028}\x{1109}\x{0029}", # U+3206 => 0028 1109 0029
"\xE3\x88\x87" => "\x{0028}\x{110B}\x{0029}", # U+3207 => 0028 110B 0029
"\xE3\x88\x88" => "\x{0028}\x{110C}\x{0029}", # U+3208 => 0028 110C 0029
"\xE3\x88\x89" => "\x{0028}\x{110E}\x{0029}", # U+3209 => 0028 110E 0029
"\xE3\x88\x8A" => "\x{0028}\x{110F}\x{0029}", # U+320A => 0028 110F 0029
"\xE3\x88\x8B" => "\x{0028}\x{1110}\x{0029}", # U+320B => 0028 1110 0029
"\xE3\x88\x8C" => "\x{0028}\x{1111}\x{0029}", # U+320C => 0028 1111 0029
"\xE3\x88\x8D" => "\x{0028}\x{1112}\x{0029}", # U+320D => 0028 1112 0029
"\xE3\x88\x8E" => "\x{0028}\x{AC00}\x{0029}", # U+320E => 0028 AC00 0029
"\xE3\x88\x8F" => "\x{0028}\x{B098}\x{0029}", # U+320F => 0028 B098 0029
"\xE3\x88\x90" => "\x{0028}\x{B2E4}\x{0029}", # U+3210 => 0028 B2E4 0029
"\xE3\x88\x91" => "\x{0028}\x{B77C}\x{0029}", # U+3211 => 0028 B77C 0029
"\xE3\x88\x92" => "\x{0028}\x{B9C8}\x{0029}", # U+3212 => 0028 B9C8 0029
"\xE3\x88\x93" => "\x{0028}\x{BC14}\x{0029}", # U+3213 => 0028 BC14 0029
"\xE3\x88\x94" => "\x{0028}\x{C0AC}\x{0029}", # U+3214 => 0028 C0AC 0029
"\xE3\x88\x95" => "\x{0028}\x{C544}\x{0029}", # U+3215 => 0028 C544 0029
"\xE3\x88\x96" => "\x{0028}\x{C790}\x{0029}", # U+3216 => 0028 C790 0029
"\xE3\x88\x97" => "\x{0028}\x{CC28}\x{0029}", # U+3217 => 0028 CC28 0029
"\xE3\x88\x98" => "\x{0028}\x{CE74}\x{0029}", # U+3218 => 0028 CE74 0029
"\xE3\x88\x99" => "\x{0028}\x{D0C0}\x{0029}", # U+3219 => 0028 D0C0 0029
"\xE3\x88\x9A" => "\x{0028}\x{D30C}\x{0029}", # U+321A => 0028 D30C 0029
"\xE3\x88\x9B" => "\x{0028}\x{D558}\x{0029}", # U+321B => 0028 D558 0029
"\xE3\x88\x9C" => "\x{0028}\x{C8FC}\x{0029}", # U+321C => 0028 C8FC 0029
"\xE3\x88\x9D" => "\x{0028}\x{C624}\x{C804}\x{0029}", # U+321D => 0028 C624 C804 0029
"\xE3\x88\x9E" => "\x{0028}\x{C624}\x{D6C4}\x{0029}", # U+321E => 0028 C624 D6C4 0029
"\xE3\x88\xA0" => "\x{0028}\x{4E00}\x{0029}", # U+3220 => 0028 4E00 0029
"\xE3\x88\xA1" => "\x{0028}\x{4E8C}\x{0029}", # U+3221 => 0028 4E8C 0029
"\xE3\x88\xA2" => "\x{0028}\x{4E09}\x{0029}", # U+3222 => 0028 4E09 0029
"\xE3\x88\xA3" => "\x{0028}\x{56DB}\x{0029}", # U+3223 => 0028 56DB 0029
"\xE3\x88\xA4" => "\x{0028}\x{4E94}\x{0029}", # U+3224 => 0028 4E94 0029
"\xE3\x88\xA5" => "\x{0028}\x{516D}\x{0029}", # U+3225 => 0028 516D 0029
"\xE3\x88\xA6" => "\x{0028}\x{4E03}\x{0029}", # U+3226 => 0028 4E03 0029
"\xE3\x88\xA7" => "\x{0028}\x{516B}\x{0029}", # U+3227 => 0028 516B 0029
"\xE3\x88\xA8" => "\x{0028}\x{4E5D}\x{0029}", # U+3228 => 0028 4E5D 0029
"\xE3\x88\xA9" => "\x{0028}\x{5341}\x{0029}", # U+3229 => 0028 5341 0029
"\xE3\x88\xAA" => "\x{0028}\x{6708}\x{0029}", # U+322A => 0028 6708 0029
"\xE3\x88\xAB" => "\x{0028}\x{706B}\x{0029}", # U+322B => 0028 706B 0029
"\xE3\x88\xAC" => "\x{0028}\x{6C34}\x{0029}", # U+322C => 0028 6C34 0029
"\xE3\x88\xAD" => "\x{0028}\x{6728}\x{0029}", # U+322D => 0028 6728 0029
"\xE3\x88\xAE" => "\x{0028}\x{91D1}\x{0029}", # U+322E => 0028 91D1 0029
"\xE3\x88\xAF" => "\x{0028}\x{571F}\x{0029}", # U+322F => 0028 571F 0029
"\xE3\x88\xB0" => "\x{0028}\x{65E5}\x{0029}", # U+3230 => 0028 65E5 0029
"\xE3\x88\xB1" => "\x{0028}\x{682A}\x{0029}", # U+3231 => 0028 682A 0029
"\xE3\x88\xB2" => "\x{0028}\x{6709}\x{0029}", # U+3232 => 0028 6709 0029
"\xE3\x88\xB3" => "\x{0028}\x{793E}\x{0029}", # U+3233 => 0028 793E 0029
"\xE3\x88\xB4" => "\x{0028}\x{540D}\x{0029}", # U+3234 => 0028 540D 0029
"\xE3\x88\xB5" => "\x{0028}\x{7279}\x{0029}", # U+3235 => 0028 7279 0029
"\xE3\x88\xB6" => "\x{0028}\x{8CA1}\x{0029}", # U+3236 => 0028 8CA1 0029
"\xE3\x88\xB7" => "\x{0028}\x{795D}\x{0029}", # U+3237 => 0028 795D 0029
"\xE3\x88\xB8" => "\x{0028}\x{52B4}\x{0029}", # U+3238 => 0028 52B4 0029
"\xE3\x88\xB9" => "\x{0028}\x{4EE3}\x{0029}", # U+3239 => 0028 4EE3 0029
"\xE3\x88\xBA" => "\x{0028}\x{547C}\x{0029}", # U+323A => 0028 547C 0029
"\xE3\x88\xBB" => "\x{0028}\x{5B66}\x{0029}", # U+323B => 0028 5B66 0029
"\xE3\x88\xBC" => "\x{0028}\x{76E3}\x{0029}", # U+323C => 0028 76E3 0029
"\xE3\x88\xBD" => "\x{0028}\x{4F01}\x{0029}", # U+323D => 0028 4F01 0029
"\xE3\x88\xBE" => "\x{0028}\x{8CC7}\x{0029}", # U+323E => 0028 8CC7 0029
"\xE3\x88\xBF" => "\x{0028}\x{5354}\x{0029}", # U+323F => 0028 5354 0029
"\xE3\x89\x80" => "\x{0028}\x{796D}\x{0029}", # U+3240 => 0028 796D 0029
"\xE3\x89\x81" => "\x{0028}\x{4F11}\x{0029}", # U+3241 => 0028 4F11 0029
"\xE3\x89\x82" => "\x{0028}\x{81EA}\x{0029}", # U+3242 => 0028 81EA 0029
"\xE3\x89\x83" => "\x{0028}\x{81F3}\x{0029}", # U+3243 => 0028 81F3 0029
"\xE3\x89\x90" => "\x{0070}\x{0074}\x{0065}", # U+3250 => 0070 0074 0065
"\xE3\x89\x91" => "\x{0032}\x{0031}",         # U+3251 => 0032 0031
"\xE3\x89\x92" => "\x{0032}\x{0032}",         # U+3252 => 0032 0032
"\xE3\x89\x93" => "\x{0032}\x{0033}",         # U+3253 => 0032 0033
"\xE3\x89\x94" => "\x{0032}\x{0034}",         # U+3254 => 0032 0034
"\xE3\x89\x95" => "\x{0032}\x{0035}",         # U+3255 => 0032 0035
"\xE3\x89\x96" => "\x{0032}\x{0036}",         # U+3256 => 0032 0036
"\xE3\x89\x97" => "\x{0032}\x{0037}",         # U+3257 => 0032 0037
"\xE3\x89\x98" => "\x{0032}\x{0038}",         # U+3258 => 0032 0038
"\xE3\x89\x99" => "\x{0032}\x{0039}",         # U+3259 => 0032 0039
"\xE3\x89\x9A" => "\x{0033}\x{0030}",         # U+325A => 0033 0030
"\xE3\x89\x9B" => "\x{0033}\x{0031}",         # U+325B => 0033 0031
"\xE3\x89\x9C" => "\x{0033}\x{0032}",         # U+325C => 0033 0032
"\xE3\x89\x9D" => "\x{0033}\x{0033}",         # U+325D => 0033 0033
"\xE3\x89\x9E" => "\x{0033}\x{0034}",         # U+325E => 0033 0034
"\xE3\x89\x9F" => "\x{0033}\x{0035}",         # U+325F => 0033 0035
"\xE3\x89\xBC" => "\x{CC38}\x{ACE0}",         # U+327C => CC38 ACE0
"\xE3\x89\xBD" => "\x{C8FC}\x{C758}",         # U+327D => C8FC C758
"\xE3\x8A\xB1" => "\x{0033}\x{0036}",         # U+32B1 => 0033 0036
"\xE3\x8A\xB2" => "\x{0033}\x{0037}",         # U+32B2 => 0033 0037
"\xE3\x8A\xB3" => "\x{0033}\x{0038}",         # U+32B3 => 0033 0038
"\xE3\x8A\xB4" => "\x{0033}\x{0039}",         # U+32B4 => 0033 0039
"\xE3\x8A\xB5" => "\x{0034}\x{0030}",         # U+32B5 => 0034 0030
"\xE3\x8A\xB6" => "\x{0034}\x{0031}",         # U+32B6 => 0034 0031
"\xE3\x8A\xB7" => "\x{0034}\x{0032}",         # U+32B7 => 0034 0032
"\xE3\x8A\xB8" => "\x{0034}\x{0033}",         # U+32B8 => 0034 0033
"\xE3\x8A\xB9" => "\x{0034}\x{0034}",         # U+32B9 => 0034 0034
"\xE3\x8A\xBA" => "\x{0034}\x{0035}",         # U+32BA => 0034 0035
"\xE3\x8A\xBB" => "\x{0034}\x{0036}",         # U+32BB => 0034 0036
"\xE3\x8A\xBC" => "\x{0034}\x{0037}",         # U+32BC => 0034 0037
"\xE3\x8A\xBD" => "\x{0034}\x{0038}",         # U+32BD => 0034 0038
"\xE3\x8A\xBE" => "\x{0034}\x{0039}",         # U+32BE => 0034 0039
"\xE3\x8A\xBF" => "\x{0035}\x{0030}",         # U+32BF => 0035 0030
"\xE3\x8B\x80" => "\x{0031}\x{6708}",         # U+32C0 => 0031 6708
"\xE3\x8B\x81" => "\x{0032}\x{6708}",         # U+32C1 => 0032 6708
"\xE3\x8B\x82" => "\x{0033}\x{6708}",         # U+32C2 => 0033 6708
"\xE3\x8B\x83" => "\x{0034}\x{6708}",         # U+32C3 => 0034 6708
"\xE3\x8B\x84" => "\x{0035}\x{6708}",         # U+32C4 => 0035 6708
"\xE3\x8B\x85" => "\x{0036}\x{6708}",         # U+32C5 => 0036 6708
"\xE3\x8B\x86" => "\x{0037}\x{6708}",         # U+32C6 => 0037 6708
"\xE3\x8B\x87" => "\x{0038}\x{6708}",         # U+32C7 => 0038 6708
"\xE3\x8B\x88" => "\x{0039}\x{6708}",         # U+32C8 => 0039 6708
"\xE3\x8B\x89" => "\x{0031}\x{0030}\x{6708}", # U+32C9 => 0031 0030 6708
"\xE3\x8B\x8A" => "\x{0031}\x{0031}\x{6708}", # U+32CA => 0031 0031 6708
"\xE3\x8B\x8B" => "\x{0031}\x{0032}\x{6708}", # U+32CB => 0031 0032 6708
"\xE3\x8B\x8C" => "\x{0068}\x{0067}",         # U+32CC => 0068 0067
"\xE3\x8B\x8D" => "\x{0065}\x{0072}\x{0067}", # U+32CD => 0065 0072 0067
"\xE3\x8B\x8E" => "\x{0065}\x{0076}",         # U+32CE => 0065 0076
"\xE3\x8B\x8F" => "\x{006C}\x{0074}\x{0064}", # U+32CF => 006C 0074 0064
"\xE3\x8B\xBF" => "\x{4EE4}\x{548C}",         # U+32FF => 4EE4 548C
"\xE3\x8C\x80" => "\x{30A2}\x{30D1}\x{30FC}\x{30C8}", # U+3300 => 30A2 30D1 30FC 30C8
"\xE3\x8C\x81" => "\x{30A2}\x{30EB}\x{30D5}\x{30A1}", # U+3301 => 30A2 30EB 30D5 30A1
"\xE3\x8C\x82" => "\x{30A2}\x{30F3}\x{30DA}\x{30A2}", # U+3302 => 30A2 30F3 30DA 30A2
"\xE3\x8C\x83" => "\x{30A2}\x{30FC}\x{30EB}", # U+3303 => 30A2 30FC 30EB
"\xE3\x8C\x84" => "\x{30A4}\x{30CB}\x{30F3}\x{30B0}", # U+3304 => 30A4 30CB 30F3 30B0
"\xE3\x8C\x85" => "\x{30A4}\x{30F3}\x{30C1}", # U+3305 => 30A4 30F3 30C1
"\xE3\x8C\x86" => "\x{30A6}\x{30A9}\x{30F3}", # U+3306 => 30A6 30A9 30F3
"\xE3\x8C\x87" => "\x{30A8}\x{30B9}\x{30AF}\x{30FC}\x{30C9}", # U+3307 => 30A8 30B9 30AF 30FC 30C9
"\xE3\x8C\x88" => "\x{30A8}\x{30FC}\x{30AB}\x{30FC}", # U+3308 => 30A8 30FC 30AB 30FC
"\xE3\x8C\x89" => "\x{30AA}\x{30F3}\x{30B9}", # U+3309 => 30AA 30F3 30B9
"\xE3\x8C\x8A" => "\x{30AA}\x{30FC}\x{30E0}", # U+330A => 30AA 30FC 30E0
"\xE3\x8C\x8B" => "\x{30AB}\x{30A4}\x{30EA}", # U+330B => 30AB 30A4 30EA
"\xE3\x8C\x8C" => "\x{30AB}\x{30E9}\x{30C3}\x{30C8}", # U+330C => 30AB 30E9 30C3 30C8
"\xE3\x8C\x8D" => "\x{30AB}\x{30ED}\x{30EA}\x{30FC}", # U+330D => 30AB 30ED 30EA 30FC
"\xE3\x8C\x8E" => "\x{30AC}\x{30ED}\x{30F3}", # U+330E => 30AC 30ED 30F3
"\xE3\x8C\x8F" => "\x{30AC}\x{30F3}\x{30DE}", # U+330F => 30AC 30F3 30DE
"\xE3\x8C\x90" => "\x{30AE}\x{30AC}",         # U+3310 => 30AE 30AC
"\xE3\x8C\x91" => "\x{30AE}\x{30CB}\x{30FC}", # U+3311 => 30AE 30CB 30FC
"\xE3\x8C\x92" => "\x{30AD}\x{30E5}\x{30EA}\x{30FC}", # U+3312 => 30AD 30E5 30EA 30FC
"\xE3\x8C\x93" => "\x{30AE}\x{30EB}\x{30C0}\x{30FC}", # U+3313 => 30AE 30EB 30C0 30FC
"\xE3\x8C\x94" => "\x{30AD}\x{30ED}",         # U+3314 => 30AD 30ED
"\xE3\x8C\x95" => "\x{30AD}\x{30ED}\x{30B0}\x{30E9}\x{30E0}", # U+3315 => 30AD 30ED 30B0 30E9 30E0
"\xE3\x8C\x96" => "\x{30AD}\x{30ED}\x{30E1}\x{30FC}\x{30C8}\x{30EB}", # U+3316 => 30AD 30ED 30E1 30FC 30C8 30EB
"\xE3\x8C\x97" => "\x{30AD}\x{30ED}\x{30EF}\x{30C3}\x{30C8}", # U+3317 => 30AD 30ED 30EF 30C3 30C8
"\xE3\x8C\x98" => "\x{30B0}\x{30E9}\x{30E0}", # U+3318 => 30B0 30E9 30E0
"\xE3\x8C\x99" => "\x{30B0}\x{30E9}\x{30E0}\x{30C8}\x{30F3}", # U+3319 => 30B0 30E9 30E0 30C8 30F3
"\xE3\x8C\x9A" => "\x{30AF}\x{30EB}\x{30BC}\x{30A4}\x{30ED}", # U+331A => 30AF 30EB 30BC 30A4 30ED
"\xE3\x8C\x9B" => "\x{30AF}\x{30ED}\x{30FC}\x{30CD}", # U+331B => 30AF 30ED 30FC 30CD
"\xE3\x8C\x9C" => "\x{30B1}\x{30FC}\x{30B9}", # U+331C => 30B1 30FC 30B9
"\xE3\x8C\x9D" => "\x{30B3}\x{30EB}\x{30CA}", # U+331D => 30B3 30EB 30CA
"\xE3\x8C\x9E" => "\x{30B3}\x{30FC}\x{30DD}", # U+331E => 30B3 30FC 30DD
"\xE3\x8C\x9F" => "\x{30B5}\x{30A4}\x{30AF}\x{30EB}", # U+331F => 30B5 30A4 30AF 30EB
"\xE3\x8C\xA0" => "\x{30B5}\x{30F3}\x{30C1}\x{30FC}\x{30E0}", # U+3320 => 30B5 30F3 30C1 30FC 30E0
"\xE3\x8C\xA1" => "\x{30B7}\x{30EA}\x{30F3}\x{30B0}", # U+3321 => 30B7 30EA 30F3 30B0
"\xE3\x8C\xA2" => "\x{30BB}\x{30F3}\x{30C1}", # U+3322 => 30BB 30F3 30C1
"\xE3\x8C\xA3" => "\x{30BB}\x{30F3}\x{30C8}", # U+3323 => 30BB 30F3 30C8
"\xE3\x8C\xA4" => "\x{30C0}\x{30FC}\x{30B9}", # U+3324 => 30C0 30FC 30B9
"\xE3\x8C\xA5" => "\x{30C7}\x{30B7}",         # U+3325 => 30C7 30B7
"\xE3\x8C\xA6" => "\x{30C9}\x{30EB}",         # U+3326 => 30C9 30EB
"\xE3\x8C\xA7" => "\x{30C8}\x{30F3}",         # U+3327 => 30C8 30F3
"\xE3\x8C\xA8" => "\x{30CA}\x{30CE}",         # U+3328 => 30CA 30CE
"\xE3\x8C\xA9" => "\x{30CE}\x{30C3}\x{30C8}", # U+3329 => 30CE 30C3 30C8
"\xE3\x8C\xAA" => "\x{30CF}\x{30A4}\x{30C4}", # U+332A => 30CF 30A4 30C4
"\xE3\x8C\xAB" => "\x{30D1}\x{30FC}\x{30BB}\x{30F3}\x{30C8}", # U+332B => 30D1 30FC 30BB 30F3 30C8
"\xE3\x8C\xAC" => "\x{30D1}\x{30FC}\x{30C4}", # U+332C => 30D1 30FC 30C4
"\xE3\x8C\xAD" => "\x{30D0}\x{30FC}\x{30EC}\x{30EB}", # U+332D => 30D0 30FC 30EC 30EB
"\xE3\x8C\xAE" => "\x{30D4}\x{30A2}\x{30B9}\x{30C8}\x{30EB}", # U+332E => 30D4 30A2 30B9 30C8 30EB
"\xE3\x8C\xAF" => "\x{30D4}\x{30AF}\x{30EB}", # U+332F => 30D4 30AF 30EB
"\xE3\x8C\xB0" => "\x{30D4}\x{30B3}",         # U+3330 => 30D4 30B3
"\xE3\x8C\xB1" => "\x{30D3}\x{30EB}",         # U+3331 => 30D3 30EB
"\xE3\x8C\xB2" => "\x{30D5}\x{30A1}\x{30E9}\x{30C3}\x{30C9}", # U+3332 => 30D5 30A1 30E9 30C3 30C9
"\xE3\x8C\xB3" => "\x{30D5}\x{30A3}\x{30FC}\x{30C8}", # U+3333 => 30D5 30A3 30FC 30C8
"\xE3\x8C\xB4" => "\x{30D6}\x{30C3}\x{30B7}\x{30A7}\x{30EB}", # U+3334 => 30D6 30C3 30B7 30A7 30EB
"\xE3\x8C\xB5" => "\x{30D5}\x{30E9}\x{30F3}", # U+3335 => 30D5 30E9 30F3
"\xE3\x8C\xB6" => "\x{30D8}\x{30AF}\x{30BF}\x{30FC}\x{30EB}", # U+3336 => 30D8 30AF 30BF 30FC 30EB
"\xE3\x8C\xB7" => "\x{30DA}\x{30BD}",         # U+3337 => 30DA 30BD
"\xE3\x8C\xB8" => "\x{30DA}\x{30CB}\x{30D2}", # U+3338 => 30DA 30CB 30D2
"\xE3\x8C\xB9" => "\x{30D8}\x{30EB}\x{30C4}", # U+3339 => 30D8 30EB 30C4
"\xE3\x8C\xBA" => "\x{30DA}\x{30F3}\x{30B9}", # U+333A => 30DA 30F3 30B9
"\xE3\x8C\xBB" => "\x{30DA}\x{30FC}\x{30B8}", # U+333B => 30DA 30FC 30B8
"\xE3\x8C\xBC" => "\x{30D9}\x{30FC}\x{30BF}", # U+333C => 30D9 30FC 30BF
"\xE3\x8C\xBD" => "\x{30DD}\x{30A4}\x{30F3}\x{30C8}", # U+333D => 30DD 30A4 30F3 30C8
"\xE3\x8C\xBE" => "\x{30DC}\x{30EB}\x{30C8}", # U+333E => 30DC 30EB 30C8
"\xE3\x8C\xBF" => "\x{30DB}\x{30F3}",         # U+333F => 30DB 30F3
"\xE3\x8D\x80" => "\x{30DD}\x{30F3}\x{30C9}", # U+3340 => 30DD 30F3 30C9
"\xE3\x8D\x81" => "\x{30DB}\x{30FC}\x{30EB}", # U+3341 => 30DB 30FC 30EB
"\xE3\x8D\x82" => "\x{30DB}\x{30FC}\x{30F3}", # U+3342 => 30DB 30FC 30F3
"\xE3\x8D\x83" => "\x{30DE}\x{30A4}\x{30AF}\x{30ED}", # U+3343 => 30DE 30A4 30AF 30ED
"\xE3\x8D\x84" => "\x{30DE}\x{30A4}\x{30EB}", # U+3344 => 30DE 30A4 30EB
"\xE3\x8D\x85" => "\x{30DE}\x{30C3}\x{30CF}", # U+3345 => 30DE 30C3 30CF
"\xE3\x8D\x86" => "\x{30DE}\x{30EB}\x{30AF}", # U+3346 => 30DE 30EB 30AF
"\xE3\x8D\x87" => "\x{30DE}\x{30F3}\x{30B7}\x{30E7}\x{30F3}", # U+3347 => 30DE 30F3 30B7 30E7 30F3
"\xE3\x8D\x88" => "\x{30DF}\x{30AF}\x{30ED}\x{30F3}", # U+3348 => 30DF 30AF 30ED 30F3
"\xE3\x8D\x89" => "\x{30DF}\x{30EA}",         # U+3349 => 30DF 30EA
"\xE3\x8D\x8A" => "\x{30DF}\x{30EA}\x{30D0}\x{30FC}\x{30EB}", # U+334A => 30DF 30EA 30D0 30FC 30EB
"\xE3\x8D\x8B" => "\x{30E1}\x{30AC}",         # U+334B => 30E1 30AC
"\xE3\x8D\x8C" => "\x{30E1}\x{30AC}\x{30C8}\x{30F3}", # U+334C => 30E1 30AC 30C8 30F3
"\xE3\x8D\x8D" => "\x{30E1}\x{30FC}\x{30C8}\x{30EB}", # U+334D => 30E1 30FC 30C8 30EB
"\xE3\x8D\x8E" => "\x{30E4}\x{30FC}\x{30C9}", # U+334E => 30E4 30FC 30C9
"\xE3\x8D\x8F" => "\x{30E4}\x{30FC}\x{30EB}", # U+334F => 30E4 30FC 30EB
"\xE3\x8D\x90" => "\x{30E6}\x{30A2}\x{30F3}", # U+3350 => 30E6 30A2 30F3
"\xE3\x8D\x91" => "\x{30EA}\x{30C3}\x{30C8}\x{30EB}", # U+3351 => 30EA 30C3 30C8 30EB
"\xE3\x8D\x92" => "\x{30EA}\x{30E9}",         # U+3352 => 30EA 30E9
"\xE3\x8D\x93" => "\x{30EB}\x{30D4}\x{30FC}", # U+3353 => 30EB 30D4 30FC
"\xE3\x8D\x94" => "\x{30EB}\x{30FC}\x{30D6}\x{30EB}", # U+3354 => 30EB 30FC 30D6 30EB
"\xE3\x8D\x95" => "\x{30EC}\x{30E0}",         # U+3355 => 30EC 30E0
"\xE3\x8D\x96" => "\x{30EC}\x{30F3}\x{30C8}\x{30B2}\x{30F3}", # U+3356 => 30EC 30F3 30C8 30B2 30F3
"\xE3\x8D\x97" => "\x{30EF}\x{30C3}\x{30C8}", # U+3357 => 30EF 30C3 30C8
"\xE3\x8D\x98" => "\x{0030}\x{70B9}",         # U+3358 => 0030 70B9
"\xE3\x8D\x99" => "\x{0031}\x{70B9}",         # U+3359 => 0031 70B9
"\xE3\x8D\x9A" => "\x{0032}\x{70B9}",         # U+335A => 0032 70B9
"\xE3\x8D\x9B" => "\x{0033}\x{70B9}",         # U+335B => 0033 70B9
"\xE3\x8D\x9C" => "\x{0034}\x{70B9}",         # U+335C => 0034 70B9
"\xE3\x8D\x9D" => "\x{0035}\x{70B9}",         # U+335D => 0035 70B9
"\xE3\x8D\x9E" => "\x{0036}\x{70B9}",         # U+335E => 0036 70B9
"\xE3\x8D\x9F" => "\x{0037}\x{70B9}",         # U+335F => 0037 70B9
"\xE3\x8D\xA0" => "\x{0038}\x{70B9}",         # U+3360 => 0038 70B9
"\xE3\x8D\xA1" => "\x{0039}\x{70B9}",         # U+3361 => 0039 70B9
"\xE3\x8D\xA2" => "\x{0031}\x{0030}\x{70B9}", # U+3362 => 0031 0030 70B9
"\xE3\x8D\xA3" => "\x{0031}\x{0031}\x{70B9}", # U+3363 => 0031 0031 70B9
"\xE3\x8D\xA4" => "\x{0031}\x{0032}\x{70B9}", # U+3364 => 0031 0032 70B9
"\xE3\x8D\xA5" => "\x{0031}\x{0033}\x{70B9}", # U+3365 => 0031 0033 70B9
"\xE3\x8D\xA6" => "\x{0031}\x{0034}\x{70B9}", # U+3366 => 0031 0034 70B9
"\xE3\x8D\xA7" => "\x{0031}\x{0035}\x{70B9}", # U+3367 => 0031 0035 70B9
"\xE3\x8D\xA8" => "\x{0031}\x{0036}\x{70B9}", # U+3368 => 0031 0036 70B9
"\xE3\x8D\xA9" => "\x{0031}\x{0037}\x{70B9}", # U+3369 => 0031 0037 70B9
"\xE3\x8D\xAA" => "\x{0031}\x{0038}\x{70B9}", # U+336A => 0031 0038 70B9
"\xE3\x8D\xAB" => "\x{0031}\x{0039}\x{70B9}", # U+336B => 0031 0039 70B9
"\xE3\x8D\xAC" => "\x{0032}\x{0030}\x{70B9}", # U+336C => 0032 0030 70B9
"\xE3\x8D\xAD" => "\x{0032}\x{0031}\x{70B9}", # U+336D => 0032 0031 70B9
"\xE3\x8D\xAE" => "\x{0032}\x{0032}\x{70B9}", # U+336E => 0032 0032 70B9
"\xE3\x8D\xAF" => "\x{0032}\x{0033}\x{70B9}", # U+336F => 0032 0033 70B9
"\xE3\x8D\xB0" => "\x{0032}\x{0034}\x{70B9}", # U+3370 => 0032 0034 70B9
"\xE3\x8D\xB1" => "\x{0068}\x{0070}\x{0061}", # U+3371 => 0068 0070 0061
"\xE3\x8D\xB2" => "\x{0064}\x{0061}",         # U+3372 => 0064 0061
"\xE3\x8D\xB3" => "\x{0061}\x{0075}",         # U+3373 => 0061 0075
"\xE3\x8D\xB4" => "\x{0062}\x{0061}\x{0072}", # U+3374 => 0062 0061 0072
"\xE3\x8D\xB5" => "\x{006F}\x{0076}",         # U+3375 => 006F 0076
"\xE3\x8D\xB6" => "\x{0070}\x{0063}",         # U+3376 => 0070 0063
"\xE3\x8D\xB7" => "\x{0064}\x{006D}",         # U+3377 => 0064 006D
"\xE3\x8D\xB8" => "\x{0064}\x{006D}\x{0032}", # U+3378 => 0064 006D 0032
"\xE3\x8D\xB9" => "\x{0064}\x{006D}\x{0033}", # U+3379 => 0064 006D 0033
"\xE3\x8D\xBA" => "\x{0069}\x{0075}",         # U+337A => 0069 0075
"\xE3\x8D\xBB" => "\x{5E73}\x{6210}",         # U+337B => 5E73 6210
"\xE3\x8D\xBC" => "\x{662D}\x{548C}",         # U+337C => 662D 548C
"\xE3\x8D\xBD" => "\x{5927}\x{6B63}",         # U+337D => 5927 6B63
"\xE3\x8D\xBE" => "\x{660E}\x{6CBB}",         # U+337E => 660E 6CBB
"\xE3\x8D\xBF" => "\x{682A}\x{5F0F}\x{4F1A}\x{793E}", # U+337F => 682A 5F0F 4F1A 793E
"\xE3\x8E\x80" => "\x{0070}\x{0061}",         # U+3380 => 0070 0061
"\xE3\x8E\x81" => "\x{006E}\x{0061}",         # U+3381 => 006E 0061
"\xE3\x8E\x82" => "\x{03BC}\x{0061}",         # U+3382 => 03BC 0061
"\xE3\x8E\x83" => "\x{006D}\x{0061}",         # U+3383 => 006D 0061
"\xE3\x8E\x84" => "\x{006B}\x{0061}",         # U+3384 => 006B 0061
"\xE3\x8E\x85" => "\x{006B}\x{0062}",         # U+3385 => 006B 0062
"\xE3\x8E\x86" => "\x{006D}\x{0062}",         # U+3386 => 006D 0062
"\xE3\x8E\x87" => "\x{0067}\x{0062}",         # U+3387 => 0067 0062
"\xE3\x8E\x88" => "\x{0063}\x{0061}\x{006C}", # U+3388 => 0063 0061 006C
"\xE3\x8E\x89" => "\x{006B}\x{0063}\x{0061}\x{006C}", # U+3389 => 006B 0063 0061 006C
"\xE3\x8E\x8A" => "\x{0070}\x{0066}",         # U+338A => 0070 0066
"\xE3\x8E\x8B" => "\x{006E}\x{0066}",         # U+338B => 006E 0066
"\xE3\x8E\x8C" => "\x{03BC}\x{0066}",         # U+338C => 03BC 0066
"\xE3\x8E\x8D" => "\x{03BC}\x{0067}",         # U+338D => 03BC 0067
"\xE3\x8E\x8E" => "\x{006D}\x{0067}",         # U+338E => 006D 0067
"\xE3\x8E\x8F" => "\x{006B}\x{0067}",         # U+338F => 006B 0067
"\xE3\x8E\x90" => "\x{0068}\x{007A}",         # U+3390 => 0068 007A
"\xE3\x8E\x91" => "\x{006B}\x{0068}\x{007A}", # U+3391 => 006B 0068 007A
"\xE3\x8E\x92" => "\x{006D}\x{0068}\x{007A}", # U+3392 => 006D 0068 007A
"\xE3\x8E\x93" => "\x{0067}\x{0068}\x{007A}", # U+3393 => 0067 0068 007A
"\xE3\x8E\x94" => "\x{0074}\x{0068}\x{007A}", # U+3394 => 0074 0068 007A
"\xE3\x8E\x95" => "\x{03BC}\x{006C}",         # U+3395 => 03BC 006C
"\xE3\x8E\x96" => "\x{006D}\x{006C}",         # U+3396 => 006D 006C
"\xE3\x8E\x97" => "\x{0064}\x{006C}",         # U+3397 => 0064 006C
"\xE3\x8E\x98" => "\x{006B}\x{006C}",         # U+3398 => 006B 006C
"\xE3\x8E\x99" => "\x{0066}\x{006D}",         # U+3399 => 0066 006D
"\xE3\x8E\x9A" => "\x{006E}\x{006D}",         # U+339A => 006E 006D
"\xE3\x8E\x9B" => "\x{03BC}\x{006D}",         # U+339B => 03BC 006D
"\xE3\x8E\x9C" => "\x{006D}\x{006D}",         # U+339C => 006D 006D
"\xE3\x8E\x9D" => "\x{0063}\x{006D}",         # U+339D => 0063 006D
"\xE3\x8E\x9E" => "\x{006B}\x{006D}",         # U+339E => 006B 006D
"\xE3\x8E\x9F" => "\x{006D}\x{006D}\x{0032}", # U+339F => 006D 006D 0032
"\xE3\x8E\xA0" => "\x{0063}\x{006D}\x{0032}", # U+33A0 => 0063 006D 0032
"\xE3\x8E\xA1" => "\x{006D}\x{0032}",         # U+33A1 => 006D 0032
"\xE3\x8E\xA2" => "\x{006B}\x{006D}\x{0032}", # U+33A2 => 006B 006D 0032
"\xE3\x8E\xA3" => "\x{006D}\x{006D}\x{0033}", # U+33A3 => 006D 006D 0033
"\xE3\x8E\xA4" => "\x{0063}\x{006D}\x{0033}", # U+33A4 => 0063 006D 0033
"\xE3\x8E\xA5" => "\x{006D}\x{0033}",         # U+33A5 => 006D 0033
"\xE3\x8E\xA6" => "\x{006B}\x{006D}\x{0033}", # U+33A6 => 006B 006D 0033
"\xE3\x8E\xA7" => "\x{006D}\x{2215}\x{0073}", # U+33A7 => 006D 2215 0073
"\xE3\x8E\xA8" => "\x{006D}\x{2215}\x{0073}\x{0032}", # U+33A8 => 006D 2215 0073 0032
"\xE3\x8E\xA9" => "\x{0070}\x{0061}",         # U+33A9 => 0070 0061
"\xE3\x8E\xAA" => "\x{006B}\x{0070}\x{0061}", # U+33AA => 006B 0070 0061
"\xE3\x8E\xAB" => "\x{006D}\x{0070}\x{0061}", # U+33AB => 006D 0070 0061
"\xE3\x8E\xAC" => "\x{0067}\x{0070}\x{0061}", # U+33AC => 0067 0070 0061
"\xE3\x8E\xAD" => "\x{0072}\x{0061}\x{0064}", # U+33AD => 0072 0061 0064
"\xE3\x8E\xAE" => "\x{0072}\x{0061}\x{0064}\x{2215}\x{0073}", # U+33AE => 0072 0061 0064 2215 0073
"\xE3\x8E\xAF" => "\x{0072}\x{0061}\x{0064}\x{2215}\x{0073}\x{0032}", # U+33AF => 0072 0061 0064 2215 0073 0032
"\xE3\x8E\xB0" => "\x{0070}\x{0073}",         # U+33B0 => 0070 0073
"\xE3\x8E\xB1" => "\x{006E}\x{0073}",         # U+33B1 => 006E 0073
"\xE3\x8E\xB2" => "\x{03BC}\x{0073}",         # U+33B2 => 03BC 0073
"\xE3\x8E\xB3" => "\x{006D}\x{0073}",         # U+33B3 => 006D 0073
"\xE3\x8E\xB4" => "\x{0070}\x{0076}",         # U+33B4 => 0070 0076
"\xE3\x8E\xB5" => "\x{006E}\x{0076}",         # U+33B5 => 006E 0076
"\xE3\x8E\xB6" => "\x{03BC}\x{0076}",         # U+33B6 => 03BC 0076
"\xE3\x8E\xB7" => "\x{006D}\x{0076}",         # U+33B7 => 006D 0076
"\xE3\x8E\xB8" => "\x{006B}\x{0076}",         # U+33B8 => 006B 0076
"\xE3\x8E\xB9" => "\x{006D}\x{0076}",         # U+33B9 => 006D 0076
"\xE3\x8E\xBA" => "\x{0070}\x{0077}",         # U+33BA => 0070 0077
"\xE3\x8E\xBB" => "\x{006E}\x{0077}",         # U+33BB => 006E 0077
"\xE3\x8E\xBC" => "\x{03BC}\x{0077}",         # U+33BC => 03BC 0077
"\xE3\x8E\xBD" => "\x{006D}\x{0077}",         # U+33BD => 006D 0077
"\xE3\x8E\xBE" => "\x{006B}\x{0077}",         # U+33BE => 006B 0077
"\xE3\x8E\xBF" => "\x{006D}\x{0077}",         # U+33BF => 006D 0077
"\xE3\x8F\x80" => "\x{006B}\x{03C9}",         # U+33C0 => 006B 03C9
"\xE3\x8F\x81" => "\x{006D}\x{03C9}",         # U+33C1 => 006D 03C9
"\xE3\x8F\x82" => "\x{0061}\x{002E}\x{006D}\x{002E}", # U+33C2 => 0061 002E 006D 002E
"\xE3\x8F\x83" => "\x{0062}\x{0071}",         # U+33C3 => 0062 0071
"\xE3\x8F\x84" => "\x{0063}\x{0063}",         # U+33C4 => 0063 0063
"\xE3\x8F\x85" => "\x{0063}\x{0064}",         # U+33C5 => 0063 0064
"\xE3\x8F\x86" => "\x{0063}\x{2215}\x{006B}\x{0067}", # U+33C6 => 0063 2215 006B 0067
"\xE3\x8F\x87" => "\x{0063}\x{006F}\x{002E}", # U+33C7 => 0063 006F 002E
"\xE3\x8F\x88" => "\x{0064}\x{0062}",         # U+33C8 => 0064 0062
"\xE3\x8F\x89" => "\x{0067}\x{0079}",         # U+33C9 => 0067 0079
"\xE3\x8F\x8A" => "\x{0068}\x{0061}",         # U+33CA => 0068 0061
"\xE3\x8F\x8B" => "\x{0068}\x{0070}",         # U+33CB => 0068 0070
"\xE3\x8F\x8C" => "\x{0069}\x{006E}",         # U+33CC => 0069 006E
"\xE3\x8F\x8D" => "\x{006B}\x{006B}",         # U+33CD => 006B 006B
"\xE3\x8F\x8E" => "\x{006B}\x{006D}",         # U+33CE => 006B 006D
"\xE3\x8F\x8F" => "\x{006B}\x{0074}",         # U+33CF => 006B 0074
"\xE3\x8F\x90" => "\x{006C}\x{006D}",         # U+33D0 => 006C 006D
"\xE3\x8F\x91" => "\x{006C}\x{006E}",         # U+33D1 => 006C 006E
"\xE3\x8F\x92" => "\x{006C}\x{006F}\x{0067}", # U+33D2 => 006C 006F 0067
"\xE3\x8F\x93" => "\x{006C}\x{0078}",         # U+33D3 => 006C 0078
"\xE3\x8F\x94" => "\x{006D}\x{0062}",         # U+33D4 => 006D 0062
"\xE3\x8F\x95" => "\x{006D}\x{0069}\x{006C}", # U+33D5 => 006D 0069 006C
"\xE3\x8F\x96" => "\x{006D}\x{006F}\x{006C}", # U+33D6 => 006D 006F 006C
"\xE3\x8F\x97" => "\x{0070}\x{0068}",         # U+33D7 => 0070 0068
"\xE3\x8F\x98" => "\x{0070}\x{002E}\x{006D}\x{002E}", # U+33D8 => 0070 002E 006D 002E
"\xE3\x8F\x99" => "\x{0070}\x{0070}\x{006D}", # U+33D9 => 0070 0070 006D
"\xE3\x8F\x9A" => "\x{0070}\x{0072}",         # U+33DA => 0070 0072
"\xE3\x8F\x9B" => "\x{0073}\x{0072}",         # U+33DB => 0073 0072
"\xE3\x8F\x9C" => "\x{0073}\x{0076}",         # U+33DC => 0073 0076
"\xE3\x8F\x9D" => "\x{0077}\x{0062}",         # U+33DD => 0077 0062
"\xE3\x8F\x9E" => "\x{0076}\x{2215}\x{006D}", # U+33DE => 0076 2215 006D
"\xE3\x8F\x9F" => "\x{0061}\x{2215}\x{006D}", # U+33DF => 0061 2215 006D
"\xE3\x8F\xA0" => "\x{0031}\x{65E5}",         # U+33E0 => 0031 65E5
"\xE3\x8F\xA1" => "\x{0032}\x{65E5}",         # U+33E1 => 0032 65E5
"\xE3\x8F\xA2" => "\x{0033}\x{65E5}",         # U+33E2 => 0033 65E5
"\xE3\x8F\xA3" => "\x{0034}\x{65E5}",         # U+33E3 => 0034 65E5
"\xE3\x8F\xA4" => "\x{0035}\x{65E5}",         # U+33E4 => 0035 65E5
"\xE3\x8F\xA5" => "\x{0036}\x{65E5}",         # U+33E5 => 0036 65E5
"\xE3\x8F\xA6" => "\x{0037}\x{65E5}",         # U+33E6 => 0037 65E5
"\xE3\x8F\xA7" => "\x{0038}\x{65E5}",         # U+33E7 => 0038 65E5
"\xE3\x8F\xA8" => "\x{0039}\x{65E5}",         # U+33E8 => 0039 65E5
"\xE3\x8F\xA9" => "\x{0031}\x{0030}\x{65E5}", # U+33E9 => 0031 0030 65E5
"\xE3\x8F\xAA" => "\x{0031}\x{0031}\x{65E5}", # U+33EA => 0031 0031 65E5
"\xE3\x8F\xAB" => "\x{0031}\x{0032}\x{65E5}", # U+33EB => 0031 0032 65E5
"\xE3\x8F\xAC" => "\x{0031}\x{0033}\x{65E5}", # U+33EC => 0031 0033 65E5
"\xE3\x8F\xAD" => "\x{0031}\x{0034}\x{65E5}", # U+33ED => 0031 0034 65E5
"\xE3\x8F\xAE" => "\x{0031}\x{0035}\x{65E5}", # U+33EE => 0031 0035 65E5
"\xE3\x8F\xAF" => "\x{0031}\x{0036}\x{65E5}", # U+33EF => 0031 0036 65E5
"\xE3\x8F\xB0" => "\x{0031}\x{0037}\x{65E5}", # U+33F0 => 0031 0037 65E5
"\xE3\x8F\xB1" => "\x{0031}\x{0038}\x{65E5}", # U+33F1 => 0031 0038 65E5
"\xE3\x8F\xB2" => "\x{0031}\x{0039}\x{65E5}", # U+33F2 => 0031 0039 65E5
"\xE3\x8F\xB3" => "\x{0032}\x{0030}\x{65E5}", # U+33F3 => 0032 0030 65E5
"\xE3\x8F\xB4" => "\x{0032}\x{0031}\x{65E5}", # U+33F4 => 0032 0031 65E5
"\xE3\x8F\xB5" => "\x{0032}\x{0032}\x{65E5}", # U+33F5 => 0032 0032 65E5
"\xE3\x8F\xB6" => "\x{0032}\x{0033}\x{65E5}", # U+33F6 => 0032 0033 65E5
"\xE3\x8F\xB7" => "\x{0032}\x{0034}\x{65E5}", # U+33F7 => 0032 0034 65E5
"\xE3\x8F\xB8" => "\x{0032}\x{0035}\x{65E5}", # U+33F8 => 0032 0035 65E5
"\xE3\x8F\xB9" => "\x{0032}\x{0036}\x{65E5}", # U+33F9 => 0032 0036 65E5
"\xE3\x8F\xBA" => "\x{0032}\x{0037}\x{65E5}", # U+33FA => 0032 0037 65E5
"\xE3\x8F\xBB" => "\x{0032}\x{0038}\x{65E5}", # U+33FB => 0032 0038 65E5
"\xE3\x8F\xBC" => "\x{0032}\x{0039}\x{65E5}", # U+33FC => 0032 0039 65E5
"\xE3\x8F\xBD" => "\x{0033}\x{0030}\x{65E5}", # U+33FD => 0033 0030 65E5
"\xE3\x8F\xBE" => "\x{0033}\x{0031}\x{65E5}", # U+33FE => 0033 0031 65E5
"\xE3\x8F\xBF" => "\x{0067}\x{0061}\x{006C}", # U+33FF => 0067 0061 006C
"\xEF\xAC\x80" => "\x{0066}\x{0066}",         # U+FB00 => 0066 0066
"\xEF\xAC\x81" => "\x{0066}\x{0069}",         # U+FB01 => 0066 0069
"\xEF\xAC\x82" => "\x{0066}\x{006C}",         # U+FB02 => 0066 006C
"\xEF\xAC\x83" => "\x{0066}\x{0066}\x{0069}", # U+FB03 => 0066 0066 0069
"\xEF\xAC\x84" => "\x{0066}\x{0066}\x{006C}", # U+FB04 => 0066 0066 006C
"\xEF\xAC\x85" => "\x{0073}\x{0074}",         # U+FB05 => 0073 0074
"\xEF\xAC\x86" => "\x{0073}\x{0074}",         # U+FB06 => 0073 0074
"\xEF\xAC\x93" => "\x{0574}\x{0576}",         # U+FB13 => 0574 0576
"\xEF\xAC\x94" => "\x{0574}\x{0565}",         # U+FB14 => 0574 0565
"\xEF\xAC\x95" => "\x{0574}\x{056B}",         # U+FB15 => 0574 056B
"\xEF\xAC\x96" => "\x{057E}\x{0576}",         # U+FB16 => 057E 0576
"\xEF\xAC\x97" => "\x{0574}\x{056D}",         # U+FB17 => 0574 056D
"\xEF\xAC\x9D" => "\x{05D9}\x{05B4}",         # U+FB1D => 05D9 05B4
"\xEF\xAC\x9F" => "\x{05F2}\x{05B7}",         # U+FB1F => 05F2 05B7
"\xEF\xAC\xAA" => "\x{05E9}\x{05C1}",         # U+FB2A => 05E9 05C1
"\xEF\xAC\xAB" => "\x{05E9}\x{05C2}",         # U+FB2B => 05E9 05C2
"\xEF\xAC\xAC" => "\x{05E9}\x{05BC}\x{05C1}", # U+FB2C => 05E9 05BC 05C1
"\xEF\xAC\xAD" => "\x{05E9}\x{05BC}\x{05C2}", # U+FB2D => 05E9 05BC 05C2
"\xEF\xAC\xAE" => "\x{05D0}\x{05B7}",         # U+FB2E => 05D0 05B7
"\xEF\xAC\xAF" => "\x{05D0}\x{05B8}",         # U+FB2F => 05D0 05B8
"\xEF\xAC\xB0" => "\x{05D0}\x{05BC}",         # U+FB30 => 05D0 05BC
"\xEF\xAC\xB1" => "\x{05D1}\x{05BC}",         # U+FB31 => 05D1 05BC
"\xEF\xAC\xB2" => "\x{05D2}\x{05BC}",         # U+FB32 => 05D2 05BC
"\xEF\xAC\xB3" => "\x{05D3}\x{05BC}",         # U+FB33 => 05D3 05BC
"\xEF\xAC\xB4" => "\x{05D4}\x{05BC}",         # U+FB34 => 05D4 05BC
"\xEF\xAC\xB5" => "\x{05D5}\x{05BC}",         # U+FB35 => 05D5 05BC
"\xEF\xAC\xB6" => "\x{05D6}\x{05BC}",         # U+FB36 => 05D6 05BC
"\xEF\xAC\xB8" => "\x{05D8}\x{05BC}",         # U+FB38 => 05D8 05BC
"\xEF\xAC\xB9" => "\x{05D9}\x{05BC}",         # U+FB39 => 05D9 05BC
"\xEF\xAC\xBA" => "\x{05DA}\x{05BC}",         # U+FB3A => 05DA 05BC
"\xEF\xAC\xBB" => "\x{05DB}\x{05BC}",         # U+FB3B => 05DB 05BC
"\xEF\xAC\xBC" => "\x{05DC}\x{05BC}",         # U+FB3C => 05DC 05BC
"\xEF\xAC\xBE" => "\x{05DE}\x{05BC}",         # U+FB3E => 05DE 05BC
"\xEF\xAD\x80" => "\x{05E0}\x{05BC}",         # U+FB40 => 05E0 05BC
"\xEF\xAD\x81" => "\x{05E1}\x{05BC}",         # U+FB41 => 05E1 05BC
"\xEF\xAD\x83" => "\x{05E3}\x{05BC}",         # U+FB43 => 05E3 05BC
"\xEF\xAD\x84" => "\x{05E4}\x{05BC}",         # U+FB44 => 05E4 05BC
"\xEF\xAD\x86" => "\x{05E6}\x{05BC}",         # U+FB46 => 05E6 05BC
"\xEF\xAD\x87" => "\x{05E7}\x{05BC}",         # U+FB47 => 05E7 05BC
"\xEF\xAD\x88" => "\x{05E8}\x{05BC}",         # U+FB48 => 05E8 05BC
"\xEF\xAD\x89" => "\x{05E9}\x{05BC}",         # U+FB49 => 05E9 05BC
"\xEF\xAD\x8A" => "\x{05EA}\x{05BC}",         # U+FB4A => 05EA 05BC
"\xEF\xAD\x8B" => "\x{05D5}\x{05B9}",         # U+FB4B => 05D5 05B9
"\xEF\xAD\x8C" => "\x{05D1}\x{05BF}",         # U+FB4C => 05D1 05BF
"\xEF\xAD\x8D" => "\x{05DB}\x{05BF}",         # U+FB4D => 05DB 05BF
"\xEF\xAD\x8E" => "\x{05E4}\x{05BF}",         # U+FB4E => 05E4 05BF
"\xEF\xAD\x8F" => "\x{05D0}\x{05DC}",         # U+FB4F => 05D0 05DC
"\xEF\xAF\x9D" => "\x{06C7}\x{0674}",         # U+FBDD => 06C7 0674
"\xEF\xAF\xAA" => "\x{0626}\x{0627}",         # U+FBEA => 0626 0627
"\xEF\xAF\xAB" => "\x{0626}\x{0627}",         # U+FBEB => 0626 0627
"\xEF\xAF\xAC" => "\x{0626}\x{06D5}",         # U+FBEC => 0626 06D5
"\xEF\xAF\xAD" => "\x{0626}\x{06D5}",         # U+FBED => 0626 06D5
"\xEF\xAF\xAE" => "\x{0626}\x{0648}",         # U+FBEE => 0626 0648
"\xEF\xAF\xAF" => "\x{0626}\x{0648}",         # U+FBEF => 0626 0648
"\xEF\xAF\xB0" => "\x{0626}\x{06C7}",         # U+FBF0 => 0626 06C7
"\xEF\xAF\xB1" => "\x{0626}\x{06C7}",         # U+FBF1 => 0626 06C7
"\xEF\xAF\xB2" => "\x{0626}\x{06C6}",         # U+FBF2 => 0626 06C6
"\xEF\xAF\xB3" => "\x{0626}\x{06C6}",         # U+FBF3 => 0626 06C6
"\xEF\xAF\xB4" => "\x{0626}\x{06C8}",         # U+FBF4 => 0626 06C8
"\xEF\xAF\xB5" => "\x{0626}\x{06C8}",         # U+FBF5 => 0626 06C8
"\xEF\xAF\xB6" => "\x{0626}\x{06D0}",         # U+FBF6 => 0626 06D0
"\xEF\xAF\xB7" => "\x{0626}\x{06D0}",         # U+FBF7 => 0626 06D0
"\xEF\xAF\xB8" => "\x{0626}\x{06D0}",         # U+FBF8 => 0626 06D0
"\xEF\xAF\xB9" => "\x{0626}\x{0649}",         # U+FBF9 => 0626 0649
"\xEF\xAF\xBA" => "\x{0626}\x{0649}",         # U+FBFA => 0626 0649
"\xEF\xAF\xBB" => "\x{0626}\x{0649}",         # U+FBFB => 0626 0649
"\xEF\xB0\x80" => "\x{0626}\x{062C}",         # U+FC00 => 0626 062C
"\xEF\xB0\x81" => "\x{0626}\x{062D}",         # U+FC01 => 0626 062D
"\xEF\xB0\x82" => "\x{0626}\x{0645}",         # U+FC02 => 0626 0645
"\xEF\xB0\x83" => "\x{0626}\x{0649}",         # U+FC03 => 0626 0649
"\xEF\xB0\x84" => "\x{0626}\x{064A}",         # U+FC04 => 0626 064A
"\xEF\xB0\x85" => "\x{0628}\x{062C}",         # U+FC05 => 0628 062C
"\xEF\xB0\x86" => "\x{0628}\x{062D}",         # U+FC06 => 0628 062D
"\xEF\xB0\x87" => "\x{0628}\x{062E}",         # U+FC07 => 0628 062E
"\xEF\xB0\x88" => "\x{0628}\x{0645}",         # U+FC08 => 0628 0645
"\xEF\xB0\x89" => "\x{0628}\x{0649}",         # U+FC09 => 0628 0649
"\xEF\xB0\x8A" => "\x{0628}\x{064A}",         # U+FC0A => 0628 064A
"\xEF\xB0\x8B" => "\x{062A}\x{062C}",         # U+FC0B => 062A 062C
"\xEF\xB0\x8C" => "\x{062A}\x{062D}",         # U+FC0C => 062A 062D
"\xEF\xB0\x8D" => "\x{062A}\x{062E}",         # U+FC0D => 062A 062E
"\xEF\xB0\x8E" => "\x{062A}\x{0645}",         # U+FC0E => 062A 0645
"\xEF\xB0\x8F" => "\x{062A}\x{0649}",         # U+FC0F => 062A 0649
"\xEF\xB0\x90" => "\x{062A}\x{064A}",         # U+FC10 => 062A 064A
"\xEF\xB0\x91" => "\x{062B}\x{062C}",         # U+FC11 => 062B 062C
"\xEF\xB0\x92" => "\x{062B}\x{0645}",         # U+FC12 => 062B 0645
"\xEF\xB0\x93" => "\x{062B}\x{0649}",         # U+FC13 => 062B 0649
"\xEF\xB0\x94" => "\x{062B}\x{064A}",         # U+FC14 => 062B 064A
"\xEF\xB0\x95" => "\x{062C}\x{062D}",         # U+FC15 => 062C 062D
"\xEF\xB0\x96" => "\x{062C}\x{0645}",         # U+FC16 => 062C 0645
"\xEF\xB0\x97" => "\x{062D}\x{062C}",         # U+FC17 => 062D 062C
"\xEF\xB0\x98" => "\x{062D}\x{0645}",         # U+FC18 => 062D 0645
"\xEF\xB0\x99" => "\x{062E}\x{062C}",         # U+FC19 => 062E 062C
"\xEF\xB0\x9A" => "\x{062E}\x{062D}",         # U+FC1A => 062E 062D
"\xEF\xB0\x9B" => "\x{062E}\x{0645}",         # U+FC1B => 062E 0645
"\xEF\xB0\x9C" => "\x{0633}\x{062C}",         # U+FC1C => 0633 062C
"\xEF\xB0\x9D" => "\x{0633}\x{062D}",         # U+FC1D => 0633 062D
"\xEF\xB0\x9E" => "\x{0633}\x{062E}",         # U+FC1E => 0633 062E
"\xEF\xB0\x9F" => "\x{0633}\x{0645}",         # U+FC1F => 0633 0645
"\xEF\xB0\xA0" => "\x{0635}\x{062D}",         # U+FC20 => 0635 062D
"\xEF\xB0\xA1" => "\x{0635}\x{0645}",         # U+FC21 => 0635 0645
"\xEF\xB0\xA2" => "\x{0636}\x{062C}",         # U+FC22 => 0636 062C
"\xEF\xB0\xA3" => "\x{0636}\x{062D}",         # U+FC23 => 0636 062D
"\xEF\xB0\xA4" => "\x{0636}\x{062E}",         # U+FC24 => 0636 062E
"\xEF\xB0\xA5" => "\x{0636}\x{0645}",         # U+FC25 => 0636 0645
"\xEF\xB0\xA6" => "\x{0637}\x{062D}",         # U+FC26 => 0637 062D
"\xEF\xB0\xA7" => "\x{0637}\x{0645}",         # U+FC27 => 0637 0645
"\xEF\xB0\xA8" => "\x{0638}\x{0645}",         # U+FC28 => 0638 0645
"\xEF\xB0\xA9" => "\x{0639}\x{062C}",         # U+FC29 => 0639 062C
"\xEF\xB0\xAA" => "\x{0639}\x{0645}",         # U+FC2A => 0639 0645
"\xEF\xB0\xAB" => "\x{063A}\x{062C}",         # U+FC2B => 063A 062C
"\xEF\xB0\xAC" => "\x{063A}\x{0645}",         # U+FC2C => 063A 0645
"\xEF\xB0\xAD" => "\x{0641}\x{062C}",         # U+FC2D => 0641 062C
"\xEF\xB0\xAE" => "\x{0641}\x{062D}",         # U+FC2E => 0641 062D
"\xEF\xB0\xAF" => "\x{0641}\x{062E}",         # U+FC2F => 0641 062E
"\xEF\xB0\xB0" => "\x{0641}\x{0645}",         # U+FC30 => 0641 0645
"\xEF\xB0\xB1" => "\x{0641}\x{0649}",         # U+FC31 => 0641 0649
"\xEF\xB0\xB2" => "\x{0641}\x{064A}",         # U+FC32 => 0641 064A
"\xEF\xB0\xB3" => "\x{0642}\x{062D}",         # U+FC33 => 0642 062D
"\xEF\xB0\xB4" => "\x{0642}\x{0645}",         # U+FC34 => 0642 0645
"\xEF\xB0\xB5" => "\x{0642}\x{0649}",         # U+FC35 => 0642 0649
"\xEF\xB0\xB6" => "\x{0642}\x{064A}",         # U+FC36 => 0642 064A
"\xEF\xB0\xB7" => "\x{0643}\x{0627}",         # U+FC37 => 0643 0627
"\xEF\xB0\xB8" => "\x{0643}\x{062C}",         # U+FC38 => 0643 062C
"\xEF\xB0\xB9" => "\x{0643}\x{062D}",         # U+FC39 => 0643 062D
"\xEF\xB0\xBA" => "\x{0643}\x{062E}",         # U+FC3A => 0643 062E
"\xEF\xB0\xBB" => "\x{0643}\x{0644}",         # U+FC3B => 0643 0644
"\xEF\xB0\xBC" => "\x{0643}\x{0645}",         # U+FC3C => 0643 0645
"\xEF\xB0\xBD" => "\x{0643}\x{0649}",         # U+FC3D => 0643 0649
"\xEF\xB0\xBE" => "\x{0643}\x{064A}",         # U+FC3E => 0643 064A
"\xEF\xB0\xBF" => "\x{0644}\x{062C}",         # U+FC3F => 0644 062C
"\xEF\xB1\x80" => "\x{0644}\x{062D}",         # U+FC40 => 0644 062D
"\xEF\xB1\x81" => "\x{0644}\x{062E}",         # U+FC41 => 0644 062E
"\xEF\xB1\x82" => "\x{0644}\x{0645}",         # U+FC42 => 0644 0645
"\xEF\xB1\x83" => "\x{0644}\x{0649}",         # U+FC43 => 0644 0649
"\xEF\xB1\x84" => "\x{0644}\x{064A}",         # U+FC44 => 0644 064A
"\xEF\xB1\x85" => "\x{0645}\x{062C}",         # U+FC45 => 0645 062C
"\xEF\xB1\x86" => "\x{0645}\x{062D}",         # U+FC46 => 0645 062D
"\xEF\xB1\x87" => "\x{0645}\x{062E}",         # U+FC47 => 0645 062E
"\xEF\xB1\x88" => "\x{0645}\x{0645}",         # U+FC48 => 0645 0645
"\xEF\xB1\x89" => "\x{0645}\x{0649}",         # U+FC49 => 0645 0649
"\xEF\xB1\x8A" => "\x{0645}\x{064A}",         # U+FC4A => 0645 064A
"\xEF\xB1\x8B" => "\x{0646}\x{062C}",         # U+FC4B => 0646 062C
"\xEF\xB1\x8C" => "\x{0646}\x{062D}",         # U+FC4C => 0646 062D
"\xEF\xB1\x8D" => "\x{0646}\x{062E}",         # U+FC4D => 0646 062E
"\xEF\xB1\x8E" => "\x{0646}\x{0645}",         # U+FC4E => 0646 0645
"\xEF\xB1\x8F" => "\x{0646}\x{0649}",         # U+FC4F => 0646 0649
"\xEF\xB1\x90" => "\x{0646}\x{064A}",         # U+FC50 => 0646 064A
"\xEF\xB1\x91" => "\x{0647}\x{062C}",         # U+FC51 => 0647 062C
"\xEF\xB1\x92" => "\x{0647}\x{0645}",         # U+FC52 => 0647 0645
"\xEF\xB1\x93" => "\x{0647}\x{0649}",         # U+FC53 => 0647 0649
"\xEF\xB1\x94" => "\x{0647}\x{064A}",         # U+FC54 => 0647 064A
"\xEF\xB1\x95" => "\x{064A}\x{062C}",         # U+FC55 => 064A 062C
"\xEF\xB1\x96" => "\x{064A}\x{062D}",         # U+FC56 => 064A 062D
"\xEF\xB1\x97" => "\x{064A}\x{062E}",         # U+FC57 => 064A 062E
"\xEF\xB1\x98" => "\x{064A}\x{0645}",         # U+FC58 => 064A 0645
"\xEF\xB1\x99" => "\x{064A}\x{0649}",         # U+FC59 => 064A 0649
"\xEF\xB1\x9A" => "\x{064A}\x{064A}",         # U+FC5A => 064A 064A
"\xEF\xB1\x9B" => "\x{0630}\x{0670}",         # U+FC5B => 0630 0670
"\xEF\xB1\x9C" => "\x{0631}\x{0670}",         # U+FC5C => 0631 0670
"\xEF\xB1\x9D" => "\x{0649}\x{0670}",         # U+FC5D => 0649 0670
"\xEF\xB1\x9E" => "\x{0020}\x{064C}\x{0651}", # U+FC5E => 0020 064C 0651
"\xEF\xB1\x9F" => "\x{0020}\x{064D}\x{0651}", # U+FC5F => 0020 064D 0651
"\xEF\xB1\xA0" => "\x{0020}\x{064E}\x{0651}", # U+FC60 => 0020 064E 0651
"\xEF\xB1\xA1" => "\x{0020}\x{064F}\x{0651}", # U+FC61 => 0020 064F 0651
"\xEF\xB1\xA2" => "\x{0020}\x{0650}\x{0651}", # U+FC62 => 0020 0650 0651
"\xEF\xB1\xA3" => "\x{0020}\x{0651}\x{0670}", # U+FC63 => 0020 0651 0670
"\xEF\xB1\xA4" => "\x{0626}\x{0631}",         # U+FC64 => 0626 0631
"\xEF\xB1\xA5" => "\x{0626}\x{0632}",         # U+FC65 => 0626 0632
"\xEF\xB1\xA6" => "\x{0626}\x{0645}",         # U+FC66 => 0626 0645
"\xEF\xB1\xA7" => "\x{0626}\x{0646}",         # U+FC67 => 0626 0646
"\xEF\xB1\xA8" => "\x{0626}\x{0649}",         # U+FC68 => 0626 0649
"\xEF\xB1\xA9" => "\x{0626}\x{064A}",         # U+FC69 => 0626 064A
"\xEF\xB1\xAA" => "\x{0628}\x{0631}",         # U+FC6A => 0628 0631
"\xEF\xB1\xAB" => "\x{0628}\x{0632}",         # U+FC6B => 0628 0632
"\xEF\xB1\xAC" => "\x{0628}\x{0645}",         # U+FC6C => 0628 0645
"\xEF\xB1\xAD" => "\x{0628}\x{0646}",         # U+FC6D => 0628 0646
"\xEF\xB1\xAE" => "\x{0628}\x{0649}",         # U+FC6E => 0628 0649
"\xEF\xB1\xAF" => "\x{0628}\x{064A}",         # U+FC6F => 0628 064A
"\xEF\xB1\xB0" => "\x{062A}\x{0631}",         # U+FC70 => 062A 0631
"\xEF\xB1\xB1" => "\x{062A}\x{0632}",         # U+FC71 => 062A 0632
"\xEF\xB1\xB2" => "\x{062A}\x{0645}",         # U+FC72 => 062A 0645
"\xEF\xB1\xB3" => "\x{062A}\x{0646}",         # U+FC73 => 062A 0646
"\xEF\xB1\xB4" => "\x{062A}\x{0649}",         # U+FC74 => 062A 0649
"\xEF\xB1\xB5" => "\x{062A}\x{064A}",         # U+FC75 => 062A 064A
"\xEF\xB1\xB6" => "\x{062B}\x{0631}",         # U+FC76 => 062B 0631
"\xEF\xB1\xB7" => "\x{062B}\x{0632}",         # U+FC77 => 062B 0632
"\xEF\xB1\xB8" => "\x{062B}\x{0645}",         # U+FC78 => 062B 0645
"\xEF\xB1\xB9" => "\x{062B}\x{0646}",         # U+FC79 => 062B 0646
"\xEF\xB1\xBA" => "\x{062B}\x{0649}",         # U+FC7A => 062B 0649
"\xEF\xB1\xBB" => "\x{062B}\x{064A}",         # U+FC7B => 062B 064A
"\xEF\xB1\xBC" => "\x{0641}\x{0649}",         # U+FC7C => 0641 0649
"\xEF\xB1\xBD" => "\x{0641}\x{064A}",         # U+FC7D => 0641 064A
"\xEF\xB1\xBE" => "\x{0642}\x{0649}",         # U+FC7E => 0642 0649
"\xEF\xB1\xBF" => "\x{0642}\x{064A}",         # U+FC7F => 0642 064A
"\xEF\xB2\x80" => "\x{0643}\x{0627}",         # U+FC80 => 0643 0627
"\xEF\xB2\x81" => "\x{0643}\x{0644}",         # U+FC81 => 0643 0644
"\xEF\xB2\x82" => "\x{0643}\x{0645}",         # U+FC82 => 0643 0645
"\xEF\xB2\x83" => "\x{0643}\x{0649}",         # U+FC83 => 0643 0649
"\xEF\xB2\x84" => "\x{0643}\x{064A}",         # U+FC84 => 0643 064A
"\xEF\xB2\x85" => "\x{0644}\x{0645}",         # U+FC85 => 0644 0645
"\xEF\xB2\x86" => "\x{0644}\x{0649}",         # U+FC86 => 0644 0649
"\xEF\xB2\x87" => "\x{0644}\x{064A}",         # U+FC87 => 0644 064A
"\xEF\xB2\x88" => "\x{0645}\x{0627}",         # U+FC88 => 0645 0627
"\xEF\xB2\x89" => "\x{0645}\x{0645}",         # U+FC89 => 0645 0645
"\xEF\xB2\x8A" => "\x{0646}\x{0631}",         # U+FC8A => 0646 0631
"\xEF\xB2\x8B" => "\x{0646}\x{0632}",         # U+FC8B => 0646 0632
"\xEF\xB2\x8C" => "\x{0646}\x{0645}",         # U+FC8C => 0646 0645
"\xEF\xB2\x8D" => "\x{0646}\x{0646}",         # U+FC8D => 0646 0646
"\xEF\xB2\x8E" => "\x{0646}\x{0649}",         # U+FC8E => 0646 0649
"\xEF\xB2\x8F" => "\x{0646}\x{064A}",         # U+FC8F => 0646 064A
"\xEF\xB2\x90" => "\x{0649}\x{0670}",         # U+FC90 => 0649 0670
"\xEF\xB2\x91" => "\x{064A}\x{0631}",         # U+FC91 => 064A 0631
"\xEF\xB2\x92" => "\x{064A}\x{0632}",         # U+FC92 => 064A 0632
"\xEF\xB2\x93" => "\x{064A}\x{0645}",         # U+FC93 => 064A 0645
"\xEF\xB2\x94" => "\x{064A}\x{0646}",         # U+FC94 => 064A 0646
"\xEF\xB2\x95" => "\x{064A}\x{0649}",         # U+FC95 => 064A 0649
"\xEF\xB2\x96" => "\x{064A}\x{064A}",         # U+FC96 => 064A 064A
"\xEF\xB2\x97" => "\x{0626}\x{062C}",         # U+FC97 => 0626 062C
"\xEF\xB2\x98" => "\x{0626}\x{062D}",         # U+FC98 => 0626 062D
"\xEF\xB2\x99" => "\x{0626}\x{062E}",         # U+FC99 => 0626 062E
"\xEF\xB2\x9A" => "\x{0626}\x{0645}",         # U+FC9A => 0626 0645
"\xEF\xB2\x9B" => "\x{0626}\x{0647}",         # U+FC9B => 0626 0647
"\xEF\xB2\x9C" => "\x{0628}\x{062C}",         # U+FC9C => 0628 062C
"\xEF\xB2\x9D" => "\x{0628}\x{062D}",         # U+FC9D => 0628 062D
"\xEF\xB2\x9E" => "\x{0628}\x{062E}",         # U+FC9E => 0628 062E
"\xEF\xB2\x9F" => "\x{0628}\x{0645}",         # U+FC9F => 0628 0645
"\xEF\xB2\xA0" => "\x{0628}\x{0647}",         # U+FCA0 => 0628 0647
"\xEF\xB2\xA1" => "\x{062A}\x{062C}",         # U+FCA1 => 062A 062C
"\xEF\xB2\xA2" => "\x{062A}\x{062D}",         # U+FCA2 => 062A 062D
"\xEF\xB2\xA3" => "\x{062A}\x{062E}",         # U+FCA3 => 062A 062E
"\xEF\xB2\xA4" => "\x{062A}\x{0645}",         # U+FCA4 => 062A 0645
"\xEF\xB2\xA5" => "\x{062A}\x{0647}",         # U+FCA5 => 062A 0647
"\xEF\xB2\xA6" => "\x{062B}\x{0645}",         # U+FCA6 => 062B 0645
"\xEF\xB2\xA7" => "\x{062C}\x{062D}",         # U+FCA7 => 062C 062D
"\xEF\xB2\xA8" => "\x{062C}\x{0645}",         # U+FCA8 => 062C 0645
"\xEF\xB2\xA9" => "\x{062D}\x{062C}",         # U+FCA9 => 062D 062C
"\xEF\xB2\xAA" => "\x{062D}\x{0645}",         # U+FCAA => 062D 0645
"\xEF\xB2\xAB" => "\x{062E}\x{062C}",         # U+FCAB => 062E 062C
"\xEF\xB2\xAC" => "\x{062E}\x{0645}",         # U+FCAC => 062E 0645
"\xEF\xB2\xAD" => "\x{0633}\x{062C}",         # U+FCAD => 0633 062C
"\xEF\xB2\xAE" => "\x{0633}\x{062D}",         # U+FCAE => 0633 062D
"\xEF\xB2\xAF" => "\x{0633}\x{062E}",         # U+FCAF => 0633 062E
"\xEF\xB2\xB0" => "\x{0633}\x{0645}",         # U+FCB0 => 0633 0645
"\xEF\xB2\xB1" => "\x{0635}\x{062D}",         # U+FCB1 => 0635 062D
"\xEF\xB2\xB2" => "\x{0635}\x{062E}",         # U+FCB2 => 0635 062E
"\xEF\xB2\xB3" => "\x{0635}\x{0645}",         # U+FCB3 => 0635 0645
"\xEF\xB2\xB4" => "\x{0636}\x{062C}",         # U+FCB4 => 0636 062C
"\xEF\xB2\xB5" => "\x{0636}\x{062D}",         # U+FCB5 => 0636 062D
"\xEF\xB2\xB6" => "\x{0636}\x{062E}",         # U+FCB6 => 0636 062E
"\xEF\xB2\xB7" => "\x{0636}\x{0645}",         # U+FCB7 => 0636 0645
"\xEF\xB2\xB8" => "\x{0637}\x{062D}",         # U+FCB8 => 0637 062D
"\xEF\xB2\xB9" => "\x{0638}\x{0645}",         # U+FCB9 => 0638 0645
"\xEF\xB2\xBA" => "\x{0639}\x{062C}",         # U+FCBA => 0639 062C
"\xEF\xB2\xBB" => "\x{0639}\x{0645}",         # U+FCBB => 0639 0645
"\xEF\xB2\xBC" => "\x{063A}\x{062C}",         # U+FCBC => 063A 062C
"\xEF\xB2\xBD" => "\x{063A}\x{0645}",         # U+FCBD => 063A 0645
"\xEF\xB2\xBE" => "\x{0641}\x{062C}",         # U+FCBE => 0641 062C
"\xEF\xB2\xBF" => "\x{0641}\x{062D}",         # U+FCBF => 0641 062D
"\xEF\xB3\x80" => "\x{0641}\x{062E}",         # U+FCC0 => 0641 062E
"\xEF\xB3\x81" => "\x{0641}\x{0645}",         # U+FCC1 => 0641 0645
"\xEF\xB3\x82" => "\x{0642}\x{062D}",         # U+FCC2 => 0642 062D
"\xEF\xB3\x83" => "\x{0642}\x{0645}",         # U+FCC3 => 0642 0645
"\xEF\xB3\x84" => "\x{0643}\x{062C}",         # U+FCC4 => 0643 062C
"\xEF\xB3\x85" => "\x{0643}\x{062D}",         # U+FCC5 => 0643 062D
"\xEF\xB3\x86" => "\x{0643}\x{062E}",         # U+FCC6 => 0643 062E
"\xEF\xB3\x87" => "\x{0643}\x{0644}",         # U+FCC7 => 0643 0644
"\xEF\xB3\x88" => "\x{0643}\x{0645}",         # U+FCC8 => 0643 0645
"\xEF\xB3\x89" => "\x{0644}\x{062C}",         # U+FCC9 => 0644 062C
"\xEF\xB3\x8A" => "\x{0644}\x{062D}",         # U+FCCA => 0644 062D
"\xEF\xB3\x8B" => "\x{0644}\x{062E}",         # U+FCCB => 0644 062E
"\xEF\xB3\x8C" => "\x{0644}\x{0645}",         # U+FCCC => 0644 0645
"\xEF\xB3\x8D" => "\x{0644}\x{0647}",         # U+FCCD => 0644 0647
"\xEF\xB3\x8E" => "\x{0645}\x{062C}",         # U+FCCE => 0645 062C
"\xEF\xB3\x8F" => "\x{0645}\x{062D}",         # U+FCCF => 0645 062D
"\xEF\xB3\x90" => "\x{0645}\x{062E}",         # U+FCD0 => 0645 062E
"\xEF\xB3\x91" => "\x{0645}\x{0645}",         # U+FCD1 => 0645 0645
"\xEF\xB3\x92" => "\x{0646}\x{062C}",         # U+FCD2 => 0646 062C
"\xEF\xB3\x93" => "\x{0646}\x{062D}",         # U+FCD3 => 0646 062D
"\xEF\xB3\x94" => "\x{0646}\x{062E}",         # U+FCD4 => 0646 062E
"\xEF\xB3\x95" => "\x{0646}\x{0645}",         # U+FCD5 => 0646 0645
"\xEF\xB3\x96" => "\x{0646}\x{0647}",         # U+FCD6 => 0646 0647
"\xEF\xB3\x97" => "\x{0647}\x{062C}",         # U+FCD7 => 0647 062C
"\xEF\xB3\x98" => "\x{0647}\x{0645}",         # U+FCD8 => 0647 0645
"\xEF\xB3\x99" => "\x{0647}\x{0670}",         # U+FCD9 => 0647 0670
"\xEF\xB3\x9A" => "\x{064A}\x{062C}",         # U+FCDA => 064A 062C
"\xEF\xB3\x9B" => "\x{064A}\x{062D}",         # U+FCDB => 064A 062D
"\xEF\xB3\x9C" => "\x{064A}\x{062E}",         # U+FCDC => 064A 062E
"\xEF\xB3\x9D" => "\x{064A}\x{0645}",         # U+FCDD => 064A 0645
"\xEF\xB3\x9E" => "\x{064A}\x{0647}",         # U+FCDE => 064A 0647
"\xEF\xB3\x9F" => "\x{0626}\x{0645}",         # U+FCDF => 0626 0645
"\xEF\xB3\xA0" => "\x{0626}\x{0647}",         # U+FCE0 => 0626 0647
"\xEF\xB3\xA1" => "\x{0628}\x{0645}",         # U+FCE1 => 0628 0645
"\xEF\xB3\xA2" => "\x{0628}\x{0647}",         # U+FCE2 => 0628 0647
"\xEF\xB3\xA3" => "\x{062A}\x{0645}",         # U+FCE3 => 062A 0645
"\xEF\xB3\xA4" => "\x{062A}\x{0647}",         # U+FCE4 => 062A 0647
"\xEF\xB3\xA5" => "\x{062B}\x{0645}",         # U+FCE5 => 062B 0645
"\xEF\xB3\xA6" => "\x{062B}\x{0647}",         # U+FCE6 => 062B 0647
"\xEF\xB3\xA7" => "\x{0633}\x{0645}",         # U+FCE7 => 0633 0645
"\xEF\xB3\xA8" => "\x{0633}\x{0647}",         # U+FCE8 => 0633 0647
"\xEF\xB3\xA9" => "\x{0634}\x{0645}",         # U+FCE9 => 0634 0645
"\xEF\xB3\xAA" => "\x{0634}\x{0647}",         # U+FCEA => 0634 0647
"\xEF\xB3\xAB" => "\x{0643}\x{0644}",         # U+FCEB => 0643 0644
"\xEF\xB3\xAC" => "\x{0643}\x{0645}",         # U+FCEC => 0643 0645
"\xEF\xB3\xAD" => "\x{0644}\x{0645}",         # U+FCED => 0644 0645
"\xEF\xB3\xAE" => "\x{0646}\x{0645}",         # U+FCEE => 0646 0645
"\xEF\xB3\xAF" => "\x{0646}\x{0647}",         # U+FCEF => 0646 0647
"\xEF\xB3\xB0" => "\x{064A}\x{0645}",         # U+FCF0 => 064A 0645
"\xEF\xB3\xB1" => "\x{064A}\x{0647}",         # U+FCF1 => 064A 0647
"\xEF\xB3\xB2" => "\x{0640}\x{064E}\x{0651}", # U+FCF2 => 0640 064E 0651
"\xEF\xB3\xB3" => "\x{0640}\x{064F}\x{0651}", # U+FCF3 => 0640 064F 0651
"\xEF\xB3\xB4" => "\x{0640}\x{0650}\x{0651}", # U+FCF4 => 0640 0650 0651
"\xEF\xB3\xB5" => "\x{0637}\x{0649}",         # U+FCF5 => 0637 0649
"\xEF\xB3\xB6" => "\x{0637}\x{064A}",         # U+FCF6 => 0637 064A
"\xEF\xB3\xB7" => "\x{0639}\x{0649}",         # U+FCF7 => 0639 0649
"\xEF\xB3\xB8" => "\x{0639}\x{064A}",         # U+FCF8 => 0639 064A
"\xEF\xB3\xB9" => "\x{063A}\x{0649}",         # U+FCF9 => 063A 0649
"\xEF\xB3\xBA" => "\x{063A}\x{064A}",         # U+FCFA => 063A 064A
"\xEF\xB3\xBB" => "\x{0633}\x{0649}",         # U+FCFB => 0633 0649
"\xEF\xB3\xBC" => "\x{0633}\x{064A}",         # U+FCFC => 0633 064A
"\xEF\xB3\xBD" => "\x{0634}\x{0649}",         # U+FCFD => 0634 0649
"\xEF\xB3\xBE" => "\x{0634}\x{064A}",         # U+FCFE => 0634 064A
"\xEF\xB3\xBF" => "\x{062D}\x{0649}",         # U+FCFF => 062D 0649
"\xEF\xB4\x80" => "\x{062D}\x{064A}",         # U+FD00 => 062D 064A
"\xEF\xB4\x81" => "\x{062C}\x{0649}",         # U+FD01 => 062C 0649
"\xEF\xB4\x82" => "\x{062C}\x{064A}",         # U+FD02 => 062C 064A
"\xEF\xB4\x83" => "\x{062E}\x{0649}",         # U+FD03 => 062E 0649
"\xEF\xB4\x84" => "\x{062E}\x{064A}",         # U+FD04 => 062E 064A
"\xEF\xB4\x85" => "\x{0635}\x{0649}",         # U+FD05 => 0635 0649
"\xEF\xB4\x86" => "\x{0635}\x{064A}",         # U+FD06 => 0635 064A
"\xEF\xB4\x87" => "\x{0636}\x{0649}",         # U+FD07 => 0636 0649
"\xEF\xB4\x88" => "\x{0636}\x{064A}",         # U+FD08 => 0636 064A
"\xEF\xB4\x89" => "\x{0634}\x{062C}",         # U+FD09 => 0634 062C
"\xEF\xB4\x8A" => "\x{0634}\x{062D}",         # U+FD0A => 0634 062D
"\xEF\xB4\x8B" => "\x{0634}\x{062E}",         # U+FD0B => 0634 062E
"\xEF\xB4\x8C" => "\x{0634}\x{0645}",         # U+FD0C => 0634 0645
"\xEF\xB4\x8D" => "\x{0634}\x{0631}",         # U+FD0D => 0634 0631
"\xEF\xB4\x8E" => "\x{0633}\x{0631}",         # U+FD0E => 0633 0631
"\xEF\xB4\x8F" => "\x{0635}\x{0631}",         # U+FD0F => 0635 0631
"\xEF\xB4\x90" => "\x{0636}\x{0631}",         # U+FD10 => 0636 0631
"\xEF\xB4\x91" => "\x{0637}\x{0649}",         # U+FD11 => 0637 0649
"\xEF\xB4\x92" => "\x{0637}\x{064A}",         # U+FD12 => 0637 064A
"\xEF\xB4\x93" => "\x{0639}\x{0649}",         # U+FD13 => 0639 0649
"\xEF\xB4\x94" => "\x{0639}\x{064A}",         # U+FD14 => 0639 064A
"\xEF\xB4\x95" => "\x{063A}\x{0649}",         # U+FD15 => 063A 0649
"\xEF\xB4\x96" => "\x{063A}\x{064A}",         # U+FD16 => 063A 064A
"\xEF\xB4\x97" => "\x{0633}\x{0649}",         # U+FD17 => 0633 0649
"\xEF\xB4\x98" => "\x{0633}\x{064A}",         # U+FD18 => 0633 064A
"\xEF\xB4\x99" => "\x{0634}\x{0649}",         # U+FD19 => 0634 0649
"\xEF\xB4\x9A" => "\x{0634}\x{064A}",         # U+FD1A => 0634 064A
"\xEF\xB4\x9B" => "\x{062D}\x{0649}",         # U+FD1B => 062D 0649
"\xEF\xB4\x9C" => "\x{062D}\x{064A}",         # U+FD1C => 062D 064A
"\xEF\xB4\x9D" => "\x{062C}\x{0649}",         # U+FD1D => 062C 0649
"\xEF\xB4\x9E" => "\x{062C}\x{064A}",         # U+FD1E => 062C 064A
"\xEF\xB4\x9F" => "\x{062E}\x{0649}",         # U+FD1F => 062E 0649
"\xEF\xB4\xA0" => "\x{062E}\x{064A}",         # U+FD20 => 062E 064A
"\xEF\xB4\xA1" => "\x{0635}\x{0649}",         # U+FD21 => 0635 0649
"\xEF\xB4\xA2" => "\x{0635}\x{064A}",         # U+FD22 => 0635 064A
"\xEF\xB4\xA3" => "\x{0636}\x{0649}",         # U+FD23 => 0636 0649
"\xEF\xB4\xA4" => "\x{0636}\x{064A}",         # U+FD24 => 0636 064A
"\xEF\xB4\xA5" => "\x{0634}\x{062C}",         # U+FD25 => 0634 062C
"\xEF\xB4\xA6" => "\x{0634}\x{062D}",         # U+FD26 => 0634 062D
"\xEF\xB4\xA7" => "\x{0634}\x{062E}",         # U+FD27 => 0634 062E
"\xEF\xB4\xA8" => "\x{0634}\x{0645}",         # U+FD28 => 0634 0645
"\xEF\xB4\xA9" => "\x{0634}\x{0631}",         # U+FD29 => 0634 0631
"\xEF\xB4\xAA" => "\x{0633}\x{0631}",         # U+FD2A => 0633 0631
"\xEF\xB4\xAB" => "\x{0635}\x{0631}",         # U+FD2B => 0635 0631
"\xEF\xB4\xAC" => "\x{0636}\x{0631}",         # U+FD2C => 0636 0631
"\xEF\xB4\xAD" => "\x{0634}\x{062C}",         # U+FD2D => 0634 062C
"\xEF\xB4\xAE" => "\x{0634}\x{062D}",         # U+FD2E => 0634 062D
"\xEF\xB4\xAF" => "\x{0634}\x{062E}",         # U+FD2F => 0634 062E
"\xEF\xB4\xB0" => "\x{0634}\x{0645}",         # U+FD30 => 0634 0645
"\xEF\xB4\xB1" => "\x{0633}\x{0647}",         # U+FD31 => 0633 0647
"\xEF\xB4\xB2" => "\x{0634}\x{0647}",         # U+FD32 => 0634 0647
"\xEF\xB4\xB3" => "\x{0637}\x{0645}",         # U+FD33 => 0637 0645
"\xEF\xB4\xB4" => "\x{0633}\x{062C}",         # U+FD34 => 0633 062C
"\xEF\xB4\xB5" => "\x{0633}\x{062D}",         # U+FD35 => 0633 062D
"\xEF\xB4\xB6" => "\x{0633}\x{062E}",         # U+FD36 => 0633 062E
"\xEF\xB4\xB7" => "\x{0634}\x{062C}",         # U+FD37 => 0634 062C
"\xEF\xB4\xB8" => "\x{0634}\x{062D}",         # U+FD38 => 0634 062D
"\xEF\xB4\xB9" => "\x{0634}\x{062E}",         # U+FD39 => 0634 062E
"\xEF\xB4\xBA" => "\x{0637}\x{0645}",         # U+FD3A => 0637 0645
"\xEF\xB4\xBB" => "\x{0638}\x{0645}",         # U+FD3B => 0638 0645
"\xEF\xB4\xBC" => "\x{0627}\x{064B}",         # U+FD3C => 0627 064B
"\xEF\xB4\xBD" => "\x{0627}\x{064B}",         # U+FD3D => 0627 064B
"\xEF\xB5\x90" => "\x{062A}\x{062C}\x{0645}", # U+FD50 => 062A 062C 0645
"\xEF\xB5\x91" => "\x{062A}\x{062D}\x{062C}", # U+FD51 => 062A 062D 062C
"\xEF\xB5\x92" => "\x{062A}\x{062D}\x{062C}", # U+FD52 => 062A 062D 062C
"\xEF\xB5\x93" => "\x{062A}\x{062D}\x{0645}", # U+FD53 => 062A 062D 0645
"\xEF\xB5\x94" => "\x{062A}\x{062E}\x{0645}", # U+FD54 => 062A 062E 0645
"\xEF\xB5\x95" => "\x{062A}\x{0645}\x{062C}", # U+FD55 => 062A 0645 062C
"\xEF\xB5\x96" => "\x{062A}\x{0645}\x{062D}", # U+FD56 => 062A 0645 062D
"\xEF\xB5\x97" => "\x{062A}\x{0645}\x{062E}", # U+FD57 => 062A 0645 062E
"\xEF\xB5\x98" => "\x{062C}\x{0645}\x{062D}", # U+FD58 => 062C 0645 062D
"\xEF\xB5\x99" => "\x{062C}\x{0645}\x{062D}", # U+FD59 => 062C 0645 062D
"\xEF\xB5\x9A" => "\x{062D}\x{0645}\x{064A}", # U+FD5A => 062D 0645 064A
"\xEF\xB5\x9B" => "\x{062D}\x{0645}\x{0649}", # U+FD5B => 062D 0645 0649
"\xEF\xB5\x9C" => "\x{0633}\x{062D}\x{062C}", # U+FD5C => 0633 062D 062C
"\xEF\xB5\x9D" => "\x{0633}\x{062C}\x{062D}", # U+FD5D => 0633 062C 062D
"\xEF\xB5\x9E" => "\x{0633}\x{062C}\x{0649}", # U+FD5E => 0633 062C 0649
"\xEF\xB5\x9F" => "\x{0633}\x{0645}\x{062D}", # U+FD5F => 0633 0645 062D
"\xEF\xB5\xA0" => "\x{0633}\x{0645}\x{062D}", # U+FD60 => 0633 0645 062D
"\xEF\xB5\xA1" => "\x{0633}\x{0645}\x{062C}", # U+FD61 => 0633 0645 062C
"\xEF\xB5\xA2" => "\x{0633}\x{0645}\x{0645}", # U+FD62 => 0633 0645 0645
"\xEF\xB5\xA3" => "\x{0633}\x{0645}\x{0645}", # U+FD63 => 0633 0645 0645
"\xEF\xB5\xA4" => "\x{0635}\x{062D}\x{062D}", # U+FD64 => 0635 062D 062D
"\xEF\xB5\xA5" => "\x{0635}\x{062D}\x{062D}", # U+FD65 => 0635 062D 062D
"\xEF\xB5\xA6" => "\x{0635}\x{0645}\x{0645}", # U+FD66 => 0635 0645 0645
"\xEF\xB5\xA7" => "\x{0634}\x{062D}\x{0645}", # U+FD67 => 0634 062D 0645
"\xEF\xB5\xA8" => "\x{0634}\x{062D}\x{0645}", # U+FD68 => 0634 062D 0645
"\xEF\xB5\xA9" => "\x{0634}\x{062C}\x{064A}", # U+FD69 => 0634 062C 064A
"\xEF\xB5\xAA" => "\x{0634}\x{0645}\x{062E}", # U+FD6A => 0634 0645 062E
"\xEF\xB5\xAB" => "\x{0634}\x{0645}\x{062E}", # U+FD6B => 0634 0645 062E
"\xEF\xB5\xAC" => "\x{0634}\x{0645}\x{0645}", # U+FD6C => 0634 0645 0645
"\xEF\xB5\xAD" => "\x{0634}\x{0645}\x{0645}", # U+FD6D => 0634 0645 0645
"\xEF\xB5\xAE" => "\x{0636}\x{062D}\x{0649}", # U+FD6E => 0636 062D 0649
"\xEF\xB5\xAF" => "\x{0636}\x{062E}\x{0645}", # U+FD6F => 0636 062E 0645
"\xEF\xB5\xB0" => "\x{0636}\x{062E}\x{0645}", # U+FD70 => 0636 062E 0645
"\xEF\xB5\xB1" => "\x{0637}\x{0645}\x{062D}", # U+FD71 => 0637 0645 062D
"\xEF\xB5\xB2" => "\x{0637}\x{0645}\x{062D}", # U+FD72 => 0637 0645 062D
"\xEF\xB5\xB3" => "\x{0637}\x{0645}\x{0645}", # U+FD73 => 0637 0645 0645
"\xEF\xB5\xB4" => "\x{0637}\x{0645}\x{064A}", # U+FD74 => 0637 0645 064A
"\xEF\xB5\xB5" => "\x{0639}\x{062C}\x{0645}", # U+FD75 => 0639 062C 0645
"\xEF\xB5\xB6" => "\x{0639}\x{0645}\x{0645}", # U+FD76 => 0639 0645 0645
"\xEF\xB5\xB7" => "\x{0639}\x{0645}\x{0645}", # U+FD77 => 0639 0645 0645
"\xEF\xB5\xB8" => "\x{0639}\x{0645}\x{0649}", # U+FD78 => 0639 0645 0649
"\xEF\xB5\xB9" => "\x{063A}\x{0645}\x{0645}", # U+FD79 => 063A 0645 0645
"\xEF\xB5\xBA" => "\x{063A}\x{0645}\x{064A}", # U+FD7A => 063A 0645 064A
"\xEF\xB5\xBB" => "\x{063A}\x{0645}\x{0649}", # U+FD7B => 063A 0645 0649
"\xEF\xB5\xBC" => "\x{0641}\x{062E}\x{0645}", # U+FD7C => 0641 062E 0645
"\xEF\xB5\xBD" => "\x{0641}\x{062E}\x{0645}", # U+FD7D => 0641 062E 0645
"\xEF\xB5\xBE" => "\x{0642}\x{0645}\x{062D}", # U+FD7E => 0642 0645 062D
"\xEF\xB5\xBF" => "\x{0642}\x{0645}\x{0645}", # U+FD7F => 0642 0645 0645
"\xEF\xB6\x80" => "\x{0644}\x{062D}\x{0645}", # U+FD80 => 0644 062D 0645
"\xEF\xB6\x81" => "\x{0644}\x{062D}\x{064A}", # U+FD81 => 0644 062D 064A
"\xEF\xB6\x82" => "\x{0644}\x{062D}\x{0649}", # U+FD82 => 0644 062D 0649
"\xEF\xB6\x83" => "\x{0644}\x{062C}\x{062C}", # U+FD83 => 0644 062C 062C
"\xEF\xB6\x84" => "\x{0644}\x{062C}\x{062C}", # U+FD84 => 0644 062C 062C
"\xEF\xB6\x85" => "\x{0644}\x{062E}\x{0645}", # U+FD85 => 0644 062E 0645
"\xEF\xB6\x86" => "\x{0644}\x{062E}\x{0645}", # U+FD86 => 0644 062E 0645
"\xEF\xB6\x87" => "\x{0644}\x{0645}\x{062D}", # U+FD87 => 0644 0645 062D
"\xEF\xB6\x88" => "\x{0644}\x{0645}\x{062D}", # U+FD88 => 0644 0645 062D
"\xEF\xB6\x89" => "\x{0645}\x{062D}\x{062C}", # U+FD89 => 0645 062D 062C
"\xEF\xB6\x8A" => "\x{0645}\x{062D}\x{0645}", # U+FD8A => 0645 062D 0645
"\xEF\xB6\x8B" => "\x{0645}\x{062D}\x{064A}", # U+FD8B => 0645 062D 064A
"\xEF\xB6\x8C" => "\x{0645}\x{062C}\x{062D}", # U+FD8C => 0645 062C 062D
"\xEF\xB6\x8D" => "\x{0645}\x{062C}\x{0645}", # U+FD8D => 0645 062C 0645
"\xEF\xB6\x8E" => "\x{0645}\x{062E}\x{062C}", # U+FD8E => 0645 062E 062C
"\xEF\xB6\x8F" => "\x{0645}\x{062E}\x{0645}", # U+FD8F => 0645 062E 0645
"\xEF\xB6\x92" => "\x{0645}\x{062C}\x{062E}", # U+FD92 => 0645 062C 062E
"\xEF\xB6\x93" => "\x{0647}\x{0645}\x{062C}", # U+FD93 => 0647 0645 062C
"\xEF\xB6\x94" => "\x{0647}\x{0645}\x{0645}", # U+FD94 => 0647 0645 0645
"\xEF\xB6\x95" => "\x{0646}\x{062D}\x{0645}", # U+FD95 => 0646 062D 0645
"\xEF\xB6\x96" => "\x{0646}\x{062D}\x{0649}", # U+FD96 => 0646 062D 0649
"\xEF\xB6\x97" => "\x{0646}\x{062C}\x{0645}", # U+FD97 => 0646 062C 0645
"\xEF\xB6\x98" => "\x{0646}\x{062C}\x{0645}", # U+FD98 => 0646 062C 0645
"\xEF\xB6\x99" => "\x{0646}\x{062C}\x{0649}", # U+FD99 => 0646 062C 0649
"\xEF\xB6\x9A" => "\x{0646}\x{0645}\x{064A}", # U+FD9A => 0646 0645 064A
"\xEF\xB6\x9B" => "\x{0646}\x{0645}\x{0649}", # U+FD9B => 0646 0645 0649
"\xEF\xB6\x9C" => "\x{064A}\x{0645}\x{0645}", # U+FD9C => 064A 0645 0645
"\xEF\xB6\x9D" => "\x{064A}\x{0645}\x{0645}", # U+FD9D => 064A 0645 0645
"\xEF\xB6\x9E" => "\x{0628}\x{062E}\x{064A}", # U+FD9E => 0628 062E 064A
"\xEF\xB6\x9F" => "\x{062A}\x{062C}\x{064A}", # U+FD9F => 062A 062C 064A
"\xEF\xB6\xA0" => "\x{062A}\x{062C}\x{0649}", # U+FDA0 => 062A 062C 0649
"\xEF\xB6\xA1" => "\x{062A}\x{062E}\x{064A}", # U+FDA1 => 062A 062E 064A
"\xEF\xB6\xA2" => "\x{062A}\x{062E}\x{0649}", # U+FDA2 => 062A 062E 0649
"\xEF\xB6\xA3" => "\x{062A}\x{0645}\x{064A}", # U+FDA3 => 062A 0645 064A
"\xEF\xB6\xA4" => "\x{062A}\x{0645}\x{0649}", # U+FDA4 => 062A 0645 0649
"\xEF\xB6\xA5" => "\x{062C}\x{0645}\x{064A}", # U+FDA5 => 062C 0645 064A
"\xEF\xB6\xA6" => "\x{062C}\x{062D}\x{0649}", # U+FDA6 => 062C 062D 0649
"\xEF\xB6\xA7" => "\x{062C}\x{0645}\x{0649}", # U+FDA7 => 062C 0645 0649
"\xEF\xB6\xA8" => "\x{0633}\x{062E}\x{0649}", # U+FDA8 => 0633 062E 0649
"\xEF\xB6\xA9" => "\x{0635}\x{062D}\x{064A}", # U+FDA9 => 0635 062D 064A
"\xEF\xB6\xAA" => "\x{0634}\x{062D}\x{064A}", # U+FDAA => 0634 062D 064A
"\xEF\xB6\xAB" => "\x{0636}\x{062D}\x{064A}", # U+FDAB => 0636 062D 064A
"\xEF\xB6\xAC" => "\x{0644}\x{062C}\x{064A}", # U+FDAC => 0644 062C 064A
"\xEF\xB6\xAD" => "\x{0644}\x{0645}\x{064A}", # U+FDAD => 0644 0645 064A
"\xEF\xB6\xAE" => "\x{064A}\x{062D}\x{064A}", # U+FDAE => 064A 062D 064A
"\xEF\xB6\xAF" => "\x{064A}\x{062C}\x{064A}", # U+FDAF => 064A 062C 064A
"\xEF\xB6\xB0" => "\x{064A}\x{0645}\x{064A}", # U+FDB0 => 064A 0645 064A
"\xEF\xB6\xB1" => "\x{0645}\x{0645}\x{064A}", # U+FDB1 => 0645 0645 064A
"\xEF\xB6\xB2" => "\x{0642}\x{0645}\x{064A}", # U+FDB2 => 0642 0645 064A
"\xEF\xB6\xB3" => "\x{0646}\x{062D}\x{064A}", # U+FDB3 => 0646 062D 064A
"\xEF\xB6\xB4" => "\x{0642}\x{0645}\x{062D}", # U+FDB4 => 0642 0645 062D
"\xEF\xB6\xB5" => "\x{0644}\x{062D}\x{0645}", # U+FDB5 => 0644 062D 0645
"\xEF\xB6\xB6" => "\x{0639}\x{0645}\x{064A}", # U+FDB6 => 0639 0645 064A
"\xEF\xB6\xB7" => "\x{0643}\x{0645}\x{064A}", # U+FDB7 => 0643 0645 064A
"\xEF\xB6\xB8" => "\x{0646}\x{062C}\x{062D}", # U+FDB8 => 0646 062C 062D
"\xEF\xB6\xB9" => "\x{0645}\x{062E}\x{064A}", # U+FDB9 => 0645 062E 064A
"\xEF\xB6\xBA" => "\x{0644}\x{062C}\x{0645}", # U+FDBA => 0644 062C 0645
"\xEF\xB6\xBB" => "\x{0643}\x{0645}\x{0645}", # U+FDBB => 0643 0645 0645
"\xEF\xB6\xBC" => "\x{0644}\x{062C}\x{0645}", # U+FDBC => 0644 062C 0645
"\xEF\xB6\xBD" => "\x{0646}\x{062C}\x{062D}", # U+FDBD => 0646 062C 062D
"\xEF\xB6\xBE" => "\x{062C}\x{062D}\x{064A}", # U+FDBE => 062C 062D 064A
"\xEF\xB6\xBF" => "\x{062D}\x{062C}\x{064A}", # U+FDBF => 062D 062C 064A
"\xEF\xB7\x80" => "\x{0645}\x{062C}\x{064A}", # U+FDC0 => 0645 062C 064A
"\xEF\xB7\x81" => "\x{0641}\x{0645}\x{064A}", # U+FDC1 => 0641 0645 064A
"\xEF\xB7\x82" => "\x{0628}\x{062D}\x{064A}", # U+FDC2 => 0628 062D 064A
"\xEF\xB7\x83" => "\x{0643}\x{0645}\x{0645}", # U+FDC3 => 0643 0645 0645
"\xEF\xB7\x84" => "\x{0639}\x{062C}\x{0645}", # U+FDC4 => 0639 062C 0645
"\xEF\xB7\x85" => "\x{0635}\x{0645}\x{0645}", # U+FDC5 => 0635 0645 0645
"\xEF\xB7\x86" => "\x{0633}\x{062E}\x{064A}", # U+FDC6 => 0633 062E 064A
"\xEF\xB7\x87" => "\x{0646}\x{062C}\x{064A}", # U+FDC7 => 0646 062C 064A
"\xEF\xB7\xB0" => "\x{0635}\x{0644}\x{06D2}", # U+FDF0 => 0635 0644 06D2
"\xEF\xB7\xB1" => "\x{0642}\x{0644}\x{06D2}", # U+FDF1 => 0642 0644 06D2
"\xEF\xB7\xB2" => "\x{0627}\x{0644}\x{0644}\x{0647}", # U+FDF2 => 0627 0644 0644 0647
"\xEF\xB7\xB3" => "\x{0627}\x{0643}\x{0628}\x{0631}", # U+FDF3 => 0627 0643 0628 0631
"\xEF\xB7\xB4" => "\x{0645}\x{062D}\x{0645}\x{062F}", # U+FDF4 => 0645 062D 0645 062F
"\xEF\xB7\xB5" => "\x{0635}\x{0644}\x{0639}\x{0645}", # U+FDF5 => 0635 0644 0639 0645
"\xEF\xB7\xB6" => "\x{0631}\x{0633}\x{0648}\x{0644}", # U+FDF6 => 0631 0633 0648 0644
"\xEF\xB7\xB7" => "\x{0639}\x{0644}\x{064A}\x{0647}", # U+FDF7 => 0639 0644 064A 0647
"\xEF\xB7\xB8" => "\x{0648}\x{0633}\x{0644}\x{0645}", # U+FDF8 => 0648 0633 0644 0645
"\xEF\xB7\xB9" => "\x{0635}\x{0644}\x{0649}", # U+FDF9 => 0635 0644 0649
"\xEF\xB7\xBA" => "\x{0635}\x{0644}\x{0649}\x{0020}\x{0627}\x{0644}\x{0644}\x{0647}\x{0020}\x{0639}\x{0644}\x{064A}\x{0647}\x{0020}\x{0648}\x{0633}\x{0644}\x{0645}", # U+FDFA => 0635 0644 0649 0020 0627 0644 0644 0647 0020 0639 0644 064A 0647 0020 0648 0633 0644 0645
"\xEF\xB7\xBB" => "\x{062C}\x{0644}\x{0020}\x{062C}\x{0644}\x{0627}\x{0644}\x{0647}", # U+FDFB => 062C 0644 0020 062C 0644 0627 0644 0647
"\xEF\xB7\xBC" => "\x{0631}\x{06CC}\x{0627}\x{0644}", # U+FDFC => 0631 06CC 0627 0644
"\xEF\xB8\x80" => "",                         # U+FE00 => 
"\xEF\xB8\x81" => "",                         # U+FE01 => 
"\xEF\xB8\x82" => "",                         # U+FE02 => 
"\xEF\xB8\x83" => "",                         # U+FE03 => 
"\xEF\xB8\x84" => "",                         # U+FE04 => 
"\xEF\xB8\x85" => "",                         # U+FE05 => 
"\xEF\xB8\x86" => "",                         # U+FE06 => 
"\xEF\xB8\x87" => "",                         # U+FE07 => 
"\xEF\xB8\x88" => "",                         # U+FE08 => 
"\xEF\xB8\x89" => "",                         # U+FE09 => 
"\xEF\xB8\x8A" => "",                         # U+FE0A => 
"\xEF\xB8\x8B" => "",                         # U+FE0B => 
"\xEF\xB8\x8C" => "",                         # U+FE0C => 
"\xEF\xB8\x8D" => "",                         # U+FE0D => 
"\xEF\xB8\x8E" => "",                         # U+FE0E => 
"\xEF\xB8\x8F" => "",                         # U+FE0F => 
"\xEF\xB8\x99" => "\x{002E}\x{002E}\x{002E}", # U+FE19 => 002E 002E 002E
"\xEF\xB8\xB0" => "\x{002E}\x{002E}",         # U+FE30 => 002E 002E
"\xEF\xB9\x89" => "\x{0020}\x{0305}",         # U+FE49 => 0020 0305
"\xEF\xB9\x8A" => "\x{0020}\x{0305}",         # U+FE4A => 0020 0305
"\xEF\xB9\x8B" => "\x{0020}\x{0305}",         # U+FE4B => 0020 0305
"\xEF\xB9\x8C" => "\x{0020}\x{0305}",         # U+FE4C => 0020 0305
"\xEF\xB9\xB0" => "\x{0020}\x{064B}",         # U+FE70 => 0020 064B
"\xEF\xB9\xB1" => "\x{0640}\x{064B}",         # U+FE71 => 0640 064B
"\xEF\xB9\xB2" => "\x{0020}\x{064C}",         # U+FE72 => 0020 064C
"\xEF\xB9\xB4" => "\x{0020}\x{064D}",         # U+FE74 => 0020 064D
"\xEF\xB9\xB6" => "\x{0020}\x{064E}",         # U+FE76 => 0020 064E
"\xEF\xB9\xB7" => "\x{0640}\x{064E}",         # U+FE77 => 0640 064E
"\xEF\xB9\xB8" => "\x{0020}\x{064F}",         # U+FE78 => 0020 064F
"\xEF\xB9\xB9" => "\x{0640}\x{064F}",         # U+FE79 => 0640 064F
"\xEF\xB9\xBA" => "\x{0020}\x{0650}",         # U+FE7A => 0020 0650
"\xEF\xB9\xBB" => "\x{0640}\x{0650}",         # U+FE7B => 0640 0650
"\xEF\xB9\xBC" => "\x{0020}\x{0651}",         # U+FE7C => 0020 0651
"\xEF\xB9\xBD" => "\x{0640}\x{0651}",         # U+FE7D => 0640 0651
"\xEF\xB9\xBE" => "\x{0020}\x{0652}",         # U+FE7E => 0020 0652
"\xEF\xB9\xBF" => "\x{0640}\x{0652}",         # U+FE7F => 0640 0652
"\xEF\xBB\xB5" => "\x{0644}\x{0622}",         # U+FEF5 => 0644 0622
"\xEF\xBB\xB6" => "\x{0644}\x{0622}",         # U+FEF6 => 0644 0622
"\xEF\xBB\xB7" => "\x{0644}\x{0623}",         # U+FEF7 => 0644 0623
"\xEF\xBB\xB8" => "\x{0644}\x{0623}",         # U+FEF8 => 0644 0623
"\xEF\xBB\xB9" => "\x{0644}\x{0625}",         # U+FEF9 => 0644 0625
"\xEF\xBB\xBA" => "\x{0644}\x{0625}",         # U+FEFA => 0644 0625
"\xEF\xBB\xBB" => "\x{0644}\x{0627}",         # U+FEFB => 0644 0627
"\xEF\xBB\xBC" => "\x{0644}\x{0627}",         # U+FEFC => 0644 0627
"\xEF\xBB\xBF" => "",                         # U+FEFF => 
"\xEF\xBE\xA0" => "",                         # U+FFA0 => 
"\xEF\xBF\xA3" => "\x{0020}\x{0304}",         # U+FFE3 => 0020 0304
"\xEF\xBF\xB0" => "",                         # U+FFF0 => 
"\xEF\xBF\xB1" => "",                         # U+FFF1 => 
"\xEF\xBF\xB2" => "",                         # U+FFF2 => 
"\xEF\xBF\xB3" => "",                         # U+FFF3 => 
"\xEF\xBF\xB4" => "",                         # U+FFF4 => 
"\xEF\xBF\xB5" => "",                         # U+FFF5 => 
"\xEF\xBF\xB6" => "",                         # U+FFF6 => 
"\xEF\xBF\xB7" => "",                         # U+FFF7 => 
"\xEF\xBF\xB8" => "",                         # U+FFF8 => 
"\xF0\x9B\xB2\xA0" => "",                     # U+1BCA0 => 
"\xF0\x9B\xB2\xA1" => "",                     # U+1BCA1 => 
"\xF0\x9B\xB2\xA2" => "",                     # U+1BCA2 => 
"\xF0\x9B\xB2\xA3" => "",                     # U+1BCA3 => 
"\xF0\x9D\x85\x9E" => "\x{1D157}\x{1D165}",   # U+1D15E => 1D157 1D165
"\xF0\x9D\x85\x9F" => "\x{1D158}\x{1D165}",   # U+1D15F => 1D158 1D165
"\xF0\x9D\x85\xA0" => "\x{1D158}\x{1D165}\x{1D16E}", # U+1D160 => 1D158 1D165 1D16E
"\xF0\x9D\x85\xA1" => "\x{1D158}\x{1D165}\x{1D16F}", # U+1D161 => 1D158 1D165 1D16F
"\xF0\x9D\x85\xA2" => "\x{1D158}\x{1D165}\x{1D170}", # U+1D162 => 1D158 1D165 1D170
"\xF0\x9D\x85\xA3" => "\x{1D158}\x{1D165}\x{1D171}", # U+1D163 => 1D158 1D165 1D171
"\xF0\x9D\x85\xA4" => "\x{1D158}\x{1D165}\x{1D172}", # U+1D164 => 1D158 1D165 1D172
"\xF0\x9D\x85\xB3" => "",                     # U+1D173 => 
"\xF0\x9D\x85\xB4" => "",                     # U+1D174 => 
"\xF0\x9D\x85\xB5" => "",                     # U+1D175 => 
"\xF0\x9D\x85\xB6" => "",                     # U+1D176 => 
"\xF0\x9D\x85\xB7" => "",                     # U+1D177 => 
"\xF0\x9D\x85\xB8" => "",                     # U+1D178 => 
"\xF0\x9D\x85\xB9" => "",                     # U+1D179 => 
"\xF0\x9D\x85\xBA" => "",                     # U+1D17A => 
"\xF0\x9D\x86\xBB" => "\x{1D1B9}\x{1D165}",   # U+1D1BB => 1D1B9 1D165
"\xF0\x9D\x86\xBC" => "\x{1D1BA}\x{1D165}",   # U+1D1BC => 1D1BA 1D165
"\xF0\x9D\x86\xBD" => "\x{1D1B9}\x{1D165}\x{1D16E}", # U+1D1BD => 1D1B9 1D165 1D16E
"\xF0\x9D\x86\xBE" => "\x{1D1BA}\x{1D165}\x{1D16E}", # U+1D1BE => 1D1BA 1D165 1D16E
"\xF0\x9D\x86\xBF" => "\x{1D1B9}\x{1D165}\x{1D16F}", # U+1D1BF => 1D1B9 1D165 1D16F
"\xF0\x9D\x87\x80" => "\x{1D1BA}\x{1D165}\x{1D16F}", # U+1D1C0 => 1D1BA 1D165 1D16F
"\xF0\x9F\x84\x80" => "\x{0030}\x{002E}",     # U+1F100 => 0030 002E
"\xF0\x9F\x84\x81" => "\x{0030}\x{002C}",     # U+1F101 => 0030 002C
"\xF0\x9F\x84\x82" => "\x{0031}\x{002C}",     # U+1F102 => 0031 002C
"\xF0\x9F\x84\x83" => "\x{0032}\x{002C}",     # U+1F103 => 0032 002C
"\xF0\x9F\x84\x84" => "\x{0033}\x{002C}",     # U+1F104 => 0033 002C
"\xF0\x9F\x84\x85" => "\x{0034}\x{002C}",     # U+1F105 => 0034 002C
"\xF0\x9F\x84\x86" => "\x{0035}\x{002C}",     # U+1F106 => 0035 002C
"\xF0\x9F\x84\x87" => "\x{0036}\x{002C}",     # U+1F107 => 0036 002C
"\xF0\x9F\x84\x88" => "\x{0037}\x{002C}",     # U+1F108 => 0037 002C
"\xF0\x9F\x84\x89" => "\x{0038}\x{002C}",     # U+1F109 => 0038 002C
"\xF0\x9F\x84\x8A" => "\x{0039}\x{002C}",     # U+1F10A => 0039 002C
"\xF0\x9F\x84\x90" => "\x{0028}\x{0061}\x{0029}", # U+1F110 => 0028 0061 0029
"\xF0\x9F\x84\x91" => "\x{0028}\x{0062}\x{0029}", # U+1F111 => 0028 0062 0029
"\xF0\x9F\x84\x92" => "\x{0028}\x{0063}\x{0029}", # U+1F112 => 0028 0063 0029
"\xF0\x9F\x84\x93" => "\x{0028}\x{0064}\x{0029}", # U+1F113 => 0028 0064 0029
"\xF0\x9F\x84\x94" => "\x{0028}\x{0065}\x{0029}", # U+1F114 => 0028 0065 0029
"\xF0\x9F\x84\x95" => "\x{0028}\x{0066}\x{0029}", # U+1F115 => 0028 0066 0029
"\xF0\x9F\x84\x96" => "\x{0028}\x{0067}\x{0029}", # U+1F116 => 0028 0067 0029
"\xF0\x9F\x84\x97" => "\x{0028}\x{0068}\x{0029}", # U+1F117 => 0028 0068 0029
"\xF0\x9F\x84\x98" => "\x{0028}\x{0069}\x{0029}", # U+1F118 => 0028 0069 0029
"\xF0\x9F\x84\x99" => "\x{0028}\x{006A}\x{0029}", # U+1F119 => 0028 006A 0029
"\xF0\x9F\x84\x9A" => "\x{0028}\x{006B}\x{0029}", # U+1F11A => 0028 006B 0029
"\xF0\x9F\x84\x9B" => "\x{0028}\x{006C}\x{0029}", # U+1F11B => 0028 006C 0029
"\xF0\x9F\x84\x9C" => "\x{0028}\x{006D}\x{0029}", # U+1F11C => 0028 006D 0029
"\xF0\x9F\x84\x9D" => "\x{0028}\x{006E}\x{0029}", # U+1F11D => 0028 006E 0029
"\xF0\x9F\x84\x9E" => "\x{0028}\x{006F}\x{0029}", # U+1F11E => 0028 006F 0029
"\xF0\x9F\x84\x9F" => "\x{0028}\x{0070}\x{0029}", # U+1F11F => 0028 0070 0029
"\xF0\x9F\x84\xA0" => "\x{0028}\x{0071}\x{0029}", # U+1F120 => 0028 0071 0029
"\xF0\x9F\x84\xA1" => "\x{0028}\x{0072}\x{0029}", # U+1F121 => 0028 0072 0029
"\xF0\x9F\x84\xA2" => "\x{0028}\x{0073}\x{0029}", # U+1F122 => 0028 0073 0029
"\xF0\x9F\x84\xA3" => "\x{0028}\x{0074}\x{0029}", # U+1F123 => 0028 0074 0029
"\xF0\x9F\x84\xA4" => "\x{0028}\x{0075}\x{0029}", # U+1F124 => 0028 0075 0029
"\xF0\x9F\x84\xA5" => "\x{0028}\x{0076}\x{0029}", # U+1F125 => 0028 0076 0029
"\xF0\x9F\x84\xA6" => "\x{0028}\x{0077}\x{0029}", # U+1F126 => 0028 0077 0029
"\xF0\x9F\x84\xA7" => "\x{0028}\x{0078}\x{0029}", # U+1F127 => 0028 0078 0029
"\xF0\x9F\x84\xA8" => "\x{0028}\x{0079}\x{0029}", # U+1F128 => 0028 0079 0029
"\xF0\x9F\x84\xA9" => "\x{0028}\x{007A}\x{0029}", # U+1F129 => 0028 007A 0029
"\xF0\x9F\x84\xAA" => "\x{3014}\x{0073}\x{3015}", # U+1F12A => 3014 0073 3015
"\xF0\x9F\x84\xAD" => "\x{0063}\x{0064}",     # U+1F12D => 0063 0064
"\xF0\x9F\x84\xAE" => "\x{0077}\x{007A}",     # U+1F12E => 0077 007A
"\xF0\x9F\x85\x8A" => "\x{0068}\x{0076}",     # U+1F14A => 0068 0076
"\xF0\x9F\x85\x8B" => "\x{006D}\x{0076}",     # U+1F14B => 006D 0076
"\xF0\x9F\x85\x8C" => "\x{0073}\x{0064}",     # U+1F14C => 0073 0064
"\xF0\x9F\x85\x8D" => "\x{0073}\x{0073}",     # U+1F14D => 0073 0073
"\xF0\x9F\x85\x8E" => "\x{0070}\x{0070}\x{0076}", # U+1F14E => 0070 0070 0076
"\xF0\x9F\x85\x8F" => "\x{0077}\x{0063}",     # U+1F14F => 0077 0063
"\xF0\x9F\x85\xAA" => "\x{006D}\x{0063}",     # U+1F16A => 006D 0063
"\xF0\x9F\x85\xAB" => "\x{006D}\x{0064}",     # U+1F16B => 006D 0064
"\xF0\x9F\x85\xAC" => "\x{006D}\x{0072}",     # U+1F16C => 006D 0072
"\xF0\x9F\x86\x90" => "\x{0064}\x{006A}",     # U+1F190 => 0064 006A
"\xF0\x9F\x88\x80" => "\x{307B}\x{304B}",     # U+1F200 => 307B 304B
"\xF0\x9F\x88\x81" => "\x{30B3}\x{30B3}",     # U+1F201 => 30B3 30B3
"\xF0\x9F\x89\x80" => "\x{3014}\x{672C}\x{3015}", # U+1F240 => 3014 672C 3015
"\xF0\x9F\x89\x81" => "\x{3014}\x{4E09}\x{3015}", # U+1F241 => 3014 4E09 3015
"\xF0\x9F\x89\x82" => "\x{3014}\x{4E8C}\x{3015}", # U+1F242 => 3014 4E8C 3015
"\xF0\x9F\x89\x83" => "\x{3014}\x{5B89}\x{3015}", # U+1F243 => 3014 5B89 3015
"\xF0\x9F\x89\x84" => "\x{3014}\x{70B9}\x{3015}", # U+1F244 => 3014 70B9 3015
"\xF0\x9F\x89\x85" => "\x{3014}\x{6253}\x{3015}", # U+1F245 => 3014 6253 3015
"\xF0\x9F\x89\x86" => "\x{3014}\x{76D7}\x{3015}", # U+1F246 => 3014 76D7 3015
"\xF0\x9F\x89\x87" => "\x{3014}\x{52DD}\x{3015}", # U+1F247 => 3014 52DD 3015
"\xF0\x9F\x89\x88" => "\x{3014}\x{6557}\x{3015}", # U+1F248 => 3014 6557 3015
"\xF3\xA0\x80\x80" => "",                     # U+E0000 => 
"\xF3\xA0\x80\x81" => "",                     # U+E0001 => 
"\xF3\xA0\x80\x82" => "",                     # U+E0002 => 
"\xF3\xA0\x80\x83" => "",                     # U+E0003 => 
"\xF3\xA0\x80\x84" => "",                     # U+E0004 => 
"\xF3\xA0\x80\x85" => "",                     # U+E0005 => 
"\xF3\xA0\x80\x86" => "",                     # U+E0006 => 
"\xF3\xA0\x80\x87" => "",                     # U+E0007 => 
"\xF3\xA0\x80\x88" => "",                     # U+E0008 => 
"\xF3\xA0\x80\x89" => "",                     # U+E0009 => 
"\xF3\xA0\x80\x8A" => "",                     # U+E000A => 
"\xF3\xA0\x80\x8B" => "",                     # U+E000B => 
"\xF3\xA0\x80\x8C" => "",                     # U+E000C => 
"\xF3\xA0\x80\x8D" => "",                     # U+E000D => 
"\xF3\xA0\x80\x8E" => "",                     # U+E000E => 
"\xF3\xA0\x80\x8F" => "",                     # U+E000F => 
"\xF3\xA0\x80\x90" => "",                     # U+E0010 => 
"\xF3\xA0\x80\x91" => "",                     # U+E0011 => 
"\xF3\xA0\x80\x92" => "",                     # U+E0012 => 
"\xF3\xA0\x80\x93" => "",                     # U+E0013 => 
"\xF3\xA0\x80\x94" => "",                     # U+E0014 => 
"\xF3\xA0\x80\x95" => "",                     # U+E0015 => 
"\xF3\xA0\x80\x96" => "",                     # U+E0016 => 
"\xF3\xA0\x80\x97" => "",                     # U+E0017 => 
"\xF3\xA0\x80\x98" => "",                     # U+E0018 => 
"\xF3\xA0\x80\x99" => "",                     # U+E0019 => 
"\xF3\xA0\x80\x9A" => "",                     # U+E001A => 
"\xF3\xA0\x80\x9B" => "",                     # U+E001B => 
"\xF3\xA0\x80\x9C" => "",                     # U+E001C => 
"\xF3\xA0\x80\x9D" => "",                     # U+E001D => 
"\xF3\xA0\x80\x9E" => "",                     # U+E001E => 
"\xF3\xA0\x80\x9F" => "",                     # U+E001F => 
"\xF3\xA0\x80\xA0" => "",                     # U+E0020 => 
"\xF3\xA0\x80\xA1" => "",                     # U+E0021 => 
"\xF3\xA0\x80\xA2" => "",                     # U+E0022 => 
"\xF3\xA0\x80\xA3" => "",                     # U+E0023 => 
"\xF3\xA0\x80\xA4" => "",                     # U+E0024 => 
"\xF3\xA0\x80\xA5" => "",                     # U+E0025 => 
"\xF3\xA0\x80\xA6" => "",                     # U+E0026 => 
"\xF3\xA0\x80\xA7" => "",                     # U+E0027 => 
"\xF3\xA0\x80\xA8" => "",                     # U+E0028 => 
"\xF3\xA0\x80\xA9" => "",                     # U+E0029 => 
"\xF3\xA0\x80\xAA" => "",                     # U+E002A => 
"\xF3\xA0\x80\xAB" => "",                     # U+E002B => 
"\xF3\xA0\x80\xAC" => "",                     # U+E002C => 
"\xF3\xA0\x80\xAD" => "",                     # U+E002D => 
"\xF3\xA0\x80\xAE" => "",                     # U+E002E => 
"\xF3\xA0\x80\xAF" => "",                     # U+E002F => 
"\xF3\xA0\x80\xB0" => "",                     # U+E0030 => 
"\xF3\xA0\x80\xB1" => "",                     # U+E0031 => 
"\xF3\xA0\x80\xB2" => "",                     # U+E0032 => 
"\xF3\xA0\x80\xB3" => "",                     # U+E0033 => 
"\xF3\xA0\x80\xB4" => "",                     # U+E0034 => 
"\xF3\xA0\x80\xB5" => "",                     # U+E0035 => 
"\xF3\xA0\x80\xB6" => "",                     # U+E0036 => 
"\xF3\xA0\x80\xB7" => "",                     # U+E0037 => 
"\xF3\xA0\x80\xB8" => "",                     # U+E0038 => 
"\xF3\xA0\x80\xB9" => "",                     # U+E0039 => 
"\xF3\xA0\x80\xBA" => "",                     # U+E003A => 
"\xF3\xA0\x80\xBB" => "",                     # U+E003B => 
"\xF3\xA0\x80\xBC" => "",                     # U+E003C => 
"\xF3\xA0\x80\xBD" => "",                     # U+E003D => 
"\xF3\xA0\x80\xBE" => "",                     # U+E003E => 
"\xF3\xA0\x80\xBF" => "",                     # U+E003F => 
"\xF3\xA0\x81\x80" => "",                     # U+E0040 => 
"\xF3\xA0\x81\x81" => "",                     # U+E0041 => 
"\xF3\xA0\x81\x82" => "",                     # U+E0042 => 
"\xF3\xA0\x81\x83" => "",                     # U+E0043 => 
"\xF3\xA0\x81\x84" => "",                     # U+E0044 => 
"\xF3\xA0\x81\x85" => "",                     # U+E0045 => 
"\xF3\xA0\x81\x86" => "",                     # U+E0046 => 
"\xF3\xA0\x81\x87" => "",                     # U+E0047 => 
"\xF3\xA0\x81\x88" => "",                     # U+E0048 => 
"\xF3\xA0\x81\x89" => "",                     # U+E0049 => 
"\xF3\xA0\x81\x8A" => "",                     # U+E004A => 
"\xF3\xA0\x81\x8B" => "",                     # U+E004B => 
"\xF3\xA0\x81\x8C" => "",                     # U+E004C => 
"\xF3\xA0\x81\x8D" => "",                     # U+E004D => 
"\xF3\xA0\x81\x8E" => "",                     # U+E004E => 
"\xF3\xA0\x81\x8F" => "",                     # U+E004F => 
"\xF3\xA0\x81\x90" => "",                     # U+E0050 => 
"\xF3\xA0\x81\x91" => "",                     # U+E0051 => 
"\xF3\xA0\x81\x92" => "",                     # U+E0052 => 
"\xF3\xA0\x81\x93" => "",                     # U+E0053 => 
"\xF3\xA0\x81\x94" => "",                     # U+E0054 => 
"\xF3\xA0\x81\x95" => "",                     # U+E0055 => 
"\xF3\xA0\x81\x96" => "",                     # U+E0056 => 
"\xF3\xA0\x81\x97" => "",                     # U+E0057 => 
"\xF3\xA0\x81\x98" => "",                     # U+E0058 => 
"\xF3\xA0\x81\x99" => "",                     # U+E0059 => 
"\xF3\xA0\x81\x9A" => "",                     # U+E005A => 
"\xF3\xA0\x81\x9B" => "",                     # U+E005B => 
"\xF3\xA0\x81\x9C" => "",                     # U+E005C => 
"\xF3\xA0\x81\x9D" => "",                     # U+E005D => 
"\xF3\xA0\x81\x9E" => "",                     # U+E005E => 
"\xF3\xA0\x81\x9F" => "",                     # U+E005F => 
"\xF3\xA0\x81\xA0" => "",                     # U+E0060 => 
"\xF3\xA0\x81\xA1" => "",                     # U+E0061 => 
"\xF3\xA0\x81\xA2" => "",                     # U+E0062 => 
"\xF3\xA0\x81\xA3" => "",                     # U+E0063 => 
"\xF3\xA0\x81\xA4" => "",                     # U+E0064 => 
"\xF3\xA0\x81\xA5" => "",                     # U+E0065 => 
"\xF3\xA0\x81\xA6" => "",                     # U+E0066 => 
"\xF3\xA0\x81\xA7" => "",                     # U+E0067 => 
"\xF3\xA0\x81\xA8" => "",                     # U+E0068 => 
"\xF3\xA0\x81\xA9" => "",                     # U+E0069 => 
"\xF3\xA0\x81\xAA" => "",                     # U+E006A => 
"\xF3\xA0\x81\xAB" => "",                     # U+E006B => 
"\xF3\xA0\x81\xAC" => "",                     # U+E006C => 
"\xF3\xA0\x81\xAD" => "",                     # U+E006D => 
"\xF3\xA0\x81\xAE" => "",                     # U+E006E => 
"\xF3\xA0\x81\xAF" => "",                     # U+E006F => 
"\xF3\xA0\x81\xB0" => "",                     # U+E0070 => 
"\xF3\xA0\x81\xB1" => "",                     # U+E0071 => 
"\xF3\xA0\x81\xB2" => "",                     # U+E0072 => 
"\xF3\xA0\x81\xB3" => "",                     # U+E0073 => 
"\xF3\xA0\x81\xB4" => "",                     # U+E0074 => 
"\xF3\xA0\x81\xB5" => "",                     # U+E0075 => 
"\xF3\xA0\x81\xB6" => "",                     # U+E0076 => 
"\xF3\xA0\x81\xB7" => "",                     # U+E0077 => 
"\xF3\xA0\x81\xB8" => "",                     # U+E0078 => 
"\xF3\xA0\x81\xB9" => "",                     # U+E0079 => 
"\xF3\xA0\x81\xBA" => "",                     # U+E007A => 
"\xF3\xA0\x81\xBB" => "",                     # U+E007B => 
"\xF3\xA0\x81\xBC" => "",                     # U+E007C => 
"\xF3\xA0\x81\xBD" => "",                     # U+E007D => 
"\xF3\xA0\x81\xBE" => "",                     # U+E007E => 
"\xF3\xA0\x81\xBF" => "",                     # U+E007F => 
"\xF3\xA0\x82\x80" => "",                     # U+E0080 => 
"\xF3\xA0\x82\x81" => "",                     # U+E0081 => 
"\xF3\xA0\x82\x82" => "",                     # U+E0082 => 
"\xF3\xA0\x82\x83" => "",                     # U+E0083 => 
"\xF3\xA0\x82\x84" => "",                     # U+E0084 => 
"\xF3\xA0\x82\x85" => "",                     # U+E0085 => 
"\xF3\xA0\x82\x86" => "",                     # U+E0086 => 
"\xF3\xA0\x82\x87" => "",                     # U+E0087 => 
"\xF3\xA0\x82\x88" => "",                     # U+E0088 => 
"\xF3\xA0\x82\x89" => "",                     # U+E0089 => 
"\xF3\xA0\x82\x8A" => "",                     # U+E008A => 
"\xF3\xA0\x82\x8B" => "",                     # U+E008B => 
"\xF3\xA0\x82\x8C" => "",                     # U+E008C => 
"\xF3\xA0\x82\x8D" => "",                     # U+E008D => 
"\xF3\xA0\x82\x8E" => "",                     # U+E008E => 
"\xF3\xA0\x82\x8F" => "",                     # U+E008F => 
"\xF3\xA0\x82\x90" => "",                     # U+E0090 => 
"\xF3\xA0\x82\x91" => "",                     # U+E0091 => 
"\xF3\xA0\x82\x92" => "",                     # U+E0092 => 
"\xF3\xA0\x82\x93" => "",                     # U+E0093 => 
"\xF3\xA0\x82\x94" => "",                     # U+E0094 => 
"\xF3\xA0\x82\x95" => "",                     # U+E0095 => 
"\xF3\xA0\x82\x96" => "",                     # U+E0096 => 
"\xF3\xA0\x82\x97" => "",                     # U+E0097 => 
"\xF3\xA0\x82\x98" => "",                     # U+E0098 => 
"\xF3\xA0\x82\x99" => "",                     # U+E0099 => 
"\xF3\xA0\x82\x9A" => "",                     # U+E009A => 
"\xF3\xA0\x82\x9B" => "",                     # U+E009B => 
"\xF3\xA0\x82\x9C" => "",                     # U+E009C => 
"\xF3\xA0\x82\x9D" => "",                     # U+E009D => 
"\xF3\xA0\x82\x9E" => "",                     # U+E009E => 
"\xF3\xA0\x82\x9F" => "",                     # U+E009F => 
"\xF3\xA0\x82\xA0" => "",                     # U+E00A0 => 
"\xF3\xA0\x82\xA1" => "",                     # U+E00A1 => 
"\xF3\xA0\x82\xA2" => "",                     # U+E00A2 => 
"\xF3\xA0\x82\xA3" => "",                     # U+E00A3 => 
"\xF3\xA0\x82\xA4" => "",                     # U+E00A4 => 
"\xF3\xA0\x82\xA5" => "",                     # U+E00A5 => 
"\xF3\xA0\x82\xA6" => "",                     # U+E00A6 => 
"\xF3\xA0\x82\xA7" => "",                     # U+E00A7 => 
"\xF3\xA0\x82\xA8" => "",                     # U+E00A8 => 
"\xF3\xA0\x82\xA9" => "",                     # U+E00A9 => 
"\xF3\xA0\x82\xAA" => "",                     # U+E00AA => 
"\xF3\xA0\x82\xAB" => "",                     # U+E00AB => 
"\xF3\xA0\x82\xAC" => "",                     # U+E00AC => 
"\xF3\xA0\x82\xAD" => "",                     # U+E00AD => 
"\xF3\xA0\x82\xAE" => "",                     # U+E00AE => 
"\xF3\xA0\x82\xAF" => "",                     # U+E00AF => 
"\xF3\xA0\x82\xB0" => "",                     # U+E00B0 => 
"\xF3\xA0\x82\xB1" => "",                     # U+E00B1 => 
"\xF3\xA0\x82\xB2" => "",                     # U+E00B2 => 
"\xF3\xA0\x82\xB3" => "",                     # U+E00B3 => 
"\xF3\xA0\x82\xB4" => "",                     # U+E00B4 => 
"\xF3\xA0\x82\xB5" => "",                     # U+E00B5 => 
"\xF3\xA0\x82\xB6" => "",                     # U+E00B6 => 
"\xF3\xA0\x82\xB7" => "",                     # U+E00B7 => 
"\xF3\xA0\x82\xB8" => "",                     # U+E00B8 => 
"\xF3\xA0\x82\xB9" => "",                     # U+E00B9 => 
"\xF3\xA0\x82\xBA" => "",                     # U+E00BA => 
"\xF3\xA0\x82\xBB" => "",                     # U+E00BB => 
"\xF3\xA0\x82\xBC" => "",                     # U+E00BC => 
"\xF3\xA0\x82\xBD" => "",                     # U+E00BD => 
"\xF3\xA0\x82\xBE" => "",                     # U+E00BE => 
"\xF3\xA0\x82\xBF" => "",                     # U+E00BF => 
"\xF3\xA0\x83\x80" => "",                     # U+E00C0 => 
"\xF3\xA0\x83\x81" => "",                     # U+E00C1 => 
"\xF3\xA0\x83\x82" => "",                     # U+E00C2 => 
"\xF3\xA0\x83\x83" => "",                     # U+E00C3 => 
"\xF3\xA0\x83\x84" => "",                     # U+E00C4 => 
"\xF3\xA0\x83\x85" => "",                     # U+E00C5 => 
"\xF3\xA0\x83\x86" => "",                     # U+E00C6 => 
"\xF3\xA0\x83\x87" => "",                     # U+E00C7 => 
"\xF3\xA0\x83\x88" => "",                     # U+E00C8 => 
"\xF3\xA0\x83\x89" => "",                     # U+E00C9 => 
"\xF3\xA0\x83\x8A" => "",                     # U+E00CA => 
"\xF3\xA0\x83\x8B" => "",                     # U+E00CB => 
"\xF3\xA0\x83\x8C" => "",                     # U+E00CC => 
"\xF3\xA0\x83\x8D" => "",                     # U+E00CD => 
"\xF3\xA0\x83\x8E" => "",                     # U+E00CE => 
"\xF3\xA0\x83\x8F" => "",                     # U+E00CF => 
"\xF3\xA0\x83\x90" => "",                     # U+E00D0 => 
"\xF3\xA0\x83\x91" => "",                     # U+E00D1 => 
"\xF3\xA0\x83\x92" => "",                     # U+E00D2 => 
"\xF3\xA0\x83\x93" => "",                     # U+E00D3 => 
"\xF3\xA0\x83\x94" => "",                     # U+E00D4 => 
"\xF3\xA0\x83\x95" => "",                     # U+E00D5 => 
"\xF3\xA0\x83\x96" => "",                     # U+E00D6 => 
"\xF3\xA0\x83\x97" => "",                     # U+E00D7 => 
"\xF3\xA0\x83\x98" => "",                     # U+E00D8 => 
"\xF3\xA0\x83\x99" => "",                     # U+E00D9 => 
"\xF3\xA0\x83\x9A" => "",                     # U+E00DA => 
"\xF3\xA0\x83\x9B" => "",                     # U+E00DB => 
"\xF3\xA0\x83\x9C" => "",                     # U+E00DC => 
"\xF3\xA0\x83\x9D" => "",                     # U+E00DD => 
"\xF3\xA0\x83\x9E" => "",                     # U+E00DE => 
"\xF3\xA0\x83\x9F" => "",                     # U+E00DF => 
"\xF3\xA0\x83\xA0" => "",                     # U+E00E0 => 
"\xF3\xA0\x83\xA1" => "",                     # U+E00E1 => 
"\xF3\xA0\x83\xA2" => "",                     # U+E00E2 => 
"\xF3\xA0\x83\xA3" => "",                     # U+E00E3 => 
"\xF3\xA0\x83\xA4" => "",                     # U+E00E4 => 
"\xF3\xA0\x83\xA5" => "",                     # U+E00E5 => 
"\xF3\xA0\x83\xA6" => "",                     # U+E00E6 => 
"\xF3\xA0\x83\xA7" => "",                     # U+E00E7 => 
"\xF3\xA0\x83\xA8" => "",                     # U+E00E8 => 
"\xF3\xA0\x83\xA9" => "",                     # U+E00E9 => 
"\xF3\xA0\x83\xAA" => "",                     # U+E00EA => 
"\xF3\xA0\x83\xAB" => "",                     # U+E00EB => 
"\xF3\xA0\x83\xAC" => "",                     # U+E00EC => 
"\xF3\xA0\x83\xAD" => "",                     # U+E00ED => 
"\xF3\xA0\x83\xAE" => "",                     # U+E00EE => 
"\xF3\xA0\x83\xAF" => "",                     # U+E00EF => 
"\xF3\xA0\x83\xB0" => "",                     # U+E00F0 => 
"\xF3\xA0\x83\xB1" => "",                     # U+E00F1 => 
"\xF3\xA0\x83\xB2" => "",                     # U+E00F2 => 
"\xF3\xA0\x83\xB3" => "",                     # U+E00F3 => 
"\xF3\xA0\x83\xB4" => "",                     # U+E00F4 => 
"\xF3\xA0\x83\xB5" => "",                     # U+E00F5 => 
"\xF3\xA0\x83\xB6" => "",                     # U+E00F6 => 
"\xF3\xA0\x83\xB7" => "",                     # U+E00F7 => 
"\xF3\xA0\x83\xB8" => "",                     # U+E00F8 => 
"\xF3\xA0\x83\xB9" => "",                     # U+E00F9 => 
"\xF3\xA0\x83\xBA" => "",                     # U+E00FA => 
"\xF3\xA0\x83\xBB" => "",                     # U+E00FB => 
"\xF3\xA0\x83\xBC" => "",                     # U+E00FC => 
"\xF3\xA0\x83\xBD" => "",                     # U+E00FD => 
"\xF3\xA0\x83\xBE" => "",                     # U+E00FE => 
"\xF3\xA0\x83\xBF" => "",                     # U+E00FF => 
"\xF3\xA0\x84\x80" => "",                     # U+E0100 => 
"\xF3\xA0\x84\x81" => "",                     # U+E0101 => 
"\xF3\xA0\x84\x82" => "",                     # U+E0102 => 
"\xF3\xA0\x84\x83" => "",                     # U+E0103 => 
"\xF3\xA0\x84\x84" => "",                     # U+E0104 => 
"\xF3\xA0\x84\x85" => "",                     # U+E0105 => 
"\xF3\xA0\x84\x86" => "",                     # U+E0106 => 
"\xF3\xA0\x84\x87" => "",                     # U+E0107 => 
"\xF3\xA0\x84\x88" => "",                     # U+E0108 => 
"\xF3\xA0\x84\x89" => "",                     # U+E0109 => 
"\xF3\xA0\x84\x8A" => "",                     # U+E010A => 
"\xF3\xA0\x84\x8B" => "",                     # U+E010B => 
"\xF3\xA0\x84\x8C" => "",                     # U+E010C => 
"\xF3\xA0\x84\x8D" => "",                     # U+E010D => 
"\xF3\xA0\x84\x8E" => "",                     # U+E010E => 
"\xF3\xA0\x84\x8F" => "",                     # U+E010F => 
"\xF3\xA0\x84\x90" => "",                     # U+E0110 => 
"\xF3\xA0\x84\x91" => "",                     # U+E0111 => 
"\xF3\xA0\x84\x92" => "",                     # U+E0112 => 
"\xF3\xA0\x84\x93" => "",                     # U+E0113 => 
"\xF3\xA0\x84\x94" => "",                     # U+E0114 => 
"\xF3\xA0\x84\x95" => "",                     # U+E0115 => 
"\xF3\xA0\x84\x96" => "",                     # U+E0116 => 
"\xF3\xA0\x84\x97" => "",                     # U+E0117 => 
"\xF3\xA0\x84\x98" => "",                     # U+E0118 => 
"\xF3\xA0\x84\x99" => "",                     # U+E0119 => 
"\xF3\xA0\x84\x9A" => "",                     # U+E011A => 
"\xF3\xA0\x84\x9B" => "",                     # U+E011B => 
"\xF3\xA0\x84\x9C" => "",                     # U+E011C => 
"\xF3\xA0\x84\x9D" => "",                     # U+E011D => 
"\xF3\xA0\x84\x9E" => "",                     # U+E011E => 
"\xF3\xA0\x84\x9F" => "",                     # U+E011F => 
"\xF3\xA0\x84\xA0" => "",                     # U+E0120 => 
"\xF3\xA0\x84\xA1" => "",                     # U+E0121 => 
"\xF3\xA0\x84\xA2" => "",                     # U+E0122 => 
"\xF3\xA0\x84\xA3" => "",                     # U+E0123 => 
"\xF3\xA0\x84\xA4" => "",                     # U+E0124 => 
"\xF3\xA0\x84\xA5" => "",                     # U+E0125 => 
"\xF3\xA0\x84\xA6" => "",                     # U+E0126 => 
"\xF3\xA0\x84\xA7" => "",                     # U+E0127 => 
"\xF3\xA0\x84\xA8" => "",                     # U+E0128 => 
"\xF3\xA0\x84\xA9" => "",                     # U+E0129 => 
"\xF3\xA0\x84\xAA" => "",                     # U+E012A => 
"\xF3\xA0\x84\xAB" => "",                     # U+E012B => 
"\xF3\xA0\x84\xAC" => "",                     # U+E012C => 
"\xF3\xA0\x84\xAD" => "",                     # U+E012D => 
"\xF3\xA0\x84\xAE" => "",                     # U+E012E => 
"\xF3\xA0\x84\xAF" => "",                     # U+E012F => 
"\xF3\xA0\x84\xB0" => "",                     # U+E0130 => 
"\xF3\xA0\x84\xB1" => "",                     # U+E0131 => 
"\xF3\xA0\x84\xB2" => "",                     # U+E0132 => 
"\xF3\xA0\x84\xB3" => "",                     # U+E0133 => 
"\xF3\xA0\x84\xB4" => "",                     # U+E0134 => 
"\xF3\xA0\x84\xB5" => "",                     # U+E0135 => 
"\xF3\xA0\x84\xB6" => "",                     # U+E0136 => 
"\xF3\xA0\x84\xB7" => "",                     # U+E0137 => 
"\xF3\xA0\x84\xB8" => "",                     # U+E0138 => 
"\xF3\xA0\x84\xB9" => "",                     # U+E0139 => 
"\xF3\xA0\x84\xBA" => "",                     # U+E013A => 
"\xF3\xA0\x84\xBB" => "",                     # U+E013B => 
"\xF3\xA0\x84\xBC" => "",                     # U+E013C => 
"\xF3\xA0\x84\xBD" => "",                     # U+E013D => 
"\xF3\xA0\x84\xBE" => "",                     # U+E013E => 
"\xF3\xA0\x84\xBF" => "",                     # U+E013F => 
"\xF3\xA0\x85\x80" => "",                     # U+E0140 => 
"\xF3\xA0\x85\x81" => "",                     # U+E0141 => 
"\xF3\xA0\x85\x82" => "",                     # U+E0142 => 
"\xF3\xA0\x85\x83" => "",                     # U+E0143 => 
"\xF3\xA0\x85\x84" => "",                     # U+E0144 => 
"\xF3\xA0\x85\x85" => "",                     # U+E0145 => 
"\xF3\xA0\x85\x86" => "",                     # U+E0146 => 
"\xF3\xA0\x85\x87" => "",                     # U+E0147 => 
"\xF3\xA0\x85\x88" => "",                     # U+E0148 => 
"\xF3\xA0\x85\x89" => "",                     # U+E0149 => 
"\xF3\xA0\x85\x8A" => "",                     # U+E014A => 
"\xF3\xA0\x85\x8B" => "",                     # U+E014B => 
"\xF3\xA0\x85\x8C" => "",                     # U+E014C => 
"\xF3\xA0\x85\x8D" => "",                     # U+E014D => 
"\xF3\xA0\x85\x8E" => "",                     # U+E014E => 
"\xF3\xA0\x85\x8F" => "",                     # U+E014F => 
"\xF3\xA0\x85\x90" => "",                     # U+E0150 => 
"\xF3\xA0\x85\x91" => "",                     # U+E0151 => 
"\xF3\xA0\x85\x92" => "",                     # U+E0152 => 
"\xF3\xA0\x85\x93" => "",                     # U+E0153 => 
"\xF3\xA0\x85\x94" => "",                     # U+E0154 => 
"\xF3\xA0\x85\x95" => "",                     # U+E0155 => 
"\xF3\xA0\x85\x96" => "",                     # U+E0156 => 
"\xF3\xA0\x85\x97" => "",                     # U+E0157 => 
"\xF3\xA0\x85\x98" => "",                     # U+E0158 => 
"\xF3\xA0\x85\x99" => "",                     # U+E0159 => 
"\xF3\xA0\x85\x9A" => "",                     # U+E015A => 
"\xF3\xA0\x85\x9B" => "",                     # U+E015B => 
"\xF3\xA0\x85\x9C" => "",                     # U+E015C => 
"\xF3\xA0\x85\x9D" => "",                     # U+E015D => 
"\xF3\xA0\x85\x9E" => "",                     # U+E015E => 
"\xF3\xA0\x85\x9F" => "",                     # U+E015F => 
"\xF3\xA0\x85\xA0" => "",                     # U+E0160 => 
"\xF3\xA0\x85\xA1" => "",                     # U+E0161 => 
"\xF3\xA0\x85\xA2" => "",                     # U+E0162 => 
"\xF3\xA0\x85\xA3" => "",                     # U+E0163 => 
"\xF3\xA0\x85\xA4" => "",                     # U+E0164 => 
"\xF3\xA0\x85\xA5" => "",                     # U+E0165 => 
"\xF3\xA0\x85\xA6" => "",                     # U+E0166 => 
"\xF3\xA0\x85\xA7" => "",                     # U+E0167 => 
"\xF3\xA0\x85\xA8" => "",                     # U+E0168 => 
"\xF3\xA0\x85\xA9" => "",                     # U+E0169 => 
"\xF3\xA0\x85\xAA" => "",                     # U+E016A => 
"\xF3\xA0\x85\xAB" => "",                     # U+E016B => 
"\xF3\xA0\x85\xAC" => "",                     # U+E016C => 
"\xF3\xA0\x85\xAD" => "",                     # U+E016D => 
"\xF3\xA0\x85\xAE" => "",                     # U+E016E => 
"\xF3\xA0\x85\xAF" => "",                     # U+E016F => 
"\xF3\xA0\x85\xB0" => "",                     # U+E0170 => 
"\xF3\xA0\x85\xB1" => "",                     # U+E0171 => 
"\xF3\xA0\x85\xB2" => "",                     # U+E0172 => 
"\xF3\xA0\x85\xB3" => "",                     # U+E0173 => 
"\xF3\xA0\x85\xB4" => "",                     # U+E0174 => 
"\xF3\xA0\x85\xB5" => "",                     # U+E0175 => 
"\xF3\xA0\x85\xB6" => "",                     # U+E0176 => 
"\xF3\xA0\x85\xB7" => "",                     # U+E0177 => 
"\xF3\xA0\x85\xB8" => "",                     # U+E0178 => 
"\xF3\xA0\x85\xB9" => "",                     # U+E0179 => 
"\xF3\xA0\x85\xBA" => "",                     # U+E017A => 
"\xF3\xA0\x85\xBB" => "",                     # U+E017B => 
"\xF3\xA0\x85\xBC" => "",                     # U+E017C => 
"\xF3\xA0\x85\xBD" => "",                     # U+E017D => 
"\xF3\xA0\x85\xBE" => "",                     # U+E017E => 
"\xF3\xA0\x85\xBF" => "",                     # U+E017F => 
"\xF3\xA0\x86\x80" => "",                     # U+E0180 => 
"\xF3\xA0\x86\x81" => "",                     # U+E0181 => 
"\xF3\xA0\x86\x82" => "",                     # U+E0182 => 
"\xF3\xA0\x86\x83" => "",                     # U+E0183 => 
"\xF3\xA0\x86\x84" => "",                     # U+E0184 => 
"\xF3\xA0\x86\x85" => "",                     # U+E0185 => 
"\xF3\xA0\x86\x86" => "",                     # U+E0186 => 
"\xF3\xA0\x86\x87" => "",                     # U+E0187 => 
"\xF3\xA0\x86\x88" => "",                     # U+E0188 => 
"\xF3\xA0\x86\x89" => "",                     # U+E0189 => 
"\xF3\xA0\x86\x8A" => "",                     # U+E018A => 
"\xF3\xA0\x86\x8B" => "",                     # U+E018B => 
"\xF3\xA0\x86\x8C" => "",                     # U+E018C => 
"\xF3\xA0\x86\x8D" => "",                     # U+E018D => 
"\xF3\xA0\x86\x8E" => "",                     # U+E018E => 
"\xF3\xA0\x86\x8F" => "",                     # U+E018F => 
"\xF3\xA0\x86\x90" => "",                     # U+E0190 => 
"\xF3\xA0\x86\x91" => "",                     # U+E0191 => 
"\xF3\xA0\x86\x92" => "",                     # U+E0192 => 
"\xF3\xA0\x86\x93" => "",                     # U+E0193 => 
"\xF3\xA0\x86\x94" => "",                     # U+E0194 => 
"\xF3\xA0\x86\x95" => "",                     # U+E0195 => 
"\xF3\xA0\x86\x96" => "",                     # U+E0196 => 
"\xF3\xA0\x86\x97" => "",                     # U+E0197 => 
"\xF3\xA0\x86\x98" => "",                     # U+E0198 => 
"\xF3\xA0\x86\x99" => "",                     # U+E0199 => 
"\xF3\xA0\x86\x9A" => "",                     # U+E019A => 
"\xF3\xA0\x86\x9B" => "",                     # U+E019B => 
"\xF3\xA0\x86\x9C" => "",                     # U+E019C => 
"\xF3\xA0\x86\x9D" => "",                     # U+E019D => 
"\xF3\xA0\x86\x9E" => "",                     # U+E019E => 
"\xF3\xA0\x86\x9F" => "",                     # U+E019F => 
"\xF3\xA0\x86\xA0" => "",                     # U+E01A0 => 
"\xF3\xA0\x86\xA1" => "",                     # U+E01A1 => 
"\xF3\xA0\x86\xA2" => "",                     # U+E01A2 => 
"\xF3\xA0\x86\xA3" => "",                     # U+E01A3 => 
"\xF3\xA0\x86\xA4" => "",                     # U+E01A4 => 
"\xF3\xA0\x86\xA5" => "",                     # U+E01A5 => 
"\xF3\xA0\x86\xA6" => "",                     # U+E01A6 => 
"\xF3\xA0\x86\xA7" => "",                     # U+E01A7 => 
"\xF3\xA0\x86\xA8" => "",                     # U+E01A8 => 
"\xF3\xA0\x86\xA9" => "",                     # U+E01A9 => 
"\xF3\xA0\x86\xAA" => "",                     # U+E01AA => 
"\xF3\xA0\x86\xAB" => "",                     # U+E01AB => 
"\xF3\xA0\x86\xAC" => "",                     # U+E01AC => 
"\xF3\xA0\x86\xAD" => "",                     # U+E01AD => 
"\xF3\xA0\x86\xAE" => "",                     # U+E01AE => 
"\xF3\xA0\x86\xAF" => "",                     # U+E01AF => 
"\xF3\xA0\x86\xB0" => "",                     # U+E01B0 => 
"\xF3\xA0\x86\xB1" => "",                     # U+E01B1 => 
"\xF3\xA0\x86\xB2" => "",                     # U+E01B2 => 
"\xF3\xA0\x86\xB3" => "",                     # U+E01B3 => 
"\xF3\xA0\x86\xB4" => "",                     # U+E01B4 => 
"\xF3\xA0\x86\xB5" => "",                     # U+E01B5 => 
"\xF3\xA0\x86\xB6" => "",                     # U+E01B6 => 
"\xF3\xA0\x86\xB7" => "",                     # U+E01B7 => 
"\xF3\xA0\x86\xB8" => "",                     # U+E01B8 => 
"\xF3\xA0\x86\xB9" => "",                     # U+E01B9 => 
"\xF3\xA0\x86\xBA" => "",                     # U+E01BA => 
"\xF3\xA0\x86\xBB" => "",                     # U+E01BB => 
"\xF3\xA0\x86\xBC" => "",                     # U+E01BC => 
"\xF3\xA0\x86\xBD" => "",                     # U+E01BD => 
"\xF3\xA0\x86\xBE" => "",                     # U+E01BE => 
"\xF3\xA0\x86\xBF" => "",                     # U+E01BF => 
"\xF3\xA0\x87\x80" => "",                     # U+E01C0 => 
"\xF3\xA0\x87\x81" => "",                     # U+E01C1 => 
"\xF3\xA0\x87\x82" => "",                     # U+E01C2 => 
"\xF3\xA0\x87\x83" => "",                     # U+E01C3 => 
"\xF3\xA0\x87\x84" => "",                     # U+E01C4 => 
"\xF3\xA0\x87\x85" => "",                     # U+E01C5 => 
"\xF3\xA0\x87\x86" => "",                     # U+E01C6 => 
"\xF3\xA0\x87\x87" => "",                     # U+E01C7 => 
"\xF3\xA0\x87\x88" => "",                     # U+E01C8 => 
"\xF3\xA0\x87\x89" => "",                     # U+E01C9 => 
"\xF3\xA0\x87\x8A" => "",                     # U+E01CA => 
"\xF3\xA0\x87\x8B" => "",                     # U+E01CB => 
"\xF3\xA0\x87\x8C" => "",                     # U+E01CC => 
"\xF3\xA0\x87\x8D" => "",                     # U+E01CD => 
"\xF3\xA0\x87\x8E" => "",                     # U+E01CE => 
"\xF3\xA0\x87\x8F" => "",                     # U+E01CF => 
"\xF3\xA0\x87\x90" => "",                     # U+E01D0 => 
"\xF3\xA0\x87\x91" => "",                     # U+E01D1 => 
"\xF3\xA0\x87\x92" => "",                     # U+E01D2 => 
"\xF3\xA0\x87\x93" => "",                     # U+E01D3 => 
"\xF3\xA0\x87\x94" => "",                     # U+E01D4 => 
"\xF3\xA0\x87\x95" => "",                     # U+E01D5 => 
"\xF3\xA0\x87\x96" => "",                     # U+E01D6 => 
"\xF3\xA0\x87\x97" => "",                     # U+E01D7 => 
"\xF3\xA0\x87\x98" => "",                     # U+E01D8 => 
"\xF3\xA0\x87\x99" => "",                     # U+E01D9 => 
"\xF3\xA0\x87\x9A" => "",                     # U+E01DA => 
"\xF3\xA0\x87\x9B" => "",                     # U+E01DB => 
"\xF3\xA0\x87\x9C" => "",                     # U+E01DC => 
"\xF3\xA0\x87\x9D" => "",                     # U+E01DD => 
"\xF3\xA0\x87\x9E" => "",                     # U+E01DE => 
"\xF3\xA0\x87\x9F" => "",                     # U+E01DF => 
"\xF3\xA0\x87\xA0" => "",                     # U+E01E0 => 
"\xF3\xA0\x87\xA1" => "",                     # U+E01E1 => 
"\xF3\xA0\x87\xA2" => "",                     # U+E01E2 => 
"\xF3\xA0\x87\xA3" => "",                     # U+E01E3 => 
"\xF3\xA0\x87\xA4" => "",                     # U+E01E4 => 
"\xF3\xA0\x87\xA5" => "",                     # U+E01E5 => 
"\xF3\xA0\x87\xA6" => "",                     # U+E01E6 => 
"\xF3\xA0\x87\xA7" => "",                     # U+E01E7 => 
"\xF3\xA0\x87\xA8" => "",                     # U+E01E8 => 
"\xF3\xA0\x87\xA9" => "",                     # U+E01E9 => 
"\xF3\xA0\x87\xAA" => "",                     # U+E01EA => 
"\xF3\xA0\x87\xAB" => "",                     # U+E01EB => 
"\xF3\xA0\x87\xAC" => "",                     # U+E01EC => 
"\xF3\xA0\x87\xAD" => "",                     # U+E01ED => 
"\xF3\xA0\x87\xAE" => "",                     # U+E01EE => 
"\xF3\xA0\x87\xAF" => "",                     # U+E01EF => 
"\xF3\xA0\x87\xB0" => "",                     # U+E01F0 => 
"\xF3\xA0\x87\xB1" => "",                     # U+E01F1 => 
"\xF3\xA0\x87\xB2" => "",                     # U+E01F2 => 
"\xF3\xA0\x87\xB3" => "",                     # U+E01F3 => 
"\xF3\xA0\x87\xB4" => "",                     # U+E01F4 => 
"\xF3\xA0\x87\xB5" => "",                     # U+E01F5 => 
"\xF3\xA0\x87\xB6" => "",                     # U+E01F6 => 
"\xF3\xA0\x87\xB7" => "",                     # U+E01F7 => 
"\xF3\xA0\x87\xB8" => "",                     # U+E01F8 => 
"\xF3\xA0\x87\xB9" => "",                     # U+E01F9 => 
"\xF3\xA0\x87\xBA" => "",                     # U+E01FA => 
"\xF3\xA0\x87\xBB" => "",                     # U+E01FB => 
"\xF3\xA0\x87\xBC" => "",                     # U+E01FC => 
"\xF3\xA0\x87\xBD" => "",                     # U+E01FD => 
"\xF3\xA0\x87\xBE" => "",                     # U+E01FE => 
"\xF3\xA0\x87\xBF" => "",                     # U+E01FF => 
"\xF3\xA0\x88\x80" => "",                     # U+E0200 => 
"\xF3\xA0\x88\x81" => "",                     # U+E0201 => 
"\xF3\xA0\x88\x82" => "",                     # U+E0202 => 
"\xF3\xA0\x88\x83" => "",                     # U+E0203 => 
"\xF3\xA0\x88\x84" => "",                     # U+E0204 => 
"\xF3\xA0\x88\x85" => "",                     # U+E0205 => 
"\xF3\xA0\x88\x86" => "",                     # U+E0206 => 
"\xF3\xA0\x88\x87" => "",                     # U+E0207 => 
"\xF3\xA0\x88\x88" => "",                     # U+E0208 => 
"\xF3\xA0\x88\x89" => "",                     # U+E0209 => 
"\xF3\xA0\x88\x8A" => "",                     # U+E020A => 
"\xF3\xA0\x88\x8B" => "",                     # U+E020B => 
"\xF3\xA0\x88\x8C" => "",                     # U+E020C => 
"\xF3\xA0\x88\x8D" => "",                     # U+E020D => 
"\xF3\xA0\x88\x8E" => "",                     # U+E020E => 
"\xF3\xA0\x88\x8F" => "",                     # U+E020F => 
"\xF3\xA0\x88\x90" => "",                     # U+E0210 => 
"\xF3\xA0\x88\x91" => "",                     # U+E0211 => 
"\xF3\xA0\x88\x92" => "",                     # U+E0212 => 
"\xF3\xA0\x88\x93" => "",                     # U+E0213 => 
"\xF3\xA0\x88\x94" => "",                     # U+E0214 => 
"\xF3\xA0\x88\x95" => "",                     # U+E0215 => 
"\xF3\xA0\x88\x96" => "",                     # U+E0216 => 
"\xF3\xA0\x88\x97" => "",                     # U+E0217 => 
"\xF3\xA0\x88\x98" => "",                     # U+E0218 => 
"\xF3\xA0\x88\x99" => "",                     # U+E0219 => 
"\xF3\xA0\x88\x9A" => "",                     # U+E021A => 
"\xF3\xA0\x88\x9B" => "",                     # U+E021B => 
"\xF3\xA0\x88\x9C" => "",                     # U+E021C => 
"\xF3\xA0\x88\x9D" => "",                     # U+E021D => 
"\xF3\xA0\x88\x9E" => "",                     # U+E021E => 
"\xF3\xA0\x88\x9F" => "",                     # U+E021F => 
"\xF3\xA0\x88\xA0" => "",                     # U+E0220 => 
"\xF3\xA0\x88\xA1" => "",                     # U+E0221 => 
"\xF3\xA0\x88\xA2" => "",                     # U+E0222 => 
"\xF3\xA0\x88\xA3" => "",                     # U+E0223 => 
"\xF3\xA0\x88\xA4" => "",                     # U+E0224 => 
"\xF3\xA0\x88\xA5" => "",                     # U+E0225 => 
"\xF3\xA0\x88\xA6" => "",                     # U+E0226 => 
"\xF3\xA0\x88\xA7" => "",                     # U+E0227 => 
"\xF3\xA0\x88\xA8" => "",                     # U+E0228 => 
"\xF3\xA0\x88\xA9" => "",                     # U+E0229 => 
"\xF3\xA0\x88\xAA" => "",                     # U+E022A => 
"\xF3\xA0\x88\xAB" => "",                     # U+E022B => 
"\xF3\xA0\x88\xAC" => "",                     # U+E022C => 
"\xF3\xA0\x88\xAD" => "",                     # U+E022D => 
"\xF3\xA0\x88\xAE" => "",                     # U+E022E => 
"\xF3\xA0\x88\xAF" => "",                     # U+E022F => 
"\xF3\xA0\x88\xB0" => "",                     # U+E0230 => 
"\xF3\xA0\x88\xB1" => "",                     # U+E0231 => 
"\xF3\xA0\x88\xB2" => "",                     # U+E0232 => 
"\xF3\xA0\x88\xB3" => "",                     # U+E0233 => 
"\xF3\xA0\x88\xB4" => "",                     # U+E0234 => 
"\xF3\xA0\x88\xB5" => "",                     # U+E0235 => 
"\xF3\xA0\x88\xB6" => "",                     # U+E0236 => 
"\xF3\xA0\x88\xB7" => "",                     # U+E0237 => 
"\xF3\xA0\x88\xB8" => "",                     # U+E0238 => 
"\xF3\xA0\x88\xB9" => "",                     # U+E0239 => 
"\xF3\xA0\x88\xBA" => "",                     # U+E023A => 
"\xF3\xA0\x88\xBB" => "",                     # U+E023B => 
"\xF3\xA0\x88\xBC" => "",                     # U+E023C => 
"\xF3\xA0\x88\xBD" => "",                     # U+E023D => 
"\xF3\xA0\x88\xBE" => "",                     # U+E023E => 
"\xF3\xA0\x88\xBF" => "",                     # U+E023F => 
"\xF3\xA0\x89\x80" => "",                     # U+E0240 => 
"\xF3\xA0\x89\x81" => "",                     # U+E0241 => 
"\xF3\xA0\x89\x82" => "",                     # U+E0242 => 
"\xF3\xA0\x89\x83" => "",                     # U+E0243 => 
"\xF3\xA0\x89\x84" => "",                     # U+E0244 => 
"\xF3\xA0\x89\x85" => "",                     # U+E0245 => 
"\xF3\xA0\x89\x86" => "",                     # U+E0246 => 
"\xF3\xA0\x89\x87" => "",                     # U+E0247 => 
"\xF3\xA0\x89\x88" => "",                     # U+E0248 => 
"\xF3\xA0\x89\x89" => "",                     # U+E0249 => 
"\xF3\xA0\x89\x8A" => "",                     # U+E024A => 
"\xF3\xA0\x89\x8B" => "",                     # U+E024B => 
"\xF3\xA0\x89\x8C" => "",                     # U+E024C => 
"\xF3\xA0\x89\x8D" => "",                     # U+E024D => 
"\xF3\xA0\x89\x8E" => "",                     # U+E024E => 
"\xF3\xA0\x89\x8F" => "",                     # U+E024F => 
"\xF3\xA0\x89\x90" => "",                     # U+E0250 => 
"\xF3\xA0\x89\x91" => "",                     # U+E0251 => 
"\xF3\xA0\x89\x92" => "",                     # U+E0252 => 
"\xF3\xA0\x89\x93" => "",                     # U+E0253 => 
"\xF3\xA0\x89\x94" => "",                     # U+E0254 => 
"\xF3\xA0\x89\x95" => "",                     # U+E0255 => 
"\xF3\xA0\x89\x96" => "",                     # U+E0256 => 
"\xF3\xA0\x89\x97" => "",                     # U+E0257 => 
"\xF3\xA0\x89\x98" => "",                     # U+E0258 => 
"\xF3\xA0\x89\x99" => "",                     # U+E0259 => 
"\xF3\xA0\x89\x9A" => "",                     # U+E025A => 
"\xF3\xA0\x89\x9B" => "",                     # U+E025B => 
"\xF3\xA0\x89\x9C" => "",                     # U+E025C => 
"\xF3\xA0\x89\x9D" => "",                     # U+E025D => 
"\xF3\xA0\x89\x9E" => "",                     # U+E025E => 
"\xF3\xA0\x89\x9F" => "",                     # U+E025F => 
"\xF3\xA0\x89\xA0" => "",                     # U+E0260 => 
"\xF3\xA0\x89\xA1" => "",                     # U+E0261 => 
"\xF3\xA0\x89\xA2" => "",                     # U+E0262 => 
"\xF3\xA0\x89\xA3" => "",                     # U+E0263 => 
"\xF3\xA0\x89\xA4" => "",                     # U+E0264 => 
"\xF3\xA0\x89\xA5" => "",                     # U+E0265 => 
"\xF3\xA0\x89\xA6" => "",                     # U+E0266 => 
"\xF3\xA0\x89\xA7" => "",                     # U+E0267 => 
"\xF3\xA0\x89\xA8" => "",                     # U+E0268 => 
"\xF3\xA0\x89\xA9" => "",                     # U+E0269 => 
"\xF3\xA0\x89\xAA" => "",                     # U+E026A => 
"\xF3\xA0\x89\xAB" => "",                     # U+E026B => 
"\xF3\xA0\x89\xAC" => "",                     # U+E026C => 
"\xF3\xA0\x89\xAD" => "",                     # U+E026D => 
"\xF3\xA0\x89\xAE" => "",                     # U+E026E => 
"\xF3\xA0\x89\xAF" => "",                     # U+E026F => 
"\xF3\xA0\x89\xB0" => "",                     # U+E0270 => 
"\xF3\xA0\x89\xB1" => "",                     # U+E0271 => 
"\xF3\xA0\x89\xB2" => "",                     # U+E0272 => 
"\xF3\xA0\x89\xB3" => "",                     # U+E0273 => 
"\xF3\xA0\x89\xB4" => "",                     # U+E0274 => 
"\xF3\xA0\x89\xB5" => "",                     # U+E0275 => 
"\xF3\xA0\x89\xB6" => "",                     # U+E0276 => 
"\xF3\xA0\x89\xB7" => "",                     # U+E0277 => 
"\xF3\xA0\x89\xB8" => "",                     # U+E0278 => 
"\xF3\xA0\x89\xB9" => "",                     # U+E0279 => 
"\xF3\xA0\x89\xBA" => "",                     # U+E027A => 
"\xF3\xA0\x89\xBB" => "",                     # U+E027B => 
"\xF3\xA0\x89\xBC" => "",                     # U+E027C => 
"\xF3\xA0\x89\xBD" => "",                     # U+E027D => 
"\xF3\xA0\x89\xBE" => "",                     # U+E027E => 
"\xF3\xA0\x89\xBF" => "",                     # U+E027F => 
"\xF3\xA0\x8A\x80" => "",                     # U+E0280 => 
"\xF3\xA0\x8A\x81" => "",                     # U+E0281 => 
"\xF3\xA0\x8A\x82" => "",                     # U+E0282 => 
"\xF3\xA0\x8A\x83" => "",                     # U+E0283 => 
"\xF3\xA0\x8A\x84" => "",                     # U+E0284 => 
"\xF3\xA0\x8A\x85" => "",                     # U+E0285 => 
"\xF3\xA0\x8A\x86" => "",                     # U+E0286 => 
"\xF3\xA0\x8A\x87" => "",                     # U+E0287 => 
"\xF3\xA0\x8A\x88" => "",                     # U+E0288 => 
"\xF3\xA0\x8A\x89" => "",                     # U+E0289 => 
"\xF3\xA0\x8A\x8A" => "",                     # U+E028A => 
"\xF3\xA0\x8A\x8B" => "",                     # U+E028B => 
"\xF3\xA0\x8A\x8C" => "",                     # U+E028C => 
"\xF3\xA0\x8A\x8D" => "",                     # U+E028D => 
"\xF3\xA0\x8A\x8E" => "",                     # U+E028E => 
"\xF3\xA0\x8A\x8F" => "",                     # U+E028F => 
"\xF3\xA0\x8A\x90" => "",                     # U+E0290 => 
"\xF3\xA0\x8A\x91" => "",                     # U+E0291 => 
"\xF3\xA0\x8A\x92" => "",                     # U+E0292 => 
"\xF3\xA0\x8A\x93" => "",                     # U+E0293 => 
"\xF3\xA0\x8A\x94" => "",                     # U+E0294 => 
"\xF3\xA0\x8A\x95" => "",                     # U+E0295 => 
"\xF3\xA0\x8A\x96" => "",                     # U+E0296 => 
"\xF3\xA0\x8A\x97" => "",                     # U+E0297 => 
"\xF3\xA0\x8A\x98" => "",                     # U+E0298 => 
"\xF3\xA0\x8A\x99" => "",                     # U+E0299 => 
"\xF3\xA0\x8A\x9A" => "",                     # U+E029A => 
"\xF3\xA0\x8A\x9B" => "",                     # U+E029B => 
"\xF3\xA0\x8A\x9C" => "",                     # U+E029C => 
"\xF3\xA0\x8A\x9D" => "",                     # U+E029D => 
"\xF3\xA0\x8A\x9E" => "",                     # U+E029E => 
"\xF3\xA0\x8A\x9F" => "",                     # U+E029F => 
"\xF3\xA0\x8A\xA0" => "",                     # U+E02A0 => 
"\xF3\xA0\x8A\xA1" => "",                     # U+E02A1 => 
"\xF3\xA0\x8A\xA2" => "",                     # U+E02A2 => 
"\xF3\xA0\x8A\xA3" => "",                     # U+E02A3 => 
"\xF3\xA0\x8A\xA4" => "",                     # U+E02A4 => 
"\xF3\xA0\x8A\xA5" => "",                     # U+E02A5 => 
"\xF3\xA0\x8A\xA6" => "",                     # U+E02A6 => 
"\xF3\xA0\x8A\xA7" => "",                     # U+E02A7 => 
"\xF3\xA0\x8A\xA8" => "",                     # U+E02A8 => 
"\xF3\xA0\x8A\xA9" => "",                     # U+E02A9 => 
"\xF3\xA0\x8A\xAA" => "",                     # U+E02AA => 
"\xF3\xA0\x8A\xAB" => "",                     # U+E02AB => 
"\xF3\xA0\x8A\xAC" => "",                     # U+E02AC => 
"\xF3\xA0\x8A\xAD" => "",                     # U+E02AD => 
"\xF3\xA0\x8A\xAE" => "",                     # U+E02AE => 
"\xF3\xA0\x8A\xAF" => "",                     # U+E02AF => 
"\xF3\xA0\x8A\xB0" => "",                     # U+E02B0 => 
"\xF3\xA0\x8A\xB1" => "",                     # U+E02B1 => 
"\xF3\xA0\x8A\xB2" => "",                     # U+E02B2 => 
"\xF3\xA0\x8A\xB3" => "",                     # U+E02B3 => 
"\xF3\xA0\x8A\xB4" => "",                     # U+E02B4 => 
"\xF3\xA0\x8A\xB5" => "",                     # U+E02B5 => 
"\xF3\xA0\x8A\xB6" => "",                     # U+E02B6 => 
"\xF3\xA0\x8A\xB7" => "",                     # U+E02B7 => 
"\xF3\xA0\x8A\xB8" => "",                     # U+E02B8 => 
"\xF3\xA0\x8A\xB9" => "",                     # U+E02B9 => 
"\xF3\xA0\x8A\xBA" => "",                     # U+E02BA => 
"\xF3\xA0\x8A\xBB" => "",                     # U+E02BB => 
"\xF3\xA0\x8A\xBC" => "",                     # U+E02BC => 
"\xF3\xA0\x8A\xBD" => "",                     # U+E02BD => 
"\xF3\xA0\x8A\xBE" => "",                     # U+E02BE => 
"\xF3\xA0\x8A\xBF" => "",                     # U+E02BF => 
"\xF3\xA0\x8B\x80" => "",                     # U+E02C0 => 
"\xF3\xA0\x8B\x81" => "",                     # U+E02C1 => 
"\xF3\xA0\x8B\x82" => "",                     # U+E02C2 => 
"\xF3\xA0\x8B\x83" => "",                     # U+E02C3 => 
"\xF3\xA0\x8B\x84" => "",                     # U+E02C4 => 
"\xF3\xA0\x8B\x85" => "",                     # U+E02C5 => 
"\xF3\xA0\x8B\x86" => "",                     # U+E02C6 => 
"\xF3\xA0\x8B\x87" => "",                     # U+E02C7 => 
"\xF3\xA0\x8B\x88" => "",                     # U+E02C8 => 
"\xF3\xA0\x8B\x89" => "",                     # U+E02C9 => 
"\xF3\xA0\x8B\x8A" => "",                     # U+E02CA => 
"\xF3\xA0\x8B\x8B" => "",                     # U+E02CB => 
"\xF3\xA0\x8B\x8C" => "",                     # U+E02CC => 
"\xF3\xA0\x8B\x8D" => "",                     # U+E02CD => 
"\xF3\xA0\x8B\x8E" => "",                     # U+E02CE => 
"\xF3\xA0\x8B\x8F" => "",                     # U+E02CF => 
"\xF3\xA0\x8B\x90" => "",                     # U+E02D0 => 
"\xF3\xA0\x8B\x91" => "",                     # U+E02D1 => 
"\xF3\xA0\x8B\x92" => "",                     # U+E02D2 => 
"\xF3\xA0\x8B\x93" => "",                     # U+E02D3 => 
"\xF3\xA0\x8B\x94" => "",                     # U+E02D4 => 
"\xF3\xA0\x8B\x95" => "",                     # U+E02D5 => 
"\xF3\xA0\x8B\x96" => "",                     # U+E02D6 => 
"\xF3\xA0\x8B\x97" => "",                     # U+E02D7 => 
"\xF3\xA0\x8B\x98" => "",                     # U+E02D8 => 
"\xF3\xA0\x8B\x99" => "",                     # U+E02D9 => 
"\xF3\xA0\x8B\x9A" => "",                     # U+E02DA => 
"\xF3\xA0\x8B\x9B" => "",                     # U+E02DB => 
"\xF3\xA0\x8B\x9C" => "",                     # U+E02DC => 
"\xF3\xA0\x8B\x9D" => "",                     # U+E02DD => 
"\xF3\xA0\x8B\x9E" => "",                     # U+E02DE => 
"\xF3\xA0\x8B\x9F" => "",                     # U+E02DF => 
"\xF3\xA0\x8B\xA0" => "",                     # U+E02E0 => 
"\xF3\xA0\x8B\xA1" => "",                     # U+E02E1 => 
"\xF3\xA0\x8B\xA2" => "",                     # U+E02E2 => 
"\xF3\xA0\x8B\xA3" => "",                     # U+E02E3 => 
"\xF3\xA0\x8B\xA4" => "",                     # U+E02E4 => 
"\xF3\xA0\x8B\xA5" => "",                     # U+E02E5 => 
"\xF3\xA0\x8B\xA6" => "",                     # U+E02E6 => 
"\xF3\xA0\x8B\xA7" => "",                     # U+E02E7 => 
"\xF3\xA0\x8B\xA8" => "",                     # U+E02E8 => 
"\xF3\xA0\x8B\xA9" => "",                     # U+E02E9 => 
"\xF3\xA0\x8B\xAA" => "",                     # U+E02EA => 
"\xF3\xA0\x8B\xAB" => "",                     # U+E02EB => 
"\xF3\xA0\x8B\xAC" => "",                     # U+E02EC => 
"\xF3\xA0\x8B\xAD" => "",                     # U+E02ED => 
"\xF3\xA0\x8B\xAE" => "",                     # U+E02EE => 
"\xF3\xA0\x8B\xAF" => "",                     # U+E02EF => 
"\xF3\xA0\x8B\xB0" => "",                     # U+E02F0 => 
"\xF3\xA0\x8B\xB1" => "",                     # U+E02F1 => 
"\xF3\xA0\x8B\xB2" => "",                     # U+E02F2 => 
"\xF3\xA0\x8B\xB3" => "",                     # U+E02F3 => 
"\xF3\xA0\x8B\xB4" => "",                     # U+E02F4 => 
"\xF3\xA0\x8B\xB5" => "",                     # U+E02F5 => 
"\xF3\xA0\x8B\xB6" => "",                     # U+E02F6 => 
"\xF3\xA0\x8B\xB7" => "",                     # U+E02F7 => 
"\xF3\xA0\x8B\xB8" => "",                     # U+E02F8 => 
"\xF3\xA0\x8B\xB9" => "",                     # U+E02F9 => 
"\xF3\xA0\x8B\xBA" => "",                     # U+E02FA => 
"\xF3\xA0\x8B\xBB" => "",                     # U+E02FB => 
"\xF3\xA0\x8B\xBC" => "",                     # U+E02FC => 
"\xF3\xA0\x8B\xBD" => "",                     # U+E02FD => 
"\xF3\xA0\x8B\xBE" => "",                     # U+E02FE => 
"\xF3\xA0\x8B\xBF" => "",                     # U+E02FF => 
"\xF3\xA0\x8C\x80" => "",                     # U+E0300 => 
"\xF3\xA0\x8C\x81" => "",                     # U+E0301 => 
"\xF3\xA0\x8C\x82" => "",                     # U+E0302 => 
"\xF3\xA0\x8C\x83" => "",                     # U+E0303 => 
"\xF3\xA0\x8C\x84" => "",                     # U+E0304 => 
"\xF3\xA0\x8C\x85" => "",                     # U+E0305 => 
"\xF3\xA0\x8C\x86" => "",                     # U+E0306 => 
"\xF3\xA0\x8C\x87" => "",                     # U+E0307 => 
"\xF3\xA0\x8C\x88" => "",                     # U+E0308 => 
"\xF3\xA0\x8C\x89" => "",                     # U+E0309 => 
"\xF3\xA0\x8C\x8A" => "",                     # U+E030A => 
"\xF3\xA0\x8C\x8B" => "",                     # U+E030B => 
"\xF3\xA0\x8C\x8C" => "",                     # U+E030C => 
"\xF3\xA0\x8C\x8D" => "",                     # U+E030D => 
"\xF3\xA0\x8C\x8E" => "",                     # U+E030E => 
"\xF3\xA0\x8C\x8F" => "",                     # U+E030F => 
"\xF3\xA0\x8C\x90" => "",                     # U+E0310 => 
"\xF3\xA0\x8C\x91" => "",                     # U+E0311 => 
"\xF3\xA0\x8C\x92" => "",                     # U+E0312 => 
"\xF3\xA0\x8C\x93" => "",                     # U+E0313 => 
"\xF3\xA0\x8C\x94" => "",                     # U+E0314 => 
"\xF3\xA0\x8C\x95" => "",                     # U+E0315 => 
"\xF3\xA0\x8C\x96" => "",                     # U+E0316 => 
"\xF3\xA0\x8C\x97" => "",                     # U+E0317 => 
"\xF3\xA0\x8C\x98" => "",                     # U+E0318 => 
"\xF3\xA0\x8C\x99" => "",                     # U+E0319 => 
"\xF3\xA0\x8C\x9A" => "",                     # U+E031A => 
"\xF3\xA0\x8C\x9B" => "",                     # U+E031B => 
"\xF3\xA0\x8C\x9C" => "",                     # U+E031C => 
"\xF3\xA0\x8C\x9D" => "",                     # U+E031D => 
"\xF3\xA0\x8C\x9E" => "",                     # U+E031E => 
"\xF3\xA0\x8C\x9F" => "",                     # U+E031F => 
"\xF3\xA0\x8C\xA0" => "",                     # U+E0320 => 
"\xF3\xA0\x8C\xA1" => "",                     # U+E0321 => 
"\xF3\xA0\x8C\xA2" => "",                     # U+E0322 => 
"\xF3\xA0\x8C\xA3" => "",                     # U+E0323 => 
"\xF3\xA0\x8C\xA4" => "",                     # U+E0324 => 
"\xF3\xA0\x8C\xA5" => "",                     # U+E0325 => 
"\xF3\xA0\x8C\xA6" => "",                     # U+E0326 => 
"\xF3\xA0\x8C\xA7" => "",                     # U+E0327 => 
"\xF3\xA0\x8C\xA8" => "",                     # U+E0328 => 
"\xF3\xA0\x8C\xA9" => "",                     # U+E0329 => 
"\xF3\xA0\x8C\xAA" => "",                     # U+E032A => 
"\xF3\xA0\x8C\xAB" => "",                     # U+E032B => 
"\xF3\xA0\x8C\xAC" => "",                     # U+E032C => 
"\xF3\xA0\x8C\xAD" => "",                     # U+E032D => 
"\xF3\xA0\x8C\xAE" => "",                     # U+E032E => 
"\xF3\xA0\x8C\xAF" => "",                     # U+E032F => 
"\xF3\xA0\x8C\xB0" => "",                     # U+E0330 => 
"\xF3\xA0\x8C\xB1" => "",                     # U+E0331 => 
"\xF3\xA0\x8C\xB2" => "",                     # U+E0332 => 
"\xF3\xA0\x8C\xB3" => "",                     # U+E0333 => 
"\xF3\xA0\x8C\xB4" => "",                     # U+E0334 => 
"\xF3\xA0\x8C\xB5" => "",                     # U+E0335 => 
"\xF3\xA0\x8C\xB6" => "",                     # U+E0336 => 
"\xF3\xA0\x8C\xB7" => "",                     # U+E0337 => 
"\xF3\xA0\x8C\xB8" => "",                     # U+E0338 => 
"\xF3\xA0\x8C\xB9" => "",                     # U+E0339 => 
"\xF3\xA0\x8C\xBA" => "",                     # U+E033A => 
"\xF3\xA0\x8C\xBB" => "",                     # U+E033B => 
"\xF3\xA0\x8C\xBC" => "",                     # U+E033C => 
"\xF3\xA0\x8C\xBD" => "",                     # U+E033D => 
"\xF3\xA0\x8C\xBE" => "",                     # U+E033E => 
"\xF3\xA0\x8C\xBF" => "",                     # U+E033F => 
"\xF3\xA0\x8D\x80" => "",                     # U+E0340 => 
"\xF3\xA0\x8D\x81" => "",                     # U+E0341 => 
"\xF3\xA0\x8D\x82" => "",                     # U+E0342 => 
"\xF3\xA0\x8D\x83" => "",                     # U+E0343 => 
"\xF3\xA0\x8D\x84" => "",                     # U+E0344 => 
"\xF3\xA0\x8D\x85" => "",                     # U+E0345 => 
"\xF3\xA0\x8D\x86" => "",                     # U+E0346 => 
"\xF3\xA0\x8D\x87" => "",                     # U+E0347 => 
"\xF3\xA0\x8D\x88" => "",                     # U+E0348 => 
"\xF3\xA0\x8D\x89" => "",                     # U+E0349 => 
"\xF3\xA0\x8D\x8A" => "",                     # U+E034A => 
"\xF3\xA0\x8D\x8B" => "",                     # U+E034B => 
"\xF3\xA0\x8D\x8C" => "",                     # U+E034C => 
"\xF3\xA0\x8D\x8D" => "",                     # U+E034D => 
"\xF3\xA0\x8D\x8E" => "",                     # U+E034E => 
"\xF3\xA0\x8D\x8F" => "",                     # U+E034F => 
"\xF3\xA0\x8D\x90" => "",                     # U+E0350 => 
"\xF3\xA0\x8D\x91" => "",                     # U+E0351 => 
"\xF3\xA0\x8D\x92" => "",                     # U+E0352 => 
"\xF3\xA0\x8D\x93" => "",                     # U+E0353 => 
"\xF3\xA0\x8D\x94" => "",                     # U+E0354 => 
"\xF3\xA0\x8D\x95" => "",                     # U+E0355 => 
"\xF3\xA0\x8D\x96" => "",                     # U+E0356 => 
"\xF3\xA0\x8D\x97" => "",                     # U+E0357 => 
"\xF3\xA0\x8D\x98" => "",                     # U+E0358 => 
"\xF3\xA0\x8D\x99" => "",                     # U+E0359 => 
"\xF3\xA0\x8D\x9A" => "",                     # U+E035A => 
"\xF3\xA0\x8D\x9B" => "",                     # U+E035B => 
"\xF3\xA0\x8D\x9C" => "",                     # U+E035C => 
"\xF3\xA0\x8D\x9D" => "",                     # U+E035D => 
"\xF3\xA0\x8D\x9E" => "",                     # U+E035E => 
"\xF3\xA0\x8D\x9F" => "",                     # U+E035F => 
"\xF3\xA0\x8D\xA0" => "",                     # U+E0360 => 
"\xF3\xA0\x8D\xA1" => "",                     # U+E0361 => 
"\xF3\xA0\x8D\xA2" => "",                     # U+E0362 => 
"\xF3\xA0\x8D\xA3" => "",                     # U+E0363 => 
"\xF3\xA0\x8D\xA4" => "",                     # U+E0364 => 
"\xF3\xA0\x8D\xA5" => "",                     # U+E0365 => 
"\xF3\xA0\x8D\xA6" => "",                     # U+E0366 => 
"\xF3\xA0\x8D\xA7" => "",                     # U+E0367 => 
"\xF3\xA0\x8D\xA8" => "",                     # U+E0368 => 
"\xF3\xA0\x8D\xA9" => "",                     # U+E0369 => 
"\xF3\xA0\x8D\xAA" => "",                     # U+E036A => 
"\xF3\xA0\x8D\xAB" => "",                     # U+E036B => 
"\xF3\xA0\x8D\xAC" => "",                     # U+E036C => 
"\xF3\xA0\x8D\xAD" => "",                     # U+E036D => 
"\xF3\xA0\x8D\xAE" => "",                     # U+E036E => 
"\xF3\xA0\x8D\xAF" => "",                     # U+E036F => 
"\xF3\xA0\x8D\xB0" => "",                     # U+E0370 => 
"\xF3\xA0\x8D\xB1" => "",                     # U+E0371 => 
"\xF3\xA0\x8D\xB2" => "",                     # U+E0372 => 
"\xF3\xA0\x8D\xB3" => "",                     # U+E0373 => 
"\xF3\xA0\x8D\xB4" => "",                     # U+E0374 => 
"\xF3\xA0\x8D\xB5" => "",                     # U+E0375 => 
"\xF3\xA0\x8D\xB6" => "",                     # U+E0376 => 
"\xF3\xA0\x8D\xB7" => "",                     # U+E0377 => 
"\xF3\xA0\x8D\xB8" => "",                     # U+E0378 => 
"\xF3\xA0\x8D\xB9" => "",                     # U+E0379 => 
"\xF3\xA0\x8D\xBA" => "",                     # U+E037A => 
"\xF3\xA0\x8D\xBB" => "",                     # U+E037B => 
"\xF3\xA0\x8D\xBC" => "",                     # U+E037C => 
"\xF3\xA0\x8D\xBD" => "",                     # U+E037D => 
"\xF3\xA0\x8D\xBE" => "",                     # U+E037E => 
"\xF3\xA0\x8D\xBF" => "",                     # U+E037F => 
"\xF3\xA0\x8E\x80" => "",                     # U+E0380 => 
"\xF3\xA0\x8E\x81" => "",                     # U+E0381 => 
"\xF3\xA0\x8E\x82" => "",                     # U+E0382 => 
"\xF3\xA0\x8E\x83" => "",                     # U+E0383 => 
"\xF3\xA0\x8E\x84" => "",                     # U+E0384 => 
"\xF3\xA0\x8E\x85" => "",                     # U+E0385 => 
"\xF3\xA0\x8E\x86" => "",                     # U+E0386 => 
"\xF3\xA0\x8E\x87" => "",                     # U+E0387 => 
"\xF3\xA0\x8E\x88" => "",                     # U+E0388 => 
"\xF3\xA0\x8E\x89" => "",                     # U+E0389 => 
"\xF3\xA0\x8E\x8A" => "",                     # U+E038A => 
"\xF3\xA0\x8E\x8B" => "",                     # U+E038B => 
"\xF3\xA0\x8E\x8C" => "",                     # U+E038C => 
"\xF3\xA0\x8E\x8D" => "",                     # U+E038D => 
"\xF3\xA0\x8E\x8E" => "",                     # U+E038E => 
"\xF3\xA0\x8E\x8F" => "",                     # U+E038F => 
"\xF3\xA0\x8E\x90" => "",                     # U+E0390 => 
"\xF3\xA0\x8E\x91" => "",                     # U+E0391 => 
"\xF3\xA0\x8E\x92" => "",                     # U+E0392 => 
"\xF3\xA0\x8E\x93" => "",                     # U+E0393 => 
"\xF3\xA0\x8E\x94" => "",                     # U+E0394 => 
"\xF3\xA0\x8E\x95" => "",                     # U+E0395 => 
"\xF3\xA0\x8E\x96" => "",                     # U+E0396 => 
"\xF3\xA0\x8E\x97" => "",                     # U+E0397 => 
"\xF3\xA0\x8E\x98" => "",                     # U+E0398 => 
"\xF3\xA0\x8E\x99" => "",                     # U+E0399 => 
"\xF3\xA0\x8E\x9A" => "",                     # U+E039A => 
"\xF3\xA0\x8E\x9B" => "",                     # U+E039B => 
"\xF3\xA0\x8E\x9C" => "",                     # U+E039C => 
"\xF3\xA0\x8E\x9D" => "",                     # U+E039D => 
"\xF3\xA0\x8E\x9E" => "",                     # U+E039E => 
"\xF3\xA0\x8E\x9F" => "",                     # U+E039F => 
"\xF3\xA0\x8E\xA0" => "",                     # U+E03A0 => 
"\xF3\xA0\x8E\xA1" => "",                     # U+E03A1 => 
"\xF3\xA0\x8E\xA2" => "",                     # U+E03A2 => 
"\xF3\xA0\x8E\xA3" => "",                     # U+E03A3 => 
"\xF3\xA0\x8E\xA4" => "",                     # U+E03A4 => 
"\xF3\xA0\x8E\xA5" => "",                     # U+E03A5 => 
"\xF3\xA0\x8E\xA6" => "",                     # U+E03A6 => 
"\xF3\xA0\x8E\xA7" => "",                     # U+E03A7 => 
"\xF3\xA0\x8E\xA8" => "",                     # U+E03A8 => 
"\xF3\xA0\x8E\xA9" => "",                     # U+E03A9 => 
"\xF3\xA0\x8E\xAA" => "",                     # U+E03AA => 
"\xF3\xA0\x8E\xAB" => "",                     # U+E03AB => 
"\xF3\xA0\x8E\xAC" => "",                     # U+E03AC => 
"\xF3\xA0\x8E\xAD" => "",                     # U+E03AD => 
"\xF3\xA0\x8E\xAE" => "",                     # U+E03AE => 
"\xF3\xA0\x8E\xAF" => "",                     # U+E03AF => 
"\xF3\xA0\x8E\xB0" => "",                     # U+E03B0 => 
"\xF3\xA0\x8E\xB1" => "",                     # U+E03B1 => 
"\xF3\xA0\x8E\xB2" => "",                     # U+E03B2 => 
"\xF3\xA0\x8E\xB3" => "",                     # U+E03B3 => 
"\xF3\xA0\x8E\xB4" => "",                     # U+E03B4 => 
"\xF3\xA0\x8E\xB5" => "",                     # U+E03B5 => 
"\xF3\xA0\x8E\xB6" => "",                     # U+E03B6 => 
"\xF3\xA0\x8E\xB7" => "",                     # U+E03B7 => 
"\xF3\xA0\x8E\xB8" => "",                     # U+E03B8 => 
"\xF3\xA0\x8E\xB9" => "",                     # U+E03B9 => 
"\xF3\xA0\x8E\xBA" => "",                     # U+E03BA => 
"\xF3\xA0\x8E\xBB" => "",                     # U+E03BB => 
"\xF3\xA0\x8E\xBC" => "",                     # U+E03BC => 
"\xF3\xA0\x8E\xBD" => "",                     # U+E03BD => 
"\xF3\xA0\x8E\xBE" => "",                     # U+E03BE => 
"\xF3\xA0\x8E\xBF" => "",                     # U+E03BF => 
"\xF3\xA0\x8F\x80" => "",                     # U+E03C0 => 
"\xF3\xA0\x8F\x81" => "",                     # U+E03C1 => 
"\xF3\xA0\x8F\x82" => "",                     # U+E03C2 => 
"\xF3\xA0\x8F\x83" => "",                     # U+E03C3 => 
"\xF3\xA0\x8F\x84" => "",                     # U+E03C4 => 
"\xF3\xA0\x8F\x85" => "",                     # U+E03C5 => 
"\xF3\xA0\x8F\x86" => "",                     # U+E03C6 => 
"\xF3\xA0\x8F\x87" => "",                     # U+E03C7 => 
"\xF3\xA0\x8F\x88" => "",                     # U+E03C8 => 
"\xF3\xA0\x8F\x89" => "",                     # U+E03C9 => 
"\xF3\xA0\x8F\x8A" => "",                     # U+E03CA => 
"\xF3\xA0\x8F\x8B" => "",                     # U+E03CB => 
"\xF3\xA0\x8F\x8C" => "",                     # U+E03CC => 
"\xF3\xA0\x8F\x8D" => "",                     # U+E03CD => 
"\xF3\xA0\x8F\x8E" => "",                     # U+E03CE => 
"\xF3\xA0\x8F\x8F" => "",                     # U+E03CF => 
"\xF3\xA0\x8F\x90" => "",                     # U+E03D0 => 
"\xF3\xA0\x8F\x91" => "",                     # U+E03D1 => 
"\xF3\xA0\x8F\x92" => "",                     # U+E03D2 => 
"\xF3\xA0\x8F\x93" => "",                     # U+E03D3 => 
"\xF3\xA0\x8F\x94" => "",                     # U+E03D4 => 
"\xF3\xA0\x8F\x95" => "",                     # U+E03D5 => 
"\xF3\xA0\x8F\x96" => "",                     # U+E03D6 => 
"\xF3\xA0\x8F\x97" => "",                     # U+E03D7 => 
"\xF3\xA0\x8F\x98" => "",                     # U+E03D8 => 
"\xF3\xA0\x8F\x99" => "",                     # U+E03D9 => 
"\xF3\xA0\x8F\x9A" => "",                     # U+E03DA => 
"\xF3\xA0\x8F\x9B" => "",                     # U+E03DB => 
"\xF3\xA0\x8F\x9C" => "",                     # U+E03DC => 
"\xF3\xA0\x8F\x9D" => "",                     # U+E03DD => 
"\xF3\xA0\x8F\x9E" => "",                     # U+E03DE => 
"\xF3\xA0\x8F\x9F" => "",                     # U+E03DF => 
"\xF3\xA0\x8F\xA0" => "",                     # U+E03E0 => 
"\xF3\xA0\x8F\xA1" => "",                     # U+E03E1 => 
"\xF3\xA0\x8F\xA2" => "",                     # U+E03E2 => 
"\xF3\xA0\x8F\xA3" => "",                     # U+E03E3 => 
"\xF3\xA0\x8F\xA4" => "",                     # U+E03E4 => 
"\xF3\xA0\x8F\xA5" => "",                     # U+E03E5 => 
"\xF3\xA0\x8F\xA6" => "",                     # U+E03E6 => 
"\xF3\xA0\x8F\xA7" => "",                     # U+E03E7 => 
"\xF3\xA0\x8F\xA8" => "",                     # U+E03E8 => 
"\xF3\xA0\x8F\xA9" => "",                     # U+E03E9 => 
"\xF3\xA0\x8F\xAA" => "",                     # U+E03EA => 
"\xF3\xA0\x8F\xAB" => "",                     # U+E03EB => 
"\xF3\xA0\x8F\xAC" => "",                     # U+E03EC => 
"\xF3\xA0\x8F\xAD" => "",                     # U+E03ED => 
"\xF3\xA0\x8F\xAE" => "",                     # U+E03EE => 
"\xF3\xA0\x8F\xAF" => "",                     # U+E03EF => 
"\xF3\xA0\x8F\xB0" => "",                     # U+E03F0 => 
"\xF3\xA0\x8F\xB1" => "",                     # U+E03F1 => 
"\xF3\xA0\x8F\xB2" => "",                     # U+E03F2 => 
"\xF3\xA0\x8F\xB3" => "",                     # U+E03F3 => 
"\xF3\xA0\x8F\xB4" => "",                     # U+E03F4 => 
"\xF3\xA0\x8F\xB5" => "",                     # U+E03F5 => 
"\xF3\xA0\x8F\xB6" => "",                     # U+E03F6 => 
"\xF3\xA0\x8F\xB7" => "",                     # U+E03F7 => 
"\xF3\xA0\x8F\xB8" => "",                     # U+E03F8 => 
"\xF3\xA0\x8F\xB9" => "",                     # U+E03F9 => 
"\xF3\xA0\x8F\xBA" => "",                     # U+E03FA => 
"\xF3\xA0\x8F\xBB" => "",                     # U+E03FB => 
"\xF3\xA0\x8F\xBC" => "",                     # U+E03FC => 
"\xF3\xA0\x8F\xBD" => "",                     # U+E03FD => 
"\xF3\xA0\x8F\xBE" => "",                     # U+E03FE => 
"\xF3\xA0\x8F\xBF" => "",                     # U+E03FF => 
"\xF3\xA0\x90\x80" => "",                     # U+E0400 => 
"\xF3\xA0\x90\x81" => "",                     # U+E0401 => 
"\xF3\xA0\x90\x82" => "",                     # U+E0402 => 
"\xF3\xA0\x90\x83" => "",                     # U+E0403 => 
"\xF3\xA0\x90\x84" => "",                     # U+E0404 => 
"\xF3\xA0\x90\x85" => "",                     # U+E0405 => 
"\xF3\xA0\x90\x86" => "",                     # U+E0406 => 
"\xF3\xA0\x90\x87" => "",                     # U+E0407 => 
"\xF3\xA0\x90\x88" => "",                     # U+E0408 => 
"\xF3\xA0\x90\x89" => "",                     # U+E0409 => 
"\xF3\xA0\x90\x8A" => "",                     # U+E040A => 
"\xF3\xA0\x90\x8B" => "",                     # U+E040B => 
"\xF3\xA0\x90\x8C" => "",                     # U+E040C => 
"\xF3\xA0\x90\x8D" => "",                     # U+E040D => 
"\xF3\xA0\x90\x8E" => "",                     # U+E040E => 
"\xF3\xA0\x90\x8F" => "",                     # U+E040F => 
"\xF3\xA0\x90\x90" => "",                     # U+E0410 => 
"\xF3\xA0\x90\x91" => "",                     # U+E0411 => 
"\xF3\xA0\x90\x92" => "",                     # U+E0412 => 
"\xF3\xA0\x90\x93" => "",                     # U+E0413 => 
"\xF3\xA0\x90\x94" => "",                     # U+E0414 => 
"\xF3\xA0\x90\x95" => "",                     # U+E0415 => 
"\xF3\xA0\x90\x96" => "",                     # U+E0416 => 
"\xF3\xA0\x90\x97" => "",                     # U+E0417 => 
"\xF3\xA0\x90\x98" => "",                     # U+E0418 => 
"\xF3\xA0\x90\x99" => "",                     # U+E0419 => 
"\xF3\xA0\x90\x9A" => "",                     # U+E041A => 
"\xF3\xA0\x90\x9B" => "",                     # U+E041B => 
"\xF3\xA0\x90\x9C" => "",                     # U+E041C => 
"\xF3\xA0\x90\x9D" => "",                     # U+E041D => 
"\xF3\xA0\x90\x9E" => "",                     # U+E041E => 
"\xF3\xA0\x90\x9F" => "",                     # U+E041F => 
"\xF3\xA0\x90\xA0" => "",                     # U+E0420 => 
"\xF3\xA0\x90\xA1" => "",                     # U+E0421 => 
"\xF3\xA0\x90\xA2" => "",                     # U+E0422 => 
"\xF3\xA0\x90\xA3" => "",                     # U+E0423 => 
"\xF3\xA0\x90\xA4" => "",                     # U+E0424 => 
"\xF3\xA0\x90\xA5" => "",                     # U+E0425 => 
"\xF3\xA0\x90\xA6" => "",                     # U+E0426 => 
"\xF3\xA0\x90\xA7" => "",                     # U+E0427 => 
"\xF3\xA0\x90\xA8" => "",                     # U+E0428 => 
"\xF3\xA0\x90\xA9" => "",                     # U+E0429 => 
"\xF3\xA0\x90\xAA" => "",                     # U+E042A => 
"\xF3\xA0\x90\xAB" => "",                     # U+E042B => 
"\xF3\xA0\x90\xAC" => "",                     # U+E042C => 
"\xF3\xA0\x90\xAD" => "",                     # U+E042D => 
"\xF3\xA0\x90\xAE" => "",                     # U+E042E => 
"\xF3\xA0\x90\xAF" => "",                     # U+E042F => 
"\xF3\xA0\x90\xB0" => "",                     # U+E0430 => 
"\xF3\xA0\x90\xB1" => "",                     # U+E0431 => 
"\xF3\xA0\x90\xB2" => "",                     # U+E0432 => 
"\xF3\xA0\x90\xB3" => "",                     # U+E0433 => 
"\xF3\xA0\x90\xB4" => "",                     # U+E0434 => 
"\xF3\xA0\x90\xB5" => "",                     # U+E0435 => 
"\xF3\xA0\x90\xB6" => "",                     # U+E0436 => 
"\xF3\xA0\x90\xB7" => "",                     # U+E0437 => 
"\xF3\xA0\x90\xB8" => "",                     # U+E0438 => 
"\xF3\xA0\x90\xB9" => "",                     # U+E0439 => 
"\xF3\xA0\x90\xBA" => "",                     # U+E043A => 
"\xF3\xA0\x90\xBB" => "",                     # U+E043B => 
"\xF3\xA0\x90\xBC" => "",                     # U+E043C => 
"\xF3\xA0\x90\xBD" => "",                     # U+E043D => 
"\xF3\xA0\x90\xBE" => "",                     # U+E043E => 
"\xF3\xA0\x90\xBF" => "",                     # U+E043F => 
"\xF3\xA0\x91\x80" => "",                     # U+E0440 => 
"\xF3\xA0\x91\x81" => "",                     # U+E0441 => 
"\xF3\xA0\x91\x82" => "",                     # U+E0442 => 
"\xF3\xA0\x91\x83" => "",                     # U+E0443 => 
"\xF3\xA0\x91\x84" => "",                     # U+E0444 => 
"\xF3\xA0\x91\x85" => "",                     # U+E0445 => 
"\xF3\xA0\x91\x86" => "",                     # U+E0446 => 
"\xF3\xA0\x91\x87" => "",                     # U+E0447 => 
"\xF3\xA0\x91\x88" => "",                     # U+E0448 => 
"\xF3\xA0\x91\x89" => "",                     # U+E0449 => 
"\xF3\xA0\x91\x8A" => "",                     # U+E044A => 
"\xF3\xA0\x91\x8B" => "",                     # U+E044B => 
"\xF3\xA0\x91\x8C" => "",                     # U+E044C => 
"\xF3\xA0\x91\x8D" => "",                     # U+E044D => 
"\xF3\xA0\x91\x8E" => "",                     # U+E044E => 
"\xF3\xA0\x91\x8F" => "",                     # U+E044F => 
"\xF3\xA0\x91\x90" => "",                     # U+E0450 => 
"\xF3\xA0\x91\x91" => "",                     # U+E0451 => 
"\xF3\xA0\x91\x92" => "",                     # U+E0452 => 
"\xF3\xA0\x91\x93" => "",                     # U+E0453 => 
"\xF3\xA0\x91\x94" => "",                     # U+E0454 => 
"\xF3\xA0\x91\x95" => "",                     # U+E0455 => 
"\xF3\xA0\x91\x96" => "",                     # U+E0456 => 
"\xF3\xA0\x91\x97" => "",                     # U+E0457 => 
"\xF3\xA0\x91\x98" => "",                     # U+E0458 => 
"\xF3\xA0\x91\x99" => "",                     # U+E0459 => 
"\xF3\xA0\x91\x9A" => "",                     # U+E045A => 
"\xF3\xA0\x91\x9B" => "",                     # U+E045B => 
"\xF3\xA0\x91\x9C" => "",                     # U+E045C => 
"\xF3\xA0\x91\x9D" => "",                     # U+E045D => 
"\xF3\xA0\x91\x9E" => "",                     # U+E045E => 
"\xF3\xA0\x91\x9F" => "",                     # U+E045F => 
"\xF3\xA0\x91\xA0" => "",                     # U+E0460 => 
"\xF3\xA0\x91\xA1" => "",                     # U+E0461 => 
"\xF3\xA0\x91\xA2" => "",                     # U+E0462 => 
"\xF3\xA0\x91\xA3" => "",                     # U+E0463 => 
"\xF3\xA0\x91\xA4" => "",                     # U+E0464 => 
"\xF3\xA0\x91\xA5" => "",                     # U+E0465 => 
"\xF3\xA0\x91\xA6" => "",                     # U+E0466 => 
"\xF3\xA0\x91\xA7" => "",                     # U+E0467 => 
"\xF3\xA0\x91\xA8" => "",                     # U+E0468 => 
"\xF3\xA0\x91\xA9" => "",                     # U+E0469 => 
"\xF3\xA0\x91\xAA" => "",                     # U+E046A => 
"\xF3\xA0\x91\xAB" => "",                     # U+E046B => 
"\xF3\xA0\x91\xAC" => "",                     # U+E046C => 
"\xF3\xA0\x91\xAD" => "",                     # U+E046D => 
"\xF3\xA0\x91\xAE" => "",                     # U+E046E => 
"\xF3\xA0\x91\xAF" => "",                     # U+E046F => 
"\xF3\xA0\x91\xB0" => "",                     # U+E0470 => 
"\xF3\xA0\x91\xB1" => "",                     # U+E0471 => 
"\xF3\xA0\x91\xB2" => "",                     # U+E0472 => 
"\xF3\xA0\x91\xB3" => "",                     # U+E0473 => 
"\xF3\xA0\x91\xB4" => "",                     # U+E0474 => 
"\xF3\xA0\x91\xB5" => "",                     # U+E0475 => 
"\xF3\xA0\x91\xB6" => "",                     # U+E0476 => 
"\xF3\xA0\x91\xB7" => "",                     # U+E0477 => 
"\xF3\xA0\x91\xB8" => "",                     # U+E0478 => 
"\xF3\xA0\x91\xB9" => "",                     # U+E0479 => 
"\xF3\xA0\x91\xBA" => "",                     # U+E047A => 
"\xF3\xA0\x91\xBB" => "",                     # U+E047B => 
"\xF3\xA0\x91\xBC" => "",                     # U+E047C => 
"\xF3\xA0\x91\xBD" => "",                     # U+E047D => 
"\xF3\xA0\x91\xBE" => "",                     # U+E047E => 
"\xF3\xA0\x91\xBF" => "",                     # U+E047F => 
"\xF3\xA0\x92\x80" => "",                     # U+E0480 => 
"\xF3\xA0\x92\x81" => "",                     # U+E0481 => 
"\xF3\xA0\x92\x82" => "",                     # U+E0482 => 
"\xF3\xA0\x92\x83" => "",                     # U+E0483 => 
"\xF3\xA0\x92\x84" => "",                     # U+E0484 => 
"\xF3\xA0\x92\x85" => "",                     # U+E0485 => 
"\xF3\xA0\x92\x86" => "",                     # U+E0486 => 
"\xF3\xA0\x92\x87" => "",                     # U+E0487 => 
"\xF3\xA0\x92\x88" => "",                     # U+E0488 => 
"\xF3\xA0\x92\x89" => "",                     # U+E0489 => 
"\xF3\xA0\x92\x8A" => "",                     # U+E048A => 
"\xF3\xA0\x92\x8B" => "",                     # U+E048B => 
"\xF3\xA0\x92\x8C" => "",                     # U+E048C => 
"\xF3\xA0\x92\x8D" => "",                     # U+E048D => 
"\xF3\xA0\x92\x8E" => "",                     # U+E048E => 
"\xF3\xA0\x92\x8F" => "",                     # U+E048F => 
"\xF3\xA0\x92\x90" => "",                     # U+E0490 => 
"\xF3\xA0\x92\x91" => "",                     # U+E0491 => 
"\xF3\xA0\x92\x92" => "",                     # U+E0492 => 
"\xF3\xA0\x92\x93" => "",                     # U+E0493 => 
"\xF3\xA0\x92\x94" => "",                     # U+E0494 => 
"\xF3\xA0\x92\x95" => "",                     # U+E0495 => 
"\xF3\xA0\x92\x96" => "",                     # U+E0496 => 
"\xF3\xA0\x92\x97" => "",                     # U+E0497 => 
"\xF3\xA0\x92\x98" => "",                     # U+E0498 => 
"\xF3\xA0\x92\x99" => "",                     # U+E0499 => 
"\xF3\xA0\x92\x9A" => "",                     # U+E049A => 
"\xF3\xA0\x92\x9B" => "",                     # U+E049B => 
"\xF3\xA0\x92\x9C" => "",                     # U+E049C => 
"\xF3\xA0\x92\x9D" => "",                     # U+E049D => 
"\xF3\xA0\x92\x9E" => "",                     # U+E049E => 
"\xF3\xA0\x92\x9F" => "",                     # U+E049F => 
"\xF3\xA0\x92\xA0" => "",                     # U+E04A0 => 
"\xF3\xA0\x92\xA1" => "",                     # U+E04A1 => 
"\xF3\xA0\x92\xA2" => "",                     # U+E04A2 => 
"\xF3\xA0\x92\xA3" => "",                     # U+E04A3 => 
"\xF3\xA0\x92\xA4" => "",                     # U+E04A4 => 
"\xF3\xA0\x92\xA5" => "",                     # U+E04A5 => 
"\xF3\xA0\x92\xA6" => "",                     # U+E04A6 => 
"\xF3\xA0\x92\xA7" => "",                     # U+E04A7 => 
"\xF3\xA0\x92\xA8" => "",                     # U+E04A8 => 
"\xF3\xA0\x92\xA9" => "",                     # U+E04A9 => 
"\xF3\xA0\x92\xAA" => "",                     # U+E04AA => 
"\xF3\xA0\x92\xAB" => "",                     # U+E04AB => 
"\xF3\xA0\x92\xAC" => "",                     # U+E04AC => 
"\xF3\xA0\x92\xAD" => "",                     # U+E04AD => 
"\xF3\xA0\x92\xAE" => "",                     # U+E04AE => 
"\xF3\xA0\x92\xAF" => "",                     # U+E04AF => 
"\xF3\xA0\x92\xB0" => "",                     # U+E04B0 => 
"\xF3\xA0\x92\xB1" => "",                     # U+E04B1 => 
"\xF3\xA0\x92\xB2" => "",                     # U+E04B2 => 
"\xF3\xA0\x92\xB3" => "",                     # U+E04B3 => 
"\xF3\xA0\x92\xB4" => "",                     # U+E04B4 => 
"\xF3\xA0\x92\xB5" => "",                     # U+E04B5 => 
"\xF3\xA0\x92\xB6" => "",                     # U+E04B6 => 
"\xF3\xA0\x92\xB7" => "",                     # U+E04B7 => 
"\xF3\xA0\x92\xB8" => "",                     # U+E04B8 => 
"\xF3\xA0\x92\xB9" => "",                     # U+E04B9 => 
"\xF3\xA0\x92\xBA" => "",                     # U+E04BA => 
"\xF3\xA0\x92\xBB" => "",                     # U+E04BB => 
"\xF3\xA0\x92\xBC" => "",                     # U+E04BC => 
"\xF3\xA0\x92\xBD" => "",                     # U+E04BD => 
"\xF3\xA0\x92\xBE" => "",                     # U+E04BE => 
"\xF3\xA0\x92\xBF" => "",                     # U+E04BF => 
"\xF3\xA0\x93\x80" => "",                     # U+E04C0 => 
"\xF3\xA0\x93\x81" => "",                     # U+E04C1 => 
"\xF3\xA0\x93\x82" => "",                     # U+E04C2 => 
"\xF3\xA0\x93\x83" => "",                     # U+E04C3 => 
"\xF3\xA0\x93\x84" => "",                     # U+E04C4 => 
"\xF3\xA0\x93\x85" => "",                     # U+E04C5 => 
"\xF3\xA0\x93\x86" => "",                     # U+E04C6 => 
"\xF3\xA0\x93\x87" => "",                     # U+E04C7 => 
"\xF3\xA0\x93\x88" => "",                     # U+E04C8 => 
"\xF3\xA0\x93\x89" => "",                     # U+E04C9 => 
"\xF3\xA0\x93\x8A" => "",                     # U+E04CA => 
"\xF3\xA0\x93\x8B" => "",                     # U+E04CB => 
"\xF3\xA0\x93\x8C" => "",                     # U+E04CC => 
"\xF3\xA0\x93\x8D" => "",                     # U+E04CD => 
"\xF3\xA0\x93\x8E" => "",                     # U+E04CE => 
"\xF3\xA0\x93\x8F" => "",                     # U+E04CF => 
"\xF3\xA0\x93\x90" => "",                     # U+E04D0 => 
"\xF3\xA0\x93\x91" => "",                     # U+E04D1 => 
"\xF3\xA0\x93\x92" => "",                     # U+E04D2 => 
"\xF3\xA0\x93\x93" => "",                     # U+E04D3 => 
"\xF3\xA0\x93\x94" => "",                     # U+E04D4 => 
"\xF3\xA0\x93\x95" => "",                     # U+E04D5 => 
"\xF3\xA0\x93\x96" => "",                     # U+E04D6 => 
"\xF3\xA0\x93\x97" => "",                     # U+E04D7 => 
"\xF3\xA0\x93\x98" => "",                     # U+E04D8 => 
"\xF3\xA0\x93\x99" => "",                     # U+E04D9 => 
"\xF3\xA0\x93\x9A" => "",                     # U+E04DA => 
"\xF3\xA0\x93\x9B" => "",                     # U+E04DB => 
"\xF3\xA0\x93\x9C" => "",                     # U+E04DC => 
"\xF3\xA0\x93\x9D" => "",                     # U+E04DD => 
"\xF3\xA0\x93\x9E" => "",                     # U+E04DE => 
"\xF3\xA0\x93\x9F" => "",                     # U+E04DF => 
"\xF3\xA0\x93\xA0" => "",                     # U+E04E0 => 
"\xF3\xA0\x93\xA1" => "",                     # U+E04E1 => 
"\xF3\xA0\x93\xA2" => "",                     # U+E04E2 => 
"\xF3\xA0\x93\xA3" => "",                     # U+E04E3 => 
"\xF3\xA0\x93\xA4" => "",                     # U+E04E4 => 
"\xF3\xA0\x93\xA5" => "",                     # U+E04E5 => 
"\xF3\xA0\x93\xA6" => "",                     # U+E04E6 => 
"\xF3\xA0\x93\xA7" => "",                     # U+E04E7 => 
"\xF3\xA0\x93\xA8" => "",                     # U+E04E8 => 
"\xF3\xA0\x93\xA9" => "",                     # U+E04E9 => 
"\xF3\xA0\x93\xAA" => "",                     # U+E04EA => 
"\xF3\xA0\x93\xAB" => "",                     # U+E04EB => 
"\xF3\xA0\x93\xAC" => "",                     # U+E04EC => 
"\xF3\xA0\x93\xAD" => "",                     # U+E04ED => 
"\xF3\xA0\x93\xAE" => "",                     # U+E04EE => 
"\xF3\xA0\x93\xAF" => "",                     # U+E04EF => 
"\xF3\xA0\x93\xB0" => "",                     # U+E04F0 => 
"\xF3\xA0\x93\xB1" => "",                     # U+E04F1 => 
"\xF3\xA0\x93\xB2" => "",                     # U+E04F2 => 
"\xF3\xA0\x93\xB3" => "",                     # U+E04F3 => 
"\xF3\xA0\x93\xB4" => "",                     # U+E04F4 => 
"\xF3\xA0\x93\xB5" => "",                     # U+E04F5 => 
"\xF3\xA0\x93\xB6" => "",                     # U+E04F6 => 
"\xF3\xA0\x93\xB7" => "",                     # U+E04F7 => 
"\xF3\xA0\x93\xB8" => "",                     # U+E04F8 => 
"\xF3\xA0\x93\xB9" => "",                     # U+E04F9 => 
"\xF3\xA0\x93\xBA" => "",                     # U+E04FA => 
"\xF3\xA0\x93\xBB" => "",                     # U+E04FB => 
"\xF3\xA0\x93\xBC" => "",                     # U+E04FC => 
"\xF3\xA0\x93\xBD" => "",                     # U+E04FD => 
"\xF3\xA0\x93\xBE" => "",                     # U+E04FE => 
"\xF3\xA0\x93\xBF" => "",                     # U+E04FF => 
"\xF3\xA0\x94\x80" => "",                     # U+E0500 => 
"\xF3\xA0\x94\x81" => "",                     # U+E0501 => 
"\xF3\xA0\x94\x82" => "",                     # U+E0502 => 
"\xF3\xA0\x94\x83" => "",                     # U+E0503 => 
"\xF3\xA0\x94\x84" => "",                     # U+E0504 => 
"\xF3\xA0\x94\x85" => "",                     # U+E0505 => 
"\xF3\xA0\x94\x86" => "",                     # U+E0506 => 
"\xF3\xA0\x94\x87" => "",                     # U+E0507 => 
"\xF3\xA0\x94\x88" => "",                     # U+E0508 => 
"\xF3\xA0\x94\x89" => "",                     # U+E0509 => 
"\xF3\xA0\x94\x8A" => "",                     # U+E050A => 
"\xF3\xA0\x94\x8B" => "",                     # U+E050B => 
"\xF3\xA0\x94\x8C" => "",                     # U+E050C => 
"\xF3\xA0\x94\x8D" => "",                     # U+E050D => 
"\xF3\xA0\x94\x8E" => "",                     # U+E050E => 
"\xF3\xA0\x94\x8F" => "",                     # U+E050F => 
"\xF3\xA0\x94\x90" => "",                     # U+E0510 => 
"\xF3\xA0\x94\x91" => "",                     # U+E0511 => 
"\xF3\xA0\x94\x92" => "",                     # U+E0512 => 
"\xF3\xA0\x94\x93" => "",                     # U+E0513 => 
"\xF3\xA0\x94\x94" => "",                     # U+E0514 => 
"\xF3\xA0\x94\x95" => "",                     # U+E0515 => 
"\xF3\xA0\x94\x96" => "",                     # U+E0516 => 
"\xF3\xA0\x94\x97" => "",                     # U+E0517 => 
"\xF3\xA0\x94\x98" => "",                     # U+E0518 => 
"\xF3\xA0\x94\x99" => "",                     # U+E0519 => 
"\xF3\xA0\x94\x9A" => "",                     # U+E051A => 
"\xF3\xA0\x94\x9B" => "",                     # U+E051B => 
"\xF3\xA0\x94\x9C" => "",                     # U+E051C => 
"\xF3\xA0\x94\x9D" => "",                     # U+E051D => 
"\xF3\xA0\x94\x9E" => "",                     # U+E051E => 
"\xF3\xA0\x94\x9F" => "",                     # U+E051F => 
"\xF3\xA0\x94\xA0" => "",                     # U+E0520 => 
"\xF3\xA0\x94\xA1" => "",                     # U+E0521 => 
"\xF3\xA0\x94\xA2" => "",                     # U+E0522 => 
"\xF3\xA0\x94\xA3" => "",                     # U+E0523 => 
"\xF3\xA0\x94\xA4" => "",                     # U+E0524 => 
"\xF3\xA0\x94\xA5" => "",                     # U+E0525 => 
"\xF3\xA0\x94\xA6" => "",                     # U+E0526 => 
"\xF3\xA0\x94\xA7" => "",                     # U+E0527 => 
"\xF3\xA0\x94\xA8" => "",                     # U+E0528 => 
"\xF3\xA0\x94\xA9" => "",                     # U+E0529 => 
"\xF3\xA0\x94\xAA" => "",                     # U+E052A => 
"\xF3\xA0\x94\xAB" => "",                     # U+E052B => 
"\xF3\xA0\x94\xAC" => "",                     # U+E052C => 
"\xF3\xA0\x94\xAD" => "",                     # U+E052D => 
"\xF3\xA0\x94\xAE" => "",                     # U+E052E => 
"\xF3\xA0\x94\xAF" => "",                     # U+E052F => 
"\xF3\xA0\x94\xB0" => "",                     # U+E0530 => 
"\xF3\xA0\x94\xB1" => "",                     # U+E0531 => 
"\xF3\xA0\x94\xB2" => "",                     # U+E0532 => 
"\xF3\xA0\x94\xB3" => "",                     # U+E0533 => 
"\xF3\xA0\x94\xB4" => "",                     # U+E0534 => 
"\xF3\xA0\x94\xB5" => "",                     # U+E0535 => 
"\xF3\xA0\x94\xB6" => "",                     # U+E0536 => 
"\xF3\xA0\x94\xB7" => "",                     # U+E0537 => 
"\xF3\xA0\x94\xB8" => "",                     # U+E0538 => 
"\xF3\xA0\x94\xB9" => "",                     # U+E0539 => 
"\xF3\xA0\x94\xBA" => "",                     # U+E053A => 
"\xF3\xA0\x94\xBB" => "",                     # U+E053B => 
"\xF3\xA0\x94\xBC" => "",                     # U+E053C => 
"\xF3\xA0\x94\xBD" => "",                     # U+E053D => 
"\xF3\xA0\x94\xBE" => "",                     # U+E053E => 
"\xF3\xA0\x94\xBF" => "",                     # U+E053F => 
"\xF3\xA0\x95\x80" => "",                     # U+E0540 => 
"\xF3\xA0\x95\x81" => "",                     # U+E0541 => 
"\xF3\xA0\x95\x82" => "",                     # U+E0542 => 
"\xF3\xA0\x95\x83" => "",                     # U+E0543 => 
"\xF3\xA0\x95\x84" => "",                     # U+E0544 => 
"\xF3\xA0\x95\x85" => "",                     # U+E0545 => 
"\xF3\xA0\x95\x86" => "",                     # U+E0546 => 
"\xF3\xA0\x95\x87" => "",                     # U+E0547 => 
"\xF3\xA0\x95\x88" => "",                     # U+E0548 => 
"\xF3\xA0\x95\x89" => "",                     # U+E0549 => 
"\xF3\xA0\x95\x8A" => "",                     # U+E054A => 
"\xF3\xA0\x95\x8B" => "",                     # U+E054B => 
"\xF3\xA0\x95\x8C" => "",                     # U+E054C => 
"\xF3\xA0\x95\x8D" => "",                     # U+E054D => 
"\xF3\xA0\x95\x8E" => "",                     # U+E054E => 
"\xF3\xA0\x95\x8F" => "",                     # U+E054F => 
"\xF3\xA0\x95\x90" => "",                     # U+E0550 => 
"\xF3\xA0\x95\x91" => "",                     # U+E0551 => 
"\xF3\xA0\x95\x92" => "",                     # U+E0552 => 
"\xF3\xA0\x95\x93" => "",                     # U+E0553 => 
"\xF3\xA0\x95\x94" => "",                     # U+E0554 => 
"\xF3\xA0\x95\x95" => "",                     # U+E0555 => 
"\xF3\xA0\x95\x96" => "",                     # U+E0556 => 
"\xF3\xA0\x95\x97" => "",                     # U+E0557 => 
"\xF3\xA0\x95\x98" => "",                     # U+E0558 => 
"\xF3\xA0\x95\x99" => "",                     # U+E0559 => 
"\xF3\xA0\x95\x9A" => "",                     # U+E055A => 
"\xF3\xA0\x95\x9B" => "",                     # U+E055B => 
"\xF3\xA0\x95\x9C" => "",                     # U+E055C => 
"\xF3\xA0\x95\x9D" => "",                     # U+E055D => 
"\xF3\xA0\x95\x9E" => "",                     # U+E055E => 
"\xF3\xA0\x95\x9F" => "",                     # U+E055F => 
"\xF3\xA0\x95\xA0" => "",                     # U+E0560 => 
"\xF3\xA0\x95\xA1" => "",                     # U+E0561 => 
"\xF3\xA0\x95\xA2" => "",                     # U+E0562 => 
"\xF3\xA0\x95\xA3" => "",                     # U+E0563 => 
"\xF3\xA0\x95\xA4" => "",                     # U+E0564 => 
"\xF3\xA0\x95\xA5" => "",                     # U+E0565 => 
"\xF3\xA0\x95\xA6" => "",                     # U+E0566 => 
"\xF3\xA0\x95\xA7" => "",                     # U+E0567 => 
"\xF3\xA0\x95\xA8" => "",                     # U+E0568 => 
"\xF3\xA0\x95\xA9" => "",                     # U+E0569 => 
"\xF3\xA0\x95\xAA" => "",                     # U+E056A => 
"\xF3\xA0\x95\xAB" => "",                     # U+E056B => 
"\xF3\xA0\x95\xAC" => "",                     # U+E056C => 
"\xF3\xA0\x95\xAD" => "",                     # U+E056D => 
"\xF3\xA0\x95\xAE" => "",                     # U+E056E => 
"\xF3\xA0\x95\xAF" => "",                     # U+E056F => 
"\xF3\xA0\x95\xB0" => "",                     # U+E0570 => 
"\xF3\xA0\x95\xB1" => "",                     # U+E0571 => 
"\xF3\xA0\x95\xB2" => "",                     # U+E0572 => 
"\xF3\xA0\x95\xB3" => "",                     # U+E0573 => 
"\xF3\xA0\x95\xB4" => "",                     # U+E0574 => 
"\xF3\xA0\x95\xB5" => "",                     # U+E0575 => 
"\xF3\xA0\x95\xB6" => "",                     # U+E0576 => 
"\xF3\xA0\x95\xB7" => "",                     # U+E0577 => 
"\xF3\xA0\x95\xB8" => "",                     # U+E0578 => 
"\xF3\xA0\x95\xB9" => "",                     # U+E0579 => 
"\xF3\xA0\x95\xBA" => "",                     # U+E057A => 
"\xF3\xA0\x95\xBB" => "",                     # U+E057B => 
"\xF3\xA0\x95\xBC" => "",                     # U+E057C => 
"\xF3\xA0\x95\xBD" => "",                     # U+E057D => 
"\xF3\xA0\x95\xBE" => "",                     # U+E057E => 
"\xF3\xA0\x95\xBF" => "",                     # U+E057F => 
"\xF3\xA0\x96\x80" => "",                     # U+E0580 => 
"\xF3\xA0\x96\x81" => "",                     # U+E0581 => 
"\xF3\xA0\x96\x82" => "",                     # U+E0582 => 
"\xF3\xA0\x96\x83" => "",                     # U+E0583 => 
"\xF3\xA0\x96\x84" => "",                     # U+E0584 => 
"\xF3\xA0\x96\x85" => "",                     # U+E0585 => 
"\xF3\xA0\x96\x86" => "",                     # U+E0586 => 
"\xF3\xA0\x96\x87" => "",                     # U+E0587 => 
"\xF3\xA0\x96\x88" => "",                     # U+E0588 => 
"\xF3\xA0\x96\x89" => "",                     # U+E0589 => 
"\xF3\xA0\x96\x8A" => "",                     # U+E058A => 
"\xF3\xA0\x96\x8B" => "",                     # U+E058B => 
"\xF3\xA0\x96\x8C" => "",                     # U+E058C => 
"\xF3\xA0\x96\x8D" => "",                     # U+E058D => 
"\xF3\xA0\x96\x8E" => "",                     # U+E058E => 
"\xF3\xA0\x96\x8F" => "",                     # U+E058F => 
"\xF3\xA0\x96\x90" => "",                     # U+E0590 => 
"\xF3\xA0\x96\x91" => "",                     # U+E0591 => 
"\xF3\xA0\x96\x92" => "",                     # U+E0592 => 
"\xF3\xA0\x96\x93" => "",                     # U+E0593 => 
"\xF3\xA0\x96\x94" => "",                     # U+E0594 => 
"\xF3\xA0\x96\x95" => "",                     # U+E0595 => 
"\xF3\xA0\x96\x96" => "",                     # U+E0596 => 
"\xF3\xA0\x96\x97" => "",                     # U+E0597 => 
"\xF3\xA0\x96\x98" => "",                     # U+E0598 => 
"\xF3\xA0\x96\x99" => "",                     # U+E0599 => 
"\xF3\xA0\x96\x9A" => "",                     # U+E059A => 
"\xF3\xA0\x96\x9B" => "",                     # U+E059B => 
"\xF3\xA0\x96\x9C" => "",                     # U+E059C => 
"\xF3\xA0\x96\x9D" => "",                     # U+E059D => 
"\xF3\xA0\x96\x9E" => "",                     # U+E059E => 
"\xF3\xA0\x96\x9F" => "",                     # U+E059F => 
"\xF3\xA0\x96\xA0" => "",                     # U+E05A0 => 
"\xF3\xA0\x96\xA1" => "",                     # U+E05A1 => 
"\xF3\xA0\x96\xA2" => "",                     # U+E05A2 => 
"\xF3\xA0\x96\xA3" => "",                     # U+E05A3 => 
"\xF3\xA0\x96\xA4" => "",                     # U+E05A4 => 
"\xF3\xA0\x96\xA5" => "",                     # U+E05A5 => 
"\xF3\xA0\x96\xA6" => "",                     # U+E05A6 => 
"\xF3\xA0\x96\xA7" => "",                     # U+E05A7 => 
"\xF3\xA0\x96\xA8" => "",                     # U+E05A8 => 
"\xF3\xA0\x96\xA9" => "",                     # U+E05A9 => 
"\xF3\xA0\x96\xAA" => "",                     # U+E05AA => 
"\xF3\xA0\x96\xAB" => "",                     # U+E05AB => 
"\xF3\xA0\x96\xAC" => "",                     # U+E05AC => 
"\xF3\xA0\x96\xAD" => "",                     # U+E05AD => 
"\xF3\xA0\x96\xAE" => "",                     # U+E05AE => 
"\xF3\xA0\x96\xAF" => "",                     # U+E05AF => 
"\xF3\xA0\x96\xB0" => "",                     # U+E05B0 => 
"\xF3\xA0\x96\xB1" => "",                     # U+E05B1 => 
"\xF3\xA0\x96\xB2" => "",                     # U+E05B2 => 
"\xF3\xA0\x96\xB3" => "",                     # U+E05B3 => 
"\xF3\xA0\x96\xB4" => "",                     # U+E05B4 => 
"\xF3\xA0\x96\xB5" => "",                     # U+E05B5 => 
"\xF3\xA0\x96\xB6" => "",                     # U+E05B6 => 
"\xF3\xA0\x96\xB7" => "",                     # U+E05B7 => 
"\xF3\xA0\x96\xB8" => "",                     # U+E05B8 => 
"\xF3\xA0\x96\xB9" => "",                     # U+E05B9 => 
"\xF3\xA0\x96\xBA" => "",                     # U+E05BA => 
"\xF3\xA0\x96\xBB" => "",                     # U+E05BB => 
"\xF3\xA0\x96\xBC" => "",                     # U+E05BC => 
"\xF3\xA0\x96\xBD" => "",                     # U+E05BD => 
"\xF3\xA0\x96\xBE" => "",                     # U+E05BE => 
"\xF3\xA0\x96\xBF" => "",                     # U+E05BF => 
"\xF3\xA0\x97\x80" => "",                     # U+E05C0 => 
"\xF3\xA0\x97\x81" => "",                     # U+E05C1 => 
"\xF3\xA0\x97\x82" => "",                     # U+E05C2 => 
"\xF3\xA0\x97\x83" => "",                     # U+E05C3 => 
"\xF3\xA0\x97\x84" => "",                     # U+E05C4 => 
"\xF3\xA0\x97\x85" => "",                     # U+E05C5 => 
"\xF3\xA0\x97\x86" => "",                     # U+E05C6 => 
"\xF3\xA0\x97\x87" => "",                     # U+E05C7 => 
"\xF3\xA0\x97\x88" => "",                     # U+E05C8 => 
"\xF3\xA0\x97\x89" => "",                     # U+E05C9 => 
"\xF3\xA0\x97\x8A" => "",                     # U+E05CA => 
"\xF3\xA0\x97\x8B" => "",                     # U+E05CB => 
"\xF3\xA0\x97\x8C" => "",                     # U+E05CC => 
"\xF3\xA0\x97\x8D" => "",                     # U+E05CD => 
"\xF3\xA0\x97\x8E" => "",                     # U+E05CE => 
"\xF3\xA0\x97\x8F" => "",                     # U+E05CF => 
"\xF3\xA0\x97\x90" => "",                     # U+E05D0 => 
"\xF3\xA0\x97\x91" => "",                     # U+E05D1 => 
"\xF3\xA0\x97\x92" => "",                     # U+E05D2 => 
"\xF3\xA0\x97\x93" => "",                     # U+E05D3 => 
"\xF3\xA0\x97\x94" => "",                     # U+E05D4 => 
"\xF3\xA0\x97\x95" => "",                     # U+E05D5 => 
"\xF3\xA0\x97\x96" => "",                     # U+E05D6 => 
"\xF3\xA0\x97\x97" => "",                     # U+E05D7 => 
"\xF3\xA0\x97\x98" => "",                     # U+E05D8 => 
"\xF3\xA0\x97\x99" => "",                     # U+E05D9 => 
"\xF3\xA0\x97\x9A" => "",                     # U+E05DA => 
"\xF3\xA0\x97\x9B" => "",                     # U+E05DB => 
"\xF3\xA0\x97\x9C" => "",                     # U+E05DC => 
"\xF3\xA0\x97\x9D" => "",                     # U+E05DD => 
"\xF3\xA0\x97\x9E" => "",                     # U+E05DE => 
"\xF3\xA0\x97\x9F" => "",                     # U+E05DF => 
"\xF3\xA0\x97\xA0" => "",                     # U+E05E0 => 
"\xF3\xA0\x97\xA1" => "",                     # U+E05E1 => 
"\xF3\xA0\x97\xA2" => "",                     # U+E05E2 => 
"\xF3\xA0\x97\xA3" => "",                     # U+E05E3 => 
"\xF3\xA0\x97\xA4" => "",                     # U+E05E4 => 
"\xF3\xA0\x97\xA5" => "",                     # U+E05E5 => 
"\xF3\xA0\x97\xA6" => "",                     # U+E05E6 => 
"\xF3\xA0\x97\xA7" => "",                     # U+E05E7 => 
"\xF3\xA0\x97\xA8" => "",                     # U+E05E8 => 
"\xF3\xA0\x97\xA9" => "",                     # U+E05E9 => 
"\xF3\xA0\x97\xAA" => "",                     # U+E05EA => 
"\xF3\xA0\x97\xAB" => "",                     # U+E05EB => 
"\xF3\xA0\x97\xAC" => "",                     # U+E05EC => 
"\xF3\xA0\x97\xAD" => "",                     # U+E05ED => 
"\xF3\xA0\x97\xAE" => "",                     # U+E05EE => 
"\xF3\xA0\x97\xAF" => "",                     # U+E05EF => 
"\xF3\xA0\x97\xB0" => "",                     # U+E05F0 => 
"\xF3\xA0\x97\xB1" => "",                     # U+E05F1 => 
"\xF3\xA0\x97\xB2" => "",                     # U+E05F2 => 
"\xF3\xA0\x97\xB3" => "",                     # U+E05F3 => 
"\xF3\xA0\x97\xB4" => "",                     # U+E05F4 => 
"\xF3\xA0\x97\xB5" => "",                     # U+E05F5 => 
"\xF3\xA0\x97\xB6" => "",                     # U+E05F6 => 
"\xF3\xA0\x97\xB7" => "",                     # U+E05F7 => 
"\xF3\xA0\x97\xB8" => "",                     # U+E05F8 => 
"\xF3\xA0\x97\xB9" => "",                     # U+E05F9 => 
"\xF3\xA0\x97\xBA" => "",                     # U+E05FA => 
"\xF3\xA0\x97\xBB" => "",                     # U+E05FB => 
"\xF3\xA0\x97\xBC" => "",                     # U+E05FC => 
"\xF3\xA0\x97\xBD" => "",                     # U+E05FD => 
"\xF3\xA0\x97\xBE" => "",                     # U+E05FE => 
"\xF3\xA0\x97\xBF" => "",                     # U+E05FF => 
"\xF3\xA0\x98\x80" => "",                     # U+E0600 => 
"\xF3\xA0\x98\x81" => "",                     # U+E0601 => 
"\xF3\xA0\x98\x82" => "",                     # U+E0602 => 
"\xF3\xA0\x98\x83" => "",                     # U+E0603 => 
"\xF3\xA0\x98\x84" => "",                     # U+E0604 => 
"\xF3\xA0\x98\x85" => "",                     # U+E0605 => 
"\xF3\xA0\x98\x86" => "",                     # U+E0606 => 
"\xF3\xA0\x98\x87" => "",                     # U+E0607 => 
"\xF3\xA0\x98\x88" => "",                     # U+E0608 => 
"\xF3\xA0\x98\x89" => "",                     # U+E0609 => 
"\xF3\xA0\x98\x8A" => "",                     # U+E060A => 
"\xF3\xA0\x98\x8B" => "",                     # U+E060B => 
"\xF3\xA0\x98\x8C" => "",                     # U+E060C => 
"\xF3\xA0\x98\x8D" => "",                     # U+E060D => 
"\xF3\xA0\x98\x8E" => "",                     # U+E060E => 
"\xF3\xA0\x98\x8F" => "",                     # U+E060F => 
"\xF3\xA0\x98\x90" => "",                     # U+E0610 => 
"\xF3\xA0\x98\x91" => "",                     # U+E0611 => 
"\xF3\xA0\x98\x92" => "",                     # U+E0612 => 
"\xF3\xA0\x98\x93" => "",                     # U+E0613 => 
"\xF3\xA0\x98\x94" => "",                     # U+E0614 => 
"\xF3\xA0\x98\x95" => "",                     # U+E0615 => 
"\xF3\xA0\x98\x96" => "",                     # U+E0616 => 
"\xF3\xA0\x98\x97" => "",                     # U+E0617 => 
"\xF3\xA0\x98\x98" => "",                     # U+E0618 => 
"\xF3\xA0\x98\x99" => "",                     # U+E0619 => 
"\xF3\xA0\x98\x9A" => "",                     # U+E061A => 
"\xF3\xA0\x98\x9B" => "",                     # U+E061B => 
"\xF3\xA0\x98\x9C" => "",                     # U+E061C => 
"\xF3\xA0\x98\x9D" => "",                     # U+E061D => 
"\xF3\xA0\x98\x9E" => "",                     # U+E061E => 
"\xF3\xA0\x98\x9F" => "",                     # U+E061F => 
"\xF3\xA0\x98\xA0" => "",                     # U+E0620 => 
"\xF3\xA0\x98\xA1" => "",                     # U+E0621 => 
"\xF3\xA0\x98\xA2" => "",                     # U+E0622 => 
"\xF3\xA0\x98\xA3" => "",                     # U+E0623 => 
"\xF3\xA0\x98\xA4" => "",                     # U+E0624 => 
"\xF3\xA0\x98\xA5" => "",                     # U+E0625 => 
"\xF3\xA0\x98\xA6" => "",                     # U+E0626 => 
"\xF3\xA0\x98\xA7" => "",                     # U+E0627 => 
"\xF3\xA0\x98\xA8" => "",                     # U+E0628 => 
"\xF3\xA0\x98\xA9" => "",                     # U+E0629 => 
"\xF3\xA0\x98\xAA" => "",                     # U+E062A => 
"\xF3\xA0\x98\xAB" => "",                     # U+E062B => 
"\xF3\xA0\x98\xAC" => "",                     # U+E062C => 
"\xF3\xA0\x98\xAD" => "",                     # U+E062D => 
"\xF3\xA0\x98\xAE" => "",                     # U+E062E => 
"\xF3\xA0\x98\xAF" => "",                     # U+E062F => 
"\xF3\xA0\x98\xB0" => "",                     # U+E0630 => 
"\xF3\xA0\x98\xB1" => "",                     # U+E0631 => 
"\xF3\xA0\x98\xB2" => "",                     # U+E0632 => 
"\xF3\xA0\x98\xB3" => "",                     # U+E0633 => 
"\xF3\xA0\x98\xB4" => "",                     # U+E0634 => 
"\xF3\xA0\x98\xB5" => "",                     # U+E0635 => 
"\xF3\xA0\x98\xB6" => "",                     # U+E0636 => 
"\xF3\xA0\x98\xB7" => "",                     # U+E0637 => 
"\xF3\xA0\x98\xB8" => "",                     # U+E0638 => 
"\xF3\xA0\x98\xB9" => "",                     # U+E0639 => 
"\xF3\xA0\x98\xBA" => "",                     # U+E063A => 
"\xF3\xA0\x98\xBB" => "",                     # U+E063B => 
"\xF3\xA0\x98\xBC" => "",                     # U+E063C => 
"\xF3\xA0\x98\xBD" => "",                     # U+E063D => 
"\xF3\xA0\x98\xBE" => "",                     # U+E063E => 
"\xF3\xA0\x98\xBF" => "",                     # U+E063F => 
"\xF3\xA0\x99\x80" => "",                     # U+E0640 => 
"\xF3\xA0\x99\x81" => "",                     # U+E0641 => 
"\xF3\xA0\x99\x82" => "",                     # U+E0642 => 
"\xF3\xA0\x99\x83" => "",                     # U+E0643 => 
"\xF3\xA0\x99\x84" => "",                     # U+E0644 => 
"\xF3\xA0\x99\x85" => "",                     # U+E0645 => 
"\xF3\xA0\x99\x86" => "",                     # U+E0646 => 
"\xF3\xA0\x99\x87" => "",                     # U+E0647 => 
"\xF3\xA0\x99\x88" => "",                     # U+E0648 => 
"\xF3\xA0\x99\x89" => "",                     # U+E0649 => 
"\xF3\xA0\x99\x8A" => "",                     # U+E064A => 
"\xF3\xA0\x99\x8B" => "",                     # U+E064B => 
"\xF3\xA0\x99\x8C" => "",                     # U+E064C => 
"\xF3\xA0\x99\x8D" => "",                     # U+E064D => 
"\xF3\xA0\x99\x8E" => "",                     # U+E064E => 
"\xF3\xA0\x99\x8F" => "",                     # U+E064F => 
"\xF3\xA0\x99\x90" => "",                     # U+E0650 => 
"\xF3\xA0\x99\x91" => "",                     # U+E0651 => 
"\xF3\xA0\x99\x92" => "",                     # U+E0652 => 
"\xF3\xA0\x99\x93" => "",                     # U+E0653 => 
"\xF3\xA0\x99\x94" => "",                     # U+E0654 => 
"\xF3\xA0\x99\x95" => "",                     # U+E0655 => 
"\xF3\xA0\x99\x96" => "",                     # U+E0656 => 
"\xF3\xA0\x99\x97" => "",                     # U+E0657 => 
"\xF3\xA0\x99\x98" => "",                     # U+E0658 => 
"\xF3\xA0\x99\x99" => "",                     # U+E0659 => 
"\xF3\xA0\x99\x9A" => "",                     # U+E065A => 
"\xF3\xA0\x99\x9B" => "",                     # U+E065B => 
"\xF3\xA0\x99\x9C" => "",                     # U+E065C => 
"\xF3\xA0\x99\x9D" => "",                     # U+E065D => 
"\xF3\xA0\x99\x9E" => "",                     # U+E065E => 
"\xF3\xA0\x99\x9F" => "",                     # U+E065F => 
"\xF3\xA0\x99\xA0" => "",                     # U+E0660 => 
"\xF3\xA0\x99\xA1" => "",                     # U+E0661 => 
"\xF3\xA0\x99\xA2" => "",                     # U+E0662 => 
"\xF3\xA0\x99\xA3" => "",                     # U+E0663 => 
"\xF3\xA0\x99\xA4" => "",                     # U+E0664 => 
"\xF3\xA0\x99\xA5" => "",                     # U+E0665 => 
"\xF3\xA0\x99\xA6" => "",                     # U+E0666 => 
"\xF3\xA0\x99\xA7" => "",                     # U+E0667 => 
"\xF3\xA0\x99\xA8" => "",                     # U+E0668 => 
"\xF3\xA0\x99\xA9" => "",                     # U+E0669 => 
"\xF3\xA0\x99\xAA" => "",                     # U+E066A => 
"\xF3\xA0\x99\xAB" => "",                     # U+E066B => 
"\xF3\xA0\x99\xAC" => "",                     # U+E066C => 
"\xF3\xA0\x99\xAD" => "",                     # U+E066D => 
"\xF3\xA0\x99\xAE" => "",                     # U+E066E => 
"\xF3\xA0\x99\xAF" => "",                     # U+E066F => 
"\xF3\xA0\x99\xB0" => "",                     # U+E0670 => 
"\xF3\xA0\x99\xB1" => "",                     # U+E0671 => 
"\xF3\xA0\x99\xB2" => "",                     # U+E0672 => 
"\xF3\xA0\x99\xB3" => "",                     # U+E0673 => 
"\xF3\xA0\x99\xB4" => "",                     # U+E0674 => 
"\xF3\xA0\x99\xB5" => "",                     # U+E0675 => 
"\xF3\xA0\x99\xB6" => "",                     # U+E0676 => 
"\xF3\xA0\x99\xB7" => "",                     # U+E0677 => 
"\xF3\xA0\x99\xB8" => "",                     # U+E0678 => 
"\xF3\xA0\x99\xB9" => "",                     # U+E0679 => 
"\xF3\xA0\x99\xBA" => "",                     # U+E067A => 
"\xF3\xA0\x99\xBB" => "",                     # U+E067B => 
"\xF3\xA0\x99\xBC" => "",                     # U+E067C => 
"\xF3\xA0\x99\xBD" => "",                     # U+E067D => 
"\xF3\xA0\x99\xBE" => "",                     # U+E067E => 
"\xF3\xA0\x99\xBF" => "",                     # U+E067F => 
"\xF3\xA0\x9A\x80" => "",                     # U+E0680 => 
"\xF3\xA0\x9A\x81" => "",                     # U+E0681 => 
"\xF3\xA0\x9A\x82" => "",                     # U+E0682 => 
"\xF3\xA0\x9A\x83" => "",                     # U+E0683 => 
"\xF3\xA0\x9A\x84" => "",                     # U+E0684 => 
"\xF3\xA0\x9A\x85" => "",                     # U+E0685 => 
"\xF3\xA0\x9A\x86" => "",                     # U+E0686 => 
"\xF3\xA0\x9A\x87" => "",                     # U+E0687 => 
"\xF3\xA0\x9A\x88" => "",                     # U+E0688 => 
"\xF3\xA0\x9A\x89" => "",                     # U+E0689 => 
"\xF3\xA0\x9A\x8A" => "",                     # U+E068A => 
"\xF3\xA0\x9A\x8B" => "",                     # U+E068B => 
"\xF3\xA0\x9A\x8C" => "",                     # U+E068C => 
"\xF3\xA0\x9A\x8D" => "",                     # U+E068D => 
"\xF3\xA0\x9A\x8E" => "",                     # U+E068E => 
"\xF3\xA0\x9A\x8F" => "",                     # U+E068F => 
"\xF3\xA0\x9A\x90" => "",                     # U+E0690 => 
"\xF3\xA0\x9A\x91" => "",                     # U+E0691 => 
"\xF3\xA0\x9A\x92" => "",                     # U+E0692 => 
"\xF3\xA0\x9A\x93" => "",                     # U+E0693 => 
"\xF3\xA0\x9A\x94" => "",                     # U+E0694 => 
"\xF3\xA0\x9A\x95" => "",                     # U+E0695 => 
"\xF3\xA0\x9A\x96" => "",                     # U+E0696 => 
"\xF3\xA0\x9A\x97" => "",                     # U+E0697 => 
"\xF3\xA0\x9A\x98" => "",                     # U+E0698 => 
"\xF3\xA0\x9A\x99" => "",                     # U+E0699 => 
"\xF3\xA0\x9A\x9A" => "",                     # U+E069A => 
"\xF3\xA0\x9A\x9B" => "",                     # U+E069B => 
"\xF3\xA0\x9A\x9C" => "",                     # U+E069C => 
"\xF3\xA0\x9A\x9D" => "",                     # U+E069D => 
"\xF3\xA0\x9A\x9E" => "",                     # U+E069E => 
"\xF3\xA0\x9A\x9F" => "",                     # U+E069F => 
"\xF3\xA0\x9A\xA0" => "",                     # U+E06A0 => 
"\xF3\xA0\x9A\xA1" => "",                     # U+E06A1 => 
"\xF3\xA0\x9A\xA2" => "",                     # U+E06A2 => 
"\xF3\xA0\x9A\xA3" => "",                     # U+E06A3 => 
"\xF3\xA0\x9A\xA4" => "",                     # U+E06A4 => 
"\xF3\xA0\x9A\xA5" => "",                     # U+E06A5 => 
"\xF3\xA0\x9A\xA6" => "",                     # U+E06A6 => 
"\xF3\xA0\x9A\xA7" => "",                     # U+E06A7 => 
"\xF3\xA0\x9A\xA8" => "",                     # U+E06A8 => 
"\xF3\xA0\x9A\xA9" => "",                     # U+E06A9 => 
"\xF3\xA0\x9A\xAA" => "",                     # U+E06AA => 
"\xF3\xA0\x9A\xAB" => "",                     # U+E06AB => 
"\xF3\xA0\x9A\xAC" => "",                     # U+E06AC => 
"\xF3\xA0\x9A\xAD" => "",                     # U+E06AD => 
"\xF3\xA0\x9A\xAE" => "",                     # U+E06AE => 
"\xF3\xA0\x9A\xAF" => "",                     # U+E06AF => 
"\xF3\xA0\x9A\xB0" => "",                     # U+E06B0 => 
"\xF3\xA0\x9A\xB1" => "",                     # U+E06B1 => 
"\xF3\xA0\x9A\xB2" => "",                     # U+E06B2 => 
"\xF3\xA0\x9A\xB3" => "",                     # U+E06B3 => 
"\xF3\xA0\x9A\xB4" => "",                     # U+E06B4 => 
"\xF3\xA0\x9A\xB5" => "",                     # U+E06B5 => 
"\xF3\xA0\x9A\xB6" => "",                     # U+E06B6 => 
"\xF3\xA0\x9A\xB7" => "",                     # U+E06B7 => 
"\xF3\xA0\x9A\xB8" => "",                     # U+E06B8 => 
"\xF3\xA0\x9A\xB9" => "",                     # U+E06B9 => 
"\xF3\xA0\x9A\xBA" => "",                     # U+E06BA => 
"\xF3\xA0\x9A\xBB" => "",                     # U+E06BB => 
"\xF3\xA0\x9A\xBC" => "",                     # U+E06BC => 
"\xF3\xA0\x9A\xBD" => "",                     # U+E06BD => 
"\xF3\xA0\x9A\xBE" => "",                     # U+E06BE => 
"\xF3\xA0\x9A\xBF" => "",                     # U+E06BF => 
"\xF3\xA0\x9B\x80" => "",                     # U+E06C0 => 
"\xF3\xA0\x9B\x81" => "",                     # U+E06C1 => 
"\xF3\xA0\x9B\x82" => "",                     # U+E06C2 => 
"\xF3\xA0\x9B\x83" => "",                     # U+E06C3 => 
"\xF3\xA0\x9B\x84" => "",                     # U+E06C4 => 
"\xF3\xA0\x9B\x85" => "",                     # U+E06C5 => 
"\xF3\xA0\x9B\x86" => "",                     # U+E06C6 => 
"\xF3\xA0\x9B\x87" => "",                     # U+E06C7 => 
"\xF3\xA0\x9B\x88" => "",                     # U+E06C8 => 
"\xF3\xA0\x9B\x89" => "",                     # U+E06C9 => 
"\xF3\xA0\x9B\x8A" => "",                     # U+E06CA => 
"\xF3\xA0\x9B\x8B" => "",                     # U+E06CB => 
"\xF3\xA0\x9B\x8C" => "",                     # U+E06CC => 
"\xF3\xA0\x9B\x8D" => "",                     # U+E06CD => 
"\xF3\xA0\x9B\x8E" => "",                     # U+E06CE => 
"\xF3\xA0\x9B\x8F" => "",                     # U+E06CF => 
"\xF3\xA0\x9B\x90" => "",                     # U+E06D0 => 
"\xF3\xA0\x9B\x91" => "",                     # U+E06D1 => 
"\xF3\xA0\x9B\x92" => "",                     # U+E06D2 => 
"\xF3\xA0\x9B\x93" => "",                     # U+E06D3 => 
"\xF3\xA0\x9B\x94" => "",                     # U+E06D4 => 
"\xF3\xA0\x9B\x95" => "",                     # U+E06D5 => 
"\xF3\xA0\x9B\x96" => "",                     # U+E06D6 => 
"\xF3\xA0\x9B\x97" => "",                     # U+E06D7 => 
"\xF3\xA0\x9B\x98" => "",                     # U+E06D8 => 
"\xF3\xA0\x9B\x99" => "",                     # U+E06D9 => 
"\xF3\xA0\x9B\x9A" => "",                     # U+E06DA => 
"\xF3\xA0\x9B\x9B" => "",                     # U+E06DB => 
"\xF3\xA0\x9B\x9C" => "",                     # U+E06DC => 
"\xF3\xA0\x9B\x9D" => "",                     # U+E06DD => 
"\xF3\xA0\x9B\x9E" => "",                     # U+E06DE => 
"\xF3\xA0\x9B\x9F" => "",                     # U+E06DF => 
"\xF3\xA0\x9B\xA0" => "",                     # U+E06E0 => 
"\xF3\xA0\x9B\xA1" => "",                     # U+E06E1 => 
"\xF3\xA0\x9B\xA2" => "",                     # U+E06E2 => 
"\xF3\xA0\x9B\xA3" => "",                     # U+E06E3 => 
"\xF3\xA0\x9B\xA4" => "",                     # U+E06E4 => 
"\xF3\xA0\x9B\xA5" => "",                     # U+E06E5 => 
"\xF3\xA0\x9B\xA6" => "",                     # U+E06E6 => 
"\xF3\xA0\x9B\xA7" => "",                     # U+E06E7 => 
"\xF3\xA0\x9B\xA8" => "",                     # U+E06E8 => 
"\xF3\xA0\x9B\xA9" => "",                     # U+E06E9 => 
"\xF3\xA0\x9B\xAA" => "",                     # U+E06EA => 
"\xF3\xA0\x9B\xAB" => "",                     # U+E06EB => 
"\xF3\xA0\x9B\xAC" => "",                     # U+E06EC => 
"\xF3\xA0\x9B\xAD" => "",                     # U+E06ED => 
"\xF3\xA0\x9B\xAE" => "",                     # U+E06EE => 
"\xF3\xA0\x9B\xAF" => "",                     # U+E06EF => 
"\xF3\xA0\x9B\xB0" => "",                     # U+E06F0 => 
"\xF3\xA0\x9B\xB1" => "",                     # U+E06F1 => 
"\xF3\xA0\x9B\xB2" => "",                     # U+E06F2 => 
"\xF3\xA0\x9B\xB3" => "",                     # U+E06F3 => 
"\xF3\xA0\x9B\xB4" => "",                     # U+E06F4 => 
"\xF3\xA0\x9B\xB5" => "",                     # U+E06F5 => 
"\xF3\xA0\x9B\xB6" => "",                     # U+E06F6 => 
"\xF3\xA0\x9B\xB7" => "",                     # U+E06F7 => 
"\xF3\xA0\x9B\xB8" => "",                     # U+E06F8 => 
"\xF3\xA0\x9B\xB9" => "",                     # U+E06F9 => 
"\xF3\xA0\x9B\xBA" => "",                     # U+E06FA => 
"\xF3\xA0\x9B\xBB" => "",                     # U+E06FB => 
"\xF3\xA0\x9B\xBC" => "",                     # U+E06FC => 
"\xF3\xA0\x9B\xBD" => "",                     # U+E06FD => 
"\xF3\xA0\x9B\xBE" => "",                     # U+E06FE => 
"\xF3\xA0\x9B\xBF" => "",                     # U+E06FF => 
"\xF3\xA0\x9C\x80" => "",                     # U+E0700 => 
"\xF3\xA0\x9C\x81" => "",                     # U+E0701 => 
"\xF3\xA0\x9C\x82" => "",                     # U+E0702 => 
"\xF3\xA0\x9C\x83" => "",                     # U+E0703 => 
"\xF3\xA0\x9C\x84" => "",                     # U+E0704 => 
"\xF3\xA0\x9C\x85" => "",                     # U+E0705 => 
"\xF3\xA0\x9C\x86" => "",                     # U+E0706 => 
"\xF3\xA0\x9C\x87" => "",                     # U+E0707 => 
"\xF3\xA0\x9C\x88" => "",                     # U+E0708 => 
"\xF3\xA0\x9C\x89" => "",                     # U+E0709 => 
"\xF3\xA0\x9C\x8A" => "",                     # U+E070A => 
"\xF3\xA0\x9C\x8B" => "",                     # U+E070B => 
"\xF3\xA0\x9C\x8C" => "",                     # U+E070C => 
"\xF3\xA0\x9C\x8D" => "",                     # U+E070D => 
"\xF3\xA0\x9C\x8E" => "",                     # U+E070E => 
"\xF3\xA0\x9C\x8F" => "",                     # U+E070F => 
"\xF3\xA0\x9C\x90" => "",                     # U+E0710 => 
"\xF3\xA0\x9C\x91" => "",                     # U+E0711 => 
"\xF3\xA0\x9C\x92" => "",                     # U+E0712 => 
"\xF3\xA0\x9C\x93" => "",                     # U+E0713 => 
"\xF3\xA0\x9C\x94" => "",                     # U+E0714 => 
"\xF3\xA0\x9C\x95" => "",                     # U+E0715 => 
"\xF3\xA0\x9C\x96" => "",                     # U+E0716 => 
"\xF3\xA0\x9C\x97" => "",                     # U+E0717 => 
"\xF3\xA0\x9C\x98" => "",                     # U+E0718 => 
"\xF3\xA0\x9C\x99" => "",                     # U+E0719 => 
"\xF3\xA0\x9C\x9A" => "",                     # U+E071A => 
"\xF3\xA0\x9C\x9B" => "",                     # U+E071B => 
"\xF3\xA0\x9C\x9C" => "",                     # U+E071C => 
"\xF3\xA0\x9C\x9D" => "",                     # U+E071D => 
"\xF3\xA0\x9C\x9E" => "",                     # U+E071E => 
"\xF3\xA0\x9C\x9F" => "",                     # U+E071F => 
"\xF3\xA0\x9C\xA0" => "",                     # U+E0720 => 
"\xF3\xA0\x9C\xA1" => "",                     # U+E0721 => 
"\xF3\xA0\x9C\xA2" => "",                     # U+E0722 => 
"\xF3\xA0\x9C\xA3" => "",                     # U+E0723 => 
"\xF3\xA0\x9C\xA4" => "",                     # U+E0724 => 
"\xF3\xA0\x9C\xA5" => "",                     # U+E0725 => 
"\xF3\xA0\x9C\xA6" => "",                     # U+E0726 => 
"\xF3\xA0\x9C\xA7" => "",                     # U+E0727 => 
"\xF3\xA0\x9C\xA8" => "",                     # U+E0728 => 
"\xF3\xA0\x9C\xA9" => "",                     # U+E0729 => 
"\xF3\xA0\x9C\xAA" => "",                     # U+E072A => 
"\xF3\xA0\x9C\xAB" => "",                     # U+E072B => 
"\xF3\xA0\x9C\xAC" => "",                     # U+E072C => 
"\xF3\xA0\x9C\xAD" => "",                     # U+E072D => 
"\xF3\xA0\x9C\xAE" => "",                     # U+E072E => 
"\xF3\xA0\x9C\xAF" => "",                     # U+E072F => 
"\xF3\xA0\x9C\xB0" => "",                     # U+E0730 => 
"\xF3\xA0\x9C\xB1" => "",                     # U+E0731 => 
"\xF3\xA0\x9C\xB2" => "",                     # U+E0732 => 
"\xF3\xA0\x9C\xB3" => "",                     # U+E0733 => 
"\xF3\xA0\x9C\xB4" => "",                     # U+E0734 => 
"\xF3\xA0\x9C\xB5" => "",                     # U+E0735 => 
"\xF3\xA0\x9C\xB6" => "",                     # U+E0736 => 
"\xF3\xA0\x9C\xB7" => "",                     # U+E0737 => 
"\xF3\xA0\x9C\xB8" => "",                     # U+E0738 => 
"\xF3\xA0\x9C\xB9" => "",                     # U+E0739 => 
"\xF3\xA0\x9C\xBA" => "",                     # U+E073A => 
"\xF3\xA0\x9C\xBB" => "",                     # U+E073B => 
"\xF3\xA0\x9C\xBC" => "",                     # U+E073C => 
"\xF3\xA0\x9C\xBD" => "",                     # U+E073D => 
"\xF3\xA0\x9C\xBE" => "",                     # U+E073E => 
"\xF3\xA0\x9C\xBF" => "",                     # U+E073F => 
"\xF3\xA0\x9D\x80" => "",                     # U+E0740 => 
"\xF3\xA0\x9D\x81" => "",                     # U+E0741 => 
"\xF3\xA0\x9D\x82" => "",                     # U+E0742 => 
"\xF3\xA0\x9D\x83" => "",                     # U+E0743 => 
"\xF3\xA0\x9D\x84" => "",                     # U+E0744 => 
"\xF3\xA0\x9D\x85" => "",                     # U+E0745 => 
"\xF3\xA0\x9D\x86" => "",                     # U+E0746 => 
"\xF3\xA0\x9D\x87" => "",                     # U+E0747 => 
"\xF3\xA0\x9D\x88" => "",                     # U+E0748 => 
"\xF3\xA0\x9D\x89" => "",                     # U+E0749 => 
"\xF3\xA0\x9D\x8A" => "",                     # U+E074A => 
"\xF3\xA0\x9D\x8B" => "",                     # U+E074B => 
"\xF3\xA0\x9D\x8C" => "",                     # U+E074C => 
"\xF3\xA0\x9D\x8D" => "",                     # U+E074D => 
"\xF3\xA0\x9D\x8E" => "",                     # U+E074E => 
"\xF3\xA0\x9D\x8F" => "",                     # U+E074F => 
"\xF3\xA0\x9D\x90" => "",                     # U+E0750 => 
"\xF3\xA0\x9D\x91" => "",                     # U+E0751 => 
"\xF3\xA0\x9D\x92" => "",                     # U+E0752 => 
"\xF3\xA0\x9D\x93" => "",                     # U+E0753 => 
"\xF3\xA0\x9D\x94" => "",                     # U+E0754 => 
"\xF3\xA0\x9D\x95" => "",                     # U+E0755 => 
"\xF3\xA0\x9D\x96" => "",                     # U+E0756 => 
"\xF3\xA0\x9D\x97" => "",                     # U+E0757 => 
"\xF3\xA0\x9D\x98" => "",                     # U+E0758 => 
"\xF3\xA0\x9D\x99" => "",                     # U+E0759 => 
"\xF3\xA0\x9D\x9A" => "",                     # U+E075A => 
"\xF3\xA0\x9D\x9B" => "",                     # U+E075B => 
"\xF3\xA0\x9D\x9C" => "",                     # U+E075C => 
"\xF3\xA0\x9D\x9D" => "",                     # U+E075D => 
"\xF3\xA0\x9D\x9E" => "",                     # U+E075E => 
"\xF3\xA0\x9D\x9F" => "",                     # U+E075F => 
"\xF3\xA0\x9D\xA0" => "",                     # U+E0760 => 
"\xF3\xA0\x9D\xA1" => "",                     # U+E0761 => 
"\xF3\xA0\x9D\xA2" => "",                     # U+E0762 => 
"\xF3\xA0\x9D\xA3" => "",                     # U+E0763 => 
"\xF3\xA0\x9D\xA4" => "",                     # U+E0764 => 
"\xF3\xA0\x9D\xA5" => "",                     # U+E0765 => 
"\xF3\xA0\x9D\xA6" => "",                     # U+E0766 => 
"\xF3\xA0\x9D\xA7" => "",                     # U+E0767 => 
"\xF3\xA0\x9D\xA8" => "",                     # U+E0768 => 
"\xF3\xA0\x9D\xA9" => "",                     # U+E0769 => 
"\xF3\xA0\x9D\xAA" => "",                     # U+E076A => 
"\xF3\xA0\x9D\xAB" => "",                     # U+E076B => 
"\xF3\xA0\x9D\xAC" => "",                     # U+E076C => 
"\xF3\xA0\x9D\xAD" => "",                     # U+E076D => 
"\xF3\xA0\x9D\xAE" => "",                     # U+E076E => 
"\xF3\xA0\x9D\xAF" => "",                     # U+E076F => 
"\xF3\xA0\x9D\xB0" => "",                     # U+E0770 => 
"\xF3\xA0\x9D\xB1" => "",                     # U+E0771 => 
"\xF3\xA0\x9D\xB2" => "",                     # U+E0772 => 
"\xF3\xA0\x9D\xB3" => "",                     # U+E0773 => 
"\xF3\xA0\x9D\xB4" => "",                     # U+E0774 => 
"\xF3\xA0\x9D\xB5" => "",                     # U+E0775 => 
"\xF3\xA0\x9D\xB6" => "",                     # U+E0776 => 
"\xF3\xA0\x9D\xB7" => "",                     # U+E0777 => 
"\xF3\xA0\x9D\xB8" => "",                     # U+E0778 => 
"\xF3\xA0\x9D\xB9" => "",                     # U+E0779 => 
"\xF3\xA0\x9D\xBA" => "",                     # U+E077A => 
"\xF3\xA0\x9D\xBB" => "",                     # U+E077B => 
"\xF3\xA0\x9D\xBC" => "",                     # U+E077C => 
"\xF3\xA0\x9D\xBD" => "",                     # U+E077D => 
"\xF3\xA0\x9D\xBE" => "",                     # U+E077E => 
"\xF3\xA0\x9D\xBF" => "",                     # U+E077F => 
"\xF3\xA0\x9E\x80" => "",                     # U+E0780 => 
"\xF3\xA0\x9E\x81" => "",                     # U+E0781 => 
"\xF3\xA0\x9E\x82" => "",                     # U+E0782 => 
"\xF3\xA0\x9E\x83" => "",                     # U+E0783 => 
"\xF3\xA0\x9E\x84" => "",                     # U+E0784 => 
"\xF3\xA0\x9E\x85" => "",                     # U+E0785 => 
"\xF3\xA0\x9E\x86" => "",                     # U+E0786 => 
"\xF3\xA0\x9E\x87" => "",                     # U+E0787 => 
"\xF3\xA0\x9E\x88" => "",                     # U+E0788 => 
"\xF3\xA0\x9E\x89" => "",                     # U+E0789 => 
"\xF3\xA0\x9E\x8A" => "",                     # U+E078A => 
"\xF3\xA0\x9E\x8B" => "",                     # U+E078B => 
"\xF3\xA0\x9E\x8C" => "",                     # U+E078C => 
"\xF3\xA0\x9E\x8D" => "",                     # U+E078D => 
"\xF3\xA0\x9E\x8E" => "",                     # U+E078E => 
"\xF3\xA0\x9E\x8F" => "",                     # U+E078F => 
"\xF3\xA0\x9E\x90" => "",                     # U+E0790 => 
"\xF3\xA0\x9E\x91" => "",                     # U+E0791 => 
"\xF3\xA0\x9E\x92" => "",                     # U+E0792 => 
"\xF3\xA0\x9E\x93" => "",                     # U+E0793 => 
"\xF3\xA0\x9E\x94" => "",                     # U+E0794 => 
"\xF3\xA0\x9E\x95" => "",                     # U+E0795 => 
"\xF3\xA0\x9E\x96" => "",                     # U+E0796 => 
"\xF3\xA0\x9E\x97" => "",                     # U+E0797 => 
"\xF3\xA0\x9E\x98" => "",                     # U+E0798 => 
"\xF3\xA0\x9E\x99" => "",                     # U+E0799 => 
"\xF3\xA0\x9E\x9A" => "",                     # U+E079A => 
"\xF3\xA0\x9E\x9B" => "",                     # U+E079B => 
"\xF3\xA0\x9E\x9C" => "",                     # U+E079C => 
"\xF3\xA0\x9E\x9D" => "",                     # U+E079D => 
"\xF3\xA0\x9E\x9E" => "",                     # U+E079E => 
"\xF3\xA0\x9E\x9F" => "",                     # U+E079F => 
"\xF3\xA0\x9E\xA0" => "",                     # U+E07A0 => 
"\xF3\xA0\x9E\xA1" => "",                     # U+E07A1 => 
"\xF3\xA0\x9E\xA2" => "",                     # U+E07A2 => 
"\xF3\xA0\x9E\xA3" => "",                     # U+E07A3 => 
"\xF3\xA0\x9E\xA4" => "",                     # U+E07A4 => 
"\xF3\xA0\x9E\xA5" => "",                     # U+E07A5 => 
"\xF3\xA0\x9E\xA6" => "",                     # U+E07A6 => 
"\xF3\xA0\x9E\xA7" => "",                     # U+E07A7 => 
"\xF3\xA0\x9E\xA8" => "",                     # U+E07A8 => 
"\xF3\xA0\x9E\xA9" => "",                     # U+E07A9 => 
"\xF3\xA0\x9E\xAA" => "",                     # U+E07AA => 
"\xF3\xA0\x9E\xAB" => "",                     # U+E07AB => 
"\xF3\xA0\x9E\xAC" => "",                     # U+E07AC => 
"\xF3\xA0\x9E\xAD" => "",                     # U+E07AD => 
"\xF3\xA0\x9E\xAE" => "",                     # U+E07AE => 
"\xF3\xA0\x9E\xAF" => "",                     # U+E07AF => 
"\xF3\xA0\x9E\xB0" => "",                     # U+E07B0 => 
"\xF3\xA0\x9E\xB1" => "",                     # U+E07B1 => 
"\xF3\xA0\x9E\xB2" => "",                     # U+E07B2 => 
"\xF3\xA0\x9E\xB3" => "",                     # U+E07B3 => 
"\xF3\xA0\x9E\xB4" => "",                     # U+E07B4 => 
"\xF3\xA0\x9E\xB5" => "",                     # U+E07B5 => 
"\xF3\xA0\x9E\xB6" => "",                     # U+E07B6 => 
"\xF3\xA0\x9E\xB7" => "",                     # U+E07B7 => 
"\xF3\xA0\x9E\xB8" => "",                     # U+E07B8 => 
"\xF3\xA0\x9E\xB9" => "",                     # U+E07B9 => 
"\xF3\xA0\x9E\xBA" => "",                     # U+E07BA => 
"\xF3\xA0\x9E\xBB" => "",                     # U+E07BB => 
"\xF3\xA0\x9E\xBC" => "",                     # U+E07BC => 
"\xF3\xA0\x9E\xBD" => "",                     # U+E07BD => 
"\xF3\xA0\x9E\xBE" => "",                     # U+E07BE => 
"\xF3\xA0\x9E\xBF" => "",                     # U+E07BF => 
"\xF3\xA0\x9F\x80" => "",                     # U+E07C0 => 
"\xF3\xA0\x9F\x81" => "",                     # U+E07C1 => 
"\xF3\xA0\x9F\x82" => "",                     # U+E07C2 => 
"\xF3\xA0\x9F\x83" => "",                     # U+E07C3 => 
"\xF3\xA0\x9F\x84" => "",                     # U+E07C4 => 
"\xF3\xA0\x9F\x85" => "",                     # U+E07C5 => 
"\xF3\xA0\x9F\x86" => "",                     # U+E07C6 => 
"\xF3\xA0\x9F\x87" => "",                     # U+E07C7 => 
"\xF3\xA0\x9F\x88" => "",                     # U+E07C8 => 
"\xF3\xA0\x9F\x89" => "",                     # U+E07C9 => 
"\xF3\xA0\x9F\x8A" => "",                     # U+E07CA => 
"\xF3\xA0\x9F\x8B" => "",                     # U+E07CB => 
"\xF3\xA0\x9F\x8C" => "",                     # U+E07CC => 
"\xF3\xA0\x9F\x8D" => "",                     # U+E07CD => 
"\xF3\xA0\x9F\x8E" => "",                     # U+E07CE => 
"\xF3\xA0\x9F\x8F" => "",                     # U+E07CF => 
"\xF3\xA0\x9F\x90" => "",                     # U+E07D0 => 
"\xF3\xA0\x9F\x91" => "",                     # U+E07D1 => 
"\xF3\xA0\x9F\x92" => "",                     # U+E07D2 => 
"\xF3\xA0\x9F\x93" => "",                     # U+E07D3 => 
"\xF3\xA0\x9F\x94" => "",                     # U+E07D4 => 
"\xF3\xA0\x9F\x95" => "",                     # U+E07D5 => 
"\xF3\xA0\x9F\x96" => "",                     # U+E07D6 => 
"\xF3\xA0\x9F\x97" => "",                     # U+E07D7 => 
"\xF3\xA0\x9F\x98" => "",                     # U+E07D8 => 
"\xF3\xA0\x9F\x99" => "",                     # U+E07D9 => 
"\xF3\xA0\x9F\x9A" => "",                     # U+E07DA => 
"\xF3\xA0\x9F\x9B" => "",                     # U+E07DB => 
"\xF3\xA0\x9F\x9C" => "",                     # U+E07DC => 
"\xF3\xA0\x9F\x9D" => "",                     # U+E07DD => 
"\xF3\xA0\x9F\x9E" => "",                     # U+E07DE => 
"\xF3\xA0\x9F\x9F" => "",                     # U+E07DF => 
"\xF3\xA0\x9F\xA0" => "",                     # U+E07E0 => 
"\xF3\xA0\x9F\xA1" => "",                     # U+E07E1 => 
"\xF3\xA0\x9F\xA2" => "",                     # U+E07E2 => 
"\xF3\xA0\x9F\xA3" => "",                     # U+E07E3 => 
"\xF3\xA0\x9F\xA4" => "",                     # U+E07E4 => 
"\xF3\xA0\x9F\xA5" => "",                     # U+E07E5 => 
"\xF3\xA0\x9F\xA6" => "",                     # U+E07E6 => 
"\xF3\xA0\x9F\xA7" => "",                     # U+E07E7 => 
"\xF3\xA0\x9F\xA8" => "",                     # U+E07E8 => 
"\xF3\xA0\x9F\xA9" => "",                     # U+E07E9 => 
"\xF3\xA0\x9F\xAA" => "",                     # U+E07EA => 
"\xF3\xA0\x9F\xAB" => "",                     # U+E07EB => 
"\xF3\xA0\x9F\xAC" => "",                     # U+E07EC => 
"\xF3\xA0\x9F\xAD" => "",                     # U+E07ED => 
"\xF3\xA0\x9F\xAE" => "",                     # U+E07EE => 
"\xF3\xA0\x9F\xAF" => "",                     # U+E07EF => 
"\xF3\xA0\x9F\xB0" => "",                     # U+E07F0 => 
"\xF3\xA0\x9F\xB1" => "",                     # U+E07F1 => 
"\xF3\xA0\x9F\xB2" => "",                     # U+E07F2 => 
"\xF3\xA0\x9F\xB3" => "",                     # U+E07F3 => 
"\xF3\xA0\x9F\xB4" => "",                     # U+E07F4 => 
"\xF3\xA0\x9F\xB5" => "",                     # U+E07F5 => 
"\xF3\xA0\x9F\xB6" => "",                     # U+E07F6 => 
"\xF3\xA0\x9F\xB7" => "",                     # U+E07F7 => 
"\xF3\xA0\x9F\xB8" => "",                     # U+E07F8 => 
"\xF3\xA0\x9F\xB9" => "",                     # U+E07F9 => 
"\xF3\xA0\x9F\xBA" => "",                     # U+E07FA => 
"\xF3\xA0\x9F\xBB" => "",                     # U+E07FB => 
"\xF3\xA0\x9F\xBC" => "",                     # U+E07FC => 
"\xF3\xA0\x9F\xBD" => "",                     # U+E07FD => 
"\xF3\xA0\x9F\xBE" => "",                     # U+E07FE => 
"\xF3\xA0\x9F\xBF" => "",                     # U+E07FF => 
"\xF3\xA0\xA0\x80" => "",                     # U+E0800 => 
"\xF3\xA0\xA0\x81" => "",                     # U+E0801 => 
"\xF3\xA0\xA0\x82" => "",                     # U+E0802 => 
"\xF3\xA0\xA0\x83" => "",                     # U+E0803 => 
"\xF3\xA0\xA0\x84" => "",                     # U+E0804 => 
"\xF3\xA0\xA0\x85" => "",                     # U+E0805 => 
"\xF3\xA0\xA0\x86" => "",                     # U+E0806 => 
"\xF3\xA0\xA0\x87" => "",                     # U+E0807 => 
"\xF3\xA0\xA0\x88" => "",                     # U+E0808 => 
"\xF3\xA0\xA0\x89" => "",                     # U+E0809 => 
"\xF3\xA0\xA0\x8A" => "",                     # U+E080A => 
"\xF3\xA0\xA0\x8B" => "",                     # U+E080B => 
"\xF3\xA0\xA0\x8C" => "",                     # U+E080C => 
"\xF3\xA0\xA0\x8D" => "",                     # U+E080D => 
"\xF3\xA0\xA0\x8E" => "",                     # U+E080E => 
"\xF3\xA0\xA0\x8F" => "",                     # U+E080F => 
"\xF3\xA0\xA0\x90" => "",                     # U+E0810 => 
"\xF3\xA0\xA0\x91" => "",                     # U+E0811 => 
"\xF3\xA0\xA0\x92" => "",                     # U+E0812 => 
"\xF3\xA0\xA0\x93" => "",                     # U+E0813 => 
"\xF3\xA0\xA0\x94" => "",                     # U+E0814 => 
"\xF3\xA0\xA0\x95" => "",                     # U+E0815 => 
"\xF3\xA0\xA0\x96" => "",                     # U+E0816 => 
"\xF3\xA0\xA0\x97" => "",                     # U+E0817 => 
"\xF3\xA0\xA0\x98" => "",                     # U+E0818 => 
"\xF3\xA0\xA0\x99" => "",                     # U+E0819 => 
"\xF3\xA0\xA0\x9A" => "",                     # U+E081A => 
"\xF3\xA0\xA0\x9B" => "",                     # U+E081B => 
"\xF3\xA0\xA0\x9C" => "",                     # U+E081C => 
"\xF3\xA0\xA0\x9D" => "",                     # U+E081D => 
"\xF3\xA0\xA0\x9E" => "",                     # U+E081E => 
"\xF3\xA0\xA0\x9F" => "",                     # U+E081F => 
"\xF3\xA0\xA0\xA0" => "",                     # U+E0820 => 
"\xF3\xA0\xA0\xA1" => "",                     # U+E0821 => 
"\xF3\xA0\xA0\xA2" => "",                     # U+E0822 => 
"\xF3\xA0\xA0\xA3" => "",                     # U+E0823 => 
"\xF3\xA0\xA0\xA4" => "",                     # U+E0824 => 
"\xF3\xA0\xA0\xA5" => "",                     # U+E0825 => 
"\xF3\xA0\xA0\xA6" => "",                     # U+E0826 => 
"\xF3\xA0\xA0\xA7" => "",                     # U+E0827 => 
"\xF3\xA0\xA0\xA8" => "",                     # U+E0828 => 
"\xF3\xA0\xA0\xA9" => "",                     # U+E0829 => 
"\xF3\xA0\xA0\xAA" => "",                     # U+E082A => 
"\xF3\xA0\xA0\xAB" => "",                     # U+E082B => 
"\xF3\xA0\xA0\xAC" => "",                     # U+E082C => 
"\xF3\xA0\xA0\xAD" => "",                     # U+E082D => 
"\xF3\xA0\xA0\xAE" => "",                     # U+E082E => 
"\xF3\xA0\xA0\xAF" => "",                     # U+E082F => 
"\xF3\xA0\xA0\xB0" => "",                     # U+E0830 => 
"\xF3\xA0\xA0\xB1" => "",                     # U+E0831 => 
"\xF3\xA0\xA0\xB2" => "",                     # U+E0832 => 
"\xF3\xA0\xA0\xB3" => "",                     # U+E0833 => 
"\xF3\xA0\xA0\xB4" => "",                     # U+E0834 => 
"\xF3\xA0\xA0\xB5" => "",                     # U+E0835 => 
"\xF3\xA0\xA0\xB6" => "",                     # U+E0836 => 
"\xF3\xA0\xA0\xB7" => "",                     # U+E0837 => 
"\xF3\xA0\xA0\xB8" => "",                     # U+E0838 => 
"\xF3\xA0\xA0\xB9" => "",                     # U+E0839 => 
"\xF3\xA0\xA0\xBA" => "",                     # U+E083A => 
"\xF3\xA0\xA0\xBB" => "",                     # U+E083B => 
"\xF3\xA0\xA0\xBC" => "",                     # U+E083C => 
"\xF3\xA0\xA0\xBD" => "",                     # U+E083D => 
"\xF3\xA0\xA0\xBE" => "",                     # U+E083E => 
"\xF3\xA0\xA0\xBF" => "",                     # U+E083F => 
"\xF3\xA0\xA1\x80" => "",                     # U+E0840 => 
"\xF3\xA0\xA1\x81" => "",                     # U+E0841 => 
"\xF3\xA0\xA1\x82" => "",                     # U+E0842 => 
"\xF3\xA0\xA1\x83" => "",                     # U+E0843 => 
"\xF3\xA0\xA1\x84" => "",                     # U+E0844 => 
"\xF3\xA0\xA1\x85" => "",                     # U+E0845 => 
"\xF3\xA0\xA1\x86" => "",                     # U+E0846 => 
"\xF3\xA0\xA1\x87" => "",                     # U+E0847 => 
"\xF3\xA0\xA1\x88" => "",                     # U+E0848 => 
"\xF3\xA0\xA1\x89" => "",                     # U+E0849 => 
"\xF3\xA0\xA1\x8A" => "",                     # U+E084A => 
"\xF3\xA0\xA1\x8B" => "",                     # U+E084B => 
"\xF3\xA0\xA1\x8C" => "",                     # U+E084C => 
"\xF3\xA0\xA1\x8D" => "",                     # U+E084D => 
"\xF3\xA0\xA1\x8E" => "",                     # U+E084E => 
"\xF3\xA0\xA1\x8F" => "",                     # U+E084F => 
"\xF3\xA0\xA1\x90" => "",                     # U+E0850 => 
"\xF3\xA0\xA1\x91" => "",                     # U+E0851 => 
"\xF3\xA0\xA1\x92" => "",                     # U+E0852 => 
"\xF3\xA0\xA1\x93" => "",                     # U+E0853 => 
"\xF3\xA0\xA1\x94" => "",                     # U+E0854 => 
"\xF3\xA0\xA1\x95" => "",                     # U+E0855 => 
"\xF3\xA0\xA1\x96" => "",                     # U+E0856 => 
"\xF3\xA0\xA1\x97" => "",                     # U+E0857 => 
"\xF3\xA0\xA1\x98" => "",                     # U+E0858 => 
"\xF3\xA0\xA1\x99" => "",                     # U+E0859 => 
"\xF3\xA0\xA1\x9A" => "",                     # U+E085A => 
"\xF3\xA0\xA1\x9B" => "",                     # U+E085B => 
"\xF3\xA0\xA1\x9C" => "",                     # U+E085C => 
"\xF3\xA0\xA1\x9D" => "",                     # U+E085D => 
"\xF3\xA0\xA1\x9E" => "",                     # U+E085E => 
"\xF3\xA0\xA1\x9F" => "",                     # U+E085F => 
"\xF3\xA0\xA1\xA0" => "",                     # U+E0860 => 
"\xF3\xA0\xA1\xA1" => "",                     # U+E0861 => 
"\xF3\xA0\xA1\xA2" => "",                     # U+E0862 => 
"\xF3\xA0\xA1\xA3" => "",                     # U+E0863 => 
"\xF3\xA0\xA1\xA4" => "",                     # U+E0864 => 
"\xF3\xA0\xA1\xA5" => "",                     # U+E0865 => 
"\xF3\xA0\xA1\xA6" => "",                     # U+E0866 => 
"\xF3\xA0\xA1\xA7" => "",                     # U+E0867 => 
"\xF3\xA0\xA1\xA8" => "",                     # U+E0868 => 
"\xF3\xA0\xA1\xA9" => "",                     # U+E0869 => 
"\xF3\xA0\xA1\xAA" => "",                     # U+E086A => 
"\xF3\xA0\xA1\xAB" => "",                     # U+E086B => 
"\xF3\xA0\xA1\xAC" => "",                     # U+E086C => 
"\xF3\xA0\xA1\xAD" => "",                     # U+E086D => 
"\xF3\xA0\xA1\xAE" => "",                     # U+E086E => 
"\xF3\xA0\xA1\xAF" => "",                     # U+E086F => 
"\xF3\xA0\xA1\xB0" => "",                     # U+E0870 => 
"\xF3\xA0\xA1\xB1" => "",                     # U+E0871 => 
"\xF3\xA0\xA1\xB2" => "",                     # U+E0872 => 
"\xF3\xA0\xA1\xB3" => "",                     # U+E0873 => 
"\xF3\xA0\xA1\xB4" => "",                     # U+E0874 => 
"\xF3\xA0\xA1\xB5" => "",                     # U+E0875 => 
"\xF3\xA0\xA1\xB6" => "",                     # U+E0876 => 
"\xF3\xA0\xA1\xB7" => "",                     # U+E0877 => 
"\xF3\xA0\xA1\xB8" => "",                     # U+E0878 => 
"\xF3\xA0\xA1\xB9" => "",                     # U+E0879 => 
"\xF3\xA0\xA1\xBA" => "",                     # U+E087A => 
"\xF3\xA0\xA1\xBB" => "",                     # U+E087B => 
"\xF3\xA0\xA1\xBC" => "",                     # U+E087C => 
"\xF3\xA0\xA1\xBD" => "",                     # U+E087D => 
"\xF3\xA0\xA1\xBE" => "",                     # U+E087E => 
"\xF3\xA0\xA1\xBF" => "",                     # U+E087F => 
"\xF3\xA0\xA2\x80" => "",                     # U+E0880 => 
"\xF3\xA0\xA2\x81" => "",                     # U+E0881 => 
"\xF3\xA0\xA2\x82" => "",                     # U+E0882 => 
"\xF3\xA0\xA2\x83" => "",                     # U+E0883 => 
"\xF3\xA0\xA2\x84" => "",                     # U+E0884 => 
"\xF3\xA0\xA2\x85" => "",                     # U+E0885 => 
"\xF3\xA0\xA2\x86" => "",                     # U+E0886 => 
"\xF3\xA0\xA2\x87" => "",                     # U+E0887 => 
"\xF3\xA0\xA2\x88" => "",                     # U+E0888 => 
"\xF3\xA0\xA2\x89" => "",                     # U+E0889 => 
"\xF3\xA0\xA2\x8A" => "",                     # U+E088A => 
"\xF3\xA0\xA2\x8B" => "",                     # U+E088B => 
"\xF3\xA0\xA2\x8C" => "",                     # U+E088C => 
"\xF3\xA0\xA2\x8D" => "",                     # U+E088D => 
"\xF3\xA0\xA2\x8E" => "",                     # U+E088E => 
"\xF3\xA0\xA2\x8F" => "",                     # U+E088F => 
"\xF3\xA0\xA2\x90" => "",                     # U+E0890 => 
"\xF3\xA0\xA2\x91" => "",                     # U+E0891 => 
"\xF3\xA0\xA2\x92" => "",                     # U+E0892 => 
"\xF3\xA0\xA2\x93" => "",                     # U+E0893 => 
"\xF3\xA0\xA2\x94" => "",                     # U+E0894 => 
"\xF3\xA0\xA2\x95" => "",                     # U+E0895 => 
"\xF3\xA0\xA2\x96" => "",                     # U+E0896 => 
"\xF3\xA0\xA2\x97" => "",                     # U+E0897 => 
"\xF3\xA0\xA2\x98" => "",                     # U+E0898 => 
"\xF3\xA0\xA2\x99" => "",                     # U+E0899 => 
"\xF3\xA0\xA2\x9A" => "",                     # U+E089A => 
"\xF3\xA0\xA2\x9B" => "",                     # U+E089B => 
"\xF3\xA0\xA2\x9C" => "",                     # U+E089C => 
"\xF3\xA0\xA2\x9D" => "",                     # U+E089D => 
"\xF3\xA0\xA2\x9E" => "",                     # U+E089E => 
"\xF3\xA0\xA2\x9F" => "",                     # U+E089F => 
"\xF3\xA0\xA2\xA0" => "",                     # U+E08A0 => 
"\xF3\xA0\xA2\xA1" => "",                     # U+E08A1 => 
"\xF3\xA0\xA2\xA2" => "",                     # U+E08A2 => 
"\xF3\xA0\xA2\xA3" => "",                     # U+E08A3 => 
"\xF3\xA0\xA2\xA4" => "",                     # U+E08A4 => 
"\xF3\xA0\xA2\xA5" => "",                     # U+E08A5 => 
"\xF3\xA0\xA2\xA6" => "",                     # U+E08A6 => 
"\xF3\xA0\xA2\xA7" => "",                     # U+E08A7 => 
"\xF3\xA0\xA2\xA8" => "",                     # U+E08A8 => 
"\xF3\xA0\xA2\xA9" => "",                     # U+E08A9 => 
"\xF3\xA0\xA2\xAA" => "",                     # U+E08AA => 
"\xF3\xA0\xA2\xAB" => "",                     # U+E08AB => 
"\xF3\xA0\xA2\xAC" => "",                     # U+E08AC => 
"\xF3\xA0\xA2\xAD" => "",                     # U+E08AD => 
"\xF3\xA0\xA2\xAE" => "",                     # U+E08AE => 
"\xF3\xA0\xA2\xAF" => "",                     # U+E08AF => 
"\xF3\xA0\xA2\xB0" => "",                     # U+E08B0 => 
"\xF3\xA0\xA2\xB1" => "",                     # U+E08B1 => 
"\xF3\xA0\xA2\xB2" => "",                     # U+E08B2 => 
"\xF3\xA0\xA2\xB3" => "",                     # U+E08B3 => 
"\xF3\xA0\xA2\xB4" => "",                     # U+E08B4 => 
"\xF3\xA0\xA2\xB5" => "",                     # U+E08B5 => 
"\xF3\xA0\xA2\xB6" => "",                     # U+E08B6 => 
"\xF3\xA0\xA2\xB7" => "",                     # U+E08B7 => 
"\xF3\xA0\xA2\xB8" => "",                     # U+E08B8 => 
"\xF3\xA0\xA2\xB9" => "",                     # U+E08B9 => 
"\xF3\xA0\xA2\xBA" => "",                     # U+E08BA => 
"\xF3\xA0\xA2\xBB" => "",                     # U+E08BB => 
"\xF3\xA0\xA2\xBC" => "",                     # U+E08BC => 
"\xF3\xA0\xA2\xBD" => "",                     # U+E08BD => 
"\xF3\xA0\xA2\xBE" => "",                     # U+E08BE => 
"\xF3\xA0\xA2\xBF" => "",                     # U+E08BF => 
"\xF3\xA0\xA3\x80" => "",                     # U+E08C0 => 
"\xF3\xA0\xA3\x81" => "",                     # U+E08C1 => 
"\xF3\xA0\xA3\x82" => "",                     # U+E08C2 => 
"\xF3\xA0\xA3\x83" => "",                     # U+E08C3 => 
"\xF3\xA0\xA3\x84" => "",                     # U+E08C4 => 
"\xF3\xA0\xA3\x85" => "",                     # U+E08C5 => 
"\xF3\xA0\xA3\x86" => "",                     # U+E08C6 => 
"\xF3\xA0\xA3\x87" => "",                     # U+E08C7 => 
"\xF3\xA0\xA3\x88" => "",                     # U+E08C8 => 
"\xF3\xA0\xA3\x89" => "",                     # U+E08C9 => 
"\xF3\xA0\xA3\x8A" => "",                     # U+E08CA => 
"\xF3\xA0\xA3\x8B" => "",                     # U+E08CB => 
"\xF3\xA0\xA3\x8C" => "",                     # U+E08CC => 
"\xF3\xA0\xA3\x8D" => "",                     # U+E08CD => 
"\xF3\xA0\xA3\x8E" => "",                     # U+E08CE => 
"\xF3\xA0\xA3\x8F" => "",                     # U+E08CF => 
"\xF3\xA0\xA3\x90" => "",                     # U+E08D0 => 
"\xF3\xA0\xA3\x91" => "",                     # U+E08D1 => 
"\xF3\xA0\xA3\x92" => "",                     # U+E08D2 => 
"\xF3\xA0\xA3\x93" => "",                     # U+E08D3 => 
"\xF3\xA0\xA3\x94" => "",                     # U+E08D4 => 
"\xF3\xA0\xA3\x95" => "",                     # U+E08D5 => 
"\xF3\xA0\xA3\x96" => "",                     # U+E08D6 => 
"\xF3\xA0\xA3\x97" => "",                     # U+E08D7 => 
"\xF3\xA0\xA3\x98" => "",                     # U+E08D8 => 
"\xF3\xA0\xA3\x99" => "",                     # U+E08D9 => 
"\xF3\xA0\xA3\x9A" => "",                     # U+E08DA => 
"\xF3\xA0\xA3\x9B" => "",                     # U+E08DB => 
"\xF3\xA0\xA3\x9C" => "",                     # U+E08DC => 
"\xF3\xA0\xA3\x9D" => "",                     # U+E08DD => 
"\xF3\xA0\xA3\x9E" => "",                     # U+E08DE => 
"\xF3\xA0\xA3\x9F" => "",                     # U+E08DF => 
"\xF3\xA0\xA3\xA0" => "",                     # U+E08E0 => 
"\xF3\xA0\xA3\xA1" => "",                     # U+E08E1 => 
"\xF3\xA0\xA3\xA2" => "",                     # U+E08E2 => 
"\xF3\xA0\xA3\xA3" => "",                     # U+E08E3 => 
"\xF3\xA0\xA3\xA4" => "",                     # U+E08E4 => 
"\xF3\xA0\xA3\xA5" => "",                     # U+E08E5 => 
"\xF3\xA0\xA3\xA6" => "",                     # U+E08E6 => 
"\xF3\xA0\xA3\xA7" => "",                     # U+E08E7 => 
"\xF3\xA0\xA3\xA8" => "",                     # U+E08E8 => 
"\xF3\xA0\xA3\xA9" => "",                     # U+E08E9 => 
"\xF3\xA0\xA3\xAA" => "",                     # U+E08EA => 
"\xF3\xA0\xA3\xAB" => "",                     # U+E08EB => 
"\xF3\xA0\xA3\xAC" => "",                     # U+E08EC => 
"\xF3\xA0\xA3\xAD" => "",                     # U+E08ED => 
"\xF3\xA0\xA3\xAE" => "",                     # U+E08EE => 
"\xF3\xA0\xA3\xAF" => "",                     # U+E08EF => 
"\xF3\xA0\xA3\xB0" => "",                     # U+E08F0 => 
"\xF3\xA0\xA3\xB1" => "",                     # U+E08F1 => 
"\xF3\xA0\xA3\xB2" => "",                     # U+E08F2 => 
"\xF3\xA0\xA3\xB3" => "",                     # U+E08F3 => 
"\xF3\xA0\xA3\xB4" => "",                     # U+E08F4 => 
"\xF3\xA0\xA3\xB5" => "",                     # U+E08F5 => 
"\xF3\xA0\xA3\xB6" => "",                     # U+E08F6 => 
"\xF3\xA0\xA3\xB7" => "",                     # U+E08F7 => 
"\xF3\xA0\xA3\xB8" => "",                     # U+E08F8 => 
"\xF3\xA0\xA3\xB9" => "",                     # U+E08F9 => 
"\xF3\xA0\xA3\xBA" => "",                     # U+E08FA => 
"\xF3\xA0\xA3\xBB" => "",                     # U+E08FB => 
"\xF3\xA0\xA3\xBC" => "",                     # U+E08FC => 
"\xF3\xA0\xA3\xBD" => "",                     # U+E08FD => 
"\xF3\xA0\xA3\xBE" => "",                     # U+E08FE => 
"\xF3\xA0\xA3\xBF" => "",                     # U+E08FF => 
"\xF3\xA0\xA4\x80" => "",                     # U+E0900 => 
"\xF3\xA0\xA4\x81" => "",                     # U+E0901 => 
"\xF3\xA0\xA4\x82" => "",                     # U+E0902 => 
"\xF3\xA0\xA4\x83" => "",                     # U+E0903 => 
"\xF3\xA0\xA4\x84" => "",                     # U+E0904 => 
"\xF3\xA0\xA4\x85" => "",                     # U+E0905 => 
"\xF3\xA0\xA4\x86" => "",                     # U+E0906 => 
"\xF3\xA0\xA4\x87" => "",                     # U+E0907 => 
"\xF3\xA0\xA4\x88" => "",                     # U+E0908 => 
"\xF3\xA0\xA4\x89" => "",                     # U+E0909 => 
"\xF3\xA0\xA4\x8A" => "",                     # U+E090A => 
"\xF3\xA0\xA4\x8B" => "",                     # U+E090B => 
"\xF3\xA0\xA4\x8C" => "",                     # U+E090C => 
"\xF3\xA0\xA4\x8D" => "",                     # U+E090D => 
"\xF3\xA0\xA4\x8E" => "",                     # U+E090E => 
"\xF3\xA0\xA4\x8F" => "",                     # U+E090F => 
"\xF3\xA0\xA4\x90" => "",                     # U+E0910 => 
"\xF3\xA0\xA4\x91" => "",                     # U+E0911 => 
"\xF3\xA0\xA4\x92" => "",                     # U+E0912 => 
"\xF3\xA0\xA4\x93" => "",                     # U+E0913 => 
"\xF3\xA0\xA4\x94" => "",                     # U+E0914 => 
"\xF3\xA0\xA4\x95" => "",                     # U+E0915 => 
"\xF3\xA0\xA4\x96" => "",                     # U+E0916 => 
"\xF3\xA0\xA4\x97" => "",                     # U+E0917 => 
"\xF3\xA0\xA4\x98" => "",                     # U+E0918 => 
"\xF3\xA0\xA4\x99" => "",                     # U+E0919 => 
"\xF3\xA0\xA4\x9A" => "",                     # U+E091A => 
"\xF3\xA0\xA4\x9B" => "",                     # U+E091B => 
"\xF3\xA0\xA4\x9C" => "",                     # U+E091C => 
"\xF3\xA0\xA4\x9D" => "",                     # U+E091D => 
"\xF3\xA0\xA4\x9E" => "",                     # U+E091E => 
"\xF3\xA0\xA4\x9F" => "",                     # U+E091F => 
"\xF3\xA0\xA4\xA0" => "",                     # U+E0920 => 
"\xF3\xA0\xA4\xA1" => "",                     # U+E0921 => 
"\xF3\xA0\xA4\xA2" => "",                     # U+E0922 => 
"\xF3\xA0\xA4\xA3" => "",                     # U+E0923 => 
"\xF3\xA0\xA4\xA4" => "",                     # U+E0924 => 
"\xF3\xA0\xA4\xA5" => "",                     # U+E0925 => 
"\xF3\xA0\xA4\xA6" => "",                     # U+E0926 => 
"\xF3\xA0\xA4\xA7" => "",                     # U+E0927 => 
"\xF3\xA0\xA4\xA8" => "",                     # U+E0928 => 
"\xF3\xA0\xA4\xA9" => "",                     # U+E0929 => 
"\xF3\xA0\xA4\xAA" => "",                     # U+E092A => 
"\xF3\xA0\xA4\xAB" => "",                     # U+E092B => 
"\xF3\xA0\xA4\xAC" => "",                     # U+E092C => 
"\xF3\xA0\xA4\xAD" => "",                     # U+E092D => 
"\xF3\xA0\xA4\xAE" => "",                     # U+E092E => 
"\xF3\xA0\xA4\xAF" => "",                     # U+E092F => 
"\xF3\xA0\xA4\xB0" => "",                     # U+E0930 => 
"\xF3\xA0\xA4\xB1" => "",                     # U+E0931 => 
"\xF3\xA0\xA4\xB2" => "",                     # U+E0932 => 
"\xF3\xA0\xA4\xB3" => "",                     # U+E0933 => 
"\xF3\xA0\xA4\xB4" => "",                     # U+E0934 => 
"\xF3\xA0\xA4\xB5" => "",                     # U+E0935 => 
"\xF3\xA0\xA4\xB6" => "",                     # U+E0936 => 
"\xF3\xA0\xA4\xB7" => "",                     # U+E0937 => 
"\xF3\xA0\xA4\xB8" => "",                     # U+E0938 => 
"\xF3\xA0\xA4\xB9" => "",                     # U+E0939 => 
"\xF3\xA0\xA4\xBA" => "",                     # U+E093A => 
"\xF3\xA0\xA4\xBB" => "",                     # U+E093B => 
"\xF3\xA0\xA4\xBC" => "",                     # U+E093C => 
"\xF3\xA0\xA4\xBD" => "",                     # U+E093D => 
"\xF3\xA0\xA4\xBE" => "",                     # U+E093E => 
"\xF3\xA0\xA4\xBF" => "",                     # U+E093F => 
"\xF3\xA0\xA5\x80" => "",                     # U+E0940 => 
"\xF3\xA0\xA5\x81" => "",                     # U+E0941 => 
"\xF3\xA0\xA5\x82" => "",                     # U+E0942 => 
"\xF3\xA0\xA5\x83" => "",                     # U+E0943 => 
"\xF3\xA0\xA5\x84" => "",                     # U+E0944 => 
"\xF3\xA0\xA5\x85" => "",                     # U+E0945 => 
"\xF3\xA0\xA5\x86" => "",                     # U+E0946 => 
"\xF3\xA0\xA5\x87" => "",                     # U+E0947 => 
"\xF3\xA0\xA5\x88" => "",                     # U+E0948 => 
"\xF3\xA0\xA5\x89" => "",                     # U+E0949 => 
"\xF3\xA0\xA5\x8A" => "",                     # U+E094A => 
"\xF3\xA0\xA5\x8B" => "",                     # U+E094B => 
"\xF3\xA0\xA5\x8C" => "",                     # U+E094C => 
"\xF3\xA0\xA5\x8D" => "",                     # U+E094D => 
"\xF3\xA0\xA5\x8E" => "",                     # U+E094E => 
"\xF3\xA0\xA5\x8F" => "",                     # U+E094F => 
"\xF3\xA0\xA5\x90" => "",                     # U+E0950 => 
"\xF3\xA0\xA5\x91" => "",                     # U+E0951 => 
"\xF3\xA0\xA5\x92" => "",                     # U+E0952 => 
"\xF3\xA0\xA5\x93" => "",                     # U+E0953 => 
"\xF3\xA0\xA5\x94" => "",                     # U+E0954 => 
"\xF3\xA0\xA5\x95" => "",                     # U+E0955 => 
"\xF3\xA0\xA5\x96" => "",                     # U+E0956 => 
"\xF3\xA0\xA5\x97" => "",                     # U+E0957 => 
"\xF3\xA0\xA5\x98" => "",                     # U+E0958 => 
"\xF3\xA0\xA5\x99" => "",                     # U+E0959 => 
"\xF3\xA0\xA5\x9A" => "",                     # U+E095A => 
"\xF3\xA0\xA5\x9B" => "",                     # U+E095B => 
"\xF3\xA0\xA5\x9C" => "",                     # U+E095C => 
"\xF3\xA0\xA5\x9D" => "",                     # U+E095D => 
"\xF3\xA0\xA5\x9E" => "",                     # U+E095E => 
"\xF3\xA0\xA5\x9F" => "",                     # U+E095F => 
"\xF3\xA0\xA5\xA0" => "",                     # U+E0960 => 
"\xF3\xA0\xA5\xA1" => "",                     # U+E0961 => 
"\xF3\xA0\xA5\xA2" => "",                     # U+E0962 => 
"\xF3\xA0\xA5\xA3" => "",                     # U+E0963 => 
"\xF3\xA0\xA5\xA4" => "",                     # U+E0964 => 
"\xF3\xA0\xA5\xA5" => "",                     # U+E0965 => 
"\xF3\xA0\xA5\xA6" => "",                     # U+E0966 => 
"\xF3\xA0\xA5\xA7" => "",                     # U+E0967 => 
"\xF3\xA0\xA5\xA8" => "",                     # U+E0968 => 
"\xF3\xA0\xA5\xA9" => "",                     # U+E0969 => 
"\xF3\xA0\xA5\xAA" => "",                     # U+E096A => 
"\xF3\xA0\xA5\xAB" => "",                     # U+E096B => 
"\xF3\xA0\xA5\xAC" => "",                     # U+E096C => 
"\xF3\xA0\xA5\xAD" => "",                     # U+E096D => 
"\xF3\xA0\xA5\xAE" => "",                     # U+E096E => 
"\xF3\xA0\xA5\xAF" => "",                     # U+E096F => 
"\xF3\xA0\xA5\xB0" => "",                     # U+E0970 => 
"\xF3\xA0\xA5\xB1" => "",                     # U+E0971 => 
"\xF3\xA0\xA5\xB2" => "",                     # U+E0972 => 
"\xF3\xA0\xA5\xB3" => "",                     # U+E0973 => 
"\xF3\xA0\xA5\xB4" => "",                     # U+E0974 => 
"\xF3\xA0\xA5\xB5" => "",                     # U+E0975 => 
"\xF3\xA0\xA5\xB6" => "",                     # U+E0976 => 
"\xF3\xA0\xA5\xB7" => "",                     # U+E0977 => 
"\xF3\xA0\xA5\xB8" => "",                     # U+E0978 => 
"\xF3\xA0\xA5\xB9" => "",                     # U+E0979 => 
"\xF3\xA0\xA5\xBA" => "",                     # U+E097A => 
"\xF3\xA0\xA5\xBB" => "",                     # U+E097B => 
"\xF3\xA0\xA5\xBC" => "",                     # U+E097C => 
"\xF3\xA0\xA5\xBD" => "",                     # U+E097D => 
"\xF3\xA0\xA5\xBE" => "",                     # U+E097E => 
"\xF3\xA0\xA5\xBF" => "",                     # U+E097F => 
"\xF3\xA0\xA6\x80" => "",                     # U+E0980 => 
"\xF3\xA0\xA6\x81" => "",                     # U+E0981 => 
"\xF3\xA0\xA6\x82" => "",                     # U+E0982 => 
"\xF3\xA0\xA6\x83" => "",                     # U+E0983 => 
"\xF3\xA0\xA6\x84" => "",                     # U+E0984 => 
"\xF3\xA0\xA6\x85" => "",                     # U+E0985 => 
"\xF3\xA0\xA6\x86" => "",                     # U+E0986 => 
"\xF3\xA0\xA6\x87" => "",                     # U+E0987 => 
"\xF3\xA0\xA6\x88" => "",                     # U+E0988 => 
"\xF3\xA0\xA6\x89" => "",                     # U+E0989 => 
"\xF3\xA0\xA6\x8A" => "",                     # U+E098A => 
"\xF3\xA0\xA6\x8B" => "",                     # U+E098B => 
"\xF3\xA0\xA6\x8C" => "",                     # U+E098C => 
"\xF3\xA0\xA6\x8D" => "",                     # U+E098D => 
"\xF3\xA0\xA6\x8E" => "",                     # U+E098E => 
"\xF3\xA0\xA6\x8F" => "",                     # U+E098F => 
"\xF3\xA0\xA6\x90" => "",                     # U+E0990 => 
"\xF3\xA0\xA6\x91" => "",                     # U+E0991 => 
"\xF3\xA0\xA6\x92" => "",                     # U+E0992 => 
"\xF3\xA0\xA6\x93" => "",                     # U+E0993 => 
"\xF3\xA0\xA6\x94" => "",                     # U+E0994 => 
"\xF3\xA0\xA6\x95" => "",                     # U+E0995 => 
"\xF3\xA0\xA6\x96" => "",                     # U+E0996 => 
"\xF3\xA0\xA6\x97" => "",                     # U+E0997 => 
"\xF3\xA0\xA6\x98" => "",                     # U+E0998 => 
"\xF3\xA0\xA6\x99" => "",                     # U+E0999 => 
"\xF3\xA0\xA6\x9A" => "",                     # U+E099A => 
"\xF3\xA0\xA6\x9B" => "",                     # U+E099B => 
"\xF3\xA0\xA6\x9C" => "",                     # U+E099C => 
"\xF3\xA0\xA6\x9D" => "",                     # U+E099D => 
"\xF3\xA0\xA6\x9E" => "",                     # U+E099E => 
"\xF3\xA0\xA6\x9F" => "",                     # U+E099F => 
"\xF3\xA0\xA6\xA0" => "",                     # U+E09A0 => 
"\xF3\xA0\xA6\xA1" => "",                     # U+E09A1 => 
"\xF3\xA0\xA6\xA2" => "",                     # U+E09A2 => 
"\xF3\xA0\xA6\xA3" => "",                     # U+E09A3 => 
"\xF3\xA0\xA6\xA4" => "",                     # U+E09A4 => 
"\xF3\xA0\xA6\xA5" => "",                     # U+E09A5 => 
"\xF3\xA0\xA6\xA6" => "",                     # U+E09A6 => 
"\xF3\xA0\xA6\xA7" => "",                     # U+E09A7 => 
"\xF3\xA0\xA6\xA8" => "",                     # U+E09A8 => 
"\xF3\xA0\xA6\xA9" => "",                     # U+E09A9 => 
"\xF3\xA0\xA6\xAA" => "",                     # U+E09AA => 
"\xF3\xA0\xA6\xAB" => "",                     # U+E09AB => 
"\xF3\xA0\xA6\xAC" => "",                     # U+E09AC => 
"\xF3\xA0\xA6\xAD" => "",                     # U+E09AD => 
"\xF3\xA0\xA6\xAE" => "",                     # U+E09AE => 
"\xF3\xA0\xA6\xAF" => "",                     # U+E09AF => 
"\xF3\xA0\xA6\xB0" => "",                     # U+E09B0 => 
"\xF3\xA0\xA6\xB1" => "",                     # U+E09B1 => 
"\xF3\xA0\xA6\xB2" => "",                     # U+E09B2 => 
"\xF3\xA0\xA6\xB3" => "",                     # U+E09B3 => 
"\xF3\xA0\xA6\xB4" => "",                     # U+E09B4 => 
"\xF3\xA0\xA6\xB5" => "",                     # U+E09B5 => 
"\xF3\xA0\xA6\xB6" => "",                     # U+E09B6 => 
"\xF3\xA0\xA6\xB7" => "",                     # U+E09B7 => 
"\xF3\xA0\xA6\xB8" => "",                     # U+E09B8 => 
"\xF3\xA0\xA6\xB9" => "",                     # U+E09B9 => 
"\xF3\xA0\xA6\xBA" => "",                     # U+E09BA => 
"\xF3\xA0\xA6\xBB" => "",                     # U+E09BB => 
"\xF3\xA0\xA6\xBC" => "",                     # U+E09BC => 
"\xF3\xA0\xA6\xBD" => "",                     # U+E09BD => 
"\xF3\xA0\xA6\xBE" => "",                     # U+E09BE => 
"\xF3\xA0\xA6\xBF" => "",                     # U+E09BF => 
"\xF3\xA0\xA7\x80" => "",                     # U+E09C0 => 
"\xF3\xA0\xA7\x81" => "",                     # U+E09C1 => 
"\xF3\xA0\xA7\x82" => "",                     # U+E09C2 => 
"\xF3\xA0\xA7\x83" => "",                     # U+E09C3 => 
"\xF3\xA0\xA7\x84" => "",                     # U+E09C4 => 
"\xF3\xA0\xA7\x85" => "",                     # U+E09C5 => 
"\xF3\xA0\xA7\x86" => "",                     # U+E09C6 => 
"\xF3\xA0\xA7\x87" => "",                     # U+E09C7 => 
"\xF3\xA0\xA7\x88" => "",                     # U+E09C8 => 
"\xF3\xA0\xA7\x89" => "",                     # U+E09C9 => 
"\xF3\xA0\xA7\x8A" => "",                     # U+E09CA => 
"\xF3\xA0\xA7\x8B" => "",                     # U+E09CB => 
"\xF3\xA0\xA7\x8C" => "",                     # U+E09CC => 
"\xF3\xA0\xA7\x8D" => "",                     # U+E09CD => 
"\xF3\xA0\xA7\x8E" => "",                     # U+E09CE => 
"\xF3\xA0\xA7\x8F" => "",                     # U+E09CF => 
"\xF3\xA0\xA7\x90" => "",                     # U+E09D0 => 
"\xF3\xA0\xA7\x91" => "",                     # U+E09D1 => 
"\xF3\xA0\xA7\x92" => "",                     # U+E09D2 => 
"\xF3\xA0\xA7\x93" => "",                     # U+E09D3 => 
"\xF3\xA0\xA7\x94" => "",                     # U+E09D4 => 
"\xF3\xA0\xA7\x95" => "",                     # U+E09D5 => 
"\xF3\xA0\xA7\x96" => "",                     # U+E09D6 => 
"\xF3\xA0\xA7\x97" => "",                     # U+E09D7 => 
"\xF3\xA0\xA7\x98" => "",                     # U+E09D8 => 
"\xF3\xA0\xA7\x99" => "",                     # U+E09D9 => 
"\xF3\xA0\xA7\x9A" => "",                     # U+E09DA => 
"\xF3\xA0\xA7\x9B" => "",                     # U+E09DB => 
"\xF3\xA0\xA7\x9C" => "",                     # U+E09DC => 
"\xF3\xA0\xA7\x9D" => "",                     # U+E09DD => 
"\xF3\xA0\xA7\x9E" => "",                     # U+E09DE => 
"\xF3\xA0\xA7\x9F" => "",                     # U+E09DF => 
"\xF3\xA0\xA7\xA0" => "",                     # U+E09E0 => 
"\xF3\xA0\xA7\xA1" => "",                     # U+E09E1 => 
"\xF3\xA0\xA7\xA2" => "",                     # U+E09E2 => 
"\xF3\xA0\xA7\xA3" => "",                     # U+E09E3 => 
"\xF3\xA0\xA7\xA4" => "",                     # U+E09E4 => 
"\xF3\xA0\xA7\xA5" => "",                     # U+E09E5 => 
"\xF3\xA0\xA7\xA6" => "",                     # U+E09E6 => 
"\xF3\xA0\xA7\xA7" => "",                     # U+E09E7 => 
"\xF3\xA0\xA7\xA8" => "",                     # U+E09E8 => 
"\xF3\xA0\xA7\xA9" => "",                     # U+E09E9 => 
"\xF3\xA0\xA7\xAA" => "",                     # U+E09EA => 
"\xF3\xA0\xA7\xAB" => "",                     # U+E09EB => 
"\xF3\xA0\xA7\xAC" => "",                     # U+E09EC => 
"\xF3\xA0\xA7\xAD" => "",                     # U+E09ED => 
"\xF3\xA0\xA7\xAE" => "",                     # U+E09EE => 
"\xF3\xA0\xA7\xAF" => "",                     # U+E09EF => 
"\xF3\xA0\xA7\xB0" => "",                     # U+E09F0 => 
"\xF3\xA0\xA7\xB1" => "",                     # U+E09F1 => 
"\xF3\xA0\xA7\xB2" => "",                     # U+E09F2 => 
"\xF3\xA0\xA7\xB3" => "",                     # U+E09F3 => 
"\xF3\xA0\xA7\xB4" => "",                     # U+E09F4 => 
"\xF3\xA0\xA7\xB5" => "",                     # U+E09F5 => 
"\xF3\xA0\xA7\xB6" => "",                     # U+E09F6 => 
"\xF3\xA0\xA7\xB7" => "",                     # U+E09F7 => 
"\xF3\xA0\xA7\xB8" => "",                     # U+E09F8 => 
"\xF3\xA0\xA7\xB9" => "",                     # U+E09F9 => 
"\xF3\xA0\xA7\xBA" => "",                     # U+E09FA => 
"\xF3\xA0\xA7\xBB" => "",                     # U+E09FB => 
"\xF3\xA0\xA7\xBC" => "",                     # U+E09FC => 
"\xF3\xA0\xA7\xBD" => "",                     # U+E09FD => 
"\xF3\xA0\xA7\xBE" => "",                     # U+E09FE => 
"\xF3\xA0\xA7\xBF" => "",                     # U+E09FF => 
"\xF3\xA0\xA8\x80" => "",                     # U+E0A00 => 
"\xF3\xA0\xA8\x81" => "",                     # U+E0A01 => 
"\xF3\xA0\xA8\x82" => "",                     # U+E0A02 => 
"\xF3\xA0\xA8\x83" => "",                     # U+E0A03 => 
"\xF3\xA0\xA8\x84" => "",                     # U+E0A04 => 
"\xF3\xA0\xA8\x85" => "",                     # U+E0A05 => 
"\xF3\xA0\xA8\x86" => "",                     # U+E0A06 => 
"\xF3\xA0\xA8\x87" => "",                     # U+E0A07 => 
"\xF3\xA0\xA8\x88" => "",                     # U+E0A08 => 
"\xF3\xA0\xA8\x89" => "",                     # U+E0A09 => 
"\xF3\xA0\xA8\x8A" => "",                     # U+E0A0A => 
"\xF3\xA0\xA8\x8B" => "",                     # U+E0A0B => 
"\xF3\xA0\xA8\x8C" => "",                     # U+E0A0C => 
"\xF3\xA0\xA8\x8D" => "",                     # U+E0A0D => 
"\xF3\xA0\xA8\x8E" => "",                     # U+E0A0E => 
"\xF3\xA0\xA8\x8F" => "",                     # U+E0A0F => 
"\xF3\xA0\xA8\x90" => "",                     # U+E0A10 => 
"\xF3\xA0\xA8\x91" => "",                     # U+E0A11 => 
"\xF3\xA0\xA8\x92" => "",                     # U+E0A12 => 
"\xF3\xA0\xA8\x93" => "",                     # U+E0A13 => 
"\xF3\xA0\xA8\x94" => "",                     # U+E0A14 => 
"\xF3\xA0\xA8\x95" => "",                     # U+E0A15 => 
"\xF3\xA0\xA8\x96" => "",                     # U+E0A16 => 
"\xF3\xA0\xA8\x97" => "",                     # U+E0A17 => 
"\xF3\xA0\xA8\x98" => "",                     # U+E0A18 => 
"\xF3\xA0\xA8\x99" => "",                     # U+E0A19 => 
"\xF3\xA0\xA8\x9A" => "",                     # U+E0A1A => 
"\xF3\xA0\xA8\x9B" => "",                     # U+E0A1B => 
"\xF3\xA0\xA8\x9C" => "",                     # U+E0A1C => 
"\xF3\xA0\xA8\x9D" => "",                     # U+E0A1D => 
"\xF3\xA0\xA8\x9E" => "",                     # U+E0A1E => 
"\xF3\xA0\xA8\x9F" => "",                     # U+E0A1F => 
"\xF3\xA0\xA8\xA0" => "",                     # U+E0A20 => 
"\xF3\xA0\xA8\xA1" => "",                     # U+E0A21 => 
"\xF3\xA0\xA8\xA2" => "",                     # U+E0A22 => 
"\xF3\xA0\xA8\xA3" => "",                     # U+E0A23 => 
"\xF3\xA0\xA8\xA4" => "",                     # U+E0A24 => 
"\xF3\xA0\xA8\xA5" => "",                     # U+E0A25 => 
"\xF3\xA0\xA8\xA6" => "",                     # U+E0A26 => 
"\xF3\xA0\xA8\xA7" => "",                     # U+E0A27 => 
"\xF3\xA0\xA8\xA8" => "",                     # U+E0A28 => 
"\xF3\xA0\xA8\xA9" => "",                     # U+E0A29 => 
"\xF3\xA0\xA8\xAA" => "",                     # U+E0A2A => 
"\xF3\xA0\xA8\xAB" => "",                     # U+E0A2B => 
"\xF3\xA0\xA8\xAC" => "",                     # U+E0A2C => 
"\xF3\xA0\xA8\xAD" => "",                     # U+E0A2D => 
"\xF3\xA0\xA8\xAE" => "",                     # U+E0A2E => 
"\xF3\xA0\xA8\xAF" => "",                     # U+E0A2F => 
"\xF3\xA0\xA8\xB0" => "",                     # U+E0A30 => 
"\xF3\xA0\xA8\xB1" => "",                     # U+E0A31 => 
"\xF3\xA0\xA8\xB2" => "",                     # U+E0A32 => 
"\xF3\xA0\xA8\xB3" => "",                     # U+E0A33 => 
"\xF3\xA0\xA8\xB4" => "",                     # U+E0A34 => 
"\xF3\xA0\xA8\xB5" => "",                     # U+E0A35 => 
"\xF3\xA0\xA8\xB6" => "",                     # U+E0A36 => 
"\xF3\xA0\xA8\xB7" => "",                     # U+E0A37 => 
"\xF3\xA0\xA8\xB8" => "",                     # U+E0A38 => 
"\xF3\xA0\xA8\xB9" => "",                     # U+E0A39 => 
"\xF3\xA0\xA8\xBA" => "",                     # U+E0A3A => 
"\xF3\xA0\xA8\xBB" => "",                     # U+E0A3B => 
"\xF3\xA0\xA8\xBC" => "",                     # U+E0A3C => 
"\xF3\xA0\xA8\xBD" => "",                     # U+E0A3D => 
"\xF3\xA0\xA8\xBE" => "",                     # U+E0A3E => 
"\xF3\xA0\xA8\xBF" => "",                     # U+E0A3F => 
"\xF3\xA0\xA9\x80" => "",                     # U+E0A40 => 
"\xF3\xA0\xA9\x81" => "",                     # U+E0A41 => 
"\xF3\xA0\xA9\x82" => "",                     # U+E0A42 => 
"\xF3\xA0\xA9\x83" => "",                     # U+E0A43 => 
"\xF3\xA0\xA9\x84" => "",                     # U+E0A44 => 
"\xF3\xA0\xA9\x85" => "",                     # U+E0A45 => 
"\xF3\xA0\xA9\x86" => "",                     # U+E0A46 => 
"\xF3\xA0\xA9\x87" => "",                     # U+E0A47 => 
"\xF3\xA0\xA9\x88" => "",                     # U+E0A48 => 
"\xF3\xA0\xA9\x89" => "",                     # U+E0A49 => 
"\xF3\xA0\xA9\x8A" => "",                     # U+E0A4A => 
"\xF3\xA0\xA9\x8B" => "",                     # U+E0A4B => 
"\xF3\xA0\xA9\x8C" => "",                     # U+E0A4C => 
"\xF3\xA0\xA9\x8D" => "",                     # U+E0A4D => 
"\xF3\xA0\xA9\x8E" => "",                     # U+E0A4E => 
"\xF3\xA0\xA9\x8F" => "",                     # U+E0A4F => 
"\xF3\xA0\xA9\x90" => "",                     # U+E0A50 => 
"\xF3\xA0\xA9\x91" => "",                     # U+E0A51 => 
"\xF3\xA0\xA9\x92" => "",                     # U+E0A52 => 
"\xF3\xA0\xA9\x93" => "",                     # U+E0A53 => 
"\xF3\xA0\xA9\x94" => "",                     # U+E0A54 => 
"\xF3\xA0\xA9\x95" => "",                     # U+E0A55 => 
"\xF3\xA0\xA9\x96" => "",                     # U+E0A56 => 
"\xF3\xA0\xA9\x97" => "",                     # U+E0A57 => 
"\xF3\xA0\xA9\x98" => "",                     # U+E0A58 => 
"\xF3\xA0\xA9\x99" => "",                     # U+E0A59 => 
"\xF3\xA0\xA9\x9A" => "",                     # U+E0A5A => 
"\xF3\xA0\xA9\x9B" => "",                     # U+E0A5B => 
"\xF3\xA0\xA9\x9C" => "",                     # U+E0A5C => 
"\xF3\xA0\xA9\x9D" => "",                     # U+E0A5D => 
"\xF3\xA0\xA9\x9E" => "",                     # U+E0A5E => 
"\xF3\xA0\xA9\x9F" => "",                     # U+E0A5F => 
"\xF3\xA0\xA9\xA0" => "",                     # U+E0A60 => 
"\xF3\xA0\xA9\xA1" => "",                     # U+E0A61 => 
"\xF3\xA0\xA9\xA2" => "",                     # U+E0A62 => 
"\xF3\xA0\xA9\xA3" => "",                     # U+E0A63 => 
"\xF3\xA0\xA9\xA4" => "",                     # U+E0A64 => 
"\xF3\xA0\xA9\xA5" => "",                     # U+E0A65 => 
"\xF3\xA0\xA9\xA6" => "",                     # U+E0A66 => 
"\xF3\xA0\xA9\xA7" => "",                     # U+E0A67 => 
"\xF3\xA0\xA9\xA8" => "",                     # U+E0A68 => 
"\xF3\xA0\xA9\xA9" => "",                     # U+E0A69 => 
"\xF3\xA0\xA9\xAA" => "",                     # U+E0A6A => 
"\xF3\xA0\xA9\xAB" => "",                     # U+E0A6B => 
"\xF3\xA0\xA9\xAC" => "",                     # U+E0A6C => 
"\xF3\xA0\xA9\xAD" => "",                     # U+E0A6D => 
"\xF3\xA0\xA9\xAE" => "",                     # U+E0A6E => 
"\xF3\xA0\xA9\xAF" => "",                     # U+E0A6F => 
"\xF3\xA0\xA9\xB0" => "",                     # U+E0A70 => 
"\xF3\xA0\xA9\xB1" => "",                     # U+E0A71 => 
"\xF3\xA0\xA9\xB2" => "",                     # U+E0A72 => 
"\xF3\xA0\xA9\xB3" => "",                     # U+E0A73 => 
"\xF3\xA0\xA9\xB4" => "",                     # U+E0A74 => 
"\xF3\xA0\xA9\xB5" => "",                     # U+E0A75 => 
"\xF3\xA0\xA9\xB6" => "",                     # U+E0A76 => 
"\xF3\xA0\xA9\xB7" => "",                     # U+E0A77 => 
"\xF3\xA0\xA9\xB8" => "",                     # U+E0A78 => 
"\xF3\xA0\xA9\xB9" => "",                     # U+E0A79 => 
"\xF3\xA0\xA9\xBA" => "",                     # U+E0A7A => 
"\xF3\xA0\xA9\xBB" => "",                     # U+E0A7B => 
"\xF3\xA0\xA9\xBC" => "",                     # U+E0A7C => 
"\xF3\xA0\xA9\xBD" => "",                     # U+E0A7D => 
"\xF3\xA0\xA9\xBE" => "",                     # U+E0A7E => 
"\xF3\xA0\xA9\xBF" => "",                     # U+E0A7F => 
"\xF3\xA0\xAA\x80" => "",                     # U+E0A80 => 
"\xF3\xA0\xAA\x81" => "",                     # U+E0A81 => 
"\xF3\xA0\xAA\x82" => "",                     # U+E0A82 => 
"\xF3\xA0\xAA\x83" => "",                     # U+E0A83 => 
"\xF3\xA0\xAA\x84" => "",                     # U+E0A84 => 
"\xF3\xA0\xAA\x85" => "",                     # U+E0A85 => 
"\xF3\xA0\xAA\x86" => "",                     # U+E0A86 => 
"\xF3\xA0\xAA\x87" => "",                     # U+E0A87 => 
"\xF3\xA0\xAA\x88" => "",                     # U+E0A88 => 
"\xF3\xA0\xAA\x89" => "",                     # U+E0A89 => 
"\xF3\xA0\xAA\x8A" => "",                     # U+E0A8A => 
"\xF3\xA0\xAA\x8B" => "",                     # U+E0A8B => 
"\xF3\xA0\xAA\x8C" => "",                     # U+E0A8C => 
"\xF3\xA0\xAA\x8D" => "",                     # U+E0A8D => 
"\xF3\xA0\xAA\x8E" => "",                     # U+E0A8E => 
"\xF3\xA0\xAA\x8F" => "",                     # U+E0A8F => 
"\xF3\xA0\xAA\x90" => "",                     # U+E0A90 => 
"\xF3\xA0\xAA\x91" => "",                     # U+E0A91 => 
"\xF3\xA0\xAA\x92" => "",                     # U+E0A92 => 
"\xF3\xA0\xAA\x93" => "",                     # U+E0A93 => 
"\xF3\xA0\xAA\x94" => "",                     # U+E0A94 => 
"\xF3\xA0\xAA\x95" => "",                     # U+E0A95 => 
"\xF3\xA0\xAA\x96" => "",                     # U+E0A96 => 
"\xF3\xA0\xAA\x97" => "",                     # U+E0A97 => 
"\xF3\xA0\xAA\x98" => "",                     # U+E0A98 => 
"\xF3\xA0\xAA\x99" => "",                     # U+E0A99 => 
"\xF3\xA0\xAA\x9A" => "",                     # U+E0A9A => 
"\xF3\xA0\xAA\x9B" => "",                     # U+E0A9B => 
"\xF3\xA0\xAA\x9C" => "",                     # U+E0A9C => 
"\xF3\xA0\xAA\x9D" => "",                     # U+E0A9D => 
"\xF3\xA0\xAA\x9E" => "",                     # U+E0A9E => 
"\xF3\xA0\xAA\x9F" => "",                     # U+E0A9F => 
"\xF3\xA0\xAA\xA0" => "",                     # U+E0AA0 => 
"\xF3\xA0\xAA\xA1" => "",                     # U+E0AA1 => 
"\xF3\xA0\xAA\xA2" => "",                     # U+E0AA2 => 
"\xF3\xA0\xAA\xA3" => "",                     # U+E0AA3 => 
"\xF3\xA0\xAA\xA4" => "",                     # U+E0AA4 => 
"\xF3\xA0\xAA\xA5" => "",                     # U+E0AA5 => 
"\xF3\xA0\xAA\xA6" => "",                     # U+E0AA6 => 
"\xF3\xA0\xAA\xA7" => "",                     # U+E0AA7 => 
"\xF3\xA0\xAA\xA8" => "",                     # U+E0AA8 => 
"\xF3\xA0\xAA\xA9" => "",                     # U+E0AA9 => 
"\xF3\xA0\xAA\xAA" => "",                     # U+E0AAA => 
"\xF3\xA0\xAA\xAB" => "",                     # U+E0AAB => 
"\xF3\xA0\xAA\xAC" => "",                     # U+E0AAC => 
"\xF3\xA0\xAA\xAD" => "",                     # U+E0AAD => 
"\xF3\xA0\xAA\xAE" => "",                     # U+E0AAE => 
"\xF3\xA0\xAA\xAF" => "",                     # U+E0AAF => 
"\xF3\xA0\xAA\xB0" => "",                     # U+E0AB0 => 
"\xF3\xA0\xAA\xB1" => "",                     # U+E0AB1 => 
"\xF3\xA0\xAA\xB2" => "",                     # U+E0AB2 => 
"\xF3\xA0\xAA\xB3" => "",                     # U+E0AB3 => 
"\xF3\xA0\xAA\xB4" => "",                     # U+E0AB4 => 
"\xF3\xA0\xAA\xB5" => "",                     # U+E0AB5 => 
"\xF3\xA0\xAA\xB6" => "",                     # U+E0AB6 => 
"\xF3\xA0\xAA\xB7" => "",                     # U+E0AB7 => 
"\xF3\xA0\xAA\xB8" => "",                     # U+E0AB8 => 
"\xF3\xA0\xAA\xB9" => "",                     # U+E0AB9 => 
"\xF3\xA0\xAA\xBA" => "",                     # U+E0ABA => 
"\xF3\xA0\xAA\xBB" => "",                     # U+E0ABB => 
"\xF3\xA0\xAA\xBC" => "",                     # U+E0ABC => 
"\xF3\xA0\xAA\xBD" => "",                     # U+E0ABD => 
"\xF3\xA0\xAA\xBE" => "",                     # U+E0ABE => 
"\xF3\xA0\xAA\xBF" => "",                     # U+E0ABF => 
"\xF3\xA0\xAB\x80" => "",                     # U+E0AC0 => 
"\xF3\xA0\xAB\x81" => "",                     # U+E0AC1 => 
"\xF3\xA0\xAB\x82" => "",                     # U+E0AC2 => 
"\xF3\xA0\xAB\x83" => "",                     # U+E0AC3 => 
"\xF3\xA0\xAB\x84" => "",                     # U+E0AC4 => 
"\xF3\xA0\xAB\x85" => "",                     # U+E0AC5 => 
"\xF3\xA0\xAB\x86" => "",                     # U+E0AC6 => 
"\xF3\xA0\xAB\x87" => "",                     # U+E0AC7 => 
"\xF3\xA0\xAB\x88" => "",                     # U+E0AC8 => 
"\xF3\xA0\xAB\x89" => "",                     # U+E0AC9 => 
"\xF3\xA0\xAB\x8A" => "",                     # U+E0ACA => 
"\xF3\xA0\xAB\x8B" => "",                     # U+E0ACB => 
"\xF3\xA0\xAB\x8C" => "",                     # U+E0ACC => 
"\xF3\xA0\xAB\x8D" => "",                     # U+E0ACD => 
"\xF3\xA0\xAB\x8E" => "",                     # U+E0ACE => 
"\xF3\xA0\xAB\x8F" => "",                     # U+E0ACF => 
"\xF3\xA0\xAB\x90" => "",                     # U+E0AD0 => 
"\xF3\xA0\xAB\x91" => "",                     # U+E0AD1 => 
"\xF3\xA0\xAB\x92" => "",                     # U+E0AD2 => 
"\xF3\xA0\xAB\x93" => "",                     # U+E0AD3 => 
"\xF3\xA0\xAB\x94" => "",                     # U+E0AD4 => 
"\xF3\xA0\xAB\x95" => "",                     # U+E0AD5 => 
"\xF3\xA0\xAB\x96" => "",                     # U+E0AD6 => 
"\xF3\xA0\xAB\x97" => "",                     # U+E0AD7 => 
"\xF3\xA0\xAB\x98" => "",                     # U+E0AD8 => 
"\xF3\xA0\xAB\x99" => "",                     # U+E0AD9 => 
"\xF3\xA0\xAB\x9A" => "",                     # U+E0ADA => 
"\xF3\xA0\xAB\x9B" => "",                     # U+E0ADB => 
"\xF3\xA0\xAB\x9C" => "",                     # U+E0ADC => 
"\xF3\xA0\xAB\x9D" => "",                     # U+E0ADD => 
"\xF3\xA0\xAB\x9E" => "",                     # U+E0ADE => 
"\xF3\xA0\xAB\x9F" => "",                     # U+E0ADF => 
"\xF3\xA0\xAB\xA0" => "",                     # U+E0AE0 => 
"\xF3\xA0\xAB\xA1" => "",                     # U+E0AE1 => 
"\xF3\xA0\xAB\xA2" => "",                     # U+E0AE2 => 
"\xF3\xA0\xAB\xA3" => "",                     # U+E0AE3 => 
"\xF3\xA0\xAB\xA4" => "",                     # U+E0AE4 => 
"\xF3\xA0\xAB\xA5" => "",                     # U+E0AE5 => 
"\xF3\xA0\xAB\xA6" => "",                     # U+E0AE6 => 
"\xF3\xA0\xAB\xA7" => "",                     # U+E0AE7 => 
"\xF3\xA0\xAB\xA8" => "",                     # U+E0AE8 => 
"\xF3\xA0\xAB\xA9" => "",                     # U+E0AE9 => 
"\xF3\xA0\xAB\xAA" => "",                     # U+E0AEA => 
"\xF3\xA0\xAB\xAB" => "",                     # U+E0AEB => 
"\xF3\xA0\xAB\xAC" => "",                     # U+E0AEC => 
"\xF3\xA0\xAB\xAD" => "",                     # U+E0AED => 
"\xF3\xA0\xAB\xAE" => "",                     # U+E0AEE => 
"\xF3\xA0\xAB\xAF" => "",                     # U+E0AEF => 
"\xF3\xA0\xAB\xB0" => "",                     # U+E0AF0 => 
"\xF3\xA0\xAB\xB1" => "",                     # U+E0AF1 => 
"\xF3\xA0\xAB\xB2" => "",                     # U+E0AF2 => 
"\xF3\xA0\xAB\xB3" => "",                     # U+E0AF3 => 
"\xF3\xA0\xAB\xB4" => "",                     # U+E0AF4 => 
"\xF3\xA0\xAB\xB5" => "",                     # U+E0AF5 => 
"\xF3\xA0\xAB\xB6" => "",                     # U+E0AF6 => 
"\xF3\xA0\xAB\xB7" => "",                     # U+E0AF7 => 
"\xF3\xA0\xAB\xB8" => "",                     # U+E0AF8 => 
"\xF3\xA0\xAB\xB9" => "",                     # U+E0AF9 => 
"\xF3\xA0\xAB\xBA" => "",                     # U+E0AFA => 
"\xF3\xA0\xAB\xBB" => "",                     # U+E0AFB => 
"\xF3\xA0\xAB\xBC" => "",                     # U+E0AFC => 
"\xF3\xA0\xAB\xBD" => "",                     # U+E0AFD => 
"\xF3\xA0\xAB\xBE" => "",                     # U+E0AFE => 
"\xF3\xA0\xAB\xBF" => "",                     # U+E0AFF => 
"\xF3\xA0\xAC\x80" => "",                     # U+E0B00 => 
"\xF3\xA0\xAC\x81" => "",                     # U+E0B01 => 
"\xF3\xA0\xAC\x82" => "",                     # U+E0B02 => 
"\xF3\xA0\xAC\x83" => "",                     # U+E0B03 => 
"\xF3\xA0\xAC\x84" => "",                     # U+E0B04 => 
"\xF3\xA0\xAC\x85" => "",                     # U+E0B05 => 
"\xF3\xA0\xAC\x86" => "",                     # U+E0B06 => 
"\xF3\xA0\xAC\x87" => "",                     # U+E0B07 => 
"\xF3\xA0\xAC\x88" => "",                     # U+E0B08 => 
"\xF3\xA0\xAC\x89" => "",                     # U+E0B09 => 
"\xF3\xA0\xAC\x8A" => "",                     # U+E0B0A => 
"\xF3\xA0\xAC\x8B" => "",                     # U+E0B0B => 
"\xF3\xA0\xAC\x8C" => "",                     # U+E0B0C => 
"\xF3\xA0\xAC\x8D" => "",                     # U+E0B0D => 
"\xF3\xA0\xAC\x8E" => "",                     # U+E0B0E => 
"\xF3\xA0\xAC\x8F" => "",                     # U+E0B0F => 
"\xF3\xA0\xAC\x90" => "",                     # U+E0B10 => 
"\xF3\xA0\xAC\x91" => "",                     # U+E0B11 => 
"\xF3\xA0\xAC\x92" => "",                     # U+E0B12 => 
"\xF3\xA0\xAC\x93" => "",                     # U+E0B13 => 
"\xF3\xA0\xAC\x94" => "",                     # U+E0B14 => 
"\xF3\xA0\xAC\x95" => "",                     # U+E0B15 => 
"\xF3\xA0\xAC\x96" => "",                     # U+E0B16 => 
"\xF3\xA0\xAC\x97" => "",                     # U+E0B17 => 
"\xF3\xA0\xAC\x98" => "",                     # U+E0B18 => 
"\xF3\xA0\xAC\x99" => "",                     # U+E0B19 => 
"\xF3\xA0\xAC\x9A" => "",                     # U+E0B1A => 
"\xF3\xA0\xAC\x9B" => "",                     # U+E0B1B => 
"\xF3\xA0\xAC\x9C" => "",                     # U+E0B1C => 
"\xF3\xA0\xAC\x9D" => "",                     # U+E0B1D => 
"\xF3\xA0\xAC\x9E" => "",                     # U+E0B1E => 
"\xF3\xA0\xAC\x9F" => "",                     # U+E0B1F => 
"\xF3\xA0\xAC\xA0" => "",                     # U+E0B20 => 
"\xF3\xA0\xAC\xA1" => "",                     # U+E0B21 => 
"\xF3\xA0\xAC\xA2" => "",                     # U+E0B22 => 
"\xF3\xA0\xAC\xA3" => "",                     # U+E0B23 => 
"\xF3\xA0\xAC\xA4" => "",                     # U+E0B24 => 
"\xF3\xA0\xAC\xA5" => "",                     # U+E0B25 => 
"\xF3\xA0\xAC\xA6" => "",                     # U+E0B26 => 
"\xF3\xA0\xAC\xA7" => "",                     # U+E0B27 => 
"\xF3\xA0\xAC\xA8" => "",                     # U+E0B28 => 
"\xF3\xA0\xAC\xA9" => "",                     # U+E0B29 => 
"\xF3\xA0\xAC\xAA" => "",                     # U+E0B2A => 
"\xF3\xA0\xAC\xAB" => "",                     # U+E0B2B => 
"\xF3\xA0\xAC\xAC" => "",                     # U+E0B2C => 
"\xF3\xA0\xAC\xAD" => "",                     # U+E0B2D => 
"\xF3\xA0\xAC\xAE" => "",                     # U+E0B2E => 
"\xF3\xA0\xAC\xAF" => "",                     # U+E0B2F => 
"\xF3\xA0\xAC\xB0" => "",                     # U+E0B30 => 
"\xF3\xA0\xAC\xB1" => "",                     # U+E0B31 => 
"\xF3\xA0\xAC\xB2" => "",                     # U+E0B32 => 
"\xF3\xA0\xAC\xB3" => "",                     # U+E0B33 => 
"\xF3\xA0\xAC\xB4" => "",                     # U+E0B34 => 
"\xF3\xA0\xAC\xB5" => "",                     # U+E0B35 => 
"\xF3\xA0\xAC\xB6" => "",                     # U+E0B36 => 
"\xF3\xA0\xAC\xB7" => "",                     # U+E0B37 => 
"\xF3\xA0\xAC\xB8" => "",                     # U+E0B38 => 
"\xF3\xA0\xAC\xB9" => "",                     # U+E0B39 => 
"\xF3\xA0\xAC\xBA" => "",                     # U+E0B3A => 
"\xF3\xA0\xAC\xBB" => "",                     # U+E0B3B => 
"\xF3\xA0\xAC\xBC" => "",                     # U+E0B3C => 
"\xF3\xA0\xAC\xBD" => "",                     # U+E0B3D => 
"\xF3\xA0\xAC\xBE" => "",                     # U+E0B3E => 
"\xF3\xA0\xAC\xBF" => "",                     # U+E0B3F => 
"\xF3\xA0\xAD\x80" => "",                     # U+E0B40 => 
"\xF3\xA0\xAD\x81" => "",                     # U+E0B41 => 
"\xF3\xA0\xAD\x82" => "",                     # U+E0B42 => 
"\xF3\xA0\xAD\x83" => "",                     # U+E0B43 => 
"\xF3\xA0\xAD\x84" => "",                     # U+E0B44 => 
"\xF3\xA0\xAD\x85" => "",                     # U+E0B45 => 
"\xF3\xA0\xAD\x86" => "",                     # U+E0B46 => 
"\xF3\xA0\xAD\x87" => "",                     # U+E0B47 => 
"\xF3\xA0\xAD\x88" => "",                     # U+E0B48 => 
"\xF3\xA0\xAD\x89" => "",                     # U+E0B49 => 
"\xF3\xA0\xAD\x8A" => "",                     # U+E0B4A => 
"\xF3\xA0\xAD\x8B" => "",                     # U+E0B4B => 
"\xF3\xA0\xAD\x8C" => "",                     # U+E0B4C => 
"\xF3\xA0\xAD\x8D" => "",                     # U+E0B4D => 
"\xF3\xA0\xAD\x8E" => "",                     # U+E0B4E => 
"\xF3\xA0\xAD\x8F" => "",                     # U+E0B4F => 
"\xF3\xA0\xAD\x90" => "",                     # U+E0B50 => 
"\xF3\xA0\xAD\x91" => "",                     # U+E0B51 => 
"\xF3\xA0\xAD\x92" => "",                     # U+E0B52 => 
"\xF3\xA0\xAD\x93" => "",                     # U+E0B53 => 
"\xF3\xA0\xAD\x94" => "",                     # U+E0B54 => 
"\xF3\xA0\xAD\x95" => "",                     # U+E0B55 => 
"\xF3\xA0\xAD\x96" => "",                     # U+E0B56 => 
"\xF3\xA0\xAD\x97" => "",                     # U+E0B57 => 
"\xF3\xA0\xAD\x98" => "",                     # U+E0B58 => 
"\xF3\xA0\xAD\x99" => "",                     # U+E0B59 => 
"\xF3\xA0\xAD\x9A" => "",                     # U+E0B5A => 
"\xF3\xA0\xAD\x9B" => "",                     # U+E0B5B => 
"\xF3\xA0\xAD\x9C" => "",                     # U+E0B5C => 
"\xF3\xA0\xAD\x9D" => "",                     # U+E0B5D => 
"\xF3\xA0\xAD\x9E" => "",                     # U+E0B5E => 
"\xF3\xA0\xAD\x9F" => "",                     # U+E0B5F => 
"\xF3\xA0\xAD\xA0" => "",                     # U+E0B60 => 
"\xF3\xA0\xAD\xA1" => "",                     # U+E0B61 => 
"\xF3\xA0\xAD\xA2" => "",                     # U+E0B62 => 
"\xF3\xA0\xAD\xA3" => "",                     # U+E0B63 => 
"\xF3\xA0\xAD\xA4" => "",                     # U+E0B64 => 
"\xF3\xA0\xAD\xA5" => "",                     # U+E0B65 => 
"\xF3\xA0\xAD\xA6" => "",                     # U+E0B66 => 
"\xF3\xA0\xAD\xA7" => "",                     # U+E0B67 => 
"\xF3\xA0\xAD\xA8" => "",                     # U+E0B68 => 
"\xF3\xA0\xAD\xA9" => "",                     # U+E0B69 => 
"\xF3\xA0\xAD\xAA" => "",                     # U+E0B6A => 
"\xF3\xA0\xAD\xAB" => "",                     # U+E0B6B => 
"\xF3\xA0\xAD\xAC" => "",                     # U+E0B6C => 
"\xF3\xA0\xAD\xAD" => "",                     # U+E0B6D => 
"\xF3\xA0\xAD\xAE" => "",                     # U+E0B6E => 
"\xF3\xA0\xAD\xAF" => "",                     # U+E0B6F => 
"\xF3\xA0\xAD\xB0" => "",                     # U+E0B70 => 
"\xF3\xA0\xAD\xB1" => "",                     # U+E0B71 => 
"\xF3\xA0\xAD\xB2" => "",                     # U+E0B72 => 
"\xF3\xA0\xAD\xB3" => "",                     # U+E0B73 => 
"\xF3\xA0\xAD\xB4" => "",                     # U+E0B74 => 
"\xF3\xA0\xAD\xB5" => "",                     # U+E0B75 => 
"\xF3\xA0\xAD\xB6" => "",                     # U+E0B76 => 
"\xF3\xA0\xAD\xB7" => "",                     # U+E0B77 => 
"\xF3\xA0\xAD\xB8" => "",                     # U+E0B78 => 
"\xF3\xA0\xAD\xB9" => "",                     # U+E0B79 => 
"\xF3\xA0\xAD\xBA" => "",                     # U+E0B7A => 
"\xF3\xA0\xAD\xBB" => "",                     # U+E0B7B => 
"\xF3\xA0\xAD\xBC" => "",                     # U+E0B7C => 
"\xF3\xA0\xAD\xBD" => "",                     # U+E0B7D => 
"\xF3\xA0\xAD\xBE" => "",                     # U+E0B7E => 
"\xF3\xA0\xAD\xBF" => "",                     # U+E0B7F => 
"\xF3\xA0\xAE\x80" => "",                     # U+E0B80 => 
"\xF3\xA0\xAE\x81" => "",                     # U+E0B81 => 
"\xF3\xA0\xAE\x82" => "",                     # U+E0B82 => 
"\xF3\xA0\xAE\x83" => "",                     # U+E0B83 => 
"\xF3\xA0\xAE\x84" => "",                     # U+E0B84 => 
"\xF3\xA0\xAE\x85" => "",                     # U+E0B85 => 
"\xF3\xA0\xAE\x86" => "",                     # U+E0B86 => 
"\xF3\xA0\xAE\x87" => "",                     # U+E0B87 => 
"\xF3\xA0\xAE\x88" => "",                     # U+E0B88 => 
"\xF3\xA0\xAE\x89" => "",                     # U+E0B89 => 
"\xF3\xA0\xAE\x8A" => "",                     # U+E0B8A => 
"\xF3\xA0\xAE\x8B" => "",                     # U+E0B8B => 
"\xF3\xA0\xAE\x8C" => "",                     # U+E0B8C => 
"\xF3\xA0\xAE\x8D" => "",                     # U+E0B8D => 
"\xF3\xA0\xAE\x8E" => "",                     # U+E0B8E => 
"\xF3\xA0\xAE\x8F" => "",                     # U+E0B8F => 
"\xF3\xA0\xAE\x90" => "",                     # U+E0B90 => 
"\xF3\xA0\xAE\x91" => "",                     # U+E0B91 => 
"\xF3\xA0\xAE\x92" => "",                     # U+E0B92 => 
"\xF3\xA0\xAE\x93" => "",                     # U+E0B93 => 
"\xF3\xA0\xAE\x94" => "",                     # U+E0B94 => 
"\xF3\xA0\xAE\x95" => "",                     # U+E0B95 => 
"\xF3\xA0\xAE\x96" => "",                     # U+E0B96 => 
"\xF3\xA0\xAE\x97" => "",                     # U+E0B97 => 
"\xF3\xA0\xAE\x98" => "",                     # U+E0B98 => 
"\xF3\xA0\xAE\x99" => "",                     # U+E0B99 => 
"\xF3\xA0\xAE\x9A" => "",                     # U+E0B9A => 
"\xF3\xA0\xAE\x9B" => "",                     # U+E0B9B => 
"\xF3\xA0\xAE\x9C" => "",                     # U+E0B9C => 
"\xF3\xA0\xAE\x9D" => "",                     # U+E0B9D => 
"\xF3\xA0\xAE\x9E" => "",                     # U+E0B9E => 
"\xF3\xA0\xAE\x9F" => "",                     # U+E0B9F => 
"\xF3\xA0\xAE\xA0" => "",                     # U+E0BA0 => 
"\xF3\xA0\xAE\xA1" => "",                     # U+E0BA1 => 
"\xF3\xA0\xAE\xA2" => "",                     # U+E0BA2 => 
"\xF3\xA0\xAE\xA3" => "",                     # U+E0BA3 => 
"\xF3\xA0\xAE\xA4" => "",                     # U+E0BA4 => 
"\xF3\xA0\xAE\xA5" => "",                     # U+E0BA5 => 
"\xF3\xA0\xAE\xA6" => "",                     # U+E0BA6 => 
"\xF3\xA0\xAE\xA7" => "",                     # U+E0BA7 => 
"\xF3\xA0\xAE\xA8" => "",                     # U+E0BA8 => 
"\xF3\xA0\xAE\xA9" => "",                     # U+E0BA9 => 
"\xF3\xA0\xAE\xAA" => "",                     # U+E0BAA => 
"\xF3\xA0\xAE\xAB" => "",                     # U+E0BAB => 
"\xF3\xA0\xAE\xAC" => "",                     # U+E0BAC => 
"\xF3\xA0\xAE\xAD" => "",                     # U+E0BAD => 
"\xF3\xA0\xAE\xAE" => "",                     # U+E0BAE => 
"\xF3\xA0\xAE\xAF" => "",                     # U+E0BAF => 
"\xF3\xA0\xAE\xB0" => "",                     # U+E0BB0 => 
"\xF3\xA0\xAE\xB1" => "",                     # U+E0BB1 => 
"\xF3\xA0\xAE\xB2" => "",                     # U+E0BB2 => 
"\xF3\xA0\xAE\xB3" => "",                     # U+E0BB3 => 
"\xF3\xA0\xAE\xB4" => "",                     # U+E0BB4 => 
"\xF3\xA0\xAE\xB5" => "",                     # U+E0BB5 => 
"\xF3\xA0\xAE\xB6" => "",                     # U+E0BB6 => 
"\xF3\xA0\xAE\xB7" => "",                     # U+E0BB7 => 
"\xF3\xA0\xAE\xB8" => "",                     # U+E0BB8 => 
"\xF3\xA0\xAE\xB9" => "",                     # U+E0BB9 => 
"\xF3\xA0\xAE\xBA" => "",                     # U+E0BBA => 
"\xF3\xA0\xAE\xBB" => "",                     # U+E0BBB => 
"\xF3\xA0\xAE\xBC" => "",                     # U+E0BBC => 
"\xF3\xA0\xAE\xBD" => "",                     # U+E0BBD => 
"\xF3\xA0\xAE\xBE" => "",                     # U+E0BBE => 
"\xF3\xA0\xAE\xBF" => "",                     # U+E0BBF => 
"\xF3\xA0\xAF\x80" => "",                     # U+E0BC0 => 
"\xF3\xA0\xAF\x81" => "",                     # U+E0BC1 => 
"\xF3\xA0\xAF\x82" => "",                     # U+E0BC2 => 
"\xF3\xA0\xAF\x83" => "",                     # U+E0BC3 => 
"\xF3\xA0\xAF\x84" => "",                     # U+E0BC4 => 
"\xF3\xA0\xAF\x85" => "",                     # U+E0BC5 => 
"\xF3\xA0\xAF\x86" => "",                     # U+E0BC6 => 
"\xF3\xA0\xAF\x87" => "",                     # U+E0BC7 => 
"\xF3\xA0\xAF\x88" => "",                     # U+E0BC8 => 
"\xF3\xA0\xAF\x89" => "",                     # U+E0BC9 => 
"\xF3\xA0\xAF\x8A" => "",                     # U+E0BCA => 
"\xF3\xA0\xAF\x8B" => "",                     # U+E0BCB => 
"\xF3\xA0\xAF\x8C" => "",                     # U+E0BCC => 
"\xF3\xA0\xAF\x8D" => "",                     # U+E0BCD => 
"\xF3\xA0\xAF\x8E" => "",                     # U+E0BCE => 
"\xF3\xA0\xAF\x8F" => "",                     # U+E0BCF => 
"\xF3\xA0\xAF\x90" => "",                     # U+E0BD0 => 
"\xF3\xA0\xAF\x91" => "",                     # U+E0BD1 => 
"\xF3\xA0\xAF\x92" => "",                     # U+E0BD2 => 
"\xF3\xA0\xAF\x93" => "",                     # U+E0BD3 => 
"\xF3\xA0\xAF\x94" => "",                     # U+E0BD4 => 
"\xF3\xA0\xAF\x95" => "",                     # U+E0BD5 => 
"\xF3\xA0\xAF\x96" => "",                     # U+E0BD6 => 
"\xF3\xA0\xAF\x97" => "",                     # U+E0BD7 => 
"\xF3\xA0\xAF\x98" => "",                     # U+E0BD8 => 
"\xF3\xA0\xAF\x99" => "",                     # U+E0BD9 => 
"\xF3\xA0\xAF\x9A" => "",                     # U+E0BDA => 
"\xF3\xA0\xAF\x9B" => "",                     # U+E0BDB => 
"\xF3\xA0\xAF\x9C" => "",                     # U+E0BDC => 
"\xF3\xA0\xAF\x9D" => "",                     # U+E0BDD => 
"\xF3\xA0\xAF\x9E" => "",                     # U+E0BDE => 
"\xF3\xA0\xAF\x9F" => "",                     # U+E0BDF => 
"\xF3\xA0\xAF\xA0" => "",                     # U+E0BE0 => 
"\xF3\xA0\xAF\xA1" => "",                     # U+E0BE1 => 
"\xF3\xA0\xAF\xA2" => "",                     # U+E0BE2 => 
"\xF3\xA0\xAF\xA3" => "",                     # U+E0BE3 => 
"\xF3\xA0\xAF\xA4" => "",                     # U+E0BE4 => 
"\xF3\xA0\xAF\xA5" => "",                     # U+E0BE5 => 
"\xF3\xA0\xAF\xA6" => "",                     # U+E0BE6 => 
"\xF3\xA0\xAF\xA7" => "",                     # U+E0BE7 => 
"\xF3\xA0\xAF\xA8" => "",                     # U+E0BE8 => 
"\xF3\xA0\xAF\xA9" => "",                     # U+E0BE9 => 
"\xF3\xA0\xAF\xAA" => "",                     # U+E0BEA => 
"\xF3\xA0\xAF\xAB" => "",                     # U+E0BEB => 
"\xF3\xA0\xAF\xAC" => "",                     # U+E0BEC => 
"\xF3\xA0\xAF\xAD" => "",                     # U+E0BED => 
"\xF3\xA0\xAF\xAE" => "",                     # U+E0BEE => 
"\xF3\xA0\xAF\xAF" => "",                     # U+E0BEF => 
"\xF3\xA0\xAF\xB0" => "",                     # U+E0BF0 => 
"\xF3\xA0\xAF\xB1" => "",                     # U+E0BF1 => 
"\xF3\xA0\xAF\xB2" => "",                     # U+E0BF2 => 
"\xF3\xA0\xAF\xB3" => "",                     # U+E0BF3 => 
"\xF3\xA0\xAF\xB4" => "",                     # U+E0BF4 => 
"\xF3\xA0\xAF\xB5" => "",                     # U+E0BF5 => 
"\xF3\xA0\xAF\xB6" => "",                     # U+E0BF6 => 
"\xF3\xA0\xAF\xB7" => "",                     # U+E0BF7 => 
"\xF3\xA0\xAF\xB8" => "",                     # U+E0BF8 => 
"\xF3\xA0\xAF\xB9" => "",                     # U+E0BF9 => 
"\xF3\xA0\xAF\xBA" => "",                     # U+E0BFA => 
"\xF3\xA0\xAF\xBB" => "",                     # U+E0BFB => 
"\xF3\xA0\xAF\xBC" => "",                     # U+E0BFC => 
"\xF3\xA0\xAF\xBD" => "",                     # U+E0BFD => 
"\xF3\xA0\xAF\xBE" => "",                     # U+E0BFE => 
"\xF3\xA0\xAF\xBF" => "",                     # U+E0BFF => 
"\xF3\xA0\xB0\x80" => "",                     # U+E0C00 => 
"\xF3\xA0\xB0\x81" => "",                     # U+E0C01 => 
"\xF3\xA0\xB0\x82" => "",                     # U+E0C02 => 
"\xF3\xA0\xB0\x83" => "",                     # U+E0C03 => 
"\xF3\xA0\xB0\x84" => "",                     # U+E0C04 => 
"\xF3\xA0\xB0\x85" => "",                     # U+E0C05 => 
"\xF3\xA0\xB0\x86" => "",                     # U+E0C06 => 
"\xF3\xA0\xB0\x87" => "",                     # U+E0C07 => 
"\xF3\xA0\xB0\x88" => "",                     # U+E0C08 => 
"\xF3\xA0\xB0\x89" => "",                     # U+E0C09 => 
"\xF3\xA0\xB0\x8A" => "",                     # U+E0C0A => 
"\xF3\xA0\xB0\x8B" => "",                     # U+E0C0B => 
"\xF3\xA0\xB0\x8C" => "",                     # U+E0C0C => 
"\xF3\xA0\xB0\x8D" => "",                     # U+E0C0D => 
"\xF3\xA0\xB0\x8E" => "",                     # U+E0C0E => 
"\xF3\xA0\xB0\x8F" => "",                     # U+E0C0F => 
"\xF3\xA0\xB0\x90" => "",                     # U+E0C10 => 
"\xF3\xA0\xB0\x91" => "",                     # U+E0C11 => 
"\xF3\xA0\xB0\x92" => "",                     # U+E0C12 => 
"\xF3\xA0\xB0\x93" => "",                     # U+E0C13 => 
"\xF3\xA0\xB0\x94" => "",                     # U+E0C14 => 
"\xF3\xA0\xB0\x95" => "",                     # U+E0C15 => 
"\xF3\xA0\xB0\x96" => "",                     # U+E0C16 => 
"\xF3\xA0\xB0\x97" => "",                     # U+E0C17 => 
"\xF3\xA0\xB0\x98" => "",                     # U+E0C18 => 
"\xF3\xA0\xB0\x99" => "",                     # U+E0C19 => 
"\xF3\xA0\xB0\x9A" => "",                     # U+E0C1A => 
"\xF3\xA0\xB0\x9B" => "",                     # U+E0C1B => 
"\xF3\xA0\xB0\x9C" => "",                     # U+E0C1C => 
"\xF3\xA0\xB0\x9D" => "",                     # U+E0C1D => 
"\xF3\xA0\xB0\x9E" => "",                     # U+E0C1E => 
"\xF3\xA0\xB0\x9F" => "",                     # U+E0C1F => 
"\xF3\xA0\xB0\xA0" => "",                     # U+E0C20 => 
"\xF3\xA0\xB0\xA1" => "",                     # U+E0C21 => 
"\xF3\xA0\xB0\xA2" => "",                     # U+E0C22 => 
"\xF3\xA0\xB0\xA3" => "",                     # U+E0C23 => 
"\xF3\xA0\xB0\xA4" => "",                     # U+E0C24 => 
"\xF3\xA0\xB0\xA5" => "",                     # U+E0C25 => 
"\xF3\xA0\xB0\xA6" => "",                     # U+E0C26 => 
"\xF3\xA0\xB0\xA7" => "",                     # U+E0C27 => 
"\xF3\xA0\xB0\xA8" => "",                     # U+E0C28 => 
"\xF3\xA0\xB0\xA9" => "",                     # U+E0C29 => 
"\xF3\xA0\xB0\xAA" => "",                     # U+E0C2A => 
"\xF3\xA0\xB0\xAB" => "",                     # U+E0C2B => 
"\xF3\xA0\xB0\xAC" => "",                     # U+E0C2C => 
"\xF3\xA0\xB0\xAD" => "",                     # U+E0C2D => 
"\xF3\xA0\xB0\xAE" => "",                     # U+E0C2E => 
"\xF3\xA0\xB0\xAF" => "",                     # U+E0C2F => 
"\xF3\xA0\xB0\xB0" => "",                     # U+E0C30 => 
"\xF3\xA0\xB0\xB1" => "",                     # U+E0C31 => 
"\xF3\xA0\xB0\xB2" => "",                     # U+E0C32 => 
"\xF3\xA0\xB0\xB3" => "",                     # U+E0C33 => 
"\xF3\xA0\xB0\xB4" => "",                     # U+E0C34 => 
"\xF3\xA0\xB0\xB5" => "",                     # U+E0C35 => 
"\xF3\xA0\xB0\xB6" => "",                     # U+E0C36 => 
"\xF3\xA0\xB0\xB7" => "",                     # U+E0C37 => 
"\xF3\xA0\xB0\xB8" => "",                     # U+E0C38 => 
"\xF3\xA0\xB0\xB9" => "",                     # U+E0C39 => 
"\xF3\xA0\xB0\xBA" => "",                     # U+E0C3A => 
"\xF3\xA0\xB0\xBB" => "",                     # U+E0C3B => 
"\xF3\xA0\xB0\xBC" => "",                     # U+E0C3C => 
"\xF3\xA0\xB0\xBD" => "",                     # U+E0C3D => 
"\xF3\xA0\xB0\xBE" => "",                     # U+E0C3E => 
"\xF3\xA0\xB0\xBF" => "",                     # U+E0C3F => 
"\xF3\xA0\xB1\x80" => "",                     # U+E0C40 => 
"\xF3\xA0\xB1\x81" => "",                     # U+E0C41 => 
"\xF3\xA0\xB1\x82" => "",                     # U+E0C42 => 
"\xF3\xA0\xB1\x83" => "",                     # U+E0C43 => 
"\xF3\xA0\xB1\x84" => "",                     # U+E0C44 => 
"\xF3\xA0\xB1\x85" => "",                     # U+E0C45 => 
"\xF3\xA0\xB1\x86" => "",                     # U+E0C46 => 
"\xF3\xA0\xB1\x87" => "",                     # U+E0C47 => 
"\xF3\xA0\xB1\x88" => "",                     # U+E0C48 => 
"\xF3\xA0\xB1\x89" => "",                     # U+E0C49 => 
"\xF3\xA0\xB1\x8A" => "",                     # U+E0C4A => 
"\xF3\xA0\xB1\x8B" => "",                     # U+E0C4B => 
"\xF3\xA0\xB1\x8C" => "",                     # U+E0C4C => 
"\xF3\xA0\xB1\x8D" => "",                     # U+E0C4D => 
"\xF3\xA0\xB1\x8E" => "",                     # U+E0C4E => 
"\xF3\xA0\xB1\x8F" => "",                     # U+E0C4F => 
"\xF3\xA0\xB1\x90" => "",                     # U+E0C50 => 
"\xF3\xA0\xB1\x91" => "",                     # U+E0C51 => 
"\xF3\xA0\xB1\x92" => "",                     # U+E0C52 => 
"\xF3\xA0\xB1\x93" => "",                     # U+E0C53 => 
"\xF3\xA0\xB1\x94" => "",                     # U+E0C54 => 
"\xF3\xA0\xB1\x95" => "",                     # U+E0C55 => 
"\xF3\xA0\xB1\x96" => "",                     # U+E0C56 => 
"\xF3\xA0\xB1\x97" => "",                     # U+E0C57 => 
"\xF3\xA0\xB1\x98" => "",                     # U+E0C58 => 
"\xF3\xA0\xB1\x99" => "",                     # U+E0C59 => 
"\xF3\xA0\xB1\x9A" => "",                     # U+E0C5A => 
"\xF3\xA0\xB1\x9B" => "",                     # U+E0C5B => 
"\xF3\xA0\xB1\x9C" => "",                     # U+E0C5C => 
"\xF3\xA0\xB1\x9D" => "",                     # U+E0C5D => 
"\xF3\xA0\xB1\x9E" => "",                     # U+E0C5E => 
"\xF3\xA0\xB1\x9F" => "",                     # U+E0C5F => 
"\xF3\xA0\xB1\xA0" => "",                     # U+E0C60 => 
"\xF3\xA0\xB1\xA1" => "",                     # U+E0C61 => 
"\xF3\xA0\xB1\xA2" => "",                     # U+E0C62 => 
"\xF3\xA0\xB1\xA3" => "",                     # U+E0C63 => 
"\xF3\xA0\xB1\xA4" => "",                     # U+E0C64 => 
"\xF3\xA0\xB1\xA5" => "",                     # U+E0C65 => 
"\xF3\xA0\xB1\xA6" => "",                     # U+E0C66 => 
"\xF3\xA0\xB1\xA7" => "",                     # U+E0C67 => 
"\xF3\xA0\xB1\xA8" => "",                     # U+E0C68 => 
"\xF3\xA0\xB1\xA9" => "",                     # U+E0C69 => 
"\xF3\xA0\xB1\xAA" => "",                     # U+E0C6A => 
"\xF3\xA0\xB1\xAB" => "",                     # U+E0C6B => 
"\xF3\xA0\xB1\xAC" => "",                     # U+E0C6C => 
"\xF3\xA0\xB1\xAD" => "",                     # U+E0C6D => 
"\xF3\xA0\xB1\xAE" => "",                     # U+E0C6E => 
"\xF3\xA0\xB1\xAF" => "",                     # U+E0C6F => 
"\xF3\xA0\xB1\xB0" => "",                     # U+E0C70 => 
"\xF3\xA0\xB1\xB1" => "",                     # U+E0C71 => 
"\xF3\xA0\xB1\xB2" => "",                     # U+E0C72 => 
"\xF3\xA0\xB1\xB3" => "",                     # U+E0C73 => 
"\xF3\xA0\xB1\xB4" => "",                     # U+E0C74 => 
"\xF3\xA0\xB1\xB5" => "",                     # U+E0C75 => 
"\xF3\xA0\xB1\xB6" => "",                     # U+E0C76 => 
"\xF3\xA0\xB1\xB7" => "",                     # U+E0C77 => 
"\xF3\xA0\xB1\xB8" => "",                     # U+E0C78 => 
"\xF3\xA0\xB1\xB9" => "",                     # U+E0C79 => 
"\xF3\xA0\xB1\xBA" => "",                     # U+E0C7A => 
"\xF3\xA0\xB1\xBB" => "",                     # U+E0C7B => 
"\xF3\xA0\xB1\xBC" => "",                     # U+E0C7C => 
"\xF3\xA0\xB1\xBD" => "",                     # U+E0C7D => 
"\xF3\xA0\xB1\xBE" => "",                     # U+E0C7E => 
"\xF3\xA0\xB1\xBF" => "",                     # U+E0C7F => 
"\xF3\xA0\xB2\x80" => "",                     # U+E0C80 => 
"\xF3\xA0\xB2\x81" => "",                     # U+E0C81 => 
"\xF3\xA0\xB2\x82" => "",                     # U+E0C82 => 
"\xF3\xA0\xB2\x83" => "",                     # U+E0C83 => 
"\xF3\xA0\xB2\x84" => "",                     # U+E0C84 => 
"\xF3\xA0\xB2\x85" => "",                     # U+E0C85 => 
"\xF3\xA0\xB2\x86" => "",                     # U+E0C86 => 
"\xF3\xA0\xB2\x87" => "",                     # U+E0C87 => 
"\xF3\xA0\xB2\x88" => "",                     # U+E0C88 => 
"\xF3\xA0\xB2\x89" => "",                     # U+E0C89 => 
"\xF3\xA0\xB2\x8A" => "",                     # U+E0C8A => 
"\xF3\xA0\xB2\x8B" => "",                     # U+E0C8B => 
"\xF3\xA0\xB2\x8C" => "",                     # U+E0C8C => 
"\xF3\xA0\xB2\x8D" => "",                     # U+E0C8D => 
"\xF3\xA0\xB2\x8E" => "",                     # U+E0C8E => 
"\xF3\xA0\xB2\x8F" => "",                     # U+E0C8F => 
"\xF3\xA0\xB2\x90" => "",                     # U+E0C90 => 
"\xF3\xA0\xB2\x91" => "",                     # U+E0C91 => 
"\xF3\xA0\xB2\x92" => "",                     # U+E0C92 => 
"\xF3\xA0\xB2\x93" => "",                     # U+E0C93 => 
"\xF3\xA0\xB2\x94" => "",                     # U+E0C94 => 
"\xF3\xA0\xB2\x95" => "",                     # U+E0C95 => 
"\xF3\xA0\xB2\x96" => "",                     # U+E0C96 => 
"\xF3\xA0\xB2\x97" => "",                     # U+E0C97 => 
"\xF3\xA0\xB2\x98" => "",                     # U+E0C98 => 
"\xF3\xA0\xB2\x99" => "",                     # U+E0C99 => 
"\xF3\xA0\xB2\x9A" => "",                     # U+E0C9A => 
"\xF3\xA0\xB2\x9B" => "",                     # U+E0C9B => 
"\xF3\xA0\xB2\x9C" => "",                     # U+E0C9C => 
"\xF3\xA0\xB2\x9D" => "",                     # U+E0C9D => 
"\xF3\xA0\xB2\x9E" => "",                     # U+E0C9E => 
"\xF3\xA0\xB2\x9F" => "",                     # U+E0C9F => 
"\xF3\xA0\xB2\xA0" => "",                     # U+E0CA0 => 
"\xF3\xA0\xB2\xA1" => "",                     # U+E0CA1 => 
"\xF3\xA0\xB2\xA2" => "",                     # U+E0CA2 => 
"\xF3\xA0\xB2\xA3" => "",                     # U+E0CA3 => 
"\xF3\xA0\xB2\xA4" => "",                     # U+E0CA4 => 
"\xF3\xA0\xB2\xA5" => "",                     # U+E0CA5 => 
"\xF3\xA0\xB2\xA6" => "",                     # U+E0CA6 => 
"\xF3\xA0\xB2\xA7" => "",                     # U+E0CA7 => 
"\xF3\xA0\xB2\xA8" => "",                     # U+E0CA8 => 
"\xF3\xA0\xB2\xA9" => "",                     # U+E0CA9 => 
"\xF3\xA0\xB2\xAA" => "",                     # U+E0CAA => 
"\xF3\xA0\xB2\xAB" => "",                     # U+E0CAB => 
"\xF3\xA0\xB2\xAC" => "",                     # U+E0CAC => 
"\xF3\xA0\xB2\xAD" => "",                     # U+E0CAD => 
"\xF3\xA0\xB2\xAE" => "",                     # U+E0CAE => 
"\xF3\xA0\xB2\xAF" => "",                     # U+E0CAF => 
"\xF3\xA0\xB2\xB0" => "",                     # U+E0CB0 => 
"\xF3\xA0\xB2\xB1" => "",                     # U+E0CB1 => 
"\xF3\xA0\xB2\xB2" => "",                     # U+E0CB2 => 
"\xF3\xA0\xB2\xB3" => "",                     # U+E0CB3 => 
"\xF3\xA0\xB2\xB4" => "",                     # U+E0CB4 => 
"\xF3\xA0\xB2\xB5" => "",                     # U+E0CB5 => 
"\xF3\xA0\xB2\xB6" => "",                     # U+E0CB6 => 
"\xF3\xA0\xB2\xB7" => "",                     # U+E0CB7 => 
"\xF3\xA0\xB2\xB8" => "",                     # U+E0CB8 => 
"\xF3\xA0\xB2\xB9" => "",                     # U+E0CB9 => 
"\xF3\xA0\xB2\xBA" => "",                     # U+E0CBA => 
"\xF3\xA0\xB2\xBB" => "",                     # U+E0CBB => 
"\xF3\xA0\xB2\xBC" => "",                     # U+E0CBC => 
"\xF3\xA0\xB2\xBD" => "",                     # U+E0CBD => 
"\xF3\xA0\xB2\xBE" => "",                     # U+E0CBE => 
"\xF3\xA0\xB2\xBF" => "",                     # U+E0CBF => 
"\xF3\xA0\xB3\x80" => "",                     # U+E0CC0 => 
"\xF3\xA0\xB3\x81" => "",                     # U+E0CC1 => 
"\xF3\xA0\xB3\x82" => "",                     # U+E0CC2 => 
"\xF3\xA0\xB3\x83" => "",                     # U+E0CC3 => 
"\xF3\xA0\xB3\x84" => "",                     # U+E0CC4 => 
"\xF3\xA0\xB3\x85" => "",                     # U+E0CC5 => 
"\xF3\xA0\xB3\x86" => "",                     # U+E0CC6 => 
"\xF3\xA0\xB3\x87" => "",                     # U+E0CC7 => 
"\xF3\xA0\xB3\x88" => "",                     # U+E0CC8 => 
"\xF3\xA0\xB3\x89" => "",                     # U+E0CC9 => 
"\xF3\xA0\xB3\x8A" => "",                     # U+E0CCA => 
"\xF3\xA0\xB3\x8B" => "",                     # U+E0CCB => 
"\xF3\xA0\xB3\x8C" => "",                     # U+E0CCC => 
"\xF3\xA0\xB3\x8D" => "",                     # U+E0CCD => 
"\xF3\xA0\xB3\x8E" => "",                     # U+E0CCE => 
"\xF3\xA0\xB3\x8F" => "",                     # U+E0CCF => 
"\xF3\xA0\xB3\x90" => "",                     # U+E0CD0 => 
"\xF3\xA0\xB3\x91" => "",                     # U+E0CD1 => 
"\xF3\xA0\xB3\x92" => "",                     # U+E0CD2 => 
"\xF3\xA0\xB3\x93" => "",                     # U+E0CD3 => 
"\xF3\xA0\xB3\x94" => "",                     # U+E0CD4 => 
"\xF3\xA0\xB3\x95" => "",                     # U+E0CD5 => 
"\xF3\xA0\xB3\x96" => "",                     # U+E0CD6 => 
"\xF3\xA0\xB3\x97" => "",                     # U+E0CD7 => 
"\xF3\xA0\xB3\x98" => "",                     # U+E0CD8 => 
"\xF3\xA0\xB3\x99" => "",                     # U+E0CD9 => 
"\xF3\xA0\xB3\x9A" => "",                     # U+E0CDA => 
"\xF3\xA0\xB3\x9B" => "",                     # U+E0CDB => 
"\xF3\xA0\xB3\x9C" => "",                     # U+E0CDC => 
"\xF3\xA0\xB3\x9D" => "",                     # U+E0CDD => 
"\xF3\xA0\xB3\x9E" => "",                     # U+E0CDE => 
"\xF3\xA0\xB3\x9F" => "",                     # U+E0CDF => 
"\xF3\xA0\xB3\xA0" => "",                     # U+E0CE0 => 
"\xF3\xA0\xB3\xA1" => "",                     # U+E0CE1 => 
"\xF3\xA0\xB3\xA2" => "",                     # U+E0CE2 => 
"\xF3\xA0\xB3\xA3" => "",                     # U+E0CE3 => 
"\xF3\xA0\xB3\xA4" => "",                     # U+E0CE4 => 
"\xF3\xA0\xB3\xA5" => "",                     # U+E0CE5 => 
"\xF3\xA0\xB3\xA6" => "",                     # U+E0CE6 => 
"\xF3\xA0\xB3\xA7" => "",                     # U+E0CE7 => 
"\xF3\xA0\xB3\xA8" => "",                     # U+E0CE8 => 
"\xF3\xA0\xB3\xA9" => "",                     # U+E0CE9 => 
"\xF3\xA0\xB3\xAA" => "",                     # U+E0CEA => 
"\xF3\xA0\xB3\xAB" => "",                     # U+E0CEB => 
"\xF3\xA0\xB3\xAC" => "",                     # U+E0CEC => 
"\xF3\xA0\xB3\xAD" => "",                     # U+E0CED => 
"\xF3\xA0\xB3\xAE" => "",                     # U+E0CEE => 
"\xF3\xA0\xB3\xAF" => "",                     # U+E0CEF => 
"\xF3\xA0\xB3\xB0" => "",                     # U+E0CF0 => 
"\xF3\xA0\xB3\xB1" => "",                     # U+E0CF1 => 
"\xF3\xA0\xB3\xB2" => "",                     # U+E0CF2 => 
"\xF3\xA0\xB3\xB3" => "",                     # U+E0CF3 => 
"\xF3\xA0\xB3\xB4" => "",                     # U+E0CF4 => 
"\xF3\xA0\xB3\xB5" => "",                     # U+E0CF5 => 
"\xF3\xA0\xB3\xB6" => "",                     # U+E0CF6 => 
"\xF3\xA0\xB3\xB7" => "",                     # U+E0CF7 => 
"\xF3\xA0\xB3\xB8" => "",                     # U+E0CF8 => 
"\xF3\xA0\xB3\xB9" => "",                     # U+E0CF9 => 
"\xF3\xA0\xB3\xBA" => "",                     # U+E0CFA => 
"\xF3\xA0\xB3\xBB" => "",                     # U+E0CFB => 
"\xF3\xA0\xB3\xBC" => "",                     # U+E0CFC => 
"\xF3\xA0\xB3\xBD" => "",                     # U+E0CFD => 
"\xF3\xA0\xB3\xBE" => "",                     # U+E0CFE => 
"\xF3\xA0\xB3\xBF" => "",                     # U+E0CFF => 
"\xF3\xA0\xB4\x80" => "",                     # U+E0D00 => 
"\xF3\xA0\xB4\x81" => "",                     # U+E0D01 => 
"\xF3\xA0\xB4\x82" => "",                     # U+E0D02 => 
"\xF3\xA0\xB4\x83" => "",                     # U+E0D03 => 
"\xF3\xA0\xB4\x84" => "",                     # U+E0D04 => 
"\xF3\xA0\xB4\x85" => "",                     # U+E0D05 => 
"\xF3\xA0\xB4\x86" => "",                     # U+E0D06 => 
"\xF3\xA0\xB4\x87" => "",                     # U+E0D07 => 
"\xF3\xA0\xB4\x88" => "",                     # U+E0D08 => 
"\xF3\xA0\xB4\x89" => "",                     # U+E0D09 => 
"\xF3\xA0\xB4\x8A" => "",                     # U+E0D0A => 
"\xF3\xA0\xB4\x8B" => "",                     # U+E0D0B => 
"\xF3\xA0\xB4\x8C" => "",                     # U+E0D0C => 
"\xF3\xA0\xB4\x8D" => "",                     # U+E0D0D => 
"\xF3\xA0\xB4\x8E" => "",                     # U+E0D0E => 
"\xF3\xA0\xB4\x8F" => "",                     # U+E0D0F => 
"\xF3\xA0\xB4\x90" => "",                     # U+E0D10 => 
"\xF3\xA0\xB4\x91" => "",                     # U+E0D11 => 
"\xF3\xA0\xB4\x92" => "",                     # U+E0D12 => 
"\xF3\xA0\xB4\x93" => "",                     # U+E0D13 => 
"\xF3\xA0\xB4\x94" => "",                     # U+E0D14 => 
"\xF3\xA0\xB4\x95" => "",                     # U+E0D15 => 
"\xF3\xA0\xB4\x96" => "",                     # U+E0D16 => 
"\xF3\xA0\xB4\x97" => "",                     # U+E0D17 => 
"\xF3\xA0\xB4\x98" => "",                     # U+E0D18 => 
"\xF3\xA0\xB4\x99" => "",                     # U+E0D19 => 
"\xF3\xA0\xB4\x9A" => "",                     # U+E0D1A => 
"\xF3\xA0\xB4\x9B" => "",                     # U+E0D1B => 
"\xF3\xA0\xB4\x9C" => "",                     # U+E0D1C => 
"\xF3\xA0\xB4\x9D" => "",                     # U+E0D1D => 
"\xF3\xA0\xB4\x9E" => "",                     # U+E0D1E => 
"\xF3\xA0\xB4\x9F" => "",                     # U+E0D1F => 
"\xF3\xA0\xB4\xA0" => "",                     # U+E0D20 => 
"\xF3\xA0\xB4\xA1" => "",                     # U+E0D21 => 
"\xF3\xA0\xB4\xA2" => "",                     # U+E0D22 => 
"\xF3\xA0\xB4\xA3" => "",                     # U+E0D23 => 
"\xF3\xA0\xB4\xA4" => "",                     # U+E0D24 => 
"\xF3\xA0\xB4\xA5" => "",                     # U+E0D25 => 
"\xF3\xA0\xB4\xA6" => "",                     # U+E0D26 => 
"\xF3\xA0\xB4\xA7" => "",                     # U+E0D27 => 
"\xF3\xA0\xB4\xA8" => "",                     # U+E0D28 => 
"\xF3\xA0\xB4\xA9" => "",                     # U+E0D29 => 
"\xF3\xA0\xB4\xAA" => "",                     # U+E0D2A => 
"\xF3\xA0\xB4\xAB" => "",                     # U+E0D2B => 
"\xF3\xA0\xB4\xAC" => "",                     # U+E0D2C => 
"\xF3\xA0\xB4\xAD" => "",                     # U+E0D2D => 
"\xF3\xA0\xB4\xAE" => "",                     # U+E0D2E => 
"\xF3\xA0\xB4\xAF" => "",                     # U+E0D2F => 
"\xF3\xA0\xB4\xB0" => "",                     # U+E0D30 => 
"\xF3\xA0\xB4\xB1" => "",                     # U+E0D31 => 
"\xF3\xA0\xB4\xB2" => "",                     # U+E0D32 => 
"\xF3\xA0\xB4\xB3" => "",                     # U+E0D33 => 
"\xF3\xA0\xB4\xB4" => "",                     # U+E0D34 => 
"\xF3\xA0\xB4\xB5" => "",                     # U+E0D35 => 
"\xF3\xA0\xB4\xB6" => "",                     # U+E0D36 => 
"\xF3\xA0\xB4\xB7" => "",                     # U+E0D37 => 
"\xF3\xA0\xB4\xB8" => "",                     # U+E0D38 => 
"\xF3\xA0\xB4\xB9" => "",                     # U+E0D39 => 
"\xF3\xA0\xB4\xBA" => "",                     # U+E0D3A => 
"\xF3\xA0\xB4\xBB" => "",                     # U+E0D3B => 
"\xF3\xA0\xB4\xBC" => "",                     # U+E0D3C => 
"\xF3\xA0\xB4\xBD" => "",                     # U+E0D3D => 
"\xF3\xA0\xB4\xBE" => "",                     # U+E0D3E => 
"\xF3\xA0\xB4\xBF" => "",                     # U+E0D3F => 
"\xF3\xA0\xB5\x80" => "",                     # U+E0D40 => 
"\xF3\xA0\xB5\x81" => "",                     # U+E0D41 => 
"\xF3\xA0\xB5\x82" => "",                     # U+E0D42 => 
"\xF3\xA0\xB5\x83" => "",                     # U+E0D43 => 
"\xF3\xA0\xB5\x84" => "",                     # U+E0D44 => 
"\xF3\xA0\xB5\x85" => "",                     # U+E0D45 => 
"\xF3\xA0\xB5\x86" => "",                     # U+E0D46 => 
"\xF3\xA0\xB5\x87" => "",                     # U+E0D47 => 
"\xF3\xA0\xB5\x88" => "",                     # U+E0D48 => 
"\xF3\xA0\xB5\x89" => "",                     # U+E0D49 => 
"\xF3\xA0\xB5\x8A" => "",                     # U+E0D4A => 
"\xF3\xA0\xB5\x8B" => "",                     # U+E0D4B => 
"\xF3\xA0\xB5\x8C" => "",                     # U+E0D4C => 
"\xF3\xA0\xB5\x8D" => "",                     # U+E0D4D => 
"\xF3\xA0\xB5\x8E" => "",                     # U+E0D4E => 
"\xF3\xA0\xB5\x8F" => "",                     # U+E0D4F => 
"\xF3\xA0\xB5\x90" => "",                     # U+E0D50 => 
"\xF3\xA0\xB5\x91" => "",                     # U+E0D51 => 
"\xF3\xA0\xB5\x92" => "",                     # U+E0D52 => 
"\xF3\xA0\xB5\x93" => "",                     # U+E0D53 => 
"\xF3\xA0\xB5\x94" => "",                     # U+E0D54 => 
"\xF3\xA0\xB5\x95" => "",                     # U+E0D55 => 
"\xF3\xA0\xB5\x96" => "",                     # U+E0D56 => 
"\xF3\xA0\xB5\x97" => "",                     # U+E0D57 => 
"\xF3\xA0\xB5\x98" => "",                     # U+E0D58 => 
"\xF3\xA0\xB5\x99" => "",                     # U+E0D59 => 
"\xF3\xA0\xB5\x9A" => "",                     # U+E0D5A => 
"\xF3\xA0\xB5\x9B" => "",                     # U+E0D5B => 
"\xF3\xA0\xB5\x9C" => "",                     # U+E0D5C => 
"\xF3\xA0\xB5\x9D" => "",                     # U+E0D5D => 
"\xF3\xA0\xB5\x9E" => "",                     # U+E0D5E => 
"\xF3\xA0\xB5\x9F" => "",                     # U+E0D5F => 
"\xF3\xA0\xB5\xA0" => "",                     # U+E0D60 => 
"\xF3\xA0\xB5\xA1" => "",                     # U+E0D61 => 
"\xF3\xA0\xB5\xA2" => "",                     # U+E0D62 => 
"\xF3\xA0\xB5\xA3" => "",                     # U+E0D63 => 
"\xF3\xA0\xB5\xA4" => "",                     # U+E0D64 => 
"\xF3\xA0\xB5\xA5" => "",                     # U+E0D65 => 
"\xF3\xA0\xB5\xA6" => "",                     # U+E0D66 => 
"\xF3\xA0\xB5\xA7" => "",                     # U+E0D67 => 
"\xF3\xA0\xB5\xA8" => "",                     # U+E0D68 => 
"\xF3\xA0\xB5\xA9" => "",                     # U+E0D69 => 
"\xF3\xA0\xB5\xAA" => "",                     # U+E0D6A => 
"\xF3\xA0\xB5\xAB" => "",                     # U+E0D6B => 
"\xF3\xA0\xB5\xAC" => "",                     # U+E0D6C => 
"\xF3\xA0\xB5\xAD" => "",                     # U+E0D6D => 
"\xF3\xA0\xB5\xAE" => "",                     # U+E0D6E => 
"\xF3\xA0\xB5\xAF" => "",                     # U+E0D6F => 
"\xF3\xA0\xB5\xB0" => "",                     # U+E0D70 => 
"\xF3\xA0\xB5\xB1" => "",                     # U+E0D71 => 
"\xF3\xA0\xB5\xB2" => "",                     # U+E0D72 => 
"\xF3\xA0\xB5\xB3" => "",                     # U+E0D73 => 
"\xF3\xA0\xB5\xB4" => "",                     # U+E0D74 => 
"\xF3\xA0\xB5\xB5" => "",                     # U+E0D75 => 
"\xF3\xA0\xB5\xB6" => "",                     # U+E0D76 => 
"\xF3\xA0\xB5\xB7" => "",                     # U+E0D77 => 
"\xF3\xA0\xB5\xB8" => "",                     # U+E0D78 => 
"\xF3\xA0\xB5\xB9" => "",                     # U+E0D79 => 
"\xF3\xA0\xB5\xBA" => "",                     # U+E0D7A => 
"\xF3\xA0\xB5\xBB" => "",                     # U+E0D7B => 
"\xF3\xA0\xB5\xBC" => "",                     # U+E0D7C => 
"\xF3\xA0\xB5\xBD" => "",                     # U+E0D7D => 
"\xF3\xA0\xB5\xBE" => "",                     # U+E0D7E => 
"\xF3\xA0\xB5\xBF" => "",                     # U+E0D7F => 
"\xF3\xA0\xB6\x80" => "",                     # U+E0D80 => 
"\xF3\xA0\xB6\x81" => "",                     # U+E0D81 => 
"\xF3\xA0\xB6\x82" => "",                     # U+E0D82 => 
"\xF3\xA0\xB6\x83" => "",                     # U+E0D83 => 
"\xF3\xA0\xB6\x84" => "",                     # U+E0D84 => 
"\xF3\xA0\xB6\x85" => "",                     # U+E0D85 => 
"\xF3\xA0\xB6\x86" => "",                     # U+E0D86 => 
"\xF3\xA0\xB6\x87" => "",                     # U+E0D87 => 
"\xF3\xA0\xB6\x88" => "",                     # U+E0D88 => 
"\xF3\xA0\xB6\x89" => "",                     # U+E0D89 => 
"\xF3\xA0\xB6\x8A" => "",                     # U+E0D8A => 
"\xF3\xA0\xB6\x8B" => "",                     # U+E0D8B => 
"\xF3\xA0\xB6\x8C" => "",                     # U+E0D8C => 
"\xF3\xA0\xB6\x8D" => "",                     # U+E0D8D => 
"\xF3\xA0\xB6\x8E" => "",                     # U+E0D8E => 
"\xF3\xA0\xB6\x8F" => "",                     # U+E0D8F => 
"\xF3\xA0\xB6\x90" => "",                     # U+E0D90 => 
"\xF3\xA0\xB6\x91" => "",                     # U+E0D91 => 
"\xF3\xA0\xB6\x92" => "",                     # U+E0D92 => 
"\xF3\xA0\xB6\x93" => "",                     # U+E0D93 => 
"\xF3\xA0\xB6\x94" => "",                     # U+E0D94 => 
"\xF3\xA0\xB6\x95" => "",                     # U+E0D95 => 
"\xF3\xA0\xB6\x96" => "",                     # U+E0D96 => 
"\xF3\xA0\xB6\x97" => "",                     # U+E0D97 => 
"\xF3\xA0\xB6\x98" => "",                     # U+E0D98 => 
"\xF3\xA0\xB6\x99" => "",                     # U+E0D99 => 
"\xF3\xA0\xB6\x9A" => "",                     # U+E0D9A => 
"\xF3\xA0\xB6\x9B" => "",                     # U+E0D9B => 
"\xF3\xA0\xB6\x9C" => "",                     # U+E0D9C => 
"\xF3\xA0\xB6\x9D" => "",                     # U+E0D9D => 
"\xF3\xA0\xB6\x9E" => "",                     # U+E0D9E => 
"\xF3\xA0\xB6\x9F" => "",                     # U+E0D9F => 
"\xF3\xA0\xB6\xA0" => "",                     # U+E0DA0 => 
"\xF3\xA0\xB6\xA1" => "",                     # U+E0DA1 => 
"\xF3\xA0\xB6\xA2" => "",                     # U+E0DA2 => 
"\xF3\xA0\xB6\xA3" => "",                     # U+E0DA3 => 
"\xF3\xA0\xB6\xA4" => "",                     # U+E0DA4 => 
"\xF3\xA0\xB6\xA5" => "",                     # U+E0DA5 => 
"\xF3\xA0\xB6\xA6" => "",                     # U+E0DA6 => 
"\xF3\xA0\xB6\xA7" => "",                     # U+E0DA7 => 
"\xF3\xA0\xB6\xA8" => "",                     # U+E0DA8 => 
"\xF3\xA0\xB6\xA9" => "",                     # U+E0DA9 => 
"\xF3\xA0\xB6\xAA" => "",                     # U+E0DAA => 
"\xF3\xA0\xB6\xAB" => "",                     # U+E0DAB => 
"\xF3\xA0\xB6\xAC" => "",                     # U+E0DAC => 
"\xF3\xA0\xB6\xAD" => "",                     # U+E0DAD => 
"\xF3\xA0\xB6\xAE" => "",                     # U+E0DAE => 
"\xF3\xA0\xB6\xAF" => "",                     # U+E0DAF => 
"\xF3\xA0\xB6\xB0" => "",                     # U+E0DB0 => 
"\xF3\xA0\xB6\xB1" => "",                     # U+E0DB1 => 
"\xF3\xA0\xB6\xB2" => "",                     # U+E0DB2 => 
"\xF3\xA0\xB6\xB3" => "",                     # U+E0DB3 => 
"\xF3\xA0\xB6\xB4" => "",                     # U+E0DB4 => 
"\xF3\xA0\xB6\xB5" => "",                     # U+E0DB5 => 
"\xF3\xA0\xB6\xB6" => "",                     # U+E0DB6 => 
"\xF3\xA0\xB6\xB7" => "",                     # U+E0DB7 => 
"\xF3\xA0\xB6\xB8" => "",                     # U+E0DB8 => 
"\xF3\xA0\xB6\xB9" => "",                     # U+E0DB9 => 
"\xF3\xA0\xB6\xBA" => "",                     # U+E0DBA => 
"\xF3\xA0\xB6\xBB" => "",                     # U+E0DBB => 
"\xF3\xA0\xB6\xBC" => "",                     # U+E0DBC => 
"\xF3\xA0\xB6\xBD" => "",                     # U+E0DBD => 
"\xF3\xA0\xB6\xBE" => "",                     # U+E0DBE => 
"\xF3\xA0\xB6\xBF" => "",                     # U+E0DBF => 
"\xF3\xA0\xB7\x80" => "",                     # U+E0DC0 => 
"\xF3\xA0\xB7\x81" => "",                     # U+E0DC1 => 
"\xF3\xA0\xB7\x82" => "",                     # U+E0DC2 => 
"\xF3\xA0\xB7\x83" => "",                     # U+E0DC3 => 
"\xF3\xA0\xB7\x84" => "",                     # U+E0DC4 => 
"\xF3\xA0\xB7\x85" => "",                     # U+E0DC5 => 
"\xF3\xA0\xB7\x86" => "",                     # U+E0DC6 => 
"\xF3\xA0\xB7\x87" => "",                     # U+E0DC7 => 
"\xF3\xA0\xB7\x88" => "",                     # U+E0DC8 => 
"\xF3\xA0\xB7\x89" => "",                     # U+E0DC9 => 
"\xF3\xA0\xB7\x8A" => "",                     # U+E0DCA => 
"\xF3\xA0\xB7\x8B" => "",                     # U+E0DCB => 
"\xF3\xA0\xB7\x8C" => "",                     # U+E0DCC => 
"\xF3\xA0\xB7\x8D" => "",                     # U+E0DCD => 
"\xF3\xA0\xB7\x8E" => "",                     # U+E0DCE => 
"\xF3\xA0\xB7\x8F" => "",                     # U+E0DCF => 
"\xF3\xA0\xB7\x90" => "",                     # U+E0DD0 => 
"\xF3\xA0\xB7\x91" => "",                     # U+E0DD1 => 
"\xF3\xA0\xB7\x92" => "",                     # U+E0DD2 => 
"\xF3\xA0\xB7\x93" => "",                     # U+E0DD3 => 
"\xF3\xA0\xB7\x94" => "",                     # U+E0DD4 => 
"\xF3\xA0\xB7\x95" => "",                     # U+E0DD5 => 
"\xF3\xA0\xB7\x96" => "",                     # U+E0DD6 => 
"\xF3\xA0\xB7\x97" => "",                     # U+E0DD7 => 
"\xF3\xA0\xB7\x98" => "",                     # U+E0DD8 => 
"\xF3\xA0\xB7\x99" => "",                     # U+E0DD9 => 
"\xF3\xA0\xB7\x9A" => "",                     # U+E0DDA => 
"\xF3\xA0\xB7\x9B" => "",                     # U+E0DDB => 
"\xF3\xA0\xB7\x9C" => "",                     # U+E0DDC => 
"\xF3\xA0\xB7\x9D" => "",                     # U+E0DDD => 
"\xF3\xA0\xB7\x9E" => "",                     # U+E0DDE => 
"\xF3\xA0\xB7\x9F" => "",                     # U+E0DDF => 
"\xF3\xA0\xB7\xA0" => "",                     # U+E0DE0 => 
"\xF3\xA0\xB7\xA1" => "",                     # U+E0DE1 => 
"\xF3\xA0\xB7\xA2" => "",                     # U+E0DE2 => 
"\xF3\xA0\xB7\xA3" => "",                     # U+E0DE3 => 
"\xF3\xA0\xB7\xA4" => "",                     # U+E0DE4 => 
"\xF3\xA0\xB7\xA5" => "",                     # U+E0DE5 => 
"\xF3\xA0\xB7\xA6" => "",                     # U+E0DE6 => 
"\xF3\xA0\xB7\xA7" => "",                     # U+E0DE7 => 
"\xF3\xA0\xB7\xA8" => "",                     # U+E0DE8 => 
"\xF3\xA0\xB7\xA9" => "",                     # U+E0DE9 => 
"\xF3\xA0\xB7\xAA" => "",                     # U+E0DEA => 
"\xF3\xA0\xB7\xAB" => "",                     # U+E0DEB => 
"\xF3\xA0\xB7\xAC" => "",                     # U+E0DEC => 
"\xF3\xA0\xB7\xAD" => "",                     # U+E0DED => 
"\xF3\xA0\xB7\xAE" => "",                     # U+E0DEE => 
"\xF3\xA0\xB7\xAF" => "",                     # U+E0DEF => 
"\xF3\xA0\xB7\xB0" => "",                     # U+E0DF0 => 
"\xF3\xA0\xB7\xB1" => "",                     # U+E0DF1 => 
"\xF3\xA0\xB7\xB2" => "",                     # U+E0DF2 => 
"\xF3\xA0\xB7\xB3" => "",                     # U+E0DF3 => 
"\xF3\xA0\xB7\xB4" => "",                     # U+E0DF4 => 
"\xF3\xA0\xB7\xB5" => "",                     # U+E0DF5 => 
"\xF3\xA0\xB7\xB6" => "",                     # U+E0DF6 => 
"\xF3\xA0\xB7\xB7" => "",                     # U+E0DF7 => 
"\xF3\xA0\xB7\xB8" => "",                     # U+E0DF8 => 
"\xF3\xA0\xB7\xB9" => "",                     # U+E0DF9 => 
"\xF3\xA0\xB7\xBA" => "",                     # U+E0DFA => 
"\xF3\xA0\xB7\xBB" => "",                     # U+E0DFB => 
"\xF3\xA0\xB7\xBC" => "",                     # U+E0DFC => 
"\xF3\xA0\xB7\xBD" => "",                     # U+E0DFD => 
"\xF3\xA0\xB7\xBE" => "",                     # U+E0DFE => 
"\xF3\xA0\xB7\xBF" => "",                     # U+E0DFF => 
"\xF3\xA0\xB8\x80" => "",                     # U+E0E00 => 
"\xF3\xA0\xB8\x81" => "",                     # U+E0E01 => 
"\xF3\xA0\xB8\x82" => "",                     # U+E0E02 => 
"\xF3\xA0\xB8\x83" => "",                     # U+E0E03 => 
"\xF3\xA0\xB8\x84" => "",                     # U+E0E04 => 
"\xF3\xA0\xB8\x85" => "",                     # U+E0E05 => 
"\xF3\xA0\xB8\x86" => "",                     # U+E0E06 => 
"\xF3\xA0\xB8\x87" => "",                     # U+E0E07 => 
"\xF3\xA0\xB8\x88" => "",                     # U+E0E08 => 
"\xF3\xA0\xB8\x89" => "",                     # U+E0E09 => 
"\xF3\xA0\xB8\x8A" => "",                     # U+E0E0A => 
"\xF3\xA0\xB8\x8B" => "",                     # U+E0E0B => 
"\xF3\xA0\xB8\x8C" => "",                     # U+E0E0C => 
"\xF3\xA0\xB8\x8D" => "",                     # U+E0E0D => 
"\xF3\xA0\xB8\x8E" => "",                     # U+E0E0E => 
"\xF3\xA0\xB8\x8F" => "",                     # U+E0E0F => 
"\xF3\xA0\xB8\x90" => "",                     # U+E0E10 => 
"\xF3\xA0\xB8\x91" => "",                     # U+E0E11 => 
"\xF3\xA0\xB8\x92" => "",                     # U+E0E12 => 
"\xF3\xA0\xB8\x93" => "",                     # U+E0E13 => 
"\xF3\xA0\xB8\x94" => "",                     # U+E0E14 => 
"\xF3\xA0\xB8\x95" => "",                     # U+E0E15 => 
"\xF3\xA0\xB8\x96" => "",                     # U+E0E16 => 
"\xF3\xA0\xB8\x97" => "",                     # U+E0E17 => 
"\xF3\xA0\xB8\x98" => "",                     # U+E0E18 => 
"\xF3\xA0\xB8\x99" => "",                     # U+E0E19 => 
"\xF3\xA0\xB8\x9A" => "",                     # U+E0E1A => 
"\xF3\xA0\xB8\x9B" => "",                     # U+E0E1B => 
"\xF3\xA0\xB8\x9C" => "",                     # U+E0E1C => 
"\xF3\xA0\xB8\x9D" => "",                     # U+E0E1D => 
"\xF3\xA0\xB8\x9E" => "",                     # U+E0E1E => 
"\xF3\xA0\xB8\x9F" => "",                     # U+E0E1F => 
"\xF3\xA0\xB8\xA0" => "",                     # U+E0E20 => 
"\xF3\xA0\xB8\xA1" => "",                     # U+E0E21 => 
"\xF3\xA0\xB8\xA2" => "",                     # U+E0E22 => 
"\xF3\xA0\xB8\xA3" => "",                     # U+E0E23 => 
"\xF3\xA0\xB8\xA4" => "",                     # U+E0E24 => 
"\xF3\xA0\xB8\xA5" => "",                     # U+E0E25 => 
"\xF3\xA0\xB8\xA6" => "",                     # U+E0E26 => 
"\xF3\xA0\xB8\xA7" => "",                     # U+E0E27 => 
"\xF3\xA0\xB8\xA8" => "",                     # U+E0E28 => 
"\xF3\xA0\xB8\xA9" => "",                     # U+E0E29 => 
"\xF3\xA0\xB8\xAA" => "",                     # U+E0E2A => 
"\xF3\xA0\xB8\xAB" => "",                     # U+E0E2B => 
"\xF3\xA0\xB8\xAC" => "",                     # U+E0E2C => 
"\xF3\xA0\xB8\xAD" => "",                     # U+E0E2D => 
"\xF3\xA0\xB8\xAE" => "",                     # U+E0E2E => 
"\xF3\xA0\xB8\xAF" => "",                     # U+E0E2F => 
"\xF3\xA0\xB8\xB0" => "",                     # U+E0E30 => 
"\xF3\xA0\xB8\xB1" => "",                     # U+E0E31 => 
"\xF3\xA0\xB8\xB2" => "",                     # U+E0E32 => 
"\xF3\xA0\xB8\xB3" => "",                     # U+E0E33 => 
"\xF3\xA0\xB8\xB4" => "",                     # U+E0E34 => 
"\xF3\xA0\xB8\xB5" => "",                     # U+E0E35 => 
"\xF3\xA0\xB8\xB6" => "",                     # U+E0E36 => 
"\xF3\xA0\xB8\xB7" => "",                     # U+E0E37 => 
"\xF3\xA0\xB8\xB8" => "",                     # U+E0E38 => 
"\xF3\xA0\xB8\xB9" => "",                     # U+E0E39 => 
"\xF3\xA0\xB8\xBA" => "",                     # U+E0E3A => 
"\xF3\xA0\xB8\xBB" => "",                     # U+E0E3B => 
"\xF3\xA0\xB8\xBC" => "",                     # U+E0E3C => 
"\xF3\xA0\xB8\xBD" => "",                     # U+E0E3D => 
"\xF3\xA0\xB8\xBE" => "",                     # U+E0E3E => 
"\xF3\xA0\xB8\xBF" => "",                     # U+E0E3F => 
"\xF3\xA0\xB9\x80" => "",                     # U+E0E40 => 
"\xF3\xA0\xB9\x81" => "",                     # U+E0E41 => 
"\xF3\xA0\xB9\x82" => "",                     # U+E0E42 => 
"\xF3\xA0\xB9\x83" => "",                     # U+E0E43 => 
"\xF3\xA0\xB9\x84" => "",                     # U+E0E44 => 
"\xF3\xA0\xB9\x85" => "",                     # U+E0E45 => 
"\xF3\xA0\xB9\x86" => "",                     # U+E0E46 => 
"\xF3\xA0\xB9\x87" => "",                     # U+E0E47 => 
"\xF3\xA0\xB9\x88" => "",                     # U+E0E48 => 
"\xF3\xA0\xB9\x89" => "",                     # U+E0E49 => 
"\xF3\xA0\xB9\x8A" => "",                     # U+E0E4A => 
"\xF3\xA0\xB9\x8B" => "",                     # U+E0E4B => 
"\xF3\xA0\xB9\x8C" => "",                     # U+E0E4C => 
"\xF3\xA0\xB9\x8D" => "",                     # U+E0E4D => 
"\xF3\xA0\xB9\x8E" => "",                     # U+E0E4E => 
"\xF3\xA0\xB9\x8F" => "",                     # U+E0E4F => 
"\xF3\xA0\xB9\x90" => "",                     # U+E0E50 => 
"\xF3\xA0\xB9\x91" => "",                     # U+E0E51 => 
"\xF3\xA0\xB9\x92" => "",                     # U+E0E52 => 
"\xF3\xA0\xB9\x93" => "",                     # U+E0E53 => 
"\xF3\xA0\xB9\x94" => "",                     # U+E0E54 => 
"\xF3\xA0\xB9\x95" => "",                     # U+E0E55 => 
"\xF3\xA0\xB9\x96" => "",                     # U+E0E56 => 
"\xF3\xA0\xB9\x97" => "",                     # U+E0E57 => 
"\xF3\xA0\xB9\x98" => "",                     # U+E0E58 => 
"\xF3\xA0\xB9\x99" => "",                     # U+E0E59 => 
"\xF3\xA0\xB9\x9A" => "",                     # U+E0E5A => 
"\xF3\xA0\xB9\x9B" => "",                     # U+E0E5B => 
"\xF3\xA0\xB9\x9C" => "",                     # U+E0E5C => 
"\xF3\xA0\xB9\x9D" => "",                     # U+E0E5D => 
"\xF3\xA0\xB9\x9E" => "",                     # U+E0E5E => 
"\xF3\xA0\xB9\x9F" => "",                     # U+E0E5F => 
"\xF3\xA0\xB9\xA0" => "",                     # U+E0E60 => 
"\xF3\xA0\xB9\xA1" => "",                     # U+E0E61 => 
"\xF3\xA0\xB9\xA2" => "",                     # U+E0E62 => 
"\xF3\xA0\xB9\xA3" => "",                     # U+E0E63 => 
"\xF3\xA0\xB9\xA4" => "",                     # U+E0E64 => 
"\xF3\xA0\xB9\xA5" => "",                     # U+E0E65 => 
"\xF3\xA0\xB9\xA6" => "",                     # U+E0E66 => 
"\xF3\xA0\xB9\xA7" => "",                     # U+E0E67 => 
"\xF3\xA0\xB9\xA8" => "",                     # U+E0E68 => 
"\xF3\xA0\xB9\xA9" => "",                     # U+E0E69 => 
"\xF3\xA0\xB9\xAA" => "",                     # U+E0E6A => 
"\xF3\xA0\xB9\xAB" => "",                     # U+E0E6B => 
"\xF3\xA0\xB9\xAC" => "",                     # U+E0E6C => 
"\xF3\xA0\xB9\xAD" => "",                     # U+E0E6D => 
"\xF3\xA0\xB9\xAE" => "",                     # U+E0E6E => 
"\xF3\xA0\xB9\xAF" => "",                     # U+E0E6F => 
"\xF3\xA0\xB9\xB0" => "",                     # U+E0E70 => 
"\xF3\xA0\xB9\xB1" => "",                     # U+E0E71 => 
"\xF3\xA0\xB9\xB2" => "",                     # U+E0E72 => 
"\xF3\xA0\xB9\xB3" => "",                     # U+E0E73 => 
"\xF3\xA0\xB9\xB4" => "",                     # U+E0E74 => 
"\xF3\xA0\xB9\xB5" => "",                     # U+E0E75 => 
"\xF3\xA0\xB9\xB6" => "",                     # U+E0E76 => 
"\xF3\xA0\xB9\xB7" => "",                     # U+E0E77 => 
"\xF3\xA0\xB9\xB8" => "",                     # U+E0E78 => 
"\xF3\xA0\xB9\xB9" => "",                     # U+E0E79 => 
"\xF3\xA0\xB9\xBA" => "",                     # U+E0E7A => 
"\xF3\xA0\xB9\xBB" => "",                     # U+E0E7B => 
"\xF3\xA0\xB9\xBC" => "",                     # U+E0E7C => 
"\xF3\xA0\xB9\xBD" => "",                     # U+E0E7D => 
"\xF3\xA0\xB9\xBE" => "",                     # U+E0E7E => 
"\xF3\xA0\xB9\xBF" => "",                     # U+E0E7F => 
"\xF3\xA0\xBA\x80" => "",                     # U+E0E80 => 
"\xF3\xA0\xBA\x81" => "",                     # U+E0E81 => 
"\xF3\xA0\xBA\x82" => "",                     # U+E0E82 => 
"\xF3\xA0\xBA\x83" => "",                     # U+E0E83 => 
"\xF3\xA0\xBA\x84" => "",                     # U+E0E84 => 
"\xF3\xA0\xBA\x85" => "",                     # U+E0E85 => 
"\xF3\xA0\xBA\x86" => "",                     # U+E0E86 => 
"\xF3\xA0\xBA\x87" => "",                     # U+E0E87 => 
"\xF3\xA0\xBA\x88" => "",                     # U+E0E88 => 
"\xF3\xA0\xBA\x89" => "",                     # U+E0E89 => 
"\xF3\xA0\xBA\x8A" => "",                     # U+E0E8A => 
"\xF3\xA0\xBA\x8B" => "",                     # U+E0E8B => 
"\xF3\xA0\xBA\x8C" => "",                     # U+E0E8C => 
"\xF3\xA0\xBA\x8D" => "",                     # U+E0E8D => 
"\xF3\xA0\xBA\x8E" => "",                     # U+E0E8E => 
"\xF3\xA0\xBA\x8F" => "",                     # U+E0E8F => 
"\xF3\xA0\xBA\x90" => "",                     # U+E0E90 => 
"\xF3\xA0\xBA\x91" => "",                     # U+E0E91 => 
"\xF3\xA0\xBA\x92" => "",                     # U+E0E92 => 
"\xF3\xA0\xBA\x93" => "",                     # U+E0E93 => 
"\xF3\xA0\xBA\x94" => "",                     # U+E0E94 => 
"\xF3\xA0\xBA\x95" => "",                     # U+E0E95 => 
"\xF3\xA0\xBA\x96" => "",                     # U+E0E96 => 
"\xF3\xA0\xBA\x97" => "",                     # U+E0E97 => 
"\xF3\xA0\xBA\x98" => "",                     # U+E0E98 => 
"\xF3\xA0\xBA\x99" => "",                     # U+E0E99 => 
"\xF3\xA0\xBA\x9A" => "",                     # U+E0E9A => 
"\xF3\xA0\xBA\x9B" => "",                     # U+E0E9B => 
"\xF3\xA0\xBA\x9C" => "",                     # U+E0E9C => 
"\xF3\xA0\xBA\x9D" => "",                     # U+E0E9D => 
"\xF3\xA0\xBA\x9E" => "",                     # U+E0E9E => 
"\xF3\xA0\xBA\x9F" => "",                     # U+E0E9F => 
"\xF3\xA0\xBA\xA0" => "",                     # U+E0EA0 => 
"\xF3\xA0\xBA\xA1" => "",                     # U+E0EA1 => 
"\xF3\xA0\xBA\xA2" => "",                     # U+E0EA2 => 
"\xF3\xA0\xBA\xA3" => "",                     # U+E0EA3 => 
"\xF3\xA0\xBA\xA4" => "",                     # U+E0EA4 => 
"\xF3\xA0\xBA\xA5" => "",                     # U+E0EA5 => 
"\xF3\xA0\xBA\xA6" => "",                     # U+E0EA6 => 
"\xF3\xA0\xBA\xA7" => "",                     # U+E0EA7 => 
"\xF3\xA0\xBA\xA8" => "",                     # U+E0EA8 => 
"\xF3\xA0\xBA\xA9" => "",                     # U+E0EA9 => 
"\xF3\xA0\xBA\xAA" => "",                     # U+E0EAA => 
"\xF3\xA0\xBA\xAB" => "",                     # U+E0EAB => 
"\xF3\xA0\xBA\xAC" => "",                     # U+E0EAC => 
"\xF3\xA0\xBA\xAD" => "",                     # U+E0EAD => 
"\xF3\xA0\xBA\xAE" => "",                     # U+E0EAE => 
"\xF3\xA0\xBA\xAF" => "",                     # U+E0EAF => 
"\xF3\xA0\xBA\xB0" => "",                     # U+E0EB0 => 
"\xF3\xA0\xBA\xB1" => "",                     # U+E0EB1 => 
"\xF3\xA0\xBA\xB2" => "",                     # U+E0EB2 => 
"\xF3\xA0\xBA\xB3" => "",                     # U+E0EB3 => 
"\xF3\xA0\xBA\xB4" => "",                     # U+E0EB4 => 
"\xF3\xA0\xBA\xB5" => "",                     # U+E0EB5 => 
"\xF3\xA0\xBA\xB6" => "",                     # U+E0EB6 => 
"\xF3\xA0\xBA\xB7" => "",                     # U+E0EB7 => 
"\xF3\xA0\xBA\xB8" => "",                     # U+E0EB8 => 
"\xF3\xA0\xBA\xB9" => "",                     # U+E0EB9 => 
"\xF3\xA0\xBA\xBA" => "",                     # U+E0EBA => 
"\xF3\xA0\xBA\xBB" => "",                     # U+E0EBB => 
"\xF3\xA0\xBA\xBC" => "",                     # U+E0EBC => 
"\xF3\xA0\xBA\xBD" => "",                     # U+E0EBD => 
"\xF3\xA0\xBA\xBE" => "",                     # U+E0EBE => 
"\xF3\xA0\xBA\xBF" => "",                     # U+E0EBF => 
"\xF3\xA0\xBB\x80" => "",                     # U+E0EC0 => 
"\xF3\xA0\xBB\x81" => "",                     # U+E0EC1 => 
"\xF3\xA0\xBB\x82" => "",                     # U+E0EC2 => 
"\xF3\xA0\xBB\x83" => "",                     # U+E0EC3 => 
"\xF3\xA0\xBB\x84" => "",                     # U+E0EC4 => 
"\xF3\xA0\xBB\x85" => "",                     # U+E0EC5 => 
"\xF3\xA0\xBB\x86" => "",                     # U+E0EC6 => 
"\xF3\xA0\xBB\x87" => "",                     # U+E0EC7 => 
"\xF3\xA0\xBB\x88" => "",                     # U+E0EC8 => 
"\xF3\xA0\xBB\x89" => "",                     # U+E0EC9 => 
"\xF3\xA0\xBB\x8A" => "",                     # U+E0ECA => 
"\xF3\xA0\xBB\x8B" => "",                     # U+E0ECB => 
"\xF3\xA0\xBB\x8C" => "",                     # U+E0ECC => 
"\xF3\xA0\xBB\x8D" => "",                     # U+E0ECD => 
"\xF3\xA0\xBB\x8E" => "",                     # U+E0ECE => 
"\xF3\xA0\xBB\x8F" => "",                     # U+E0ECF => 
"\xF3\xA0\xBB\x90" => "",                     # U+E0ED0 => 
"\xF3\xA0\xBB\x91" => "",                     # U+E0ED1 => 
"\xF3\xA0\xBB\x92" => "",                     # U+E0ED2 => 
"\xF3\xA0\xBB\x93" => "",                     # U+E0ED3 => 
"\xF3\xA0\xBB\x94" => "",                     # U+E0ED4 => 
"\xF3\xA0\xBB\x95" => "",                     # U+E0ED5 => 
"\xF3\xA0\xBB\x96" => "",                     # U+E0ED6 => 
"\xF3\xA0\xBB\x97" => "",                     # U+E0ED7 => 
"\xF3\xA0\xBB\x98" => "",                     # U+E0ED8 => 
"\xF3\xA0\xBB\x99" => "",                     # U+E0ED9 => 
"\xF3\xA0\xBB\x9A" => "",                     # U+E0EDA => 
"\xF3\xA0\xBB\x9B" => "",                     # U+E0EDB => 
"\xF3\xA0\xBB\x9C" => "",                     # U+E0EDC => 
"\xF3\xA0\xBB\x9D" => "",                     # U+E0EDD => 
"\xF3\xA0\xBB\x9E" => "",                     # U+E0EDE => 
"\xF3\xA0\xBB\x9F" => "",                     # U+E0EDF => 
"\xF3\xA0\xBB\xA0" => "",                     # U+E0EE0 => 
"\xF3\xA0\xBB\xA1" => "",                     # U+E0EE1 => 
"\xF3\xA0\xBB\xA2" => "",                     # U+E0EE2 => 
"\xF3\xA0\xBB\xA3" => "",                     # U+E0EE3 => 
"\xF3\xA0\xBB\xA4" => "",                     # U+E0EE4 => 
"\xF3\xA0\xBB\xA5" => "",                     # U+E0EE5 => 
"\xF3\xA0\xBB\xA6" => "",                     # U+E0EE6 => 
"\xF3\xA0\xBB\xA7" => "",                     # U+E0EE7 => 
"\xF3\xA0\xBB\xA8" => "",                     # U+E0EE8 => 
"\xF3\xA0\xBB\xA9" => "",                     # U+E0EE9 => 
"\xF3\xA0\xBB\xAA" => "",                     # U+E0EEA => 
"\xF3\xA0\xBB\xAB" => "",                     # U+E0EEB => 
"\xF3\xA0\xBB\xAC" => "",                     # U+E0EEC => 
"\xF3\xA0\xBB\xAD" => "",                     # U+E0EED => 
"\xF3\xA0\xBB\xAE" => "",                     # U+E0EEE => 
"\xF3\xA0\xBB\xAF" => "",                     # U+E0EEF => 
"\xF3\xA0\xBB\xB0" => "",                     # U+E0EF0 => 
"\xF3\xA0\xBB\xB1" => "",                     # U+E0EF1 => 
"\xF3\xA0\xBB\xB2" => "",                     # U+E0EF2 => 
"\xF3\xA0\xBB\xB3" => "",                     # U+E0EF3 => 
"\xF3\xA0\xBB\xB4" => "",                     # U+E0EF4 => 
"\xF3\xA0\xBB\xB5" => "",                     # U+E0EF5 => 
"\xF3\xA0\xBB\xB6" => "",                     # U+E0EF6 => 
"\xF3\xA0\xBB\xB7" => "",                     # U+E0EF7 => 
"\xF3\xA0\xBB\xB8" => "",                     # U+E0EF8 => 
"\xF3\xA0\xBB\xB9" => "",                     # U+E0EF9 => 
"\xF3\xA0\xBB\xBA" => "",                     # U+E0EFA => 
"\xF3\xA0\xBB\xBB" => "",                     # U+E0EFB => 
"\xF3\xA0\xBB\xBC" => "",                     # U+E0EFC => 
"\xF3\xA0\xBB\xBD" => "",                     # U+E0EFD => 
"\xF3\xA0\xBB\xBE" => "",                     # U+E0EFE => 
"\xF3\xA0\xBB\xBF" => "",                     # U+E0EFF => 
"\xF3\xA0\xBC\x80" => "",                     # U+E0F00 => 
"\xF3\xA0\xBC\x81" => "",                     # U+E0F01 => 
"\xF3\xA0\xBC\x82" => "",                     # U+E0F02 => 
"\xF3\xA0\xBC\x83" => "",                     # U+E0F03 => 
"\xF3\xA0\xBC\x84" => "",                     # U+E0F04 => 
"\xF3\xA0\xBC\x85" => "",                     # U+E0F05 => 
"\xF3\xA0\xBC\x86" => "",                     # U+E0F06 => 
"\xF3\xA0\xBC\x87" => "",                     # U+E0F07 => 
"\xF3\xA0\xBC\x88" => "",                     # U+E0F08 => 
"\xF3\xA0\xBC\x89" => "",                     # U+E0F09 => 
"\xF3\xA0\xBC\x8A" => "",                     # U+E0F0A => 
"\xF3\xA0\xBC\x8B" => "",                     # U+E0F0B => 
"\xF3\xA0\xBC\x8C" => "",                     # U+E0F0C => 
"\xF3\xA0\xBC\x8D" => "",                     # U+E0F0D => 
"\xF3\xA0\xBC\x8E" => "",                     # U+E0F0E => 
"\xF3\xA0\xBC\x8F" => "",                     # U+E0F0F => 
"\xF3\xA0\xBC\x90" => "",                     # U+E0F10 => 
"\xF3\xA0\xBC\x91" => "",                     # U+E0F11 => 
"\xF3\xA0\xBC\x92" => "",                     # U+E0F12 => 
"\xF3\xA0\xBC\x93" => "",                     # U+E0F13 => 
"\xF3\xA0\xBC\x94" => "",                     # U+E0F14 => 
"\xF3\xA0\xBC\x95" => "",                     # U+E0F15 => 
"\xF3\xA0\xBC\x96" => "",                     # U+E0F16 => 
"\xF3\xA0\xBC\x97" => "",                     # U+E0F17 => 
"\xF3\xA0\xBC\x98" => "",                     # U+E0F18 => 
"\xF3\xA0\xBC\x99" => "",                     # U+E0F19 => 
"\xF3\xA0\xBC\x9A" => "",                     # U+E0F1A => 
"\xF3\xA0\xBC\x9B" => "",                     # U+E0F1B => 
"\xF3\xA0\xBC\x9C" => "",                     # U+E0F1C => 
"\xF3\xA0\xBC\x9D" => "",                     # U+E0F1D => 
"\xF3\xA0\xBC\x9E" => "",                     # U+E0F1E => 
"\xF3\xA0\xBC\x9F" => "",                     # U+E0F1F => 
"\xF3\xA0\xBC\xA0" => "",                     # U+E0F20 => 
"\xF3\xA0\xBC\xA1" => "",                     # U+E0F21 => 
"\xF3\xA0\xBC\xA2" => "",                     # U+E0F22 => 
"\xF3\xA0\xBC\xA3" => "",                     # U+E0F23 => 
"\xF3\xA0\xBC\xA4" => "",                     # U+E0F24 => 
"\xF3\xA0\xBC\xA5" => "",                     # U+E0F25 => 
"\xF3\xA0\xBC\xA6" => "",                     # U+E0F26 => 
"\xF3\xA0\xBC\xA7" => "",                     # U+E0F27 => 
"\xF3\xA0\xBC\xA8" => "",                     # U+E0F28 => 
"\xF3\xA0\xBC\xA9" => "",                     # U+E0F29 => 
"\xF3\xA0\xBC\xAA" => "",                     # U+E0F2A => 
"\xF3\xA0\xBC\xAB" => "",                     # U+E0F2B => 
"\xF3\xA0\xBC\xAC" => "",                     # U+E0F2C => 
"\xF3\xA0\xBC\xAD" => "",                     # U+E0F2D => 
"\xF3\xA0\xBC\xAE" => "",                     # U+E0F2E => 
"\xF3\xA0\xBC\xAF" => "",                     # U+E0F2F => 
"\xF3\xA0\xBC\xB0" => "",                     # U+E0F30 => 
"\xF3\xA0\xBC\xB1" => "",                     # U+E0F31 => 
"\xF3\xA0\xBC\xB2" => "",                     # U+E0F32 => 
"\xF3\xA0\xBC\xB3" => "",                     # U+E0F33 => 
"\xF3\xA0\xBC\xB4" => "",                     # U+E0F34 => 
"\xF3\xA0\xBC\xB5" => "",                     # U+E0F35 => 
"\xF3\xA0\xBC\xB6" => "",                     # U+E0F36 => 
"\xF3\xA0\xBC\xB7" => "",                     # U+E0F37 => 
"\xF3\xA0\xBC\xB8" => "",                     # U+E0F38 => 
"\xF3\xA0\xBC\xB9" => "",                     # U+E0F39 => 
"\xF3\xA0\xBC\xBA" => "",                     # U+E0F3A => 
"\xF3\xA0\xBC\xBB" => "",                     # U+E0F3B => 
"\xF3\xA0\xBC\xBC" => "",                     # U+E0F3C => 
"\xF3\xA0\xBC\xBD" => "",                     # U+E0F3D => 
"\xF3\xA0\xBC\xBE" => "",                     # U+E0F3E => 
"\xF3\xA0\xBC\xBF" => "",                     # U+E0F3F => 
"\xF3\xA0\xBD\x80" => "",                     # U+E0F40 => 
"\xF3\xA0\xBD\x81" => "",                     # U+E0F41 => 
"\xF3\xA0\xBD\x82" => "",                     # U+E0F42 => 
"\xF3\xA0\xBD\x83" => "",                     # U+E0F43 => 
"\xF3\xA0\xBD\x84" => "",                     # U+E0F44 => 
"\xF3\xA0\xBD\x85" => "",                     # U+E0F45 => 
"\xF3\xA0\xBD\x86" => "",                     # U+E0F46 => 
"\xF3\xA0\xBD\x87" => "",                     # U+E0F47 => 
"\xF3\xA0\xBD\x88" => "",                     # U+E0F48 => 
"\xF3\xA0\xBD\x89" => "",                     # U+E0F49 => 
"\xF3\xA0\xBD\x8A" => "",                     # U+E0F4A => 
"\xF3\xA0\xBD\x8B" => "",                     # U+E0F4B => 
"\xF3\xA0\xBD\x8C" => "",                     # U+E0F4C => 
"\xF3\xA0\xBD\x8D" => "",                     # U+E0F4D => 
"\xF3\xA0\xBD\x8E" => "",                     # U+E0F4E => 
"\xF3\xA0\xBD\x8F" => "",                     # U+E0F4F => 
"\xF3\xA0\xBD\x90" => "",                     # U+E0F50 => 
"\xF3\xA0\xBD\x91" => "",                     # U+E0F51 => 
"\xF3\xA0\xBD\x92" => "",                     # U+E0F52 => 
"\xF3\xA0\xBD\x93" => "",                     # U+E0F53 => 
"\xF3\xA0\xBD\x94" => "",                     # U+E0F54 => 
"\xF3\xA0\xBD\x95" => "",                     # U+E0F55 => 
"\xF3\xA0\xBD\x96" => "",                     # U+E0F56 => 
"\xF3\xA0\xBD\x97" => "",                     # U+E0F57 => 
"\xF3\xA0\xBD\x98" => "",                     # U+E0F58 => 
"\xF3\xA0\xBD\x99" => "",                     # U+E0F59 => 
"\xF3\xA0\xBD\x9A" => "",                     # U+E0F5A => 
"\xF3\xA0\xBD\x9B" => "",                     # U+E0F5B => 
"\xF3\xA0\xBD\x9C" => "",                     # U+E0F5C => 
"\xF3\xA0\xBD\x9D" => "",                     # U+E0F5D => 
"\xF3\xA0\xBD\x9E" => "",                     # U+E0F5E => 
"\xF3\xA0\xBD\x9F" => "",                     # U+E0F5F => 
"\xF3\xA0\xBD\xA0" => "",                     # U+E0F60 => 
"\xF3\xA0\xBD\xA1" => "",                     # U+E0F61 => 
"\xF3\xA0\xBD\xA2" => "",                     # U+E0F62 => 
"\xF3\xA0\xBD\xA3" => "",                     # U+E0F63 => 
"\xF3\xA0\xBD\xA4" => "",                     # U+E0F64 => 
"\xF3\xA0\xBD\xA5" => "",                     # U+E0F65 => 
"\xF3\xA0\xBD\xA6" => "",                     # U+E0F66 => 
"\xF3\xA0\xBD\xA7" => "",                     # U+E0F67 => 
"\xF3\xA0\xBD\xA8" => "",                     # U+E0F68 => 
"\xF3\xA0\xBD\xA9" => "",                     # U+E0F69 => 
"\xF3\xA0\xBD\xAA" => "",                     # U+E0F6A => 
"\xF3\xA0\xBD\xAB" => "",                     # U+E0F6B => 
"\xF3\xA0\xBD\xAC" => "",                     # U+E0F6C => 
"\xF3\xA0\xBD\xAD" => "",                     # U+E0F6D => 
"\xF3\xA0\xBD\xAE" => "",                     # U+E0F6E => 
"\xF3\xA0\xBD\xAF" => "",                     # U+E0F6F => 
"\xF3\xA0\xBD\xB0" => "",                     # U+E0F70 => 
"\xF3\xA0\xBD\xB1" => "",                     # U+E0F71 => 
"\xF3\xA0\xBD\xB2" => "",                     # U+E0F72 => 
"\xF3\xA0\xBD\xB3" => "",                     # U+E0F73 => 
"\xF3\xA0\xBD\xB4" => "",                     # U+E0F74 => 
"\xF3\xA0\xBD\xB5" => "",                     # U+E0F75 => 
"\xF3\xA0\xBD\xB6" => "",                     # U+E0F76 => 
"\xF3\xA0\xBD\xB7" => "",                     # U+E0F77 => 
"\xF3\xA0\xBD\xB8" => "",                     # U+E0F78 => 
"\xF3\xA0\xBD\xB9" => "",                     # U+E0F79 => 
"\xF3\xA0\xBD\xBA" => "",                     # U+E0F7A => 
"\xF3\xA0\xBD\xBB" => "",                     # U+E0F7B => 
"\xF3\xA0\xBD\xBC" => "",                     # U+E0F7C => 
"\xF3\xA0\xBD\xBD" => "",                     # U+E0F7D => 
"\xF3\xA0\xBD\xBE" => "",                     # U+E0F7E => 
"\xF3\xA0\xBD\xBF" => "",                     # U+E0F7F => 
"\xF3\xA0\xBE\x80" => "",                     # U+E0F80 => 
"\xF3\xA0\xBE\x81" => "",                     # U+E0F81 => 
"\xF3\xA0\xBE\x82" => "",                     # U+E0F82 => 
"\xF3\xA0\xBE\x83" => "",                     # U+E0F83 => 
"\xF3\xA0\xBE\x84" => "",                     # U+E0F84 => 
"\xF3\xA0\xBE\x85" => "",                     # U+E0F85 => 
"\xF3\xA0\xBE\x86" => "",                     # U+E0F86 => 
"\xF3\xA0\xBE\x87" => "",                     # U+E0F87 => 
"\xF3\xA0\xBE\x88" => "",                     # U+E0F88 => 
"\xF3\xA0\xBE\x89" => "",                     # U+E0F89 => 
"\xF3\xA0\xBE\x8A" => "",                     # U+E0F8A => 
"\xF3\xA0\xBE\x8B" => "",                     # U+E0F8B => 
"\xF3\xA0\xBE\x8C" => "",                     # U+E0F8C => 
"\xF3\xA0\xBE\x8D" => "",                     # U+E0F8D => 
"\xF3\xA0\xBE\x8E" => "",                     # U+E0F8E => 
"\xF3\xA0\xBE\x8F" => "",                     # U+E0F8F => 
"\xF3\xA0\xBE\x90" => "",                     # U+E0F90 => 
"\xF3\xA0\xBE\x91" => "",                     # U+E0F91 => 
"\xF3\xA0\xBE\x92" => "",                     # U+E0F92 => 
"\xF3\xA0\xBE\x93" => "",                     # U+E0F93 => 
"\xF3\xA0\xBE\x94" => "",                     # U+E0F94 => 
"\xF3\xA0\xBE\x95" => "",                     # U+E0F95 => 
"\xF3\xA0\xBE\x96" => "",                     # U+E0F96 => 
"\xF3\xA0\xBE\x97" => "",                     # U+E0F97 => 
"\xF3\xA0\xBE\x98" => "",                     # U+E0F98 => 
"\xF3\xA0\xBE\x99" => "",                     # U+E0F99 => 
"\xF3\xA0\xBE\x9A" => "",                     # U+E0F9A => 
"\xF3\xA0\xBE\x9B" => "",                     # U+E0F9B => 
"\xF3\xA0\xBE\x9C" => "",                     # U+E0F9C => 
"\xF3\xA0\xBE\x9D" => "",                     # U+E0F9D => 
"\xF3\xA0\xBE\x9E" => "",                     # U+E0F9E => 
"\xF3\xA0\xBE\x9F" => "",                     # U+E0F9F => 
"\xF3\xA0\xBE\xA0" => "",                     # U+E0FA0 => 
"\xF3\xA0\xBE\xA1" => "",                     # U+E0FA1 => 
"\xF3\xA0\xBE\xA2" => "",                     # U+E0FA2 => 
"\xF3\xA0\xBE\xA3" => "",                     # U+E0FA3 => 
"\xF3\xA0\xBE\xA4" => "",                     # U+E0FA4 => 
"\xF3\xA0\xBE\xA5" => "",                     # U+E0FA5 => 
"\xF3\xA0\xBE\xA6" => "",                     # U+E0FA6 => 
"\xF3\xA0\xBE\xA7" => "",                     # U+E0FA7 => 
"\xF3\xA0\xBE\xA8" => "",                     # U+E0FA8 => 
"\xF3\xA0\xBE\xA9" => "",                     # U+E0FA9 => 
"\xF3\xA0\xBE\xAA" => "",                     # U+E0FAA => 
"\xF3\xA0\xBE\xAB" => "",                     # U+E0FAB => 
"\xF3\xA0\xBE\xAC" => "",                     # U+E0FAC => 
"\xF3\xA0\xBE\xAD" => "",                     # U+E0FAD => 
"\xF3\xA0\xBE\xAE" => "",                     # U+E0FAE => 
"\xF3\xA0\xBE\xAF" => "",                     # U+E0FAF => 
"\xF3\xA0\xBE\xB0" => "",                     # U+E0FB0 => 
"\xF3\xA0\xBE\xB1" => "",                     # U+E0FB1 => 
"\xF3\xA0\xBE\xB2" => "",                     # U+E0FB2 => 
"\xF3\xA0\xBE\xB3" => "",                     # U+E0FB3 => 
"\xF3\xA0\xBE\xB4" => "",                     # U+E0FB4 => 
"\xF3\xA0\xBE\xB5" => "",                     # U+E0FB5 => 
"\xF3\xA0\xBE\xB6" => "",                     # U+E0FB6 => 
"\xF3\xA0\xBE\xB7" => "",                     # U+E0FB7 => 
"\xF3\xA0\xBE\xB8" => "",                     # U+E0FB8 => 
"\xF3\xA0\xBE\xB9" => "",                     # U+E0FB9 => 
"\xF3\xA0\xBE\xBA" => "",                     # U+E0FBA => 
"\xF3\xA0\xBE\xBB" => "",                     # U+E0FBB => 
"\xF3\xA0\xBE\xBC" => "",                     # U+E0FBC => 
"\xF3\xA0\xBE\xBD" => "",                     # U+E0FBD => 
"\xF3\xA0\xBE\xBE" => "",                     # U+E0FBE => 
"\xF3\xA0\xBE\xBF" => "",                     # U+E0FBF => 
"\xF3\xA0\xBF\x80" => "",                     # U+E0FC0 => 
"\xF3\xA0\xBF\x81" => "",                     # U+E0FC1 => 
"\xF3\xA0\xBF\x82" => "",                     # U+E0FC2 => 
"\xF3\xA0\xBF\x83" => "",                     # U+E0FC3 => 
"\xF3\xA0\xBF\x84" => "",                     # U+E0FC4 => 
"\xF3\xA0\xBF\x85" => "",                     # U+E0FC5 => 
"\xF3\xA0\xBF\x86" => "",                     # U+E0FC6 => 
"\xF3\xA0\xBF\x87" => "",                     # U+E0FC7 => 
"\xF3\xA0\xBF\x88" => "",                     # U+E0FC8 => 
"\xF3\xA0\xBF\x89" => "",                     # U+E0FC9 => 
"\xF3\xA0\xBF\x8A" => "",                     # U+E0FCA => 
"\xF3\xA0\xBF\x8B" => "",                     # U+E0FCB => 
"\xF3\xA0\xBF\x8C" => "",                     # U+E0FCC => 
"\xF3\xA0\xBF\x8D" => "",                     # U+E0FCD => 
"\xF3\xA0\xBF\x8E" => "",                     # U+E0FCE => 
"\xF3\xA0\xBF\x8F" => "",                     # U+E0FCF => 
"\xF3\xA0\xBF\x90" => "",                     # U+E0FD0 => 
"\xF3\xA0\xBF\x91" => "",                     # U+E0FD1 => 
"\xF3\xA0\xBF\x92" => "",                     # U+E0FD2 => 
"\xF3\xA0\xBF\x93" => "",                     # U+E0FD3 => 
"\xF3\xA0\xBF\x94" => "",                     # U+E0FD4 => 
"\xF3\xA0\xBF\x95" => "",                     # U+E0FD5 => 
"\xF3\xA0\xBF\x96" => "",                     # U+E0FD6 => 
"\xF3\xA0\xBF\x97" => "",                     # U+E0FD7 => 
"\xF3\xA0\xBF\x98" => "",                     # U+E0FD8 => 
"\xF3\xA0\xBF\x99" => "",                     # U+E0FD9 => 
"\xF3\xA0\xBF\x9A" => "",                     # U+E0FDA => 
"\xF3\xA0\xBF\x9B" => "",                     # U+E0FDB => 
"\xF3\xA0\xBF\x9C" => "",                     # U+E0FDC => 
"\xF3\xA0\xBF\x9D" => "",                     # U+E0FDD => 
"\xF3\xA0\xBF\x9E" => "",                     # U+E0FDE => 
"\xF3\xA0\xBF\x9F" => "",                     # U+E0FDF => 
"\xF3\xA0\xBF\xA0" => "",                     # U+E0FE0 => 
"\xF3\xA0\xBF\xA1" => "",                     # U+E0FE1 => 
"\xF3\xA0\xBF\xA2" => "",                     # U+E0FE2 => 
"\xF3\xA0\xBF\xA3" => "",                     # U+E0FE3 => 
"\xF3\xA0\xBF\xA4" => "",                     # U+E0FE4 => 
"\xF3\xA0\xBF\xA5" => "",                     # U+E0FE5 => 
"\xF3\xA0\xBF\xA6" => "",                     # U+E0FE6 => 
"\xF3\xA0\xBF\xA7" => "",                     # U+E0FE7 => 
"\xF3\xA0\xBF\xA8" => "",                     # U+E0FE8 => 
"\xF3\xA0\xBF\xA9" => "",                     # U+E0FE9 => 
"\xF3\xA0\xBF\xAA" => "",                     # U+E0FEA => 
"\xF3\xA0\xBF\xAB" => "",                     # U+E0FEB => 
"\xF3\xA0\xBF\xAC" => "",                     # U+E0FEC => 
"\xF3\xA0\xBF\xAD" => "",                     # U+E0FED => 
"\xF3\xA0\xBF\xAE" => "",                     # U+E0FEE => 
"\xF3\xA0\xBF\xAF" => "",                     # U+E0FEF => 
"\xF3\xA0\xBF\xB0" => "",                     # U+E0FF0 => 
"\xF3\xA0\xBF\xB1" => "",                     # U+E0FF1 => 
"\xF3\xA0\xBF\xB2" => "",                     # U+E0FF2 => 
"\xF3\xA0\xBF\xB3" => "",                     # U+E0FF3 => 
"\xF3\xA0\xBF\xB4" => "",                     # U+E0FF4 => 
"\xF3\xA0\xBF\xB5" => "",                     # U+E0FF5 => 
"\xF3\xA0\xBF\xB6" => "",                     # U+E0FF6 => 
"\xF3\xA0\xBF\xB7" => "",                     # U+E0FF7 => 
"\xF3\xA0\xBF\xB8" => "",                     # U+E0FF8 => 
"\xF3\xA0\xBF\xB9" => "",                     # U+E0FF9 => 
"\xF3\xA0\xBF\xBA" => "",                     # U+E0FFA => 
"\xF3\xA0\xBF\xBB" => "",                     # U+E0FFB => 
"\xF3\xA0\xBF\xBC" => "",                     # U+E0FFC => 
"\xF3\xA0\xBF\xBD" => "",                     # U+E0FFD => 
"\xF3\xA0\xBF\xBE" => "",                     # U+E0FFE => 
"\xF3\xA0\xBF\xBF" => "",                     # U+E0FFF => 
);

return <<'END';
0041		0061
0042		0062
0043		0063
0044		0064
0045		0065
0046		0066
0047		0067
0048		0068
0049		0069
004A		006A
004B		006B
004C		006C
004D		006D
004E		006E
004F		006F
0050		0070
0051		0071
0052		0072
0053		0073
0054		0074
0055		0075
0056		0076
0057		0077
0058		0078
0059		0079
005A		007A
00A0		0020
00AA		0061
00B2		0032
00B3		0033
00B5		03BC
00B9		0031
00BA		006F
00C0		00E0
00C1		00E1
00C2		00E2
00C3		00E3
00C4		00E4
00C5		00E5
00C6		00E6
00C7		00E7
00C8		00E8
00C9		00E9
00CA		00EA
00CB		00EB
00CC		00EC
00CD		00ED
00CE		00EE
00CF		00EF
00D0		00F0
00D1		00F1
00D2		00F2
00D3		00F3
00D4		00F4
00D5		00F5
00D6		00F6
00D8		00F8
00D9		00F9
00DA		00FA
00DB		00FB
00DC		00FC
00DD		00FD
00DE		00FE
0100		0101
0102		0103
0104		0105
0106		0107
0108		0109
010A		010B
010C		010D
010E		010F
0110		0111
0112		0113
0114		0115
0116		0117
0118		0119
011A		011B
011C		011D
011E		011F
0120		0121
0122		0123
0124		0125
0126		0127
0128		0129
012A		012B
012C		012D
012E		012F
0134		0135
0136		0137
0139		013A
013B		013C
013D		013E
0141		0142
0143		0144
0145		0146
0147		0148
014A		014B
014C		014D
014E		014F
0150		0151
0152		0153
0154		0155
0156		0157
0158		0159
015A		015B
015C		015D
015E		015F
0160		0161
0162		0163
0164		0165
0166		0167
0168		0169
016A		016B
016C		016D
016E		016F
0170		0171
0172		0173
0174		0175
0176		0177
0178		00FF
0179		017A
017B		017C
017D		017E
017F		0073
0181		0253
0182		0183
0184		0185
0186		0254
0187		0188
0189		0256
018A		0257
018B		018C
018E		01DD
018F		0259
0190		025B
0191		0192
0193		0260
0194		0263
0196		0269
0197		0268
0198		0199
019C		026F
019D		0272
019F		0275
01A0		01A1
01A2		01A3
01A4		01A5
01A6		0280
01A7		01A8
01A9		0283
01AC		01AD
01AE		0288
01AF		01B0
01B1		028A
01B2		028B
01B3		01B4
01B5		01B6
01B7		0292
01B8		01B9
01BC		01BD
01CD		01CE
01CF		01D0
01D1		01D2
01D3		01D4
01D5		01D6
01D7		01D8
01D9		01DA
01DB		01DC
01DE		01DF
01E0		01E1
01E2		01E3
01E4		01E5
01E6		01E7
01E8		01E9
01EA		01EB
01EC		01ED
01EE		01EF
01F4		01F5
01F6		0195
01F7		01BF
01F8		01F9
01FA		01FB
01FC		01FD
01FE		01FF
0200		0201
0202		0203
0204		0205
0206		0207
0208		0209
020A		020B
020C		020D
020E		020F
0210		0211
0212		0213
0214		0215
0216		0217
0218		0219
021A		021B
021C		021D
021E		021F
0220		019E
0222		0223
0224		0225
0226		0227
0228		0229
022A		022B
022C		022D
022E		022F
0230		0231
0232		0233
023A		2C65
023B		023C
023D		019A
023E		2C66
0241		0242
0243		0180
0244		0289
0245		028C
0246		0247
0248		0249
024A		024B
024C		024D
024E		024F
02B0		0068
02B1		0266
02B2		006A
02B3		0072
02B4		0279
02B5		027B
02B6		0281
02B7		0077
02B8		0079
02E0		0263
02E1		006C
02E2		0073
02E3		0078
02E4		0295
0340		0300
0341		0301
0343		0313
0345		03B9
0370		0371
0372		0373
0374		02B9
0376		0377
037E		003B
037F		03F3
0386		03AC
0387		00B7
0388		03AD
0389		03AE
038A		03AF
038C		03CC
038E		03CD
038F		03CE
0391		03B1
0392		03B2
0393		03B3
0394		03B4
0395		03B5
0396		03B6
0397		03B7
0398		03B8
0399		03B9
039A		03BA
039B		03BB
039C		03BC
039D		03BD
039E		03BE
039F		03BF
03A0		03C0
03A1		03C1
03A3		03C3
03A4		03C4
03A5		03C5
03A6		03C6
03A7		03C7
03A8		03C8
03A9		03C9
03AA		03CA
03AB		03CB
03C2		03C3
03CF		03D7
03D0		03B2
03D1		03B8
03D2		03C5
03D3		03CD
03D4		03CB
03D5		03C6
03D6		03C0
03D8		03D9
03DA		03DB
03DC		03DD
03DE		03DF
03E0		03E1
03E2		03E3
03E4		03E5
03E6		03E7
03E8		03E9
03EA		03EB
03EC		03ED
03EE		03EF
03F0		03BA
03F1		03C1
03F2		03C3
03F4		03B8
03F5		03B5
03F7		03F8
03F9		03C3
03FA		03FB
03FD		037B
03FE		037C
03FF		037D
0400		0450
0401		0451
0402		0452
0403		0453
0404		0454
0405		0455
0406		0456
0407		0457
0408		0458
0409		0459
040A		045A
040B		045B
040C		045C
040D		045D
040E		045E
040F		045F
0410		0430
0411		0431
0412		0432
0413		0433
0414		0434
0415		0435
0416		0436
0417		0437
0418		0438
0419		0439
041A		043A
041B		043B
041C		043C
041D		043D
041E		043E
041F		043F
0420		0440
0421		0441
0422		0442
0423		0443
0424		0444
0425		0445
0426		0446
0427		0447
0428		0448
0429		0449
042A		044A
042B		044B
042C		044C
042D		044D
042E		044E
042F		044F
0460		0461
0462		0463
0464		0465
0466		0467
0468		0469
046A		046B
046C		046D
046E		046F
0470		0471
0472		0473
0474		0475
0476		0477
0478		0479
047A		047B
047C		047D
047E		047F
0480		0481
048A		048B
048C		048D
048E		048F
0490		0491
0492		0493
0494		0495
0496		0497
0498		0499
049A		049B
049C		049D
049E		049F
04A0		04A1
04A2		04A3
04A4		04A5
04A6		04A7
04A8		04A9
04AA		04AB
04AC		04AD
04AE		04AF
04B0		04B1
04B2		04B3
04B4		04B5
04B6		04B7
04B8		04B9
04BA		04BB
04BC		04BD
04BE		04BF
04C0		04CF
04C1		04C2
04C3		04C4
04C5		04C6
04C7		04C8
04C9		04CA
04CB		04CC
04CD		04CE
04D0		04D1
04D2		04D3
04D4		04D5
04D6		04D7
04D8		04D9
04DA		04DB
04DC		04DD
04DE		04DF
04E0		04E1
04E2		04E3
04E4		04E5
04E6		04E7
04E8		04E9
04EA		04EB
04EC		04ED
04EE		04EF
04F0		04F1
04F2		04F3
04F4		04F5
04F6		04F7
04F8		04F9
04FA		04FB
04FC		04FD
04FE		04FF
0500		0501
0502		0503
0504		0505
0506		0507
0508		0509
050A		050B
050C		050D
050E		050F
0510		0511
0512		0513
0514		0515
0516		0517
0518		0519
051A		051B
051C		051D
051E		051F
0520		0521
0522		0523
0524		0525
0526		0527
0528		0529
052A		052B
052C		052D
052E		052F
0531		0561
0532		0562
0533		0563
0534		0564
0535		0565
0536		0566
0537		0567
0538		0568
0539		0569
053A		056A
053B		056B
053C		056C
053D		056D
053E		056E
053F		056F
0540		0570
0541		0571
0542		0572
0543		0573
0544		0574
0545		0575
0546		0576
0547		0577
0548		0578
0549		0579
054A		057A
054B		057B
054C		057C
054D		057D
054E		057E
054F		057F
0550		0580
0551		0581
0552		0582
0553		0583
0554		0584
0555		0585
0556		0586
0F0C		0F0B
10A0		2D00
10A1		2D01
10A2		2D02
10A3		2D03
10A4		2D04
10A5		2D05
10A6		2D06
10A7		2D07
10A8		2D08
10A9		2D09
10AA		2D0A
10AB		2D0B
10AC		2D0C
10AD		2D0D
10AE		2D0E
10AF		2D0F
10B0		2D10
10B1		2D11
10B2		2D12
10B3		2D13
10B4		2D14
10B5		2D15
10B6		2D16
10B7		2D17
10B8		2D18
10B9		2D19
10BA		2D1A
10BB		2D1B
10BC		2D1C
10BD		2D1D
10BE		2D1E
10BF		2D1F
10C0		2D20
10C1		2D21
10C2		2D22
10C3		2D23
10C4		2D24
10C5		2D25
10C7		2D27
10CD		2D2D
10FC		10DC
13F8		13F0
13F9		13F1
13FA		13F2
13FB		13F3
13FC		13F4
13FD		13F5
1C80		0432
1C81		0434
1C82		043E
1C83		0441
1C84		0442
1C85		0442
1C86		044A
1C87		0463
1C88		A64B
1C90		10D0
1C91		10D1
1C92		10D2
1C93		10D3
1C94		10D4
1C95		10D5
1C96		10D6
1C97		10D7
1C98		10D8
1C99		10D9
1C9A		10DA
1C9B		10DB
1C9C		10DC
1C9D		10DD
1C9E		10DE
1C9F		10DF
1CA0		10E0
1CA1		10E1
1CA2		10E2
1CA3		10E3
1CA4		10E4
1CA5		10E5
1CA6		10E6
1CA7		10E7
1CA8		10E8
1CA9		10E9
1CAA		10EA
1CAB		10EB
1CAC		10EC
1CAD		10ED
1CAE		10EE
1CAF		10EF
1CB0		10F0
1CB1		10F1
1CB2		10F2
1CB3		10F3
1CB4		10F4
1CB5		10F5
1CB6		10F6
1CB7		10F7
1CB8		10F8
1CB9		10F9
1CBA		10FA
1CBD		10FD
1CBE		10FE
1CBF		10FF
1D2C		0061
1D2D		00E6
1D2E		0062
1D30		0064
1D31		0065
1D32		01DD
1D33		0067
1D34		0068
1D35		0069
1D36		006A
1D37		006B
1D38		006C
1D39		006D
1D3A		006E
1D3C		006F
1D3D		0223
1D3E		0070
1D3F		0072
1D40		0074
1D41		0075
1D42		0077
1D43		0061
1D44		0250
1D45		0251
1D46		1D02
1D47		0062
1D48		0064
1D49		0065
1D4A		0259
1D4B		025B
1D4C		025C
1D4D		0067
1D4F		006B
1D50		006D
1D51		014B
1D52		006F
1D53		0254
1D54		1D16
1D55		1D17
1D56		0070
1D57		0074
1D58		0075
1D59		1D1D
1D5A		026F
1D5B		0076
1D5C		1D25
1D5D		03B2
1D5E		03B3
1D5F		03B4
1D60		03C6
1D61		03C7
1D62		0069
1D63		0072
1D64		0075
1D65		0076
1D66		03B2
1D67		03B3
1D68		03C1
1D69		03C6
1D6A		03C7
1D78		043D
1D9B		0252
1D9C		0063
1D9D		0255
1D9E		00F0
1D9F		025C
1DA0		0066
1DA1		025F
1DA2		0261
1DA3		0265
1DA4		0268
1DA5		0269
1DA6		026A
1DA7		1D7B
1DA8		029D
1DA9		026D
1DAA		1D85
1DAB		029F
1DAC		0271
1DAD		0270
1DAE		0272
1DAF		0273
1DB0		0274
1DB1		0275
1DB2		0278
1DB3		0282
1DB4		0283
1DB5		01AB
1DB6		0289
1DB7		028A
1DB8		1D1C
1DB9		028B
1DBA		028C
1DBB		007A
1DBC		0290
1DBD		0291
1DBE		0292
1DBF		03B8
1E00		1E01
1E02		1E03
1E04		1E05
1E06		1E07
1E08		1E09
1E0A		1E0B
1E0C		1E0D
1E0E		1E0F
1E10		1E11
1E12		1E13
1E14		1E15
1E16		1E17
1E18		1E19
1E1A		1E1B
1E1C		1E1D
1E1E		1E1F
1E20		1E21
1E22		1E23
1E24		1E25
1E26		1E27
1E28		1E29
1E2A		1E2B
1E2C		1E2D
1E2E		1E2F
1E30		1E31
1E32		1E33
1E34		1E35
1E36		1E37
1E38		1E39
1E3A		1E3B
1E3C		1E3D
1E3E		1E3F
1E40		1E41
1E42		1E43
1E44		1E45
1E46		1E47
1E48		1E49
1E4A		1E4B
1E4C		1E4D
1E4E		1E4F
1E50		1E51
1E52		1E53
1E54		1E55
1E56		1E57
1E58		1E59
1E5A		1E5B
1E5C		1E5D
1E5E		1E5F
1E60		1E61
1E62		1E63
1E64		1E65
1E66		1E67
1E68		1E69
1E6A		1E6B
1E6C		1E6D
1E6E		1E6F
1E70		1E71
1E72		1E73
1E74		1E75
1E76		1E77
1E78		1E79
1E7A		1E7B
1E7C		1E7D
1E7E		1E7F
1E80		1E81
1E82		1E83
1E84		1E85
1E86		1E87
1E88		1E89
1E8A		1E8B
1E8C		1E8D
1E8E		1E8F
1E90		1E91
1E92		1E93
1E94		1E95
1E9B		1E61
1EA0		1EA1
1EA2		1EA3
1EA4		1EA5
1EA6		1EA7
1EA8		1EA9
1EAA		1EAB
1EAC		1EAD
1EAE		1EAF
1EB0		1EB1
1EB2		1EB3
1EB4		1EB5
1EB6		1EB7
1EB8		1EB9
1EBA		1EBB
1EBC		1EBD
1EBE		1EBF
1EC0		1EC1
1EC2		1EC3
1EC4		1EC5
1EC6		1EC7
1EC8		1EC9
1ECA		1ECB
1ECC		1ECD
1ECE		1ECF
1ED0		1ED1
1ED2		1ED3
1ED4		1ED5
1ED6		1ED7
1ED8		1ED9
1EDA		1EDB
1EDC		1EDD
1EDE		1EDF
1EE0		1EE1
1EE2		1EE3
1EE4		1EE5
1EE6		1EE7
1EE8		1EE9
1EEA		1EEB
1EEC		1EED
1EEE		1EEF
1EF0		1EF1
1EF2		1EF3
1EF4		1EF5
1EF6		1EF7
1EF8		1EF9
1EFA		1EFB
1EFC		1EFD
1EFE		1EFF
1F08		1F00
1F09		1F01
1F0A		1F02
1F0B		1F03
1F0C		1F04
1F0D		1F05
1F0E		1F06
1F0F		1F07
1F18		1F10
1F19		1F11
1F1A		1F12
1F1B		1F13
1F1C		1F14
1F1D		1F15
1F28		1F20
1F29		1F21
1F2A		1F22
1F2B		1F23
1F2C		1F24
1F2D		1F25
1F2E		1F26
1F2F		1F27
1F38		1F30
1F39		1F31
1F3A		1F32
1F3B		1F33
1F3C		1F34
1F3D		1F35
1F3E		1F36
1F3F		1F37
1F48		1F40
1F49		1F41
1F4A		1F42
1F4B		1F43
1F4C		1F44
1F4D		1F45
1F59		1F51
1F5B		1F53
1F5D		1F55
1F5F		1F57
1F68		1F60
1F69		1F61
1F6A		1F62
1F6B		1F63
1F6C		1F64
1F6D		1F65
1F6E		1F66
1F6F		1F67
1F71		03AC
1F73		03AD
1F75		03AE
1F77		03AF
1F79		03CC
1F7B		03CD
1F7D		03CE
1FB8		1FB0
1FB9		1FB1
1FBA		1F70
1FBB		03AC
1FBE		03B9
1FC8		1F72
1FC9		03AD
1FCA		1F74
1FCB		03AE
1FD3		0390
1FD8		1FD0
1FD9		1FD1
1FDA		1F76
1FDB		03AF
1FE3		03B0
1FE8		1FE0
1FE9		1FE1
1FEA		1F7A
1FEB		03CD
1FEC		1FE5
1FEF		0060
1FF8		1F78
1FF9		03CC
1FFA		1F7C
1FFB		03CE
2000		0020
2001		0020
2002		0020
2003		0020
2004		0020
2005		0020
2006		0020
2007		0020
2008		0020
2009		0020
200A		0020
2011		2010
2024		002E
202F		0020
205F		0020
2070		0030
2071		0069
2074		0034
2075		0035
2076		0036
2077		0037
2078		0038
2079		0039
207A		002B
207B		2212
207C		003D
207D		0028
207E		0029
207F		006E
2080		0030
2081		0031
2082		0032
2083		0033
2084		0034
2085		0035
2086		0036
2087		0037
2088		0038
2089		0039
208A		002B
208B		2212
208C		003D
208D		0028
208E		0029
2090		0061
2091		0065
2092		006F
2093		0078
2094		0259
2095		0068
2096		006B
2097		006C
2098		006D
2099		006E
209A		0070
209B		0073
209C		0074
2102		0063
2107		025B
210A		0067
210B		0068
210C		0068
210D		0068
210E		0068
210F		0127
2110		0069
2111		0069
2112		006C
2113		006C
2115		006E
2119		0070
211A		0071
211B		0072
211C		0072
211D		0072
2124		007A
2126		03C9
2128		007A
212A		006B
212B		00E5
212C		0062
212D		0063
212F		0065
2130		0065
2131		0066
2132		214E
2133		006D
2134		006F
2135		05D0
2136		05D1
2137		05D2
2138		05D3
2139		0069
213C		03C0
213D		03B3
213E		03B3
213F		03C0
2140		2211
2145		0064
2146		0064
2147		0065
2148		0069
2149		006A
2160		0069
2164		0076
2169		0078
216C		006C
216D		0063
216E		0064
216F		006D
2170		0069
2174		0076
2179		0078
217C		006C
217D		0063
217E		0064
217F		006D
2183		2184
2329		3008
232A		3009
2460		0031
2461		0032
2462		0033
2463		0034
2464		0035
2465		0036
2466		0037
2467		0038
2468		0039
24B6		0061
24B7		0062
24B8		0063
24B9		0064
24BA		0065
24BB		0066
24BC		0067
24BD		0068
24BE		0069
24BF		006A
24C0		006B
24C1		006C
24C2		006D
24C3		006E
24C4		006F
24C5		0070
24C6		0071
24C7		0072
24C8		0073
24C9		0074
24CA		0075
24CB		0076
24CC		0077
24CD		0078
24CE		0079
24CF		007A
24D0		0061
24D1		0062
24D2		0063
24D3		0064
24D4		0065
24D5		0066
24D6		0067
24D7		0068
24D8		0069
24D9		006A
24DA		006B
24DB		006C
24DC		006D
24DD		006E
24DE		006F
24DF		0070
24E0		0071
24E1		0072
24E2		0073
24E3		0074
24E4		0075
24E5		0076
24E6		0077
24E7		0078
24E8		0079
24E9		007A
24EA		0030
2C00		2C30
2C01		2C31
2C02		2C32
2C03		2C33
2C04		2C34
2C05		2C35
2C06		2C36
2C07		2C37
2C08		2C38
2C09		2C39
2C0A		2C3A
2C0B		2C3B
2C0C		2C3C
2C0D		2C3D
2C0E		2C3E
2C0F		2C3F
2C10		2C40
2C11		2C41
2C12		2C42
2C13		2C43
2C14		2C44
2C15		2C45
2C16		2C46
2C17		2C47
2C18		2C48
2C19		2C49
2C1A		2C4A
2C1B		2C4B
2C1C		2C4C
2C1D		2C4D
2C1E		2C4E
2C1F		2C4F
2C20		2C50
2C21		2C51
2C22		2C52
2C23		2C53
2C24		2C54
2C25		2C55
2C26		2C56
2C27		2C57
2C28		2C58
2C29		2C59
2C2A		2C5A
2C2B		2C5B
2C2C		2C5C
2C2D		2C5D
2C2E		2C5E
2C2F		2C5F
2C60		2C61
2C62		026B
2C63		1D7D
2C64		027D
2C67		2C68
2C69		2C6A
2C6B		2C6C
2C6D		0251
2C6E		0271
2C6F		0250
2C70		0252
2C72		2C73
2C75		2C76
2C7C		006A
2C7D		0076
2C7E		023F
2C7F		0240
2C80		2C81
2C82		2C83
2C84		2C85
2C86		2C87
2C88		2C89
2C8A		2C8B
2C8C		2C8D
2C8E		2C8F
2C90		2C91
2C92		2C93
2C94		2C95
2C96		2C97
2C98		2C99
2C9A		2C9B
2C9C		2C9D
2C9E		2C9F
2CA0		2CA1
2CA2		2CA3
2CA4		2CA5
2CA6		2CA7
2CA8		2CA9
2CAA		2CAB
2CAC		2CAD
2CAE		2CAF
2CB0		2CB1
2CB2		2CB3
2CB4		2CB5
2CB6		2CB7
2CB8		2CB9
2CBA		2CBB
2CBC		2CBD
2CBE		2CBF
2CC0		2CC1
2CC2		2CC3
2CC4		2CC5
2CC6		2CC7
2CC8		2CC9
2CCA		2CCB
2CCC		2CCD
2CCE		2CCF
2CD0		2CD1
2CD2		2CD3
2CD4		2CD5
2CD6		2CD7
2CD8		2CD9
2CDA		2CDB
2CDC		2CDD
2CDE		2CDF
2CE0		2CE1
2CE2		2CE3
2CEB		2CEC
2CED		2CEE
2CF2		2CF3
2D6F		2D61
2E9F		6BCD
2EF3		9F9F
2F00		4E00
2F01		4E28
2F02		4E36
2F03		4E3F
2F04		4E59
2F05		4E85
2F06		4E8C
2F07		4EA0
2F08		4EBA
2F09		513F
2F0A		5165
2F0B		516B
2F0C		5182
2F0D		5196
2F0E		51AB
2F0F		51E0
2F10		51F5
2F11		5200
2F12		529B
2F13		52F9
2F14		5315
2F15		531A
2F16		5338
2F17		5341
2F18		535C
2F19		5369
2F1A		5382
2F1B		53B6
2F1C		53C8
2F1D		53E3
2F1E		56D7
2F1F		571F
2F20		58EB
2F21		5902
2F22		590A
2F23		5915
2F24		5927
2F25		5973
2F26		5B50
2F27		5B80
2F28		5BF8
2F29		5C0F
2F2A		5C22
2F2B		5C38
2F2C		5C6E
2F2D		5C71
2F2E		5DDB
2F2F		5DE5
2F30		5DF1
2F31		5DFE
2F32		5E72
2F33		5E7A
2F34		5E7F
2F35		5EF4
2F36		5EFE
2F37		5F0B
2F38		5F13
2F39		5F50
2F3A		5F61
2F3B		5F73
2F3C		5FC3
2F3D		6208
2F3E		6236
2F3F		624B
2F40		652F
2F41		6534
2F42		6587
2F43		6597
2F44		65A4
2F45		65B9
2F46		65E0
2F47		65E5
2F48		66F0
2F49		6708
2F4A		6728
2F4B		6B20
2F4C		6B62
2F4D		6B79
2F4E		6BB3
2F4F		6BCB
2F50		6BD4
2F51		6BDB
2F52		6C0F
2F53		6C14
2F54		6C34
2F55		706B
2F56		722A
2F57		7236
2F58		723B
2F59		723F
2F5A		7247
2F5B		7259
2F5C		725B
2F5D		72AC
2F5E		7384
2F5F		7389
2F60		74DC
2F61		74E6
2F62		7518
2F63		751F
2F64		7528
2F65		7530
2F66		758B
2F67		7592
2F68		7676
2F69		767D
2F6A		76AE
2F6B		76BF
2F6C		76EE
2F6D		77DB
2F6E		77E2
2F6F		77F3
2F70		793A
2F71		79B8
2F72		79BE
2F73		7A74
2F74		7ACB
2F75		7AF9
2F76		7C73
2F77		7CF8
2F78		7F36
2F79		7F51
2F7A		7F8A
2F7B		7FBD
2F7C		8001
2F7D		800C
2F7E		8012
2F7F		8033
2F80		807F
2F81		8089
2F82		81E3
2F83		81EA
2F84		81F3
2F85		81FC
2F86		820C
2F87		821B
2F88		821F
2F89		826E
2F8A		8272
2F8B		8278
2F8C		864D
2F8D		866B
2F8E		8840
2F8F		884C
2F90		8863
2F91		897E
2F92		898B
2F93		89D2
2F94		8A00
2F95		8C37
2F96		8C46
2F97		8C55
2F98		8C78
2F99		8C9D
2F9A		8D64
2F9B		8D70
2F9C		8DB3
2F9D		8EAB
2F9E		8ECA
2F9F		8F9B
2FA0		8FB0
2FA1		8FB5
2FA2		9091
2FA3		9149
2FA4		91C6
2FA5		91CC
2FA6		91D1
2FA7		9577
2FA8		9580
2FA9		961C
2FAA		96B6
2FAB		96B9
2FAC		96E8
2FAD		9751
2FAE		975E
2FAF		9762
2FB0		9769
2FB1		97CB
2FB2		97ED
2FB3		97F3
2FB4		9801
2FB5		98A8
2FB6		98DB
2FB7		98DF
2FB8		9996
2FB9		9999
2FBA		99AC
2FBB		9AA8
2FBC		9AD8
2FBD		9ADF
2FBE		9B25
2FBF		9B2F
2FC0		9B32
2FC1		9B3C
2FC2		9B5A
2FC3		9CE5
2FC4		9E75
2FC5		9E7F
2FC6		9EA5
2FC7		9EBB
2FC8		9EC3
2FC9		9ECD
2FCA		9ED1
2FCB		9EF9
2FCC		9EFD
2FCD		9F0E
2FCE		9F13
2FCF		9F20
2FD0		9F3B
2FD1		9F4A
2FD2		9F52
2FD3		9F8D
2FD4		9F9C
2FD5		9FA0
3000		0020
3036		3012
3038		5341
3039		5344
303A		5345
3131		1100
3132		1101
3133		11AA
3134		1102
3135		11AC
3136		11AD
3137		1103
3138		1104
3139		1105
313A		11B0
313B		11B1
313C		11B2
313D		11B3
313E		11B4
313F		11B5
3140		111A
3141		1106
3142		1107
3143		1108
3144		1121
3145		1109
3146		110A
3147		110B
3148		110C
3149		110D
314A		110E
314B		110F
314C		1110
314D		1111
314E		1112
314F		1161
3150		1162
3151		1163
3152		1164
3153		1165
3154		1166
3155		1167
3156		1168
3157		1169
3158		116A
3159		116B
315A		116C
315B		116D
315C		116E
315D		116F
315E		1170
315F		1171
3160		1172
3161		1173
3162		1174
3163		1175
3165		1114
3166		1115
3167		11C7
3168		11C8
3169		11CC
316A		11CE
316B		11D3
316C		11D7
316D		11D9
316E		111C
316F		11DD
3170		11DF
3171		111D
3172		111E
3173		1120
3174		1122
3175		1123
3176		1127
3177		1129
3178		112B
3179		112C
317A		112D
317B		112E
317C		112F
317D		1132
317E		1136
317F		1140
3180		1147
3181		114C
3182		11F1
3183		11F2
3184		1157
3185		1158
3186		1159
3187		1184
3188		1185
3189		1188
318A		1191
318B		1192
318C		1194
318D		119E
318E		11A1
3192		4E00
3193		4E8C
3194		4E09
3195		56DB
3196		4E0A
3197		4E2D
3198		4E0B
3199		7532
319A		4E59
319B		4E19
319C		4E01
319D		5929
319E		5730
319F		4EBA
3244		554F
3245		5E7C
3246		6587
3247		7B8F
3260		1100
3261		1102
3262		1103
3263		1105
3264		1106
3265		1107
3266		1109
3267		110B
3268		110C
3269		110E
326A		110F
326B		1110
326C		1111
326D		1112
326E		AC00
326F		B098
3270		B2E4
3271		B77C
3272		B9C8
3273		BC14
3274		C0AC
3275		C544
3276		C790
3277		CC28
3278		CE74
3279		D0C0
327A		D30C
327B		D558
327E		C6B0
3280		4E00
3281		4E8C
3282		4E09
3283		56DB
3284		4E94
3285		516D
3286		4E03
3287		516B
3288		4E5D
3289		5341
328A		6708
328B		706B
328C		6C34
328D		6728
328E		91D1
328F		571F
3290		65E5
3291		682A
3292		6709
3293		793E
3294		540D
3295		7279
3296		8CA1
3297		795D
3298		52B4
3299		79D8
329A		7537
329B		5973
329C		9069
329D		512A
329E		5370
329F		6CE8
32A0		9805
32A1		4F11
32A2		5199
32A3		6B63
32A4		4E0A
32A5		4E2D
32A6		4E0B
32A7		5DE6
32A8		53F3
32A9		533B
32AA		5B97
32AB		5B66
32AC		76E3
32AD		4F01
32AE		8CC7
32AF		5354
32B0		591C
32D0		30A2
32D1		30A4
32D2		30A6
32D3		30A8
32D4		30AA
32D5		30AB
32D6		30AD
32D7		30AF
32D8		30B1
32D9		30B3
32DA		30B5
32DB		30B7
32DC		30B9
32DD		30BB
32DE		30BD
32DF		30BF
32E0		30C1
32E1		30C4
32E2		30C6
32E3		30C8
32E4		30CA
32E5		30CB
32E6		30CC
32E7		30CD
32E8		30CE
32E9		30CF
32EA		30D2
32EB		30D5
32EC		30D8
32ED		30DB
32EE		30DE
32EF		30DF
32F0		30E0
32F1		30E1
32F2		30E2
32F3		30E4
32F4		30E6
32F5		30E8
32F6		30E9
32F7		30EA
32F8		30EB
32F9		30EC
32FA		30ED
32FB		30EF
32FC		30F0
32FD		30F1
32FE		30F2
A640		A641
A642		A643
A644		A645
A646		A647
A648		A649
A64A		A64B
A64C		A64D
A64E		A64F
A650		A651
A652		A653
A654		A655
A656		A657
A658		A659
A65A		A65B
A65C		A65D
A65E		A65F
A660		A661
A662		A663
A664		A665
A666		A667
A668		A669
A66A		A66B
A66C		A66D
A680		A681
A682		A683
A684		A685
A686		A687
A688		A689
A68A		A68B
A68C		A68D
A68E		A68F
A690		A691
A692		A693
A694		A695
A696		A697
A698		A699
A69A		A69B
A69C		044A
A69D		044C
A722		A723
A724		A725
A726		A727
A728		A729
A72A		A72B
A72C		A72D
A72E		A72F
A732		A733
A734		A735
A736		A737
A738		A739
A73A		A73B
A73C		A73D
A73E		A73F
A740		A741
A742		A743
A744		A745
A746		A747
A748		A749
A74A		A74B
A74C		A74D
A74E		A74F
A750		A751
A752		A753
A754		A755
A756		A757
A758		A759
A75A		A75B
A75C		A75D
A75E		A75F
A760		A761
A762		A763
A764		A765
A766		A767
A768		A769
A76A		A76B
A76C		A76D
A76E		A76F
A770		A76F
A779		A77A
A77B		A77C
A77D		1D79
A77E		A77F
A780		A781
A782		A783
A784		A785
A786		A787
A78B		A78C
A78D		0265
A790		A791
A792		A793
A796		A797
A798		A799
A79A		A79B
A79C		A79D
A79E		A79F
A7A0		A7A1
A7A2		A7A3
A7A4		A7A5
A7A6		A7A7
A7A8		A7A9
A7AA		0266
A7AB		025C
A7AC		0261
A7AD		026C
A7AE		026A
A7B0		029E
A7B1		0287
A7B2		029D
A7B3		AB53
A7B4		A7B5
A7B6		A7B7
A7B8		A7B9
A7BA		A7BB
A7BC		A7BD
A7BE		A7BF
A7C0		A7C1
A7C2		A7C3
A7C4		A794
A7C5		0282
A7C6		1D8E
A7C7		A7C8
A7C9		A7CA
A7D0		A7D1
A7D6		A7D7
A7D8		A7D9
A7F2		0063
A7F3		0066
A7F4		0071
A7F5		A7F6
A7F8		0127
A7F9		0153
AB5C		A727
AB5D		AB37
AB5E		026B
AB5F		AB52
AB69		028D
AB70		13A0
AB71		13A1
AB72		13A2
AB73		13A3
AB74		13A4
AB75		13A5
AB76		13A6
AB77		13A7
AB78		13A8
AB79		13A9
AB7A		13AA
AB7B		13AB
AB7C		13AC
AB7D		13AD
AB7E		13AE
AB7F		13AF
AB80		13B0
AB81		13B1
AB82		13B2
AB83		13B3
AB84		13B4
AB85		13B5
AB86		13B6
AB87		13B7
AB88		13B8
AB89		13B9
AB8A		13BA
AB8B		13BB
AB8C		13BC
AB8D		13BD
AB8E		13BE
AB8F		13BF
AB90		13C0
AB91		13C1
AB92		13C2
AB93		13C3
AB94		13C4
AB95		13C5
AB96		13C6
AB97		13C7
AB98		13C8
AB99		13C9
AB9A		13CA
AB9B		13CB
AB9C		13CC
AB9D		13CD
AB9E		13CE
AB9F		13CF
ABA0		13D0
ABA1		13D1
ABA2		13D2
ABA3		13D3
ABA4		13D4
ABA5		13D5
ABA6		13D6
ABA7		13D7
ABA8		13D8
ABA9		13D9
ABAA		13DA
ABAB		13DB
ABAC		13DC
ABAD		13DD
ABAE		13DE
ABAF		13DF
ABB0		13E0
ABB1		13E1
ABB2		13E2
ABB3		13E3
ABB4		13E4
ABB5		13E5
ABB6		13E6
ABB7		13E7
ABB8		13E8
ABB9		13E9
ABBA		13EA
ABBB		13EB
ABBC		13EC
ABBD		13ED
ABBE		13EE
ABBF		13EF
F900		8C48
F901		66F4
F902		8ECA
F903		8CC8
F904		6ED1
F905		4E32
F906		53E5
F907		9F9C
F908		9F9C
F909		5951
F90A		91D1
F90B		5587
F90C		5948
F90D		61F6
F90E		7669
F90F		7F85
F910		863F
F911		87BA
F912		88F8
F913		908F
F914		6A02
F915		6D1B
F916		70D9
F917		73DE
F918		843D
F919		916A
F91A		99F1
F91B		4E82
F91C		5375
F91D		6B04
F91E		721B
F91F		862D
F920		9E1E
F921		5D50
F922		6FEB
F923		85CD
F924		8964
F925		62C9
F926		81D8
F927		881F
F928		5ECA
F929		6717
F92A		6D6A
F92B		72FC
F92C		90CE
F92D		4F86
F92E		51B7
F92F		52DE
F930		64C4
F931		6AD3
F932		7210
F933		76E7
F934		8001
F935		8606
F936		865C
F937		8DEF
F938		9732
F939		9B6F
F93A		9DFA
F93B		788C
F93C		797F
F93D		7DA0
F93E		83C9
F93F		9304
F940		9E7F
F941		8AD6
F942		58DF
F943		5F04
F944		7C60
F945		807E
F946		7262
F947		78CA
F948		8CC2
F949		96F7
F94A		58D8
F94B		5C62
F94C		6A13
F94D		6DDA
F94E		6F0F
F94F		7D2F
F950		7E37
F951		964B
F952		52D2
F953		808B
F954		51DC
F955		51CC
F956		7A1C
F957		7DBE
F958		83F1
F959		9675
F95A		8B80
F95B		62CF
F95C		6A02
F95D		8AFE
F95E		4E39
F95F		5BE7
F960		6012
F961		7387
F962		7570
F963		5317
F964		78FB
F965		4FBF
F966		5FA9
F967		4E0D
F968		6CCC
F969		6578
F96A		7D22
F96B		53C3
F96C		585E
F96D		7701
F96E		8449
F96F		8AAA
F970		6BBA
F971		8FB0
F972		6C88
F973		62FE
F974		82E5
F975		63A0
F976		7565
F977		4EAE
F978		5169
F979		51C9
F97A		6881
F97B		7CE7
F97C		826F
F97D		8AD2
F97E		91CF
F97F		52F5
F980		5442
F981		5973
F982		5EEC
F983		65C5
F984		6FFE
F985		792A
F986		95AD
F987		9A6A
F988		9E97
F989		9ECE
F98A		529B
F98B		66C6
F98C		6B77
F98D		8F62
F98E		5E74
F98F		6190
F990		6200
F991		649A
F992		6F23
F993		7149
F994		7489
F995		79CA
F996		7DF4
F997		806F
F998		8F26
F999		84EE
F99A		9023
F99B		934A
F99C		5217
F99D		52A3
F99E		54BD
F99F		70C8
F9A0		88C2
F9A1		8AAA
F9A2		5EC9
F9A3		5FF5
F9A4		637B
F9A5		6BAE
F9A6		7C3E
F9A7		7375
F9A8		4EE4
F9A9		56F9
F9AA		5BE7
F9AB		5DBA
F9AC		601C
F9AD		73B2
F9AE		7469
F9AF		7F9A
F9B0		8046
F9B1		9234
F9B2		96F6
F9B3		9748
F9B4		9818
F9B5		4F8B
F9B6		79AE
F9B7		91B4
F9B8		96B8
F9B9		60E1
F9BA		4E86
F9BB		50DA
F9BC		5BEE
F9BD		5C3F
F9BE		6599
F9BF		6A02
F9C0		71CE
F9C1		7642
F9C2		84FC
F9C3		907C
F9C4		9F8D
F9C5		6688
F9C6		962E
F9C7		5289
F9C8		677B
F9C9		67F3
F9CA		6D41
F9CB		6E9C
F9CC		7409
F9CD		7559
F9CE		786B
F9CF		7D10
F9D0		985E
F9D1		516D
F9D2		622E
F9D3		9678
F9D4		502B
F9D5		5D19
F9D6		6DEA
F9D7		8F2A
F9D8		5F8B
F9D9		6144
F9DA		6817
F9DB		7387
F9DC		9686
F9DD		5229
F9DE		540F
F9DF		5C65
F9E0		6613
F9E1		674E
F9E2		68A8
F9E3		6CE5
F9E4		7406
F9E5		75E2
F9E6		7F79
F9E7		88CF
F9E8		88E1
F9E9		91CC
F9EA		96E2
F9EB		533F
F9EC		6EBA
F9ED		541D
F9EE		71D0
F9EF		7498
F9F0		85FA
F9F1		96A3
F9F2		9C57
F9F3		9E9F
F9F4		6797
F9F5		6DCB
F9F6		81E8
F9F7		7ACB
F9F8		7B20
F9F9		7C92
F9FA		72C0
F9FB		7099
F9FC		8B58
F9FD		4EC0
F9FE		8336
F9FF		523A
FA00		5207
FA01		5EA6
FA02		62D3
FA03		7CD6
FA04		5B85
FA05		6D1E
FA06		66B4
FA07		8F3B
FA08		884C
FA09		964D
FA0A		898B
FA0B		5ED3
FA0C		5140
FA0D		55C0
FA10		585A
FA12		6674
FA15		51DE
FA16		732A
FA17		76CA
FA18		793C
FA19		795E
FA1A		7965
FA1B		798F
FA1C		9756
FA1D		7CBE
FA1E		7FBD
FA20		8612
FA22		8AF8
FA25		9038
FA26		90FD
FA2A		98EF
FA2B		98FC
FA2C		9928
FA2D		9DB4
FA2E		90DE
FA2F		96B7
FA30		4FAE
FA31		50E7
FA32		514D
FA33		52C9
FA34		52E4
FA35		5351
FA36		559D
FA37		5606
FA38		5668
FA39		5840
FA3A		58A8
FA3B		5C64
FA3C		5C6E
FA3D		6094
FA3E		6168
FA3F		618E
FA40		61F2
FA41		654F
FA42		65E2
FA43		6691
FA44		6885
FA45		6D77
FA46		6E1A
FA47		6F22
FA48		716E
FA49		722B
FA4A		7422
FA4B		7891
FA4C		793E
FA4D		7949
FA4E		7948
FA4F		7950
FA50		7956
FA51		795D
FA52		798D
FA53		798E
FA54		7A40
FA55		7A81
FA56		7BC0
FA57		7DF4
FA58		7E09
FA59		7E41
FA5A		7F72
FA5B		8005
FA5C		81ED
FA5D		8279
FA5E		8279
FA5F		8457
FA60		8910
FA61		8996
FA62		8B01
FA63		8B39
FA64		8CD3
FA65		8D08
FA66		8FB6
FA67		9038
FA68		96E3
FA69		97FF
FA6A		983B
FA6B		6075
FA6C		242EE
FA6D		8218
FA70		4E26
FA71		51B5
FA72		5168
FA73		4F80
FA74		5145
FA75		5180
FA76		52C7
FA77		52FA
FA78		559D
FA79		5555
FA7A		5599
FA7B		55E2
FA7C		585A
FA7D		58B3
FA7E		5944
FA7F		5954
FA80		5A62
FA81		5B28
FA82		5ED2
FA83		5ED9
FA84		5F69
FA85		5FAD
FA86		60D8
FA87		614E
FA88		6108
FA89		618E
FA8A		6160
FA8B		61F2
FA8C		6234
FA8D		63C4
FA8E		641C
FA8F		6452
FA90		6556
FA91		6674
FA92		6717
FA93		671B
FA94		6756
FA95		6B79
FA96		6BBA
FA97		6D41
FA98		6EDB
FA99		6ECB
FA9A		6F22
FA9B		701E
FA9C		716E
FA9D		77A7
FA9E		7235
FA9F		72AF
FAA0		732A
FAA1		7471
FAA2		7506
FAA3		753B
FAA4		761D
FAA5		761F
FAA6		76CA
FAA7		76DB
FAA8		76F4
FAA9		774A
FAAA		7740
FAAB		78CC
FAAC		7AB1
FAAD		7BC0
FAAE		7C7B
FAAF		7D5B
FAB0		7DF4
FAB1		7F3E
FAB2		8005
FAB3		8352
FAB4		83EF
FAB5		8779
FAB6		8941
FAB7		8986
FAB8		8996
FAB9		8ABF
FABA		8AF8
FABB		8ACB
FABC		8B01
FABD		8AFE
FABE		8AED
FABF		8B39
FAC0		8B8A
FAC1		8D08
FAC2		8F38
FAC3		9072
FAC4		9199
FAC5		9276
FAC6		967C
FAC7		96E3
FAC8		9756
FAC9		97DB
FACA		97FF
FACB		980B
FACC		983B
FACD		9B12
FACE		9F9C
FACF		2284A
FAD0		22844
FAD1		233D5
FAD2		3B9D
FAD3		4018
FAD4		4039
FAD5		25249
FAD6		25CD0
FAD7		27ED3
FAD8		9F43
FAD9		9F8E
FB20		05E2
FB21		05D0
FB22		05D3
FB23		05D4
FB24		05DB
FB25		05DC
FB26		05DD
FB27		05E8
FB28		05EA
FB29		002B
FB50		0671
FB51		0671
FB52		067B
FB53		067B
FB54		067B
FB55		067B
FB56		067E
FB57		067E
FB58		067E
FB59		067E
FB5A		0680
FB5B		0680
FB5C		0680
FB5D		0680
FB5E		067A
FB5F		067A
FB60		067A
FB61		067A
FB62		067F
FB63		067F
FB64		067F
FB65		067F
FB66		0679
FB67		0679
FB68		0679
FB69		0679
FB6A		06A4
FB6B		06A4
FB6C		06A4
FB6D		06A4
FB6E		06A6
FB6F		06A6
FB70		06A6
FB71		06A6
FB72		0684
FB73		0684
FB74		0684
FB75		0684
FB76		0683
FB77		0683
FB78		0683
FB79		0683
FB7A		0686
FB7B		0686
FB7C		0686
FB7D		0686
FB7E		0687
FB7F		0687
FB80		0687
FB81		0687
FB82		068D
FB83		068D
FB84		068C
FB85		068C
FB86		068E
FB87		068E
FB88		0688
FB89		0688
FB8A		0698
FB8B		0698
FB8C		0691
FB8D		0691
FB8E		06A9
FB8F		06A9
FB90		06A9
FB91		06A9
FB92		06AF
FB93		06AF
FB94		06AF
FB95		06AF
FB96		06B3
FB97		06B3
FB98		06B3
FB99		06B3
FB9A		06B1
FB9B		06B1
FB9C		06B1
FB9D		06B1
FB9E		06BA
FB9F		06BA
FBA0		06BB
FBA1		06BB
FBA2		06BB
FBA3		06BB
FBA4		06C0
FBA5		06C0
FBA6		06C1
FBA7		06C1
FBA8		06C1
FBA9		06C1
FBAA		06BE
FBAB		06BE
FBAC		06BE
FBAD		06BE
FBAE		06D2
FBAF		06D2
FBB0		06D3
FBB1		06D3
FBD3		06AD
FBD4		06AD
FBD5		06AD
FBD6		06AD
FBD7		06C7
FBD8		06C7
FBD9		06C6
FBDA		06C6
FBDB		06C8
FBDC		06C8
FBDE		06CB
FBDF		06CB
FBE0		06C5
FBE1		06C5
FBE2		06C9
FBE3		06C9
FBE4		06D0
FBE5		06D0
FBE6		06D0
FBE7		06D0
FBE8		0649
FBE9		0649
FBFC		06CC
FBFD		06CC
FBFE		06CC
FBFF		06CC
FE10		002C
FE11		3001
FE12		3002
FE13		003A
FE14		003B
FE15		0021
FE16		003F
FE17		3016
FE18		3017
FE31		2014
FE32		2013
FE33		005F
FE34		005F
FE35		0028
FE36		0029
FE37		007B
FE38		007D
FE39		3014
FE3A		3015
FE3B		3010
FE3C		3011
FE3D		300A
FE3E		300B
FE3F		3008
FE40		3009
FE41		300C
FE42		300D
FE43		300E
FE44		300F
FE47		005B
FE48		005D
FE4D		005F
FE4E		005F
FE4F		005F
FE50		002C
FE51		3001
FE52		002E
FE54		003B
FE55		003A
FE56		003F
FE57		0021
FE58		2014
FE59		0028
FE5A		0029
FE5B		007B
FE5C		007D
FE5D		3014
FE5E		3015
FE5F		0023
FE60		0026
FE61		002A
FE62		002B
FE63		002D
FE64		003C
FE65		003E
FE66		003D
FE68		005C
FE69		0024
FE6A		0025
FE6B		0040
FE80		0621
FE81		0622
FE82		0622
FE83		0623
FE84		0623
FE85		0624
FE86		0624
FE87		0625
FE88		0625
FE89		0626
FE8A		0626
FE8B		0626
FE8C		0626
FE8D		0627
FE8E		0627
FE8F		0628
FE90		0628
FE91		0628
FE92		0628
FE93		0629
FE94		0629
FE95		062A
FE96		062A
FE97		062A
FE98		062A
FE99		062B
FE9A		062B
FE9B		062B
FE9C		062B
FE9D		062C
FE9E		062C
FE9F		062C
FEA0		062C
FEA1		062D
FEA2		062D
FEA3		062D
FEA4		062D
FEA5		062E
FEA6		062E
FEA7		062E
FEA8		062E
FEA9		062F
FEAA		062F
FEAB		0630
FEAC		0630
FEAD		0631
FEAE		0631
FEAF		0632
FEB0		0632
FEB1		0633
FEB2		0633
FEB3		0633
FEB4		0633
FEB5		0634
FEB6		0634
FEB7		0634
FEB8		0634
FEB9		0635
FEBA		0635
FEBB		0635
FEBC		0635
FEBD		0636
FEBE		0636
FEBF		0636
FEC0		0636
FEC1		0637
FEC2		0637
FEC3		0637
FEC4		0637
FEC5		0638
FEC6		0638
FEC7		0638
FEC8		0638
FEC9		0639
FECA		0639
FECB		0639
FECC		0639
FECD		063A
FECE		063A
FECF		063A
FED0		063A
FED1		0641
FED2		0641
FED3		0641
FED4		0641
FED5		0642
FED6		0642
FED7		0642
FED8		0642
FED9		0643
FEDA		0643
FEDB		0643
FEDC		0643
FEDD		0644
FEDE		0644
FEDF		0644
FEE0		0644
FEE1		0645
FEE2		0645
FEE3		0645
FEE4		0645
FEE5		0646
FEE6		0646
FEE7		0646
FEE8		0646
FEE9		0647
FEEA		0647
FEEB		0647
FEEC		0647
FEED		0648
FEEE		0648
FEEF		0649
FEF0		0649
FEF1		064A
FEF2		064A
FEF3		064A
FEF4		064A
FF01		0021
FF02		0022
FF03		0023
FF04		0024
FF05		0025
FF06		0026
FF07		0027
FF08		0028
FF09		0029
FF0A		002A
FF0B		002B
FF0C		002C
FF0D		002D
FF0E		002E
FF0F		002F
FF10		0030
FF11		0031
FF12		0032
FF13		0033
FF14		0034
FF15		0035
FF16		0036
FF17		0037
FF18		0038
FF19		0039
FF1A		003A
FF1B		003B
FF1C		003C
FF1D		003D
FF1E		003E
FF1F		003F
FF20		0040
FF21		0061
FF22		0062
FF23		0063
FF24		0064
FF25		0065
FF26		0066
FF27		0067
FF28		0068
FF29		0069
FF2A		006A
FF2B		006B
FF2C		006C
FF2D		006D
FF2E		006E
FF2F		006F
FF30		0070
FF31		0071
FF32		0072
FF33		0073
FF34		0074
FF35		0075
FF36		0076
FF37		0077
FF38		0078
FF39		0079
FF3A		007A
FF3B		005B
FF3C		005C
FF3D		005D
FF3E		005E
FF3F		005F
FF40		0060
FF41		0061
FF42		0062
FF43		0063
FF44		0064
FF45		0065
FF46		0066
FF47		0067
FF48		0068
FF49		0069
FF4A		006A
FF4B		006B
FF4C		006C
FF4D		006D
FF4E		006E
FF4F		006F
FF50		0070
FF51		0071
FF52		0072
FF53		0073
FF54		0074
FF55		0075
FF56		0076
FF57		0077
FF58		0078
FF59		0079
FF5A		007A
FF5B		007B
FF5C		007C
FF5D		007D
FF5E		007E
FF5F		2985
FF60		2986
FF61		3002
FF62		300C
FF63		300D
FF64		3001
FF65		30FB
FF66		30F2
FF67		30A1
FF68		30A3
FF69		30A5
FF6A		30A7
FF6B		30A9
FF6C		30E3
FF6D		30E5
FF6E		30E7
FF6F		30C3
FF70		30FC
FF71		30A2
FF72		30A4
FF73		30A6
FF74		30A8
FF75		30AA
FF76		30AB
FF77		30AD
FF78		30AF
FF79		30B1
FF7A		30B3
FF7B		30B5
FF7C		30B7
FF7D		30B9
FF7E		30BB
FF7F		30BD
FF80		30BF
FF81		30C1
FF82		30C4
FF83		30C6
FF84		30C8
FF85		30CA
FF86		30CB
FF87		30CC
FF88		30CD
FF89		30CE
FF8A		30CF
FF8B		30D2
FF8C		30D5
FF8D		30D8
FF8E		30DB
FF8F		30DE
FF90		30DF
FF91		30E0
FF92		30E1
FF93		30E2
FF94		30E4
FF95		30E6
FF96		30E8
FF97		30E9
FF98		30EA
FF99		30EB
FF9A		30EC
FF9B		30ED
FF9C		30EF
FF9D		30F3
FF9E		3099
FF9F		309A
FFA1		1100
FFA2		1101
FFA3		11AA
FFA4		1102
FFA5		11AC
FFA6		11AD
FFA7		1103
FFA8		1104
FFA9		1105
FFAA		11B0
FFAB		11B1
FFAC		11B2
FFAD		11B3
FFAE		11B4
FFAF		11B5
FFB0		111A
FFB1		1106
FFB2		1107
FFB3		1108
FFB4		1121
FFB5		1109
FFB6		110A
FFB7		110B
FFB8		110C
FFB9		110D
FFBA		110E
FFBB		110F
FFBC		1110
FFBD		1111
FFBE		1112
FFC2		1161
FFC3		1162
FFC4		1163
FFC5		1164
FFC6		1165
FFC7		1166
FFCA		1167
FFCB		1168
FFCC		1169
FFCD		116A
FFCE		116B
FFCF		116C
FFD2		116D
FFD3		116E
FFD4		116F
FFD5		1170
FFD6		1171
FFD7		1172
FFDA		1173
FFDB		1174
FFDC		1175
FFE0		00A2
FFE1		00A3
FFE2		00AC
FFE4		00A6
FFE5		00A5
FFE6		20A9
FFE8		2502
FFE9		2190
FFEA		2191
FFEB		2192
FFEC		2193
FFED		25A0
FFEE		25CB
10400		10428
10401		10429
10402		1042A
10403		1042B
10404		1042C
10405		1042D
10406		1042E
10407		1042F
10408		10430
10409		10431
1040A		10432
1040B		10433
1040C		10434
1040D		10435
1040E		10436
1040F		10437
10410		10438
10411		10439
10412		1043A
10413		1043B
10414		1043C
10415		1043D
10416		1043E
10417		1043F
10418		10440
10419		10441
1041A		10442
1041B		10443
1041C		10444
1041D		10445
1041E		10446
1041F		10447
10420		10448
10421		10449
10422		1044A
10423		1044B
10424		1044C
10425		1044D
10426		1044E
10427		1044F
104B0		104D8
104B1		104D9
104B2		104DA
104B3		104DB
104B4		104DC
104B5		104DD
104B6		104DE
104B7		104DF
104B8		104E0
104B9		104E1
104BA		104E2
104BB		104E3
104BC		104E4
104BD		104E5
104BE		104E6
104BF		104E7
104C0		104E8
104C1		104E9
104C2		104EA
104C3		104EB
104C4		104EC
104C5		104ED
104C6		104EE
104C7		104EF
104C8		104F0
104C9		104F1
104CA		104F2
104CB		104F3
104CC		104F4
104CD		104F5
104CE		104F6
104CF		104F7
104D0		104F8
104D1		104F9
104D2		104FA
104D3		104FB
10570		10597
10571		10598
10572		10599
10573		1059A
10574		1059B
10575		1059C
10576		1059D
10577		1059E
10578		1059F
10579		105A0
1057A		105A1
1057C		105A3
1057D		105A4
1057E		105A5
1057F		105A6
10580		105A7
10581		105A8
10582		105A9
10583		105AA
10584		105AB
10585		105AC
10586		105AD
10587		105AE
10588		105AF
10589		105B0
1058A		105B1
1058C		105B3
1058D		105B4
1058E		105B5
1058F		105B6
10590		105B7
10591		105B8
10592		105B9
10594		105BB
10595		105BC
10781		02D0
10782		02D1
10783		00E6
10784		0299
10785		0253
10787		02A3
10788		AB66
10789		02A5
1078A		02A4
1078B		0256
1078C		0257
1078D		1D91
1078E		0258
1078F		025E
10790		02A9
10791		0264
10792		0262
10793		0260
10794		029B
10795		0127
10796		029C
10797		0267
10798		0284
10799		02AA
1079A		02AB
1079B		026C
1079C		1DF04
1079D		A78E
1079E		026E
1079F		1DF05
107A0		028E
107A1		1DF06
107A2		00F8
107A3		0276
107A4		0277
107A5		0071
107A6		027A
107A7		1DF08
107A8		027D
107A9		027E
107AA		0280
107AB		02A8
107AC		02A6
107AD		AB67
107AE		02A7
107AF		0288
107B0		2C71
107B2		028F
107B3		02A1
107B4		02A2
107B5		0298
107B6		01C0
107B7		01C1
107B8		01C2
107B9		1DF0A
107BA		1DF1E
10C80		10CC0
10C81		10CC1
10C82		10CC2
10C83		10CC3
10C84		10CC4
10C85		10CC5
10C86		10CC6
10C87		10CC7
10C88		10CC8
10C89		10CC9
10C8A		10CCA
10C8B		10CCB
10C8C		10CCC
10C8D		10CCD
10C8E		10CCE
10C8F		10CCF
10C90		10CD0
10C91		10CD1
10C92		10CD2
10C93		10CD3
10C94		10CD4
10C95		10CD5
10C96		10CD6
10C97		10CD7
10C98		10CD8
10C99		10CD9
10C9A		10CDA
10C9B		10CDB
10C9C		10CDC
10C9D		10CDD
10C9E		10CDE
10C9F		10CDF
10CA0		10CE0
10CA1		10CE1
10CA2		10CE2
10CA3		10CE3
10CA4		10CE4
10CA5		10CE5
10CA6		10CE6
10CA7		10CE7
10CA8		10CE8
10CA9		10CE9
10CAA		10CEA
10CAB		10CEB
10CAC		10CEC
10CAD		10CED
10CAE		10CEE
10CAF		10CEF
10CB0		10CF0
10CB1		10CF1
10CB2		10CF2
118A0		118C0
118A1		118C1
118A2		118C2
118A3		118C3
118A4		118C4
118A5		118C5
118A6		118C6
118A7		118C7
118A8		118C8
118A9		118C9
118AA		118CA
118AB		118CB
118AC		118CC
118AD		118CD
118AE		118CE
118AF		118CF
118B0		118D0
118B1		118D1
118B2		118D2
118B3		118D3
118B4		118D4
118B5		118D5
118B6		118D6
118B7		118D7
118B8		118D8
118B9		118D9
118BA		118DA
118BB		118DB
118BC		118DC
118BD		118DD
118BE		118DE
118BF		118DF
16E40		16E60
16E41		16E61
16E42		16E62
16E43		16E63
16E44		16E64
16E45		16E65
16E46		16E66
16E47		16E67
16E48		16E68
16E49		16E69
16E4A		16E6A
16E4B		16E6B
16E4C		16E6C
16E4D		16E6D
16E4E		16E6E
16E4F		16E6F
16E50		16E70
16E51		16E71
16E52		16E72
16E53		16E73
16E54		16E74
16E55		16E75
16E56		16E76
16E57		16E77
16E58		16E78
16E59		16E79
16E5A		16E7A
16E5B		16E7B
16E5C		16E7C
16E5D		16E7D
16E5E		16E7E
16E5F		16E7F
1D400		0061
1D401		0062
1D402		0063
1D403		0064
1D404		0065
1D405		0066
1D406		0067
1D407		0068
1D408		0069
1D409		006A
1D40A		006B
1D40B		006C
1D40C		006D
1D40D		006E
1D40E		006F
1D40F		0070
1D410		0071
1D411		0072
1D412		0073
1D413		0074
1D414		0075
1D415		0076
1D416		0077
1D417		0078
1D418		0079
1D419		007A
1D41A		0061
1D41B		0062
1D41C		0063
1D41D		0064
1D41E		0065
1D41F		0066
1D420		0067
1D421		0068
1D422		0069
1D423		006A
1D424		006B
1D425		006C
1D426		006D
1D427		006E
1D428		006F
1D429		0070
1D42A		0071
1D42B		0072
1D42C		0073
1D42D		0074
1D42E		0075
1D42F		0076
1D430		0077
1D431		0078
1D432		0079
1D433		007A
1D434		0061
1D435		0062
1D436		0063
1D437		0064
1D438		0065
1D439		0066
1D43A		0067
1D43B		0068
1D43C		0069
1D43D		006A
1D43E		006B
1D43F		006C
1D440		006D
1D441		006E
1D442		006F
1D443		0070
1D444		0071
1D445		0072
1D446		0073
1D447		0074
1D448		0075
1D449		0076
1D44A		0077
1D44B		0078
1D44C		0079
1D44D		007A
1D44E		0061
1D44F		0062
1D450		0063
1D451		0064
1D452		0065
1D453		0066
1D454		0067
1D456		0069
1D457		006A
1D458		006B
1D459		006C
1D45A		006D
1D45B		006E
1D45C		006F
1D45D		0070
1D45E		0071
1D45F		0072
1D460		0073
1D461		0074
1D462		0075
1D463		0076
1D464		0077
1D465		0078
1D466		0079
1D467		007A
1D468		0061
1D469		0062
1D46A		0063
1D46B		0064
1D46C		0065
1D46D		0066
1D46E		0067
1D46F		0068
1D470		0069
1D471		006A
1D472		006B
1D473		006C
1D474		006D
1D475		006E
1D476		006F
1D477		0070
1D478		0071
1D479		0072
1D47A		0073
1D47B		0074
1D47C		0075
1D47D		0076
1D47E		0077
1D47F		0078
1D480		0079
1D481		007A
1D482		0061
1D483		0062
1D484		0063
1D485		0064
1D486		0065
1D487		0066
1D488		0067
1D489		0068
1D48A		0069
1D48B		006A
1D48C		006B
1D48D		006C
1D48E		006D
1D48F		006E
1D490		006F
1D491		0070
1D492		0071
1D493		0072
1D494		0073
1D495		0074
1D496		0075
1D497		0076
1D498		0077
1D499		0078
1D49A		0079
1D49B		007A
1D49C		0061
1D49E		0063
1D49F		0064
1D4A2		0067
1D4A5		006A
1D4A6		006B
1D4A9		006E
1D4AA		006F
1D4AB		0070
1D4AC		0071
1D4AE		0073
1D4AF		0074
1D4B0		0075
1D4B1		0076
1D4B2		0077
1D4B3		0078
1D4B4		0079
1D4B5		007A
1D4B6		0061
1D4B7		0062
1D4B8		0063
1D4B9		0064
1D4BB		0066
1D4BD		0068
1D4BE		0069
1D4BF		006A
1D4C0		006B
1D4C1		006C
1D4C2		006D
1D4C3		006E
1D4C5		0070
1D4C6		0071
1D4C7		0072
1D4C8		0073
1D4C9		0074
1D4CA		0075
1D4CB		0076
1D4CC		0077
1D4CD		0078
1D4CE		0079
1D4CF		007A
1D4D0		0061
1D4D1		0062
1D4D2		0063
1D4D3		0064
1D4D4		0065
1D4D5		0066
1D4D6		0067
1D4D7		0068
1D4D8		0069
1D4D9		006A
1D4DA		006B
1D4DB		006C
1D4DC		006D
1D4DD		006E
1D4DE		006F
1D4DF		0070
1D4E0		0071
1D4E1		0072
1D4E2		0073
1D4E3		0074
1D4E4		0075
1D4E5		0076
1D4E6		0077
1D4E7		0078
1D4E8		0079
1D4E9		007A
1D4EA		0061
1D4EB		0062
1D4EC		0063
1D4ED		0064
1D4EE		0065
1D4EF		0066
1D4F0		0067
1D4F1		0068
1D4F2		0069
1D4F3		006A
1D4F4		006B
1D4F5		006C
1D4F6		006D
1D4F7		006E
1D4F8		006F
1D4F9		0070
1D4FA		0071
1D4FB		0072
1D4FC		0073
1D4FD		0074
1D4FE		0075
1D4FF		0076
1D500		0077
1D501		0078
1D502		0079
1D503		007A
1D504		0061
1D505		0062
1D507		0064
1D508		0065
1D509		0066
1D50A		0067
1D50D		006A
1D50E		006B
1D50F		006C
1D510		006D
1D511		006E
1D512		006F
1D513		0070
1D514		0071
1D516		0073
1D517		0074
1D518		0075
1D519		0076
1D51A		0077
1D51B		0078
1D51C		0079
1D51E		0061
1D51F		0062
1D520		0063
1D521		0064
1D522		0065
1D523		0066
1D524		0067
1D525		0068
1D526		0069
1D527		006A
1D528		006B
1D529		006C
1D52A		006D
1D52B		006E
1D52C		006F
1D52D		0070
1D52E		0071
1D52F		0072
1D530		0073
1D531		0074
1D532		0075
1D533		0076
1D534		0077
1D535		0078
1D536		0079
1D537		007A
1D538		0061
1D539		0062
1D53B		0064
1D53C		0065
1D53D		0066
1D53E		0067
1D540		0069
1D541		006A
1D542		006B
1D543		006C
1D544		006D
1D546		006F
1D54A		0073
1D54B		0074
1D54C		0075
1D54D		0076
1D54E		0077
1D54F		0078
1D550		0079
1D552		0061
1D553		0062
1D554		0063
1D555		0064
1D556		0065
1D557		0066
1D558		0067
1D559		0068
1D55A		0069
1D55B		006A
1D55C		006B
1D55D		006C
1D55E		006D
1D55F		006E
1D560		006F
1D561		0070
1D562		0071
1D563		0072
1D564		0073
1D565		0074
1D566		0075
1D567		0076
1D568		0077
1D569		0078
1D56A		0079
1D56B		007A
1D56C		0061
1D56D		0062
1D56E		0063
1D56F		0064
1D570		0065
1D571		0066
1D572		0067
1D573		0068
1D574		0069
1D575		006A
1D576		006B
1D577		006C
1D578		006D
1D579		006E
1D57A		006F
1D57B		0070
1D57C		0071
1D57D		0072
1D57E		0073
1D57F		0074
1D580		0075
1D581		0076
1D582		0077
1D583		0078
1D584		0079
1D585		007A
1D586		0061
1D587		0062
1D588		0063
1D589		0064
1D58A		0065
1D58B		0066
1D58C		0067
1D58D		0068
1D58E		0069
1D58F		006A
1D590		006B
1D591		006C
1D592		006D
1D593		006E
1D594		006F
1D595		0070
1D596		0071
1D597		0072
1D598		0073
1D599		0074
1D59A		0075
1D59B		0076
1D59C		0077
1D59D		0078
1D59E		0079
1D59F		007A
1D5A0		0061
1D5A1		0062
1D5A2		0063
1D5A3		0064
1D5A4		0065
1D5A5		0066
1D5A6		0067
1D5A7		0068
1D5A8		0069
1D5A9		006A
1D5AA		006B
1D5AB		006C
1D5AC		006D
1D5AD		006E
1D5AE		006F
1D5AF		0070
1D5B0		0071
1D5B1		0072
1D5B2		0073
1D5B3		0074
1D5B4		0075
1D5B5		0076
1D5B6		0077
1D5B7		0078
1D5B8		0079
1D5B9		007A
1D5BA		0061
1D5BB		0062
1D5BC		0063
1D5BD		0064
1D5BE		0065
1D5BF		0066
1D5C0		0067
1D5C1		0068
1D5C2		0069
1D5C3		006A
1D5C4		006B
1D5C5		006C
1D5C6		006D
1D5C7		006E
1D5C8		006F
1D5C9		0070
1D5CA		0071
1D5CB		0072
1D5CC		0073
1D5CD		0074
1D5CE		0075
1D5CF		0076
1D5D0		0077
1D5D1		0078
1D5D2		0079
1D5D3		007A
1D5D4		0061
1D5D5		0062
1D5D6		0063
1D5D7		0064
1D5D8		0065
1D5D9		0066
1D5DA		0067
1D5DB		0068
1D5DC		0069
1D5DD		006A
1D5DE		006B
1D5DF		006C
1D5E0		006D
1D5E1		006E
1D5E2		006F
1D5E3		0070
1D5E4		0071
1D5E5		0072
1D5E6		0073
1D5E7		0074
1D5E8		0075
1D5E9		0076
1D5EA		0077
1D5EB		0078
1D5EC		0079
1D5ED		007A
1D5EE		0061
1D5EF		0062
1D5F0		0063
1D5F1		0064
1D5F2		0065
1D5F3		0066
1D5F4		0067
1D5F5		0068
1D5F6		0069
1D5F7		006A
1D5F8		006B
1D5F9		006C
1D5FA		006D
1D5FB		006E
1D5FC		006F
1D5FD		0070
1D5FE		0071
1D5FF		0072
1D600		0073
1D601		0074
1D602		0075
1D603		0076
1D604		0077
1D605		0078
1D606		0079
1D607		007A
1D608		0061
1D609		0062
1D60A		0063
1D60B		0064
1D60C		0065
1D60D		0066
1D60E		0067
1D60F		0068
1D610		0069
1D611		006A
1D612		006B
1D613		006C
1D614		006D
1D615		006E
1D616		006F
1D617		0070
1D618		0071
1D619		0072
1D61A		0073
1D61B		0074
1D61C		0075
1D61D		0076
1D61E		0077
1D61F		0078
1D620		0079
1D621		007A
1D622		0061
1D623		0062
1D624		0063
1D625		0064
1D626		0065
1D627		0066
1D628		0067
1D629		0068
1D62A		0069
1D62B		006A
1D62C		006B
1D62D		006C
1D62E		006D
1D62F		006E
1D630		006F
1D631		0070
1D632		0071
1D633		0072
1D634		0073
1D635		0074
1D636		0075
1D637		0076
1D638		0077
1D639		0078
1D63A		0079
1D63B		007A
1D63C		0061
1D63D		0062
1D63E		0063
1D63F		0064
1D640		0065
1D641		0066
1D642		0067
1D643		0068
1D644		0069
1D645		006A
1D646		006B
1D647		006C
1D648		006D
1D649		006E
1D64A		006F
1D64B		0070
1D64C		0071
1D64D		0072
1D64E		0073
1D64F		0074
1D650		0075
1D651		0076
1D652		0077
1D653		0078
1D654		0079
1D655		007A
1D656		0061
1D657		0062
1D658		0063
1D659		0064
1D65A		0065
1D65B		0066
1D65C		0067
1D65D		0068
1D65E		0069
1D65F		006A
1D660		006B
1D661		006C
1D662		006D
1D663		006E
1D664		006F
1D665		0070
1D666		0071
1D667		0072
1D668		0073
1D669		0074
1D66A		0075
1D66B		0076
1D66C		0077
1D66D		0078
1D66E		0079
1D66F		007A
1D670		0061
1D671		0062
1D672		0063
1D673		0064
1D674		0065
1D675		0066
1D676		0067
1D677		0068
1D678		0069
1D679		006A
1D67A		006B
1D67B		006C
1D67C		006D
1D67D		006E
1D67E		006F
1D67F		0070
1D680		0071
1D681		0072
1D682		0073
1D683		0074
1D684		0075
1D685		0076
1D686		0077
1D687		0078
1D688		0079
1D689		007A
1D68A		0061
1D68B		0062
1D68C		0063
1D68D		0064
1D68E		0065
1D68F		0066
1D690		0067
1D691		0068
1D692		0069
1D693		006A
1D694		006B
1D695		006C
1D696		006D
1D697		006E
1D698		006F
1D699		0070
1D69A		0071
1D69B		0072
1D69C		0073
1D69D		0074
1D69E		0075
1D69F		0076
1D6A0		0077
1D6A1		0078
1D6A2		0079
1D6A3		007A
1D6A4		0131
1D6A5		0237
1D6A8		03B1
1D6A9		03B2
1D6AA		03B3
1D6AB		03B4
1D6AC		03B5
1D6AD		03B6
1D6AE		03B7
1D6AF		03B8
1D6B0		03B9
1D6B1		03BA
1D6B2		03BB
1D6B3		03BC
1D6B4		03BD
1D6B5		03BE
1D6B6		03BF
1D6B7		03C0
1D6B8		03C1
1D6B9		03B8
1D6BA		03C3
1D6BB		03C4
1D6BC		03C5
1D6BD		03C6
1D6BE		03C7
1D6BF		03C8
1D6C0		03C9
1D6C1		2207
1D6C2		03B1
1D6C3		03B2
1D6C4		03B3
1D6C5		03B4
1D6C6		03B5
1D6C7		03B6
1D6C8		03B7
1D6C9		03B8
1D6CA		03B9
1D6CB		03BA
1D6CC		03BB
1D6CD		03BC
1D6CE		03BD
1D6CF		03BE
1D6D0		03BF
1D6D1		03C0
1D6D2		03C1
1D6D3		03C3
1D6D4		03C3
1D6D5		03C4
1D6D6		03C5
1D6D7		03C6
1D6D8		03C7
1D6D9		03C8
1D6DA		03C9
1D6DB		2202
1D6DC		03B5
1D6DD		03B8
1D6DE		03BA
1D6DF		03C6
1D6E0		03C1
1D6E1		03C0
1D6E2		03B1
1D6E3		03B2
1D6E4		03B3
1D6E5		03B4
1D6E6		03B5
1D6E7		03B6
1D6E8		03B7
1D6E9		03B8
1D6EA		03B9
1D6EB		03BA
1D6EC		03BB
1D6ED		03BC
1D6EE		03BD
1D6EF		03BE
1D6F0		03BF
1D6F1		03C0
1D6F2		03C1
1D6F3		03B8
1D6F4		03C3
1D6F5		03C4
1D6F6		03C5
1D6F7		03C6
1D6F8		03C7
1D6F9		03C8
1D6FA		03C9
1D6FB		2207
1D6FC		03B1
1D6FD		03B2
1D6FE		03B3
1D6FF		03B4
1D700		03B5
1D701		03B6
1D702		03B7
1D703		03B8
1D704		03B9
1D705		03BA
1D706		03BB
1D707		03BC
1D708		03BD
1D709		03BE
1D70A		03BF
1D70B		03C0
1D70C		03C1
1D70D		03C3
1D70E		03C3
1D70F		03C4
1D710		03C5
1D711		03C6
1D712		03C7
1D713		03C8
1D714		03C9
1D715		2202
1D716		03B5
1D717		03B8
1D718		03BA
1D719		03C6
1D71A		03C1
1D71B		03C0
1D71C		03B1
1D71D		03B2
1D71E		03B3
1D71F		03B4
1D720		03B5
1D721		03B6
1D722		03B7
1D723		03B8
1D724		03B9
1D725		03BA
1D726		03BB
1D727		03BC
1D728		03BD
1D729		03BE
1D72A		03BF
1D72B		03C0
1D72C		03C1
1D72D		03B8
1D72E		03C3
1D72F		03C4
1D730		03C5
1D731		03C6
1D732		03C7
1D733		03C8
1D734		03C9
1D735		2207
1D736		03B1
1D737		03B2
1D738		03B3
1D739		03B4
1D73A		03B5
1D73B		03B6
1D73C		03B7
1D73D		03B8
1D73E		03B9
1D73F		03BA
1D740		03BB
1D741		03BC
1D742		03BD
1D743		03BE
1D744		03BF
1D745		03C0
1D746		03C1
1D747		03C3
1D748		03C3
1D749		03C4
1D74A		03C5
1D74B		03C6
1D74C		03C7
1D74D		03C8
1D74E		03C9
1D74F		2202
1D750		03B5
1D751		03B8
1D752		03BA
1D753		03C6
1D754		03C1
1D755		03C0
1D756		03B1
1D757		03B2
1D758		03B3
1D759		03B4
1D75A		03B5
1D75B		03B6
1D75C		03B7
1D75D		03B8
1D75E		03B9
1D75F		03BA
1D760		03BB
1D761		03BC
1D762		03BD
1D763		03BE
1D764		03BF
1D765		03C0
1D766		03C1
1D767		03B8
1D768		03C3
1D769		03C4
1D76A		03C5
1D76B		03C6
1D76C		03C7
1D76D		03C8
1D76E		03C9
1D76F		2207
1D770		03B1
1D771		03B2
1D772		03B3
1D773		03B4
1D774		03B5
1D775		03B6
1D776		03B7
1D777		03B8
1D778		03B9
1D779		03BA
1D77A		03BB
1D77B		03BC
1D77C		03BD
1D77D		03BE
1D77E		03BF
1D77F		03C0
1D780		03C1
1D781		03C3
1D782		03C3
1D783		03C4
1D784		03C5
1D785		03C6
1D786		03C7
1D787		03C8
1D788		03C9
1D789		2202
1D78A		03B5
1D78B		03B8
1D78C		03BA
1D78D		03C6
1D78E		03C1
1D78F		03C0
1D790		03B1
1D791		03B2
1D792		03B3
1D793		03B4
1D794		03B5
1D795		03B6
1D796		03B7
1D797		03B8
1D798		03B9
1D799		03BA
1D79A		03BB
1D79B		03BC
1D79C		03BD
1D79D		03BE
1D79E		03BF
1D79F		03C0
1D7A0		03C1
1D7A1		03B8
1D7A2		03C3
1D7A3		03C4
1D7A4		03C5
1D7A5		03C6
1D7A6		03C7
1D7A7		03C8
1D7A8		03C9
1D7A9		2207
1D7AA		03B1
1D7AB		03B2
1D7AC		03B3
1D7AD		03B4
1D7AE		03B5
1D7AF		03B6
1D7B0		03B7
1D7B1		03B8
1D7B2		03B9
1D7B3		03BA
1D7B4		03BB
1D7B5		03BC
1D7B6		03BD
1D7B7		03BE
1D7B8		03BF
1D7B9		03C0
1D7BA		03C1
1D7BB		03C3
1D7BC		03C3
1D7BD		03C4
1D7BE		03C5
1D7BF		03C6
1D7C0		03C7
1D7C1		03C8
1D7C2		03C9
1D7C3		2202
1D7C4		03B5
1D7C5		03B8
1D7C6		03BA
1D7C7		03C6
1D7C8		03C1
1D7C9		03C0
1D7CA		03DD
1D7CB		03DD
1D7CE		0030
1D7CF		0031
1D7D0		0032
1D7D1		0033
1D7D2		0034
1D7D3		0035
1D7D4		0036
1D7D5		0037
1D7D6		0038
1D7D7		0039
1D7D8		0030
1D7D9		0031
1D7DA		0032
1D7DB		0033
1D7DC		0034
1D7DD		0035
1D7DE		0036
1D7DF		0037
1D7E0		0038
1D7E1		0039
1D7E2		0030
1D7E3		0031
1D7E4		0032
1D7E5		0033
1D7E6		0034
1D7E7		0035
1D7E8		0036
1D7E9		0037
1D7EA		0038
1D7EB		0039
1D7EC		0030
1D7ED		0031
1D7EE		0032
1D7EF		0033
1D7F0		0034
1D7F1		0035
1D7F2		0036
1D7F3		0037
1D7F4		0038
1D7F5		0039
1D7F6		0030
1D7F7		0031
1D7F8		0032
1D7F9		0033
1D7FA		0034
1D7FB		0035
1D7FC		0036
1D7FD		0037
1D7FE		0038
1D7FF		0039
1E030		0430
1E031		0431
1E032		0432
1E033		0433
1E034		0434
1E035		0435
1E036		0436
1E037		0437
1E038		0438
1E039		043A
1E03A		043B
1E03B		043C
1E03C		043E
1E03D		043F
1E03E		0440
1E03F		0441
1E040		0442
1E041		0443
1E042		0444
1E043		0445
1E044		0446
1E045		0447
1E046		0448
1E047		044B
1E048		044D
1E049		044E
1E04A		A689
1E04B		04D9
1E04C		0456
1E04D		0458
1E04E		04E9
1E04F		04AF
1E050		04CF
1E051		0430
1E052		0431
1E053		0432
1E054		0433
1E055		0434
1E056		0435
1E057		0436
1E058		0437
1E059		0438
1E05A		043A
1E05B		043B
1E05C		043E
1E05D		043F
1E05E		0441
1E05F		0443
1E060		0444
1E061		0445
1E062		0446
1E063		0447
1E064		0448
1E065		044A
1E066		044B
1E067		0491
1E068		0456
1E069		0455
1E06A		045F
1E06B		04AB
1E06C		A651
1E06D		04B1
1E900		1E922
1E901		1E923
1E902		1E924
1E903		1E925
1E904		1E926
1E905		1E927
1E906		1E928
1E907		1E929
1E908		1E92A
1E909		1E92B
1E90A		1E92C
1E90B		1E92D
1E90C		1E92E
1E90D		1E92F
1E90E		1E930
1E90F		1E931
1E910		1E932
1E911		1E933
1E912		1E934
1E913		1E935
1E914		1E936
1E915		1E937
1E916		1E938
1E917		1E939
1E918		1E93A
1E919		1E93B
1E91A		1E93C
1E91B		1E93D
1E91C		1E93E
1E91D		1E93F
1E91E		1E940
1E91F		1E941
1E920		1E942
1E921		1E943
1EE00		0627
1EE01		0628
1EE02		062C
1EE03		062F
1EE05		0648
1EE06		0632
1EE07		062D
1EE08		0637
1EE09		064A
1EE0A		0643
1EE0B		0644
1EE0C		0645
1EE0D		0646
1EE0E		0633
1EE0F		0639
1EE10		0641
1EE11		0635
1EE12		0642
1EE13		0631
1EE14		0634
1EE15		062A
1EE16		062B
1EE17		062E
1EE18		0630
1EE19		0636
1EE1A		0638
1EE1B		063A
1EE1C		066E
1EE1D		06BA
1EE1E		06A1
1EE1F		066F
1EE21		0628
1EE22		062C
1EE24		0647
1EE27		062D
1EE29		064A
1EE2A		0643
1EE2B		0644
1EE2C		0645
1EE2D		0646
1EE2E		0633
1EE2F		0639
1EE30		0641
1EE31		0635
1EE32		0642
1EE34		0634
1EE35		062A
1EE36		062B
1EE37		062E
1EE39		0636
1EE3B		063A
1EE42		062C
1EE47		062D
1EE49		064A
1EE4B		0644
1EE4D		0646
1EE4E		0633
1EE4F		0639
1EE51		0635
1EE52		0642
1EE54		0634
1EE57		062E
1EE59		0636
1EE5B		063A
1EE5D		06BA
1EE5F		066F
1EE61		0628
1EE62		062C
1EE64		0647
1EE67		062D
1EE68		0637
1EE69		064A
1EE6A		0643
1EE6C		0645
1EE6D		0646
1EE6E		0633
1EE6F		0639
1EE70		0641
1EE71		0635
1EE72		0642
1EE74		0634
1EE75		062A
1EE76		062B
1EE77		062E
1EE79		0636
1EE7A		0638
1EE7B		063A
1EE7C		066E
1EE7E		06A1
1EE80		0627
1EE81		0628
1EE82		062C
1EE83		062F
1EE84		0647
1EE85		0648
1EE86		0632
1EE87		062D
1EE88		0637
1EE89		064A
1EE8B		0644
1EE8C		0645
1EE8D		0646
1EE8E		0633
1EE8F		0639
1EE90		0641
1EE91		0635
1EE92		0642
1EE93		0631
1EE94		0634
1EE95		062A
1EE96		062B
1EE97		062E
1EE98		0630
1EE99		0636
1EE9A		0638
1EE9B		063A
1EEA1		0628
1EEA2		062C
1EEA3		062F
1EEA5		0648
1EEA6		0632
1EEA7		062D
1EEA8		0637
1EEA9		064A
1EEAB		0644
1EEAC		0645
1EEAD		0646
1EEAE		0633
1EEAF		0639
1EEB0		0641
1EEB1		0635
1EEB2		0642
1EEB3		0631
1EEB4		0634
1EEB5		062A
1EEB6		062B
1EEB7		062E
1EEB8		0630
1EEB9		0636
1EEBA		0638
1EEBB		063A
1F12B		0063
1F12C		0072
1F130		0061
1F131		0062
1F132		0063
1F133		0064
1F134		0065
1F135		0066
1F136		0067
1F137		0068
1F138		0069
1F139		006A
1F13A		006B
1F13B		006C
1F13C		006D
1F13D		006E
1F13E		006F
1F13F		0070
1F140		0071
1F141		0072
1F142		0073
1F143		0074
1F144		0075
1F145		0076
1F146		0077
1F147		0078
1F148		0079
1F149		007A
1F202		30B5
1F210		624B
1F211		5B57
1F212		53CC
1F213		30C7
1F214		4E8C
1F215		591A
1F216		89E3
1F217		5929
1F218		4EA4
1F219		6620
1F21A		7121
1F21B		6599
1F21C		524D
1F21D		5F8C
1F21E		518D
1F21F		65B0
1F220		521D
1F221		7D42
1F222		751F
1F223		8CA9
1F224		58F0
1F225		5439
1F226		6F14
1F227		6295
1F228		6355
1F229		4E00
1F22A		4E09
1F22B		904A
1F22C		5DE6
1F22D		4E2D
1F22E		53F3
1F22F		6307
1F230		8D70
1F231		6253
1F232		7981
1F233		7A7A
1F234		5408
1F235		6E80
1F236		6709
1F237		6708
1F238		7533
1F239		5272
1F23A		55B6
1F23B		914D
1F250		5F97
1F251		53EF
1FBF0		0030
1FBF1		0031
1FBF2		0032
1FBF3		0033
1FBF4		0034
1FBF5		0035
1FBF6		0036
1FBF7		0037
1FBF8		0038
1FBF9		0039
2F800		4E3D
2F801		4E38
2F802		4E41
2F803		20122
2F804		4F60
2F805		4FAE
2F806		4FBB
2F807		5002
2F808		507A
2F809		5099
2F80A		50E7
2F80B		50CF
2F80C		349E
2F80D		2063A
2F80E		514D
2F80F		5154
2F810		5164
2F811		5177
2F812		2051C
2F813		34B9
2F814		5167
2F815		518D
2F816		2054B
2F817		5197
2F818		51A4
2F819		4ECC
2F81A		51AC
2F81B		51B5
2F81C		291DF
2F81D		51F5
2F81E		5203
2F81F		34DF
2F820		523B
2F821		5246
2F822		5272
2F823		5277
2F824		3515
2F825		52C7
2F826		52C9
2F827		52E4
2F828		52FA
2F829		5305
2F82A		5306
2F82B		5317
2F82C		5349
2F82D		5351
2F82E		535A
2F82F		5373
2F830		537D
2F831		537F
2F832		537F
2F833		537F
2F834		20A2C
2F835		7070
2F836		53CA
2F837		53DF
2F838		20B63
2F839		53EB
2F83A		53F1
2F83B		5406
2F83C		549E
2F83D		5438
2F83E		5448
2F83F		5468
2F840		54A2
2F841		54F6
2F842		5510
2F843		5553
2F844		5563
2F845		5584
2F846		5584
2F847		5599
2F848		55AB
2F849		55B3
2F84A		55C2
2F84B		5716
2F84C		5606
2F84D		5717
2F84E		5651
2F84F		5674
2F850		5207
2F851		58EE
2F852		57CE
2F853		57F4
2F854		580D
2F855		578B
2F856		5832
2F857		5831
2F858		58AC
2F859		214E4
2F85A		58F2
2F85B		58F7
2F85C		5906
2F85D		591A
2F85E		5922
2F85F		5962
2F860		216A8
2F861		216EA
2F862		59EC
2F863		5A1B
2F864		5A27
2F865		59D8
2F866		5A66
2F867		36EE
2F868		36FC
2F869		5B08
2F86A		5B3E
2F86B		5B3E
2F86C		219C8
2F86D		5BC3
2F86E		5BD8
2F86F		5BE7
2F870		5BF3
2F871		21B18
2F872		5BFF
2F873		5C06
2F874		5F53
2F875		5C22
2F876		3781
2F877		5C60
2F878		5C6E
2F879		5CC0
2F87A		5C8D
2F87B		21DE4
2F87C		5D43
2F87D		21DE6
2F87E		5D6E
2F87F		5D6B
2F880		5D7C
2F881		5DE1
2F882		5DE2
2F883		382F
2F884		5DFD
2F885		5E28
2F886		5E3D
2F887		5E69
2F888		3862
2F889		22183
2F88A		387C
2F88B		5EB0
2F88C		5EB3
2F88D		5EB6
2F88E		5ECA
2F88F		2A392
2F890		5EFE
2F891		22331
2F892		22331
2F893		8201
2F894		5F22
2F895		5F22
2F896		38C7
2F897		232B8
2F898		261DA
2F899		5F62
2F89A		5F6B
2F89B		38E3
2F89C		5F9A
2F89D		5FCD
2F89E		5FD7
2F89F		5FF9
2F8A0		6081
2F8A1		393A
2F8A2		391C
2F8A3		6094
2F8A4		226D4
2F8A5		60C7
2F8A6		6148
2F8A7		614C
2F8A8		614E
2F8A9		614C
2F8AA		617A
2F8AB		618E
2F8AC		61B2
2F8AD		61A4
2F8AE		61AF
2F8AF		61DE
2F8B0		61F2
2F8B1		61F6
2F8B2		6210
2F8B3		621B
2F8B4		625D
2F8B5		62B1
2F8B6		62D4
2F8B7		6350
2F8B8		22B0C
2F8B9		633D
2F8BA		62FC
2F8BB		6368
2F8BC		6383
2F8BD		63E4
2F8BE		22BF1
2F8BF		6422
2F8C0		63C5
2F8C1		63A9
2F8C2		3A2E
2F8C3		6469
2F8C4		647E
2F8C5		649D
2F8C6		6477
2F8C7		3A6C
2F8C8		654F
2F8C9		656C
2F8CA		2300A
2F8CB		65E3
2F8CC		66F8
2F8CD		6649
2F8CE		3B19
2F8CF		6691
2F8D0		3B08
2F8D1		3AE4
2F8D2		5192
2F8D3		5195
2F8D4		6700
2F8D5		669C
2F8D6		80AD
2F8D7		43D9
2F8D8		6717
2F8D9		671B
2F8DA		6721
2F8DB		675E
2F8DC		6753
2F8DD		233C3
2F8DE		3B49
2F8DF		67FA
2F8E0		6785
2F8E1		6852
2F8E2		6885
2F8E3		2346D
2F8E4		688E
2F8E5		681F
2F8E6		6914
2F8E7		3B9D
2F8E8		6942
2F8E9		69A3
2F8EA		69EA
2F8EB		6AA8
2F8EC		236A3
2F8ED		6ADB
2F8EE		3C18
2F8EF		6B21
2F8F0		238A7
2F8F1		6B54
2F8F2		3C4E
2F8F3		6B72
2F8F4		6B9F
2F8F5		6BBA
2F8F6		6BBB
2F8F7		23A8D
2F8F8		21D0B
2F8F9		23AFA
2F8FA		6C4E
2F8FB		23CBC
2F8FC		6CBF
2F8FD		6CCD
2F8FE		6C67
2F8FF		6D16
2F900		6D3E
2F901		6D77
2F902		6D41
2F903		6D69
2F904		6D78
2F905		6D85
2F906		23D1E
2F907		6D34
2F908		6E2F
2F909		6E6E
2F90A		3D33
2F90B		6ECB
2F90C		6EC7
2F90D		23ED1
2F90E		6DF9
2F90F		6F6E
2F910		23F5E
2F911		23F8E
2F912		6FC6
2F913		7039
2F914		701E
2F915		701B
2F916		3D96
2F917		704A
2F918		707D
2F919		7077
2F91A		70AD
2F91B		20525
2F91C		7145
2F91D		24263
2F91E		719C
2F91F		243AB
2F920		7228
2F921		7235
2F922		7250
2F923		24608
2F924		7280
2F925		7295
2F926		24735
2F927		24814
2F928		737A
2F929		738B
2F92A		3EAC
2F92B		73A5
2F92C		3EB8
2F92D		3EB8
2F92E		7447
2F92F		745C
2F930		7471
2F931		7485
2F932		74CA
2F933		3F1B
2F934		7524
2F935		24C36
2F936		753E
2F937		24C92
2F938		7570
2F939		2219F
2F93A		7610
2F93B		24FA1
2F93C		24FB8
2F93D		25044
2F93E		3FFC
2F93F		4008
2F940		76F4
2F941		250F3
2F942		250F2
2F943		25119
2F944		25133
2F945		771E
2F946		771F
2F947		771F
2F948		774A
2F949		4039
2F94A		778B
2F94B		4046
2F94C		4096
2F94D		2541D
2F94E		784E
2F94F		788C
2F950		78CC
2F951		40E3
2F952		25626
2F953		7956
2F954		2569A
2F955		256C5
2F956		798F
2F957		79EB
2F958		412F
2F959		7A40
2F95A		7A4A
2F95B		7A4F
2F95C		2597C
2F95D		25AA7
2F95E		25AA7
2F95F		7AEE
2F960		4202
2F961		25BAB
2F962		7BC6
2F963		7BC9
2F964		4227
2F965		25C80
2F966		7CD2
2F967		42A0
2F968		7CE8
2F969		7CE3
2F96A		7D00
2F96B		25F86
2F96C		7D63
2F96D		4301
2F96E		7DC7
2F96F		7E02
2F970		7E45
2F971		4334
2F972		26228
2F973		26247
2F974		4359
2F975		262D9
2F976		7F7A
2F977		2633E
2F978		7F95
2F979		7FFA
2F97A		8005
2F97B		264DA
2F97C		26523
2F97D		8060
2F97E		265A8
2F97F		8070
2F980		2335F
2F981		43D5
2F982		80B2
2F983		8103
2F984		440B
2F985		813E
2F986		5AB5
2F987		267A7
2F988		267B5
2F989		23393
2F98A		2339C
2F98B		8201
2F98C		8204
2F98D		8F9E
2F98E		446B
2F98F		8291
2F990		828B
2F991		829D
2F992		52B3
2F993		82B1
2F994		82B3
2F995		82BD
2F996		82E6
2F997		26B3C
2F998		82E5
2F999		831D
2F99A		8363
2F99B		83AD
2F99C		8323
2F99D		83BD
2F99E		83E7
2F99F		8457
2F9A0		8353
2F9A1		83CA
2F9A2		83CC
2F9A3		83DC
2F9A4		26C36
2F9A5		26D6B
2F9A6		26CD5
2F9A7		452B
2F9A8		84F1
2F9A9		84F3
2F9AA		8516
2F9AB		273CA
2F9AC		8564
2F9AD		26F2C
2F9AE		455D
2F9AF		4561
2F9B0		26FB1
2F9B1		270D2
2F9B2		456B
2F9B3		8650
2F9B4		865C
2F9B5		8667
2F9B6		8669
2F9B7		86A9
2F9B8		8688
2F9B9		870E
2F9BA		86E2
2F9BB		8779
2F9BC		8728
2F9BD		876B
2F9BE		8786
2F9BF		45D7
2F9C0		87E1
2F9C1		8801
2F9C2		45F9
2F9C3		8860
2F9C4		8863
2F9C5		27667
2F9C6		88D7
2F9C7		88DE
2F9C8		4635
2F9C9		88FA
2F9CA		34BB
2F9CB		278AE
2F9CC		27966
2F9CD		46BE
2F9CE		46C7
2F9CF		8AA0
2F9D0		8AED
2F9D1		8B8A
2F9D2		8C55
2F9D3		27CA8
2F9D4		8CAB
2F9D5		8CC1
2F9D6		8D1B
2F9D7		8D77
2F9D8		27F2F
2F9D9		20804
2F9DA		8DCB
2F9DB		8DBC
2F9DC		8DF0
2F9DD		208DE
2F9DE		8ED4
2F9DF		8F38
2F9E0		285D2
2F9E1		285ED
2F9E2		9094
2F9E3		90F1
2F9E4		9111
2F9E5		2872E
2F9E6		911B
2F9E7		9238
2F9E8		92D7
2F9E9		92D8
2F9EA		927C
2F9EB		93F9
2F9EC		9415
2F9ED		28BFA
2F9EE		958B
2F9EF		4995
2F9F0		95B7
2F9F1		28D77
2F9F2		49E6
2F9F3		96C3
2F9F4		5DB2
2F9F5		9723
2F9F6		29145
2F9F7		2921A
2F9F8		4A6E
2F9F9		4A76
2F9FA		97E0
2F9FB		2940A
2F9FC		4AB2
2F9FD		29496
2F9FE		980B
2F9FF		980B
2FA00		9829
2FA01		295B6
2FA02		98E2
2FA03		4B33
2FA04		9929
2FA05		99A7
2FA06		99C2
2FA07		99FE
2FA08		4BCE
2FA09		29B30
2FA0A		9B12
2FA0B		9C40
2FA0C		9CFD
2FA0D		4CCE
2FA0E		4CED
2FA0F		9D67
2FA10		2A0CE
2FA11		4CF8
2FA12		2A105
2FA13		2A20E
2FA14		2A291
2FA15		9EBB
2FA16		4D56
2FA17		9EF9
2FA18		9EFE
2FA19		9F05
2FA1A		9F0F
2FA1B		9F16
2FA1C		9F3B
2FA1D		2A600
END
