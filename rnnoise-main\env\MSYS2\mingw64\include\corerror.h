/*
 * Copyright 2008 <PERSON>
 * Copyright 2014 Kai T<PERSON>z
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifndef __WINE_CORERROR_H
#define __WINE_CORERROR_H

#include <winerror.h>

#ifndef FACILITY_URT
#define FACILITY_URT 0x13
#endif

#ifndef EMAKEHR
#define SMAKEHR(val) MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_URT, val)
#define EMAKEHR(val) MAKE_HRESULT(SEVERITY_ERROR, FACILITY_URT, val)
#endif

#define COR_E_ARGUMENT              E_INVALIDARG
#define COR_E_INVALIDCAST           E_NOINTERFACE
#define COR_E_NULLREFERENCE         E_POINTER
#define COR_E_OUTOFMEMORY           E_OUTOFMEMORY
#define COR_E_UNAUTHORIZEDACCESS    E_ACCESSDENIED

#define COR_E_ARITHMETIC            HRESULT_FROM_WIN32(ERROR_ARITHMETIC_OVERFLOW)
#define COR_E_STACKOVERFLOW         HRESULT_FROM_WIN32(ERROR_STACK_OVERFLOW)
#define COR_E_ENDOFSTREAM           HRESULT_FROM_WIN32(ERROR_HANDLE_EOF)
#define COR_E_FILENOTFOUND          HRESULT_FROM_WIN32(ERROR_FILE_NOT_FOUND)
#define COR_E_BAD_PATHNAME          HRESULT_FROM_WIN32(ERROR_BAD_PATHNAME)
#define COR_E_DIRECTORYNOTFOUND     HRESULT_FROM_WIN32(ERROR_PATH_NOT_FOUND)
#define COR_E_PATHTOOLONG           HRESULT_FROM_WIN32(ERROR_FILENAME_EXCED_RANGE)

#define COR_E_AMBIGUOUSMATCH    _HRESULT_TYPEDEF_(0x8000211D)
#define COR_E_TARGETPARAMCOUNT  _HRESULT_TYPEDEF_(0x8002000E)
#define COR_E_DIVIDEBYZERO      _HRESULT_TYPEDEF_(0x80020012)
#define COR_E_BADIMAGEFORMAT    _HRESULT_TYPEDEF_(0x8007000B)

#define COR_E_ASSEMBLYEXPECTED          EMAKEHR(0x1018)
#define COR_E_TYPEUNLOADED              EMAKEHR(0x1013)
#define COR_E_APPDOMAINUNLOADED         EMAKEHR(0x1014)
#define COR_E_CANNOTUNLOADAPPDOMAIN     EMAKEHR(0x1015)
#define COR_E_FIXUPSINEXE               EMAKEHR(0x1019)
#define COR_E_NO_LOADLIBRARY_ALLOWED    EMAKEHR(0x101a)
#define COR_E_NEWER_RUNTIME             EMAKEHR(0x101b)
#define COR_E_CANNOT_SET_POLICY         EMAKEHR(0x101c)
#define COR_E_CANNOT_SPECIFY_EVIDENCE   EMAKEHR(0x101d)
#define COR_E_MULTIMODULEASSEMBLIESDIALLOWED EMAKEHR(0x101e)
#define COR_E_MODULE_HASH_CHECK_FAILED  EMAKEHR(0x1039)
#define COR_E_ASSEMBLY_NOT_EXPECTED     EMAKEHR(0x1057)
#define COR_E_EXCEPTION                 EMAKEHR(0x1500)
#define COR_E_SYSTEM                    EMAKEHR(0x1501)
#define COR_E_ARGUMENTOUTOFRANGE        EMAKEHR(0x1502)
#define COR_E_ARRAYTYPEMISMATCH         EMAKEHR(0x1503)
#define COR_E_CONTEXTMARSHAL            EMAKEHR(0x1504)
#define COR_E_TIMEOUT                   EMAKEHR(0x1505)
#define COR_E_EXECUTIONENGINE           EMAKEHR(0x1506)
#define COR_E_FIELDACCESS               EMAKEHR(0x1507)
#define COR_E_INDEXOUTOFRANGE           EMAKEHR(0x1508)
#define COR_E_INVALIDOPERATION          EMAKEHR(0x1509)
#define COR_E_SECURITY                  EMAKEHR(0x150A)
#define COR_E_REMOTING                  EMAKEHR(0x150B)
#define COR_E_SERIALIZATION             EMAKEHR(0x150C)
#define COR_E_VERIFICATION              EMAKEHR(0x150D)
#define COR_E_SERVER                    EMAKEHR(0x150E)
#define COR_E_SERVICEDCOMPONENT         EMAKEHR(0x150F)
#define COR_E_METHODACCESS              EMAKEHR(0x1510)
#define COR_E_MISSINGFIELD              EMAKEHR(0x1511)
#define COR_E_MISSINGMEMBER             EMAKEHR(0x1512)
#define COR_E_MISSINGMETHOD             EMAKEHR(0x1513)
#define COR_E_MULTICASTNOTSUPPORTED     EMAKEHR(0x1514)
#define COR_E_NOTSUPPORTED              EMAKEHR(0x1515)
#define COR_E_OVERFLOW                  EMAKEHR(0x1516)
#define COR_E_RANK                      EMAKEHR(0x1517)
#define COR_E_SYNCHRONIZATIONLOCK       EMAKEHR(0x1518)
#define COR_E_THREADINTERRUPTED         EMAKEHR(0x1519)
#define COR_E_MEMBERACCESS              EMAKEHR(0x151A)
#define COR_E_THREADSTATE               EMAKEHR(0x1520)
#define COR_E_THREADSTOP                EMAKEHR(0x1521)
#define COR_E_TYPELOAD                  EMAKEHR(0x1522)
#define COR_E_ENTRYPOINTNOTFOUND        EMAKEHR(0x1523)
#define COR_E_DLLNOTFOUND               EMAKEHR(0x1524)
#define COR_E_THREADSTART               EMAKEHR(0x1525)
#define COR_E_INVALIDCOMOBJECT          EMAKEHR(0x1527)
#define COR_E_NOTFINITENUMBER           EMAKEHR(0x1528)
#define COR_E_DUPLICATEWAITOBJECT       EMAKEHR(0x1529)
#define COR_E_SEMAPHOREFULL             EMAKEHR(0x152B)
#define COR_E_WAITHANDLECANNOTBEOPENED  EMAKEHR(0x152C)
#define COR_E_ABANDONEDMUTEX            EMAKEHR(0x152D)
#define COR_E_THREADABORTED             EMAKEHR(0x1530)
#define COR_E_INVALIDOLEVARIANTTYPE     EMAKEHR(0x1531)
#define COR_E_MISSINGMANIFESTRESOURCE   EMAKEHR(0x1532)
#define COR_E_SAFEARRAYTYPEMISMATCH     EMAKEHR(0x1533)
#define COR_E_TYPEINITIALIZATION        EMAKEHR(0x1534)
#define COR_E_MARSHALDIRECTIVE          EMAKEHR(0x1535)
#define COR_E_MISSINGSATELLITEASSEMBLY  EMAKEHR(0x1536)
#define COR_E_FORMAT                    EMAKEHR(0x1537)
#define COR_E_SAFEARRAYRANKMISMATCH     EMAKEHR(0x1538)
#define COR_E_PLATFORMNOTSUPPORTED      EMAKEHR(0x1539)
#define COR_E_INVALIDPROGRAM            EMAKEHR(0x153A)
#define COR_E_OPERATIONCANCELED         EMAKEHR(0x153B)
#define COR_E_INSUFFICIENTMEMORY        EMAKEHR(0x153D)
#define COR_E_RUNTIMEWRAPPED            EMAKEHR(0x153E)
#define COR_E_DEVICESNOTSUPPORTED       EMAKEHR(0x1540)
#define COR_E_DATAMISALIGNED            EMAKEHR(0x1541)
#define COR_E_CODECONTRACTFAILED        EMAKEHR(0x1542)
#define COR_E_TYPEACCESS                EMAKEHR(0x1543)
#define COR_E_ACCESSING_CCW             EMAKEHR(0x1544)
#define COR_E_LOADING_REFERENCE_ASSEMBLY EMAKEHR(0x1058)
#define COR_E_KEYNOTFOUND               EMAKEHR(0x1577)
#define COR_E_INSUFFICIENTEXECUTIONSTACK EMAKEHR(0x1578)
#define COR_E_APPLICATION               EMAKEHR(0x1600)
#define COR_E_INVALIDFILTERCRITERIA     EMAKEHR(0x1601)
#define COR_E_REFLECTIONTYPELOAD        EMAKEHR(0x1602)
#define COR_E_TARGET                    EMAKEHR(0x1603)
#define COR_E_TARGETINVOCATION          EMAKEHR(0x1604)
#define COR_E_CUSTOMATTRIBUTEFORMAT     EMAKEHR(0x1605)
#define COR_E_IO                        EMAKEHR(0x1620)
#define COR_E_FILELOAD                  EMAKEHR(0x1621)
#define COR_E_OBJECTDISPOSED            EMAKEHR(0x1622)
#define COR_E_FAILFAST                  EMAKEHR(0x1623)
#define COR_E_HOSTPROTECTION            EMAKEHR(0x1640)
#define COR_E_ILLEGAL_REENTRANCY        EMAKEHR(0x1641)

#define COR_E_SqlException EMAKEHR(0x1904)
#define COR_E_Data EMAKEHR(0x1920)
#define COR_E_DataDeletedRowInaccessible EMAKEHR(0x1921)
#define COR_E_DataDuplicateName EMAKEHR(0x1922)
#define COR_E_DataInRowChangingEvent EMAKEHR(0x1923)
#define COR_E_DataInvalidConstraint EMAKEHR(0x1924)
#define COR_E_DataMissingPrimaryKey EMAKEHR(0x1925)
#define COR_E_DataNoNullAllowed EMAKEHR(0x1926)
#define COR_E_DataReadOnly EMAKEHR(0x1927)
#define COR_E_DataRowNotInTable EMAKEHR(0x1928)
#define COR_E_DataVersionNotFound EMAKEHR(0x1929)
#define COR_E_DataConstraint EMAKEHR(0x192a)
#define COR_E_StrongTyping EMAKEHR(0x192b)
#define COR_E_SqlType EMAKEHR(0x1930)
#define COR_E_SqlNullValue EMAKEHR(0x1931)
#define COR_E_SqlTruncate EMAKEHR(0x1932)
#define COR_E_AdapterMapping EMAKEHR(0x1933)
#define COR_E_DataAdapter EMAKEHR(0x1934)
#define COR_E_DBConcurrency EMAKEHR(0x1935)
#define COR_E_OperationAborted EMAKEHR(0x1936)
#define COR_E_InvalidUdt EMAKEHR(0x1937)
#define COR_E_OdbcException EMAKEHR(0x1937)
#define COR_E_OracleException EMAKEHR(0x1938)
#define COR_E_Xml EMAKEHR(0x1940)
#define COR_E_XmlSchema EMAKEHR(0x1941)
#define COR_E_XmlXslt EMAKEHR(0x1942)
#define COR_E_XmlXPath EMAKEHR(0x1943)
#define COR_E_XmlQuery EMAKEHR(0x1944)

#define FUSION_E_REF_DEF_MISMATCH EMAKEHR(0x1040)
#define FUSION_E_INVALID_PRIVATE_ASM_LOCATION EMAKEHR(0x1041)
#define FUSION_E_ASM_MODULE_MISSING EMAKEHR(0x1042)
#define FUSION_E_UNEXPECTED_MODULE_FOUND EMAKEHR(0x1043)
#define FUSION_E_PRIVATE_ASM_DISALLOWED EMAKEHR(0x1044)
#define FUSION_E_SIGNATURE_CHECK_FAILED EMAKEHR(0x1045)
#define FUSION_E_DATABASE_ERROR         EMAKEHR(0x1046)
#define FUSION_E_INVALID_NAME           EMAKEHR(0x1047)
#define FUSION_E_CODE_DOWNLOAD_DISABLED EMAKEHR(0x1048)
#define FUSION_E_UNINSTALL_DISALLOWED   EMAKEHR(0x1049)
#define FUSION_E_HOST_GAC_ASM_MISMATCH  EMAKEHR(0x1050)
#define FUSION_E_LOADFROM_BLOCKED       EMAKEHR(0x1051)
#define FUSION_E_CACHEFILE_FAILED       EMAKEHR(0x1052)
#define FUSION_E_APP_DOMAIN_LOCKED      EMAKEHR(0x1053)
#define FUSION_E_CONFIGURATION_ERROR    EMAKEHR(0x1054)
#define FUSION_E_MANIFEST_PARSE_ERROR   EMAKEHR(0x1055)
#define FUSION_E_INVALID_ASSEMBLY_REFERENCE EMAKEHR(0x1056)

#define CLDB_E_FILE_BADREAD             EMAKEHR(0x1100)
#define CLDB_E_FILE_BADWRITE            EMAKEHR(0x1101)
#define CLDB_E_FILE_READONLY            EMAKEHR(0x1103)
#define CLDB_E_NAME_ERROR               EMAKEHR(0x1105)
#define CLDB_E_TRUNCATION               EMAKEHR(0x1106)
#define CLDB_E_FILE_OLDVER              EMAKEHR(0x1107)
#define CLDB_E_RELOCATED                EMAKEHR(0x1108)
#define CLDB_E_SMDUPLICATE              EMAKEHR(0x110a)
#define CLDB_E_NO_DATA                  EMAKEHR(0x110b)
#define CLDB_E_READONLY                 EMAKEHR(0x110c)
#define CLDB_E_INCOMPATIBLE             EMAKEHR(0x110d)
#define CLDB_E_FILE_CORRUPT             EMAKEHR(0x110e)
#define CLDB_E_SCHEMA_VERNOTFOUND       EMAKEHR(0x110f)
#define CLDB_E_BADUPDATEMODE            EMAKEHR(0x1110)
#define CLDB_E_INDEX_NONULLKEYS         EMAKEHR(0x1121)
#define CLDB_E_INDEX_DUPLICATE          EMAKEHR(0x1122)
#define CLDB_E_INDEX_BADTYPE            EMAKEHR(0x1123)
#define CLDB_E_INDEX_NOTFOUND           EMAKEHR(0x1124)
#define CLDB_E_RECORD_NOTFOUND          EMAKEHR(0x1130)
#define CLDB_E_RECORD_OVERFLOW          EMAKEHR(0x1131)
#define CLDB_E_RECORD_DUPLICATE         EMAKEHR(0x1132)
#define CLDB_E_RECORD_PKREQUIRED        EMAKEHR(0x1133)
#define CLDB_E_RECORD_DELETED           EMAKEHR(0x1134)
#define CLDB_E_RECORD_OUTOFORDER        EMAKEHR(0x1135)
#define CLDB_E_COLUMN_OVERFLOW          EMAKEHR(0x1140)
#define CLDB_E_COLUMN_READONLY          EMAKEHR(0x1141)
#define CLDB_E_COLUMN_SPECIALCOL        EMAKEHR(0x1142)
#define CLDB_E_COLUMN_PKNONULLS         EMAKEHR(0x1143)
#define CLDB_E_TABLE_CANTDROP           EMAKEHR(0x1150)
#define CLDB_E_OBJECT_NOTFOUND          EMAKEHR(0x1151)
#define CLDB_E_OBJECT_COLNOTFOUND       EMAKEHR(0x1152)
#define CLDB_E_VECTOR_BADINDEX          EMAKEHR(0x1153)
#define CLDB_E_TOO_BIG                  EMAKEHR(0x1154)
#define CLDB_E_INTERNALERROR            EMAKEHR(0x1fff)

#define CLDB_S_TRUNCATION               SMAKEHR(0x1106)
#define CLDB_S_NULL                     SMAKEHR(0x1109)
#define CLDB_S_INDEX_TABLESCANREQUIRED  SMAKEHR(0x1125)

#define CLR_E_APP_CONFIG_NOT_ALLOWED_IN_APPX_PROCESS EMAKEHR(0x104a)
#define CLR_E_SHIM_RUNTIME                   EMAKEHR(0x1700)
#define CLR_E_SHIM_RUNTIMELOAD               EMAKEHR(0x1700)
#define CLR_E_SHIM_RUNTIMEEXPORT             EMAKEHR(0x1701)
#define CLR_E_SHIM_INSTALLROOT               EMAKEHR(0x1702)
#define CLR_E_SHIM_INSTALLCOMP               EMAKEHR(0x1703)
#define CLR_E_SHIM_LEGACYRUNTIMEALREADYBOUND EMAKEHR(0x1704)
#define CLR_E_SHIM_SHUTDOWNINPROGRESS        EMAKEHR(0x1705)
#define CLR_E_BIND_ASSEMBLY_VERSION_TOO_LOW  EMAKEHR(0x2000)
#define CLR_E_BIND_ASSEMBLY_PUBLIC_KEY_MISMATCH EMAKEHR(0x2001)
#define CLR_E_BIND_IMAGE_UNAVAILABLE         EMAKEHR(0x2002)
#define CLR_E_BIND_UNRECOGNIZED_IDENTITY_FORMAT EMAKEHR(0x2003)
#define CLR_E_BIND_ASSEMBLY_NOT_FOUND        EMAKEHR(0x2004)
#define CLR_E_BIND_TYPE_NOT_FOUND            EMAKEHR(0x2005)

#define CLR_OPTSVC_E_CONTROLLER_INTERRUPT EMAKEHR(0x1e00)

#define META_S_PARAM_MISMATCH SMAKEHR(0x1189)
#define META_S_DUPLICATE      SMAKEHR(0x1197)

#define TLBX_E_INVALID_TYPEINFO EMAKEHR(0x1160)
#define TLBX_E_INVALID_TYPEINFO_UNNAMED EMAKEHR(0x1161)
#define TLBX_E_CTX_NESTED EMAKEHR(0x1162)
#define TLBX_E_ERROR_MESSAGE EMAKEHR(0x1163)
#define TLBX_E_CANT_SAVE EMAKEHR(0x1164)
#define TLBX_E_CANTLOADLIBRARY EMAKEHR(0x1166)
#define TLBX_E_BAD_VT_TYPE EMAKEHR(0x1167)
#define TLBX_E_NO_MSCOREE_TLB EMAKEHR(0x1168)
#define TLBX_E_BAD_MSCOREE_TLB EMAKEHR(0x1169)
#define TLBX_E_TLB_EXCEPTION EMAKEHR(0x116a)
#define TLBX_E_MULTIPLE_LCIDS EMAKEHR(0x116b)
#define TLBX_E_AMBIGUOUS_RETURN EMAKEHR(0x116d)
#define TLBX_E_DUPLICATE_TYPE_NAME EMAKEHR(0x116e)
#define TLBX_E_ASANY EMAKEHR(0x1175)
#define TLBX_E_INVALIDLCIDPARAM EMAKEHR(0x1176)
#define TLBX_E_LCIDONDISPONLYITF EMAKEHR(0x1177)
#define TLBX_E_NONPUBLIC_FIELD EMAKEHR(0x1178)
#define TLBX_E_BAD_NAMES EMAKEHR(0x117b)
#define TLBX_E_GENERICINST_SIGNATURE EMAKEHR(0x117d)
#define TLBX_E_GENERICPAR_SIGNATURE EMAKEHR(0x117e)
#define TLBX_E_NO_SAFEHANDLE_ARRAYS EMAKEHR(0x1186)
#define TLBX_E_NO_CRITICALHANDLE_ARRAYS EMAKEHR(0x118c)
#define TLBX_E_CANT_LOAD_MODULE EMAKEHR(0x11a0)
#define TLBX_E_CANT_LOAD_CLASS EMAKEHR(0x11a1)
#define TLBX_E_NULL_MODULE EMAKEHR(0x11a2)
#define TLBX_E_NO_CLSID_KEY EMAKEHR(0x11a3)
#define TLBX_E_CIRCULAR_EXPORT EMAKEHR(0x11a4)
#define TLBX_E_CIRCULAR_IMPORT EMAKEHR(0x11a5)
#define TLBX_E_BAD_NATIVETYPE EMAKEHR(0x11a6)
#define TLBX_E_BAD_VTABLE EMAKEHR(0x11a7)
#define TLBX_E_CRM_NON_STATIC EMAKEHR(0x11a8)
#define TLBX_E_CRM_INVALID_SIG EMAKEHR(0x11a9)
#define TLBX_E_CLASS_LOAD_EXCEPTION EMAKEHR(0x11aa)
#define TLBX_E_UNKNOWN_SIGNATURE EMAKEHR(0x11ab)
#define TLBX_E_REFERENCED_TYPELIB EMAKEHR(0x11ac)
#define TLBX_E_INVALID_NAMESPACE EMAKEHR(0x11ad)
#define TLBX_E_LAYOUT_ERROR EMAKEHR(0x11ae)
#define TLBX_E_NOTIUNKNOWN EMAKEHR(0x11af)
#define TLBX_E_NONVISIBLEVALUECLASS EMAKEHR(0x11b0)
#define TLBX_E_LPTSTR_NOT_ALLOWED EMAKEHR(0x11b1)
#define TLBX_E_AUTO_CS_NOT_ALLOWED EMAKEHR(0x11b2)
#define TLBX_E_ENUM_VALUE_INVALID EMAKEHR(0x11b5)
#define TLBX_E_DUPLICATE_IID EMAKEHR(0x11b6)
#define TLBX_E_NO_NESTED_ARRAYS EMAKEHR(0x11b7)
#define TLBX_E_PARAM_ERROR_NAMED EMAKEHR(0x11b8)
#define TLBX_E_PARAM_ERROR_UNNAMED EMAKEHR(0x11b9)
#define TLBX_E_AGNOST_SIGNATURE EMAKEHR(0x11ba)
#define TLBX_E_CONVERT_FAIL EMAKEHR(0x11bb)
#define TLBX_E_BAD_SIGNATURE EMAKEHR(0x11bd)
#define TLBX_E_ARRAY_NEEDS_NT_FIXED EMAKEHR(0x11be)
#define TLBX_E_CLASS_NEEDS_NT_INTF EMAKEHR(0x11bf)
#define TLBX_E_TYPED_REF EMAKEHR(0x11da)
#define TLBX_E_BITNESS_MISMATCH EMAKEHR(0x11e1)
#define TLBX_E_EVENT_WITH_NEWENUM EMAKEHR(0x11e2)
#define TLBX_E_PROPGET_WITHOUT_RETURN EMAKEHR(0x11e3)
#define TLBX_E_CIRCULAR_EXPORT2 EMAKEHR(0x1b52)

#define TLBX_I_TYPEINFO_IMPORTED SMAKEHR(0x116c)
#define TLBX_I_PIA_REGISTERED_FOR_TLB SMAKEHR(0x116d)
#define TLBX_I_AGNOSTIC_ASSEMBLY SMAKEHR(0x116e)
#define TLBX_I_USEIUNKNOWN SMAKEHR(0x116f)
#define TLBX_I_UNCONVERTABLE_ARGS SMAKEHR(0x1170)
#define TLBX_I_UNCONVERTABLE_FIELD SMAKEHR(0x1171)
#define TLBX_I_NONSEQUENTIALSTRUCT EMAKEHR(0x1172)
#define TLBX_I_RESOLVEREFFAILED EMAKEHR(0x1174)
#define TLBX_I_TYPE_EXPORTED SMAKEHR(0x1179)
#define TLBX_I_DUPLICATE_DISPID SMAKEHR(0x117a)
#define TLBX_I_REF_TYPE_AS_STRUCT SMAKEHR(0x117c)
#define TLBX_I_GENERIC_TYPE SMAKEHR(0x117f)
#define TLBX_I_GENERIC_BASE_TYPE SMAKEHR(0x11e0)

#define TLBX_S_REFERENCED_TYPELIB SMAKEHR(0x11ac)
#define TLBX_S_NOSTDINTERFACE     SMAKEHR(0x11b3)
#define TLBX_S_DUPLICATE_DISPID   SMAKEHR(0x11b4)

#define TLBX_W_LIBNOTREGISTERED EMAKEHR(0x1165)
#define TLBX_W_WARNING_MESSAGE SMAKEHR(0x1173)
#define TLBX_W_ASSEMBLY_HAS_EMBEDDED_TLB SMAKEHR(0x1174)
#define TLBX_W_CROSS_COMPILE_NO_REFS SMAKEHR(0x1175)
#define TLBX_W_PURE_CROSS_COMPILE SMAKEHR(0x1176)
#define TLBX_W_NON_INTEGRAL_CA_TYPE SMAKEHR(0x1184)
#define TLBX_W_IENUM_CA_ON_IUNK SMAKEHR(0x1185)
#define TLBX_W_DUAL_NOT_DISPATCH EMAKEHR(0x11bc)
#define TLBX_W_NO_PROPS_IN_EVENTS EMAKEHR(0x11d3)
#define TLBX_W_ENUM_VALUE_TOOBIG SMAKEHR(0x11d5)
#define TLBX_W_EXPORTING_AUTO_LAYOUT SMAKEHR(0x11d9)
#define TLBX_W_DEFAULT_INTF_NOT_VISIBLE SMAKEHR(0x11db)
#define TLBX_W_BAD_SAFEARRAYFIELD_NO_ELEMENTVT SMAKEHR(0x11de)
#define TLBX_W_LAYOUTCLASS_AS_INTERFACE SMAKEHR(0x11df)

#define VLDTR_E_RID_OUTOFRANGE EMAKEHR(0x1203)
#define VLDTR_E_CDTKN_OUTOFRANGE EMAKEHR(0x1204)
#define VLDTR_E_CDRID_OUTOFRANGE EMAKEHR(0x1205)
#define VLDTR_E_STRING_INVALID EMAKEHR(0x1206)
#define VLDTR_E_GUID_INVALID EMAKEHR(0x1207)
#define VLDTR_E_BLOB_INVALID EMAKEHR(0x1208)
#define VLDTR_E_MOD_MULTI EMAKEHR(0x1209)
#define VLDTR_E_MOD_NULLMVID EMAKEHR(0x120a)
#define VLDTR_E_TR_NAMENULL EMAKEHR(0x120b)
#define VLDTR_E_TR_DUP EMAKEHR(0x120c)
#define VLDTR_E_TD_NAMENULL EMAKEHR(0x120d)
#define VLDTR_E_TD_DUPNAME EMAKEHR(0x120e)
#define VLDTR_E_TD_DUPGUID EMAKEHR(0x120f)
#define VLDTR_E_TD_NOTIFACEOBJEXTNULL EMAKEHR(0x1210)
#define VLDTR_E_TD_OBJEXTENDSNONNULL EMAKEHR(0x1211)
#define VLDTR_E_TD_EXTENDSSEALED EMAKEHR(0x1212)
#define VLDTR_E_TD_DLTNORTSPCL EMAKEHR(0x1213)
#define VLDTR_E_TD_RTSPCLNOTDLT EMAKEHR(0x1214)
#define VLDTR_E_MI_DECLPRIV EMAKEHR(0x1215)
#define VLDTR_E_AS_BADNAME EMAKEHR(0x1216)
#define VLDTR_E_FILE_SYSNAME EMAKEHR(0x1217)
#define VLDTR_E_MI_BODYSTATIC EMAKEHR(0x1218)
#define VLDTR_E_TD_IFACENOTABS EMAKEHR(0x1219)
#define VLDTR_E_TD_IFACEPARNOTNIL EMAKEHR(0x121a)
#define VLDTR_E_TD_IFACEGUIDNULL EMAKEHR(0x121b)
#define VLDTR_E_MI_DECLFINAL EMAKEHR(0x121c)
#define VLDTR_E_TD_VTNOTSEAL EMAKEHR(0x121d)
#define VLDTR_E_PD_BADFLAGS EMAKEHR(0x121e)
#define VLDTR_E_IFACE_DUP EMAKEHR(0x121f)
#define VLDTR_E_MR_NAMENULL EMAKEHR(0x1220)
#define VLDTR_E_MR_VTBLNAME EMAKEHR(0x1221)
#define VLDTR_E_MR_DELNAME EMAKEHR(0x1222)
#define VLDTR_E_MR_PARNIL EMAKEHR(0x1223)
#define VLDTR_E_MR_BADCALLINGCONV EMAKEHR(0x1224)
#define VLDTR_E_MR_NOTVARARG EMAKEHR(0x1225)
#define VLDTR_E_MR_NAMEDIFF EMAKEHR(0x1226)
#define VLDTR_E_MR_SIGDIFF EMAKEHR(0x1227)
#define VLDTR_E_MR_DUP EMAKEHR(0x1228)
#define VLDTR_E_CL_TDAUTO EMAKEHR(0x1229)
#define VLDTR_E_CL_BADPCKSZ EMAKEHR(0x122a)
#define VLDTR_E_CL_DUP EMAKEHR(0x122b)
#define VLDTR_E_FL_BADOFFSET EMAKEHR(0x122c)
#define VLDTR_E_FL_TDNIL EMAKEHR(0x122d)
#define VLDTR_E_FL_NOCL EMAKEHR(0x122e)
#define VLDTR_E_FL_TDNOTEXPLCT EMAKEHR(0x122f)
#define VLDTR_E_FL_FLDSTATIC EMAKEHR(0x1230)
#define VLDTR_E_FL_DUP EMAKEHR(0x1231)
#define VLDTR_E_MODREF_NAMENULL EMAKEHR(0x1232)
#define VLDTR_E_MODREF_DUP EMAKEHR(0x1233)
#define VLDTR_E_TR_BADSCOPE EMAKEHR(0x1234)
#define VLDTR_E_TD_NESTEDNOENCL EMAKEHR(0x1235)
#define VLDTR_E_TD_EXTTRRES EMAKEHR(0x1236)
#define VLDTR_E_SIGNULL EMAKEHR(0x1237)
#define VLDTR_E_SIGNODATA EMAKEHR(0x1238)
#define VLDTR_E_MD_BADCALLINGCONV EMAKEHR(0x1239)
#define VLDTR_E_MD_THISSTATIC EMAKEHR(0x123a)
#define VLDTR_E_MD_NOTTHISNOTSTATIC EMAKEHR(0x123b)
#define VLDTR_E_MD_NOARGCNT EMAKEHR(0x123c)
#define VLDTR_E_SIG_MISSELTYPE EMAKEHR(0x123d)
#define VLDTR_E_SIG_MISSTKN EMAKEHR(0x123e)
#define VLDTR_E_SIG_TKNBAD EMAKEHR(0x123f)
#define VLDTR_E_SIG_MISSFPTR EMAKEHR(0x1240)
#define VLDTR_E_SIG_MISSFPTRARGCNT EMAKEHR(0x1241)
#define VLDTR_E_SIG_MISSRANK EMAKEHR(0x1242)
#define VLDTR_E_SIG_MISSNSIZE EMAKEHR(0x1243)
#define VLDTR_E_SIG_MISSSIZE EMAKEHR(0x1244)
#define VLDTR_E_SIG_MISSNLBND EMAKEHR(0x1245)
#define VLDTR_E_SIG_MISSLBND EMAKEHR(0x1246)
#define VLDTR_E_SIG_BADELTYPE EMAKEHR(0x1247)
#define VLDTR_E_SIG_MISSVASIZE EMAKEHR(0x1248)
#define VLDTR_E_FD_BADCALLINGCONV EMAKEHR(0x1249)
#define VLDTR_E_MD_NAMENULL EMAKEHR(0x124a)
#define VLDTR_E_MD_PARNIL EMAKEHR(0x124b)
#define VLDTR_E_MD_DUP EMAKEHR(0x124c)
#define VLDTR_E_FD_NAMENULL EMAKEHR(0x124d)
#define VLDTR_E_FD_PARNIL EMAKEHR(0x124e)
#define VLDTR_E_FD_DUP EMAKEHR(0x124f)
#define VLDTR_E_AS_MULTI EMAKEHR(0x1250)
#define VLDTR_E_AS_NAMENULL EMAKEHR(0x1251)
#define VLDTR_E_SIG_TOKTYPEMISMATCH EMAKEHR(0x1252)
#define VLDTR_E_CL_TDINTF EMAKEHR(0x1253)
#define VLDTR_E_ASOS_OSPLTFRMIDINVAL EMAKEHR(0x1254)
#define VLDTR_E_AR_NAMENULL EMAKEHR(0x1255)
#define VLDTR_E_TD_ENCLNOTNESTED EMAKEHR(0x1256)
#define VLDTR_E_AROS_OSPLTFRMIDINVAL EMAKEHR(0x1257)
#define VLDTR_E_FILE_NAMENULL EMAKEHR(0x1258)
#define VLDTR_E_CT_NAMENULL EMAKEHR(0x1259)
#define VLDTR_E_TD_EXTENDSCHILD EMAKEHR(0x125a)
#define VLDTR_E_MAR_NAMENULL EMAKEHR(0x125b)
#define VLDTR_E_FILE_DUP EMAKEHR(0x125c)
#define VLDTR_E_FILE_NAMEFULLQLFD EMAKEHR(0x125d)
#define VLDTR_E_CT_DUP EMAKEHR(0x125e)
#define VLDTR_E_MAR_DUP EMAKEHR(0x125f)
#define VLDTR_E_MAR_NOTPUBPRIV EMAKEHR(0x1260)
#define VLDTR_E_TD_ENUMNOVALUE EMAKEHR(0x1261)
#define VLDTR_E_TD_ENUMVALSTATIC EMAKEHR(0x1262)
#define VLDTR_E_TD_ENUMVALNOTSN EMAKEHR(0x1263)
#define VLDTR_E_TD_ENUMFLDNOTST EMAKEHR(0x1264)
#define VLDTR_E_TD_ENUMFLDNOTLIT EMAKEHR(0x1265)
#define VLDTR_E_TD_ENUMNOLITFLDS EMAKEHR(0x1266)
#define VLDTR_E_TD_ENUMFLDSIGMISMATCH EMAKEHR(0x1267)
#define VLDTR_E_TD_ENUMVALNOT1ST EMAKEHR(0x1268)
#define VLDTR_E_FD_NOTVALUERTSN EMAKEHR(0x1269)
#define VLDTR_E_FD_VALUEPARNOTENUM EMAKEHR(0x126a)
#define VLDTR_E_FD_INSTINIFACE EMAKEHR(0x126b)
#define VLDTR_E_FD_NOTPUBINIFACE EMAKEHR(0x126c)
#define VLDTR_E_FMD_GLOBALNOTPUBPRIVSC EMAKEHR(0x126d)
#define VLDTR_E_FMD_GLOBALNOTSTATIC EMAKEHR(0x126e)
#define VLDTR_E_FD_GLOBALNORVA EMAKEHR(0x126f)
#define VLDTR_E_MD_CTORZERORVA EMAKEHR(0x1270)
#define VLDTR_E_FD_MARKEDNOMARSHAL EMAKEHR(0x1271)
#define VLDTR_E_FD_MARSHALNOTMARKED EMAKEHR(0x1272)
#define VLDTR_E_FD_MARKEDNODEFLT EMAKEHR(0x1273)
#define VLDTR_E_FD_DEFLTNOTMARKED EMAKEHR(0x1274)
#define VLDTR_E_FMD_MARKEDNOSECUR EMAKEHR(0x1275)
#define VLDTR_E_FMD_SECURNOTMARKED EMAKEHR(0x1276)
#define VLDTR_E_FMD_PINVOKENOTSTATIC EMAKEHR(0x1277)
#define VLDTR_E_FMD_MARKEDNOPINVOKE EMAKEHR(0x1278)
#define VLDTR_E_FMD_PINVOKENOTMARKED EMAKEHR(0x1279)
#define VLDTR_E_FMD_BADIMPLMAP EMAKEHR(0x127a)
#define VLDTR_E_IMAP_BADMODREF EMAKEHR(0x127b)
#define VLDTR_E_IMAP_BADMEMBER EMAKEHR(0x127c)
#define VLDTR_E_IMAP_BADIMPORTNAME EMAKEHR(0x127d)
#define VLDTR_E_IMAP_BADCALLCONV EMAKEHR(0x127e)
#define VLDTR_E_FMD_BADACCESSFLAG EMAKEHR(0x127f)
#define VLDTR_E_FD_INITONLYANDLITERAL EMAKEHR(0x1280)
#define VLDTR_E_FD_LITERALNOTSTATIC EMAKEHR(0x1281)
#define VLDTR_E_FMD_RTSNNOTSN EMAKEHR(0x1282)
#define VLDTR_E_MD_ABSTPARNOTABST EMAKEHR(0x1283)
#define VLDTR_E_MD_NOTSTATABSTININTF EMAKEHR(0x1284)
#define VLDTR_E_MD_NOTPUBININTF EMAKEHR(0x1285)
#define VLDTR_E_MD_CTORININTF EMAKEHR(0x1286)
#define VLDTR_E_MD_GLOBALCTORCCTOR EMAKEHR(0x1287)
#define VLDTR_E_MD_CTORSTATIC EMAKEHR(0x1288)
#define VLDTR_E_MD_CTORNOTSNRTSN EMAKEHR(0x1289)
#define VLDTR_E_MD_CTORVIRT EMAKEHR(0x128a)
#define VLDTR_E_MD_CTORABST EMAKEHR(0x128b)
#define VLDTR_E_MD_CCTORNOTSTATIC EMAKEHR(0x128c)
#define VLDTR_E_MD_ZERORVA EMAKEHR(0x128d)
#define VLDTR_E_MD_FINNOTVIRT EMAKEHR(0x128e)
#define VLDTR_E_MD_STATANDFINORVIRT EMAKEHR(0x128f)
#define VLDTR_E_MD_ABSTANDFINAL EMAKEHR(0x1290)
#define VLDTR_E_MD_ABSTANDIMPL EMAKEHR(0x1291)
#define VLDTR_E_MD_ABSTANDPINVOKE EMAKEHR(0x1292)
#define VLDTR_E_MD_ABSTNOTVIRT EMAKEHR(0x1293)
#define VLDTR_E_MD_NOTABSTNOTIMPL EMAKEHR(0x1294)
#define VLDTR_E_MD_NOTABSTBADFLAGSRVA EMAKEHR(0x1295)
#define VLDTR_E_MD_PRIVSCOPENORVA EMAKEHR(0x1296)
#define VLDTR_E_MD_GLOBALABSTORVIRT EMAKEHR(0x1297)
#define VLDTR_E_SIG_LONGFORM EMAKEHR(0x1298)
#define VLDTR_E_MD_MULTIPLESEMANTICS EMAKEHR(0x1299)
#define VLDTR_E_MD_INVALIDSEMANTICS EMAKEHR(0x129a)
#define VLDTR_E_MD_SEMANTICSNOTEXIST EMAKEHR(0x129b)
#define VLDTR_E_MI_DECLNOTVIRT EMAKEHR(0x129c)
#define VLDTR_E_FMD_GLOBALITEM EMAKEHR(0x129d)
#define VLDTR_E_MD_MULTSEMANTICFLAGS EMAKEHR(0x129e)
#define VLDTR_E_MD_NOSEMANTICFLAGS EMAKEHR(0x129f)
#define VLDTR_E_FD_FLDINIFACE EMAKEHR(0x12a0)
#define VLDTR_E_AS_HASHALGID EMAKEHR(0x12a1)
#define VLDTR_E_AS_PROCID EMAKEHR(0x12a2)
#define VLDTR_E_AR_PROCID EMAKEHR(0x12a3)
#define VLDTR_E_CN_PARENTRANGE EMAKEHR(0x12a4)
#define VLDTR_E_AS_BADFLAGS EMAKEHR(0x12a5)
#define VLDTR_E_TR_HASTYPEDEF EMAKEHR(0x12a6)
#define VLDTR_E_IFACE_BADIMPL EMAKEHR(0x12a7)
#define VLDTR_E_IFACE_BADIFACE EMAKEHR(0x12a8)
#define VLDTR_E_TD_SECURNOTMARKED EMAKEHR(0x12a9)
#define VLDTR_E_TD_MARKEDNOSECUR EMAKEHR(0x12aa)
#define VLDTR_E_MD_CCTORHASARGS EMAKEHR(0x12ab)
#define VLDTR_E_CT_BADIMPL EMAKEHR(0x12ac)
#define VLDTR_E_MI_ALIENBODY EMAKEHR(0x12ad)
#define VLDTR_E_MD_CCTORCALLCONV EMAKEHR(0x12ae)
#define VLDTR_E_MI_BADCLASS EMAKEHR(0x12af)
#define VLDTR_E_MI_CLASSISINTF EMAKEHR(0x12b0)
#define VLDTR_E_MI_BADDECL EMAKEHR(0x12b1)
#define VLDTR_E_MI_BADBODY EMAKEHR(0x12b2)
#define VLDTR_E_MI_DUP EMAKEHR(0x12b3)
#define VLDTR_E_FD_BADPARENT EMAKEHR(0x12b4)
#define VLDTR_E_MD_PARAMOUTOFSEQ EMAKEHR(0x12b5)
#define VLDTR_E_MD_PARASEQTOOBIG EMAKEHR(0x12b6)
#define VLDTR_E_MD_PARMMARKEDNOMARSHAL EMAKEHR(0x12b7)
#define VLDTR_E_MD_PARMMARSHALNOTMARKED EMAKEHR(0x12b8)
#define VLDTR_E_MD_PARMMARKEDNODEFLT EMAKEHR(0x12ba)
#define VLDTR_E_MD_PARMDEFLTNOTMARKED EMAKEHR(0x12bb)
#define VLDTR_E_PR_BADSCOPE EMAKEHR(0x12bc)
#define VLDTR_E_PR_NONAME EMAKEHR(0x12bd)
#define VLDTR_E_PR_NOSIG EMAKEHR(0x12be)
#define VLDTR_E_PR_DUP EMAKEHR(0x12bf)
#define VLDTR_E_PR_BADCALLINGCONV EMAKEHR(0x12c0)
#define VLDTR_E_PR_MARKEDNODEFLT EMAKEHR(0x12c1)
#define VLDTR_E_PR_DEFLTNOTMARKED EMAKEHR(0x12c2)
#define VLDTR_E_PR_BADSEMANTICS EMAKEHR(0x12c3)
#define VLDTR_E_PR_BADMETHOD EMAKEHR(0x12c4)
#define VLDTR_E_PR_ALIENMETHOD EMAKEHR(0x12c5)
#define VLDTR_E_CN_BLOBNOTNULL EMAKEHR(0x12c6)
#define VLDTR_E_CN_BLOBNULL EMAKEHR(0x12c7)
#define VLDTR_E_EV_BADSCOPE EMAKEHR(0x12c8)
#define VLDTR_E_EV_NONAME EMAKEHR(0x12ca)
#define VLDTR_E_EV_DUP EMAKEHR(0x12cb)
#define VLDTR_E_EV_BADEVTYPE EMAKEHR(0x12cc)
#define VLDTR_E_EV_EVTYPENOTCLASS EMAKEHR(0x12cd)
#define VLDTR_E_EV_BADSEMANTICS EMAKEHR(0x12ce)
#define VLDTR_E_EV_BADMETHOD EMAKEHR(0x12cf)
#define VLDTR_E_EV_ALIENMETHOD EMAKEHR(0x12d0)
#define VLDTR_E_EV_NOADDON EMAKEHR(0x12d1)
#define VLDTR_E_EV_NOREMOVEON EMAKEHR(0x12d2)
#define VLDTR_E_CT_DUPTDNAME EMAKEHR(0x12d3)
#define VLDTR_E_MAR_BADOFFSET EMAKEHR(0x12d4)
#define VLDTR_E_DS_BADOWNER EMAKEHR(0x12d5)
#define VLDTR_E_DS_BADFLAGS EMAKEHR(0x12d6)
#define VLDTR_E_DS_NOBLOB EMAKEHR(0x12d7)
#define VLDTR_E_MAR_BADIMPL EMAKEHR(0x12d8)
#define VLDTR_E_MR_VARARGCALLINGCONV EMAKEHR(0x12da)
#define VLDTR_E_MD_CTORNOTVOID EMAKEHR(0x12db)
#define VLDTR_E_EV_FIRENOTVOID EMAKEHR(0x12dc)
#define VLDTR_E_AS_BADLOCALE EMAKEHR(0x12dd)
#define VLDTR_E_CN_PARENTTYPE EMAKEHR(0x12de)
#define VLDTR_E_SIG_SENTINMETHODDEF EMAKEHR(0x12df)
#define VLDTR_E_SIG_SENTMUSTVARARG EMAKEHR(0x12e0)
#define VLDTR_E_SIG_MULTSENTINELS EMAKEHR(0x12e1)
#define VLDTR_E_SIG_LASTSENTINEL EMAKEHR(0x12e2)
#define VLDTR_E_SIG_MISSARG EMAKEHR(0x12e3)
#define VLDTR_E_SIG_BYREFINFIELD EMAKEHR(0x12e4)
#define VLDTR_E_MD_SYNCMETHODINVTYPE EMAKEHR(0x12e5)
#define VLDTR_E_TD_NAMETOOLONG EMAKEHR(0x12e6)
#define VLDTR_E_AS_PROCDUP EMAKEHR(0x12e7)
#define VLDTR_E_ASOS_DUP EMAKEHR(0x12e8)
#define VLDTR_E_MAR_BADFLAGS EMAKEHR(0x12e9)
#define VLDTR_E_CT_NOTYPEDEFID EMAKEHR(0x12ea)
#define VLDTR_E_FILE_BADFLAGS EMAKEHR(0x12eb)
#define VLDTR_E_FILE_NULLHASH EMAKEHR(0x12ec)
#define VLDTR_E_MOD_NONAME EMAKEHR(0x12ed)
#define VLDTR_E_MOD_NAMEFULLQLFD EMAKEHR(0x12ee)
#define VLDTR_E_TD_RTSPCLNOTSPCL EMAKEHR(0x12ef)
#define VLDTR_E_TD_EXTENDSIFACE EMAKEHR(0x12f0)
#define VLDTR_E_MD_CTORPINVOKE EMAKEHR(0x12f1)
#define VLDTR_E_TD_SYSENUMNOTCLASS EMAKEHR(0x12f2)
#define VLDTR_E_TD_SYSENUMNOTEXTVTYPE EMAKEHR(0x12f3)
#define VLDTR_E_MI_SIGMISMATCH EMAKEHR(0x12f4)
#define VLDTR_E_TD_ENUMHASMETHODS EMAKEHR(0x12f5)
#define VLDTR_E_TD_ENUMIMPLIFACE EMAKEHR(0x12f6)
#define VLDTR_E_TD_ENUMHASPROP EMAKEHR(0x12f7)
#define VLDTR_E_TD_ENUMHASEVENT EMAKEHR(0x12f8)
#define VLDTR_E_TD_BADMETHODLST EMAKEHR(0x12f9)
#define VLDTR_E_TD_BADFIELDLST EMAKEHR(0x12fa)
#define VLDTR_E_CN_BADTYPE EMAKEHR(0x12fb)
#define VLDTR_E_TD_ENUMNOINSTFLD EMAKEHR(0x12fc)
#define VLDTR_E_TD_ENUMMULINSTFLD EMAKEHR(0x12fd)
#define VLDTR_E_INTERRUPTED EMAKEHR(0x12fe)
#define VLDTR_E_NOTINIT EMAKEHR(0x12ff)
#define VLDTR_E_IFACE_NOTIFACE EMAKEHR(0x1b00)
#define VLDTR_E_FD_RVAHASNORVA EMAKEHR(0x1b01)
#define VLDTR_E_FD_RVAHASZERORVA EMAKEHR(0x1b02)
#define VLDTR_E_MD_RVAANDIMPLMAP EMAKEHR(0x1b03)
#define VLDTR_E_TD_EXTRAFLAGS EMAKEHR(0x1b04)
#define VLDTR_E_TD_EXTENDSITSELF EMAKEHR(0x1b05)
#define VLDTR_E_TD_SYSVTNOTEXTOBJ EMAKEHR(0x1b06)
#define VLDTR_E_TD_EXTTYPESPEC EMAKEHR(0x1b07)
#define VLDTR_E_TD_VTNOSIZE EMAKEHR(0x1b09)
#define VLDTR_E_TD_IFACESEALED EMAKEHR(0x1b0a)
#define VLDTR_E_NC_BADNESTED EMAKEHR(0x1b0b)
#define VLDTR_E_NC_BADENCLOSER EMAKEHR(0x1b0c)
#define VLDTR_E_NC_DUP EMAKEHR(0x1b0d)
#define VLDTR_E_NC_DUPENCLOSER EMAKEHR(0x1b0e)
#define VLDTR_E_FRVA_ZERORVA EMAKEHR(0x1b0f)
#define VLDTR_E_FRVA_BADFIELD EMAKEHR(0x1b10)
#define VLDTR_E_FRVA_DUPRVA EMAKEHR(0x1b11)
#define VLDTR_E_FRVA_DUPFIELD EMAKEHR(0x1b12)
#define VLDTR_E_EP_BADTOKEN EMAKEHR(0x1b13)
#define VLDTR_E_EP_INSTANCE EMAKEHR(0x1b14)
#define VLDTR_E_TD_ENUMFLDBADTYPE EMAKEHR(0x1b15)
#define VLDTR_E_MD_BADRVA EMAKEHR(0x1b16)
#define VLDTR_E_FD_LITERALNODEFAULT EMAKEHR(0x1b17)
#define VLDTR_E_IFACE_METHNOTIMPL EMAKEHR(0x1b18)
#define VLDTR_E_CA_BADPARENT EMAKEHR(0x1b19)
#define VLDTR_E_CA_BADTYPE EMAKEHR(0x1b1a)
#define VLDTR_E_CA_NOTCTOR EMAKEHR(0x1b1b)
#define VLDTR_E_CA_BADSIG EMAKEHR(0x1b1c)
#define VLDTR_E_CA_NOSIG EMAKEHR(0x1b1d)
#define VLDTR_E_CA_BADPROLOG EMAKEHR(0x1b1e)
#define VLDTR_E_MD_BADLOCALSIGTOK EMAKEHR(0x1b1f)
#define VLDTR_E_MD_BADHEADER EMAKEHR(0x1b20)
#define VLDTR_E_EP_TOOMANYARGS EMAKEHR(0x1b21)
#define VLDTR_E_EP_BADRET EMAKEHR(0x1b22)
#define VLDTR_E_EP_BADARG EMAKEHR(0x1b23)
#define VLDTR_E_SIG_BADVOID EMAKEHR(0x1b24)
#define VLDTR_E_IFACE_METHMULTIMPL EMAKEHR(0x1b25)
#define VLDTR_E_GP_NAMENULL EMAKEHR(0x1b26)
#define VLDTR_E_GP_OWNERNIL EMAKEHR(0x1b27)
#define VLDTR_E_GP_DUPNAME EMAKEHR(0x1b28)
#define VLDTR_E_GP_DUPNUMBER EMAKEHR(0x1b29)
#define VLDTR_E_GP_NONSEQ_BY_OWNER EMAKEHR(0x1b2a)
#define VLDTR_E_GP_NONSEQ_BY_NUMBER EMAKEHR(0x1b2b)
#define VLDTR_E_GP_UNEXPECTED_OWNER_FOR_VARIANT_VAR EMAKEHR(0x1b2c)
#define VLDTR_E_GP_ILLEGAL_VARIANT_MVAR EMAKEHR(0x1b2d)
#define VLDTR_E_GP_ILLEGAL_VARIANCE_FLAGS EMAKEHR(0x1b2e)
#define VLDTR_E_GP_REFANDVALUETYPE EMAKEHR(0x1b2f)
#define VLDTR_E_GPC_OWNERNIL EMAKEHR(0x1b30)
#define VLDTR_E_GPC_DUP EMAKEHR(0x1b31)
#define VLDTR_E_GPC_NONCONTIGUOUS EMAKEHR(0x1b32)
#define VLDTR_E_MS_METHODNIL EMAKEHR(0x1b33)
#define VLDTR_E_MS_DUP EMAKEHR(0x1b34)
#define VLDTR_E_MS_BADCALLINGCONV EMAKEHR(0x1b35)
#define VLDTR_E_MS_MISSARITY EMAKEHR(0x1b36)
#define VLDTR_E_MS_MISSARG EMAKEHR(0x1b37)
#define VLDTR_E_MS_ARITYMISMATCH EMAKEHR(0x1b38)
#define VLDTR_E_MS_METHODNOTGENERIC EMAKEHR(0x1b39)
#define VLDTR_E_SIG_MISSARITY EMAKEHR(0x1b3a)
#define VLDTR_E_SIG_ARITYMISMATCH EMAKEHR(0x1b3b)
#define VLDTR_E_MD_GENERIC_CCTOR EMAKEHR(0x1b3c)
#define VLDTR_E_MD_GENERIC_CTOR EMAKEHR(0x1b3d)
#define VLDTR_E_MD_GENERIC_IMPORT EMAKEHR(0x1b3e)
#define VLDTR_E_MD_GENERIC_BADCALLCONV EMAKEHR(0x1b3f)
#define VLDTR_E_EP_GENERIC_METHOD EMAKEHR(0x1b40)
#define VLDTR_E_MD_MISSARITY EMAKEHR(0x1b41)
#define VLDTR_E_MD_ARITYZERO EMAKEHR(0x1b42)
#define VLDTR_E_SIG_ARITYZERO EMAKEHR(0x1b43)
#define VLDTR_E_MS_ARITYZERO EMAKEHR(0x1b44)
#define VLDTR_E_MD_GPMISMATCH EMAKEHR(0x1b45)
#define VLDTR_E_EP_GENERIC_TYPE EMAKEHR(0x1b46)
#define VLDTR_E_MI_DECLNOTGENERIC EMAKEHR(0x1b47)
#define VLDTR_E_MI_IMPLNOTGENERIC EMAKEHR(0x1b48)
#define VLDTR_E_MI_ARITYMISMATCH EMAKEHR(0x1b49)
#define VLDTR_E_TD_EXTBADTYPESPEC EMAKEHR(0x1b4a)
#define VLDTR_E_SIG_BYREFINST EMAKEHR(0x1b4b)
#define VLDTR_E_MS_BYREFINST EMAKEHR(0x1b4c)
#define VLDTR_E_TS_EMPTY EMAKEHR(0x1b4d)
#define VLDTR_E_TS_HASSENTINALS EMAKEHR(0x1b4e)
#define VLDTR_E_TD_GENERICHASEXPLAYOUT EMAKEHR(0x1b4f)
#define VLDTR_E_SIG_BADTOKTYPE EMAKEHR(0x1b50)
#define VLDTR_E_IFACE_METHNOTIMPLTHISMOD EMAKEHR(0x1b51)

#define VLDTR_S_WRN SMAKEHR(0x1200)
#define VLDTR_S_ERR SMAKEHR(0x1201)
#define VLDTR_S_WRNERR SMAKEHR(0x1202)

#define CORDBG_E_UNRECOVERABLE_ERROR EMAKEHR(0x1300)
#define CORDBG_E_PROCESS_TERMINATED EMAKEHR(0x1301)
#define CORDBG_E_PROCESS_NOT_SYNCHRONIZED EMAKEHR(0x1302)
#define CORDBG_E_CLASS_NOT_LOADED EMAKEHR(0x1303)
#define CORDBG_E_IL_VAR_NOT_AVAILABLE EMAKEHR(0x1304)
#define CORDBG_E_BAD_REFERENCE_VALUE EMAKEHR(0x1305)
#define CORDBG_E_FIELD_NOT_AVAILABLE EMAKEHR(0x1306)
#define CORDBG_E_NON_NATIVE_FRAME EMAKEHR(0x1307)
#define CORDBG_E_NONCONTINUABLE_EXCEPTION EMAKEHR(0x1308)
#define CORDBG_E_CODE_NOT_AVAILABLE EMAKEHR(0x1309)
#define CORDBG_E_FUNCTION_NOT_IL EMAKEHR(0x130a)
#define CORDBG_E_CANT_SET_IP_INTO_FINALLY EMAKEHR(0x130e)
#define CORDBG_E_CANT_SET_IP_OUT_OF_FINALLY EMAKEHR(0x130f)
#define CORDBG_E_CANT_SET_IP_INTO_CATCH EMAKEHR(0x1310)
#define CORDBG_E_SET_IP_NOT_ALLOWED_ON_NONLEAF_FRAME EMAKEHR(0x1311)
#define CORDBG_E_SET_IP_IMPOSSIBLE EMAKEHR(0x1312)
#define CORDBG_E_FUNC_EVAL_BAD_START_POINT EMAKEHR(0x1313)
#define CORDBG_E_INVALID_OBJECT EMAKEHR(0x1314)
#define CORDBG_E_FUNC_EVAL_NOT_COMPLETE EMAKEHR(0x1315)
#define CORDBG_E_INPROC_NOT_IMPL EMAKEHR(0x1318)
#define CORDBG_E_STATIC_VAR_NOT_AVAILABLE EMAKEHR(0x131a)
#define CORDBG_E_OBJECT_IS_NOT_COPYABLE_VALUE_CLASS EMAKEHR(0x131b)
#define CORDBG_E_CANT_SETIP_INTO_OR_OUT_OF_FILTER EMAKEHR(0x131c)
#define CORDBG_E_CANT_CHANGE_JIT_SETTING_FOR_ZAP_MODULE EMAKEHR(0x131d)
#define CORDBG_E_CANT_SET_IP_OUT_OF_FINALLY_ON_WIN64 EMAKEHR(0x131e)
#define CORDBG_E_CANT_SET_IP_OUT_OF_CATCH_ON_WIN64 EMAKEHR(0x131f)
#define CORDBG_E_REMOTE_CONNECTION_CONN_RESET EMAKEHR(0x1320)
#define CORDBG_E_REMOTE_CONNECTION_KEEP_ALIVE EMAKEHR(0x1321)
#define CORDBG_E_REMOTE_CONNECTION_FATAL_ERROR EMAKEHR(0x1322)
#define CORDBG_E_CANT_SET_TO_JMC EMAKEHR(0x1323)
#define CORDBG_E_NO_CONTEXT_FOR_INTERNAL_FRAME EMAKEHR(0x1325)
#define CORDBG_E_NOT_CHILD_FRAME EMAKEHR(0x1326)
#define CORDBG_E_NON_MATCHING_CONTEXT EMAKEHR(0x1327)
#define CORDBG_E_PAST_END_OF_STACK EMAKEHR(0x1328)
#define CORDBG_E_FUNC_EVAL_CANNOT_UPDATE_REGISTER_IN_NONLEAF_FRAME EMAKEHR(0x1329)
#define CORDBG_E_BAD_THREAD_STATE EMAKEHR(0x132d)
#define CORDBG_E_DEBUGGER_ALREADY_ATTACHED EMAKEHR(0x132e)
#define CORDBG_E_SUPERFLOUS_CONTINUE EMAKEHR(0x132f)
#define CORDBG_E_SET_VALUE_NOT_ALLOWED_ON_NONLEAF_FRAME EMAKEHR(0x1330)
#define CORDBG_E_ENC_EH_MAX_NESTING_LEVEL_CANT_INCREASE EMAKEHR(0x1331)
#define CORDBG_E_ENC_MODULE_NOT_ENC_ENABLED EMAKEHR(0x1332)
#define CORDBG_E_SET_IP_NOT_ALLOWED_ON_EXCEPTION EMAKEHR(0x1333)
#define CORDBG_E_VARIABLE_IS_ACTUALLY_LITERAL EMAKEHR(0x1334)
#define CORDBG_E_PROCESS_DETACHED EMAKEHR(0x1335)
#define CORDBG_E_ENC_METHOD_SIG_CHANGED EMAKEHR(0x1336)
#define CORDBG_E_ENC_METHOD_NO_LOCAL_SIG EMAKEHR(0x1337)
#define CORDBG_E_ENC_CANT_ADD_FIELD_TO_VALUE_OR_LAYOUT_CLASS EMAKEHR(0x1338)
#define CORDBG_E_ENC_CANT_CHANGE_FIELD EMAKEHR(0x1339)
#define CORDBG_E_ENC_CANT_ADD_NON_PRIVATE_MEMBER EMAKEHR(0x133a)
#define CORDBG_E_FIELD_NOT_STATIC EMAKEHR(0x133b)
#define CORDBG_E_FIELD_NOT_INSTANCE EMAKEHR(0x133c)
#define CORDBG_E_ENC_ZAPPED_WITHOUT_ENC EMAKEHR(0x133d)
#define CORDBG_E_ENC_BAD_METHOD_INFO EMAKEHR(0x133e)
#define CORDBG_E_ENC_JIT_CANT_UPDATE EMAKEHR(0x133f)
#define CORDBG_E_ENC_MISSING_CLASS EMAKEHR(0x1340)
#define CORDBG_E_ENC_INTERNAL_ERROR EMAKEHR(0x1341)
#define CORDBG_E_ENC_HANGING_FIELD EMAKEHR(0x1342)
#define CORDBG_E_MODULE_NOT_LOADED EMAKEHR(0x1343)
#define CORDBG_E_ENC_CANT_CHANGE_SUPERCLASS EMAKEHR(0x1344)
#define CORDBG_E_UNABLE_TO_SET_BREAKPOINT EMAKEHR(0x1345)
#define CORDBG_E_DEBUGGING_NOT_POSSIBLE EMAKEHR(0x1346)
#define CORDBG_E_KERNEL_DEBUGGER_ENABLED EMAKEHR(0x1347)
#define CORDBG_E_KERNEL_DEBUGGER_PRESENT EMAKEHR(0x1348)
#define CORDBG_E_HELPER_THREAD_DEAD EMAKEHR(0x1349)
#define CORDBG_E_INTERFACE_INHERITANCE_CANT_CHANGE EMAKEHR(0x134a)
#define CORDBG_E_INCOMPATIBLE_PROTOCOL EMAKEHR(0x134b)
#define CORDBG_E_TOO_MANY_PROCESSES EMAKEHR(0x134c)
#define CORDBG_E_INTEROP_NOT_SUPPORTED EMAKEHR(0x134d)
#define CORDBG_E_NO_REMAP_BREAKPIONT EMAKEHR(0x134e)
#define CORDBG_E_OBJECT_NEUTERED EMAKEHR(0x134f)
#define CORDBG_E_THREAD_NOT_SCHEDULED EMAKEHR(0x1c00)
#define CORDBG_E_HANDLE_HAS_BEEN_DISPOSED EMAKEHR(0x1c01)
#define CORDBG_E_NONINTERCEPTABLE_EXCEPTION EMAKEHR(0x1c02)
#define CORDBG_E_CANT_UNWIND_ABOVE_CALLBACK EMAKEHR(0x1c03)
#define CORDBG_E_INTERCEPT_FRAME_ALREADY_SET EMAKEHR(0x1c04)
#define CORDBG_E_NO_NATIVE_PATCH_AT_ADDR EMAKEHR(0x1c05)
#define CORDBG_E_MUST_BE_INTEROP_DEBUGGING EMAKEHR(0x1c06)
#define CORDBG_E_NATIVE_PATCH_ALREADY_AT_ADDR EMAKEHR(0x1c07)
#define CORDBG_E_TIMEOUT EMAKEHR(0x1c08)
#define CORDBG_E_CANT_CALL_ON_THIS_THREAD EMAKEHR(0x1c09)
#define CORDBG_E_ENC_INFOLESS_METHOD EMAKEHR(0x1c0a)
#define CORDBG_E_ENC_NESTED_HANLDERS EMAKEHR(0x1c0b)
#define CORDBG_E_ENC_IN_FUNCLET EMAKEHR(0x1c0c)
#define CORDBG_E_ENC_LOCALLOC EMAKEHR(0x1c0d)
#define CORDBG_E_ENC_EDIT_NOT_SUPPORTED EMAKEHR(0x1c0e)
#define CORDBG_E_FEABORT_DELAYED_UNTIL_THREAD_RESUMED EMAKEHR(0x1c0f)
#define CORDBG_E_NOTREADY EMAKEHR(0x1c10)
#define CORDBG_E_CANNOT_RESOLVE_ASSEMBLY EMAKEHR(0x1c11)
#define CORDBG_E_MUST_BE_IN_LOAD_MODULE EMAKEHR(0x1c12)
#define CORDBG_E_CANNOT_BE_ON_ATTACH EMAKEHR(0x1c13)
#define CORDBG_E_NGEN_NOT_SUPPORTED EMAKEHR(0x1c14)
#define CORDBG_E_ILLEGAL_SHUTDOWN_ORDER EMAKEHR(0x1c15)
#define CORDBG_E_CANNOT_DEBUG_FIBER_PROCESS EMAKEHR(0x1c16)
#define CORDBG_E_MUST_BE_IN_CREATE_PROCESS EMAKEHR(0x1c17)
#define CORDBG_E_DETACH_FAILED_OUTSTANDING_EVALS EMAKEHR(0x1c18)
#define CORDBG_E_DETACH_FAILED_OUTSTANDING_STEPPERS EMAKEHR(0x1c19)
#define CORDBG_E_CANT_INTEROP_STEP_OUT EMAKEHR(0x1c20)
#define CORDBG_E_DETACH_FAILED_OUTSTANDING_BREAKPOINTS EMAKEHR(0x1c21)
#define CORDBG_E_ILLEGAL_IN_STACK_OVERFLOW EMAKEHR(0x1c22)
#define CORDBG_E_ILLEGAL_AT_GC_UNSAFE_POINT EMAKEHR(0x1c23)
#define CORDBG_E_ILLEGAL_IN_PROLOG EMAKEHR(0x1c24)
#define CORDBG_E_ILLEGAL_IN_NATIVE_CODE EMAKEHR(0x1c25)
#define CORDBG_E_ILLEGAL_IN_OPTIMIZED_CODE EMAKEHR(0x1c26)
#define CORDBG_E_MINIDUMP_UNSUPPORTED EMAKEHR(0x1c27)
#define CORDBG_E_APPDOMAIN_MISMATCH EMAKEHR(0x1c28)
#define CORDBG_E_CONTEXT_UNVAILABLE EMAKEHR(0x1c29)
#define CORDBG_E_UNCOMPATIBLE_PLATFORMS EMAKEHR(0x1c30)
#define CORDBG_E_DEBUGGING_DISABLED EMAKEHR(0x1c31)
#define CORDBG_E_DETACH_FAILED_ON_ENC EMAKEHR(0x1c32)
#define CORDBG_E_CURRENT_EXCEPTION_IS_OUTSIDE_CURRENT_EXECUTION_SCOPE EMAKEHR(0x1c33)
#define CORDBG_E_HELPER_MAY_DEADLOCK EMAKEHR(0x1c34)
#define CORDBG_E_MISSING_METADATA EMAKEHR(0x1c35)
#define CORDBG_E_TARGET_INCONSISTENT EMAKEHR(0x1c36)
#define CORDBG_E_DETACH_FAILED_OUTSTANDING_TARGET_RESOURCES EMAKEHR(0x1c37)
#define CORDBG_E_TARGET_READONLY EMAKEHR(0x1c38)
#define CORDBG_E_MISMATCHED_CORWKS_AND_DACWKS_DLLS EMAKEHR(0x1c39)
#define CORDBG_E_MODULE_LOADED_FROM_DISK EMAKEHR(0x1c3a)
#define CORDBG_E_SYMBOLS_NOT_AVAILABLE EMAKEHR(0x1c3b)
#define CORDBG_E_DEBUG_COMPONENT_MISSING EMAKEHR(0x1c3c)
#define CORDBG_E_REMOTE_MISMATCHED_CERTS EMAKEHR(0x1c3d)
#define CORDBG_E_REMOTE_NETWORK_FAILURE EMAKEHR(0x1c3e)
#define CORDBG_E_REMOTE_NO_LISTENER EMAKEHR(0x1c3f)
#define CORDBG_E_REMOTE_UNKNOWN_TARGET EMAKEHR(0x1c40)
#define CORDBG_E_REMOTE_INVALID_CONFIG EMAKEHR(0x1c41)
#define CORDBG_E_REMOTE_MISMATCHED_PROTOCOLS EMAKEHR(0x1c42)
#define CORDBG_E_LIBRARY_PROVIDER_ERROR EMAKEHR(0x1c43)
#define CORDBG_E_NOT_CLR EMAKEHR(0x1c44)
#define CORDBG_E_MISSING_DATA_TARGET_INTERFACE EMAKEHR(0x1c45)
#define CORDBG_E_UNSUPPORTED_DEBUGGING_MODEL EMAKEHR(0x1c46)
#define CORDBG_E_UNSUPPORTED_FORWARD_COMPAT EMAKEHR(0x1c47)
#define CORDBG_E_UNSUPPORTED_VERSION_STRUCT EMAKEHR(0x1c48)
#define CORDBG_E_READVIRTUAL_FAILURE EMAKEHR(0x1c49)
#define CORDBG_E_VALUE_POINTS_TO_FUNCTION EMAKEHR(0x1c4a)
#define CORDBG_E_CORRUPT_OBJECT EMAKEHR(0x1c4b)
#define CORDBG_E_GC_STRUCTURES_INVALID EMAKEHR(0x1c4c)

#define CORDBG_S_BAD_START_SEQUENCE_POINT SMAKEHR(0x130b)
#define CORDBG_S_BAD_END_SEQUENCE_POINT SMAKEHR(0x130c)
#define CORDBG_S_INSUFFICIENT_INFO_FOR_SET_IP SMAKEHR(0x130d)
#define CORDBG_S_FUNC_EVAL_HAS_NO_RESULT SMAKEHR(0x1316)
#define CORDBG_S_VALUE_POINTS_TO_VOID SMAKEHR(0x1317)
#define CORDBG_S_FUNC_EVAL_ABORTED SMAKEHR(0x1319)
#define CORDBG_S_AT_END_OF_STACK SMAKEHR(0x1324)
#define CORDBG_S_NOT_ALL_BITS_SET SMAKEHR(0x1c13)

#define CORPROF_E_FUNCTION_NOT_COMPILED EMAKEHR(0x1350)
#define CORPROF_E_DATAINCOMPLETE EMAKEHR(0x1351)
#define CORPROF_E_NOT_REJITABLE_METHODS EMAKEHR(0x1352)
#define CORPROF_E_CANNOT_UPDATE_METHOD EMAKEHR(0x1353)
#define CORPROF_E_FUNCTION_NOT_IL EMAKEHR(0x1354)
#define CORPROF_E_NOT_MANAGED_THREAD EMAKEHR(0x1355)
#define CORPROF_E_CALL_ONLY_FROM_INIT EMAKEHR(0x1356)
#define CORPROF_E_INPROC_NOT_ENABLED EMAKEHR(0x1357)
#define CORPROF_E_JITMAPS_NOT_ENABLED EMAKEHR(0x1358)
#define CORPROF_E_INPROC_ALREADY_BEGUN EMAKEHR(0x1359)
#define CORPROF_E_INPROC_NOT_AVAILABLE EMAKEHR(0x135a)
#define CORPROF_E_NOT_YET_AVAILABLE EMAKEHR(0x135b)
#define CORPROF_E_TYPE_IS_PARAMETERIZED EMAKEHR(0x135c)
#define CORPROF_E_FUNCTION_IS_PARAMETERIZED EMAKEHR(0x135d)
#define CORPROF_E_STACKSNAPSHOT_INVALID_TGT_THREAD EMAKEHR(0x135e)
#define CORPROF_E_STACKSNAPSHOT_UNMANAGED_CTX EMAKEHR(0x135f)
#define CORPROF_E_STACKSNAPSHOT_UNSAFE EMAKEHR(0x1360)
#define CORPROF_E_STACKSNAPSHOT_ABORTED EMAKEHR(0x1361)
#define CORPROF_E_LITERALS_HAVE_NO_ADDRESS EMAKEHR(0x1362)
#define CORPROF_E_UNSUPPORTED_CALL_SEQUENCE EMAKEHR(0x1363)
#define CORPROF_E_ASYNCHRONOUS_UNSAFE EMAKEHR(0x1364)
#define CORPROF_E_CLASSID_IS_ARRAY EMAKEHR(0x1365)
#define CORPROF_E_CLASSID_IS_COMPOSITE EMAKEHR(0x1366)
#define CORPROF_E_PROFILER_DETACHING EMAKEHR(0x1367)
#define CORPROF_E_PROFILER_NOT_ATTACHABLE EMAKEHR(0x1368)
#define CORPROF_E_UNRECOGNIZED_PIPE_MSG_FORMAT EMAKEHR(0x1369)
#define CORPROF_E_PROFILER_ALREADY_ACTIVE EMAKEHR(0x136a)
#define CORPROF_E_PROFILEE_INCOMPATIBLE_WITH_TRIGGER EMAKEHR(0x136b)
#define CORPROF_E_IPC_FAILED EMAKEHR(0x136c)
#define CORPROF_E_PROFILEE_PROCESS_NOT_FOUND EMAKEHR(0x136d)
#define CORPROF_E_CALLBACK3_REQUIRED EMAKEHR(0x136e)
#define CORPROF_E_UNSUPPORTED_FOR_ATTACHING_PROFILER EMAKEHR(0x136f)
#define CORPROF_E_IRREVERSIBLE_INSTRUMENTATION_PRESENT EMAKEHR(0x1370)
#define CORPROF_E_RUNTIME_UNINITIALIZED EMAKEHR(0x1371)
#define CORPROF_E_IMMUTABLE_FLAGS_SET EMAKEHR(0x1372)
#define CORPROF_E_PROFILER_NOT_YET_INITIALIZED EMAKEHR(0x1373)
#define CORPROF_E_INCONSISTENT_WITH_FLAGS EMAKEHR(0x1374)
#define CORPROF_E_PROFILER_CANCEL_ACTIVATION EMAKEHR(0x1375)
#define CORPROF_E_CONCURRENT_GC_NOT_PROFILABLE EMAKEHR(0x1376)
#define CORPROF_E_INCONSISTENT_FLAGS_WITH_HOST_PROTECTION_SETTING EMAKEHR(0x1377)
#define CORPROF_E_DEBUGGING_DISABLED EMAKEHR(0x1378)
#define CORPROF_E_TIMEOUT_WAITING_FOR_CONCURRENT_GC EMAKEHR(0x1379)
#define CORPROF_E_MODULE_IS_DYNAMIC EMAKEHR(0x137a)
#define CORPROF_E_CALLBACK4_REQUIRED EMAKEHR(0x137b)
#define CORPROF_E_REJIT_NOT_ENABLED EMAKEHR(0x137c)
#define CORPROF_E_ACTIVE_REJIT_REQUEST_NOT_FOUND EMAKEHR(0x137d)
#define CORPROF_E_FUNCTION_IS_COLLECTIBLE EMAKEHR(0x137e)
#define CORPROF_E_REJIT_REQUIRES_DISABLE_NGEN EMAKEHR(0x137f)

#define CORSEC_E_DECODE_SET EMAKEHR(0x1410)
#define CORSEC_E_ENCODE_SET EMAKEHR(0x1411)
#define CORSEC_E_UNSUPPORTED_FORMAT EMAKEHR(0x1412)
#define CORSEC_E_CRYPTOAPI_CALL_FAILED EMAKEHR(0x1413)
#define CORSEC_E_NO_SUITABLE_CSP EMAKEHR(0x1414)
#define CORSEC_E_INVALID_ATTR EMAKEHR(0x1415)
#define CORSEC_E_POLICY_EXCEPTION EMAKEHR(0x1416)
#define CORSEC_E_MIN_GRANT_FAIL EMAKEHR(0x1417)
#define CORSEC_E_NO_EXEC_PERM EMAKEHR(0x1418)
#define CORSEC_E_XMLSYNTAX EMAKEHR(0x1419)
#define CORSEC_E_INVALID_STRONGNAME EMAKEHR(0x141a)
#define CORSEC_E_MISSING_STRONGNAME EMAKEHR(0x141b)
#define CORSEC_E_CONTAINER_NOT_FOUND EMAKEHR(0x141c)
#define CORSEC_E_INVALID_IMAGE_FORMAT EMAKEHR(0x141d)
#define CORSEC_E_INVALID_PUBLICKEY EMAKEHR(0x141e)
#define CORSEC_E_SIGNATURE_MISMATCH EMAKEHR(0x1420)
#define CORSEC_E_INVALID_SIGNATUREKEY EMAKEHR(0x1422)
#define CORSEC_E_INVALID_COUNTERSIGNATURE EMAKEHR(0x1423)
#define CORSEC_E_CRYPTO EMAKEHR(0x1430)
#define CORSEC_E_CRYPTO_UNEX_OPER EMAKEHR(0x1431)

#define CORSECATTR_E_BAD_ATTRIBUTE EMAKEHR(0x143a)
#define CORSECATTR_E_MISSING_CONSTRUCTOR EMAKEHR(0x143b)
#define CORSECATTR_E_FAILED_TO_CREATE_PERM EMAKEHR(0x143c)
#define CORSECATTR_E_BAD_ACTION_ASM EMAKEHR(0x143d)
#define CORSECATTR_E_BAD_ACTION_OTHER EMAKEHR(0x143e)
#define CORSECATTR_E_BAD_PARENT EMAKEHR(0x143f)
#define CORSECATTR_E_TRUNCATED EMAKEHR(0x1440)
#define CORSECATTR_E_BAD_VERSION EMAKEHR(0x1441)
#define CORSECATTR_E_BAD_ACTION EMAKEHR(0x1442)
#define CORSECATTR_E_NO_SELF_REF EMAKEHR(0x1443)
#define CORSECATTR_E_BAD_NONCAS EMAKEHR(0x1444)
#define CORSECATTR_E_ASSEMBLY_LOAD_FAILED EMAKEHR(0x1445)
#define CORSECATTR_E_ASSEMBLY_LOAD_FAILED_EX EMAKEHR(0x1446)
#define CORSECATTR_E_TYPE_LOAD_FAILED EMAKEHR(0x1447)
#define CORSECATTR_E_TYPE_LOAD_FAILED_EX EMAKEHR(0x1448)
#define CORSECATTR_E_ABSTRACT EMAKEHR(0x1449)
#define CORSECATTR_E_UNSUPPORTED_TYPE EMAKEHR(0x144a)
#define CORSECATTR_E_UNSUPPORTED_ENUM_TYPE EMAKEHR(0x144b)
#define CORSECATTR_E_NO_FIELD EMAKEHR(0x144c)
#define CORSECATTR_E_NO_PROPERTY EMAKEHR(0x144d)
#define CORSECATTR_E_EXCEPTION EMAKEHR(0x144e)
#define CORSECATTR_E_EXCEPTION_HR EMAKEHR(0x144f)

#define CEE_E_ENTRYPOINT EMAKEHR(0x1000)
#define CEE_E_CVTRES_NOT_FOUND EMAKEHR(0x1001)

#define HOST_E_DEADLOCK EMAKEHR(0x1020)
#define HOST_E_INTERRUPTED EMAKEHR(0x1021)
#define HOST_E_INVALIDOPERATION EMAKEHR(0x1022)
#define HOST_E_CLRNOTAVAILABLE EMAKEHR(0x1023)
#define HOST_E_TIMEOUT EMAKEHR(0x1024)
#define HOST_E_NOT_OWNER EMAKEHR(0x1025)
#define HOST_E_ABANDONED EMAKEHR(0x1026)
#define HOST_E_EXITPROCESS_THREADABORT EMAKEHR(0x1027)
#define HOST_E_EXITPROCESS_ADUNLOAD EMAKEHR(0x1028)
#define HOST_E_EXITPROCESS_TIMEOUT EMAKEHR(0x1029)
#define HOST_E_EXITPROCESS_OUTOFMEMORY EMAKEHR(0x102a)
#define HOST_E_EXITPROCESS_STACKOVERFLOW EMAKEHR(0x102b)

#define ISS_E_ISOSTORE_START EMAKEHR(0x1450)
#define ISS_E_ISOSTORE EMAKEHR(0x1450)
#define ISS_E_OPEN_STORE_FILE EMAKEHR(0x1460)
#define ISS_E_OPEN_FILE_MAPPING EMAKEHR(0x1461)
#define ISS_E_MAP_VIEW_OF_FILE EMAKEHR(0x1462)
#define ISS_E_GET_FILE_SIZE EMAKEHR(0x1463)
#define ISS_E_CREATE_MUTEX EMAKEHR(0x1464)
#define ISS_E_LOCK_FAILED EMAKEHR(0x1465)
#define ISS_E_FILE_WRITE EMAKEHR(0x1466)
#define ISS_E_SET_FILE_POINTER EMAKEHR(0x1467)
#define ISS_E_CREATE_DIR EMAKEHR(0x1468)
#define ISS_E_STORE_NOT_OPEN EMAKEHR(0x1469)
#define ISS_E_CORRUPTED_STORE_FILE EMAKEHR(0x1480)
#define ISS_E_STORE_VERSION EMAKEHR(0x1481)
#define ISS_E_FILE_NOT_MAPPED EMAKEHR(0x1482)
#define ISS_E_BLOCK_SIZE_TOO_SMALL EMAKEHR(0x1483)
#define ISS_E_ALLOC_TOO_LARGE EMAKEHR(0x1484)
#define ISS_E_USAGE_WILL_EXCEED_QUOTA EMAKEHR(0x1485)
#define ISS_E_TABLE_ROW_NOT_FOUND EMAKEHR(0x1486)
#define ISS_E_DEPRECATE EMAKEHR(0x14a0)
#define ISS_E_CALLER EMAKEHR(0x14a1)
#define ISS_E_PATH_LENGTH EMAKEHR(0x14a2)
#define ISS_E_MACHINE EMAKEHR(0x14a3)
#define ISS_E_MACHINE_DACL EMAKEHR(0x14a4)
#define ISS_E_ISOSTORE_END EMAKEHR(0x14ff)

#define META_E_DUPLICATE EMAKEHR(0x1180)
#define META_E_GUID_REQUIRED EMAKEHR(0x1181)
#define META_E_TYPEDEF_MISMATCH EMAKEHR(0x1182)
#define META_E_MERGE_COLLISION EMAKEHR(0x1183)
#define META_E_METHD_NOT_FOUND EMAKEHR(0x1187)
#define META_E_FIELD_NOT_FOUND EMAKEHR(0x1188)
#define META_E_PARAM_MISMATCH EMAKEHR(0x1189)
#define META_E_BADMETADATA EMAKEHR(0x118a)
#define META_E_INTFCEIMPL_NOT_FOUND EMAKEHR(0x118b)
#define META_E_CLASS_LAYOUT_INCONSISTENT EMAKEHR(0x118d)
#define META_E_FIELD_MARSHAL_NOT_FOUND EMAKEHR(0x118e)
#define META_E_METHODSEM_NOT_FOUND EMAKEHR(0x118f)
#define META_E_EVENT_NOT_FOUND EMAKEHR(0x1190)
#define META_E_PROP_NOT_FOUND EMAKEHR(0x1191)
#define META_E_BAD_SIGNATURE EMAKEHR(0x1192)
#define META_E_BAD_INPUT_PARAMETER EMAKEHR(0x1193)
#define META_E_METHDIMPL_INCONSISTENT EMAKEHR(0x1194)
#define META_E_MD_INCONSISTENCY EMAKEHR(0x1195)
#define META_E_CANNOTRESOLVETYPEREF EMAKEHR(0x1196)
#define META_E_STRINGSPACE_FULL EMAKEHR(0x1198)
#define META_E_UNEXPECTED_REMAP EMAKEHR(0x1199)
#define META_E_HAS_UNMARKALL EMAKEHR(0x119a)
#define META_E_MUST_CALL_UNMARKALL EMAKEHR(0x119b)
#define META_E_GENERICPARAM_INCONSISTENT EMAKEHR(0x119c)
#define META_E_EVENT_COUNTS EMAKEHR(0x119d)
#define META_E_PROPERTY_COUNTS EMAKEHR(0x119e)
#define META_E_TYPEDEF_MISSING EMAKEHR(0x119f)
#define META_E_INVALID_TOKEN_TYPE EMAKEHR(0x115f)
#define META_E_CA_INVALID_TARGET EMAKEHR(0x11c0)
#define META_E_CA_INVALID_VALUE EMAKEHR(0x11c1)
#define META_E_CA_INVALID_BLOB EMAKEHR(0x11c2)
#define META_E_CA_REPEATED_ARG EMAKEHR(0x11c3)
#define META_E_CA_UNKNOWN_ARGUMENT EMAKEHR(0x11c4)
#define META_E_CA_VARIANT_NYI EMAKEHR(0x11c5)
#define META_E_CA_ARRAY_NYI EMAKEHR(0x11c6)
#define META_E_CA_UNEXPECTED_TYPE EMAKEHR(0x11c7)
#define META_E_CA_INVALID_ARGTYPE EMAKEHR(0x11c8)
#define META_E_CA_INVALID_ARG_FOR_TYPE EMAKEHR(0x11c9)
#define META_E_CA_INVALID_UUID EMAKEHR(0x11ca)
#define META_E_CA_INVALID_MARSHALAS_FIELDS EMAKEHR(0x11cb)
#define META_E_CA_NT_FIELDONLY EMAKEHR(0x11cc)
#define META_E_CA_NEGATIVE_PARAMINDEX EMAKEHR(0x11cd)
#define META_E_CA_NEGATIVE_MULTIPLIER EMAKEHR(0x11ce)
#define META_E_CA_NEGATIVE_CONSTSIZE EMAKEHR(0x11cf)
#define META_E_CA_FIXEDSTR_SIZE_REQUIRED EMAKEHR(0x11d0)
#define META_E_CA_CUSTMARSH_TYPE_REQUIRED EMAKEHR(0x11d1)
#define META_E_CA_FILENAME_REQUIRED EMAKEHR(0x11d2)
#define META_E_NOT_IN_ENC_MODE EMAKEHR(0x11d4)
#define META_E_METHOD_COUNTS EMAKEHR(0x11d6)
#define META_E_FIELD_COUNTS EMAKEHR(0x11d7)
#define META_E_PARAM_COUNTS EMAKEHR(0x11d8)
#define META_E_MISMATCHED_VISIBLITY EMAKEHR(0x11e4)
#define META_E_CA_BAD_FRIENDS_ARGS EMAKEHR(0x11e5)
#define META_E_CA_FRIENDS_SN_REQUIRED EMAKEHR(0x11e6)

#define MSEE_E_LOADLIBFAILED EMAKEHR(0x1010)
#define MSEE_E_GETPROCFAILED EMAKEHR(0x1011)
#define MSEE_E_MULTCOPIESLOADED EMAKEHR(0x1012)
#define MSEE_E_ASSEMBLYLOADINPROGRESS EMAKEHR(0x1016)
#define MSEE_E_CANNOTCREATEAPPDOMAIN EMAKEHR(0x1017)

#define NGEN_FAILED_GET_DEPENDENCIES EMAKEHR(0x1f00)
#define NGEN_FAILED_NATIVE_IMAGE_DELETE EMAKEHR(0x1f01)
#define NGEN_E_TOO_MANY_INTERFACES EMAKEHR(0x1f02)
#define NGEN_E_OLDER_RUNTIME EMAKEHR(0x1f03)
#define NGEN_E_WORKER_UNEXPECTED_EXIT EMAKEHR(0x1f04)
#define NGEN_E_WORKER_UNEXPECTED_SYNC EMAKEHR(0x1f05)
#define NGEN_E_SYS_ASM_NI_MISSING EMAKEHR(0x1f06)
#define NGEN_E_EXE_MACHINE_TYPE_MISMATCH EMAKEHR(0x1f07)
#define NGEN_E_ASSEMBLY_EXCLUSION_FILE_PARSE_ERROR EMAKEHR(0x1f08)
#define NGEN_E_HARDBOUND_DEPENDENCY_MISSING EMAKEHR(0x1f09)
#define NGEN_E_NOT_RUNNING_IN_EXPECTED_PACKAGE EMAKEHR(0x1f0a)

#define PEFMT_E_NO_CONTENTS      EMAKEHR(0x1d00)
#define PEFMT_E_NO_NTHEADERS     EMAKEHR(0x1d01)
#define PEFMT_E_64BIT            EMAKEHR(0x1d02)
#define PEFMT_E_NO_CORHEADER     EMAKEHR(0x1d03)
#define PEFMT_E_NOT_ILONLY       EMAKEHR(0x1d04)
#define PEFMT_E_IMPORT_DLLS      EMAKEHR(0x1d05)
#define PEFMT_E_EXE_NOENTRYPOINT EMAKEHR(0x1d06)
#define PEFMT_E_BASE_RELOCS      EMAKEHR(0x1d07)
#define PEFMT_E_ENTRYPOINT       EMAKEHR(0x1d08)
#define PEFMT_E_ZERO_SIZEOFCODE  EMAKEHR(0x1d09)
#define PEFMT_E_BAD_CORHEADER    EMAKEHR(0x1d0a)
#define PEFMT_E_32BIT            EMAKEHR(0x1d0b)

#define SECURITY_E_XML_TO_ASN_ENCODING   EMAKEHR(0x1400)
#define SECURITY_E_INCOMPATIBLE_SHARE    EMAKEHR(0x1401)
#define SECURITY_E_UNVERIFIABLE          EMAKEHR(0x1402)
#define SECURITY_E_INCOMPATIBLE_EVIDENCE EMAKEHR(0x1403)

#define SN_E_PUBLICKEY_MISMATCH EMAKEHR(0x1421)
#define SN_CRYPTOAPI_CALL_FAILED EMAKEHR(0x1413)
#define SN_NO_SUITABLE_CSP EMAKEHR(0x1414)

#define VER_E_HRESULT EMAKEHR(0x1801)
#define VER_E_OFFSET EMAKEHR(0x1802)
#define VER_E_OPCODE EMAKEHR(0x1803)
#define VER_E_OPERAND EMAKEHR(0x1804)
#define VER_E_TOKEN EMAKEHR(0x1805)
#define VER_E_EXCEPT EMAKEHR(0x1806)
#define VER_E_STACK_SLOT EMAKEHR(0x1807)
#define VER_E_LOC EMAKEHR(0x1808)
#define VER_E_ARG EMAKEHR(0x1809)
#define VER_E_FOUND EMAKEHR(0x180a)
#define VER_E_EXPECTED EMAKEHR(0x180b)
#define VER_E_LOC_BYNAME EMAKEHR(0x180c)
#define VER_E_UNKNOWN_OPCODE EMAKEHR(0x1810)
#define VER_E_SIG_CALLCONV EMAKEHR(0x1811)
#define VER_E_SIG_ELEMTYPE EMAKEHR(0x1812)
#define VER_E_RET_SIG EMAKEHR(0x1814)
#define VER_E_FIELD_SIG EMAKEHR(0x1815)
#define VER_E_OPEN_DLGT_PROT_ACC EMAKEHR(0x1816)
#define VER_E_INTERNAL EMAKEHR(0x1818)
#define VER_E_STACK_TOO_LARGE EMAKEHR(0x1819)
#define VER_E_ARRAY_NAME_LONG EMAKEHR(0x181a)
#define VER_E_FALLTHRU EMAKEHR(0x1820)
#define VER_E_TRY_GTEQ_END EMAKEHR(0x1821)
#define VER_E_TRYEND_GT_CS EMAKEHR(0x1822)
#define VER_E_HND_GTEQ_END EMAKEHR(0x1823)
#define VER_E_HNDEND_GT_CS EMAKEHR(0x1824)
#define VER_E_FLT_GTEQ_CS EMAKEHR(0x1825)
#define VER_E_TRY_START EMAKEHR(0x1826)
#define VER_E_HND_START EMAKEHR(0x1827)
#define VER_E_FLT_START EMAKEHR(0x1828)
#define VER_E_TRY_OVERLAP EMAKEHR(0x1829)
#define VER_E_TRY_EQ_HND_FIL EMAKEHR(0x182a)
#define VER_E_TRY_SHARE_FIN_FAL EMAKEHR(0x182b)
#define VER_E_HND_OVERLAP EMAKEHR(0x182c)
#define VER_E_HND_EQ EMAKEHR(0x182d)
#define VER_E_FIL_OVERLAP EMAKEHR(0x182e)
#define VER_E_FIL_EQ EMAKEHR(0x182f)
#define VER_E_FIL_CONT_TRY EMAKEHR(0x1830)
#define VER_E_FIL_CONT_HND EMAKEHR(0x1831)
#define VER_E_FIL_CONT_FIL EMAKEHR(0x1832)
#define VER_E_FIL_GTEQ_CS EMAKEHR(0x1833)
#define VER_E_FIL_START EMAKEHR(0x1834)
#define VER_E_FALLTHRU_EXCEP EMAKEHR(0x1835)
#define VER_E_FALLTHRU_INTO_HND EMAKEHR(0x1836)
#define VER_E_FALLTHRU_INTO_FIL EMAKEHR(0x1837)
#define VER_E_LEAVE EMAKEHR(0x1838)
#define VER_E_RETHROW EMAKEHR(0x1839)
#define VER_E_ENDFINALLY EMAKEHR(0x183a)
#define VER_E_ENDFILTER EMAKEHR(0x183b)
#define VER_E_ENDFILTER_MISSING EMAKEHR(0x183c)
#define VER_E_BR_INTO_TRY EMAKEHR(0x183d)
#define VER_E_BR_INTO_HND EMAKEHR(0x183e)
#define VER_E_BR_INTO_FIL EMAKEHR(0x183f)
#define VER_E_BR_OUTOF_TRY EMAKEHR(0x1840)
#define VER_E_BR_OUTOF_HND EMAKEHR(0x1841)
#define VER_E_BR_OUTOF_FIL EMAKEHR(0x1842)
#define VER_E_BR_OUTOF_FIN EMAKEHR(0x1843)
#define VER_E_RET_FROM_TRY EMAKEHR(0x1844)
#define VER_E_RET_FROM_HND EMAKEHR(0x1845)
#define VER_E_RET_FROM_FIL EMAKEHR(0x1846)
#define VER_E_BAD_JMP_TARGET EMAKEHR(0x1847)
#define VER_E_PATH_LOC EMAKEHR(0x1848)
#define VER_E_PATH_THIS EMAKEHR(0x1849)
#define VER_E_PATH_STACK EMAKEHR(0x184a)
#define VER_E_PATH_STACK_DEPTH EMAKEHR(0x184b)
#define VER_E_THIS EMAKEHR(0x184c)
#define VER_E_THIS_UNINIT_EXCEP EMAKEHR(0x184d)
#define VER_E_THIS_UNINIT_STORE EMAKEHR(0x184e)
#define VER_E_THIS_UNINIT_RET EMAKEHR(0x184f)
#define VER_E_THIS_UNINIT_V_RET EMAKEHR(0x1850)
#define VER_E_THIS_UNINIT_BR EMAKEHR(0x1851)
#define VER_E_LDFTN_CTOR EMAKEHR(0x1852)
#define VER_E_STACK_NOT_EQ EMAKEHR(0x1853)
#define VER_E_STACK_UNEXPECTED EMAKEHR(0x1854)
#define VER_E_STACK_EXCEPTION EMAKEHR(0x1855)
#define VER_E_STACK_OVERFLOW EMAKEHR(0x1856)
#define VER_E_STACK_UNDERFLOW EMAKEHR(0x1857)
#define VER_E_STACK_EMPTY EMAKEHR(0x1858)
#define VER_E_STACK_UNINIT EMAKEHR(0x1859)
#define VER_E_STACK_I_I4_I8 EMAKEHR(0x185a)
#define VER_E_STACK_R_R4_R8 EMAKEHR(0x185b)
#define VER_E_STACK_NO_R_I8 EMAKEHR(0x185c)
#define VER_E_STACK_NUMERIC EMAKEHR(0x185d)
#define VER_E_STACK_OBJREF EMAKEHR(0x185e)
#define VER_E_STACK_P_OBJREF EMAKEHR(0x185f)
#define VER_E_STACK_BYREF EMAKEHR(0x1860)
#define VER_E_STACK_METHOD EMAKEHR(0x1861)
#define VER_E_STACK_ARRAY_SD EMAKEHR(0x1862)
#define VER_E_STACK_VALCLASS EMAKEHR(0x1863)
#define VER_E_STACK_P_VALCLASS EMAKEHR(0x1864)
#define VER_E_STACK_NO_VALCLASS EMAKEHR(0x1865)
#define VER_E_LOC_DEAD EMAKEHR(0x1866)
#define VER_E_LOC_NUM EMAKEHR(0x1867)
#define VER_E_ARG_NUM EMAKEHR(0x1868)
#define VER_E_TOKEN_RESOLVE EMAKEHR(0x1869)
#define VER_E_TOKEN_TYPE EMAKEHR(0x186a)
#define VER_E_TOKEN_TYPE_MEMBER EMAKEHR(0x186b)
#define VER_E_TOKEN_TYPE_FIELD EMAKEHR(0x186c)
#define VER_E_TOKEN_TYPE_SIG EMAKEHR(0x186d)
#define VER_E_UNVERIFIABLE EMAKEHR(0x186e)
#define VER_E_LDSTR_OPERAND EMAKEHR(0x186f)
#define VER_E_RET_PTR_TO_STACK EMAKEHR(0x1870)
#define VER_E_RET_VOID EMAKEHR(0x1871)
#define VER_E_RET_MISSING EMAKEHR(0x1872)
#define VER_E_RET_EMPTY EMAKEHR(0x1873)
#define VER_E_RET_UNINIT EMAKEHR(0x1874)
#define VER_E_ARRAY_ACCESS EMAKEHR(0x1875)
#define VER_E_ARRAY_V_STORE EMAKEHR(0x1876)
#define VER_E_ARRAY_SD EMAKEHR(0x1877)
#define VER_E_ARRAY_SD_PTR EMAKEHR(0x1878)
#define VER_E_ARRAY_FIELD EMAKEHR(0x1879)
#define VER_E_ARGLIST EMAKEHR(0x187a)
#define VER_E_VALCLASS EMAKEHR(0x187b)
#define VER_E_METHOD_ACCESS EMAKEHR(0x187c)
#define VER_E_FIELD_ACCESS EMAKEHR(0x187d)
#define VER_E_DEAD EMAKEHR(0x187e)
#define VER_E_FIELD_STATIC EMAKEHR(0x187f)
#define VER_E_FIELD_NO_STATIC EMAKEHR(0x1880)
#define VER_E_ADDR EMAKEHR(0x1881)
#define VER_E_ADDR_BYREF EMAKEHR(0x1882)
#define VER_E_ADDR_LITERAL EMAKEHR(0x1883)
#define VER_E_INITONLY EMAKEHR(0x1884)
#define VER_E_THROW EMAKEHR(0x1885)
#define VER_E_CALLVIRT_VALCLASS EMAKEHR(0x1886)
#define VER_E_CALL_SIG EMAKEHR(0x1887)
#define VER_E_CALL_STATIC EMAKEHR(0x1888)
#define VER_E_CTOR EMAKEHR(0x1889)
#define VER_E_CTOR_VIRT EMAKEHR(0x188a)
#define VER_E_CTOR_OR_SUPER EMAKEHR(0x188b)
#define VER_E_CTOR_MUL_INIT EMAKEHR(0x188c)
#define VER_E_SIG EMAKEHR(0x188d)
#define VER_E_SIG_ARRAY EMAKEHR(0x188e)
#define VER_E_SIG_ARRAY_PTR EMAKEHR(0x188f)
#define VER_E_SIG_ARRAY_BYREF EMAKEHR(0x1890)
#define VER_E_SIG_ELEM_PTR EMAKEHR(0x1891)
#define VER_E_SIG_VARARG EMAKEHR(0x1892)
#define VER_E_SIG_VOID EMAKEHR(0x1893)
#define VER_E_SIG_BYREF_BYREF EMAKEHR(0x1894)
#define VER_E_CODE_SIZE_ZERO EMAKEHR(0x1896)
#define VER_E_BAD_VARARG EMAKEHR(0x1897)
#define VER_E_TAIL_CALL EMAKEHR(0x1898)
#define VER_E_TAIL_BYREF EMAKEHR(0x1899)
#define VER_E_TAIL_RET EMAKEHR(0x189a)
#define VER_E_TAIL_RET_VOID EMAKEHR(0x189b)
#define VER_E_TAIL_RET_TYPE EMAKEHR(0x189c)
#define VER_E_TAIL_STACK_EMPTY EMAKEHR(0x189d)
#define VER_E_METHOD_END EMAKEHR(0x189e)
#define VER_E_BAD_BRANCH EMAKEHR(0x189f)
#define VER_E_FIN_OVERLAP EMAKEHR(0x18a0)
#define VER_E_LEXICAL_NESTING EMAKEHR(0x18a1)
#define VER_E_VOLATILE EMAKEHR(0x18a2)
#define VER_E_UNALIGNED EMAKEHR(0x18a3)
#define VER_E_INNERMOST_FIRST EMAKEHR(0x18a4)
#define VER_E_CALLI_VIRTUAL EMAKEHR(0x18a5)
#define VER_E_CALL_ABSTRACT EMAKEHR(0x18a6)
#define VER_E_STACK_UNEXP_ARRAY EMAKEHR(0x18a7)
#define VER_E_NOT_IN_GC_HEAP EMAKEHR(0x18a8)
#define VER_E_TRY_N_EMPTY_STACK EMAKEHR(0x18a9)
#define VER_E_DLGT_CTOR EMAKEHR(0x18aa)
#define VER_E_DLGT_BB EMAKEHR(0x18ab)
#define VER_E_DLGT_PATTERN EMAKEHR(0x18ac)
#define VER_E_DLGT_LDFTN EMAKEHR(0x18ad)
#define VER_E_FTN_ABSTRACT EMAKEHR(0x18ae)
#define VER_E_SIG_C_VC EMAKEHR(0x18af)
#define VER_E_SIG_VC_C EMAKEHR(0x18b0)
#define VER_E_BOX_PTR_TO_STACK EMAKEHR(0x18b1)
#define VER_E_SIG_BYREF_TB_AH EMAKEHR(0x18b2)
#define VER_E_SIG_ARRAY_TB_AH EMAKEHR(0x18b3)
#define VER_E_ENDFILTER_STACK EMAKEHR(0x18b4)
#define VER_E_DLGT_SIG_I EMAKEHR(0x18b5)
#define VER_E_DLGT_SIG_O EMAKEHR(0x18b6)
#define VER_E_RA_PTR_TO_STACK EMAKEHR(0x18b7)
#define VER_E_CATCH_VALUE_TYPE EMAKEHR(0x18b8)
#define VER_E_CATCH_BYREF EMAKEHR(0x18b9)
#define VER_E_FIL_PRECEED_HND EMAKEHR(0x18ba)
#define VER_E_LDVIRTFTN_STATIC EMAKEHR(0x18bb)
#define VER_E_CALLVIRT_STATIC EMAKEHR(0x18bc)
#define VER_E_INITLOCALS EMAKEHR(0x18bd)
#define VER_E_BR_TO_EXCEPTION EMAKEHR(0x18be)
#define VER_E_CALL_CTOR EMAKEHR(0x18bf)
#define VER_E_VALCLASS_OBJREF_VAR EMAKEHR(0x18c0)
#define VER_E_STACK_P_VALCLASS_OBJREF_VAR EMAKEHR(0x18c1)
#define VER_E_SIG_VAR_PARAM EMAKEHR(0x18c2)
#define VER_E_SIG_MVAR_PARAM EMAKEHR(0x18c3)
#define VER_E_SIG_VAR_ARG EMAKEHR(0x18c4)
#define VER_E_SIG_MVAR_ARG EMAKEHR(0x18c5)
#define VER_E_SIG_GENERICINST EMAKEHR(0x18c6)
#define VER_E_SIG_METHOD_INST EMAKEHR(0x18c7)
#define VER_E_SIG_METHOD_PARENT_INST EMAKEHR(0x18c8)
#define VER_E_SIG_FIELD_PARENT_INST EMAKEHR(0x18c9)
#define VER_E_CALLCONV_NOT_GENERICINST EMAKEHR(0x18ca)
#define VER_E_TOKEN_BAD_METHOD_SPEC EMAKEHR(0x18cb)
#define VER_E_BAD_READONLY_PREFIX EMAKEHR(0x18cc)
#define VER_E_BAD_CONSTRAINED_PREFIX EMAKEHR(0x18cd)
#define VER_E_CIRCULAR_VAR_CONSTRAINTS EMAKEHR(0x18ce)
#define VER_E_CIRCULAR_MVAR_CONSTRAINTS EMAKEHR(0x18cf)
#define VER_E_UNSATISFIED_METHOD_INST EMAKEHR(0x18d0)
#define VER_E_UNSATISFIED_METHOD_PARENT_INST EMAKEHR(0x18d1)
#define VER_E_UNSATISFIED_FIELD_PARENT_INST EMAKEHR(0x18d2)
#define VER_E_UNSATISFIED_BOX_OPERAND EMAKEHR(0x18d3)
#define VER_E_CONSTRAINED_CALL_WITH_NON_BYREF_THIS EMAKEHR(0x18d4)
#define VER_E_CONSTRAINED_OF_NON_VARIABLE_TYPE EMAKEHR(0x18d5)
#define VER_E_READONLY_UNEXPECTED_CALLEE EMAKEHR(0x18d6)
#define VER_E_READONLY_ILLEGAL_WRITE EMAKEHR(0x18d7)
#define VER_E_READONLY_IN_MKREFANY EMAKEHR(0x18d8)
#define VER_E_UNALIGNED_ALIGNMENT EMAKEHR(0x18d9)
#define VER_E_TAILCALL_INSIDE_EH EMAKEHR(0x18da)
#define VER_E_BACKWARD_BRANCH EMAKEHR(0x18db)
#define VER_E_CALL_TO_VTYPE_BASE EMAKEHR(0x18dc)
#define VER_E_NEWOBJ_OF_ABSTRACT_CLASS EMAKEHR(0x18dd)
#define VER_E_UNMANAGED_POINTER EMAKEHR(0x18de)
#define VER_E_LDFTN_NON_FINAL_VIRTUAL EMAKEHR(0x18df)
#define VER_E_FIELD_OVERLAP EMAKEHR(0x18e0)
#define VER_E_THIS_MISMATCH EMAKEHR(0x18e1)
#define VER_E_STACK_I_I4 EMAKEHR(0x18e2)
#define VER_E_BAD_PE EMAKEHR(0x18f0)
#define VER_E_BAD_MD EMAKEHR(0x18f1)
#define VER_E_BAD_APPDOMAIN EMAKEHR(0x18f2)
#define VER_E_TYPELOAD EMAKEHR(0x18f3)
#define VER_E_PE_LOAD EMAKEHR(0x18f4)
#define VER_E_WRITE_RVA_STATIC EMAKEHR(0x18f5)
#define VER_E_INITIALIZE_ARRAY_MISSING_TOKEN EMAKEHR(0x18f6)

#endif    /* __WINE_CORERROR_H */
