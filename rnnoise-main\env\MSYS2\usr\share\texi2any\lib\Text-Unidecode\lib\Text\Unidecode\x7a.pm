# Time-stamp: "Sat Jul 14 00:27:33 2001 by Automatic Bizooty (__blocks2pm.plx)"
$Text::Unidecode::Char[0x7a] = [
'<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON>n ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ',
'<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', 'Ji ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', 'Kun ', 'Leng ', 'Peng ', '<PERSON> ', '<PERSON> ',
'Chou ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', qq{[?] }, '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', 'Ji<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ',
'<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ',
'<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON>hi ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ',
'<PERSON>u ', '<PERSON> ', qq{[?] }, '<PERSON> ', '<PERSON> ', '<PERSON>e ', 'Ji ', '<PERSON>i ', '<PERSON> ', '<PERSON>u ', 'Jiao ', '<PERSON>o ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON>i ',
'<PERSON>g ', '<PERSON> ', '<PERSON> ', '<PERSON>ng ', 'Nuo ', 'Yu ', 'Bin ', 'Ji ', 'Tui ', 'Wen ', 'Cheng ', 'Huo ', 'Gong ', 'Lu ', 'Biao ', qq{[?] },
'Rang ', 'Zhuo ', 'Li ', 'Zan ', 'Xue ', 'Wa ', 'Jiu ', 'Qiong ', 'Xi ', 'Qiong ', 'Kong ', 'Yu ', 'Sen ', 'Jing ', 'Yao ', 'Chuan ',
'Zhun ', 'Tu ', 'Lao ', 'Qie ', 'Zhai ', 'Yao ', 'Bian ', 'Bao ', 'Yao ', 'Bing ', 'Wa ', 'Zhu ', 'Jiao ', 'Qiao ', 'Diao ', 'Wu ',
'Gui ', 'Yao ', 'Zhi ', 'Chuang ', 'Yao ', 'Tiao ', 'Jiao ', 'Chuang ', 'Jiong ', 'Xiao ', 'Cheng ', 'Kou ', 'Cuan ', 'Wo ', 'Dan ', 'Ku ',
'Ke ', 'Zhui ', 'Xu ', 'Su ', 'Guan ', 'Kui ', 'Dou ', qq{[?] }, 'Yin ', 'Wo ', 'Wa ', 'Ya ', 'Yu ', 'Ju ', 'Qiong ', 'Yao ',
'Yao ', 'Tiao ', 'Chao ', 'Yu ', 'Tian ', 'Diao ', 'Ju ', 'Liao ', 'Xi ', 'Wu ', 'Kui ', 'Chuang ', 'Zhao ', qq{[?] }, 'Kuan ', 'Long ',
'Cheng ', 'Cui ', 'Piao ', 'Zao ', 'Cuan ', 'Qiao ', 'Qiong ', 'Dou ', 'Zao ', 'Long ', 'Qie ', 'Li ', 'Chu ', 'Shi ', 'Fou ', 'Qian ',
'Chu ', 'Hong ', 'Qi ', 'Qian ', 'Gong ', 'Shi ', 'Shu ', 'Miao ', 'Ju ', 'Zhan ', 'Zhu ', 'Ling ', 'Long ', 'Bing ', 'Jing ', 'Jing ',
'Zhang ', 'Yi ', 'Si ', 'Jun ', 'Hong ', 'Tong ', 'Song ', 'Jing ', 'Diao ', 'Yi ', 'Shu ', 'Jing ', 'Qu ', 'Jie ', 'Ping ', 'Duan ',
'Shao ', 'Zhuan ', 'Ceng ', 'Deng ', 'Cui ', 'Huai ', 'Jing ', 'Kan ', 'Jing ', 'Zhu ', 'Zhu ', 'Le ', 'Peng ', 'Yu ', 'Chi ', 'Gan ',
];
1;
