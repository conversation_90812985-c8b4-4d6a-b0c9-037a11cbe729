.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_est_record_overhead_size" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_est_record_overhead_size \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "size_t gnutls_est_record_overhead_size(gnutls_protocol_t " version ", gnutls_cipher_algorithm_t " cipher ", gnutls_mac_algorithm_t " mac ", gnutls_compression_method_t " comp ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_protocol_t version" 12
is a \fBgnutls_protocol_t\fP value
.IP "gnutls_cipher_algorithm_t cipher" 12
is a \fBgnutls_cipher_algorithm_t\fP value
.IP "gnutls_mac_algorithm_t mac" 12
is a \fBgnutls_mac_algorithm_t\fP value
.IP "gnutls_compression_method_t comp" 12
is a \fBgnutls_compression_method_t\fP value (ignored)
.IP "unsigned int flags" 12
must be zero
.SH "DESCRIPTION"
This function will return the set size in bytes of the overhead
due to TLS (or DTLS) per record.

Note that this function may provide inaccurate values when TLS
extensions that modify the record format are negotiated. In these
cases a more accurate value can be obtained using \fBgnutls_record_overhead_size()\fP 
after a completed handshake.
.SH "SINCE"
3.2.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
