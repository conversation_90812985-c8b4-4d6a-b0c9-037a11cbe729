This is gnutls.info, produced by makeinfo version 7.1 from gnutls.texi.

This manual is last updated 28 January 2025 for version 3.8.9 of GnuTLS.

Copyright © 2001-2025 Free Software Foundation, Inc.\\ Copyright ©
2001-2025 <PERSON><PERSON>

     Permission is granted to copy, distribute and/or modify this
     document under the terms of the GNU Free Documentation License,
     Version 1.3 or any later version published by the Free Software
     Foundation; with no Invariant Sections, no Front-Cover Texts, and
     no Back-Cover Texts.  A copy of the license is included in the
     section entitled "GNU Free Documentation License".
INFO-DIR-SECTION Software libraries
START-INFO-DIR-ENTRY
* GnuTLS: (gnutls).		GNU Transport Layer Security Library.
END-INFO-DIR-ENTRY

INFO-DIR-SECTION System Administration
START-INFO-DIR-ENTRY
* certtool: (gnutls)certtool Invocation.	Manipulate certificates and keys.
* gnutls-serv: (gnutls)gnutls-serv Invocation.	GnuTLS test server.
* gnutls-cli: (gnutls)gnutls-cli Invocation.	GnuTLS test client.
* gnutls-cli-debug: (gnutls)gnutls-cli-debug Invocation.	GnuTLS debug client.
* psktool: (gnutls)psktool Invocation.	Simple TLS-Pre-Shared-Keys manager.
* srptool: (gnutls)srptool Invocation.	Simple SRP password tool.
END-INFO-DIR-ENTRY


File: gnutls.info,  Node: OCSP API,  Next: PKCS 12 API,  Prev: PKCS 7 API,  Up: API reference

E.5 OCSP API
============

The following functions are for OCSP certificate status checking.  Their
prototypes lie in ‘gnutls/ocsp.h’.

gnutls_ocsp_req_add_cert
------------------------

 -- Function: int gnutls_ocsp_req_add_cert (gnutls_ocsp_req_t REQ,
          gnutls_digest_algorithm_t DIGEST, gnutls_x509_crt_t ISSUER,
          gnutls_x509_crt_t CERT)
     REQ: should contain a ‘gnutls_ocsp_req_t’ type

     DIGEST: hash algorithm, a ‘gnutls_digest_algorithm_t’ value

     ISSUER: issuer of ‘subject’ certificate

     CERT: certificate to request status for

     This function will add another request to the OCSP request for a
     particular certificate.  The issuer name hash, issuer key hash, and
     serial number fields is populated as follows.  The issuer name and
     the serial number is taken from ‘cert’ .  The issuer key is taken
     from ‘issuer’ .  The hashed values will be hashed using the
     ‘digest’ algorithm, normally ‘GNUTLS_DIG_SHA1’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.

gnutls_ocsp_req_add_cert_id
---------------------------

 -- Function: int gnutls_ocsp_req_add_cert_id (gnutls_ocsp_req_t REQ,
          gnutls_digest_algorithm_t DIGEST, const gnutls_datum_t *
          ISSUER_NAME_HASH, const gnutls_datum_t * ISSUER_KEY_HASH,
          const gnutls_datum_t * SERIAL_NUMBER)
     REQ: should contain a ‘gnutls_ocsp_req_t’ type

     DIGEST: hash algorithm, a ‘gnutls_digest_algorithm_t’ value

     ISSUER_NAME_HASH: hash of issuer's DN

     ISSUER_KEY_HASH: hash of issuer's public key

     SERIAL_NUMBER: serial number of certificate to check

     This function will add another request to the OCSP request for a
     particular certificate having the issuer name hash of
     ‘issuer_name_hash’ and issuer key hash of ‘issuer_key_hash’ (both
     hashed using ‘digest’ ) and serial number ‘serial_number’ .

     The information needed corresponds to the CertID structure:

     <informalexample><programlisting> CertID	::= SEQUENCE {
     hashAlgorithm AlgorithmIdentifier, issuerNameHash OCTET STRING, -
     Hash of Issuer's DN issuerKeyHash OCTET STRING, - Hash of Issuers
     public key serialNumber	CertificateSerialNumber }
     </programlisting></informalexample>

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.

gnutls_ocsp_req_deinit
----------------------

 -- Function: void gnutls_ocsp_req_deinit (gnutls_ocsp_req_t REQ)
     REQ: The data to be deinitialized

     This function will deinitialize a OCSP request structure.

gnutls_ocsp_req_export
----------------------

 -- Function: int gnutls_ocsp_req_export (gnutls_ocsp_req_const_t REQ,
          gnutls_datum_t * DATA)
     REQ: Holds the OCSP request

     DATA: newly allocate buffer holding DER encoded OCSP request

     This function will export the OCSP request to DER format.

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

gnutls_ocsp_req_get_cert_id
---------------------------

 -- Function: int gnutls_ocsp_req_get_cert_id (gnutls_ocsp_req_const_t
          REQ, unsigned INDX, gnutls_digest_algorithm_t * DIGEST,
          gnutls_datum_t * ISSUER_NAME_HASH, gnutls_datum_t *
          ISSUER_KEY_HASH, gnutls_datum_t * SERIAL_NUMBER)
     REQ: should contain a ‘gnutls_ocsp_req_t’ type

     INDX: Specifies which extension OID to get.  Use (0) to get the
     first one.

     DIGEST: output variable with ‘gnutls_digest_algorithm_t’ hash
     algorithm

     ISSUER_NAME_HASH: output buffer with hash of issuer's DN

     ISSUER_KEY_HASH: output buffer with hash of issuer's public key

     SERIAL_NUMBER: output buffer with serial number of certificate to
     check

     This function will return the certificate information of the ‘indx’
     'ed request in the OCSP request.  The information returned
     corresponds to the CertID structure:

     <informalexample><programlisting> CertID	::= SEQUENCE {
     hashAlgorithm AlgorithmIdentifier, issuerNameHash OCTET STRING, -
     Hash of Issuer's DN issuerKeyHash OCTET STRING, - Hash of Issuers
     public key serialNumber	CertificateSerialNumber }
     </programlisting></informalexample>

     Each of the pointers to output variables may be NULL to indicate
     that the caller is not interested in that value.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  If you have reached
     the last CertID available ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’
     will be returned.

gnutls_ocsp_req_get_extension
-----------------------------

 -- Function: int gnutls_ocsp_req_get_extension (gnutls_ocsp_req_const_t
          REQ, unsigned INDX, gnutls_datum_t * OID, unsigned int *
          CRITICAL, gnutls_datum_t * DATA)
     REQ: should contain a ‘gnutls_ocsp_req_t’ type

     INDX: Specifies which extension OID to get.  Use (0) to get the
     first one.

     OID: will hold newly allocated buffer with OID of extension, may be
     NULL

     CRITICAL: output variable with critical flag, may be NULL.

     DATA: will hold newly allocated buffer with extension data, may be
     NULL

     This function will return all information about the requested
     extension in the OCSP request.  The information returned is the
     OID, the critical flag, and the data itself.  The extension OID
     will be stored as a string.  Any of ‘oid’ , ‘critical’ , and ‘data’
     may be NULL which means that the caller is not interested in
     getting that information back.

     The caller needs to deallocate memory by calling ‘gnutls_free()’ on
     ‘oid’ ->data and ‘data’ ->data.

     Since 3.7.0 ‘oid’ ->size does not account for the terminating null
     byte.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  If you have reached
     the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

gnutls_ocsp_req_get_nonce
-------------------------

 -- Function: int gnutls_ocsp_req_get_nonce (gnutls_ocsp_req_const_t
          REQ, unsigned int * CRITICAL, gnutls_datum_t * NONCE)
     REQ: should contain a ‘gnutls_ocsp_req_t’ type

     CRITICAL: whether nonce extension is marked critical, or NULL

     NONCE: will hold newly allocated buffer with nonce data

     This function will return the OCSP request nonce extension data.

     The caller needs to deallocate memory by calling ‘gnutls_free()’ on
     ‘nonce’ ->data.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.

gnutls_ocsp_req_get_version
---------------------------

 -- Function: int gnutls_ocsp_req_get_version (gnutls_ocsp_req_const_t
          REQ)
     REQ: should contain a ‘gnutls_ocsp_req_t’ type

     This function will return the version of the OCSP request.
     Typically this is always 1 indicating version 1.

     *Returns:* version of OCSP request, or a negative error code on
     error.

gnutls_ocsp_req_import
----------------------

 -- Function: int gnutls_ocsp_req_import (gnutls_ocsp_req_t REQ, const
          gnutls_datum_t * DATA)
     REQ: The data to store the parsed request.

     DATA: DER encoded OCSP request.

     This function will convert the given DER encoded OCSP request to
     the native ‘gnutls_ocsp_req_t’ format.  The output will be stored
     in ‘req’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_ocsp_req_init
--------------------

 -- Function: int gnutls_ocsp_req_init (gnutls_ocsp_req_t * REQ)
     REQ: A pointer to the type to be initialized

     This function will initialize an OCSP request structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_ocsp_req_print
---------------------

 -- Function: int gnutls_ocsp_req_print (gnutls_ocsp_req_const_t REQ,
          gnutls_ocsp_print_formats_t FORMAT, gnutls_datum_t * OUT)
     REQ: The data to be printed

     FORMAT: Indicate the format to use

     OUT: Newly allocated datum with (0) terminated string.

     This function will pretty print a OCSP request, suitable for
     display to a human.

     If the format is ‘GNUTLS_OCSP_PRINT_FULL’ then all fields of the
     request will be output, on multiple lines.

     The output ‘out’ ->data needs to be deallocate using
     ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_ocsp_req_randomize_nonce
-------------------------------

 -- Function: int gnutls_ocsp_req_randomize_nonce (gnutls_ocsp_req_t
          REQ)
     REQ: should contain a ‘gnutls_ocsp_req_t’ type

     This function will add or update an nonce extension to the OCSP
     request with a newly generated random value.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.

gnutls_ocsp_req_set_extension
-----------------------------

 -- Function: int gnutls_ocsp_req_set_extension (gnutls_ocsp_req_t REQ,
          const char * OID, unsigned int CRITICAL, const gnutls_datum_t
          * DATA)
     REQ: should contain a ‘gnutls_ocsp_req_t’ type

     OID: buffer with OID of extension as a string.

     CRITICAL: critical flag, normally false.

     DATA: the extension data

     This function will add an extension to the OCSP request.  Calling
     this function multiple times for the same OID will overwrite values
     from earlier calls.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.

gnutls_ocsp_req_set_nonce
-------------------------

 -- Function: int gnutls_ocsp_req_set_nonce (gnutls_ocsp_req_t REQ,
          unsigned int CRITICAL, const gnutls_datum_t * NONCE)
     REQ: should contain a ‘gnutls_ocsp_req_t’ type

     CRITICAL: critical flag, normally false.

     NONCE: the nonce data

     This function will add an nonce extension to the OCSP request.
     Calling this function multiple times will overwrite values from
     earlier calls.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.

gnutls_ocsp_resp_check_crt
--------------------------

 -- Function: int gnutls_ocsp_resp_check_crt (gnutls_ocsp_resp_const_t
          RESP, unsigned int INDX, gnutls_x509_crt_t CRT)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     INDX: Specifies response number to get.  Use (0) to get the first
     one.

     CRT: The certificate to check

     This function will check whether the OCSP response is about the
     provided certificate.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.

     *Since:* 3.1.3

gnutls_ocsp_resp_deinit
-----------------------

 -- Function: void gnutls_ocsp_resp_deinit (gnutls_ocsp_resp_t RESP)
     RESP: The data to be deinitialized

     This function will deinitialize a OCSP response structure.

gnutls_ocsp_resp_export
-----------------------

 -- Function: int gnutls_ocsp_resp_export (gnutls_ocsp_resp_const_t
          RESP, gnutls_datum_t * DATA)
     RESP: Holds the OCSP response

     DATA: newly allocate buffer holding DER encoded OCSP response

     This function will export the OCSP response to DER format.

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

gnutls_ocsp_resp_export2
------------------------

 -- Function: int gnutls_ocsp_resp_export2 (gnutls_ocsp_resp_const_t
          RESP, gnutls_datum_t * DATA, gnutls_x509_crt_fmt_t FMT)
     RESP: Holds the OCSP response

     DATA: newly allocate buffer holding DER or PEM encoded OCSP
     response

     FMT: DER or PEM

     This function will export the OCSP response to DER or PEM format.

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

     *Since:* 3.6.3

gnutls_ocsp_resp_get_certs
--------------------------

 -- Function: int gnutls_ocsp_resp_get_certs (gnutls_ocsp_resp_const_t
          RESP, gnutls_x509_crt_t ** CERTS, size_t * NCERTS)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     CERTS: newly allocated array with ‘gnutls_x509_crt_t’ certificates

     NCERTS: output variable with number of allocated certs.

     This function will extract the X.509 certificates found in the
     Basic OCSP Response.  The ‘certs’ output variable will hold a newly
     allocated zero-terminated array with X.509 certificates.

     Every certificate in the array needs to be de-allocated with
     ‘gnutls_x509_crt_deinit()’ and the array itself must be freed using
     ‘gnutls_free()’ .

     Both the ‘certs’ and ‘ncerts’ variables may be NULL. Then the
     function will work as normal but will not return the NULL:d
     information.  This can be used to get the number of certificates
     only, or to just get the certificate array without its size.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_ocsp_resp_get_extension
------------------------------

 -- Function: int gnutls_ocsp_resp_get_extension
          (gnutls_ocsp_resp_const_t RESP, unsigned INDX, gnutls_datum_t
          * OID, unsigned int * CRITICAL, gnutls_datum_t * DATA)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     INDX: Specifies which extension OID to get.  Use (0) to get the
     first one.

     OID: will hold newly allocated buffer with OID of extension, may be
     NULL

     CRITICAL: output variable with critical flag, may be NULL.

     DATA: will hold newly allocated buffer with extension data, may be
     NULL

     This function will return all information about the requested
     extension in the OCSP response.  The information returned is the
     OID, the critical flag, and the data itself.  The extension OID
     will be stored as a string.  Any of ‘oid’ , ‘critical’ , and ‘data’
     may be NULL which means that the caller is not interested in
     getting that information back.

     The caller needs to deallocate memory by calling ‘gnutls_free()’ on
     ‘oid’ ->data and ‘data’ ->data.

     Since 3.7.0 ‘oid’ ->size does not account for the terminating null
     byte.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  If you have reached
     the last extension available
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

gnutls_ocsp_resp_get_nonce
--------------------------

 -- Function: int gnutls_ocsp_resp_get_nonce (gnutls_ocsp_resp_const_t
          RESP, unsigned int * CRITICAL, gnutls_datum_t * NONCE)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     CRITICAL: whether nonce extension is marked critical

     NONCE: will hold newly allocated buffer with nonce data

     This function will return the Basic OCSP Response nonce extension
     data.

     The caller needs to deallocate memory by calling ‘gnutls_free()’ on
     ‘nonce’ ->data.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.

gnutls_ocsp_resp_get_produced
-----------------------------

 -- Function: time_t gnutls_ocsp_resp_get_produced
          (gnutls_ocsp_resp_const_t RESP)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     This function will return the time when the OCSP response was
     signed.

     *Returns:* signing time, or (time_t)-1 on error.

gnutls_ocsp_resp_get_responder
------------------------------

 -- Function: int gnutls_ocsp_resp_get_responder
          (gnutls_ocsp_resp_const_t RESP, gnutls_datum_t * DN)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     DN: newly allocated buffer with name

     This function will extract the name of the Basic OCSP Response in
     the provided buffer.  The name will be in the form
     "C=xxxx,O=yyyy,CN=zzzz" as described in RFC2253.  The output string
     will be ASCII or UTF-8 encoded, depending on the certificate data.

     If the responder ID is not a name but a hash, this function will
     return zero and the ‘dn’ elements will be set to ‘NULL’ .

     The caller needs to deallocate memory by calling ‘gnutls_free()’ on
     ‘dn’ ->data.

     This function does not output a fully RFC4514 compliant string, if
     that is required see ‘gnutls_ocsp_resp_get_responder2()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  When no data exist it
     will return success and set ‘dn’ elements to zero.

gnutls_ocsp_resp_get_responder2
-------------------------------

 -- Function: int gnutls_ocsp_resp_get_responder2
          (gnutls_ocsp_resp_const_t RESP, gnutls_datum_t * DN, unsigned
          FLAGS)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     DN: newly allocated buffer with name

     FLAGS: zero or ‘GNUTLS_X509_DN_FLAG_COMPAT’

     This function will extract the name of the Basic OCSP Response in
     the provided buffer.  The name will be in the form
     "C=xxxx,O=yyyy,CN=zzzz" as described in RFC2253.  The output string
     will be ASCII or UTF-8 encoded, depending on the certificate data.

     If the responder ID is not a name but a hash, this function will
     return zero and the ‘dn’ elements will be set to ‘NULL’ .

     The caller needs to deallocate memory by calling ‘gnutls_free()’ on
     ‘dn’ ->data.

     When the flag ‘GNUTLS_X509_DN_FLAG_COMPAT’ is specified, the output
     format will match the format output by previous to 3.5.6 versions
     of GnuTLS which was not not fully RFC4514-compliant.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  When no data exist it
     will return ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ .

gnutls_ocsp_resp_get_responder_raw_id
-------------------------------------

 -- Function: int gnutls_ocsp_resp_get_responder_raw_id
          (gnutls_ocsp_resp_const_t RESP, unsigned TYPE, gnutls_datum_t
          * RAW)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     TYPE: should be ‘GNUTLS_OCSP_RESP_ID_KEY’ or
     ‘GNUTLS_OCSP_RESP_ID_DN’

     RAW: newly allocated buffer with the raw ID

     This function will extract the raw key (or DN) ID of the Basic OCSP
     Response in the provided buffer.  If the responder ID is not a key
     ID then this function will return
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ .

     The caller needs to deallocate memory by calling ‘gnutls_free()’ on
     ‘dn’ ->data.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.

gnutls_ocsp_resp_get_response
-----------------------------

 -- Function: int gnutls_ocsp_resp_get_response
          (gnutls_ocsp_resp_const_t RESP, gnutls_datum_t *
          RESPONSE_TYPE_OID, gnutls_datum_t * RESPONSE)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     RESPONSE_TYPE_OID: newly allocated output buffer with response type
     OID

     RESPONSE: newly allocated output buffer with DER encoded response

     This function will extract the response type OID in and the
     response data from an OCSP response.  Normally the
     ‘response_type_oid’ is always "1.3.6.1.5.5.7.48.1.1" which means
     the ‘response’ should be decoded as a Basic OCSP Response, but
     technically other response types could be used.

     This function is typically only useful when you want to extract the
     response type OID of an response for diagnostic purposes.
     Otherwise ‘gnutls_ocsp_resp_import()’ will decode the basic OCSP
     response part and the caller need not worry about that aspect.

     Since 3.7.0 ‘response_type_oid’ ->size does not account for the
     terminating null byte.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_ocsp_resp_get_signature
------------------------------

 -- Function: int gnutls_ocsp_resp_get_signature
          (gnutls_ocsp_resp_const_t RESP, gnutls_datum_t * SIG)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     SIG: newly allocated output buffer with signature data

     This function will extract the signature field of a OCSP response.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_ocsp_resp_get_signature_algorithm
----------------------------------------

 -- Function: int gnutls_ocsp_resp_get_signature_algorithm
          (gnutls_ocsp_resp_const_t RESP)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     This function will return a value of the ‘gnutls_sign_algorithm_t’
     enumeration that is the signature algorithm that has been used to
     sign the OCSP response.

     *Returns:* a ‘gnutls_sign_algorithm_t’ value, or a negative error
     code on error.

gnutls_ocsp_resp_get_single
---------------------------

 -- Function: int gnutls_ocsp_resp_get_single (gnutls_ocsp_resp_const_t
          RESP, unsigned INDX, gnutls_digest_algorithm_t * DIGEST,
          gnutls_datum_t * ISSUER_NAME_HASH, gnutls_datum_t *
          ISSUER_KEY_HASH, gnutls_datum_t * SERIAL_NUMBER, unsigned int
          * CERT_STATUS, time_t * THIS_UPDATE, time_t * NEXT_UPDATE,
          time_t * REVOCATION_TIME, unsigned int * REVOCATION_REASON)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     INDX: Specifies response number to get.  Use (0) to get the first
     one.

     DIGEST: output variable with ‘gnutls_digest_algorithm_t’ hash
     algorithm

     ISSUER_NAME_HASH: output buffer with hash of issuer's DN

     ISSUER_KEY_HASH: output buffer with hash of issuer's public key

     SERIAL_NUMBER: output buffer with serial number of certificate to
     check

     CERT_STATUS: a certificate status, a ‘gnutls_ocsp_cert_status_t’
     enum.

     THIS_UPDATE: time at which the status is known to be correct.

     NEXT_UPDATE: when newer information will be available, or
     (time_t)-1 if unspecified

     REVOCATION_TIME: when ‘cert_status’ is ‘GNUTLS_OCSP_CERT_REVOKED’ ,
     holds time of revocation.

     REVOCATION_REASON: revocation reason, a ‘gnutls_x509_crl_reason_t’
     enum.

     This function will return the certificate information of the ‘indx’
     'ed response in the Basic OCSP Response ‘resp’ .  The information
     returned corresponds to the OCSP SingleResponse structure except
     the final singleExtensions.

     Each of the pointers to output variables may be NULL to indicate
     that the caller is not interested in that value.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.  If you have reached
     the last CertID available ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’
     will be returned.

gnutls_ocsp_resp_get_status
---------------------------

 -- Function: int gnutls_ocsp_resp_get_status (gnutls_ocsp_resp_const_t
          RESP)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     This function will return the status of a OCSP response, an
     ‘gnutls_ocsp_resp_status_t’ enumeration.

     *Returns:* status of OCSP request as a ‘gnutls_ocsp_resp_status_t’
     , or a negative error code on error.

gnutls_ocsp_resp_get_version
----------------------------

 -- Function: int gnutls_ocsp_resp_get_version (gnutls_ocsp_resp_const_t
          RESP)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     This function will return the version of the Basic OCSP Response.
     Typically this is always 1 indicating version 1.

     *Returns:* version of Basic OCSP response, or a negative error code
     on error.

gnutls_ocsp_resp_import
-----------------------

 -- Function: int gnutls_ocsp_resp_import (gnutls_ocsp_resp_t RESP,
          const gnutls_datum_t * DATA)
     RESP: The data to store the parsed response.

     DATA: DER encoded OCSP response.

     This function will convert the given DER encoded OCSP response to
     the native ‘gnutls_ocsp_resp_t’ format.  It also decodes the Basic
     OCSP Response part, if any.  The output will be stored in ‘resp’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_ocsp_resp_import2
------------------------

 -- Function: int gnutls_ocsp_resp_import2 (gnutls_ocsp_resp_t RESP,
          const gnutls_datum_t * DATA, gnutls_x509_crt_fmt_t FMT)
     RESP: The data to store the parsed response.

     DATA: DER or PEM encoded OCSP response.

     FMT: DER or PEM

     This function will convert the given OCSP response to the native
     ‘gnutls_ocsp_resp_t’ format.  It also decodes the Basic OCSP
     Response part, if any.  The output will be stored in ‘resp’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.3

gnutls_ocsp_resp_init
---------------------

 -- Function: int gnutls_ocsp_resp_init (gnutls_ocsp_resp_t * RESP)
     RESP: A pointer to the type to be initialized

     This function will initialize an OCSP response structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_ocsp_resp_list_import2
-----------------------------

 -- Function: int gnutls_ocsp_resp_list_import2 (gnutls_ocsp_resp_t **
          OCSPS, unsigned int * SIZE, const gnutls_datum_t * RESP_DATA,
          gnutls_x509_crt_fmt_t FORMAT, unsigned int FLAGS)
     OCSPS: Will hold the parsed OCSP response list.

     SIZE: It will contain the size of the list.

     RESP_DATA: The PEM encoded OCSP list.

     FORMAT: One of ‘GNUTLS_X509_FMT_PEM’ or ‘GNUTLS_X509_FMT_DER’

     FLAGS: must be (0) or an OR'd sequence of
     gnutls_certificate_import_flags.

     This function will convert the given PEM encoded OCSP response list
     to the native gnutls_ocsp_resp_t format.  The output will be stored
     in ‘ocsps’ which will be allocated and initialized.

     The OCSP responses should have a header of "OCSP RESPONSE".

     To deinitialize responses, you need to deinitialize each
     ‘gnutls_ocsp_resp_t’ structure independently, and use
     ‘gnutls_free()’ at ‘ocsps’ .

     In PEM files, when no OCSP responses are detected
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Returns:* the number of responses read or a negative error value.

     *Since:* 3.6.3

gnutls_ocsp_resp_print
----------------------

 -- Function: int gnutls_ocsp_resp_print (gnutls_ocsp_resp_const_t RESP,
          gnutls_ocsp_print_formats_t FORMAT, gnutls_datum_t * OUT)
     RESP: The data to be printed

     FORMAT: Indicate the format to use

     OUT: Newly allocated datum with (0) terminated string.

     This function will pretty print a OCSP response, suitable for
     display to a human.

     If the format is ‘GNUTLS_OCSP_PRINT_FULL’ then all fields of the
     response will be output, on multiple lines.

     The output ‘out’ ->data needs to be deallocate using
     ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_ocsp_resp_verify
-----------------------

 -- Function: int gnutls_ocsp_resp_verify (gnutls_ocsp_resp_const_t
          RESP, gnutls_x509_trust_list_t TRUSTLIST, unsigned int *
          VERIFY, unsigned int FLAGS)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     TRUSTLIST: trust anchors as a ‘gnutls_x509_trust_list_t’ type

     VERIFY: output variable with verification status, an
     ‘gnutls_ocsp_verify_reason_t’

     FLAGS: verification flags from ‘gnutls_certificate_verify_flags’

     Verify signature of the Basic OCSP Response against the public key
     in the certificate of a trusted signer.  The ‘trustlist’ should be
     populated with trust anchors.  The function will extract the signer
     certificate from the Basic OCSP Response and will verify it against
     the ‘trustlist’ .  A trusted signer is a certificate that is either
     in ‘trustlist’ , or it is signed directly by a certificate in
     ‘trustlist’ and has the id-ad-ocspSigning Extended Key Usage bit
     set.

     The output ‘verify’ variable will hold verification status codes
     (e.g., ‘GNUTLS_OCSP_VERIFY_SIGNER_NOT_FOUND’ ,
     ‘GNUTLS_OCSP_VERIFY_INSECURE_ALGORITHM’ ) which are only valid if
     the function returned ‘GNUTLS_E_SUCCESS’ .

     Note that the function returns ‘GNUTLS_E_SUCCESS’ even when
     verification failed.  The caller must always inspect the ‘verify’
     variable to find out the verification status.

     The ‘flags’ variable should be 0 for now.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_ocsp_resp_verify_direct
------------------------------

 -- Function: int gnutls_ocsp_resp_verify_direct
          (gnutls_ocsp_resp_const_t RESP, gnutls_x509_crt_t ISSUER,
          unsigned int * VERIFY, unsigned int FLAGS)
     RESP: should contain a ‘gnutls_ocsp_resp_t’ type

     ISSUER: certificate believed to have signed the response

     VERIFY: output variable with verification status, an
     ‘gnutls_ocsp_verify_reason_t’

     FLAGS: verification flags from ‘gnutls_certificate_verify_flags’

     Verify signature of the Basic OCSP Response against the public key
     in the ‘issuer’ certificate.

     The output ‘verify’ variable will hold verification status codes
     (e.g., ‘GNUTLS_OCSP_VERIFY_SIGNER_NOT_FOUND’ ,
     ‘GNUTLS_OCSP_VERIFY_INSECURE_ALGORITHM’ ) which are only valid if
     the function returned ‘GNUTLS_E_SUCCESS’ .

     Note that the function returns ‘GNUTLS_E_SUCCESS’ even when
     verification failed.  The caller must always inspect the ‘verify’
     variable to find out the verification status.

     The ‘flags’ variable should be 0 for now.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.


File: gnutls.info,  Node: PKCS 12 API,  Next: PKCS 11 API,  Prev: OCSP API,  Up: API reference

E.6 PKCS 12 API
===============

The following functions are to be used for PKCS 12 handling.  Their
prototypes lie in ‘gnutls/pkcs12.h’.

gnutls_pkcs12_bag_decrypt
-------------------------

 -- Function: int gnutls_pkcs12_bag_decrypt (gnutls_pkcs12_bag_t BAG,
          const char * PASS)
     BAG: The bag

     PASS: The password used for encryption, must be ASCII.

     This function will decrypt the given encrypted bag and return 0 on
     success.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.

gnutls_pkcs12_bag_deinit
------------------------

 -- Function: void gnutls_pkcs12_bag_deinit (gnutls_pkcs12_bag_t BAG)
     BAG: A pointer to the type to be initialized

     This function will deinitialize a PKCS12 Bag structure.

gnutls_pkcs12_bag_enc_info
--------------------------

 -- Function: int gnutls_pkcs12_bag_enc_info (gnutls_pkcs12_bag_t BAG,
          unsigned int * SCHEMA, unsigned int * CIPHER, void * SALT,
          unsigned int * SALT_SIZE, unsigned int * ITER_COUNT, char **
          OID)
     BAG: The bag

     SCHEMA: indicate the schema as one of ‘gnutls_pkcs_encrypt_flags_t’

     CIPHER: the cipher used as ‘gnutls_cipher_algorithm_t’

     SALT: PBKDF2 salt (if non-NULL then ‘salt_size’ initially holds its
     size)

     SALT_SIZE: PBKDF2 salt size

     ITER_COUNT: PBKDF2 iteration count

     OID: if non-NULL it will contain an allocated null-terminated
     variable with the OID

     This function will provide information on the encryption algorithms
     used in an encrypted bag.  If the structure algorithms are unknown
     the code ‘GNUTLS_E_UNKNOWN_CIPHER_TYPE’ will be returned, and only
     ‘oid’ , will be set.  That is, ‘oid’ will be set on encrypted bags
     whether supported or not.  It must be deinitialized using
     ‘gnutls_free()’ .  The other variables are only set on supported
     structures.

     *Returns:* ‘GNUTLS_E_INVALID_REQUEST’ if the provided bag isn't
     encrypted, ‘GNUTLS_E_UNKNOWN_CIPHER_TYPE’ if the structure's
     encryption isn't supported, or another negative error code in case
     of a failure.  Zero on success.

gnutls_pkcs12_bag_encrypt
-------------------------

 -- Function: int gnutls_pkcs12_bag_encrypt (gnutls_pkcs12_bag_t BAG,
          const char * PASS, unsigned int FLAGS)
     BAG: The bag

     PASS: The password used for encryption, must be ASCII

     FLAGS: should be one of ‘gnutls_pkcs_encrypt_flags_t’ elements
     bitwise or'd

     This function will encrypt the given bag.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error code is returned.

gnutls_pkcs12_bag_get_count
---------------------------

 -- Function: int gnutls_pkcs12_bag_get_count (gnutls_pkcs12_bag_t BAG)
     BAG: The bag

     This function will return the number of the elements within the
     bag.

     *Returns:* Number of elements in bag, or an negative error code on
     error.

gnutls_pkcs12_bag_get_data
--------------------------

 -- Function: int gnutls_pkcs12_bag_get_data (gnutls_pkcs12_bag_t BAG,
          unsigned INDX, gnutls_datum_t * DATA)
     BAG: The bag

     INDX: The element of the bag to get the data from

     DATA: where the bag's data will be.  Should be treated as constant.

     This function will return the bag's data.  The data is a constant
     that is stored into the bag.  Should not be accessed after the bag
     is deleted.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs12_bag_get_friendly_name
-----------------------------------

 -- Function: int gnutls_pkcs12_bag_get_friendly_name
          (gnutls_pkcs12_bag_t BAG, unsigned INDX, char ** NAME)
     BAG: The bag

     INDX: The bag's element to add the id

     NAME: will hold a pointer to the name (to be treated as const)

     This function will return the friendly name, of the specified bag
     element.  The key ID is usually used to distinguish the local
     private key and the certificate pair.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  or a negative error code on
     error.

gnutls_pkcs12_bag_get_key_id
----------------------------

 -- Function: int gnutls_pkcs12_bag_get_key_id (gnutls_pkcs12_bag_t BAG,
          unsigned INDX, gnutls_datum_t * ID)
     BAG: The bag

     INDX: The bag's element to add the id

     ID: where the ID will be copied (to be treated as const)

     This function will return the key ID, of the specified bag element.
     The key ID is usually used to distinguish the local private key and
     the certificate pair.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  or a negative error code on
     error.

gnutls_pkcs12_bag_get_type
--------------------------

 -- Function: int gnutls_pkcs12_bag_get_type (gnutls_pkcs12_bag_t BAG,
          unsigned INDX)
     BAG: The bag

     INDX: The element of the bag to get the type

     This function will return the bag's type.

     *Returns:* On error a negative error value or one of the
     ‘gnutls_pkcs12_bag_type_t’ enumerations.

gnutls_pkcs12_bag_init
----------------------

 -- Function: int gnutls_pkcs12_bag_init (gnutls_pkcs12_bag_t * BAG)
     BAG: A pointer to the type to be initialized

     This function will initialize a PKCS12 bag structure.  PKCS12 Bags
     usually contain private keys, lists of X.509 Certificates and X.509
     Certificate revocation lists.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs12_bag_set_crl
-------------------------

 -- Function: int gnutls_pkcs12_bag_set_crl (gnutls_pkcs12_bag_t BAG,
          gnutls_x509_crl_t CRL)
     BAG: The bag

     CRL: the CRL to be copied.

     This function will insert the given CRL into the bag.  This is just
     a wrapper over ‘gnutls_pkcs12_bag_set_data()’ .

     *Returns:* the index of the added bag on success, or a negative
     error code on failure.

gnutls_pkcs12_bag_set_crt
-------------------------

 -- Function: int gnutls_pkcs12_bag_set_crt (gnutls_pkcs12_bag_t BAG,
          gnutls_x509_crt_t CRT)
     BAG: The bag

     CRT: the certificate to be copied.

     This function will insert the given certificate into the bag.  This
     is just a wrapper over ‘gnutls_pkcs12_bag_set_data()’ .

     *Returns:* the index of the added bag on success, or a negative
     value on failure.

gnutls_pkcs12_bag_set_data
--------------------------

 -- Function: int gnutls_pkcs12_bag_set_data (gnutls_pkcs12_bag_t BAG,
          gnutls_pkcs12_bag_type_t TYPE, const gnutls_datum_t * DATA)
     BAG: The bag

     TYPE: The data's type

     DATA: the data to be copied.

     This function will insert the given data of the given type into the
     bag.

     *Returns:* the index of the added bag on success, or a negative
     value on error.

gnutls_pkcs12_bag_set_friendly_name
-----------------------------------

 -- Function: int gnutls_pkcs12_bag_set_friendly_name
          (gnutls_pkcs12_bag_t BAG, unsigned INDX, const char * NAME)
     BAG: The bag

     INDX: The bag's element to add the id

     NAME: the name

     This function will add the given key friendly name, to the
     specified, by the index, bag element.  The name will be encoded as
     a 'Friendly name' bag attribute, which is usually used to set a
     user name to the local private key and the certificate pair.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  or a negative error code on
     error.

gnutls_pkcs12_bag_set_key_id
----------------------------

 -- Function: int gnutls_pkcs12_bag_set_key_id (gnutls_pkcs12_bag_t BAG,
          unsigned INDX, const gnutls_datum_t * ID)
     BAG: The bag

     INDX: The bag's element to add the id

     ID: the ID

     This function will add the given key ID, to the specified, by the
     index, bag element.  The key ID will be encoded as a 'Local key
     identifier' bag attribute, which is usually used to distinguish the
     local private key and the certificate pair.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.  or a negative error code on
     error.

gnutls_pkcs12_bag_set_privkey
-----------------------------

 -- Function: int gnutls_pkcs12_bag_set_privkey (gnutls_pkcs12_bag_t
          BAG, gnutls_x509_privkey_t PRIVKEY, const char * PASSWORD,
          unsigned FLAGS)
     BAG: The bag

     PRIVKEY: the private key to be copied.

     PASSWORD: the password to protect the key with (may be ‘NULL’ )

     FLAGS: should be one of ‘gnutls_pkcs_encrypt_flags_t’ elements
     bitwise or'd

     This function will insert the given private key into the bag.  This
     is just a wrapper over ‘gnutls_pkcs12_bag_set_data()’ .

     *Returns:* the index of the added bag on success, or a negative
     value on failure.

gnutls_pkcs12_deinit
--------------------

 -- Function: void gnutls_pkcs12_deinit (gnutls_pkcs12_t PKCS12)
     PKCS12: The type to be initialized

     This function will deinitialize a PKCS12 type.

gnutls_pkcs12_export
--------------------

 -- Function: int gnutls_pkcs12_export (gnutls_pkcs12_t PKCS12,
          gnutls_x509_crt_fmt_t FORMAT, void * OUTPUT_DATA, size_t *
          OUTPUT_DATA_SIZE)
     PKCS12: A pkcs12 type

     FORMAT: the format of output params.  One of PEM or DER.

     OUTPUT_DATA: will contain a structure PEM or DER encoded

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will export the pkcs12 structure to DER or PEM
     format.

     If the buffer provided is not long enough to hold the output, then
     *output_data_size will be updated and GNUTLS_E_SHORT_MEMORY_BUFFER
     will be returned.

     If the structure is PEM encoded, it will have a header of "BEGIN
     PKCS12".

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

gnutls_pkcs12_export2
---------------------

 -- Function: int gnutls_pkcs12_export2 (gnutls_pkcs12_t PKCS12,
          gnutls_x509_crt_fmt_t FORMAT, gnutls_datum_t * OUT)
     PKCS12: A pkcs12 type

     FORMAT: the format of output params.  One of PEM or DER.

     OUT: will contain a structure PEM or DER encoded

     This function will export the pkcs12 structure to DER or PEM
     format.

     The output buffer is allocated using ‘gnutls_malloc()’ .

     If the structure is PEM encoded, it will have a header of "BEGIN
     PKCS12".

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

     *Since:* 3.1.3

gnutls_pkcs12_generate_mac
--------------------------

 -- Function: int gnutls_pkcs12_generate_mac (gnutls_pkcs12_t PKCS12,
          const char * PASS)
     PKCS12: A pkcs12 type

     PASS: The password for the MAC

     This function will generate a MAC for the PKCS12 structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs12_generate_mac2
---------------------------

 -- Function: int gnutls_pkcs12_generate_mac2 (gnutls_pkcs12_t PKCS12,
          gnutls_mac_algorithm_t MAC, const char * PASS)
     PKCS12: A pkcs12 type

     MAC: the MAC algorithm to use

     PASS: The password for the MAC

     This function will generate a MAC for the PKCS12 structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs12_generate_mac3
---------------------------

 -- Function: int gnutls_pkcs12_generate_mac3 (gnutls_pkcs12_t PKCS12,
          gnutls_mac_algorithm_t MAC, const char * PASS, unsigned int
          FLAGS)
     PKCS12: A pkcs12 type

     MAC: the MAC algorithm to use

     PASS: The password for the MAC

     FLAGS: an ORed sequence of gnutls_pkcs12_flags_t

     This function will generate a MAC for the PKCS12 structure.

     If ‘flags’ contains ‘GNUTLS_PKCS12_USE_PBMAC1’ , it uses PBMAC1 key
     derivation function instead of the PKCS‘12’ one.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs12_get_bag
---------------------

 -- Function: int gnutls_pkcs12_get_bag (gnutls_pkcs12_t PKCS12, int
          INDX, gnutls_pkcs12_bag_t BAG)
     PKCS12: A pkcs12 type

     INDX: contains the index of the bag to extract

     BAG: An initialized bag, where the contents of the bag will be
     copied

     This function will return a Bag from the PKCS12 structure.

     After the last Bag has been read
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs12_import
--------------------

 -- Function: int gnutls_pkcs12_import (gnutls_pkcs12_t PKCS12, const
          gnutls_datum_t * DATA, gnutls_x509_crt_fmt_t FORMAT, unsigned
          int FLAGS)
     PKCS12: The data to store the parsed PKCS12.

     DATA: The DER or PEM encoded PKCS12.

     FORMAT: One of DER or PEM

     FLAGS: an ORed sequence of gnutls_privkey_pkcs8_flags

     This function will convert the given DER or PEM encoded PKCS12 to
     the native gnutls_pkcs12_t format.  The output will be stored in
     'pkcs12'.

     If the PKCS12 is PEM encoded it should have a header of "PKCS12".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs12_init
------------------

 -- Function: int gnutls_pkcs12_init (gnutls_pkcs12_t * PKCS12)
     PKCS12: A pointer to the type to be initialized

     This function will initialize a PKCS12 type.  PKCS12 structures
     usually contain lists of X.509 Certificates and X.509 Certificate
     revocation lists.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs12_mac_info
----------------------

 -- Function: int gnutls_pkcs12_mac_info (gnutls_pkcs12_t PKCS12,
          unsigned int * MAC, void * SALT, unsigned int * SALT_SIZE,
          unsigned int * ITER_COUNT, char ** OID)
     PKCS12: A pkcs12 type

     MAC: the MAC algorithm used as ‘gnutls_mac_algorithm_t’

     SALT: the salt used for string to key (if non-NULL then ‘salt_size’
     initially holds its size)

     SALT_SIZE: string to key salt size

     ITER_COUNT: string to key iteration count

     OID: if non-NULL it will contain an allocated null-terminated
     variable with the OID

     This function will provide information on the MAC algorithm used in
     a PKCS ‘12’ structure.  If the structure algorithms are unknown the
     code ‘GNUTLS_E_UNKNOWN_HASH_ALGORITHM’ will be returned, and only
     ‘oid’ , will be set.  That is, ‘oid’ will be set on structures with
     a MAC whether supported or not.  It must be deinitialized using
     ‘gnutls_free()’ .  The other variables are only set on supported
     structures.

     *Returns:* ‘GNUTLS_E_INVALID_REQUEST’ if the provided structure
     doesn't contain a MAC, ‘GNUTLS_E_UNKNOWN_HASH_ALGORITHM’ if the
     structure's MAC isn't supported, or another negative error code in
     case of a failure.  Zero on success.

gnutls_pkcs12_set_bag
---------------------

 -- Function: int gnutls_pkcs12_set_bag (gnutls_pkcs12_t PKCS12,
          gnutls_pkcs12_bag_t BAG)
     PKCS12: should contain a gnutls_pkcs12_t type

     BAG: An initialized bag

     This function will insert a Bag into the PKCS12 structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs12_simple_parse
--------------------------

 -- Function: int gnutls_pkcs12_simple_parse (gnutls_pkcs12_t P12, const
          char * PASSWORD, gnutls_x509_privkey_t * KEY,
          gnutls_x509_crt_t ** CHAIN, unsigned int * CHAIN_LEN,
          gnutls_x509_crt_t ** EXTRA_CERTS, unsigned int *
          EXTRA_CERTS_LEN, gnutls_x509_crl_t * CRL, unsigned int FLAGS)
     P12: A pkcs12 type

     PASSWORD: optional password used to decrypt the structure, bags and
     keys.

     KEY: a structure to store the parsed private key.

     CHAIN: the corresponding to key certificate chain (may be ‘NULL’ )

     CHAIN_LEN: will be updated with the number of additional (may be
     ‘NULL’ )

     EXTRA_CERTS: optional pointer to receive an array of additional
     certificates found in the PKCS12 structure (may be ‘NULL’ ).

     EXTRA_CERTS_LEN: will be updated with the number of additional
     certs (may be ‘NULL’ ).

     CRL: an optional structure to store the parsed CRL (may be ‘NULL’
     ).

     FLAGS: should be zero or one of GNUTLS_PKCS12_SP_*

     This function parses a PKCS12 structure in ‘pkcs12’ and extracts
     the private key, the corresponding certificate chain, any
     additional certificates and a CRL. The structures in ‘key’ ,
     ‘chain’ ‘crl’ , and ‘extra_certs’ must not be initialized.

     The ‘extra_certs’ and ‘extra_certs_len’ parameters are optional and
     both may be set to ‘NULL’ .  If either is non-‘NULL’ , then both
     must be set.  The value for ‘extra_certs’ is allocated using
     ‘gnutls_malloc()’ .

     Encrypted PKCS12 bags and PKCS8 private keys are supported, but
     only with password based security and the same password for all
     operations.

     Note that a PKCS12 structure may contain many keys and/or
     certificates, and there is no way to identify which key/certificate
     pair you want.  For this reason this function is useful for PKCS12
     files that contain only one key/certificate pair and/or one CRL.

     If the provided structure has encrypted fields but no password is
     provided then this function returns ‘GNUTLS_E_DECRYPTION_FAILED’ .

     Note that normally the chain constructed does not include self
     signed certificates, to comply with TLS' requirements.  If,
     however, the flag ‘GNUTLS_PKCS12_SP_INCLUDE_SELF_SIGNED’ is
     specified then self signed certificates will be included in the
     chain.

     Prior to using this function the PKCS ‘12’ structure integrity must
     be verified using ‘gnutls_pkcs12_verify_mac()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.0

gnutls_pkcs12_verify_mac
------------------------

 -- Function: int gnutls_pkcs12_verify_mac (gnutls_pkcs12_t PKCS12,
          const char * PASS)
     PKCS12: should contain a gnutls_pkcs12_t type

     PASS: The password for the MAC

     This function will verify the MAC for the PKCS12 structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.


File: gnutls.info,  Node: PKCS 11 API,  Next: TPM API,  Prev: PKCS 12 API,  Up: API reference

E.7 Hardware token via PKCS 11 API
==================================

The following functions are to be used for PKCS 11 handling.  Their
prototypes lie in ‘gnutls/pkcs11.h’.

gnutls_pkcs11_add_provider
--------------------------

 -- Function: int gnutls_pkcs11_add_provider (const char * NAME, const
          char * PARAMS)
     NAME: The filename of the module

     PARAMS: should be NULL or a known string (see description)

     This function will load and add a PKCS 11 module to the module list
     used in gnutls.  After this function is called the module will be
     used for PKCS 11 operations.

     When loading a module to be used for certificate verification, use
     the string 'trusted' as ‘params’ .

     Note that this function is not thread safe.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pkcs11_copy_attached_extension
-------------------------------------

 -- Function: int gnutls_pkcs11_copy_attached_extension (const char *
          TOKEN_URL, gnutls_x509_crt_t CRT, gnutls_datum_t * DATA, const
          char * LABEL, unsigned int FLAGS)
     TOKEN_URL: A PKCS ‘11’ URL specifying a token

     CRT: An X.509 certificate object

     DATA: the attached extension

     LABEL: A name to be used for the attached extension (may be ‘NULL’
     )

     FLAGS: One of GNUTLS_PKCS11_OBJ_FLAG_*

     This function will copy an the attached extension in ‘data’ for the
     certificate provided in ‘crt’ in the PKCS ‘11’ token specified by
     the URL (typically a trust module).  The extension must be in
     RFC5280 Extension format.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.8

gnutls_pkcs11_copy_pubkey
-------------------------

 -- Function: int gnutls_pkcs11_copy_pubkey (const char * TOKEN_URL,
          gnutls_pubkey_t PUBKEY, const char * LABEL, const
          gnutls_datum_t * CID, unsigned int KEY_USAGE, unsigned int
          FLAGS)
     TOKEN_URL: A PKCS ‘11’ URL specifying a token

     PUBKEY: The public key to copy

     LABEL: The name to be used for the stored data

     CID: The CKA_ID to set for the object -if NULL, the ID will be
     derived from the public key

     KEY_USAGE: One of GNUTLS_KEY_*

     FLAGS: One of GNUTLS_PKCS11_OBJ_FLAG_*

     This function will copy a public key object into a PKCS ‘11’ token
     specified by a URL. Valid flags to mark the key:
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_TRUSTED’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_PRIVATE’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_CA’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_ALWAYS_AUTH’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.6

gnutls_pkcs11_copy_secret_key
-----------------------------

 -- Function: int gnutls_pkcs11_copy_secret_key (const char * TOKEN_URL,
          gnutls_datum_t * KEY, const char * LABEL, unsigned int
          KEY_USAGE, unsigned int FLAGS)
     TOKEN_URL: A PKCS ‘11’ URL specifying a token

     KEY: The raw key

     LABEL: A name to be used for the stored data

     KEY_USAGE: One of GNUTLS_KEY_*

     FLAGS: One of GNUTLS_PKCS11_OBJ_FLAG_*

     This function will copy a raw secret (symmetric) key into a PKCS
     ‘11’ token specified by a URL. The key can be marked as sensitive
     or not.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pkcs11_copy_x509_crt
---------------------------

 -- Function: int gnutls_pkcs11_copy_x509_crt (const char * TOKEN_URL,
          gnutls_x509_crt_t CRT, const char * LABEL, unsigned int FLAGS)
     TOKEN_URL: A PKCS ‘11’ URL specifying a token

     CRT: A certificate

     LABEL: A name to be used for the stored data

     FLAGS: One of GNUTLS_PKCS11_OBJ_FLAG_*

     This function will copy a certificate into a PKCS ‘11’ token
     specified by a URL. The certificate can be marked as trusted or
     not.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pkcs11_copy_x509_crt2
----------------------------

 -- Function: int gnutls_pkcs11_copy_x509_crt2 (const char * TOKEN_URL,
          gnutls_x509_crt_t CRT, const char * LABEL, const
          gnutls_datum_t * CID, unsigned int FLAGS)
     TOKEN_URL: A PKCS ‘11’ URL specifying a token

     CRT: The certificate to copy

     LABEL: The name to be used for the stored data

     CID: The CKA_ID to set for the object -if NULL, the ID will be
     derived from the public key

     FLAGS: One of GNUTLS_PKCS11_OBJ_FLAG_*

     This function will copy a certificate into a PKCS ‘11’ token
     specified by a URL. Valid flags to mark the certificate:
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_TRUSTED’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_PRIVATE’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_CA’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_ALWAYS_AUTH’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_pkcs11_copy_x509_privkey
-------------------------------

 -- Function: int gnutls_pkcs11_copy_x509_privkey (const char *
          TOKEN_URL, gnutls_x509_privkey_t KEY, const char * LABEL,
          unsigned int KEY_USAGE, unsigned int FLAGS)
     TOKEN_URL: A PKCS ‘11’ URL specifying a token

     KEY: A private key

     LABEL: A name to be used for the stored data

     KEY_USAGE: One of GNUTLS_KEY_*

     FLAGS: One of GNUTLS_PKCS11_OBJ_* flags

     This function will copy a private key into a PKCS ‘11’ token
     specified by a URL.

     Since 3.6.3 the objects are marked as sensitive by default unless
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_NOT_SENSITIVE’ is specified.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pkcs11_copy_x509_privkey2
--------------------------------

 -- Function: int gnutls_pkcs11_copy_x509_privkey2 (const char *
          TOKEN_URL, gnutls_x509_privkey_t KEY, const char * LABEL,
          const gnutls_datum_t * CID, unsigned int KEY_USAGE, unsigned
          int FLAGS)
     TOKEN_URL: A PKCS ‘11’ URL specifying a token

     KEY: A private key

     LABEL: A name to be used for the stored data

     CID: The CKA_ID to set for the object -if NULL, the ID will be
     derived from the public key

     KEY_USAGE: One of GNUTLS_KEY_*

     FLAGS: One of GNUTLS_PKCS11_OBJ_* flags

     This function will copy a private key into a PKCS ‘11’ token
     specified by a URL.

     Since 3.6.3 the objects are marked as sensitive by default unless
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_NOT_SENSITIVE’ is specified.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_pkcs11_crt_is_known
--------------------------

 -- Function: unsigned gnutls_pkcs11_crt_is_known (const char * URL,
          gnutls_x509_crt_t CERT, unsigned int FLAGS)
     URL: A PKCS 11 url identifying a token

     CERT: is the certificate to find issuer for

     FLAGS: Use zero or flags from ‘GNUTLS_PKCS11_OBJ_FLAG’ .

     This function will check whether the provided certificate is stored
     in the specified token.  This is useful in combination with
     ‘GNUTLS_PKCS11_OBJ_FLAG_RETRIEVE_TRUSTED’ or
     ‘GNUTLS_PKCS11_OBJ_FLAG_RETRIEVE_DISTRUSTED’ , to check whether a
     CA is present or a certificate is distrusted in a trust PKCS ‘11’
     module.

     This function can be used with a ‘url’ of "pkcs11:", and in that
     case all modules will be searched.  To restrict the modules to the
     marked as trusted in p11-kit use the
     ‘GNUTLS_PKCS11_OBJ_FLAG_PRESENT_IN_TRUSTED_MODULE’ flag.

     Note that the flag ‘GNUTLS_PKCS11_OBJ_FLAG_RETRIEVE_DISTRUSTED’ is
     specific to p11-kit trust modules.

     *Returns:* If the certificate exists non-zero is returned,
     otherwise zero.

     *Since:* 3.3.0

gnutls_pkcs11_deinit
--------------------

 -- Function: void gnutls_pkcs11_deinit ( VOID)

     This function will deinitialize the PKCS 11 subsystem in gnutls.
     This function is only needed if you need to deinitialize the
     subsystem without calling ‘gnutls_global_deinit()’ .

     *Since:* 2.12.0

gnutls_pkcs11_delete_url
------------------------

 -- Function: int gnutls_pkcs11_delete_url (const char * OBJECT_URL,
          unsigned int FLAGS)
     OBJECT_URL: The URL of the object to delete.

     FLAGS: One of GNUTLS_PKCS11_OBJ_* flags

     This function will delete objects matching the given URL. Note that
     not all tokens support the delete operation.

     *Returns:* On success, the number of objects deleted is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pkcs11_get_pin_function
------------------------------

 -- Function: gnutls_pin_callback_t gnutls_pkcs11_get_pin_function (void
          ** USERDATA)
     USERDATA: data to be supplied to callback

     This function will return the callback function set using
     ‘gnutls_pkcs11_set_pin_function()’ .

     *Returns:* The function set or NULL otherwise.

     *Since:* 3.1.0

gnutls_pkcs11_get_raw_issuer
----------------------------

 -- Function: int gnutls_pkcs11_get_raw_issuer (const char * URL,
          gnutls_x509_crt_t CERT, gnutls_datum_t * ISSUER,
          gnutls_x509_crt_fmt_t FMT, unsigned int FLAGS)
     URL: A PKCS 11 url identifying a token

     CERT: is the certificate to find issuer for

     ISSUER: Will hold the issuer if any in an allocated buffer.

     FMT: The format of the exported issuer.

     FLAGS: Use zero or flags from ‘GNUTLS_PKCS11_OBJ_FLAG’ .

     This function will return the issuer of a given certificate, if it
     is stored in the token.  By default only marked as trusted issuers
     are returned.  If any issuer should be returned specify
     ‘GNUTLS_PKCS11_OBJ_FLAG_RETRIEVE_ANY’ in ‘flags’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.2.7

gnutls_pkcs11_get_raw_issuer_by_dn
----------------------------------

 -- Function: int gnutls_pkcs11_get_raw_issuer_by_dn (const char * URL,
          const gnutls_datum_t * DN, gnutls_datum_t * ISSUER,
          gnutls_x509_crt_fmt_t FMT, unsigned int FLAGS)
     URL: A PKCS 11 url identifying a token

     DN: is the DN to search for

     ISSUER: Will hold the issuer if any in an allocated buffer.

     FMT: The format of the exported issuer.

     FLAGS: Use zero or flags from ‘GNUTLS_PKCS11_OBJ_FLAG’ .

     This function will return the certificate with the given DN, if it
     is stored in the token.  By default only marked as trusted issuers
     are returned.  If any issuer should be returned specify
     ‘GNUTLS_PKCS11_OBJ_FLAG_RETRIEVE_ANY’ in ‘flags’ .

     The name of the function includes issuer because it can be used to
     discover issuers of certificates.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_pkcs11_get_raw_issuer_by_subject_key_id
----------------------------------------------

 -- Function: int gnutls_pkcs11_get_raw_issuer_by_subject_key_id (const
          char * URL, const gnutls_datum_t * DN, const gnutls_datum_t *
          SPKI, gnutls_datum_t * ISSUER, gnutls_x509_crt_fmt_t FMT,
          unsigned int FLAGS)
     URL: A PKCS 11 url identifying a token

     DN: is the DN to search for (may be ‘NULL’ )

     SPKI: is the subject key ID to search for

     ISSUER: Will hold the issuer if any in an allocated buffer.

     FMT: The format of the exported issuer.

     FLAGS: Use zero or flags from ‘GNUTLS_PKCS11_OBJ_FLAG’ .

     This function will return the certificate with the given DN and
     ‘spki’ , if it is stored in the token.  By default only marked as
     trusted issuers are returned.  If any issuer should be returned
     specify ‘GNUTLS_PKCS11_OBJ_FLAG_RETRIEVE_ANY’ in ‘flags’ .

     The name of the function includes issuer because it can be used to
     discover issuers of certificates.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.2

gnutls_pkcs11_init
------------------

 -- Function: int gnutls_pkcs11_init (unsigned int FLAGS, const char *
          DEPRECATED_CONFIG_FILE)
     FLAGS: An ORed sequence of ‘GNUTLS_PKCS11_FLAG_’ *

     DEPRECATED_CONFIG_FILE: either NULL or the location of a deprecated
     configuration file

     This function will initialize the PKCS 11 subsystem in gnutls.  It
     will read configuration files if ‘GNUTLS_PKCS11_FLAG_AUTO’ is used
     or allow you to independently load PKCS 11 modules using
     ‘gnutls_pkcs11_add_provider()’ if ‘GNUTLS_PKCS11_FLAG_MANUAL’ is
     specified.

     You don't need to call this function since GnuTLS 3.3.0 because it
     is being called during the first request PKCS 11 operation.  That
     call will assume the ‘GNUTLS_PKCS11_FLAG_AUTO’ flag.  If another
     flags are required then it must be called independently prior to
     any PKCS 11 operation.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pkcs11_obj_deinit
------------------------

 -- Function: void gnutls_pkcs11_obj_deinit (gnutls_pkcs11_obj_t OBJ)
     OBJ: The type to be deinitialized

     This function will deinitialize a certificate structure.

     *Since:* 2.12.0

gnutls_pkcs11_obj_export
------------------------

 -- Function: int gnutls_pkcs11_obj_export (gnutls_pkcs11_obj_t OBJ,
          void * OUTPUT_DATA, size_t * OUTPUT_DATA_SIZE)
     OBJ: Holds the object

     OUTPUT_DATA: will contain the object data

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will export the PKCS11 object data.  It is normal for
     data to be inaccessible and in that case ‘GNUTLS_E_INVALID_REQUEST’
     will be returned.

     If the buffer provided is not long enough to hold the output, then
     *output_data_size is updated and GNUTLS_E_SHORT_MEMORY_BUFFER will
     be returned.

     *Returns:* In case of failure a negative error code will be
     returned, and ‘GNUTLS_E_SUCCESS’ (0) on success.

     *Since:* 2.12.0

gnutls_pkcs11_obj_export2
-------------------------

 -- Function: int gnutls_pkcs11_obj_export2 (gnutls_pkcs11_obj_t OBJ,
          gnutls_datum_t * OUT)
     OBJ: Holds the object

     OUT: will contain the object data

     This function will export the PKCS11 object data.  It is normal for
     data to be inaccessible and in that case ‘GNUTLS_E_INVALID_REQUEST’
     will be returned.

     The output buffer is allocated using ‘gnutls_malloc()’ .

     *Returns:* In case of failure a negative error code will be
     returned, and ‘GNUTLS_E_SUCCESS’ (0) on success.

     *Since:* 3.1.3

gnutls_pkcs11_obj_export3
-------------------------

 -- Function: int gnutls_pkcs11_obj_export3 (gnutls_pkcs11_obj_t OBJ,
          gnutls_x509_crt_fmt_t FMT, gnutls_datum_t * OUT)
     OBJ: Holds the object

     FMT: The format of the exported data

     OUT: will contain the object data

     This function will export the PKCS11 object data.  It is normal for
     data to be inaccessible and in that case ‘GNUTLS_E_INVALID_REQUEST’
     will be returned.

     The output buffer is allocated using ‘gnutls_malloc()’ .

     *Returns:* In case of failure a negative error code will be
     returned, and ‘GNUTLS_E_SUCCESS’ (0) on success.

     *Since:* 3.2.7

gnutls_pkcs11_obj_export_url
----------------------------

 -- Function: int gnutls_pkcs11_obj_export_url (gnutls_pkcs11_obj_t OBJ,
          gnutls_pkcs11_url_type_t DETAILED, char ** URL)
     OBJ: Holds the PKCS 11 certificate

     DETAILED: non zero if a detailed URL is required

     URL: will contain an allocated url

     This function will export a URL identifying the given object.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pkcs11_obj_flags_get_str
-------------------------------

 -- Function: char * gnutls_pkcs11_obj_flags_get_str (unsigned int
          FLAGS)
     FLAGS: holds the flags

     This function given an or-sequence of ‘GNUTLS_PKCS11_OBJ_FLAG_MARK’
     , will return an allocated string with its description.  The string
     needs to be deallocated using ‘gnutls_free()’ .

     *Returns:* If flags is zero ‘NULL’ is returned, otherwise an
     allocated string.

     *Since:* 3.3.7

gnutls_pkcs11_obj_get_exts
--------------------------

 -- Function: int gnutls_pkcs11_obj_get_exts (gnutls_pkcs11_obj_t OBJ,
          gnutls_x509_ext_st ** EXTS, unsigned int * EXTS_SIZE, unsigned
          int FLAGS)
     OBJ: should contain a ‘gnutls_pkcs11_obj_t’ type

     EXTS: a pointer to a ‘gnutls_x509_ext_st’ pointer

     EXTS_SIZE: will be updated with the number of ‘exts’

     FLAGS: Or sequence of ‘GNUTLS_PKCS11_OBJ_’ * flags

     This function will return information about attached extensions
     that associate to the provided object (which should be a
     certificate).  The extensions are the attached p11-kit trust module
     extensions.

     Each element of ‘exts’ must be deinitialized using
     ‘gnutls_x509_ext_deinit()’ while ‘exts’ should be deallocated using
     ‘gnutls_free()’ .

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success or a negative error
     code on error.

     *Since:* 3.3.8

gnutls_pkcs11_obj_get_flags
---------------------------

 -- Function: int gnutls_pkcs11_obj_get_flags (gnutls_pkcs11_obj_t OBJ,
          unsigned int * OFLAGS)
     OBJ: The pkcs11 object

     OFLAGS: Will hold the output flags

     This function will return the flags of the object.  The ‘oflags’
     will be flags from ‘gnutls_pkcs11_obj_flags’ .  That is, the
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_’ * flags.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.7

gnutls_pkcs11_obj_get_info
--------------------------

 -- Function: int gnutls_pkcs11_obj_get_info (gnutls_pkcs11_obj_t OBJ,
          gnutls_pkcs11_obj_info_t ITYPE, void * OUTPUT, size_t *
          OUTPUT_SIZE)
     OBJ: should contain a ‘gnutls_pkcs11_obj_t’ type

     ITYPE: Denotes the type of information requested

     OUTPUT: where output will be stored

     OUTPUT_SIZE: contains the maximum size of the output buffer and
     will be overwritten with the actual size.

     This function will return information about the PKCS11 certificate
     such as the label, id as well as token information where the key is
     stored.

     When output is text, a null terminated string is written to
     ‘output’ and its string length is written to ‘output_size’ (without
     null terminator).  If the buffer is too small, ‘output_size’ will
     contain the expected buffer size (with null terminator for text)
     and return ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ .

     In versions previously to 3.6.0 this function included the null
     terminator to ‘output_size’ .  After 3.6.0 the output size doesn't
     include the terminator character.

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success or a negative error
     code on error.

     *Since:* 2.12.0

gnutls_pkcs11_obj_get_ptr
-------------------------

 -- Function: int gnutls_pkcs11_obj_get_ptr (gnutls_pkcs11_obj_t OBJ,
          void ** PTR, void ** SESSION, void ** OHANDLE, unsigned long *
          SLOT_ID, unsigned int FLAGS)
     OBJ: should contain a ‘gnutls_pkcs11_obj_t’ type

     PTR: will contain the CK_FUNCTION_LIST_PTR pointer (may be ‘NULL’ )

     SESSION: will contain the CK_SESSION_HANDLE of the object

     OHANDLE: will contain the CK_OBJECT_HANDLE of the object

     SLOT_ID: the identifier of the slot (may be ‘NULL’ )

     FLAGS: Or sequence of GNUTLS_PKCS11_OBJ_* flags

     Obtains the PKCS‘11’ session handles of an object.  ‘session’ and
     ‘ohandle’ must be deinitialized by the caller.  The returned
     pointers are independent of the ‘obj’ lifetime.

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success or a negative error
     code on error.

     *Since:* 3.6.3

gnutls_pkcs11_obj_get_type
--------------------------

 -- Function: gnutls_pkcs11_obj_type_t gnutls_pkcs11_obj_get_type
          (gnutls_pkcs11_obj_t OBJ)
     OBJ: Holds the PKCS 11 object

     This function will return the type of the object being stored in
     the structure.

     *Returns:* The type of the object

     *Since:* 2.12.0

gnutls_pkcs11_obj_import_url
----------------------------

 -- Function: int gnutls_pkcs11_obj_import_url (gnutls_pkcs11_obj_t OBJ,
          const char * URL, unsigned int FLAGS)
     OBJ: The structure to store the object

     URL: a PKCS 11 url identifying the key

     FLAGS: Or sequence of GNUTLS_PKCS11_OBJ_* flags

     This function will "import" a PKCS 11 URL identifying an object
     (e.g.  certificate) to the ‘gnutls_pkcs11_obj_t’ type.  This does
     not involve any parsing (such as X.509 or OpenPGP) since the
     ‘gnutls_pkcs11_obj_t’ is format agnostic.  Only data are
     transferred.

     If the flag ‘GNUTLS_PKCS11_OBJ_FLAG_OVERWRITE_TRUSTMOD_EXT’ is
     specified any certificate read, will have its extensions
     overwritten by any stapled extensions in the trust module.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pkcs11_obj_init
----------------------

 -- Function: int gnutls_pkcs11_obj_init (gnutls_pkcs11_obj_t * OBJ)
     OBJ: A pointer to the type to be initialized

     This function will initialize a pkcs11 certificate structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pkcs11_obj_list_import_url3
----------------------------------

 -- Function: int gnutls_pkcs11_obj_list_import_url3
          (gnutls_pkcs11_obj_t * P_LIST, unsigned int * N_LIST, const
          char * URL, unsigned int FLAGS)
     P_LIST: An uninitialized object list (may be ‘NULL’ )

     N_LIST: Initially should hold the maximum size of the list.  Will
     contain the actual size.

     URL: A PKCS 11 url identifying a set of objects

     FLAGS: Or sequence of GNUTLS_PKCS11_OBJ_* flags

     This function will initialize and set values to an object list by
     using all objects identified by a PKCS 11 URL.

     This function will enumerate all the objects specified by the
     PKCS‘11’ URL provided.  It expects an already allocated ‘p_list’
     which has * ‘n_list’ elements, and that value will be updated to
     the actual number of present objects.  The ‘p_list’ objects will be
     initialized and set by this function.  To obtain a list of all
     available objects use a ‘url’ of 'pkcs11:'.

     All returned objects must be deinitialized using
     ‘gnutls_pkcs11_obj_deinit()’ .

     The supported in this function ‘flags’ are
     ‘GNUTLS_PKCS11_OBJ_FLAG_LOGIN’ , ‘GNUTLS_PKCS11_OBJ_FLAG_LOGIN_SO’
     , ‘GNUTLS_PKCS11_OBJ_FLAG_PRESENT_IN_TRUSTED_MODULE’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_CRT’ , ‘GNUTLS_PKCS11_OBJ_FLAG_PUBKEY’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_PRIVKEY’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_WITH_PRIVKEY’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_CA’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_TRUSTED’ , and since 3.5.1 the
     ‘GNUTLS_PKCS11_OBJ_FLAG_OVERWRITE_TRUSTMOD_EXT’ .

     On versions of GnuTLS prior to 3.4.0 the equivalent function was
     ‘gnutls_pkcs11_obj_list_import_url()’ .  That is also available on
     this version as a macro which maps to this function.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_pkcs11_obj_list_import_url4
----------------------------------

 -- Function: int gnutls_pkcs11_obj_list_import_url4
          (gnutls_pkcs11_obj_t ** P_LIST, unsigned int * N_LIST, const
          char * URL, unsigned int FLAGS)
     P_LIST: An uninitialized object list (may be NULL)

     N_LIST: It will contain the size of the list.

     URL: A PKCS 11 url identifying a set of objects

     FLAGS: Or sequence of GNUTLS_PKCS11_OBJ_* flags

     This function will enumerate all the objects specified by the
     PKCS‘11’ URL provided.  It will initialize and set values to the
     object pointer list ( ‘p_list’ ) provided.  To obtain a list of all
     available objects use a ‘url’ of 'pkcs11:'.

     All returned objects must be deinitialized using
     ‘gnutls_pkcs11_obj_deinit()’ , and ‘p_list’ must be deinitialized
     using ‘gnutls_free()’ .

     The supported in this function ‘flags’ are
     ‘GNUTLS_PKCS11_OBJ_FLAG_LOGIN’ , ‘GNUTLS_PKCS11_OBJ_FLAG_LOGIN_SO’
     , ‘GNUTLS_PKCS11_OBJ_FLAG_PRESENT_IN_TRUSTED_MODULE’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_CRT’ , ‘GNUTLS_PKCS11_OBJ_FLAG_PUBKEY’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_PRIVKEY’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_WITH_PRIVKEY’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_CA’ ,
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_TRUSTED’ , and since 3.5.1 the
     ‘GNUTLS_PKCS11_OBJ_FLAG_OVERWRITE_TRUSTMOD_EXT’ .

     On versions of GnuTLS prior to 3.4.0 the equivalent function was
     ‘gnutls_pkcs11_obj_list_import_url2()’ .  That is also available on
     this version as a macro which maps to this function.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_pkcs11_obj_set_info
--------------------------

 -- Function: int gnutls_pkcs11_obj_set_info (gnutls_pkcs11_obj_t OBJ,
          gnutls_pkcs11_obj_info_t ITYPE, const void * DATA, size_t
          DATA_SIZE, unsigned FLAGS)
     OBJ: should contain a ‘gnutls_pkcs11_obj_t’ type

     ITYPE: Denotes the type of information to be set

     DATA: the data to set

     DATA_SIZE: the size of data

     FLAGS: Or sequence of GNUTLS_PKCS11_OBJ_* flags

     This function will set attributes on the provided object.
     Available options for ‘itype’ are ‘GNUTLS_PKCS11_OBJ_LABEL’ ,
     ‘GNUTLS_PKCS11_OBJ_ID_HEX’ , and ‘GNUTLS_PKCS11_OBJ_ID’ .

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success or a negative error
     code on error.

     *Since:* 3.4.0

gnutls_pkcs11_obj_set_pin_function
----------------------------------

 -- Function: void gnutls_pkcs11_obj_set_pin_function
          (gnutls_pkcs11_obj_t OBJ, gnutls_pin_callback_t FN, void *
          USERDATA)
     OBJ: The object structure

     FN: the callback

     USERDATA: data associated with the callback

     This function will set a callback function to be used when required
     to access the object.  This function overrides the global set using
     ‘gnutls_pkcs11_set_pin_function()’ .

     *Since:* 3.1.0

gnutls_pkcs11_privkey_cpy
-------------------------

 -- Function: int gnutls_pkcs11_privkey_cpy (gnutls_pkcs11_privkey_t
          DST, gnutls_pkcs11_privkey_t SRC)
     DST: The destination key, which should be initialized.

     SRC: The source key

     This function will copy a private key from source to destination
     key.  Destination has to be initialized.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_pkcs11_privkey_deinit
----------------------------

 -- Function: void gnutls_pkcs11_privkey_deinit (gnutls_pkcs11_privkey_t
          KEY)
     KEY: the key to be deinitialized

     This function will deinitialize a private key structure.

gnutls_pkcs11_privkey_export_pubkey
-----------------------------------

 -- Function: int gnutls_pkcs11_privkey_export_pubkey
          (gnutls_pkcs11_privkey_t PKEY, gnutls_x509_crt_fmt_t FMT,
          gnutls_datum_t * DATA, unsigned int FLAGS)
     PKEY: The private key

     FMT: the format of output params.  PEM or DER.

     DATA: will hold the public key

     FLAGS: should be zero

     This function will extract the public key (modulus and public
     exponent) from the private key specified by the ‘url’ private key.
     This public key will be stored in ‘pubkey’ in the format specified
     by ‘fmt’ .  ‘pubkey’ should be deinitialized using ‘gnutls_free()’
     .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.7

gnutls_pkcs11_privkey_export_url
--------------------------------

 -- Function: int gnutls_pkcs11_privkey_export_url
          (gnutls_pkcs11_privkey_t KEY, gnutls_pkcs11_url_type_t
          DETAILED, char ** URL)
     KEY: Holds the PKCS 11 key

     DETAILED: non zero if a detailed URL is required

     URL: will contain an allocated url

     This function will export a URL identifying the given key.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs11_privkey_generate
------------------------------

 -- Function: int gnutls_pkcs11_privkey_generate (const char * URL,
          gnutls_pk_algorithm_t PK, unsigned int BITS, const char *
          LABEL, unsigned int FLAGS)
     URL: a token URL

     PK: the public key algorithm

     BITS: the security bits

     LABEL: a label

     FLAGS: should be zero

     This function will generate a private key in the specified by the
     ‘url’ token.  The private key will be generate within the token and
     will not be exportable.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_pkcs11_privkey_generate2
-------------------------------

 -- Function: int gnutls_pkcs11_privkey_generate2 (const char * URL,
          gnutls_pk_algorithm_t PK, unsigned int BITS, const char *
          LABEL, gnutls_x509_crt_fmt_t FMT, gnutls_datum_t * PUBKEY,
          unsigned int FLAGS)
     URL: a token URL

     PK: the public key algorithm

     BITS: the security bits

     LABEL: a label

     FMT: the format of output params.  PEM or DER

     PUBKEY: will hold the public key (may be ‘NULL’ )

     FLAGS: zero or an OR'ed sequence of ‘GNUTLS_PKCS11_OBJ_FLAGs’

     This function will generate a private key in the specified by the
     ‘url’ token.  The private key will be generate within the token and
     will not be exportable.  This function will store the DER-encoded
     public key in the SubjectPublicKeyInfo format in ‘pubkey’ .  The
     ‘pubkey’ should be deinitialized using ‘gnutls_free()’ .

     Note that when generating an elliptic curve key, the curve can be
     substituted in the place of the bits parameter using the
     ‘GNUTLS_CURVE_TO_BITS()’ macro.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.5

gnutls_pkcs11_privkey_generate3
-------------------------------

 -- Function: int gnutls_pkcs11_privkey_generate3 (const char * URL,
          gnutls_pk_algorithm_t PK, unsigned int BITS, const char *
          LABEL, const gnutls_datum_t * CID, gnutls_x509_crt_fmt_t FMT,
          gnutls_datum_t * PUBKEY, unsigned int KEY_USAGE, unsigned int
          FLAGS)
     URL: a token URL

     PK: the public key algorithm

     BITS: the security bits

     LABEL: a label

     CID: The CKA_ID to use for the new object

     FMT: the format of output params.  PEM or DER

     PUBKEY: will hold the public key (may be ‘NULL’ )

     KEY_USAGE: One of GNUTLS_KEY_*

     FLAGS: zero or an OR'ed sequence of ‘GNUTLS_PKCS11_OBJ_FLAGs’

     This function will generate a private key in the specified by the
     ‘url’ token.  The private key will be generate within the token and
     will not be exportable.  This function will store the DER-encoded
     public key in the SubjectPublicKeyInfo format in ‘pubkey’ .  The
     ‘pubkey’ should be deinitialized using ‘gnutls_free()’ .

     Note that when generating an elliptic curve key, the curve can be
     substituted in the place of the bits parameter using the
     ‘GNUTLS_CURVE_TO_BITS()’ macro.

     Since 3.6.3 the objects are marked as sensitive by default unless
     ‘GNUTLS_PKCS11_OBJ_FLAG_MARK_NOT_SENSITIVE’ is specified.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_pkcs11_privkey_get_info
------------------------------

 -- Function: int gnutls_pkcs11_privkey_get_info
          (gnutls_pkcs11_privkey_t PKEY, gnutls_pkcs11_obj_info_t ITYPE,
          void * OUTPUT, size_t * OUTPUT_SIZE)
     PKEY: should contain a ‘gnutls_pkcs11_privkey_t’ type

     ITYPE: Denotes the type of information requested

     OUTPUT: where output will be stored

     OUTPUT_SIZE: contains the maximum size of the output and will be
     overwritten with actual

     This function will return information about the PKCS 11 private key
     such as the label, id as well as token information where the key is
     stored.  When output is text it returns null terminated string
     although ‘output_size’ contains the size of the actual data only.

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success or a negative error
     code on error.

gnutls_pkcs11_privkey_get_pk_algorithm
--------------------------------------

 -- Function: int gnutls_pkcs11_privkey_get_pk_algorithm
          (gnutls_pkcs11_privkey_t KEY, unsigned int * BITS)
     KEY: should contain a ‘gnutls_pkcs11_privkey_t’ type

     BITS: if bits is non null it will hold the size of the parameters'
     in bits

     This function will return the public key algorithm of a private
     key.

     *Returns:* a member of the ‘gnutls_pk_algorithm_t’ enumeration on
     success, or a negative error code on error.

gnutls_pkcs11_privkey_import_url
--------------------------------

 -- Function: int gnutls_pkcs11_privkey_import_url
          (gnutls_pkcs11_privkey_t PKEY, const char * URL, unsigned int
          FLAGS)
     PKEY: The private key

     URL: a PKCS 11 url identifying the key

     FLAGS: Or sequence of GNUTLS_PKCS11_OBJ_* flags

     This function will "import" a PKCS 11 URL identifying a private key
     to the ‘gnutls_pkcs11_privkey_t’ type.  In reality since in most
     cases keys cannot be exported, the private key structure is being
     associated with the available operations on the token.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs11_privkey_init
--------------------------

 -- Function: int gnutls_pkcs11_privkey_init (gnutls_pkcs11_privkey_t *
          KEY)
     KEY: A pointer to the type to be initialized

     This function will initialize an private key structure.  This
     structure can be used for accessing an underlying PKCS‘11’ object.

     In versions of GnuTLS later than 3.5.11 the object is protected
     using locks and a single ‘gnutls_pkcs11_privkey_t’ can be re-used
     by many threads.  However, for performance it is recommended to
     utilize one object per key per thread.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs11_privkey_set_pin_function
--------------------------------------

 -- Function: void gnutls_pkcs11_privkey_set_pin_function
          (gnutls_pkcs11_privkey_t KEY, gnutls_pin_callback_t FN, void *
          USERDATA)
     KEY: The private key

     FN: the callback

     USERDATA: data associated with the callback

     This function will set a callback function to be used when required
     to access the object.  This function overrides the global set using
     ‘gnutls_pkcs11_set_pin_function()’ .

     *Since:* 3.1.0

gnutls_pkcs11_privkey_status
----------------------------

 -- Function: unsigned gnutls_pkcs11_privkey_status
          (gnutls_pkcs11_privkey_t KEY)
     KEY: Holds the key

     Checks the status of the private key token.

     *Returns:* this function will return non-zero if the token holding
     the private key is still available (inserted), and zero otherwise.

     *Since:* 3.1.9

gnutls_pkcs11_reinit
--------------------

 -- Function: int gnutls_pkcs11_reinit ( VOID)

     This function will reinitialize the PKCS 11 subsystem in gnutls.
     This is required by PKCS 11 when an application uses ‘fork()’ .
     The reinitialization function must be called on the child.

     Note that since GnuTLS 3.3.0, the reinitialization of the PKCS ‘11’
     subsystem occurs automatically after fork.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_pkcs11_set_pin_function
------------------------------

 -- Function: void gnutls_pkcs11_set_pin_function (gnutls_pin_callback_t
          FN, void * USERDATA)
     FN: The PIN callback, a ‘gnutls_pin_callback_t()’ function.

     USERDATA: data to be supplied to callback

     This function will set a callback function to be used when a PIN is
     required for PKCS 11 operations.  See ‘gnutls_pin_callback_t()’ on
     how the callback should behave.

     *Since:* 2.12.0

gnutls_pkcs11_set_token_function
--------------------------------

 -- Function: void gnutls_pkcs11_set_token_function
          (gnutls_pkcs11_token_callback_t FN, void * USERDATA)
     FN: The token callback

     USERDATA: data to be supplied to callback

     This function will set a callback function to be used when a token
     needs to be inserted to continue PKCS 11 operations.

     *Since:* 2.12.0

gnutls_pkcs11_token_check_mechanism
-----------------------------------

 -- Function: unsigned gnutls_pkcs11_token_check_mechanism (const char *
          URL, unsigned long MECHANISM, void * PTR, unsigned PSIZE,
          unsigned FLAGS)
     URL: should contain a PKCS 11 URL

     MECHANISM: The PKCS ‘11’ mechanism ID

     PTR: if set it should point to a CK_MECHANISM_INFO struct

     PSIZE: the size of CK_MECHANISM_INFO struct (for safety)

     FLAGS: must be zero

     This function will return whether a mechanism is supported by the
     given token.  If the mechanism is supported and ‘ptr’ is set, it
     will be updated with the token information.

     *Returns:* Non-zero if the mechanism is supported or zero
     otherwise.

     *Since:* 3.6.0

gnutls_pkcs11_token_get_flags
-----------------------------

 -- Function: int gnutls_pkcs11_token_get_flags (const char * URL,
          unsigned int * FLAGS)
     URL: should contain a PKCS 11 URL

     FLAGS: The output flags (GNUTLS_PKCS11_TOKEN_*)

     This function will return information about the PKCS 11 token
     flags.

     The supported flags are: ‘GNUTLS_PKCS11_TOKEN_HW’ and
     ‘GNUTLS_PKCS11_TOKEN_TRUSTED’ .

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success or a negative error
     code on error.

     *Since:* 2.12.0

gnutls_pkcs11_token_get_info
----------------------------

 -- Function: int gnutls_pkcs11_token_get_info (const char * URL,
          gnutls_pkcs11_token_info_t TTYPE, void * OUTPUT, size_t *
          OUTPUT_SIZE)
     URL: should contain a PKCS 11 URL

     TTYPE: Denotes the type of information requested

     OUTPUT: where output will be stored

     OUTPUT_SIZE: contains the maximum size of the output buffer and
     will be overwritten with the actual size.

     This function will return information about the PKCS 11 token such
     as the label, id, etc.

     When output is text, a null terminated string is written to
     ‘output’ and its string length is written to ‘output_size’ (without
     null terminator).  If the buffer is too small, ‘output_size’ will
     contain the expected buffer size (with null terminator for text)
     and return ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ .

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success or a negative error
     code on error.

     *Since:* 2.12.0

gnutls_pkcs11_token_get_mechanism
---------------------------------

 -- Function: int gnutls_pkcs11_token_get_mechanism (const char * URL,
          unsigned int IDX, unsigned long * MECHANISM)
     URL: should contain a PKCS 11 URL

     IDX: The index of the mechanism

     MECHANISM: The PKCS ‘11’ mechanism ID

     This function will return the names of the supported mechanisms by
     the token.  It should be called with an increasing index until it
     return GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE.

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success or a negative error
     code on error.

     *Since:* 2.12.0

gnutls_pkcs11_token_get_ptr
---------------------------

 -- Function: int gnutls_pkcs11_token_get_ptr (const char * URL, void **
          PTR, unsigned long * SLOT_ID, unsigned int FLAGS)
     URL: should contain a PKCS‘11’ URL identifying a token

     PTR: will contain the CK_FUNCTION_LIST_PTR pointer

     SLOT_ID: will contain the slot_id (may be ‘NULL’ )

     FLAGS: should be zero

     This function will return the function pointer of the specified
     token by the URL. The returned pointers are valid until gnutls is
     deinitialized, c.f.  ‘_global_deinit()’ .

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success or a negative error
     code on error.

     *Since:* 3.6.3

gnutls_pkcs11_token_get_random
------------------------------

 -- Function: int gnutls_pkcs11_token_get_random (const char *
          TOKEN_URL, void * RNDDATA, size_t LEN)
     TOKEN_URL: A PKCS ‘11’ URL specifying a token

     RNDDATA: A pointer to the memory area to be filled with random data

     LEN: The number of bytes of randomness to request

     This function will get random data from the given token.  It will
     store rnddata and fill the memory pointed to by rnddata with len
     random bytes from the token.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs11_token_get_url
---------------------------

 -- Function: int gnutls_pkcs11_token_get_url (unsigned int SEQ,
          gnutls_pkcs11_url_type_t DETAILED, char ** URL)
     SEQ: sequence number starting from 0

     DETAILED: non zero if a detailed URL is required

     URL: will contain an allocated url

     This function will return the URL for each token available in
     system.  The url has to be released using ‘gnutls_free()’

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ if the sequence number
     exceeds the available tokens, otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pkcs11_token_init
------------------------

 -- Function: int gnutls_pkcs11_token_init (const char * TOKEN_URL,
          const char * SO_PIN, const char * LABEL)
     TOKEN_URL: A PKCS ‘11’ URL specifying a token

     SO_PIN: Security Officer's PIN

     LABEL: A name to be used for the token

     This function will initialize (format) a token.  If the token is at
     a factory defaults state the security officer's PIN given will be
     set to be the default.  Otherwise it should match the officer's
     PIN.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs11_token_set_pin
---------------------------

 -- Function: int gnutls_pkcs11_token_set_pin (const char * TOKEN_URL,
          const char * OLDPIN, const char * NEWPIN, unsigned int FLAGS)
     TOKEN_URL: A PKCS ‘11’ URL specifying a token

     OLDPIN: old user's PIN

     NEWPIN: new user's PIN

     FLAGS: one of ‘gnutls_pin_flag_t’ .

     This function will modify or set a user or administrator's PIN for
     the given token.  If it is called to set a PIN for first time the
     oldpin must be ‘NULL’ .  When setting the admin's PIN with the
     ‘GNUTLS_PIN_SO’ flag, the ‘oldpin’ value must be provided (this
     requirement is relaxed after GnuTLS 3.6.5 since which the PIN will
     be requested if missing).

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_pkcs11_type_get_name
---------------------------

 -- Function: const char * gnutls_pkcs11_type_get_name
          (gnutls_pkcs11_obj_type_t TYPE)
     TYPE: Holds the PKCS 11 object type, a ‘gnutls_pkcs11_obj_type_t’ .

     This function will return a human readable description of the
     PKCS11 object type ‘obj’ .  It will return "Unknown" for unknown
     types.

     *Returns:* human readable string labeling the PKCS11 object type
     ‘type’ .

     *Since:* 2.12.0

gnutls_x509_crt_import_pkcs11
-----------------------------

 -- Function: int gnutls_x509_crt_import_pkcs11 (gnutls_x509_crt_t CRT,
          gnutls_pkcs11_obj_t PKCS11_CRT)
     CRT: A certificate of type ‘gnutls_x509_crt_t’

     PKCS11_CRT: A PKCS 11 object that contains a certificate

     This function will import a PKCS 11 certificate to a
     ‘gnutls_x509_crt_t’ structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_x509_crt_list_import_pkcs11
----------------------------------

 -- Function: int gnutls_x509_crt_list_import_pkcs11 (gnutls_x509_crt_t
          * CERTS, unsigned int CERT_MAX, gnutls_pkcs11_obj_t *const
          OBJS, unsigned int FLAGS)
     CERTS: A list of certificates of type ‘gnutls_x509_crt_t’

     CERT_MAX: The maximum size of the list

     OBJS: A list of PKCS 11 objects

     FLAGS: 0 for now

     This function will import a PKCS 11 certificate list to a list of
     ‘gnutls_x509_crt_t’ type.  These must not be initialized.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0


File: gnutls.info,  Node: TPM API,  Next: Abstract key API,  Prev: PKCS 11 API,  Up: API reference

E.8 TPM API
===========

The following functions are to be used for TPM handling.  Their
prototypes lie in ‘gnutls/tpm.h’.

gnutls_tpm_get_registered
-------------------------

 -- Function: int gnutls_tpm_get_registered (gnutls_tpm_key_list_t *
          LIST)
     LIST: a list to store the keys

     This function will get a list of stored keys in the TPM. The uuid
     of those keys

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.0

gnutls_tpm_key_list_deinit
--------------------------

 -- Function: void gnutls_tpm_key_list_deinit (gnutls_tpm_key_list_t
          LIST)
     LIST: a list of the keys

     This function will deinitialize the list of stored keys in the TPM.

     *Since:* 3.1.0

gnutls_tpm_key_list_get_url
---------------------------

 -- Function: int gnutls_tpm_key_list_get_url (gnutls_tpm_key_list_t
          LIST, unsigned int IDX, char ** URL, unsigned int FLAGS)
     LIST: a list of the keys

     IDX: The index of the key (starting from zero)

     URL: The URL to be returned

     FLAGS: should be zero

     This function will return for each given index a URL of the
     corresponding key.  If the provided index is out of bounds then
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ is returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.0

gnutls_tpm_privkey_delete
-------------------------

 -- Function: int gnutls_tpm_privkey_delete (const char * URL, const
          char * SRK_PASSWORD)
     URL: the URL describing the key

     SRK_PASSWORD: a password for the SRK key

     This function will unregister the private key from the TPM chip.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.0

gnutls_tpm_privkey_generate
---------------------------

 -- Function: int gnutls_tpm_privkey_generate (gnutls_pk_algorithm_t PK,
          unsigned int BITS, const char * SRK_PASSWORD, const char *
          KEY_PASSWORD, gnutls_tpmkey_fmt_t FORMAT,
          gnutls_x509_crt_fmt_t PUB_FORMAT, gnutls_datum_t * PRIVKEY,
          gnutls_datum_t * PUBKEY, unsigned int FLAGS)
     PK: the public key algorithm

     BITS: the security bits

     SRK_PASSWORD: a password to protect the exported key (optional)

     KEY_PASSWORD: the password for the TPM (optional)

     FORMAT: the format of the private key

     PUB_FORMAT: the format of the public key

     PRIVKEY: the generated key

     PUBKEY: the corresponding public key (may be null)

     FLAGS: should be a list of GNUTLS_TPM_* flags

     This function will generate a private key in the TPM chip.  The
     private key will be generated within the chip and will be exported
     in a wrapped with TPM's master key form.  Furthermore the wrapped
     key can be protected with the provided ‘password’ .

     Note that bits in TPM is quantized value.  If the input value is
     not one of the allowed values, then it will be quantized to one of
     512, 1024, 2048, 4096, 8192 and 16384.

     Allowed flags are:

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.0


File: gnutls.info,  Node: Abstract key API,  Next: Socket specific API,  Prev: TPM API,  Up: API reference

E.9 Abstract key API
====================

The following functions are to be used for abstract key handling.  Their
prototypes lie in ‘gnutls/abstract.h’.

gnutls_certificate_set_key
--------------------------

 -- Function: int gnutls_certificate_set_key
          (gnutls_certificate_credentials_t RES, const char ** NAMES,
          int NAMES_SIZE, gnutls_pcert_st * PCERT_LIST, int
          PCERT_LIST_SIZE, gnutls_privkey_t KEY)
     RES: is a ‘gnutls_certificate_credentials_t’ type.

     NAMES: is an array of DNS names belonging to the public-key (NULL
     if none)

     NAMES_SIZE: holds the size of the names list

     PCERT_LIST: contains a certificate list (chain) or raw public-key

     PCERT_LIST_SIZE: holds the size of the certificate list

     KEY: is a ‘gnutls_privkey_t’ key corresponding to the first
     public-key in pcert_list

     This function sets a public/private key pair in the
     gnutls_certificate_credentials_t type.  The given public key may be
     encapsulated in a certificate or can be given as a raw key.  This
     function may be called more than once, in case multiple key pairs
     exist for the server.  For clients that want to send more than
     their own end- entity certificate (e.g., also an intermediate CA
     cert), the full certificate chain must be provided in ‘pcert_list’
     .

     Note that the ‘key’ will become part of the credentials structure
     and must not be deallocated.  It will be automatically deallocated
     when the ‘res’ structure is deinitialized.

     If this function fails, the ‘res’ structure is at an undefined
     state and it must not be reused to load other keys or certificates.

     Note that, this function by default returns zero on success and a
     negative value on error.  Since 3.5.6, when the flag
     ‘GNUTLS_CERTIFICATE_API_V2’ is set using
     ‘gnutls_certificate_set_flags()’ it returns an index (greater or
     equal to zero).  That index can be used for other functions to
     refer to the added key-pair.

     Since GnuTLS 3.6.6 this function also handles raw public keys.

     *Returns:* On success this functions returns zero, and otherwise a
     negative value on error (see above for modifying that behavior).

     *Since:* 3.0

gnutls_certificate_set_retrieve_function2
-----------------------------------------

 -- Function: void gnutls_certificate_set_retrieve_function2
          (gnutls_certificate_credentials_t CRED,
          gnutls_certificate_retrieve_function2 * FUNC)
     CRED: is a ‘gnutls_certificate_credentials_t’ type.

     FUNC: is the callback function

     This function sets a callback to be called in order to retrieve the
     certificate to be used in the handshake.  The callback will take
     control only if a certificate is requested by the peer.

     The callback's function prototype is: int
     (*callback)(gnutls_session_t, const gnutls_datum_t* req_ca_dn, int
     nreqs, const gnutls_pk_algorithm_t* pk_algos, int pk_algos_length,
     gnutls_pcert_st** pcert, unsigned int *pcert_length,
     gnutls_privkey_t * pkey);

     ‘req_ca_dn’ is only used in X.509 certificates.  Contains a list
     with the CA names that the server considers trusted.  This is a
     hint and typically the client should send a certificate that is
     signed by one of these CAs.  These names, when available, are DER
     encoded.  To get a more meaningful value use the function
     ‘gnutls_x509_rdn_get()’ .

     ‘pk_algos’ contains a list with server's acceptable public key
     algorithms.  The certificate returned should support the server's
     given algorithms.

     ‘pcert’ should contain a single certificate and public key or a
     list of them.

     ‘pcert_length’ is the size of the previous list.

     ‘pkey’ is the private key.

     If the callback function is provided then gnutls will call it, in
     the handshake, after the certificate request message has been
     received.  All the provided by the callback values will not be
     released or modified by gnutls.

     In server side pk_algos and req_ca_dn are NULL.

     The callback function should set the certificate list to be sent,
     and return 0 on success.  If no certificate was selected then the
     number of certificates should be set to zero.  The value (-1)
     indicates error and the handshake will be terminated.  If both
     certificates are set in the credentials and a callback is
     available, the callback takes predence.

     *Since:* 3.0

gnutls_certificate_set_retrieve_function3
-----------------------------------------

 -- Function: void gnutls_certificate_set_retrieve_function3
          (gnutls_certificate_credentials_t CRED,
          gnutls_certificate_retrieve_function3 * FUNC)
     CRED: is a ‘gnutls_certificate_credentials_t’ type.

     FUNC: is the callback function

     This function sets a callback to be called in order to retrieve the
     certificate and OCSP responses to be used in the handshake.  ‘func’
     will be called only if the peer requests a certificate either
     during handshake or during post-handshake authentication.

     The callback's function prototype is defined in 'abstract.h':

     int gnutls_certificate_retrieve_function3( gnutls_session_t, const
     struct gnutls_cert_retr_st *info, gnutls_pcert_st **certs, unsigned
     int *certs_length, gnutls_ocsp_data_st **ocsp, unsigned int
     *ocsp_length, gnutls_privkey_t *privkey, unsigned int *flags);

     The info field of the callback contains: ‘req_ca_dn’ which is a
     list with the CA names that the server considers trusted.  This is
     a hint and typically the client should send a certificate that is
     signed by one of these CAs.  These names, when available, are DER
     encoded.  To get a more meaningful value use the function
     ‘gnutls_x509_rdn_get()’ .  ‘pk_algos’ contains a list with server's
     acceptable public key algorithms.  The certificate returned should
     support the server's given algorithms.

     The callback should fill-in the following values:

     ‘certs’ should contain an allocated list of certificates and public
     keys.  ‘certs_length’ is the size of the previous list.  ‘ocsp’
     should contain an allocated list of OCSP responses.  ‘ocsp_length’
     is the size of the previous list.  ‘privkey’ is the private key.

     If flags in the callback are set to ‘GNUTLS_CERT_RETR_DEINIT_ALL’
     then all provided values must be allocated using ‘gnutls_malloc()’
     , and will be released by gnutls; otherwise they will not be
     touched by gnutls.

     The callback function should set the certificate and OCSP response
     list to be sent, and return 0 on success.  If no certificates are
     available, the ‘certs_length’ and ‘ocsp_length’ should be set to
     zero.  The return value (-1) indicates error and the handshake will
     be terminated.  If both certificates are set in the credentials and
     a callback is available, the callback takes predence.

     Raw public-keys: In case raw public-keys are negotiated as
     certificate type, certificates that would normally hold the
     public-key material are not available.  In that case, ‘certs’
     contains an allocated list with only the public key.  Since there
     is no certificate, there is also no certificate status.  Therefore,
     OCSP information should not be set.

     *Since:* 3.6.3

gnutls_pcert_deinit
-------------------

 -- Function: void gnutls_pcert_deinit (gnutls_pcert_st * PCERT)
     PCERT: The structure to be deinitialized

     This function will deinitialize a pcert structure.

     *Since:* 3.0

gnutls_pcert_export_openpgp
---------------------------

 -- Function: int gnutls_pcert_export_openpgp (gnutls_pcert_st * PCERT,
          gnutls_openpgp_crt_t * CRT)
     PCERT: The pcert structure.

     CRT: An initialized ‘gnutls_openpgp_crt_t’ .

     This function is no-op.

     *Returns:* ‘GNUTLS_E_UNIMPLEMENTED_FEATURE’ .

     *Since:* 3.4.0

gnutls_pcert_export_x509
------------------------

 -- Function: int gnutls_pcert_export_x509 (gnutls_pcert_st * PCERT,
          gnutls_x509_crt_t * CRT)
     PCERT: The pcert structure.

     CRT: An initialized ‘gnutls_x509_crt_t’ .

     Converts the given ‘gnutls_pcert_t’ type into a ‘gnutls_x509_crt_t’
     .  This function only works if the type of ‘pcert’ is
     ‘GNUTLS_CRT_X509’ .  When successful, the value written to ‘crt’
     must be freed with ‘gnutls_x509_crt_deinit()’ when no longer
     needed.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_pcert_import_openpgp
---------------------------

 -- Function: int gnutls_pcert_import_openpgp (gnutls_pcert_st * PCERT,
          gnutls_openpgp_crt_t CRT, unsigned int FLAGS)
     PCERT: The pcert structure

     CRT: The raw certificate to be imported

     FLAGS: zero for now

     This function is no-op.

     *Returns:* ‘GNUTLS_E_UNIMPLEMENTED_FEATURE’ .

     *Since:* 3.0

gnutls_pcert_import_openpgp_raw
-------------------------------

 -- Function: int gnutls_pcert_import_openpgp_raw (gnutls_pcert_st *
          PCERT, const gnutls_datum_t * CERT, gnutls_openpgp_crt_fmt_t
          FORMAT, gnutls_openpgp_keyid_t KEYID, unsigned int FLAGS)
     PCERT: The pcert structure

     CERT: The raw certificate to be imported

     FORMAT: The format of the certificate

     KEYID: The key ID to use (NULL for the master key)

     FLAGS: zero for now

     This function is no-op.

     *Returns:* ‘GNUTLS_E_UNIMPLEMENTED_FEATURE’ .

     *Since:* 3.0

gnutls_pcert_import_rawpk
-------------------------

 -- Function: int gnutls_pcert_import_rawpk (gnutls_pcert_st * PCERT,
          gnutls_pubkey_t PUBKEY, unsigned int FLAGS)
     PCERT: The pcert structure to import the data into.

     PUBKEY: The raw public-key in ‘gnutls_pubkey_t’ format to be
     imported

     FLAGS: zero for now

     This convenience function will import (i.e.  convert) the given raw
     public key ‘pubkey’ into a ‘gnutls_pcert_st’ structure.  The
     structure must be deinitialized afterwards using
     ‘gnutls_pcert_deinit()’ .  The given ‘pubkey’ must not be
     deinitialized because it will be associated with the given ‘pcert’
     structure and will be deinitialized with it.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.6

gnutls_pcert_import_rawpk_raw
-----------------------------

 -- Function: int gnutls_pcert_import_rawpk_raw (gnutls_pcert_st *
          PCERT, const gnutls_datum_t * RAWPUBKEY, gnutls_x509_crt_fmt_t
          FORMAT, unsigned int KEY_USAGE, unsigned int FLAGS)
     PCERT: The pcert structure to import the data into.

     RAWPUBKEY: The raw public-key in ‘gnutls_datum_t’ format to be
     imported.

     FORMAT: The format of the raw public-key.  DER or PEM.

     KEY_USAGE: An ORed sequence of ‘GNUTLS_KEY_’ * flags.

     FLAGS: zero for now

     This convenience function will import (i.e.  convert) the given raw
     public key ‘rawpubkey’ into a ‘gnutls_pcert_st’ structure.  The
     structure must be deinitialized afterwards using
     ‘gnutls_pcert_deinit()’ .  Note that the caller is responsible for
     freeing ‘rawpubkey’ .  All necessary values will be copied into
     ‘pcert’ .

     Key usage (as defined by X.509 extension (2.5.29.15)) can be
     explicitly set because there is no certificate structure around the
     key to define this value.  See for more info
     ‘gnutls_x509_crt_get_key_usage()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.6

gnutls_pcert_import_x509
------------------------

 -- Function: int gnutls_pcert_import_x509 (gnutls_pcert_st * PCERT,
          gnutls_x509_crt_t CRT, unsigned int FLAGS)
     PCERT: The pcert structure

     CRT: The certificate to be imported

     FLAGS: zero for now

     This convenience function will import the given certificate to a
     ‘gnutls_pcert_st’ structure.  The structure must be deinitialized
     afterwards using ‘gnutls_pcert_deinit()’ ;

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_pcert_import_x509_list
-----------------------------

 -- Function: int gnutls_pcert_import_x509_list (gnutls_pcert_st *
          PCERT_LIST, gnutls_x509_crt_t * CRT, unsigned * NCRT, unsigned
          int FLAGS)
     PCERT_LIST: The structures to store the certificates; must not
     contain initialized ‘gnutls_pcert_st’ structures.

     CRT: The certificates to be imported

     NCRT: The number of certificates in ‘crt’ ; will be updated if
     necessary

     FLAGS: zero or ‘GNUTLS_X509_CRT_LIST_SORT’

     This convenience function will import the given certificates to an
     already allocated set of ‘gnutls_pcert_st’ structures.  The
     structures must be deinitialized afterwards using
     ‘gnutls_pcert_deinit()’ .  ‘pcert_list’ should contain space for at
     least ‘ncrt’ elements.

     In the case ‘GNUTLS_X509_CRT_LIST_SORT’ is specified and that
     function cannot sort the list, ‘GNUTLS_E_CERTIFICATE_LIST_UNSORTED’
     will be returned.  Currently sorting can fail if the list size
     exceeds an internal constraint (16).

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_pcert_import_x509_raw
----------------------------

 -- Function: int gnutls_pcert_import_x509_raw (gnutls_pcert_st * PCERT,
          const gnutls_datum_t * CERT, gnutls_x509_crt_fmt_t FORMAT,
          unsigned int FLAGS)
     PCERT: The pcert structure

     CERT: The raw certificate to be imported

     FORMAT: The format of the certificate

     FLAGS: zero for now

     This convenience function will import the given certificate to a
     ‘gnutls_pcert_st’ structure.  The structure must be deinitialized
     afterwards using ‘gnutls_pcert_deinit()’ ;

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_pcert_list_import_x509_file
----------------------------------

 -- Function: int gnutls_pcert_list_import_x509_file (gnutls_pcert_st *
          PCERT_LIST, unsigned * PCERT_LIST_SIZE, const char * FILE,
          gnutls_x509_crt_fmt_t FORMAT, gnutls_pin_callback_t PIN_FN,
          void * PIN_FN_USERDATA, unsigned int FLAGS)
     PCERT_LIST: The structures to store the certificates; must not
     contain initialized ‘gnutls_pcert_st’ structures.

     PCERT_LIST_SIZE: Initially must hold the maximum number of certs.
     It will be updated with the number of certs available.

     FILE: A file or supported URI with the certificates to load

     FORMAT: ‘GNUTLS_X509_FMT_DER’ or ‘GNUTLS_X509_FMT_PEM’ if a file is
     given

     PIN_FN: a PIN callback if not globally set

     PIN_FN_USERDATA: parameter for the PIN callback

     FLAGS: zero or flags from ‘gnutls_certificate_import_flags’

     This convenience function will import a certificate chain from the
     given file or supported URI to ‘gnutls_pcert_st’ structures.  The
     structures must be deinitialized afterwards using
     ‘gnutls_pcert_deinit()’ .

     This function will always return a sorted certificate chain.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value; if the ‘pcert’ list doesn't have
     enough space ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ will be returned.

     *Since:* 3.6.3

gnutls_pcert_list_import_x509_raw
---------------------------------

 -- Function: int gnutls_pcert_list_import_x509_raw (gnutls_pcert_st *
          PCERT_LIST, unsigned int * PCERT_LIST_SIZE, const
          gnutls_datum_t * DATA, gnutls_x509_crt_fmt_t FORMAT, unsigned
          int FLAGS)
     PCERT_LIST: The structures to store the certificates; must not
     contain initialized ‘gnutls_pcert_st’ structures.

     PCERT_LIST_SIZE: Initially must hold the maximum number of certs.
     It will be updated with the number of certs available.

     DATA: The certificates.

     FORMAT: One of DER or PEM.

     FLAGS: must be (0) or an OR'd sequence of
     gnutls_certificate_import_flags.

     This function will import the provided DER or PEM encoded
     certificates to an already allocated set of ‘gnutls_pcert_st’
     structures.  The structures must be deinitialized afterwards using
     ‘gnutls_pcert_deinit()’ .  ‘pcert_list’ should contain space for at
     least ‘pcert_list_size’ elements.

     If the Certificate is PEM encoded it should have a header of "X509
     CERTIFICATE", or "CERTIFICATE".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value; if the ‘pcert’ list doesn't have
     enough space ‘GNUTLS_E_SHORT_MEMORY_BUFFER’ will be returned.

     *Since:* 3.0

gnutls_privkey_decrypt_data
---------------------------

 -- Function: int gnutls_privkey_decrypt_data (gnutls_privkey_t KEY,
          unsigned int FLAGS, const gnutls_datum_t * CIPHERTEXT,
          gnutls_datum_t * PLAINTEXT)
     KEY: Holds the key

     FLAGS: zero for now

     CIPHERTEXT: holds the data to be decrypted

     PLAINTEXT: will contain the decrypted data, allocated with
     ‘gnutls_malloc()’

     This function will decrypt the given data using the algorithm
     supported by the private key.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_privkey_decrypt_data2
----------------------------

 -- Function: int gnutls_privkey_decrypt_data2 (gnutls_privkey_t KEY,
          unsigned int FLAGS, const gnutls_datum_t * CIPHERTEXT,
          unsigned char * PLAINTEXT, size_t PLAINTEXT_SIZE)
     KEY: Holds the key

     FLAGS: zero for now

     CIPHERTEXT: holds the data to be decrypted

     PLAINTEXT: a preallocated buffer that will be filled with the
     plaintext

     PLAINTEXT_SIZE: in/out size of the plaintext

     This function will decrypt the given data using the algorithm
     supported by the private key.  Unlike with
     ‘gnutls_privkey_decrypt_data()’ this function operates in constant
     time and constant memory access.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.5

gnutls_privkey_deinit
---------------------

 -- Function: void gnutls_privkey_deinit (gnutls_privkey_t KEY)
     KEY: The key to be deinitialized

     This function will deinitialize a private key structure.

     *Since:* 2.12.0

gnutls_privkey_derive_secret
----------------------------

 -- Function: int gnutls_privkey_derive_secret (gnutls_privkey_t
          PRIVKEY, gnutls_pubkey_t PUBKEY, const gnutls_datum_t * NONCE,
          gnutls_datum_t * SECRET, unsigned int FLAGS)
     PRIVKEY: a private key of type ‘gnutls_privkey_t’

     PUBKEY: a public key of type ‘gnutls_pubkey_t’

     NONCE: an optional nonce value

     SECRET: where shared secret will be stored

     FLAGS: must be zero

     This function will calculate a shared secret from our ‘privkey’ and
     peer's ‘pubkey’ .  The result will be stored in ‘secret’ , whose
     data member should be freed after use using ‘gnutls_free()’ .
     ‘privkey’ and ‘pubkey’ must be backed by the X.509 keys.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.8.2

gnutls_privkey_export_dh_raw
----------------------------

 -- Function: int gnutls_privkey_export_dh_raw (gnutls_privkey_t KEY,
          gnutls_dh_params_t PARAMS, gnutls_datum_t * Y, gnutls_datum_t
          * X, unsigned int FLAGS)
     KEY: Holds the private key

     PARAMS: will hold the Diffie-Hellman parameters (optional), must be
     initialized

     Y: will hold the y (optional)

     X: will hold the x

     FLAGS: flags from ‘gnutls_abstract_export_flags_t’

     This function will export the Diffie-Hellman private key parameter
     found in the given ‘gnutls_privkey_t’ structure.  The new parameter
     will be allocated using ‘gnutls_malloc()’ and will be stored in the
     appropriate datum.

     To retrieve other parameters common in both public key and private
     key, use ‘gnutls_dh_params_export_raw()’ .

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.8.2

gnutls_privkey_export_dsa_raw
-----------------------------

 -- Function: int gnutls_privkey_export_dsa_raw (gnutls_privkey_t KEY,
          gnutls_datum_t * P, gnutls_datum_t * Q, gnutls_datum_t * G,
          gnutls_datum_t * Y, gnutls_datum_t * X)
     KEY: Holds the public key

     P: will hold the p

     Q: will hold the q

     G: will hold the g

     Y: will hold the y

     X: will hold the x

     This function will export the DSA private key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.3.0

gnutls_privkey_export_dsa_raw2
------------------------------

 -- Function: int gnutls_privkey_export_dsa_raw2 (gnutls_privkey_t KEY,
          gnutls_datum_t * P, gnutls_datum_t * Q, gnutls_datum_t * G,
          gnutls_datum_t * Y, gnutls_datum_t * X, unsigned int FLAGS)
     KEY: Holds the public key

     P: will hold the p

     Q: will hold the q

     G: will hold the g

     Y: will hold the y

     X: will hold the x

     FLAGS: flags from ‘gnutls_abstract_export_flags_t’

     This function will export the DSA private key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.6.0

gnutls_privkey_export_ecc_raw
-----------------------------

 -- Function: int gnutls_privkey_export_ecc_raw (gnutls_privkey_t KEY,
          gnutls_ecc_curve_t * CURVE, gnutls_datum_t * X, gnutls_datum_t
          * Y, gnutls_datum_t * K)
     KEY: Holds the public key

     CURVE: will hold the curve

     X: will hold the x-coordinate

     Y: will hold the y-coordinate

     K: will hold the private key

     This function will export the ECC private key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     In EdDSA curves the ‘y’ parameter will be ‘NULL’ and the other
     parameters will be in the native format for the curve.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.3.0

gnutls_privkey_export_ecc_raw2
------------------------------

 -- Function: int gnutls_privkey_export_ecc_raw2 (gnutls_privkey_t KEY,
          gnutls_ecc_curve_t * CURVE, gnutls_datum_t * X, gnutls_datum_t
          * Y, gnutls_datum_t * K, unsigned int FLAGS)
     KEY: Holds the public key

     CURVE: will hold the curve

     X: will hold the x-coordinate

     Y: will hold the y-coordinate

     K: will hold the private key

     FLAGS: flags from ‘gnutls_abstract_export_flags_t’

     This function will export the ECC private key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     In EdDSA curves the ‘y’ parameter will be ‘NULL’ and the other
     parameters will be in the native format for the curve.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.6.0

gnutls_privkey_export_gost_raw2
-------------------------------

 -- Function: int gnutls_privkey_export_gost_raw2 (gnutls_privkey_t KEY,
          gnutls_ecc_curve_t * CURVE, gnutls_digest_algorithm_t *
          DIGEST, gnutls_gost_paramset_t * PARAMSET, gnutls_datum_t * X,
          gnutls_datum_t * Y, gnutls_datum_t * K, unsigned int FLAGS)
     KEY: Holds the public key

     CURVE: will hold the curve

     DIGEST: will hold the digest

     PARAMSET: will hold the GOST parameter set ID

     X: will hold the x-coordinate

     Y: will hold the y-coordinate

     K: will hold the private key

     FLAGS: flags from ‘gnutls_abstract_export_flags_t’

     This function will export the GOST private key's parameters found
     in the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Note:* parameters will be stored with least significant byte
     first.  On version 3.6.3 this was incorrectly returned in
     big-endian format.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.6.3

gnutls_privkey_export_openpgp
-----------------------------

 -- Function: int gnutls_privkey_export_openpgp (gnutls_privkey_t PKEY,
          gnutls_openpgp_privkey_t * KEY)
     PKEY: The private key

     KEY: Location for the key to be exported.

     This function is no-op.

     *Returns:* ‘GNUTLS_E_UNIMPLEMENTED_FEATURE’ .

     *Since:* 3.4.0

gnutls_privkey_export_pkcs11
----------------------------

 -- Function: int gnutls_privkey_export_pkcs11 (gnutls_privkey_t PKEY,
          gnutls_pkcs11_privkey_t * KEY)
     PKEY: The private key

     KEY: Location for the key to be exported.

     Converts the given abstract private key to a
     ‘gnutls_pkcs11_privkey_t’ type.  The key must be of type
     ‘GNUTLS_PRIVKEY_PKCS11’ .  The key returned in ‘key’ must be
     deinitialized with ‘gnutls_pkcs11_privkey_deinit()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_privkey_export_rsa_raw
-----------------------------

 -- Function: int gnutls_privkey_export_rsa_raw (gnutls_privkey_t KEY,
          gnutls_datum_t * M, gnutls_datum_t * E, gnutls_datum_t * D,
          gnutls_datum_t * P, gnutls_datum_t * Q, gnutls_datum_t * U,
          gnutls_datum_t * E1, gnutls_datum_t * E2)
     KEY: Holds the certificate

     M: will hold the modulus

     E: will hold the public exponent

     D: will hold the private exponent

     P: will hold the first prime (p)

     Q: will hold the second prime (q)

     U: will hold the coefficient

     E1: will hold e1 = d mod (p-1)

     E2: will hold e2 = d mod (q-1)

     This function will export the RSA private key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.  For
     EdDSA keys, the ‘y’ value should be ‘NULL’ .

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.3.0

gnutls_privkey_export_rsa_raw2
------------------------------

 -- Function: int gnutls_privkey_export_rsa_raw2 (gnutls_privkey_t KEY,
          gnutls_datum_t * M, gnutls_datum_t * E, gnutls_datum_t * D,
          gnutls_datum_t * P, gnutls_datum_t * Q, gnutls_datum_t * U,
          gnutls_datum_t * E1, gnutls_datum_t * E2, unsigned int FLAGS)
     KEY: Holds the certificate

     M: will hold the modulus

     E: will hold the public exponent

     D: will hold the private exponent

     P: will hold the first prime (p)

     Q: will hold the second prime (q)

     U: will hold the coefficient

     E1: will hold e1 = d mod (p-1)

     E2: will hold e2 = d mod (q-1)

     FLAGS: flags from ‘gnutls_abstract_export_flags_t’

     This function will export the RSA private key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.6.0

gnutls_privkey_export_x509
--------------------------

 -- Function: int gnutls_privkey_export_x509 (gnutls_privkey_t PKEY,
          gnutls_x509_privkey_t * KEY)
     PKEY: The private key

     KEY: Location for the key to be exported.

     Converts the given abstract private key to a
     ‘gnutls_x509_privkey_t’ type.  The abstract key must be of type
     ‘GNUTLS_PRIVKEY_X509’ .  The input ‘key’ must not be initialized.
     The key returned in ‘key’ should be deinitialized using
     ‘gnutls_x509_privkey_deinit()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_privkey_generate
-----------------------

 -- Function: int gnutls_privkey_generate (gnutls_privkey_t PKEY,
          gnutls_pk_algorithm_t ALGO, unsigned int BITS, unsigned int
          FLAGS)
     PKEY: An initialized private key

     ALGO: is one of the algorithms in ‘gnutls_pk_algorithm_t’ .

     BITS: the size of the parameters to generate

     FLAGS: Must be zero or flags from ‘gnutls_privkey_flags_t’ .

     This function will generate a random private key.  Note that this
     function must be called on an initialized private key.

     The flag ‘GNUTLS_PRIVKEY_FLAG_PROVABLE’ instructs the key
     generation process to use algorithms like Shawe-Taylor (from FIPS
     PUB186-4) which generate provable parameters out of a seed for RSA
     and DSA keys.  See ‘gnutls_privkey_generate2()’ for more
     information.

     Note that when generating an elliptic curve key, the curve can be
     substituted in the place of the bits parameter using the
     ‘GNUTLS_CURVE_TO_BITS()’ macro.  The input to the macro is any
     curve from ‘gnutls_ecc_curve_t’ .

     For DSA keys, if the subgroup size needs to be specified check the
     ‘GNUTLS_SUBGROUP_TO_BITS()’ macro.

     It is recommended to do not set the number of ‘bits’ directly, use
     ‘gnutls_sec_param_to_pk_bits()’ instead .

     See also ‘gnutls_privkey_generate2()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_privkey_generate2
------------------------

 -- Function: int gnutls_privkey_generate2 (gnutls_privkey_t PKEY,
          gnutls_pk_algorithm_t ALGO, unsigned int BITS, unsigned int
          FLAGS, const gnutls_keygen_data_st * DATA, unsigned DATA_SIZE)
     PKEY: The private key

     ALGO: is one of the algorithms in ‘gnutls_pk_algorithm_t’ .

     BITS: the size of the modulus

     FLAGS: Must be zero or flags from ‘gnutls_privkey_flags_t’ .

     DATA: Allow specifying ‘gnutls_keygen_data_st’ types such as the
     seed to be used.

     DATA_SIZE: The number of ‘data’ available.

     This function will generate a random private key.  Note that this
     function must be called on an initialized private key.

     The flag ‘GNUTLS_PRIVKEY_FLAG_PROVABLE’ instructs the key
     generation process to use algorithms like Shawe-Taylor (from FIPS
     PUB186-4) which generate provable parameters out of a seed for RSA
     and DSA keys.  On DSA keys the PQG parameters are generated using
     the seed, while on RSA the two primes.  To specify an explicit seed
     (by default a random seed is used), use the ‘data’ with a
     ‘GNUTLS_KEYGEN_SEED’ type.

     Note that when generating an elliptic curve key, the curve can be
     substituted in the place of the bits parameter using the
     ‘GNUTLS_CURVE_TO_BITS()’ macro.

     To export the generated keys in memory or in files it is
     recommended to use the PKCS‘8’ form as it can handle all key types,
     and can store additional parameters such as the seed, in case of
     provable RSA or DSA keys.  Generated keys can be exported in memory
     using ‘gnutls_privkey_export_x509()’ , and then with
     ‘gnutls_x509_privkey_export2_pkcs8()’ .

     If key generation is part of your application, avoid setting the
     number of bits directly, and instead use
     ‘gnutls_sec_param_to_pk_bits()’ .  That way the generated keys will
     adapt to the security levels of the underlying GnuTLS library.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.0

gnutls_privkey_get_pk_algorithm
-------------------------------

 -- Function: int gnutls_privkey_get_pk_algorithm (gnutls_privkey_t KEY,
          unsigned int * BITS)
     KEY: should contain a ‘gnutls_privkey_t’ type

     BITS: If set will return the number of bits of the parameters (may
     be NULL)

     This function will return the public key algorithm of a private key
     and if possible will return a number of bits that indicates the
     security parameter of the key.

     *Returns:* a member of the ‘gnutls_pk_algorithm_t’ enumeration on
     success, or a negative error code on error.

     *Since:* 2.12.0

gnutls_privkey_get_seed
-----------------------

 -- Function: int gnutls_privkey_get_seed (gnutls_privkey_t KEY,
          gnutls_digest_algorithm_t * DIGEST, void * SEED, size_t *
          SEED_SIZE)
     KEY: should contain a ‘gnutls_privkey_t’ type

     DIGEST: if non-NULL it will contain the digest algorithm used for
     key generation (if applicable)

     SEED: where seed will be copied to

     SEED_SIZE: originally holds the size of ‘seed’ , will be updated
     with actual size

     This function will return the seed that was used to generate the
     given private key.  That function will succeed only if the key was
     generated as a provable key.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.5.0

gnutls_privkey_get_spki
-----------------------

 -- Function: int gnutls_privkey_get_spki (gnutls_privkey_t PRIVKEY,
          gnutls_x509_spki_t SPKI, unsigned int FLAGS)
     PRIVKEY: a public key of type ‘gnutls_privkey_t’

     SPKI: a SubjectPublicKeyInfo structure of type
     ‘gnutls_privkey_spki_t’

     FLAGS: must be zero

     This function will return the public key information if available.
     The provided ‘spki’ must be initialized.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.0

gnutls_privkey_get_type
-----------------------

 -- Function: gnutls_privkey_type_t gnutls_privkey_get_type
          (gnutls_privkey_t KEY)
     KEY: should contain a ‘gnutls_privkey_t’ type

     This function will return the type of the private key.  This is
     actually the type of the subsystem used to set this private key.

     *Returns:* a member of the ‘gnutls_privkey_type_t’ enumeration on
     success, or a negative error code on error.

     *Since:* 2.12.0

gnutls_privkey_import_dh_raw
----------------------------

 -- Function: int gnutls_privkey_import_dh_raw (gnutls_privkey_t KEY,
          const gnutls_dh_params_t PARAMS, const gnutls_datum_t * Y,
          const gnutls_datum_t * X)
     KEY: The structure to store the parsed key

     PARAMS: holds the ‘gnutls_dh_params_t’

     Y: holds the y (optional)

     X: holds the x

     This function will convert the given Diffie-Hellman raw parameters
     to the native ‘gnutls_privkey_t’ format.  The output will be stored
     in ‘key’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.8.2

gnutls_privkey_import_dsa_raw
-----------------------------

 -- Function: int gnutls_privkey_import_dsa_raw (gnutls_privkey_t KEY,
          const gnutls_datum_t * P, const gnutls_datum_t * Q, const
          gnutls_datum_t * G, const gnutls_datum_t * Y, const
          gnutls_datum_t * X)
     KEY: The structure to store the parsed key

     P: holds the p

     Q: holds the q

     G: holds the g

     Y: holds the y (optional)

     X: holds the x

     This function will convert the given DSA raw parameters to the
     native ‘gnutls_privkey_t’ format.  The output will be stored in
     ‘key’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_privkey_import_ecc_raw
-----------------------------

 -- Function: int gnutls_privkey_import_ecc_raw (gnutls_privkey_t KEY,
          gnutls_ecc_curve_t CURVE, const gnutls_datum_t * X, const
          gnutls_datum_t * Y, const gnutls_datum_t * K)
     KEY: The key

     CURVE: holds the curve

     X: holds the x-coordinate

     Y: holds the y-coordinate

     K: holds the k (private key)

     This function will convert the given elliptic curve parameters to
     the native ‘gnutls_privkey_t’ format.  The output will be stored in
     ‘key’ .

     In EdDSA curves the ‘y’ parameter should be ‘NULL’ and the ‘x’ and
     ‘k’ parameters must be in the native format for the curve.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_privkey_import_ext
-------------------------

 -- Function: int gnutls_privkey_import_ext (gnutls_privkey_t PKEY,
          gnutls_pk_algorithm_t PK, void * USERDATA,
          gnutls_privkey_sign_func SIGN_FUNC,
          gnutls_privkey_decrypt_func DECRYPT_FUNC, unsigned int FLAGS)
     PKEY: The private key

     PK: The public key algorithm

     USERDATA: private data to be provided to the callbacks

     SIGN_FUNC: callback for signature operations

     DECRYPT_FUNC: callback for decryption operations

     FLAGS: Flags for the import

     This function will associate the given callbacks with the
     ‘gnutls_privkey_t’ type.  At least one of the two callbacks must be
     non-null.

     Note that the signing function is supposed to "raw" sign data,
     i.e., without any hashing or preprocessing.  In case of RSA the
     DigestInfo will be provided, and the signing function is expected
     to do the PKCS ‘1’ 1.5 padding and the exponentiation.

     See also ‘gnutls_privkey_import_ext3()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_privkey_import_ext2
--------------------------

 -- Function: int gnutls_privkey_import_ext2 (gnutls_privkey_t PKEY,
          gnutls_pk_algorithm_t PK, void * USERDATA,
          gnutls_privkey_sign_func SIGN_FN, gnutls_privkey_decrypt_func
          DECRYPT_FN, gnutls_privkey_deinit_func DEINIT_FN, unsigned int
          FLAGS)
     PKEY: The private key

     PK: The public key algorithm

     USERDATA: private data to be provided to the callbacks

     SIGN_FN: callback for signature operations

     DECRYPT_FN: callback for decryption operations

     DEINIT_FN: a deinitialization function

     FLAGS: Flags for the import

     This function will associate the given callbacks with the
     ‘gnutls_privkey_t’ type.  At least one of the two callbacks must be
     non-null.  If a deinitialization function is provided then flags is
     assumed to contain ‘GNUTLS_PRIVKEY_IMPORT_AUTO_RELEASE’ .

     Note that the signing function is supposed to "raw" sign data,
     i.e., without any hashing or preprocessing.  In case of RSA the
     DigestInfo will be provided, and the signing function is expected
     to do the PKCS ‘1’ 1.5 padding and the exponentiation.

     See also ‘gnutls_privkey_import_ext3()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1

gnutls_privkey_import_ext3
--------------------------

 -- Function: int gnutls_privkey_import_ext3 (gnutls_privkey_t PKEY,
          void * USERDATA, gnutls_privkey_sign_func SIGN_FN,
          gnutls_privkey_decrypt_func DECRYPT_FN,
          gnutls_privkey_deinit_func DEINIT_FN, gnutls_privkey_info_func
          INFO_FN, unsigned int FLAGS)
     PKEY: The private key

     USERDATA: private data to be provided to the callbacks

     SIGN_FN: callback for signature operations

     DECRYPT_FN: callback for decryption operations

     DEINIT_FN: a deinitialization function

     INFO_FN: returns info about the public key algorithm (should not be
     ‘NULL’ )

     FLAGS: Flags for the import

     This function will associate the given callbacks with the
     ‘gnutls_privkey_t’ type.  At least one of the two callbacks must be
     non-null.  If a deinitialization function is provided then flags is
     assumed to contain ‘GNUTLS_PRIVKEY_IMPORT_AUTO_RELEASE’ .

     Note that the signing function is supposed to "raw" sign data,
     i.e., without any hashing or preprocessing.  In case of RSA the
     DigestInfo will be provided, and the signing function is expected
     to do the PKCS ‘1’ 1.5 padding and the exponentiation.

     The ‘info_fn’ must provide information on the algorithms supported
     by this private key, and should support the flags
     ‘GNUTLS_PRIVKEY_INFO_PK_ALGO’ and ‘GNUTLS_PRIVKEY_INFO_SIGN_ALGO’ .
     It must return -1 on unknown flags.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_privkey_import_ext4
--------------------------

 -- Function: int gnutls_privkey_import_ext4 (gnutls_privkey_t PKEY,
          void * USERDATA, gnutls_privkey_sign_data_func SIGN_DATA_FN,
          gnutls_privkey_sign_hash_func SIGN_HASH_FN,
          gnutls_privkey_decrypt_func DECRYPT_FN,
          gnutls_privkey_deinit_func DEINIT_FN, gnutls_privkey_info_func
          INFO_FN, unsigned int FLAGS)
     PKEY: The private key

     USERDATA: private data to be provided to the callbacks

     SIGN_DATA_FN: callback for signature operations (may be ‘NULL’ )

     SIGN_HASH_FN: callback for signature operations (may be ‘NULL’ )

     DECRYPT_FN: callback for decryption operations (may be ‘NULL’ )

     DEINIT_FN: a deinitialization function

     INFO_FN: returns info about the public key algorithm (should not be
     ‘NULL’ )

     FLAGS: Flags for the import

     This function will associate the given callbacks with the
     ‘gnutls_privkey_t’ type.  At least one of the callbacks must be
     non-null.  If a deinitialization function is provided then flags is
     assumed to contain ‘GNUTLS_PRIVKEY_IMPORT_AUTO_RELEASE’ .

     Note that in contrast with the signing function of
     ‘gnutls_privkey_import_ext3()’ , the signing functions provided to
     this function take explicitly the signature algorithm as parameter
     and different functions are provided to sign the data and hashes.

     The ‘sign_hash_fn’ is to be called to sign pre-hashed data.  The
     input to the callback is the output of the hash (such as SHA256)
     corresponding to the signature algorithm.  For RSA PKCS‘1’
     signatures, the signature algorithm can be set to
     ‘GNUTLS_SIGN_RSA_RAW’ , and in that case the data should be handled
     as if they were an RSA PKCS‘1’ DigestInfo structure.

     The ‘sign_data_fn’ is to be called to sign data.  The input data
     will be he data to be signed (and hashed), with the provided
     signature algorithm.  This function is to be used for signature
     algorithms like Ed25519 which cannot take pre-hashed data as input.

     When both ‘sign_data_fn’ and ‘sign_hash_fn’ functions are provided
     they must be able to operate on all the supported signature
     algorithms, unless prohibited by the type of the algorithm (e.g.,
     as with Ed25519).

     The ‘info_fn’ must provide information on the signature algorithms
     supported by this private key, and should support the flags
     ‘GNUTLS_PRIVKEY_INFO_PK_ALGO’ ,
     ‘GNUTLS_PRIVKEY_INFO_HAVE_SIGN_ALGO’ and
     ‘GNUTLS_PRIVKEY_INFO_PK_ALGO_BITS’ .  It must return -1 on unknown
     flags.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.0

gnutls_privkey_import_gost_raw
------------------------------

 -- Function: int gnutls_privkey_import_gost_raw (gnutls_privkey_t KEY,
          gnutls_ecc_curve_t CURVE, gnutls_digest_algorithm_t DIGEST,
          gnutls_gost_paramset_t PARAMSET, const gnutls_datum_t * X,
          const gnutls_datum_t * Y, const gnutls_datum_t * K)
     KEY: The key

     CURVE: holds the curve

     DIGEST: holds the digest

     PARAMSET: holds the GOST parameter set ID

     X: holds the x-coordinate

     Y: holds the y-coordinate

     K: holds the k (private key)

     This function will convert the given GOST private key's parameters
     to the native ‘gnutls_privkey_t’ format.  The output will be stored
     in ‘key’ .  ‘digest’ should be one of GNUTLS_DIG_GOSR_94,
     GNUTLS_DIG_STREEBOG_256 or GNUTLS_DIG_STREEBOG_512.  If ‘paramset’
     is set to GNUTLS_GOST_PARAMSET_UNKNOWN default one will be selected
     depending on ‘digest’ .

     *Note:* parameters should be stored with least significant byte
     first.  On version 3.6.3 big-endian format was used incorrectly.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.3

gnutls_privkey_import_openpgp
-----------------------------

 -- Function: int gnutls_privkey_import_openpgp (gnutls_privkey_t PKEY,
          gnutls_openpgp_privkey_t KEY, unsigned int FLAGS)
     PKEY: The private key

     KEY: The private key to be imported

     FLAGS: Flags for the import

     This function is no-op.

     *Returns:* ‘GNUTLS_E_UNIMPLEMENTED_FEATURE’ .

     *Since:* 2.12.0

gnutls_privkey_import_openpgp_raw
---------------------------------

 -- Function: int gnutls_privkey_import_openpgp_raw (gnutls_privkey_t
          PKEY, const gnutls_datum_t * DATA, gnutls_openpgp_crt_fmt_t
          FORMAT, const gnutls_openpgp_keyid_t KEYID, const char *
          PASSWORD)
     PKEY: The private key

     DATA: The private key data to be imported

     FORMAT: The format of the private key

     KEYID: The key id to use (optional)

     PASSWORD: A password (optional)

     This function is no-op.

     *Returns:* ‘GNUTLS_E_UNIMPLEMENTED_FEATURE’ .

     *Since:* 3.1.0

gnutls_privkey_import_pkcs11
----------------------------

 -- Function: int gnutls_privkey_import_pkcs11 (gnutls_privkey_t PKEY,
          gnutls_pkcs11_privkey_t KEY, unsigned int FLAGS)
     PKEY: The private key

     KEY: The private key to be imported

     FLAGS: Flags for the import

     This function will import the given private key to the abstract
     ‘gnutls_privkey_t’ type.

     The ‘gnutls_pkcs11_privkey_t’ object must not be deallocated during
     the lifetime of this structure.

     ‘flags’ might be zero or one of
     ‘GNUTLS_PRIVKEY_IMPORT_AUTO_RELEASE’ and
     ‘GNUTLS_PRIVKEY_IMPORT_COPY’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_privkey_import_pkcs11_url
--------------------------------

 -- Function: int gnutls_privkey_import_pkcs11_url (gnutls_privkey_t
          KEY, const char * URL)
     KEY: A key of type ‘gnutls_pubkey_t’

     URL: A PKCS 11 url

     This function will import a PKCS 11 private key to a
     ‘gnutls_privkey_t’ type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.0

gnutls_privkey_import_rsa_raw
-----------------------------

 -- Function: int gnutls_privkey_import_rsa_raw (gnutls_privkey_t KEY,
          const gnutls_datum_t * M, const gnutls_datum_t * E, const
          gnutls_datum_t * D, const gnutls_datum_t * P, const
          gnutls_datum_t * Q, const gnutls_datum_t * U, const
          gnutls_datum_t * E1, const gnutls_datum_t * E2)
     KEY: The structure to store the parsed key

     M: holds the modulus

     E: holds the public exponent

     D: holds the private exponent (optional)

     P: holds the first prime (p)

     Q: holds the second prime (q)

     U: holds the coefficient (optional)

     E1: holds e1 = d mod (p-1) (optional)

     E2: holds e2 = d mod (q-1) (optional)

     This function will convert the given RSA raw parameters to the
     native ‘gnutls_privkey_t’ format.  The output will be stored in
     ‘key’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_privkey_import_tpm_raw
-----------------------------

 -- Function: int gnutls_privkey_import_tpm_raw (gnutls_privkey_t PKEY,
          const gnutls_datum_t * FDATA, gnutls_tpmkey_fmt_t FORMAT,
          const char * SRK_PASSWORD, const char * KEY_PASSWORD, unsigned
          int FLAGS)
     PKEY: The private key

     FDATA: The TPM key to be imported

     FORMAT: The format of the private key

     SRK_PASSWORD: The password for the SRK key (optional)

     KEY_PASSWORD: A password for the key (optional)

     FLAGS: should be zero

     This function will import the given private key to the abstract
     ‘gnutls_privkey_t’ type.

     With respect to passwords the same as in
     ‘gnutls_privkey_import_tpm_url()’ apply.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.0

gnutls_privkey_import_tpm_url
-----------------------------

 -- Function: int gnutls_privkey_import_tpm_url (gnutls_privkey_t PKEY,
          const char * URL, const char * SRK_PASSWORD, const char *
          KEY_PASSWORD, unsigned int FLAGS)
     PKEY: The private key

     URL: The URL of the TPM key to be imported

     SRK_PASSWORD: The password for the SRK key (optional)

     KEY_PASSWORD: A password for the key (optional)

     FLAGS: One of the GNUTLS_PRIVKEY_* flags

     This function will import the given private key to the abstract
     ‘gnutls_privkey_t’ type.

     Note that unless ‘GNUTLS_PRIVKEY_DISABLE_CALLBACKS’ is specified,
     if incorrect (or NULL) passwords are given the PKCS11 callback
     functions will be used to obtain the correct passwords.  Otherwise
     if the SRK password is wrong ‘GNUTLS_E_TPM_SRK_PASSWORD_ERROR’ is
     returned and if the key password is wrong or not provided then
     ‘GNUTLS_E_TPM_KEY_PASSWORD_ERROR’ is returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.0

gnutls_privkey_import_url
-------------------------

 -- Function: int gnutls_privkey_import_url (gnutls_privkey_t KEY, const
          char * URL, unsigned int FLAGS)
     KEY: A key of type ‘gnutls_privkey_t’

     URL: A PKCS 11 url

     FLAGS: should be zero

     This function will import a PKCS11 or TPM URL as a private key.
     The supported URL types can be checked using
     ‘gnutls_url_is_supported()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.0

gnutls_privkey_import_x509
--------------------------

 -- Function: int gnutls_privkey_import_x509 (gnutls_privkey_t PKEY,
          gnutls_x509_privkey_t KEY, unsigned int FLAGS)
     PKEY: The private key

     KEY: The private key to be imported

     FLAGS: Flags for the import

     This function will import the given private key to the abstract
     ‘gnutls_privkey_t’ type.

     The ‘gnutls_x509_privkey_t’ object must not be deallocated during
     the lifetime of this structure.

     ‘flags’ might be zero or one of
     ‘GNUTLS_PRIVKEY_IMPORT_AUTO_RELEASE’ and
     ‘GNUTLS_PRIVKEY_IMPORT_COPY’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_privkey_import_x509_raw
------------------------------

 -- Function: int gnutls_privkey_import_x509_raw (gnutls_privkey_t PKEY,
          const gnutls_datum_t * DATA, gnutls_x509_crt_fmt_t FORMAT,
          const char * PASSWORD, unsigned int FLAGS)
     PKEY: The private key

     DATA: The private key data to be imported

     FORMAT: The format of the private key

     PASSWORD: A password (optional)

     FLAGS: an ORed sequence of gnutls_pkcs_encrypt_flags_t

     This function will import the given private key to the abstract
     ‘gnutls_privkey_t’ type.

     The supported formats are basic unencrypted key, PKCS8, PKCS12,
     TSS2, and the openssl format.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.0

gnutls_privkey_init
-------------------

 -- Function: int gnutls_privkey_init (gnutls_privkey_t * KEY)
     KEY: A pointer to the type to be initialized

     This function will initialize a private key object.  The object can
     be used to generate, import, and perform cryptographic operations
     on the associated private key.

     Note that when the underlying private key is a PKCS‘11’ key (i.e.,
     when imported with a PKCS‘11’ URI), the limitations of
     ‘gnutls_pkcs11_privkey_init()’ apply to this object as well.  In
     versions of GnuTLS later than 3.5.11 the object is protected using
     locks and a single ‘gnutls_privkey_t’ can be re-used by many
     threads.  However, for performance it is recommended to utilize one
     object per key per thread.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_privkey_set_flags
------------------------

 -- Function: void gnutls_privkey_set_flags (gnutls_privkey_t KEY,
          unsigned int FLAGS)
     KEY: A key of type ‘gnutls_privkey_t’

     FLAGS: flags from the ‘gnutls_privkey_flags’

     This function will set flags for the specified private key, after
     it is generated.  Currently this is useful for the
     ‘GNUTLS_PRIVKEY_FLAG_EXPORT_COMPAT’ to allow exporting a "provable"
     private key in backwards compatible way.

     *Since:* 3.5.0

gnutls_privkey_set_pin_function
-------------------------------

 -- Function: void gnutls_privkey_set_pin_function (gnutls_privkey_t
          KEY, gnutls_pin_callback_t FN, void * USERDATA)
     KEY: A key of type ‘gnutls_privkey_t’

     FN: the callback

     USERDATA: data associated with the callback

     This function will set a callback function to be used when required
     to access the object.  This function overrides any other global PIN
     functions.

     Note that this function must be called right after initialization
     to have effect.

     *Since:* 3.1.0

gnutls_privkey_set_spki
-----------------------

 -- Function: int gnutls_privkey_set_spki (gnutls_privkey_t PRIVKEY,
          const gnutls_x509_spki_t SPKI, unsigned int FLAGS)
     PRIVKEY: a public key of type ‘gnutls_privkey_t’

     SPKI: a SubjectPublicKeyInfo structure of type
     ‘gnutls_privkey_spki_t’

     FLAGS: must be zero

     This function will set the public key information.  The provided
     ‘spki’ must be initialized.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.0

gnutls_privkey_sign_data
------------------------

 -- Function: int gnutls_privkey_sign_data (gnutls_privkey_t SIGNER,
          gnutls_digest_algorithm_t HASH, unsigned int FLAGS, const
          gnutls_datum_t * DATA, gnutls_datum_t * SIGNATURE)
     SIGNER: Holds the key

     HASH: should be a digest algorithm

     FLAGS: Zero or one of ‘gnutls_privkey_flags_t’

     DATA: holds the data to be signed

     SIGNATURE: will contain the signature allocated with
     ‘gnutls_malloc()’

     This function will sign the given data using a signature algorithm
     supported by the private key.  Signature algorithms are always used
     together with a hash functions.  Different hash functions may be
     used for the RSA algorithm, but only the SHA family for the DSA
     keys.

     You may use ‘gnutls_pubkey_get_preferred_hash_algorithm()’ to
     determine the hash algorithm.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_privkey_sign_data2
-------------------------

 -- Function: int gnutls_privkey_sign_data2 (gnutls_privkey_t SIGNER,
          gnutls_sign_algorithm_t ALGO, unsigned int FLAGS, const
          gnutls_datum_t * DATA, gnutls_datum_t * SIGNATURE)
     SIGNER: Holds the key

     ALGO: The signature algorithm used

     FLAGS: Zero or one of ‘gnutls_privkey_flags_t’

     DATA: holds the data to be signed

     SIGNATURE: will contain the signature allocated with
     ‘gnutls_malloc()’

     This function will sign the given data using the specified
     signature algorithm.  This function is an enhancement of
     ‘gnutls_privkey_sign_data()’ , as it allows utilizing a alternative
     signature algorithm where possible (e.g, use an RSA key with
     RSA-PSS).

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.0

gnutls_privkey_sign_hash
------------------------

 -- Function: int gnutls_privkey_sign_hash (gnutls_privkey_t SIGNER,
          gnutls_digest_algorithm_t HASH_ALGO, unsigned int FLAGS, const
          gnutls_datum_t * HASH_DATA, gnutls_datum_t * SIGNATURE)
     SIGNER: Holds the signer's key

     HASH_ALGO: The hash algorithm used

     FLAGS: Zero or one of ‘gnutls_privkey_flags_t’

     HASH_DATA: holds the data to be signed

     SIGNATURE: will contain newly allocated signature

     This function will sign the given hashed data using a signature
     algorithm supported by the private key.  Signature algorithms are
     always used together with a hash functions.  Different hash
     functions may be used for the RSA algorithm, but only SHA-XXX for
     the DSA keys.

     You may use ‘gnutls_pubkey_get_preferred_hash_algorithm()’ to
     determine the hash algorithm.

     The flags may be ‘GNUTLS_PRIVKEY_SIGN_FLAG_TLS1_RSA’ or
     ‘GNUTLS_PRIVKEY_SIGN_FLAG_RSA_PSS’ .  In the former case this
     function will ignore ‘hash_algo’ and perform a raw PKCS1 signature,
     and in the latter an RSA-PSS signature will be generated.

     Note that, not all algorithm support signing already hashed data.
     When signing with Ed25519, ‘gnutls_privkey_sign_data()’ should be
     used.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_privkey_sign_hash2
-------------------------

 -- Function: int gnutls_privkey_sign_hash2 (gnutls_privkey_t SIGNER,
          gnutls_sign_algorithm_t ALGO, unsigned int FLAGS, const
          gnutls_datum_t * HASH_DATA, gnutls_datum_t * SIGNATURE)
     SIGNER: Holds the signer's key

     ALGO: The signature algorithm used

     FLAGS: Zero or one of ‘gnutls_privkey_flags_t’

     HASH_DATA: holds the data to be signed

     SIGNATURE: will contain newly allocated signature

     This function will sign the given hashed data using the specified
     signature algorithm.  This function is an enhancement of
     ‘gnutls_privkey_sign_hash()’ , as it allows utilizing a alternative
     signature algorithm where possible (e.g, use an RSA key with
     RSA-PSS).

     The flags may be ‘GNUTLS_PRIVKEY_SIGN_FLAG_TLS1_RSA’ .  In that
     case this function will ignore ‘hash_algo’ and perform a raw PKCS1
     signature.  Note that this flag is supported since 3.6.9.

     Note also that, not all algorithm support signing already hashed
     data.  When signing with Ed25519, ‘gnutls_privkey_sign_data2()’
     should be used instead.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.0

gnutls_privkey_status
---------------------

 -- Function: int gnutls_privkey_status (gnutls_privkey_t KEY)
     KEY: Holds the key

     Checks the status of the private key token.  This function is an
     actual wrapper over ‘gnutls_pkcs11_privkey_status()’ , and if the
     private key is a PKCS ‘11’ token it will check whether it is
     inserted or not.

     *Returns:* this function will return non-zero if the token holding
     the private key is still available (inserted), and zero otherwise.

     *Since:* 3.1.10

gnutls_privkey_verify_params
----------------------------

 -- Function: int gnutls_privkey_verify_params (gnutls_privkey_t KEY)
     KEY: should contain a ‘gnutls_privkey_t’ type

     This function will verify the private key parameters.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_privkey_verify_seed
--------------------------

 -- Function: int gnutls_privkey_verify_seed (gnutls_privkey_t KEY,
          gnutls_digest_algorithm_t DIGEST, const void * SEED, size_t
          SEED_SIZE)
     KEY: should contain a ‘gnutls_privkey_t’ type

     DIGEST: it contains the digest algorithm used for key generation
     (if applicable)

     SEED: the seed of the key to be checked with

     SEED_SIZE: holds the size of ‘seed’

     This function will verify that the given private key was generated
     from the provided seed.

     *Returns:* In case of a verification failure
     ‘GNUTLS_E_PRIVKEY_VERIFICATION_ERROR’ is returned, and zero or
     positive code on success.

     *Since:* 3.5.0

gnutls_pubkey_deinit
--------------------

 -- Function: void gnutls_pubkey_deinit (gnutls_pubkey_t KEY)
     KEY: The key to be deinitialized

     This function will deinitialize a public key structure.

     *Since:* 2.12.0

gnutls_pubkey_encrypt_data
--------------------------

 -- Function: int gnutls_pubkey_encrypt_data (gnutls_pubkey_t KEY,
          unsigned int FLAGS, const gnutls_datum_t * PLAINTEXT,
          gnutls_datum_t * CIPHERTEXT)
     KEY: Holds the public key

     FLAGS: should be 0 for now

     PLAINTEXT: The data to be encrypted

     CIPHERTEXT: contains the encrypted data

     This function will encrypt the given data, using the public key.
     On success the ‘ciphertext’ will be allocated using
     ‘gnutls_malloc()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_pubkey_export
--------------------

 -- Function: int gnutls_pubkey_export (gnutls_pubkey_t KEY,
          gnutls_x509_crt_fmt_t FORMAT, void * OUTPUT_DATA, size_t *
          OUTPUT_DATA_SIZE)
     KEY: Holds the certificate

     FORMAT: the format of output params.  One of PEM or DER.

     OUTPUT_DATA: will contain a certificate PEM or DER encoded

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will export the public key to DER or PEM format.  The
     contents of the exported data is the SubjectPublicKeyInfo X.509
     structure.

     If the buffer provided is not long enough to hold the output, then
     *output_data_size is updated and ‘GNUTLS_E_SHORT_MEMORY_BUFFER’
     will be returned.

     If the structure is PEM encoded, it will have a header of "BEGIN
     CERTIFICATE".

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

     *Since:* 2.12.0

gnutls_pubkey_export2
---------------------

 -- Function: int gnutls_pubkey_export2 (gnutls_pubkey_t KEY,
          gnutls_x509_crt_fmt_t FORMAT, gnutls_datum_t * OUT)
     KEY: Holds the certificate

     FORMAT: the format of output params.  One of PEM or DER.

     OUT: will contain a certificate PEM or DER encoded

     This function will export the public key to DER or PEM format.  The
     contents of the exported data is the SubjectPublicKeyInfo X.509
     structure.

     The output buffer will be allocated using ‘gnutls_malloc()’ .

     If the structure is PEM encoded, it will have a header of "BEGIN
     CERTIFICATE".

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

     *Since:* 3.1.3

gnutls_pubkey_export_dh_raw
---------------------------

 -- Function: int gnutls_pubkey_export_dh_raw (gnutls_pubkey_t KEY,
          gnutls_dh_params_t PARAMS, gnutls_datum_t * Y, unsigned FLAGS)
     KEY: Holds the public key

     PARAMS: will hold the Diffie-Hellman parameter (optional), must be
     initialized

     Y: will hold the y

     FLAGS: flags from ‘gnutls_abstract_export_flags_t’

     This function will export the Diffie-Hellman public key parameter
     found in the given public key.  The new parameter will be allocated
     using ‘gnutls_malloc()’ and will be stored in the appropriate
     datum.

     To retrieve other parameters common in both public key and private
     key, use ‘gnutls_dh_params_export_raw()’ .

     This function allows for ‘NULL’ parameters since 3.4.1.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.8.2

gnutls_pubkey_export_dsa_raw
----------------------------

 -- Function: int gnutls_pubkey_export_dsa_raw (gnutls_pubkey_t KEY,
          gnutls_datum_t * P, gnutls_datum_t * Q, gnutls_datum_t * G,
          gnutls_datum_t * Y)
     KEY: Holds the public key

     P: will hold the p (may be ‘NULL’ )

     Q: will hold the q (may be ‘NULL’ )

     G: will hold the g (may be ‘NULL’ )

     Y: will hold the y (may be ‘NULL’ )

     This function will export the DSA public key's parameters found in
     the given certificate.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     This function allows for ‘NULL’ parameters since 3.4.1.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.3.0

gnutls_pubkey_export_dsa_raw2
-----------------------------

 -- Function: int gnutls_pubkey_export_dsa_raw2 (gnutls_pubkey_t KEY,
          gnutls_datum_t * P, gnutls_datum_t * Q, gnutls_datum_t * G,
          gnutls_datum_t * Y, unsigned FLAGS)
     KEY: Holds the public key

     P: will hold the p (may be ‘NULL’ )

     Q: will hold the q (may be ‘NULL’ )

     G: will hold the g (may be ‘NULL’ )

     Y: will hold the y (may be ‘NULL’ )

     FLAGS: flags from ‘gnutls_abstract_export_flags_t’

     This function will export the DSA public key's parameters found in
     the given certificate.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     This function allows for ‘NULL’ parameters since 3.4.1.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.6.0

gnutls_pubkey_export_ecc_raw
----------------------------

 -- Function: int gnutls_pubkey_export_ecc_raw (gnutls_pubkey_t KEY,
          gnutls_ecc_curve_t * CURVE, gnutls_datum_t * X, gnutls_datum_t
          * Y)
     KEY: Holds the public key

     CURVE: will hold the curve (may be ‘NULL’ )

     X: will hold x-coordinate (may be ‘NULL’ )

     Y: will hold y-coordinate (may be ‘NULL’ )

     This function will export the ECC public key's parameters found in
     the given key.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     In EdDSA curves the ‘y’ parameter will be ‘NULL’ and the other
     parameters will be in the native format for the curve.

     This function allows for ‘NULL’ parameters since 3.4.1.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.0

gnutls_pubkey_export_ecc_raw2
-----------------------------

 -- Function: int gnutls_pubkey_export_ecc_raw2 (gnutls_pubkey_t KEY,
          gnutls_ecc_curve_t * CURVE, gnutls_datum_t * X, gnutls_datum_t
          * Y, unsigned int FLAGS)
     KEY: Holds the public key

     CURVE: will hold the curve (may be ‘NULL’ )

     X: will hold x-coordinate (may be ‘NULL’ )

     Y: will hold y-coordinate (may be ‘NULL’ )

     FLAGS: flags from ‘gnutls_abstract_export_flags_t’

     This function will export the ECC public key's parameters found in
     the given key.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     In EdDSA curves the ‘y’ parameter will be ‘NULL’ and the other
     parameters will be in the native format for the curve.

     This function allows for ‘NULL’ parameters since 3.4.1.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.6.0

gnutls_pubkey_export_ecc_x962
-----------------------------

 -- Function: int gnutls_pubkey_export_ecc_x962 (gnutls_pubkey_t KEY,
          gnutls_datum_t * PARAMETERS, gnutls_datum_t * ECPOINT)
     KEY: Holds the public key

     PARAMETERS: DER encoding of an ANSI X9.62 parameters

     ECPOINT: DER encoding of ANSI X9.62 ECPoint

     This function will export the ECC public key's parameters found in
     the given certificate.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.3.0

gnutls_pubkey_export_gost_raw2
------------------------------

 -- Function: int gnutls_pubkey_export_gost_raw2 (gnutls_pubkey_t KEY,
          gnutls_ecc_curve_t * CURVE, gnutls_digest_algorithm_t *
          DIGEST, gnutls_gost_paramset_t * PARAMSET, gnutls_datum_t * X,
          gnutls_datum_t * Y, unsigned int FLAGS)
     KEY: Holds the public key

     CURVE: will hold the curve (may be ‘NULL’ )

     DIGEST: will hold the curve (may be ‘NULL’ )

     PARAMSET: will hold the parameters id (may be ‘NULL’ )

     X: will hold the x-coordinate (may be ‘NULL’ )

     Y: will hold the y-coordinate (may be ‘NULL’ )

     FLAGS: flags from ‘gnutls_abstract_export_flags_t’

     This function will export the GOST public key's parameters found in
     the given key.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     *Note:* parameters will be stored with least significant byte
     first.  On version 3.6.3 this was incorrectly returned in
     big-endian format.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.6.3

gnutls_pubkey_export_rsa_raw
----------------------------

 -- Function: int gnutls_pubkey_export_rsa_raw (gnutls_pubkey_t KEY,
          gnutls_datum_t * M, gnutls_datum_t * E)
     KEY: Holds the certificate

     M: will hold the modulus (may be ‘NULL’ )

     E: will hold the public exponent (may be ‘NULL’ )

     This function will export the RSA public key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     This function allows for ‘NULL’ parameters since 3.4.1.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.3.0

gnutls_pubkey_export_rsa_raw2
-----------------------------

 -- Function: int gnutls_pubkey_export_rsa_raw2 (gnutls_pubkey_t KEY,
          gnutls_datum_t * M, gnutls_datum_t * E, unsigned FLAGS)
     KEY: Holds the certificate

     M: will hold the modulus (may be ‘NULL’ )

     E: will hold the public exponent (may be ‘NULL’ )

     FLAGS: flags from ‘gnutls_abstract_export_flags_t’

     This function will export the RSA public key's parameters found in
     the given structure.  The new parameters will be allocated using
     ‘gnutls_malloc()’ and will be stored in the appropriate datum.

     This function allows for ‘NULL’ parameters since 3.4.1.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.6.0

gnutls_pubkey_get_key_id
------------------------

 -- Function: int gnutls_pubkey_get_key_id (gnutls_pubkey_t KEY,
          unsigned int FLAGS, unsigned char * OUTPUT_DATA, size_t *
          OUTPUT_DATA_SIZE)
     KEY: Holds the public key

     FLAGS: should be one of the flags from ‘gnutls_keyid_flags_t’

     OUTPUT_DATA: will contain the key ID

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     This function will return a unique ID that depends on the public
     key parameters.  This ID can be used in checking whether a
     certificate corresponds to the given public key.

     If the buffer provided is not long enough to hold the output, then
     *output_data_size is updated and ‘GNUTLS_E_SHORT_MEMORY_BUFFER’
     will be returned.  The output will normally be a SHA-1 hash output,
     which is 20 bytes.

     *Returns:* In case of failure a negative error code will be
     returned, and 0 on success.

     *Since:* 2.12.0

gnutls_pubkey_get_key_usage
---------------------------

 -- Function: int gnutls_pubkey_get_key_usage (gnutls_pubkey_t KEY,
          unsigned int * USAGE)
     KEY: should contain a ‘gnutls_pubkey_t’ type

     USAGE: If set will return the number of bits of the parameters (may
     be NULL)

     This function will return the key usage of the public key.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pubkey_get_openpgp_key_id
--------------------------------

 -- Function: int gnutls_pubkey_get_openpgp_key_id (gnutls_pubkey_t KEY,
          unsigned int FLAGS, unsigned char * OUTPUT_DATA, size_t *
          OUTPUT_DATA_SIZE, unsigned int * SUBKEY)
     KEY: Holds the public key

     FLAGS: should be one of the flags from ‘gnutls_keyid_flags_t’

     OUTPUT_DATA: will contain the key ID

     OUTPUT_DATA_SIZE: holds the size of output_data (and will be
     replaced by the actual size of parameters)

     SUBKEY: ignored

     This function is no-op.

     *Returns:* ‘GNUTLS_E_UNIMPLEMENTED_FEATURE’ .

     *Since:* 2.12.0

gnutls_pubkey_get_pk_algorithm
------------------------------

 -- Function: int gnutls_pubkey_get_pk_algorithm (gnutls_pubkey_t KEY,
          unsigned int * BITS)
     KEY: should contain a ‘gnutls_pubkey_t’ type

     BITS: If set will return the number of bits of the parameters (may
     be NULL)

     This function will return the public key algorithm of a public key
     and if possible will return a number of bits that indicates the
     security parameter of the key.

     *Returns:* a member of the ‘gnutls_pk_algorithm_t’ enumeration on
     success, or a negative error code on error.

     *Since:* 2.12.0

gnutls_pubkey_get_preferred_hash_algorithm
------------------------------------------

 -- Function: int gnutls_pubkey_get_preferred_hash_algorithm
          (gnutls_pubkey_t KEY, gnutls_digest_algorithm_t * HASH,
          unsigned int * MAND)
     KEY: Holds the certificate

     HASH: The result of the call with the hash algorithm used for
     signature

     MAND: If non zero it means that the algorithm MUST use this hash.
     May be NULL.

     This function will read the certificate and return the appropriate
     digest algorithm to use for signing with this certificate.  Some
     certificates (i.e.  DSA might not be able to sign without the
     preferred algorithm).

     To get the signature algorithm instead of just the hash use
     ‘gnutls_pk_to_sign()’ with the algorithm of the certificate/key and
     the provided ‘hash’ .

     *Returns:* the 0 if the hash algorithm is found.  A negative error
     code is returned on error.

     *Since:* 2.12.0

gnutls_pubkey_get_spki
----------------------

 -- Function: int gnutls_pubkey_get_spki (gnutls_pubkey_t PUBKEY,
          gnutls_x509_spki_t SPKI, unsigned int FLAGS)
     PUBKEY: a public key of type ‘gnutls_pubkey_t’

     SPKI: a SubjectPublicKeyInfo structure of type
     ‘gnutls_pubkey_spki_t’

     FLAGS: must be zero

     This function will return the public key information if available.
     The provided ‘spki’ must be initialized.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.0

gnutls_pubkey_import
--------------------

 -- Function: int gnutls_pubkey_import (gnutls_pubkey_t KEY, const
          gnutls_datum_t * DATA, gnutls_x509_crt_fmt_t FORMAT)
     KEY: The public key.

     DATA: The DER or PEM encoded certificate.

     FORMAT: One of DER or PEM

     This function will import the provided public key in a
     SubjectPublicKeyInfo X.509 structure to a native ‘gnutls_pubkey_t’
     type.  The output will be stored in ‘key’ .  If the public key is
     PEM encoded it should have a header of "PUBLIC KEY".

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pubkey_import_dh_raw
---------------------------

 -- Function: int gnutls_pubkey_import_dh_raw (gnutls_pubkey_t KEY,
          const gnutls_dh_params_t PARAMS, const gnutls_datum_t * Y)
     KEY: The structure to store the parsed key

     PARAMS: holds the ‘gnutls_dh_params_t’

     Y: holds the y

     This function will convert the given Diffie-Hellman raw parameters
     to the native ‘gnutls_pubkey_t’ format.  The output will be stored
     in ‘key’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.8.2

gnutls_pubkey_import_dsa_raw
----------------------------

 -- Function: int gnutls_pubkey_import_dsa_raw (gnutls_pubkey_t KEY,
          const gnutls_datum_t * P, const gnutls_datum_t * Q, const
          gnutls_datum_t * G, const gnutls_datum_t * Y)
     KEY: The structure to store the parsed key

     P: holds the p

     Q: holds the q

     G: holds the g

     Y: holds the y

     This function will convert the given DSA raw parameters to the
     native ‘gnutls_pubkey_t’ format.  The output will be stored in
     ‘key’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pubkey_import_ecc_raw
----------------------------

 -- Function: int gnutls_pubkey_import_ecc_raw (gnutls_pubkey_t KEY,
          gnutls_ecc_curve_t CURVE, const gnutls_datum_t * X, const
          gnutls_datum_t * Y)
     KEY: The structure to store the parsed key

     CURVE: holds the curve

     X: holds the x-coordinate

     Y: holds the y-coordinate

     This function will convert the given elliptic curve parameters to a
     ‘gnutls_pubkey_t’ .  The output will be stored in ‘key’ .

     In EdDSA curves the ‘y’ parameter should be ‘NULL’ and the ‘x’
     parameter must be the value in the native format for the curve.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_pubkey_import_ecc_x962
-----------------------------

 -- Function: int gnutls_pubkey_import_ecc_x962 (gnutls_pubkey_t KEY,
          const gnutls_datum_t * PARAMETERS, const gnutls_datum_t *
          ECPOINT)
     KEY: The structure to store the parsed key

     PARAMETERS: DER encoding of an ANSI X9.62 parameters

     ECPOINT: DER encoding of ANSI X9.62 ECPoint

     This function will convert the given elliptic curve parameters to a
     ‘gnutls_pubkey_t’ .  The output will be stored in ‘key’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.0

gnutls_pubkey_import_gost_raw
-----------------------------

 -- Function: int gnutls_pubkey_import_gost_raw (gnutls_pubkey_t KEY,
          gnutls_ecc_curve_t CURVE, gnutls_digest_algorithm_t DIGEST,
          gnutls_gost_paramset_t PARAMSET, const gnutls_datum_t * X,
          const gnutls_datum_t * Y)
     KEY: The structure to store the parsed key

     CURVE: holds the curve

     DIGEST: holds the digest

     PARAMSET: holds the parameters id

     X: holds the x-coordinate

     Y: holds the y-coordinate

     This function will convert the given GOST public key's parameters
     to a ‘gnutls_pubkey_t’ .  The output will be stored in ‘key’ .
     ‘digest’ should be one of GNUTLS_DIG_GOSR_94,
     GNUTLS_DIG_STREEBOG_256 or GNUTLS_DIG_STREEBOG_512.  If ‘paramset’
     is set to GNUTLS_GOST_PARAMSET_UNKNOWN default one will be selected
     depending on ‘digest’ .

     *Note:* parameters should be stored with least significant byte
     first.  On version 3.6.3 big-endian format was used incorrectly.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.3

gnutls_pubkey_import_openpgp
----------------------------

 -- Function: int gnutls_pubkey_import_openpgp (gnutls_pubkey_t KEY,
          gnutls_openpgp_crt_t CRT, unsigned int FLAGS)
     KEY: The public key

     CRT: The certificate to be imported

     FLAGS: should be zero

     This function is no-op.

     *Returns:* ‘GNUTLS_E_UNIMPLEMENTED_FEATURE’ .

     *Since:* 2.12.0

gnutls_pubkey_import_openpgp_raw
--------------------------------

 -- Function: int gnutls_pubkey_import_openpgp_raw (gnutls_pubkey_t
          PKEY, const gnutls_datum_t * DATA, gnutls_openpgp_crt_fmt_t
          FORMAT, const gnutls_openpgp_keyid_t KEYID, unsigned int
          FLAGS)
     PKEY: The public key

     DATA: The public key data to be imported

     FORMAT: The format of the public key

     KEYID: The key id to use (optional)

     FLAGS: Should be zero

     This function is no-op.

     *Returns:* ‘GNUTLS_E_UNIMPLEMENTED_FEATURE’ .

     *Since:* 3.1.3

gnutls_pubkey_import_pkcs11
---------------------------

 -- Function: int gnutls_pubkey_import_pkcs11 (gnutls_pubkey_t KEY,
          gnutls_pkcs11_obj_t OBJ, unsigned int FLAGS)
     KEY: The public key

     OBJ: The parameters to be imported

     FLAGS: should be zero

     Imports a public key from a pkcs11 key.  This function will import
     the given public key to the abstract ‘gnutls_pubkey_t’ type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pubkey_import_privkey
----------------------------

 -- Function: int gnutls_pubkey_import_privkey (gnutls_pubkey_t KEY,
          gnutls_privkey_t PKEY, unsigned int USAGE, unsigned int FLAGS)
     KEY: The public key

     PKEY: The private key

     USAGE: GNUTLS_KEY_* key usage flags.

     FLAGS: should be zero

     Imports the public key from a private.  This function will import
     the given public key to the abstract ‘gnutls_pubkey_t’ type.

     Note that in certain keys this operation may not be possible, e.g.,
     in other than RSA PKCS‘11’ keys.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pubkey_import_rsa_raw
----------------------------

 -- Function: int gnutls_pubkey_import_rsa_raw (gnutls_pubkey_t KEY,
          const gnutls_datum_t * M, const gnutls_datum_t * E)
     KEY: The key

     M: holds the modulus

     E: holds the public exponent

     This function will replace the parameters in the given structure.
     The new parameters should be stored in the appropriate
     gnutls_datum.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, or an negative error
     code.

     *Since:* 2.12.0

gnutls_pubkey_import_tpm_raw
----------------------------

 -- Function: int gnutls_pubkey_import_tpm_raw (gnutls_pubkey_t PKEY,
          const gnutls_datum_t * FDATA, gnutls_tpmkey_fmt_t FORMAT,
          const char * SRK_PASSWORD, unsigned int FLAGS)
     PKEY: The public key

     FDATA: The TPM key to be imported

     FORMAT: The format of the private key

     SRK_PASSWORD: The password for the SRK key (optional)

     FLAGS: One of the GNUTLS_PUBKEY_* flags

     This function will import the public key from the provided TPM key
     structure.

     With respect to passwords the same as in
     ‘gnutls_pubkey_import_tpm_url()’ apply.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.0

gnutls_pubkey_import_tpm_url
----------------------------

 -- Function: int gnutls_pubkey_import_tpm_url (gnutls_pubkey_t PKEY,
          const char * URL, const char * SRK_PASSWORD, unsigned int
          FLAGS)
     PKEY: The public key

     URL: The URL of the TPM key to be imported

     SRK_PASSWORD: The password for the SRK key (optional)

     FLAGS: should be zero

     This function will import the given private key to the abstract
     ‘gnutls_privkey_t’ type.

     Note that unless ‘GNUTLS_PUBKEY_DISABLE_CALLBACKS’ is specified, if
     incorrect (or NULL) passwords are given the PKCS11 callback
     functions will be used to obtain the correct passwords.  Otherwise
     if the SRK password is wrong ‘GNUTLS_E_TPM_SRK_PASSWORD_ERROR’ is
     returned.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.0

gnutls_pubkey_import_url
------------------------

 -- Function: int gnutls_pubkey_import_url (gnutls_pubkey_t KEY, const
          char * URL, unsigned int FLAGS)
     KEY: A key of type ‘gnutls_pubkey_t’

     URL: A PKCS 11 url

     FLAGS: One of GNUTLS_PKCS11_OBJ_* flags

     This function will import a public key from the provided URL.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.0

gnutls_pubkey_import_x509
-------------------------

 -- Function: int gnutls_pubkey_import_x509 (gnutls_pubkey_t KEY,
          gnutls_x509_crt_t CRT, unsigned int FLAGS)
     KEY: The public key

     CRT: The certificate to be imported

     FLAGS: should be zero

     This function will import the given public key to the abstract
     ‘gnutls_pubkey_t’ type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pubkey_import_x509_crq
-----------------------------

 -- Function: int gnutls_pubkey_import_x509_crq (gnutls_pubkey_t KEY,
          gnutls_x509_crq_t CRQ, unsigned int FLAGS)
     KEY: The public key

     CRQ: The certificate to be imported

     FLAGS: should be zero

     This function will import the given public key to the abstract
     ‘gnutls_pubkey_t’ type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.5

gnutls_pubkey_import_x509_raw
-----------------------------

 -- Function: int gnutls_pubkey_import_x509_raw (gnutls_pubkey_t PKEY,
          const gnutls_datum_t * DATA, gnutls_x509_crt_fmt_t FORMAT,
          unsigned int FLAGS)
     PKEY: The public key

     DATA: The public key data to be imported

     FORMAT: The format of the public key

     FLAGS: should be zero

     This function will import the given public key to the abstract
     ‘gnutls_pubkey_t’ type.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.3

gnutls_pubkey_init
------------------

 -- Function: int gnutls_pubkey_init (gnutls_pubkey_t * KEY)
     KEY: A pointer to the type to be initialized

     This function will initialize a public key.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pubkey_print
-------------------

 -- Function: int gnutls_pubkey_print (gnutls_pubkey_t PUBKEY,
          gnutls_certificate_print_formats_t FORMAT, gnutls_datum_t *
          OUT)
     PUBKEY: The data to be printed

     FORMAT: Indicate the format to use

     OUT: Newly allocated datum with null terminated string.

     This function will pretty print public key information, suitable
     for display to a human.

     Only ‘GNUTLS_CRT_PRINT_FULL’ and ‘GNUTLS_CRT_PRINT_FULL_NUMBERS’
     are implemented.

     The output ‘out’ needs to be deallocated using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.1.5

gnutls_pubkey_set_key_usage
---------------------------

 -- Function: int gnutls_pubkey_set_key_usage (gnutls_pubkey_t KEY,
          unsigned int USAGE)
     KEY: a certificate of type ‘gnutls_x509_crt_t’

     USAGE: an ORed sequence of the GNUTLS_KEY_* elements.

     This function will set the key usage flags of the public key.  This
     is only useful if the key is to be exported to a certificate or
     certificate request.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_pubkey_set_pin_function
------------------------------

 -- Function: void gnutls_pubkey_set_pin_function (gnutls_pubkey_t KEY,
          gnutls_pin_callback_t FN, void * USERDATA)
     KEY: A key of type ‘gnutls_pubkey_t’

     FN: the callback

     USERDATA: data associated with the callback

     This function will set a callback function to be used when required
     to access the object.  This function overrides any other global PIN
     functions.

     Note that this function must be called right after initialization
     to have effect.

     *Since:* 3.1.0

gnutls_pubkey_set_spki
----------------------

 -- Function: int gnutls_pubkey_set_spki (gnutls_pubkey_t PUBKEY, const
          gnutls_x509_spki_t SPKI, unsigned int FLAGS)
     PUBKEY: a public key of type ‘gnutls_pubkey_t’

     SPKI: a SubjectPublicKeyInfo structure of type
     ‘gnutls_pubkey_spki_t’

     FLAGS: must be zero

     This function will set the public key information.  The provided
     ‘spki’ must be initialized.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.6.0

gnutls_pubkey_verify_data2
--------------------------

 -- Function: int gnutls_pubkey_verify_data2 (gnutls_pubkey_t PUBKEY,
          gnutls_sign_algorithm_t ALGO, unsigned int FLAGS, const
          gnutls_datum_t * DATA, const gnutls_datum_t * SIGNATURE)
     PUBKEY: Holds the public key

     ALGO: The signature algorithm used

     FLAGS: Zero or an OR list of ‘gnutls_certificate_verify_flags’

     DATA: holds the signed data

     SIGNATURE: contains the signature

     This function will verify the given signed data, using the
     parameters from the certificate.

     *Returns:* In case of a verification failure
     ‘GNUTLS_E_PK_SIG_VERIFY_FAILED’ is returned, and zero or positive
     code on success.  For known to be insecure signatures this function
     will return ‘GNUTLS_E_INSUFFICIENT_SECURITY’ unless the flag
     ‘GNUTLS_VERIFY_ALLOW_BROKEN’ is specified.

     *Since:* 3.0

gnutls_pubkey_verify_hash2
--------------------------

 -- Function: int gnutls_pubkey_verify_hash2 (gnutls_pubkey_t KEY,
          gnutls_sign_algorithm_t ALGO, unsigned int FLAGS, const
          gnutls_datum_t * HASH, const gnutls_datum_t * SIGNATURE)
     KEY: Holds the public key

     ALGO: The signature algorithm used

     FLAGS: Zero or an OR list of ‘gnutls_certificate_verify_flags’

     HASH: holds the hash digest to be verified

     SIGNATURE: contains the signature

     This function will verify the given signed digest, using the
     parameters from the public key.  Note that unlike
     ‘gnutls_privkey_sign_hash()’ , this function accepts a signature
     algorithm instead of a digest algorithm.  You can use
     ‘gnutls_pk_to_sign()’ to get the appropriate value.

     *Returns:* In case of a verification failure
     ‘GNUTLS_E_PK_SIG_VERIFY_FAILED’ is returned, and zero or positive
     code on success.  For known to be insecure signatures this function
     will return ‘GNUTLS_E_INSUFFICIENT_SECURITY’ unless the flag
     ‘GNUTLS_VERIFY_ALLOW_BROKEN’ is specified.

     *Since:* 3.0

gnutls_pubkey_verify_params
---------------------------

 -- Function: int gnutls_pubkey_verify_params (gnutls_pubkey_t KEY)
     KEY: should contain a ‘gnutls_pubkey_t’ type

     This function will verify the public key parameters.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.3.0

gnutls_register_custom_url
--------------------------

 -- Function: int gnutls_register_custom_url (const gnutls_custom_url_st
          * ST)
     ST: A ‘gnutls_custom_url_st’ structure

     Register a custom URL. This will affect the following functions:
     ‘gnutls_url_is_supported()’ , ‘gnutls_privkey_import_url()’ ,
     gnutls_pubkey_import_url, ‘gnutls_x509_crt_import_url()’ and all
     functions that depend on them, e.g.,
     ‘gnutls_certificate_set_x509_key_file2()’ .

     The provided structure and callback functions must be valid
     throughout the lifetime of the process.  The registration of an
     existing URL type will fail with ‘GNUTLS_E_INVALID_REQUEST’ .
     Since GnuTLS 3.5.0 this function can be used to override the
     builtin URLs.

     This function is not thread safe.

     *Returns:* returns zero if the given structure was imported or a
     negative value otherwise.

     *Since:* 3.4.0

gnutls_system_key_add_x509
--------------------------

 -- Function: int gnutls_system_key_add_x509 (gnutls_x509_crt_t CRT,
          gnutls_x509_privkey_t PRIVKEY, const char * LABEL, char **
          CERT_URL, char ** KEY_URL)
     CRT: the certificate to be added

     PRIVKEY: the key to be added

     LABEL: the friendly name to describe the key

     CERT_URL: if non-NULL it will contain an allocated value with the
     certificate URL

     KEY_URL: if non-NULL it will contain an allocated value with the
     key URL

     This function will added the given key and certificate pair, to the
     system list.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_system_key_delete
------------------------

 -- Function: int gnutls_system_key_delete (const char * CERT_URL, const
          char * KEY_URL)
     CERT_URL: the URL of the certificate

     KEY_URL: the URL of the key

     This function will delete the key and certificate pair.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_system_key_iter_deinit
-----------------------------

 -- Function: void gnutls_system_key_iter_deinit
          (gnutls_system_key_iter_t ITER)
     ITER: an iterator of system keys

     This function will deinitialize the iterator.

     *Since:* 3.4.0

gnutls_system_key_iter_get_info
-------------------------------

 -- Function: int gnutls_system_key_iter_get_info
          (gnutls_system_key_iter_t * ITER, unsigned CERT_TYPE, char **
          CERT_URL, char ** KEY_URL, char ** LABEL, gnutls_datum_t *
          DER, unsigned int FLAGS)
     ITER: an iterator of the system keys (must be set to ‘NULL’
     initially)

     CERT_TYPE: A value of gnutls_certificate_type_t which indicates the
     type of certificate to look for

     CERT_URL: The certificate URL of the pair (may be ‘NULL’ )

     KEY_URL: The key URL of the pair (may be ‘NULL’ )

     LABEL: The friendly name (if any) of the pair (may be ‘NULL’ )

     DER: if non-NULL the DER data of the certificate

     FLAGS: should be zero

     This function will return on each call a certificate and key pair
     URLs, as well as a label associated with them, and the DER-encoded
     certificate.  When the iteration is complete it will return
     ‘GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE’ .

     Typically ‘cert_type’ should be ‘GNUTLS_CRT_X509’ .

     All values set are allocated and must be cleared using
     ‘gnutls_free()’ ,

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 3.4.0

gnutls_x509_crl_privkey_sign
----------------------------

 -- Function: int gnutls_x509_crl_privkey_sign (gnutls_x509_crl_t CRL,
          gnutls_x509_crt_t ISSUER, gnutls_privkey_t ISSUER_KEY,
          gnutls_digest_algorithm_t DIG, unsigned int FLAGS)
     CRL: should contain a gnutls_x509_crl_t type

     ISSUER: is the certificate of the certificate issuer

     ISSUER_KEY: holds the issuer's private key

     DIG: The message digest to use.  GNUTLS_DIG_SHA256 is the safe
     choice unless you know what you're doing.

     FLAGS: must be 0

     This function will sign the CRL with the issuer's private key, and
     will copy the issuer's information into the CRL.

     This must be the last step in a certificate CRL since all the
     previously set parameters are now signed.

     A known limitation of this function is, that a newly-signed CRL
     will not be fully functional (e.g., for signature verification),
     until it is exported an re-imported.

     After GnuTLS 3.6.1 the value of ‘dig’ may be ‘GNUTLS_DIG_UNKNOWN’ ,
     and in that case, a suitable but reasonable for the key algorithm
     will be selected.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     Since 2.12.0

gnutls_x509_crq_privkey_sign
----------------------------

 -- Function: int gnutls_x509_crq_privkey_sign (gnutls_x509_crq_t CRQ,
          gnutls_privkey_t KEY, gnutls_digest_algorithm_t DIG, unsigned
          int FLAGS)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     KEY: holds a private key

     DIG: The message digest to use, i.e., ‘GNUTLS_DIG_SHA1’

     FLAGS: must be 0

     This function will sign the certificate request with a private key.
     This must be the same key as the one used in
     ‘gnutls_x509_crt_set_key()’ since a certificate request is self
     signed.

     This must be the last step in a certificate request generation
     since all the previously set parameters are now signed.

     A known limitation of this function is, that a newly-signed request
     will not be fully functional (e.g., for signature verification),
     until it is exported an re-imported.

     After GnuTLS 3.6.1 the value of ‘dig’ may be ‘GNUTLS_DIG_UNKNOWN’ ,
     and in that case, a suitable but reasonable for the key algorithm
     will be selected.

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.  ‘GNUTLS_E_ASN1_VALUE_NOT_FOUND’ is returned if you
     didn't set all information in the certificate request (e.g., the
     version using ‘gnutls_x509_crq_set_version()’ ).

     *Since:* 2.12.0

gnutls_x509_crq_set_pubkey
--------------------------

 -- Function: int gnutls_x509_crq_set_pubkey (gnutls_x509_crq_t CRQ,
          gnutls_pubkey_t KEY)
     CRQ: should contain a ‘gnutls_x509_crq_t’ type

     KEY: holds a public key

     This function will set the public parameters from the given public
     key to the request.  The ‘key’ can be deallocated after that.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0

gnutls_x509_crt_privkey_sign
----------------------------

 -- Function: int gnutls_x509_crt_privkey_sign (gnutls_x509_crt_t CRT,
          gnutls_x509_crt_t ISSUER, gnutls_privkey_t ISSUER_KEY,
          gnutls_digest_algorithm_t DIG, unsigned int FLAGS)
     CRT: a certificate of type ‘gnutls_x509_crt_t’

     ISSUER: is the certificate of the certificate issuer

     ISSUER_KEY: holds the issuer's private key

     DIG: The message digest to use, ‘GNUTLS_DIG_SHA256’ is a safe
     choice

     FLAGS: must be 0

     This function will sign the certificate with the issuer's private
     key, and will copy the issuer's information into the certificate.

     This must be the last step in a certificate generation since all
     the previously set parameters are now signed.

     A known limitation of this function is, that a newly-signed
     certificate will not be fully functional (e.g., for signature
     verification), until it is exported an re-imported.

     After GnuTLS 3.6.1 the value of ‘dig’ may be ‘GNUTLS_DIG_UNKNOWN’ ,
     and in that case, a suitable but reasonable for the key algorithm
     will be selected.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

gnutls_x509_crt_set_pubkey
--------------------------

 -- Function: int gnutls_x509_crt_set_pubkey (gnutls_x509_crt_t CRT,
          gnutls_pubkey_t KEY)
     CRT: should contain a ‘gnutls_x509_crt_t’ type

     KEY: holds a public key

     This function will set the public parameters from the given public
     key to the certificate.  The ‘key’ can be deallocated after that.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     *Since:* 2.12.0


File: gnutls.info,  Node: Socket specific API,  Next: DANE API,  Prev: Abstract key API,  Up: API reference

E.10 Socket specific API
========================

The prototypes for the following functions lie in ‘gnutls/socket.h’.

gnutls_transport_set_fastopen
-----------------------------

 -- Function: void gnutls_transport_set_fastopen (gnutls_session_t
          SESSION, int FD, struct sockaddr * CONNECT_ADDR, socklen_t
          CONNECT_ADDRLEN, unsigned int FLAGS)
     SESSION: is a ‘gnutls_session_t’ type.

     FD: is the session's socket descriptor

     CONNECT_ADDR: is the address we want to connect to

     CONNECT_ADDRLEN: is the length of ‘connect_addr’

     FLAGS: must be zero

     Enables TCP Fast Open (TFO) for the specified TLS client session.
     That means that TCP connection establishment and the transmission
     of the first TLS client hello packet are combined.  The peer's
     address must be specified in ‘connect_addr’ and ‘connect_addrlen’ ,
     and the socket specified by ‘fd’ should not be connected.

     TFO only works for TCP sockets of type AF_INET and AF_INET6.  If
     the OS doesn't support TCP fast open this function will result to
     gnutls using ‘connect()’ transparently during the first write.

     *Note:* This function overrides all the transport callback
     functions.  If this is undesirable, TCP Fast Open must be
     implemented on the user callback functions without calling this
     function.  When using this function, transport callbacks must not
     be set, and ‘gnutls_transport_set_ptr()’ or
     ‘gnutls_transport_set_int()’ must not be called.

     On GNU/Linux TFO has to be enabled at the system layer, that is in
     /proc/sys/net/ipv4/tcp_fastopen, bit 0 has to be set.

     This function has no effect on server sessions.

     *Since:* 3.5.3


File: gnutls.info,  Node: DANE API,  Next: Cryptographic API,  Prev: Socket specific API,  Up: API reference

E.11 DANE API
=============

The following functions are to be used for DANE certificate
verification.  Their prototypes lie in ‘gnutls/dane.h’.  Note that you
need to link with the ‘libgnutls-dane’ library to use them.

dane_cert_type_name
-------------------

 -- Function: const char * dane_cert_type_name (dane_cert_type_t TYPE)
     TYPE: is a DANE match type

     Convert a ‘dane_cert_type_t’ value to a string.

     *Returns:* a string that contains the name of the specified type,
     or ‘NULL’ .

dane_cert_usage_name
--------------------

 -- Function: const char * dane_cert_usage_name (dane_cert_usage_t
          USAGE)
     USAGE: is a DANE certificate usage

     Convert a ‘dane_cert_usage_t’ value to a string.

     *Returns:* a string that contains the name of the specified type,
     or ‘NULL’ .

dane_match_type_name
--------------------

 -- Function: const char * dane_match_type_name (dane_match_type_t TYPE)
     TYPE: is a DANE match type

     Convert a ‘dane_match_type_t’ value to a string.

     *Returns:* a string that contains the name of the specified type,
     or ‘NULL’ .

dane_query_data
---------------

 -- Function: int dane_query_data (dane_query_t Q, unsigned int IDX,
          unsigned int * USAGE, unsigned int * TYPE, unsigned int *
          MATCH, gnutls_datum_t * DATA)
     Q: The query result structure

     IDX: The index of the query response.

     USAGE: The certificate usage (see ‘dane_cert_usage_t’ )

     TYPE: The certificate type (see ‘dane_cert_type_t’ )

     MATCH: The DANE matching type (see ‘dane_match_type_t’ )

     DATA: The DANE data.

     This function will provide the DANE data from the query response.

     *Returns:* On success, ‘DANE_E_SUCCESS’ (0) is returned, otherwise
     a negative error value.

dane_query_deinit
-----------------

 -- Function: void dane_query_deinit (dane_query_t Q)
     Q: The structure to be deinitialized

     This function will deinitialize a DANE query result structure.

dane_query_entries
------------------

 -- Function: unsigned int dane_query_entries (dane_query_t Q)
     Q: The query result structure

     This function will return the number of entries in a query.

     *Returns:* The number of entries.

dane_query_status
-----------------

 -- Function: dane_query_status_t dane_query_status (dane_query_t Q)
     Q: The query result structure

     This function will return the status of the query response.  See
     ‘dane_query_status_t’ for the possible types.

     *Returns:* The status type.

dane_query_tlsa
---------------

 -- Function: int dane_query_tlsa (dane_state_t S, dane_query_t * R,
          const char * HOST, const char * PROTO, unsigned int PORT)
     S: The DANE state structure

     R: A structure to place the result

     HOST: The host name to resolve.

     PROTO: The protocol type (tcp, udp, etc.)

     PORT: The service port number (eg.  443).

     This function will query the DNS server for the TLSA (DANE) data
     for the given host.

     *Returns:* On success, ‘DANE_E_SUCCESS’ (0) is returned, otherwise
     a negative error value.

dane_query_to_raw_tlsa
----------------------

 -- Function: int dane_query_to_raw_tlsa (dane_query_t Q, unsigned int *
          DATA_ENTRIES, char *** DANE_DATA, int ** DANE_DATA_LEN, int *
          SECURE, int * BOGUS)
     Q: The query result structure

     DATA_ENTRIES: Pointer set to the number of entries in the query

     DANE_DATA: Pointer to contain an array of DNS rdata items,
     terminated with a NULL pointer; caller must guarantee that the
     referenced data remains valid until ‘dane_query_deinit()’ is
     called.

     DANE_DATA_LEN: Pointer to contain the length n bytes of the
     dane_data items

     SECURE: Pointer set true if the result is validated securely, false
     if validation failed or the domain queried has no security info

     BOGUS: Pointer set true if the result was not secure due to a
     security failure

     This function will provide the DANE data from the query response.

     The pointers dane_data and dane_data_len are allocated with
     ‘gnutls_malloc()’ to contain the data from the query result
     structure (individual ‘dane_data’ items simply point to the
     original data and are not allocated separately).  The returned
     ‘dane_data’ are only valid during the lifetime of ‘q’ .

     *Returns:* On success, ‘DANE_E_SUCCESS’ (0) is returned, otherwise
     a negative error value.

dane_raw_tlsa
-------------

 -- Function: int dane_raw_tlsa (dane_state_t S, dane_query_t * R, char
          *const * DANE_DATA, const int * DANE_DATA_LEN, int SECURE, int
          BOGUS)
     S: The DANE state structure

     R: A structure to place the result

     DANE_DATA: array of DNS rdata items, terminated with a NULL
     pointer; caller must guarantee that the referenced data remains
     valid until ‘dane_query_deinit()’ is called.

     DANE_DATA_LEN: the length n bytes of the dane_data items

     SECURE: true if the result is validated securely, false if
     validation failed or the domain queried has no security info

     BOGUS: if the result was not secure (secure = 0) due to a security
     failure, and the result is due to a security failure, bogus is
     true.

     This function will fill in the TLSA (DANE) structure from the given
     raw DNS record data.  The ‘dane_data’ must be valid during the
     lifetime of the query.

     *Returns:* On success, ‘DANE_E_SUCCESS’ (0) is returned, otherwise
     a negative error value.

dane_state_deinit
-----------------

 -- Function: void dane_state_deinit (dane_state_t S)
     S: The structure to be deinitialized

     This function will deinitialize a DANE query structure.

dane_state_init
---------------

 -- Function: int dane_state_init (dane_state_t * S, unsigned int FLAGS)
     S: The structure to be initialized

     FLAGS: flags from the ‘dane_state_flags’ enumeration

     This function will initialize the backend resolver.  It is intended
     to be used in scenarios where multiple resolvings occur, to
     optimize against multiple re-initializations.

     *Returns:* On success, ‘DANE_E_SUCCESS’ (0) is returned, otherwise
     a negative error value.

dane_state_set_dlv_file
-----------------------

 -- Function: int dane_state_set_dlv_file (dane_state_t S, const char *
          FILE)
     S: The structure to be deinitialized

     FILE: The file holding the DLV keys.

     This function will set a file with trusted keys for DLV (DNSSEC
     Lookaside Validation).

dane_strerror
-------------

 -- Function: const char * dane_strerror (int ERROR)
     ERROR: is a DANE error code, a negative error code

     This function is similar to strerror.  The difference is that it
     accepts an error number returned by a gnutls function; In case of
     an unknown error a descriptive string is sent instead of ‘NULL’ .

     Error codes are always a negative error code.

     *Returns:* A string explaining the DANE error message.

dane_verification_status_print
------------------------------

 -- Function: int dane_verification_status_print (unsigned int STATUS,
          gnutls_datum_t * OUT, unsigned int FLAGS)
     STATUS: The status flags to be printed

     OUT: Newly allocated datum with (0) terminated string.

     FLAGS: should be zero

     This function will pretty print the status of a verification
     process - eg.  the one obtained by ‘dane_verify_crt()’ .

     The output ‘out’ needs to be deallocated using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

dane_verify_crt
---------------

 -- Function: int dane_verify_crt (dane_state_t S, const gnutls_datum_t
          * CHAIN, unsigned CHAIN_SIZE, gnutls_certificate_type_t
          CHAIN_TYPE, const char * HOSTNAME, const char * PROTO,
          unsigned int PORT, unsigned int SFLAGS, unsigned int VFLAGS,
          unsigned int * VERIFY)
     S: A DANE state structure (may be NULL)

     CHAIN: A certificate chain

     CHAIN_SIZE: The size of the chain

     CHAIN_TYPE: The type of the certificate chain

     HOSTNAME: The hostname associated with the chain

     PROTO: The protocol of the service connecting (e.g.  tcp)

     PORT: The port of the service connecting (e.g.  443)

     SFLAGS: Flags for the initialization of ‘s’ (if NULL)

     VFLAGS: Verification flags; an OR'ed list of ‘dane_verify_flags_t’
     .

     VERIFY: An OR'ed list of ‘dane_verify_status_t’ .

     This function will verify the given certificate chain against the
     CA constrains and/or the certificate available via DANE. If no
     information via DANE can be obtained the flag
     ‘DANE_VERIFY_NO_DANE_INFO’ is set.  If a DNSSEC signature is not
     available for the DANE record then the verify flag
     ‘DANE_VERIFY_NO_DNSSEC_DATA’ is set.

     Due to the many possible options of DANE, there is no single threat
     model countered.  When notifying the user about DANE verification
     results it may be better to mention: DANE verification did not
     reject the certificate, rather than mentioning a successful DANE
     verification.

     Note that this function is designed to be run in addition to PKIX -
     certificate chain - verification.  To be run independently the
     ‘DANE_VFLAG_ONLY_CHECK_EE_USAGE’ flag should be specified; then the
     function will check whether the key of the peer matches the key
     advertised in the DANE entry.

     *Returns:* a negative error code on error and ‘DANE_E_SUCCESS’ (0)
     when the DANE entries were successfully parsed, irrespective of
     whether they were verified (see ‘verify’ for that information).  If
     no usable entries were encountered
     ‘DANE_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

dane_verify_crt_raw
-------------------

 -- Function: int dane_verify_crt_raw (dane_state_t S, const
          gnutls_datum_t * CHAIN, unsigned CHAIN_SIZE,
          gnutls_certificate_type_t CHAIN_TYPE, dane_query_t R, unsigned
          int SFLAGS, unsigned int VFLAGS, unsigned int * VERIFY)
     S: A DANE state structure (may be NULL)

     CHAIN: A certificate chain

     CHAIN_SIZE: The size of the chain

     CHAIN_TYPE: The type of the certificate chain

     R: DANE data to check against

     SFLAGS: Flags for the initialization of ‘s’ (if NULL)

     VFLAGS: Verification flags; an OR'ed list of ‘dane_verify_flags_t’
     .

     VERIFY: An OR'ed list of ‘dane_verify_status_t’ .

     This is the low-level function of ‘dane_verify_crt()’ .  See the
     high level function for documentation.

     This function does not perform any resolving, it utilizes cached
     entries from ‘r’ .

     *Returns:* a negative error code on error and ‘DANE_E_SUCCESS’ (0)
     when the DANE entries were successfully parsed, irrespective of
     whether they were verified (see ‘verify’ for that information).  If
     no usable entries were encountered
     ‘DANE_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.

dane_verify_session_crt
-----------------------

 -- Function: int dane_verify_session_crt (dane_state_t S,
          gnutls_session_t SESSION, const char * HOSTNAME, const char *
          PROTO, unsigned int PORT, unsigned int SFLAGS, unsigned int
          VFLAGS, unsigned int * VERIFY)
     S: A DANE state structure (may be NULL)

     SESSION: A gnutls session

     HOSTNAME: The hostname associated with the chain

     PROTO: The protocol of the service connecting (e.g.  tcp)

     PORT: The port of the service connecting (e.g.  443)

     SFLAGS: Flags for the initialization of ‘s’ (if NULL)

     VFLAGS: Verification flags; an OR'ed list of ‘dane_verify_flags_t’
     .

     VERIFY: An OR'ed list of ‘dane_verify_status_t’ .

     This function will verify session's certificate chain against the
     CA constrains and/or the certificate available via DANE. See
     ‘dane_verify_crt()’ for more information.

     This will not verify the chain for validity; unless the DANE
     verification is restricted to end certificates, this must be be
     performed separately using ‘gnutls_certificate_verify_peers3()’ .

     *Returns:* a negative error code on error and ‘DANE_E_SUCCESS’ (0)
     when the DANE entries were successfully parsed, irrespective of
     whether they were verified (see ‘verify’ for that information).  If
     no usable entries were encountered
     ‘DANE_E_REQUESTED_DATA_NOT_AVAILABLE’ will be returned.


File: gnutls.info,  Node: Cryptographic API,  Next: Compatibility API,  Prev: DANE API,  Up: API reference

E.12 Cryptographic API
======================

The following functions are to be used for low-level cryptographic
operations.  Their prototypes lie in ‘gnutls/crypto.h’.

Note that due to historic reasons several functions, (e.g.  *note
gnutls_mac_list::, *note gnutls_mac_get_name::) of this API are part of
the *note Core TLS API::.

gnutls_aead_cipher_decrypt
--------------------------

 -- Function: int gnutls_aead_cipher_decrypt (gnutls_aead_cipher_hd_t
          HANDLE, const void * NONCE, size_t NONCE_LEN, const void *
          AUTH, size_t AUTH_LEN, size_t TAG_SIZE, const void * CTEXT,
          size_t CTEXT_LEN, void * PTEXT, size_t * PTEXT_LEN)
     HANDLE: is a ‘gnutls_aead_cipher_hd_t’ type.

     NONCE: the nonce to set

     NONCE_LEN: The length of the nonce

     AUTH: additional data to be authenticated

     AUTH_LEN: The length of the data

     TAG_SIZE: The size of the tag to use (use zero for the default)

     CTEXT: the data to decrypt (including the authentication tag)

     CTEXT_LEN: the length of data to decrypt (includes tag size)

     PTEXT: the decrypted data

     PTEXT_LEN: the length of decrypted data (initially must hold the
     maximum available size)

     This function will decrypt the given data using the algorithm
     specified by the context.  This function must be provided the
     complete data to be decrypted, including the authentication tag.
     On several AEAD ciphers, the authentication tag is appended to the
     ciphertext, though this is not a general rule.  This function will
     fail if the tag verification fails.

     *Returns:* Zero or a negative error code on verification failure or
     other error.

     *Since:* 3.4.0

gnutls_aead_cipher_decryptv2
----------------------------

 -- Function: int gnutls_aead_cipher_decryptv2 (gnutls_aead_cipher_hd_t
          HANDLE, const void * NONCE, size_t NONCE_LEN, const giovec_t *
          AUTH_IOV, int AUTH_IOVCNT, const giovec_t * IOV, int IOVCNT,
          void * TAG, size_t TAG_SIZE)
     HANDLE: is a ‘gnutls_aead_cipher_hd_t’ type.

     NONCE: the nonce to set

     NONCE_LEN: The length of the nonce

     AUTH_IOV: additional data to be authenticated

     AUTH_IOVCNT: The number of buffers in ‘auth_iov’

     IOV: the data to decrypt

     IOVCNT: The number of buffers in ‘iov’

     TAG: The authentication tag

     TAG_SIZE: The size of the tag to use (use zero for the default)

     This is similar to ‘gnutls_aead_cipher_decrypt()’ , but it performs
     in-place encryption on the provided data buffers.

     *Returns:* Zero or a negative error code on error.

     *Since:* 3.6.10

gnutls_aead_cipher_deinit
-------------------------

 -- Function: void gnutls_aead_cipher_deinit (gnutls_aead_cipher_hd_t
          HANDLE)
     HANDLE: is a ‘gnutls_aead_cipher_hd_t’ type.

     This function will deinitialize all resources occupied by the given
     authenticated-encryption context.

     *Since:* 3.4.0

gnutls_aead_cipher_encrypt
--------------------------

 -- Function: int gnutls_aead_cipher_encrypt (gnutls_aead_cipher_hd_t
          HANDLE, const void * NONCE, size_t NONCE_LEN, const void *
          AUTH, size_t AUTH_LEN, size_t TAG_SIZE, const void * PTEXT,
          size_t PTEXT_LEN, void * CTEXT, size_t * CTEXT_LEN)
     HANDLE: is a ‘gnutls_aead_cipher_hd_t’ type.

     NONCE: the nonce to set

     NONCE_LEN: The length of the nonce

     AUTH: additional data to be authenticated

     AUTH_LEN: The length of the data

     TAG_SIZE: The size of the tag to use (use zero for the default)

     PTEXT: the data to encrypt

     PTEXT_LEN: The length of data to encrypt

     CTEXT: the encrypted data including authentication tag

     CTEXT_LEN: the length of encrypted data (initially must hold the
     maximum available size, including space for tag)

     This function will encrypt the given data using the algorithm
     specified by the context.  The output data will contain the
     authentication tag.

     *Returns:* Zero or a negative error code on error.

     *Since:* 3.4.0

gnutls_aead_cipher_encryptv
---------------------------

 -- Function: int gnutls_aead_cipher_encryptv (gnutls_aead_cipher_hd_t
          HANDLE, const void * NONCE, size_t NONCE_LEN, const giovec_t *
          AUTH_IOV, int AUTH_IOVCNT, size_t TAG_SIZE, const giovec_t *
          IOV, int IOVCNT, void * CTEXT, size_t * CTEXT_LEN)
     HANDLE: is a ‘gnutls_aead_cipher_hd_t’ type.

     NONCE: the nonce to set

     NONCE_LEN: The length of the nonce

     AUTH_IOV: additional data to be authenticated

     AUTH_IOVCNT: The number of buffers in ‘auth_iov’

     TAG_SIZE: The size of the tag to use (use zero for the default)

     IOV: the data to be encrypted

     IOVCNT: The number of buffers in ‘iov’

     CTEXT: the encrypted data including authentication tag

     CTEXT_LEN: the length of encrypted data (initially must hold the
     maximum available size, including space for tag)

     This function will encrypt the provided data buffers using the
     algorithm specified by the context.  The output data will contain
     the authentication tag.

     *Returns:* Zero or a negative error code on error.

     *Since:* 3.6.3

gnutls_aead_cipher_encryptv2
----------------------------

 -- Function: int gnutls_aead_cipher_encryptv2 (gnutls_aead_cipher_hd_t
          HANDLE, const void * NONCE, size_t NONCE_LEN, const giovec_t *
          AUTH_IOV, int AUTH_IOVCNT, const giovec_t * IOV, int IOVCNT,
          void * TAG, size_t * TAG_SIZE)
     HANDLE: is a ‘gnutls_aead_cipher_hd_t’ type.

     NONCE: the nonce to set

     NONCE_LEN: The length of the nonce

     AUTH_IOV: additional data to be authenticated

     AUTH_IOVCNT: The number of buffers in ‘auth_iov’

     IOV: the data to be encrypted

     IOVCNT: The number of buffers in ‘iov’

     TAG: The authentication tag

     TAG_SIZE: The size of the tag to use (use zero for the default)

     This is similar to ‘gnutls_aead_cipher_encrypt()’ , but it performs
     in-place encryption on the provided data buffers.

     *Returns:* Zero or a negative error code on error.

     *Since:* 3.6.10

gnutls_aead_cipher_init
-----------------------

 -- Function: int gnutls_aead_cipher_init (gnutls_aead_cipher_hd_t *
          HANDLE, gnutls_cipher_algorithm_t CIPHER, const gnutls_datum_t
          * KEY)
     HANDLE: is a ‘gnutls_aead_cipher_hd_t’ type.

     CIPHER: the authenticated-encryption algorithm to use

     KEY: The key to be used for encryption

     This function will initialize an context that can be used for
     encryption/decryption of data.  This will effectively use the
     current crypto backend in use by gnutls or the cryptographic
     accelerator in use.

     *Returns:* Zero or a negative error code on error.

     *Since:* 3.4.0

gnutls_aead_cipher_set_key
--------------------------

 -- Function: int gnutls_aead_cipher_set_key (gnutls_aead_cipher_hd_t
          HANDLE, const gnutls_datum_t * KEY)
     HANDLE: is a ‘gnutls_aead_cipher_hd_t’ type.

     KEY: The key to be used for encryption

     This function will set a new key without re-initializing the
     context.

     *Returns:* Zero or a negative error code on error.

     *Since:* 3.7.5

gnutls_cipher_add_auth
----------------------

 -- Function: int gnutls_cipher_add_auth (gnutls_cipher_hd_t HANDLE,
          const void * PTEXT, size_t PTEXT_SIZE)
     HANDLE: is a ‘gnutls_cipher_hd_t’ type

     PTEXT: the data to be authenticated

     PTEXT_SIZE: the length of the data

     This function operates on authenticated encryption with associated
     data (AEAD) ciphers and authenticate the input data.  This function
     can only be called once and before any encryption operations.

     *Returns:* Zero or a negative error code on error.

     *Since:* 3.0

gnutls_cipher_decrypt
---------------------

 -- Function: int gnutls_cipher_decrypt (gnutls_cipher_hd_t HANDLE, void
          * CTEXT, size_t CTEXT_LEN)
     HANDLE: is a ‘gnutls_cipher_hd_t’ type

     CTEXT: the data to decrypt

     CTEXT_LEN: the length of data to decrypt

     This function will decrypt the given data using the algorithm
     specified by the context.

     Note that in AEAD ciphers, this will not check the tag.  You will
     need to compare the tag sent with the value returned from
     ‘gnutls_cipher_tag()’ .

     *Returns:* Zero or a negative error code on error.

     *Since:* 2.10.0

gnutls_cipher_decrypt2
----------------------

 -- Function: int gnutls_cipher_decrypt2 (gnutls_cipher_hd_t HANDLE,
          const void * CTEXT, size_t CTEXT_LEN, void * PTEXT, size_t
          PTEXT_LEN)
     HANDLE: is a ‘gnutls_cipher_hd_t’ type

     CTEXT: the data to decrypt

     CTEXT_LEN: the length of data to decrypt

     PTEXT: the decrypted data

     PTEXT_LEN: the available length for decrypted data

     This function will decrypt the given data using the algorithm
     specified by the context.  For block ciphers the ‘ctext_len’ must
     be a multiple of the block size.  For the supported ciphers the
     plaintext data length will equal the ciphertext size.

     Note that in AEAD ciphers, this will not check the tag.  You will
     need to compare the tag sent with the value returned from
     ‘gnutls_cipher_tag()’ .

     *Returns:* Zero or a negative error code on error.

     *Since:* 2.12.0

gnutls_cipher_decrypt3
----------------------

 -- Function: int gnutls_cipher_decrypt3 (gnutls_cipher_hd_t HANDLE,
          const void * CTEXT, size_t CTEXT_LEN, void * PTEXT, size_t *
          PTEXT_LEN, unsigned FLAGS)
     HANDLE: is a ‘gnutls_cipher_hd_t’ type

     CTEXT: the data to decrypt

     CTEXT_LEN: the length of data to decrypt

     PTEXT: the decrypted data

     PTEXT_LEN: the available length for decrypted data

     FLAGS: flags for padding

     This function will decrypt the given data using the algorithm
     specified by the context.  If ‘flags’ is specified, padding for the
     decrypted data will be removed accordingly and ‘ptext_len’ will be
     updated.

     *Returns:* Zero or a negative error code on error.

     *Since:* 3.7.7

gnutls_cipher_deinit
--------------------

 -- Function: void gnutls_cipher_deinit (gnutls_cipher_hd_t HANDLE)
     HANDLE: is a ‘gnutls_cipher_hd_t’ type

     This function will deinitialize all resources occupied by the given
     encryption context.

     *Since:* 2.10.0

gnutls_cipher_encrypt
---------------------

 -- Function: int gnutls_cipher_encrypt (gnutls_cipher_hd_t HANDLE, void
          * PTEXT, size_t PTEXT_LEN)
     HANDLE: is a ‘gnutls_cipher_hd_t’ type

     PTEXT: the data to encrypt

     PTEXT_LEN: the length of data to encrypt

     This function will encrypt the given data using the algorithm
     specified by the context.

     *Returns:* Zero or a negative error code on error.

     *Since:* 2.10.0

gnutls_cipher_encrypt2
----------------------

 -- Function: int gnutls_cipher_encrypt2 (gnutls_cipher_hd_t HANDLE,
          const void * PTEXT, size_t PTEXT_LEN, void * CTEXT, size_t
          CTEXT_LEN)
     HANDLE: is a ‘gnutls_cipher_hd_t’ type

     PTEXT: the data to encrypt

     PTEXT_LEN: the length of data to encrypt

     CTEXT: the encrypted data

     CTEXT_LEN: the available length for encrypted data

     This function will encrypt the given data using the algorithm
     specified by the context.  For block ciphers the ‘ptext_len’ must
     be a multiple of the block size.  For the supported ciphers the
     encrypted data length will equal the plaintext size.

     *Returns:* Zero or a negative error code on error.

     *Since:* 2.12.0

gnutls_cipher_encrypt3
----------------------

 -- Function: int gnutls_cipher_encrypt3 (gnutls_cipher_hd_t HANDLE,
          const void * PTEXT, size_t PTEXT_LEN, void * CTEXT, size_t *
          CTEXT_LEN, unsigned FLAGS)
     HANDLE: is a ‘gnutls_cipher_hd_t’ type

     PTEXT: the data to encrypt

     PTEXT_LEN: the length of data to encrypt

     CTEXT: the encrypted data

     CTEXT_LEN: the length of encrypted data (initially must hold the
     maximum available size)

     FLAGS: flags for padding

     This function will encrypt the given data using the algorithm
     specified by the context.  For block ciphers, ‘ptext_len’ is
     typically a multiple of the block size.  If not, the caller can
     instruct the function to pad the last block according to ‘flags’ .
     Currently, the only available padding scheme is
     ‘GNUTLS_CIPHER_PADDING_PKCS7’ .

     If ‘ctext’ is not ‘NULL’ , it must hold enough space to store
     resulting cipher text.  To check the required size, this function
     can be called with ‘ctext’ set to ‘NULL’ .  Then ‘ctext_len’ will
     be updated without performing actual encryption.

     *Returns:* Zero or a negative error code on error.

     *Since:* 3.7.7

gnutls_cipher_get_block_size
----------------------------

 -- Function: unsigned gnutls_cipher_get_block_size
          (gnutls_cipher_algorithm_t ALGORITHM)
     ALGORITHM: is an encryption algorithm

     *Returns:* the block size of the encryption algorithm.

     *Since:* 2.10.0

gnutls_cipher_get_iv_size
-------------------------

 -- Function: unsigned gnutls_cipher_get_iv_size
          (gnutls_cipher_algorithm_t ALGORITHM)
     ALGORITHM: is an encryption algorithm

     This function returns the size of the initialization vector (IV)
     for the provided algorithm.  For algorithms with variable size IV
     (e.g., AES-CCM), the returned size will be the one used by TLS.

     *Returns:* block size for encryption algorithm.

     *Since:* 3.2.0

gnutls_cipher_get_tag_size
--------------------------

 -- Function: unsigned gnutls_cipher_get_tag_size
          (gnutls_cipher_algorithm_t ALGORITHM)
     ALGORITHM: is an encryption algorithm

     This function returns the tag size of an authenticated encryption
     algorithm.  For non-AEAD algorithms, it returns zero.

     *Returns:* the tag size of the authenticated encryption algorithm.

     *Since:* 3.2.2

gnutls_cipher_init
------------------

 -- Function: int gnutls_cipher_init (gnutls_cipher_hd_t * HANDLE,
          gnutls_cipher_algorithm_t CIPHER, const gnutls_datum_t * KEY,
          const gnutls_datum_t * IV)
     HANDLE: is a ‘gnutls_cipher_hd_t’ type

     CIPHER: the encryption algorithm to use

     KEY: the key to be used for encryption/decryption

     IV: the IV to use (if not applicable set NULL)

     This function will initialize the ‘handle’ context to be usable for
     encryption/decryption of data.  This will effectively use the
     current crypto backend in use by gnutls or the cryptographic
     accelerator in use.

     *Returns:* Zero or a negative error code on error.

     *Since:* 2.10.0

gnutls_cipher_set_iv
--------------------

 -- Function: void gnutls_cipher_set_iv (gnutls_cipher_hd_t HANDLE, void
          * IV, size_t IVLEN)
     HANDLE: is a ‘gnutls_cipher_hd_t’ type

     IV: the IV to set

     IVLEN: the length of the IV

     This function will set the IV to be used for the next encryption
     block.

     *Since:* 3.0

gnutls_cipher_tag
-----------------

 -- Function: int gnutls_cipher_tag (gnutls_cipher_hd_t HANDLE, void *
          TAG, size_t TAG_SIZE)
     HANDLE: is a ‘gnutls_cipher_hd_t’ type

     TAG: will hold the tag

     TAG_SIZE: the length of the tag to return

     This function operates on authenticated encryption with associated
     data (AEAD) ciphers and will return the output tag.

     *Returns:* Zero or a negative error code on error.

     *Since:* 3.0

gnutls_crypto_register_aead_cipher
----------------------------------

 -- Function: int gnutls_crypto_register_aead_cipher
          (gnutls_cipher_algorithm_t ALGORITHM, int PRIORITY,
          gnutls_cipher_init_func INIT, gnutls_cipher_setkey_func
          SETKEY, gnutls_cipher_aead_encrypt_func AEAD_ENCRYPT,
          gnutls_cipher_aead_decrypt_func AEAD_DECRYPT,
          gnutls_cipher_deinit_func DEINIT)
     ALGORITHM: is the gnutls AEAD cipher identifier

     PRIORITY: is the priority of the algorithm

     INIT: A function which initializes the cipher

     SETKEY: A function which sets the key of the cipher

     AEAD_ENCRYPT: Perform the AEAD encryption

     AEAD_DECRYPT: Perform the AEAD decryption

     DEINIT: A function which deinitializes the cipher

     This function will register a cipher algorithm to be used by
     gnutls.  Any algorithm registered will override the included
     algorithms and by convention kernel implemented algorithms have
     priority of 90 and CPU-assisted of 80.  The algorithm with the
     lowest priority will be used by gnutls.

     In the case the registered init or setkey functions return
     ‘GNUTLS_E_NEED_FALLBACK’ , GnuTLS will attempt to use the next in
     priority registered cipher.

     The functions registered will be used with the new AEAD API
     introduced in GnuTLS 3.4.0.  Internally GnuTLS uses the new AEAD
     API.

     *Deprecated:* since 3.7.0 it is no longer possible to override
     cipher implementation

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.4.0

gnutls_crypto_register_cipher
-----------------------------

 -- Function: int gnutls_crypto_register_cipher
          (gnutls_cipher_algorithm_t ALGORITHM, int PRIORITY,
          gnutls_cipher_init_func INIT, gnutls_cipher_setkey_func
          SETKEY, gnutls_cipher_setiv_func SETIV,
          gnutls_cipher_encrypt_func ENCRYPT, gnutls_cipher_decrypt_func
          DECRYPT, gnutls_cipher_deinit_func DEINIT)
     ALGORITHM: is the gnutls algorithm identifier

     PRIORITY: is the priority of the algorithm

     INIT: A function which initializes the cipher

     SETKEY: A function which sets the key of the cipher

     SETIV: A function which sets the nonce/IV of the cipher (non-AEAD)

     ENCRYPT: A function which performs encryption (non-AEAD)

     DECRYPT: A function which performs decryption (non-AEAD)

     DEINIT: A function which deinitializes the cipher

     This function will register a cipher algorithm to be used by
     gnutls.  Any algorithm registered will override the included
     algorithms and by convention kernel implemented algorithms have
     priority of 90 and CPU-assisted of 80.  The algorithm with the
     lowest priority will be used by gnutls.

     In the case the registered init or setkey functions return
     ‘GNUTLS_E_NEED_FALLBACK’ , GnuTLS will attempt to use the next in
     priority registered cipher.

     The functions which are marked as non-AEAD they are not required
     when registering a cipher to be used with the new AEAD API
     introduced in GnuTLS 3.4.0.  Internally GnuTLS uses the new AEAD
     API.

     *Deprecated:* since 3.7.0 it is no longer possible to override
     cipher implementation

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.4.0

gnutls_crypto_register_digest
-----------------------------

 -- Function: int gnutls_crypto_register_digest
          (gnutls_digest_algorithm_t ALGORITHM, int PRIORITY,
          gnutls_digest_init_func INIT, gnutls_digest_hash_func HASH,
          gnutls_digest_output_func OUTPUT, gnutls_digest_deinit_func
          DEINIT, gnutls_digest_fast_func HASH_FAST)
     ALGORITHM: is the gnutls digest identifier

     PRIORITY: is the priority of the algorithm

     INIT: A function which initializes the digest

     HASH: Perform the hash operation

     OUTPUT: Provide the output of the digest

     DEINIT: A function which deinitializes the digest

     HASH_FAST: Perform the digest operation in one go

     This function will register a digest algorithm to be used by
     gnutls.  Any algorithm registered will override the included
     algorithms and by convention kernel implemented algorithms have
     priority of 90 and CPU-assisted of 80.  The algorithm with the
     lowest priority will be used by gnutls.

     *Deprecated:* since 3.7.0 it is no longer possible to override
     cipher implementation

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.4.0

gnutls_crypto_register_mac
--------------------------

 -- Function: int gnutls_crypto_register_mac (gnutls_mac_algorithm_t
          ALGORITHM, int PRIORITY, gnutls_mac_init_func INIT,
          gnutls_mac_setkey_func SETKEY, gnutls_mac_setnonce_func
          SETNONCE, gnutls_mac_hash_func HASH, gnutls_mac_output_func
          OUTPUT, gnutls_mac_deinit_func DEINIT, gnutls_mac_fast_func
          HASH_FAST)
     ALGORITHM: is the gnutls MAC identifier

     PRIORITY: is the priority of the algorithm

     INIT: A function which initializes the MAC

     SETKEY: A function which sets the key of the MAC

     SETNONCE: A function which sets the nonce for the mac (may be
     ‘NULL’ for common MAC algorithms)

     HASH: Perform the hash operation

     OUTPUT: Provide the output of the MAC

     DEINIT: A function which deinitializes the MAC

     HASH_FAST: Perform the MAC operation in one go

     This function will register a MAC algorithm to be used by gnutls.
     Any algorithm registered will override the included algorithms and
     by convention kernel implemented algorithms have priority of 90 and
     CPU-assisted of 80.  The algorithm with the lowest priority will be
     used by gnutls.

     *Deprecated:* since 3.7.0 it is no longer possible to override
     cipher implementation

     *Returns:* ‘GNUTLS_E_SUCCESS’ on success, otherwise a negative
     error code.

     *Since:* 3.4.0

gnutls_decode_ber_digest_info
-----------------------------

 -- Function: int gnutls_decode_ber_digest_info (const gnutls_datum_t *
          INFO, gnutls_digest_algorithm_t * HASH, unsigned char *
          DIGEST, unsigned int * DIGEST_SIZE)
     INFO: an RSA BER encoded DigestInfo structure

     HASH: will contain the hash algorithm of the structure

     DIGEST: will contain the hash output of the structure

     DIGEST_SIZE: will contain the hash size of the structure; initially
     must hold the maximum size of ‘digest’

     This function will parse an RSA PKCS‘1’ 1.5 DigestInfo structure
     and report the hash algorithm used as well as the digest data.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise an error code is returned.

     *Since:* 3.5.0

gnutls_decode_gost_rs_value
---------------------------

 -- Function: int gnutls_decode_gost_rs_value (const gnutls_datum_t *
          SIG_VALUE, gnutls_datum_t * R, gnutls_datum_t * S)
     SIG_VALUE: will holds a GOST signature according to RFC 4491
     section 2.2.2

     R: will contain the r value

     S: will contain the s value

     This function will decode the provided ‘sig_value’ , into ‘r’ and
     ‘s’ elements.  See RFC 4491 section 2.2.2 for the format of
     signature value.

     The output values may be padded with a zero byte to prevent them
     from being interpreted as negative values.  The value should be
     deallocated using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise an error code is returned.

     *Since:* 3.6.0

gnutls_decode_rs_value
----------------------

 -- Function: int gnutls_decode_rs_value (const gnutls_datum_t *
          SIG_VALUE, gnutls_datum_t * R, gnutls_datum_t * S)
     SIG_VALUE: holds a Dss-Sig-Value DER or BER encoded structure

     R: will contain the r value

     S: will contain the s value

     This function will decode the provided ‘sig_value’ , into ‘r’ and
     ‘s’ elements.  The Dss-Sig-Value is used for DSA and ECDSA
     signatures.

     The output values may be padded with a zero byte to prevent them
     from being interpreted as negative values.  The value should be
     deallocated using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise an error code is returned.

     *Since:* 3.6.0

gnutls_encode_ber_digest_info
-----------------------------

 -- Function: int gnutls_encode_ber_digest_info
          (gnutls_digest_algorithm_t HASH, const gnutls_datum_t *
          DIGEST, gnutls_datum_t * OUTPUT)
     HASH: the hash algorithm that was used to get the digest

     DIGEST: must contain the digest data

     OUTPUT: will contain the allocated DigestInfo BER encoded data

     This function will encode the provided digest data, and its
     algorithm into an RSA PKCS‘1’ 1.5 DigestInfo structure.

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise an error code is returned.

     *Since:* 3.5.0

gnutls_encode_gost_rs_value
---------------------------

 -- Function: int gnutls_encode_gost_rs_value (gnutls_datum_t *
          SIG_VALUE, const gnutls_datum_t * R, const gnutls_datum_t * S)
     SIG_VALUE: will hold a GOST signature according to RFC 4491 section
     2.2.2

     R: must contain the r value

     S: must contain the s value

     This function will encode the provided r and s values, into binary
     representation according to RFC 4491 section 2.2.2, used for GOST R
     34.10-2001 (and thus also for GOST R 34.10-2012) signatures.

     The output value should be deallocated using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise an error code is returned.

     *Since:* 3.6.0

gnutls_encode_rs_value
----------------------

 -- Function: int gnutls_encode_rs_value (gnutls_datum_t * SIG_VALUE,
          const gnutls_datum_t * R, const gnutls_datum_t * S)
     SIG_VALUE: will hold a Dss-Sig-Value DER encoded structure

     R: must contain the r value

     S: must contain the s value

     This function will encode the provided r and s values, into a
     Dss-Sig-Value structure, used for DSA and ECDSA signatures.

     The output value should be deallocated using ‘gnutls_free()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise an error code is returned.

     *Since:* 3.6.0

gnutls_hash
-----------

 -- Function: int gnutls_hash (gnutls_hash_hd_t HANDLE, const void *
          PTEXT, size_t PTEXT_LEN)
     HANDLE: is a ‘gnutls_hash_hd_t’ type

     PTEXT: the data to hash

     PTEXT_LEN: the length of data to hash

     This function will hash the given data using the algorithm
     specified by the context.

     *Returns:* Zero or a negative error code on error.

     *Since:* 2.10.0

gnutls_hash_copy
----------------

 -- Function: gnutls_hash_hd_t gnutls_hash_copy (gnutls_hash_hd_t
          HANDLE)
     HANDLE: is a ‘gnutls_hash_hd_t’ type

     This function will create a copy of Message Digest context,
     containing all its current state.  Copying contexts for Message
     Digests registered using ‘gnutls_crypto_register_digest()’ is not
     supported and will always result in an error.  In addition to that,
     some of the Message Digest implementations do not support this
     operation.  Applications should check the return value and provide
     a proper fallback.

     *Returns:* new Message Digest context or NULL in case of an error.

     *Since:* 3.6.9

gnutls_hash_deinit
------------------

 -- Function: void gnutls_hash_deinit (gnutls_hash_hd_t HANDLE, void *
          DIGEST)
     HANDLE: is a ‘gnutls_hash_hd_t’ type

     DIGEST: is the output value of the hash

     This function will deinitialize all resources occupied by the given
     hash context.

     *Since:* 2.10.0

gnutls_hash_fast
----------------

 -- Function: int gnutls_hash_fast (gnutls_digest_algorithm_t ALGORITHM,
          const void * PTEXT, size_t PTEXT_LEN, void * DIGEST)
     ALGORITHM: the hash algorithm to use

     PTEXT: the data to hash

     PTEXT_LEN: the length of data to hash

     DIGEST: is the output value of the hash

     This convenience function will hash the given data and return
     output on a single call.

     *Returns:* Zero or a negative error code on error.

     *Since:* 2.10.0

gnutls_hash_get_len
-------------------

 -- Function: unsigned gnutls_hash_get_len (gnutls_digest_algorithm_t
          ALGORITHM)
     ALGORITHM: the hash algorithm to use

     This function will return the length of the output data of the
     given hash algorithm.

     *Returns:* The length or zero on error.

     *Since:* 2.10.0

gnutls_hash_init
----------------

 -- Function: int gnutls_hash_init (gnutls_hash_hd_t * DIG,
          gnutls_digest_algorithm_t ALGORITHM)
     DIG: is a ‘gnutls_hash_hd_t’ type

     ALGORITHM: the hash algorithm to use

     This function will initialize an context that can be used to
     produce a Message Digest of data.  This will effectively use the
     current crypto backend in use by gnutls or the cryptographic
     accelerator in use.

     *Returns:* Zero or a negative error code on error.

     *Since:* 2.10.0

gnutls_hash_output
------------------

 -- Function: void gnutls_hash_output (gnutls_hash_hd_t HANDLE, void *
          DIGEST)
     HANDLE: is a ‘gnutls_hash_hd_t’ type

     DIGEST: is the output value of the hash

     This function will output the current hash value and reset the
     state of the hash.  If ‘digest’ is ‘NULL’ , it only resets the
     state of the hash.

     *Since:* 2.10.0

gnutls_hash_squeeze
-------------------

 -- Function: int gnutls_hash_squeeze (gnutls_hash_hd_t HANDLE, void *
          OUTPUT, size_t LENGTH)
     HANDLE: a ‘gnutls_hash_hd_t’

     OUTPUT: destination to store the output; must be equal to or larger
     than ‘length’

     LENGTH: length of ‘output’

     This function will extract digest output of ‘length’ bytes.  The
     ‘handle’ must be initialized with ‘gnutls_hash_init()’ as an
     extended output function (XOF), such as ‘GNUTLS_DIG_SHAKE_128’ or
     ‘GNUTLS_DIG_SHAKE_256’ .

     This function can be called multiple times.  To reset the state of
     ‘handle’ , call ‘gnutls_hash_deinit()’ with ‘NULL’ as the digest
     argument.

     *Returns:* ‘GNUTLS_E_SUCCESS’ (0) on success; negative error code
     otherwise.

     *Since:* 3.8.6

gnutls_hkdf_expand
------------------

 -- Function: int gnutls_hkdf_expand (gnutls_mac_algorithm_t MAC, const
          gnutls_datum_t * KEY, const gnutls_datum_t * INFO, void *
          OUTPUT, size_t LENGTH)
     MAC: the mac algorithm used internally

     KEY: the pseudorandom key created with HKDF-Extract

     INFO: the optional informational data

     OUTPUT: the output value of the expand operation

     LENGTH: the desired length of the output key

     This function will derive a variable length keying material from
     the pseudorandom key using the HKDF-Expand function as defined in
     RFC 5869.

     *Returns:* Zero or a negative error code on error.

     *Since:* 3.6.13

gnutls_hkdf_extract
-------------------

 -- Function: int gnutls_hkdf_extract (gnutls_mac_algorithm_t MAC, const
          gnutls_datum_t * KEY, const gnutls_datum_t * SALT, void *
          OUTPUT)
     MAC: the mac algorithm used internally

     KEY: the initial keying material

     SALT: the optional salt

     OUTPUT: the output value of the extract operation

     This function will derive a fixed-size key using the HKDF-Extract
     function as defined in RFC 5869.

     *Returns:* Zero or a negative error code on error.

     *Since:* 3.6.13

gnutls_hmac
-----------

 -- Function: int gnutls_hmac (gnutls_hmac_hd_t HANDLE, const void *
          PTEXT, size_t PTEXT_LEN)
     HANDLE: is a ‘gnutls_hmac_hd_t’ type

     PTEXT: the data to hash

     PTEXT_LEN: the length of data to hash

     This function will hash the given data using the algorithm
     specified by the context.

     *Returns:* Zero or a negative error code on error.

     *Since:* 2.10.0

gnutls_hmac_copy
----------------

 -- Function: gnutls_hmac_hd_t gnutls_hmac_copy (gnutls_hmac_hd_t
          HANDLE)
     HANDLE: is a ‘gnutls_hmac_hd_t’ type

     This function will create a copy of MAC context, containing all its
     current state.  Copying contexts for MACs registered using
     ‘gnutls_crypto_register_mac()’ is not supported and will always
     result in an error.  In addition to that, some of the MAC
     implementations do not support this operation.  Applications should
     check the return value and provide a proper fallback.

     *Returns:* new MAC context or NULL in case of an error.

     *Since:* 3.6.9

gnutls_hmac_deinit
------------------

 -- Function: void gnutls_hmac_deinit (gnutls_hmac_hd_t HANDLE, void *
          DIGEST)
     HANDLE: is a ‘gnutls_hmac_hd_t’ type

     DIGEST: is the output value of the MAC

     This function will deinitialize all resources occupied by the given
     hmac context.

     *Since:* 2.10.0

gnutls_hmac_fast
----------------

 -- Function: int gnutls_hmac_fast (gnutls_mac_algorithm_t ALGORITHM,
          const void * KEY, size_t KEYLEN, const void * PTEXT, size_t
          PTEXT_LEN, void * DIGEST)
     ALGORITHM: the hash algorithm to use

     KEY: the key to use

     KEYLEN: the length of the key

     PTEXT: the data to hash

     PTEXT_LEN: the length of data to hash

     DIGEST: is the output value of the hash

     This convenience function will hash the given data and return
     output on a single call.  Note, this call will not work for MAC
     algorithms that require nonce (like UMAC or GMAC).

     *Returns:* Zero or a negative error code on error.

     *Since:* 2.10.0

gnutls_hmac_get_key_size
------------------------

 -- Function: unsigned gnutls_hmac_get_key_size (gnutls_mac_algorithm_t
          ALGORITHM)
     ALGORITHM: the mac algorithm to use

     This function will return the size of the key to be used with this
     algorithm.  On the algorithms which may accept arbitrary key sizes,
     the returned size is the MAC key size used in the TLS protocol.

     *Returns:* The key size or zero on error.

     *Since:* 3.6.12

gnutls_hmac_get_len
-------------------

 -- Function: unsigned gnutls_hmac_get_len (gnutls_mac_algorithm_t
          ALGORITHM)
     ALGORITHM: the hmac algorithm to use

     This function will return the length of the output data of the
     given hmac algorithm.

     *Returns:* The length or zero on error.

     *Since:* 2.10.0

gnutls_hmac_init
----------------

 -- Function: int gnutls_hmac_init (gnutls_hmac_hd_t * DIG,
          gnutls_mac_algorithm_t ALGORITHM, const void * KEY, size_t
          KEYLEN)
     DIG: is a ‘gnutls_hmac_hd_t’ type

     ALGORITHM: the HMAC algorithm to use

     KEY: the key to be used for encryption

     KEYLEN: the length of the key

     This function will initialize an context that can be used to
     produce a Message Authentication Code (MAC) of data.  This will
     effectively use the current crypto backend in use by gnutls or the
     cryptographic accelerator in use.

     Note that despite the name of this function, it can be used for
     other MAC algorithms than HMAC.

     *Returns:* Zero or a negative error code on error.

     *Since:* 2.10.0

gnutls_hmac_output
------------------

 -- Function: void gnutls_hmac_output (gnutls_hmac_hd_t HANDLE, void *
          DIGEST)
     HANDLE: is a ‘gnutls_hmac_hd_t’ type

     DIGEST: is the output value of the MAC

     This function will output the current MAC value and reset the state
     of the MAC.

     *Since:* 2.10.0

gnutls_hmac_set_nonce
---------------------

 -- Function: void gnutls_hmac_set_nonce (gnutls_hmac_hd_t HANDLE, const
          void * NONCE, size_t NONCE_LEN)
     HANDLE: is a ‘gnutls_hmac_hd_t’ type

     NONCE: the data to set as nonce

     NONCE_LEN: the length of data

     This function will set the nonce in the MAC algorithm.

     *Since:* 3.2.0

gnutls_mac_get_nonce_size
-------------------------

 -- Function: size_t gnutls_mac_get_nonce_size (gnutls_mac_algorithm_t
          ALGORITHM)
     ALGORITHM: is an encryption algorithm

     Returns the size of the nonce used by the MAC in TLS.

     *Returns:* length (in bytes) of the given MAC nonce size, or 0.

     *Since:* 3.2.0

gnutls_pbkdf2
-------------

 -- Function: int gnutls_pbkdf2 (gnutls_mac_algorithm_t MAC, const
          gnutls_datum_t * KEY, const gnutls_datum_t * SALT, unsigned
          ITER_COUNT, void * OUTPUT, size_t LENGTH)
     MAC: the mac algorithm used internally

     KEY: the initial keying material

     SALT: the salt

     ITER_COUNT: the iteration count

     OUTPUT: the output value

     LENGTH: the desired length of the output key

     This function will derive a variable length keying material from a
     password according to PKCS ‘5’ PBKDF2.

     *Returns:* Zero or a negative error code on error.

     *Since:* 3.6.13

gnutls_rnd
----------

 -- Function: int gnutls_rnd (gnutls_rnd_level_t LEVEL, void * DATA,
          size_t LEN)
     LEVEL: a security level

     DATA: place to store random bytes

     LEN: The requested size

     This function will generate random data and store it to output
     buffer.  The value of ‘level’ should be one of ‘GNUTLS_RND_NONCE’ ,
     ‘GNUTLS_RND_RANDOM’ and ‘GNUTLS_RND_KEY’ .  See the manual and
     ‘gnutls_rnd_level_t’ for detailed information.

     This function is thread-safe and also fork-safe.

     *Returns:* Zero on success, or a negative error code on error.

     *Since:* 2.12.0

gnutls_rnd_refresh
------------------

 -- Function: void gnutls_rnd_refresh ( VOID)

     This function refreshes the random generator state.  That is the
     current precise time, CPU usage, and other values are input into
     its state.

     On a slower rate input from /dev/urandom is mixed too.

     *Since:* 3.1.7


File: gnutls.info,  Node: Compatibility API,  Prev: Cryptographic API,  Up: API reference

E.13 Compatibility API
======================

The following functions are carried over from old GnuTLS released.  They
might be removed at a later version.  Their prototypes lie in
‘gnutls/compat.h’.

gnutls_compression_get
----------------------

 -- Function: gnutls_compression_method_t gnutls_compression_get
          (gnutls_session_t SESSION)
     SESSION: is a ‘gnutls_session_t’ type.

     Get the currently used compression algorithm.

     *Returns:* the currently used compression method, a
     ‘gnutls_compression_method_t’ value.

gnutls_compression_get_id
-------------------------

 -- Function: gnutls_compression_method_t gnutls_compression_get_id
          (const char * NAME)
     NAME: is a compression method name

     The names are compared in a case insensitive way.

     *Returns:* an id of the specified in a string compression method,
     or ‘GNUTLS_COMP_UNKNOWN’ on error.

gnutls_compression_get_name
---------------------------

 -- Function: const char * gnutls_compression_get_name
          (gnutls_compression_method_t ALGORITHM)
     ALGORITHM: is a Compression algorithm

     Convert a ‘gnutls_compression_method_t’ value to a string.

     *Returns:* a pointer to a string that contains the name of the
     specified compression algorithm, or ‘NULL’ .

gnutls_compression_list
-----------------------

 -- Function: const gnutls_compression_method_t *
          gnutls_compression_list ( VOID)

     Get a list of compression methods.

     *Returns:* a zero-terminated list of ‘gnutls_compression_method_t’
     integers indicating the available compression methods.

gnutls_global_set_mem_functions
-------------------------------

 -- Function: void gnutls_global_set_mem_functions
          (gnutls_alloc_function ALLOC_FUNC, gnutls_alloc_function
          SECURE_ALLOC_FUNC, gnutls_is_secure_function IS_SECURE_FUNC,
          gnutls_realloc_function REALLOC_FUNC, gnutls_free_function
          FREE_FUNC)
     ALLOC_FUNC: it's the default memory allocation function.  Like
     ‘malloc()’ .

     SECURE_ALLOC_FUNC: This is the memory allocation function that will
     be used for sensitive data.

     IS_SECURE_FUNC: a function that returns 0 if the memory given is
     not secure.  May be NULL.

     REALLOC_FUNC: A realloc function

     FREE_FUNC: The function that frees allocated data.  Must accept a
     NULL pointer.

     *Deprecated:* since 3.3.0 it is no longer possible to replace the
     internally used memory allocation functions

     This is the function where you set the memory allocation functions
     gnutls is going to use.  By default the libc's allocation functions
     (‘malloc()’ , ‘free()’ ), are used by gnutls, to allocate both
     sensitive and not sensitive data.  This function is provided to set
     the memory allocation functions to something other than the
     defaults

     This function must be called before ‘gnutls_global_init()’ is
     called.  This function is not thread safe.

gnutls_openpgp_privkey_sign_hash
--------------------------------

 -- Function: int gnutls_openpgp_privkey_sign_hash
          (gnutls_openpgp_privkey_t KEY, const gnutls_datum_t * HASH,
          gnutls_datum_t * SIGNATURE)
     KEY: Holds the key

     HASH: holds the data to be signed

     SIGNATURE: will contain newly allocated signature

     This function is no-op.

     *Returns:* ‘GNUTLS_E_UNIMPLEMENTED_FEATURE’ .

gnutls_priority_compression_list
--------------------------------

 -- Function: int gnutls_priority_compression_list (gnutls_priority_t
          PCACHE, const unsigned int ** LIST)
     PCACHE: is a ‘gnutls_priority_t’ type.

     LIST: will point to an integer list

     Get a list of available compression method in the priority
     structure.

     *Returns:* the number of methods, or an error code.

     *Since:* 3.0

gnutls_x509_crt_get_preferred_hash_algorithm
--------------------------------------------

 -- Function: int gnutls_x509_crt_get_preferred_hash_algorithm
          (gnutls_x509_crt_t CRT, gnutls_digest_algorithm_t * HASH,
          unsigned int * MAND)
     CRT: Holds the certificate

     HASH: The result of the call with the hash algorithm used for
     signature

     MAND: If non-zero it means that the algorithm MUST use this hash.
     May be ‘NULL’ .

     This function will read the certificate and return the appropriate
     digest algorithm to use for signing with this certificate.  Some
     certificates (i.e.  DSA might not be able to sign without the
     preferred algorithm).

     *Deprecated:* Please use
     ‘gnutls_pubkey_get_preferred_hash_algorithm()’ .

     *Returns:* the 0 if the hash algorithm is found.  A negative error
     code is returned on error.

     *Since:* 2.12.0

gnutls_x509_privkey_sign_hash
-----------------------------

 -- Function: int gnutls_x509_privkey_sign_hash (gnutls_x509_privkey_t
          KEY, const gnutls_datum_t * HASH, gnutls_datum_t * SIGNATURE)
     KEY: a key

     HASH: holds the data to be signed

     SIGNATURE: will contain newly allocated signature

     This function will sign the given hash using the private key.  Do
     not use this function directly unless you know what it is.  Typical
     signing requires the data to be hashed and stored in special
     formats (e.g.  BER Digest-Info for RSA).

     This API is provided only for backwards compatibility, and thus
     restricted to RSA, DSA and ECDSA key types.  For other key types
     please use ‘gnutls_privkey_sign_hash()’ and
     ‘gnutls_privkey_sign_data()’ .

     *Returns:* On success, ‘GNUTLS_E_SUCCESS’ (0) is returned,
     otherwise a negative error value.

     Deprecated in: 2.12.0


File: gnutls.info,  Node: Copying Information,  Next: Bibliography,  Prev: API reference,  Up: Top

Appendix F Copying Information
******************************

GNU Free Documentation License
==============================

                     Version 1.3, 3 November 2008

     Copyright © 2000, 2001, 2002, 2007, 2008 Free Software Foundation, Inc.
     <https://fsf.org/>

     Everyone is permitted to copy and distribute verbatim copies
     of this license document, but changing it is not allowed.

  0. PREAMBLE

     The purpose of this License is to make a manual, textbook, or other
     functional and useful document “free” in the sense of freedom: to
     assure everyone the effective freedom to copy and redistribute it,
     with or without modifying it, either commercially or
     noncommercially.  Secondarily, this License preserves for the
     author and publisher a way to get credit for their work, while not
     being considered responsible for modifications made by others.

     This License is a kind of "copyleft", which means that derivative
     works of the document must themselves be free in the same sense.
     It complements the GNU General Public License, which is a copyleft
     license designed for free software.

     We have designed this License in order to use it for manuals for
     free software, because free software needs free documentation: a
     free program should come with manuals providing the same freedoms
     that the software does.  But this License is not limited to
     software manuals; it can be used for any textual work, regardless
     of subject matter or whether it is published as a printed book.  We
     recommend this License principally for works whose purpose is
     instruction or reference.

  1. APPLICABILITY AND DEFINITIONS

     This License applies to any manual or other work, in any medium,
     that contains a notice placed by the copyright holder saying it can
     be distributed under the terms of this License.  Such a notice
     grants a world-wide, royalty-free license, unlimited in duration,
     to use that work under the conditions stated herein.  The
     "Document", below, refers to any such manual or work.  Any member
     of the public is a licensee, and is addressed as "you".  You accept
     the license if you copy, modify or distribute the work in a way
     requiring permission under copyright law.

     A "Modified Version" of the Document means any work containing the
     Document or a portion of it, either copied verbatim, or with
     modifications and/or translated into another language.

     A "Secondary Section" is a named appendix or a front-matter section
     of the Document that deals exclusively with the relationship of the
     publishers or authors of the Document to the Document's overall
     subject (or to related matters) and contains nothing that could
     fall directly within that overall subject.  (Thus, if the Document
     is in part a textbook of mathematics, a Secondary Section may not
     explain any mathematics.)  The relationship could be a matter of
     historical connection with the subject or with related matters, or
     of legal, commercial, philosophical, ethical or political position
     regarding them.

     The "Invariant Sections" are certain Secondary Sections whose
     titles are designated, as being those of Invariant Sections, in the
     notice that says that the Document is released under this License.
     If a section does not fit the above definition of Secondary then it
     is not allowed to be designated as Invariant.  The Document may
     contain zero Invariant Sections.  If the Document does not identify
     any Invariant Sections then there are none.

     The "Cover Texts" are certain short passages of text that are
     listed, as Front-Cover Texts or Back-Cover Texts, in the notice
     that says that the Document is released under this License.  A
     Front-Cover Text may be at most 5 words, and a Back-Cover Text may
     be at most 25 words.

     A "Transparent" copy of the Document means a machine-readable copy,
     represented in a format whose specification is available to the
     general public, that is suitable for revising the document
     straightforwardly with generic text editors or (for images composed
     of pixels) generic paint programs or (for drawings) some widely
     available drawing editor, and that is suitable for input to text
     formatters or for automatic translation to a variety of formats
     suitable for input to text formatters.  A copy made in an otherwise
     Transparent file format whose markup, or absence of markup, has
     been arranged to thwart or discourage subsequent modification by
     readers is not Transparent.  An image format is not Transparent if
     used for any substantial amount of text.  A copy that is not
     "Transparent" is called "Opaque".

     Examples of suitable formats for Transparent copies include plain
     ASCII without markup, Texinfo input format, LaTeX input format,
     SGML or XML using a publicly available DTD, and standard-conforming
     simple HTML, PostScript or PDF designed for human modification.
     Examples of transparent image formats include PNG, XCF and JPG.
     Opaque formats include proprietary formats that can be read and
     edited only by proprietary word processors, SGML or XML for which
     the DTD and/or processing tools are not generally available, and
     the machine-generated HTML, PostScript or PDF produced by some word
     processors for output purposes only.

     The "Title Page" means, for a printed book, the title page itself,
     plus such following pages as are needed to hold, legibly, the
     material this License requires to appear in the title page.  For
     works in formats which do not have any title page as such, "Title
     Page" means the text near the most prominent appearance of the
     work's title, preceding the beginning of the body of the text.

     The "publisher" means any person or entity that distributes copies
     of the Document to the public.

     A section "Entitled XYZ" means a named subunit of the Document
     whose title either is precisely XYZ or contains XYZ in parentheses
     following text that translates XYZ in another language.  (Here XYZ
     stands for a specific section name mentioned below, such as
     "Acknowledgements", "Dedications", "Endorsements", or "History".)
     To "Preserve the Title" of such a section when you modify the
     Document means that it remains a section "Entitled XYZ" according
     to this definition.

     The Document may include Warranty Disclaimers next to the notice
     which states that this License applies to the Document.  These
     Warranty Disclaimers are considered to be included by reference in
     this License, but only as regards disclaiming warranties: any other
     implication that these Warranty Disclaimers may have is void and
     has no effect on the meaning of this License.

  2. VERBATIM COPYING

     You may copy and distribute the Document in any medium, either
     commercially or noncommercially, provided that this License, the
     copyright notices, and the license notice saying this License
     applies to the Document are reproduced in all copies, and that you
     add no other conditions whatsoever to those of this License.  You
     may not use technical measures to obstruct or control the reading
     or further copying of the copies you make or distribute.  However,
     you may accept compensation in exchange for copies.  If you
     distribute a large enough number of copies you must also follow the
     conditions in section 3.

     You may also lend copies, under the same conditions stated above,
     and you may publicly display copies.

  3. COPYING IN QUANTITY

     If you publish printed copies (or copies in media that commonly
     have printed covers) of the Document, numbering more than 100, and
     the Document's license notice requires Cover Texts, you must
     enclose the copies in covers that carry, clearly and legibly, all
     these Cover Texts: Front-Cover Texts on the front cover, and
     Back-Cover Texts on the back cover.  Both covers must also clearly
     and legibly identify you as the publisher of these copies.  The
     front cover must present the full title with all words of the title
     equally prominent and visible.  You may add other material on the
     covers in addition.  Copying with changes limited to the covers, as
     long as they preserve the title of the Document and satisfy these
     conditions, can be treated as verbatim copying in other respects.

     If the required texts for either cover are too voluminous to fit
     legibly, you should put the first ones listed (as many as fit
     reasonably) on the actual cover, and continue the rest onto
     adjacent pages.

     If you publish or distribute Opaque copies of the Document
     numbering more than 100, you must either include a machine-readable
     Transparent copy along with each Opaque copy, or state in or with
     each Opaque copy a computer-network location from which the general
     network-using public has access to download using public-standard
     network protocols a complete Transparent copy of the Document, free
     of added material.  If you use the latter option, you must take
     reasonably prudent steps, when you begin distribution of Opaque
     copies in quantity, to ensure that this Transparent copy will
     remain thus accessible at the stated location until at least one
     year after the last time you distribute an Opaque copy (directly or
     through your agents or retailers) of that edition to the public.

     It is requested, but not required, that you contact the authors of
     the Document well before redistributing any large number of copies,
     to give them a chance to provide you with an updated version of the
     Document.

  4. MODIFICATIONS

     You may copy and distribute a Modified Version of the Document
     under the conditions of sections 2 and 3 above, provided that you
     release the Modified Version under precisely this License, with the
     Modified Version filling the role of the Document, thus licensing
     distribution and modification of the Modified Version to whoever
     possesses a copy of it.  In addition, you must do these things in
     the Modified Version:

       A. Use in the Title Page (and on the covers, if any) a title
          distinct from that of the Document, and from those of previous
          versions (which should, if there were any, be listed in the
          History section of the Document).  You may use the same title
          as a previous version if the original publisher of that
          version gives permission.

       B. List on the Title Page, as authors, one or more persons or
          entities responsible for authorship of the modifications in
          the Modified Version, together with at least five of the
          principal authors of the Document (all of its principal
          authors, if it has fewer than five), unless they release you
          from this requirement.

       C. State on the Title page the name of the publisher of the
          Modified Version, as the publisher.

       D. Preserve all the copyright notices of the Document.

       E. Add an appropriate copyright notice for your modifications
          adjacent to the other copyright notices.

       F. Include, immediately after the copyright notices, a license
          notice giving the public permission to use the Modified
          Version under the terms of this License, in the form shown in
          the Addendum below.

       G. Preserve in that license notice the full lists of Invariant
          Sections and required Cover Texts given in the Document's
          license notice.

       H. Include an unaltered copy of this License.

       I. Preserve the section Entitled "History", Preserve its Title,
          and add to it an item stating at least the title, year, new
          authors, and publisher of the Modified Version as given on the
          Title Page.  If there is no section Entitled "History" in the
          Document, create one stating the title, year, authors, and
          publisher of the Document as given on its Title Page, then add
          an item describing the Modified Version as stated in the
          previous sentence.

       J. Preserve the network location, if any, given in the Document
          for public access to a Transparent copy of the Document, and
          likewise the network locations given in the Document for
          previous versions it was based on.  These may be placed in the
          "History" section.  You may omit a network location for a work
          that was published at least four years before the Document
          itself, or if the original publisher of the version it refers
          to gives permission.

       K. For any section Entitled "Acknowledgements" or "Dedications",
          Preserve the Title of the section, and preserve in the section
          all the substance and tone of each of the contributor
          acknowledgements and/or dedications given therein.

       L. Preserve all the Invariant Sections of the Document, unaltered
          in their text and in their titles.  Section numbers or the
          equivalent are not considered part of the section titles.

       M. Delete any section Entitled "Endorsements".  Such a section
          may not be included in the Modified Version.

       N. Do not retitle any existing section to be Entitled
          "Endorsements" or to conflict in title with any Invariant
          Section.

       O. Preserve any Warranty Disclaimers.

     If the Modified Version includes new front-matter sections or
     appendices that qualify as Secondary Sections and contain no
     material copied from the Document, you may at your option designate
     some or all of these sections as invariant.  To do this, add their
     titles to the list of Invariant Sections in the Modified Version's
     license notice.  These titles must be distinct from any other
     section titles.

     You may add a section Entitled "Endorsements", provided it contains
     nothing but endorsements of your Modified Version by various
     parties--for example, statements of peer review or that the text
     has been approved by an organization as the authoritative
     definition of a standard.

     You may add a passage of up to five words as a Front-Cover Text,
     and a passage of up to 25 words as a Back-Cover Text, to the end of
     the list of Cover Texts in the Modified Version.  Only one passage
     of Front-Cover Text and one of Back-Cover Text may be added by (or
     through arrangements made by) any one entity.  If the Document
     already includes a cover text for the same cover, previously added
     by you or by arrangement made by the same entity you are acting on
     behalf of, you may not add another; but you may replace the old
     one, on explicit permission from the previous publisher that added
     the old one.

     The author(s) and publisher(s) of the Document do not by this
     License give permission to use their names for publicity for or to
     assert or imply endorsement of any Modified Version.

  5. COMBINING DOCUMENTS

     You may combine the Document with other documents released under
     this License, under the terms defined in section 4 above for
     modified versions, provided that you include in the combination all
     of the Invariant Sections of all of the original documents,
     unmodified, and list them all as Invariant Sections of your
     combined work in its license notice, and that you preserve all
     their Warranty Disclaimers.

     The combined work need only contain one copy of this License, and
     multiple identical Invariant Sections may be replaced with a single
     copy.  If there are multiple Invariant Sections with the same name
     but different contents, make the title of each such section unique
     by adding at the end of it, in parentheses, the name of the
     original author or publisher of that section if known, or else a
     unique number.  Make the same adjustment to the section titles in
     the list of Invariant Sections in the license notice of the
     combined work.

     In the combination, you must combine any sections Entitled
     "History" in the various original documents, forming one section
     Entitled "History"; likewise combine any sections Entitled
     "Acknowledgements", and any sections Entitled "Dedications".  You
     must delete all sections Entitled "Endorsements."

  6. COLLECTIONS OF DOCUMENTS

     You may make a collection consisting of the Document and other
     documents released under this License, and replace the individual
     copies of this License in the various documents with a single copy
     that is included in the collection, provided that you follow the
     rules of this License for verbatim copying of each of the documents
     in all other respects.

     You may extract a single document from such a collection, and
     distribute it individually under this License, provided you insert
     a copy of this License into the extracted document, and follow this
     License in all other respects regarding verbatim copying of that
     document.

  7. AGGREGATION WITH INDEPENDENT WORKS

     A compilation of the Document or its derivatives with other
     separate and independent documents or works, in or on a volume of a
     storage or distribution medium, is called an "aggregate" if the
     copyright resulting from the compilation is not used to limit the
     legal rights of the compilation's users beyond what the individual
     works permit.  When the Document is included in an aggregate, this
     License does not apply to the other works in the aggregate which
     are not themselves derivative works of the Document.

     If the Cover Text requirement of section 3 is applicable to these
     copies of the Document, then if the Document is less than one half
     of the entire aggregate, the Document's Cover Texts may be placed
     on covers that bracket the Document within the aggregate, or the
     electronic equivalent of covers if the Document is in electronic
     form.  Otherwise they must appear on printed covers that bracket
     the whole aggregate.

  8. TRANSLATION

     Translation is considered a kind of modification, so you may
     distribute translations of the Document under the terms of section
     4.  Replacing Invariant Sections with translations requires special
     permission from their copyright holders, but you may include
     translations of some or all Invariant Sections in addition to the
     original versions of these Invariant Sections.  You may include a
     translation of this License, and all the license notices in the
     Document, and any Warranty Disclaimers, provided that you also
     include the original English version of this License and the
     original versions of those notices and disclaimers.  In case of a
     disagreement between the translation and the original version of
     this License or a notice or disclaimer, the original version will
     prevail.

     If a section in the Document is Entitled "Acknowledgements",
     "Dedications", or "History", the requirement (section 4) to
     Preserve its Title (section 1) will typically require changing the
     actual title.

  9. TERMINATION

     You may not copy, modify, sublicense, or distribute the Document
     except as expressly provided under this License.  Any attempt
     otherwise to copy, modify, sublicense, or distribute it is void,
     and will automatically terminate your rights under this License.

     However, if you cease all violation of this License, then your
     license from a particular copyright holder is reinstated (a)
     provisionally, unless and until the copyright holder explicitly and
     finally terminates your license, and (b) permanently, if the
     copyright holder fails to notify you of the violation by some
     reasonable means prior to 60 days after the cessation.

     Moreover, your license from a particular copyright holder is
     reinstated permanently if the copyright holder notifies you of the
     violation by some reasonable means, this is the first time you have
     received notice of violation of this License (for any work) from
     that copyright holder, and you cure the violation prior to 30 days
     after your receipt of the notice.

     Termination of your rights under this section does not terminate
     the licenses of parties who have received copies or rights from you
     under this License.  If your rights have been terminated and not
     permanently reinstated, receipt of a copy of some or all of the
     same material does not give you any rights to use it.

  10. FUTURE REVISIONS OF THIS LICENSE

     The Free Software Foundation may publish new, revised versions of
     the GNU Free Documentation License from time to time.  Such new
     versions will be similar in spirit to the present version, but may
     differ in detail to address new problems or concerns.  See
     <https://www.gnu.org/licenses/>.

     Each version of the License is given a distinguishing version
     number.  If the Document specifies that a particular numbered
     version of this License "or any later version" applies to it, you
     have the option of following the terms and conditions either of
     that specified version or of any later version that has been
     published (not as a draft) by the Free Software Foundation.  If the
     Document does not specify a version number of this License, you may
     choose any version ever published (not as a draft) by the Free
     Software Foundation.  If the Document specifies that a proxy can
     decide which future versions of this License can be used, that
     proxy's public statement of acceptance of a version permanently
     authorizes you to choose that version for the Document.

  11. RELICENSING

     "Massive Multiauthor Collaboration Site" (or "MMC Site") means any
     World Wide Web server that publishes copyrightable works and also
     provides prominent facilities for anybody to edit those works.  A
     public wiki that anybody can edit is an example of such a server.
     A "Massive Multiauthor Collaboration" (or "MMC") contained in the
     site means any set of copyrightable works thus published on the MMC
     site.

     "CC-BY-SA" means the Creative Commons Attribution-Share Alike 3.0
     license published by Creative Commons Corporation, a not-for-profit
     corporation with a principal place of business in San Francisco,
     California, as well as future copyleft versions of that license
     published by that same organization.

     "Incorporate" means to publish or republish a Document, in whole or
     in part, as part of another Document.

     An MMC is "eligible for relicensing" if it is licensed under this
     License, and if all works that were first published under this
     License somewhere other than this MMC, and subsequently
     incorporated in whole or in part into the MMC, (1) had no cover
     texts or invariant sections, and (2) were thus incorporated prior
     to November 1, 2008.

     The operator of an MMC Site may republish an MMC contained in the
     site under CC-BY-SA on the same site at any time before August 1,
     2009, provided the MMC is eligible for relicensing.

ADDENDUM: How to use this License for your documents
====================================================

To use this License in a document you have written, include a copy of
the License in the document and put the following copyright and license
notices just after the title page:

       Copyright (C)  YEAR  YOUR NAME.
       Permission is granted to copy, distribute and/or modify this document
       under the terms of the GNU Free Documentation License, Version 1.3
       or any later version published by the Free Software Foundation;
       with no Invariant Sections, no Front-Cover Texts, and no Back-Cover
       Texts.  A copy of the license is included in the section entitled ``GNU
       Free Documentation License''.

If you have Invariant Sections, Front-Cover Texts and Back-Cover Texts,
replace the "with...Texts." line with this:

         with the Invariant Sections being LIST THEIR TITLES, with
         the Front-Cover Texts being LIST, and with the Back-Cover Texts
         being LIST.

If you have Invariant Sections without Cover Texts, or some other
combination of the three, merge those two alternatives to suit the
situation.

If your document contains nontrivial examples of program code, we
recommend releasing these examples in parallel under your choice of free
software license, such as the GNU General Public License, to permit
their use in free software.

