## Syntax highlighting for Fortran 90/95.

## Original author:  Pascal Gentil

syntax fortran "\.(f|for|f90|f95)$"
comment "!"

color red "\<[0-9]+\>"

icolor green "\<(action|advance|all|allocatable|allocated|any|apostrophe)\>"
icolor green "\<(append|asis|assign|assignment|associated|character|common)\>"
icolor green "\<(complex|data|default|delim|dimension|double precision)\>"
icolor green "\<(elemental|epsilon|external|file|fmt|form|format|huge)\>"
icolor green "\<(implicit|include|index|inquire|integer|intent|interface)\>"
icolor green "\<(intrinsic|iostat|kind|logical|module|none|null|only)\>"
icolor green "\<(operator|optional|pack|parameter|pointer|position|private)\>"
icolor green "\<(program|public|real|recl|recursive|selected_int_kind)\>"
icolor green "\<(selected_real_kind|subroutine|status)\>"

icolor cyan "\<(abs|achar|adjustl|adjustr|allocate|bit_size|call|char)\>"
icolor cyan "\<(close|contains|count|cpu_time|cshift|date_and_time)\>"
icolor cyan "\<(deallocate|digits|dot_product|eor|eoshift|function|iachar)\>"
icolor cyan "\<(iand|ibclr|ibits|ibset|ichar|ieor|iolength|ior|ishft|ishftc)\>"
icolor cyan "\<(lbound|len|len_trim|matmul|maxexponent|maxloc|maxval|merge)\>"
icolor cyan "\<(minexponent|minloc|minval|mvbits|namelist|nearest|nullify)\>"
icolor cyan "\<(open|pad|present|print|product|pure|quote|radix)\>"
icolor cyan "\<(random_number|random_seed|range|read|readwrite|replace)\>"
icolor cyan "\<(reshape|rewind|save|scan|sequence|shape|sign|size|spacing)\>"
icolor cyan "\<(spread|sum|system_clock|target|transfer|transpose|trim)\>"
icolor cyan "\<(ubound|unpack|verify|write|tiny|type|use|yes)\>"

icolor yellow "\<(.and.|case|do|else|else?if|else?where|end|end?do|end?if)\>"
icolor yellow "\<(end?select|.eqv.|forall|if|lge|lgt|lle|llt|.neqv.|.not.)\>"
icolor yellow "\<(.or.|repeat|select case|then|where|while)\>"

icolor magenta "\<(continue|cycle|exit|go?to|result|return)\>"

# Strings.
color yellow ""([^"\]|\\.)*""

# Comments.
color blue "!.*"
