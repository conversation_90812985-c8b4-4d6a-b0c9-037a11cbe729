<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html; charset=ANSI_X3.4-1968"><title>cygwin_attach_handle_to_fd</title><link rel="stylesheet" type="text/css" href="docbook.css"><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot"><link rel="home" href="cygwin-api.html" title="Cygwin API Reference"><link rel="up" href="func-cygwin-misc.html" title="Miscellaneous functions"><link rel="prev" href="func-cygwin-misc.html" title="Miscellaneous functions"><link rel="next" href="func-cygwin-internal.html" title="cygwin_internal"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table width="100%" summary="Navigation header"><tr><th colspan="3" align="center">cygwin_attach_handle_to_fd</th></tr><tr><td width="20%" align="left"><a accesskey="p" href="func-cygwin-misc.html">Prev</a>&#160;</td><th width="60%" align="center">Miscellaneous functions</th><td width="20%" align="right">&#160;<a accesskey="n" href="func-cygwin-internal.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="func-cygwin-attach-handle-to-fd"></a><div class="titlepage"></div><div class="refnamediv"><h2>Name</h2><p>cygwin_attach_handle_to_fd</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="funcsynopsis"><pre class="funcsynopsisinfo">
#include &lt;sys/cygwin.h&gt;
</pre><p><code class="funcdef">int
<b class="fsfunc">cygwin_attach_handle_to_fd</b>(</code>char *<var class="pdparam">name</var>, int <var class="pdparam">fd</var>, HANDLE <var class="pdparam">handle</var>, int <var class="pdparam">bin</var>, int <var class="pdparam">access</var><code>)</code>;</p></div></div><div class="refsect1"><a name="func-cygwin-attach-handle-to-fd-desc"></a><h2>Description</h2><p>This function can be used to turn a Win32 "handle" into a
posix-style file handle. <em class="parameter"><code>fd</code></em> may be -1 to
make cygwin allocate a handle; the actual handle is returned
in all cases.</p><p>Even after using function, Cygwin doesn't know anything about the
underlying file or device.  It just tries to supply the typical file
functions on a "best-effort" basis.  Use with care.  Don't expect too
much.</p></div></div><div class="navfooter"><hr><table width="100%" summary="Navigation footer"><tr><td width="40%" align="left"><a accesskey="p" href="func-cygwin-misc.html">Prev</a>&#160;</td><td width="20%" align="center"><a accesskey="u" href="func-cygwin-misc.html">Up</a></td><td width="40%" align="right">&#160;<a accesskey="n" href="func-cygwin-internal.html">Next</a></td></tr><tr><td width="40%" align="left" valign="top">Miscellaneous functions&#160;</td><td width="20%" align="center"><a accesskey="h" href="cygwin-api.html">Home</a></td><td width="40%" align="right" valign="top">&#160;cygwin_internal</td></tr></table></div></body></html>
