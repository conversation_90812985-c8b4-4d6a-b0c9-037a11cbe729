/*
 * usbprint.h
 *
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION (WINAPI_PARTITION_DESKTOP)
#define USBPRINT_IOCTL_INDEX 0x0000
#define IOCTL_USBPRINT_GET_LPT_STATUS CTL_CODE (FILE_DEVICE_UNKNOWN, USBPRINT_IOCTL_INDEX+12, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_USBPRINT_GET_1284_ID CTL_CODE (FILE_DEVICE_UNKNOWN, USBPRINT_IOCTL_INDEX+13, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_USBPRINT_VENDOR_SET_COMMAND CTL_CODE (FILE_DEVICE_UNKNOWN, USBPRINT_IOCTL_INDEX+14, <PERSON><PERSON><PERSON>_BUFFERED, FILE_ANY_ACCESS)
#define IOC<PERSON>_USBPRINT_VENDOR_GET_COMMAND CTL_CODE (FILE_DEVICE_UNKNOWN, USBPRINT_IOCTL_INDEX+15, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_USBPRINT_SOFT_RESET CTL_CODE (FILE_DEVICE_UNKNOWN, USBPRINT_IOCTL_INDEX+16, METHOD_BUFFERED, FILE_ANY_ACCESS)

DEFINE_GUID (GUID_DEVINTERFACE_USBPRINT, 0x28d78fad, 0x5a12, 0x11d1, 0xae, 0x5b, 0x0, 0x0, 0xf8, 0x3, 0xa8, 0xc2);
#endif
