.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_get_ours" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_get_ours \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const gnutls_datum_t * gnutls_certificate_get_ours(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.SH "DESCRIPTION"
Gets the certificate as sent to the peer in the last handshake.
The certificate is in raw (DER) format.  No certificate
list is being returned. Only the first certificate.

This function returns the certificate that was sent in the current
handshake. In subsequent resumed sessions this function will return
\fBNULL\fP. That differs from \fBgnutls_certificate_get_peers()\fP which always
returns the peer's certificate used in the original session.
.SH "RETURNS"
a pointer to a \fBgnutls_datum_t\fP containing our
certificate, or \fBNULL\fP in case of an error or if no certificate
was used.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
