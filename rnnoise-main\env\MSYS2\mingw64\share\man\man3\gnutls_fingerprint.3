.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_fingerprint" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_fingerprint \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_fingerprint(gnutls_digest_algorithm_t " algo ", const gnutls_datum_t * " data ", void * " result ", size_t * " result_size ");"
.SH ARGUMENTS
.IP "gnutls_digest_algorithm_t algo" 12
is a digest algorithm
.IP "const gnutls_datum_t * data" 12
is the data
.IP "void * result" 12
is the place where the result will be copied (may be null).
.IP "size_t * result_size" 12
should hold the size of the result. The actual size
of the returned result will also be copied there.
.SH "DESCRIPTION"
This function will calculate a fingerprint (actually a hash), of
the given data.  The result is not printable data.  You should
convert it to hex, or to something else printable.

This is the usual way to calculate a fingerprint of an X.509 DER
encoded certificate.  Note however that the fingerprint of an
OpenPGP certificate is not just a hash and cannot be calculated with this
function.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise
an error code is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
