.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_resp_verify" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_resp_verify \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_resp_verify(gnutls_ocsp_resp_const_t " resp ", gnutls_x509_trust_list_t " trustlist ", unsigned int * " verify ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_resp_const_t resp" 12
should contain a \fBgnutls_ocsp_resp_t\fP type
.IP "gnutls_x509_trust_list_t trustlist" 12
trust anchors as a \fBgnutls_x509_trust_list_t\fP type
.IP "unsigned int * verify" 12
output variable with verification status, an \fBgnutls_ocsp_verify_reason_t\fP
.IP "unsigned int flags" 12
verification flags from \fBgnutls_certificate_verify_flags\fP
.SH "DESCRIPTION"
Verify signature of the Basic OCSP Response against the public key
in the certificate of a trusted signer.  The  \fItrustlist\fP should be
populated with trust anchors.  The function will extract the signer
certificate from the Basic OCSP Response and will verify it against
the  \fItrustlist\fP .  A trusted signer is a certificate that is either
in  \fItrustlist\fP , or it is signed directly by a certificate in
 \fItrustlist\fP and has the id\-ad\-ocspSigning Extended Key Usage bit
set.

The output  \fIverify\fP variable will hold verification status codes
(e.g., \fBGNUTLS_OCSP_VERIFY_SIGNER_NOT_FOUND\fP,
\fBGNUTLS_OCSP_VERIFY_INSECURE_ALGORITHM\fP) which are only valid if the
function returned \fBGNUTLS_E_SUCCESS\fP.

Note that the function returns \fBGNUTLS_E_SUCCESS\fP even when
verification failed.  The caller must always inspect the  \fIverify\fP variable to find out the verification status.

The  \fIflags\fP variable should be 0 for now.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
