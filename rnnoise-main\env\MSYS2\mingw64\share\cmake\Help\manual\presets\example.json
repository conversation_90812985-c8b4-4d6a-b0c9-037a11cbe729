{"version": 10, "cmakeMinimumRequired": {"major": 3, "minor": 23, "patch": 0}, "$comment": "An example CMakePresets.json file", "include": ["otherThings.json", "moreThings.json"], "configurePresets": [{"$comment": ["This is a comment row.", "This is another comment,", "just because we can do it"], "name": "default", "displayName": "<PERSON><PERSON><PERSON>g", "description": "Default build using Ninja generator", "generator": "Ninja", "binaryDir": "${sourceDir}/build/default", "cacheVariables": {"FIRST_CACHE_VARIABLE": {"type": "BOOL", "value": "OFF"}, "SECOND_CACHE_VARIABLE": "ON"}, "environment": {"MY_ENVIRONMENT_VARIABLE": "Test", "PATH": "$env{HOME}/ninja/bin:$penv{PATH}"}, "vendor": {"example.com/ExampleIDE/1.0": {"autoFormat": true}}}, {"name": "ninja-multi", "inherits": "default", "displayName": "Ninja Multi-Config", "description": "Default build using Ninja Multi-Config generator", "generator": "Ninja Multi-Config"}, {"name": "windows-only", "inherits": "default", "displayName": "Windows-only configuration", "description": "This build is only available on Windows", "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}}], "buildPresets": [{"name": "default", "configurePreset": "default"}], "testPresets": [{"name": "default", "configurePreset": "default", "output": {"outputOnFailure": true}, "execution": {"noTestsAction": "error", "stopOnFailure": true}}], "packagePresets": [{"name": "default", "configurePreset": "default", "generators": ["TGZ"]}], "workflowPresets": [{"name": "default", "steps": [{"type": "configure", "name": "default"}, {"type": "build", "name": "default"}, {"type": "test", "name": "default"}, {"type": "package", "name": "default"}]}], "vendor": {"example.com/ExampleIDE/1.0": {"autoFormat": false}}}