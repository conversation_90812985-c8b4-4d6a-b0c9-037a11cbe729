<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/container.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('container_8h.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">container.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>File formats.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__mt.html">lzma_mt</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Multithreading options.  <a href="structlzma__mt.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:af3ca20ff228b363a82515c1aee9e27bc" id="r_af3ca20ff228b363a82515c1aee9e27bc"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af3ca20ff228b363a82515c1aee9e27bc">LZMA_PRESET_DEFAULT</a>&#160;&#160;&#160;UINT32_C(6)</td></tr>
<tr class="memdesc:af3ca20ff228b363a82515c1aee9e27bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default compression preset.  <br /></td></tr>
<tr class="separator:af3ca20ff228b363a82515c1aee9e27bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a97e40265e355a21bd2465aaa5b85f03d" id="r_a97e40265e355a21bd2465aaa5b85f03d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a97e40265e355a21bd2465aaa5b85f03d">LZMA_PRESET_LEVEL_MASK</a>&#160;&#160;&#160;UINT32_C(0x1F)</td></tr>
<tr class="memdesc:a97e40265e355a21bd2465aaa5b85f03d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Mask for preset level.  <br /></td></tr>
<tr class="separator:a97e40265e355a21bd2465aaa5b85f03d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af524fe9af5737820fdadcd40a2c26deb" id="r_af524fe9af5737820fdadcd40a2c26deb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af524fe9af5737820fdadcd40a2c26deb">LZMA_PRESET_EXTREME</a>&#160;&#160;&#160;(UINT32_C(1) &lt;&lt; 31)</td></tr>
<tr class="memdesc:af524fe9af5737820fdadcd40a2c26deb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extreme compression preset.  <br /></td></tr>
<tr class="separator:af524fe9af5737820fdadcd40a2c26deb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada9cd20febb28b5ed6656de9184a86e9" id="r_ada9cd20febb28b5ed6656de9184a86e9"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ada9cd20febb28b5ed6656de9184a86e9">LZMA_TELL_NO_CHECK</a>&#160;&#160;&#160;UINT32_C(0x01)</td></tr>
<tr class="separator:ada9cd20febb28b5ed6656de9184a86e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae21fb746037c82735d40d428c462e078" id="r_ae21fb746037c82735d40d428c462e078"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae21fb746037c82735d40d428c462e078">LZMA_TELL_UNSUPPORTED_CHECK</a>&#160;&#160;&#160;UINT32_C(0x02)</td></tr>
<tr class="separator:ae21fb746037c82735d40d428c462e078"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0bdde702a77ff42b90a99c0bf4147b6b" id="r_a0bdde702a77ff42b90a99c0bf4147b6b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0bdde702a77ff42b90a99c0bf4147b6b">LZMA_TELL_ANY_CHECK</a>&#160;&#160;&#160;UINT32_C(0x04)</td></tr>
<tr class="separator:a0bdde702a77ff42b90a99c0bf4147b6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1289925ae1c63a8e86f69f3657118a4d" id="r_a1289925ae1c63a8e86f69f3657118a4d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1289925ae1c63a8e86f69f3657118a4d">LZMA_IGNORE_CHECK</a>&#160;&#160;&#160;UINT32_C(0x10)</td></tr>
<tr class="separator:a1289925ae1c63a8e86f69f3657118a4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a563c84b5f368b3dd00d92ea903c5c33d" id="r_a563c84b5f368b3dd00d92ea903c5c33d"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a563c84b5f368b3dd00d92ea903c5c33d">LZMA_CONCATENATED</a>&#160;&#160;&#160;UINT32_C(0x08)</td></tr>
<tr class="separator:a563c84b5f368b3dd00d92ea903c5c33d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa1f469ed3d4b2eaf12f8081657efc9d5" id="r_aa1f469ed3d4b2eaf12f8081657efc9d5"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa1f469ed3d4b2eaf12f8081657efc9d5">LZMA_FAIL_FAST</a>&#160;&#160;&#160;UINT32_C(0x20)</td></tr>
<tr class="separator:aa1f469ed3d4b2eaf12f8081657efc9d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a62c853cf7dbf008bdbd97b2685c3eabf" id="r_a62c853cf7dbf008bdbd97b2685c3eabf"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a62c853cf7dbf008bdbd97b2685c3eabf">lzma_easy_encoder_memusage</a> (uint32_t preset) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a62c853cf7dbf008bdbd97b2685c3eabf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate approximate memory usage of easy encoder.  <br /></td></tr>
<tr class="separator:a62c853cf7dbf008bdbd97b2685c3eabf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3562055d26c18fad067a7c7516eaddf5" id="r_a3562055d26c18fad067a7c7516eaddf5"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3562055d26c18fad067a7c7516eaddf5">lzma_easy_decoder_memusage</a> (uint32_t preset) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:a3562055d26c18fad067a7c7516eaddf5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate approximate decoder memory usage of a preset.  <br /></td></tr>
<tr class="separator:a3562055d26c18fad067a7c7516eaddf5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acbdad999c544872f0f5d242f0d1a4ed4" id="r_acbdad999c544872f0f5d242f0d1a4ed4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acbdad999c544872f0f5d242f0d1a4ed4">lzma_easy_encoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, uint32_t preset, <a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a> check) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:acbdad999c544872f0f5d242f0d1a4ed4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize .xz Stream encoder using a preset number.  <br /></td></tr>
<tr class="separator:acbdad999c544872f0f5d242f0d1a4ed4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac5e71f2711b57391186671967435faf2" id="r_ac5e71f2711b57391186671967435faf2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac5e71f2711b57391186671967435faf2">lzma_easy_buffer_encode</a> (uint32_t preset, <a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a> check, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator, const uint8_t *in, size_t in_size, uint8_t *out, size_t *out_pos, size_t out_size) lzma_nothrow</td></tr>
<tr class="memdesc:ac5e71f2711b57391186671967435faf2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-call .xz Stream encoding using a preset number.  <br /></td></tr>
<tr class="separator:ac5e71f2711b57391186671967435faf2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a97aec94c9fedd7646cfa51c4f4cd52" id="r_a1a97aec94c9fedd7646cfa51c4f4cd52"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1a97aec94c9fedd7646cfa51c4f4cd52">lzma_stream_encoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filters, <a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a> check) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a1a97aec94c9fedd7646cfa51c4f4cd52"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize .xz Stream encoder using a custom filter chain.  <br /></td></tr>
<tr class="separator:a1a97aec94c9fedd7646cfa51c4f4cd52"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7cf41496d77f4d346e006b26ed8e101" id="r_ad7cf41496d77f4d346e006b26ed8e101"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad7cf41496d77f4d346e006b26ed8e101">lzma_stream_encoder_mt_memusage</a> (const <a class="el" href="structlzma__mt.html">lzma_mt</a> *options) lzma_nothrow lzma_attr_pure</td></tr>
<tr class="memdesc:ad7cf41496d77f4d346e006b26ed8e101"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate approximate memory usage of multithreaded .xz encoder.  <br /></td></tr>
<tr class="separator:ad7cf41496d77f4d346e006b26ed8e101"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f8793518711ee84d1abf12ea3aaba42" id="r_a3f8793518711ee84d1abf12ea3aaba42"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3f8793518711ee84d1abf12ea3aaba42">lzma_stream_encoder_mt</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, const <a class="el" href="structlzma__mt.html">lzma_mt</a> *options) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a3f8793518711ee84d1abf12ea3aaba42"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize multithreaded .xz Stream encoder.  <br /></td></tr>
<tr class="separator:a3f8793518711ee84d1abf12ea3aaba42"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7eaeda1cb811ac1f5a6403691df8a894" id="r_a7eaeda1cb811ac1f5a6403691df8a894"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7eaeda1cb811ac1f5a6403691df8a894">lzma_mt_block_size</a> (const <a class="el" href="structlzma__filter.html">lzma_filter</a> *filters) lzma_nothrow</td></tr>
<tr class="memdesc:a7eaeda1cb811ac1f5a6403691df8a894"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate recommended Block size for multithreaded .xz encoder.  <br /></td></tr>
<tr class="separator:a7eaeda1cb811ac1f5a6403691df8a894"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a26fcc5bccdf3f862caa4c992d01e1a72" id="r_a26fcc5bccdf3f862caa4c992d01e1a72"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a26fcc5bccdf3f862caa4c992d01e1a72">lzma_alone_encoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, const <a class="el" href="structlzma__options__lzma.html">lzma_options_lzma</a> *options) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a26fcc5bccdf3f862caa4c992d01e1a72"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize .lzma encoder (legacy file format)  <br /></td></tr>
<tr class="separator:a26fcc5bccdf3f862caa4c992d01e1a72"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66d4366a47b8332bff2a512f44f5c45e" id="r_a66d4366a47b8332bff2a512f44f5c45e"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a66d4366a47b8332bff2a512f44f5c45e">lzma_stream_buffer_bound</a> (size_t uncompressed_size) lzma_nothrow</td></tr>
<tr class="memdesc:a66d4366a47b8332bff2a512f44f5c45e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate output buffer size for single-call Stream encoder.  <br /></td></tr>
<tr class="separator:a66d4366a47b8332bff2a512f44f5c45e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e645ccaeace3b13a6981e03c6e190ad" id="r_a6e645ccaeace3b13a6981e03c6e190ad"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6e645ccaeace3b13a6981e03c6e190ad">lzma_stream_buffer_encode</a> (<a class="el" href="structlzma__filter.html">lzma_filter</a> *filters, <a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a> check, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator, const uint8_t *in, size_t in_size, uint8_t *out, size_t *out_pos, size_t out_size) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a6e645ccaeace3b13a6981e03c6e190ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-call .xz Stream encoder.  <br /></td></tr>
<tr class="separator:a6e645ccaeace3b13a6981e03c6e190ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abfc8f11acf837b167aa94b7071b54c30" id="r_abfc8f11acf837b167aa94b7071b54c30"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abfc8f11acf837b167aa94b7071b54c30">lzma_microlzma_encoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, const <a class="el" href="structlzma__options__lzma.html">lzma_options_lzma</a> *options) lzma_nothrow</td></tr>
<tr class="memdesc:abfc8f11acf837b167aa94b7071b54c30"><td class="mdescLeft">&#160;</td><td class="mdescRight">MicroLZMA encoder.  <br /></td></tr>
<tr class="separator:abfc8f11acf837b167aa94b7071b54c30"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a02b7683ef98d8049788961370a8b28c0" id="r_a02b7683ef98d8049788961370a8b28c0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a02b7683ef98d8049788961370a8b28c0">lzma_stream_decoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, uint64_t memlimit, uint32_t flags) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a02b7683ef98d8049788961370a8b28c0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize .xz Stream decoder.  <br /></td></tr>
<tr class="separator:a02b7683ef98d8049788961370a8b28c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7179d178e6430c10e2006a467921e98e" id="r_a7179d178e6430c10e2006a467921e98e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7179d178e6430c10e2006a467921e98e">lzma_stream_decoder_mt</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, const <a class="el" href="structlzma__mt.html">lzma_mt</a> *options) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a7179d178e6430c10e2006a467921e98e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize multithreaded .xz Stream decoder.  <br /></td></tr>
<tr class="separator:a7179d178e6430c10e2006a467921e98e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a21cbebf2771617bb1e956385cfb353e3" id="r_a21cbebf2771617bb1e956385cfb353e3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a21cbebf2771617bb1e956385cfb353e3">lzma_auto_decoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, uint64_t memlimit, uint32_t flags) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a21cbebf2771617bb1e956385cfb353e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decode .xz, .lzma, and .lz (lzip) files with autodetection.  <br /></td></tr>
<tr class="separator:a21cbebf2771617bb1e956385cfb353e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f43c3a1035e5a226dcd298f4162b861" id="r_a5f43c3a1035e5a226dcd298f4162b861"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a5f43c3a1035e5a226dcd298f4162b861">lzma_alone_decoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, uint64_t memlimit) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a5f43c3a1035e5a226dcd298f4162b861"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize .lzma decoder (legacy file format)  <br /></td></tr>
<tr class="separator:a5f43c3a1035e5a226dcd298f4162b861"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a97689f5709e0db1e2dac450f5ce4e5eb" id="r_a97689f5709e0db1e2dac450f5ce4e5eb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a97689f5709e0db1e2dac450f5ce4e5eb">lzma_lzip_decoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, uint64_t memlimit, uint32_t flags) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:a97689f5709e0db1e2dac450f5ce4e5eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize .lz (lzip) decoder (a foreign file format)  <br /></td></tr>
<tr class="separator:a97689f5709e0db1e2dac450f5ce4e5eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa58f237f6cea97ef0eb9bf5c37a3008d" id="r_aa58f237f6cea97ef0eb9bf5c37a3008d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa58f237f6cea97ef0eb9bf5c37a3008d">lzma_stream_buffer_decode</a> (uint64_t *memlimit, uint32_t flags, const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *allocator, const uint8_t *in, size_t *in_pos, size_t in_size, uint8_t *out, size_t *out_pos, size_t out_size) lzma_nothrow lzma_attr_warn_unused_result</td></tr>
<tr class="memdesc:aa58f237f6cea97ef0eb9bf5c37a3008d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Single-call .xz Stream decoder.  <br /></td></tr>
<tr class="separator:aa58f237f6cea97ef0eb9bf5c37a3008d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa8372dae3e7c907c36f7bb5426aeacdf" id="r_aa8372dae3e7c907c36f7bb5426aeacdf"><td class="memItemLeft" align="right" valign="top"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa8372dae3e7c907c36f7bb5426aeacdf">lzma_microlzma_decoder</a> (<a class="el" href="structlzma__stream.html">lzma_stream</a> *strm, uint64_t comp_size, uint64_t uncomp_size, <a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a> uncomp_size_is_exact, uint32_t dict_size) lzma_nothrow</td></tr>
<tr class="memdesc:aa8372dae3e7c907c36f7bb5426aeacdf"><td class="mdescLeft">&#160;</td><td class="mdescRight">MicroLZMA decoder.  <br /></td></tr>
<tr class="separator:aa8372dae3e7c907c36f7bb5426aeacdf"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>File formats. </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead. </dd></dl>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="af3ca20ff228b363a82515c1aee9e27bc" name="af3ca20ff228b363a82515c1aee9e27bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af3ca20ff228b363a82515c1aee9e27bc">&#9670;&#160;</a></span>LZMA_PRESET_DEFAULT</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_PRESET_DEFAULT&#160;&#160;&#160;UINT32_C(6)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default compression preset. </p>
<p>It's not straightforward to recommend a default preset, because in some cases keeping the resource usage relatively low is more important that getting the maximum compression ratio. </p>

</div>
</div>
<a id="a97e40265e355a21bd2465aaa5b85f03d" name="a97e40265e355a21bd2465aaa5b85f03d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a97e40265e355a21bd2465aaa5b85f03d">&#9670;&#160;</a></span>LZMA_PRESET_LEVEL_MASK</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_PRESET_LEVEL_MASK&#160;&#160;&#160;UINT32_C(0x1F)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Mask for preset level. </p>
<p>This is useful only if you need to extract the level from the preset variable. That should be rare. </p>

</div>
</div>
<a id="af524fe9af5737820fdadcd40a2c26deb" name="af524fe9af5737820fdadcd40a2c26deb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af524fe9af5737820fdadcd40a2c26deb">&#9670;&#160;</a></span>LZMA_PRESET_EXTREME</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_PRESET_EXTREME&#160;&#160;&#160;(UINT32_C(1) &lt;&lt; 31)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extreme compression preset. </p>
<p>This flag modifies the preset to make the encoding significantly slower while improving the compression ratio only marginally. This is useful when you don't mind spending time to get as small result as possible.</p>
<p>This flag doesn't affect the memory usage requirements of the decoder (at least not significantly). The memory usage of the encoder may be increased a little but only at the lowest preset levels (0-3). </p>

</div>
</div>
<a id="ada9cd20febb28b5ed6656de9184a86e9" name="ada9cd20febb28b5ed6656de9184a86e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada9cd20febb28b5ed6656de9184a86e9">&#9670;&#160;</a></span>LZMA_TELL_NO_CHECK</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_TELL_NO_CHECK&#160;&#160;&#160;UINT32_C(0x01)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This flag makes <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> return LZMA_NO_CHECK if the input stream being decoded has no integrity check. Note that when used with <a class="el" href="#a21cbebf2771617bb1e956385cfb353e3" title="Decode .xz, .lzma, and .lz (lzip) files with autodetection.">lzma_auto_decoder()</a>, all .lzma files will trigger LZMA_NO_CHECK if LZMA_TELL_NO_CHECK is used. </p>

</div>
</div>
<a id="ae21fb746037c82735d40d428c462e078" name="ae21fb746037c82735d40d428c462e078"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae21fb746037c82735d40d428c462e078">&#9670;&#160;</a></span>LZMA_TELL_UNSUPPORTED_CHECK</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_TELL_UNSUPPORTED_CHECK&#160;&#160;&#160;UINT32_C(0x02)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This flag makes <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> return LZMA_UNSUPPORTED_CHECK if the input stream has an integrity check, but the type of the integrity check is not supported by this liblzma version or build. Such files can still be decoded, but the integrity check cannot be verified. </p>

</div>
</div>
<a id="a0bdde702a77ff42b90a99c0bf4147b6b" name="a0bdde702a77ff42b90a99c0bf4147b6b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0bdde702a77ff42b90a99c0bf4147b6b">&#9670;&#160;</a></span>LZMA_TELL_ANY_CHECK</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_TELL_ANY_CHECK&#160;&#160;&#160;UINT32_C(0x04)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This flag makes <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> return LZMA_GET_CHECK as soon as the type of the integrity check is known. The type can then be got with <a class="el" href="check_8h.html#a8d7c3ffabfd024485f03fa209536c746" title="Get the type of the integrity check.">lzma_get_check()</a>. </p>

</div>
</div>
<a id="a1289925ae1c63a8e86f69f3657118a4d" name="a1289925ae1c63a8e86f69f3657118a4d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1289925ae1c63a8e86f69f3657118a4d">&#9670;&#160;</a></span>LZMA_IGNORE_CHECK</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_IGNORE_CHECK&#160;&#160;&#160;UINT32_C(0x10)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This flag makes <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> not calculate and verify the integrity check of the compressed data in .xz files. This means that invalid integrity check values won't be detected and LZMA_DATA_ERROR won't be returned in such cases.</p>
<p>This flag only affects the checks of the compressed data itself; the CRC32 values in the .xz headers will still be verified normally.</p>
<p>Don't use this flag unless you know what you are doing. Possible reasons to use this flag:</p>
<ul>
<li>Trying to recover data from a corrupt .xz file.</li>
<li>Speeding up decompression, which matters mostly with SHA-256 or with files that have compressed extremely well. It's recommended to not use this flag for this purpose unless the file integrity is verified externally in some other way.</li>
</ul>
<p>Support for this flag was added in liblzma 5.1.4beta. </p>

</div>
</div>
<a id="a563c84b5f368b3dd00d92ea903c5c33d" name="a563c84b5f368b3dd00d92ea903c5c33d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a563c84b5f368b3dd00d92ea903c5c33d">&#9670;&#160;</a></span>LZMA_CONCATENATED</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_CONCATENATED&#160;&#160;&#160;UINT32_C(0x08)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This flag enables decoding of concatenated files with file formats that allow concatenating compressed files as is. From the formats currently supported by liblzma, only the .xz and .lz formats allow concatenated files. Concatenated files are not allowed with the legacy .lzma format.</p>
<p>This flag also affects the usage of the 'action' argument for <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>. When LZMA_CONCATENATED is used, <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> won't return LZMA_STREAM_END unless LZMA_FINISH is used as 'action'. Thus, the application has to set LZMA_FINISH in the same way as it does when encoding.</p>
<p>If LZMA_CONCATENATED is not used, the decoders still accept LZMA_FINISH as 'action' for <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>, but the usage of LZMA_FINISH isn't required. </p>

</div>
</div>
<a id="aa1f469ed3d4b2eaf12f8081657efc9d5" name="aa1f469ed3d4b2eaf12f8081657efc9d5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa1f469ed3d4b2eaf12f8081657efc9d5">&#9670;&#160;</a></span>LZMA_FAIL_FAST</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_FAIL_FAST&#160;&#160;&#160;UINT32_C(0x20)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This flag makes the threaded decoder report errors (like LZMA_DATA_ERROR) as soon as they are detected. This saves time when the application has no interest in a partially decompressed truncated or corrupt file. Note that due to timing randomness, if the same truncated or corrupt input is decompressed multiple times with this flag, a different amount of output may be produced by different runs, and even the error code might vary.</p>
<p>When using LZMA_FAIL_FAST, it is recommended to use LZMA_FINISH to tell the decoder when no more input will be coming because it can help fast detection and reporting of truncated files. Note that in this situation truncated files might be diagnosed with LZMA_DATA_ERROR instead of LZMA_OK or LZMA_BUF_ERROR!</p>
<p>Without this flag the threaded decoder will provide as much output as possible at first and then report the pending error. This default behavior matches the single-threaded decoder and provides repeatable behavior with truncated or corrupt input. There are a few special cases where the behavior can still differ like memory allocation failures (LZMA_MEM_ERROR).</p>
<p>Single-threaded decoders currently ignore this flag.</p>
<p>Support for this flag was added in liblzma 5.3.3alpha. Note that in older versions this flag isn't supported (LZMA_OPTIONS_ERROR) even by functions that ignore this flag in newer liblzma versions. </p>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="a62c853cf7dbf008bdbd97b2685c3eabf" name="a62c853cf7dbf008bdbd97b2685c3eabf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a62c853cf7dbf008bdbd97b2685c3eabf">&#9670;&#160;</a></span>lzma_easy_encoder_memusage()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_easy_encoder_memusage </td>
          <td>(</td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>preset</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculate approximate memory usage of easy encoder. </p>
<p>This function is a wrapper for <a class="el" href="filter_8h.html#a730f9391e85a5979bcd1b32643ae7176" title="Calculate approximate memory requirements for raw encoder.">lzma_raw_encoder_memusage()</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">preset</td><td>Compression preset (level and possible flags)</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Number of bytes of memory required for the given preset when encoding or UINT64_MAX on error. </dd></dl>

</div>
</div>
<a id="a3562055d26c18fad067a7c7516eaddf5" name="a3562055d26c18fad067a7c7516eaddf5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3562055d26c18fad067a7c7516eaddf5">&#9670;&#160;</a></span>lzma_easy_decoder_memusage()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_easy_decoder_memusage </td>
          <td>(</td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>preset</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculate approximate decoder memory usage of a preset. </p>
<p>This function is a wrapper for <a class="el" href="filter_8h.html#a58511249ae9206d7de7c5d1f05842297" title="Calculate approximate memory requirements for raw decoder.">lzma_raw_decoder_memusage()</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">preset</td><td>Compression preset (level and possible flags)</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Number of bytes of memory required to decompress a file that was compressed using the given preset or UINT64_MAX on error. </dd></dl>

</div>
</div>
<a id="acbdad999c544872f0f5d242f0d1a4ed4" name="acbdad999c544872f0f5d242f0d1a4ed4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acbdad999c544872f0f5d242f0d1a4ed4">&#9670;&#160;</a></span>lzma_easy_encoder()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_easy_encoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>preset</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a></td>          <td class="paramname"><span class="paramname"><em>check</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initialize .xz Stream encoder using a preset number. </p>
<p>This function is intended for those who just want to use the basic features of liblzma (that is, most developers out there).</p>
<p>If initialization fails (return value is not LZMA_OK), all the memory allocated for *strm by liblzma is always freed. Thus, there is no need to call <a class="el" href="base_8h.html#a854ff37464ae1225febf14db1af43308" title="Free memory allocated for the coder data structures.">lzma_end()</a> after failed initialization.</p>
<p>If initialization succeeds, use <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> to do the actual encoding. Valid values for 'action' (the second argument of <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>) are LZMA_RUN, LZMA_SYNC_FLUSH, LZMA_FULL_FLUSH, and LZMA_FINISH. In future, there may be compression levels or flags that don't support LZMA_SYNC_FLUSH.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">preset</td><td>Compression preset to use. A preset consist of level number and zero or more flags. Usually flags aren't used, so preset is simply a number [0, 9] which match the options -0 ... -9 of the xz command line tool. Additional flags can be set using bitwise-or with the preset level number, e.g. 6 | LZMA_PRESET_EXTREME. </td></tr>
    <tr><td class="paramname">check</td><td>Integrity check type to use. See <a class="el" href="check_8h.html" title="Integrity checks.">check.h</a> for available checks. The xz command line tool defaults to LZMA_CHECK_CRC64, which is a good choice if you are unsure. LZMA_CHECK_CRC32 is good too as long as the uncompressed file is not many gigabytes.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Initialization succeeded. Use <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> to encode your data.</li>
<li>LZMA_MEM_ERROR: Memory allocation failed.</li>
<li>LZMA_OPTIONS_ERROR: The given compression preset is not supported by this build of liblzma.</li>
<li>LZMA_UNSUPPORTED_CHECK: The given check type is not supported by this liblzma build.</li>
<li>LZMA_PROG_ERROR: One or more of the parameters have values that will never be valid. For example, strm == NULL. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="ac5e71f2711b57391186671967435faf2" name="ac5e71f2711b57391186671967435faf2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac5e71f2711b57391186671967435faf2">&#9670;&#160;</a></span>lzma_easy_buffer_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_easy_buffer_encode </td>
          <td>(</td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>preset</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a></td>          <td class="paramname"><span class="paramname"><em>check</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>in_size</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>out</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>out_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>out_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Single-call .xz Stream encoding using a preset number. </p>
<p>The maximum required output buffer size can be calculated with <a class="el" href="#a66d4366a47b8332bff2a512f44f5c45e" title="Calculate output buffer size for single-call Stream encoder.">lzma_stream_buffer_bound()</a>.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">preset</td><td>Compression preset to use. See the description in <a class="el" href="#acbdad999c544872f0f5d242f0d1a4ed4" title="Initialize .xz Stream encoder using a preset number.">lzma_easy_encoder()</a>. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">check</td><td>Type of the integrity check to calculate from uncompressed data. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_size</td><td>Size of the input buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out_pos</td><td>The next byte will be written to out[*out_pos]. *out_pos is updated only if encoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">out_size</td><td>Size of the out buffer; the first byte into which no data is written to is out[out_size].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Encoding was successful.</li>
<li>LZMA_BUF_ERROR: Not enough output buffer space.</li>
<li>LZMA_UNSUPPORTED_CHECK</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_DATA_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a1a97aec94c9fedd7646cfa51c4f4cd52" name="a1a97aec94c9fedd7646cfa51c4f4cd52"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a97aec94c9fedd7646cfa51c4f4cd52">&#9670;&#160;</a></span>lzma_stream_encoder()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_stream_encoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filters</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a></td>          <td class="paramname"><span class="paramname"><em>check</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initialize .xz Stream encoder using a custom filter chain. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">filters</td><td>Array of filters terminated with .id == LZMA_VLI_UNKNOWN. See filters.h for more information. </td></tr>
    <tr><td class="paramname">check</td><td>Type of the integrity check to calculate from uncompressed data.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Initialization was successful.</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_UNSUPPORTED_CHECK</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="ad7cf41496d77f4d346e006b26ed8e101" name="ad7cf41496d77f4d346e006b26ed8e101"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad7cf41496d77f4d346e006b26ed8e101">&#9670;&#160;</a></span>lzma_stream_encoder_mt_memusage()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_stream_encoder_mt_memusage </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__mt.html">lzma_mt</a> *</td>          <td class="paramname"><span class="paramname"><em>options</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculate approximate memory usage of multithreaded .xz encoder. </p>
<p>Since doing the encoding in threaded mode doesn't affect the memory requirements of single-threaded decompressor, you can use lzma_easy_decoder_memusage(options-&gt;preset) or lzma_raw_decoder_memusage(options-&gt;filters) to calculate the decompressor memory requirements.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">options</td><td>Compression options</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Number of bytes of memory required for encoding with the given options. If an error occurs, for example due to unsupported preset or filter chain, UINT64_MAX is returned. </dd></dl>

</div>
</div>
<a id="a3f8793518711ee84d1abf12ea3aaba42" name="a3f8793518711ee84d1abf12ea3aaba42"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3f8793518711ee84d1abf12ea3aaba42">&#9670;&#160;</a></span>lzma_stream_encoder_mt()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_stream_encoder_mt </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__mt.html">lzma_mt</a> *</td>          <td class="paramname"><span class="paramname"><em>options</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initialize multithreaded .xz Stream encoder. </p>
<p>This provides the functionality of <a class="el" href="#acbdad999c544872f0f5d242f0d1a4ed4" title="Initialize .xz Stream encoder using a preset number.">lzma_easy_encoder()</a> and <a class="el" href="#a1a97aec94c9fedd7646cfa51c4f4cd52" title="Initialize .xz Stream encoder using a custom filter chain.">lzma_stream_encoder()</a> as a single function for multithreaded use.</p>
<p>The supported actions for <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> are LZMA_RUN, LZMA_FULL_FLUSH, LZMA_FULL_BARRIER, and LZMA_FINISH. Support for LZMA_SYNC_FLUSH might be added in the future.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">options</td><td>Pointer to multithreaded compression options</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_UNSUPPORTED_CHECK</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a7eaeda1cb811ac1f5a6403691df8a894" name="a7eaeda1cb811ac1f5a6403691df8a894"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7eaeda1cb811ac1f5a6403691df8a894">&#9670;&#160;</a></span>lzma_mt_block_size()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t lzma_mt_block_size </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filters</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculate recommended Block size for multithreaded .xz encoder. </p>
<p>This calculates a recommended Block size for multithreaded encoding given a filter chain. This is used internally by <a class="el" href="#a3f8793518711ee84d1abf12ea3aaba42" title="Initialize multithreaded .xz Stream encoder.">lzma_stream_encoder_mt()</a> to determine the Block size if the block_size member is not set to the special value of 0 in the <a class="el" href="structlzma__mt.html" title="Multithreading options.">lzma_mt</a> options struct.</p>
<p>If one wishes to change the filters between Blocks, this function is helpful to set the block_size member of the <a class="el" href="structlzma__mt.html" title="Multithreading options.">lzma_mt</a> struct before calling <a class="el" href="#a3f8793518711ee84d1abf12ea3aaba42" title="Initialize multithreaded .xz Stream encoder.">lzma_stream_encoder_mt()</a>. Since the block_size member represents the maximum possible Block size for the multithreaded .xz encoder, one can use this function to find the maximum recommended Block size based on all planned filter chains. Otherwise, the multithreaded encoder will base its maximum Block size on the first filter chain used (if the block_size member is not set), which may unnecessarily limit the Block size for a later filter chain.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">filters</td><td>Array of filters terminated with .id == LZMA_VLI_UNKNOWN.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Recommended Block size in bytes, or UINT64_MAX if an error occurred. </dd></dl>

</div>
</div>
<a id="a26fcc5bccdf3f862caa4c992d01e1a72" name="a26fcc5bccdf3f862caa4c992d01e1a72"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a26fcc5bccdf3f862caa4c992d01e1a72">&#9670;&#160;</a></span>lzma_alone_encoder()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_alone_encoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__options__lzma.html">lzma_options_lzma</a> *</td>          <td class="paramname"><span class="paramname"><em>options</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initialize .lzma encoder (legacy file format) </p>
<p>The .lzma format is sometimes called the LZMA_Alone format, which is the reason for the name of this function. The .lzma format supports only the LZMA1 filter. There is no support for integrity checks like CRC32.</p>
<p>Use this function if and only if you need to create files readable by legacy LZMA tools such as LZMA Utils 4.32.x. Moving to the .xz format is strongly recommended.</p>
<p>The valid action values for <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> are LZMA_RUN and LZMA_FINISH. No kind of flushing is supported, because the file format doesn't make it possible.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">options</td><td>Pointer to encoder options</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a66d4366a47b8332bff2a512f44f5c45e" name="a66d4366a47b8332bff2a512f44f5c45e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a66d4366a47b8332bff2a512f44f5c45e">&#9670;&#160;</a></span>lzma_stream_buffer_bound()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t lzma_stream_buffer_bound </td>
          <td>(</td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>uncompressed_size</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculate output buffer size for single-call Stream encoder. </p>
<p>When trying to compress incompressible data, the encoded size will be slightly bigger than the input data. This function calculates how much output buffer space is required to be sure that <a class="el" href="#a6e645ccaeace3b13a6981e03c6e190ad" title="Single-call .xz Stream encoder.">lzma_stream_buffer_encode()</a> doesn't return LZMA_BUF_ERROR.</p>
<p>The calculated value is not exact, but it is guaranteed to be big enough. The actual maximum output space required may be slightly smaller (up to about 100 bytes). This should not be a problem in practice.</p>
<p>If the calculated maximum size doesn't fit into size_t or would make the Stream grow past LZMA_VLI_MAX (which should never happen in practice), zero is returned to indicate the error.</p>
<dl class="section note"><dt>Note</dt><dd>The limit calculated by this function applies only to single-call encoding. Multi-call encoding may (and probably will) have larger maximum expansion when encoding incompressible data. Currently there is no function to calculate the maximum expansion of multi-call encoding.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">uncompressed_size</td><td>Size in bytes of the uncompressed input data</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Maximum number of bytes needed to store the compressed data. </dd></dl>

</div>
</div>
<a id="a6e645ccaeace3b13a6981e03c6e190ad" name="a6e645ccaeace3b13a6981e03c6e190ad"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6e645ccaeace3b13a6981e03c6e190ad">&#9670;&#160;</a></span>lzma_stream_buffer_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_stream_buffer_encode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__filter.html">lzma_filter</a> *</td>          <td class="paramname"><span class="paramname"><em>filters</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="check_8h.html#a0a6100c719ac9aa49be3fdf7519e8c3f">lzma_check</a></td>          <td class="paramname"><span class="paramname"><em>check</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>in_size</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>out</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>out_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>out_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Single-call .xz Stream encoder. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">filters</td><td>Array of filters terminated with .id == LZMA_VLI_UNKNOWN. See filters.h for more information. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">check</td><td>Type of the integrity check to calculate from uncompressed data. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_size</td><td>Size of the input buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out_pos</td><td>The next byte will be written to out[*out_pos]. *out_pos is updated only if encoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">out_size</td><td>Size of the out buffer; the first byte into which no data is written to is out[out_size].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Encoding was successful.</li>
<li>LZMA_BUF_ERROR: Not enough output buffer space.</li>
<li>LZMA_UNSUPPORTED_CHECK</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_DATA_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="abfc8f11acf837b167aa94b7071b54c30" name="abfc8f11acf837b167aa94b7071b54c30"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abfc8f11acf837b167aa94b7071b54c30">&#9670;&#160;</a></span>lzma_microlzma_encoder()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_microlzma_encoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__options__lzma.html">lzma_options_lzma</a> *</td>          <td class="paramname"><span class="paramname"><em>options</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>MicroLZMA encoder. </p>
<p>The MicroLZMA format is a raw LZMA stream whose first byte (always 0x00) has been replaced with bitwise-negation of the LZMA properties (lc/lp/pb). This encoding ensures that the first byte of MicroLZMA stream is never 0x00. There is no end of payload marker and thus the uncompressed size must be stored separately. For the best error detection the dictionary size should be stored separately as well but alternatively one may use the uncompressed size as the dictionary size when decoding.</p>
<p>With the MicroLZMA encoder, <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> behaves slightly unusually. The action argument must be LZMA_FINISH and the return value will never be LZMA_OK. Thus the encoding is always done with a single <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> after the initialization. The benefit of the combination of initialization function and <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> is that memory allocations can be reused for better performance.</p>
<p><a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> will try to encode as much input as is possible to fit into the given output buffer. If not all input can be encoded, the stream will be finished without encoding all the input. The caller must check both input and output buffer usage after <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> (total_in and total_out in <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> can be convenient). Often <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> can fill the output buffer completely if there is a lot of input, but sometimes a few bytes may remain unused because the next LZMA symbol would require more space.</p>
<p><a class="el" href="structlzma__stream.html#a5ff28ea4e39148723c19f59811627904">lzma_stream.avail_out</a> must be at least 6. Otherwise LZMA_PROG_ERROR will be returned.</p>
<p>The LZMA dictionary should be reasonably low to speed up the encoder re-initialization. A good value is bigger than the resulting uncompressed size of most of the output chunks. For example, if output size is 4 KiB, dictionary size of 32 KiB or 64 KiB is good. If the data compresses extremely well, even 128 KiB may be useful.</p>
<p>The MicroLZMA format and this encoder variant were made with the EROFS file system in mind. This format may be convenient in other embedded uses too where many small streams are needed. XZ Embedded includes a decoder for this format.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">options</td><td>Pointer to encoder options</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_STREAM_END: All good. Check the amounts of input used and output produced. Store the amount of input used (uncompressed size) as it needs to be known to decompress the data.</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_PROG_ERROR: In addition to the generic reasons for this error code, this may also be returned if there isn't enough output space (6 bytes) to create a valid MicroLZMA stream. </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a02b7683ef98d8049788961370a8b28c0" name="a02b7683ef98d8049788961370a8b28c0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a02b7683ef98d8049788961370a8b28c0">&#9670;&#160;</a></span>lzma_stream_decoder()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_stream_decoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t</td>          <td class="paramname"><span class="paramname"><em>memlimit</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>flags</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initialize .xz Stream decoder. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">memlimit</td><td>Memory usage limit as bytes. Use UINT64_MAX to effectively disable the limiter. liblzma 5.2.3 and earlier don't allow 0 here and return LZMA_PROG_ERROR; later versions treat 0 as if 1 had been specified. </td></tr>
    <tr><td class="paramname">flags</td><td>Bitwise-or of zero or more of the decoder flags: LZMA_TELL_NO_CHECK, LZMA_TELL_UNSUPPORTED_CHECK, LZMA_TELL_ANY_CHECK, LZMA_IGNORE_CHECK, LZMA_CONCATENATED, LZMA_FAIL_FAST</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Initialization was successful.</li>
<li>LZMA_MEM_ERROR: Cannot allocate memory.</li>
<li>LZMA_OPTIONS_ERROR: Unsupported flags</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a7179d178e6430c10e2006a467921e98e" name="a7179d178e6430c10e2006a467921e98e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7179d178e6430c10e2006a467921e98e">&#9670;&#160;</a></span>lzma_stream_decoder_mt()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_stream_decoder_mt </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__mt.html">lzma_mt</a> *</td>          <td class="paramname"><span class="paramname"><em>options</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initialize multithreaded .xz Stream decoder. </p>
<p>The decoder can decode multiple Blocks in parallel. This requires that each Block Header contains the Compressed Size and Uncompressed size fields which are added by the multi-threaded encoder, see <a class="el" href="#a3f8793518711ee84d1abf12ea3aaba42" title="Initialize multithreaded .xz Stream encoder.">lzma_stream_encoder_mt()</a>.</p>
<p>A Stream with one Block will only utilize one thread. A Stream with multiple Blocks but without size information in Block Headers will be processed in single-threaded mode in the same way as done by <a class="el" href="#a02b7683ef98d8049788961370a8b28c0" title="Initialize .xz Stream decoder.">lzma_stream_decoder()</a>. Concatenated Streams are processed one Stream at a time; no inter-Stream parallelization is done.</p>
<p>This function behaves like <a class="el" href="#a02b7683ef98d8049788961370a8b28c0" title="Initialize .xz Stream decoder.">lzma_stream_decoder()</a> when options-&gt;threads == 1 and options-&gt;memlimit_threading &lt;= 1.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">options</td><td>Pointer to multithreaded compression options</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Initialization was successful.</li>
<li>LZMA_MEM_ERROR: Cannot allocate memory.</li>
<li>LZMA_MEMLIMIT_ERROR: Memory usage limit was reached.</li>
<li>LZMA_OPTIONS_ERROR: Unsupported flags.</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a21cbebf2771617bb1e956385cfb353e3" name="a21cbebf2771617bb1e956385cfb353e3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a21cbebf2771617bb1e956385cfb353e3">&#9670;&#160;</a></span>lzma_auto_decoder()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_auto_decoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t</td>          <td class="paramname"><span class="paramname"><em>memlimit</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>flags</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Decode .xz, .lzma, and .lz (lzip) files with autodetection. </p>
<p>This decoder autodetects between the .xz, .lzma, and .lz file formats, and calls <a class="el" href="#a02b7683ef98d8049788961370a8b28c0" title="Initialize .xz Stream decoder.">lzma_stream_decoder()</a>, <a class="el" href="#a5f43c3a1035e5a226dcd298f4162b861" title="Initialize .lzma decoder (legacy file format)">lzma_alone_decoder()</a>, or <a class="el" href="#a97689f5709e0db1e2dac450f5ce4e5eb" title="Initialize .lz (lzip) decoder (a foreign file format)">lzma_lzip_decoder()</a> once the type of the input file has been detected.</p>
<p>Support for .lz was added in 5.4.0.</p>
<p>If the flag LZMA_CONCATENATED is used and the input is a .lzma file: For historical reasons concatenated .lzma files aren't supported. If there is trailing data after one .lzma stream, <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> will return LZMA_DATA_ERROR. (<a class="el" href="#a5f43c3a1035e5a226dcd298f4162b861" title="Initialize .lzma decoder (legacy file format)">lzma_alone_decoder()</a> doesn't have such a check as it doesn't support any decoder flags. It will return LZMA_STREAM_END after one .lzma stream.)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">memlimit</td><td>Memory usage limit as bytes. Use UINT64_MAX to effectively disable the limiter. liblzma 5.2.3 and earlier don't allow 0 here and return LZMA_PROG_ERROR; later versions treat 0 as if 1 had been specified. </td></tr>
    <tr><td class="paramname">flags</td><td>Bitwise-or of zero or more of the decoder flags: LZMA_TELL_NO_CHECK, LZMA_TELL_UNSUPPORTED_CHECK, LZMA_TELL_ANY_CHECK, LZMA_IGNORE_CHECK, LZMA_CONCATENATED, LZMA_FAIL_FAST</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Initialization was successful.</li>
<li>LZMA_MEM_ERROR: Cannot allocate memory.</li>
<li>LZMA_OPTIONS_ERROR: Unsupported flags</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a5f43c3a1035e5a226dcd298f4162b861" name="a5f43c3a1035e5a226dcd298f4162b861"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5f43c3a1035e5a226dcd298f4162b861">&#9670;&#160;</a></span>lzma_alone_decoder()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_alone_decoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t</td>          <td class="paramname"><span class="paramname"><em>memlimit</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initialize .lzma decoder (legacy file format) </p>
<p>Valid 'action' arguments to <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> are LZMA_RUN and LZMA_FINISH. There is no need to use LZMA_FINISH, but it's allowed because it may simplify certain types of applications.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">memlimit</td><td>Memory usage limit as bytes. Use UINT64_MAX to effectively disable the limiter. liblzma 5.2.3 and earlier don't allow 0 here and return LZMA_PROG_ERROR; later versions treat 0 as if 1 had been specified.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="a97689f5709e0db1e2dac450f5ce4e5eb" name="a97689f5709e0db1e2dac450f5ce4e5eb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a97689f5709e0db1e2dac450f5ce4e5eb">&#9670;&#160;</a></span>lzma_lzip_decoder()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_lzip_decoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t</td>          <td class="paramname"><span class="paramname"><em>memlimit</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>flags</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initialize .lz (lzip) decoder (a foreign file format) </p>
<p>This decoder supports the .lz format version 0 and the unextended .lz format version 1:</p>
<ul>
<li>Files in the format version 0 were produced by lzip 1.3 and older. Such files aren't common but may be found from file archives as a few source packages were released in this format. People might have old personal files in this format too. Decompression support for the format version 0 was removed in lzip 1.18.</li>
<li>lzip 1.3 added decompression support for .lz format version 1 files. Compression support was added in lzip 1.4. In lzip 1.6 the .lz format version 1 was extended to support the Sync Flush marker. This extension is not supported by liblzma. <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> will return LZMA_DATA_ERROR at the location of the Sync Flush marker. In practice files with the Sync Flush marker are very rare and thus liblzma can decompress almost all .lz files.</li>
</ul>
<p>Just like with <a class="el" href="#a02b7683ef98d8049788961370a8b28c0" title="Initialize .xz Stream decoder.">lzma_stream_decoder()</a> for .xz files, LZMA_CONCATENATED should be used when decompressing normal standalone .lz files.</p>
<p>The .lz format allows putting non-.lz data at the end of a file after at least one valid .lz member. That is, one can append custom data at the end of a .lz file and the decoder is required to ignore it. In liblzma this is relevant only when LZMA_CONCATENATED is used. In that case <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> will return LZMA_STREAM_END and leave <a class="el" href="structlzma__stream.html#a72fdc738c793f07a5c29715aa57802cf">lzma_stream.next_in</a> pointing to the first byte of the non-.lz data. An exception to this is if the first 1-3 bytes of the non-.lz data are identical to the .lz magic bytes (0x4C, 0x5A, 0x49, 0x50; "LZIP" in US-ASCII). In such a case the 1-3 bytes will have been ignored by <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a>. If one wishes to locate the non-.lz data reliably, one must ensure that the first byte isn't 0x4C. Actually one should ensure that none of the first four bytes of trailing data are equal to the magic bytes because lzip &gt;= 1.20 requires it by default.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">memlimit</td><td>Memory usage limit as bytes. Use UINT64_MAX to effectively disable the limiter. </td></tr>
    <tr><td class="paramname">flags</td><td>Bitwise-or of flags, or zero for no flags. All decoder flags listed above are supported although only LZMA_CONCATENATED and (in very rare cases) LZMA_IGNORE_CHECK are actually useful. LZMA_TELL_NO_CHECK, LZMA_TELL_UNSUPPORTED_CHECK, and LZMA_FAIL_FAST do nothing. LZMA_TELL_ANY_CHECK is supported for consistency only as CRC32 is always used in the .lz format.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Initialization was successful.</li>
<li>LZMA_MEM_ERROR: Cannot allocate memory.</li>
<li>LZMA_OPTIONS_ERROR: Unsupported flags</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="aa58f237f6cea97ef0eb9bf5c37a3008d" name="aa58f237f6cea97ef0eb9bf5c37a3008d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa58f237f6cea97ef0eb9bf5c37a3008d">&#9670;&#160;</a></span>lzma_stream_buffer_decode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_stream_buffer_decode </td>
          <td>(</td>
          <td class="paramtype">uint64_t *</td>          <td class="paramname"><span class="paramname"><em>memlimit</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>flags</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="structlzma__allocator.html">lzma_allocator</a> *</td>          <td class="paramname"><span class="paramname"><em>allocator</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const uint8_t *</td>          <td class="paramname"><span class="paramname"><em>in</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>in_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>in_size</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>out</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *</td>          <td class="paramname"><span class="paramname"><em>out_pos</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>out_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Single-call .xz Stream decoder. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir"></td><td class="paramname">memlimit</td><td>Pointer to how much memory the decoder is allowed to allocate. The value pointed by this pointer is modified if and only if LZMA_MEMLIMIT_ERROR is returned. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">flags</td><td>Bitwise-or of zero or more of the decoder flags: LZMA_TELL_NO_CHECK, LZMA_TELL_UNSUPPORTED_CHECK, LZMA_IGNORE_CHECK, LZMA_CONCATENATED, LZMA_FAIL_FAST. Note that LZMA_TELL_ANY_CHECK is not allowed and will return LZMA_PROG_ERROR. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">allocator</td><td><a class="el" href="structlzma__allocator.html" title="Custom functions for memory handling.">lzma_allocator</a> for custom allocator functions. Set to NULL to use malloc() and free(). </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in</td><td>Beginning of the input buffer </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_pos</td><td>The next byte will be read from in[*in_pos]. *in_pos is updated only if decoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">in_size</td><td>Size of the input buffer; the first byte that won't be read is in[in_size]. </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out</td><td>Beginning of the output buffer </td></tr>
    <tr><td class="paramdir">[out]</td><td class="paramname">out_pos</td><td>The next byte will be written to out[*out_pos]. *out_pos is updated only if decoding succeeds. </td></tr>
    <tr><td class="paramdir"></td><td class="paramname">out_size</td><td>Size of the out buffer; the first byte into which no data is written to is out[out_size].</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK: Decoding was successful.</li>
<li>LZMA_FORMAT_ERROR</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_DATA_ERROR</li>
<li>LZMA_NO_CHECK: This can be returned only if using the LZMA_TELL_NO_CHECK flag.</li>
<li>LZMA_UNSUPPORTED_CHECK: This can be returned only if using the LZMA_TELL_UNSUPPORTED_CHECK flag.</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_MEMLIMIT_ERROR: Memory usage limit was reached. The minimum required memlimit value was stored to *memlimit.</li>
<li>LZMA_BUF_ERROR: Output buffer was too small.</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
<a id="aa8372dae3e7c907c36f7bb5426aeacdf" name="aa8372dae3e7c907c36f7bb5426aeacdf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa8372dae3e7c907c36f7bb5426aeacdf">&#9670;&#160;</a></span>lzma_microlzma_decoder()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e">lzma_ret</a> lzma_microlzma_decoder </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structlzma__stream.html">lzma_stream</a> *</td>          <td class="paramname"><span class="paramname"><em>strm</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t</td>          <td class="paramname"><span class="paramname"><em>comp_size</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t</td>          <td class="paramname"><span class="paramname"><em>uncomp_size</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="base_8h.html#abbc819c74b484c846825ae1388a50a59">lzma_bool</a></td>          <td class="paramname"><span class="paramname"><em>uncomp_size_is_exact</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>dict_size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>MicroLZMA decoder. </p>
<p>See <a class="el" href="#abfc8f11acf837b167aa94b7071b54c30" title="MicroLZMA encoder.">lzma_microlzma_encoder()</a> for more information.</p>
<p>The <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> usage with this decoder is completely normal. The special behavior of <a class="el" href="base_8h.html#a28cc09bc422d5ba1e0187c9f2af5d957" title="Encode or decode data.">lzma_code()</a> applies to <a class="el" href="#abfc8f11acf837b167aa94b7071b54c30" title="MicroLZMA encoder.">lzma_microlzma_encoder()</a> only.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">strm</td><td>Pointer to <a class="el" href="structlzma__stream.html" title="Passing data to and from liblzma.">lzma_stream</a> that is at least initialized with LZMA_STREAM_INIT. </td></tr>
    <tr><td class="paramname">comp_size</td><td>Compressed size of the MicroLZMA stream. The caller must somehow know this exactly. </td></tr>
    <tr><td class="paramname">uncomp_size</td><td>Uncompressed size of the MicroLZMA stream. If the exact uncompressed size isn't known, this can be set to a value that is at most as big as the exact uncompressed size would be, but then the next argument uncomp_size_is_exact must be false. </td></tr>
    <tr><td class="paramname">uncomp_size_is_exact</td><td>If true, uncomp_size must be exactly correct. This will improve error detection at the end of the stream. If the exact uncompressed size isn't known, this must be false. uncomp_size must still be at most as big as the exact uncompressed size is. Setting this to false when the exact size is known will work but error detection at the end of the stream will be weaker. </td></tr>
    <tr><td class="paramname">dict_size</td><td>LZMA dictionary size that was used when compressing the data. It is OK to use a bigger value too but liblzma will then allocate more memory than would actually be required and error detection will be slightly worse. (Note that with the implementation in XZ Embedded it doesn't affect the memory usage if one specifies bigger dictionary than actually required.)</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Possible <a class="el" href="base_8h.html#a8494e0457e1463d6d2b6836018d87b6e" title="Return values used by several functions in liblzma.">lzma_ret</a> values:<ul>
<li>LZMA_OK</li>
<li>LZMA_MEM_ERROR</li>
<li>LZMA_OPTIONS_ERROR</li>
<li>LZMA_PROG_ERROR </li>
</ul>
</dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li><li class="navelem"><a class="el" href="container_8h.html">container.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
