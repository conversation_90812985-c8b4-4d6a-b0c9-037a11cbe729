/* Script for -Ur */
/* Copyright (C) 2014-2025 Free Software Foundation, Inc.
   Copying and distribution of this script, with or without modification,
   are permitted in any medium without royalty provided the copyright
   notice and this notice are preserved.  */
OUTPUT_FORMAT(pe-i386)
SEARCH_DIR("/usr/x86_64-pc-cygwin/lib");
SECTIONS
{
  .text  :
  {
    *(.text)
  }
  /* The Cygwin32 library uses a section to avoid copying certain data
     on fork.  This used to be named ".data".  The linker used
     to include this between __data_start__ and __data_end__, but that
     breaks building the cygwin32 dll.  Instead, we name the section
     ".data_cygwin_nocopy" and explicitly include it after __data_end__. */
  .data  :
  {
    *(.data)
    KEEP(*(.jcr))
  }
  .rdata  :
  {
    *(.rdata)
    . = ALIGN(4);
    /* read-only parts of .didat */
    /* This cannot currently be handled with grouped sections.
	See pe.em:sort_sections.  */
    /* .ctors & .dtors */
       /* Note: we always define __CTOR_LIST__ and ___CTOR_LIST__ here,
          we do not PROVIDE them.  This is because the ctors.o startup
	  code in libgcc defines them as common symbols, with the
          expectation that they will be overridden by the definitions
	  here.  If we PROVIDE the symbols then they will not be
	  overridden and global constructors will not be run.
	  See PR 22762 for more details.

	  This does mean that it is not possible for a user to define
	  their own __CTOR_LIST__ and __DTOR_LIST__ symbols; if they do,
	  the content from those variables are included but the symbols
	  defined here silently take precedence.  If they truly need to
	  be redefined, a custom linker script will have to be used.
	  (The custom script can just be a copy of this script with the
	  PROVIDE() qualifiers added).
	  In particular this means that ld -Ur does not work, because
	  the proper __CTOR_LIST__ set by ld -Ur is overridden by a
	  bogus __CTOR_LIST__ set by the final link.  See PR 46.  */
       ___CTOR_LIST__ = .;
       __CTOR_LIST__ = .;
       LONG (-1);
       KEEP(*(.ctors));
       KEEP(*(.ctor));
       KEEP(*(SORT_BY_NAME(.ctors.*)));
       LONG (0);
       /* See comment about __CTOR_LIST__ above.  The same reasoning
          applies here too.  */
       ___DTOR_LIST__ = .;
       __DTOR_LIST__ = .;
       LONG (-1);
       KEEP(*(.dtors));
       KEEP(*(.dtor));
       KEEP(*(SORT_BY_NAME(.dtors.*)));
       LONG (0);
    /* .CRT */
    /* ___crt_xl_end__ is defined in the TLS Directory support code */
  }
  .eh_frame  :
  {
    KEEP(*(.eh_frame))
  }
  .pdata  :
  {
    KEEP(*(.pdata))
  }
  .bss  :
  {
    *(.bss)
    *(COMMON)
  }
  .edata  :
  {
    *(.edata)
  }
  /DISCARD/ :
  {
    *(.debug$S)
    *(.debug$T)
    *(.debug$F)
  }
  .idata  :
  {
    /* This cannot currently be handled with grouped sections.
	See pe.em:sort_sections.  */
  }
  .didat  :
  {
    /* This cannot currently be handled with grouped sections.
	See pe.em:sort_sections.  */
  }
  /* Windows TLS expects .tls$AAA to be at the start and .tls$ZZZ to be
     at the end of section.  This is important because _tls_start MUST
     be at the beginning of the section to enable SECREL32 relocations with TLS
     data.  */
  .tls  :
  {
    *(.tls)
  }
  .endjunk  :
  {
    /* end is deprecated, don't use it */
  }
  .rsrc  : SUBALIGN(4)
  {
    *(.rsrc)
  }
  .reloc  :
  {
    *(.reloc)
  }
  .stab   :
  {
    *(.stab)
  }
  .stabstr   :
  {
    *(.stabstr)
  }
  /* DWARF debug sections.
     Symbols in the DWARF debugging sections are relative to the beginning
     of the section.  Unlike other targets that fake this by putting the
     section VMA at 0, the PE format will not allow it.  */
  /* DWARF 1.1 and DWARF 2.  */
  .debug_aranges   :
  {
    *(.debug_aranges)
  }
  .zdebug_aranges   :
  {
    *(.zdebug_aranges)
  }
  .debug_pubnames   :
  {
    *(.debug_pubnames)
  }
  .zdebug_pubnames   :
  {
    *(.zdebug_pubnames)
  }
  /* DWARF 2.  */
  .debug_info   :
  {
    *(.debug_info)
  }
  .zdebug_info   :
  {
    *(.zdebug_info)
  }
  .debug_abbrev   :
  {
    *(.debug_abbrev)
  }
  .zdebug_abbrev   :
  {
    *(.zdebug_abbrev)
  }
  .debug_line   :
  {
    *(.debug_line)
  }
  .zdebug_line   :
  {
    *(.zdebug_line)
  }
  .debug_frame   :
  {
    *(.debug_frame*)
  }
  .zdebug_frame   :
  {
    *(.zdebug_frame*)
  }
  .debug_str   :
  {
    *(.debug_str)
  }
  .zdebug_str   :
  {
    *(.zdebug_str)
  }
  .debug_loc   :
  {
    *(.debug_loc)
  }
  .zdebug_loc   :
  {
    *(.zdebug_loc)
  }
  .debug_macinfo   :
  {
    *(.debug_macinfo)
  }
  .zdebug_macinfo   :
  {
    *(.zdebug_macinfo)
  }
  /* SGI/MIPS DWARF 2 extensions.  */
  .debug_weaknames   :
  {
    *(.debug_weaknames)
  }
  .zdebug_weaknames   :
  {
    *(.zdebug_weaknames)
  }
  .debug_funcnames   :
  {
    *(.debug_funcnames)
  }
  .zdebug_funcnames   :
  {
    *(.zdebug_funcnames)
  }
  .debug_typenames   :
  {
    *(.debug_typenames)
  }
  .zdebug_typenames   :
  {
    *(.zdebug_typenames)
  }
  .debug_varnames   :
  {
    *(.debug_varnames)
  }
  .zdebug_varnames   :
  {
    *(.zdebug_varnames)
  }
  /* DWARF 3.  */
  .debug_pubtypes   :
  {
    *(.debug_pubtypes)
  }
  .zdebug_pubtypes   :
  {
    *(.zdebug_pubtypes)
  }
  .debug_ranges   :
  {
    *(.debug_ranges)
  }
  .zdebug_ranges   :
  {
    *(.zdebug_ranges)
  }
  /* DWARF 4.  */
  .debug_types   :
  {
    *(.debug_types)
  }
  .zdebug_types   :
  {
    *(.zdebug_types)
  }
  /* DWARF 5.  */
  .debug_addr   :
  {
    *(.debug_addr)
  }
  .zdebug_addr   :
  {
    *(.zdebug_addr)
  }
  .debug_line_str   :
  {
    *(.debug_line_str)
  }
  .zdebug_line_str   :
  {
    *(.zdebug_line_str)
  }
  .debug_loclists   :
  {
    *(.debug_loclists)
  }
  .zdebug_loclists   :
  {
    *(.zdebug_loclists)
  }
  .debug_macro   :
  {
    *(.debug_macro)
  }
  .zdebug_macro   :
  {
    *(.zdebug_macro)
  }
  .debug_names   :
  {
    *(.debug_names)
  }
  .zdebug_names   :
  {
    *(.zdebug_names)
  }
  .debug_rnglists   :
  {
    *(.debug_rnglists)
  }
  .zdebug_rnglists   :
  {
    *(.zdebug_rnglists)
  }
  .debug_str_offsets   :
  {
    *(.debug_str_offsets)
  }
  .zdebug_str_offsets   :
  {
    *(.zdebug_str_offsets)
  }
  .debug_sup   :
  {
    *(.debug_sup)
  }
  /* For Go and Rust.  */
  .debug_gdb_scripts   :
  {
    *(.debug_gdb_scripts)
  }
  .zdebug_gdb_scripts   :
  {
    *(.zdebug_gdb_scripts)
  }
}
