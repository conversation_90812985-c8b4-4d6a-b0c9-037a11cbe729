.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_get_direction" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_get_direction \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_record_get_direction(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
This function is useful to determine whether a GnuTLS function was interrupted
while sending or receiving, so that \fBselect()\fP or \fBpoll()\fP may be called appropriately.

It provides information about the internals of the record
protocol and is only useful if a prior gnutls function call,
e.g.  \fBgnutls_handshake()\fP, was interrupted and returned
\fBGNUTLS_E_INTERRUPTED\fP or \fBGNUTLS_E_AGAIN\fP. After such an interrupt
applications may call \fBselect()\fP or \fBpoll()\fP before restoring the
interrupted GnuTLS function.

This function's output is unreliable if you are using the same
 \fIsession\fP in different threads for sending and receiving.
.SH "RETURNS"
0 if interrupted while trying to read data, or 1 while trying to write data.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
