MinGW-w64 licensing
*******************

The copyright and license notices have been divided in two files:
The notices in COPYING.MinGW-w64.txt (this file) apply only to
MinGW-w64 itself. These don't apply to the binaries built with
MinGW-w64 unless you specifically tell MinGW-w64 to link against
these parts, for example, by enabling profiling code.

In addition to the notices in this file, also the notices in
COPYING.MinGW-w64-runtime.txt apply to MinGW-w64. Some (possibly
all) notices in that file may apply also to the binaries built with
this version of MinGW-w64. The idea is that if you create binary
packages of your software with MinGW-w64, you can simply copy
COPYING.MinGW-w64-runtime.txt into your package to fulfill the
license requirements of the MinGW runtime.

If you think that not all notices apply to your package and want to
remove some of them, note that, for example, the gdtoa files always
get linked in if you use any printf-like function. So usually it is
easiest and safest to just keep all the notices.


====================
GCC and GNU binutils
====================

Copyright (C) Free Software Foundation
License: GNU GPLv3+ (see the file COPYING.GPLv3)


==============
Profiling code
==============

Copyright 1998, 1999, 2000, 2001, 2002 Red Hat, Inc.
License: GNU GPLv2+ (see the file COPYING.GPLv2)

        *       *       *       *       *       *       *

Copyright (c) 1982, 1983, 1986, 1992, 1993
     The Regents of the University of California.  All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.
4. Neither the name of the University nor the names of its contributors
   may be used to endorse or promote products derived from this software
   without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
SUCH DAMAGE.


=======================
DirectX and DDK headers
=======================

DirectX and DDK headers are under GNU LGPLv2.1+ (see the file
COPYING.LGPLv2.1) and copyrighted by various people. Using these
headers doesn't make LGPLv2.1 apply to your code, because these
headers files contain only data structure definitions, short
macros, and short inline functions. Here is the relevant part
from LGPLv2.1 section 5 paragraph 4:

    If such an object file uses only numerical parameters, data
    structure layouts and accessors, and small macros and small
    inline functions (ten lines or less in length), then the use
    of the object file is unrestricted, regardless of whether it
    is legally a derivative work.

====================
libmangle and gendef
====================

Copyright (c) 2009 mingw-w64 project

Contributing authors: Kai Tietz, Jonathan Yong

Permission is hereby granted, free of charge, to any person obtaining a
copy of this software and associated documentation files (the "Software"),
to deal in the Software without restriction, including without limitation
the rights to use, copy, modify, merge, publish, distribute, sublicense,
and/or sell copies of the Software, and to permit persons to whom the
Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.


====
PSEH
====

Copyright (c) 2004-2008 KJK::Hyperion

Permission is hereby granted, free of charge, to any person obtaining a
copy of this software and associated documentation files (the "Software"),
to deal in the Software without restriction, including without limitation
the rights to use, copy, modify, merge, publish, distribute, sublicense,
and/or sell copies of the Software, and to permit persons to whom the
Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.
