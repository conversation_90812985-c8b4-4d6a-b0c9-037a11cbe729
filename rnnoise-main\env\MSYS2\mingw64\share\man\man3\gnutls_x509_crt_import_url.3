.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_import_url" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_import_url \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_import_url(gnutls_x509_crt_t " crt ", const char * " url ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
A certificate of type \fBgnutls_x509_crt_t\fP
.IP "const char * url" 12
A PKCS 11 url
.IP "unsigned int flags" 12
One of GNUTLS_PKCS11_OBJ_* flags for PKCS\fB11\fP URLs or zero otherwise
.SH "DESCRIPTION"
This function will import a certificate present in a PKCS\fB11\fP token
or any type of back\-end that supports URLs.

In previous versions of gnutls this function was named
gnutls_x509_crt_import_pkcs11_url, and the old name is
an alias to this one.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
