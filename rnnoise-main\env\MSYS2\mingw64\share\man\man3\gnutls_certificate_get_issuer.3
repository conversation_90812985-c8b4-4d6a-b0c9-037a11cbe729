.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_get_issuer" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_get_issuer \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_get_issuer(gnutls_certificate_credentials_t " sc ", gnutls_x509_crt_t " cert ", gnutls_x509_crt_t * " issuer ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t sc" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "gnutls_x509_crt_t cert" 12
is the certificate to find issuer for
.IP "gnutls_x509_crt_t * issuer" 12
Will hold the issuer if any. Should be treated as constant.
.IP "unsigned int flags" 12
Use zero or \fBGNUTLS_TL_GET_COPY\fP
.SH "DESCRIPTION"
This function will return the issuer of a given certificate.
If the flag \fBGNUTLS_TL_GET_COPY\fP is specified a copy of the issuer
will be returned which must be freed using \fBgnutls_x509_crt_deinit()\fP.
In that case the provided  \fIissuer\fP must not be initialized.

As with \fBgnutls_x509_trust_list_get_issuer()\fP this function requires
the \fBGNUTLS_TL_GET_COPY\fP flag in order to operate with PKCS\fB11\fP trust
lists in a thread\-safe way.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
