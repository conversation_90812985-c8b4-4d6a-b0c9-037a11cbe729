# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V282
49
50
185
186
1633
1634
1777
1778
1985
1986
2407
2408
2535
2536
2663
2664
2791
2792
2919
2920
3047
3048
3175
3176
3193
3194
3196
3197
3303
3304
3431
3432
3559
3560
3665
3666
3793
3794
3873
3874
4161
4162
4241
4242
4969
4970
6113
6114
6129
6130
6161
6162
6471
6472
6609
6610
6618
6619
6785
6786
6801
6802
6993
6994
7089
7090
7233
7234
7249
7250
8321
8322
8543
8545
8560
8561
9312
9313
9332
9333
9352
9353
9461
9462
10102
10103
10112
10113
10122
10123
12321
12322
12690
12691
12832
12833
12928
12929
19968
19969
22769
22770
22777
22778
24186
24187
24332
24333
42529
42530
42726
42727
43217
43218
43265
43266
43473
43474
43505
43506
43601
43602
44017
44018
65297
65298
65799
65800
65858
65859
65880
65883
66273
66274
66336
66337
66513
66514
66721
66722
67672
67673
67705
67706
67751
67752
67835
67836
67862
67863
68032
68033
68160
68161
68221
68222
68253
68254
68331
68332
68440
68441
68472
68473
68521
68522
68858
68859
68913
68914
69216
69217
69405
69406
69457
69458
69573
69574
69714
69715
69735
69736
69873
69874
69943
69944
70097
70098
70113
70114
70385
70386
70737
70738
70865
70866
71249
71250
71361
71362
71473
71474
71905
71906
72017
72018
72785
72786
72794
72795
73041
73042
73121
73122
73553
73554
74773
74774
74782
74783
74796
74797
74804
74805
74831
74832
74840
74841
92769
92770
92865
92866
93009
93010
93825
93826
93844
93845
119489
119490
119521
119522
119648
119649
119666
119667
119671
119672
120783
120784
120793
120794
120803
120804
120813
120814
120823
120824
123201
123202
123633
123634
124145
124146
125127
125128
125265
125266
126065
126066
126115
126116
126129
126130
126209
126210
127234
127235
130033
130034
133418
133419
END
