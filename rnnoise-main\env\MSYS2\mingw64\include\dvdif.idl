/*
 * Copyright (C) 2020 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#if 0
pragma makedep install
#endif

import "unknwn.idl";
import "objidl.idl";

cpp_quote("#include <ddraw.h>")

cpp_quote("#if 0")
interface IGraphBuilder;
cpp_quote("#endif")

typedef enum tagDVD_DOMAIN
{
    DVD_DOMAIN_FirstPlay = 1,
    DVD_DOMAIN_VideoManagerMenu,
    DVD_DOMAIN_VideoTitleSetMenu,
    DVD_DOMAIN_Title,
    DVD_DOMAIN_Stop
} DVD_DOMAIN;

typedef enum tagDVD_MENU_ID
{
    DVD_MENU_Title      = 2,
    DVD_MENU_Root       = 3,
    DVD_MENU_Subpicture = 4,
    DVD_MENU_Audio      = 5,
    DVD_MENU_Angle      = 6,
    DVD_MENU_Chapter    = 7
} DVD_MENU_ID;

typedef enum tagDVD_DISC_SIDE
{
    DVD_SIDE_A = 1,
    DVD_SIDE_B = 2
} DVD_DISC_SIDE;

typedef enum tagDVD_PREFERRED_DISPLAY_MODE
{
    DISPLAY_CONTENT_DEFAULT         = 0,
    DISPLAY_16x9                    = 1,
    DISPLAY_4x3_PANSCAN_PREFERRED   = 2,
    DISPLAY_4x3_LETTERBOX_PREFERRED = 3
} DVD_PREFERRED_DISPLAY_MODE;

typedef WORD DVD_REGISTER;
typedef DVD_REGISTER GPRMARRAY[16];
typedef DVD_REGISTER SPRMARRAY[24];

typedef struct tagDVD_ATR
{
    ULONG ulCAT;
    BYTE  pbATRI[768];
} DVD_ATR;

typedef BYTE DVD_VideoATR[2];
typedef BYTE DVD_AudioATR[8];
typedef BYTE DVD_SubpictureATR[6];

typedef enum tagDVD_FRAMERATE
{
    DVD_FPS_25        = 1,
    DVD_FPS_30NonDrop = 3
} DVD_FRAMERATE;

cpp_quote("typedef struct tagDVD_TIMECODE")
cpp_quote("{")
cpp_quote("   ULONG Hours1 : 4;")
cpp_quote("   ULONG Hours10 : 4;")
cpp_quote("   ULONG Minutes1 : 4;")
cpp_quote("   ULONG Minutes10 : 4;")
cpp_quote("   ULONG Seconds1 : 4;")
cpp_quote("   ULONG Seconds10 : 4;")
cpp_quote("   ULONG Frames1 : 4;")
cpp_quote("   ULONG Frames10 : 2;")
cpp_quote("   ULONG FrameRateCode : 2;")
cpp_quote("} DVD_TIMECODE;")

typedef enum tagDVD_NavCmdType
{
    DVD_NavCmdType_Pre    = 1,
    DVD_NavCmdType_Post   = 2,
    DVD_NavCmdType_Cell   = 3,
    DVD_NavCmdType_Button = 4
} DVD_NavCmdType;

typedef enum tagDVD_TIMECODE_FLAGS
{
    DVD_TC_FLAG_25fps         = 0x00000001,
    DVD_TC_FLAG_30fps         = 0x00000002,
    DVD_TC_FLAG_DropFrame     = 0x00000004,
    DVD_TC_FLAG_Interpolated  = 0x00000008
} DVD_TIMECODE_FLAGS;

typedef struct tagDVD_HMSF_TIMECODE
{
    BYTE bHours;
    BYTE bMinutes;
    BYTE bSeconds;
    BYTE bFrames;
} DVD_HMSF_TIMECODE;

typedef struct tagDVD_PLAYBACK_LOCATION2
{
    ULONG TitleNum;
    ULONG ChapterNum;
    DVD_HMSF_TIMECODE TimeCode;
    ULONG TimeCodeFlags;
} DVD_PLAYBACK_LOCATION2;

typedef struct tagDVD_PLAYBACK_LOCATION
{
    ULONG TitleNum;
    ULONG ChapterNum;
    ULONG TimeCode;
} DVD_PLAYBACK_LOCATION;

typedef DWORD VALID_UOP_SOMTHING_OR_OTHER;

typedef enum
{
    UOP_FLAG_Play_Title_Or_AtTime         = 0x00000001,
    UOP_FLAG_Play_Chapter                 = 0x00000002,
    UOP_FLAG_Play_Title                   = 0x00000004,
    UOP_FLAG_Stop                         = 0x00000008,
    UOP_FLAG_ReturnFromSubMenu            = 0x00000010,
    UOP_FLAG_Play_Chapter_Or_AtTime       = 0x00000020,
    UOP_FLAG_PlayPrev_Or_Replay_Chapter   = 0x00000040,
    UOP_FLAG_PlayNext_Chapter             = 0x00000080,
    UOP_FLAG_Play_Forwards                = 0x00000100,
    UOP_FLAG_Play_Backwards               = 0x00000200,
    UOP_FLAG_ShowMenu_Title               = 0x00000400,
    UOP_FLAG_ShowMenu_Root                = 0x00000800,
    UOP_FLAG_ShowMenu_SubPic              = 0x00001000,
    UOP_FLAG_ShowMenu_Audio               = 0x00002000,
    UOP_FLAG_ShowMenu_Angle               = 0x00004000,
    UOP_FLAG_ShowMenu_Chapter             = 0x00008000,
    UOP_FLAG_Resume                       = 0x00010000,
    UOP_FLAG_Select_Or_Activate_Button    = 0x00020000,
    UOP_FLAG_Still_Off                    = 0x00040000,
    UOP_FLAG_Pause_On                     = 0x00080000,
    UOP_FLAG_Select_Audio_Stream          = 0x00100000,
    UOP_FLAG_Select_SubPic_Stream         = 0x00200000,
    UOP_FLAG_Select_Angle                 = 0x00400000,
    UOP_FLAG_Select_Karaoke_Audio_Presentation_Mode = 0x00800000,
    UOP_FLAG_Select_Video_Mode_Preference = 0x01000000
} VALID_UOP_FLAG;

typedef enum
{
    DVD_CMD_FLAG_None              = 0x00000000,
    DVD_CMD_FLAG_Flush             = 0x00000001,
    DVD_CMD_FLAG_SendEvents        = 0x00000002,
    DVD_CMD_FLAG_Block             = 0x00000004,
    DVD_CMD_FLAG_StartWhenRendered = 0x00000008,
    DVD_CMD_FLAG_EndAfterRendered  = 0x00000010
} DVD_CMD_FLAGS;

typedef enum
{
    DVD_ResetOnStop                     = 1,
    DVD_NotifyParentalLevelChange       = 2,
    DVD_HMSF_TimeCodeEvents             = 3,
    DVD_AudioDuringFFwdRew              = 4,
    DVD_EnableNonblockingAPIs           = 5,
    DVD_CacheSizeInMB                   = 6,
    DVD_EnablePortableBookmarks         = 7,
    DVD_EnableExtendedCopyProtectErrors = 8,
    DVD_NotifyPositionChange            = 9,
    DVD_IncreaseOutputControl           = 10,
    DVD_EnableStreaming                 = 11,
    DVD_EnableESOutput                  = 12,
    DVD_EnableTitleLength               = 13,
    DVD_DisableStillThrottle            = 14,
    DVD_EnableLoggingEvents             = 15,
    DVD_MaxReadBurstInKB                = 16,
    DVD_ReadBurstPeriodInMS             = 17,
    DVD_RestartDisc                     = 18,
    DVD_EnableCC                        = 19
} DVD_OPTION_FLAG;

typedef enum
{
    DVD_Relative_Upper = 1,
    DVD_Relative_Lower = 2,
    DVD_Relative_Left  = 3,
    DVD_Relative_Right = 4
} DVD_RELATIVE_BUTTON;

typedef enum tagDVD_PARENTAL_LEVEL
{
    DVD_PARENTAL_LEVEL_8 = 0x8000,
    DVD_PARENTAL_LEVEL_7 = 0x4000,
    DVD_PARENTAL_LEVEL_6 = 0x2000,
    DVD_PARENTAL_LEVEL_5 = 0x1000,
    DVD_PARENTAL_LEVEL_4 = 0x0800,
    DVD_PARENTAL_LEVEL_3 = 0x0400,
    DVD_PARENTAL_LEVEL_2 = 0x0200,
    DVD_PARENTAL_LEVEL_1 = 0x0100
} DVD_PARENTAL_LEVEL;

typedef enum tagDVD_AUDIO_LANG_EXT
{
    DVD_AUD_EXT_NotSpecified      = 0,
    DVD_AUD_EXT_Captions          = 1,
    DVD_AUD_EXT_VisuallyImpaired  = 2,
    DVD_AUD_EXT_DirectorComments1 = 3,
    DVD_AUD_EXT_DirectorComments2 = 4
} DVD_AUDIO_LANG_EXT;

typedef enum tagDVD_SUBPICTURE_LANG_EXT
{
    DVD_SP_EXT_NotSpecified              = 0,
    DVD_SP_EXT_Caption_Normal            = 1,
    DVD_SP_EXT_Caption_Big               = 2,
    DVD_SP_EXT_Caption_Children          = 3,
    DVD_SP_EXT_CC_Normal                 = 5,
    DVD_SP_EXT_CC_Big                    = 6,
    DVD_SP_EXT_CC_Children               = 7,
    DVD_SP_EXT_Forced                    = 9,
    DVD_SP_EXT_DirectorComments_Normal   = 13,
    DVD_SP_EXT_DirectorComments_Big      = 14,
    DVD_SP_EXT_DirectorComments_Children = 15
} DVD_SUBPICTURE_LANG_EXT;

typedef enum tagDVD_AUDIO_APPMODE
{
    DVD_AudioMode_None     = 0,
    DVD_AudioMode_Karaoke  = 1,
    DVD_AudioMode_Surround = 2,
    DVD_AudioMode_Other    = 3
} DVD_AUDIO_APPMODE;

typedef enum tagDVD_AUDIO_FORMAT
{
    DVD_AudioFormat_AC3       = 0,
    DVD_AudioFormat_MPEG1     = 1,
    DVD_AudioFormat_MPEG1_DRC = 2,
    DVD_AudioFormat_MPEG2     = 3,
    DVD_AudioFormat_MPEG2_DRC = 4,
    DVD_AudioFormat_LPCM      = 5,
    DVD_AudioFormat_DTS       = 6,
    DVD_AudioFormat_SDDS      = 7,
    DVD_AudioFormat_Other     = 8
} DVD_AUDIO_FORMAT;

typedef enum tagDVD_KARAOKE_DOWNMIX
{
    DVD_Mix_0to0 = 0x0001,
    DVD_Mix_1to0 = 0x0002,
    DVD_Mix_2to0 = 0x0004,
    DVD_Mix_3to0 = 0x0008,
    DVD_Mix_4to0 = 0x0010,
    DVD_Mix_Lto0 = 0x0020,
    DVD_Mix_Rto0 = 0x0040,
    DVD_Mix_0to1 = 0x0100,
    DVD_Mix_1to1 = 0x0200,
    DVD_Mix_2to1 = 0x0400,
    DVD_Mix_3to1 = 0x0800,
    DVD_Mix_4to1 = 0x1000,
    DVD_Mix_Lto1 = 0x2000,
    DVD_Mix_Rto1 = 0x4000
} DVD_KARAOKE_DOWNMIX;

typedef enum tagDVD_KARAOKE_CONTENTS
{
    DVD_Karaoke_GuideVocal1  = 0x0001,
    DVD_Karaoke_GuideVocal2  = 0x0002,
    DVD_Karaoke_GuideMelody1 = 0x0004,
    DVD_Karaoke_GuideMelody2 = 0x0008,
    DVD_Karaoke_GuideMelodyA = 0x0010,
    DVD_Karaoke_GuideMelodyB = 0x0020,
    DVD_Karaoke_SoundEffectA = 0x0040,
    DVD_Karaoke_SoundEffectB = 0x0080
} DVD_KARAOKE_CONTENTS;

typedef enum tagDVD_KARAOKE_ASSIGNMENT
{
    DVD_Assignment_reserved0   = 0,
    DVD_Assignment_reserved1   = 1,
    DVD_Assignment_LR          = 2,
    DVD_Assignment_LRM         = 3,
    DVD_Assignment_LR1         = 4,
    DVD_Assignment_LRM1        = 5,
    DVD_Assignment_LR12        = 6,
    DVD_Assignment_LRM12       = 7
} DVD_KARAOKE_ASSIGNMENT;

typedef struct tagDVD_MUA_MixingInfo
{
    BOOL  fMixTo0;
    BOOL  fMixTo1;
    BOOL  fMix0InPhase;
    BOOL  fMix1InPhase;
    DWORD dwSpeakerPosition;
} DVD_MUA_MixingInfo;

typedef struct tagDVD_MUA_Coeff
{
    double log2_alpha;
    double log2_beta;
} DVD_MUA_Coeff;

typedef enum tagDVD_VIDEO_COMPRESSION
{
    DVD_VideoCompression_Other  = 0,
    DVD_VideoCompression_MPEG1  = 1,
    DVD_VideoCompression_MPEG2  = 2
} DVD_VIDEO_COMPRESSION;

typedef enum tagDVD_SUBPICTURE_TYPE
{
    DVD_SPType_NotSpecified = 0,
    DVD_SPType_Language     = 1,
    DVD_SPType_Other        = 2
} DVD_SUBPICTURE_TYPE;

typedef enum tagDVD_SUBPICTURE_CODING
{
    DVD_SPCoding_RunLength = 0,
    DVD_SPCoding_Extended  = 1,
    DVD_SPCoding_Other     = 2
} DVD_SUBPICTURE_CODING;

typedef enum tagDVD_TITLE_APPMODE
{
    DVD_AppMode_Not_Specified = 0,
    DVD_AppMode_Karaoke       = 1,
    DVD_AppMode_Other         = 3
} DVD_TITLE_APPMODE;

enum DVD_TextStringType
{
    DVD_Struct_Volume      = 0x01,
    DVD_Struct_Title       = 0x02,
    DVD_Struct_ParentalID  = 0x03,
    DVD_Struct_PartOfTitle = 0x04,
    DVD_Struct_Cell        = 0x05,
    DVD_Stream_Audio       = 0x10,
    DVD_Stream_Subpicture  = 0x11,
    DVD_Stream_Angle       = 0x12,
    DVD_Channel_Audio      = 0x20,
    DVD_General_Name       = 0x30,
    DVD_General_Comments   = 0x31,
    DVD_Title_Series       = 0x38,
    DVD_Title_Movie        = 0x39,
    DVD_Title_Video        = 0x3a,
    DVD_Title_Album        = 0x3b,
    DVD_Title_Song         = 0x3c,
    DVD_Title_Other        = 0x3f,
    DVD_Title_Sub_Series   = 0x40,
    DVD_Title_Sub_Movie    = 0x41,
    DVD_Title_Sub_Video    = 0x42,
    DVD_Title_Sub_Album    = 0x43,
    DVD_Title_Sub_Song     = 0x44,
    DVD_Title_Sub_Other    = 0x47,
    DVD_Title_Orig_Series  = 0x48,
    DVD_Title_Orig_Movie   = 0x49,
    DVD_Title_Orig_Video   = 0x4a,
    DVD_Title_Orig_Album   = 0x4b,
    DVD_Title_Orig_Song    = 0x4c,
    DVD_Title_Orig_Other   = 0x4f,
    DVD_Other_Scene        = 0x50,
    DVD_Other_Cut          = 0x51,
    DVD_Other_Take         = 0x52
};

enum DVD_TextCharSet
{
    DVD_CharSet_Unicode                       = 0,
    DVD_CharSet_ISO646                        = 1,
    DVD_CharSet_JIS_Roman_Kanji               = 2,
    DVD_CharSet_ISO8859_1                     = 3,
    DVD_CharSet_ShiftJIS_Kanji_Roman_Katakana = 4
};

typedef struct tagDVD_AudioAttributes
{
    DVD_AUDIO_APPMODE AppMode;
    BYTE  AppModeData;
    DVD_AUDIO_FORMAT AudioFormat;
    LCID  Language;
    DVD_AUDIO_LANG_EXT LanguageExtension;
    BOOL  fHasMultichannelInfo;
    DWORD dwFrequency;
    BYTE  bQuantization;
    BYTE  bNumberOfChannels;
    DWORD dwReserved[2];
} DVD_AudioAttributes;

typedef struct tagDVD_MultichannelAudioAttributes
{
    DVD_MUA_MixingInfo Info[8];
    DVD_MUA_Coeff Coeff[8];
} DVD_MultichannelAudioAttributes;

typedef struct tagDVD_VideoAttributes
{
    BOOL  fPanscanPermitted;
    BOOL  fLetterboxPermitted;
    ULONG ulAspectX;
    ULONG ulAspectY;
    ULONG ulFrameRate;
    ULONG ulFrameHeight;
    DVD_VIDEO_COMPRESSION Compression;
    BOOL  fLine21Field1InGOP;
    BOOL  fLine21Field2InGOP;
    ULONG ulSourceResolutionX;
    ULONG ulSourceResolutionY;
    BOOL  fIsSourceLetterboxed;
    BOOL  fIsFilmMode;
} DVD_VideoAttributes;

typedef struct tagDVD_SubpictureAttributes
{
    DVD_SUBPICTURE_TYPE Type;
    DVD_SUBPICTURE_CODING CodingMode;
    LCID Language;
    DVD_SUBPICTURE_LANG_EXT LanguageExtension;
} DVD_SubpictureAttributes;

typedef struct tagDVD_KaraokeAttributes
{
    BYTE bVersion;
    BOOL fMasterOfCeremoniesInGuideVocal1;
    BOOL fDuet;
    DVD_KARAOKE_ASSIGNMENT ChannelAssignment;
    WORD wChannelContents[8];
} DVD_KaraokeAttributes;

typedef struct tagDVD_TitleMainAttributes
{
    union
    {
        DVD_TITLE_APPMODE AppMode;
        DVD_HMSF_TIMECODE TitleLength;
    } DUMMYUNIONNAME;
    DVD_VideoAttributes VideoAttributes;
    ULONG ulNumberOfAudioStreams;
    DVD_AudioAttributes  AudioAttributes[8];
    DVD_MultichannelAudioAttributes MultichannelAudioAttributes[8];
    ULONG ulNumberOfSubpictureStreams;
    DVD_SubpictureAttributes SubpictureAttributes[32];
} DVD_TitleAttributes;

typedef struct tagDVD_MenuAttributes
{
    BOOL fCompatibleRegion[8];
    DVD_VideoAttributes VideoAttributes;
    BOOL fAudioPresent;
    DVD_AudioAttributes AudioAttributes;
    BOOL fSubpicturePresent;
    DVD_SubpictureAttributes SubpictureAttributes;
} DVD_MenuAttributes;

cpp_quote ("#define DVD_TITLE_MENU           0x000")
cpp_quote ("#define DVD_STREAM_DATA_VMGM     0x400")
cpp_quote ("#define DVD_STREAM_DATA_VTSM     0x401")
cpp_quote ("#define DVD_STREAM_DATA_CURRENT  0x800")
cpp_quote ("#define DVD_DEFAULT_AUDIO_STREAM 0x00f")

cpp_quote ("#define DVD_AUDIO_CAPS_AC3   0x00000001")
cpp_quote ("#define DVD_AUDIO_CAPS_MPEG2 0x00000002")
cpp_quote ("#define DVD_AUDIO_CAPS_LPCM  0x00000004")
cpp_quote ("#define DVD_AUDIO_CAPS_DTS   0x00000008")
cpp_quote ("#define DVD_AUDIO_CAPS_SDDS  0x00000010")

typedef struct tagDVD_DECODER_CAPS
{
    DWORD  dwSize;
    DWORD  dwAudioCaps;
    double dFwdMaxRateVideo;
    double dFwdMaxRateAudio;
    double dFwdMaxRateSP;
    double dBwdMaxRateVideo;
    double dBwdMaxRateAudio;
    double dBwdMaxRateSP;
    DWORD  dwRes1;
    DWORD  dwRes2;
    DWORD  dwRes3;
    DWORD  dwRes4;
} DVD_DECODER_CAPS;

typedef enum _AM_DVD_GRAPH_FLAGS
{
    AM_DVD_HWDEC_PREFER = 0x0001,
    AM_DVD_HWDEC_ONLY   = 0x0002,
    AM_DVD_SWDEC_PREFER = 0x0004,
    AM_DVD_SWDEC_ONLY   = 0x0008,
    AM_DVD_NOVPE        = 0x0100,
    AM_DVD_DO_NOT_CLEAR = 0x0200,
    AM_DVD_VMR9_ONLY    = 0x0800,
    AM_DVD_EVR_ONLY     = 0x1000,
    AM_DVD_EVR_QOS      = 0x2000,
    AM_DVD_ADAPT_GRAPH  = 0x4000,
    AM_DVD_MASK         = 0xffff
} AM_DVD_GRAPH_FLAGS;

typedef enum _AM_DVD_STREAM_FLAGS
{
    AM_DVD_STREAM_VIDEO  = 0x01,
    AM_DVD_STREAM_AUDIO  = 0x02,
    AM_DVD_STREAM_SUBPIC = 0x04
} AM_DVD_STREAM_FLAGS;

typedef struct tagAM_DVD_RENDERSTATUS
{
    HRESULT hrVPEStatus;
    BOOL  bDvdVolInvalid;
    BOOL  bDvdVolUnknown;
    BOOL  bNoLine21In;
    BOOL  bNoLine21Out;
    int   iNumStreams;
    int   iNumStreamsFailed;
    DWORD dwFailedStreamsFlag;
} AM_DVD_RENDERSTATUS;

[
    local,
    object,
    uuid(a70efe61-e2a3-11d0-a9be-00aa0061be93),
]
interface IDvdControl : IUnknown
{
    HRESULT TitlePlay([in] ULONG title);
    HRESULT ChapterPlay([in] ULONG title, [in] ULONG chapter);
    HRESULT TimePlay([in] ULONG title, [in] ULONG time);
    HRESULT StopForResume();
    HRESULT GoUp();
    HRESULT TimeSearch([in] ULONG time);
    HRESULT ChapterSearch([in] ULONG chapter);
    HRESULT PrevPGSearch();
    HRESULT TopPGSearch();
    HRESULT NextPGSearch();
    HRESULT ForwardScan([in] double speed);
    HRESULT BackwardScan([in] double speed);
    HRESULT MenuCall([in] DVD_MENU_ID id);
    HRESULT Resume();
    HRESULT UpperButtonSelect();
    HRESULT LowerButtonSelect();
    HRESULT LeftButtonSelect();
    HRESULT RightButtonSelect();
    HRESULT ButtonActivate();
    HRESULT ButtonSelectActivate([in] ULONG button);
    HRESULT StillOff();
    HRESULT PauseOn();
    HRESULT PauseOff();
    HRESULT MenuLanguageSelect([in] LCID language);
    HRESULT AudioStreamChange([in] ULONG audio);
    HRESULT SubpictureStreamChange([in] ULONG subpicture, [in] BOOL enable);
    HRESULT AngleChange([in] ULONG angle);
    HRESULT ParentalLevelSelect([in] ULONG level);
    HRESULT ParentalCountrySelect([in] WORD country);
    HRESULT KaraokeAudioPresentationModeChange([in] ULONG mode);
    HRESULT VideoModePreference([in] ULONG mode);
    HRESULT SetRoot([in] const WCHAR *path);
    HRESULT MouseActivate([in] POINT point);
    HRESULT MouseSelect([in] POINT point);
    HRESULT ChapterPlayAutoStop([in] ULONG title, [in] ULONG chapter, [in] ULONG count);
}

[
    local,
    object,
    uuid(a70efe60-e2a3-11d0-a9be-00aa0061be93),
]
interface IDvdInfo : IUnknown
{
    HRESULT GetCurrentDomain([out] DVD_DOMAIN *domain);
    HRESULT GetCurrentLocation([out] DVD_PLAYBACK_LOCATION *location);
    HRESULT GetTotalTitleTime([out] ULONG *time);
    HRESULT GetCurrentButton([out] ULONG *count, [out] ULONG *current);
    HRESULT GetCurrentAngle([out] ULONG *count, [out] ULONG *current);
    HRESULT GetCurrentAudio([out] ULONG *count, [out] ULONG *current);
    HRESULT GetCurrentSubpicture([out] ULONG *count, [out] ULONG *current, [out] BOOL *enable);
    HRESULT GetCurrentUOPS([out] VALID_UOP_SOMTHING_OR_OTHER *uops);
    HRESULT GetAllSPRMs([out] SPRMARRAY *regs);
    HRESULT GetAllGPRMs([out] GPRMARRAY *regs);
    HRESULT GetAudioLanguage([in] ULONG stream, [out] LCID *language);
    HRESULT GetSubpictureLanguage([in] ULONG stream, [out] LCID *language);
    HRESULT GetTitleAttributes([in] ULONG title, [out] DVD_ATR *attr);
    HRESULT GetVMGAttributes([out] DVD_ATR *attr);
    HRESULT GetCurrentVideoAttributes([out] DVD_VideoATR *attr);
    HRESULT GetCurrentAudioAttributes([out] DVD_AudioATR *attr);
    HRESULT GetCurrentSubpictureAttributes([out] DVD_SubpictureATR *attr);
    HRESULT GetCurrentVolumeInfo([out] ULONG *volume_count, [out] ULONG *current, [out] DVD_DISC_SIDE *side, [out] ULONG *title_count);
    HRESULT GetDVDTextInfo([out, size_is(size)] BYTE *text_manager, [in] ULONG size, [out] ULONG *ret_size);
    HRESULT GetPlayerParentalLevel([out] ULONG *level, [out] ULONG *country_code);
    HRESULT GetNumberOfChapters([in] ULONG title, [out] ULONG *count);
    HRESULT GetTitleParentalLevels([in] ULONG title, [out] ULONG *levels);
    HRESULT GetRoot([out, size_is(size)] char *path, [in] ULONG size, [out] ULONG *ret_size);
}

[
    local,
    object,
    uuid(5a4a97e4-94ee-4a55-9751-74b5643aa27d),
]
interface IDvdCmd : IUnknown
{
    HRESULT WaitForStart();
    HRESULT WaitForEnd();
}

[
    local,
    object,
    uuid(86303d6d-1c4a-4087-ab42-f711167048ef),
]
interface IDvdState : IUnknown
{
    HRESULT GetDiscID([out] ULONGLONG *id);
    HRESULT GetParentalLevel([out] ULONG *level);
}

[
    local,
    object,
    uuid(33bc7430-eec0-11d2-8201-00a0c9d74842),
]
interface IDvdControl2 : IUnknown
{
    HRESULT PlayTitle([in] ULONG title, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT PlayChapterInTitle([in] ULONG title, [in] ULONG chapter, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT PlayTimeInTitle([in] ULONG title, [in] DVD_HMSF_TIMECODE *time, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT Stop();
    HRESULT ReturnFromSubmenu([in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT PlayAtTime([in] DVD_HMSF_TIMECODE *time, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT PlayChapter([in] ULONG chapter, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT PlayPrevChapter([in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT ReplayChapter([in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT PlayNextChapter([in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT PlayForwards([in] double speed, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT PlayBackwards([in] double speed, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT ShowMenu([in] DVD_MENU_ID id, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT Resume([in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT SelectRelativeButton([in] DVD_RELATIVE_BUTTON button);
    HRESULT ActivateButton();
    HRESULT SelectButton([in] ULONG button);
    HRESULT SelectAndActivateButton([in] ULONG button);
    HRESULT StillOff();
    HRESULT Pause([in] BOOL enable);
    HRESULT SelectAudioStream([in] ULONG stream, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT SelectSubpictureStream([in] ULONG stream, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT SetSubpictureState([in] BOOL enable, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT SelectAngle([in] ULONG angle, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT SelectParentalLevel([in] ULONG level);
    HRESULT SelectParentalCountry([in] BYTE country[2]);
    HRESULT SelectKaraokeAudioPresentationMode([in] ULONG mode);
    HRESULT SelectVideoModePreference([in] ULONG mode);
    HRESULT SetDVDDirectory([in] const WCHAR *path);
    HRESULT ActivateAtPosition([in] POINT point);
    HRESULT SelectAtPosition([in] POINT point);
    HRESULT PlayChaptersAutoStop([in] ULONG title, [in] ULONG chapter, [in] ULONG count, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT AcceptParentalLevelChange([in] BOOL accept);
    HRESULT SetOption([in] DVD_OPTION_FLAG flag, [in] BOOL option);
    HRESULT SetState([in] IDvdState *state, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT PlayPeriodInTitleAutoStop([in] ULONG title, [in] DVD_HMSF_TIMECODE *start_time, [in] DVD_HMSF_TIMECODE *end_time, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT SetGRPM([in] ULONG index, [in] WORD value, [in] DWORD flags, [out] IDvdCmd **cmd);
    HRESULT SelectDefaultMenuLanguage([in] LCID language);
    HRESULT SelectDefaultAudioLanguage([in] LCID language, [in] DVD_AUDIO_LANG_EXT extension);
    HRESULT SelectDefaultSubpictureLanguage([in] LCID language, [in] DVD_SUBPICTURE_LANG_EXT extension);
}

[
    local,
    object,
    uuid(34151510-eec0-11d2-8201-00a0c9d74842),
]
interface IDvdInfo2 : IUnknown
{
    HRESULT GetCurrentDomain([out] DVD_DOMAIN *domain);
    HRESULT GetCurrentLocation([out] DVD_PLAYBACK_LOCATION2 *location);
    HRESULT GetTotalTitleTime([out] DVD_HMSF_TIMECODE *time, [out] ULONG *flags);
    HRESULT GetCurrentButton([out] ULONG *count, [out] ULONG *current);
    HRESULT GetCurrentAngle([out] ULONG *count, [out] ULONG *current);
    HRESULT GetCurrentAudio([out] ULONG *count, [out] ULONG *current);
    HRESULT GetCurrentSubpicture([out] ULONG *count, [out] ULONG *current, [out] BOOL *enable);
    HRESULT GetCurrentUOPS([out] ULONG *uops);
    HRESULT GetAllSPRMs([out] SPRMARRAY *regs);
    HRESULT GetAllGPRMs([out] GPRMARRAY *regs);
    HRESULT GetAudioLanguage([in] ULONG stream, [out] LCID *language);
    HRESULT GetSubpictureLanguage([in] ULONG stream, [out] LCID *language);
    HRESULT GetTitleAttributes([in] ULONG index, [out] DVD_MenuAttributes *menu, [out] DVD_TitleAttributes *title);
    HRESULT GetVMGAttributes([out] DVD_MenuAttributes *attr);
    HRESULT GetVideoAttributes([out] DVD_VideoAttributes *attr);
    HRESULT GetAudioAttributes([in] ULONG stream, [out] DVD_AudioAttributes *attr);
    HRESULT GetKaraokeAttributes([in] ULONG stream, [out] DVD_KaraokeAttributes *attr);
    HRESULT GetSubpictureAttributes([in] ULONG stream, [out] DVD_SubpictureAttributes *attr);
    HRESULT GetCurrentVolumeInfo([out] ULONG *volume_count, [out] ULONG *current, [out] DVD_DISC_SIDE *side, [out] ULONG *title_count);
    HRESULT GetDVDTextNumberOfLanguages([out] ULONG *count);
    HRESULT GetDVDTextLanguageInfo([in] ULONG index, [out] ULONG *string_count, [out] LCID *language, [out] enum DVD_TextCharSet *character_set);
    HRESULT GetDVDTextStringAsNative([in] ULONG lang_index, [in] ULONG string_index, [out] BYTE *string, [in] ULONG size, [out] ULONG *ret_size, [out] enum DVD_TextStringType *type);
    HRESULT GetDVDTextStringAsUnicode([in] ULONG lang_index, [in] ULONG string_index, [out] WCHAR *string, [in] ULONG size, [out] ULONG *ret_size, [out] enum DVD_TextStringType *type);
    HRESULT GetPlayerParentalLevel([out] ULONG *level, [out] BYTE country_code[2]);
    HRESULT GetNumberOfChapters([in] ULONG title, [out] ULONG *count);
    HRESULT GetTitleParentalLevels([in] ULONG title, [out] ULONG *levels);
    HRESULT GetDVDDirectory([out, size_is(size)] WCHAR *path, [in] ULONG size, [out] ULONG *ret_size);
    HRESULT IsAudioStreamEnabled([in] ULONG stream, [out] BOOL *enable);
    HRESULT GetDiscID([in] const WCHAR *path, [out] ULONGLONG *id);
    HRESULT GetState([out] IDvdState **state);
    HRESULT GetMenuLanguages([out] LCID *languages, [in] ULONG count, [out] ULONG *ret_count);
    HRESULT GetButtonAtPosition([in] POINT point, [out] ULONG *button);
    HRESULT GetCmdFromEvent([in] LONG_PTR param, [out] IDvdCmd **cmd);
    HRESULT GetDefaultMenuLanguage([out] LCID *language);
    HRESULT GetDefaultAudioLanguage([out] LCID *language, [out] DVD_AUDIO_LANG_EXT *extension);
    HRESULT SelectDefaultSubpictureLanguage([out] LCID *language, [out] DVD_SUBPICTURE_LANG_EXT *extension);
    HRESULT GetDecoderCaps([out] DVD_DECODER_CAPS *caps);
    HRESULT GetButtonRect([in] ULONG button, [out] RECT *rect);
    HRESULT IsSubpictureStreamEnabled([in] ULONG stream, [out] BOOL *enable);
}

[
    local,
    object,
    uuid(fcc152b6-f372-11d0-8e00-00c04fd7c08b),
]
interface IDvdGraphBuilder : IUnknown
{
    HRESULT GetFiltergraph([out] IGraphBuilder **graph);
    HRESULT GetDvdInterface([in] REFIID iid, [out] void **out);
    HRESULT RenderDvdVideoVolume([in] const WCHAR *path, [in] DWORD flags, [out] AM_DVD_RENDERSTATUS *status);
}
