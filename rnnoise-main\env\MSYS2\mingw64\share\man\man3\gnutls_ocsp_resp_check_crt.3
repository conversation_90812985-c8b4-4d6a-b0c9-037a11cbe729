.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_resp_check_crt" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_resp_check_crt \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "int gnutls_ocsp_resp_check_crt(gnutls_ocsp_resp_const_t " resp ", unsigned int " indx ", gnutls_x509_crt_t " crt ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_resp_const_t resp" 12
should contain a \fBgnutls_ocsp_resp_t\fP type
.IP "unsigned int indx" 12
Specifies response number to get. Use (0) to get the first one.
.IP "gnutls_x509_crt_t crt" 12
The certificate to check
.SH "DESCRIPTION"
This function will check whether the OCSP response
is about the provided certificate.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error code is returned.  
.SH "SINCE"
3.1.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
