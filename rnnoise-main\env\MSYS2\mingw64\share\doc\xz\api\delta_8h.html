<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/delta.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('delta_8h.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#enum-members">Enumerations</a>  </div>
  <div class="headertitle"><div class="title">delta.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Delta filter.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__options__delta.html">lzma_options_delta</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Options for the Delta filter.  <a href="structlzma__options__delta.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:a7ced67235ad7a01ae31d32ecf1e634cb" id="r_a7ced67235ad7a01ae31d32ecf1e634cb"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7ced67235ad7a01ae31d32ecf1e634cb">LZMA_FILTER_DELTA</a>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x03)</td></tr>
<tr class="memdesc:a7ced67235ad7a01ae31d32ecf1e634cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter ID.  <br /></td></tr>
<tr class="separator:a7ced67235ad7a01ae31d32ecf1e634cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a466886d9d01392f61bdf267687a4f96e" id="r_a466886d9d01392f61bdf267687a4f96e"><td class="memItemLeft" align="right" valign="top"><a id="a466886d9d01392f61bdf267687a4f96e" name="a466886d9d01392f61bdf267687a4f96e"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_DELTA_DIST_MIN</b>&#160;&#160;&#160;1</td></tr>
<tr class="memdesc:a466886d9d01392f61bdf267687a4f96e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum value for <a class="el" href="structlzma__options__delta.html#a31b4b0b5a2462cb9433c2663b8a62790" title="Delta distance.">lzma_options_delta.dist</a>. <br /></td></tr>
<tr class="separator:a466886d9d01392f61bdf267687a4f96e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afdf8a5ce184ddf9f8070de637775da94" id="r_afdf8a5ce184ddf9f8070de637775da94"><td class="memItemLeft" align="right" valign="top"><a id="afdf8a5ce184ddf9f8070de637775da94" name="afdf8a5ce184ddf9f8070de637775da94"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_DELTA_DIST_MAX</b>&#160;&#160;&#160;256</td></tr>
<tr class="memdesc:afdf8a5ce184ddf9f8070de637775da94"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum value for <a class="el" href="structlzma__options__delta.html#a31b4b0b5a2462cb9433c2663b8a62790" title="Delta distance.">lzma_options_delta.dist</a>. <br /></td></tr>
<tr class="separator:afdf8a5ce184ddf9f8070de637775da94"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="enum-members" name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:a04d84d7fa6cefdc219b6e2e96ff36fe1" id="r_a04d84d7fa6cefdc219b6e2e96ff36fe1"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a04d84d7fa6cefdc219b6e2e96ff36fe1">lzma_delta_type</a> { <b>LZMA_DELTA_TYPE_BYTE</b>
 }</td></tr>
<tr class="memdesc:a04d84d7fa6cefdc219b6e2e96ff36fe1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Type of the delta calculation.  <a href="#a04d84d7fa6cefdc219b6e2e96ff36fe1">More...</a><br /></td></tr>
<tr class="separator:a04d84d7fa6cefdc219b6e2e96ff36fe1"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Delta filter. </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead. </dd></dl>
</div><h2 class="groupheader">Macro Definition Documentation</h2>
<a id="a7ced67235ad7a01ae31d32ecf1e634cb" name="a7ced67235ad7a01ae31d32ecf1e634cb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7ced67235ad7a01ae31d32ecf1e634cb">&#9670;&#160;</a></span>LZMA_FILTER_DELTA</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define LZMA_FILTER_DELTA&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x03)</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Filter ID. </p>
<p>Filter ID of the Delta filter. This is used as <a class="el" href="structlzma__filter.html#aef1d9709759f39e61db77547b2326929" title="Filter ID.">lzma_filter.id</a>. </p>

</div>
</div>
<h2 class="groupheader">Enumeration Type Documentation</h2>
<a id="a04d84d7fa6cefdc219b6e2e96ff36fe1" name="a04d84d7fa6cefdc219b6e2e96ff36fe1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a04d84d7fa6cefdc219b6e2e96ff36fe1">&#9670;&#160;</a></span>lzma_delta_type</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="#a04d84d7fa6cefdc219b6e2e96ff36fe1">lzma_delta_type</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Type of the delta calculation. </p>
<p>Currently only byte-wise delta is supported. Other possible types could be, for example, delta of 16/32/64-bit little/big endian integers, but these are not currently planned since byte-wise delta is almost as good. </p>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li><li class="navelem"><a class="el" href="delta_8h.html">delta.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
