<?xml version="1.0" encoding="UTF-8"?>

<?include "cpack_variables.wxi"?>

<Wix
    xmlns="http://wixtoolset.org/schemas/v4/wxs"@CPACK_WIX_CUSTOM_XMLNS_EXPANDED@
    RequiredVersion="4.0"
    >

    <Package
        Name="$(var.CPACK_PACKAGE_NAME)"
        Version="$(var.CPACK_PACKAGE_VERSION)"
        Manufacturer="$(var.CPACK_PACKAGE_VENDOR)"
        UpgradeCode="$(var.CPACK_WIX_UPGRADE_GUID)"
        ProductCode="$(var.CPACK_WIX_PRODUCT_GUID)"
        Scope="$(var.CPACK_WIX_INSTALL_SCOPE)"
        InstallerVersion="500"
        Language="1033"
        Compressed="yes"
        >

        <Media Id="1" Cabinet="media1.cab" EmbedCab="yes"/>

        <MajorUpgrade
            Schedule="afterInstallInitialize"
            AllowSameVersionUpgrades="yes"
            DowngradeErrorMessage="A later version of [ProductName] is already installed. Setup will now exit."/>

        <WixVariable Id="WixUILicenseRtf" Value="$(var.CPACK_WIX_LICENSE_RTF)"/>
        <Property Id="WIXUI_INSTALLDIR" Value="INSTALL_ROOT"/>

        <?ifdef CPACK_WIX_PRODUCT_ICON?>
        <Property Id="ARPPRODUCTICON" Value="ProductIcon.ico" />
        <Icon Id="ProductIcon.ico" SourceFile="$(var.CPACK_WIX_PRODUCT_ICON)"/>
        <?endif?>

        <?ifdef CPACK_WIX_UI_BANNER?>
        <WixVariable Id="WixUIBannerBmp" Value="$(var.CPACK_WIX_UI_BANNER)"/>
        <?endif?>

        <?ifdef CPACK_WIX_UI_DIALOG?>
        <WixVariable Id="WixUIDialogBmp" Value="$(var.CPACK_WIX_UI_DIALOG)"/>
        <?endif?>

        <FeatureRef Id="ProductFeature"/>

        <ui:WixUI Id="$(var.CPACK_WIX_UI_REF)" />
        <UIRef Id="WixUI_ErrorProgressText" />

        <?include "properties.wxi"?>
        <?include "product_fragment.wxi"?>
    </Package>
</Wix>
