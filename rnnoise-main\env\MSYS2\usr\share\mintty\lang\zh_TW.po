# Traditional Chinese translations for mintty package
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the mintty package.
# <AUTHOR> <EMAIL>, 2018.
#
msgid ""
msgstr ""
"Project-Id-Version: mintty\n"
"Report-Msgid-Bugs-To: https://github.com/mintty/mintty/issues/700\n"
"POT-Creation-Date: 2025-03-22 07:33+0100\n"
"PO-Revision-Date: 2019-10-16 12:32+0800\n"
"Last-Translator: lcy0321 <<EMAIL>>\n"
"Language-Team: \n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: charset.c:228 charset.c:239 winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "(Default)"
msgstr "（預設）"

#: charset.c:250
msgid "(OEM codepage)"
msgstr "（OEM codepage）"

#: charset.c:254
msgid "(ANSI codepage)"
msgstr "（ANSI codepage）"

#: child.c:96
msgid "There are no available terminals"
msgstr "沒有可用的終端機"

#: child.c:171
msgid "Error: Could not open log file"
msgstr "錯誤：無法開啟紀錄檔案"

#: child.c:334
msgid "Error: Could not fork child process"
msgstr "錯誤：無法建立子程序"

#: child.c:336
msgid "DLL rebasing may be required; see 'rebaseall / rebase --help'"
msgstr "可能需要進行 DLL 重定位；見「rebaseall / rebase --help」"

#. __ %1$s: client command (e.g. shell) to be run; %2$s: error message
#: child.c:426
msgid "Failed to run '%s': %s"
msgstr "無法執行「%s」：%s"

#. __ %1$s: client command (e.g. shell) terminated, %2$i: exit code
#: child.c:578
msgid "%s: Exit %i"
msgstr "%s：Exit %i"

#. __ default inline notification if ExitWrite=yes
#: child.c:585
msgid "TERMINATED"
msgstr "已終止"

#: child.c:1232
msgid "Error: Could not fork child daemon"
msgstr "錯誤：無法建立子背景程序"

#. __ Setting false for Boolean options (localization optional)
#: config.c:655
msgid "no"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:657
msgid "yes"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:659
msgid "false"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:661
msgid "true"
msgstr ""

#. __ Setting false for Boolean options (localization optional)
#: config.c:663
msgid "off"
msgstr ""

#. __ Setting true for Boolean options (localization optional)
#: config.c:665
msgid "on"
msgstr ""

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:694
msgid "stretch"
msgstr "延展"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:696
msgid "align"
msgstr "對齊"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:698
msgid "middle"
msgstr "置中"

#. __ Options - Text - Emojis - Placement (localization optional)
#: config.c:700
msgid "full"
msgstr "完整"

#. __ %s: unknown option name
#: config.c:866
msgid "Ignoring unknown option '%s'"
msgstr "忽略未知選項「%s」"

#: config.c:914 config.c:943
msgid "Internal error: too many options"
msgstr "內部錯誤：選項過多"

#: config.c:930
msgid "Internal error: too many options/comments"
msgstr "內部錯誤：選項／註解過多"

#. __ %2$s: option name, %1$s: invalid value
#. report errors only during initialisation
#: config.c:1100
msgid "Ignoring invalid value '%s' for option '%s'"
msgstr "忽略無效值「%s」（「%s」選項）"

#. __ %s: option name
#: config.c:1112
msgid "Ignoring option '%s' with missing value"
msgstr "忽略「%s」選項（遺漏值）"

#. __ %1$s: config file name, %2$s: error message
#: config.c:1791
msgid ""
"Could not save options to '%s':\n"
"%s."
msgstr ""
"無法儲存選項至「%s」：\n"
"%s。"

#: config.c:2172
msgid "◇ None (printing disabled) ◇"
msgstr "◇ 無（停用列印） ◇"

#: config.c:2174
msgid "◆ Default printer ◆"
msgstr "◆ 預設印表機 ◆"

#. __ UI localization disabled
#: config.c:2283
msgid "– None –"
msgstr "– 無 –"

#. __ UI localization: use Windows desktop setting
#: config.c:2285
msgid "@ Windows language @"
msgstr "@ Windows 語言 @"

#. __ UI localization: use environment variable setting (LANGUAGE, LC_*)
#: config.c:2287
msgid "* Locale environm. *"
msgstr "* Locale 環境變數 *"

#. __ UI localization: use mintty configuration setting (Text - Locale)
#: config.c:2289
msgid "= cfg. Text Locale ="
msgstr "= 已設定的語言 ="

#: config.c:2394
msgid "simple beep"
msgstr "簡單的嗶聲"

#: config.c:2395
msgid "no beep"
msgstr "無嗶聲"

#: config.c:2396
msgid "Default Beep"
msgstr "預設嗶聲"

#: config.c:2397
msgid "Critical Stop"
msgstr "緊急停止"

#: config.c:2398
msgid "Question"
msgstr "問題"

#: config.c:2399
msgid "Exclamation"
msgstr "驚嘆聲"

#: config.c:2400
msgid "Asterisk"
msgstr "星號"

#: config.c:2443
msgid "◇ None (system sound) ◇"
msgstr "◇ 無（系統音效） ◇"

#. __ terminal theme / colour scheme
#. __ emojis style
#: config.c:2874 config.c:3425
msgid "◇ None ◇"
msgstr "◇ 無 ◇"

#. __ indicator of unsaved downloaded colour scheme
#: config.c:2877
msgid "downloaded / give me a name!"
msgstr "已下載／給我個名字！"

#: config.c:2983
msgid "Could not load web theme"
msgstr "無法載入網路主題"

#: config.c:3040
msgid "Cannot write theme file"
msgstr "無法寫入主題檔案"

#: config.c:3045
msgid "Cannot store theme file"
msgstr "無法儲存主題檔案"

#. __ Options - Text:
#: config.c:3502 config.c:3840 config.c:3939
msgid "as font"
msgstr "示粗體字"

#. __ Options - Text:
#: config.c:3503 config.c:3845 config.c:3944
msgid "as colour"
msgstr "示粗體字"

#: config.c:3504
msgid "as font & as colour"
msgstr "示粗體字 & 示粗體字"

#. __ Options - Text:
#: config.c:3505 config.c:3850 config.c:3949
msgid "xterm"
msgstr ""

#. __ Dialog button - show About text
#: config.c:3653
msgid "About..."
msgstr "關於..."

#. __ Dialog button - save changes
#: config.c:3656
msgid "Save"
msgstr "儲存"

#. __ Dialog button - cancel
#: config.c:3660 winctrls.c:1277 windialog.c:895
msgid "Cancel"
msgstr "取消"

#. __ Dialog button - apply changes
#: config.c:3664
msgid "Apply"
msgstr "套用"

#. __ Dialog button - take notice
#: config.c:3668 windialog.c:892
msgid "I see"
msgstr "了解"

#. __ Dialog button - confirm action
#: config.c:3670 winctrls.c:1276 windialog.c:894
msgid "OK"
msgstr "確認"

#. __ Options - Looks: treeview label
#: config.c:3677 config.c:3708 config.c:3767
msgid "Looks"
msgstr "外觀"

#. __ Options - Looks: panel title
#: config.c:3679
msgid "Looks in Terminal"
msgstr "終端機外觀"

#. __ Options - Looks: section title
#: config.c:3681
msgid "Colours"
msgstr "色彩"

#. __ Options - Looks:
#: config.c:3685
msgid "&Foreground..."
msgstr "前景(&F)"

#. __ Options - Looks:
#: config.c:3689
msgid "&Background..."
msgstr "背景(&B)"

#. __ Options - Looks:
#: config.c:3693
msgid "&Cursor..."
msgstr "色彩(&C)"

#. __ Options - Looks:
#: config.c:3697
msgid "&Theme"
msgstr "主題(&T)"

#. __ Options - Looks: name of web service
#: config.c:3702
msgid "Color Scheme Designer"
msgstr "色彩樣式設計工具"

#. __ Options - Looks: store colour scheme
#: config.c:3705 winctrls.c:484
msgid "Store"
msgstr "保存"

#. __ Options - Looks: section title
#: config.c:3710
msgid "Transparency"
msgstr "透明度"

#. __ Options - Looks: transparency
#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Window:
#: config.c:3716 config.c:4098 config.c:4261 config.c:4459
msgid "&Off"
msgstr "關(&O)"

#. __ Options - Looks: transparency
#: config.c:3718
msgid "&Low"
msgstr "低(&L)"

#. __ Options - Looks: transparency, short form of radio button label "Medium"
#: config.c:3720
msgid "&Med."
msgstr "中(&M)"

#. __ Options - Looks: transparency
#: config.c:3722
msgid "&Medium"
msgstr "中(&M)"

#. __ Options - Looks: transparency
#: config.c:3724
msgid "&High"
msgstr "高(&H)"

#. __ Options - Looks: transparency
#: config.c:3726
msgid "Gla&ss"
msgstr "玻璃(&S)"

#. __ Options - Looks: transparency
#: config.c:3733 config.c:3745 config.c:3752
msgid "Opa&que when focused"
msgstr "使用中不透明(&Q)"

#. __ Options - Looks: transparency
#: config.c:3738
msgid "Blu&r"
msgstr "模糊(&R)"

#: config.c:3759
msgid "◄"
msgstr ""

#: config.c:3762
msgid "►"
msgstr ""

#. __ Options - Looks: section title
#: config.c:3769
msgid "Cursor"
msgstr "游標"

#. __ Options - Looks: cursor type
#: config.c:3774
msgid "Li&ne"
msgstr "豎線(&N)"

#. __ Options - Looks: cursor type
#: config.c:3776
msgid "Bloc&k"
msgstr "方塊(K)"

#. __ Options - Looks: cursor type
#: config.c:3779
msgid "Bo&x"
msgstr ""

#. __ Options - Looks: cursor type
#: config.c:3782
msgid "&Underscore"
msgstr "下劃線(&U)"

#. __ Options - Looks: cursor feature
#: config.c:3787
msgid "Blinkin&g"
msgstr "閃爍(&G)"

#. __ Options - Text: treeview label
#: config.c:3794 config.c:3819 config.c:3834 config.c:3883 config.c:3933
#: config.c:3958 config.c:3980 config.c:3993 config.c:4001
msgid "Text"
msgstr "文字"

#. __ Options - Text: panel title
#: config.c:3796
msgid "Text and Font properties"
msgstr "文字與字型屬性"

#. __ Options - Text: section title
#: config.c:3798
msgid "Font"
msgstr "字型"

#. __ Options - Text:
#. __ Font chooser:
#: config.c:3806 winctrls.c:1287
msgid "Font st&yle:"
msgstr "字型樣式(&Y)"

#. __ Font chooser:
#: config.c:3811 winctrls.c:1289
msgid "&Size:"
msgstr "大小(&S)："

#. __ Options - Text:
#: config.c:3823 config.c:3902
msgid "Sho&w bold as font"
msgstr "以字型顯示粗體字(&W)"

#. __ Options - Text:
#: config.c:3828 config.c:3907
msgid "Show &bold as colour"
msgstr "以顏色顯示粗體字(&B)"

#. __ Options - Text:
#: config.c:3836 config.c:3857 config.c:3935 config.c:3962
msgid "Show bold"
msgstr "以字型顯"

#. __ Options - Text:
#: config.c:3864 config.c:3912 config.c:3968
msgid "&Allow blinking"
msgstr "允許閃爍(&A)"

#. __ Options - Text:
#: config.c:3869 config.c:3973
msgid "Show dim as font"
msgstr "以字型顯示淡的字(&W)"

#. __ Options - Text:
#: config.c:3887 config.c:3920 config.c:3955
msgid "Font smoothing"
msgstr "字體平滑"

#. __ Options - Text:
#: config.c:3890 config.c:3923 config.c:4149 config.c:4188 config.c:4342
#: config.c:4355
msgid "&Default"
msgstr "預設(&D)"

#. __ Options - Text:
#. __ Options - Window: scrollbar
#: config.c:3892 config.c:3925 config.c:4147 config.c:4186 config.c:4340
#: config.c:4353 config.c:4440
msgid "&None"
msgstr "無(&N)"

#. __ Options - Text:
#: config.c:3894 config.c:3927 config.c:4148 config.c:4187 config.c:4341
#: config.c:4354
msgid "&Partial"
msgstr "部分(&P)"

#. __ Options - Text:
#: config.c:3896 config.c:3929 config.c:4150 config.c:4189 config.c:4343
#: config.c:4356
msgid "&Full"
msgstr "完整(&F)"

#: config.c:3983
msgid "&Locale"
msgstr "語言(&L)"

#: config.c:3986
msgid "&Character set"
msgstr "字元集(C)"

#. __ Options - Text - Emojis:
#. __ Options - Text:
#: config.c:3997 config.c:4003
msgid "Emojis"
msgstr "表情符號"

#. __ Options - Text - Emojis:
#: config.c:4007
msgid "Style"
msgstr "風格"

#. __ Options - Text - Emojis:
#: config.c:4012
msgid "Placement"
msgstr "顯示方式"

#. __ Options - Keys: treeview label
#: config.c:4020 config.c:4050 config.c:4085 config.c:4103
msgid "Keys"
msgstr "按鍵"

#. __ Options - Keys: panel title
#: config.c:4022
msgid "Keyboard features"
msgstr "鍵盤功能"

#. __ Options - Keys:
#: config.c:4026
msgid "&Backarrow sends ^H"
msgstr "後退鍵傳送^H(&B)"

#. __ Options - Keys:
#: config.c:4031
msgid "&Delete sends DEL"
msgstr "刪除鍵傳送DEL(&D)"

#. __ Options - Keys:
#: config.c:4036
msgid "Ctrl+LeftAlt is Alt&Gr"
msgstr "Ctrl+左Alt為AltGr(&G)"

#. __ Options - Keys:
#: config.c:4041
msgid "AltGr is also Alt"
msgstr "將 AltGr 也視為 Alt"

#. __ Options - Keys:
#: config.c:4046
msgid "&Esc/Enter reset IME to alphanumeric"
msgstr ""

#. __ Options - Keys: section title
#: config.c:4052
msgid "Shortcuts"
msgstr "快捷鍵"

#. __ Options - Keys:
#: config.c:4055
msgid "Cop&y and Paste (Ctrl/Shift+Ins)"
msgstr "複製與貼上（Ctrl/Shift+Ins）(&Y)"

#. __ Options - Keys:
#: config.c:4060
msgid "&Menu and Full Screen (Alt+Space/Enter)"
msgstr "選單與全螢幕（Alt+Space/Enter）(&M)"

#. __ Options - Keys:
#: config.c:4065
msgid "&Switch window (Ctrl+[Shift+]Tab)"
msgstr "切換視窗（Ctrl+[Shift+]Tab）(&S)"

#. __ Options - Keys:
#: config.c:4070
msgid "&Zoom (Ctrl+plus/minus/zero)"
msgstr "縮放（Ctrl+plus/minus/zero）(&Z)"

#. __ Options - Keys:
#: config.c:4075
msgid "&Alt+Fn shortcuts"
msgstr "Alt+Fn 快捷鍵(&A)"

#. __ Options - Keys:
#: config.c:4080
msgid "&Ctrl+Shift+letter shortcuts"
msgstr "Ctrl+Shift+字母 快捷鍵(&C)"

#. __ Options - Keys: section title
#: config.c:4087 config.c:4105
msgid "Compose key"
msgstr "組合鍵"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Shift:
#. __ Options - Window:
#. __ Options - Modifier - Shift:
#: config.c:4092 config.c:4253 config.c:4272 config.c:4451 config.c:4470
msgid "&Shift"
msgstr "&Shift"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Control:
#. __ Options - Window:
#. __ Options - Modifier - Control:
#: config.c:4094 config.c:4255 config.c:4280 config.c:4453 config.c:4478
msgid "&Ctrl"
msgstr "&Ctrl"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Alt:
#. __ Options - Window:
#. __ Options - Modifier - Alt:
#: config.c:4096 config.c:4257 config.c:4276 config.c:4455 config.c:4474
msgid "&Alt"
msgstr "&Alt"

#. __ Options - Mouse: treeview label
#: config.c:4112 config.c:4201 config.c:4233
msgid "Mouse"
msgstr "滑鼠"

#. __ Options - Mouse: panel title
#: config.c:4114
msgid "Mouse functions"
msgstr "滑鼠功能"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4122 config.c:4155 config.c:4171 config.c:4319
msgid "Cop&y on select"
msgstr "選取時複製(&Y)"

#. __ Options - Mouse:
#. __ Options - Selection:
#. __ Context menu:
#: config.c:4127 config.c:4160 config.c:4324 wininput.c:685
msgid "Copy with TABs"
msgstr "以純文字複製（包含 &Tab）"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4132 config.c:4165 config.c:4177 config.c:4329
msgid "Copy as &rich text"
msgstr "以格式化文字複製(&R)"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4138 config.c:4145 config.c:4184 config.c:4338 config.c:4351
msgid "Copy as &HTML"
msgstr "複製&HTML"

#. __ Options - Mouse:
#: config.c:4197
msgid "Clic&ks place command line cursor"
msgstr "點擊定位游標(&K)"

#. __ Options - Mouse: section title
#: config.c:4203
msgid "Click actions"
msgstr "點擊動作"

#. __ Options - Mouse:
#: config.c:4206
msgid "Right mouse button"
msgstr "滑鼠右鍵"

#. __ Options - Mouse:
#: config.c:4209 config.c:4223
msgid "&Paste"
msgstr "貼上(&P)"

#. __ Options - Mouse:
#: config.c:4211 config.c:4225
msgid "E&xtend"
msgstr "擴展(&X)"

#. __ Options - Mouse:
#: config.c:4213
msgid "&Menu"
msgstr "選單(&M)"

#. __ Options - Mouse:
#: config.c:4215 config.c:4229
msgid "Ente&r"
msgstr "Ente&r"

#. __ Options - Mouse:
#: config.c:4220
msgid "Middle mouse button"
msgstr "滑鼠中鍵"

#. __ Options - Mouse:
#: config.c:4227
msgid "&Nothing"
msgstr "無(&N)"

#. __ Options - Mouse: section title
#: config.c:4235
msgid "Application mouse mode"
msgstr "應用程式滑鼠模式"

#. __ Options - Mouse:
#: config.c:4238
msgid "Default click target"
msgstr "預設點擊目標"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4241
msgid "&Window"
msgstr "視窗(&W)"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4243
msgid "&Application"
msgstr "應用程式(&A)"

#. __ Options - Mouse:
#: config.c:4250 config.c:4268
msgid "Modifier for overriding default"
msgstr "覆蓋預設設定的修飾鍵"

#. __ Options - Window:
#. __ Options - Modifier - Win:
#. __ Options - Window:
#. __ Options - Modifier - Win:
#: config.c:4259 config.c:4284 config.c:4457 config.c:4482
msgid "&Win"
msgstr "&Win"

#. __ Options - Modifier - Super:
#: config.c:4288 config.c:4486
msgid "&Sup"
msgstr "&Sup"

#. __ Options - Modifier - Hyper:
#: config.c:4292 config.c:4490
msgid "&Hyp"
msgstr "&Hyp"

#. __ Options - Selection: treeview label
#: config.c:4302 config.c:4313 config.c:4377
msgid "Selection"
msgstr "選取"

#. __ Options - Selection: panel title
#: config.c:4304
msgid "Selection and clipboard"
msgstr "選取與剪貼簿"

#. __ Options - Selection:
#: config.c:4308
msgid "Clear selection on input"
msgstr "輸入時解除選取"

#. __ Options - Selection: section title
#: config.c:4315
msgid "Clipboard"
msgstr "剪貼簿"

#. __ Options - Selection:
#: config.c:4365
msgid "Trim space from selection"
msgstr "修剪選取範圍前後的空白"

#. __ Options - Selection:
#: config.c:4371
msgid "Allow setting selection"
msgstr "允許調整選取的設定值"

#. __ Options - Selection: section title
#. __ Options - Window: treeview label
#: config.c:4379 config.c:4401 config.c:4426 config.c:4499
msgid "Window"
msgstr "視窗"

#. __ Options - Selection: clock position of info popup for text size
#: config.c:4384
msgid "Show size while selecting (0..12)"
msgstr "選取時顯示長度"

#. __ Options - Selection:
#: config.c:4391
msgid "Suspend output while selecting"
msgstr "選取時暫停輸出"

#. __ Options - Window: panel title
#: config.c:4403
msgid "Window properties"
msgstr "視窗屬性"

#. __ Options - Window: section title
#: config.c:4405
msgid "Default size"
msgstr "預設大小"

#. __ Options - Window:
#: config.c:4409
msgid "Colu&mns"
msgstr "欄(&M)"

#. __ Options - Window:
#: config.c:4413
msgid "Ro&ws"
msgstr "列(&W)"

#. __ Options - Window:
#: config.c:4417
msgid "C&urrent size"
msgstr "目前大小(&U)"

#. __ Options - Window:
#: config.c:4422
msgid "Re&wrap on resize"
msgstr "調整大小時調整內容(&W)"

#. __ Options - Window:
#: config.c:4430
msgid "Scroll&back lines"
msgstr "螢幕緩衝區行數(&B)"

#. __ Options - Window:
#: config.c:4435
msgid "Scrollbar"
msgstr "捲軸"

#. __ Options - Window: scrollbar
#: config.c:4438
msgid "&Left"
msgstr "左(&L)"

#. __ Options - Window: scrollbar
#: config.c:4442
msgid "&Right"
msgstr "右(&R)"

#. __ Options - Window:
#: config.c:4448 config.c:4466
msgid "Modifier for scrolling"
msgstr "滾動的修飾鍵"

#. __ Options - Window:
#: config.c:4495
msgid "&PgUp and PgDn scroll without modifier"
msgstr "PgUp 與 PgDn 滾動不需修飾鍵(&P)"

#. __ Options - Window: section title
#: config.c:4501
msgid "UI language"
msgstr "介面語言"

#. __ Options - Terminal: treeview label
#: config.c:4511 config.c:4524 config.c:4585 config.c:4599
msgid "Terminal"
msgstr "終端機"

#. __ Options - Terminal: panel title
#: config.c:4513
msgid "Terminal features"
msgstr "終端機功能"

#. __ Options - Terminal:
#: config.c:4517
msgid "&Type"
msgstr "類型(&T)"

#. __ Options - Terminal: answerback string for ^E request
#: config.c:4521
msgid "&Answerback"
msgstr "&Answerback"

#. __ Options - Terminal: section title
#: config.c:4526
msgid "Bell"
msgstr "提示音"

#. __ Options - Terminal: bell
#: config.c:4533
msgid "► &Play"
msgstr "► 播放(&P)"

#. __ Options - Terminal: bell
#: config.c:4539
msgid "&Wave"
msgstr "聲音(&W)"

#. __ Options - Terminal: bell
#: config.c:4561 config.c:4574
msgid "&Flash"
msgstr "閃爍(&F)"

#. __ Options - Terminal: bell
#: config.c:4563 config.c:4578
msgid "&Highlight in taskbar"
msgstr "於工作列醒目提示(&H)"

#. __ Options - Terminal: bell
#: config.c:4565 config.c:4582
msgid "&Popup"
msgstr "彈出視窗(&P)"

#. __ Options - Terminal: section title
#: config.c:4587
msgid "Printer"
msgstr "印表機"

#. __ Options - Terminal:
#: config.c:4602
msgid "Prompt about running processes on &close"
msgstr "離開時提示執行中的程式(&C)"

#. __ Options - Terminal:
#. __ Context menu:
#: config.c:4607 wininput.c:581
msgid "Status Line"
msgstr ""

#: textprint.c:44 textprint.c:127
msgid "[Printing...] "
msgstr "［列印中...］"

#. __ Options - Text: font chooser activation button
#: winctrls.c:935
msgid "&Select..."
msgstr "選擇(&S)..."

#. __ Font chooser: title bar label
#: winctrls.c:1281
msgid "Font "
msgstr "字型"

#. __ Font chooser: button
#: winctrls.c:1283
msgid "&Apply"
msgstr "套用(&A)"

#. __ Font chooser:
#: winctrls.c:1285
msgid "&Font:"
msgstr "字型(&F)："

#. __ Font chooser:
#: winctrls.c:1291
msgid "Sample"
msgstr "範例"

#. __ Font chooser: text sample ("AaBbYyZz" by default)
#: winctrls.c:1295 winctrls.c:1554 winctrls.c:1719
msgid "Ferqœm’4€"
msgstr "ABC abc 文字"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1312
msgid "Sc&ript:"
msgstr "字符集(&R)"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1314
msgid "<A>Show more fonts</A>"
msgstr "<A>顯示更多字型</A>"

#. __ Colour chooser: title bar label
#: winctrls.c:1319
msgid "Colour "
msgstr "色彩"

#. __ Colour chooser:
#: winctrls.c:1332 winctrls.c:1344
msgid "B&asic colours:"
msgstr "基本色彩(&A)"

#. __ Colour chooser:
#: winctrls.c:1353
msgid "&Custom colours:"
msgstr "自訂色彩(&C)"

#. __ Colour chooser:
#: winctrls.c:1360
msgid "De&fine Custom Colours >>"
msgstr "定義自訂色彩(&F) >>"

#. __ Colour chooser:
#: winctrls.c:1363
msgid "Colour"
msgstr "色彩"

#. __ Colour chooser:
#: winctrls.c:1365
msgid "|S&olid"
msgstr "|原色(&O)"

#. __ Colour chooser:
#: winctrls.c:1367
msgid "&Hue:"
msgstr "色調(&H):"

#. __ Colour chooser:
#: winctrls.c:1370
msgid "&Sat:"
msgstr "濃度(&S):"

#. __ Colour chooser:
#: winctrls.c:1372
msgid "&Lum:"
msgstr "亮度(&L):"

#. __ Colour chooser:
#: winctrls.c:1374
msgid "&Red:"
msgstr "紅(&R):"

#. __ Colour chooser:
#: winctrls.c:1376
msgid "&Green:"
msgstr "綠(&G):"

#. __ Colour chooser:
#: winctrls.c:1378
msgid "&Blue:"
msgstr "藍(&B):"

#. __ Colour chooser:
#: winctrls.c:1381
msgid "A&dd to Custom Colours"
msgstr "新增自訂色彩(&D)"

#. __ Options: dialog title
#: windialog.c:266 windialog.c:839
msgid "Options"
msgstr "選項"

#. __ Options: dialog title: "mintty <release> available (for download)"
#: windialog.c:268
msgid "available"
msgstr "可用"

#. __ Options: dialog width scale factor (80...200)
#: windialog.c:783
msgid "100"
msgstr "115"

#: windialog.c:924 windialog.c:951
msgid "Error"
msgstr "錯誤"

#. __ Context menu, session switcher ("virtual tabs") menu label
#: wininput.c:300
msgid "Session switcher"
msgstr "Session 切換器"

#. __ Context menu, session launcher ("virtual tabs") menu label
#: wininput.c:320
msgid "Session launcher"
msgstr "Session 啟動器"

#: wininput.c:429 wininput.c:435
msgid "Ctrl+"
msgstr "Ctrl+"

#: wininput.c:430 wininput.c:436
msgid "Alt+"
msgstr "Alt+"

#: wininput.c:431 wininput.c:437
msgid "Shift+"
msgstr "Shift+"

#. __ System menu:
#: wininput.c:462
msgid "&Restore"
msgstr "還原(&R)"

#. __ System menu:
#: wininput.c:464
msgid "&Move"
msgstr "移動(&M)"

#. __ System menu:
#: wininput.c:466
msgid "&Size"
msgstr "大小(&S)"

#. __ System menu:
#: wininput.c:468
msgid "Mi&nimize"
msgstr "最小化(&N)"

#. __ System menu:
#: wininput.c:470
msgid "Ma&ximize"
msgstr "最大化(&X)"

#. __ System menu:
#: wininput.c:472
msgid "&Close"
msgstr "關閉(&C)"

#. __ System menu:
#: wininput.c:477
msgid "New &Window"
msgstr "開新視窗(&W)"

#. __ System menu:
#: wininput.c:483
msgid "New &Tab"
msgstr "開新分頁(T&)"

#. __ Context menu:
#: wininput.c:490
msgid "&Copy"
msgstr "複製(&C)"

#. __ Context menu:
#: wininput.c:509
msgid "&Paste "
msgstr "貼上(&P)"

#. __ Context menu:
#: wininput.c:514
msgid "Copy → Paste"
msgstr "複製 → 貼上"

#. __ Context menu:
#: wininput.c:519
msgid "S&earch"
msgstr "搜尋(&E)"

#. __ Context menu:
#: wininput.c:526
msgid "&Log to File"
msgstr "記錄至檔案(&L)"

#. __ Context menu:
#: wininput.c:532
msgid "Character &Info"
msgstr "字元資訊(&I)"

#. __ Context menu:
#: wininput.c:538
msgid "VT220 Keyboard"
msgstr "VT220 鍵盤"

#. __ Context menu:
#: wininput.c:543
msgid "&Reset"
msgstr "重設(&R)"

#. __ Context menu:
#: wininput.c:551
msgid "&Default Size"
msgstr "預設大小(&D)"

#. __ Context menu:
#: wininput.c:561
msgid "Scroll&bar"
msgstr "捲軸(&B)"

#. __ Context menu:
#: wininput.c:567
msgid "&Full Screen"
msgstr "全螢幕(&F)"

#. __ Context menu:
#: wininput.c:573
msgid "Flip &Screen"
msgstr "切換螢幕(&S)"

#. __ System menu:
#: wininput.c:591 wininput.c:763
msgid "Copy &Title"
msgstr "複製標題(&T)"

#. __ System menu:
#. __ Context menu:
#. __ System menu:
#: wininput.c:593 wininput.c:747 wininput.c:765
msgid "&Options..."
msgstr "選項(&O)..."

#. __ Context menu:
#: wininput.c:678
msgid "Ope&n"
msgstr "開啟(&N)"

#. __ Context menu:
#: wininput.c:683
msgid "Copy as text"
msgstr "複製text"

#. __ Context menu:
#: wininput.c:687
msgid "Copy as RTF"
msgstr "複製RTF"

#. __ Context menu:
#: wininput.c:689
msgid "Copy as HTML text"
msgstr "複製HTML text"

#. __ Context menu:
#: wininput.c:691
msgid "Copy as HTML"
msgstr "複製HTML"

#. __ Context menu:
#: wininput.c:693
msgid "Copy as HTML full"
msgstr "複製HTML完整"

#. __ Context menu:
#: wininput.c:700
msgid "Select &All"
msgstr "全選(&A)"

#. __ Context menu:
#: wininput.c:702
msgid "Save as &Image"
msgstr "儲存成圖片(&I)"

#. __ Context menu: write terminal window contents as HTML file
#: wininput.c:714
msgid "HTML Screen Dump"
msgstr "將螢幕內容存為 HTML"

#. __ Context menu: clear scrollback buffer (lines scrolled off the window)
#: wininput.c:722
msgid "Clear Scrollback"
msgstr "清除螢幕緩衝區"

#. __ Context menu: generate a TTY BRK condition (tty line interrupt)
#: wininput.c:733
msgid "Send Break"
msgstr "傳送 Break"

#. __ Context menu, user commands
#: wininput.c:835
msgid "User commands"
msgstr "使用者指令"

#: wininput.c:1503 wininput.c:1524 wininput.c:1526 wininput.c:1528
#: wininput.c:1565
msgid "[NO SCROLL] "
msgstr "［停用捲動］"

#: wininput.c:1516 wininput.c:1525 wininput.c:1530 wininput.c:1586
msgid "[SCROLL MODE] "
msgstr "［捲動模式］"

#: winmain.c:3845
msgid "Processes are running in session:"
msgstr "有程式正在執行於 Session："

#: winmain.c:3846
msgid "Close anyway?"
msgstr "仍要關閉？"

#: winmain.c:3870
msgid "Reset terminal?"
msgstr "重設 終端機？"

#: winmain.c:4100
msgid "Try '--help' for more information"
msgstr "嘗試「--help」以獲得更多資訊"

#: winmain.c:4108
msgid "Could not load icon"
msgstr "無法載入圖示"

#: winmain.c:6402
msgid "Usage:"
msgstr "使用方式："

#: winmain.c:6403
msgid "[OPTION]... [ PROGRAM [ARG]... | - ]"
msgstr "[OPTION]... [ PROGRAM [ARG]... | - ]"

#. __ help text (output of -H / --help), after initial line ("synopsis")
#: winmain.c:6406
msgid ""
"Start a new terminal session running the specified program or the user's "
"shell.\n"
"If a dash is given instead of a program, invoke the shell as a login shell.\n"
"\n"
"Options:\n"
"  -c, --config FILE     Load specified config file (cf. -C or -o ThemeFile)\n"
"  -e, --exec ...        Treat remaining arguments as the command to execute\n"
"  -h, --hold never|start|error|always  Keep window open after command "
"finishes\n"
"  -p, --position X,Y    Open window at specified coordinates\n"
"  -p, --position center|left|right|top|bottom  Open window at special "
"position\n"
"  -p, --position @N     Open window on monitor N\n"
"  -s, --size COLS,ROWS  Set screen size in characters (also COLSxROWS)\n"
"  -s, --size maxwidth|maxheight  Set max screen size in given dimension\n"
"  -t, --title TITLE     Set window title (default: the invoked command) (cf. "
"-T)\n"
"  -w, --window normal|min|max|full|hide  Set initial window state\n"
"  -i, --icon FILE[,IX]  Load window icon from file, optionally with index\n"
"  -l, --log FILE|-      Log output to file or stdout\n"
"      --nobidi|--nortl  Disable bidi (right-to-left support)\n"
"  -o, --option OPT=VAL  Set/Override config file option with given value\n"
"  -B, --Border frame|void  Use thin/no window border\n"
"  -R, --Report s|o      Report window position (short/long) after exit\n"
"      --nopin           Make this instance not pinnable to taskbar\n"
"  -D, --daemon          Start new instance with Windows shortcut key\n"
"  -H, --help            Display help and exit\n"
"  -V, --version         Print version information and exit\n"
"See manual page for further command line options and configuration.\n"
msgstr ""
"開啟一個用於執行指定程式或使用者 shell 的新終端機 session。\n"
"如果指定的程式為 dash，將該 shell 呼叫為一個 login shell。\n"
"\n"
"選項：\n"
"    -c, --config FILE     載入指定的設定檔（參見 -C 或 -o ThemeFile）\n"
"    -e, --exec ...        將剩餘參數作為命令執行\n"
"    -h, --hold never|start|error|always  命令執行完後不關閉視窗\n"
"    -p, --position X,Y    在指定座標開啟視窗\n"
"    -p, --position center|left|right|top|bottom  在指定位置開啟視窗\n"
"    -p, --position @N     於螢幕 N 開啟視窗\n"
"    -s, --size COLS,ROWS  設定螢幕大小（也可用 COLSxROWS）\n"
"    -s, --size maxwidth|maxheight  指定螢幕最大尺寸\n"
"    -t, --title TITLE     設定視窗標題（預設：執行的命令）（參見 -T）\n"
"    -w, --window normal|min|max|full|hide  設定初始的視窗狀態\n"
"    -i, --icon FILE[,IX]  自檔案載入視窗圖示，可指定 index\n"
"    -l, --log FILE|-      將記錄輸出至檔案或 stdout\n"
"        --nobidi|--nortl  停用 bidi（由右至左的支援）\n"
"    -o, --option OPT=VAL  以指定的選項設定／覆蓋設定檔中的選項\n"
"    -B, --Border frame|void  使用窄／無邊框\n"
"    -R, --Report s|o      離開前回報視窗位置（短｜長）\n"
"        --nopin           令這個執行個體不能被釘選到工作列\n"
"    -D, --daemon          以 Windows 快捷鍵啟動新的執行個體\n"
"    -H, --help            顯示說明並離開\n"
"    -V, --version         輸出版本資訊並離開\n"
"參閱說明頁面以取得進一步的命令列選項與設定。\n"

#: winmain.c:6580 winmain.c:6693 winmain.c:6700
msgid "WSL distribution '%s' not found"
msgstr "找不到 WSL 發行版 %s"

#: winmain.c:6731
msgid "Duplicate option '%s'"
msgstr "重複的選項「%s」"

#: winmain.c:6739 winmain.c:6830
msgid "Unknown option '%s'"
msgstr "未知的選項「%s」"

#: winmain.c:6741
msgid "Option '%s' requires an argument"
msgstr "選項「%s」需要一個參數"

#: winmain.c:6768
msgid "Syntax error in position argument '%s'"
msgstr "Position 參數「%s」有語法錯誤"

#: winmain.c:6779
msgid "Syntax error in size argument '%s'"
msgstr "Size 參數「%s」有語法錯誤"

#: winmain.c:6939
msgid "Syntax error in geometry argument '%s'"
msgstr "Geometry 參數「%s」有語法錯誤"

#: winmain.c:7038
msgid "Mintty could not detach from caller, starting anyway"
msgstr "Mintty 無法自 caller 分離，仍開始啟動"

#: winmain.c:7387
msgid "Using default title due to invalid characters in program name"
msgstr "由於程式名稱中有不合法的字元，使用預設標題"

#: winsearch.c:232
msgid "◀"
msgstr "◀"

#: winsearch.c:233
msgid "▶"
msgstr "▶"

#: winsearch.c:234
msgid "X"
msgstr "X"

#. __ Options - Text: font properties information: "Leading": total line padding (see option RowSpacing), Bold/Underline modes (font or manual, see options BoldAsFont/UnderlineManual/UnderlineColour)
#: wintext.c:165
msgid "Leading: %d, Bold: %s, Underline: %s"
msgstr "行距：%d，粗體：%s，底線：%s"

#. __ Options - Text: font properties: value taken from font
#: wintext.c:167
msgid "font"
msgstr "字型"

#. __ Options - Text: font properties: value affected by option
#: wintext.c:169
msgid "manual"
msgstr "手動"

#: wintext.c:544
msgid "Font not found, using system substitute"
msgstr "找不到字型，改用系統字型"

#: wintext.c:559
msgid "Font has limited support for character ranges"
msgstr "字型僅支援部分字元範圍"

#: wintext.c:702
msgid "Font installation corrupt, using system substitute"
msgstr "字型安裝錯誤，改用系統字型"

#: wintext.c:756
msgid "Font does not support system locale"
msgstr "字型不支援系統語言"

#: appinfo.h:64
msgid "There is no warranty, to the extent permitted by law."
msgstr "除法律規定外，不承擔任何責任"

#. __ %s: WEBSITE (URL)
#: appinfo.h:69
msgid ""
"Please report bugs or request enhancements through the issue tracker on the "
"mintty project page located at\n"
"%s.\n"
"See also the Wiki there for further hints, thanks and credits."
msgstr ""
"請回報問題或功能改進至 mintty 專案頁面的問題追蹤系統：\n"
"%s\n"
"可參閱 Wiki 以取得更多提示、致謝與許可。"
