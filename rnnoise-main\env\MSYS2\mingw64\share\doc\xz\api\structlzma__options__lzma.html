<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma_options_lzma Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('structlzma__options__lzma.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle"><div class="title">lzma_options_lzma Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Options specific to the LZMA1 and LZMA2 filters.  
 <a href="#details">More...</a></p>

<p><code>#include &lt;lzma12.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:aeb3f86002405a1191af86def46fca5ad" id="r_aeb3f86002405a1191af86def46fca5ad"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aeb3f86002405a1191af86def46fca5ad">dict_size</a></td></tr>
<tr class="memdesc:aeb3f86002405a1191af86def46fca5ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dictionary size in bytes.  <br /></td></tr>
<tr class="separator:aeb3f86002405a1191af86def46fca5ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16a58c1ee3ec18c820d5cb03dde3739a" id="r_a16a58c1ee3ec18c820d5cb03dde3739a"><td class="memItemLeft" align="right" valign="top">const uint8_t *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a16a58c1ee3ec18c820d5cb03dde3739a">preset_dict</a></td></tr>
<tr class="memdesc:a16a58c1ee3ec18c820d5cb03dde3739a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pointer to an initial dictionary.  <br /></td></tr>
<tr class="separator:a16a58c1ee3ec18c820d5cb03dde3739a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a655ad4cce9e4dac9cf2a5c8daaa629e0" id="r_a655ad4cce9e4dac9cf2a5c8daaa629e0"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a655ad4cce9e4dac9cf2a5c8daaa629e0">preset_dict_size</a></td></tr>
<tr class="memdesc:a655ad4cce9e4dac9cf2a5c8daaa629e0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Size of the preset dictionary.  <br /></td></tr>
<tr class="separator:a655ad4cce9e4dac9cf2a5c8daaa629e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95f6188e5b5f05c50ec463a315df3585" id="r_a95f6188e5b5f05c50ec463a315df3585"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a95f6188e5b5f05c50ec463a315df3585">lc</a></td></tr>
<tr class="memdesc:a95f6188e5b5f05c50ec463a315df3585"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of literal context bits.  <br /></td></tr>
<tr class="separator:a95f6188e5b5f05c50ec463a315df3585"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0fe9c54e808fce3090b6994d95fe41fe" id="r_a0fe9c54e808fce3090b6994d95fe41fe"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0fe9c54e808fce3090b6994d95fe41fe">lp</a></td></tr>
<tr class="memdesc:a0fe9c54e808fce3090b6994d95fe41fe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of literal position bits.  <br /></td></tr>
<tr class="separator:a0fe9c54e808fce3090b6994d95fe41fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acae107b3d3e9d0d4fe16103be22f4408" id="r_acae107b3d3e9d0d4fe16103be22f4408"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#acae107b3d3e9d0d4fe16103be22f4408">pb</a></td></tr>
<tr class="memdesc:acae107b3d3e9d0d4fe16103be22f4408"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of position bits.  <br /></td></tr>
<tr class="separator:acae107b3d3e9d0d4fe16103be22f4408"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d711df9bda046fd3899abf21fa250d5" id="r_a1d711df9bda046fd3899abf21fa250d5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866">lzma_mode</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1d711df9bda046fd3899abf21fa250d5">mode</a></td></tr>
<tr class="separator:a1d711df9bda046fd3899abf21fa250d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0352ea7f8b6a43b745a44f6cb4e2d263" id="r_a0352ea7f8b6a43b745a44f6cb4e2d263"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0352ea7f8b6a43b745a44f6cb4e2d263">nice_len</a></td></tr>
<tr class="memdesc:a0352ea7f8b6a43b745a44f6cb4e2d263"><td class="mdescLeft">&#160;</td><td class="mdescRight">Nice length of a match.  <br /></td></tr>
<tr class="separator:a0352ea7f8b6a43b745a44f6cb4e2d263"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa99612cd52259093007f33513882dcd0" id="r_aa99612cd52259093007f33513882dcd0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80">lzma_match_finder</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aa99612cd52259093007f33513882dcd0">mf</a></td></tr>
<tr class="separator:aa99612cd52259093007f33513882dcd0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4226f686e8c9f6288595fe23d0e15713" id="r_a4226f686e8c9f6288595fe23d0e15713"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a4226f686e8c9f6288595fe23d0e15713">depth</a></td></tr>
<tr class="memdesc:a4226f686e8c9f6288595fe23d0e15713"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum search depth in the match finder.  <br /></td></tr>
<tr class="separator:a4226f686e8c9f6288595fe23d0e15713"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade251d13ef46bcacb4e052b83693878c" id="r_ade251d13ef46bcacb4e052b83693878c"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ade251d13ef46bcacb4e052b83693878c">ext_flags</a></td></tr>
<tr class="memdesc:ade251d13ef46bcacb4e052b83693878c"><td class="mdescLeft">&#160;</td><td class="mdescRight">For LZMA_FILTER_LZMA1EXT: Extended flags.  <br /></td></tr>
<tr class="separator:ade251d13ef46bcacb4e052b83693878c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a971da8385dcebd01e60235afb3b717f9" id="r_a971da8385dcebd01e60235afb3b717f9"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a971da8385dcebd01e60235afb3b717f9">ext_size_low</a></td></tr>
<tr class="memdesc:a971da8385dcebd01e60235afb3b717f9"><td class="mdescLeft">&#160;</td><td class="mdescRight">For LZMA_FILTER_LZMA1EXT: Uncompressed size (low bits)  <br /></td></tr>
<tr class="separator:a971da8385dcebd01e60235afb3b717f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae5b3c2375c43ddfacf093980385fb9e3" id="r_ae5b3c2375c43ddfacf093980385fb9e3"><td class="memItemLeft" align="right" valign="top">uint32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae5b3c2375c43ddfacf093980385fb9e3">ext_size_high</a></td></tr>
<tr class="memdesc:ae5b3c2375c43ddfacf093980385fb9e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">For LZMA_FILTER_LZMA1EXT: Uncompressed size (high bits)  <br /></td></tr>
<tr class="separator:ae5b3c2375c43ddfacf093980385fb9e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Options specific to the LZMA1 and LZMA2 filters. </p>
<p>Since LZMA1 and LZMA2 share most of the code, it's simplest to share the options structure too. For encoding, all but the reserved variables need to be initialized unless specifically mentioned otherwise. <a class="el" href="lzma12_8h.html#aa62c28944fe3575653a4c25780400d77" title="Set a compression preset to lzma_options_lzma structure.">lzma_lzma_preset()</a> can be used to get a good starting point.</p>
<p>For raw decoding, both LZMA1 and LZMA2 need dict_size, preset_dict, and preset_dict_size (if preset_dict != NULL). LZMA1 needs also lc, lp, and pb. </p>
</div><h2 class="groupheader">Field Documentation</h2>
<a id="aeb3f86002405a1191af86def46fca5ad" name="aeb3f86002405a1191af86def46fca5ad"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeb3f86002405a1191af86def46fca5ad">&#9670;&#160;</a></span>dict_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_options_lzma::dict_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Dictionary size in bytes. </p>
<p>Dictionary size indicates how many bytes of the recently processed uncompressed data is kept in memory. One method to reduce size of the uncompressed data is to store distance-length pairs, which indicate what data to repeat from the dictionary buffer. Thus, the bigger the dictionary, the better the compression ratio usually is.</p>
<p>Maximum size of the dictionary depends on multiple things:</p><ul>
<li>Memory usage limit</li>
<li>Available address space (not a problem on 64-bit systems)</li>
<li>Selected match finder (encoder only)</li>
</ul>
<p>Currently the maximum dictionary size for encoding is 1.5 GiB (i.e. (UINT32_C(1) &lt;&lt; 30) + (UINT32_C(1) &lt;&lt; 29)) even on 64-bit systems for certain match finder implementation reasons. In the future, there may be match finders that support bigger dictionaries.</p>
<p>Decoder already supports dictionaries up to 4 GiB - 1 B (i.e. UINT32_MAX), so increasing the maximum dictionary size of the encoder won't cause problems for old decoders.</p>
<p>Because extremely small dictionaries sizes would have unneeded overhead in the decoder, the minimum dictionary size is 4096 bytes.</p>
<dl class="section note"><dt>Note</dt><dd>When decoding, too big dictionary does no other harm than wasting memory. </dd></dl>

</div>
</div>
<a id="a16a58c1ee3ec18c820d5cb03dde3739a" name="a16a58c1ee3ec18c820d5cb03dde3739a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a16a58c1ee3ec18c820d5cb03dde3739a">&#9670;&#160;</a></span>preset_dict</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const uint8_t* lzma_options_lzma::preset_dict</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pointer to an initial dictionary. </p>
<p>It is possible to initialize the LZ77 history window using a preset dictionary. It is useful when compressing many similar, relatively small chunks of data independently from each other. The preset dictionary should contain typical strings that occur in the files being compressed. The most probable strings should be near the end of the preset dictionary.</p>
<p>This feature should be used only in special situations. For now, it works correctly only with raw encoding and decoding. Currently none of the container formats supported by liblzma allow preset dictionary when decoding, thus if you create a .xz or .lzma file with preset dictionary, it cannot be decoded with the regular decoder functions. In the future, the .xz format will likely get support for preset dictionary though. </p>

</div>
</div>
<a id="a655ad4cce9e4dac9cf2a5c8daaa629e0" name="a655ad4cce9e4dac9cf2a5c8daaa629e0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a655ad4cce9e4dac9cf2a5c8daaa629e0">&#9670;&#160;</a></span>preset_dict_size</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_options_lzma::preset_dict_size</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Size of the preset dictionary. </p>
<p>Specifies the size of the preset dictionary. If the size is bigger than dict_size, only the last dict_size bytes are processed.</p>
<p>This variable is read only when preset_dict is not NULL. If preset_dict is not NULL but preset_dict_size is zero, no preset dictionary is used (identical to only setting preset_dict to NULL). </p>

</div>
</div>
<a id="a95f6188e5b5f05c50ec463a315df3585" name="a95f6188e5b5f05c50ec463a315df3585"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a95f6188e5b5f05c50ec463a315df3585">&#9670;&#160;</a></span>lc</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_options_lzma::lc</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Number of literal context bits. </p>
<p>How many of the highest bits of the previous uncompressed eight-bit byte (also known as 'literal') are taken into account when predicting the bits of the next literal.</p>
<p>E.g. in typical English text, an upper-case letter is often followed by a lower-case letter, and a lower-case letter is usually followed by another lower-case letter. In the US-ASCII character set, the highest three bits are 010 for upper-case letters and 011 for lower-case letters. When lc is at least 3, the literal coding can take advantage of this property in the uncompressed data.</p>
<p>There is a limit that applies to literal context bits and literal position bits together: lc + lp &lt;= 4. Without this limit the decoding could become very slow, which could have security related results in some cases like email servers doing virus scanning. This limit also simplifies the internal implementation in liblzma.</p>
<p>There may be LZMA1 streams that have lc + lp &gt; 4 (maximum possible lc would be 8). It is not possible to decode such streams with liblzma. </p>

</div>
</div>
<a id="a0fe9c54e808fce3090b6994d95fe41fe" name="a0fe9c54e808fce3090b6994d95fe41fe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0fe9c54e808fce3090b6994d95fe41fe">&#9670;&#160;</a></span>lp</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_options_lzma::lp</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Number of literal position bits. </p>
<p>lp affects what kind of alignment in the uncompressed data is assumed when encoding literals. A literal is a single 8-bit byte. See pb below for more information about alignment. </p>

</div>
</div>
<a id="acae107b3d3e9d0d4fe16103be22f4408" name="acae107b3d3e9d0d4fe16103be22f4408"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acae107b3d3e9d0d4fe16103be22f4408">&#9670;&#160;</a></span>pb</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_options_lzma::pb</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Number of position bits. </p>
<p>pb affects what kind of alignment in the uncompressed data is assumed in general. The default means four-byte alignment (2^ pb =2^2=4), which is often a good choice when there's no better guess.</p>
<p>When the alignment is known, setting pb accordingly may reduce the file size a little. E.g. with text files having one-byte alignment (US-ASCII, ISO-8859-*, UTF-8), setting pb=0 can improve compression slightly. For UTF-16 text, pb=1 is a good choice. If the alignment is an odd number like 3 bytes, pb=0 might be the best choice.</p>
<p>Even though the assumed alignment can be adjusted with pb and lp, LZMA1 and LZMA2 still slightly favor 16-byte alignment. It might be worth taking into account when designing file formats that are likely to be often compressed with LZMA1 or LZMA2. </p>

</div>
</div>
<a id="a1d711df9bda046fd3899abf21fa250d5" name="a1d711df9bda046fd3899abf21fa250d5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1d711df9bda046fd3899abf21fa250d5">&#9670;&#160;</a></span>mode</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="lzma12_8h.html#a1032316e3075c2c8086fb17104b91866">lzma_mode</a> lzma_options_lzma::mode</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Compression mode </p>

</div>
</div>
<a id="a0352ea7f8b6a43b745a44f6cb4e2d263" name="a0352ea7f8b6a43b745a44f6cb4e2d263"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0352ea7f8b6a43b745a44f6cb4e2d263">&#9670;&#160;</a></span>nice_len</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_options_lzma::nice_len</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Nice length of a match. </p>
<p>This determines how many bytes the encoder compares from the match candidates when looking for the best match. Once a match of at least nice_len bytes long is found, the encoder stops looking for better candidates and encodes the match. (Naturally, if the found match is actually longer than nice_len, the actual length is encoded; it's not truncated to nice_len.)</p>
<p>Bigger values usually increase the compression ratio and compression time. For most files, 32 to 128 is a good value, which gives very good compression ratio at good speed.</p>
<p>The exact minimum value depends on the match finder. The maximum is 273, which is the maximum length of a match that LZMA1 and LZMA2 can encode. </p>

</div>
</div>
<a id="aa99612cd52259093007f33513882dcd0" name="aa99612cd52259093007f33513882dcd0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa99612cd52259093007f33513882dcd0">&#9670;&#160;</a></span>mf</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="lzma12_8h.html#acf740075f86fa61dc408d6d0dbf8fa80">lzma_match_finder</a> lzma_options_lzma::mf</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Match finder ID </p>

</div>
</div>
<a id="a4226f686e8c9f6288595fe23d0e15713" name="a4226f686e8c9f6288595fe23d0e15713"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4226f686e8c9f6288595fe23d0e15713">&#9670;&#160;</a></span>depth</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_options_lzma::depth</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Maximum search depth in the match finder. </p>
<p>For every input byte, match finder searches through the hash chain or binary tree in a loop, each iteration going one step deeper in the chain or tree. The searching stops if</p><ul>
<li>a match of at least nice_len bytes long is found;</li>
<li>all match candidates from the hash chain or binary tree have been checked; or</li>
<li>maximum search depth is reached.</li>
</ul>
<p>Maximum search depth is needed to prevent the match finder from wasting too much time in case there are lots of short match candidates. On the other hand, stopping the search before all candidates have been checked can reduce compression ratio.</p>
<p>Setting depth to zero tells liblzma to use an automatic default value, that depends on the selected match finder and nice_len. The default is in the range [4, 200] or so (it may vary between liblzma versions).</p>
<p>Using a bigger depth value than the default can increase compression ratio in some cases. There is no strict maximum value, but high values (thousands or millions) should be used with care: the encoder could remain fast enough with typical input, but malicious input could cause the match finder to slow down dramatically, possibly creating a denial of service attack. </p>

</div>
</div>
<a id="ade251d13ef46bcacb4e052b83693878c" name="ade251d13ef46bcacb4e052b83693878c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ade251d13ef46bcacb4e052b83693878c">&#9670;&#160;</a></span>ext_flags</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_options_lzma::ext_flags</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>For LZMA_FILTER_LZMA1EXT: Extended flags. </p>
<p>This is used only with LZMA_FILTER_LZMA1EXT.</p>
<p>Currently only one flag is supported, LZMA_LZMA1EXT_ALLOW_EOPM:</p>
<ul>
<li>Encoder: If the flag is set, then end marker is written just like it is with LZMA_FILTER_LZMA1. Without this flag the end marker isn't written and the application has to store the uncompressed size somewhere outside the compressed stream. To decompress streams without the end marker, the application has to set the correct uncompressed size in ext_size_low and ext_size_high.</li>
<li><p class="startli">Decoder: If the uncompressed size in ext_size_low and ext_size_high is set to the special value UINT64_MAX (indicating unknown uncompressed size) then this flag is ignored and the end marker must always be present, that is, the behavior is identical to LZMA_FILTER_LZMA1.</p>
<p class="startli">Otherwise, if this flag isn't set, then the input stream must not have the end marker; if the end marker is detected then it will result in LZMA_DATA_ERROR. This is useful when it is known that the stream must not have the end marker and strict validation is wanted.</p>
<p class="startli">If this flag is set, then it is autodetected if the end marker is present after the specified number of uncompressed bytes has been decompressed (ext_size_low and ext_size_high). The end marker isn't allowed in any other position. This behavior is useful when uncompressed size is known but the end marker may or may not be present. This is the case, for example, in .7z files (valid .7z files that have the end marker in LZMA1 streams are rare but they do exist). </p>
</li>
</ul>

</div>
</div>
<a id="a971da8385dcebd01e60235afb3b717f9" name="a971da8385dcebd01e60235afb3b717f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a971da8385dcebd01e60235afb3b717f9">&#9670;&#160;</a></span>ext_size_low</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_options_lzma::ext_size_low</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>For LZMA_FILTER_LZMA1EXT: Uncompressed size (low bits) </p>
<p>The 64-bit uncompressed size is needed for decompression with LZMA_FILTER_LZMA1EXT. The size is ignored by the encoder.</p>
<p>The special value UINT64_MAX indicates that the uncompressed size is unknown and that the end of payload marker (also known as end of stream marker) must be present to indicate the end of the LZMA1 stream. Any other value indicates the expected uncompressed size of the LZMA1 stream. (If LZMA1 was used together with filters that change the size of the data then the uncompressed size of the LZMA1 stream could be different than the final uncompressed size of the filtered stream.)</p>
<p>ext_size_low holds the least significant 32 bits of the uncompressed size. The most significant 32 bits must be set in ext_size_high. The macro <a class="el" href="lzma12_8h.html#a73ed0293db4e59d73a702d66fef537c3" title="Macro to set the 64-bit uncompressed size in ext_size_*.">lzma_set_ext_size(opt_lzma, u64size)</a> can be used to set these members.</p>
<p>The 64-bit uncompressed size is split into two uint32_t variables because there were no reserved uint64_t members and using the same options structure for LZMA_FILTER_LZMA1, LZMA_FILTER_LZMA1EXT, and LZMA_FILTER_LZMA2 was otherwise more convenient than having a new options structure for LZMA_FILTER_LZMA1EXT. (Replacing two uint32_t members with one uint64_t changes the ABI on some systems as the alignment of this struct can increase from 4 bytes to 8.) </p>

</div>
</div>
<a id="ae5b3c2375c43ddfacf093980385fb9e3" name="ae5b3c2375c43ddfacf093980385fb9e3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae5b3c2375c43ddfacf093980385fb9e3">&#9670;&#160;</a></span>ext_size_high</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t lzma_options_lzma::ext_size_high</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>For LZMA_FILTER_LZMA1EXT: Uncompressed size (high bits) </p>
<p>This holds the most significant 32 bits of the uncompressed size. </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>lzma/<a class="el" href="lzma12_8h.html">lzma12.h</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structlzma__options__lzma.html">lzma_options_lzma</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
