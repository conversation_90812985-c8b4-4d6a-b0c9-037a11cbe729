.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_session_key_update" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_session_key_update \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_session_key_update(gnutls_session_t " session ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned flags" 12
zero of \fBGNUTLS_KU_PEER\fP
.SH "DESCRIPTION"
This function will update/refresh the session keys when the
TLS protocol is 1.3 or better. The peer is notified of the
update by sending a message, so this function should be
treated similarly to \fBgnutls_record_send()\fP \-\-i.e., it may
return \fBGNUTLS_E_AGAIN\fP or \fBGNUTLS_E_INTERRUPTED\fP.

When this flag \fBGNUTLS_KU_PEER\fP is specified, this function
in addition to updating the local keys, will ask the peer to
refresh its keys too.

If the negotiated version is not TLS 1.3 or better this
function will return \fBGNUTLS_E_INVALID_REQUEST\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "SINCE"
3.6.3
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
