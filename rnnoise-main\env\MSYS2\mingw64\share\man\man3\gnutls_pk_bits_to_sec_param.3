.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pk_bits_to_sec_param" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pk_bits_to_sec_param \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "gnutls_sec_param_t gnutls_pk_bits_to_sec_param(gnutls_pk_algorithm_t " algo ", unsigned int " bits ");"
.SH ARGUMENTS
.IP "gnutls_pk_algorithm_t algo" 12
is a public key algorithm
.IP "unsigned int bits" 12
is the number of bits
.SH "DESCRIPTION"
This is the inverse of \fBgnutls_sec_param_to_pk_bits()\fP. Given an algorithm
and the number of bits, it will return the security parameter. This is
a rough indication.
.SH "RETURNS"
The security parameter.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
