## Syntax highlighting for Lua.

## Original author:  <PERSON>
## License:  GPL version 3 or newer

syntax lua "\.lua$"
magic "Lua script"
comment "--"

linter luacheck --no-color

color brightwhite "\[\[.*\]\]"

# Operators
color brightyellow ":|\*|/|%|\+|-|\^|>|>=|<|<=|~=|=|\.\.|#|\<(not|and|or)\>"
# Don't partially color ... as an operator
color normal "\.\.\."

# Statements
color brightblue "\<(do|end|while|repeat|until|if|elseif|then|else|for|in|function|local|return|break)\>"

# Keywords
color brightyellow "\<(_G|_VERSION|assert|collectgarbage|dofile|error|getfenv|getmetatable|ipairs|load|loadfile|module|next|pairs|pcall|print|rawequal|rawget|rawlen|rawset|require|select|setfenv|setmetatable|tonumber|tostring|type|unpack|xpcall)[[:blank:]]*\("

# Standard library
color brightyellow "\<coroutine\.(create|isyieldable|resume|running|status|wrap|yield)\>"
color brightyellow "\<debug\.(debug|(get|set)(fenv|hook|local|metatable|(up|user)value)|getinfo|getregistry|traceback|upvalue(id|join))\>"
color brightyellow "\<io\.(close|flush|input|lines|output|p?open|read|tmpfile|type|write|std(in|out|err))\>"
color brightyellow "\<math\.(abs|acos|asin|atan2?|ceil|cosh?|deg|exp|floor|fmod|frexp|huge|ldexp|log10|log)\>"
color brightyellow "\<math\.((max|min)(integer)?|modf?|pi|pow|rad|random(seed)?|sinh?|sqrt|tan|tointeger|type|ult)\>"
color brightyellow "\<os\.(clock|date|difftime|execute|exit|getenv|remove|rename|setlocale|time|tmpname)\>"
color brightyellow "\<package\.(config|cpath|loaded|loadlib|path|preload|searchers|searchpath|seeall)\>"
color brightyellow "\<string\.(byte|char|dump|find|format|gmatch|gsub|len|lower|match|pack|packsize|rep|reverse|sub|unpack|upper)\>"
color brightyellow "\<table\.(concat|insert|maxn|move|pack|remove|sort|unpack)\>"
color brightyellow "\<utf8\.(char|charpattern|codepoint|codes|len|offset)\>"

# File handle methods
color brightyellow ":(close|flush|lines|read|seek|setvbuf|write)\>"

# External files
color brightgreen "\<(dofile|require)\>"

# Special words
color brightmagenta "\<(false|nil|true)\>"

# Decimal and hexadecimal numbers
color red "\<[0-9]+(\.[0-9]*)?([Ee][+-]?[0-9]+)?\>"
color red "\<0x[[:xdigit:]]+(\.[[:xdigit:]]*)?([Pp][+-]?[0-9]+)?\>"

# Brackets
color brightmagenta "\(|\)|\[|\]|\{|\}"

# Shebang
color brightcyan "^#!.*"

# Strings
color red ""([^"\]|\\.)*"|'([^'\]|\\.)*'"

# Simple comments and multiline comments
color green "--.*"
color green start="--\[\[" end="\]\]"
