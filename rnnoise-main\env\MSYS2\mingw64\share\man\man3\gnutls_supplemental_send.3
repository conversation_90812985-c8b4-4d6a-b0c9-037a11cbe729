.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_supplemental_send" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_supplemental_send \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_supplemental_send(gnutls_session_t " session ", unsigned " do_send_supplemental ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "unsigned do_send_supplemental" 12
non\-zero in order to send supplemental data
.SH "DESCRIPTION"
This function is to be called by an extension handler to
instruct gnutls to send supplemental data during the handshake process.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
