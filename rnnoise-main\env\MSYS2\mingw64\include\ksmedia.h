/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#if !defined(_KSMEDIA_)
#define _KSMEDIA_

#include <ks.h>

typedef struct {
  KSPROPERTY Property;
  KSMULTIPLE_ITEM MultipleItem;
} KSMULTIPLE_DATA_PROP,*PKSMULTIPLE_DATA_PROP;

#define STATIC_KSMEDIUMSETID_MidiBus					\
	0x05908040,0x3246,0x11D0,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("05908040-3246-11D0-A5D6-28DB04C10000",KSMEDIUMSETID_MidiBus);
#define KSMEDIUMSETID_MidiBus DEFINE_GUIDNAMED(KSMEDIUMSETID_MidiBus)

#define STATIC_KSMEDIUMSETID_VPBus					\
	0xA18C15EC,0xCE43,0x11D0,0xAB,0xE7,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("A18C15EC-CE43-11D0-ABE7-00A0C9223196",KSMEDIUMSETID_VPBus);
#define KSMEDIUMSETID_VPBus DEFINE_GUIDNAMED(KSMEDIUMSETID_VPBus)

#define STATIC_KSINTERFACESETID_Media					\
	0x3A13EB40,0x30A7,0x11D0,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("3A13EB40-30A7-11D0-A5D6-28DB04C10000",KSINTERFACESETID_Media);
#define KSINTERFACESETID_Media DEFINE_GUIDNAMED(KSINTERFACESETID_Media)

typedef enum {
  KSINTERFACE_MEDIA_MUSIC,
  KSINTERFACE_MEDIA_WAVE_BUFFERED,
  KSINTERFACE_MEDIA_WAVE_QUEUED
} KSINTERFACE_MEDIA;

#ifndef INIT_USBAUDIO_MID
#define INIT_USBAUDIO_MID(guid,id)					\
{									\
	(guid)->Data1 = 0x4e1cecd2 + (USHORT)(id);			\
	(guid)->Data2 = 0x1679;						\
	(guid)->Data3 = 0x463b;						\
	(guid)->Data4[0] = 0xa7;					\
	(guid)->Data4[1] = 0x2f;					\
	(guid)->Data4[2] = 0xa5;					\
	(guid)->Data4[3] = 0xbf;					\
	(guid)->Data4[4] = 0x64;					\
	(guid)->Data4[5] = 0xc8;					\
	(guid)->Data4[6] = 0x6e;					\
	(guid)->Data4[7] = 0xba;					\
}
#define EXTRACT_USBAUDIO_MID(guid)					\
	(USHORT)((guid)->Data1 - 0x4e1cecd2)
#define DEFINE_USBAUDIO_MID_GUID(id)					\
	0x4e1cecd2+(USHORT)(id),0x1679,0x463b,0xa7,0x2f,0xa5,0xbf,0x64,0xc8,0x6e,0xba
#define IS_COMPATIBLE_USBAUDIO_MID(guid)				\
	(((guid)->Data1 >= 0x4e1cecd2) &&				\
	 ((guid)->Data1 < 0x4e1cecd2 + 0xffff) &&			\
	 ((guid)->Data2 == 0x1679) &&					\
	 ((guid)->Data3 == 0x463b) &&					\
	 ((guid)->Data4[0] == 0xa7) &&					\
	 ((guid)->Data4[1] == 0x2f) &&					\
	 ((guid)->Data4[2] == 0xa5) &&					\
	 ((guid)->Data4[3] == 0xbf) &&					\
	 ((guid)->Data4[4] == 0x64) &&					\
	 ((guid)->Data4[5] == 0xc8) &&					\
	 ((guid)->Data4[6] == 0x6e) &&					\
	 ((guid)->Data4[7] == 0xba) )
#endif /* INIT_USBAUDIO_MID */

#ifndef INIT_USBAUDIO_PID
#define INIT_USBAUDIO_PID(guid,id)					\
{									\
	(guid)->Data1 = 0xabcc5a5e + (USHORT)(id);			\
	(guid)->Data2 = 0xc263;						\
	(guid)->Data3 = 0x463b;						\
	(guid)->Data4[0] = 0xa7;					\
	(guid)->Data4[1] = 0x2f;					\
	(guid)->Data4[2] = 0xa5;					\
	(guid)->Data4[3] = 0xbf;					\
	(guid)->Data4[4] = 0x64;					\
	(guid)->Data4[5] = 0xc8;					\
	(guid)->Data4[6] = 0x6e;					\
	(guid)->Data4[7] = 0xba;					\
}
#define EXTRACT_USBAUDIO_PID(guid)					\
	(USHORT)((guid)->Data1 - 0xabcc5a5e)
#define DEFINE_USBAUDIO_PID_GUID(id)					\
	0xabcc5a5e+(USHORT)(id),0xc263,0x463b,0xa7,0x2f,0xa5,0xbf,0x64,0xc8,0x6e,0xba
#define IS_COMPATIBLE_USBAUDIO_PID(guid)				\
	(((guid)->Data1 >= 0xabcc5a5e) &&				\
	 ((guid)->Data1 < 0xabcc5a5e + 0xffff) &&			\
	 ((guid)->Data2 == 0xc263) &&					\
	 ((guid)->Data3 == 0x463b) &&					\
	 ((guid)->Data4[0] == 0xa7) &&					\
	 ((guid)->Data4[1] == 0x2f) &&					\
	 ((guid)->Data4[2] == 0xa5) &&					\
	 ((guid)->Data4[3] == 0xbf) &&					\
	 ((guid)->Data4[4] == 0x64) &&					\
	 ((guid)->Data4[5] == 0xc8) &&					\
	 ((guid)->Data4[6] == 0x6e) &&					\
	 ((guid)->Data4[7] == 0xba) )
#endif /* INIT_USBAUDIO_PID */

#ifndef INIT_USBAUDIO_PRODUCT_NAME
#define INIT_USBAUDIO_PRODUCT_NAME(guid,vid,pid,strIndex)		\
{									\
	(guid)->Data1 = 0XFC575048 + (USHORT)(vid);			\
	(guid)->Data2 = 0x2E08 + (USHORT)(pid);				\
	(guid)->Data3 = 0x463B + (USHORT)(strIndex);			\
	(guid)->Data4[0] = 0xA7;					\
	(guid)->Data4[1] = 0x2F;					\
	(guid)->Data4[2] = 0xA5;					\
	(guid)->Data4[3] = 0xBF;					\
	(guid)->Data4[4] = 0x64;					\
	(guid)->Data4[5] = 0xC8;					\
	(guid)->Data4[6] = 0x6E;					\
	(guid)->Data4[7] = 0xBA;					\
}
#define DEFINE_USBAUDIO_PRODUCT_NAME(vid,pid,strIndex)			\
	0xFC575048+(USHORT)(vid),0x2E08+(USHORT)(pid),0x463B+(USHORT)(strIndex),0xA7,0x2F,0xA5,0xBF,0x64,0xC8,0x6E,0xBA
#endif /* INIT_USBAUDIO_PRODUCT_NAME */

#define STATIC_KSCOMPONENTID_USBAUDIO					\
	0x8F1275F0,0x26E9,0x4264,0xBA,0x4D,0x39,0xFF,0xF0,0x1D,0x94,0xAA
DEFINE_GUIDSTRUCT("8F1275F0-26E9-4264-BA4D-39FFF01D94AA",KSCOMPONENTID_USBAUDIO);
#define KSCOMPONENTID_USBAUDIO DEFINE_GUIDNAMED(KSCOMPONENTID_USBAUDIO)

#define INIT_USB_TERMINAL(guid,id)					\
{									\
	(guid)->Data1 = 0xDFF219E0 + (USHORT)(id);			\
	(guid)->Data2 = 0xF70F;						\
	(guid)->Data3 = 0x11D0;						\
	(guid)->Data4[0] = 0xb9;					\
	(guid)->Data4[1] = 0x17;					\
	(guid)->Data4[2] = 0x00;					\
	(guid)->Data4[3] = 0xa0;					\
	(guid)->Data4[4] = 0xc9;					\
	(guid)->Data4[5] = 0x22;					\
	(guid)->Data4[6] = 0x31;					\
	(guid)->Data4[7] = 0x96;					\
}
#define EXTRACT_USB_TERMINAL(guid)					\
	(USHORT)((guid)->Data1 - 0xDFF219E0)
#define DEFINE_USB_TERMINAL_GUID(id)					\
	0xDFF219E0+(USHORT)(id),0xF70F,0x11D0,0xB9,0x17,0x00,0xA0,0xC9,0x22,0x31,0x96

#define STATIC_KSNODETYPE_MICROPHONE					\
	DEFINE_USB_TERMINAL_GUID(0x0201)
DEFINE_GUIDSTRUCT("DFF21BE1-F70F-11D0-B917-00A0C9223196",KSNODETYPE_MICROPHONE);
#define KSNODETYPE_MICROPHONE DEFINE_GUIDNAMED(KSNODETYPE_MICROPHONE)

#define STATIC_KSNODETYPE_DESKTOP_MICROPHONE				\
	DEFINE_USB_TERMINAL_GUID(0x0202)
DEFINE_GUIDSTRUCT("DFF21BE2-F70F-11D0-B917-00A0C9223196",KSNODETYPE_DESKTOP_MICROPHONE);
#define KSNODETYPE_DESKTOP_MICROPHONE DEFINE_GUIDNAMED(KSNODETYPE_DESKTOP_MICROPHONE)

#define STATIC_KSNODETYPE_PERSONAL_MICROPHONE				\
	DEFINE_USB_TERMINAL_GUID(0x0203)
DEFINE_GUIDSTRUCT("DFF21BE3-F70F-11D0-B917-00A0C9223196",KSNODETYPE_PERSONAL_MICROPHONE);
#define KSNODETYPE_PERSONAL_MICROPHONE DEFINE_GUIDNAMED(KSNODETYPE_PERSONAL_MICROPHONE)

#define STATIC_KSNODETYPE_OMNI_DIRECTIONAL_MICROPHONE			\
	DEFINE_USB_TERMINAL_GUID(0x0204)
DEFINE_GUIDSTRUCT("DFF21BE4-F70F-11D0-B917-00A0C9223196",KSNODETYPE_OMNI_DIRECTIONAL_MICROPHONE);
#define KSNODETYPE_OMNI_DIRECTIONAL_MICROPHONE DEFINE_GUIDNAMED(KSNODETYPE_OMNI_DIRECTIONAL_MICROPHONE)

#define STATIC_KSNODETYPE_MICROPHONE_ARRAY				\
	DEFINE_USB_TERMINAL_GUID(0x0205)
DEFINE_GUIDSTRUCT("DFF21BE5-F70F-11D0-B917-00A0C9223196",KSNODETYPE_MICROPHONE_ARRAY);
#define KSNODETYPE_MICROPHONE_ARRAY DEFINE_GUIDNAMED(KSNODETYPE_MICROPHONE_ARRAY)

#define STATIC_KSNODETYPE_PROCESSING_MICROPHONE_ARRAY			\
	DEFINE_USB_TERMINAL_GUID(0x0206)
DEFINE_GUIDSTRUCT("DFF21BE6-F70F-11D0-B917-00A0C9223196",KSNODETYPE_PROCESSING_MICROPHONE_ARRAY);
#define KSNODETYPE_PROCESSING_MICROPHONE_ARRAY DEFINE_GUIDNAMED(KSNODETYPE_PROCESSING_MICROPHONE_ARRAY)

#define STATIC_KSCATEGORY_MICROPHONE_ARRAY_PROCESSOR			\
	0x830a44f2,0xa32d,0x476b,0xbe,0x97,0x42,0x84,0x56,0x73,0xb3,0x5a
DEFINE_GUIDSTRUCT("830a44f2-a32d-476b-be97-42845673b35a",KSCATEGORY_MICROPHONE_ARRAY_PROCESSOR);
#define KSCATEGORY_MICROPHONE_ARRAY_PROCESSOR DEFINE_GUIDNAMED(KSCATEGORY_MICROPHONE_ARRAY_PROCESSOR)

#define STATIC_KSNODETYPE_SPEAKER					\
	DEFINE_USB_TERMINAL_GUID(0x0301)
DEFINE_GUIDSTRUCT("DFF21CE1-F70F-11D0-B917-00A0C9223196",KSNODETYPE_SPEAKER);
#define KSNODETYPE_SPEAKER DEFINE_GUIDNAMED(KSNODETYPE_SPEAKER)

#define STATIC_KSNODETYPE_HEADPHONES					\
	DEFINE_USB_TERMINAL_GUID(0x0302)
DEFINE_GUIDSTRUCT("DFF21CE2-F70F-11D0-B917-00A0C9223196",KSNODETYPE_HEADPHONES);
#define KSNODETYPE_HEADPHONES DEFINE_GUIDNAMED(KSNODETYPE_HEADPHONES)

#define STATIC_KSNODETYPE_HEAD_MOUNTED_DISPLAY_AUDIO			\
	DEFINE_USB_TERMINAL_GUID(0x0303)
DEFINE_GUIDSTRUCT("DFF21CE3-F70F-11D0-B917-00A0C9223196",KSNODETYPE_HEAD_MOUNTED_DISPLAY_AUDIO);
#define KSNODETYPE_HEAD_MOUNTED_DISPLAY_AUDIO DEFINE_GUIDNAMED(KSNODETYPE_HEAD_MOUNTED_DISPLAY_AUDIO)

#define STATIC_KSNODETYPE_DESKTOP_SPEAKER				\
	DEFINE_USB_TERMINAL_GUID(0x0304)
DEFINE_GUIDSTRUCT("DFF21CE4-F70F-11D0-B917-00A0C9223196",KSNODETYPE_DESKTOP_SPEAKER);
#define KSNODETYPE_DESKTOP_SPEAKER DEFINE_GUIDNAMED(KSNODETYPE_DESKTOP_SPEAKER)

#define STATIC_KSNODETYPE_ROOM_SPEAKER					\
	DEFINE_USB_TERMINAL_GUID(0x0305)
DEFINE_GUIDSTRUCT("DFF21CE5-F70F-11D0-B917-00A0C9223196",KSNODETYPE_ROOM_SPEAKER);
#define KSNODETYPE_ROOM_SPEAKER DEFINE_GUIDNAMED(KSNODETYPE_ROOM_SPEAKER)

#define STATIC_KSNODETYPE_COMMUNICATION_SPEAKER				\
	DEFINE_USB_TERMINAL_GUID(0x0306)
DEFINE_GUIDSTRUCT("DFF21CE6-F70F-11D0-B917-00A0C9223196",KSNODETYPE_COMMUNICATION_SPEAKER);
#define KSNODETYPE_COMMUNICATION_SPEAKER DEFINE_GUIDNAMED(KSNODETYPE_COMMUNICATION_SPEAKER)

#define STATIC_KSNODETYPE_LOW_FREQUENCY_EFFECTS_SPEAKER			\
	DEFINE_USB_TERMINAL_GUID(0x0307)
DEFINE_GUIDSTRUCT("DFF21CE7-F70F-11D0-B917-00A0C9223196",KSNODETYPE_LOW_FREQUENCY_EFFECTS_SPEAKER);
#define KSNODETYPE_LOW_FREQUENCY_EFFECTS_SPEAKER DEFINE_GUIDNAMED(KSNODETYPE_LOW_FREQUENCY_EFFECTS_SPEAKER)

#define STATIC_KSNODETYPE_HANDSET					\
	DEFINE_USB_TERMINAL_GUID(0x0401)
DEFINE_GUIDSTRUCT("DFF21DE1-F70F-11D0-B917-00A0C9223196",KSNODETYPE_HANDSET);
#define KSNODETYPE_HANDSET DEFINE_GUIDNAMED(KSNODETYPE_HANDSET)

#define STATIC_KSNODETYPE_HEADSET					\
	DEFINE_USB_TERMINAL_GUID(0x0402)
DEFINE_GUIDSTRUCT("DFF21DE2-F70F-11D0-B917-00A0C9223196",KSNODETYPE_HEADSET);
#define KSNODETYPE_HEADSET DEFINE_GUIDNAMED(KSNODETYPE_HEADSET)

#define STATIC_KSNODETYPE_SPEAKERPHONE_NO_ECHO_REDUCTION		\
	DEFINE_USB_TERMINAL_GUID(0x0403)
DEFINE_GUIDSTRUCT("DFF21DE3-F70F-11D0-B917-00A0C9223196",KSNODETYPE_SPEAKERPHONE_NO_ECHO_REDUCTION);
#define KSNODETYPE_SPEAKERPHONE_NO_ECHO_REDUCTION DEFINE_GUIDNAMED(KSNODETYPE_SPEAKERPHONE_NO_ECHO_REDUCTION)

#define STATIC_KSNODETYPE_ECHO_SUPPRESSING_SPEAKERPHONE			\
	DEFINE_USB_TERMINAL_GUID(0x0404)
DEFINE_GUIDSTRUCT("DFF21DE4-F70F-11D0-B917-00A0C9223196",KSNODETYPE_ECHO_SUPPRESSING_SPEAKERPHONE);
#define KSNODETYPE_ECHO_SUPPRESSING_SPEAKERPHONE DEFINE_GUIDNAMED(KSNODETYPE_ECHO_SUPPRESSING_SPEAKERPHONE)

#define STATIC_KSNODETYPE_ECHO_CANCELING_SPEAKERPHONE			\
	DEFINE_USB_TERMINAL_GUID(0x0405)
DEFINE_GUIDSTRUCT("DFF21DE5-F70F-11D0-B917-00A0C9223196",KSNODETYPE_ECHO_CANCELING_SPEAKERPHONE);
#define KSNODETYPE_ECHO_CANCELING_SPEAKERPHONE DEFINE_GUIDNAMED(KSNODETYPE_ECHO_CANCELING_SPEAKERPHONE)

#define STATIC_KSNODETYPE_PHONE_LINE					\
	DEFINE_USB_TERMINAL_GUID(0x0501)
DEFINE_GUIDSTRUCT("DFF21EE1-F70F-11D0-B917-00A0C9223196",KSNODETYPE_PHONE_LINE);
#define KSNODETYPE_PHONE_LINE DEFINE_GUIDNAMED(KSNODETYPE_PHONE_LINE)

#define STATIC_KSNODETYPE_TELEPHONE					\
	DEFINE_USB_TERMINAL_GUID(0x0502)
DEFINE_GUIDSTRUCT("DFF21EE2-F70F-11D0-B917-00A0C9223196",KSNODETYPE_TELEPHONE);
#define KSNODETYPE_TELEPHONE DEFINE_GUIDNAMED(KSNODETYPE_TELEPHONE)

#define STATIC_KSNODETYPE_DOWN_LINE_PHONE				\
	DEFINE_USB_TERMINAL_GUID(0x0503)
DEFINE_GUIDSTRUCT("DFF21EE3-F70F-11D0-B917-00A0C9223196",KSNODETYPE_DOWN_LINE_PHONE);
#define KSNODETYPE_DOWN_LINE_PHONE DEFINE_GUIDNAMED(KSNODETYPE_DOWN_LINE_PHONE)

#define STATIC_KSNODETYPE_ANALOG_CONNECTOR				\
	DEFINE_USB_TERMINAL_GUID(0x601)
DEFINE_GUIDSTRUCT("DFF21FE1-F70F-11D0-B917-00A0C9223196",KSNODETYPE_ANALOG_CONNECTOR);
#define KSNODETYPE_ANALOG_CONNECTOR DEFINE_GUIDNAMED(KSNODETYPE_ANALOG_CONNECTOR)

#define STATIC_KSNODETYPE_DIGITAL_AUDIO_INTERFACE			\
	DEFINE_USB_TERMINAL_GUID(0x0602)
DEFINE_GUIDSTRUCT("DFF21FE2-F70F-11D0-B917-00A0C9223196",KSNODETYPE_DIGITAL_AUDIO_INTERFACE);
#define KSNODETYPE_DIGITAL_AUDIO_INTERFACE DEFINE_GUIDNAMED(KSNODETYPE_DIGITAL_AUDIO_INTERFACE)

#define STATIC_KSNODETYPE_LINE_CONNECTOR				\
	DEFINE_USB_TERMINAL_GUID(0x0603)
DEFINE_GUIDSTRUCT("DFF21FE3-F70F-11D0-B917-00A0C9223196",KSNODETYPE_LINE_CONNECTOR);
#define KSNODETYPE_LINE_CONNECTOR DEFINE_GUIDNAMED(KSNODETYPE_LINE_CONNECTOR)

#define STATIC_KSNODETYPE_LEGACY_AUDIO_CONNECTOR			\
	DEFINE_USB_TERMINAL_GUID(0x0604)
DEFINE_GUIDSTRUCT("DFF21FE4-F70F-11D0-B917-00A0C9223196",KSNODETYPE_LEGACY_AUDIO_CONNECTOR);
#define KSNODETYPE_LEGACY_AUDIO_CONNECTOR DEFINE_GUIDNAMED(KSNODETYPE_LEGACY_AUDIO_CONNECTOR)

#define STATIC_KSNODETYPE_SPDIF_INTERFACE				\
	DEFINE_USB_TERMINAL_GUID(0x0605)
DEFINE_GUIDSTRUCT("DFF21FE5-F70F-11D0-B917-00A0C9223196",KSNODETYPE_SPDIF_INTERFACE);
#define KSNODETYPE_SPDIF_INTERFACE DEFINE_GUIDNAMED(KSNODETYPE_SPDIF_INTERFACE)

#define STATIC_KSNODETYPE_1394_DA_STREAM				\
	DEFINE_USB_TERMINAL_GUID(0x0606)
DEFINE_GUIDSTRUCT("DFF21FE6-F70F-11D0-B917-00A0C9223196",KSNODETYPE_1394_DA_STREAM);
#define KSNODETYPE_1394_DA_STREAM DEFINE_GUIDNAMED(KSNODETYPE_1394_DA_STREAM)

#define STATIC_KSNODETYPE_1394_DV_STREAM_SOUNDTRACK			\
	DEFINE_USB_TERMINAL_GUID(0x0607)
DEFINE_GUIDSTRUCT("DFF21FE7-F70F-11D0-B917-00A0C9223196",KSNODETYPE_1394_DV_STREAM_SOUNDTRACK);
#define KSNODETYPE_1394_DV_STREAM_SOUNDTRACK DEFINE_GUIDNAMED(KSNODETYPE_1394_DV_STREAM_SOUNDTRACK)

#define STATIC_KSNODETYPE_LEVEL_CALIBRATION_NOISE_SOURCE		\
	DEFINE_USB_TERMINAL_GUID(0x0701)
DEFINE_GUIDSTRUCT("DFF220E1-F70F-11D0-B917-00A0C9223196",KSNODETYPE_LEVEL_CALIBRATION_NOISE_SOURCE);
#define KSNODETYPE_LEVEL_CALIBRATION_NOISE_SOURCE DEFINE_GUIDNAMED(KSNODETYPE_LEVEL_CALIBRATION_NOISE_SOURCE)

#define STATIC_KSNODETYPE_EQUALIZATION_NOISE				\
	DEFINE_USB_TERMINAL_GUID(0x0702)
DEFINE_GUIDSTRUCT("DFF220E2-F70F-11D0-B917-00A0C9223196",KSNODETYPE_EQUALIZATION_NOISE);
#define KSNODETYPE_EQUALIZATION_NOISE DEFINE_GUIDNAMED(KSNODETYPE_EQUALIZATION_NOISE)

#define STATIC_KSNODETYPE_CD_PLAYER					\
	DEFINE_USB_TERMINAL_GUID(0x0703)
DEFINE_GUIDSTRUCT("DFF220E3-F70F-11D0-B917-00A0C9223196",KSNODETYPE_CD_PLAYER);
#define KSNODETYPE_CD_PLAYER DEFINE_GUIDNAMED(KSNODETYPE_CD_PLAYER)

#define STATIC_KSNODETYPE_DAT_IO_DIGITAL_AUDIO_TAPE			\
	DEFINE_USB_TERMINAL_GUID(0x0704)
DEFINE_GUIDSTRUCT("DFF220E4-F70F-11D0-B917-00A0C9223196",KSNODETYPE_DAT_IO_DIGITAL_AUDIO_TAPE);
#define KSNODETYPE_DAT_IO_DIGITAL_AUDIO_TAPE DEFINE_GUIDNAMED(KSNODETYPE_DAT_IO_DIGITAL_AUDIO_TAPE)

#define STATIC_KSNODETYPE_DCC_IO_DIGITAL_COMPACT_CASSETTE		\
	DEFINE_USB_TERMINAL_GUID(0x0705)
DEFINE_GUIDSTRUCT("DFF220E5-F70F-11D0-B917-00A0C9223196",KSNODETYPE_DCC_IO_DIGITAL_COMPACT_CASSETTE);
#define KSNODETYPE_DCC_IO_DIGITAL_COMPACT_CASSETTE DEFINE_GUIDNAMED(KSNODETYPE_DCC_IO_DIGITAL_COMPACT_CASSETTE)

#define STATIC_KSNODETYPE_MINIDISK					\
	DEFINE_USB_TERMINAL_GUID(0x0706)
DEFINE_GUIDSTRUCT("DFF220E6-F70F-11D0-B917-00A0C9223196",KSNODETYPE_MINIDISK);
#define KSNODETYPE_MINIDISK DEFINE_GUIDNAMED(KSNODETYPE_MINIDISK)

#define STATIC_KSNODETYPE_ANALOG_TAPE					\
	DEFINE_USB_TERMINAL_GUID(0x0707)
DEFINE_GUIDSTRUCT("DFF220E7-F70F-11D0-B917-00A0C9223196",KSNODETYPE_ANALOG_TAPE);
#define KSNODETYPE_ANALOG_TAPE DEFINE_GUIDNAMED(KSNODETYPE_ANALOG_TAPE)

#define STATIC_KSNODETYPE_PHONOGRAPH					\
	DEFINE_USB_TERMINAL_GUID(0x0708)
DEFINE_GUIDSTRUCT("DFF220E8-F70F-11D0-B917-00A0C9223196",KSNODETYPE_PHONOGRAPH);
#define KSNODETYPE_PHONOGRAPH DEFINE_GUIDNAMED(KSNODETYPE_PHONOGRAPH)

#define STATIC_KSNODETYPE_VCR_AUDIO					\
	DEFINE_USB_TERMINAL_GUID(0x0708)
DEFINE_GUIDSTRUCT("DFF220E9-F70F-11D0-B917-00A0C9223196",KSNODETYPE_VCR_AUDIO);
#define KSNODETYPE_VCR_AUDIO DEFINE_GUIDNAMED(KSNODETYPE_VCR_AUDIO)

#define STATIC_KSNODETYPE_VIDEO_DISC_AUDIO				\
	DEFINE_USB_TERMINAL_GUID(0x070A)
DEFINE_GUIDSTRUCT("DFF220EA-F70F-11D0-B917-00A0C9223196",KSNODETYPE_VIDEO_DISC_AUDIO);
#define KSNODETYPE_VIDEO_DISC_AUDIO DEFINE_GUIDNAMED(KSNODETYPE_VIDEO_DISC_AUDIO)

#define STATIC_KSNODETYPE_DVD_AUDIO					\
	DEFINE_USB_TERMINAL_GUID(0x070B)
DEFINE_GUIDSTRUCT("DFF220EB-F70F-11D0-B917-00A0C9223196",KSNODETYPE_DVD_AUDIO);
#define KSNODETYPE_DVD_AUDIO DEFINE_GUIDNAMED(KSNODETYPE_DVD_AUDIO)

#define STATIC_KSNODETYPE_TV_TUNER_AUDIO				\
	DEFINE_USB_TERMINAL_GUID(0x070C)
DEFINE_GUIDSTRUCT("DFF220EC-F70F-11D0-B917-00A0C9223196",KSNODETYPE_TV_TUNER_AUDIO);
#define KSNODETYPE_TV_TUNER_AUDIO DEFINE_GUIDNAMED(KSNODETYPE_TV_TUNER_AUDIO)

#define STATIC_KSNODETYPE_SATELLITE_RECEIVER_AUDIO			\
	DEFINE_USB_TERMINAL_GUID(0x070D)
DEFINE_GUIDSTRUCT("DFF220ED-F70F-11D0-B917-00A0C9223196",KSNODETYPE_SATELLITE_RECEIVER_AUDIO);
#define KSNODETYPE_SATELLITE_RECEIVER_AUDIO DEFINE_GUIDNAMED(KSNODETYPE_SATELLITE_RECEIVER_AUDIO)

#define STATIC_KSNODETYPE_CABLE_TUNER_AUDIO				\
	DEFINE_USB_TERMINAL_GUID(0x070E)
DEFINE_GUIDSTRUCT("DFF220EE-F70F-11D0-B917-00A0C9223196",KSNODETYPE_CABLE_TUNER_AUDIO);
#define KSNODETYPE_CABLE_TUNER_AUDIO DEFINE_GUIDNAMED(KSNODETYPE_CABLE_TUNER_AUDIO)

#define STATIC_KSNODETYPE_DSS_AUDIO					\
	DEFINE_USB_TERMINAL_GUID(0x070F)
DEFINE_GUIDSTRUCT("DFF220EF-F70F-11D0-B917-00A0C9223196",KSNODETYPE_DSS_AUDIO);
#define KSNODETYPE_DSS_AUDIO DEFINE_GUIDNAMED(KSNODETYPE_DSS_AUDIO)

#define STATIC_KSNODETYPE_RADIO_RECEIVER				\
	DEFINE_USB_TERMINAL_GUID(0x0710)
DEFINE_GUIDSTRUCT("DFF220F0-F70F-11D0-B917-00A0C9223196",KSNODETYPE_RADIO_RECEIVER);
#define KSNODETYPE_RADIO_RECEIVER DEFINE_GUIDNAMED(KSNODETYPE_RADIO_RECEIVER)

#define STATIC_KSNODETYPE_RADIO_TRANSMITTER				\
	DEFINE_USB_TERMINAL_GUID(0x0711)
DEFINE_GUIDSTRUCT("DFF220F1-F70F-11D0-B917-00A0C9223196",KSNODETYPE_RADIO_TRANSMITTER);
#define KSNODETYPE_RADIO_TRANSMITTER DEFINE_GUIDNAMED(KSNODETYPE_RADIO_TRANSMITTER)

#define STATIC_KSNODETYPE_MULTITRACK_RECORDER				\
	DEFINE_USB_TERMINAL_GUID(0x0712)
DEFINE_GUIDSTRUCT("DFF220F2-F70F-11D0-B917-00A0C9223196",KSNODETYPE_MULTITRACK_RECORDER);
#define KSNODETYPE_MULTITRACK_RECORDER DEFINE_GUIDNAMED(KSNODETYPE_MULTITRACK_RECORDER)

#define STATIC_KSNODETYPE_SYNTHESIZER					\
	DEFINE_USB_TERMINAL_GUID(0x0713)
DEFINE_GUIDSTRUCT("DFF220F3-F70F-11D0-B917-00A0C9223196",KSNODETYPE_SYNTHESIZER);
#define KSNODETYPE_SYNTHESIZER DEFINE_GUIDNAMED(KSNODETYPE_SYNTHESIZER)

#if NTDDI_VERSION < NTDDI_VISTA
#define STATIC_KSNODETYPE_SWSYNTH					\
	0x423274A0,0x8B81,0x11D1,0xA0,0x50,0x00,0x00,0xF8,0x00,0x47,0x88
DEFINE_GUIDSTRUCT("423274A0-8B81-11D1-A050-0000F8004788",KSNODETYPE_SWSYNTH);
#define KSNODETYPE_SWSYNTH DEFINE_GUIDNAMED(KSNODETYPE_SWSYNTH)

#define STATIC_KSNODETYPE_SWMIDI					\
	0xCB9BEFA0,0xA251,0x11D1,0xA0,0x50,0x00,0x00,0xF8,0x00,0x47,0x88
DEFINE_GUIDSTRUCT("CB9BEFA0-A251-11D1-A050-0000F8004788",KSNODETYPE_SWMIDI);
#define KSNODETYPE_SWMIDI DEFINE_GUIDNAMED(KSNODETYPE_SWMIDI)
#endif  /* NTDDI_VERSION < NTDDI_VISTA */

#define STATIC_KSNODETYPE_DRM_DESCRAMBLE				\
	0xFFBB6E3F,0xCCFE,0x4D84,0x90,0xD9,0x42,0x14,0x18,0xB0,0x3A,0x8E
DEFINE_GUIDSTRUCT("FFBB6E3F-CCFE-4D84-90D9-421418B03A8E",KSNODETYPE_DRM_DESCRAMBLE);
#define KSNODETYPE_DRM_DESCRAMBLE DEFINE_GUIDNAMED(KSNODETYPE_DRM_DESCRAMBLE)

#define STATIC_KSCATEGORY_AUDIO						\
	0x6994AD04,0x93EF,0x11D0,0xA3,0xCC,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("6994AD04-93EF-11D0-A3CC-00A0C9223196",KSCATEGORY_AUDIO);
#define KSCATEGORY_AUDIO DEFINE_GUIDNAMED(KSCATEGORY_AUDIO)

#define STATIC_KSCATEGORY_VIDEO						\
	0x6994AD05,0x93EF,0x11D0,0xA3,0xCC,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("6994AD05-93EF-11D0-A3CC-00A0C9223196",KSCATEGORY_VIDEO);
#define KSCATEGORY_VIDEO DEFINE_GUIDNAMED(KSCATEGORY_VIDEO)

#define STATIC_KSCATEGORY_TEXT						\
	0x6994AD06,0x93EF,0x11D0,0xA3,0xCC,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("6994AD06-93EF-11D0-A3CC-00A0C9223196",KSCATEGORY_TEXT);
#define KSCATEGORY_TEXT DEFINE_GUIDNAMED(KSCATEGORY_TEXT)

#if NTDDI_VERSION >= NTDDI_VISTA
#define STATIC_KSCATEGORY_REALTIME \
	0xEB115FFCL,0x10C8,0x4964,0x83,0x1D,0x6D,0xCB,0x02,0xE6,0xF2,0x3F
DEFINE_GUIDSTRUCT("EB115FFC-10C8-4964-831D-6DCB02E6F23F",KSCATEGORY_REALTIME);
#define KSCATEGORY_REALTIME DEFINE_GUIDNAMED(KSCATEGORY_REALTIME)
#endif  /* NTDDI_VERSION >= NTDDI_VISTA */

#define STATIC_KSCATEGORY_NETWORK					\
	0x67C9CC3C,0x69C4,0x11D2,0x87,0x59,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("67C9CC3C-69C4-11D2-8759-00A0C9223196",KSCATEGORY_NETWORK);
#define KSCATEGORY_NETWORK DEFINE_GUIDNAMED(KSCATEGORY_NETWORK)

#define STATIC_KSCATEGORY_TOPOLOGY					\
	0xDDA54A40,0x1E4C,0x11D1,0xA0,0x50,0x40,0x57,0x05,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("DDA54A40-1E4C-11D1-A050-405705C10000",KSCATEGORY_TOPOLOGY);
#define KSCATEGORY_TOPOLOGY DEFINE_GUIDNAMED(KSCATEGORY_TOPOLOGY)

#define STATIC_KSCATEGORY_VIRTUAL					\
	0x3503EAC4,0x1F26,0x11D1,0x8A,0xB0,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("3503EAC4-1F26-11D1-8AB0-00A0C9223196",KSCATEGORY_VIRTUAL);
#define KSCATEGORY_VIRTUAL DEFINE_GUIDNAMED(KSCATEGORY_VIRTUAL)

#define STATIC_KSCATEGORY_ACOUSTIC_ECHO_CANCEL				\
	0xBF963D80,0xC559,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("BF963D80-C559-11D0-8A2B-00A0C9255AC1",KSCATEGORY_ACOUSTIC_ECHO_CANCEL);
#define KSCATEGORY_ACOUSTIC_ECHO_CANCEL DEFINE_GUIDNAMED(KSCATEGORY_ACOUSTIC_ECHO_CANCEL)

#if NTDDI_VERSION < NTDDI_VISTA
#define STATIC_KSCATEGORY_SYSAUDIO					\
	0xA7C7A5B1,0x5AF3,0x11D1,0x9C,0xED,0x00,0xA0,0x24,0xBF,0x04,0x07
DEFINE_GUIDSTRUCT("A7C7A5B1-5AF3-11D1-9CED-00A024BF0407",KSCATEGORY_SYSAUDIO);
#define KSCATEGORY_SYSAUDIO DEFINE_GUIDNAMED(KSCATEGORY_SYSAUDIO)

#define STATIC_KSCATEGORY_WDMAUD					\
	0x3E227E76,0x690D,0x11D2,0x81,0x61,0x00,0x00,0xF8,0x77,0x5B,0xF1
DEFINE_GUIDSTRUCT("3E227E76-690D-11D2-8161-0000F8775BF1",KSCATEGORY_WDMAUD);
#define KSCATEGORY_WDMAUD DEFINE_GUIDNAMED(KSCATEGORY_WDMAUD)

#define STATIC_KSCATEGORY_AUDIO_GFX					\
	0x9BAF9572,0x340C,0x11D3,0xAB,0xDC,0x00,0xA0,0xC9,0x0A,0xB1,0x6F
DEFINE_GUIDSTRUCT("9BAF9572-340C-11D3-ABDC-00A0C90AB16F",KSCATEGORY_AUDIO_GFX);
#define KSCATEGORY_AUDIO_GFX DEFINE_GUIDNAMED(KSCATEGORY_AUDIO_GFX)

#define STATIC_KSCATEGORY_AUDIO_SPLITTER				\
	0x9EA331FA,0xB91B,0x45F8,0x92,0x85,0xBD,0x2B,0xC7,0x7A,0xFC,0xDE
DEFINE_GUIDSTRUCT("9EA331FA-B91B-45F8-9285-BD2BC77AFCDE",KSCATEGORY_AUDIO_SPLITTER);
#define KSCATEGORY_AUDIO_SPLITTER DEFINE_GUIDNAMED(KSCATEGORY_AUDIO_SPLITTER)
#endif /* NTDDI_VERSION < NTDDI_VISTA */

#define STATIC_KSCATEGORY_SYNTHESIZER		STATIC_KSNODETYPE_SYNTHESIZER
#define KSCATEGORY_SYNTHESIZER			KSNODETYPE_SYNTHESIZER

#define STATIC_KSCATEGORY_DRM_DESCRAMBLE	STATIC_KSNODETYPE_DRM_DESCRAMBLE
#define KSCATEGORY_DRM_DESCRAMBLE		KSNODETYPE_DRM_DESCRAMBLE

#if NTDDI_VERSION < NTDDI_VISTA
#define STATIC_KSCATEGORY_AUDIO_DEVICE					\
	0xFBF6F530,0x07B9,0x11D2,0xA7,0x1E,0x00,0x00,0xF8,0x00,0x47,0x88
DEFINE_GUIDSTRUCT("FBF6F530-07B9-11D2-A71E-0000F8004788",KSCATEGORY_AUDIO_DEVICE);
#define KSCATEGORY_AUDIO_DEVICE DEFINE_GUIDNAMED(KSCATEGORY_AUDIO_DEVICE)

#define STATIC_KSCATEGORY_PREFERRED_WAVEOUT_DEVICE			\
	0xD6C5066E,0x72C1,0x11D2,0x97,0x55,0x00,0x00,0xF8,0x00,0x47,0x88
DEFINE_GUIDSTRUCT("D6C5066E-72C1-11D2-9755-0000F8004788",KSCATEGORY_PREFERRED_WAVEOUT_DEVICE);
#define KSCATEGORY_PREFERRED_WAVEOUT_DEVICE DEFINE_GUIDNAMED(KSCATEGORY_PREFERRED_WAVEOUT_DEVICE)

#define STATIC_KSCATEGORY_PREFERRED_WAVEIN_DEVICE			\
	0xD6C50671,0x72C1,0x11D2,0x97,0x55,0x00,0x00,0xF8,0x00,0x47,0x88
DEFINE_GUIDSTRUCT("D6C50671-72C1-11D2-9755-0000F8004788",KSCATEGORY_PREFERRED_WAVEIN_DEVICE);
#define KSCATEGORY_PREFERRED_WAVEIN_DEVICE DEFINE_GUIDNAMED(KSCATEGORY_PREFERRED_WAVEIN_DEVICE)

#define STATIC_KSCATEGORY_PREFERRED_MIDIOUT_DEVICE			\
	0xD6C50674,0x72C1,0x11D2,0x97,0x55,0x00,0x00,0xF8,0x00,0x47,0x88
DEFINE_GUIDSTRUCT("D6C50674-72C1-11D2-9755-0000F8004788",KSCATEGORY_PREFERRED_MIDIOUT_DEVICE);
#define KSCATEGORY_PREFERRED_MIDIOUT_DEVICE DEFINE_GUIDNAMED(KSCATEGORY_PREFERRED_MIDIOUT_DEVICE)
#endif  /* NTDDI_VERSION < NTDDI_VISTA */

#define STATIC_KSCATEGORY_WDMAUD_USE_PIN_NAME				\
	0x47A4FA20,0xA251,0x11D1,0xA0,0x50,0x00,0x00,0xF8,0x00,0x47,0x88
DEFINE_GUIDSTRUCT("47A4FA20-A251-11D1-A050-0000F8004788",KSCATEGORY_WDMAUD_USE_PIN_NAME);
#define KSCATEGORY_WDMAUD_USE_PIN_NAME DEFINE_GUIDNAMED(KSCATEGORY_WDMAUD_USE_PIN_NAME)

#define STATIC_KSCATEGORY_ESCALANTE_PLATFORM_DRIVER			\
	0x74f3aea8,0x9768,0x11d1,0x8e,0x07,0x00,0xa0,0xc9,0x5e,0xc2,0x2e
DEFINE_GUIDSTRUCT("74f3aea8-9768-11d1-8e07-00a0c95ec22e",KSCATEGORY_ESCALANTE_PLATFORM_DRIVER);
#define KSCATEGORY_ESCALANTE_PLATFORM_DRIVER DEFINE_GUIDNAMED(KSCATEGORY_ESCALANTE_PLATFORM_DRIVER)

#define STATIC_KSDATAFORMAT_TYPE_VIDEO					\
	0x73646976,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71
DEFINE_GUIDSTRUCT("*************-0010-8000-00aa00389b71",KSDATAFORMAT_TYPE_VIDEO);
#define KSDATAFORMAT_TYPE_VIDEO DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_VIDEO)

#define STATIC_KSDATAFORMAT_TYPE_AUDIO					\
	0x73647561,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71
DEFINE_GUIDSTRUCT("*************-0010-8000-00aa00389b71",KSDATAFORMAT_TYPE_AUDIO);
#define KSDATAFORMAT_TYPE_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_AUDIO)

#define STATIC_KSDATAFORMAT_TYPE_TEXT					\
	0x73747874,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71
DEFINE_GUIDSTRUCT("*************-0010-8000-00aa00389b71",KSDATAFORMAT_TYPE_TEXT);
#define KSDATAFORMAT_TYPE_TEXT DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_TEXT)

#if !defined(DEFINE_WAVEFORMATEX_GUID)
#define DEFINE_WAVEFORMATEX_GUID(x)					\
	(USHORT)(x),0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71
#endif

#define STATIC_KSDATAFORMAT_SUBTYPE_WAVEFORMATEX			\
	0x00000000,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71
DEFINE_GUIDSTRUCT("00000000-0000-0010-8000-00aa00389b71",KSDATAFORMAT_SUBTYPE_WAVEFORMATEX);
#define KSDATAFORMAT_SUBTYPE_WAVEFORMATEX DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_WAVEFORMATEX)

#define INIT_WAVEFORMATEX_GUID(Guid,x)					\
{									\
	*(Guid) = KSDATAFORMAT_SUBTYPE_WAVEFORMATEX;			\
	(Guid)->Data1 = (USHORT)(x);					\
}

#define EXTRACT_WAVEFORMATEX_ID(Guid)					\
	(USHORT)((Guid)->Data1)

#define IS_VALID_WAVEFORMATEX_GUID(Guid)				\
	(!memcmp(((PUSHORT)&KSDATAFORMAT_SUBTYPE_WAVEFORMATEX) + 1, ((PUSHORT)(Guid)) + 1,sizeof(GUID) - sizeof(USHORT)))

#ifndef INIT_MMREG_MID
#define INIT_MMREG_MID(guid,id)						\
{									\
	(guid)->Data1 = 0xd5a47fa7 + (USHORT)(id);			\
	(guid)->Data2 = 0x6d98;						\
	(guid)->Data3 = 0x11d1;						\
	(guid)->Data4[0] = 0xa2;					\
	(guid)->Data4[1] = 0x1a;					\
	(guid)->Data4[2] = 0x00;					\
	(guid)->Data4[3] = 0xa0;					\
	(guid)->Data4[4] = 0xc9;					\
	(guid)->Data4[5] = 0x22;					\
	(guid)->Data4[6] = 0x31;					\
	(guid)->Data4[7] = 0x96;					\
}
#define EXTRACT_MMREG_MID(guid)						\
	(USHORT)((guid)->Data1 - 0xd5a47fa7)
#define DEFINE_MMREG_MID_GUID(id)					\
	0xd5a47fa7+(USHORT)(id),0x6d98,0x11d1,0xa2,0x1a,0x00,0xa0,0xc9,0x22,0x31,0x96

#define IS_COMPATIBLE_MMREG_MID(guid)					\
	(((guid)->Data1 >= 0xd5a47fa7) &&				\
	 ((guid)->Data1 < 0xd5a47fa7 + 0xffff) &&			\
	 ((guid)->Data2 == 0x6d98) &&					\
	 ((guid)->Data3 == 0x11d1) &&					\
	 ((guid)->Data4[0] == 0xa2) &&					\
	 ((guid)->Data4[1] == 0x1a) &&					\
	 ((guid)->Data4[2] == 0x00) &&					\
	 ((guid)->Data4[3] == 0xa0) &&					\
	 ((guid)->Data4[4] == 0xc9) &&					\
	 ((guid)->Data4[5] == 0x22) &&					\
	 ((guid)->Data4[6] == 0x31) &&					\
	 ((guid)->Data4[7] == 0x96) )
#endif /* INIT_MMREG_MID */

#ifndef INIT_MMREG_PID
#define INIT_MMREG_PID(guid,id)						\
{									\
	(guid)->Data1 = 0xe36dc2ac + (USHORT)(id);			\
	(guid)->Data2 = 0x6d9a;						\
	(guid)->Data3 = 0x11d1;						\
	(guid)->Data4[0] = 0xa2;					\
	(guid)->Data4[1] = 0x1a;					\
	(guid)->Data4[2] = 0x00;					\
	(guid)->Data4[3] = 0xa0;					\
	(guid)->Data4[4] = 0xc9;					\
	(guid)->Data4[5] = 0x22;					\
	(guid)->Data4[6] = 0x31;					\
	(guid)->Data4[7] = 0x96;					\
}
#define EXTRACT_MMREG_PID(guid)						\
	(USHORT)((guid)->Data1 - 0xe36dc2ac)
#define DEFINE_MMREG_PID_GUID(id)					\
	0xe36dc2ac+(USHORT)(id),0x6d9a,0x11d1,0xa2,0x1a,0x00,0xa0,0xc9,0x22,0x31,0x96

#define IS_COMPATIBLE_MMREG_PID(guid)					\
	(((guid)->Data1 >= 0xe36dc2ac) &&				\
	 ((guid)->Data1 < 0xe36dc2ac + 0xffff) &&			\
	 ((guid)->Data2 == 0x6d9a) &&					\
	 ((guid)->Data3 == 0x11d1) &&					\
	 ((guid)->Data4[0] == 0xa2) &&					\
	 ((guid)->Data4[1] == 0x1a) &&					\
	 ((guid)->Data4[2] == 0x00) &&					\
	 ((guid)->Data4[3] == 0xa0) &&					\
	 ((guid)->Data4[4] == 0xc9) &&					\
	 ((guid)->Data4[5] == 0x22) &&					\
	 ((guid)->Data4[6] == 0x31) &&					\
	 ((guid)->Data4[7] == 0x96) )
#endif /* INIT_MMREG_PID */

#define STATIC_KSDATAFORMAT_SUBTYPE_ANALOG				\
	0x6dba3190,0x67bd,0x11cf,0xa0,0xf7,0x00,0x20,0xaf,0xd1,0x56,0xe4
DEFINE_GUIDSTRUCT("6dba3190-67bd-11cf-a0f7-0020afd156e4",KSDATAFORMAT_SUBTYPE_ANALOG);
#define KSDATAFORMAT_SUBTYPE_ANALOG DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_ANALOG)

#define STATIC_KSDATAFORMAT_SUBTYPE_PCM					\
	DEFINE_WAVEFORMATEX_GUID(WAVE_FORMAT_PCM)
DEFINE_GUIDSTRUCT("00000001-0000-0010-8000-00aa00389b71",KSDATAFORMAT_SUBTYPE_PCM);
#define KSDATAFORMAT_SUBTYPE_PCM DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_PCM)

#ifdef _INC_MMREG
#define STATIC_KSDATAFORMAT_SUBTYPE_IEEE_FLOAT				\
	DEFINE_WAVEFORMATEX_GUID(WAVE_FORMAT_IEEE_FLOAT)
DEFINE_GUIDSTRUCT("00000003-0000-0010-8000-00aa00389b71",KSDATAFORMAT_SUBTYPE_IEEE_FLOAT);
#define KSDATAFORMAT_SUBTYPE_IEEE_FLOAT DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_IEEE_FLOAT)

#define STATIC_KSDATAFORMAT_SUBTYPE_DRM					\
	DEFINE_WAVEFORMATEX_GUID(WAVE_FORMAT_DRM)
DEFINE_GUIDSTRUCT("00000009-0000-0010-8000-00aa00389b71",KSDATAFORMAT_SUBTYPE_DRM);
#define KSDATAFORMAT_SUBTYPE_DRM DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_DRM)

#define STATIC_KSDATAFORMAT_SUBTYPE_ALAW				\
	DEFINE_WAVEFORMATEX_GUID(WAVE_FORMAT_ALAW)
DEFINE_GUIDSTRUCT("00000006-0000-0010-8000-00aa00389b71",KSDATAFORMAT_SUBTYPE_ALAW);
#define KSDATAFORMAT_SUBTYPE_ALAW DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_ALAW)

#define STATIC_KSDATAFORMAT_SUBTYPE_MULAW				\
	DEFINE_WAVEFORMATEX_GUID(WAVE_FORMAT_MULAW)
DEFINE_GUIDSTRUCT("00000007-0000-0010-8000-00aa00389b71",KSDATAFORMAT_SUBTYPE_MULAW);
#define KSDATAFORMAT_SUBTYPE_MULAW DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_MULAW)

#define STATIC_KSDATAFORMAT_SUBTYPE_ADPCM				\
	DEFINE_WAVEFORMATEX_GUID(WAVE_FORMAT_ADPCM)
DEFINE_GUIDSTRUCT("00000002-0000-0010-8000-00aa00389b71",KSDATAFORMAT_SUBTYPE_ADPCM);
#define KSDATAFORMAT_SUBTYPE_ADPCM DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_ADPCM)

#define STATIC_KSDATAFORMAT_SUBTYPE_MPEG				\
	DEFINE_WAVEFORMATEX_GUID(WAVE_FORMAT_MPEG)
DEFINE_GUIDSTRUCT("00000050-0000-0010-8000-00aa00389b71",KSDATAFORMAT_SUBTYPE_MPEG);
#define KSDATAFORMAT_SUBTYPE_MPEG DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_MPEG)
#endif /* _INC_MMREG */

#define STATIC_KSDATAFORMAT_SUBTYPE_IEC61937_DOLBY_DIGITAL	\
	DEFINE_WAVEFORMATEX_GUID(WAVE_FORMAT_DOLBY_AC3_SPDIF)
DEFINE_GUIDSTRUCT("00000092-0000-0010-8000-00aa00389b71",KSDATAFORMAT_SUBTYPE_IEC61937_DOLBY_DIGITAL);
#define KSDATAFORMAT_SUBTYPE_IEC61937_DOLBY_DIGITAL DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_IEC61937_DOLBY_DIGITAL)

#define STATIC_KSDATAFORMAT_SUBTYPE_IEC61937_DTS	\
	DEFINE_WAVEFORMATEX_GUID(WAVE_FORMAT_DTS)
DEFINE_GUIDSTRUCT("00000008-0000-0010-8000-00aa00389b71",KSDATAFORMAT_SUBTYPE_IEC61937_DTS);
#define KSDATAFORMAT_SUBTYPE_IEC61937_DTS DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_IEC61937_DTS)

#define STATIC_KSDATAFORMAT_SUBTYPE_IEC61937_DTS_HD	\
	0x0000000b,0x0cea,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71
DEFINE_GUIDSTRUCT("0000000b-0cea-0010-8000-00aa00389b71",KSDATAFORMAT_SUBTYPE_IEC61937_DTS_HD);
#define KSDATAFORMAT_SUBTYPE_IEC61937_DTS_HD DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_IEC61937_DTS_HD)

#define STATIC_KSDATAFORMAT_SUBTYPE_IEC61937_DOLBY_DIGITAL_PLUS	\
	0x0000000a,0x0cea,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71
DEFINE_GUIDSTRUCT("0000000a-0cea-0010-8000-00aa00389b71",KSDATAFORMAT_SUBTYPE_IEC61937_DOLBY_DIGITAL_PLUS);
#define KSDATAFORMAT_SUBTYPE_IEC61937_DOLBY_DIGITAL_PLUS DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_IEC61937_DOLBY_DIGITAL_PLUS)

#define STATIC_KSDATAFORMAT_SUBTYPE_IEC61937_DOLBY_MLP	\
	0x0000000c,0x0cea,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71
DEFINE_GUIDSTRUCT("0000000c-0cea-0010-8000-00aa00389b71",KSDATAFORMAT_SUBTYPE_IEC61937_DOLBY_MLP);
#define KSDATAFORMAT_SUBTYPE_IEC61937_DOLBY_MLP DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_IEC61937_DOLBY_MLP)

#define STATIC_KSDATAFORMAT_SPECIFIER_VC_ID				\
	0xAD98D184,0xAAC3,0x11D0,0xA4,0x1C,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("AD98D184-AAC3-11D0-A41C-00A0C9223196",KSDATAFORMAT_SPECIFIER_VC_ID);
#define KSDATAFORMAT_SPECIFIER_VC_ID DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_VC_ID)

#define STATIC_KSDATAFORMAT_SPECIFIER_WAVEFORMATEX			\
	0x05589f81,0xc356,0x11ce,0xbf,0x01,0x00,0xaa,0x00,0x55,0x59,0x5a
DEFINE_GUIDSTRUCT("05589f81-c356-11ce-bf01-00aa0055595a",KSDATAFORMAT_SPECIFIER_WAVEFORMATEX);
#define KSDATAFORMAT_SPECIFIER_WAVEFORMATEX DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_WAVEFORMATEX)

#define STATIC_KSDATAFORMAT_SPECIFIER_DSOUND				\
	0x518590a2,0xa184,0x11d0,0x85,0x22,0x00,0xc0,0x4f,0xd9,0xba,0xf3
DEFINE_GUIDSTRUCT("518590a2-a184-11d0-8522-00c04fd9baf3",KSDATAFORMAT_SPECIFIER_DSOUND);
#define KSDATAFORMAT_SPECIFIER_DSOUND DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_DSOUND)

#if defined(_INC_MMSYSTEM) || defined(_INC_MMREG)
#if !defined(PACK_PRAGMAS_NOT_SUPPORTED)
#include <pshpack1.h>
#endif
typedef struct {
  KSDATAFORMAT DataFormat;
  WAVEFORMATEX WaveFormatEx;
} KSDATAFORMAT_WAVEFORMATEX,*PKSDATAFORMAT_WAVEFORMATEX;

#ifndef _WAVEFORMATEXTENSIBLE_
#define _WAVEFORMATEXTENSIBLE_
typedef struct {
  WAVEFORMATEX Format;
  union {
    WORD wValidBitsPerSample;
    WORD wSamplesPerBlock;
    WORD wReserved;
  } Samples;
  DWORD dwChannelMask;

  GUID SubFormat;
} WAVEFORMATEXTENSIBLE,*PWAVEFORMATEXTENSIBLE;
#endif /* _WAVEFORMATEXTENSIBLE_ */

#if !defined(WAVE_FORMAT_EXTENSIBLE)
#define WAVE_FORMAT_EXTENSIBLE			0xFFFE
#endif

#ifndef _WAVEFORMATEXTENSIBLE_IEC61937_
#define _WAVEFORMATEXTENSIBLE_IEC61937_
typedef struct {
    WAVEFORMATEXTENSIBLE    FormatExt;
    DWORD                   dwEncodedSamplesPerSec;
    DWORD                   dwEncodedChannelCount;
    DWORD                   dwAverageBytesPerSec;
} WAVEFORMATEXTENSIBLE_IEC61937, *PWAVEFORMATEXTENSIBLE_IEC61937;
#endif /* _WAVEFORMATEXTENSIBLE_IEC61937_ */

typedef struct {
  ULONG Flags;
  ULONG Control;
  WAVEFORMATEX WaveFormatEx;
} KSDSOUND_BUFFERDESC,*PKSDSOUND_BUFFERDESC;

typedef struct {
  KSDATAFORMAT DataFormat;
  KSDSOUND_BUFFERDESC BufferDesc;
} KSDATAFORMAT_DSOUND,*PKSDATAFORMAT_DSOUND;

#if !defined(PACK_PRAGMAS_NOT_SUPPORTED)
#include <poppack.h>
#endif
#endif /* defined(_INC_MMSYSTEM) || defined(_INC_MMREG) */

#define KSDSOUND_BUFFER_PRIMARY			0x00000001
#define KSDSOUND_BUFFER_STATIC			0x00000002
#define KSDSOUND_BUFFER_LOCHARDWARE		0x00000004
#define KSDSOUND_BUFFER_LOCSOFTWARE		0x00000008

#define KSDSOUND_BUFFER_CTRL_3D			0x00000001
#define KSDSOUND_BUFFER_CTRL_FREQUENCY		0x00000002
#define KSDSOUND_BUFFER_CTRL_PAN		0x00000004
#define KSDSOUND_BUFFER_CTRL_VOLUME		0x00000008
#define KSDSOUND_BUFFER_CTRL_POSITIONNOTIFY	0x00000010

typedef struct {
  DWORDLONG PlayOffset;
  DWORDLONG WriteOffset;
} KSAUDIO_POSITION,*PKSAUDIO_POSITION;

typedef struct _DS3DVECTOR {
  __C89_NAMELESS union {
    FLOAT x;
    FLOAT dvX;
  };
  __C89_NAMELESS union {
    FLOAT y;
    FLOAT dvY;
  };
  __C89_NAMELESS union {
    FLOAT z;
    FLOAT dvZ;
  };
} DS3DVECTOR,*PDS3DVECTOR;

#define STATIC_KSPROPSETID_DirectSound3DListener			\
	0x437b3414,0xd060,0x11d0,0x85,0x83,0x00,0xc0,0x4f,0xd9,0xba,0xf3
DEFINE_GUIDSTRUCT("437b3414-d060-11d0-8583-00c04fd9baf3",KSPROPSETID_DirectSound3DListener);
#define KSPROPSETID_DirectSound3DListener DEFINE_GUIDNAMED(KSPROPSETID_DirectSound3DListener)

typedef enum {
  KSPROPERTY_DIRECTSOUND3DLISTENER_ALL,
  KSPROPERTY_DIRECTSOUND3DLISTENER_POSITION,
  KSPROPERTY_DIRECTSOUND3DLISTENER_VELOCITY,
  KSPROPERTY_DIRECTSOUND3DLISTENER_ORIENTATION,
  KSPROPERTY_DIRECTSOUND3DLISTENER_DISTANCEFACTOR,
  KSPROPERTY_DIRECTSOUND3DLISTENER_ROLLOFFFACTOR,
  KSPROPERTY_DIRECTSOUND3DLISTENER_DOPPLERFACTOR,
  KSPROPERTY_DIRECTSOUND3DLISTENER_BATCH,
  KSPROPERTY_DIRECTSOUND3DLISTENER_ALLOCATION
} KSPROPERTY_DIRECTSOUND3DLISTENER;

typedef struct {
  DS3DVECTOR Position;
  DS3DVECTOR Velocity;
  DS3DVECTOR OrientFront;
  DS3DVECTOR OrientTop;
  FLOAT DistanceFactor;
  FLOAT RolloffFactor;
  FLOAT DopplerFactor;
} KSDS3D_LISTENER_ALL,*PKSDS3D_LISTENER_ALL;

typedef struct {
  DS3DVECTOR Front;
  DS3DVECTOR Top;
} KSDS3D_LISTENER_ORIENTATION,*PKSDS3D_LISTENER_ORIENTATION;

#define STATIC_KSPROPSETID_DirectSound3DBuffer				\
	0x437b3411,0xd060,0x11d0,0x85,0x83,0x00,0xc0,0x4f,0xd9,0xba,0xf3
DEFINE_GUIDSTRUCT("437b3411-d060-11d0-8583-00c04fd9baf3",KSPROPSETID_DirectSound3DBuffer);
#define KSPROPSETID_DirectSound3DBuffer DEFINE_GUIDNAMED(KSPROPSETID_DirectSound3DBuffer)

typedef enum {
  KSPROPERTY_DIRECTSOUND3DBUFFER_ALL,
  KSPROPERTY_DIRECTSOUND3DBUFFER_POSITION,
  KSPROPERTY_DIRECTSOUND3DBUFFER_VELOCITY,
  KSPROPERTY_DIRECTSOUND3DBUFFER_CONEANGLES,
  KSPROPERTY_DIRECTSOUND3DBUFFER_CONEORIENTATION,
  KSPROPERTY_DIRECTSOUND3DBUFFER_CONEOUTSIDEVOLUME,
  KSPROPERTY_DIRECTSOUND3DBUFFER_MINDISTANCE,
  KSPROPERTY_DIRECTSOUND3DBUFFER_MAXDISTANCE,
  KSPROPERTY_DIRECTSOUND3DBUFFER_MODE
} KSPROPERTY_DIRECTSOUND3DBUFFER;

typedef struct {
  DS3DVECTOR Position;
  DS3DVECTOR Velocity;
  ULONG InsideConeAngle;
  ULONG OutsideConeAngle;
  DS3DVECTOR ConeOrientation;
  LONG ConeOutsideVolume;
  FLOAT MinDistance;
  FLOAT MaxDistance;
  ULONG Mode;
} KSDS3D_BUFFER_ALL,*PKSDS3D_BUFFER_ALL;

typedef struct {
  ULONG InsideConeAngle;
  ULONG OutsideConeAngle;
} KSDS3D_BUFFER_CONE_ANGLES,*PKSDS3D_BUFFER_CONE_ANGLES;

#define KSAUDIO_STEREO_SPEAKER_GEOMETRY_HEADPHONE	(-1)
#define KSAUDIO_STEREO_SPEAKER_GEOMETRY_MIN		5
#define KSAUDIO_STEREO_SPEAKER_GEOMETRY_NARROW		10
#define KSAUDIO_STEREO_SPEAKER_GEOMETRY_WIDE		20
#define KSAUDIO_STEREO_SPEAKER_GEOMETRY_MAX		180

#define KSDSOUND_3D_MODE_NORMAL			0x00000000
#define KSDSOUND_3D_MODE_HEADRELATIVE		0x00000001
#define KSDSOUND_3D_MODE_DISABLE		0x00000002

#define KSDSOUND_BUFFER_CTRL_HRTF_3D		0x40000000

typedef struct {
  ULONG Size;
  ULONG Enabled;
  WINBOOL SwapChannels;
  WINBOOL ZeroAzimuth;
  WINBOOL CrossFadeOutput;
  ULONG FilterSize;
} KSDS3D_HRTF_PARAMS_MSG,*PKSDS3D_HRTF_PARAMS_MSG;

typedef enum {
  FULL_FILTER,
  LIGHT_FILTER,
  KSDS3D_FILTER_QUALITY_COUNT
} KSDS3D_HRTF_FILTER_QUALITY;

typedef struct {
  ULONG Size;
  KSDS3D_HRTF_FILTER_QUALITY Quality;
  FLOAT SampleRate;
  ULONG MaxFilterSize;
  ULONG FilterTransientMuteLength;
  ULONG FilterOverlapBufferLength;
  ULONG OutputOverlapBufferLength;
  ULONG Reserved;
} KSDS3D_HRTF_INIT_MSG,*PKSDS3D_HRTF_INIT_MSG;

typedef enum {
  FLOAT_COEFF,
  SHORT_COEFF,
  KSDS3D_COEFF_COUNT
} KSDS3D_HRTF_COEFF_FORMAT;

typedef enum {
  DIRECT_FORM,
  CASCADE_FORM,
  KSDS3D_FILTER_METHOD_COUNT
} KSDS3D_HRTF_FILTER_METHOD;

typedef enum {
  DS3D_HRTF_VERSION_1
} KSDS3D_HRTF_FILTER_VERSION;

typedef struct {
  KSDS3D_HRTF_FILTER_METHOD FilterMethod;
  KSDS3D_HRTF_COEFF_FORMAT CoeffFormat;
  KSDS3D_HRTF_FILTER_VERSION Version;
  ULONG Reserved;
} KSDS3D_HRTF_FILTER_FORMAT_MSG,*PKSDS3D_HRTF_FILTER_FORMAT_MSG;

#define STATIC_KSPROPSETID_Hrtf3d					\
	0xb66decb0,0xa083,0x11d0,0x85,0x1e,0x00,0xc0,0x4f,0xd9,0xba,0xf3
DEFINE_GUIDSTRUCT("b66decb0-a083-11d0-851e-00c04fd9baf3",KSPROPSETID_Hrtf3d);
#define KSPROPSETID_Hrtf3d DEFINE_GUIDNAMED(KSPROPSETID_Hrtf3d)

typedef enum {
  KSPROPERTY_HRTF3D_PARAMS = 0,
  KSPROPERTY_HRTF3D_INITIALIZE,
  KSPROPERTY_HRTF3D_FILTER_FORMAT
} KSPROPERTY_HRTF3D;

typedef struct {
  LONG Channel;
  FLOAT VolSmoothScale;
  FLOAT TotalDryAttenuation;
  FLOAT TotalWetAttenuation;
  LONG SmoothFrequency;
  LONG Delay;
} KSDS3D_ITD_PARAMS,*PKSDS3D_ITD_PARAMS;

typedef struct {
  ULONG Enabled;
  KSDS3D_ITD_PARAMS LeftParams;
  KSDS3D_ITD_PARAMS RightParams;
  ULONG Reserved;
} KSDS3D_ITD_PARAMS_MSG,*PKSDS3D_ITD_PARAMS_MSG;

#define STATIC_KSPROPSETID_Itd3d					\
	0x6429f090,0x9fd9,0x11d0,0xa7,0x5b,0x00,0xa0,0xc9,0x03,0x65,0xe3
DEFINE_GUIDSTRUCT("6429f090-9fd9-11d0-a75b-00a0c90365e3",KSPROPSETID_Itd3d);
#define KSPROPSETID_Itd3d DEFINE_GUIDNAMED(KSPROPSETID_Itd3d)

typedef enum {
  KSPROPERTY_ITD3D_PARAMS = 0
} KSPROPERTY_ITD3D;

typedef struct {
  KSDATARANGE DataRange;
  ULONG MaximumChannels;
  ULONG MinimumBitsPerSample;
  ULONG MaximumBitsPerSample;
  ULONG MinimumSampleFrequency;
  ULONG MaximumSampleFrequency;
} KSDATARANGE_AUDIO,*PKSDATARANGE_AUDIO;

#define STATIC_KSDATAFORMAT_SUBTYPE_RIFF				\
	0x4995DAEE,0x9EE6,0x11D0,0xA4,0x0E,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("4995DAEE-9EE6-11D0-A40E-00A0C9223196",KSDATAFORMAT_SUBTYPE_RIFF);
#define KSDATAFORMAT_SUBTYPE_RIFF DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_RIFF)

#define STATIC_KSDATAFORMAT_SUBTYPE_RIFFWAVE				\
	0xe436eb8b,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70
DEFINE_GUIDSTRUCT("e436eb8b-524f-11ce-9f53-0020af0ba770",KSDATAFORMAT_SUBTYPE_RIFFWAVE);
#define KSDATAFORMAT_SUBTYPE_RIFFWAVE DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_RIFFWAVE)

#define STATIC_KSPROPSETID_Bibliographic				\
	0x07BA150E,0xE2B1,0x11D0,0xAC,0x17,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("07BA150E-E2B1-11D0-AC17-00A0C9223196",KSPROPSETID_Bibliographic);
#define KSPROPSETID_Bibliographic DEFINE_GUIDNAMED(KSPROPSETID_Bibliographic)

typedef enum {
  KSPROPERTY_BIBLIOGRAPHIC_LEADER = 'RDL ',
  KSPROPERTY_BIBLIOGRAPHIC_LCCN = '010 ',
  KSPROPERTY_BIBLIOGRAPHIC_ISBN = '020 ',
  KSPROPERTY_BIBLIOGRAPHIC_ISSN = '220 ',
  KSPROPERTY_BIBLIOGRAPHIC_CATALOGINGSOURCE = '040 ',
  KSPROPERTY_BIBLIOGRAPHIC_MAINPERSONALNAME = '001 ',
  KSPROPERTY_BIBLIOGRAPHIC_MAINCORPORATEBODY = '011 ',
  KSPROPERTY_BIBLIOGRAPHIC_MAINMEETINGNAME = '111 ',
  KSPROPERTY_BIBLIOGRAPHIC_MAINUNIFORMTITLE = '031 ',
  KSPROPERTY_BIBLIOGRAPHIC_UNIFORMTITLE = '042 ',
  KSPROPERTY_BIBLIOGRAPHIC_TITLESTATEMENT = '542 ',
  KSPROPERTY_BIBLIOGRAPHIC_VARYINGFORMTITLE = '642 ',
  KSPROPERTY_BIBLIOGRAPHIC_PUBLICATION = '062 ',
  KSPROPERTY_BIBLIOGRAPHIC_PHYSICALDESCRIPTION = '003 ',
  KSPROPERTY_BIBLIOGRAPHIC_ADDEDENTRYTITLE = '044 ',
  KSPROPERTY_BIBLIOGRAPHIC_SERIESSTATEMENT = '094 ',
  KSPROPERTY_BIBLIOGRAPHIC_GENERALNOTE = '005 ',
  KSPROPERTY_BIBLIOGRAPHIC_BIBLIOGRAPHYNOTE = '405 ',
  KSPROPERTY_BIBLIOGRAPHIC_CONTENTSNOTE = '505 ',
  KSPROPERTY_BIBLIOGRAPHIC_CREATIONCREDIT = '805 ',
  KSPROPERTY_BIBLIOGRAPHIC_CITATION = '015 ',
  KSPROPERTY_BIBLIOGRAPHIC_PARTICIPANT = '115 ',
  KSPROPERTY_BIBLIOGRAPHIC_SUMMARY = '025 ',
  KSPROPERTY_BIBLIOGRAPHIC_TARGETAUDIENCE = '125 ',
  KSPROPERTY_BIBLIOGRAPHIC_ADDEDFORMAVAILABLE = '035 ',
  KSPROPERTY_BIBLIOGRAPHIC_SYSTEMDETAILS = '835 ',
  KSPROPERTY_BIBLIOGRAPHIC_AWARDS = '685 ',
  KSPROPERTY_BIBLIOGRAPHIC_ADDEDENTRYPERSONALNAME = '006 ',
  KSPROPERTY_BIBLIOGRAPHIC_ADDEDENTRYTOPICALTERM = '056 ',
  KSPROPERTY_BIBLIOGRAPHIC_ADDEDENTRYGEOGRAPHIC = '156 ',
  KSPROPERTY_BIBLIOGRAPHIC_INDEXTERMGENRE = '556 ',
  KSPROPERTY_BIBLIOGRAPHIC_INDEXTERMCURRICULUM = '856 ',
  KSPROPERTY_BIBLIOGRAPHIC_ADDEDENTRYUNIFORMTITLE = '037 ',
  KSPROPERTY_BIBLIOGRAPHIC_ADDEDENTRYRELATED = '047 ',
  KSPROPERTY_BIBLIOGRAPHIC_SERIESSTATEMENTPERSONALNAME = '008 ',
  KSPROPERTY_BIBLIOGRAPHIC_SERIESSTATEMENTUNIFORMTITLE = '038 '
} KSPROPERTY_BIBLIOGRAPHIC;

#define STATIC_KSPROPSETID_TopologyNode					\
	0x45FFAAA1,0x6E1B,0x11D0,0xBC,0xF2,0x44,0x45,0x53,0x54,0x00,0x00
DEFINE_GUIDSTRUCT("45FFAAA1-6E1B-11D0-BCF2-************",KSPROPSETID_TopologyNode);
#define KSPROPSETID_TopologyNode DEFINE_GUIDNAMED(KSPROPSETID_TopologyNode)

typedef enum {
  KSPROPERTY_TOPOLOGYNODE_ENABLE = 1,
  KSPROPERTY_TOPOLOGYNODE_RESET
} KSPROPERTY_TOPOLOGYNODE;

#define STATIC_KSPROPSETID_RtAudio					\
	0xa855a48c,0x2f78,0x4729,0x90,0x51,0x19,0x68,0x74,0x6b,0x9e,0xef
DEFINE_GUIDSTRUCT("A855A48C-2F78-4729-9051-1968746B9EEF",KSPROPSETID_RtAudio);
#define KSPROPSETID_RtAudio DEFINE_GUIDNAMED(KSPROPSETID_RtAudio)

typedef enum {
  KSPROPERTY_RTAUDIO_GETPOSITIONFUNCTION
} KSPROPERTY_RTAUDIO;

#define STATIC_KSPROPSETID_DrmAudioStream				\
	0x2f2c8ddd,0x4198,0x4fac,0xba,0x29,0x61,0xbb,0x5,0xb7,0xde,0x6
DEFINE_GUIDSTRUCT("2F2C8DDD-4198-4fac-BA29-61BB05B7DE06",KSPROPSETID_DrmAudioStream);
#define KSPROPSETID_DrmAudioStream DEFINE_GUIDNAMED(KSPROPSETID_DrmAudioStream)

typedef enum {
  KSPROPERTY_DRMAUDIOSTREAM_CONTENTID
} KSPROPERTY_DRMAUDIOSTREAM;

#define STATIC_KSPROPSETID_Audio					\
	0x45FFAAA0,0x6E1B,0x11D0,0xBC,0xF2,0x44,0x45,0x53,0x54,0x00,0x00
DEFINE_GUIDSTRUCT("45FFAAA0-6E1B-11D0-BCF2-************",KSPROPSETID_Audio);
#define KSPROPSETID_Audio DEFINE_GUIDNAMED(KSPROPSETID_Audio)

typedef enum {
  KSPROPERTY_AUDIO_LATENCY = 1,
  KSPROPERTY_AUDIO_COPY_PROTECTION,
  KSPROPERTY_AUDIO_CHANNEL_CONFIG,
  KSPROPERTY_AUDIO_VOLUMELEVEL,
  KSPROPERTY_AUDIO_POSITION,
  KSPROPERTY_AUDIO_DYNAMIC_RANGE,
  KSPROPERTY_AUDIO_QUALITY,
  KSPROPERTY_AUDIO_SAMPLING_RATE,
  KSPROPERTY_AUDIO_DYNAMIC_SAMPLING_RATE,
  KSPROPERTY_AUDIO_MIX_LEVEL_TABLE,
  KSPROPERTY_AUDIO_MIX_LEVEL_CAPS,
  KSPROPERTY_AUDIO_MUX_SOURCE,
  KSPROPERTY_AUDIO_MUTE,
  KSPROPERTY_AUDIO_BASS,
  KSPROPERTY_AUDIO_MID,
  KSPROPERTY_AUDIO_TREBLE,
  KSPROPERTY_AUDIO_BASS_BOOST,
  KSPROPERTY_AUDIO_EQ_LEVEL,
  KSPROPERTY_AUDIO_NUM_EQ_BANDS,
  KSPROPERTY_AUDIO_EQ_BANDS,
  KSPROPERTY_AUDIO_AGC,
  KSPROPERTY_AUDIO_DELAY,
  KSPROPERTY_AUDIO_LOUDNESS,
  KSPROPERTY_AUDIO_WIDE_MODE,
  KSPROPERTY_AUDIO_WIDENESS,
  KSPROPERTY_AUDIO_REVERB_LEVEL,
  KSPROPERTY_AUDIO_CHORUS_LEVEL,
  KSPROPERTY_AUDIO_DEV_SPECIFIC,
  KSPROPERTY_AUDIO_DEMUX_DEST,
  KSPROPERTY_AUDIO_STEREO_ENHANCE,
  KSPROPERTY_AUDIO_MANUFACTURE_GUID,
  KSPROPERTY_AUDIO_PRODUCT_GUID,
  KSPROPERTY_AUDIO_CPU_RESOURCES,
  KSPROPERTY_AUDIO_STEREO_SPEAKER_GEOMETRY,
  KSPROPERTY_AUDIO_SURROUND_ENCODE,
  KSPROPERTY_AUDIO_3D_INTERFACE,
  KSPROPERTY_AUDIO_PEAKMETER,
  KSPROPERTY_AUDIO_ALGORITHM_INSTANCE,
  KSPROPERTY_AUDIO_FILTER_STATE,
  KSPROPERTY_AUDIO_PREFERRED_STATUS
} KSPROPERTY_AUDIO;

#define KSAUDIO_QUALITY_WORST			0x0
#define KSAUDIO_QUALITY_PC			0x1
#define KSAUDIO_QUALITY_BASIC			0x2
#define KSAUDIO_QUALITY_ADVANCED		0x3

#define KSAUDIO_CPU_RESOURCES_NOT_HOST_CPU	0x00000000
#define KSAUDIO_CPU_RESOURCES_HOST_CPU		0x7FFFFFFF

typedef struct {
  WINBOOL fCopyrighted;
  WINBOOL fOriginal;
} KSAUDIO_COPY_PROTECTION,*PKSAUDIO_COPY_PROTECTION;

typedef struct {
  LONG ActiveSpeakerPositions;
} KSAUDIO_CHANNEL_CONFIG,*PKSAUDIO_CHANNEL_CONFIG;

#ifndef _SPEAKER_POSITIONS_
#define _SPEAKER_POSITIONS_
#define SPEAKER_FRONT_LEFT		0x1
#define SPEAKER_FRONT_RIGHT		0x2
#define SPEAKER_FRONT_CENTER		0x4
#define SPEAKER_LOW_FREQUENCY		0x8
#define SPEAKER_BACK_LEFT		0x10
#define SPEAKER_BACK_RIGHT		0x20
#define SPEAKER_FRONT_LEFT_OF_CENTER	0x40
#define SPEAKER_FRONT_RIGHT_OF_CENTER	0x80
#define SPEAKER_BACK_CENTER		0x100
#define SPEAKER_SIDE_LEFT		0x200
#define SPEAKER_SIDE_RIGHT		0x400
#define SPEAKER_TOP_CENTER		0x800
#define SPEAKER_TOP_FRONT_LEFT		0x1000
#define SPEAKER_TOP_FRONT_CENTER	0x2000
#define SPEAKER_TOP_FRONT_RIGHT		0x4000
#define SPEAKER_TOP_BACK_LEFT		0x8000
#define SPEAKER_TOP_BACK_CENTER		0x10000
#define SPEAKER_TOP_BACK_RIGHT		0x20000

#define SPEAKER_RESERVED		0x7FFC0000

#define SPEAKER_ALL			0x80000000
#endif  /* _SPEAKER_POSITIONS_ */

#define KSAUDIO_SPEAKER_DIRECTOUT	0
#define KSAUDIO_SPEAKER_MONO		(SPEAKER_FRONT_CENTER)
#define KSAUDIO_SPEAKER_STEREO		(SPEAKER_FRONT_LEFT | SPEAKER_FRONT_RIGHT)
#define KSAUDIO_SPEAKER_QUAD		(SPEAKER_FRONT_LEFT | SPEAKER_FRONT_RIGHT |		\
					 SPEAKER_BACK_LEFT | SPEAKER_BACK_RIGHT)
#define KSAUDIO_SPEAKER_SURROUND	(SPEAKER_FRONT_LEFT | SPEAKER_FRONT_RIGHT |		\
					 SPEAKER_FRONT_CENTER | SPEAKER_BACK_CENTER)
#define KSAUDIO_SPEAKER_5POINT1		(SPEAKER_FRONT_LEFT | SPEAKER_FRONT_RIGHT |		\
					 SPEAKER_FRONT_CENTER | SPEAKER_LOW_FREQUENCY |		\
					 SPEAKER_BACK_LEFT | SPEAKER_BACK_RIGHT)
#define KSAUDIO_SPEAKER_7POINT1		(SPEAKER_FRONT_LEFT | SPEAKER_FRONT_RIGHT |		\
					 SPEAKER_FRONT_CENTER | SPEAKER_LOW_FREQUENCY |		\
					 SPEAKER_BACK_LEFT | SPEAKER_BACK_RIGHT |		\
					 SPEAKER_FRONT_LEFT_OF_CENTER | SPEAKER_FRONT_RIGHT_OF_CENTER)
#define KSAUDIO_SPEAKER_5POINT1_SURROUND (SPEAKER_FRONT_LEFT | SPEAKER_FRONT_RIGHT |		\
					  SPEAKER_FRONT_CENTER | SPEAKER_LOW_FREQUENCY |	\
					  SPEAKER_SIDE_LEFT | SPEAKER_SIDE_RIGHT)
#define KSAUDIO_SPEAKER_7POINT1_SURROUND (SPEAKER_FRONT_LEFT | SPEAKER_FRONT_RIGHT |		\
					  SPEAKER_FRONT_CENTER | SPEAKER_LOW_FREQUENCY |	\
					  SPEAKER_BACK_LEFT | SPEAKER_BACK_RIGHT |		\
					  SPEAKER_SIDE_LEFT | SPEAKER_SIDE_RIGHT)

#define KSAUDIO_SPEAKER_5POINT1_BACK	KSAUDIO_SPEAKER_5POINT1
#define KSAUDIO_SPEAKER_7POINT1_WIDE	KSAUDIO_SPEAKER_7POINT1

#define KSAUDIO_SPEAKER_GROUND_FRONT_LEFT	SPEAKER_FRONT_LEFT
#define KSAUDIO_SPEAKER_GROUND_FRONT_CENTER	SPEAKER_FRONT_CENTER
#define KSAUDIO_SPEAKER_GROUND_FRONT_RIGHT	SPEAKER_FRONT_RIGHT
#define KSAUDIO_SPEAKER_GROUND_REAR_LEFT	SPEAKER_BACK_LEFT
#define KSAUDIO_SPEAKER_GROUND_REAR_RIGHT	SPEAKER_BACK_RIGHT
#define KSAUDIO_SPEAKER_TOP_MIDDLE		SPEAKER_TOP_CENTER
#define KSAUDIO_SPEAKER_SUPER_WOOFER		SPEAKER_LOW_FREQUENCY

typedef struct {
  ULONG QuietCompression;
  ULONG LoudCompression;
} KSAUDIO_DYNAMIC_RANGE,*PKSAUDIO_DYNAMIC_RANGE;

typedef struct {
  WINBOOL Mute;
  LONG Level;
} KSAUDIO_MIXLEVEL,*PKSAUDIO_MIXLEVEL;

typedef struct {
  WINBOOL Mute;
  LONG Minimum;
  LONG Maximum;
  LONG Reset;
} KSAUDIO_MIX_CAPS,*PKSAUDIO_MIX_CAPS;

typedef struct {
  ULONG InputChannels;
  ULONG OutputChannels;
  KSAUDIO_MIX_CAPS Capabilities[1];
} KSAUDIO_MIXCAP_TABLE,*PKSAUDIO_MIXCAP_TABLE;

typedef enum {
  SE_TECH_NONE,
  SE_TECH_ANALOG_DEVICES_PHAT,
  SE_TECH_CREATIVE,
  SE_TECH_NATIONAL_SEMI,
  SE_TECH_YAMAHA_YMERSION,
  SE_TECH_BBE,
  SE_TECH_CRYSTAL_SEMI,
  SE_TECH_QSOUND_QXPANDER,
  SE_TECH_SPATIALIZER,
  SE_TECH_SRS,
  SE_TECH_PLATFORM_TECH,
  SE_TECH_AKM,
  SE_TECH_AUREAL,
  SE_TECH_AZTECH,
  SE_TECH_BINAURA,
  SE_TECH_ESS_TECH,
  SE_TECH_HARMAN_VMAX,
  SE_TECH_NVIDEA,
  SE_TECH_PHILIPS_INCREDIBLE,
  SE_TECH_TEXAS_INST,
  SE_TECH_VLSI_TECH
} SE_TECHNIQUE;

typedef struct {
  SE_TECHNIQUE Technique;
  ULONG Center;
  ULONG Depth;
  ULONG Reserved;
} KSAUDIO_STEREO_ENHANCE,*PKSAUDIO_STEREO_ENHANCE;

#if NTDDI_VERSION < NTDDI_VISTA
typedef enum {
  KSPROPERTY_SYSAUDIO_NORMAL_DEFAULT = 0,
  KSPROPERTY_SYSAUDIO_PLAYBACK_DEFAULT,
  KSPROPERTY_SYSAUDIO_RECORD_DEFAULT,
  KSPROPERTY_SYSAUDIO_MIDI_DEFAULT,
  KSPROPERTY_SYSAUDIO_MIXER_DEFAULT
} KSPROPERTY_SYSAUDIO_DEFAULT_TYPE;

typedef struct {
  WINBOOL Enable;
  KSPROPERTY_SYSAUDIO_DEFAULT_TYPE DeviceType;
  ULONG Flags;
  ULONG Reserved;
} KSAUDIO_PREFERRED_STATUS,*PKSAUDIO_PREFERRED_STATUS;
#endif  /* NTDDI_VERSION < NTDDI_VISTA */

#define STATIC_KSNODETYPE_DAC						\
	0x507AE360,0xC554,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("507AE360-C554-11D0-8A2B-00A0C9255AC1",KSNODETYPE_DAC);
#define KSNODETYPE_DAC DEFINE_GUIDNAMED(KSNODETYPE_DAC)

#define STATIC_KSNODETYPE_ADC						\
	0x4D837FE0,0xC555,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("4D837FE0-C555-11D0-8A2B-00A0C9255AC1",KSNODETYPE_ADC);
#define KSNODETYPE_ADC DEFINE_GUIDNAMED(KSNODETYPE_ADC)

#define STATIC_KSNODETYPE_SRC						\
	0x9DB7B9E0,0xC555,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("9DB7B9E0-C555-11D0-8A2B-00A0C9255AC1",KSNODETYPE_SRC);
#define KSNODETYPE_SRC DEFINE_GUIDNAMED(KSNODETYPE_SRC)

#define STATIC_KSNODETYPE_SUPERMIX					\
	0xE573ADC0,0xC555,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("E573ADC0-C555-11D0-8A2B-00A0C9255AC1",KSNODETYPE_SUPERMIX);
#define KSNODETYPE_SUPERMIX DEFINE_GUIDNAMED(KSNODETYPE_SUPERMIX)

#define STATIC_KSNODETYPE_MUX						\
	0x2CEAF780,0xC556,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("2CEAF780-C556-11D0-8A2B-00A0C9255AC1",KSNODETYPE_MUX);
#define KSNODETYPE_MUX DEFINE_GUIDNAMED(KSNODETYPE_MUX)

#define STATIC_KSNODETYPE_DEMUX						\
	0xC0EB67D4,0xE807,0x11D0,0x95,0x8A,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("C0EB67D4-E807-11D0-958A-00C04FB925D3",KSNODETYPE_DEMUX);
#define KSNODETYPE_DEMUX DEFINE_GUIDNAMED(KSNODETYPE_DEMUX)

#define STATIC_KSNODETYPE_SUM						\
	0xDA441A60,0xC556,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("DA441A60-C556-11D0-8A2B-00A0C9255AC1",KSNODETYPE_SUM);
#define KSNODETYPE_SUM DEFINE_GUIDNAMED(KSNODETYPE_SUM)

#define STATIC_KSNODETYPE_MUTE						\
	0x02B223C0,0xC557,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("02B223C0-C557-11D0-8A2B-00A0C9255AC1",KSNODETYPE_MUTE);
#define KSNODETYPE_MUTE DEFINE_GUIDNAMED(KSNODETYPE_MUTE)

#define STATIC_KSNODETYPE_VOLUME					\
	0x3A5ACC00,0xC557,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("3A5ACC00-C557-11D0-8A2B-00A0C9255AC1",KSNODETYPE_VOLUME);
#define KSNODETYPE_VOLUME DEFINE_GUIDNAMED(KSNODETYPE_VOLUME)

#define STATIC_KSNODETYPE_TONE						\
	0x7607E580,0xC557,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("7607E580-C557-11D0-8A2B-00A0C9255AC1",KSNODETYPE_TONE);
#define KSNODETYPE_TONE DEFINE_GUIDNAMED(KSNODETYPE_TONE)

#define STATIC_KSNODETYPE_EQUALIZER					\
	0x9D41B4A0,0xC557,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("9D41B4A0-C557-11D0-8A2B-00A0C9255AC1",KSNODETYPE_EQUALIZER);
#define KSNODETYPE_EQUALIZER DEFINE_GUIDNAMED(KSNODETYPE_EQUALIZER)

#define STATIC_KSNODETYPE_AGC						\
	0xE88C9BA0,0xC557,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("E88C9BA0-C557-11D0-8A2B-00A0C9255AC1",KSNODETYPE_AGC);
#define KSNODETYPE_AGC DEFINE_GUIDNAMED(KSNODETYPE_AGC)

#define STATIC_KSNODETYPE_NOISE_SUPPRESS				\
	0xe07f903f,0x62fd,0x4e60,0x8c,0xdd,0xde,0xa7,0x23,0x66,0x65,0xb5
DEFINE_GUIDSTRUCT("E07F903F-62FD-4e60-8CDD-DEA7236665B5",KSNODETYPE_NOISE_SUPPRESS);
#define KSNODETYPE_NOISE_SUPPRESS DEFINE_GUIDNAMED(KSNODETYPE_NOISE_SUPPRESS)

#define STATIC_KSNODETYPE_DELAY						\
	0x144981E0,0xC558,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("144981E0-C558-11D0-8A2B-00A0C9255AC1",KSNODETYPE_DELAY);
#define KSNODETYPE_DELAY DEFINE_GUIDNAMED(KSNODETYPE_DELAY)

#define STATIC_KSNODETYPE_LOUDNESS					\
	0x41887440,0xC558,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("41887440-C558-11D0-8A2B-00A0C9255AC1",KSNODETYPE_LOUDNESS);
#define KSNODETYPE_LOUDNESS DEFINE_GUIDNAMED(KSNODETYPE_LOUDNESS)

#define STATIC_KSNODETYPE_PROLOGIC_DECODER				\
	0x831C2C80,0xC558,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("831C2C80-C558-11D0-8A2B-00A0C9255AC1",KSNODETYPE_PROLOGIC_DECODER);
#define KSNODETYPE_PROLOGIC_DECODER DEFINE_GUIDNAMED(KSNODETYPE_PROLOGIC_DECODER)

#define STATIC_KSNODETYPE_STEREO_WIDE					\
	0xA9E69800,0xC558,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("A9E69800-C558-11D0-8A2B-00A0C9255AC1",KSNODETYPE_STEREO_WIDE);
#define KSNODETYPE_STEREO_WIDE DEFINE_GUIDNAMED(KSNODETYPE_STEREO_WIDE)

#define STATIC_KSNODETYPE_STEREO_ENHANCE				\
	0xAF6878AC,0xE83F,0x11D0,0x95,0x8A,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("AF6878AC-E83F-11D0-958A-00C04FB925D3",KSNODETYPE_STEREO_ENHANCE);
#define KSNODETYPE_STEREO_ENHANCE DEFINE_GUIDNAMED(KSNODETYPE_STEREO_ENHANCE)

#define STATIC_KSNODETYPE_REVERB					\
	0xEF0328E0,0xC558,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("EF0328E0-C558-11D0-8A2B-00A0C9255AC1",KSNODETYPE_REVERB);
#define KSNODETYPE_REVERB DEFINE_GUIDNAMED(KSNODETYPE_REVERB)

#define STATIC_KSNODETYPE_CHORUS					\
	0x20173F20,0xC559,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("20173F20-C559-11D0-8A2B-00A0C9255AC1",KSNODETYPE_CHORUS);
#define KSNODETYPE_CHORUS DEFINE_GUIDNAMED(KSNODETYPE_CHORUS)

#define STATIC_KSNODETYPE_3D_EFFECTS					\
	0x55515860,0xC559,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("55515860-C559-11D0-8A2B-00A0C9255AC1",KSNODETYPE_3D_EFFECTS);
#define KSNODETYPE_3D_EFFECTS DEFINE_GUIDNAMED(KSNODETYPE_3D_EFFECTS)

#define STATIC_KSNODETYPE_ACOUSTIC_ECHO_CANCEL STATIC_KSCATEGORY_ACOUSTIC_ECHO_CANCEL
#define KSNODETYPE_ACOUSTIC_ECHO_CANCEL KSCATEGORY_ACOUSTIC_ECHO_CANCEL

#define STATIC_KSALGORITHMINSTANCE_SYSTEM_ACOUSTIC_ECHO_CANCEL		\
	0x1c22c56d,0x9879,0x4f5b,0xa3,0x89,0x27,0x99,0x6d,0xdc,0x28,0x10
DEFINE_GUIDSTRUCT("1C22C56D-9879-4f5b-A389-27996DDC2810",KSALGORITHMINSTANCE_SYSTEM_ACOUSTIC_ECHO_CANCEL);
#define KSALGORITHMINSTANCE_SYSTEM_ACOUSTIC_ECHO_CANCEL DEFINE_GUIDNAMED(KSALGORITHMINSTANCE_SYSTEM_ACOUSTIC_ECHO_CANCEL)

#define STATIC_KSALGORITHMINSTANCE_SYSTEM_NOISE_SUPPRESS		\
	0x5ab0882e,0x7274,0x4516,0x87,0x7d,0x4e,0xee,0x99,0xba,0x4f,0xd0
DEFINE_GUIDSTRUCT("5AB0882E-7274-4516-877D-4EEE99BA4FD0",KSALGORITHMINSTANCE_SYSTEM_NOISE_SUPPRESS);
#define KSALGORITHMINSTANCE_SYSTEM_NOISE_SUPPRESS DEFINE_GUIDNAMED(KSALGORITHMINSTANCE_SYSTEM_NOISE_SUPPRESS)

#define STATIC_KSALGORITHMINSTANCE_SYSTEM_AGC				\
	0x950e55b9,0x877c,0x4c67,0xbe,0x8,0xe4,0x7b,0x56,0x11,0x13,0xa
DEFINE_GUIDSTRUCT("950E55B9-877C-4c67-BE08-E47B5611130A",KSALGORITHMINSTANCE_SYSTEM_AGC);
#define KSALGORITHMINSTANCE_SYSTEM_AGC DEFINE_GUIDNAMED(KSALGORITHMINSTANCE_SYSTEM_AGC)

#define STATIC_KSALGORITHMINSTANCE_SYSTEM_MICROPHONE_ARRAY_PROCESSOR	\
	0xB6F5A0A0,0x9E61,0x4F8C,0x91,0xE3,0x76,0xCF,0xF,0x3C,0x47,0x1F
DEFINE_GUIDSTRUCT("B6F5A0A0-9E61-4f8c-91E3-76CF0F3C471F",KSALGORITHMINSTANCE_SYSTEM_MICROPHONE_ARRAY_PROCESSOR);
#define KSALGORITHMINSTANCE_SYSTEM_MICROPHONE_ARRAY_PROCESSOR DEFINE_GUIDNAMED(KSALGORITHMINSTANCE_SYSTEM_MICROPHONE_ARRAY_PROCESSOR)

#define STATIC_KSNODETYPE_MICROPHONE_ARRAY_PROCESSOR STATIC_KSCATEGORY_MICROPHONE_ARRAY_PROCESSOR
#define KSNODETYPE_MICROPHONE_ARRAY_PROCESSOR KSCATEGORY_MICROPHONE_ARRAY_PROCESSOR

#define STATIC_KSNODETYPE_DEV_SPECIFIC					\
	0x941C7AC0,0xC559,0x11D0,0x8A,0x2B,0x00,0xA0,0xC9,0x25,0x5A,0xC1
DEFINE_GUIDSTRUCT("941C7AC0-C559-11D0-8A2B-00A0C9255AC1",KSNODETYPE_DEV_SPECIFIC);
#define KSNODETYPE_DEV_SPECIFIC DEFINE_GUIDNAMED(KSNODETYPE_DEV_SPECIFIC)

#define STATIC_KSNODETYPE_PROLOGIC_ENCODER				\
	0x8074C5B2,0x3C66,0x11D2,0xB4,0x5A,0x30,0x78,0x30,0x2C,0x20,0x30
DEFINE_GUIDSTRUCT("8074C5B2-3C66-11D2-B45A-3078302C2030",KSNODETYPE_PROLOGIC_ENCODER);
#define KSNODETYPE_PROLOGIC_ENCODER DEFINE_GUIDNAMED(KSNODETYPE_PROLOGIC_ENCODER)
#define KSNODETYPE_SURROUND_ENCODER KSNODETYPE_PROLOGIC_ENCODER

#define STATIC_KSNODETYPE_PEAKMETER					\
	0xa085651e,0x5f0d,0x4b36,0xa8,0x69,0xd1,0x95,0xd6,0xab,0x4b,0x9e
DEFINE_GUIDSTRUCT("A085651E-5F0D-4b36-A869-D195D6AB4B9E",KSNODETYPE_PEAKMETER);
#define KSNODETYPE_PEAKMETER DEFINE_GUIDNAMED(KSNODETYPE_PEAKMETER)

#define STATIC_KSAUDFNAME_BASS						\
	0x185FEDE0,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDE0-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_BASS);
#define KSAUDFNAME_BASS DEFINE_GUIDNAMED(KSAUDFNAME_BASS)

#define STATIC_KSAUDFNAME_TREBLE					\
	0x185FEDE1,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDE1-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_TREBLE);
#define KSAUDFNAME_TREBLE DEFINE_GUIDNAMED(KSAUDFNAME_TREBLE)

#define STATIC_KSAUDFNAME_3D_STEREO					\
	0x185FEDE2,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDE2-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_3D_STEREO);
#define KSAUDFNAME_3D_STEREO DEFINE_GUIDNAMED(KSAUDFNAME_3D_STEREO)

#define STATIC_KSAUDFNAME_MASTER_VOLUME					\
	0x185FEDE3,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDE3-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_MASTER_VOLUME);
#define KSAUDFNAME_MASTER_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_MASTER_VOLUME)

#define STATIC_KSAUDFNAME_MASTER_MUTE					\
	0x185FEDE4,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDE4-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_MASTER_MUTE);
#define KSAUDFNAME_MASTER_MUTE DEFINE_GUIDNAMED(KSAUDFNAME_MASTER_MUTE)

#define STATIC_KSAUDFNAME_WAVE_VOLUME					\
	0x185FEDE5,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDE5-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_WAVE_VOLUME);
#define KSAUDFNAME_WAVE_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_WAVE_VOLUME)

#define STATIC_KSAUDFNAME_WAVE_MUTE					\
	0x185FEDE6,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDE6-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_WAVE_MUTE);
#define KSAUDFNAME_WAVE_MUTE DEFINE_GUIDNAMED(KSAUDFNAME_WAVE_MUTE)

#define STATIC_KSAUDFNAME_MIDI_VOLUME					\
	0x185FEDE7,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDE7-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_MIDI_VOLUME);
#define KSAUDFNAME_MIDI_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_MIDI_VOLUME)

#define STATIC_KSAUDFNAME_MIDI_MUTE					\
	0x185FEDE8,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDE8-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_MIDI_MUTE);
#define KSAUDFNAME_MIDI_MUTE DEFINE_GUIDNAMED(KSAUDFNAME_MIDI_MUTE)

#define STATIC_KSAUDFNAME_CD_VOLUME					\
	0x185FEDE9,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDE9-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_CD_VOLUME);
#define KSAUDFNAME_CD_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_CD_VOLUME)

#define STATIC_KSAUDFNAME_CD_MUTE					\
	0x185FEDEA,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDEA-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_CD_MUTE);
#define KSAUDFNAME_CD_MUTE DEFINE_GUIDNAMED(KSAUDFNAME_CD_MUTE)

#define STATIC_KSAUDFNAME_LINE_VOLUME					\
	0x185FEDEB,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDEB-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_LINE_VOLUME);
#define KSAUDFNAME_LINE_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_LINE_VOLUME)

#define STATIC_KSAUDFNAME_LINE_MUTE					\
	0x185FEDEC,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDEC-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_LINE_MUTE);
#define KSAUDFNAME_LINE_MUTE DEFINE_GUIDNAMED(KSAUDFNAME_LINE_MUTE)

#define STATIC_KSAUDFNAME_MIC_VOLUME					\
	0x185FEDED,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDED-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_MIC_VOLUME);
#define KSAUDFNAME_MIC_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_MIC_VOLUME)

#define STATIC_KSAUDFNAME_MIC_MUTE					\
	0x185FEDEE,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDEE-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_MIC_MUTE);
#define KSAUDFNAME_MIC_MUTE DEFINE_GUIDNAMED(KSAUDFNAME_MIC_MUTE)

#define STATIC_KSAUDFNAME_RECORDING_SOURCE				\
	0x185FEDEF,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDEF-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_RECORDING_SOURCE);
#define KSAUDFNAME_RECORDING_SOURCE DEFINE_GUIDNAMED(KSAUDFNAME_RECORDING_SOURCE)

#define STATIC_KSAUDFNAME_PC_SPEAKER_VOLUME				\
	0x185FEDF0,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDF0-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_PC_SPEAKER_VOLUME);
#define KSAUDFNAME_PC_SPEAKER_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_PC_SPEAKER_VOLUME)

#define STATIC_KSAUDFNAME_PC_SPEAKER_MUTE				\
	0x185FEDF1,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDF1-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_PC_SPEAKER_MUTE);
#define KSAUDFNAME_PC_SPEAKER_MUTE DEFINE_GUIDNAMED(KSAUDFNAME_PC_SPEAKER_MUTE)

#define STATIC_KSAUDFNAME_MIDI_IN_VOLUME				\
	0x185FEDF2,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDF2-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_MIDI_IN_VOLUME);
#define KSAUDFNAME_MIDI_IN_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_MIDI_IN_VOLUME)

#define STATIC_KSAUDFNAME_CD_IN_VOLUME					\
	0x185FEDF3,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDF3-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_CD_IN_VOLUME);
#define KSAUDFNAME_CD_IN_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_CD_IN_VOLUME)

#define STATIC_KSAUDFNAME_LINE_IN_VOLUME				\
	0x185FEDF4,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDF4-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_LINE_IN_VOLUME);
#define KSAUDFNAME_LINE_IN_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_LINE_IN_VOLUME)

#define STATIC_KSAUDFNAME_MIC_IN_VOLUME					\
	0x185FEDF5,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDF5-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_MIC_IN_VOLUME);
#define KSAUDFNAME_MIC_IN_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_MIC_IN_VOLUME)

#define STATIC_KSAUDFNAME_WAVE_IN_VOLUME				\
	0x185FEDF6,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDF6-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_WAVE_IN_VOLUME);
#define KSAUDFNAME_WAVE_IN_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_WAVE_IN_VOLUME)

#define STATIC_KSAUDFNAME_VOLUME_CONTROL				\
	0x185FEDF7,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDF7-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_VOLUME_CONTROL);
#define KSAUDFNAME_VOLUME_CONTROL DEFINE_GUIDNAMED(KSAUDFNAME_VOLUME_CONTROL)

#define STATIC_KSAUDFNAME_MIDI						\
	0x185FEDF8,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDF8-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_MIDI);
#define KSAUDFNAME_MIDI DEFINE_GUIDNAMED(KSAUDFNAME_MIDI)

#define STATIC_KSAUDFNAME_LINE_IN					\
	0x185FEDF9,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDF9-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_LINE_IN);
#define KSAUDFNAME_LINE_IN DEFINE_GUIDNAMED(KSAUDFNAME_LINE_IN)

#define STATIC_KSAUDFNAME_RECORDING_CONTROL				\
	0x185FEDFA,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDFA-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_RECORDING_CONTROL);
#define KSAUDFNAME_RECORDING_CONTROL DEFINE_GUIDNAMED(KSAUDFNAME_RECORDING_CONTROL)

#define STATIC_KSAUDFNAME_CD_AUDIO					\
	0x185FEDFB,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDFB-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_CD_AUDIO);
#define KSAUDFNAME_CD_AUDIO DEFINE_GUIDNAMED(KSAUDFNAME_CD_AUDIO)

#define STATIC_KSAUDFNAME_AUX_VOLUME					\
	0x185FEDFC,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDFC-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_AUX_VOLUME);
#define KSAUDFNAME_AUX_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_AUX_VOLUME)

#define STATIC_KSAUDFNAME_AUX_MUTE					\
	0x185FEDFD,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDFD-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_AUX_MUTE);
#define KSAUDFNAME_AUX_MUTE DEFINE_GUIDNAMED(KSAUDFNAME_AUX_MUTE)

#define STATIC_KSAUDFNAME_AUX						\
	0x185FEDFE,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDFE-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_AUX);
#define KSAUDFNAME_AUX DEFINE_GUIDNAMED(KSAUDFNAME_AUX)

#define STATIC_KSAUDFNAME_PC_SPEAKER					\
	0x185FEDFF,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEDFF-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_PC_SPEAKER);
#define KSAUDFNAME_PC_SPEAKER DEFINE_GUIDNAMED(KSAUDFNAME_PC_SPEAKER)

#define STATIC_KSAUDFNAME_WAVE_OUT_MIX					\
	0x185FEE00,0x9905,0x11D1,0x95,0xA9,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("185FEE00-9905-11D1-95A9-00C04FB925D3",KSAUDFNAME_WAVE_OUT_MIX);
#define KSAUDFNAME_WAVE_OUT_MIX DEFINE_GUIDNAMED(KSAUDFNAME_WAVE_OUT_MIX)

#define STATIC_KSAUDFNAME_MONO_OUT					\
	0xf9b41dc3,0x96e2,0x11d2,0xac,0x4c,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("F9B41DC3-96E2-11d2-AC4C-00C04F8EFB68",KSAUDFNAME_MONO_OUT);
#define KSAUDFNAME_MONO_OUT DEFINE_GUIDNAMED(KSAUDFNAME_MONO_OUT)

#define STATIC_KSAUDFNAME_STEREO_MIX					\
	0xdff077,0x96e3,0x11d2,0xac,0x4c,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("00DFF077-96E3-11d2-AC4C-00C04F8EFB68",KSAUDFNAME_STEREO_MIX);
#define KSAUDFNAME_STEREO_MIX DEFINE_GUIDNAMED(KSAUDFNAME_STEREO_MIX)

#define STATIC_KSAUDFNAME_MONO_MIX					\
	0xdff078,0x96e3,0x11d2,0xac,0x4c,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("00DFF078-96E3-11d2-AC4C-00C04F8EFB68",KSAUDFNAME_MONO_MIX);
#define KSAUDFNAME_MONO_MIX DEFINE_GUIDNAMED(KSAUDFNAME_MONO_MIX)

#define STATIC_KSAUDFNAME_MONO_OUT_VOLUME				\
	0x1ad247eb,0x96e3,0x11d2,0xac,0x4c,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("1AD247EB-96E3-11d2-AC4C-00C04F8EFB68",KSAUDFNAME_MONO_OUT_VOLUME);
#define KSAUDFNAME_MONO_OUT_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_MONO_OUT_VOLUME)

#define STATIC_KSAUDFNAME_MONO_OUT_MUTE					\
	0x1ad247ec,0x96e3,0x11d2,0xac,0x4c,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("1AD247EC-96E3-11d2-AC4C-00C04F8EFB68",KSAUDFNAME_MONO_OUT_MUTE);
#define KSAUDFNAME_MONO_OUT_MUTE DEFINE_GUIDNAMED(KSAUDFNAME_MONO_OUT_MUTE)

#define STATIC_KSAUDFNAME_STEREO_MIX_VOLUME				\
	0x1ad247ed,0x96e3,0x11d2,0xac,0x4c,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("1AD247ED-96E3-11d2-AC4C-00C04F8EFB68",KSAUDFNAME_STEREO_MIX_VOLUME);
#define KSAUDFNAME_STEREO_MIX_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_STEREO_MIX_VOLUME)

#define STATIC_KSAUDFNAME_STEREO_MIX_MUTE				\
	0x22b0eafd,0x96e3,0x11d2,0xac,0x4c,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("22B0EAFD-96E3-11d2-AC4C-00C04F8EFB68",KSAUDFNAME_STEREO_MIX_MUTE);
#define KSAUDFNAME_STEREO_MIX_MUTE DEFINE_GUIDNAMED(KSAUDFNAME_STEREO_MIX_MUTE)

#define STATIC_KSAUDFNAME_MONO_MIX_VOLUME				\
	0x22b0eafe,0x96e3,0x11d2,0xac,0x4c,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("22B0EAFE-96E3-11d2-AC4C-00C04F8EFB68",KSAUDFNAME_MONO_MIX_VOLUME);
#define KSAUDFNAME_MONO_MIX_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_MONO_MIX_VOLUME)

#define STATIC_KSAUDFNAME_MONO_MIX_MUTE					\
	0x2bc31d69,0x96e3,0x11d2,0xac,0x4c,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("2BC31D69-96E3-11d2-AC4C-00C04F8EFB68",KSAUDFNAME_MONO_MIX_MUTE);
#define KSAUDFNAME_MONO_MIX_MUTE DEFINE_GUIDNAMED(KSAUDFNAME_MONO_MIX_MUTE)

#define STATIC_KSAUDFNAME_MICROPHONE_BOOST				\
	0x2bc31d6a,0x96e3,0x11d2,0xac,0x4c,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("2BC31D6A-96E3-11d2-AC4C-00C04F8EFB68",KSAUDFNAME_MICROPHONE_BOOST);
#define KSAUDFNAME_MICROPHONE_BOOST DEFINE_GUIDNAMED(KSAUDFNAME_MICROPHONE_BOOST)

#define STATIC_KSAUDFNAME_ALTERNATE_MICROPHONE				\
	0x2bc31d6b,0x96e3,0x11d2,0xac,0x4c,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("2BC31D6B-96E3-11d2-AC4C-00C04F8EFB68",KSAUDFNAME_ALTERNATE_MICROPHONE);
#define KSAUDFNAME_ALTERNATE_MICROPHONE DEFINE_GUIDNAMED(KSAUDFNAME_ALTERNATE_MICROPHONE)

#define STATIC_KSAUDFNAME_3D_DEPTH					\
	0x63ff5747,0x991f,0x11d2,0xac,0x4d,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("63FF5747-991F-11d2-AC4D-00C04F8EFB68",KSAUDFNAME_3D_DEPTH);
#define KSAUDFNAME_3D_DEPTH DEFINE_GUIDNAMED(KSAUDFNAME_3D_DEPTH)

#define STATIC_KSAUDFNAME_3D_CENTER					\
	0x9f0670b4,0x991f,0x11d2,0xac,0x4d,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("9F0670B4-991F-11d2-AC4D-00C04F8EFB68",KSAUDFNAME_3D_CENTER);
#define KSAUDFNAME_3D_CENTER DEFINE_GUIDNAMED(KSAUDFNAME_3D_CENTER)

#define STATIC_KSAUDFNAME_VIDEO_VOLUME					\
	0x9b46e708,0x992a,0x11d2,0xac,0x4d,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("9B46E708-992A-11d2-AC4D-00C04F8EFB68",KSAUDFNAME_VIDEO_VOLUME);
#define KSAUDFNAME_VIDEO_VOLUME DEFINE_GUIDNAMED(KSAUDFNAME_VIDEO_VOLUME)

#define STATIC_KSAUDFNAME_VIDEO_MUTE					\
	0x9b46e709,0x992a,0x11d2,0xac,0x4d,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("9B46E709-992A-11d2-AC4D-00C04F8EFB68",KSAUDFNAME_VIDEO_MUTE);
#define KSAUDFNAME_VIDEO_MUTE DEFINE_GUIDNAMED(KSAUDFNAME_VIDEO_MUTE)

#define STATIC_KSAUDFNAME_VIDEO						\
	0x915daec4,0xa434,0x11d2,0xac,0x52,0x0,0xc0,0x4f,0x8e,0xfb,0x68
DEFINE_GUIDSTRUCT("915DAEC4-A434-11d2-AC52-00C04F8EFB68",KSAUDFNAME_VIDEO);
#define KSAUDFNAME_VIDEO DEFINE_GUIDNAMED(KSAUDFNAME_VIDEO)

#define STATIC_KSAUDFNAME_PEAKMETER					\
	0x57e24340,0xfc5b,0x4612,0xa5,0x62,0x72,0xb1,0x1a,0x29,0xdf,0xae
DEFINE_GUIDSTRUCT("57E24340-FC5B-4612-A562-72B11A29DFAE",KSAUDFNAME_PEAKMETER);
#define KSAUDFNAME_PEAKMETER DEFINE_GUIDNAMED(KSAUDFNAME_PEAKMETER)

#define KSNODEPIN_STANDARD_IN		1
#define KSNODEPIN_STANDARD_OUT		0

#define KSNODEPIN_SUM_MUX_IN		1
#define KSNODEPIN_SUM_MUX_OUT		0

#define KSNODEPIN_DEMUX_IN		0
#define KSNODEPIN_DEMUX_OUT		1

#if NTDDI_VERSION < NTDDI_VISTA
#define KSNODEPIN_AEC_RENDER_IN		1
#define KSNODEPIN_AEC_RENDER_OUT	0
#define KSNODEPIN_AEC_CAPTURE_IN	2
#define KSNODEPIN_AEC_CAPTURE_OUT	3
#endif  /* NTDDI_VERSION < NTDDI_VISTA */

#define STATIC_KSMETHODSETID_Wavetable					\
	0xDCEF31EB,0xD907,0x11D0,0x95,0x83,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("DCEF31EB-D907-11D0-9583-00C04FB925D3",KSMETHODSETID_Wavetable);
#define KSMETHODSETID_Wavetable DEFINE_GUIDNAMED(KSMETHODSETID_Wavetable)

typedef enum {
  KSMETHOD_WAVETABLE_WAVE_ALLOC,
  KSMETHOD_WAVETABLE_WAVE_FREE,
  KSMETHOD_WAVETABLE_WAVE_FIND,
  KSMETHOD_WAVETABLE_WAVE_WRITE
} KSMETHOD_WAVETABLE;

typedef struct {
  KSIDENTIFIER Identifier;
  ULONG Size;
  WINBOOL Looped;
  ULONG LoopPoint;
  WINBOOL InROM;
  KSDATAFORMAT Format;
} KSWAVETABLE_WAVE_DESC,*PKSWAVETABLE_WAVE_DESC;

#if NTDDI_VERSION < NTDDI_VISTA
#define STATIC_KSPROPSETID_Acoustic_Echo_Cancel				\
	0xd7a4af8b,0x3dc1,0x4902,0x91,0xea,0x8a,0x15,0xc9,0x0e,0x05,0xb2
DEFINE_GUIDSTRUCT("D7A4AF8B-3DC1-4902-91EA-8A15C90E05B2",KSPROPSETID_Acoustic_Echo_Cancel);
#define KSPROPSETID_Acoustic_Echo_Cancel DEFINE_GUIDNAMED(KSPROPSETID_Acoustic_Echo_Cancel)
#endif  /* NTDDI_VERSION < NTDDI_VISTA */

typedef enum {
  KSPROPERTY_AEC_NOISE_FILL_ENABLE = 0,
  KSPROPERTY_AEC_STATUS,
  KSPROPERTY_AEC_MODE
} KSPROPERTY_AEC;

#define AEC_STATUS_FD_HISTORY_UNINITIALIZED		0x0
#define AEC_STATUS_FD_HISTORY_CONTINUOUSLY_CONVERGED	0x1
#define AEC_STATUS_FD_HISTORY_PREVIOUSLY_DIVERGED	0x2
#define AEC_STATUS_FD_CURRENTLY_CONVERGED		0x8

#define AEC_MODE_PASS_THROUGH				0x0
#define AEC_MODE_HALF_DUPLEX				0x1
#define AEC_MODE_FULL_DUPLEX				0x2

#if (NTDDI_VERSION < NTDDI_WS03)
#define STATIC_KSPROPSETID_Wave_Queued					\
	0x16a15b10,0x16f0,0x11d0,0xa1,0x95,0x00,0x20,0xaf,0xd1,0x56,0xe4
DEFINE_GUIDSTRUCT("16a15b10-16f0-11d0-a195-0020afd156e4", KSPROPSETID_Wave_Queued);
#define KSPROPSETID_Wave_Queued DEFINE_GUIDNAMED(KSPROPSETID_Wave_Queued)
#endif /* NTDDI_VERSION < NTDDI_WS03 */

#define STATIC_KSPROPSETID_Wave						\
	0x924e54b0,0x630f,0x11cf,0xad,0xa7,0x08,0x00,0x3e,0x30,0x49,0x4a
DEFINE_GUIDSTRUCT("924e54b0-630f-11cf-ada7-08003e30494a",KSPROPSETID_Wave);
#define KSPROPSETID_Wave DEFINE_GUIDNAMED(KSPROPSETID_Wave)

typedef enum {
  KSPROPERTY_WAVE_COMPATIBLE_CAPABILITIES,
  KSPROPERTY_WAVE_INPUT_CAPABILITIES,
  KSPROPERTY_WAVE_OUTPUT_CAPABILITIES,
  KSPROPERTY_WAVE_BUFFER,
  KSPROPERTY_WAVE_FREQUENCY,
  KSPROPERTY_WAVE_VOLUME,
  KSPROPERTY_WAVE_PAN
} KSPROPERTY_WAVE;

typedef struct {
  ULONG ulDeviceType;
} KSWAVE_COMPATCAPS,*PKSWAVE_COMPATCAPS;

#define KSWAVE_COMPATCAPS_INPUT		0x00000000
#define KSWAVE_COMPATCAPS_OUTPUT	0x00000001

typedef struct {
  ULONG MaximumChannelsPerConnection;
  ULONG MinimumBitsPerSample;
  ULONG MaximumBitsPerSample;
  ULONG MinimumSampleFrequency;
  ULONG MaximumSampleFrequency;
  ULONG TotalConnections;
  ULONG ActiveConnections;
} KSWAVE_INPUT_CAPABILITIES,*PKSWAVE_INPUT_CAPABILITIES;

typedef struct {
  ULONG MaximumChannelsPerConnection;
  ULONG MinimumBitsPerSample;
  ULONG MaximumBitsPerSample;
  ULONG MinimumSampleFrequency;
  ULONG MaximumSampleFrequency;
  ULONG TotalConnections;
  ULONG StaticConnections;
  ULONG StreamingConnections;
  ULONG ActiveConnections;
  ULONG ActiveStaticConnections;
  ULONG ActiveStreamingConnections;
  ULONG Total3DConnections;
  ULONG Static3DConnections;
  ULONG Streaming3DConnections;
  ULONG Active3DConnections;
  ULONG ActiveStatic3DConnections;
  ULONG ActiveStreaming3DConnections;
  ULONG TotalSampleMemory;
  ULONG FreeSampleMemory;
  ULONG LargestFreeContiguousSampleMemory;
} KSWAVE_OUTPUT_CAPABILITIES,*PKSWAVE_OUTPUT_CAPABILITIES;

typedef struct {
  LONG LeftAttenuation;
  LONG RightAttenuation;
} KSWAVE_VOLUME,*PKSWAVE_VOLUME;

#define KSWAVE_BUFFER_ATTRIBUTEF_LOOPING	0x00000001
#define KSWAVE_BUFFER_ATTRIBUTEF_STATIC		0x00000002

typedef struct {
  ULONG Attributes;
  ULONG BufferSize;
  PVOID BufferAddress;
} KSWAVE_BUFFER,*PKSWAVE_BUFFER;

#define STATIC_KSMUSIC_TECHNOLOGY_PORT					\
	0x86C92E60,0x62E8,0x11CF,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("86C92E60-62E8-11CF-A5D6-28DB04C10000",KSMUSIC_TECHNOLOGY_PORT);
#define KSMUSIC_TECHNOLOGY_PORT DEFINE_GUIDNAMED(KSMUSIC_TECHNOLOGY_PORT)

#define STATIC_KSMUSIC_TECHNOLOGY_SQSYNTH				\
	0x0ECF4380,0x62E9,0x11CF,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("0ECF4380-62E9-11CF-A5D6-28DB04C10000",KSMUSIC_TECHNOLOGY_SQSYNTH);
#define KSMUSIC_TECHNOLOGY_SQSYNTH DEFINE_GUIDNAMED(KSMUSIC_TECHNOLOGY_SQSYNTH)

#define STATIC_KSMUSIC_TECHNOLOGY_FMSYNTH				\
	0x252C5C80,0x62E9,0x11CF,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("252C5C80-62E9-11CF-A5D6-28DB04C10000",KSMUSIC_TECHNOLOGY_FMSYNTH);
#define KSMUSIC_TECHNOLOGY_FMSYNTH DEFINE_GUIDNAMED(KSMUSIC_TECHNOLOGY_FMSYNTH)

#define STATIC_KSMUSIC_TECHNOLOGY_WAVETABLE				\
	0x394EC7C0,0x62E9,0x11CF,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("394EC7C0-62E9-11CF-A5D6-28DB04C10000",KSMUSIC_TECHNOLOGY_WAVETABLE);
#define KSMUSIC_TECHNOLOGY_WAVETABLE DEFINE_GUIDNAMED(KSMUSIC_TECHNOLOGY_WAVETABLE)

#define STATIC_KSMUSIC_TECHNOLOGY_SWSYNTH				\
	0x37407736,0x3620,0x11D1,0x85,0xD3,0x00,0x00,0xF8,0x75,0x43,0x80
DEFINE_GUIDSTRUCT("*************-11D1-85D3-0000F8754380",KSMUSIC_TECHNOLOGY_SWSYNTH);
#define KSMUSIC_TECHNOLOGY_SWSYNTH DEFINE_GUIDNAMED(KSMUSIC_TECHNOLOGY_SWSYNTH)

#if NTDDI_VERSION < NTDDI_WS03
#define STATIC_KSPROPSETID_WaveTable					\
	0x8539E660,0x62E9,0x11CF,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("8539E660-62E9-11CF-A5D6-28DB04C10000",KSPROPSETID_WaveTable);
#define KSPROPSETID_WaveTable DEFINE_GUIDNAMED(KSPROPSETID_WaveTable)

typedef enum {
  KSPROPERTY_WAVETABLE_LOAD_SAMPLE,
  KSPROPERTY_WAVETABLE_UNLOAD_SAMPLE,
  KSPROPERTY_WAVETABLE_MEMORY,
  KSPROPERTY_WAVETABLE_VERSION
} KSPROPERTY_WAVETABLE;
#endif /* NTDDI_VERSION < NTDDI_WS03 */

typedef struct {
  KSDATARANGE DataRange;
  GUID Technology;
  ULONG Channels;
  ULONG Notes;
  ULONG ChannelMask;
} KSDATARANGE_MUSIC,*PKSDATARANGE_MUSIC;

#if NTDDI_VERSION < NTDDI_WS03
#define STATIC_KSEVENTSETID_Cyclic					\
	0x142C1AC0,0x072A,0x11D0,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("142C1AC0-072A-11D0-A5D6-28DB04C10000",KSEVENTSETID_Cyclic);
#define KSEVENTSETID_Cyclic DEFINE_GUIDNAMED(KSEVENTSETID_Cyclic)

typedef enum {
  KSEVENT_CYCLIC_TIME_INTERVAL
} KSEVENT_CYCLIC_TIME;
#endif /* NTDDI_VERSION < NTDDI_WS03 */

#define STATIC_KSPROPSETID_Cyclic					\
	0x3FFEAEA0,0x2BEE,0x11CF,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("3FFEAEA0-2BEE-11CF-A5D6-28DB04C10000",KSPROPSETID_Cyclic);
#define KSPROPSETID_Cyclic DEFINE_GUIDNAMED(KSPROPSETID_Cyclic)

typedef enum {
  KSPROPERTY_CYCLIC_POSITION
} KSPROPERTY_CYCLIC;

#define STATIC_KSEVENTSETID_AudioControlChange				\
	0xE85E9698,0xFA2F,0x11D1,0x95,0xBD,0x00,0xC0,0x4F,0xB9,0x25,0xD3
DEFINE_GUIDSTRUCT("E85E9698-FA2F-11D1-95BD-00C04FB925D3",KSEVENTSETID_AudioControlChange);
#define KSEVENTSETID_AudioControlChange DEFINE_GUIDNAMED(KSEVENTSETID_AudioControlChange)

typedef enum {
  KSEVENT_CONTROL_CHANGE
} KSEVENT_AUDIO_CONTROL_CHANGE;

#define STATIC_KSEVENTSETID_LoopedStreaming				\
	0x4682B940,0xC6EF,0x11D0,0x96,0xD8,0x00,0xAA,0x00,0x51,0xE5,0x1D
DEFINE_GUIDSTRUCT("4682B940-C6EF-11D0-96D8-00AA0051E51D",KSEVENTSETID_LoopedStreaming);
#define KSEVENTSETID_LoopedStreaming DEFINE_GUIDNAMED(KSEVENTSETID_LoopedStreaming)

typedef enum {
  KSEVENT_LOOPEDSTREAMING_POSITION
} KSEVENT_LOOPEDSTREAMING;

typedef struct {
  KSEVENTDATA KsEventData;
  DWORDLONG Position;
} LOOPEDSTREAMING_POSITION_EVENT_DATA,*PLOOPEDSTREAMING_POSITION_EVENT_DATA;

#if NTDDI_VERSION < NTDDI_VISTA
#define STATIC_KSPROPSETID_Sysaudio					\
	0xCBE3FAA0,0xCC75,0x11D0,0xB4,0x65,0x00,0x00,0x1A,0x18,0x18,0xE6
DEFINE_GUIDSTRUCT("CBE3FAA0-CC75-11D0-B465-00001A1818E6",KSPROPSETID_Sysaudio);
#define KSPROPSETID_Sysaudio DEFINE_GUIDNAMED(KSPROPSETID_Sysaudio)

typedef enum {
  KSPROPERTY_SYSAUDIO_DEVICE_COUNT = 1,
  KSPROPERTY_SYSAUDIO_DEVICE_FRIENDLY_NAME = 2,
  KSPROPERTY_SYSAUDIO_DEVICE_INSTANCE = 3,
  KSPROPERTY_SYSAUDIO_DEVICE_INTERFACE_NAME = 4,
  KSPROPERTY_SYSAUDIO_SELECT_GRAPH = 5,
  KSPROPERTY_SYSAUDIO_CREATE_VIRTUAL_SOURCE = 6,
  KSPROPERTY_SYSAUDIO_DEVICE_DEFAULT = 7,
  KSPROPERTY_SYSAUDIO_INSTANCE_INFO = 14,
  KSPROPERTY_SYSAUDIO_COMPONENT_ID = 16
} KSPROPERTY_SYSAUDIO;

typedef struct {
  KSPROPERTY Property;
  GUID PinCategory;
  GUID PinName;
} SYSAUDIO_CREATE_VIRTUAL_SOURCE,*PSYSAUDIO_CREATE_VIRTUAL_SOURCE;

typedef struct {
  KSPROPERTY Property;
  ULONG PinId;
  ULONG NodeId;
  ULONG Flags;
  ULONG Reserved;
} SYSAUDIO_SELECT_GRAPH,*PSYSAUDIO_SELECT_GRAPH;

typedef struct {
  KSPROPERTY Property;
  ULONG Flags;
  ULONG DeviceNumber;
} SYSAUDIO_INSTANCE_INFO,*PSYSAUDIO_INSTANCE_INFO;

#define SYSAUDIO_FLAGS_DONT_COMBINE_PINS	0x00000001

#define STATIC_KSPROPSETID_Sysaudio_Pin					\
	0xA3A53220,0xC6E4,0x11D0,0xB4,0x65,0x00,0x00,0x1A,0x18,0x18,0xE6
DEFINE_GUIDSTRUCT("A3A53220-C6E4-11D0-B465-00001A1818E6",KSPROPSETID_Sysaudio_Pin);
#define KSPROPSETID_Sysaudio_Pin DEFINE_GUIDNAMED(KSPROPSETID_Sysaudio_Pin)

typedef enum {
  KSPROPERTY_SYSAUDIO_ATTACH_VIRTUAL_SOURCE = 1
} KSPROPERTY_SYSAUDIO_PIN;

typedef struct {
  KSPROPERTY Property;
  ULONG MixerPinId;
  ULONG Reserved;
} SYSAUDIO_ATTACH_VIRTUAL_SOURCE,*PSYSAUDIO_ATTACH_VIRTUAL_SOURCE;
#endif  /* NTDDI_VERSION < NTDDI_VISTA */

typedef struct {
  KSPROPERTY Property;
  ULONG NodeId;
  ULONG Reserved;
} KSNODEPROPERTY,*PKSNODEPROPERTY;

typedef struct {
  KSNODEPROPERTY NodeProperty;
  LONG Channel;
  ULONG Reserved;
} KSNODEPROPERTY_AUDIO_CHANNEL,*PKSNODEPROPERTY_AUDIO_CHANNEL;

typedef struct {
  KSNODEPROPERTY NodeProperty;
  ULONG DevSpecificId;
  ULONG DeviceInfo;
  ULONG Length;
} KSNODEPROPERTY_AUDIO_DEV_SPECIFIC,*PKSNODEPROPERTY_AUDIO_DEV_SPECIFIC;

typedef struct {
  KSNODEPROPERTY NodeProperty;
  PVOID ListenerId;
#ifndef _WIN64
  ULONG Reserved;
#endif
} KSNODEPROPERTY_AUDIO_3D_LISTENER,*PKSNODEPROPERTY_AUDIO_3D_LISTENER;

typedef struct {
  KSNODEPROPERTY NodeProperty;
  PVOID AppContext;
  ULONG Length;
#ifndef _WIN64
  ULONG Reserved;
#endif
} KSNODEPROPERTY_AUDIO_PROPERTY,*PKSNODEPROPERTY_AUDIO_PROPERTY;

#if NTDDI_VERSION < NTDDI_VISTA
#define STATIC_KSPROPSETID_AudioGfx					\
	0x79a9312e,0x59ae,0x43b0,0xa3,0x50,0x8b,0x5,0x28,0x4c,0xab,0x24
DEFINE_GUIDSTRUCT("79A9312E-59AE-43b0-A350-8B05284CAB24",KSPROPSETID_AudioGfx);
#define KSPROPSETID_AudioGfx DEFINE_GUIDNAMED(KSPROPSETID_AudioGfx)
#endif  /* NTDDI_VERSION < NTDDI_VISTA */

typedef enum {
  KSPROPERTY_AUDIOGFX_RENDERTARGETDEVICEID,
  KSPROPERTY_AUDIOGFX_CAPTURETARGETDEVICEID
} KSPROPERTY_AUDIOGFX;

#if NTDDI_VERSION < NTDDI_WS03
#define STATIC_KSPROPSETID_Linear					\
	0x5A2FFE80,0x16B9,0x11D0,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("5A2FFE80-16B9-11D0-A5D6-28DB04C10000",KSPROPSETID_Linear);
#define KSPROPSETID_Linear DEFINE_GUIDNAMED(KSPROPSETID_Linear)

typedef enum {
  KSPROPERTY_LINEAR_POSITION
} KSPROPERTY_LINEAR;
#endif  /* NTDDI_VERSION < NTDDI_WS03 */

#define STATIC_KSDATAFORMAT_TYPE_MUSIC					\
	0xE725D360,0x62CC,0x11CF,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("E725D360-62CC-11CF-A5D6-28DB04C10000",KSDATAFORMAT_TYPE_MUSIC);
#define KSDATAFORMAT_TYPE_MUSIC DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_MUSIC)

#define STATIC_KSDATAFORMAT_TYPE_MIDI					\
	0x7364696D,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71
DEFINE_GUIDSTRUCT("7364696D-0000-0010-8000-00aa00389b71",KSDATAFORMAT_TYPE_MIDI);
#define KSDATAFORMAT_TYPE_MIDI DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_MIDI)

#define STATIC_KSDATAFORMAT_SUBTYPE_MIDI				\
	0x1D262760,0xE957,0x11CF,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("1D262760-E957-11CF-A5D6-28DB04C10000",KSDATAFORMAT_SUBTYPE_MIDI);
#define KSDATAFORMAT_SUBTYPE_MIDI DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_MIDI)

#define STATIC_KSDATAFORMAT_SUBTYPE_MIDI_BUS				\
	0x2CA15FA0,0x6CFE,0x11CF,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00
DEFINE_GUIDSTRUCT("2CA15FA0-6CFE-11CF-A5D6-28DB04C10000",KSDATAFORMAT_SUBTYPE_MIDI_BUS);
#define KSDATAFORMAT_SUBTYPE_MIDI_BUS DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_MIDI_BUS)

#define STATIC_KSDATAFORMAT_SUBTYPE_RIFFMIDI				\
	0x4995DAF0,0x9EE6,0x11D0,0xA4,0x0E,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("4995DAF0-9EE6-11D0-A40E-00A0C9223196",KSDATAFORMAT_SUBTYPE_RIFFMIDI);
#define KSDATAFORMAT_SUBTYPE_RIFFMIDI DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_RIFFMIDI)

typedef struct {
  ULONG TimeDeltaMs;

  ULONG ByteCount;
} KSMUSICFORMAT,*PKSMUSICFORMAT;

#define STATIC_KSDATAFORMAT_TYPE_STANDARD_ELEMENTARY_STREAM		\
	0x36523b11,0x8ee5,0x11d1,0x8c,0xa3,0x00,0x60,0xb0,0x57,0x66,0x4a
DEFINE_GUIDSTRUCT("36523B11-8EE5-11d1-8CA3-0060B057664A",KSDATAFORMAT_TYPE_STANDARD_ELEMENTARY_STREAM);
#define KSDATAFORMAT_TYPE_STANDARD_ELEMENTARY_STREAM DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_STANDARD_ELEMENTARY_STREAM)

#define STATIC_KSDATAFORMAT_TYPE_STANDARD_PES_PACKET			\
	0x36523b12,0x8ee5,0x11d1,0x8c,0xa3,0x00,0x60,0xb0,0x57,0x66,0x4a
DEFINE_GUIDSTRUCT("36523B12-8EE5-11d1-8CA3-0060B057664A",KSDATAFORMAT_TYPE_STANDARD_PES_PACKET);
#define KSDATAFORMAT_TYPE_STANDARD_PES_PACKET DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_STANDARD_PES_PACKET)

#define STATIC_KSDATAFORMAT_TYPE_STANDARD_PACK_HEADER			\
	0x36523b13,0x8ee5,0x11d1,0x8c,0xa3,0x00,0x60,0xb0,0x57,0x66,0x4a
DEFINE_GUIDSTRUCT("36523B13-8EE5-11d1-8CA3-0060B057664A",KSDATAFORMAT_TYPE_STANDARD_PACK_HEADER);
#define KSDATAFORMAT_TYPE_STANDARD_PACK_HEADER DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_STANDARD_PACK_HEADER)

#define STATIC_KSDATAFORMAT_SUBTYPE_STANDARD_MPEG1_VIDEO		\
	0x36523b21,0x8ee5,0x11d1,0x8c,0xa3,0x00,0x60,0xb0,0x57,0x66,0x4a
DEFINE_GUIDSTRUCT("36523B21-8EE5-11d1-8CA3-0060B057664A",KSDATAFORMAT_SUBTYPE_STANDARD_MPEG1_VIDEO);
#define KSDATAFORMAT_SUBTYPE_STANDARD_MPEG1_VIDEO DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_STANDARD_MPEG1_VIDEO)

#define STATIC_KSDATAFORMAT_SUBTYPE_STANDARD_MPEG1_AUDIO		\
	0x36523b22,0x8ee5,0x11d1,0x8c,0xa3,0x00,0x60,0xb0,0x57,0x66,0x4a
DEFINE_GUIDSTRUCT("36523B22-8EE5-11d1-8CA3-0060B057664A",KSDATAFORMAT_SUBTYPE_STANDARD_MPEG1_AUDIO);
#define KSDATAFORMAT_SUBTYPE_STANDARD_MPEG1_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_STANDARD_MPEG1_AUDIO)

#define STATIC_KSDATAFORMAT_SUBTYPE_STANDARD_MPEG2_VIDEO		\
	0x36523b23,0x8ee5,0x11d1,0x8c,0xa3,0x00,0x60,0xb0,0x57,0x66,0x4a
DEFINE_GUIDSTRUCT("36523B23-8EE5-11d1-8CA3-0060B057664A",KSDATAFORMAT_SUBTYPE_STANDARD_MPEG2_VIDEO);
#define KSDATAFORMAT_SUBTYPE_STANDARD_MPEG2_VIDEO DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_STANDARD_MPEG2_VIDEO)

#define STATIC_KSDATAFORMAT_SUBTYPE_STANDARD_MPEG2_AUDIO		\
	0x36523b24,0x8ee5,0x11d1,0x8c,0xa3,0x00,0x60,0xb0,0x57,0x66,0x4a
DEFINE_GUIDSTRUCT("36523B24-8EE5-11d1-8CA3-0060B057664A",KSDATAFORMAT_SUBTYPE_STANDARD_MPEG2_AUDIO);
#define KSDATAFORMAT_SUBTYPE_STANDARD_MPEG2_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_STANDARD_MPEG2_AUDIO)

#define STATIC_KSDATAFORMAT_SUBTYPE_STANDARD_AC3_AUDIO			\
	0x36523b25,0x8ee5,0x11d1,0x8c,0xa3,0x00,0x60,0xb0,0x57,0x66,0x4a
DEFINE_GUIDSTRUCT("36523B25-8EE5-11d1-8CA3-0060B057664A",KSDATAFORMAT_SUBTYPE_STANDARD_AC3_AUDIO);
#define KSDATAFORMAT_SUBTYPE_STANDARD_AC3_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_STANDARD_AC3_AUDIO)

#define STATIC_KSDATAFORMAT_SPECIFIER_DIALECT_MPEG1_VIDEO		\
	0x36523b31,0x8ee5,0x11d1,0x8c,0xa3,0x00,0x60,0xb0,0x57,0x66,0x4a
DEFINE_GUIDSTRUCT("36523B31-8EE5-11d1-8CA3-0060B057664A",KSDATAFORMAT_SPECIFIER_DIALECT_MPEG1_VIDEO);
#define KSDATAFORMAT_SPECIFIER_DIALECT_MPEG1_VIDEO DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_DIALECT_MPEG1_VIDEO)

#define STATIC_KSDATAFORMAT_SPECIFIER_DIALECT_MPEG1_AUDIO		\
	0x36523b32,0x8ee5,0x11d1,0x8c,0xa3,0x00,0x60,0xb0,0x57,0x66,0x4a
DEFINE_GUIDSTRUCT("36523B32-8EE5-11d1-8CA3-0060B057664A",KSDATAFORMAT_SPECIFIER_DIALECT_MPEG1_AUDIO);
#define KSDATAFORMAT_SPECIFIER_DIALECT_MPEG1_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_DIALECT_MPEG1_AUDIO)

#define STATIC_KSDATAFORMAT_SPECIFIER_DIALECT_MPEG2_VIDEO		\
	0x36523b33,0x8ee5,0x11d1,0x8c,0xa3,0x00,0x60,0xb0,0x57,0x66,0x4a
DEFINE_GUIDSTRUCT("36523B33-8EE5-11d1-8CA3-0060B057664A",KSDATAFORMAT_SPECIFIER_DIALECT_MPEG2_VIDEO);
#define KSDATAFORMAT_SPECIFIER_DIALECT_MPEG2_VIDEO DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_DIALECT_MPEG2_VIDEO)

#define STATIC_KSDATAFORMAT_SPECIFIER_DIALECT_MPEG2_AUDIO		\
	0x36523b34,0x8ee5,0x11d1,0x8c,0xa3,0x00,0x60,0xb0,0x57,0x66,0x4a
DEFINE_GUIDSTRUCT("36523B34-8EE5-11d1-8CA3-0060B057664A",KSDATAFORMAT_SPECIFIER_DIALECT_MPEG2_AUDIO);
#define KSDATAFORMAT_SPECIFIER_DIALECT_MPEG2_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_DIALECT_MPEG2_AUDIO)

#define STATIC_KSDATAFORMAT_SPECIFIER_DIALECT_AC3_AUDIO			\
	0x36523b35,0x8ee5,0x11d1,0x8c,0xa3,0x00,0x60,0xb0,0x57,0x66,0x4a
DEFINE_GUIDSTRUCT("36523B35-8EE5-11d1-8CA3-0060B057664A",KSDATAFORMAT_SPECIFIER_DIALECT_AC3_AUDIO);
#define KSDATAFORMAT_SPECIFIER_DIALECT_AC3_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_DIALECT_AC3_AUDIO)

#define STATIC_KSDATAFORMAT_SUBTYPE_DSS_VIDEO				\
	0xa0af4f81,0xe163,0x11d0,0xba,0xd9,0x00,0x60,0x97,0x44,0x11,0x1a
DEFINE_GUIDSTRUCT("a0af4f81-e163-11d0-bad9-00609744111a",KSDATAFORMAT_SUBTYPE_DSS_VIDEO);
#define KSDATAFORMAT_SUBTYPE_DSS_VIDEO DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_DSS_VIDEO)

#define STATIC_KSDATAFORMAT_SUBTYPE_DSS_AUDIO				\
		0xa0af4f82,0xe163,0x11d0,0xba,0xd9,0x00,0x60,0x97,0x44,0x11,0x1a
DEFINE_GUIDSTRUCT("a0af4f82-e163-11d0-bad9-00609744111a",KSDATAFORMAT_SUBTYPE_DSS_AUDIO);
#define KSDATAFORMAT_SUBTYPE_DSS_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_DSS_AUDIO)

#define STATIC_KSDATAFORMAT_SUBTYPE_MPEG1Packet				\
	0xe436eb80,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70
DEFINE_GUIDSTRUCT("e436eb80-524f-11ce-9F53-0020af0ba770",KSDATAFORMAT_SUBTYPE_MPEG1Packet);
#define KSDATAFORMAT_SUBTYPE_MPEG1Packet DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_MPEG1Packet)

#define STATIC_KSDATAFORMAT_SUBTYPE_MPEG1Payload			\
	0xe436eb81,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70
DEFINE_GUIDSTRUCT("e436eb81-524f-11ce-9F53-0020af0ba770",KSDATAFORMAT_SUBTYPE_MPEG1Payload);
#define KSDATAFORMAT_SUBTYPE_MPEG1Payload DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_MPEG1Payload)

#define STATIC_KSDATAFORMAT_SUBTYPE_MPEG1Video				\
	0xe436eb86,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70
DEFINE_GUIDSTRUCT("e436eb86-524f-11ce-9f53-0020af0ba770",KSDATAFORMAT_SUBTYPE_MPEG1Video);
#define KSDATAFORMAT_SUBTYPE_MPEG1Video DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_MPEG1Video)

#define STATIC_KSDATAFORMAT_SPECIFIER_MPEG1_VIDEO			\
	0x05589f82,0xc356,0x11ce,0xbf,0x01,0x00,0xaa,0x00,0x55,0x59,0x5a
DEFINE_GUIDSTRUCT("05589f82-c356-11ce-bf01-00aa0055595a",KSDATAFORMAT_SPECIFIER_MPEG1_VIDEO);
#define KSDATAFORMAT_SPECIFIER_MPEG1_VIDEO DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_MPEG1_VIDEO)

#define STATIC_KSDATAFORMAT_TYPE_MPEG2_PES				\
	0xe06d8020,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d8020-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_TYPE_MPEG2_PES);
#define KSDATAFORMAT_TYPE_MPEG2_PES DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_MPEG2_PES)

#define STATIC_KSDATAFORMAT_TYPE_MPEG2_PROGRAM				\
	0xe06d8022,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d8022-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_TYPE_MPEG2_PROGRAM);
#define KSDATAFORMAT_TYPE_MPEG2_PROGRAM DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_MPEG2_PROGRAM)

#define STATIC_KSDATAFORMAT_TYPE_MPEG2_TRANSPORT			\
	0xe06d8023,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d8023-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_TYPE_MPEG2_TRANSPORT);
#define KSDATAFORMAT_TYPE_MPEG2_TRANSPORT DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_MPEG2_TRANSPORT)

#define STATIC_KSDATAFORMAT_SUBTYPE_MPEG2_VIDEO				\
	0xe06d8026,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d8026-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_SUBTYPE_MPEG2_VIDEO);
#define KSDATAFORMAT_SUBTYPE_MPEG2_VIDEO DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_MPEG2_VIDEO)

#define STATIC_KSDATAFORMAT_SPECIFIER_MPEG2_VIDEO			\
	0xe06d80e3,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d80e3-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_SPECIFIER_MPEG2_VIDEO);
#define KSDATAFORMAT_SPECIFIER_MPEG2_VIDEO DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_MPEG2_VIDEO)

#define STATIC_KSPROPSETID_Mpeg2Vid					\
	0xC8E11B60,0x0CC9,0x11D0,0xBD,0x69,0x00,0x35,0x05,0xC1,0x03,0xA9
DEFINE_GUIDSTRUCT("C8E11B60-0CC9-11D0-BD69-003505C103A9",KSPROPSETID_Mpeg2Vid);
#define KSPROPSETID_Mpeg2Vid DEFINE_GUIDNAMED(KSPROPSETID_Mpeg2Vid)

typedef enum {
  KSPROPERTY_MPEG2VID_MODES,
  KSPROPERTY_MPEG2VID_CUR_MODE,
  KSPROPERTY_MPEG2VID_4_3_RECT,
  KSPROPERTY_MPEG2VID_16_9_RECT,
  KSPROPERTY_MPEG2VID_16_9_PANSCAN
} KSPROPERTY_MPEG2VID;

#define KSMPEGVIDMODE_PANSCAN	0x0001
#define KSMPEGVIDMODE_LTRBOX	0x0002
#define KSMPEGVIDMODE_SCALE	0x0004

typedef struct _KSMPEGVID_RECT {
  ULONG StartX;
  ULONG StartY;
  ULONG EndX;
  ULONG EndY;
} KSMPEGVID_RECT,*PKSMPEGVID_RECT;

#define STATIC_KSDATAFORMAT_SUBTYPE_MPEG2_AUDIO				\
	0xe06d802b,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d802b-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_SUBTYPE_MPEG2_AUDIO);
#define KSDATAFORMAT_SUBTYPE_MPEG2_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_MPEG2_AUDIO)

#define STATIC_KSDATAFORMAT_SPECIFIER_MPEG2_AUDIO			\
	0xe06d80e5,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d80e5-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_SPECIFIER_MPEG2_AUDIO);
#define KSDATAFORMAT_SPECIFIER_MPEG2_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_MPEG2_AUDIO)

#define STATIC_KSDATAFORMAT_SUBTYPE_LPCM_AUDIO				\
	0xe06d8032,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d8032-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_SUBTYPE_LPCM_AUDIO);
#define KSDATAFORMAT_SUBTYPE_LPCM_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_LPCM_AUDIO)

#define STATIC_KSDATAFORMAT_SPECIFIER_LPCM_AUDIO			\
	0xe06d80e6,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d80e6-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_SPECIFIER_LPCM_AUDIO);
#define KSDATAFORMAT_SPECIFIER_LPCM_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_LPCM_AUDIO)

#define STATIC_KSDATAFORMAT_SUBTYPE_AC3_AUDIO				\
	0xe06d802c,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d802c-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_SUBTYPE_AC3_AUDIO);
#define KSDATAFORMAT_SUBTYPE_AC3_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_AC3_AUDIO)

#define STATIC_KSDATAFORMAT_SPECIFIER_AC3_AUDIO				\
	0xe06d80e4,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d80e4-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_SPECIFIER_AC3_AUDIO);
#define KSDATAFORMAT_SPECIFIER_AC3_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_AC3_AUDIO)

#define STATIC_KSPROPSETID_AC3						\
	0xBFABE720,0x6E1F,0x11D0,0xBC,0xF2,0x44,0x45,0x53,0x54,0x00,0x00
DEFINE_GUIDSTRUCT("BFABE720-6E1F-11D0-BCF2-************",KSPROPSETID_AC3);
#define KSPROPSETID_AC3 DEFINE_GUIDNAMED(KSPROPSETID_AC3)

typedef enum {
  KSPROPERTY_AC3_ERROR_CONCEALMENT = 1,
  KSPROPERTY_AC3_ALTERNATE_AUDIO,
  KSPROPERTY_AC3_DOWNMIX,
  KSPROPERTY_AC3_BIT_STREAM_MODE,
  KSPROPERTY_AC3_DIALOGUE_LEVEL,
  KSPROPERTY_AC3_LANGUAGE_CODE,
  KSPROPERTY_AC3_ROOM_TYPE
} KSPROPERTY_AC3;

typedef struct {
  WINBOOL fRepeatPreviousBlock;
  WINBOOL fErrorInCurrentBlock;
} KSAC3_ERROR_CONCEALMENT,*PKSAC3_ERROR_CONCEALMENT;

typedef struct {
  WINBOOL fStereo;
  ULONG DualMode;
} KSAC3_ALTERNATE_AUDIO,*PKSAC3_ALTERNATE_AUDIO;

#define KSAC3_ALTERNATE_AUDIO_1		1
#define KSAC3_ALTERNATE_AUDIO_2		2
#define KSAC3_ALTERNATE_AUDIO_BOTH	3

typedef struct {
  WINBOOL fDownMix;
  WINBOOL fDolbySurround;
} KSAC3_DOWNMIX,*PKSAC3_DOWNMIX;

typedef struct {
  LONG BitStreamMode;
} KSAC3_BIT_STREAM_MODE,*PKSAC3_BIT_STREAM_MODE;

#define KSAC3_SERVICE_MAIN_AUDIO	0
#define KSAC3_SERVICE_NO_DIALOG		1
#define KSAC3_SERVICE_VISUALLY_IMPAIRED	2
#define KSAC3_SERVICE_HEARING_IMPAIRED	3
#define KSAC3_SERVICE_DIALOG_ONLY	4
#define KSAC3_SERVICE_COMMENTARY	5
#define KSAC3_SERVICE_EMERGENCY_FLASH	6
#define KSAC3_SERVICE_VOICE_OVER	7

typedef struct {
  ULONG DialogueLevel;
} KSAC3_DIALOGUE_LEVEL,*PKSAC3_DIALOGUE_LEVEL;

typedef struct {
  WINBOOL fLargeRoom;
} KSAC3_ROOM_TYPE,*PKSAC3_ROOM_TYPE;

#define STATIC_KSDATAFORMAT_SUBTYPE_DTS_AUDIO				\
	0xe06d8033,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d8033-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_SUBTYPE_DTS_AUDIO);
#define KSDATAFORMAT_SUBTYPE_DTS_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_DTS_AUDIO)

#define STATIC_KSDATAFORMAT_SUBTYPE_SDDS_AUDIO				\
	0xe06d8034,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d8034-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_SUBTYPE_SDDS_AUDIO);
#define KSDATAFORMAT_SUBTYPE_SDDS_AUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_SDDS_AUDIO)

#define STATIC_KSPROPSETID_AudioDecoderOut				\
	0x6ca6e020,0x43bd,0x11d0,0xbd,0x6a,0x00,0x35,0x05,0xc1,0x03,0xa9
DEFINE_GUIDSTRUCT("6ca6e020-43bd-11d0-bd6a-003505c103a9",KSPROPSETID_AudioDecoderOut);
#define KSPROPSETID_AudioDecoderOut DEFINE_GUIDNAMED(KSPROPSETID_AudioDecoderOut)

typedef enum {
  KSPROPERTY_AUDDECOUT_MODES,
  KSPROPERTY_AUDDECOUT_CUR_MODE
} KSPROPERTY_AUDDECOUT;

#define KSAUDDECOUTMODE_STEREO_ANALOG	0x0001
#define KSAUDDECOUTMODE_PCM_51		0x0002
#define KSAUDDECOUTMODE_SPDIFF		0x0004

#define STATIC_KSDATAFORMAT_SUBTYPE_SUBPICTURE				\
	0xe06d802d,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea
DEFINE_GUIDSTRUCT("e06d802d-db46-11cf-b4d1-00805f6cbbea",KSDATAFORMAT_SUBTYPE_SUBPICTURE);
#define KSDATAFORMAT_SUBTYPE_SUBPICTURE DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_SUBPICTURE)

#define STATIC_KSPROPSETID_DvdSubPic					\
	0xac390460,0x43af,0x11d0,0xbd,0x6a,0x00,0x35,0x05,0xc1,0x03,0xa9
DEFINE_GUIDSTRUCT("ac390460-43af-11d0-bd6a-003505c103a9",KSPROPSETID_DvdSubPic);
#define KSPROPSETID_DvdSubPic DEFINE_GUIDNAMED(KSPROPSETID_DvdSubPic)

typedef enum {
  KSPROPERTY_DVDSUBPIC_PALETTE,
  KSPROPERTY_DVDSUBPIC_HLI,
  KSPROPERTY_DVDSUBPIC_COMPOSIT_ON
} KSPROPERTY_DVDSUBPIC;

typedef struct _KS_DVD_YCrCb {
  UCHAR Reserved;
  UCHAR Y;
  UCHAR Cr;
  UCHAR Cb;
} KS_DVD_YCrCb,*PKS_DVD_YCrCb;

typedef struct _KS_DVD_YUV {
  UCHAR Reserved;
  UCHAR Y;
  UCHAR V;
  UCHAR U;
} KS_DVD_YUV,*PKS_DVD_YUV;

typedef struct _KSPROPERTY_SPPAL {
  KS_DVD_YUV sppal[16];
} KSPROPERTY_SPPAL,*PKSPROPERTY_SPPAL;

typedef struct _KS_COLCON {
  UCHAR emph1col:4;
  UCHAR emph2col:4;
  UCHAR backcol:4;
  UCHAR patcol:4;
  UCHAR emph1con:4;
  UCHAR emph2con:4;
  UCHAR backcon:4;
  UCHAR patcon:4;
} KS_COLCON,*PKS_COLCON;

typedef struct _KSPROPERTY_SPHLI {
  USHORT HLISS;
  USHORT Reserved;
  ULONG StartPTM;
  ULONG EndPTM;
  USHORT StartX;
  USHORT StartY;
  USHORT StopX;
  USHORT StopY;
  KS_COLCON ColCon;
} KSPROPERTY_SPHLI,*PKSPROPERTY_SPHLI;

typedef WINBOOL KSPROPERTY_COMPOSIT_ON,*PKSPROPERTY_COMPOSIT_ON;

#define STATIC_KSPROPSETID_CopyProt					\
	0x0E8A0A40,0x6AEF,0x11D0,0x9E,0xD0,0x00,0xA0,0x24,0xCA,0x19,0xB3
DEFINE_GUIDSTRUCT("0E8A0A40-6AEF-11D0-9ED0-00A024CA19B3",KSPROPSETID_CopyProt);
#define KSPROPSETID_CopyProt DEFINE_GUIDNAMED(KSPROPSETID_CopyProt)

typedef enum {
  KSPROPERTY_DVDCOPY_CHLG_KEY = 0x01,
  KSPROPERTY_DVDCOPY_DVD_KEY1,
  KSPROPERTY_DVDCOPY_DEC_KEY2,
  KSPROPERTY_DVDCOPY_TITLE_KEY,
  KSPROPERTY_COPY_MACROVISION,
  KSPROPERTY_DVDCOPY_REGION,
  KSPROPERTY_DVDCOPY_SET_COPY_STATE,
  KSPROPERTY_DVDCOPY_DISC_KEY = 0x80
} KSPROPERTY_COPYPROT;

typedef struct _KS_DVDCOPY_CHLGKEY {
  BYTE ChlgKey[10];
  BYTE Reserved[2];
} KS_DVDCOPY_CHLGKEY,*PKS_DVDCOPY_CHLGKEY;

typedef struct _KS_DVDCOPY_BUSKEY {
  BYTE BusKey[5];
  BYTE Reserved[1];
} KS_DVDCOPY_BUSKEY,*PKS_DVDCOPY_BUSKEY;

typedef struct _KS_DVDCOPY_DISCKEY {
  BYTE DiscKey[2048];
} KS_DVDCOPY_DISCKEY,*PKS_DVDCOPY_DISCKEY;

typedef struct _KS_DVDCOPY_REGION {
  UCHAR Reserved;
  UCHAR RegionData;
  UCHAR Reserved2[2];
} KS_DVDCOPY_REGION,*PKS_DVDCOPY_REGION;

typedef struct _KS_DVDCOPY_TITLEKEY {
  ULONG KeyFlags;
  ULONG ReservedNT[2];
  UCHAR TitleKey[6];
  UCHAR Reserved[2];
} KS_DVDCOPY_TITLEKEY,*PKS_DVDCOPY_TITLEKEY;

typedef struct _KS_COPY_MACROVISION {
  ULONG MACROVISIONLevel;
} KS_COPY_MACROVISION,*PKS_COPY_MACROVISION;

typedef struct _KS_DVDCOPY_SET_COPY_STATE {
  ULONG DVDCopyState;
} KS_DVDCOPY_SET_COPY_STATE,*PKS_DVDCOPY_SET_COPY_STATE;

typedef enum {
  KS_DVDCOPYSTATE_INITIALIZE,
  KS_DVDCOPYSTATE_INITIALIZE_TITLE,
  KS_DVDCOPYSTATE_AUTHENTICATION_NOT_REQUIRED,
  KS_DVDCOPYSTATE_AUTHENTICATION_REQUIRED,
  KS_DVDCOPYSTATE_DONE
} KS_DVDCOPYSTATE;

typedef enum {
  KS_MACROVISION_DISABLED,
  KS_MACROVISION_LEVEL1,
  KS_MACROVISION_LEVEL2,
  KS_MACROVISION_LEVEL3
} KS_COPY_MACROVISION_LEVEL,*PKS_COPY_MACROVISION_LEVEL;

#define KS_DVD_CGMS_RESERVED_MASK	0x00000078

#define KS_DVD_CGMS_COPY_PROTECT_MASK	0x00000018
#define KS_DVD_CGMS_COPY_PERMITTED	0x00000000
#define KS_DVD_CGMS_COPY_ONCE		0x00000010
#define KS_DVD_CGMS_NO_COPY		0x00000018

#define KS_DVD_COPYRIGHT_MASK		0x00000040
#define KS_DVD_NOT_COPYRIGHTED		0x00000000
#define KS_DVD_COPYRIGHTED		0x00000040

#define KS_DVD_SECTOR_PROTECT_MASK	0x00000020
#define KS_DVD_SECTOR_NOT_PROTECTED	0x00000000
#define KS_DVD_SECTOR_PROTECTED		0x00000020

#define STATIC_KSCATEGORY_TVTUNER					\
	0xa799a800,0xa46d,0x11d0,0xa1,0x8c,0x00,0xa0,0x24,0x01,0xdc,0xd4
DEFINE_GUIDSTRUCT("a799a800-a46d-11d0-a18c-00a02401dcd4",KSCATEGORY_TVTUNER);
#define KSCATEGORY_TVTUNER DEFINE_GUIDNAMED(KSCATEGORY_TVTUNER)

#define STATIC_KSCATEGORY_CROSSBAR					\
	0xa799a801,0xa46d,0x11d0,0xa1,0x8c,0x00,0xa0,0x24,0x01,0xdc,0xd4
DEFINE_GUIDSTRUCT("a799a801-a46d-11d0-a18c-00a02401dcd4",KSCATEGORY_CROSSBAR);
#define KSCATEGORY_CROSSBAR DEFINE_GUIDNAMED(KSCATEGORY_CROSSBAR)

#define STATIC_KSCATEGORY_TVAUDIO					\
	0xa799a802,0xa46d,0x11d0,0xa1,0x8c,0x00,0xa0,0x24,0x01,0xdc,0xd4
DEFINE_GUIDSTRUCT("a799a802-a46d-11d0-a18c-00a02401dcd4",KSCATEGORY_TVAUDIO);
#define KSCATEGORY_TVAUDIO DEFINE_GUIDNAMED(KSCATEGORY_TVAUDIO)

#define STATIC_KSCATEGORY_VPMUX						\
	0xa799a803,0xa46d,0x11d0,0xa1,0x8c,0x00,0xa0,0x24,0x01,0xdc,0xd4
DEFINE_GUIDSTRUCT("a799a803-a46d-11d0-a18c-00a02401dcd4",KSCATEGORY_VPMUX);
#define KSCATEGORY_VPMUX DEFINE_GUIDNAMED(KSCATEGORY_VPMUX)

#define STATIC_KSCATEGORY_VBICODEC					\
	0x07dad660,0x22f1,0x11d1,0xa9,0xf4,0x00,0xc0,0x4f,0xbb,0xde,0x8f
DEFINE_GUIDSTRUCT("07dad660-22f1-11d1-a9f4-00c04fbbde8f",KSCATEGORY_VBICODEC);
#define KSCATEGORY_VBICODEC DEFINE_GUIDNAMED(KSCATEGORY_VBICODEC)

#define STATIC_KSDATAFORMAT_SUBTYPE_VPVideo				\
	0x5a9b6a40,0x1a22,0x11d1,0xba,0xd9,0x0,0x60,0x97,0x44,0x11,0x1a
DEFINE_GUIDSTRUCT("5a9b6a40-1a22-11d1-bad9-00609744111a",KSDATAFORMAT_SUBTYPE_VPVideo);
#define KSDATAFORMAT_SUBTYPE_VPVideo DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_VPVideo)

#define STATIC_KSDATAFORMAT_SUBTYPE_VPVBI				\
	0x5a9b6a41,0x1a22,0x11d1,0xba,0xd9,0x0,0x60,0x97,0x44,0x11,0x1a
DEFINE_GUIDSTRUCT("5a9b6a41-1a22-11d1-bad9-00609744111a",KSDATAFORMAT_SUBTYPE_VPVBI);
#define KSDATAFORMAT_SUBTYPE_VPVBI DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_VPVBI)

#define STATIC_KSDATAFORMAT_SPECIFIER_VIDEOINFO				\
	0x05589f80,0xc356,0x11ce,0xbf,0x01,0x00,0xaa,0x00,0x55,0x59,0x5a
DEFINE_GUIDSTRUCT("05589f80-c356-11ce-bf01-00aa0055595a",KSDATAFORMAT_SPECIFIER_VIDEOINFO);
#define KSDATAFORMAT_SPECIFIER_VIDEOINFO DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_VIDEOINFO)

#define STATIC_KSDATAFORMAT_SPECIFIER_VIDEOINFO2			\
	0xf72a76A0,0xeb0a,0x11d0,0xac,0xe4,0x00,0x00,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("f72a76A0-eb0a-11d0-ace4-0000c0cc16ba",KSDATAFORMAT_SPECIFIER_VIDEOINFO2);
#define KSDATAFORMAT_SPECIFIER_VIDEOINFO2 DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_VIDEOINFO2)

#define STATIC_KSDATAFORMAT_TYPE_ANALOGVIDEO				\
	0x0482dde1,0x7817,0x11cf,0x8a,0x03,0x00,0xaa,0x00,0x6e,0xcb,0x65
DEFINE_GUIDSTRUCT("0482dde1-7817-11cf-8a03-00aa006ecb65",KSDATAFORMAT_TYPE_ANALOGVIDEO);
#define KSDATAFORMAT_TYPE_ANALOGVIDEO DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_ANALOGVIDEO)

#define STATIC_KSDATAFORMAT_SPECIFIER_ANALOGVIDEO			\
	0x0482dde0,0x7817,0x11cf,0x8a,0x03,0x00,0xaa,0x00,0x6e,0xcb,0x65
DEFINE_GUIDSTRUCT("0482dde0-7817-11cf-8a03-00aa006ecb65",KSDATAFORMAT_SPECIFIER_ANALOGVIDEO);
#define KSDATAFORMAT_SPECIFIER_ANALOGVIDEO DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_ANALOGVIDEO)

#define STATIC_KSDATAFORMAT_TYPE_ANALOGAUDIO				\
	0x0482dee1,0x7817,0x11cf,0x8a,0x03,0x00,0xaa,0x00,0x6e,0xcb,0x65
DEFINE_GUIDSTRUCT("0482DEE1-7817-11cf-8a03-00aa006ecb65",KSDATAFORMAT_TYPE_ANALOGAUDIO);
#define KSDATAFORMAT_TYPE_ANALOGAUDIO DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_ANALOGAUDIO)

#define STATIC_KSDATAFORMAT_SPECIFIER_VBI				\
	0xf72a76e0,0xeb0a,0x11d0,0xac,0xe4,0x00,0x00,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("f72a76e0-eb0a-11d0-ace4-0000c0cc16ba",KSDATAFORMAT_SPECIFIER_VBI);
#define KSDATAFORMAT_SPECIFIER_VBI DEFINE_GUIDNAMED(KSDATAFORMAT_SPECIFIER_VBI)

#define STATIC_KSDATAFORMAT_TYPE_VBI					\
	0xf72a76e1,0xeb0a,0x11d0,0xac,0xe4,0x00,0x00,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("f72a76e1-eb0a-11d0-ace4-0000c0cc16ba",KSDATAFORMAT_TYPE_VBI);
#define KSDATAFORMAT_TYPE_VBI DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_VBI)

#define STATIC_KSDATAFORMAT_SUBTYPE_RAW8				\
	0xca20d9a0,0x3e3e,0x11d1,0x9b,0xf9,0x0,0xc0,0x4f,0xbb,0xde,0xbf
DEFINE_GUIDSTRUCT("ca20d9a0-3e3e-11d1-9bf9-00c04fbbdebf",KSDATAFORMAT_SUBTYPE_RAW8);
#define KSDATAFORMAT_SUBTYPE_RAW8 DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_RAW8)

#define STATIC_KSDATAFORMAT_SUBTYPE_CC					\
	0x33214cc1,0x11f,0x11d2,0xb4,0xb1,0x0,0xa0,0xd1,0x2,0xcf,0xbe
DEFINE_GUIDSTRUCT("33214CC1-011F-11D2-B4B1-00A0D102CFBE",KSDATAFORMAT_SUBTYPE_CC);
#define KSDATAFORMAT_SUBTYPE_CC DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_CC)

#define STATIC_KSDATAFORMAT_SUBTYPE_NABTS				\
	0xf72a76e2,0xeb0a,0x11d0,0xac,0xe4,0x00,0x00,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("f72a76e2-eb0a-11d0-ace4-0000c0cc16ba",KSDATAFORMAT_SUBTYPE_NABTS);
#define KSDATAFORMAT_SUBTYPE_NABTS DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_NABTS)

#define STATIC_KSDATAFORMAT_SUBTYPE_TELETEXT				\
	0xf72a76e3,0xeb0a,0x11d0,0xac,0xe4,0x00,0x00,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("f72a76e3-eb0a-11d0-ace4-0000c0cc16ba",KSDATAFORMAT_SUBTYPE_TELETEXT);
#define KSDATAFORMAT_SUBTYPE_TELETEXT DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_TELETEXT)

#define KS_BI_RGB		__MSABI_LONG(0)
#define KS_BI_RLE8		__MSABI_LONG(1)
#define KS_BI_RLE4		__MSABI_LONG(2)
#define KS_BI_BITFIELDS		__MSABI_LONG(3)

typedef struct tagKS_RGBQUAD {
  BYTE rgbBlue;
  BYTE rgbGreen;
  BYTE rgbRed;
  BYTE rgbReserved;
} KS_RGBQUAD,*PKS_RGBQUAD;

#define KS_iPALETTE_COLORS	256
#define KS_iEGA_COLORS		16
#define KS_iMASK_COLORS		3
#define KS_iTRUECOLOR		16
#define KS_iRED			0
#define KS_iGREEN		1
#define KS_iBLUE		2
#define KS_iPALETTE		8
#define KS_iMAXBITS		8
#define KS_SIZE_EGA_PALETTE	(KS_iEGA_COLORS *sizeof(KS_RGBQUAD))
#define KS_SIZE_PALETTE		(KS_iPALETTE_COLORS *sizeof(KS_RGBQUAD))

typedef struct tagKS_BITMAPINFOHEADER {
  DWORD biSize;
  LONG biWidth;
  LONG biHeight;
  WORD biPlanes;
  WORD biBitCount;
  DWORD biCompression;
  DWORD biSizeImage;
  LONG biXPelsPerMeter;
  LONG biYPelsPerMeter;
  DWORD biClrUsed;
  DWORD biClrImportant;
} KS_BITMAPINFOHEADER,*PKS_BITMAPINFOHEADER;

typedef struct tag_KS_TRUECOLORINFO {
  DWORD dwBitMasks[KS_iMASK_COLORS];
  KS_RGBQUAD bmiColors[KS_iPALETTE_COLORS];
} KS_TRUECOLORINFO,*PKS_TRUECOLORINFO;

#define KS_WIDTHBYTES(bits)	((DWORD)(((bits)+31) & (~31)) / 8)
#define KS_DIBWIDTHBYTES(bi)	(DWORD)KS_WIDTHBYTES((DWORD)(bi).biWidth *(DWORD)(bi).biBitCount)
#define KS__DIBSIZE(bi)		(KS_DIBWIDTHBYTES(bi) *(DWORD)(bi).biHeight)
#define KS_DIBSIZE(bi)		((bi).biHeight < 0 ? (-1)*(KS__DIBSIZE(bi)) : KS__DIBSIZE(bi))

#ifndef __REFERENCE_TIME_DEFINED
#define __REFERENCE_TIME_DEFINED
typedef LONGLONG REFERENCE_TIME;
#endif /*__REFERENCE_TIME_DEFINED*/

typedef struct tagKS_VIDEOINFOHEADER {
  RECT rcSource;
  RECT rcTarget;
  DWORD dwBitRate;
  DWORD dwBitErrorRate;
  REFERENCE_TIME AvgTimePerFrame;
  KS_BITMAPINFOHEADER bmiHeader;
} KS_VIDEOINFOHEADER,*PKS_VIDEOINFOHEADER;

typedef struct tagKS_VIDEOINFO {
  RECT rcSource;
  RECT rcTarget;
  DWORD dwBitRate;
  DWORD dwBitErrorRate;
  REFERENCE_TIME AvgTimePerFrame;
  KS_BITMAPINFOHEADER bmiHeader;
  __C89_NAMELESS union {
    KS_RGBQUAD bmiColors[KS_iPALETTE_COLORS];
    DWORD dwBitMasks[KS_iMASK_COLORS];
    KS_TRUECOLORINFO TrueColorInfo;
  };
} KS_VIDEOINFO,*PKS_VIDEOINFO;

#define KS_SIZE_MASKS			(KS_iMASK_COLORS *sizeof(DWORD))
#define KS_SIZE_PREHEADER		(FIELD_OFFSET(KS_VIDEOINFOHEADER,bmiHeader))

#define KS_SIZE_VIDEOHEADER(pbmi)	((pbmi)->bmiHeader.biSize + KS_SIZE_PREHEADER)

typedef struct tagKS_VBIINFOHEADER {
  ULONG StartLine;
  ULONG EndLine;
  ULONG SamplingFrequency;
  ULONG MinLineStartTime;
  ULONG MaxLineStartTime;
  ULONG ActualLineStartTime;
  ULONG ActualLineEndTime;
  ULONG VideoStandard;
  ULONG SamplesPerLine;
  ULONG StrideInBytes;
  ULONG BufferSize;
} KS_VBIINFOHEADER,*PKS_VBIINFOHEADER;

#define KS_VBIDATARATE_NABTS		(__MSABI_LONG(5727272))
#define KS_VBIDATARATE_CC		(__MSABI_LONG(503493))
#define KS_VBISAMPLINGRATE_4X_NABTS	((__LONG32)(4*KS_VBIDATARATE_NABTS))
#define KS_VBISAMPLINGRATE_47X_NABTS	(__MSABI_LONG(27000000))
#define KS_VBISAMPLINGRATE_5X_NABTS	((__LONG32)(5*KS_VBIDATARATE_NABTS))

#define KS_47NABTS_SCALER		(KS_VBISAMPLINGRATE_47X_NABTS/(double)KS_VBIDATARATE_NABTS)

typedef struct tagKS_AnalogVideoInfo {
  RECT rcSource;
  RECT rcTarget;
  DWORD dwActiveWidth;
  DWORD dwActiveHeight;
  REFERENCE_TIME AvgTimePerFrame;
} KS_ANALOGVIDEOINFO,*PKS_ANALOGVIDEOINFO;

#define KS_TVTUNER_CHANGE_BEGIN_TUNE	__MSABI_LONG(0x0001)
#define KS_TVTUNER_CHANGE_END_TUNE	__MSABI_LONG(0x0002)

typedef struct tagKS_TVTUNER_CHANGE_INFO {
  DWORD dwFlags;
  DWORD dwCountryCode;
  DWORD dwAnalogVideoStandard;
  DWORD dwChannel;
} KS_TVTUNER_CHANGE_INFO,*PKS_TVTUNER_CHANGE_INFO;

typedef enum {
  KS_MPEG2Level_Low,
  KS_MPEG2Level_Main,
  KS_MPEG2Level_High1440,
  KS_MPEG2Level_High
} KS_MPEG2Level;

typedef enum {
  KS_MPEG2Profile_Simple,
  KS_MPEG2Profile_Main,
  KS_MPEG2Profile_SNRScalable,
  KS_MPEG2Profile_SpatiallyScalable,
  KS_MPEG2Profile_High
} KS_MPEG2Profile;

#define KS_INTERLACE_IsInterlaced		0x00000001
#define KS_INTERLACE_1FieldPerSample		0x00000002
#define KS_INTERLACE_Field1First		0x00000004
#define KS_INTERLACE_UNUSED			0x00000008
#define KS_INTERLACE_FieldPatternMask		0x00000030
#define KS_INTERLACE_FieldPatField1Only		0x00000000
#define KS_INTERLACE_FieldPatField2Only		0x00000010
#define KS_INTERLACE_FieldPatBothRegular	0x00000020
#define KS_INTERLACE_FieldPatBothIrregular	0x00000030
#define KS_INTERLACE_DisplayModeMask		0x000000c0
#define KS_INTERLACE_DisplayModeBobOnly		0x00000000
#define KS_INTERLACE_DisplayModeWeaveOnly	0x00000040
#define KS_INTERLACE_DisplayModeBobOrWeave	0x00000080

#define KS_MPEG2_DoPanScan			0x00000001
#define KS_MPEG2_DVDLine21Field1		0x00000002
#define KS_MPEG2_DVDLine21Field2		0x00000004
#define KS_MPEG2_SourceIsLetterboxed		0x00000008
#define KS_MPEG2_FilmCameraMode			0x00000010
#define KS_MPEG2_LetterboxAnalogOut		0x00000020
#define KS_MPEG2_DSS_UserData			0x00000040
#define KS_MPEG2_DVB_UserData			0x00000080
#define KS_MPEG2_27MhzTimebase			0x00000100

typedef struct tagKS_VIDEOINFOHEADER2 {
  RECT rcSource;
  RECT rcTarget;
  DWORD dwBitRate;
  DWORD dwBitErrorRate;
  REFERENCE_TIME AvgTimePerFrame;
  DWORD dwInterlaceFlags;
  DWORD dwCopyProtectFlags;
  DWORD dwPictAspectRatioX;
  DWORD dwPictAspectRatioY;
  DWORD dwReserved1;
  DWORD dwReserved2;
  KS_BITMAPINFOHEADER bmiHeader;
} KS_VIDEOINFOHEADER2,*PKS_VIDEOINFOHEADER2;

typedef struct tagKS_MPEG1VIDEOINFO {
  KS_VIDEOINFOHEADER hdr;
  DWORD dwStartTimeCode;
  DWORD cbSequenceHeader;
  BYTE bSequenceHeader[1];
} KS_MPEG1VIDEOINFO,*PKS_MPEG1VIDEOINFO;

#define KS_MAX_SIZE_MPEG1_SEQUENCE_INFO	140
#define KS_SIZE_MPEG1VIDEOINFO(pv)	(FIELD_OFFSET(KS_MPEG1VIDEOINFO,bSequenceHeader[0]) + (pv)->cbSequenceHeader)
#define KS_MPEG1_SEQUENCE_INFO(pv)	((const BYTE *)(pv)->bSequenceHeader)

typedef struct tagKS_MPEGVIDEOINFO2 {
  KS_VIDEOINFOHEADER2 hdr;
  DWORD dwStartTimeCode;
  DWORD cbSequenceHeader;
  DWORD dwProfile;
  DWORD dwLevel;
  DWORD dwFlags;
  DWORD bSequenceHeader[1];
} KS_MPEGVIDEOINFO2,*PKS_MPEGVIDEOINFO2;

#define KS_SIZE_MPEGVIDEOINFO2(pv)	(FIELD_OFFSET(KS_MPEGVIDEOINFO2,bSequenceHeader[0]) + (pv)->cbSequenceHeader)
#define KS_MPEG1_SEQUENCE_INFO(pv)	((const BYTE *)(pv)->bSequenceHeader)

#define KS_MPEGAUDIOINFO_27MhzTimebase	0x00000001

typedef struct tagKS_MPEAUDIOINFO {
  DWORD dwFlags;
  DWORD dwReserved1;
  DWORD dwReserved2;
  DWORD dwReserved3;
} KS_MPEGAUDIOINFO,*PKS_MPEGAUDIOINFO;

typedef struct tagKS_DATAFORMAT_VIDEOINFOHEADER {
  KSDATAFORMAT DataFormat;
  KS_VIDEOINFOHEADER VideoInfoHeader;
} KS_DATAFORMAT_VIDEOINFOHEADER,*PKS_DATAFORMAT_VIDEOINFOHEADER;

typedef struct tagKS_DATAFORMAT_VIDEOINFOHEADER2 {
  KSDATAFORMAT DataFormat;
  KS_VIDEOINFOHEADER2 VideoInfoHeader2;
} KS_DATAFORMAT_VIDEOINFOHEADER2,*PKS_DATAFORMAT_VIDEOINFOHEADER2;

typedef struct tagKS_DATAFORMAT_VIDEOINFO_PALETTE {
  KSDATAFORMAT DataFormat;
  KS_VIDEOINFO VideoInfo;
} KS_DATAFORMAT_VIDEOINFO_PALETTE,*PKS_DATAFORMAT_VIDEOINFO_PALETTE;

typedef struct tagKS_DATAFORMAT_VBIINFOHEADER {
  KSDATAFORMAT DataFormat;
  KS_VBIINFOHEADER VBIInfoHeader;
} KS_DATAFORMAT_VBIINFOHEADER,*PKS_DATAFORMAT_VBIINFOHEADER;

typedef struct _KS_VIDEO_STREAM_CONFIG_CAPS {
  GUID guid;
  ULONG VideoStandard;
  SIZE InputSize;
  SIZE MinCroppingSize;
  SIZE MaxCroppingSize;
  int CropGranularityX;
  int CropGranularityY;
  int CropAlignX;
  int CropAlignY;
  SIZE MinOutputSize;
  SIZE MaxOutputSize;
  int OutputGranularityX;
  int OutputGranularityY;
  int StretchTapsX;
  int StretchTapsY;
  int ShrinkTapsX;
  int ShrinkTapsY;
  LONGLONG MinFrameInterval;
  LONGLONG MaxFrameInterval;
  LONG MinBitsPerSecond;
  LONG MaxBitsPerSecond;
} KS_VIDEO_STREAM_CONFIG_CAPS,*PKS_VIDEO_STREAM_CONFIG_CAPS;

typedef struct tagKS_DATARANGE_VIDEO {
  KSDATARANGE DataRange;
  WINBOOL bFixedSizeSamples;
  WINBOOL bTemporalCompression;
  DWORD StreamDescriptionFlags;
  DWORD MemoryAllocationFlags;
  KS_VIDEO_STREAM_CONFIG_CAPS ConfigCaps;
  KS_VIDEOINFOHEADER VideoInfoHeader;
} KS_DATARANGE_VIDEO,*PKS_DATARANGE_VIDEO;

typedef struct tagKS_DATARANGE_VIDEO2 {
  KSDATARANGE DataRange;
  WINBOOL bFixedSizeSamples;
  WINBOOL bTemporalCompression;
  DWORD StreamDescriptionFlags;
  DWORD MemoryAllocationFlags;
  KS_VIDEO_STREAM_CONFIG_CAPS ConfigCaps;
  KS_VIDEOINFOHEADER2 VideoInfoHeader;
} KS_DATARANGE_VIDEO2,*PKS_DATARANGE_VIDEO2;

typedef struct tagKS_DATARANGE_MPEG1_VIDEO {
  KSDATARANGE DataRange;
  WINBOOL bFixedSizeSamples;
  WINBOOL bTemporalCompression;
  DWORD StreamDescriptionFlags;
  DWORD MemoryAllocationFlags;
  KS_VIDEO_STREAM_CONFIG_CAPS ConfigCaps;
  KS_MPEG1VIDEOINFO VideoInfoHeader;
} KS_DATARANGE_MPEG1_VIDEO,*PKS_DATARANGE_MPEG1_VIDEO;

typedef struct tagKS_DATARANGE_MPEG2_VIDEO {
  KSDATARANGE DataRange;
  WINBOOL bFixedSizeSamples;
  WINBOOL bTemporalCompression;
  DWORD StreamDescriptionFlags;
  DWORD MemoryAllocationFlags;
  KS_VIDEO_STREAM_CONFIG_CAPS ConfigCaps;
  KS_MPEGVIDEOINFO2 VideoInfoHeader;
} KS_DATARANGE_MPEG2_VIDEO,*PKS_DATARANGE_MPEG2_VIDEO;

typedef struct tagKS_DATARANGE_VIDEO_PALETTE {
  KSDATARANGE DataRange;
  WINBOOL bFixedSizeSamples;
  WINBOOL bTemporalCompression;
  DWORD StreamDescriptionFlags;
  DWORD MemoryAllocationFlags;
  KS_VIDEO_STREAM_CONFIG_CAPS ConfigCaps;
  KS_VIDEOINFO VideoInfo;
} KS_DATARANGE_VIDEO_PALETTE,*PKS_DATARANGE_VIDEO_PALETTE;

typedef struct tagKS_DATARANGE_VIDEO_VBI {
  KSDATARANGE DataRange;
  WINBOOL bFixedSizeSamples;
  WINBOOL bTemporalCompression;
  DWORD StreamDescriptionFlags;
  DWORD MemoryAllocationFlags;
  KS_VIDEO_STREAM_CONFIG_CAPS ConfigCaps;
  KS_VBIINFOHEADER VBIInfoHeader;
} KS_DATARANGE_VIDEO_VBI,*PKS_DATARANGE_VIDEO_VBI;

typedef struct tagKS_DATARANGE_ANALOGVIDEO {
  KSDATARANGE DataRange;
  KS_ANALOGVIDEOINFO AnalogVideoInfo;
} KS_DATARANGE_ANALOGVIDEO,*PKS_DATARANGE_ANALOGVIDEO;

#define KS_VIDEOSTREAM_PREVIEW		0x0001
#define KS_VIDEOSTREAM_CAPTURE		0x0002
#define KS_VIDEOSTREAM_VBI		0x0010
#define KS_VIDEOSTREAM_NABTS		0x0020
#define KS_VIDEOSTREAM_CC		0x0100
#define KS_VIDEOSTREAM_EDS		0x0200
#define KS_VIDEOSTREAM_TELETEXT		0x0400
#define KS_VIDEOSTREAM_STILL		0x1000
#define KS_VIDEOSTREAM_IS_VPE		0x8000

#define KS_VIDEO_ALLOC_VPE_SYSTEM	0x0001
#define KS_VIDEO_ALLOC_VPE_DISPLAY	0x0002
#define KS_VIDEO_ALLOC_VPE_AGP		0x0004

#define STATIC_KSPROPSETID_VBICAP_PROPERTIES				\
	0xf162c607,0x7b35,0x496f,0xad,0x7f,0x2d,0xca,0x3b,0x46,0xb7,0x18
DEFINE_GUIDSTRUCT("F162C607-7B35-496f-AD7F-2DCA3B46B718",KSPROPSETID_VBICAP_PROPERTIES);
#define KSPROPSETID_VBICAP_PROPERTIES DEFINE_GUIDNAMED(KSPROPSETID_VBICAP_PROPERTIES)

typedef enum {
  KSPROPERTY_VBICAP_PROPERTIES_PROTECTION = 0x01
} KSPROPERTY_VBICAP;

typedef struct _VBICAP_PROPERTIES_PROTECTION_S {
  KSPROPERTY Property;
  ULONG StreamIndex;
  ULONG Status;
} VBICAP_PROPERTIES_PROTECTION_S,*PVBICAP_PROPERTIES_PROTECTION_S;

#define KS_VBICAP_PROTECTION_MV_PRESENT				__MSABI_LONG(0x0001)
#define KS_VBICAP_PROTECTION_MV_HARDWARE			__MSABI_LONG(0x0002)
#define KS_VBICAP_PROTECTION_MV_DETECTED			__MSABI_LONG(0x0004)

#define KS_NABTS_GROUPID_ORIGINAL_CONTENT_BASE			0x800
#define KS_NABTS_GROUPID_ORIGINAL_CONTENT_ADVERTISER_BASE	0x810

#define KS_NABTS_GROUPID_PRODUCTION_COMPANY_CONTENT_BASE	0x820
#define KS_NABTS_GROUPID_PRODUCTION_COMPANY_ADVERTISER_BASE	0x830

#define KS_NABTS_GROUPID_SYNDICATED_SHOW_CONTENT_BASE		0x840
#define KS_NABTS_GROUPID_SYNDICATED_SHOW_ADVERTISER_BASE	0x850

#define KS_NABTS_GROUPID_NETWORK_WIDE_CONTENT_BASE		0x860
#define KS_NABTS_GROUPID_NETWORK_WIDE_ADVERTISER_BASE		0x870

#define KS_NABTS_GROUPID_TELEVISION_STATION_CONTENT_BASE	0x880
#define KS_NABTS_GROUPID_TELEVISION_STATION_ADVERTISER_BASE	0x890

#define KS_NABTS_GROUPID_LOCAL_CABLE_SYSTEM_CONTENT_BASE	0x8A0
#define KS_NABTS_GROUPID_LOCAL_CABLE_SYSTEM_ADVERTISER_BASE	0x8B0

#define KS_NABTS_GROUPID_MICROSOFT_RESERVED_TEST_DATA_BASE	0x8F0

#define STATIC_KSDATAFORMAT_TYPE_NABTS					\
	0xe757bca0,0x39ac,0x11d1,0xa9,0xf5,0x0,0xc0,0x4f,0xbb,0xde,0x8f
DEFINE_GUIDSTRUCT("E757BCA0-39AC-11d1-A9F5-00C04FBBDE8F",KSDATAFORMAT_TYPE_NABTS);
#define KSDATAFORMAT_TYPE_NABTS DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_NABTS)

#define STATIC_KSDATAFORMAT_SUBTYPE_NABTS_FEC				\
	0xe757bca1,0x39ac,0x11d1,0xa9,0xf5,0x0,0xc0,0x4f,0xbb,0xde,0x8f
DEFINE_GUIDSTRUCT("E757BCA1-39AC-11d1-A9F5-00C04FBBDE8F",KSDATAFORMAT_SUBTYPE_NABTS_FEC);
#define KSDATAFORMAT_SUBTYPE_NABTS_FEC DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_NABTS_FEC)

#define MAX_NABTS_VBI_LINES_PER_FIELD	11
#define NABTS_LINES_PER_BUNDLE		16
#define NABTS_PAYLOAD_PER_LINE		28
#define NABTS_BYTES_PER_LINE		36

typedef struct _NABTSFEC_BUFFER {
  ULONG dataSize;
  USHORT groupID;
  USHORT Reserved;
  UCHAR data[NABTS_LINES_PER_BUNDLE *NABTS_PAYLOAD_PER_LINE];
} NABTSFEC_BUFFER,*PNABTSFEC_BUFFER;

#define STATIC_KSPROPSETID_VBICodecFiltering				\
	0xcafeb0ca,0x8715,0x11d0,0xbd,0x6a,0x00,0x35,0xc0,0xed,0xba,0xbe
DEFINE_GUIDSTRUCT("cafeb0ca-8715-11d0-bd6a-0035c0edbabe",KSPROPSETID_VBICodecFiltering);
#define KSPROPSETID_VBICodecFiltering DEFINE_GUIDNAMED(KSPROPSETID_VBICodecFiltering)

typedef enum {
  KSPROPERTY_VBICODECFILTERING_SCANLINES_REQUESTED_BIT_ARRAY = 0x01,
  KSPROPERTY_VBICODECFILTERING_SCANLINES_DISCOVERED_BIT_ARRAY,
  KSPROPERTY_VBICODECFILTERING_SUBSTREAMS_REQUESTED_BIT_ARRAY,
  KSPROPERTY_VBICODECFILTERING_SUBSTREAMS_DISCOVERED_BIT_ARRAY,
  KSPROPERTY_VBICODECFILTERING_STATISTICS
} KSPROPERTY_VBICODECFILTERING;

typedef struct _VBICODECFILTERING_SCANLINES {
  DWORD DwordBitArray[32];
} VBICODECFILTERING_SCANLINES,*PVBICODECFILTERING_SCANLINES;

typedef struct _VBICODECFILTERING_NABTS_SUBSTREAMS {
  DWORD SubstreamMask[128];
} VBICODECFILTERING_NABTS_SUBSTREAMS,*PVBICODECFILTERING_NABTS_SUBSTREAMS;

typedef struct _VBICODECFILTERING_CC_SUBSTREAMS {
  DWORD SubstreamMask;
} VBICODECFILTERING_CC_SUBSTREAMS,*PVBICODECFILTERING_CC_SUBSTREAMS;

#define KS_CC_SUBSTREAM_ODD		__MSABI_LONG(0x0001)
#define KS_CC_SUBSTREAM_EVEN		__MSABI_LONG(0x0002)

#define KS_CC_SUBSTREAM_FIELD1_MASK	__MSABI_LONG(0x00F0)
#define KS_CC_SUBSTREAM_SERVICE_CC1	__MSABI_LONG(0x0010)
#define KS_CC_SUBSTREAM_SERVICE_CC2	__MSABI_LONG(0x0020)
#define KS_CC_SUBSTREAM_SERVICE_T1	__MSABI_LONG(0x0040)
#define KS_CC_SUBSTREAM_SERVICE_T2	__MSABI_LONG(0x0080)

#define KS_CC_SUBSTREAM_FIELD2_MASK	__MSABI_LONG(0x1F00)
#define KS_CC_SUBSTREAM_SERVICE_CC3	__MSABI_LONG(0x0100)
#define KS_CC_SUBSTREAM_SERVICE_CC4	__MSABI_LONG(0x0200)
#define KS_CC_SUBSTREAM_SERVICE_T3	__MSABI_LONG(0x0400)
#define KS_CC_SUBSTREAM_SERVICE_T4	__MSABI_LONG(0x0800)
#define KS_CC_SUBSTREAM_SERVICE_XDS	__MSABI_LONG(0x1000)

#define CC_MAX_HW_DECODE_LINES		12
typedef struct _CC_BYTE_PAIR {
  BYTE Decoded[2];
  USHORT Reserved;
} CC_BYTE_PAIR,*PCC_BYTE_PAIR;

typedef struct _CC_HW_FIELD {
  VBICODECFILTERING_SCANLINES ScanlinesRequested;
  ULONG fieldFlags;
  LONGLONG PictureNumber;
  CC_BYTE_PAIR Lines[CC_MAX_HW_DECODE_LINES];
} CC_HW_FIELD,*PCC_HW_FIELD;

#ifndef PACK_PRAGMAS_NOT_SUPPORTED
#include <pshpack1.h>
#endif
typedef struct _NABTS_BUFFER_LINE {
  BYTE Confidence;
  BYTE Bytes[NABTS_BYTES_PER_LINE];
} NABTS_BUFFER_LINE,*PNABTS_BUFFER_LINE;

#define NABTS_BUFFER_PICTURENUMBER_SUPPORT	1
typedef struct _NABTS_BUFFER {
  VBICODECFILTERING_SCANLINES ScanlinesRequested;
  LONGLONG PictureNumber;
  NABTS_BUFFER_LINE NabtsLines[MAX_NABTS_VBI_LINES_PER_FIELD];
} NABTS_BUFFER,*PNABTS_BUFFER;
#ifndef PACK_PRAGMAS_NOT_SUPPORTED
#include <poppack.h>
#endif

#define WST_TVTUNER_CHANGE_BEGIN_TUNE	__MSABI_LONG(0x1000)
#define WST_TVTUNER_CHANGE_END_TUNE	__MSABI_LONG(0x2000)

#define MAX_WST_VBI_LINES_PER_FIELD	17
#define WST_BYTES_PER_LINE		42

typedef struct _WST_BUFFER_LINE {
  BYTE Confidence;
  BYTE Bytes[WST_BYTES_PER_LINE];
} WST_BUFFER_LINE,*PWST_BUFFER_LINE;

typedef struct _WST_BUFFER {
  VBICODECFILTERING_SCANLINES ScanlinesRequested;
  WST_BUFFER_LINE WstLines[MAX_WST_VBI_LINES_PER_FIELD];
} WST_BUFFER,*PWST_BUFFER;

typedef struct _VBICODECFILTERING_STATISTICS_COMMON {
  DWORD InputSRBsProcessed;
  DWORD OutputSRBsProcessed;
  DWORD SRBsIgnored;
  DWORD InputSRBsMissing;
  DWORD OutputSRBsMissing;
  DWORD OutputFailures;
  DWORD InternalErrors;
  DWORD ExternalErrors;
  DWORD InputDiscontinuities;
  DWORD DSPFailures;
  DWORD TvTunerChanges;
  DWORD VBIHeaderChanges;
  DWORD LineConfidenceAvg;
  DWORD BytesOutput;
} VBICODECFILTERING_STATISTICS_COMMON,*PVBICODECFILTERING_STATISTICS_COMMON;

typedef struct _VBICODECFILTERING_STATISTICS_COMMON_PIN {
  DWORD SRBsProcessed;
  DWORD SRBsIgnored;
  DWORD SRBsMissing;
  DWORD InternalErrors;
  DWORD ExternalErrors;
  DWORD Discontinuities;
  DWORD LineConfidenceAvg;
  DWORD BytesOutput;
} VBICODECFILTERING_STATISTICS_COMMON_PIN,*PVBICODECFILTERING_STATISTICS_COMMON_PIN;

typedef struct _VBICODECFILTERING_STATISTICS_NABTS {
  VBICODECFILTERING_STATISTICS_COMMON Common;
  DWORD FECBundleBadLines;
  DWORD FECQueueOverflows;
  DWORD FECCorrectedLines;
  DWORD FECUncorrectableLines;
  DWORD BundlesProcessed;
  DWORD BundlesSent2IP;
  DWORD FilteredLines;
} VBICODECFILTERING_STATISTICS_NABTS,*PVBICODECFILTERING_STATISTICS_NABTS;

typedef struct _VBICODECFILTERING_STATISTICS_NABTS_PIN {
  VBICODECFILTERING_STATISTICS_COMMON_PIN Common;
} VBICODECFILTERING_STATISTICS_NABTS_PIN,*PVBICODECFILTERING_STATISTICS_NABTS_PIN;

typedef struct _VBICODECFILTERING_STATISTICS_CC {
  VBICODECFILTERING_STATISTICS_COMMON Common;
} VBICODECFILTERING_STATISTICS_CC,*PVBICODECFILTERING_STATISTICS_CC;

typedef struct _VBICODECFILTERING_STATISTICS_CC_PIN {
  VBICODECFILTERING_STATISTICS_COMMON_PIN Common;
} VBICODECFILTERING_STATISTICS_CC_PIN,*PVBICODECFILTERING_STATISTICS_CC_PIN;

typedef struct _VBICODECFILTERING_STATISTICS_TELETEXT {
  VBICODECFILTERING_STATISTICS_COMMON Common;
} VBICODECFILTERING_STATISTICS_TELETEXT,*PVBICODECFILTERING_STATISTICS_TELETEXT;

typedef struct _VBICODECFILTERING_STATISTICS_TELETEXT_PIN {
  VBICODECFILTERING_STATISTICS_COMMON_PIN Common;
} VBICODECFILTERING_STATISTICS_TELETEXT_PIN,*PVBICODECFILTERING_STATISTICS_TELETEXT_PIN;

typedef struct {
  KSPROPERTY Property;
  VBICODECFILTERING_SCANLINES Scanlines;
} KSPROPERTY_VBICODECFILTERING_SCANLINES_S,*PKSPROPERTY_VBICODECFILTERING_SCANLINES_S;

typedef struct {
  KSPROPERTY Property;
  VBICODECFILTERING_NABTS_SUBSTREAMS Substreams;
} KSPROPERTY_VBICODECFILTERING_NABTS_SUBSTREAMS_S,*PKSPROPERTY_VBICODECFILTERING_NABTS_SUBSTREAMS_S;

typedef struct {
  KSPROPERTY Property;
  VBICODECFILTERING_CC_SUBSTREAMS Substreams;
} KSPROPERTY_VBICODECFILTERING_CC_SUBSTREAMS_S,*PKSPROPERTY_VBICODECFILTERING_CC_SUBSTREAMS_S;

typedef struct {
  KSPROPERTY Property;
  VBICODECFILTERING_STATISTICS_COMMON Statistics;
} KSPROPERTY_VBICODECFILTERING_STATISTICS_COMMON_S,*PKSPROPERTY_VBICODECFILTERING_STATISTICS_COMMON_S;

typedef struct {
  KSPROPERTY Property;
  VBICODECFILTERING_STATISTICS_COMMON_PIN Statistics;
} KSPROPERTY_VBICODECFILTERING_STATISTICS_COMMON_PIN_S,*PKSPROPERTY_VBICODECFILTERING_STATISTICS_COMMON_PIN_S;

typedef struct {
  KSPROPERTY Property;
  VBICODECFILTERING_STATISTICS_NABTS Statistics;
} KSPROPERTY_VBICODECFILTERING_STATISTICS_NABTS_S,*PKSPROPERTY_VBICODECFILTERING_STATISTICS_NABTS_S;

typedef struct {
  KSPROPERTY Property;
  VBICODECFILTERING_STATISTICS_NABTS_PIN Statistics;
} KSPROPERTY_VBICODECFILTERING_STATISTICS_NABTS_PIN_S,*PKSPROPERTY_VBICODECFILTERING_STATISTICS_NABTS_PIN_S;

typedef struct {
  KSPROPERTY Property;
  VBICODECFILTERING_STATISTICS_CC Statistics;
} KSPROPERTY_VBICODECFILTERING_STATISTICS_CC_S,*PKSPROPERTY_VBICODECFILTERING_STATISTICS_CC_S;

typedef struct {
  KSPROPERTY Property;
  VBICODECFILTERING_STATISTICS_CC_PIN Statistics;
} KSPROPERTY_VBICODECFILTERING_STATISTICS_CC_PIN_S,*PKSPROPERTY_VBICODECFILTERING_STATISTICS_CC_PIN_S;

#define STATIC_PINNAME_VIDEO_CAPTURE					\
	0xfb6c4281,0x353,0x11d1,0x90,0x5f,0x0,0x0,0xc0,0xcc,0x16,0xba
#define STATIC_PINNAME_CAPTURE STATIC_PINNAME_VIDEO_CAPTURE
DEFINE_GUIDSTRUCT("FB6C4281-0353-11d1-905F-0000C0CC16BA",PINNAME_VIDEO_CAPTURE);
#define PINNAME_VIDEO_CAPTURE DEFINE_GUIDNAMED(PINNAME_VIDEO_CAPTURE)
#define PINNAME_CAPTURE PINNAME_VIDEO_CAPTURE

#define STATIC_PINNAME_VIDEO_CC_CAPTURE					\
	0x1aad8061,0x12d,0x11d2,0xb4,0xb1,0x0,0xa0,0xd1,0x2,0xcf,0xbe
#define STATIC_PINNAME_CC_CAPTURE STATIC_PINNAME_VIDEO_CC_CAPTURE
DEFINE_GUIDSTRUCT("1AAD8061-012D-11d2-B4B1-00A0D102CFBE",PINNAME_VIDEO_CC_CAPTURE);
#define PINNAME_VIDEO_CC_CAPTURE DEFINE_GUIDNAMED(PINNAME_VIDEO_CC_CAPTURE)

#define STATIC_PINNAME_VIDEO_NABTS_CAPTURE				\
	0x29703660,0x498a,0x11d2,0xb4,0xb1,0x0,0xa0,0xd1,0x2,0xcf,0xbe
#define STATIC_PINNAME_NABTS_CAPTURE STATIC_PINNAME_VIDEO_NABTS_CAPTURE
DEFINE_GUIDSTRUCT("29703660-498A-11d2-B4B1-00A0D102CFBE",PINNAME_VIDEO_NABTS_CAPTURE);
#define PINNAME_VIDEO_NABTS_CAPTURE DEFINE_GUIDNAMED(PINNAME_VIDEO_NABTS_CAPTURE)

#define STATIC_PINNAME_VIDEO_PREVIEW					\
	0xfb6c4282,0x353,0x11d1,0x90,0x5f,0x0,0x0,0xc0,0xcc,0x16,0xba
#define STATIC_PINNAME_PREVIEW STATIC_PINNAME_VIDEO_PREVIEW
DEFINE_GUIDSTRUCT("FB6C4282-0353-11d1-905F-0000C0CC16BA",PINNAME_VIDEO_PREVIEW);
#define PINNAME_VIDEO_PREVIEW DEFINE_GUIDNAMED(PINNAME_VIDEO_PREVIEW)
#define PINNAME_PREVIEW PINNAME_VIDEO_PREVIEW

#define STATIC_PINNAME_VIDEO_ANALOGVIDEOIN				\
	0xfb6c4283,0x353,0x11d1,0x90,0x5f,0x0,0x0,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("FB6C4283-0353-11d1-905F-0000C0CC16BA",PINNAME_VIDEO_ANALOGVIDEOIN);
#define PINNAME_VIDEO_ANALOGVIDEOIN DEFINE_GUIDNAMED(PINNAME_VIDEO_ANALOGVIDEOIN)

#define STATIC_PINNAME_VIDEO_VBI					\
	0xfb6c4284,0x353,0x11d1,0x90,0x5f,0x0,0x0,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("FB6C4284-0353-11d1-905F-0000C0CC16BA",PINNAME_VIDEO_VBI);
#define PINNAME_VIDEO_VBI DEFINE_GUIDNAMED(PINNAME_VIDEO_VBI)

#define STATIC_PINNAME_VIDEO_VIDEOPORT					\
	0xfb6c4285,0x353,0x11d1,0x90,0x5f,0x0,0x0,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("FB6C4285-0353-11d1-905F-0000C0CC16BA",PINNAME_VIDEO_VIDEOPORT);
#define PINNAME_VIDEO_VIDEOPORT DEFINE_GUIDNAMED(PINNAME_VIDEO_VIDEOPORT)

#define STATIC_PINNAME_VIDEO_NABTS					\
	0xfb6c4286,0x353,0x11d1,0x90,0x5f,0x0,0x0,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("FB6C4286-0353-11d1-905F-0000C0CC16BA",PINNAME_VIDEO_NABTS);
#define PINNAME_VIDEO_NABTS DEFINE_GUIDNAMED(PINNAME_VIDEO_NABTS)

#define STATIC_PINNAME_VIDEO_EDS					\
	0xfb6c4287,0x353,0x11d1,0x90,0x5f,0x0,0x0,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("FB6C4287-0353-11d1-905F-0000C0CC16BA",PINNAME_VIDEO_EDS);
#define PINNAME_VIDEO_EDS DEFINE_GUIDNAMED(PINNAME_VIDEO_EDS)

#define STATIC_PINNAME_VIDEO_TELETEXT					\
	0xfb6c4288,0x353,0x11d1,0x90,0x5f,0x0,0x0,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("FB6C4288-0353-11d1-905F-0000C0CC16BA",PINNAME_VIDEO_TELETEXT);
#define PINNAME_VIDEO_TELETEXT DEFINE_GUIDNAMED(PINNAME_VIDEO_TELETEXT)

#define STATIC_PINNAME_VIDEO_CC						\
	0xfb6c4289,0x353,0x11d1,0x90,0x5f,0x0,0x0,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("FB6C4289-0353-11d1-905F-0000C0CC16BA",PINNAME_VIDEO_CC);
#define PINNAME_VIDEO_CC DEFINE_GUIDNAMED(PINNAME_VIDEO_CC)

#define STATIC_PINNAME_VIDEO_STILL					\
	0xfb6c428A,0x353,0x11d1,0x90,0x5f,0x0,0x0,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("FB6C428A-0353-11d1-905F-0000C0CC16BA",PINNAME_VIDEO_STILL);
#define PINNAME_VIDEO_STILL DEFINE_GUIDNAMED(PINNAME_VIDEO_STILL)

#define STATIC_PINNAME_VIDEO_TIMECODE					\
	0xfb6c428B,0x353,0x11d1,0x90,0x5f,0x0,0x0,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("FB6C428B-0353-11d1-905F-0000C0CC16BA",PINNAME_VIDEO_TIMECODE);
#define PINNAME_VIDEO_TIMECODE DEFINE_GUIDNAMED(PINNAME_VIDEO_TIMECODE)

#define STATIC_PINNAME_VIDEO_VIDEOPORT_VBI				\
	0xfb6c428C,0x353,0x11d1,0x90,0x5f,0x0,0x0,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("FB6C428C-0353-11d1-905F-0000C0CC16BA",PINNAME_VIDEO_VIDEOPORT_VBI);
#define PINNAME_VIDEO_VIDEOPORT_VBI DEFINE_GUIDNAMED(PINNAME_VIDEO_VIDEOPORT_VBI)


typedef enum {
  KS_CAPTURE_ALLOC_INVALID	= 0,
  KS_CAPTURE_ALLOC_SYSTEM	= 0x0001,
  KS_CAPTURE_ALLOC_VRAM		= 0x0002,
  KS_CAPTURE_ALLOC_SYSTEM_AGP	= 0x0004,
  KS_CAPTURE_ALLOC_VRAM_MAPPED	= 0x0008
} CAPTURE_MEMORY_ALLOCATION_FLAGS,*PCAPTURE_MEMORY_ALLOCATION_FLAGS;

#define STATIC_KSPROPSETID_VramCapture					\
	0xe73face3,0x2880,0x4902,0xb7,0x99,0x88,0xd0,0xcd,0x63,0x4e,0xf
DEFINE_GUIDSTRUCT("E73FACE3-2880-4902-B799-88D0CD634E0F", KSPROPSETID_VramCapture);
#define KSPROPSETID_VramCapture DEFINE_GUIDNAMED(KSPROPSETID_VramCapture)

typedef enum {
  KSPROPERTY_DISPLAY_ADAPTER_GUID = 1,
  KSPROPERTY_PREFERRED_CAPTURE_SURFACE,
  KSPROPERTY_CURRENT_CAPTURE_SURFACE,
  KSPROPERTY_MAP_CAPTURE_HANDLE_TO_VRAM_ADDRESS
} KSPROPERTY_VIDMEM_TRANSPORT;

#define DEFINE_KSPROPERTY_ITEM_DISPLAY_ADAPTER_GUID(GetHandler)		\
	DEFINE_KSPROPERTY_ITEM(						\
				KSPROPERTY_DISPLAY_ADAPTER_GUID,	\
				(GetHandler),				\
				sizeof(KSPROPERTY),			\
				sizeof(GUID),				\
				NULL,NULL,0,NULL,NULL,0)
#define DEFINE_KSPROPERTY_PREFERRED_CAPTURE_SURFACE(GetHandler)		\
	DEFINE_KSPROPERTY_ITEM(						\
				KSPROPERTY_PREFERRED_CAPTURE_SURFACE,	\
				(GetHandler),				\
				sizeof(KSPROPERTY),			\
				sizeof(CAPTURE_MEMORY_ALLOCATION_FLAGS),\
				NULL,NULL,0,NULL,NULL,0)
#define DEFINE_KSPROPERTY_CURRENT_CAPTURE_SURFACE(GetHandler,SetHandler)\
	DEFINE_KSPROPERTY_ITEM(						\
				KSPROPERTY_CURRENT_CAPTURE_SURFACE,	\
				(GetHandler),				\
				sizeof(KSPROPERTY),			\
				sizeof(CAPTURE_MEMORY_ALLOCATION_FLAGS),\
				(SetHandler),NULL,0,NULL,NULL,0)
#define DEFINE_KSPROPERTY_MAP_CAPTURE_HANDLE_TO_VRAM_ADDRESS(GetHandler)\
	DEFINE_KSPROPERTY_ITEM(						\
				KSPROPERTY_MAP_CAPTURE_HANDLE_TO_VRAM_ADDRESS,\
				(GetHandler),				\
				sizeof(VRAM_SURFACE_INFO_PROPERTY_S),	\
				sizeof(DWORD),				\
				NULL,NULL,0,NULL,NULL,0)

typedef struct {
  UINT_PTR hSurface;
  LONGLONG VramPhysicalAddress;
  DWORD cbCaptured;
  DWORD dwWidth;
  DWORD dwHeight;
  DWORD dwLinearSize;
  LONG lPitch;
  ULONGLONG ullReserved[16];
} VRAM_SURFACE_INFO,*PVRAM_SURFACE_INFO;

typedef struct {
  KSPROPERTY Property;
  PVRAM_SURFACE_INFO pVramSurfaceInfo;
} VRAM_SURFACE_INFO_PROPERTY_S,*PVRAM_SURFACE_INFO_PROPERTY_S;


#define KS_VIDEO_FLAG_FRAME		__MSABI_LONG(0x0000)
#define KS_VIDEO_FLAG_FIELD1		__MSABI_LONG(0x0001)
#define KS_VIDEO_FLAG_FIELD2		__MSABI_LONG(0x0002)

#define KS_VIDEO_FLAG_I_FRAME		__MSABI_LONG(0x0000)
#define KS_VIDEO_FLAG_P_FRAME		__MSABI_LONG(0x0010)
#define KS_VIDEO_FLAG_B_FRAME		__MSABI_LONG(0x0020)

typedef struct tagKS_FRAME_INFO {
  ULONG ExtendedHeaderSize;
  DWORD dwFrameFlags;
  LONGLONG PictureNumber;
  LONGLONG DropCount;
  HANDLE hDirectDraw;
  HANDLE hSurfaceHandle;
  RECT DirectDrawRect;

  DWORD Reserved1;
  DWORD Reserved2;
  DWORD Reserved3;
  DWORD Reserved4;
} KS_FRAME_INFO,*PKS_FRAME_INFO;

#define KS_VBI_FLAG_FIELD1		__MSABI_LONG(0x0001)
#define KS_VBI_FLAG_FIELD2		__MSABI_LONG(0x0002)

#define KS_VBI_FLAG_MV_PRESENT		__MSABI_LONG(0x0100)
#define KS_VBI_FLAG_MV_HARDWARE		__MSABI_LONG(0x0200)
#define KS_VBI_FLAG_MV_DETECTED		__MSABI_LONG(0x0400)

#define KS_VBI_FLAG_TVTUNER_CHANGE	__MSABI_LONG(0x0010)
#define KS_VBI_FLAG_VBIINFOHEADER_CHANGE __MSABI_LONG(0x0020)

typedef struct tagKS_VBI_FRAME_INFO {
  ULONG ExtendedHeaderSize;
  DWORD dwFrameFlags;
  LONGLONG PictureNumber;
  LONGLONG DropCount;
  DWORD dwSamplingFrequency;
  KS_TVTUNER_CHANGE_INFO TvTunerChangeInfo;
  KS_VBIINFOHEADER VBIInfoHeader;
} KS_VBI_FRAME_INFO,*PKS_VBI_FRAME_INFO;

typedef enum
{
  KS_AnalogVideo_None = 0x00000000,
  KS_AnalogVideo_NTSC_M = 0x00000001,
  KS_AnalogVideo_NTSC_M_J = 0x00000002,
  KS_AnalogVideo_NTSC_433 = 0x00000004,
  KS_AnalogVideo_PAL_B = 0x00000010,
  KS_AnalogVideo_PAL_D = 0x00000020,
  KS_AnalogVideo_PAL_G = 0x00000040,
  KS_AnalogVideo_PAL_H = 0x00000080,
  KS_AnalogVideo_PAL_I = 0x00000100,
  KS_AnalogVideo_PAL_M = 0x00000200,
  KS_AnalogVideo_PAL_N = 0x00000400,
  KS_AnalogVideo_PAL_60 = 0x00000800,
  KS_AnalogVideo_SECAM_B = 0x00001000,
  KS_AnalogVideo_SECAM_D = 0x00002000,
  KS_AnalogVideo_SECAM_G = 0x00004000,
  KS_AnalogVideo_SECAM_H = 0x00008000,
  KS_AnalogVideo_SECAM_K = 0x00010000,
  KS_AnalogVideo_SECAM_K1 = 0x00020000,
  KS_AnalogVideo_SECAM_L = 0x00040000,
  KS_AnalogVideo_SECAM_L1 = 0x00080000,
  KS_AnalogVideo_PAL_N_COMBO = 0x00100000
} KS_AnalogVideoStandard;

#define KS_AnalogVideo_NTSC_Mask	0x00000007
#define KS_AnalogVideo_PAL_Mask		0x00100FF0
#define KS_AnalogVideo_SECAM_Mask	0x000FF000

#define STATIC_PROPSETID_ALLOCATOR_CONTROL				\
	0x53171960,0x148e,0x11d2,0x99,0x79,0x0,0x0,0xc0,0xcc,0x16,0xba
DEFINE_GUIDSTRUCT("53171960-148E-11d2-9979-0000C0CC16BA",PROPSETID_ALLOCATOR_CONTROL);
#define PROPSETID_ALLOCATOR_CONTROL DEFINE_GUIDNAMED(PROPSETID_ALLOCATOR_CONTROL)

typedef enum {
  KSPROPERTY_ALLOCATOR_CONTROL_HONOR_COUNT,
  KSPROPERTY_ALLOCATOR_CONTROL_SURFACE_SIZE,
  KSPROPERTY_ALLOCATOR_CONTROL_CAPTURE_CAPS,
  KSPROPERTY_ALLOCATOR_CONTROL_CAPTURE_INTERLEAVE
} KSPROPERTY_ALLOCATOR_CONTROL;

typedef struct {
  ULONG CX;
  ULONG CY;
} KSPROPERTY_ALLOCATOR_CONTROL_SURFACE_SIZE_S,*PKSPROPERTY_ALLOCATOR_CONTROL_SURFACE_SIZE_S;

typedef struct {
  ULONG InterleavedCapSupported;
} KSPROPERTY_ALLOCATOR_CONTROL_CAPTURE_CAPS_S,*PKSPROPERTY_ALLOCATOR_CONTROL_CAPTURE_CAPS_S;

typedef struct {
  ULONG InterleavedCapPossible;
} KSPROPERTY_ALLOCATOR_CONTROL_CAPTURE_INTERLEAVE_S,*PKSPROPERTY_ALLOCATOR_CONTROL_CAPTURE_INTERLEAVE_S;

#define STATIC_PROPSETID_VIDCAP_VIDEOPROCAMP				\
	0xC6E13360,0x30AC,0x11d0,0xa1,0x8c,0x00,0xA0,0xC9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("C6E13360-30AC-11d0-A18C-00A0C9118956",PROPSETID_VIDCAP_VIDEOPROCAMP);
#define PROPSETID_VIDCAP_VIDEOPROCAMP DEFINE_GUIDNAMED(PROPSETID_VIDCAP_VIDEOPROCAMP)

typedef enum {
  KSPROPERTY_VIDEOPROCAMP_BRIGHTNESS,
  KSPROPERTY_VIDEOPROCAMP_CONTRAST,
  KSPROPERTY_VIDEOPROCAMP_HUE,
  KSPROPERTY_VIDEOPROCAMP_SATURATION,
  KSPROPERTY_VIDEOPROCAMP_SHARPNESS,
  KSPROPERTY_VIDEOPROCAMP_GAMMA,
  KSPROPERTY_VIDEOPROCAMP_COLORENABLE,
  KSPROPERTY_VIDEOPROCAMP_WHITEBALANCE,
  KSPROPERTY_VIDEOPROCAMP_BACKLIGHT_COMPENSATION,
  KSPROPERTY_VIDEOPROCAMP_GAIN,
  KSPROPERTY_VIDEOPROCAMP_DIGITAL_MULTIPLIER,
  KSPROPERTY_VIDEOPROCAMP_DIGITAL_MULTIPLIER_LIMIT,
  KSPROPERTY_VIDEOPROCAMP_WHITEBALANCE_COMPONENT,
  KSPROPERTY_VIDEOPROCAMP_POWERLINE_FREQUENCY
} KSPROPERTY_VIDCAP_VIDEOPROCAMP;

typedef struct {
  KSPROPERTY Property;
  LONG Value;
  ULONG Flags;
  ULONG Capabilities;
} KSPROPERTY_VIDEOPROCAMP_S,*PKSPROPERTY_VIDEOPROCAMP_S;

typedef struct {
  KSP_NODE NodeProperty;
  LONG Value;
  ULONG Flags;
  ULONG Capabilities;
} KSPROPERTY_VIDEOPROCAMP_NODE_S,*PKSPROPERTY_VIDEOPROCAMP_NODE_S;

typedef struct {
  KSPROPERTY Property;
  LONG Value1;
  ULONG Flags;
  ULONG Capabilities;
  LONG Value2;
} KSPROPERTY_VIDEOPROCAMP_S2,*PKSPROPERTY_VIDEOPROCAMP_S2;

typedef struct {
  KSP_NODE NodeProperty;
  LONG Value1;
  ULONG Flags;
  ULONG Capabilities;
  LONG Value2;
} KSPROPERTY_VIDEOPROCAMP_NODE_S2,*PKSPROPERTY_VIDEOPROCAMP_NODE_S2;

#define KSPROPERTY_VIDEOPROCAMP_FLAGS_AUTO	0X0001L
#define KSPROPERTY_VIDEOPROCAMP_FLAGS_MANUAL	0X0002L

#define STATIC_PROPSETID_VIDCAP_SELECTOR				\
	0x1ABDAECA,0x68B6,0x4F83,0x93,0x71,0xB4,0x13,0x90,0x7C,0x7B,0x9F
DEFINE_GUIDSTRUCT("1ABDAECA-68B6-4F83-9371-B413907C7B9F",PROPSETID_VIDCAP_SELECTOR);
#define PROPSETID_VIDCAP_SELECTOR DEFINE_GUIDNAMED(PROPSETID_VIDCAP_SELECTOR)

typedef enum {
  KSPROPERTY_SELECTOR_SOURCE_NODE_ID,
  KSPROPERTY_SELECTOR_NUM_SOURCES
} KSPROPERTY_VIDCAP_SELECTOR,*PKSPROPERTY_VIDCAP_SELECTOR;

typedef struct {
  KSPROPERTY Property;
  LONG Value;
  ULONG Flags;
  ULONG Capabilities;
} KSPROPERTY_SELECTOR_S,*PKSPROPERTY_SELECTOR_S;

typedef struct {
  KSP_NODE NodeProperty;
  LONG Value;
  ULONG Flags;
  ULONG Capabilities;
} KSPROPERTY_SELECTOR_NODE_S,*PKSPROPERTY_SELECTOR_NODE_S;

#define STATIC_PROPSETID_TUNER						\
	0x6a2e0605,0x28e4,0x11d0,0xa1,0x8c,0x00,0xa0,0xc9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("6a2e0605-28e4-11d0-a18c-00a0c9118956",PROPSETID_TUNER);
#define PROPSETID_TUNER DEFINE_GUIDNAMED(PROPSETID_TUNER)

typedef enum {
  KSPROPERTY_TUNER_CAPS,
  KSPROPERTY_TUNER_MODE_CAPS,
  KSPROPERTY_TUNER_MODE,
  KSPROPERTY_TUNER_STANDARD,
  KSPROPERTY_TUNER_FREQUENCY,
  KSPROPERTY_TUNER_INPUT,
  KSPROPERTY_TUNER_STATUS,
  KSPROPERTY_TUNER_IF_MEDIUM
} KSPROPERTY_TUNER;

typedef enum {
  KSPROPERTY_TUNER_MODE_TV = 0X0001,
  KSPROPERTY_TUNER_MODE_FM_RADIO = 0X0002,
  KSPROPERTY_TUNER_MODE_AM_RADIO = 0X0004,
  KSPROPERTY_TUNER_MODE_DSS = 0X0008,
  KSPROPERTY_TUNER_MODE_ATSC = 0X0010
} KSPROPERTY_TUNER_MODES;

typedef enum {
  KS_TUNER_TUNING_EXACT = 1,
  KS_TUNER_TUNING_FINE,
  KS_TUNER_TUNING_COARSE
} KS_TUNER_TUNING_FLAGS;

typedef enum {
  KS_TUNER_STRATEGY_PLL = 0X01,
  KS_TUNER_STRATEGY_SIGNAL_STRENGTH = 0X02,
  KS_TUNER_STRATEGY_DRIVER_TUNES = 0X04
} KS_TUNER_STRATEGY;

typedef struct {
  KSPROPERTY Property;
  ULONG ModesSupported;
  KSPIN_MEDIUM VideoMedium;
  KSPIN_MEDIUM TVAudioMedium;
  KSPIN_MEDIUM RadioAudioMedium;
} KSPROPERTY_TUNER_CAPS_S,*PKSPROPERTY_TUNER_CAPS_S;

typedef struct {
  KSPROPERTY Property;
  KSPIN_MEDIUM IFMedium;
} KSPROPERTY_TUNER_IF_MEDIUM_S,*PKSPROPERTY_TUNER_IF_MEDIUM_S;

typedef struct {
  KSPROPERTY Property;
  ULONG Mode;
  ULONG StandardsSupported;
  ULONG MinFrequency;
  ULONG MaxFrequency;
  ULONG TuningGranularity;
  ULONG NumberOfInputs;
  ULONG SettlingTime;
  ULONG Strategy;
} KSPROPERTY_TUNER_MODE_CAPS_S,*PKSPROPERTY_TUNER_MODE_CAPS_S;

typedef struct {
  KSPROPERTY Property;
  ULONG Mode;
} KSPROPERTY_TUNER_MODE_S,*PKSPROPERTY_TUNER_MODE_S;

typedef struct {
  KSPROPERTY Property;
  ULONG Frequency;
  ULONG LastFrequency;
  ULONG TuningFlags;
  ULONG VideoSubChannel;
  ULONG AudioSubChannel;
  ULONG Channel;
  ULONG Country;
} KSPROPERTY_TUNER_FREQUENCY_S,*PKSPROPERTY_TUNER_FREQUENCY_S;

typedef struct {
  KSPROPERTY Property;
  ULONG Standard;
} KSPROPERTY_TUNER_STANDARD_S,*PKSPROPERTY_TUNER_STANDARD_S;

typedef struct {
  KSPROPERTY Property;
  ULONG InputIndex;
} KSPROPERTY_TUNER_INPUT_S,*PKSPROPERTY_TUNER_INPUT_S;

typedef struct {
  KSPROPERTY Property;
  ULONG CurrentFrequency;
  ULONG PLLOffset;
  ULONG SignalStrength;
  ULONG Busy;
} KSPROPERTY_TUNER_STATUS_S,*PKSPROPERTY_TUNER_STATUS_S;

#define STATIC_EVENTSETID_TUNER						\
	0x6a2e0606,0x28e4,0x11d0,0xa1,0x8c,0x00,0xa0,0xc9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("6a2e0606-28e4-11d0-a18c-00a0c9118956",EVENTSETID_TUNER);
#define EVENTSETID_TUNER DEFINE_GUIDNAMED(EVENTSETID_TUNER)

typedef enum {
  KSEVENT_TUNER_CHANGED
} KSEVENT_TUNER;

#define STATIC_KSNODETYPE_VIDEO_STREAMING				\
	0xDFF229E1,0xF70F,0x11D0,0xB9,0x17,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("DFF229E1-F70F-11D0-B917-00A0C9223196",KSNODETYPE_VIDEO_STREAMING);
#define KSNODETYPE_VIDEO_STREAMING DEFINE_GUIDNAMED(KSNODETYPE_VIDEO_STREAMING)

#define STATIC_KSNODETYPE_VIDEO_INPUT_TERMINAL				\
	0xDFF229E2,0xF70F,0x11D0,0xB9,0x17,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("DFF229E2-F70F-11D0-B917-00A0C9223196",KSNODETYPE_VIDEO_INPUT_TERMINAL);
#define KSNODETYPE_VIDEO_INPUT_TERMINAL DEFINE_GUIDNAMED(KSNODETYPE_VIDEO_INPUT_TERMINAL)

#define STATIC_KSNODETYPE_VIDEO_OUTPUT_TERMINAL				\
	0xDFF229E3,0xF70F,0x11D0,0xB9,0x17,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("DFF229E3-F70F-11D0-B917-00A0C9223196",KSNODETYPE_VIDEO_OUTPUT_TERMINAL);
#define KSNODETYPE_VIDEO_OUTPUT_TERMINAL DEFINE_GUIDNAMED(KSNODETYPE_VIDEO_OUTPUT_TERMINAL)

#define STATIC_KSNODETYPE_VIDEO_SELECTOR				\
	0xDFF229E4,0xF70F,0x11D0,0xB9,0x17,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("DFF229E4-F70F-11D0-B917-00A0C9223196",KSNODETYPE_VIDEO_SELECTOR);
#define KSNODETYPE_VIDEO_SELECTOR DEFINE_GUIDNAMED(KSNODETYPE_VIDEO_SELECTOR)

#define STATIC_KSNODETYPE_VIDEO_PROCESSING				\
	0xDFF229E5,0xF70F,0x11D0,0xB9,0x17,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("DFF229E5-F70F-11D0-B917-00A0C9223196",KSNODETYPE_VIDEO_PROCESSING);
#define KSNODETYPE_VIDEO_PROCESSING DEFINE_GUIDNAMED(KSNODETYPE_VIDEO_PROCESSING)

#define STATIC_KSNODETYPE_VIDEO_CAMERA_TERMINAL				\
	0xDFF229E6,0xF70F,0x11D0,0xB9,0x17,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("DFF229E6-F70F-11D0-B917-00A0C9223196",KSNODETYPE_VIDEO_CAMERA_TERMINAL);
#define KSNODETYPE_VIDEO_CAMERA_TERMINAL DEFINE_GUIDNAMED(KSNODETYPE_VIDEO_CAMERA_TERMINAL)

#define STATIC_KSNODETYPE_VIDEO_INPUT_MTT				\
	0xDFF229E7,0xF70F,0x11D0,0xB9,0x17,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("DFF229E7-F70F-11D0-B917-00A0C9223196",KSNODETYPE_VIDEO_INPUT_MTT);
#define KSNODETYPE_VIDEO_INPUT_MTT DEFINE_GUIDNAMED(KSNODETYPE_VIDEO_INPUT_MTT)

#define STATIC_KSNODETYPE_VIDEO_OUTPUT_MTT				\
	0xDFF229E8,0xF70F,0x11D0,0xB9,0x17,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("DFF229E8-F70F-11D0-B917-00A0C9223196",KSNODETYPE_VIDEO_OUTPUT_MTT);
#define KSNODETYPE_VIDEO_OUTPUT_MTT DEFINE_GUIDNAMED(KSNODETYPE_VIDEO_OUTPUT_MTT)

#define STATIC_PROPSETID_VIDCAP_VIDEOENCODER				\
	0x6a2e0610,0x28e4,0x11d0,0xa1,0x8c,0x00,0xa0,0xc9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("6a2e0610-28e4-11d0-a18c-00a0c9118956",PROPSETID_VIDCAP_VIDEOENCODER);
#define PROPSETID_VIDCAP_VIDEOENCODER DEFINE_GUIDNAMED(PROPSETID_VIDCAP_VIDEOENCODER)

typedef enum {
  KSPROPERTY_VIDEOENCODER_CAPS,
  KSPROPERTY_VIDEOENCODER_STANDARD,
  KSPROPERTY_VIDEOENCODER_COPYPROTECTION,
  KSPROPERTY_VIDEOENCODER_CC_ENABLE
} KSPROPERTY_VIDCAP_VIDEOENCODER;

typedef struct {
  KSPROPERTY Property;
  LONG Value;
  ULONG Flags;
  ULONG Capabilities;
} KSPROPERTY_VIDEOENCODER_S,*PKSPROPERTY_VIDEOENCODER_S;

#define STATIC_PROPSETID_VIDCAP_VIDEODECODER				\
	0xC6E13350,0x30AC,0x11d0,0xA1,0x8C,0x00,0xA0,0xC9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("C6E13350-30AC-11d0-A18C-00A0C9118956",PROPSETID_VIDCAP_VIDEODECODER);
#define PROPSETID_VIDCAP_VIDEODECODER DEFINE_GUIDNAMED(PROPSETID_VIDCAP_VIDEODECODER)

typedef enum {
  KSPROPERTY_VIDEODECODER_CAPS,
  KSPROPERTY_VIDEODECODER_STANDARD,
  KSPROPERTY_VIDEODECODER_STATUS,
  KSPROPERTY_VIDEODECODER_OUTPUT_ENABLE,
  KSPROPERTY_VIDEODECODER_VCR_TIMING
} KSPROPERTY_VIDCAP_VIDEODECODER;

typedef enum {
  KS_VIDEODECODER_FLAGS_CAN_DISABLE_OUTPUT = 0X0001,
  KS_VIDEODECODER_FLAGS_CAN_USE_VCR_LOCKING = 0X0002,
  KS_VIDEODECODER_FLAGS_CAN_INDICATE_LOCKED = 0X0004
} KS_VIDEODECODER_FLAGS;

typedef struct {
  KSPROPERTY Property;
  ULONG StandardsSupported;
  ULONG Capabilities;
  ULONG SettlingTime;
  ULONG HSyncPerVSync;
} KSPROPERTY_VIDEODECODER_CAPS_S,*PKSPROPERTY_VIDEODECODER_CAPS_S;

typedef struct {
  KSPROPERTY Property;
  ULONG NumberOfLines;
  ULONG SignalLocked;
} KSPROPERTY_VIDEODECODER_STATUS_S,*PKSPROPERTY_VIDEODECODER_STATUS_S;

typedef struct {
  KSPROPERTY Property;
  ULONG Value;
} KSPROPERTY_VIDEODECODER_S,*PKSPROPERTY_VIDEODECODER_S;

#define STATIC_EVENTSETID_VIDEODECODER					\
	0x6a2e0621,0x28e4,0x11d0,0xa1,0x8c,0x00,0xa0,0xc9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("6a2e0621-28e4-11d0-a18c-00a0c9118956",EVENTSETID_VIDEODECODER);
#define EVENTSETID_VIDEODECODER DEFINE_GUIDNAMED(EVENTSETID_VIDEODECODER)

typedef enum {
  KSEVENT_VIDEODECODER_CHANGED
} KSEVENT_VIDEODECODER;

#define STATIC_PROPSETID_VIDCAP_CAMERACONTROL				\
	0xC6E13370,0x30AC,0x11d0,0xa1,0x8C,0x00,0xA0,0xC9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("C6E13370-30AC-11d0-A18C-00A0C9118956",PROPSETID_VIDCAP_CAMERACONTROL);
#define PROPSETID_VIDCAP_CAMERACONTROL DEFINE_GUIDNAMED(PROPSETID_VIDCAP_CAMERACONTROL)

typedef enum {
  KSPROPERTY_CAMERACONTROL_PAN,
  KSPROPERTY_CAMERACONTROL_TILT,
  KSPROPERTY_CAMERACONTROL_ROLL,
  KSPROPERTY_CAMERACONTROL_ZOOM,
  KSPROPERTY_CAMERACONTROL_EXPOSURE,
  KSPROPERTY_CAMERACONTROL_IRIS,
  KSPROPERTY_CAMERACONTROL_FOCUS,
  KSPROPERTY_CAMERACONTROL_SCANMODE,
  KSPROPERTY_CAMERACONTROL_PRIVACY,
  KSPROPERTY_CAMERACONTROL_PANTILT,
  KSPROPERTY_CAMERACONTROL_PAN_RELATIVE,
  KSPROPERTY_CAMERACONTROL_TILT_RELATIVE,
  KSPROPERTY_CAMERACONTROL_ROLL_RELATIVE,
  KSPROPERTY_CAMERACONTROL_ZOOM_RELATIVE,
  KSPROPERTY_CAMERACONTROL_EXPOSURE_RELATIVE,
  KSPROPERTY_CAMERACONTROL_IRIS_RELATIVE,
  KSPROPERTY_CAMERACONTROL_FOCUS_RELATIVE,
  KSPROPERTY_CAMERACONTROL_PANTILT_RELATIVE,
  KSPROPERTY_CAMERACONTROL_FOCAL_LENGTH
} KSPROPERTY_VIDCAP_CAMERACONTROL;

typedef struct {
  KSPROPERTY Property;
  LONG Value;
  ULONG Flags;
  ULONG Capabilities;
} KSPROPERTY_CAMERACONTROL_S,*PKSPROPERTY_CAMERACONTROL_S;

typedef struct {
  KSP_NODE NodeProperty;
  LONG Value;
  ULONG Flags;
  ULONG Capabilities;
} KSPROPERTY_CAMERACONTROL_NODE_S,PKSPROPERTY_CAMERACONTROL_NODE_S;

typedef struct {
  KSPROPERTY Property;
  LONG Value1;
  ULONG Flags;
  ULONG Capabilities;
  LONG Value2;
} KSPROPERTY_CAMERACONTROL_S2,*PKSPROPERTY_CAMERACONTROL_S2;

typedef struct {
  KSP_NODE NodeProperty;
  LONG Value1;
  ULONG Flags;
  ULONG Capabilities;
  LONG Value2;
} KSPROPERTY_CAMERACONTROL_NODE_S2,*PKSPROPERTY_CAMERACONTROL_NODE_S2;

typedef struct {
  KSPROPERTY Property;
  LONG lOcularFocalLength;
  LONG lObjectiveFocalLengthMin;
  LONG lObjectiveFocalLengthMax;
} KSPROPERTY_CAMERACONTROL_FOCAL_LENGTH_S,*PKSPROPERTY_CAMERACONTROL_FOCAL_LENGTH_S;

typedef struct {
  KSNODEPROPERTY NodeProperty;
  LONG lOcularFocalLength;
  LONG lObjectiveFocalLengthMin;
  LONG lObjectiveFocalLengthMax;
} KSPROPERTY_CAMERACONTROL_NODE_FOCAL_LENGTH_S,*PKSPROPERTY_CAMERACONTROL_NODE_FOCAL_LENGTH_S;

#define KSPROPERTY_CAMERACONTROL_FLAGS_AUTO	0X0001L
#define KSPROPERTY_CAMERACONTROL_FLAGS_MANUAL	0X0002L

#define KSPROPERTY_CAMERACONTROL_FLAGS_ABSOLUTE	0X0000L
#define KSPROPERTY_CAMERACONTROL_FLAGS_RELATIVE	0X0010L

#ifndef __EDevCtrl__
#define __EDevCtrl__

#define STATIC_PROPSETID_EXT_DEVICE					\
	0xB5730A90,0x1A2C,0x11cf,0x8c,0x23,0x00,0xAA,0x00,0x6B,0x68,0x14
DEFINE_GUIDSTRUCT("B5730A90-1A2C-11cf-8C23-00AA006B6814",PROPSETID_EXT_DEVICE);
#define PROPSETID_EXT_DEVICE DEFINE_GUIDNAMED(PROPSETID_EXT_DEVICE)

typedef enum {
  KSPROPERTY_EXTDEVICE_ID,
  KSPROPERTY_EXTDEVICE_VERSION,
  KSPROPERTY_EXTDEVICE_POWER_STATE,
  KSPROPERTY_EXTDEVICE_PORT,
  KSPROPERTY_EXTDEVICE_CAPABILITIES
} KSPROPERTY_EXTDEVICE;

typedef struct tagDEVCAPS{
  LONG CanRecord;
  LONG CanRecordStrobe;
  LONG HasAudio;
  LONG HasVideo;
  LONG UsesFiles;
  LONG CanSave;
  LONG DeviceType;
  LONG TCRead;
  LONG TCWrite;
  LONG CTLRead;
  LONG IndexRead;
  LONG Preroll;
  LONG Postroll;
  LONG SyncAcc;
  LONG NormRate;
  LONG CanPreview;
  LONG CanMonitorSrc;
  LONG CanTest;
  LONG VideoIn;
  LONG AudioIn;
  LONG Calibrate;
  LONG SeekType;
  LONG SimulatedHardware;
} DEVCAPS,*PDEVCAPS;

typedef struct {
  KSPROPERTY Property;
  union {
    DEVCAPS Capabilities;
    ULONG DevPort;
    ULONG PowerState;
    WCHAR pawchString[MAX_PATH];
    DWORD NodeUniqueID[2];
  } u;
} KSPROPERTY_EXTDEVICE_S,*PKSPROPERTY_EXTDEVICE_S;

#define STATIC_PROPSETID_EXT_TRANSPORT					\
	0xA03CD5F0,0x3045,0x11cf,0x8c,0x44,0x00,0xAA,0x00,0x6B,0x68,0x14
DEFINE_GUIDSTRUCT("A03CD5F0-3045-11cf-8C44-00AA006B6814",PROPSETID_EXT_TRANSPORT);
#define PROPSETID_EXT_TRANSPORT DEFINE_GUIDNAMED(PROPSETID_EXT_TRANSPORT)

typedef enum {
  KSPROPERTY_EXTXPORT_CAPABILITIES,
  KSPROPERTY_EXTXPORT_INPUT_SIGNAL_MODE,
  KSPROPERTY_EXTXPORT_OUTPUT_SIGNAL_MODE,
  KSPROPERTY_EXTXPORT_LOAD_MEDIUM,
  KSPROPERTY_EXTXPORT_MEDIUM_INFO,
  KSPROPERTY_EXTXPORT_STATE,
  KSPROPERTY_EXTXPORT_STATE_NOTIFY,
  KSPROPERTY_EXTXPORT_TIMECODE_SEARCH,
  KSPROPERTY_EXTXPORT_ATN_SEARCH,
  KSPROPERTY_EXTXPORT_RTC_SEARCH,
  KSPROPERTY_RAW_AVC_CMD
} KSPROPERTY_EXTXPORT;

typedef struct tagTRANSPORTSTATUS {
  LONG Mode;
  LONG LastError;
  LONG RecordInhibit;
  LONG ServoLock;
  LONG MediaPresent;
  LONG MediaLength;
  LONG MediaSize;
  LONG MediaTrackCount;
  LONG MediaTrackLength;
  LONG MediaTrackSide;
  LONG MediaType;
  LONG LinkMode;
  LONG NotifyOn;
} TRANSPORTSTATUS,*PTRANSPORTSTATUS;

typedef struct tagTRANSPORTBASICPARMS {
  LONG TimeFormat;
  LONG TimeReference;
  LONG Superimpose;
  LONG EndStopAction;
  LONG RecordFormat;
  LONG StepFrames;
  LONG SetpField;
  LONG Preroll;
  LONG RecPreroll;
  LONG Postroll;
  LONG EditDelay;
  LONG PlayTCDelay;
  LONG RecTCDelay;
  LONG EditField;
  LONG FrameServo;
  LONG ColorFrameServo;
  LONG ServoRef;
  LONG WarnGenlock;
  LONG SetTracking;
  TCHAR VolumeName[40];
  LONG Ballistic[20];
  LONG Speed;
  LONG CounterFormat;
  LONG TunerChannel;
  LONG TunerNumber;
  LONG TimerEvent;
  LONG TimerStartDay;
  LONG TimerStartTime;
  LONG TimerStopDay;
  LONG TimerStopTime;
} TRANSPORTBASICPARMS,*PTRANSPORTBASICPARMS;

typedef struct tagTRANSPORTVIDEOPARMS {
  LONG OutputMode;
  LONG Input;
} TRANSPORTVIDEOPARMS,*PTRANSPORTVIDEOPARMS;

typedef struct tagTRANSPORTAUDIOPARMS {
  LONG EnableOutput;
  LONG EnableRecord;
  LONG EnableSelsync;
  LONG Input;
  LONG MonitorSource;
} TRANSPORTAUDIOPARMS,*PTRANSPORTAUDIOPARMS;

typedef struct {
  WINBOOL MediaPresent;
  ULONG MediaType;
  WINBOOL RecordInhibit;
} MEDIUM_INFO,*PMEDIUM_INFO;

typedef struct {
  ULONG Mode;
  ULONG State;
} TRANSPORT_STATE,*PTRANSPORT_STATE;

typedef struct {
  KSPROPERTY Property;
  union {
    ULONG Capabilities;
    ULONG SignalMode;
    ULONG LoadMedium;
    MEDIUM_INFO MediumInfo;
    TRANSPORT_STATE XPrtState;
    struct {
      BYTE frame;
      BYTE second;
      BYTE minute;
      BYTE hour;
    } Timecode;
    DWORD dwTimecode;
    DWORD dwAbsTrackNumber;
    struct {
      ULONG PayloadSize;
      BYTE Payload[512];
    } RawAVC;
  } u;
} KSPROPERTY_EXTXPORT_S,*PKSPROPERTY_EXTXPORT_S;

typedef struct {
  KSP_NODE NodeProperty;
  union {
    ULONG Capabilities;
    ULONG SignalMode;
    ULONG LoadMedium;
    MEDIUM_INFO MediumInfo;
    TRANSPORT_STATE XPrtState;
    struct {
      BYTE frame;
      BYTE second;
      BYTE minute;
      BYTE hour;
    } Timecode;
    DWORD dwTimecode;
    DWORD dwAbsTrackNumber;
    struct {
      ULONG PayloadSize;
      BYTE Payload[512];
    } RawAVC;
  } u;
} KSPROPERTY_EXTXPORT_NODE_S,*PKSPROPERTY_EXTXPORT_NODE_S;

#define STATIC_PROPSETID_TIMECODE_READER				\
	0x9B496CE1,0x811B,0x11cf,0x8C,0x77,0x00,0xAA,0x00,0x6B,0x68,0x14
DEFINE_GUIDSTRUCT("9B496CE1-811B-11cf-8C77-00AA006B6814",PROPSETID_TIMECODE_READER);
#define PROPSETID_TIMECODE_READER DEFINE_GUIDNAMED(PROPSETID_TIMECODE_READER)

typedef enum {
  KSPROPERTY_TIMECODE_READER,
  KSPROPERTY_ATN_READER,
  KSPROPERTY_RTC_READER
} KSPROPERTY_TIMECODE;

#ifndef TIMECODE_DEFINED
#define TIMECODE_DEFINED
typedef union _timecode {
  struct {
    WORD wFrameRate;
    WORD wFrameFract;
    DWORD dwFrames;
  };
  DWORDLONG qw;
} TIMECODE;
typedef TIMECODE *PTIMECODE;

typedef struct tagTIMECODE_SAMPLE {
  LONGLONG qwTick;
  TIMECODE timecode;
  DWORD dwUser;
  DWORD dwFlags;
} TIMECODE_SAMPLE;

typedef TIMECODE_SAMPLE *PTIMECODE_SAMPLE;
#endif /* TIMECODE_DEFINED */

typedef struct {
  KSPROPERTY Property;
  TIMECODE_SAMPLE TimecodeSamp;
} KSPROPERTY_TIMECODE_S,*PKSPROPERTY_TIMECODE_S;

typedef struct {
  KSP_NODE NodeProperty;
  TIMECODE_SAMPLE TimecodeSamp;
} KSPROPERTY_TIMECODE_NODE_S,*PKSPROPERTY_TIMECODE_NODE_S;

#define STATIC_KSEVENTSETID_EXTDEV_Command				\
	0x109c7988,0xb3cb,0x11d2,0xb4,0x8e,0x00,0x60,0x97,0xb3,0x39,0x1b
DEFINE_GUIDSTRUCT("109c7988-b3cb-11d2-b48e-006097b3391b",KSEVENTSETID_EXTDEV_Command);
#define KSEVENTSETID_EXTDEV_Command DEFINE_GUIDNAMED(KSEVENTSETID_EXTDEV_Command)

typedef enum {
  KSEVENT_EXTDEV_COMMAND_NOTIFY_INTERIM_READY,
  KSEVENT_EXTDEV_COMMAND_CONTROL_INTERIM_READY,
  KSEVENT_EXTDEV_COMMAND_BUSRESET,
  KSEVENT_EXTDEV_TIMECODE_UPDATE,
  KSEVENT_EXTDEV_OPERATION_MODE_UPDATE,
  KSEVENT_EXTDEV_TRANSPORT_STATE_UPDATE,
  KSEVENT_EXTDEV_NOTIFY_REMOVAL,
  KSEVENT_EXTDEV_NOTIFY_MEDIUM_CHANGE
} KSEVENT_DEVCMD;
#endif /* __EDevCtrl__ */

#define STATIC_PROPSETID_VIDCAP_CROSSBAR				\
	0x6a2e0640,0x28e4,0x11d0,0xa1,0x8c,0x00,0xa0,0xc9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("6a2e0640-28e4-11d0-a18c-00a0c9118956",PROPSETID_VIDCAP_CROSSBAR);
#define PROPSETID_VIDCAP_CROSSBAR DEFINE_GUIDNAMED(PROPSETID_VIDCAP_CROSSBAR)

typedef enum {
  KSPROPERTY_CROSSBAR_CAPS,
  KSPROPERTY_CROSSBAR_PININFO,
  KSPROPERTY_CROSSBAR_CAN_ROUTE,
  KSPROPERTY_CROSSBAR_ROUTE
} KSPROPERTY_VIDCAP_CROSSBAR;

typedef struct {
  KSPROPERTY Property;
  ULONG NumberOfInputs;
  ULONG NumberOfOutputs;
} KSPROPERTY_CROSSBAR_CAPS_S,*PKSPROPERTY_CROSSBAR_CAPS_S;

typedef struct {
  KSPROPERTY Property;
  KSPIN_DATAFLOW Direction;
  ULONG Index;
  ULONG PinType;
  ULONG RelatedPinIndex;
  KSPIN_MEDIUM Medium;
} KSPROPERTY_CROSSBAR_PININFO_S,*PKSPROPERTY_CROSSBAR_PININFO_S;

typedef struct {
  KSPROPERTY Property;
  ULONG IndexInputPin;
  ULONG IndexOutputPin;
  ULONG CanRoute;
} KSPROPERTY_CROSSBAR_ROUTE_S,*PKSPROPERTY_CROSSBAR_ROUTE_S;

#define STATIC_EVENTSETID_CROSSBAR					\
	0x6a2e0641,0x28e4,0x11d0,0xa1,0x8c,0x00,0xa0,0xc9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("6a2e0641-28e4-11d0-a18c-00a0c9118956",EVENTSETID_CROSSBAR);
#define EVENTSETID_CROSSBAR DEFINE_GUIDNAMED(EVENTSETID_CROSSBAR)

typedef enum {
  KSEVENT_CROSSBAR_CHANGED
} KSEVENT_CROSSBAR;

typedef enum {
  KS_PhysConn_Video_Tuner = 1,
  KS_PhysConn_Video_Composite,
  KS_PhysConn_Video_SVideo,
  KS_PhysConn_Video_RGB,
  KS_PhysConn_Video_YRYBY,
  KS_PhysConn_Video_SerialDigital,
  KS_PhysConn_Video_ParallelDigital,
  KS_PhysConn_Video_SCSI,
  KS_PhysConn_Video_AUX,
  KS_PhysConn_Video_1394,
  KS_PhysConn_Video_USB,
  KS_PhysConn_Video_VideoDecoder,
  KS_PhysConn_Video_VideoEncoder,
  KS_PhysConn_Video_SCART,
  KS_PhysConn_Audio_Tuner = 4096,
  KS_PhysConn_Audio_Line,
  KS_PhysConn_Audio_Mic,
  KS_PhysConn_Audio_AESDigital,
  KS_PhysConn_Audio_SPDIFDigital,
  KS_PhysConn_Audio_SCSI,
  KS_PhysConn_Audio_AUX,
  KS_PhysConn_Audio_1394,
  KS_PhysConn_Audio_USB,
  KS_PhysConn_Audio_AudioDecoder
} KS_PhysicalConnectorType;

#define STATIC_PROPSETID_VIDCAP_TVAUDIO					\
	0x6a2e0650,0x28e4,0x11d0,0xa1,0x8c,0x00,0xa0,0xc9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("6a2e0650-28e4-11d0-a18c-00a0c9118956",PROPSETID_VIDCAP_TVAUDIO);
#define PROPSETID_VIDCAP_TVAUDIO DEFINE_GUIDNAMED(PROPSETID_VIDCAP_TVAUDIO)

typedef enum {
  KSPROPERTY_TVAUDIO_CAPS,
  KSPROPERTY_TVAUDIO_MODE,
  KSPROPERTY_TVAUDIO_CURRENTLY_AVAILABLE_MODES
} KSPROPERTY_VIDCAP_TVAUDIO;

#define KS_TVAUDIO_MODE_MONO	0x0001
#define KS_TVAUDIO_MODE_STEREO	0x0002
#define KS_TVAUDIO_MODE_LANG_A	0x0010
#define KS_TVAUDIO_MODE_LANG_B	0x0020
#define KS_TVAUDIO_MODE_LANG_C	0x0040

typedef struct {
  KSPROPERTY Property;
  ULONG Capabilities;
  KSPIN_MEDIUM InputMedium;
  KSPIN_MEDIUM OutputMedium;
} KSPROPERTY_TVAUDIO_CAPS_S,*PKSPROPERTY_TVAUDIO_CAPS_S;

typedef struct {
  KSPROPERTY Property;
  ULONG Mode;
} KSPROPERTY_TVAUDIO_S,*PKSPROPERTY_TVAUDIO_S;

#define STATIC_KSEVENTSETID_VIDCAP_TVAUDIO				\
	0x6a2e0651,0x28e4,0x11d0,0xa1,0x8c,0x00,0xa0,0xc9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("6a2e0651-28e4-11d0-a18c-00a0c9118956",KSEVENTSETID_VIDCAP_TVAUDIO);
#define KSEVENTSETID_VIDCAP_TVAUDIO DEFINE_GUIDNAMED(KSEVENTSETID_VIDCAP_TVAUDIO)

typedef enum {
  KSEVENT_TVAUDIO_CHANGED
} KSEVENT_TVAUDIO;

#define STATIC_PROPSETID_VIDCAP_VIDEOCOMPRESSION			\
	0xC6E13343,0x30AC,0x11d0,0xA1,0x8C,0x00,0xA0,0xC9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("C6E13343-30AC-11d0-A18C-00A0C9118956",PROPSETID_VIDCAP_VIDEOCOMPRESSION);
#define PROPSETID_VIDCAP_VIDEOCOMPRESSION DEFINE_GUIDNAMED(PROPSETID_VIDCAP_VIDEOCOMPRESSION)

typedef enum {
  KSPROPERTY_VIDEOCOMPRESSION_GETINFO,
  KSPROPERTY_VIDEOCOMPRESSION_KEYFRAME_RATE,
  KSPROPERTY_VIDEOCOMPRESSION_PFRAMES_PER_KEYFRAME,
  KSPROPERTY_VIDEOCOMPRESSION_QUALITY,
  KSPROPERTY_VIDEOCOMPRESSION_OVERRIDE_KEYFRAME,
  KSPROPERTY_VIDEOCOMPRESSION_OVERRIDE_FRAME_SIZE,
  KSPROPERTY_VIDEOCOMPRESSION_WINDOWSIZE
} KSPROPERTY_VIDCAP_VIDEOCOMPRESSION;

typedef enum {
  KS_CompressionCaps_CanQuality = 1,
  KS_CompressionCaps_CanCrunch = 2,
  KS_CompressionCaps_CanKeyFrame = 4,
  KS_CompressionCaps_CanBFrame = 8,
  KS_CompressionCaps_CanWindow = 0x10
} KS_CompressionCaps;

typedef enum {
  KS_StreamingHint_FrameInterval = 0x0100,
  KS_StreamingHint_KeyFrameRate = 0x0200,
  KS_StreamingHint_PFrameRate = 0x0400,
  KS_StreamingHint_CompQuality = 0x0800,
  KS_StreamingHint_CompWindowSize = 0x1000
} KS_VideoStreamingHints;

typedef struct {
  KSPROPERTY Property;
  ULONG StreamIndex;
  LONG DefaultKeyFrameRate;
  LONG DefaultPFrameRate;
  LONG DefaultQuality;
  LONG NumberOfQualitySettings;
  LONG Capabilities;
} KSPROPERTY_VIDEOCOMPRESSION_GETINFO_S,*PKSPROPERTY_VIDEOCOMPRESSION_GETINFO_S;

typedef struct {
  KSPROPERTY Property;
  ULONG StreamIndex;
  LONG Value;
} KSPROPERTY_VIDEOCOMPRESSION_S,*PKSPROPERTY_VIDEOCOMPRESSION_S;

typedef struct {
  KSPROPERTY Property;
  ULONG StreamIndex;
  LONG Value;
  ULONG Flags;
} KSPROPERTY_VIDEOCOMPRESSION_S1,*PKSPROPERTY_VIDEOCOMPRESSION_S1;

#define STATIC_KSDATAFORMAT_SUBTYPE_OVERLAY				\
	0xe436eb7f,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70
DEFINE_GUIDSTRUCT("e436eb7f-524f-11ce-9f53-0020af0ba770",KSDATAFORMAT_SUBTYPE_OVERLAY);
#define KSDATAFORMAT_SUBTYPE_OVERLAY DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_OVERLAY)

#define STATIC_KSPROPSETID_OverlayUpdate				\
	0x490EA5CF,0x7681,0x11D1,0xA2,0x1C,0x00,0xA0,0xC9,0x22,0x31,0x96
DEFINE_GUIDSTRUCT("490EA5CF-7681-11D1-A21C-00A0C9223196",KSPROPSETID_OverlayUpdate);
#define KSPROPSETID_OverlayUpdate DEFINE_GUIDNAMED(KSPROPSETID_OverlayUpdate)

typedef enum {
  KSPROPERTY_OVERLAYUPDATE_INTERESTS,
  KSPROPERTY_OVERLAYUPDATE_CLIPLIST = 0x1,
  KSPROPERTY_OVERLAYUPDATE_PALETTE = 0x2,
  KSPROPERTY_OVERLAYUPDATE_COLORKEY = 0x4,
  KSPROPERTY_OVERLAYUPDATE_VIDEOPOSITION = 0x8,
  KSPROPERTY_OVERLAYUPDATE_DISPLAYCHANGE = 0x10,
  KSPROPERTY_OVERLAYUPDATE_COLORREF = 0x10000000
} KSPROPERTY_OVERLAYUPDATE;

typedef struct {
  ULONG PelsWidth;
  ULONG PelsHeight;
  ULONG BitsPerPel;
  WCHAR DeviceID[1];
} KSDISPLAYCHANGE,*PKSDISPLAYCHANGE;

#define DEFINE_KSPROPERTY_ITEM_OVERLAYUPDATE_INTERESTS(Handler)		\
	DEFINE_KSPROPERTY_ITEM(						\
				KSPROPERTY_OVERLAYUPDATE_INTERESTS,	\
				(Handler),				\
				sizeof(KSPROPERTY),			\
				sizeof(ULONG),				\
				NULL, NULL, 0, NULL, NULL, 0)

#define DEFINE_KSPROPERTY_ITEM_OVERLAYUPDATE_PALETTE(Handler)		\
	DEFINE_KSPROPERTY_ITEM(						\
				KSPROPERTY_OVERLAYUPDATE_PALETTE,	\
				NULL,					\
				sizeof(KSPROPERTY),			\
				0,					\
				(Handler),				\
				NULL, 0, NULL, NULL, 0)

#define DEFINE_KSPROPERTY_ITEM_OVERLAYUPDATE_COLORKEY(Handler)		\
	DEFINE_KSPROPERTY_ITEM(						\
				KSPROPERTY_OVERLAYUPDATE_COLORKEY,	\
				NULL,					\
				sizeof(KSPROPERTY),			\
				sizeof(COLORKEY),			\
				(Handler),				\
				NULL, 0, NULL, NULL, 0)

#define DEFINE_KSPROPERTY_ITEM_OVERLAYUPDATE_CLIPLIST(Handler)		\
	DEFINE_KSPROPERTY_ITEM(						\
				KSPROPERTY_OVERLAYUPDATE_CLIPLIST,	\
				NULL,					\
				sizeof(KSPROPERTY),			\
				2 *sizeof(RECT) + sizeof(RGNDATAHEADER),\
				(Handler),				\
				NULL, 0, NULL, NULL, 0)

#define DEFINE_KSPROPERTY_ITEM_OVERLAYUPDATE_VIDEOPOSITION(Handler)	\
	DEFINE_KSPROPERTY_ITEM(						\
				KSPROPERTY_OVERLAYUPDATE_VIDEOPOSITION,	\
				NULL,					\
				sizeof(KSPROPERTY),			\
				2 *sizeof(RECT),			\
				(Handler),				\
				NULL, 0, NULL, NULL, 0)

#define DEFINE_KSPROPERTY_ITEM_OVERLAYUPDATE_DISPLAYCHANGE(Handler)	\
	DEFINE_KSPROPERTY_ITEM(						\
				KSPROPERTY_OVERLAYUPDATE_DISPLAYCHANGE,	\
				NULL,					\
				sizeof(KSPROPERTY),			\
				sizeof(KSDISPLAYCHANGE),		\
				(Handler),				\
				NULL, 0, NULL, NULL, 0)

#define DEFINE_KSPROPERTY_ITEM_OVERLAYUPDATE_COLORREF(Handler)		\
	DEFINE_KSPROPERTY_ITEM(						\
				KSPROPERTY_OVERLAYUPDATE_COLORREF,	\
				(Handler),				\
				sizeof(KSPROPERTY),			\
				sizeof(COLORREF),			\
				NULL,					\
				NULL, 0, NULL, NULL, 0)

#define STATIC_PROPSETID_VIDCAP_VIDEOCONTROL				\
	0x6a2e0670,0x28e4,0x11d0,0xa1,0x8c,0x00,0xa0,0xc9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("6a2e0670-28e4-11d0-a18c-00a0c9118956",PROPSETID_VIDCAP_VIDEOCONTROL);
#define PROPSETID_VIDCAP_VIDEOCONTROL DEFINE_GUIDNAMED(PROPSETID_VIDCAP_VIDEOCONTROL)

typedef enum {
  KSPROPERTY_VIDEOCONTROL_CAPS,
  KSPROPERTY_VIDEOCONTROL_ACTUAL_FRAME_RATE,
  KSPROPERTY_VIDEOCONTROL_FRAME_RATES,
  KSPROPERTY_VIDEOCONTROL_MODE
} KSPROPERTY_VIDCAP_VIDEOCONTROL;

typedef enum {
  KS_VideoControlFlag_FlipHorizontal = 0x0001,
  KS_VideoControlFlag_FlipVertical = 0x0002,
  KS_Obsolete_VideoControlFlag_ExternalTriggerEnable = 0x0010,
  KS_Obsolete_VideoControlFlag_Trigger = 0x0020,
  KS_VideoControlFlag_ExternalTriggerEnable = 0x0004,
  KS_VideoControlFlag_Trigger = 0x0008
} KS_VideoControlFlags;

typedef struct {
  KSPROPERTY Property;
  ULONG StreamIndex;
  ULONG VideoControlCaps;
} KSPROPERTY_VIDEOCONTROL_CAPS_S,*PKSPROPERTY_VIDEOCONTROL_CAPS_S;

typedef struct {
  KSPROPERTY Property;
  ULONG StreamIndex;
  LONG Mode;
} KSPROPERTY_VIDEOCONTROL_MODE_S,*PKSPROPERTY_VIDEOCONTROL_MODE_S;

typedef struct {
  KSPROPERTY Property;
  ULONG StreamIndex;
  ULONG RangeIndex;
  SIZE Dimensions;
  LONGLONG CurrentActualFrameRate;
  LONGLONG CurrentMaxAvailableFrameRate;
} KSPROPERTY_VIDEOCONTROL_ACTUAL_FRAME_RATE_S,*PKSPROPERTY_VIDEOCONTROL_ACTUAL_FRAME_RATE_S;

typedef struct {
  KSPROPERTY Property;
  ULONG StreamIndex;
  ULONG RangeIndex;
  SIZE Dimensions;
} KSPROPERTY_VIDEOCONTROL_FRAME_RATES_S,*PKSPROPERTY_VIDEOCONTROL_FRAME_RATES_S;

#define STATIC_PROPSETID_VIDCAP_DROPPEDFRAMES				\
	0xC6E13344,0x30AC,0x11d0,0xa1,0x8c,0x00,0xa0,0xc9,0x11,0x89,0x56
DEFINE_GUIDSTRUCT("C6E13344-30AC-11d0-A18C-00A0C9118956",PROPSETID_VIDCAP_DROPPEDFRAMES);
#define PROPSETID_VIDCAP_DROPPEDFRAMES DEFINE_GUIDNAMED(PROPSETID_VIDCAP_DROPPEDFRAMES)

typedef enum {
  KSPROPERTY_DROPPEDFRAMES_CURRENT
} KSPROPERTY_VIDCAP_DROPPEDFRAMES;

typedef struct {
  KSPROPERTY Property;
  LONGLONG PictureNumber;
  LONGLONG DropCount;
  ULONG AverageFrameSize;
} KSPROPERTY_DROPPEDFRAMES_CURRENT_S,*PKSPROPERTY_DROPPEDFRAMES_CURRENT_S;

#define STATIC_KSPROPSETID_VPConfig					\
	0xbc29a660,0x30e3,0x11d0,0x9e,0x69,0x00,0xc0,0x4f,0xd7,0xc1,0x5b
DEFINE_GUIDSTRUCT("bc29a660-30e3-11d0-9e69-00c04fd7c15b",KSPROPSETID_VPConfig);
#define KSPROPSETID_VPConfig DEFINE_GUIDNAMED(KSPROPSETID_VPConfig)

#define STATIC_KSPROPSETID_VPVBIConfig					\
	0xec529b00,0x1a1f,0x11d1,0xba,0xd9,0x0,0x60,0x97,0x44,0x11,0x1a
DEFINE_GUIDSTRUCT("ec529b00-1a1f-11d1-bad9-00609744111a",KSPROPSETID_VPVBIConfig);
#define KSPROPSETID_VPVBIConfig DEFINE_GUIDNAMED(KSPROPSETID_VPVBIConfig)

typedef enum {
  KSPROPERTY_VPCONFIG_NUMCONNECTINFO,
  KSPROPERTY_VPCONFIG_GETCONNECTINFO,
  KSPROPERTY_VPCONFIG_SETCONNECTINFO,
  KSPROPERTY_VPCONFIG_VPDATAINFO,
  KSPROPERTY_VPCONFIG_MAXPIXELRATE,
  KSPROPERTY_VPCONFIG_INFORMVPINPUT,
  KSPROPERTY_VPCONFIG_NUMVIDEOFORMAT,
  KSPROPERTY_VPCONFIG_GETVIDEOFORMAT,
  KSPROPERTY_VPCONFIG_SETVIDEOFORMAT,
  KSPROPERTY_VPCONFIG_INVERTPOLARITY,
  KSPROPERTY_VPCONFIG_DECIMATIONCAPABILITY,
  KSPROPERTY_VPCONFIG_SCALEFACTOR,
  KSPROPERTY_VPCONFIG_DDRAWHANDLE,
  KSPROPERTY_VPCONFIG_VIDEOPORTID,
  KSPROPERTY_VPCONFIG_DDRAWSURFACEHANDLE,
  KSPROPERTY_VPCONFIG_SURFACEPARAMS
} KSPROPERTY_VPCONFIG;

#define STATIC_CLSID_KsIBasicAudioInterfaceHandler			\
	0xb9f8ac3e,0x0f71,0x11d2,0xb7,0x2c,0x00,0xc0,0x4f,0xb6,0xbd,0x3d
DEFINE_GUIDSTRUCT("b9f8ac3e-0f71-11d2-b72c-00c04fb6bd3d",CLSID_KsIBasicAudioInterfaceHandler);
#define CLSID_KsIBasicAudioInterfaceHandler DEFINE_GUIDNAMED(CLSID_KsIBasicAudioInterfaceHandler)

#ifdef __IVPType__
typedef struct {
  AMVPSIZE Size;
  DWORD MaxPixelsPerSecond;
  DWORD Reserved;
} KSVPMAXPIXELRATE,*PKSVPMAXPIXELRATE;

typedef struct {
  KSPROPERTY Property;
  AMVPSIZE Size;
} KSVPSIZE_PROP,*PKSVPSIZE_PROP;

typedef struct {
  DWORD dwPitch;
  DWORD dwXOrigin;
  DWORD dwYOrigin;
} KSVPSURFACEPARAMS,*PKSVPSURFACEPARAMS;
#else /* __IVPType__ */

#ifndef __DDRAW_INCLUDED__
#define DDPF_FOURCC __MSABI_LONG(0x00000004)

#ifndef _DDPIXELFORMAT_DEFINED
#define _DDPIXELFORMAT_DEFINED
typedef struct _DDPIXELFORMAT
{
  DWORD dwSize;
  DWORD dwFlags;
  DWORD dwFourCC;
  __C89_NAMELESS union
  {
    DWORD dwRGBBitCount;
    DWORD dwYUVBitCount;
    DWORD dwZBufferBitDepth;
    DWORD dwAlphaBitDepth;
  };
  __C89_NAMELESS union
  {
    DWORD dwRBitMask;
    DWORD dwYBitMask;
  };
  __C89_NAMELESS union
  {
    DWORD dwGBitMask;
    DWORD dwUBitMask;
  };
  __C89_NAMELESS union
  {
    DWORD dwBBitMask;
    DWORD dwVBitMask;
  };
  __C89_NAMELESS union
  {
    DWORD dwRGBAlphaBitMask;
    DWORD dwYUVAlphaBitMask;
    DWORD dwRGBZBitMask;
    DWORD dwYUVZBitMask;
  };
} DDPIXELFORMAT,*LPDDPIXELFORMAT;
#endif /* _DDPIXELFORMAT_DEFINED */

#endif /* __DDRAW_INCLUDED__ */

#ifndef __DVP_INCLUDED__
typedef struct _DDVIDEOPORTCONNECT {
  DWORD dwSize;
  DWORD dwPortWidth;
  GUID guidTypeID;
  DWORD dwFlags;
  ULONG_PTR dwReserved1;
} DDVIDEOPORTCONNECT,*LPDDVIDEOPORTCONNECT;

#define DDVPTYPE_E_HREFH_VREFH						\
	0x54F39980,0xDA60,0x11CF,0x9B,0x06,0x00,0xA0,0xC9,0x03,0xA3,0xB8

#define DDVPTYPE_E_HREFL_VREFL						\
	0xE09C77E0,0xDA60,0x11CF,0x9B,0x06,0x00,0xA0,0xC9,0x03,0xA3,0xB8
#endif /* __DVP_INCLUDED__ */

typedef enum
{
  KS_PixAspectRatio_NTSC4x3,
  KS_PixAspectRatio_NTSC16x9,
  KS_PixAspectRatio_PAL4x3,
  KS_PixAspectRatio_PAL16x9
} KS_AMPixAspectRatio;

typedef enum
{
  KS_AMVP_DO_NOT_CARE,
  KS_AMVP_BEST_BANDWIDTH,
  KS_AMVP_INPUT_SAME_AS_OUTPUT
} KS_AMVP_SELECTFORMATBY;

typedef enum
{
  KS_AMVP_MODE_WEAVE,
  KS_AMVP_MODE_BOBINTERLEAVED,
  KS_AMVP_MODE_BOBNONINTERLEAVED,
  KS_AMVP_MODE_SKIPEVEN,
  KS_AMVP_MODE_SKIPODD
} KS_AMVP_MODE;

typedef struct tagKS_AMVPDIMINFO
{
  DWORD dwFieldWidth;
  DWORD dwFieldHeight;
  DWORD dwVBIWidth;
  DWORD dwVBIHeight;
  RECT rcValidRegion;
} KS_AMVPDIMINFO,*PKS_AMVPDIMINFO;

typedef struct tagKS_AMVPDATAINFO
{
  DWORD dwSize;
  DWORD dwMicrosecondsPerField;
  KS_AMVPDIMINFO amvpDimInfo;
  DWORD dwPictAspectRatioX;
  DWORD dwPictAspectRatioY;
  WINBOOL bEnableDoubleClock;
  WINBOOL bEnableVACT;
  WINBOOL bDataIsInterlaced;
  LONG lHalfLinesOdd;
  WINBOOL bFieldPolarityInverted;
  DWORD dwNumLinesInVREF;
  LONG lHalfLinesEven;
  DWORD dwReserved1;
} KS_AMVPDATAINFO,*PKS_AMVPDATAINFO;

typedef struct tagKS_AMVPSIZE
{
  DWORD dwWidth;
  DWORD dwHeight;
} KS_AMVPSIZE,*PKS_AMVPSIZE;

typedef struct {
  KS_AMVPSIZE Size;
  DWORD MaxPixelsPerSecond;
  DWORD Reserved;
} KSVPMAXPIXELRATE,*PKSVPMAXPIXELRATE;

typedef struct {
  KSPROPERTY Property;
  KS_AMVPSIZE Size;
} KSVPSIZE_PROP,*PKSVPSIZE_PROP;

typedef struct {
  DWORD dwPitch;
  DWORD dwXOrigin;
  DWORD dwYOrigin;
} KSVPSURFACEPARAMS,*PKSVPSURFACEPARAMS;
#endif /* __IVPType__ */

#define STATIC_KSEVENTSETID_VPNotify					\
	0x20c5598e,0xd3c8,0x11d0,0x8d,0xfc,0x00,0xc0,0x4f,0xd7,0xc0,0x8b
DEFINE_GUIDSTRUCT("20c5598e-d3c8-11d0-8dfc-00c04fd7c08b",KSEVENTSETID_VPNotify);
#define KSEVENTSETID_VPNotify DEFINE_GUIDNAMED(KSEVENTSETID_VPNotify)

typedef enum {
  KSEVENT_VPNOTIFY_FORMATCHANGE
} KSEVENT_VPNOTIFY;

#define STATIC_KSEVENTSETID_VIDCAPTOSTI					\
	0xdb47de20,0xf628,0x11d1,0xba,0x41,0x0,0xa0,0xc9,0xd,0x2b,0x5
DEFINE_GUIDSTRUCT("DB47DE20-F628-11d1-BA41-00A0C90D2B05",KSEVENTSETID_VIDCAPTOSTI);
#define KSEVENTSETID_VIDCAPNotify DEFINE_GUIDNAMED(KSEVENTSETID_VIDCAPTOSTI)

typedef enum {
  KSEVENT_VIDCAPTOSTI_EXT_TRIGGER,
  KSEVENT_VIDCAP_AUTO_UPDATE,
  KSEVENT_VIDCAP_SEARCH
} KSEVENT_VIDCAPTOSTI;

typedef enum {
  KSPROPERTY_EXTENSION_UNIT_INFO,
  KSPROPERTY_EXTENSION_UNIT_CONTROL,
  KSPROPERTY_EXTENSION_UNIT_PASS_THROUGH = 0xffff
} KSPROPERTY_EXTENSION_UNIT,*PKSPROPERTY_EXTENSION_UNIT;

#define STATIC_KSEVENTSETID_VPVBINotify					\
	0xec529b01,0x1a1f,0x11d1,0xba,0xd9,0x0,0x60,0x97,0x44,0x11,0x1a
DEFINE_GUIDSTRUCT("ec529b01-1a1f-11d1-bad9-00609744111a",KSEVENTSETID_VPVBINotify);
#define KSEVENTSETID_VPVBINotify DEFINE_GUIDNAMED(KSEVENTSETID_VPVBINotify)

typedef enum {
  KSEVENT_VPVBINOTIFY_FORMATCHANGE
} KSEVENT_VPVBINOTIFY;

#define STATIC_KSDATAFORMAT_TYPE_AUXLine21Data				\
	0x670aea80,0x3a82,0x11d0,0xb7,0x9b,0x00,0xaa,0x00,0x37,0x67,0xa7
DEFINE_GUIDSTRUCT("670aea80-3a82-11d0-b79b-00aa003767a7",KSDATAFORMAT_TYPE_AUXLine21Data);
#define KSDATAFORMAT_TYPE_AUXLine21Data DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_AUXLine21Data)

#define STATIC_KSDATAFORMAT_SUBTYPE_Line21_BytePair			\
	0x6e8d4a22,0x310c,0x11d0,0xb7,0x9a,0x00,0xaa,0x00,0x37,0x67,0xa7
DEFINE_GUIDSTRUCT("6e8d4a22-310c-11d0-b79a-00aa003767a7",KSDATAFORMAT_SUBTYPE_Line21_BytePair);
#define KSDATAFORMAT_SUBTYPE_Line21_BytePair DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_Line21_BytePair)

#define STATIC_KSDATAFORMAT_SUBTYPE_Line21_GOPPacket			\
	0x6e8d4a23,0x310c,0x11d0,0xb7,0x9a,0x00,0xaa,0x00,0x37,0x67,0xa7
DEFINE_GUIDSTRUCT("6e8d4a23-310c-11d0-b79a-00aa003767a7",KSDATAFORMAT_SUBTYPE_Line21_GOPPacket);
#define KSDATAFORMAT_SUBTYPE_Line21_GOPPacket DEFINE_GUIDNAMED(KSDATAFORMAT_SUBTYPE_Line21_GOPPacket)

typedef struct _KSGOP_USERDATA {
  ULONG sc;
  ULONG reserved1;
  BYTE cFields;
  CHAR l21Data[3];
} KSGOP_USERDATA,*PKSGOP_USERDATA;

#define STATIC_KSDATAFORMAT_TYPE_DVD_ENCRYPTED_PACK			\
	0xed0b916a,0x044d,0x11d1,0xaa,0x78,0x00,0xc0,0x4f,0xc3,0x1d,0x60
DEFINE_GUIDSTRUCT("ed0b916a-044d-11d1-aa78-00c04fc31d60",KSDATAFORMAT_TYPE_DVD_ENCRYPTED_PACK);
#define KSDATAFORMAT_TYPE_DVD_ENCRYPTED_PACK DEFINE_GUIDNAMED(KSDATAFORMAT_TYPE_DVD_ENCRYPTED_PACK)

#define KS_AM_UseNewCSSKey			0x1

#define STATIC_KSPROPSETID_TSRateChange					\
	0xa503c5c0,0x1d1d,0x11d1,0xad,0x80,0x44,0x45,0x53,0x54,0x0,0x0
DEFINE_GUIDSTRUCT("A503C5C0-1D1D-11D1-AD80-************",KSPROPSETID_TSRateChange);
#define KSPROPSETID_TSRateChange DEFINE_GUIDNAMED(KSPROPSETID_TSRateChange)

typedef enum {
  KS_AM_RATE_SimpleRateChange = 1,
  KS_AM_RATE_ExactRateChange = 2,
  KS_AM_RATE_MaxFullDataRate = 3,
  KS_AM_RATE_Step = 4
} KS_AM_PROPERTY_TS_RATE_CHANGE;

typedef struct {
  REFERENCE_TIME StartTime;
  LONG Rate;
} KS_AM_SimpleRateChange,*PKS_AM_SimpleRateChange;

typedef struct {
  REFERENCE_TIME OutputZeroTime;
  LONG Rate;
} KS_AM_ExactRateChange,*PKS_AM_ExactRateChange;

typedef LONG KS_AM_MaxFullDataRate;
typedef DWORD KS_AM_Step;

#define STATIC_KSCATEGORY_ENCODER					\
	0x19689bf6,0xc384,0x48fd,0xad,0x51,0x90,0xe5,0x8c,0x79,0xf7,0xb
DEFINE_GUIDSTRUCT("19689BF6-C384-48fd-AD51-90E58C79F70B",KSCATEGORY_ENCODER);
#define KSCATEGORY_ENCODER DEFINE_GUIDNAMED(KSCATEGORY_ENCODER)

#define STATIC_KSCATEGORY_MULTIPLEXER					\
	0x7a5de1d3,0x1a1,0x452c,0xb4,0x81,0x4f,0xa2,0xb9,0x62,0x71,0xe8
DEFINE_GUIDSTRUCT("7A5DE1D3-01A1-452c-B481-4FA2B96271E8",KSCATEGORY_MULTIPLEXER);
#define KSCATEGORY_MULTIPLEXER DEFINE_GUIDNAMED(KSCATEGORY_MULTIPLEXER)

#ifndef __ENCODER_API_GUIDS__
#define __ENCODER_API_GUIDS__

#define STATIC_ENCAPIPARAM_BITRATE					\
	0x49cc4c43,0xca83,0x4ad4,0xa9,0xaf,0xf3,0x69,0x6a,0xf6,0x66,0xdf
DEFINE_GUIDSTRUCT("49CC4C43-CA83-4ad4-A9AF-F3696AF666DF",ENCAPIPARAM_BITRATE);
#define ENCAPIPARAM_BITRATE DEFINE_GUIDNAMED(ENCAPIPARAM_BITRATE)

#define STATIC_ENCAPIPARAM_PEAK_BITRATE					\
	0x703f16a9,0x3d48,0x44a1,0xb0,0x77,0x1,0x8d,0xff,0x91,0x5d,0x19
DEFINE_GUIDSTRUCT("703F16A9-3D48-44a1-B077-018DFF915D19",ENCAPIPARAM_PEAK_BITRATE);
#define ENCAPIPARAM_PEAK_BITRATE DEFINE_GUIDNAMED(ENCAPIPARAM_PEAK_BITRATE)

#define STATIC_ENCAPIPARAM_BITRATE_MODE					\
	0xee5fb25c,0xc713,0x40d1,0x9d,0x58,0xc0,0xd7,0x24,0x1e,0x25,0xf
DEFINE_GUIDSTRUCT("EE5FB25C-C713-40d1-9D58-C0D7241E250F",ENCAPIPARAM_BITRATE_MODE);
#define ENCAPIPARAM_BITRATE_MODE DEFINE_GUIDNAMED(ENCAPIPARAM_BITRATE_MODE)

#define STATIC_CODECAPI_CHANGELISTS					\
	0x62b12acf,0xf6b0,0x47d9,0x94,0x56,0x96,0xf2,0x2c,0x4e,0x0b,0x9d
DEFINE_GUIDSTRUCT("62B12ACF-F6B0-47D9-9456-96F22C4E0B9D",CODECAPI_CHANGELISTS);
#define CODECAPI_CHANGELISTS DEFINE_GUIDNAMED(CODECAPI_CHANGELISTS)

#define STATIC_CODECAPI_VIDEO_ENCODER					\
	0x7112e8e1,0x3d03,0x47ef,0x8e,0x60,0x03,0xf1,0xcf,0x53,0x73,0x01
DEFINE_GUIDSTRUCT("7112E8E1-3D03-47EF-8E60-03F1CF537301",CODECAPI_VIDEO_ENCODER);
#define CODECAPI_VIDEO_ENCODER DEFINE_GUIDNAMED(CODECAPI_VIDEO_ENCODER)

#define STATIC_CODECAPI_AUDIO_ENCODER					\
	0xb9d19a3e,0xf897,0x429c,0xbc,0x46,0x81,0x38,0xb7,0x27,0x2b,0x2d
DEFINE_GUIDSTRUCT("B9D19A3E-F897-429C-BC46-8138B7272B2D",CODECAPI_AUDIO_ENCODER);
#define CODECAPI_AUDIO_ENCODER DEFINE_GUIDNAMED(CODECAPI_AUDIO_ENCODER)

#define STATIC_CODECAPI_SETALLDEFAULTS					\
	0x6c5e6a7c,0xacf8,0x4f55,0xa9,0x99,0x1a,0x62,0x81,0x09,0x05,0x1b
DEFINE_GUIDSTRUCT("6C5E6A7C-ACF8-4F55-A999-1A628109051B",CODECAPI_SETALLDEFAULTS);
#define CODECAPI_SETALLDEFAULTS DEFINE_GUIDNAMED(CODECAPI_SETALLDEFAULTS)

#define STATIC_CODECAPI_ALLSETTINGS					\
	0x6a577e92,0x83e1,0x4113,0xad,0xc2,0x4f,0xce,0xc3,0x2f,0x83,0xa1
DEFINE_GUIDSTRUCT("6A577E92-83E1-4113-ADC2-4FCEC32F83A1",CODECAPI_ALLSETTINGS);
#define CODECAPI_ALLSETTINGS DEFINE_GUIDNAMED(CODECAPI_ALLSETTINGS)

#define STATIC_CODECAPI_SUPPORTSEVENTS					\
	0x0581af97,0x7693,0x4dbd,0x9d,0xca,0x3f,0x9e,0xbd,0x65,0x85,0xa1
DEFINE_GUIDSTRUCT("0581AF97-7693-4DBD-9DCA-3F9EBD6585A1",CODECAPI_SUPPORTSEVENTS);
#define CODECAPI_SUPPORTSEVENTS DEFINE_GUIDNAMED(CODECAPI_SUPPORTSEVENTS)

#define STATIC_CODECAPI_CURRENTCHANGELIST				\
	0x1cb14e83,0x7d72,0x4657,0x83,0xfd,0x47,0xa2,0xc5,0xb9,0xd1,0x3d
DEFINE_GUIDSTRUCT("1CB14E83-7D72-4657-83FD-47A2C5B9D13D",CODECAPI_CURRENTCHANGELIST);
#define CODECAPI_CURRENTCHANGELIST DEFINE_GUIDNAMED(CODECAPI_CURRENTCHANGELIST)
#endif /* __ENCODER_API_GUIDS__ */

#ifndef __ENCODER_API_DEFINES__
#define __ENCODER_API_DEFINES__
typedef enum {
  ConstantBitRate = 0,
  VariableBitRateAverage,
  VariableBitRatePeak
} VIDEOENCODER_BITRATE_MODE;
#endif /* __ENCODER_API_DEFINES__ */

#if (_WIN32_WINNT >= 0x0601)
typedef enum _TunerDecoderLockType {
  Tuner_LockType_None                        = 0x00,
  Tuner_LockType_Within_Scan_Sensing_Range   = 0x01,
  Tuner_LockType_Locked                      = 0x02
} TunerLockType;
#endif /*(_WIN32_WINNT >= 0x0601)*/

/* From devicetopology.h */
typedef enum __WIDL_devicetopology_generated_name_00000002 {
    eConnTypeUnknown = 0,
    eConnType3Point5mm = 1,
    eConnTypeQuarter = 2,
    eConnTypeAtapiInternal = 3,
    eConnTypeRCA = 4,
    eConnTypeOptical = 5,
    eConnTypeOtherDigital = 6,
    eConnTypeOtherAnalog = 7,
    eConnTypeMultichannelAnalogDIN = 8,
    eConnTypeXlrProfessional = 9,
    eConnTypeRj11Modem = 10,
    eConnTypeRJ11Modem = eConnTypeRj11Modem,
    eConnTypeCombination = 11
} EPcxConnectionType;

typedef enum __WIDL_devicetopology_generated_name_00000003 {
    eGeoLocRear = 1,
    eGeoLocFront = 2,
    eGeoLocLeft = 3,
    eGeoLocRight = 4,
    eGeoLocTop = 5,
    eGeoLocBottom = 6,
    eGeoLocRearPanel = 7,
    eGeoLocRiser = 8,
    eGeoLocInsideMobileLid = 9,
    eGeoLocDrivebay = 10,
    eGeoLocHDMI = 11,
    eGeoLocOutsideMobileLid = 12,
    eGeoLocATAPI = 13,
    eGeoLocReserved5 = 14,
    eGeoLocReserved6 = 15
} EPcxGeoLocation;

typedef enum __WIDL_devicetopology_generated_name_00000004 {
    eGenLocPrimaryBox = 0,
    eGenLocInternal = 1,
    eGenLocSeparate = 2,
    eGenLocOther = 3
} EPcxGenLocation;

typedef enum __WIDL_devicetopology_generated_name_00000005 {
    ePortConnJack = 0,
    ePortConnIntegratedDevice = 1,
    ePortConnBothIntegratedAndJack = 2,
    ePortConnUnknown = 3
} EPxcPortConnection;

typedef struct __WIDL_devicetopology_generated_name_00000006 {
    DWORD ChannelMapping;
    COLORREF Color;
    EPcxConnectionType ConnectionType;
    EPcxGeoLocation GeoLocation;
    EPcxGenLocation GenLocation;
    EPxcPortConnection PortConnection;
    WINBOOL IsConnected;
} KSJACK_DESCRIPTION;

typedef KSJACK_DESCRIPTION *PKSJACK_DESCRIPTION;

typedef enum __WIDL_devicetopology_generated_name_00000007 {
    KSJACK_SINK_CONNECTIONTYPE_HDMI = 0,
    KSJACK_SINK_CONNECTIONTYPE_DISPLAYPORT = 1
} KSJACK_SINK_CONNECTIONTYPE;

typedef struct _tagKSJACK_SINK_INFORMATION {
    KSJACK_SINK_CONNECTIONTYPE ConnType;
    WORD ManufacturerId;
    WORD ProductId;
    WORD AudioLatency;
    WINBOOL HDCPCapable;
    WINBOOL AICapable;
    UCHAR SinkDescriptionLength;
    WCHAR SinkDescription[32];
    LUID PortId;
} KSJACK_SINK_INFORMATION;

typedef struct _tagKSJACK_DESCRIPTION2 {
    DWORD DeviceStateInfo;
    DWORD JackCapabilities;
} KSJACK_DESCRIPTION2;

typedef struct _tagKSJACK_DESCRIPTION2 *PKSJACK_DESCRIPTION2;

#endif /* _KSMEDIA_ */

