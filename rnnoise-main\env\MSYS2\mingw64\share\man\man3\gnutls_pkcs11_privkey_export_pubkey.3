.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_privkey_export_pubkey" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_privkey_export_pubkey \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_privkey_export_pubkey(gnutls_pkcs11_privkey_t " pkey ", gnutls_x509_crt_fmt_t " fmt ", gnutls_datum_t * " data ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_privkey_t pkey" 12
The private key
.IP "gnutls_x509_crt_fmt_t fmt" 12
the format of output params. PEM or DER.
.IP "gnutls_datum_t * data" 12
will hold the public key
.IP "unsigned int flags" 12
should be zero
.SH "DESCRIPTION"
This function will extract the public key (modulus and public
exponent) from the private key specified by the  \fIurl\fP private key.
This public key will be stored in  \fIpubkey\fP in the format specified
by  \fIfmt\fP .  \fIpubkey\fP should be deinitialized using \fBgnutls_free()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.3.7
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
