## Syntax highlighting for Python.

syntax python "\.py$"
header "^#!.*python"
magic "Python script"
comment "#"

# Alternative linter: pylint --exit-zero
linter pyflakes

# Function definitions.
color brightblue "def [[:alnum:]_]+"
# Keywords.
color brightcyan "\<(and|as|assert|async|await|break|class|continue)\>"
color brightcyan "\<(def|del|elif|else|except|finally|for|from)\>"
color brightcyan "\<(global|if|import|in|is|lambda|nonlocal|not|or)\>"
color brightcyan "\<(pass|raise|return|try|while|with|yield)\>"

# These two are keywords in Python 2, but functions in Python 3,
# so only color them when they are followed by whitespace, assuming
# that print(x) is a function invocation and print (x) is a statement.
color brightcyan "\<(exec|print)([[:blank:]]|$)"

# Special values.
color brightmagenta "\<(False|None|True)\>"

# Decorators.
color cyan "@[[:alpha:]_][[:alnum:]_.]*"

# Mono-quoted strings.
color brightgreen "'([^'\]|\\.)*'|"([^"\]|\\.)*"|'''|""""
color normal "'''|""""
# Comments.
color brightred "(^|[[:blank:]])#.*"
# Triple-quoted strings.
color brightgreen start="'''([^'),]|$)" end="(^|[^(\])'''"
color brightgreen start=""""([^"),]|$)" end="(^|[^(\])""""

# Backslash escapes.
color lime "\\($|[\'"abfnrtv]|[0-3]?[0-7]?[0-7]|x[[:xdigit:]]{2})"
color lime "\\(N\{[[:alpha:]]+\}|u[[:xdigit:]]{4}|U[[:xdigit:]]{8})"

# Reminders.
color brightwhite,yellow "\<(FIXME|TODO|XXX)\>"

# Trailing whitespace.
color ,green "[[:space:]]+$"
