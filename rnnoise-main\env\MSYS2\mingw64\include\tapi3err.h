/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef __TAPI3ERR_H__
#define __TAPI3ERR_H__

#define TAPI_E_NOTENOUGHMEMORY ((HRESULT)0x80040001)
#define TAPI_E_NOITEMS ((HRESULT)0x80040002)
#define TAPI_E_NOTSUPPORTED ((HRESULT)0x80040003)
#define TAPI_E_INVALIDMEDIATYPE ((HRESULT)0x80040004)
#define TAPI_E_OPERATIONFAILED ((HRESULT)0x80040005)
#define TAPI_E_ALLOCATED ((HRESULT)0x80040006)
#define TAPI_E_CALLUNAVAIL ((HRESULT)0x80040007)
#define TAPI_E_COMPLETIONOVERRUN ((HRESULT)0x80040008)
#define TAPI_E_CONFERENCEFULL ((HRESULT)0x80040009)
#define TAPI_E_DIALMODIFIERNOTSUPPORTED ((HRESULT)0x8004000A)
#define TAPI_E_INUSE ((HRESULT)0x8004000B)
#define TAPI_E_INVALADDRESS ((HRESULT)0x8004000C)
#define TAPI_E_INVALADDRESSSTATE ((HRESULT)0x8004000D)
#define TAPI_E_INVALCALLPARAMS ((HRESULT)0x8004000E)
#define TAPI_E_INVALCALLPRIVILEGE ((HRESULT)0x8004000F)
#define TAPI_E_INVALCALLSTATE ((HRESULT)0x80040010)
#define TAPI_E_INVALCARD ((HRESULT)0x80040011)
#define TAPI_E_INVALCOMPLETIONID ((HRESULT)0x80040012)
#define TAPI_E_INVALCOUNTRYCODE ((HRESULT)0x80040013)
#define TAPI_E_INVALDEVICECLASS ((HRESULT)0x80040014)
#define TAPI_E_INVALDIALPARAMS ((HRESULT)0x80040015)
#define TAPI_E_INVALDIGITS ((HRESULT)0x80040016)
#define TAPI_E_INVALGROUPID ((HRESULT)0x80040017)
#define TAPI_E_INVALLOCATION ((HRESULT)0x80040018)
#define TAPI_E_INVALMESSAGEID ((HRESULT)0x80040019)
#define TAPI_E_INVALPARKID ((HRESULT)0x8004001A)
#define TAPI_E_INVALRATE ((HRESULT)0x8004001B)
#define TAPI_E_INVALTIMEOUT ((HRESULT)0x8004001C)
#define TAPI_E_INVALTONE ((HRESULT)0x8004001D)
#define TAPI_E_INVALLIST ((HRESULT)0x8004001E)
#define TAPI_E_INVALMODE ((HRESULT)0x8004001F)
#define TAPI_E_NOCONFERENCE ((HRESULT)0x80040020)
#define TAPI_E_NODEVICE ((HRESULT)0x80040021)
#define TAPI_E_NOREQUEST ((HRESULT)0x80040022)
#define TAPI_E_NOTOWNER ((HRESULT)0x80040023)
#define TAPI_E_NOTREGISTERED ((HRESULT)0x80040024)
#define TAPI_E_REQUESTOVERRUN ((HRESULT)0x80040025)
#define TAPI_E_TARGETNOTFOUND ((HRESULT)0x80040026)
#define TAPI_E_TARGETSELF ((HRESULT)0x80040027)
#define TAPI_E_USERUSERINFOTOOBIG ((HRESULT)0x80040028)
#define TAPI_E_REINIT ((HRESULT)0x80040029)
#define TAPI_E_ADDRESSBLOCKED ((HRESULT)0x8004002A)
#define TAPI_E_BILLINGREJECTED ((HRESULT)0x8004002B)
#define TAPI_E_INVALFEATURE ((HRESULT)0x8004002C)
#define TAPI_E_INVALBUTTONLAMPID ((HRESULT)0x8004002D)
#define TAPI_E_INVALBUTTONSTATE ((HRESULT)0x8004002E)
#define TAPI_E_INVALDATAID ((HRESULT)0x8004002F)
#define TAPI_E_INVALHOOKSWITCHDEV ((HRESULT)0x80040030)
#define TAPI_E_DROPPED ((HRESULT)0x80040031)
#define TAPI_E_NOREQUESTRECIPIENT ((HRESULT)0x80040032)
#define TAPI_E_REQUESTQUEUEFULL ((HRESULT)0x80040033)
#define TAPI_E_DESTBUSY ((HRESULT)0x80040034)
#define TAPI_E_DESTNOANSWER ((HRESULT)0x80040035)
#define TAPI_E_DESTUNAVAIL ((HRESULT)0x80040036)
#define TAPI_E_REQUESTFAILED ((HRESULT)0x80040037)
#define TAPI_E_REQUESTCANCELLED ((HRESULT)0x80040038)
#define TAPI_E_INVALPRIVILEGE ((HRESULT)0x80040039)
#define TAPI_E_INVALIDDIRECTION ((HRESULT)0x8004003A)
#define TAPI_E_INVALIDTERMINAL ((HRESULT)0x8004003B)
#define TAPI_E_INVALIDTERMINALCLASS ((HRESULT)0x8004003C)
#define TAPI_E_NODRIVER ((HRESULT)0x8004003D)
#define TAPI_E_MAXSTREAMS ((HRESULT)0x8004003E)
#define TAPI_E_NOTERMINALSELECTED ((HRESULT)0x8004003F)
#define TAPI_E_TERMINALINUSE ((HRESULT)0x80040040)
#define TAPI_E_NOTSTOPPED ((HRESULT)0x80040041)
#define TAPI_E_MAXTERMINALS ((HRESULT)0x80040042)
#define TAPI_E_INVALIDSTREAM ((HRESULT)0x80040043)
#define TAPI_E_TIMEOUT ((HRESULT)0x80040044)
#define TAPI_E_CALLCENTER_GROUP_REMOVED ((HRESULT)0x80040045)
#define TAPI_E_CALLCENTER_QUEUE_REMOVED ((HRESULT)0x80040046)
#define TAPI_E_CALLCENTER_NO_AGENT_ID ((HRESULT)0x80040047)
#define TAPI_E_CALLCENTER_INVALAGENTID ((HRESULT)0x80040048)
#define TAPI_E_CALLCENTER_INVALAGENTGROUP ((HRESULT)0x80040049)
#define TAPI_E_CALLCENTER_INVALPASSWORD ((HRESULT)0x8004004A)
#define TAPI_E_CALLCENTER_INVALAGENTSTATE ((HRESULT)0x8004004B)
#define TAPI_E_CALLCENTER_INVALAGENTACTIVITY ((HRESULT)0x8004004C)
#define TAPI_E_REGISTRY_SETTING_CORRUPT ((HRESULT)0x8004004D)
#define TAPI_E_TERMINAL_PEER ((HRESULT)0x8004004E)
#define TAPI_E_PEER_NOT_SET ((HRESULT)0x8004004F)
#define TAPI_E_NOEVENT ((HRESULT)0x80040050)
#define TAPI_E_INVALADDRESSTYPE ((HRESULT)0x80040051)
#define TAPI_E_RESOURCEUNAVAIL ((HRESULT)0x80040052)
#define TAPI_E_PHONENOTOPEN ((HRESULT)0x80040053)
#define TAPI_E_CALLNOTSELECTED ((HRESULT)0x80040054)
#define TAPI_E_WRONGEVENT ((HRESULT)0x80040055)
#define TAPI_E_NOFORMAT ((HRESULT)0x80040056)
#define TAPI_E_INVALIDSTREAMSTATE ((HRESULT)0x80040057)
#define TAPI_E_WRONG_STATE ((HRESULT)0x80040058)
#define TAPI_E_NOT_INITIALIZED ((HRESULT)0x80040059)
#define TAPI_E_SERVICE_NOT_RUNNING ((HRESULT)0x8004005A)
#endif
