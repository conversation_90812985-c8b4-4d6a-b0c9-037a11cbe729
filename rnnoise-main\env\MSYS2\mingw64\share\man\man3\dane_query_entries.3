.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "dane_query_entries" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
dane_query_entries \- API function
.SH SYNOPSIS
.B #include <gnutls/dane.h>
.sp
.BI "unsigned int dane_query_entries(dane_query_t " q ");"
.SH ARGUMENTS
.IP "dane_query_t q" 12
The query result structure
.SH "DESCRIPTION"
This function will return the number of entries in a query.
.SH "RETURNS"
The number of entries.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
