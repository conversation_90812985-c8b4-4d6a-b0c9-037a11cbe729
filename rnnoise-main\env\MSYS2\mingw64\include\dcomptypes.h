/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef _DCOMPTYPES_H_
#define _DCOMPTYPES_H_

#include <dxgitype.h>
#include <dxgi1_2.h>
#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

enum DCOMPOSITION_BITMAP_INTERPOLATION_MODE {
    DCOMPOSITION_BITMAP_INTERPOLATION_MODE_NEAREST_NEIGHBOR = 0,
    DCOMPOSITION_BITMAP_INTERPOLATION_MODE_LINEAR = 1,
    DCOMPOSITION_BITMAP_INTERPOLATION_MODE_INHERIT = 0xffffffff
};

enum DCOMPOSITION_BORDER_MODE {
    DCOMPOSITION_BORDER_MODE_SOFT = 0,
    DCOMPOSITION_BORDER_MODE_HARD = 1,
    DCOMPOSITION_BORDER_MODE_INHERIT = 0xffffffff
};

enum DCOMPOSITION_COMPOSITE_MODE {
    DCOMPOSITION_COMPOSITE_MODE_SOURCE_OVER = 0,
    DCOMPOSITION_COMPOSITE_MODE_DESTINATION_INVERT = 1,
#if _WIN32_WINNT >= _WIN32_WINNT_WINBLUE
    DCOMPOSITION_COMPOSITE_MODE_MIN_BLEND = 2,
#endif
    DCOMPOSITION_COMPOSITE_MODE_INHERIT = 0xffffffff
};

#if _WIN32_WINNT >= _WIN32_WINNT_WINBLUE
enum DCOMPOSITION_BACKFACE_VISIBILITY {
    DCOMPOSITION_BACKFACE_VISIBILITY_VISIBLE = 0,
    DCOMPOSITION_BACKFACE_VISIBILITY_HIDDEN = 1,
    DCOMPOSITION_BACKFACE_VISIBILITY_INHERIT = 0xffffffff
};

enum DCOMPOSITION_OPACITY_MODE {
    DCOMPOSITION_OPACITY_MODE_LAYER = 0,
    DCOMPOSITION_OPACITY_MODE_MULTIPLY = 1,
    DCOMPOSITION_OPACITY_MODE_INHERIT = 0xffffffff
};
#endif

#if _WIN32_WINNT >= _WIN32_WINNT_WINTHRESHOLD
enum DCOMPOSITION_DEPTH_MODE {
    DCOMPOSITION_DEPTH_MODE_TREE = 0,
    DCOMPOSITION_DEPTH_MODE_SPATIAL = 1,
    DCOMPOSITION_DEPTH_MODE_SORTED = 3,
    DCOMPOSITION_DEPTH_MODE_INHERIT = 0xffffffff
};
#endif

typedef struct {
    LARGE_INTEGER lastFrameTime;
    DXGI_RATIONAL currentCompositionRate;
    LARGE_INTEGER currentTime;
    LARGE_INTEGER timeFrequency;
    LARGE_INTEGER nextEstimatedFrameTime;
} DCOMPOSITION_FRAME_STATISTICS;

#define COMPOSITIONOBJECT_READ 0x01
#define COMPOSITIONOBJECT_WRITE 0x02
#define COMPOSITIONOBJECT_ALL_ACCESS (COMPOSITIONOBJECT_READ | COMPOSITIONOBJECT_WRITE)

#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */

#endif /* _DCOMPTYPES_H_ */
