.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_set_proxy" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_set_proxy \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_set_proxy(gnutls_x509_crt_t " crt ", int " pathLenConstraint ", const char * " policyLanguage ", const char * " policy ", size_t " sizeof_policy ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t crt" 12
a certificate of type \fBgnutls_x509_crt_t\fP
.IP "int pathLenConstraint" 12
non\-negative error codes indicate maximum length of path,
and negative error codes indicate that the pathLenConstraints field should
not be present.
.IP "const char * policyLanguage" 12
OID describing the language of  \fIpolicy\fP .
.IP "const char * policy" 12
uint8_t byte array with policy language, can be \fBNULL\fP
.IP "size_t sizeof_policy" 12
size of  \fIpolicy\fP .
.SH "DESCRIPTION"
This function will set the proxyCertInfo extension.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
