# rewind.awk --- rewind the current file and start over
#
# <PERSON>, arno<PERSON>@skeeve.com, Public Domain
# September 2000

function rewind(    i)
{
    # shift remaining arguments up
    for (i = ARGC; i > ARGIND; i--)
        ARGV[i] = ARGV[i-1]

    # make sure gawk knows to keep going
    ARGC++

    # make current file next to get done
    ARGV[ARGIND+1] = FILENAME

    # do it
    nextfile
}
