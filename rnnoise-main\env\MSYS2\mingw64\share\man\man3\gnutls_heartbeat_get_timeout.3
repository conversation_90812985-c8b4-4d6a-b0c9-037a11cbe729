.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_heartbeat_get_timeout" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_heartbeat_get_timeout \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned int gnutls_heartbeat_get_timeout(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
This function will return the milliseconds remaining
for a retransmission of the previously sent ping
message. This function is useful when ping is used in
non\-blocking mode, to estimate when to call \fBgnutls_heartbeat_ping()\fP
if no packets have been received.
.SH "RETURNS"
the remaining time in milliseconds.
.SH "SINCE"
3.1.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
