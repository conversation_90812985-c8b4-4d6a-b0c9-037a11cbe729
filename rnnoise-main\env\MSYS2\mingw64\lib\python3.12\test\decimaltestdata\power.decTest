------------------------------------------------------------------------
-- power.decTest -- decimal exponentiation [power(x, y)]              --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- In addition to the power operator testcases here, see also the file
-- powersqrt.decTest which includes all the tests from
-- squareroot.decTest implemented using power(x, 0.5)

extended:    1
precision:   16
rounding:    half_even
maxExponent: 384
minExponent: -383

-- base checks.  Note 0**0 is an error.
powx001 power    '0'  '0'         -> NaN Invalid_operation
powx002 power    '0'  '1'         -> '0'
powx003 power    '0'  '2'         -> '0'
powx004 power    '1'  '0'         -> '1'
powx005 power    '1'  '1'         -> '1'
powx006 power    '1'  '2'         -> '1'

powx010 power    '2'  '0'         -> '1'
powx011 power    '2'  '1'         -> '2'
powx012 power    '2'  '2'         -> '4'
powx013 power    '2'  '3'         -> '8'
powx014 power    '2'  '4'         -> '16'
powx015 power    '2'  '5'         -> '32'
powx016 power    '2'  '6'         -> '64'
powx017 power    '2'  '7'         -> '128'
powx018 power    '2'  '8'         -> '256'
powx019 power    '2'  '9'         -> '512'
powx020 power    '2'  '10'        -> '1024'
powx021 power    '2'  '11'        -> '2048'
powx022 power    '2'  '12'        -> '4096'
powx023 power    '2'  '15'        -> '32768'
powx024 power    '2'  '16'        -> '65536'
powx025 power    '2'  '31'        -> '2147483648'
-- NB 0 not stripped in next
powx026 power    '2'  '32'        -> '4294967296'

precision: 9
powx027 power    '2'  '31'        -> '2.14748365E+9' Inexact Rounded
-- NB 0 not stripped in next
powx028 power    '2'  '32'        -> '4.29496730E+9' Inexact Rounded
precision: 10
powx029 power    '2'  '31'        -> '2147483648'
powx030 power    '2'  '32'        -> '4294967296'
precision: 9

powx031 power    '3'  '2'         -> 9
powx032 power    '4'  '2'         -> 16
powx033 power    '5'  '2'         -> 25
powx034 power    '6'  '2'         -> 36
powx035 power    '7'  '2'         -> 49
powx036 power    '8'  '2'         -> 64
powx037 power    '9'  '2'         -> 81
powx038 power    '10' '2'         -> 100
powx039 power    '11' '2'         -> 121
powx040 power    '12' '2'         -> 144

powx041 power    '3'  '3'         -> 27
powx042 power    '4'  '3'         -> 64
powx043 power    '5'  '3'         -> 125
powx044 power    '6'  '3'         -> 216
powx045 power    '7'  '3'         -> 343
powx047 power   '-3'  '3'         -> -27
powx048 power   '-4'  '3'         -> -64
powx049 power   '-5'  '3'         -> -125
powx050 power   '-6'  '3'         -> -216
powx051 power   '-7'  '3'         -> -343

powx052 power   '10'  '0'         -> 1
powx053 power   '10'  '1'         -> 10
powx054 power   '10'  '2'         -> 100
powx055 power   '10'  '3'         -> 1000
powx056 power   '10'  '4'         -> 10000
powx057 power   '10'  '5'         -> 100000
powx058 power   '10'  '6'         -> 1000000
powx059 power   '10'  '7'         -> 10000000
powx060 power   '10'  '8'         -> 100000000
powx061 power   '10'  '9'         -> 1.00000000E+9 Rounded
powx062 power   '10'  '22'        -> 1.00000000E+22 Rounded
powx063 power   '10'  '77'        -> 1.00000000E+77 Rounded
powx064 power   '10'  '99'        -> 1.00000000E+99 Rounded

powx070 power  '0.3'  '0'           -> '1'
powx071 power  '0.3'  '1'           -> '0.3'
powx072 power  '0.3'  '1.00'        -> '0.3'
powx073 power  '0.3'  '2.00'        -> '0.09'
powx074 power  '0.3'  '2.000000000' -> '0.09'
powx075 power  '6.0'  '1'           -> '6.0'     -- NB zeros not stripped
powx076 power  '6.0'  '2'           -> '36.00'   -- ..
powx077 power   '-3'  '2'           -> '9'       -- from NetRexx book
powx078 power    '4'  '3'           -> '64'      -- .. (sort of)

powx080 power   0.1    0            -> 1
powx081 power   0.1    1            -> 0.1
powx082 power   0.1    2            -> 0.01
powx083 power   0.1    3            -> 0.001
powx084 power   0.1    4            -> 0.0001
powx085 power   0.1    5            -> 0.00001
powx086 power   0.1    6            -> 0.000001
powx087 power   0.1    7            -> 1E-7
powx088 power   0.1    8            -> 1E-8
powx089 power   0.1    9            -> 1E-9

powx090 power   101    2            -> 10201
powx091 power   101    3            -> 1030301
powx092 power   101    4            -> 104060401
powx093 power   101    5            -> 1.05101005E+10 Inexact Rounded
powx094 power   101    6            -> 1.06152015E+12 Inexact Rounded
powx095 power   101    7            -> 1.07213535E+14 Inexact Rounded

-- negative powers
powx099 power  '1'  '-1'    -> 1
powx100 power  '3'  '-1'    -> 0.333333333 Inexact Rounded
powx101 power  '2'  '-1'    -> 0.5
powx102 power  '2'  '-2'    -> 0.25
powx103 power  '2'  '-4'    -> 0.0625
powx104 power  '2'  '-8'    -> 0.00390625
powx105 power  '2'  '-16'   -> 0.0000152587891 Inexact Rounded
powx106 power  '2'  '-32'   -> 2.32830644E-10 Inexact Rounded
powx108 power  '2'  '-64'   -> 5.42101086E-20 Inexact Rounded
powx110 power  '10'  '-8'   -> 1E-8
powx111 power  '10'  '-7'   -> 1E-7
powx112 power  '10'  '-6'   -> 0.000001
powx113 power  '10'  '-5'   -> 0.00001
powx114 power  '10'  '-4'   -> 0.0001
powx115 power  '10'  '-3'   -> 0.001
powx116 power  '10'  '-2'   -> 0.01
powx117 power  '10'  '-1'   -> 0.1
powx121 power  '10'  '-77'  -> '1E-77'
powx122 power  '10'  '-22'  -> '1E-22'

powx123 power   '2'  '-1'   -> '0.5'
powx124 power   '2'  '-2'   -> '0.25'
powx125 power   '2'  '-4'   -> '0.0625'

powx126 power   '0'  '-1'   -> Infinity
powx127 power   '0'  '-2'   -> Infinity
powx128 power   -0   '-1'   -> -Infinity
powx129 power   -0   '-2'   -> Infinity

-- "0.5" tests from original Rexx diagnostics [loop unrolled]
powx200 power   0.5    0    -> 1
powx201 power   0.5    1    -> 0.5
powx202 power   0.5    2    -> 0.25
powx203 power   0.5    3    -> 0.125
powx204 power   0.5    4    -> 0.0625
powx205 power   0.5    5    -> 0.03125
powx206 power   0.5    6    -> 0.015625
powx207 power   0.5    7    -> 0.0078125
powx208 power   0.5    8    -> 0.00390625
powx209 power   0.5    9    -> 0.001953125
powx210 power   0.5   10    -> 0.0009765625

powx211 power 1  100000000  -> 1
powx212 power 1  999999998  -> 1
powx213 power 1  999999999  -> 1


-- The Vienna case.  Checks both setup and 1/acc working precision
-- Modified 1998.12.14 as RHS no longer rounded before use (must fit)
-- Modified 1990.02.04 as LHS is now rounded (instead of truncated to guard)
--    '123456789E+10'    -- lhs .. rounded to 1.23E+18
--    '-1.23000e+2'      -- rhs .. [was: -1.23455e+2, rounds to -123]
-- Modified 2002.10.06 -- finally, no input rounding
-- With input rounding, result would be 8.74E-2226
precision: 3
maxexponent: 5000
minexponent: -5000
powx219 power '123456789E+10' '-1.23000e+2' -> '5.54E-2226' Inexact Rounded

-- zeros
maxexponent: +96
minexponent: -95
precision: 7
powx223 power          0E-30 3  ->  0
powx224 power          0E-10 3  ->  0
powx225 power          0E-1  3  ->  0
powx226 power          0E+0  3  ->  0
powx227 power          0     3  ->  0
powx228 power          0E+1  3  ->  0
powx229 power          0E+10 3  ->  0
powx230 power          0E+30 3  ->  0
powx231 power     3    0E-30    ->  1
powx232 power     3    0E-10    ->  1
powx233 power     3    0E-1     ->  1
powx234 power     3    0E+0     ->  1
powx235 power     3    0        ->  1
powx236 power     3    0E+1     ->  1
powx237 power     3    0E+10    ->  1
powx238 power     3    0E+30    ->  1
powx239 power          0E-30 -3 ->  Infinity
powx240 power          0E-10 -3 ->  Infinity
powx241 power          0E-1  -3 ->  Infinity
powx242 power          0E+0  -3 ->  Infinity
powx243 power          0     -3 ->  Infinity
powx244 power          0E+1  -3 ->  Infinity
powx245 power          0E+10 -3 ->  Infinity
powx246 power          0E+30 -3 ->  Infinity
powx247 power    -3    0E-30    ->  1
powx248 power    -3    0E-10    ->  1
powx249 power    -3    0E-1     ->  1
powx250 power    -3    0E+0     ->  1
powx251 power    -3    0        ->  1
powx252 power    -3    0E+1     ->  1
powx253 power    -3    0E+10    ->  1
powx254 power    -3    0E+30    ->  1

-- a few lhs negatives
precision: 9
maxExponent: 999
minexponent: -999
powx260 power   -10   '0'         -> 1
powx261 power   -10   '1'         -> -10
powx262 power   -10   '2'         -> 100
powx263 power   -10   '3'         -> -1000
powx264 power   -10   '4'         -> 10000
powx265 power   -10   '5'         -> -100000
powx266 power   -10   '6'         -> 1000000
powx267 power   -10   '7'         -> -10000000
powx268 power   -10   '8'         -> 100000000
powx269 power   -10   '9'         -> -1.00000000E+9 Rounded
powx270 power   -10   '22'        -> 1.00000000E+22 Rounded
powx271 power   -10   '77'        -> -1.00000000E+77 Rounded
powx272 power   -10   '99'        -> -1.00000000E+99 Rounded

-- some more edge cases
precision:   15
maxExponent: 999
minexponent: -999
powx391 power  0.1   999 -> 1E-999
powx392 power  0.099 999 -> 4.360732062E-1004 Underflow Subnormal Inexact Rounded
powx393 power  0.098 999 -> 1.71731E-1008 Underflow Subnormal Inexact Rounded
powx394 power  0.097 999 -> 6E-1013 Underflow Subnormal Inexact Rounded
powx395 power  0.096 999 -> 0E-1013 Underflow Subnormal Inexact Rounded Clamped
powx396 power  0.01  999 -> 0E-1013 Underflow Subnormal Inexact Rounded Clamped
powx397 power  0.02 100000000 -> 0E-1013 Underflow Subnormal Inexact Rounded Clamped

-- multiply tests are here to aid checking and test for consistent handling
-- of underflow
precision: 5
maxexponent: 999
minexponent: -999

-- squares
mulx400 multiply  1E-502     1e-502 -> 0E-1003    Subnormal Inexact Underflow Rounded Clamped
mulx401 multiply  1E-501     1e-501 -> 1E-1002    Subnormal
mulx402 multiply  2E-501     2e-501 -> 4E-1002    Subnormal
mulx403 multiply  4E-501     4e-501 -> 1.6E-1001  Subnormal
mulx404 multiply 10E-501    10e-501 -> 1.00E-1000 Subnormal
mulx405 multiply 30E-501    30e-501 -> 9.00E-1000 Subnormal
mulx406 multiply 40E-501    40e-501 -> 1.600E-999

powx400 power     1E-502     2      -> 0E-1003    Underflow Subnormal Inexact Rounded Clamped
powx401 power     1E-501     2      -> 1E-1002    Subnormal
powx402 power     2E-501     2      -> 4E-1002    Subnormal
powx403 power     4E-501     2      -> 1.6E-1001  Subnormal
powx404 power    10E-501     2      -> 1.00E-1000 Subnormal
powx405 power    30E-501     2      -> 9.00E-1000 Subnormal
powx406 power    40E-501     2      -> 1.600E-999

-- cubes
mulx410 multiply  1E-670     1e-335 -> 0E-1003    Underflow Subnormal Inexact Rounded Clamped
mulx411 multiply  1E-668     1e-334 -> 1E-1002    Subnormal
mulx412 multiply  4E-668     2e-334 -> 8E-1002    Subnormal
mulx413 multiply  9E-668     3e-334 -> 2.7E-1001  Subnormal
mulx414 multiply 16E-668     4e-334 -> 6.4E-1001  Subnormal
mulx415 multiply 25E-668     5e-334 -> 1.25E-1000 Subnormal
mulx416 multiply 10E-668   100e-334 -> 1.000E-999

powx410 power     1E-335     3      -> 0E-1003    Underflow Subnormal Inexact Rounded Clamped
powx411 power     1E-334     3      -> 1E-1002    Subnormal
powx412 power     2E-334     3      -> 8E-1002    Subnormal
powx413 power     3E-334     3      -> 2.7E-1001  Subnormal
powx414 power     4E-334     3      -> 6.4E-1001  Subnormal
powx415 power     5E-334     3      -> 1.25E-1000 Subnormal
powx416 power    10E-334     3      -> 1.000E-999

-- negative powers, testing subnormals
precision:   5
maxExponent: 999
minexponent: -999
powx421 power  2.5E-501     -2         ->  Infinity Overflow Inexact Rounded
powx422 power  2.5E-500     -2         ->  1.6E+999

powx423 power  2.5E+499     -2         ->  1.6E-999
powx424 power  2.5E+500     -2         ->  1.6E-1001 Subnormal
powx425 power  2.5E+501     -2         ->    2E-1003 Underflow Subnormal Inexact Rounded
powx426 power  2.5E+502     -2         ->    0E-1003 Underflow Subnormal Inexact Rounded Clamped

powx427 power 0.25E+499     -2         ->  1.6E-997
powx428 power 0.25E+500     -2         ->  1.6E-999
powx429 power 0.25E+501     -2         ->  1.6E-1001 Subnormal
powx430 power 0.25E+502     -2         ->    2E-1003 Underflow Subnormal Inexact Rounded
powx431 power 0.25E+503     -2         ->    0E-1003 Underflow Subnormal Inexact Rounded Clamped

powx432 power 0.04E+499     -2         ->  6.25E-996
powx433 power 0.04E+500     -2         ->  6.25E-998
powx434 power 0.04E+501     -2         ->  6.25E-1000 Subnormal
powx435 power 0.04E+502     -2         ->   6.2E-1002 Underflow Subnormal Inexact Rounded
powx436 power 0.04E+503     -2         ->     1E-1003 Underflow Subnormal Inexact Rounded
powx437 power 0.04E+504     -2         ->     0E-1003 Underflow Subnormal Inexact Rounded Clamped

powx441 power 0.04E+334     -3         ->  1.5625E-998
powx442 power 0.04E+335     -3         ->    1.56E-1001 Underflow Subnormal Inexact Rounded
powx443 power 0.04E+336     -3         ->       0E-1003 Underflow Subnormal Inexact Rounded Clamped
powx444 power 0.25E+333     -3         ->     6.4E-998
powx445 power 0.25E+334     -3         ->     6.4E-1001 Subnormal
powx446 power 0.25E+335     -3         ->       1E-1003 Underflow Subnormal Inexact Rounded
powx447 power 0.25E+336     -3         ->       0E-1003 Underflow Subnormal Inexact Rounded Clamped
-- check sign for cubes  and a few squares
powx448 power -0.04E+334    -3         -> -1.5625E-998
powx449 power -0.04E+335    -3         ->   -1.56E-1001 Underflow Subnormal Inexact Rounded
powx450 power -0.04E+336    -3         ->      -0E-1003 Underflow Subnormal Inexact Rounded Clamped
powx451 power -0.25E+333    -3         ->    -6.4E-998
powx452 power -0.25E+334    -3         ->    -6.4E-1001 Subnormal
powx453 power -0.25E+335    -3         ->      -1E-1003 Underflow Subnormal Inexact Rounded
powx454 power -0.25E+336    -3         ->      -0E-1003 Underflow Subnormal Inexact Rounded Clamped
powx455 power -0.04E+499    -2         ->    6.25E-996
powx456 power -0.04E+500    -2         ->    6.25E-998
powx457 power -0.04E+501    -2         ->    6.25E-1000 Subnormal
powx458 power -0.04E+502    -2         ->     6.2E-1002 Underflow Subnormal Inexact Rounded

-- test -0s
precision: 9
powx560 power  0  0        ->  NaN Invalid_operation
powx561 power  0 -0        ->  NaN Invalid_operation
powx562 power -0  0        ->  NaN Invalid_operation
powx563 power -0 -0        ->  NaN Invalid_operation
powx564 power  1  0        ->  1
powx565 power  1 -0        ->  1
powx566 power -1  0        ->  1
powx567 power -1 -0        ->  1
powx568 power  0  1        ->  0
powx569 power  0 -1        ->  Infinity
powx570 power -0  1        -> -0
powx571 power -0 -1        -> -Infinity
powx572 power  0  2        ->  0
powx573 power  0 -2        ->  Infinity
powx574 power -0  2        ->  0
powx575 power -0 -2        ->  Infinity
powx576 power  0  3        ->  0
powx577 power  0 -3        ->  Infinity
powx578 power -0  3        -> -0
powx579 power -0 -3        -> -Infinity

-- Specials
powx580 power  Inf  -Inf   ->  0
powx581 power  Inf  -1000  ->  0
powx582 power  Inf  -1     ->  0
powx583 power  Inf  -0.5   ->  0
powx584 power  Inf  -0     ->  1
powx585 power  Inf   0     ->  1
powx586 power  Inf   0.5   ->  Infinity
powx587 power  Inf   1     ->  Infinity
powx588 power  Inf   1000  ->  Infinity
powx589 power  Inf   Inf   ->  Infinity
powx590 power -1000  Inf   ->  NaN  Invalid_operation
powx591 power -Inf   Inf   ->  NaN  Invalid_operation
powx592 power -1     Inf   ->  NaN  Invalid_operation
powx593 power -0.5   Inf   ->  NaN  Invalid_operation
powx594 power -0     Inf   ->  0
powx595 power  0     Inf   ->  0
powx596 power  0.5   Inf   ->  0
powx597 power  1     Inf   ->  1.00000000 Inexact Rounded
powx598 power  1000  Inf   ->  Infinity
powx599 power  Inf   Inf   ->  Infinity

powx600 power -Inf  -Inf   ->  NaN  Invalid_operation
powx601 power -Inf  -1000  ->  0
powx602 power -Inf  -1     -> -0
powx603 power -Inf  -0.5   ->  NaN  Invalid_operation
powx604 power -Inf  -0     ->  1
powx605 power -Inf   0     ->  1
powx606 power -Inf   0.5   ->  NaN  Invalid_operation
powx607 power -Inf   1     -> -Infinity
powx608 power -Inf   1000  ->  Infinity
powx609 power -Inf   Inf   ->  NaN  Invalid_operation
powx610 power -1000  Inf   ->  NaN  Invalid_operation
powx611 power -Inf  -Inf   ->  NaN  Invalid_operation
powx612 power -1    -Inf   ->  NaN  Invalid_operation
powx613 power -0.5  -Inf   ->  NaN  Invalid_operation
powx614 power -0    -Inf   ->  Infinity
powx615 power  0    -Inf   ->  Infinity
powx616 power  0.5  -Inf   ->  Infinity
powx617 power  1    -Inf   ->  1.00000000 Inexact Rounded
powx618 power  1000 -Inf   ->  0
powx619 power  Inf  -Inf   ->  0

powx621 power  NaN -Inf    ->  NaN
powx622 power  NaN -1000   ->  NaN
powx623 power  NaN -1      ->  NaN
powx624 power  NaN -0.5    ->  NaN
powx625 power  NaN -0      ->  NaN
powx626 power  NaN  0      ->  NaN
powx627 power  NaN  0.5    ->  NaN
powx628 power  NaN  1      ->  NaN
powx629 power  NaN  1000   ->  NaN
powx630 power  NaN  Inf    ->  NaN
powx631 power  NaN  NaN    ->  NaN
powx632 power -Inf  NaN    ->  NaN
powx633 power -1000 NaN    ->  NaN
powx634 power -1    NaN    ->  NaN
powx635 power -0    NaN    ->  NaN
powx636 power  0    NaN    ->  NaN
powx637 power  1    NaN    ->  NaN
powx638 power  1000 NaN    ->  NaN
powx639 power  Inf  NaN    ->  NaN

powx641 power  sNaN -Inf   ->  NaN  Invalid_operation
powx642 power  sNaN -1000  ->  NaN  Invalid_operation
powx643 power  sNaN -1     ->  NaN  Invalid_operation
powx644 power  sNaN -0.5   ->  NaN  Invalid_operation
powx645 power  sNaN -0     ->  NaN  Invalid_operation
powx646 power  sNaN  0     ->  NaN  Invalid_operation
powx647 power  sNaN  0.5   ->  NaN  Invalid_operation
powx648 power  sNaN  1     ->  NaN  Invalid_operation
powx649 power  sNaN  1000  ->  NaN  Invalid_operation
powx650 power  sNaN  NaN   ->  NaN  Invalid_operation
powx651 power  sNaN sNaN   ->  NaN  Invalid_operation
powx652 power  NaN  sNaN   ->  NaN  Invalid_operation
powx653 power -Inf  sNaN   ->  NaN  Invalid_operation
powx654 power -1000 sNaN   ->  NaN  Invalid_operation
powx655 power -1    sNaN   ->  NaN  Invalid_operation
powx656 power -0.5  sNaN   ->  NaN  Invalid_operation
powx657 power -0    sNaN   ->  NaN  Invalid_operation
powx658 power  0    sNaN   ->  NaN  Invalid_operation
powx659 power  0.5  sNaN   ->  NaN  Invalid_operation
powx660 power  1    sNaN   ->  NaN  Invalid_operation
powx661 power  1000 sNaN   ->  NaN  Invalid_operation
powx662 power  Inf  sNaN   ->  NaN  Invalid_operation
powx663 power  NaN  sNaN   ->  NaN  Invalid_operation

-- NaN propagation
powx670 power  NaN3  sNaN7  ->  NaN7  Invalid_operation
powx671 power  sNaN8  NaN6  ->  NaN8  Invalid_operation
powx672 power  1     sNaN7  ->  NaN7  Invalid_operation
powx673 power  sNaN8  1     ->  NaN8  Invalid_operation
powx674 power  Inf   sNaN7  ->  NaN7  Invalid_operation
powx675 power  sNaN8  Inf   ->  NaN8  Invalid_operation
powx676 power  Inf    NaN9  ->  NaN9
powx677 power  NaN6   Inf   ->  NaN6
powx678 power  1      NaN5  ->  NaN5
powx679 power  NaN2   1     ->  NaN2
powx680 power  NaN2   Nan4  ->  NaN2
powx681 power  NaN    Nan4  ->  NaN
powx682 power  NaN345 Nan   ->  NaN345
powx683 power  Inf    -sNaN7 -> -NaN7  Invalid_operation
powx684 power  -sNaN8  Inf   -> -NaN8  Invalid_operation
powx685 power  Inf    -NaN9  -> -NaN9
powx686 power  -NaN6   Inf   -> -NaN6
powx687 power  -NaN2  -Nan4  -> -NaN2

-- long operand and RHS range checks
maxexponent: 999
minexponent: -999
precision: 9
powx701 power 12345678000 1 -> 1.23456780E+10 Rounded
powx702 power 1234567800  1 -> 1.23456780E+9 Rounded
powx703 power 1234567890  1 -> 1.23456789E+9 Rounded
powx704 power 1234567891  1 -> 1.23456789E+9 Inexact Rounded
powx705 power 12345678901 1 -> 1.23456789E+10 Inexact Rounded
powx706 power 1234567896  1 -> 1.23456790E+9 Inexact Rounded

precision: 15
-- still checking
powx741 power 12345678000 1 -> 12345678000
powx742 power 1234567800  1 -> 1234567800
powx743 power 1234567890  1 -> 1234567890
powx744 power 1234567891  1 -> 1234567891
powx745 power 12345678901 1 -> 12345678901
powx746 power 1234567896  1 -> 1234567896

maxexponent: 999999
minexponent: -999999
precision: 9

-- near out-of-range edge cases
powx163 power   '10'  '999999' -> '1.00000000E+999999' Rounded
powx164 power   '10'  '999998' -> '1.00000000E+999998' Rounded
powx165 power   '10'  '999997' -> '1.00000000E+999997' Rounded
powx166 power   '10'  '333333' -> '1.00000000E+333333' Rounded
powx183 power   '7'   '1000000'  -> 1.09651419E+845098 Inexact Rounded
powx184 power   '7'   '1000001'  -> 7.67559934E+845098 Inexact Rounded
powx186 power   '7'   '-1000001' -> 1.30282986E-845099 Inexact Rounded
powx187 power   '7'   '-1000000' -> 9.11980901E-845099 Inexact Rounded
powx118 power  '10'  '-333333'   -> 1E-333333
powx119 power  '10'  '-999998'   -> 1E-999998
powx120 power  '10'  '-999999'   -> 1E-999999
powx181 power   '7'   '999998'   -> 2.23778406E+845096 Inexact Rounded
powx182 power   '7'   '999999'   -> 1.56644884E+845097 Inexact Rounded
powx189 power   '7'   '-999999'  -> 6.38386631E-845098 Inexact Rounded
powx190 power   '7'   '-999998'  -> 4.46870641E-845097 Inexact Rounded

-- overflow and underflow tests
precision: 9

powx277 power  9             999999 -> 3.59084629E+954241 Inexact Rounded
powx278 power  9.99999999    999999 -> 9.99000501E+999998 Inexact Rounded
powx279 power 10             999999 -> 1.00000000E+999999         Rounded
powx280 power 10.0000001     999999 -> 1.01005016E+999999 Inexact Rounded
powx281 power 10.000001      999999 -> 1.10517080E+999999 Inexact Rounded
powx282 power 10.00001       999999 -> 2.71827775E+999999 Inexact Rounded
powx283 power 10.0001        999999 -> Infinity Overflow Inexact Rounded
powx285 power 11             999999 -> Infinity Overflow Inexact Rounded
powx286 power 12             999999 -> Infinity Overflow Inexact Rounded
powx287 power 999            999999 -> Infinity Overflow Inexact Rounded
powx288 power 999999999      999999 -> Infinity Overflow Inexact Rounded
powx289 power 9.9E999999999  999999 -> Infinity Overflow Inexact Rounded

powx290 power 0.5            999999 -> 2.02006812E-301030 Inexact Rounded
powx291 power 0.1            999999 -> 1E-999999  -- unrounded
powx292 power 0.09           999999 -> 0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx293 power 0.05           999999 -> 0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx294 power 0.01           999999 -> 0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx295 power 0.0001         999999 -> 0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx297 power 0.0000001      999999 -> 0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx298 power 0.0000000001   999999 -> 0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx299 power 1E-999999999   999999 -> 0E-1000007 Underflow Subnormal Inexact Rounded Clamped

powx310 power -9             999999 -> -3.59084629E+954241 Inexact Rounded
powx311 power -10            999999 -> -1.00000000E+999999 Rounded
powx312 power -10.0001       999999 -> -Infinity Overflow Inexact Rounded
powx313 power -10.1          999999 -> -Infinity Overflow Inexact Rounded
powx314 power -11            999999 -> -Infinity Overflow Inexact Rounded
powx315 power -12            999999 -> -Infinity Overflow Inexact Rounded
powx316 power -999           999999 -> -Infinity Overflow Inexact Rounded
powx317 power -999999        999999 -> -Infinity Overflow Inexact Rounded
powx318 power -999999999     999999 -> -Infinity Overflow Inexact Rounded
powx319 power -9.9E999999999 999999 -> -Infinity Overflow Inexact Rounded

powx320 power -0.5           999999 -> -2.02006812E-301030 Inexact Rounded
powx321 power -0.1           999999 -> -1E-999999
powx322 power -0.09          999999 -> -0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx323 power -0.05          999999 -> -0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx324 power -0.01          999999 -> -0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx325 power -0.0001        999999 -> -0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx327 power -0.0000001     999999 -> -0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx328 power -0.0000000001  999999 -> -0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx329 power -1E-999999999  999999 -> -0E-1000007 Underflow Subnormal Inexact Rounded Clamped

-- note no trim of next result
powx330 power -9             999998 ->  3.98982921E+954240 Inexact Rounded
powx331 power -10            999998 ->  1.00000000E+999998 Rounded
powx332 power -10.0001       999998 ->  Infinity Overflow Inexact Rounded
powx333 power -10.1          999998 ->  Infinity Overflow Inexact Rounded
powx334 power -11            999998 ->  Infinity Overflow Inexact Rounded
powx335 power -12            999998 ->  Infinity Overflow Inexact Rounded
powx336 power -999           999998 ->  Infinity Overflow Inexact Rounded
powx337 power -999999        999998 ->  Infinity Overflow Inexact Rounded
powx338 power -999999999     999998 ->  Infinity Overflow Inexact Rounded
powx339 power -9.9E999999999 999998 ->  Infinity Overflow Inexact Rounded

powx340 power -0.5           999998 ->  4.04013624E-301030 Inexact Rounded
powx341 power -0.1           999998 ->  1E-999998  -- NB exact unrounded
powx342 power -0.09          999998 ->  0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx343 power -0.05          999998 ->  0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx344 power -0.01          999998 ->  0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx345 power -0.0001        999998 ->  0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx347 power -0.0000001     999998 ->  0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx348 power -0.0000000001  999998 ->  0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx349 power -1E-999999999  999998 ->  0E-1000007 Underflow Subnormal Inexact Rounded Clamped

-- some subnormals
precision: 9
-- [precision is 9, so smallest exponent is -1000000007
powx350 power  1e-1          500000 ->  1E-500000
powx351 power  1e-2          999999 ->  0E-1000007 Underflow Subnormal Inexact Rounded Clamped
powx352 power  1e-2          500000 ->  1E-1000000 Subnormal
powx353 power  1e-2          500001 ->  1E-1000002 Subnormal
powx354 power  1e-2          500002 ->  1E-1000004 Subnormal
powx355 power  1e-2          500003 ->  1E-1000006 Subnormal
powx356 power  1e-2          500004 ->  0E-1000007 Underflow Subnormal Inexact Rounded Clamped

powx360 power  0.010001      500000 ->  5.17176082E-999979 Inexact Rounded
powx361 power  0.010000001   500000 ->  1.0512711E-1000000 Underflow Subnormal Inexact Rounded
powx362 power  0.010000001   500001 ->  1.05127E-1000002 Underflow Subnormal Inexact Rounded
powx363 power  0.0100000009  500000 ->  1.0460279E-1000000 Underflow Subnormal Inexact Rounded
powx364 power  0.0100000001  500000 ->  1.0050125E-1000000 Underflow Subnormal Inexact Rounded
powx365 power  0.01          500000 ->  1E-1000000 Subnormal
powx366 power  0.0099999999  500000 ->  9.950125E-1000001 Underflow Subnormal Inexact Rounded
powx367 power  0.0099999998  500000 ->  9.900498E-1000001 Underflow Subnormal Inexact Rounded
powx368 power  0.0099999997  500000 ->  9.851119E-1000001 Underflow Subnormal Inexact Rounded
powx369 power  0.0099999996  500000 ->  9.801987E-1000001 Underflow Subnormal Inexact Rounded
powx370 power  0.009         500000 ->  0E-1000007 Underflow Subnormal Inexact Rounded Clamped

-- 1/subnormal -> overflow
powx371 power  1e-1         -500000 ->  1E+500000
powx372 power  1e-2         -999999 ->  Infinity Overflow Inexact Rounded
powx373 power  1e-2         -500000 ->  Infinity Overflow Inexact Rounded
powx374 power  1e-2         -500001 ->  Infinity Overflow Inexact Rounded
powx375 power  1e-2         -500002 ->  Infinity Overflow Inexact Rounded
powx376 power  1e-2         -500003 ->  Infinity Overflow Inexact Rounded
powx377 power  1e-2         -500004 ->  Infinity Overflow Inexact Rounded

powx381 power  0.010001     -500000 ->  1.93357743E+999978 Inexact Rounded
powx382 power  0.010000001  -500000 ->  9.51229427E+999999 Inexact Rounded
powx383 power  0.010000001  -500001 ->  Infinity Overflow  Inexact Rounded
powx384 power  0.0100000009 -500000 ->  9.55997484E+999999 Inexact Rounded
powx385 power  0.0100000001 -500000 ->  9.95012479E+999999 Inexact Rounded
powx386 power  0.01         -500000 ->  Infinity Overflow Inexact Rounded
powx387 power  0.009999     -500000 ->  Infinity Overflow Inexact Rounded

-- negative power giving subnormal
powx388 power  100.000001   -500000 ->  9.950125E-1000001 Underflow Subnormal Inexact Rounded


-- test some 'false integer' boundaries
precision:   16
rounding:    half_even
maxExponent: 384
minExponent: -383
powx501 power  100  1E+1   ->  1.000000000000000E+20     Rounded
powx502 power  100  1E+2   ->  1.000000000000000E+200    Rounded
powx503 power  100  1E+3   ->  Infinity Overflow Inexact Rounded
powx504 power  100  1E+4   ->  Infinity Overflow Inexact Rounded
powx505 power  100  1E+5   ->  Infinity Overflow Inexact Rounded
powx506 power  100  1E+6   ->  Infinity Overflow Inexact Rounded
powx507 power  100  1E+7   ->  Infinity Overflow Inexact Rounded
powx508 power  100  1E+8   ->  Infinity Overflow Inexact Rounded
powx509 power  100  1E+9   ->  Infinity Overflow Inexact Rounded
powx510 power  100  1E+10  ->  Infinity Overflow Inexact Rounded
powx511 power  100  1E+11  ->  Infinity Overflow Inexact Rounded
powx512 power  100  1E+12  ->  Infinity Overflow Inexact Rounded
powx513 power  100  1E+13  ->  Infinity Overflow Inexact Rounded
powx514 power  100  1E+14  ->  Infinity Overflow Inexact Rounded
powx515 power  100  1E+15  ->  Infinity Overflow Inexact Rounded
powx516 power  100  1E+16  ->  Infinity Overflow Inexact Rounded
powx517 power  100  1E+17  ->  Infinity Overflow Inexact Rounded
powx518 power  100  1E+18  ->  Infinity Overflow Inexact Rounded
powx519 power  100  1E+19  ->  Infinity Overflow Inexact Rounded
powx520 power  100  1E+20  ->  Infinity Overflow Inexact Rounded
powx521 power  100  1E+21  ->  Infinity Overflow Inexact Rounded
powx522 power  100  1E+22  ->  Infinity Overflow Inexact Rounded
powx523 power  100  1E+23  ->  Infinity Overflow Inexact Rounded
powx524 power  100  1E+24  ->  Infinity Overflow Inexact Rounded
powx525 power  100  1E+25  ->  Infinity Overflow Inexact Rounded
powx526 power  100  1E+26  ->  Infinity Overflow Inexact Rounded
powx527 power  100  1E+27  ->  Infinity Overflow Inexact Rounded
powx528 power  100  1E+28  ->  Infinity Overflow Inexact Rounded
powx529 power  100  1E+29  ->  Infinity Overflow Inexact Rounded
powx530 power  100  1E+30  ->  Infinity Overflow Inexact Rounded
powx531 power  100  1E+40  ->  Infinity Overflow Inexact Rounded
powx532 power  100  1E+50  ->  Infinity Overflow Inexact Rounded
powx533 power  100  1E+100 ->  Infinity Overflow Inexact Rounded
powx534 power  100  1E+383 ->  Infinity Overflow Inexact Rounded

-- a check for double-rounded subnormals
precision:   5
maxexponent: 79
minexponent: -79
powx750 power     1.2347E-40  2      ->  1.524E-80 Inexact Rounded Subnormal Underflow

-- Null tests
powx900 power  1 # -> NaN Invalid_operation
powx901 power  # 1 -> NaN Invalid_operation

----------------------------------------------------------------------
-- Below here are tests with a precision or context outside of the  --
-- decNumber 'mathematical functions' restricted range.  These      --
-- remain supported in decNumber to minimize breakage, but may be   --
-- outside the range of other implementations.                      --
----------------------------------------------------------------------
maxexponent: 999999999
minexponent: -999999999
precision: 9
powx1063 power   '10'  '999999999' -> '1.00000000E+999999999' Rounded
powx1064 power   '10'  '999999998' -> '1.00000000E+999999998' Rounded
powx1065 power   '10'  '999999997' -> '1.00000000E+999999997' Rounded
powx1066 power   '10'  '333333333' -> '1.00000000E+333333333' Rounded
-- next two are integer-out-of range
powx1183 power   '7'   '1000000000'  -> NaN Invalid_context
powx1184 power   '7'   '1000000001'  -> NaN Invalid_context
powx1186 power   '7'   '-1000000001' -> 1.38243630E-845098041 Inexact Rounded
powx1187 power   '7'   '-1000000000' -> 9.67705411E-845098041 Inexact Rounded

-- out-of-range edge cases
powx1118 power  '10'  '-333333333'   -> 1E-333333333
powx1119 power  '10'  '-999999998'   -> 1E-999999998
powx1120 power  '10'  '-999999999'   -> 1E-999999999
powx1181 power   '7'   '999999998'   -> 2.10892313E+845098038 Inexact Rounded
powx1182 power   '7'   '999999999'   -> 1.47624619E+845098039 Inexact Rounded
powx1189 power   '7'   '-999999999'  -> 6.77393787E-845098040 Inexact Rounded
powx1190 power   '7'   '-999999998'  -> 4.74175651E-845098039 Inexact Rounded

-- A (rare) case where the last digit is not within 0.5 ULP with classic precision
precision: 9
powx1215 power "-21971575.0E+31454441" "-7" -> "-4.04549502E-220181139" Inexact Rounded
precision: 20
powx1216 power "-21971575.0E+31454441" "-7" -> "-4.0454950249324891788E-220181139" Inexact Rounded

-- overflow and underflow tests
precision: 9
powx1280 power  9            999999999 -> 3.05550054E+954242508 Inexact Rounded
powx1281 power 10            999999999 -> 1.00000000E+999999999 Rounded
powx1282 power 10.0001       999999999 -> Infinity Overflow Inexact Rounded
powx1283 power 10.1          999999999 -> Infinity Overflow Inexact Rounded
powx1284 power 11            999999999 -> Infinity Overflow Inexact Rounded
powx1285 power 12            999999999 -> Infinity Overflow Inexact Rounded
powx1286 power 999           999999999 -> Infinity Overflow Inexact Rounded
powx1287 power 999999        999999999 -> Infinity Overflow Inexact Rounded
powx1288 power 999999999     999999999 -> Infinity Overflow Inexact Rounded
powx1289 power 9.9E999999999 999999999 -> Infinity Overflow Inexact Rounded

powx1290 power 0.5           999999999 -> 4.33559594E-301029996 Inexact Rounded
powx1291 power 0.1           999999999 -> 1E-999999999  -- unrounded
powx1292 power 0.09          999999999 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1293 power 0.05          999999999 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1294 power 0.01          999999999 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1295 power 0.0001        999999999 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1297 power 0.0000001     999999999 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1298 power 0.0000000001  999999999 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1299 power 1E-999999999  999999999 -> 0E-1000000007 Underflow Subnormal Inexact Rounded Clamped

powx1310 power -9             999999999 -> -3.05550054E+954242508 Inexact Rounded
powx1311 power -10            999999999 -> -1.00000000E+999999999 Rounded
powx1312 power -10.0001       999999999 -> -Infinity Overflow Inexact Rounded
powx1313 power -10.1          999999999 -> -Infinity Overflow Inexact Rounded
powx1314 power -11            999999999 -> -Infinity Overflow Inexact Rounded
powx1315 power -12            999999999 -> -Infinity Overflow Inexact Rounded
powx1316 power -999           999999999 -> -Infinity Overflow Inexact Rounded
powx1317 power -999999        999999999 -> -Infinity Overflow Inexact Rounded
powx1318 power -999999999     999999999 -> -Infinity Overflow Inexact Rounded
powx1319 power -9.9E999999999 999999999 -> -Infinity Overflow Inexact Rounded

powx1320 power -0.5           999999999 -> -4.33559594E-301029996 Inexact Rounded
powx1321 power -0.1           999999999 -> -1E-999999999
powx1322 power -0.09          999999999 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1323 power -0.05          999999999 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1324 power -0.01          999999999 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1325 power -0.0001        999999999 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1327 power -0.0000001     999999999 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1328 power -0.0000000001  999999999 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1329 power -1E-999999999  999999999 -> -0E-1000000007 Underflow Subnormal Inexact Rounded Clamped

-- note no trim of next result
powx1330 power -9             999999998 ->  3.39500060E+954242507 Inexact Rounded
powx1331 power -10            999999998 ->  1.00000000E+999999998 Rounded
powx1332 power -10.0001       999999998 ->  Infinity Overflow Inexact Rounded
powx1333 power -10.1          999999998 ->  Infinity Overflow Inexact Rounded
powx1334 power -11            999999998 ->  Infinity Overflow Inexact Rounded
powx1335 power -12            999999998 ->  Infinity Overflow Inexact Rounded
powx1336 power -999           999999998 ->  Infinity Overflow Inexact Rounded
powx1337 power -999999        999999998 ->  Infinity Overflow Inexact Rounded
powx1338 power -999999999     999999998 ->  Infinity Overflow Inexact Rounded
powx1339 power -9.9E999999999 999999998 ->  Infinity Overflow Inexact Rounded

powx1340 power -0.5           999999998 ->  8.67119187E-301029996 Inexact Rounded
powx1341 power -0.1           999999998 ->  1E-999999998  -- NB exact unrounded
powx1342 power -0.09          999999998 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1343 power -0.05          999999998 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1344 power -0.01          999999998 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1345 power -0.0001        999999998 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1347 power -0.0000001     999999998 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1348 power -0.0000000001  999999998 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1349 power -1E-999999999  999999998 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped

-- some subnormals
precision: 9
-- [precision is 9, so smallest exponent is -1000000007
powx1350 power  1e-1          500000000 ->  1E-500000000
powx1351 power  1e-2          999999999 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1352 power  1e-2          500000000 ->  1E-1000000000 Subnormal
powx1353 power  1e-2          500000001 ->  1E-1000000002 Subnormal
powx1354 power  1e-2          500000002 ->  1E-1000000004 Subnormal
powx1355 power  1e-2          500000003 ->  1E-1000000006 Subnormal
powx1356 power  1e-2          500000004 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped

powx1360 power  0.010001      500000000 ->  4.34941988E-999978287 Inexact Rounded
powx1361 power  0.010000001   500000000 ->  5.18469257E-999999979 Inexact Rounded
powx1362 power  0.010000001   500000001 ->  5.18469309E-999999981 Inexact Rounded
powx1363 power  0.0100000009  500000000 ->  3.49342003E-999999981 Inexact Rounded
powx1364 power  0.0100000001  500000000 ->  1.48413155E-999999998 Inexact Rounded
powx1365 power  0.01          500000000 ->  1E-1000000000 Subnormal
powx1366 power  0.0099999999  500000000 ->  6.7379E-1000000003 Underflow Subnormal Inexact Rounded
powx1367 power  0.0099999998  500000000 ->  4.54E-1000000005 Underflow Subnormal Inexact Rounded
powx1368 power  0.0099999997  500000000 ->  3E-1000000007 Underflow Subnormal Inexact Rounded
powx1369 power  0.0099999996  500000000 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped
powx1370 power  0.009         500000000 ->  0E-1000000007 Underflow Subnormal Inexact Rounded Clamped

-- 1/subnormal -> overflow
powx1371 power  1e-1         -500000000 ->  1E+500000000
powx1372 power  1e-2         -999999999 ->  Infinity Overflow Inexact Rounded
powx1373 power  1e-2         -500000000 ->  Infinity Overflow Inexact Rounded
powx1374 power  1e-2         -500000001 ->  Infinity Overflow Inexact Rounded
powx1375 power  1e-2         -500000002 ->  Infinity Overflow Inexact Rounded
powx1376 power  1e-2         -500000003 ->  Infinity Overflow Inexact Rounded
powx1377 power  1e-2         -500000004 ->  Infinity Overflow Inexact Rounded

powx1381 power  0.010001     -500000000 ->  2.29915719E+999978286 Inexact Rounded
powx1382 power  0.010000001  -500000000 ->  1.92875467E+999999978 Inexact Rounded
powx1383 power  0.010000001  -500000001 ->  1.92875448E+999999980 Inexact Rounded
powx1384 power  0.0100000009 -500000000 ->  2.86252438E+999999980 Inexact Rounded
powx1385 power  0.0100000001 -500000000 ->  6.73794717E+999999997 Inexact Rounded
powx1386 power  0.01         -500000000 ->  Infinity Overflow Inexact Rounded
powx1387 power  0.009999     -500000000 ->  Infinity Overflow Inexact Rounded

-- negative power giving subnormal
powx1388 power  100.000001   -500000000 ->  6.7379E-1000000003 Underflow Subnormal Inexact Rounded

----------------------------------------------------------------------
-- Below here are the tests with a non-integer rhs, including the   --
-- tests that previously caused Invalid operation.  An integer-only --
-- (on rhs) implementation should handle all the tests above as     --
-- shown, and would flag most of the following tests as Invalid.    --
----------------------------------------------------------------------
precision:   16
rounding:    half_even
maxExponent: 384
minExponent: -383

powx2000 power 7 '10000000000'        -> Infinity Overflow Inexact Rounded
powx2001 power 2 '2.000001'           -> 4.000002772589683 Inexact Rounded
powx2002 power 2 '2.00000000'         -> 4
powx2003 power 2 '2.000000001'        -> 4.000000002772589 Inexact Rounded
powx2004 power 2 '2.0000000001'       -> 4.000000000277259 Inexact Rounded
powx2005 power 2 '2.00000000001'      -> 4.000000000027726 Inexact Rounded
powx2006 power 2 '2.000000000001'     -> 4.000000000002773 Inexact Rounded
powx2007 power 2 '2.0000000000001'    -> 4.000000000000277 Inexact Rounded
powx2008 power 2 '2.00000000000001'   -> 4.000000000000028 Inexact Rounded
powx2009 power 2 '2.000000000000001'  -> 4.000000000000003 Inexact Rounded
powx2010 power 2 '2.0000000000000001' -> 4.000000000000000 Inexact Rounded
--                1 234567890123456

powx2011 power 1 1234 -> 1
precision: 4
powx2012 power 1 1234 -> 1
precision: 3
powx2013 power 1 1234     -> 1
powx2014 power 1 12.34e+2 -> 1
powx2015 power 1 12.3     -> 1.00 Inexact Rounded
powx2016 power 1 12.0     -> 1
powx2017 power 1 1.01     -> 1.00 Inexact Rounded
powx2018 power 2 1.00     -> 2
powx2019 power 2 2.00     -> 4
precision: 9
powx2030 power 1 1.0001           -> 1.00000000 Inexact Rounded
powx2031 power 1 1.0000001        -> 1.00000000 Inexact Rounded
powx2032 power 1 1.0000000001     -> 1.00000000 Inexact Rounded
powx2033 power 1 1.0000000000001  -> 1.00000000 Inexact Rounded
precision: 5
powx2034 power 1 1.0001           -> 1.0000 Inexact Rounded
powx2035 power 1 1.0000001        -> 1.0000 Inexact Rounded
powx2036 power 1 1.0000000001     -> 1.0000 Inexact Rounded
powx2037 power 1 1.0000000000001  -> 1.0000 Inexact Rounded
powx2038 power 1 1.0000000000001  -> 1.0000 Inexact Rounded

rounding: ceiling
precision: 3
powx2039 power 1 1.01     -> 1.00 Inexact Rounded
powx2040 power 1 12.3     -> 1.00 Inexact Rounded
rounding: half_even

-- 1 ** any integer, including big ones, should be exact
powx2041 power 1 1000000000   -> 1
powx2042 power 1 9999999999   -> 1
powx2043 power 1 12345678000  -> 1
powx2044 power 1 1234567800   -> 1
powx2045 power 1 1234567890   -> 1
powx2046 power 1 11234567891  -> 1
powx2047 power 1 12345678901  -> 1
powx2048 power 1 1234567896   -> 1
powx2049 power 1 -1234567896  -> 1
powx2051 power 1 1000000000   -> 1
powx2052 power 1 -1000000000  -> 1
powx2053 power 1 12345678000  -> 1
powx2054 power 1 -1234567896  -> 1
powx2055 power 1 1000000000   -> 1
powx2056 power 1 4300000000   -> 1
powx2057 power 1 -1000000000  -> 1
-- negatives ... but not out of range for decNumber
powx2061 power -1 100000   -> 1
powx2062 power -1 999999   -> -1
powx2063 power -1 1278000  -> 1
powx2064 power -1 127803   -> -1
powx2065 power -1 127890   -> 1
powx2066 power -1 1167891  -> -1
powx2067 power -1 1278901  -> -1
powx2068 power -1 127896   -> 1
powx2069 power -1 -167897  -> -1
powx2071 power -1 100000   -> 1
powx2072 power -1 -100001  -> -1
powx2073 power -1 1278000  -> 1
powx2074 power -1 -167896  -> 1
powx2075 power -1 100000   -> 1
powx2076 power -1 -100009  -> -1

-- The above were derived from the earlier version of power.decTest;
-- now start new tests for power(x,y) for non-integer y
precision: 9

-- tests from specification
powx2081 power 2           3    ->  '8'
powx2082 power -2          3    ->  '-8'
powx2083 power 2          -3    ->  '0.125'
powx2084 power 1.7        '8'   ->  '69.7575744' Inexact Rounded
powx2085 power 10 0.301029996   ->  2.00000000   Inexact Rounded
powx2086 power Infinity   '-1'  ->  '0'
powx2087 power Infinity   '0'   ->  '1'
powx2088 power Infinity   '1'   ->  'Infinity'
powx2089 power -Infinity  '-1'  ->  '-0'
powx2090 power -Infinity  '0'   ->  '1'
powx2091 power -Infinity  '1'   ->  '-Infinity'
powx2092 power -Infinity  '2'   ->  'Infinity'
powx2093 power 0  0             ->  'NaN' Invalid_operation

precision:   16
rounding:    half_even
maxExponent: 384
minExponent: -383

-- basics
powx2100 power 1E-7     1E-7   -> 0.9999983881917339      Inexact Rounded
powx2101 power 0.003    1E-7   -> 0.9999994190858697      Inexact Rounded
powx2102 power 0.7      1E-7   -> 0.9999999643325062      Inexact Rounded
powx2103 power 1.2      1E-7   -> 1.000000018232156       Inexact Rounded
powx2104 power 71       1E-7   -> 1.000000426268079       Inexact Rounded
powx2105 power 9E+9     1E-7   -> 1.000002292051668       Inexact Rounded

powx2110 power 1E-7     0.003  -> 0.9527961640236519      Inexact Rounded
powx2111 power 0.003    0.003  -> 0.9827235503366797      Inexact Rounded
powx2112 power 0.7      0.003  -> 0.9989305474406207      Inexact Rounded
powx2113 power 1.2      0.003  -> 1.000547114282834       Inexact Rounded
powx2114 power 71       0.003  -> 1.012870156273545       Inexact Rounded
powx2115 power 9E+9     0.003  -> 1.071180671278787       Inexact Rounded

powx2120 power 1E-7     0.7    -> 0.00001258925411794167  Inexact Rounded
powx2121 power 0.003    0.7    -> 0.01713897630281030     Inexact Rounded
powx2122 power 0.7      0.7    -> 0.7790559126704491      Inexact Rounded
powx2123 power 1.2      0.7    -> 1.136126977198889       Inexact Rounded
powx2124 power 71       0.7    -> 19.76427300093870       Inexact Rounded
powx2125 power 9E+9     0.7    -> 9289016.976853710       Inexact Rounded

powx2130 power 1E-7     1.2    -> 3.981071705534973E-9    Inexact Rounded
powx2131 power 0.003    1.2    -> 0.0009387403933595694   Inexact Rounded
powx2132 power 0.7      1.2    -> 0.6518049405663864      Inexact Rounded
powx2133 power 1.2      1.2    -> 1.244564747203978       Inexact Rounded
powx2134 power 71       1.2    -> 166.5367244638552       Inexact Rounded
powx2135 power 9E+9     1.2    -> 881233526124.8791       Inexact Rounded

powx2140 power 1E-7     71     -> 0E-398                  Inexact Rounded Underflow Subnormal Clamped
powx2141 power 0.003    71     -> 7.509466514979725E-180  Inexact Rounded
powx2142 power 0.7      71     -> 1.004525211269079E-11   Inexact Rounded
powx2143 power 1.2      71     -> 418666.7483186515       Inexact Rounded
powx2144 power 71       71     -> 2.750063734834616E+131  Inexact Rounded
powx2145 power 9E+9     71     -> Infinity                Inexact Rounded Overflow

powx2150 power 1E-7     9E+9   -> 0E-398                  Inexact Rounded Underflow Subnormal Clamped
powx2151 power 0.003    9E+9   -> 0E-398                  Inexact Rounded Underflow Subnormal Clamped
powx2152 power 0.7      9E+9   -> 0E-398                  Inexact Rounded Underflow Subnormal Clamped
powx2153 power 1.2      9E+9   -> Infinity                Inexact Rounded Overflow
powx2154 power 71       9E+9   -> Infinity                Inexact Rounded Overflow
powx2155 power 9E+9     9E+9   -> Infinity                Inexact Rounded Overflow

-- number line milestones with lhs<1 and lhs>1

-- Overflow boundary (Nmax)
powx2202 power 71     207.966651583983200 -> Infinity Inexact Rounded Overflow
powx2201 power 71     207.966651583983199 -> 9.999999999999994E+384 Inexact Rounded
powx2204 power 0.003 -152.603449817093577 -> Infinity Inexact Rounded Overflow
powx2203 power 0.003 -152.603449817093576 -> 9.999999999999994E+384 Inexact Rounded

-- Nmin boundary
powx2211 power 71    -206.886305341988480 -> 1.000000000000005E-383 Inexact Rounded
powx2212 power 71    -206.886305341988481 -> 1.000000000000001E-383 Inexact Rounded
powx2213 power 71    -206.886305341988482 -> 9.99999999999997E-384  Inexact Rounded Underflow Subnormal
powx2214 power 71    -206.886305341988483 -> 9.99999999999992E-384  Inexact Rounded Underflow Subnormal
--                                           9.999999999999924565357019820

powx2215 power 0.003  151.810704623238543 -> 1.000000000000009E-383 Inexact Rounded
powx2216 power 0.003  151.810704623238544 -> 1.000000000000003E-383 Inexact Rounded
powx2217 power 0.003  151.810704623238545 -> 9.99999999999997E-384  Inexact Rounded Underflow Subnormal
powx2218 power 0.003  151.810704623238546 -> 9.99999999999991E-384  Inexact Rounded Underflow Subnormal

-- Ntiny boundary, these edge cases determined using half_up rounding
rounding: half_up
powx2221 power 71    -215.151510469220498 -> 1E-398    Inexact Rounded Underflow Subnormal
powx2222 power 71    -215.151510469220499 -> 1E-398    Inexact Rounded Underflow Subnormal
powx2223 power 71    -215.151510469220500 -> 0E-398    Inexact Rounded Underflow Subnormal Clamped
powx2224 power 71    -215.151510469220501 -> 0E-398    Inexact Rounded Underflow Subnormal Clamped

powx2225 power 0.003  157.875613618285691 -> 1E-398    Inexact Rounded Underflow Subnormal
powx2226 power 0.003  157.875613618285692 -> 1E-398    Inexact Rounded Underflow Subnormal
powx2227 power 0.003  157.875613618285693 -> 0E-398    Inexact Rounded Underflow Subnormal Clamped
powx2228 power 0.003  220                 -> 0E-398    Inexact Rounded Underflow Subnormal Clamped
rounding: half_even

-- power(10, y) are important ...

-- Integer powers are exact, unless over/underflow
powx2301 power 10     385     -> Infinity Overflow Inexact Rounded
powx2302 power 10     384     -> 1.000000000000000E+384 Rounded
powx2303 power 10      17     -> 1.000000000000000E+17 Rounded
powx2304 power 10      16     -> 1.000000000000000E+16 Rounded
powx2305 power 10      15     -> 1000000000000000
powx2306 power 10      10     -> 10000000000
powx2307 power 10       5     -> 100000
powx2308 power 10       1     -> 10
powx2309 power 10       0     -> 1
powx2310 power 10      -1     -> 0.1
powx2311 power 10      -5     -> 0.00001
powx2312 power 10      -6     -> 0.000001
powx2313 power 10      -7     -> 1E-7
powx2314 power 10      -8     -> 1E-8
powx2315 power 10      -9     -> 1E-9
powx2316 power 10     -10     -> 1E-10
powx2317 power 10    -383     -> 1E-383
powx2318 power 10    -384     -> 1E-384 Subnormal
powx2319 power 10    -385     -> 1E-385 Subnormal
powx2320 power 10    -397     -> 1E-397 Subnormal
powx2321 power 10    -398     -> 1E-398 Subnormal
powx2322 power 10    -399     -> 0E-398 Subnormal Underflow Inexact Rounded Clamped
powx2323 power 10    -400     -> 0E-398 Subnormal Underflow Inexact Rounded Clamped

-- Independent sanity check: 1961 Godfrey & Siddons four-figure logs
powx2351 power 10   0.0000    -> 1
powx2352 power 10   0.3010    -> 1.999861869632744 Inexact Rounded
powx2353 power 10   0.4771    -> 2.999853181190793 Inexact Rounded
powx2354 power 10   0.6021    -> 4.000368510461250 Inexact Rounded
powx2355 power 10   0.6990    -> 5.000345349769785 Inexact Rounded
powx2356 power 10   0.7782    -> 6.000673538641164 Inexact Rounded
powx2357 power 10   0.8451    -> 7.000031591308969 Inexact Rounded
powx2358 power 10   0.9031    -> 8.000184448550990 Inexact Rounded
powx2359 power 10   0.9542    -> 8.999119108700520 Inexact Rounded
powx2360 power 10   0.9956    -> 9.899197750805841 Inexact Rounded
powx2361 power 10   0.9996    -> 9.990793899844618 Inexact Rounded
precision: 4
powx2371 power 10   0.0000    -> 1
powx2372 power 10   0.3010    -> 2.000 Inexact Rounded
powx2373 power 10   0.4771    -> 3.000 Inexact Rounded
powx2374 power 10   0.6021    -> 4.000 Inexact Rounded
powx2375 power 10   0.6990    -> 5.000 Inexact Rounded
powx2376 power 10   0.7782    -> 6.001 Inexact Rounded
powx2377 power 10   0.8451    -> 7.000 Inexact Rounded
powx2378 power 10   0.9031    -> 8.000 Inexact Rounded
powx2379 power 10   0.9542    -> 8.999 Inexact Rounded
powx2380 power 10   0.9956    -> 9.899 Inexact Rounded
powx2381 power 10   0.9996    -> 9.991 Inexact Rounded

-- 10**x ~=2 (inverse of the test in log10.decTest)
precision: 50
powx2401 power 10 0.30102999566398119521373889472449302676818988146211 -> 2.0000000000000000000000000000000000000000000000000 Inexact Rounded
precision: 49
powx2402 power 10 0.3010299956639811952137388947244930267681898814621 -> 2.000000000000000000000000000000000000000000000000 Inexact Rounded
precision: 48
powx2403 power 10 0.301029995663981195213738894724493026768189881462 -> 2.00000000000000000000000000000000000000000000000 Inexact Rounded
precision: 47
powx2404 power 10 0.30102999566398119521373889472449302676818988146 -> 2.0000000000000000000000000000000000000000000000 Inexact Rounded
precision: 46
powx2405 power 10 0.3010299956639811952137388947244930267681898815 -> 2.000000000000000000000000000000000000000000000 Inexact Rounded
precision: 45
powx2406 power 10 0.301029995663981195213738894724493026768189881 -> 2.00000000000000000000000000000000000000000000 Inexact Rounded
precision: 44
powx2407 power 10 0.30102999566398119521373889472449302676818988 -> 2.0000000000000000000000000000000000000000000 Inexact Rounded
precision: 43
powx2408 power 10 0.3010299956639811952137388947244930267681899 -> 2.000000000000000000000000000000000000000000 Inexact Rounded
precision: 42
powx2409 power 10 0.301029995663981195213738894724493026768190 -> 2.00000000000000000000000000000000000000000 Inexact Rounded
precision: 41
powx2410 power 10 0.30102999566398119521373889472449302676819 -> 2.0000000000000000000000000000000000000000 Inexact Rounded
precision: 40
powx2411 power 10 0.3010299956639811952137388947244930267682 -> 2.000000000000000000000000000000000000000 Inexact Rounded
precision: 39
powx2412 power 10 0.301029995663981195213738894724493026768 -> 2.00000000000000000000000000000000000000 Inexact Rounded
precision: 38
powx2413 power 10 0.30102999566398119521373889472449302677 -> 2.0000000000000000000000000000000000000 Inexact Rounded
precision: 37
powx2414 power 10 0.3010299956639811952137388947244930268 -> 2.000000000000000000000000000000000000 Inexact Rounded
precision: 36
powx2415 power 10 0.301029995663981195213738894724493027 -> 2.00000000000000000000000000000000000 Inexact Rounded
precision: 35
powx2416 power 10 0.30102999566398119521373889472449303 -> 2.0000000000000000000000000000000000 Inexact Rounded
precision: 34
powx2417 power 10 0.3010299956639811952137388947244930 -> 2.000000000000000000000000000000000 Inexact Rounded
precision: 33
powx2418 power 10 0.301029995663981195213738894724493 -> 2.00000000000000000000000000000000 Inexact Rounded
precision: 32
powx2419 power 10 0.30102999566398119521373889472449 -> 2.0000000000000000000000000000000 Inexact Rounded
precision: 31
powx2420 power 10 0.3010299956639811952137388947245 -> 2.000000000000000000000000000000 Inexact Rounded
precision: 30
powx2421 power 10 0.301029995663981195213738894725 -> 2.00000000000000000000000000000 Inexact Rounded
precision: 29
powx2422 power 10 0.30102999566398119521373889472 -> 2.0000000000000000000000000000 Inexact Rounded
precision: 28
powx2423 power 10 0.3010299956639811952137388947 -> 2.000000000000000000000000000 Inexact Rounded
precision: 27
powx2424 power 10 0.301029995663981195213738895 -> 2.00000000000000000000000000 Inexact Rounded
precision: 26
powx2425 power 10 0.30102999566398119521373889 -> 2.0000000000000000000000000 Inexact Rounded
precision: 25
powx2426 power 10 0.3010299956639811952137389 -> 2.000000000000000000000000 Inexact Rounded
precision: 24
powx2427 power 10 0.301029995663981195213739 -> 2.00000000000000000000000 Inexact Rounded
precision: 23
powx2428 power 10 0.30102999566398119521374 -> 2.0000000000000000000000 Inexact Rounded
precision: 22
powx2429 power 10 0.3010299956639811952137 -> 2.000000000000000000000 Inexact Rounded
precision: 21
powx2430 power 10 0.301029995663981195214 -> 2.00000000000000000000 Inexact Rounded
precision: 20
powx2431 power 10 0.30102999566398119521 -> 2.0000000000000000000 Inexact Rounded
precision: 19
powx2432 power 10 0.3010299956639811952 -> 2.000000000000000000 Inexact Rounded
precision: 18
powx2433 power 10 0.301029995663981195 -> 2.00000000000000000 Inexact Rounded
precision: 17
powx2434 power 10 0.30102999566398120 -> 2.0000000000000000 Inexact Rounded
precision: 16
powx2435 power 10 0.3010299956639812 -> 2.000000000000000 Inexact Rounded
precision: 15
powx2436 power 10 0.301029995663981 -> 2.00000000000000 Inexact Rounded
precision: 14
powx2437 power 10 0.30102999566398 -> 2.0000000000000 Inexact Rounded
precision: 13
powx2438 power 10 0.3010299956640 -> 2.000000000000 Inexact Rounded
precision: 12
powx2439 power 10 0.301029995664 -> 2.00000000000 Inexact Rounded
precision: 11
powx2440 power 10 0.30102999566 -> 2.0000000000 Inexact Rounded
precision: 10
powx2441 power 10 0.3010299957 -> 2.000000000 Inexact Rounded
precision:  9
powx2442 power 10 0.301029996 -> 2.00000000 Inexact Rounded
precision:  8
powx2443 power 10 0.30103000 -> 2.0000000 Inexact Rounded
precision:  7
powx2444 power 10 0.3010300 -> 2.000000 Inexact Rounded
precision:  6
powx2445 power 10 0.301030 -> 2.00000 Inexact Rounded
precision:  5
powx2446 power 10 0.30103 -> 2.0000 Inexact Rounded
precision:  4
powx2447 power 10 0.3010 -> 2.000 Inexact Rounded
precision:  3
powx2448 power 10 0.301 -> 2.00 Inexact Rounded
precision:  2
powx2449 power 10 0.30 -> 2.0 Inexact Rounded
precision:  1
powx2450 power 10 0.3 -> 2 Inexact Rounded

maxExponent: 384
minExponent: -383
precision:   16
rounding:    half_even

-- Close-to-e tests
precision:   34
powx2500 power 10 0.4342944819032518276511289189166048     -> 2.718281828459045235360287471352661  Inexact Rounded
powx2501 power 10 0.4342944819032518276511289189166049     -> 2.718281828459045235360287471352661  Inexact Rounded
powx2502 power 10 0.4342944819032518276511289189166050     -> 2.718281828459045235360287471352662  Inexact Rounded
powx2503 power 10 0.4342944819032518276511289189166051     -> 2.718281828459045235360287471352663  Inexact Rounded
powx2504 power 10 0.4342944819032518276511289189166052     -> 2.718281828459045235360287471352663  Inexact Rounded

-- e**e, 16->34
powx2505 power 2.718281828459045 2.718281828459045 -> '15.15426224147925705633739513098219' Inexact Rounded

-- Sequence around an integer
powx2512 power 10 2.9999999999999999999999999999999997     -> 999.9999999999999999999999999999993  Inexact Rounded
powx2513 power 10 2.9999999999999999999999999999999998     -> 999.9999999999999999999999999999995  Inexact Rounded
powx2514 power 10 2.9999999999999999999999999999999999     -> 999.9999999999999999999999999999998  Inexact Rounded
powx2515 power 10 3.0000000000000000000000000000000000     -> 1000
powx2516 power 10 3.0000000000000000000000000000000001     -> 1000.000000000000000000000000000000  Inexact Rounded
powx2517 power 10 3.0000000000000000000000000000000002     -> 1000.000000000000000000000000000000  Inexact Rounded
powx2518 power 10 3.0000000000000000000000000000000003     -> 1000.000000000000000000000000000001  Inexact Rounded

-- randomly generated tests
maxExponent: 384
minExponent: -383

-- P=34, within 0-999 -- positive arg2
Precision: 34
powx3201 power 5.301557744131969249145904611290735  369.3175647984435534243813466380579 -> 3.427165676345688240023113326603960E+267 Inexact Rounded
powx3202 power 0.0000000000506875655819165973738225 21.93514102704466434121826965196878 -> 1.498169860033487321566659495340789E-226 Inexact Rounded
powx3203 power 97.88877680721519917858007810494043  5.159898445242793470476673109899554 -> 18705942904.43290467281449559427982      Inexact Rounded
powx3204 power 7.380441015594399747973924380493799  17.93614173904818313507525109033288 -> 3715757985820076.273336082702577274      Inexact Rounded
powx3205 power 2.045623627647350918819219169855040  1082.999652407430697958175966996254 -> 4.208806435006704867447150904279854E+336 Inexact Rounded
powx3206 power 0.0000000762582873112118926142955423 20.30534237055073996975203864170432 -> 2.967574278677013090697130349198877E-145 Inexact Rounded
powx3207 power 0.0000000000194091470907814855660535 14.71164213947722238856835440242911 -> 2.564391397469554735037158345963280E-158 Inexact Rounded
powx3208 power 0.0000000000509434185382818596853504 20.97051498204188277347203735421595 -> 1.420157372748083000927138678417272E-216 Inexact Rounded
powx3209 power 0.0005389217212073307301395750745119 43.96798225485747315858678755538971 -> 1.957850185781292007977898626137240E-144 Inexact Rounded
powx3210 power 498.5690105989136050444077447411198  128.1038813807243375878831104745803 -> 3.882212970903893127009102293596268E+345 Inexact Rounded
powx3211 power 0.0000000935428918637303954281938975 5.736933454863278597460091596496099 -> 4.733219644540496152403967823635195E-41  Inexact Rounded
powx3212 power 8.581586784734161309180363110126352  252.0229459968869784643374981477208 -> 1.907464842458674622356177850049873E+235 Inexact Rounded
powx3213 power 294.1005302951621709143320795278305  155.5466374141708615975111014663722 -> 9.251717033292072959166737280729728E+383 Inexact Rounded
powx3214 power 0.0000000041253343654396865855722090 19.00170974760425576247662125110472 -> 4.779566288553864405790921353593512E-160 Inexact Rounded
powx3215 power 0.0000000000046912257352141395184092 24.66089523148729269098773236636878 -> 4.205126874048597849476723538057527E-280 Inexact Rounded
powx3216 power 0.0000000000036796674296520639450494 22.09713956900694689234335912523078 -> 2.173081843837539818472071316420405E-253 Inexact Rounded
powx3217 power 9.659887100303037657934372148567685  277.3765665424320875993026404492216 -> 1.614974043145519382749740616665041E+273 Inexact Rounded
powx3218 power 0.0000083231310642229204398943076403 29.33123211782131466471359128190372 -> 1.013330439786660210757226597785328E-149 Inexact Rounded
powx3219 power 0.0938084859086450954956863725653664 262.6091918199905272837286784975012 -> 1.262802485286301066967555821509344E-270 Inexact Rounded
powx3220 power 8.194926977580900145696305910223304  184.3705133945546202012995485297248 -> 2.696353910907824016690021495828584E+168 Inexact Rounded
powx3221 power 72.39594594653085161522285114566120  168.7721909489321402152033939836725 -> 7.379858293630460043361584410795031E+313 Inexact Rounded
powx3222 power 0.0000000000003436856010144185445537 26.34329868961274988994452526178983 -> 4.585379573595865689605567720192768E-329 Inexact Rounded
powx3223 power 20.18365633762226550254542489492623  127.2099705237021350103678072707790 -> 1.020919629336979353690271762206060E+166 Inexact Rounded
powx3224 power 0.0000000553723990761530290129268131 8.157597566134754638015199501162405 -> 6.349030513396147480954474615067145E-60  Inexact Rounded
powx3225 power 0.0001028742674265840656614682618035 93.99842317306603797965470281716482 -> 1.455871110222736531854990397769940E-375 Inexact Rounded
powx3226 power 95.90195152775543876489746343266050  143.5992850002211509777720799352475 -> 3.881540015848530405189834366588567E+284 Inexact Rounded
powx3227 power 0.0000000000041783747057233878360333 12.14591167764993506821334760954430 -> 6.190998557456885985124592807383163E-139 Inexact Rounded
powx3228 power 0.5572830497086740798434917090018768 1001.921811263919522230330241349166 -> 3.871145158537170450093833881625838E-255 Inexact Rounded
powx3229 power 516.4754759779093954790813881333232  29.23812463126309057800793645336343 -> 2.110986192408878294012450052929185E+79  Inexact Rounded
powx3230 power 0.0000835892099464584776847299020706 27.64279992884843877453592659341588 -> 1.891535098905506689512376224943293E-113 Inexact Rounded
powx3231 power 72.45836577748571838139900165184955  166.2562890735032545091688015160084 -> 1.784091549041561516923092542939141E+309 Inexact Rounded
powx3232 power 305.1823317643335924007629563009032  83.01065159508472884219290136319623 -> 1.757493136164395229602456782779110E+206 Inexact Rounded
powx3233 power 7.108527102951713603542835791733786  145.7057852766236365450463428821948 -> 1.285934774113104362663619896550528E+124 Inexact Rounded
powx3234 power 6.471393503175464828149365697049824  64.11741937262455725284754171995720 -> 9.978990355881803195280027533011699E+51  Inexact Rounded
powx3235 power 39.72898094138459885662380866268385  239.9677288017447400786672779735168 -> 5.422218208517098335832848487375086E+383 Inexact Rounded
powx3236 power 0.0002865592332736973000183287329933 90.34733869590583787065642532641096 -> 8.293733126976212033209243257136796E-321 Inexact Rounded
powx3237 power 0.0000011343384394864811195077357936 1.926568285528399656789140809399396 -> 3.516055639378350146874261077470142E-12  Inexact Rounded
powx3238 power 0.0000000035321610295065299384889224 7.583861778824284092434085265265582 -> 7.970899823817369764381976286536230E-65  Inexact Rounded
powx3239 power 657.5028301569352677543770758346683  90.55778453811965116200206020172758 -> 1.522530898581564200655160665723268E+255 Inexact Rounded
powx3240 power 8.484756398325748879450577520251447  389.7468292476262478578280531222417 -> 8.595142803587368093392510310811218E+361 Inexact Rounded

-- P=16, within 0-99 -- positive arg2
Precision: 16
powx3101 power 0.0000215524639223 48.37532522355252 -> 1.804663257287277E-226 Inexact Rounded
powx3102 power 00.80705856227999  2706.777535121391 -> 1.029625065876157E-252 Inexact Rounded
powx3103 power 3.445441676383689  428.5185892455830 -> 1.657401683096454E+230 Inexact Rounded
powx3104 power 0.0040158689495826 159.5725558816240 -> 4.255743665762492E-383 Inexact Rounded
powx3105 power 0.0000841553281215 38.32504413453944 -> 6.738653902512052E-157 Inexact Rounded
powx3106 power 0.7322610252571353 502.1254457674118 -> 1.109978126985943E-68  Inexact Rounded
powx3107 power 10.75052532144880  67.34180604734781 -> 2.873015019470189E+69  Inexact Rounded
powx3108 power 26.20425952945617  104.6002671186488 -> 2.301859355777030E+148 Inexact Rounded
powx3109 power 0.0000055737473850 31.16285859005424 -> 1.883348470100446E-164 Inexact Rounded
powx3110 power 61.06096011360700  10.93608439088726 -> 3.382686473028249E+19  Inexact Rounded
powx3111 power 9.340880853257137  179.9094938131726 -> 3.819299795937696E+174 Inexact Rounded
powx3112 power 0.0000050767371756 72.03346394186741 -> 4.216236691569869E-382 Inexact Rounded
powx3113 power 6.838478807860596  47.49665590602285 -> 4.547621630099203E+39  Inexact Rounded
powx3114 power 0.1299324346439081 397.7440523576938 -> 3.065047705553981E-353 Inexact Rounded
powx3115 power 0.0003418047034264 20.00516791512018 -> 4.546189665380487E-70  Inexact Rounded
powx3116 power 0.0001276899611715 78.12968287355703 -> 5.960217405063995E-305 Inexact Rounded
powx3117 power 25.93160588180509  252.6245071004620 -> 1.472171597589146E+357 Inexact Rounded
powx3118 power 35.47516857763178  86.14723037360925 -> 3.324299908481125E+133 Inexact Rounded
powx3119 power 0.0000048171086721 43.31965603038666 -> 4.572331516616228E-231 Inexact Rounded
powx3120 power 17.97652681097851  144.4684576550292 -> 1.842509906097860E+181 Inexact Rounded
powx3121 power 3.622765141518729  305.1948680344950 -> 4.132320967578704E+170 Inexact Rounded
powx3122 power 0.0080959002453519 143.9899444945627 -> 6.474627812947047E-302 Inexact Rounded
powx3123 power 9.841699927276571  299.2466668837188 -> 1.489097656208736E+297 Inexact Rounded
powx3124 power 0.0786659206232355 347.4750796962570 -> 2.05764809646925E-384  Inexact Rounded Underflow Subnormal
powx3125 power 0.0000084459792645 52.47348690745487 -> 6.076251876516942E-267 Inexact Rounded
powx3126 power 27.86589909967504  191.7296537102283 -> 1.157064112989386E+277 Inexact Rounded
powx3127 power 0.0000419907937234 58.44957702730767 -> 1.496950672075162E-256 Inexact Rounded
powx3128 power 0.0000664977739382 80.06749213261876 -> 3.488517620107875E-335 Inexact Rounded
powx3129 power 58.49554484886656  125.8480768373499 -> 2.449089862146640E+222 Inexact Rounded
powx3130 power 15.02820060024449  212.3527988973338 -> 8.307913932682067E+249 Inexact Rounded
powx3131 power 0.0002650089942992 30.92173123678761 -> 2.517827664836147E-111 Inexact Rounded
powx3132 power 0.0007342977426578 69.49168880741123 -> 1.600168665674440E-218 Inexact Rounded
powx3133 power 0.0063816068650629 150.1400094183812 -> 2.705057295799001E-330 Inexact Rounded
powx3134 power 9.912921122728791  297.8274013633411 -> 4.967624993438900E+296 Inexact Rounded
powx3135 power 1.988603563989245  768.4862967922182 -> 2.692842474899596E+229 Inexact Rounded
powx3136 power 8.418014519517691  164.2431359980725 -> 9.106211585888836E+151 Inexact Rounded
powx3137 power 6.068823604450686  120.2955212365837 -> 1.599431918105982E+94  Inexact Rounded
powx3138 power 56.90062738303850  54.90468294683645 -> 2.312839177902428E+96  Inexact Rounded
powx3139 power 5.710905139750871  73.44608752962156 -> 3.775876053709929E+55  Inexact Rounded
powx3140 power 0.0000017446761203 1.223981492228899 -> 8.952936595465635E-8   Inexact Rounded

-- P=7, within 0-9 -- positive arg2
Precision: 7
powx3001 power 8.738689  55.96523 -> 4.878180E+52  Inexact Rounded
powx3002 power 0.0404763 147.4965 -> 3.689722E-206 Inexact Rounded
powx3003 power 0.0604232 76.69778 -> 3.319183E-94  Inexact Rounded
powx3004 power 0.0058855 107.5018 -> 1.768875E-240 Inexact Rounded
powx3005 power 2.058302  1173.050 -> 5.778899E+367 Inexact Rounded
powx3006 power 0.0056998 85.70157 -> 4.716783E-193 Inexact Rounded
powx3007 power 0.8169297 3693.537 -> 4.475962E-325 Inexact Rounded
powx3008 power 0.2810153 659.9568 -> 1.533177E-364 Inexact Rounded
powx3009 power 4.617478  15.68308 -> 2.629748E+10  Inexact Rounded
powx3010 power 0.0296418 244.2302 -> 6.207949E-374 Inexact Rounded
powx3011 power 0.0036456 127.9987 -> 8.120891E-313 Inexact Rounded
powx3012 power 0.5012813 577.5418 -> 6.088802E-174 Inexact Rounded
powx3013 power 0.0033275 119.9800 -> 5.055049E-298 Inexact Rounded
powx3014 power 0.0037652 111.7092 -> 1.560351E-271 Inexact Rounded
powx3015 power 0.6463252 239.0568 -> 4.864564E-46  Inexact Rounded
powx3016 power 4.784378  475.0521 -> 8.964460E+322 Inexact Rounded
powx3017 power 4.610305  563.1791 -> 6.290298E+373 Inexact Rounded
powx3018 power 0.0175167 80.52208 -> 3.623472E-142 Inexact Rounded
powx3019 power 5.238307  356.7944 -> 4.011461E+256 Inexact Rounded
powx3020 power 0.0003527 96.26347 -> 4.377932E-333 Inexact Rounded
powx3021 power 0.0015155 136.0516 -> 2.57113E-384  Inexact Rounded Underflow Subnormal
powx3022 power 5.753573  273.2340 -> 4.373184E+207 Inexact Rounded
powx3023 power 7.778665  332.7917 -> 3.060640E+296 Inexact Rounded
powx3024 power 1.432479  2046.064 -> 2.325829E+319 Inexact Rounded
powx3025 power 5.610516  136.4563 -> 1.607502E+102 Inexact Rounded
powx3026 power 0.0050697 137.4513 -> 3.522315E-316 Inexact Rounded
powx3027 power 5.678737  85.16253 -> 1.713909E+64  Inexact Rounded
powx3028 power 0.0816167 236.1973 -> 9.228802E-258 Inexact Rounded
powx3029 power 0.2602805 562.0157 -> 2.944556E-329 Inexact Rounded
powx3030 power 0.0080936 24.25367 -> 1.839755E-51  Inexact Rounded
powx3031 power 4.092016  82.94603 -> 5.724948E+50  Inexact Rounded
powx3032 power 0.0078255 7.204184 -> 6.675342E-16  Inexact Rounded
powx3033 power 0.9917693 29846.44 -> 7.430177E-108 Inexact Rounded
powx3034 power 1.610380  301.2467 -> 2.170142E+62  Inexact Rounded
powx3035 power 0.0588236 212.1097 -> 1.023196E-261 Inexact Rounded
powx3036 power 2.498069  531.4647 -> 2.054561E+211 Inexact Rounded
powx3037 power 9.964342  326.5438 -> 1.089452E+326 Inexact Rounded
powx3038 power 0.0820626 268.8718 -> 1.107350E-292 Inexact Rounded
powx3039 power 6.176486  360.7779 -> 1.914449E+285 Inexact Rounded
powx3040 power 4.206363  16.17288 -> 1.231314E+10  Inexact Rounded

-- P=34, within 0-999 -- negative arg2
Precision: 34
powx3701 power 376.0915270000109486633402827007902  -35.69822349904102131649243701958463 -> 1.165722831225506457828653413200143E-92  Inexact Rounded
powx3702 power 0.0000000503747440074613191665845314 -9.520308341497979093021813571450575 -> 3.000432478861883953977971226770410E+69  Inexact Rounded
powx3703 power 290.6858731495339778337953407938308  -118.5459048597789693292455673428367 -> 9.357969047113989238392527565200302E-293 Inexact Rounded
powx3704 power 4.598864607620052062908700928454182  -299.8323667698931125720218537483753 -> 2.069641269855413539579128114448478E-199 Inexact Rounded
powx3705 power 2.556952676986830645708349254938903  -425.1755373251941383147998924703593 -> 4.428799777833598654260883861514638E-174 Inexact Rounded
powx3706 power 0.0000005656198763404221986640610118 -32.83361380678301321230028730075315 -> 1.340270622401829145968477601029251E+205 Inexact Rounded
powx3707 power 012.4841978642452960750801410372125  -214.3734291828712962809866663321921 -> 9.319857751170603140459057535971202E-236 Inexact Rounded
powx3708 power 0.0000000056041586148066919174315551 -37.21129049213858341528033343116533 -> 1.118345010652454313186702341873169E+307 Inexact Rounded
powx3709 power 0.0694569218941833767199998804202152 -8.697509072368973932501239815677732 -> 11862866995.51026489032838174290271      Inexact Rounded
powx3710 power 6.380984024259450398729243522354144  -451.0635696889193561457985486366827 -> 8.800353109387322474809325670314330E-364 Inexact Rounded
powx3711 power 786.0264840756809048288007204917801  -43.09935384678762773057342161718540 -> 1.616324183365644133979585419925934E-125 Inexact Rounded
powx3712 power 96.07836427113204744101287948445130  -185.1414572546330024388914720271876 -> 8.586320815218383004023264980018610E-368 Inexact Rounded
powx3713 power 0.0000000002332189796855870659792406 -5.779561613164628076880609893753327 -> 4.678450775876385793618570483345066E+55  Inexact Rounded
powx3714 power 0.7254146672024602242369943237968857 -2115.512891397828615710130092245691 -> 8.539080958041689288202111403102495E+294 Inexact Rounded
powx3715 power 0.0017380543649702864796144008592137 -6.307668017761022788220578633538713 -> 256309141459075651.2275798017695017      Inexact Rounded
powx3716 power 05.29498758952276908267649116142379  -287.3233896734103442991981056134167 -> 1.039130027847489364009368608104291E-208 Inexact Rounded
powx3717 power 15.64403593865932622003462779104178  -110.5296633358063267478609032002475 -> 9.750540276026524527375125980296142E-133 Inexact Rounded
powx3718 power 89.69639006761571087634945077373508  -181.3209914139357665609268339422627 -> 8.335034232277762924539395632025281E-355 Inexact Rounded
powx3719 power 6.974087483731006359914914110135058  -174.6815625746710345173615508179842 -> 4.553072265122011176641590109568031E-148 Inexact Rounded
powx3720 power 0.0034393024010554821130553772681993 -93.60931598413919272595497100497364 -> 4.067468855817145539589988349449394E+230 Inexact Rounded
powx3721 power 63.32834072300379155053737260965633  -168.3926799435088324825751446957616 -> 4.207907835462640471617519501741094E-304 Inexact Rounded
powx3722 power 00.00216088174206276369011255907785  -70.12279562855442784757874508991013 -> 8.000657143378187029609343435067057E+186 Inexact Rounded
powx3723 power 934.5957982703545893572134393004375  -102.2287735565878252484031426026726 -> 2.073813769209257617246544424827240E-304 Inexact Rounded
powx3724 power 107.9116792558793921873995885441177  -44.11941092260869786313838181499158 -> 2.005476533631183268912552168759595E-90  Inexact Rounded
powx3725 power 0.0000000000188049827381428191769262 -19.32118917192242027966847501724073 -> 1.713174297100918857053338286389034E+207 Inexact Rounded
powx3726 power 614.9820907366248142166636259027728  -4.069913257030791586645250035698123 -> 4.462432572576935752713876293746717E-12  Inexact Rounded
powx3727 power 752.0655175769182096165651274049422  -22.59292060348797472013598378334370 -> 1.039881526694635205040192531504131E-65  Inexact Rounded
powx3728 power 72.20446632047659449616175456059013  -175.4705356401853924020842356605072 -> 7.529540175791582421966947814549028E-327 Inexact Rounded
powx3729 power 518.8346486600403405764055847937416  -65.87320268592761588756963215588232 -> 1.420189426992170936958891180073151E-179 Inexact Rounded
powx3730 power 3.457164372003960576453458502270716  -440.3201118177861273814529713443698 -> 6.176418595751201287186292664257369E-238 Inexact Rounded
powx3731 power 7.908352793344189720739467675503991  -298.6646112894719680394152664740255 -> 5.935857120229147638104675057695125E-269 Inexact Rounded
powx3732 power 0.0000004297399403788595027926075086 -22.66504617185071293588817501468339 -> 2.012270405520600820469665145636204E+144 Inexact Rounded
powx3733 power 0.0000008592124097322966354868716443 -9.913109586558030204789520190180906 -> 1.354958763843310237046818832755215E+60  Inexact Rounded
powx3734 power 161.4806080561258105880907470989925  -70.72907837434814261716311990271578 -> 6.632555003698945544941329872901929E-157 Inexact Rounded
powx3735 power 0.0000000090669568624173832705631918 -36.53759624613665940127058439106640 -> 7.161808401023414735428130112941559E+293 Inexact Rounded
powx3736 power 0.0000000000029440295978365709342752 -1.297354238738921988884421117731562 -> 911731060579291.7661267358872917380      Inexact Rounded
powx3737 power 21.37477220144832172175460425143692  -76.95949933640539226475686997477889 -> 4.481741242418091914011962399912885E-103 Inexact Rounded
powx3738 power 0.0000000000186657798201636342150903 -20.18296240350678245567049161730909 -> 3.483954007114900406906338526575672E+216 Inexact Rounded
powx3739 power 0.0006522464792960191985996959126792 -80.03762491483514679886504099194414 -> 9.266548513614215557228467517053035E+254 Inexact Rounded
powx3740 power 0.0000000032851343694200568966168055 -21.53462116926375512242403160008026 -> 4.873201679668455240861376213601189E+182 Inexact Rounded

-- P=16, within 0-99 -- negative arg2
Precision: 16
powx3601 power 0.0000151338748474 -40.84655618364688 -> 7.628470824137755E+196 Inexact Rounded
powx3602 power 0.1542771848654862 -435.8830009466800 -> 6.389817177800744E+353 Inexact Rounded
powx3603 power 48.28477749367364  -218.5929209902050 -> 8.531049532576154E-369 Inexact Rounded
powx3604 power 7.960775891584911  -12.78113732182505 -> 3.053270889769488E-12  Inexact Rounded
powx3605 power 0.9430340651863058 -9010.470056913748 -> 3.313374654923807E+229 Inexact Rounded
powx3606 power 0.0000202661501602 -65.57915207383306 -> 5.997379176536464E+307 Inexact Rounded
powx3607 power 04.33007440798390  -232.0476834666588 -> 2.007827183010456E-148 Inexact Rounded
powx3608 power 0.0000141944643914 -11.32407921958717 -> 7.902934485074846E+54  Inexact Rounded
powx3609 power 0.0000021977758261 -53.53706138253307 -> 8.195631772317815E+302 Inexact Rounded
powx3610 power 39.51297655474188  -19.40370976012326 -> 1.040699608072659E-31  Inexact Rounded
powx3611 power 38.71210232488775  -66.58341618227921 -> 1.886855066146495E-106 Inexact Rounded
powx3612 power 0.0000804235229062 -6.715207948992859 -> 3.134757864389333E+27  Inexact Rounded
powx3613 power 0.0000073547092399 -11.27725685719934 -> 7.781428390953695E+57  Inexact Rounded
powx3614 power 52.72181272599316  -186.1422311607435 -> 2.916601998744177E-321 Inexact Rounded
powx3615 power 0.0969519963083306 -280.8220862151369 -> 3.955906885970987E+284 Inexact Rounded
powx3616 power 94.07263302150081  -148.2031146071230 -> 3.361958990752490E-293 Inexact Rounded
powx3617 power 85.80286965053704  -90.21453695813759 -> 3.715602429645798E-175 Inexact Rounded
powx3618 power 03.52699858152259  -492.0414362539196 -> 4.507309220081092E-270 Inexact Rounded
powx3619 power 0.0508278086396068 -181.0871731572167 -> 2.034428013017949E+234 Inexact Rounded
powx3620 power 0.395576740303172  -915.5524507432392 -> 5.706585187437578E+368 Inexact Rounded
powx3621 power 38.06105826789202  -49.75913753435335 -> 2.273188991431738E-79  Inexact Rounded
powx3622 power 0.0003656748910646 -73.28988491310354 -> 7.768936940568763E+251 Inexact Rounded
powx3623 power 0.0000006373551809 -51.30825234200690 -> 7.697618167701985E+317 Inexact Rounded
powx3624 power 82.41729920673856  -35.73319631625699 -> 3.424042354585529E-69  Inexact Rounded
powx3625 power 0.7845821453127670 -971.4982028897663 -> 2.283415527661089E+102 Inexact Rounded
powx3626 power 4.840983673433497  -182.3730452370515 -> 1.220591407927770E-125 Inexact Rounded
powx3627 power 0.0000006137592139 -2.122139474431484 -> 15231217034839.29      Inexact Rounded
powx3628 power 0.0003657962862984 -35.97993782448099 -> 4.512701319250839E+123 Inexact Rounded
powx3629 power 40.93693004443150  -165.1362408792997 -> 6.044276411057239E-267 Inexact Rounded
powx3630 power 0.2941552583028898 -17.41046264945892 -> 1787833103.503346      Inexact Rounded
powx3631 power 63.99335135369977  -69.92417205168579 -> 5.099359804872509E-127 Inexact Rounded
powx3632 power 0.0000657924467388 -89.14497293588313 -> 6.145878266688521E+372 Inexact Rounded
powx3633 power 11.35071250339147  -323.3705865614542 -> 6.863626248766775E-342 Inexact Rounded
powx3634 power 23.88024718470895  -277.7117513329510 -> 2.006441422612815E-383 Inexact Rounded
powx3635 power 0.0000009111939914 -58.51782946929182 -> 2.954352883996773E+353 Inexact Rounded
powx3636 power 0.0000878179048782 -75.81060420238669 -> 3.306878455207585E+307 Inexact Rounded
powx3637 power 07.39190564273779  -287.5047307244636 -> 1.692080354659805E-250 Inexact Rounded
powx3638 power 0.0000298310819799 -1.844740377759355 -> 222874718.7238888      Inexact Rounded
powx3639 power 0.0000006412929384 -28.24850078229290 -> 8.737164230666529E+174 Inexact Rounded
powx3640 power 0.0000010202965998 -47.17573701956498 -> 4.392845306049341E+282 Inexact Rounded

-- P=7, within 0-9 -- negative arg2
Precision: 7
powx3501 power 0.326324  -71.96509  -> 1.000673E+35  Inexact Rounded
powx3502 power 0.0017635 -0.7186967 -> 95.28419      Inexact Rounded
powx3503 power 8.564155  -253.0899  -> 8.850512E-237 Inexact Rounded
powx3504 power 8.987272  -2.155789  -> 0.008793859   Inexact Rounded
powx3505 power 9.604856  -139.9630  -> 3.073492E-138 Inexact Rounded
powx3506 power 0.8472919 -2539.085  -> 5.372686E+182 Inexact Rounded
powx3507 power 5.312329  -60.32965  -> 1.753121E-44  Inexact Rounded
powx3508 power 0.0338294 -100.5440  -> 7.423939E+147 Inexact Rounded
powx3509 power 0.0017777 -130.8583  -> 7.565629E+359 Inexact Rounded
powx3510 power 8.016154  -405.5689  -> 2.395977E-367 Inexact Rounded
powx3511 power 5.016570  -327.8906  -> 2.203784E-230 Inexact Rounded
powx3512 power 0.8161743 -744.5276  -> 4.786899E+65  Inexact Rounded
powx3513 power 0.0666343 -164.7320  -> 5.951240E+193 Inexact Rounded
powx3514 power 0.0803966 -202.2666  -> 2.715512E+221 Inexact Rounded
powx3515 power 0.0014752 -12.55547  -> 3.518905E+35  Inexact Rounded
powx3516 power 9.737565  -14.69615  -> 2.975672E-15  Inexact Rounded
powx3517 power 0.6634172 -152.7308  -> 1.654458E+27  Inexact Rounded
powx3518 power 0.0009337 -33.32939  -> 9.575039E+100 Inexact Rounded
powx3519 power 8.679922  -224.4194  -> 2.392446E-211 Inexact Rounded
powx3520 power 7.390494  -161.9483  -> 2.088375E-141 Inexact Rounded
powx3521 power 0.4631489 -417.1673  -> 2.821106E+139 Inexact Rounded
powx3522 power 0.0095471 -7.677458  -> 3.231855E+15  Inexact Rounded
powx3523 power 6.566339  -176.1867  -> 9.965633E-145 Inexact Rounded
powx3524 power 2.696128  -26.15501  -> 5.419731E-12  Inexact Rounded
powx3525 power 0.4464366 -852.1893  -> 2.957725E+298 Inexact Rounded
powx3526 power 0.4772006 -921.4111  -> 1.118105E+296 Inexact Rounded
powx3527 power 8.923696  -359.2211  -> 3.501573E-342 Inexact Rounded
powx3528 power 0.0018008 -66.91252  -> 4.402718E+183 Inexact Rounded
powx3529 power 0.0811964 -92.83278  -> 1.701111E+101 Inexact Rounded
powx3530 power 0.0711219 -58.94347  -> 4.644148E+67  Inexact Rounded
powx3531 power 7.958121  -50.66123  -> 2.311161E-46  Inexact Rounded
powx3532 power 6.106466  -81.83610  -> 4.943285E-65  Inexact Rounded
powx3533 power 4.557634  -129.5268  -> 4.737917E-86  Inexact Rounded
powx3534 power 0.0027348 -9.180135  -> 3.383524E+23  Inexact Rounded
powx3535 power 0.0083924 -46.24016  -> 9.996212E+95  Inexact Rounded
powx3536 power 2.138523  -47.25897  -> 2.507009E-16  Inexact Rounded
powx3537 power 1.626728  -1573.830  -> 2.668117E-333 Inexact Rounded
powx3538 power 0.082615  -164.5842  -> 1.717882E+178 Inexact Rounded
powx3539 power 7.636003  -363.6763  -> 8.366174E-322 Inexact Rounded
powx3540 power 0.0021481 -138.0065  -> 1.562505E+368 Inexact Rounded


-- Invalid operations due to restrictions
-- [next two probably skipped by most test harnesses]
precision: 100000000
powx4001  power 1 1.1 ->  NaN         Invalid_context
precision:  99999999
powx4002  power 1 1.1 ->  NaN         Invalid_context

precision: 9
maxExponent:   1000000
minExponent:   -999999
powx4003  power 1 1.1  ->  NaN        Invalid_context
maxExponent:    999999
minExponent:   -999999
powx4004  power 1 1.1  ->  1.00000000 Inexact Rounded
maxExponent:    999999
minExponent:  -1000000
powx4005  power 1 1.1  ->  NaN        Invalid_context
maxExponent:    999999
minExponent:   -999998
powx4006  power 1 1.1  ->  1.00000000 Inexact Rounded

-- operand range violations
powx4007  power 1             1.1E+999999  ->  1
powx4008  power 1             1.1E+1000000 ->  NaN Invalid_operation
powx4009  power 1.1E+999999   1.1          ->  Infinity Overflow Inexact Rounded
powx4010  power 1.1E+1000000  1.1          ->  NaN Invalid_operation
powx4011  power 1             1.1E-1999997 ->  1.00000000 Inexact Rounded
powx4012  power 1             1.1E-1999998 ->  NaN Invalid_operation
powx4013  power 1.1E-1999997  1.1          ->  0E-1000006 Underflow Inexact Rounded Clamped Subnormal
powx4014  power 1.1E-1999998  1.1          ->  NaN Invalid_operation

-- rounding modes -- power is sensitive
precision:   7
maxExponent: 99
minExponent: -99

--   0.7  ** 3.3 =>   0.30819354053418943822
--   0.7  ** 3.4 =>   0.29739477638272533854
--  -1.2  ** 17  => -22.18611106740436992
--  -1.3  ** 17  => -86.50415919381337933
--   0.5  ** 11  =>   0.00048828125
--   3.15 ** 3   =>  31.255875

rounding: up
powx4100  power      0.7  3.3 ->  0.3081936 Inexact Rounded
powx4101  power      0.7  3.4 ->  0.2973948 Inexact Rounded
powx4102  power     -1.2  17  -> -22.18612  Inexact Rounded
powx4103  power     -1.3  17  -> -86.50416  Inexact Rounded
powx4104  power  17 81.27115  -> 9.999974E+99 Inexact Rounded
powx4105  power  17 81.27116  -> Infinity     Overflow Inexact Rounded

rounding: down
powx4120  power      0.7  3.3 ->  0.3081935 Inexact Rounded
powx4121  power      0.7  3.4 ->  0.2973947 Inexact Rounded
powx4122  power     -1.2  17  -> -22.18611  Inexact Rounded
powx4123  power     -1.3  17  -> -86.50415  Inexact Rounded
powx4124  power  17 81.27115  -> 9.999973E+99 Inexact Rounded
powx4125  power  17 81.27116  -> 9.999999E+99 Overflow Inexact Rounded

rounding: floor
powx4140  power      0.7  3.3 ->  0.3081935 Inexact Rounded
powx4141  power      0.7  3.4 ->  0.2973947 Inexact Rounded
powx4142  power     -1.2  17  -> -22.18612  Inexact Rounded
powx4143  power     -1.3  17  -> -86.50416  Inexact Rounded
powx4144  power  17 81.27115  -> 9.999973E+99 Inexact Rounded
powx4145  power  17 81.27116  -> 9.999999E+99 Overflow Inexact Rounded

rounding: ceiling
powx4160  power      0.7  3.3 ->  0.3081936 Inexact Rounded
powx4161  power      0.7  3.4 ->  0.2973948 Inexact Rounded
powx4162  power     -1.2  17  -> -22.18611  Inexact Rounded
powx4163  power     -1.3  17  -> -86.50415  Inexact Rounded
powx4164  power  17 81.27115  -> 9.999974E+99 Inexact Rounded
powx4165  power  17 81.27116  -> Infinity     Overflow Inexact Rounded

rounding: half_up
powx4180  power      0.7  3.3 ->  0.3081935 Inexact Rounded
powx4181  power      0.7  3.4 ->  0.2973948 Inexact Rounded
powx4182  power     -1.2  17  -> -22.18611  Inexact Rounded
powx4183  power     -1.3  17  -> -86.50416  Inexact Rounded
powx4184  power      0.5  11  ->  0.0004882813 Inexact Rounded
powx4185  power      3.15  3  ->  31.25588  Inexact Rounded
powx4186  power  17 81.27115  -> 9.999974E+99 Inexact Rounded
powx4187  power  17 81.27116  -> Infinity     Overflow Inexact Rounded

rounding: half_even
powx4200  power      0.7  3.3 ->  0.3081935 Inexact Rounded
powx4201  power      0.7  3.4 ->  0.2973948 Inexact Rounded
powx4202  power     -1.2  17  -> -22.18611  Inexact Rounded
powx4203  power     -1.3  17  -> -86.50416  Inexact Rounded
powx4204  power      0.5  11  ->  0.0004882812 Inexact Rounded
powx4205  power      3.15  3  ->  31.25588  Inexact Rounded
powx4206  power  17 81.27115  -> 9.999974E+99 Inexact Rounded
powx4207  power  17 81.27116  -> Infinity     Overflow Inexact Rounded

rounding: half_down
powx4220  power      0.7  3.3 ->  0.3081935 Inexact Rounded
powx4221  power      0.7  3.4 ->  0.2973948 Inexact Rounded
powx4222  power     -1.2  17  -> -22.18611  Inexact Rounded
powx4223  power     -1.3  17  -> -86.50416  Inexact Rounded
powx4224  power      0.5  11  ->  0.0004882812 Inexact Rounded
powx4225  power      3.15  3  ->  31.25587  Inexact Rounded
powx4226  power     -3.15  3  -> -31.25587  Inexact Rounded
powx4227  power  17 81.27115  -> 9.999974E+99 Inexact Rounded
powx4228  power  17 81.27116  -> Infinity     Overflow Inexact Rounded


-- more rounding tests as per Ilan Nehama's suggestions & analysis
-- these are likely to show > 0.5 ulp error for very small powers
precision:   7
maxExponent: 96
minExponent: -95

-- For x=nextfp(1)=1.00..001 (where the number of 0s is precision-2)
--   power(x,y)=x when the rounding is up (e.g., toward_pos_inf or
--   ceil) for any y in (0,1].
rounding: ceiling
powx4301  power  1.000001 0          -> 1
-- The next test should be skipped for decNumber
powx4302  power  1.000001 1e-101     -> 1.000001   Inexact Rounded
-- The next test should be skipped for decNumber
powx4303  power  1.000001 1e-95      -> 1.000001   Inexact Rounded
powx4304  power  1.000001 1e-10      -> 1.000001   Inexact Rounded
powx4305  power  1.000001 0.1        -> 1.000001   Inexact Rounded
powx4306  power  1.000001 0.1234567  -> 1.000001   Inexact Rounded
powx4307  power  1.000001 0.7        -> 1.000001   Inexact Rounded
powx4308  power  1.000001 0.9999999  -> 1.000001   Inexact Rounded
powx4309  power  1.000001 1.000000   -> 1.000001
--   power(x,y)=1 when the rounding is down (e.g. toward_zero or
--   floor) for any y in [0,1).
rounding: floor
powx4321  power  1.000001 0          -> 1
powx4322  power  1.000001 1e-101     -> 1.000000   Inexact Rounded
powx4323  power  1.000001 1e-95      -> 1.000000   Inexact Rounded
powx4324  power  1.000001 1e-10      -> 1.000000   Inexact Rounded
powx4325  power  1.000001 0.1        -> 1.000000   Inexact Rounded
powx4326  power  1.000001 0.1234567  -> 1.000000   Inexact Rounded
powx4327  power  1.000001 0.7        -> 1.000000   Inexact Rounded
powx4328  power  1.000001 0.9999999  -> 1.000000   Inexact Rounded
powx4329  power  1.000001 1.000000   -> 1.000001

-- For x=prevfp(1)=0.99..99 (where the number of 9s is precision)
--   power(x,y)=x when the rounding is down for any y in (0,1].
rounding: floor
powx4341  power  0.9999999 0          -> 1
-- The next test should be skipped for decNumber
powx4342  power  0.9999999 1e-101     -> 0.9999999   Inexact Rounded
-- The next test should be skipped for decNumber
powx4343  power  0.9999999 1e-95      -> 0.9999999   Inexact Rounded
powx4344  power  0.9999999 1e-10      -> 0.9999999   Inexact Rounded
powx4345  power  0.9999999 0.1        -> 0.9999999   Inexact Rounded
powx4346  power  0.9999999 0.1234567  -> 0.9999999   Inexact Rounded
powx4347  power  0.9999999 0.7        -> 0.9999999   Inexact Rounded
powx4348  power  0.9999999 0.9999999  -> 0.9999999   Inexact Rounded
powx4349  power  0.9999999 1.000000   -> 0.9999999
--   power(x,y)=1 when the rounding is up for any y in (0,1].
rounding: ceiling
powx4361  power  0.9999999 0          -> 1
powx4362  power  0.9999999 1e-101     -> 1.000000    Inexact Rounded
powx4363  power  0.9999999 1e-95      -> 1.000000    Inexact Rounded
powx4364  power  0.9999999 1e-10      -> 1.000000    Inexact Rounded
powx4365  power  0.9999999 0.1        -> 1.000000    Inexact Rounded
powx4366  power  0.9999999 0.1234567  -> 1.000000    Inexact Rounded
powx4367  power  0.9999999 0.7        -> 1.000000    Inexact Rounded
powx4368  power  0.9999999 0.9999999  -> 1.000000    Inexact Rounded
powx4369  power  0.9999999 1.000000   -> 0.9999999

-- For x=nextfp(0)
--   power(x,y)=0 when the rounding is down for any y larger than 1.
rounding: floor
powx4382  power  1e-101    0          -> 1
powx4383  power  1e-101    0.9999999  -> 1E-101 Underflow Subnormal Inexact Rounded
powx4384  power  1e-101    1.000000   -> 1E-101 Subnormal
powx4385  power  1e-101    1.000001   -> 0E-101 Underflow Subnormal Inexact Rounded Clamped
powx4386  power  1e-101    2.000000   -> 0E-101 Underflow Subnormal Inexact Rounded Clamped
