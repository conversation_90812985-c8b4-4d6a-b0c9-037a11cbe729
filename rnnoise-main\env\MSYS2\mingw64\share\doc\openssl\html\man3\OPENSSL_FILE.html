<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OPENSSL_FILE</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OPENSSL_FILE, OPENSSL_LINE, OPENSSL_FUNC, OPENSSL_MSTR, OPENSSL_MSTR_HELPER - generic C programming utility macros</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/macros.h&gt;

#define OPENSSL_FILE /* typically: __FILE__ */
#define OPENSSL_LINE /* typically: __LINE__ */
#define OPENSSL_FUNC /* typically: __func__ */

#define OPENSSL_MSTR_HELPER(x) #x
#define OPENSSL_MSTR(x) OPENSSL_MSTR_HELPER(x)</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The macros <b>OPENSSL_FILE</b> and <b>OPENSSL_LINE</b> typically yield the current filename and line number during C compilation. When <b>OPENSSL_NO_FILENAMES</b> is defined they yield <b>&quot;&quot;</b> and <b>0</b>, respectively.</p>

<p>The macro <b>OPENSSL_FUNC</b> attempts to yield the name of the C function currently being compiled, as far as language and compiler versions allow. Otherwise, it yields &quot;(unknown function)&quot;.</p>

<p>The macro <b>OPENSSL_MSTR</b> yields the expansion of the macro given as argument, which is useful for concatenation with string constants. The macro <b>OPENSSL_MSTR_HELPER</b> is an auxiliary macro for this purpose.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>see above</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/crypto.html">crypto(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p><b>OPENSSL_FUNC</b>, <b>OPENSSL_MSTR</b>, and <b>OPENSSL_MSTR_HELPER</b> were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2018-2019 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


