------------------------------------------------------------------------
-- decDouble.decTest -- run all decDouble decimal arithmetic tests    --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- decDouble tests
dectest: ddAbs
dectest: ddAdd
dectest: ddAnd
dectest: ddBase
dectest: ddCanonical
dectest: ddClass
dectest: ddCompare
dectest: ddCompareSig
dectest: ddCompareTotal
dectest: ddCompareTotalMag
dectest: ddCopy
dectest: ddCopyAbs
dectest: ddCopyNegate
dectest: ddCopySign
dectest: ddDivide
dectest: ddDivideInt
dectest: ddEncode
dectest: ddFMA
dectest: ddInvert
dectest: ddLogB
dectest: ddMax
dectest: ddMaxMag
dectest: ddMin
dectest: ddMinMag
dectest: ddMinus
dectest: ddMultiply
dectest: ddNextMinus
dectest: ddNextPlus
dectest: ddNextToward
dectest: ddOr
dectest: ddPlus
dectest: ddQuantize
dectest: ddReduce
dectest: ddRemainder
dectest: ddRemainderNear
dectest: ddRotate
dectest: ddSameQuantum
dectest: ddScaleB
dectest: ddShift
dectest: ddSubtract
dectest: ddToIntegral
dectest: ddXor

