.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_attribute_info" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_attribute_info \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_attribute_info(gnutls_x509_crq_t " crq ", unsigned " indx ", void * " oid ", size_t * " sizeof_oid ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "unsigned indx" 12
Specifies which attribute number to get. Use (0) to get the first one.
.IP "void * oid" 12
a pointer to a structure to hold the OID
.IP "size_t * sizeof_oid" 12
initially holds the maximum size of  \fIoid\fP , on return
holds actual size of  \fIoid\fP .
.SH "DESCRIPTION"
This function will return the requested attribute OID in the
certificate, and the critical flag for it.  The attribute OID will
be stored as a string in the provided buffer.  Use
\fBgnutls_x509_crq_get_attribute_data()\fP to extract the data.

If the buffer provided is not long enough to hold the output, then
* \fIsizeof_oid\fP is updated and \fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP will be
returned.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error code in case of an error.  If your have reached the
last extension available \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
will be returned.
.SH "SINCE"
2.8.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
