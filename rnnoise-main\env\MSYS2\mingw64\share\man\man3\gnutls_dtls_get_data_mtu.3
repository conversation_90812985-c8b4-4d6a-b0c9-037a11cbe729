.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_dtls_get_data_mtu" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_dtls_get_data_mtu \- API function
.SH SYNOPSIS
.B #include <gnutls/dtls.h>
.sp
.BI "unsigned int gnutls_dtls_get_data_mtu(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
This function will return the actual maximum transfer unit for
application data. I.e. DTLS headers are subtracted from the
actual MTU which is set using \fBgnutls_dtls_set_mtu()\fP.
.SH "RETURNS"
the maximum allowed transfer unit.
.SH "SINCE"
3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
