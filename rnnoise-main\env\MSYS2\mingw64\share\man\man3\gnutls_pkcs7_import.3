.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs7_import" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs7_import \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs7.h>
.sp
.BI "int gnutls_pkcs7_import(gnutls_pkcs7_t " pkcs7 ", const gnutls_datum_t * " data ", gnutls_x509_crt_fmt_t " format ");"
.SH ARGUMENTS
.IP "gnutls_pkcs7_t pkcs7" 12
The data to store the parsed PKCS7.
.IP "const gnutls_datum_t * data" 12
The DER or PEM encoded PKCS7.
.IP "gnutls_x509_crt_fmt_t format" 12
One of DER or PEM
.SH "DESCRIPTION"
This function will convert the given DER or PEM encoded PKCS7 to
the native \fBgnutls_pkcs7_t\fP format.  The output will be stored in
 \fIpkcs7\fP . Any signed data that may be present inside the  \fIpkcs7\fP structure, like certificates set by \fBgnutls_pkcs7_set_crt()\fP, will
be freed and overwritten by this function.

If the PKCS7 is PEM encoded it should have a header of "PKCS7".
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
