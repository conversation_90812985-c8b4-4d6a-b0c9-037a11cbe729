// Generated by contrib/unicode/gen_libstdcxx_unicode_data.py, do not edit.

// Copyright The GNU Toolchain Authors.
//
// This file is part of the GNU ISO C++ Library.  This library is free
// software; you can redistribute it and/or modify it under the
// terms of the GNU General Public License as published by the
// Free Software Foundation; either version 3, or (at your option)
// any later version.

// This library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// Under Section 7 of GPL version 3, you are granted additional
// permissions described in the GCC Runtime Library Exception, version
// 3.1, as published by the Free Software Foundation.

// You should have received a copy of the GNU General Public License and
// a copy of the GCC Runtime Library Exception along with this program;
// see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
// <http://www.gnu.org/licenses/>.

/** @file bits/unicode-data.h
 *  This is an internal header file, included by other library headers.
 *  Do not attempt to use it directly. @headername{format}
 */

#ifndef _GLIBCXX_GET_UNICODE_DATA
# error "This is not a public header, do not include it directly"
#elif _GLIBCXX_GET_UNICODE_DATA != 160000
# error "Version mismatch for Unicode static data"
#endif

  // Table generated by contrib/unicode/gen_libstdcxx_unicode_data.py,
  // from EastAsianWidth.txt from the Unicode standard.
  inline constexpr char32_t __width_edges[] = {
    0x1100, 0x1160, 0x231a, 0x231c, 0x2329, 0x232b, 0x23e9, 0x23ed,
    0x23f0, 0x23f1, 0x23f3, 0x23f4, 0x25fd, 0x25ff, 0x2614, 0x2616,
    0x2630, 0x2638, 0x2648, 0x2654, 0x267f, 0x2680, 0x268a, 0x2690,
    0x2693, 0x2694, 0x26a1, 0x26a2, 0x26aa, 0x26ac, 0x26bd, 0x26bf,
    0x26c4, 0x26c6, 0x26ce, 0x26cf, 0x26d4, 0x26d5, 0x26ea, 0x26eb,
    0x26f2, 0x26f4, 0x26f5, 0x26f6, 0x26fa, 0x26fb, 0x26fd, 0x26fe,
    0x2705, 0x2706, 0x270a, 0x270c, 0x2728, 0x2729, 0x274c, 0x274d,
    0x274e, 0x274f, 0x2753, 0x2756, 0x2757, 0x2758, 0x2795, 0x2798,
    0x27b0, 0x27b1, 0x27bf, 0x27c0, 0x2b1b, 0x2b1d, 0x2b50, 0x2b51,
    0x2b55, 0x2b56, 0x2e80, 0x2e9a, 0x2e9b, 0x2ef4, 0x2f00, 0x2fd6,
    0x2ff0, 0x303f, 0x3041, 0x3097, 0x3099, 0x3100, 0x3105, 0x3130,
    0x3131, 0x318f, 0x3190, 0x31e6, 0x31ef, 0x321f, 0x3220, 0x3248,
    0x3250, 0xa48d, 0xa490, 0xa4c7, 0xa960, 0xa97d, 0xac00, 0xd7a4,
    0xf900, 0xfb00, 0xfe10, 0xfe1a, 0xfe30, 0xfe53, 0xfe54, 0xfe67,
    0xfe68, 0xfe6c, 0xff01, 0xff61, 0xffe0, 0xffe7, 0x16fe0, 0x16fe5,
    0x16ff0, 0x16ff2, 0x17000, 0x187f8, 0x18800, 0x18cd6, 0x18cff, 0x18d09,
    0x1aff0, 0x1aff4, 0x1aff5, 0x1affc, 0x1affd, 0x1afff, 0x1b000, 0x1b123,
    0x1b132, 0x1b133, 0x1b150, 0x1b153, 0x1b155, 0x1b156, 0x1b164, 0x1b168,
    0x1b170, 0x1b2fc, 0x1d300, 0x1d357, 0x1d360, 0x1d377, 0x1f004, 0x1f005,
    0x1f0cf, 0x1f0d0, 0x1f18e, 0x1f18f, 0x1f191, 0x1f19b, 0x1f200, 0x1f203,
    0x1f210, 0x1f23c, 0x1f240, 0x1f249, 0x1f250, 0x1f252, 0x1f260, 0x1f266,
    0x1f300, 0x1f650, 0x1f680, 0x1f6c6, 0x1f6cc, 0x1f6cd, 0x1f6d0, 0x1f6d3,
    0x1f6d5, 0x1f6d8, 0x1f6dc, 0x1f6e0, 0x1f6eb, 0x1f6ed, 0x1f6f4, 0x1f6fd,
    0x1f7e0, 0x1f7ec, 0x1f7f0, 0x1f7f1, 0x1f900, 0x1fa00, 0x1fa70, 0x1fa7d,
    0x1fa80, 0x1fa8a, 0x1fa8f, 0x1fac7, 0x1face, 0x1fadd, 0x1fadf, 0x1faea,
    0x1faf0, 0x1faf9, 0x20000, 0x2fffe, 0x30000, 0x3fffe,
  };

  // Values generated by contrib/unicode/gen_libstdcxx_unicode_data.py,
  // from DerivedGeneralCategory.txt from the Unicode standard.
  // Entries are (code_point << 1) + escape.
  inline constexpr uint32_t __escape_edges[] = {
    0x1, 0x42, 0xff, 0x142, 0x15b, 0x15c,
    0x6f1, 0x6f4, 0x701, 0x708, 0x717, 0x718,
    0x71b, 0x71c, 0x745, 0x746, 0xa61, 0xa62,
    0xaaf, 0xab2, 0xb17, 0xb1a, 0xb21, 0xb22,
    0xb91, 0xba0, 0xbd7, 0xbde, 0xbeb, 0xc0c,
    0xc39, 0xc3a, 0xdbb, 0xdbc, 0xe1d, 0xe20,
    0xe97, 0xe9a, 0xf65, 0xf80, 0xff7, 0xffa,
    0x105d, 0x1060, 0x107f, 0x1080, 0x10b9, 0x10bc,
    0x10bf, 0x10c0, 0x10d7, 0x10e0, 0x111f, 0x112e,
    0x11c5, 0x11c6, 0x1309, 0x130a, 0x131b, 0x131e,
    0x1323, 0x1326, 0x1353, 0x1354, 0x1363, 0x1364,
    0x1367, 0x136c, 0x1375, 0x1378, 0x138b, 0x138e,
    0x1393, 0x1396, 0x139f, 0x13ae, 0x13b1, 0x13b8,
    0x13bd, 0x13be, 0x13c9, 0x13cc, 0x13ff, 0x1402,
    0x1409, 0x140a, 0x1417, 0x141e, 0x1423, 0x1426,
    0x1453, 0x1454, 0x1463, 0x1464, 0x1469, 0x146a,
    0x146f, 0x1470, 0x1475, 0x1478, 0x147b, 0x147c,
    0x1487, 0x148e, 0x1493, 0x1496, 0x149d, 0x14a2,
    0x14a5, 0x14b2, 0x14bb, 0x14bc, 0x14bf, 0x14cc,
    0x14ef, 0x1502, 0x1509, 0x150a, 0x151d, 0x151e,
    0x1525, 0x1526, 0x1553, 0x1554, 0x1563, 0x1564,
    0x1569, 0x156a, 0x1575, 0x1578, 0x158d, 0x158e,
    0x1595, 0x1596, 0x159d, 0x15a0, 0x15a3, 0x15c0,
    0x15c9, 0x15cc, 0x15e5, 0x15f2, 0x1601, 0x1602,
    0x1609, 0x160a, 0x161b, 0x161e, 0x1623, 0x1626,
    0x1653, 0x1654, 0x1663, 0x1664, 0x1669, 0x166a,
    0x1675, 0x1678, 0x168b, 0x168e, 0x1693, 0x1696,
    0x169d, 0x16aa, 0x16b1, 0x16b8, 0x16bd, 0x16be,
    0x16c9, 0x16cc, 0x16f1, 0x1704, 0x1709, 0x170a,
    0x1717, 0x171c, 0x1723, 0x1724, 0x172d, 0x1732,
    0x1737, 0x1738, 0x173b, 0x173c, 0x1741, 0x1746,
    0x174b, 0x1750, 0x1757, 0x175c, 0x1775, 0x177c,
    0x1787, 0x178c, 0x1793, 0x1794, 0x179d, 0x17a0,
    0x17a3, 0x17ae, 0x17b1, 0x17cc, 0x17f7, 0x1800,
    0x181b, 0x181c, 0x1823, 0x1824, 0x1853, 0x1854,
    0x1875, 0x1878, 0x188b, 0x188c, 0x1893, 0x1894,
    0x189d, 0x18aa, 0x18af, 0x18b0, 0x18b7, 0x18ba,
    0x18bd, 0x18c0, 0x18c9, 0x18cc, 0x18e1, 0x18ee,
    0x191b, 0x191c, 0x1923, 0x1924, 0x1953, 0x1954,
    0x1969, 0x196a, 0x1975, 0x1978, 0x198b, 0x198c,
    0x1993, 0x1994, 0x199d, 0x19aa, 0x19af, 0x19ba,
    0x19bf, 0x19c0, 0x19c9, 0x19cc, 0x19e1, 0x19e2,
    0x19e9, 0x1a00, 0x1a1b, 0x1a1c, 0x1a23, 0x1a24,
    0x1a8b, 0x1a8c, 0x1a93, 0x1a94, 0x1aa1, 0x1aa8,
    0x1ac9, 0x1acc, 0x1b01, 0x1b02, 0x1b09, 0x1b0a,
    0x1b2f, 0x1b34, 0x1b65, 0x1b66, 0x1b79, 0x1b7a,
    0x1b7d, 0x1b80, 0x1b8f, 0x1b94, 0x1b97, 0x1b9e,
    0x1bab, 0x1bac, 0x1baf, 0x1bb0, 0x1bc1, 0x1bcc,
    0x1be1, 0x1be4, 0x1beb, 0x1c02, 0x1c77, 0x1c7e,
    0x1cb9, 0x1d02, 0x1d07, 0x1d08, 0x1d0b, 0x1d0c,
    0x1d17, 0x1d18, 0x1d49, 0x1d4a, 0x1d4d, 0x1d4e,
    0x1d7d, 0x1d80, 0x1d8b, 0x1d8c, 0x1d8f, 0x1d90,
    0x1d9f, 0x1da0, 0x1db5, 0x1db8, 0x1dc1, 0x1e00,
    0x1e91, 0x1e92, 0x1edb, 0x1ee2, 0x1f31, 0x1f32,
    0x1f7b, 0x1f7c, 0x1f9b, 0x1f9c, 0x1fb7, 0x2000,
    0x218d, 0x218e, 0x2191, 0x219a, 0x219d, 0x21a0,
    0x2493, 0x2494, 0x249d, 0x24a0, 0x24af, 0x24b0,
    0x24b3, 0x24b4, 0x24bd, 0x24c0, 0x2513, 0x2514,
    0x251d, 0x2520, 0x2563, 0x2564, 0x256d, 0x2570,
    0x257f, 0x2580, 0x2583, 0x2584, 0x258d, 0x2590,
    0x25af, 0x25b0, 0x2623, 0x2624, 0x262d, 0x2630,
    0x26b7, 0x26ba, 0x26fb, 0x2700, 0x2735, 0x2740,
    0x27ed, 0x27f0, 0x27fd, 0x2800, 0x2d01, 0x2d02,
    0x2d3b, 0x2d40, 0x2df3, 0x2e00, 0x2e2d, 0x2e3e,
    0x2e6f, 0x2e80, 0x2ea9, 0x2ec0, 0x2edb, 0x2edc,
    0x2ee3, 0x2ee4, 0x2ee9, 0x2f00, 0x2fbd, 0x2fc0,
    0x2fd5, 0x2fe0, 0x2ff5, 0x3000, 0x301d, 0x301e,
    0x3035, 0x3040, 0x30f3, 0x3100, 0x3157, 0x3160,
    0x31ed, 0x3200, 0x323f, 0x3240, 0x3259, 0x3260,
    0x3279, 0x3280, 0x3283, 0x3288, 0x32dd, 0x32e0,
    0x32eb, 0x3300, 0x3359, 0x3360, 0x3395, 0x33a0,
    0x33b7, 0x33bc, 0x3439, 0x343c, 0x34bf, 0x34c0,
    0x34fb, 0x34fe, 0x3515, 0x3520, 0x3535, 0x3540,
    0x355d, 0x3560, 0x359f, 0x3600, 0x369b, 0x369c,
    0x37e9, 0x37f8, 0x3871, 0x3876, 0x3895, 0x389a,
    0x3917, 0x3920, 0x3977, 0x397a, 0x3991, 0x39a0,
    0x39f7, 0x3a00, 0x3e2d, 0x3e30, 0x3e3d, 0x3e40,
    0x3e8d, 0x3e90, 0x3e9d, 0x3ea0, 0x3eb1, 0x3eb2,
    0x3eb5, 0x3eb6, 0x3eb9, 0x3eba, 0x3ebd, 0x3ebe,
    0x3efd, 0x3f00, 0x3f6b, 0x3f6c, 0x3f8b, 0x3f8c,
    0x3fa9, 0x3fac, 0x3fb9, 0x3fba, 0x3fe1, 0x3fe4,
    0x3feb, 0x3fec, 0x3fff, 0x4020, 0x4051, 0x4060,
    0x40bf, 0x40e0, 0x40e5, 0x40e8, 0x411f, 0x4120,
    0x413b, 0x4140, 0x4183, 0x41a0, 0x41e3, 0x4200,
    0x4319, 0x4320, 0x4855, 0x4880, 0x4897, 0x48c0,
    0x56e9, 0x56ec, 0x572d, 0x572e, 0x59e9, 0x59f2,
    0x5a4d, 0x5a4e, 0x5a51, 0x5a5a, 0x5a5d, 0x5a60,
    0x5ad1, 0x5ade, 0x5ae3, 0x5afe, 0x5b2f, 0x5b40,
    0x5b4f, 0x5b50, 0x5b5f, 0x5b60, 0x5b6f, 0x5b70,
    0x5b7f, 0x5b80, 0x5b8f, 0x5b90, 0x5b9f, 0x5ba0,
    0x5baf, 0x5bb0, 0x5bbf, 0x5bc0, 0x5cbd, 0x5d00,
    0x5d35, 0x5d36, 0x5de9, 0x5e00, 0x5fad, 0x5fe0,
    0x6001, 0x6002, 0x6081, 0x6082, 0x612f, 0x6132,
    0x6201, 0x620a, 0x6261, 0x6262, 0x631f, 0x6320,
    0x63cd, 0x63de, 0x643f, 0x6440, 0x1491b, 0x14920,
    0x1498f, 0x149a0, 0x14c59, 0x14c80, 0x14df1, 0x14e00,
    0x14f9d, 0x14fa0, 0x14fa5, 0x14fa6, 0x14fa9, 0x14faa,
    0x14fbb, 0x14fe4, 0x1505b, 0x15060, 0x15075, 0x15080,
    0x150f1, 0x15100, 0x1518d, 0x1519c, 0x151b5, 0x151c0,
    0x152a9, 0x152be, 0x152fb, 0x15300, 0x1539d, 0x1539e,
    0x153b5, 0x153bc, 0x153ff, 0x15400, 0x1546f, 0x15480,
    0x1549d, 0x154a0, 0x154b5, 0x154b8, 0x15587, 0x155b6,
    0x155ef, 0x15602, 0x1560f, 0x15612, 0x1561f, 0x15622,
    0x1562f, 0x15640, 0x1564f, 0x15650, 0x1565f, 0x15660,
    0x156d9, 0x156e0, 0x157dd, 0x157e0, 0x157f5, 0x15800,
    0x1af49, 0x1af60, 0x1af8f, 0x1af96, 0x1aff9, 0x1f200,
    0x1f4dd, 0x1f4e0, 0x1f5b5, 0x1f600, 0x1f60f, 0x1f626,
    0x1f631, 0x1f63a, 0x1f66f, 0x1f670, 0x1f67b, 0x1f67c,
    0x1f67f, 0x1f680, 0x1f685, 0x1f686, 0x1f68b, 0x1f68c,
    0x1f787, 0x1f7a6, 0x1fb21, 0x1fb24, 0x1fb91, 0x1fb9e,
    0x1fba1, 0x1fbe0, 0x1fc35, 0x1fc40, 0x1fca7, 0x1fca8,
    0x1fccf, 0x1fcd0, 0x1fcd9, 0x1fce0, 0x1fceb, 0x1fcec,
    0x1fdfb, 0x1fe02, 0x1ff7f, 0x1ff84, 0x1ff91, 0x1ff94,
    0x1ffa1, 0x1ffa4, 0x1ffb1, 0x1ffb4, 0x1ffbb, 0x1ffc0,
    0x1ffcf, 0x1ffd0, 0x1ffdf, 0x1fff8, 0x1fffd, 0x20000,
    0x20019, 0x2001a, 0x2004f, 0x20050, 0x20077, 0x20078,
    0x2007d, 0x2007e, 0x2009d, 0x200a0, 0x200bd, 0x20100,
    0x201f7, 0x20200, 0x20207, 0x2020e, 0x20269, 0x2026e,
    0x2031f, 0x20320, 0x2033b, 0x20340, 0x20343, 0x203a0,
    0x203fd, 0x20500, 0x2053b, 0x20540, 0x205a3, 0x205c0,
    0x205f9, 0x20600, 0x20649, 0x2065a, 0x20697, 0x206a0,
    0x206f7, 0x20700, 0x2073d, 0x2073e, 0x20789, 0x20790,
    0x207ad, 0x20800, 0x2093d, 0x20940, 0x20955, 0x20960,
    0x209a9, 0x209b0, 0x209f9, 0x20a00, 0x20a51, 0x20a60,
    0x20ac9, 0x20ade, 0x20af7, 0x20af8, 0x20b17, 0x20b18,
    0x20b27, 0x20b28, 0x20b2d, 0x20b2e, 0x20b45, 0x20b46,
    0x20b65, 0x20b66, 0x20b75, 0x20b76, 0x20b7b, 0x20b80,
    0x20be9, 0x20c00, 0x20e6f, 0x20e80, 0x20ead, 0x20ec0,
    0x20ed1, 0x20f00, 0x20f0d, 0x20f0e, 0x20f63, 0x20f64,
    0x20f77, 0x21000, 0x2100d, 0x21010, 0x21013, 0x21014,
    0x2106d, 0x2106e, 0x21073, 0x21078, 0x2107b, 0x2107e,
    0x210ad, 0x210ae, 0x2113f, 0x2114e, 0x21161, 0x211c0,
    0x211e7, 0x211e8, 0x211ed, 0x211f6, 0x21239, 0x2123e,
    0x21275, 0x2127e, 0x21281, 0x21300, 0x21371, 0x21378,
    0x213a1, 0x213a4, 0x21409, 0x2140a, 0x2140f, 0x21418,
    0x21429, 0x2142a, 0x21431, 0x21432, 0x2146d, 0x21470,
    0x21477, 0x2147e, 0x21493, 0x214a0, 0x214b3, 0x214c0,
    0x21541, 0x21580, 0x215cf, 0x215d6, 0x215ef, 0x21600,
    0x2166d, 0x21672, 0x216ad, 0x216b0, 0x216e7, 0x216f0,
    0x21725, 0x21732, 0x2173b, 0x21752, 0x21761, 0x21800,
    0x21893, 0x21900, 0x21967, 0x21980, 0x219e7, 0x219f4,
    0x21a51, 0x21a60, 0x21a75, 0x21a80, 0x21acd, 0x21ad2,
    0x21b0d, 0x21b1c, 0x21b21, 0x21cc0, 0x21cff, 0x21d00,
    0x21d55, 0x21d56, 0x21d5d, 0x21d60, 0x21d65, 0x21d84,
    0x21d8b, 0x21df8, 0x21e51, 0x21e60, 0x21eb5, 0x21ee0,
    0x21f15, 0x21f60, 0x21f99, 0x21fc0, 0x21fef, 0x22000,
    0x2209d, 0x220a4, 0x220ed, 0x220fe, 0x2217b, 0x2217c,
    0x22187, 0x221a0, 0x221d3, 0x221e0, 0x221f5, 0x22200,
    0x2226b, 0x2226c, 0x22291, 0x222a0, 0x222ef, 0x22300,
    0x223c1, 0x223c2, 0x223eb, 0x22400, 0x22425, 0x22426,
    0x22485, 0x22500, 0x2250f, 0x22510, 0x22513, 0x22514,
    0x2251d, 0x2251e, 0x2253d, 0x2253e, 0x22555, 0x22560,
    0x225d7, 0x225e0, 0x225f5, 0x22600, 0x22609, 0x2260a,
    0x2261b, 0x2261e, 0x22623, 0x22626, 0x22653, 0x22654,
    0x22663, 0x22664, 0x22669, 0x2266a, 0x22675, 0x22676,
    0x2268b, 0x2268e, 0x22693, 0x22696, 0x2269d, 0x226a0,
    0x226a3, 0x226ae, 0x226b1, 0x226ba, 0x226c9, 0x226cc,
    0x226db, 0x226e0, 0x226eb, 0x22700, 0x22715, 0x22716,
    0x22719, 0x2271c, 0x2271f, 0x22720, 0x2276d, 0x2276e,
    0x22783, 0x22784, 0x22787, 0x2278a, 0x2278d, 0x2278e,
    0x22797, 0x22798, 0x227ad, 0x227ae, 0x227b3, 0x227c2,
    0x227c7, 0x22800, 0x228b9, 0x228ba, 0x228c5, 0x22900,
    0x22991, 0x229a0, 0x229b5, 0x22b00, 0x22b6d, 0x22b70,
    0x22bbd, 0x22c00, 0x22c8b, 0x22ca0, 0x22cb5, 0x22cc0,
    0x22cdb, 0x22d00, 0x22d75, 0x22d80, 0x22d95, 0x22da0,
    0x22dc9, 0x22e00, 0x22e37, 0x22e3a, 0x22e59, 0x22e60,
    0x22e8f, 0x23000, 0x23079, 0x23140, 0x231e7, 0x231fe,
    0x2320f, 0x23212, 0x23215, 0x23218, 0x23229, 0x2322a,
    0x2322f, 0x23230, 0x2326d, 0x2326e, 0x23273, 0x23276,
    0x2328f, 0x232a0, 0x232b5, 0x23340, 0x23351, 0x23354,
    0x233b1, 0x233b4, 0x233cb, 0x23400, 0x23491, 0x234a0,
    0x23547, 0x23560, 0x235f3, 0x23600, 0x23615, 0x23780,
    0x237c5, 0x237e0, 0x237f5, 0x23800, 0x23813, 0x23814,
    0x2386f, 0x23870, 0x2388d, 0x238a0, 0x238db, 0x238e0,
    0x23921, 0x23924, 0x23951, 0x23952, 0x2396f, 0x23a00,
    0x23a0f, 0x23a10, 0x23a15, 0x23a16, 0x23a6f, 0x23a74,
    0x23a77, 0x23a78, 0x23a7d, 0x23a7e, 0x23a91, 0x23aa0,
    0x23ab5, 0x23ac0, 0x23acd, 0x23ace, 0x23ad3, 0x23ad4,
    0x23b1f, 0x23b20, 0x23b25, 0x23b26, 0x23b33, 0x23b40,
    0x23b55, 0x23dc0, 0x23df3, 0x23e00, 0x23e23, 0x23e24,
    0x23e77, 0x23e7c, 0x23eb7, 0x23f60, 0x23f63, 0x23f80,
    0x23fe5, 0x23ffe, 0x24735, 0x24800, 0x248df, 0x248e0,
    0x248eb, 0x24900, 0x24a89, 0x25f20, 0x25fe7, 0x26000,
    0x26861, 0x26880, 0x268ad, 0x268c0, 0x287f7, 0x28800,
    0x28c8f, 0x2c200, 0x2c275, 0x2d000, 0x2d473, 0x2d480,
    0x2d4bf, 0x2d4c0, 0x2d4d5, 0x2d4dc, 0x2d57f, 0x2d580,
    0x2d595, 0x2d5a0, 0x2d5dd, 0x2d5e0, 0x2d5ed, 0x2d600,
    0x2d68d, 0x2d6a0, 0x2d6b5, 0x2d6b6, 0x2d6c5, 0x2d6c6,
    0x2d6f1, 0x2d6fa, 0x2d721, 0x2da80, 0x2daf5, 0x2dc80,
    0x2dd37, 0x2de00, 0x2de97, 0x2de9e, 0x2df11, 0x2df1e,
    0x2df41, 0x2dfc0, 0x2dfcb, 0x2dfe0, 0x2dfe5, 0x2e000,
    0x30ff1, 0x31000, 0x319ad, 0x319fe, 0x31a13, 0x35fe0,
    0x35fe9, 0x35fea, 0x35ff9, 0x35ffa, 0x35fff, 0x36000,
    0x36247, 0x36264, 0x36267, 0x362a0, 0x362a7, 0x362aa,
    0x362ad, 0x362c8, 0x362d1, 0x362e0, 0x365f9, 0x37800,
    0x378d7, 0x378e0, 0x378fb, 0x37900, 0x37913, 0x37920,
    0x37935, 0x37938, 0x37941, 0x39800, 0x399f5, 0x39a00,
    0x39d69, 0x39e00, 0x39e5d, 0x39e60, 0x39e8f, 0x39ea0,
    0x39f89, 0x3a000, 0x3a1ed, 0x3a200, 0x3a24f, 0x3a252,
    0x3a2e7, 0x3a2f6, 0x3a3d7, 0x3a400, 0x3a48d, 0x3a580,
    0x3a5a9, 0x3a5c0, 0x3a5e9, 0x3a600, 0x3a6af, 0x3a6c0,
    0x3a6f3, 0x3a800, 0x3a8ab, 0x3a8ac, 0x3a93b, 0x3a93c,
    0x3a941, 0x3a944, 0x3a947, 0x3a94a, 0x3a94f, 0x3a952,
    0x3a95b, 0x3a95c, 0x3a975, 0x3a976, 0x3a979, 0x3a97a,
    0x3a989, 0x3a98a, 0x3aa0d, 0x3aa0e, 0x3aa17, 0x3aa1a,
    0x3aa2b, 0x3aa2c, 0x3aa3b, 0x3aa3c, 0x3aa75, 0x3aa76,
    0x3aa7f, 0x3aa80, 0x3aa8b, 0x3aa8c, 0x3aa8f, 0x3aa94,
    0x3aaa3, 0x3aaa4, 0x3ad4d, 0x3ad50, 0x3af99, 0x3af9c,
    0x3b519, 0x3b536, 0x3b541, 0x3b542, 0x3b561, 0x3be00,
    0x3be3f, 0x3be4a, 0x3be57, 0x3c000, 0x3c00f, 0x3c010,
    0x3c033, 0x3c036, 0x3c045, 0x3c046, 0x3c04b, 0x3c04c,
    0x3c057, 0x3c060, 0x3c0dd, 0x3c11e, 0x3c121, 0x3c200,
    0x3c25b, 0x3c260, 0x3c27d, 0x3c280, 0x3c295, 0x3c29c,
    0x3c2a1, 0x3c520, 0x3c55f, 0x3c580, 0x3c5f5, 0x3c5fe,
    0x3c601, 0x3c9a0, 0x3c9f5, 0x3cba0, 0x3cbf7, 0x3cbfe,
    0x3cc01, 0x3cfc0, 0x3cfcf, 0x3cfd0, 0x3cfd9, 0x3cfda,
    0x3cfdf, 0x3cfe0, 0x3cfff, 0x3d000, 0x3d18b, 0x3d18e,
    0x3d1af, 0x3d200, 0x3d299, 0x3d2a0, 0x3d2b5, 0x3d2bc,
    0x3d2c1, 0x3d8e2, 0x3d96b, 0x3da02, 0x3da7d, 0x3dc00,
    0x3dc09, 0x3dc0a, 0x3dc41, 0x3dc42, 0x3dc47, 0x3dc48,
    0x3dc4b, 0x3dc4e, 0x3dc51, 0x3dc52, 0x3dc67, 0x3dc68,
    0x3dc71, 0x3dc72, 0x3dc75, 0x3dc76, 0x3dc79, 0x3dc84,
    0x3dc87, 0x3dc8e, 0x3dc91, 0x3dc92, 0x3dc95, 0x3dc96,
    0x3dc99, 0x3dc9a, 0x3dca1, 0x3dca2, 0x3dca7, 0x3dca8,
    0x3dcab, 0x3dcae, 0x3dcb1, 0x3dcb2, 0x3dcb5, 0x3dcb6,
    0x3dcb9, 0x3dcba, 0x3dcbd, 0x3dcbe, 0x3dcc1, 0x3dcc2,
    0x3dcc7, 0x3dcc8, 0x3dccb, 0x3dcce, 0x3dcd7, 0x3dcd8,
    0x3dce7, 0x3dce8, 0x3dcf1, 0x3dcf2, 0x3dcfb, 0x3dcfc,
    0x3dcff, 0x3dd00, 0x3dd15, 0x3dd16, 0x3dd39, 0x3dd42,
    0x3dd49, 0x3dd4a, 0x3dd55, 0x3dd56, 0x3dd79, 0x3dde0,
    0x3dde5, 0x3e000, 0x3e059, 0x3e060, 0x3e129, 0x3e140,
    0x3e15f, 0x3e162, 0x3e181, 0x3e182, 0x3e1a1, 0x3e1a2,
    0x3e1ed, 0x3e200, 0x3e35d, 0x3e3cc, 0x3e407, 0x3e420,
    0x3e479, 0x3e480, 0x3e493, 0x3e4a0, 0x3e4a5, 0x3e4c0,
    0x3e4cd, 0x3e600, 0x3edb1, 0x3edb8, 0x3eddb, 0x3ede0,
    0x3edfb, 0x3ee00, 0x3eeef, 0x3eef6, 0x3efb5, 0x3efc0,
    0x3efd9, 0x3efe0, 0x3efe3, 0x3f000, 0x3f019, 0x3f020,
    0x3f091, 0x3f0a0, 0x3f0b5, 0x3f0c0, 0x3f111, 0x3f120,
    0x3f15d, 0x3f160, 0x3f179, 0x3f180, 0x3f185, 0x3f200,
    0x3f4a9, 0x3f4c0, 0x3f4dd, 0x3f4e0, 0x3f4fb, 0x3f500,
    0x3f515, 0x3f51e, 0x3f58f, 0x3f59c, 0x3f5bb, 0x3f5be,
    0x3f5d5, 0x3f5e0, 0x3f5f3, 0x3f600, 0x3f727, 0x3f728,
    0x3f7f5, 0x40000, 0x54dc1, 0x54e00, 0x56e75, 0x56e80,
    0x5703d, 0x57040, 0x59d45, 0x59d60, 0x5d7c3, 0x5d7e0,
    0x5dcbd, 0x5f000, 0x5f43d, 0x60000, 0x62697, 0x626a0,
    0x64761, 0x1c0200, 0x1c03e1,
  };

  enum class _Gcb_property {
    _Gcb_Other = 0,
    _Gcb_Control = 1,
    _Gcb_LF = 2,
    _Gcb_CR = 3,
    _Gcb_Extend = 4,
    _Gcb_Prepend = 5,
    _Gcb_SpacingMark = 6,
    _Gcb_L = 7,
    _Gcb_V = 8,
    _Gcb_T = 9,
    _Gcb_ZWJ = 10,
    _Gcb_LV = 11,
    _Gcb_LVT = 12,
    _Gcb_Regional_Indicator = 13,
  };

  // Values generated by contrib/unicode/gen_libstdcxx_unicode_data.py,
  // from GraphemeBreakProperty.txt from the Unicode standard.
  // Entries are (code_point << shift_bits) + property.
  inline constexpr int __gcb_shift_bits = 0x4;
  inline constexpr uint32_t __gcb_edges[] = {
    0x1, 0xa2, 0xb1, 0xd3, 0xe1, 0x200,
    0x7f1, 0xa00, 0xad1, 0xae0, 0x3004, 0x3700,
    0x4834, 0x48a0, 0x5914, 0x5be0, 0x5bf4, 0x5c00,
    0x5c14, 0x5c30, 0x5c44, 0x5c60, 0x5c74, 0x5c80,
    0x6005, 0x6060, 0x6104, 0x61b0, 0x61c1, 0x61d0,
    0x64b4, 0x6600, 0x6704, 0x6710, 0x6d64, 0x6dd5,
    0x6de0, 0x6df4, 0x6e50, 0x6e74, 0x6e90, 0x6ea4,
    0x6ee0, 0x70f5, 0x7100, 0x7114, 0x7120, 0x7304,
    0x74b0, 0x7a64, 0x7b10, 0x7eb4, 0x7f40, 0x7fd4,
    0x7fe0, 0x8164, 0x81a0, 0x81b4, 0x8240, 0x8254,
    0x8280, 0x8294, 0x82e0, 0x8594, 0x85c0, 0x8905,
    0x8920, 0x8974, 0x8a00, 0x8ca4, 0x8e25, 0x8e34,
    0x9036, 0x9040, 0x93a4, 0x93b6, 0x93c4, 0x93d0,
    0x93e6, 0x9414, 0x9496, 0x94d4, 0x94e6, 0x9500,
    0x9514, 0x9580, 0x9624, 0x9640, 0x9814, 0x9826,
    0x9840, 0x9bc4, 0x9bd0, 0x9be4, 0x9bf6, 0x9c14,
    0x9c50, 0x9c76, 0x9c90, 0x9cb6, 0x9cd4, 0x9ce0,
    0x9d74, 0x9d80, 0x9e24, 0x9e40, 0x9fe4, 0x9ff0,
    0xa014, 0xa036, 0xa040, 0xa3c4, 0xa3d0, 0xa3e6,
    0xa414, 0xa430, 0xa474, 0xa490, 0xa4b4, 0xa4e0,
    0xa514, 0xa520, 0xa704, 0xa720, 0xa754, 0xa760,
    0xa814, 0xa836, 0xa840, 0xabc4, 0xabd0, 0xabe6,
    0xac14, 0xac60, 0xac74, 0xac96, 0xaca0, 0xacb6,
    0xacd4, 0xace0, 0xae24, 0xae40, 0xafa4, 0xb000,
    0xb014, 0xb026, 0xb040, 0xb3c4, 0xb3d0, 0xb3e4,
    0xb406, 0xb414, 0xb450, 0xb476, 0xb490, 0xb4b6,
    0xb4d4, 0xb4e0, 0xb554, 0xb580, 0xb624, 0xb640,
    0xb824, 0xb830, 0xbbe4, 0xbbf6, 0xbc04, 0xbc16,
    0xbc30, 0xbc66, 0xbc90, 0xbca6, 0xbcd4, 0xbce0,
    0xbd74, 0xbd80, 0xc004, 0xc016, 0xc044, 0xc050,
    0xc3c4, 0xc3d0, 0xc3e4, 0xc416, 0xc450, 0xc464,
    0xc490, 0xc4a4, 0xc4e0, 0xc554, 0xc570, 0xc624,
    0xc640, 0xc814, 0xc826, 0xc840, 0xcbc4, 0xcbd0,
    0xcbe6, 0xcbf4, 0xcc16, 0xcc24, 0xcc36, 0xcc50,
    0xcc64, 0xcc90, 0xcca4, 0xcce0, 0xcd54, 0xcd70,
    0xce24, 0xce40, 0xcf36, 0xcf40, 0xd004, 0xd026,
    0xd040, 0xd3b4, 0xd3d0, 0xd3e4, 0xd3f6, 0xd414,
    0xd450, 0xd466, 0xd490, 0xd4a6, 0xd4d4, 0xd4e5,
    0xd4f0, 0xd574, 0xd580, 0xd624, 0xd640, 0xd814,
    0xd826, 0xd840, 0xdca4, 0xdcb0, 0xdcf4, 0xdd06,
    0xdd24, 0xdd50, 0xdd64, 0xdd70, 0xdd86, 0xddf4,
    0xde00, 0xdf26, 0xdf40, 0xe314, 0xe320, 0xe336,
    0xe344, 0xe3b0, 0xe474, 0xe4f0, 0xeb14, 0xeb20,
    0xeb36, 0xeb44, 0xebd0, 0xec84, 0xecf0, 0xf184,
    0xf1a0, 0xf354, 0xf360, 0xf374, 0xf380, 0xf394,
    0xf3a0, 0xf3e6, 0xf400, 0xf714, 0xf7f6, 0xf804,
    0xf850, 0xf864, 0xf880, 0xf8d4, 0xf980, 0xf994,
    0xfbd0, 0xfc64, 0xfc70, 0x102d4, 0x10316, 0x10324,
    0x10380, 0x10394, 0x103b6, 0x103d4, 0x103f0, 0x10566,
    0x10584, 0x105a0, 0x105e4, 0x10610, 0x10714, 0x10750,
    0x10824, 0x10830, 0x10846, 0x10854, 0x10870, 0x108d4,
    0x108e0, 0x109d4, 0x109e0, 0x11007, 0x11608, 0x11a89,
    0x12000, 0x135d4, 0x13600, 0x17124, 0x17160, 0x17324,
    0x17350, 0x17524, 0x17540, 0x17724, 0x17740, 0x17b44,
    0x17b66, 0x17b74, 0x17be6, 0x17c64, 0x17c76, 0x17c94,
    0x17d40, 0x17dd4, 0x17de0, 0x180b4, 0x180e1, 0x180f4,
    0x18100, 0x18854, 0x18870, 0x18a94, 0x18aa0, 0x19204,
    0x19236, 0x19274, 0x19296, 0x192c0, 0x19306, 0x19324,
    0x19336, 0x19394, 0x193c0, 0x1a174, 0x1a196, 0x1a1b4,
    0x1a1c0, 0x1a556, 0x1a564, 0x1a576, 0x1a584, 0x1a5f0,
    0x1a604, 0x1a610, 0x1a624, 0x1a630, 0x1a654, 0x1a6d6,
    0x1a734, 0x1a7d0, 0x1a7f4, 0x1a800, 0x1ab04, 0x1acf0,
    0x1b004, 0x1b046, 0x1b050, 0x1b344, 0x1b3e6, 0x1b424,
    0x1b450, 0x1b6b4, 0x1b740, 0x1b804, 0x1b826, 0x1b830,
    0x1ba16, 0x1ba24, 0x1ba66, 0x1ba84, 0x1bae0, 0x1be64,
    0x1be76, 0x1be84, 0x1bea6, 0x1bed4, 0x1bee6, 0x1bef4,
    0x1bf40, 0x1c246, 0x1c2c4, 0x1c346, 0x1c364, 0x1c380,
    0x1cd04, 0x1cd30, 0x1cd44, 0x1ce16, 0x1ce24, 0x1ce90,
    0x1ced4, 0x1cee0, 0x1cf44, 0x1cf50, 0x1cf76, 0x1cf84,
    0x1cfa0, 0x1dc04, 0x1e000, 0x200b1, 0x200c4, 0x200da,
    0x200e1, 0x20100, 0x20281, 0x202f0, 0x20601, 0x20700,
    0x20d04, 0x20f10, 0x2cef4, 0x2cf20, 0x2d7f4, 0x2d800,
    0x2de04, 0x2e000, 0x302a4, 0x30300, 0x30994, 0x309b0,
    0xa66f4, 0xa6730, 0xa6744, 0xa67e0, 0xa69e4, 0xa6a00,
    0xa6f04, 0xa6f20, 0xa8024, 0xa8030, 0xa8064, 0xa8070,
    0xa80b4, 0xa80c0, 0xa8236, 0xa8254, 0xa8276, 0xa8280,
    0xa82c4, 0xa82d0, 0xa8806, 0xa8820, 0xa8b46, 0xa8c44,
    0xa8c60, 0xa8e04, 0xa8f20, 0xa8ff4, 0xa9000, 0xa9264,
    0xa92e0, 0xa9474, 0xa9526, 0xa9534, 0xa9540, 0xa9607,
    0xa97d0, 0xa9804, 0xa9836, 0xa9840, 0xa9b34, 0xa9b46,
    0xa9b64, 0xa9ba6, 0xa9bc4, 0xa9be6, 0xa9c04, 0xa9c10,
    0xa9e54, 0xa9e60, 0xaa294, 0xaa2f6, 0xaa314, 0xaa336,
    0xaa354, 0xaa370, 0xaa434, 0xaa440, 0xaa4c4, 0xaa4d6,
    0xaa4e0, 0xaa7c4, 0xaa7d0, 0xaab04, 0xaab10, 0xaab24,
    0xaab50, 0xaab74, 0xaab90, 0xaabe4, 0xaac00, 0xaac14,
    0xaac20, 0xaaeb6, 0xaaec4, 0xaaee6, 0xaaf00, 0xaaf56,
    0xaaf64, 0xaaf70, 0xabe36, 0xabe54, 0xabe66, 0xabe84,
    0xabe96, 0xabeb0, 0xabec6, 0xabed4, 0xabee0, 0xac00b,
    0xac01c, 0xac1cb, 0xac1dc, 0xac38b, 0xac39c, 0xac54b,
    0xac55c, 0xac70b, 0xac71c, 0xac8cb, 0xac8dc, 0xaca8b,
    0xaca9c, 0xacc4b, 0xacc5c, 0xace0b, 0xace1c, 0xacfcb,
    0xacfdc, 0xad18b, 0xad19c, 0xad34b, 0xad35c, 0xad50b,
    0xad51c, 0xad6cb, 0xad6dc, 0xad88b, 0xad89c, 0xada4b,
    0xada5c, 0xadc0b, 0xadc1c, 0xaddcb, 0xadddc, 0xadf8b,
    0xadf9c, 0xae14b, 0xae15c, 0xae30b, 0xae31c, 0xae4cb,
    0xae4dc, 0xae68b, 0xae69c, 0xae84b, 0xae85c, 0xaea0b,
    0xaea1c, 0xaebcb, 0xaebdc, 0xaed8b, 0xaed9c, 0xaef4b,
    0xaef5c, 0xaf10b, 0xaf11c, 0xaf2cb, 0xaf2dc, 0xaf48b,
    0xaf49c, 0xaf64b, 0xaf65c, 0xaf80b, 0xaf81c, 0xaf9cb,
    0xaf9dc, 0xafb8b, 0xafb9c, 0xafd4b, 0xafd5c, 0xaff0b,
    0xaff1c, 0xb00cb, 0xb00dc, 0xb028b, 0xb029c, 0xb044b,
    0xb045c, 0xb060b, 0xb061c, 0xb07cb, 0xb07dc, 0xb098b,
    0xb099c, 0xb0b4b, 0xb0b5c, 0xb0d0b, 0xb0d1c, 0xb0ecb,
    0xb0edc, 0xb108b, 0xb109c, 0xb124b, 0xb125c, 0xb140b,
    0xb141c, 0xb15cb, 0xb15dc, 0xb178b, 0xb179c, 0xb194b,
    0xb195c, 0xb1b0b, 0xb1b1c, 0xb1ccb, 0xb1cdc, 0xb1e8b,
    0xb1e9c, 0xb204b, 0xb205c, 0xb220b, 0xb221c, 0xb23cb,
    0xb23dc, 0xb258b, 0xb259c, 0xb274b, 0xb275c, 0xb290b,
    0xb291c, 0xb2acb, 0xb2adc, 0xb2c8b, 0xb2c9c, 0xb2e4b,
    0xb2e5c, 0xb300b, 0xb301c, 0xb31cb, 0xb31dc, 0xb338b,
    0xb339c, 0xb354b, 0xb355c, 0xb370b, 0xb371c, 0xb38cb,
    0xb38dc, 0xb3a8b, 0xb3a9c, 0xb3c4b, 0xb3c5c, 0xb3e0b,
    0xb3e1c, 0xb3fcb, 0xb3fdc, 0xb418b, 0xb419c, 0xb434b,
    0xb435c, 0xb450b, 0xb451c, 0xb46cb, 0xb46dc, 0xb488b,
    0xb489c, 0xb4a4b, 0xb4a5c, 0xb4c0b, 0xb4c1c, 0xb4dcb,
    0xb4ddc, 0xb4f8b, 0xb4f9c, 0xb514b, 0xb515c, 0xb530b,
    0xb531c, 0xb54cb, 0xb54dc, 0xb568b, 0xb569c, 0xb584b,
    0xb585c, 0xb5a0b, 0xb5a1c, 0xb5bcb, 0xb5bdc, 0xb5d8b,
    0xb5d9c, 0xb5f4b, 0xb5f5c, 0xb610b, 0xb611c, 0xb62cb,
    0xb62dc, 0xb648b, 0xb649c, 0xb664b, 0xb665c, 0xb680b,
    0xb681c, 0xb69cb, 0xb69dc, 0xb6b8b, 0xb6b9c, 0xb6d4b,
    0xb6d5c, 0xb6f0b, 0xb6f1c, 0xb70cb, 0xb70dc, 0xb728b,
    0xb729c, 0xb744b, 0xb745c, 0xb760b, 0xb761c, 0xb77cb,
    0xb77dc, 0xb798b, 0xb799c, 0xb7b4b, 0xb7b5c, 0xb7d0b,
    0xb7d1c, 0xb7ecb, 0xb7edc, 0xb808b, 0xb809c, 0xb824b,
    0xb825c, 0xb840b, 0xb841c, 0xb85cb, 0xb85dc, 0xb878b,
    0xb879c, 0xb894b, 0xb895c, 0xb8b0b, 0xb8b1c, 0xb8ccb,
    0xb8cdc, 0xb8e8b, 0xb8e9c, 0xb904b, 0xb905c, 0xb920b,
    0xb921c, 0xb93cb, 0xb93dc, 0xb958b, 0xb959c, 0xb974b,
    0xb975c, 0xb990b, 0xb991c, 0xb9acb, 0xb9adc, 0xb9c8b,
    0xb9c9c, 0xb9e4b, 0xb9e5c, 0xba00b, 0xba01c, 0xba1cb,
    0xba1dc, 0xba38b, 0xba39c, 0xba54b, 0xba55c, 0xba70b,
    0xba71c, 0xba8cb, 0xba8dc, 0xbaa8b, 0xbaa9c, 0xbac4b,
    0xbac5c, 0xbae0b, 0xbae1c, 0xbafcb, 0xbafdc, 0xbb18b,
    0xbb19c, 0xbb34b, 0xbb35c, 0xbb50b, 0xbb51c, 0xbb6cb,
    0xbb6dc, 0xbb88b, 0xbb89c, 0xbba4b, 0xbba5c, 0xbbc0b,
    0xbbc1c, 0xbbdcb, 0xbbddc, 0xbbf8b, 0xbbf9c, 0xbc14b,
    0xbc15c, 0xbc30b, 0xbc31c, 0xbc4cb, 0xbc4dc, 0xbc68b,
    0xbc69c, 0xbc84b, 0xbc85c, 0xbca0b, 0xbca1c, 0xbcbcb,
    0xbcbdc, 0xbcd8b, 0xbcd9c, 0xbcf4b, 0xbcf5c, 0xbd10b,
    0xbd11c, 0xbd2cb, 0xbd2dc, 0xbd48b, 0xbd49c, 0xbd64b,
    0xbd65c, 0xbd80b, 0xbd81c, 0xbd9cb, 0xbd9dc, 0xbdb8b,
    0xbdb9c, 0xbdd4b, 0xbdd5c, 0xbdf0b, 0xbdf1c, 0xbe0cb,
    0xbe0dc, 0xbe28b, 0xbe29c, 0xbe44b, 0xbe45c, 0xbe60b,
    0xbe61c, 0xbe7cb, 0xbe7dc, 0xbe98b, 0xbe99c, 0xbeb4b,
    0xbeb5c, 0xbed0b, 0xbed1c, 0xbeecb, 0xbeedc, 0xbf08b,
    0xbf09c, 0xbf24b, 0xbf25c, 0xbf40b, 0xbf41c, 0xbf5cb,
    0xbf5dc, 0xbf78b, 0xbf79c, 0xbf94b, 0xbf95c, 0xbfb0b,
    0xbfb1c, 0xbfccb, 0xbfcdc, 0xbfe8b, 0xbfe9c, 0xc004b,
    0xc005c, 0xc020b, 0xc021c, 0xc03cb, 0xc03dc, 0xc058b,
    0xc059c, 0xc074b, 0xc075c, 0xc090b, 0xc091c, 0xc0acb,
    0xc0adc, 0xc0c8b, 0xc0c9c, 0xc0e4b, 0xc0e5c, 0xc100b,
    0xc101c, 0xc11cb, 0xc11dc, 0xc138b, 0xc139c, 0xc154b,
    0xc155c, 0xc170b, 0xc171c, 0xc18cb, 0xc18dc, 0xc1a8b,
    0xc1a9c, 0xc1c4b, 0xc1c5c, 0xc1e0b, 0xc1e1c, 0xc1fcb,
    0xc1fdc, 0xc218b, 0xc219c, 0xc234b, 0xc235c, 0xc250b,
    0xc251c, 0xc26cb, 0xc26dc, 0xc288b, 0xc289c, 0xc2a4b,
    0xc2a5c, 0xc2c0b, 0xc2c1c, 0xc2dcb, 0xc2ddc, 0xc2f8b,
    0xc2f9c, 0xc314b, 0xc315c, 0xc330b, 0xc331c, 0xc34cb,
    0xc34dc, 0xc368b, 0xc369c, 0xc384b, 0xc385c, 0xc3a0b,
    0xc3a1c, 0xc3bcb, 0xc3bdc, 0xc3d8b, 0xc3d9c, 0xc3f4b,
    0xc3f5c, 0xc410b, 0xc411c, 0xc42cb, 0xc42dc, 0xc448b,
    0xc449c, 0xc464b, 0xc465c, 0xc480b, 0xc481c, 0xc49cb,
    0xc49dc, 0xc4b8b, 0xc4b9c, 0xc4d4b, 0xc4d5c, 0xc4f0b,
    0xc4f1c, 0xc50cb, 0xc50dc, 0xc528b, 0xc529c, 0xc544b,
    0xc545c, 0xc560b, 0xc561c, 0xc57cb, 0xc57dc, 0xc598b,
    0xc599c, 0xc5b4b, 0xc5b5c, 0xc5d0b, 0xc5d1c, 0xc5ecb,
    0xc5edc, 0xc608b, 0xc609c, 0xc624b, 0xc625c, 0xc640b,
    0xc641c, 0xc65cb, 0xc65dc, 0xc678b, 0xc679c, 0xc694b,
    0xc695c, 0xc6b0b, 0xc6b1c, 0xc6ccb, 0xc6cdc, 0xc6e8b,
    0xc6e9c, 0xc704b, 0xc705c, 0xc720b, 0xc721c, 0xc73cb,
    0xc73dc, 0xc758b, 0xc759c, 0xc774b, 0xc775c, 0xc790b,
    0xc791c, 0xc7acb, 0xc7adc, 0xc7c8b, 0xc7c9c, 0xc7e4b,
    0xc7e5c, 0xc800b, 0xc801c, 0xc81cb, 0xc81dc, 0xc838b,
    0xc839c, 0xc854b, 0xc855c, 0xc870b, 0xc871c, 0xc88cb,
    0xc88dc, 0xc8a8b, 0xc8a9c, 0xc8c4b, 0xc8c5c, 0xc8e0b,
    0xc8e1c, 0xc8fcb, 0xc8fdc, 0xc918b, 0xc919c, 0xc934b,
    0xc935c, 0xc950b, 0xc951c, 0xc96cb, 0xc96dc, 0xc988b,
    0xc989c, 0xc9a4b, 0xc9a5c, 0xc9c0b, 0xc9c1c, 0xc9dcb,
    0xc9ddc, 0xc9f8b, 0xc9f9c, 0xca14b, 0xca15c, 0xca30b,
    0xca31c, 0xca4cb, 0xca4dc, 0xca68b, 0xca69c, 0xca84b,
    0xca85c, 0xcaa0b, 0xcaa1c, 0xcabcb, 0xcabdc, 0xcad8b,
    0xcad9c, 0xcaf4b, 0xcaf5c, 0xcb10b, 0xcb11c, 0xcb2cb,
    0xcb2dc, 0xcb48b, 0xcb49c, 0xcb64b, 0xcb65c, 0xcb80b,
    0xcb81c, 0xcb9cb, 0xcb9dc, 0xcbb8b, 0xcbb9c, 0xcbd4b,
    0xcbd5c, 0xcbf0b, 0xcbf1c, 0xcc0cb, 0xcc0dc, 0xcc28b,
    0xcc29c, 0xcc44b, 0xcc45c, 0xcc60b, 0xcc61c, 0xcc7cb,
    0xcc7dc, 0xcc98b, 0xcc99c, 0xccb4b, 0xccb5c, 0xccd0b,
    0xccd1c, 0xccecb, 0xccedc, 0xcd08b, 0xcd09c, 0xcd24b,
    0xcd25c, 0xcd40b, 0xcd41c, 0xcd5cb, 0xcd5dc, 0xcd78b,
    0xcd79c, 0xcd94b, 0xcd95c, 0xcdb0b, 0xcdb1c, 0xcdccb,
    0xcdcdc, 0xcde8b, 0xcde9c, 0xce04b, 0xce05c, 0xce20b,
    0xce21c, 0xce3cb, 0xce3dc, 0xce58b, 0xce59c, 0xce74b,
    0xce75c, 0xce90b, 0xce91c, 0xceacb, 0xceadc, 0xcec8b,
    0xcec9c, 0xcee4b, 0xcee5c, 0xcf00b, 0xcf01c, 0xcf1cb,
    0xcf1dc, 0xcf38b, 0xcf39c, 0xcf54b, 0xcf55c, 0xcf70b,
    0xcf71c, 0xcf8cb, 0xcf8dc, 0xcfa8b, 0xcfa9c, 0xcfc4b,
    0xcfc5c, 0xcfe0b, 0xcfe1c, 0xcffcb, 0xcffdc, 0xd018b,
    0xd019c, 0xd034b, 0xd035c, 0xd050b, 0xd051c, 0xd06cb,
    0xd06dc, 0xd088b, 0xd089c, 0xd0a4b, 0xd0a5c, 0xd0c0b,
    0xd0c1c, 0xd0dcb, 0xd0ddc, 0xd0f8b, 0xd0f9c, 0xd114b,
    0xd115c, 0xd130b, 0xd131c, 0xd14cb, 0xd14dc, 0xd168b,
    0xd169c, 0xd184b, 0xd185c, 0xd1a0b, 0xd1a1c, 0xd1bcb,
    0xd1bdc, 0xd1d8b, 0xd1d9c, 0xd1f4b, 0xd1f5c, 0xd210b,
    0xd211c, 0xd22cb, 0xd22dc, 0xd248b, 0xd249c, 0xd264b,
    0xd265c, 0xd280b, 0xd281c, 0xd29cb, 0xd29dc, 0xd2b8b,
    0xd2b9c, 0xd2d4b, 0xd2d5c, 0xd2f0b, 0xd2f1c, 0xd30cb,
    0xd30dc, 0xd328b, 0xd329c, 0xd344b, 0xd345c, 0xd360b,
    0xd361c, 0xd37cb, 0xd37dc, 0xd398b, 0xd399c, 0xd3b4b,
    0xd3b5c, 0xd3d0b, 0xd3d1c, 0xd3ecb, 0xd3edc, 0xd408b,
    0xd409c, 0xd424b, 0xd425c, 0xd440b, 0xd441c, 0xd45cb,
    0xd45dc, 0xd478b, 0xd479c, 0xd494b, 0xd495c, 0xd4b0b,
    0xd4b1c, 0xd4ccb, 0xd4cdc, 0xd4e8b, 0xd4e9c, 0xd504b,
    0xd505c, 0xd520b, 0xd521c, 0xd53cb, 0xd53dc, 0xd558b,
    0xd559c, 0xd574b, 0xd575c, 0xd590b, 0xd591c, 0xd5acb,
    0xd5adc, 0xd5c8b, 0xd5c9c, 0xd5e4b, 0xd5e5c, 0xd600b,
    0xd601c, 0xd61cb, 0xd61dc, 0xd638b, 0xd639c, 0xd654b,
    0xd655c, 0xd670b, 0xd671c, 0xd68cb, 0xd68dc, 0xd6a8b,
    0xd6a9c, 0xd6c4b, 0xd6c5c, 0xd6e0b, 0xd6e1c, 0xd6fcb,
    0xd6fdc, 0xd718b, 0xd719c, 0xd734b, 0xd735c, 0xd750b,
    0xd751c, 0xd76cb, 0xd76dc, 0xd788b, 0xd789c, 0xd7a40,
    0xd7b08, 0xd7c70, 0xd7cb9, 0xd7fc0, 0xfb1e4, 0xfb1f0,
    0xfe004, 0xfe100, 0xfe204, 0xfe300, 0xfeff1, 0xff000,
    0xff9e4, 0xffa00, 0xfff01, 0xfffc0, 0x101fd4, 0x101fe0,
    0x102e04, 0x102e10, 0x103764, 0x1037b0, 0x10a014, 0x10a040,
    0x10a054, 0x10a070, 0x10a0c4, 0x10a100, 0x10a384, 0x10a3b0,
    0x10a3f4, 0x10a400, 0x10ae54, 0x10ae70, 0x10d244, 0x10d280,
    0x10d694, 0x10d6e0, 0x10eab4, 0x10ead0, 0x10efc4, 0x10f000,
    0x10f464, 0x10f510, 0x10f824, 0x10f860, 0x110006, 0x110014,
    0x110026, 0x110030, 0x110384, 0x110470, 0x110704, 0x110710,
    0x110734, 0x110750, 0x1107f4, 0x110826, 0x110830, 0x110b06,
    0x110b34, 0x110b76, 0x110b94, 0x110bb0, 0x110bd5, 0x110be0,
    0x110c24, 0x110c30, 0x110cd5, 0x110ce0, 0x111004, 0x111030,
    0x111274, 0x1112c6, 0x1112d4, 0x111350, 0x111456, 0x111470,
    0x111734, 0x111740, 0x111804, 0x111826, 0x111830, 0x111b36,
    0x111b64, 0x111bf6, 0x111c04, 0x111c10, 0x111c25, 0x111c40,
    0x111c94, 0x111cd0, 0x111ce6, 0x111cf4, 0x111d00, 0x1122c6,
    0x1122f4, 0x112326, 0x112344, 0x112380, 0x1123e4, 0x1123f0,
    0x112414, 0x112420, 0x112df4, 0x112e06, 0x112e34, 0x112eb0,
    0x113004, 0x113026, 0x113040, 0x1133b4, 0x1133d0, 0x1133e4,
    0x1133f6, 0x113404, 0x113416, 0x113450, 0x113476, 0x113490,
    0x1134b6, 0x1134d4, 0x1134e0, 0x113574, 0x113580, 0x113626,
    0x113640, 0x113664, 0x1136d0, 0x113704, 0x113750, 0x113b84,
    0x113b96, 0x113bb4, 0x113c10, 0x113c24, 0x113c30, 0x113c54,
    0x113c60, 0x113c74, 0x113ca6, 0x113cb0, 0x113cc6, 0x113ce4,
    0x113d15, 0x113d24, 0x113d30, 0x113e14, 0x113e30, 0x114356,
    0x114384, 0x114406, 0x114424, 0x114456, 0x114464, 0x114470,
    0x1145e4, 0x1145f0, 0x114b04, 0x114b16, 0x114b34, 0x114b96,
    0x114ba4, 0x114bb6, 0x114bd4, 0x114be6, 0x114bf4, 0x114c16,
    0x114c24, 0x114c40, 0x115af4, 0x115b06, 0x115b24, 0x115b60,
    0x115b86, 0x115bc4, 0x115be6, 0x115bf4, 0x115c10, 0x115dc4,
    0x115de0, 0x116306, 0x116334, 0x1163b6, 0x1163d4, 0x1163e6,
    0x1163f4, 0x116410, 0x116ab4, 0x116ac6, 0x116ad4, 0x116ae6,
    0x116b04, 0x116b80, 0x1171d4, 0x1171e6, 0x1171f4, 0x117200,
    0x117224, 0x117266, 0x117274, 0x1172c0, 0x1182c6, 0x1182f4,
    0x118386, 0x118394, 0x1183b0, 0x119304, 0x119316, 0x119360,
    0x119376, 0x119390, 0x1193b4, 0x1193f5, 0x119406, 0x119415,
    0x119426, 0x119434, 0x119440, 0x119d16, 0x119d44, 0x119d80,
    0x119da4, 0x119dc6, 0x119e04, 0x119e10, 0x119e46, 0x119e50,
    0x11a014, 0x11a0b0, 0x11a334, 0x11a396, 0x11a3a5, 0x11a3b4,
    0x11a3f0, 0x11a474, 0x11a480, 0x11a514, 0x11a576, 0x11a594,
    0x11a5c0, 0x11a845, 0x11a8a4, 0x11a976, 0x11a984, 0x11a9a0,
    0x11c2f6, 0x11c304, 0x11c370, 0x11c384, 0x11c3e6, 0x11c3f4,
    0x11c400, 0x11c924, 0x11ca80, 0x11ca96, 0x11caa4, 0x11cb16,
    0x11cb24, 0x11cb46, 0x11cb54, 0x11cb70, 0x11d314, 0x11d370,
    0x11d3a4, 0x11d3b0, 0x11d3c4, 0x11d3e0, 0x11d3f4, 0x11d465,
    0x11d474, 0x11d480, 0x11d8a6, 0x11d8f0, 0x11d904, 0x11d920,
    0x11d936, 0x11d954, 0x11d966, 0x11d974, 0x11d980, 0x11ef34,
    0x11ef56, 0x11ef70, 0x11f004, 0x11f025, 0x11f036, 0x11f040,
    0x11f346, 0x11f364, 0x11f3b0, 0x11f3e6, 0x11f404, 0x11f430,
    0x11f5a4, 0x11f5b0, 0x134301, 0x134404, 0x134410, 0x134474,
    0x134560, 0x1611e4, 0x1612a6, 0x1612d4, 0x161300, 0x16af04,
    0x16af50, 0x16b304, 0x16b370, 0x16d638, 0x16d640, 0x16d678,
    0x16d6b0, 0x16f4f4, 0x16f500, 0x16f516, 0x16f880, 0x16f8f4,
    0x16f930, 0x16fe44, 0x16fe50, 0x16ff04, 0x16ff20, 0x1bc9d4,
    0x1bc9f0, 0x1bca01, 0x1bca40, 0x1cf004, 0x1cf2e0, 0x1cf304,
    0x1cf470, 0x1d1654, 0x1d16a0, 0x1d16d4, 0x1d1731, 0x1d17b4,
    0x1d1830, 0x1d1854, 0x1d18c0, 0x1d1aa4, 0x1d1ae0, 0x1d2424,
    0x1d2450, 0x1da004, 0x1da370, 0x1da3b4, 0x1da6d0, 0x1da754,
    0x1da760, 0x1da844, 0x1da850, 0x1da9b4, 0x1daa00, 0x1daa14,
    0x1dab00, 0x1e0004, 0x1e0070, 0x1e0084, 0x1e0190, 0x1e01b4,
    0x1e0220, 0x1e0234, 0x1e0250, 0x1e0264, 0x1e02b0, 0x1e08f4,
    0x1e0900, 0x1e1304, 0x1e1370, 0x1e2ae4, 0x1e2af0, 0x1e2ec4,
    0x1e2f00, 0x1e4ec4, 0x1e4f00, 0x1e5ee4, 0x1e5f00, 0x1e8d04,
    0x1e8d70, 0x1e9444, 0x1e94b0, 0x1f1e6d, 0x1f2000, 0x1f3fb4,
    0x1f4000, 0xe00001, 0xe00204, 0xe00801, 0xe01004, 0xe01f01,
    0xe10000,
  };

  inline constexpr char32_t __incb_linkers[] = {
    0x094d, 0x09cd, 0x0acd, 0x0b4d, 0x0c4d, 0x0d4d,
  };

  enum class _InCB { _Consonant = 1, _Extend = 2 };

  // Values generated by contrib/unicode/gen_libstdcxx_unicode_data.py,
  // from DerivedCoreProperties.txt from the Unicode standard.
  // Entries are (code_point << 2) + property.
  inline constexpr uint32_t __incb_edges[] = {
    0xc02, 0xdc0, 0x120e, 0x1228, 0x1646, 0x16f8,
    0x16fe, 0x1700, 0x1706, 0x170c, 0x1712, 0x1718,
    0x171e, 0x1720, 0x1842, 0x186c, 0x192e, 0x1980,
    0x19c2, 0x19c4, 0x1b5a, 0x1b74, 0x1b7e, 0x1b94,
    0x1b9e, 0x1ba4, 0x1baa, 0x1bb8, 0x1c46, 0x1c48,
    0x1cc2, 0x1d2c, 0x1e9a, 0x1ec4, 0x1fae, 0x1fd0,
    0x1ff6, 0x1ff8, 0x205a, 0x2068, 0x206e, 0x2090,
    0x2096, 0x20a0, 0x20a6, 0x20b8, 0x2166, 0x2170,
    0x225e, 0x2280, 0x232a, 0x2388, 0x238e, 0x240c,
    0x2455, 0x24ea, 0x24ec, 0x24f2, 0x24f4, 0x2506,
    0x2524, 0x2546, 0x2561, 0x2580, 0x258a, 0x2590,
    0x25e1, 0x2600, 0x2606, 0x2608, 0x2655, 0x26a4,
    0x26a9, 0x26c4, 0x26c9, 0x26cc, 0x26d9, 0x26e8,
    0x26f2, 0x26f4, 0x26fa, 0x26fc, 0x2706, 0x2714,
    0x275e, 0x2760, 0x2771, 0x2778, 0x277d, 0x2780,
    0x278a, 0x2790, 0x27c1, 0x27c8, 0x27fa, 0x27fc,
    0x2806, 0x280c, 0x28f2, 0x28f4, 0x2906, 0x290c,
    0x291e, 0x2924, 0x292e, 0x2938, 0x2946, 0x2948,
    0x29c2, 0x29c8, 0x29d6, 0x29d8, 0x2a06, 0x2a0c,
    0x2a55, 0x2aa4, 0x2aa9, 0x2ac4, 0x2ac9, 0x2ad0,
    0x2ad5, 0x2ae8, 0x2af2, 0x2af4, 0x2b06, 0x2b18,
    0x2b1e, 0x2b24, 0x2b8a, 0x2b90, 0x2be5, 0x2bea,
    0x2c00, 0x2c06, 0x2c08, 0x2c55, 0x2ca4, 0x2ca9,
    0x2cc4, 0x2cc9, 0x2cd0, 0x2cd5, 0x2ce8, 0x2cf2,
    0x2cf4, 0x2cfa, 0x2d00, 0x2d06, 0x2d14, 0x2d56,
    0x2d60, 0x2d71, 0x2d78, 0x2d7d, 0x2d80, 0x2d8a,
    0x2d90, 0x2dc5, 0x2dc8, 0x2e0a, 0x2e0c, 0x2efa,
    0x2efc, 0x2f02, 0x2f04, 0x2f36, 0x2f38, 0x2f5e,
    0x2f60, 0x3002, 0x3004, 0x3012, 0x3014, 0x3055,
    0x30a4, 0x30a9, 0x30e8, 0x30f2, 0x30f4, 0x30fa,
    0x3104, 0x311a, 0x3124, 0x312a, 0x3134, 0x3156,
    0x315c, 0x3161, 0x316c, 0x318a, 0x3190, 0x3206,
    0x3208, 0x32f2, 0x32f4, 0x32fe, 0x3304, 0x330a,
    0x330c, 0x331a, 0x3324, 0x332a, 0x3338, 0x3356,
    0x335c, 0x338a, 0x3390, 0x3402, 0x3408, 0x3455,
    0x34ee, 0x34f4, 0x34fa, 0x34fc, 0x3506, 0x3514,
    0x355e, 0x3560, 0x358a, 0x3590, 0x3606, 0x3608,
    0x372a, 0x372c, 0x373e, 0x3740, 0x374a, 0x3754,
    0x375a, 0x375c, 0x377e, 0x3780, 0x38c6, 0x38c8,
    0x38d2, 0x38ec, 0x391e, 0x393c, 0x3ac6, 0x3ac8,
    0x3ad2, 0x3af4, 0x3b22, 0x3b3c, 0x3c62, 0x3c68,
    0x3cd6, 0x3cd8, 0x3cde, 0x3ce0, 0x3ce6, 0x3ce8,
    0x3dc6, 0x3dfc, 0x3e02, 0x3e14, 0x3e1a, 0x3e20,
    0x3e36, 0x3e60, 0x3e66, 0x3ef4, 0x3f1a, 0x3f1c,
    0x40b6, 0x40c4, 0x40ca, 0x40e0, 0x40e6, 0x40ec,
    0x40f6, 0x40fc, 0x4162, 0x4168, 0x417a, 0x4184,
    0x41c6, 0x41d4, 0x420a, 0x420c, 0x4216, 0x421c,
    0x4236, 0x4238, 0x4276, 0x4278, 0x4d76, 0x4d80,
    0x5c4a, 0x5c58, 0x5cca, 0x5cd4, 0x5d4a, 0x5d50,
    0x5dca, 0x5dd0, 0x5ed2, 0x5ed8, 0x5ede, 0x5ef8,
    0x5f1a, 0x5f1c, 0x5f26, 0x5f50, 0x5f76, 0x5f78,
    0x602e, 0x6038, 0x603e, 0x6040, 0x6216, 0x621c,
    0x62a6, 0x62a8, 0x6482, 0x648c, 0x649e, 0x64a4,
    0x64ca, 0x64cc, 0x64e6, 0x64f0, 0x685e, 0x6864,
    0x686e, 0x6870, 0x695a, 0x695c, 0x6962, 0x697c,
    0x6982, 0x6984, 0x698a, 0x698c, 0x6996, 0x69b4,
    0x69ce, 0x69f4, 0x69fe, 0x6a00, 0x6ac2, 0x6b3c,
    0x6c02, 0x6c10, 0x6cd2, 0x6cf8, 0x6d0a, 0x6d14,
    0x6dae, 0x6dd0, 0x6e02, 0x6e08, 0x6e8a, 0x6e98,
    0x6ea2, 0x6eb8, 0x6f9a, 0x6f9c, 0x6fa2, 0x6fa8,
    0x6fb6, 0x6fb8, 0x6fbe, 0x6fd0, 0x70b2, 0x70d0,
    0x70da, 0x70e0, 0x7342, 0x734c, 0x7352, 0x7384,
    0x738a, 0x73a4, 0x73b6, 0x73b8, 0x73d2, 0x73d4,
    0x73e2, 0x73e8, 0x7702, 0x7800, 0x8036, 0x8038,
    0x8342, 0x83c4, 0xb3be, 0xb3c8, 0xb5fe, 0xb600,
    0xb782, 0xb800, 0xc0aa, 0xc0c0, 0xc266, 0xc26c,
    0x299be, 0x299cc, 0x299d2, 0x299f8, 0x29a7a, 0x29a80,
    0x29bc2, 0x29bc8, 0x2a00a, 0x2a00c, 0x2a01a, 0x2a01c,
    0x2a02e, 0x2a030, 0x2a096, 0x2a09c, 0x2a0b2, 0x2a0b4,
    0x2a312, 0x2a318, 0x2a382, 0x2a3c8, 0x2a3fe, 0x2a400,
    0x2a49a, 0x2a4b8, 0x2a51e, 0x2a548, 0x2a54e, 0x2a550,
    0x2a602, 0x2a60c, 0x2a6ce, 0x2a6d0, 0x2a6da, 0x2a6e8,
    0x2a6f2, 0x2a6f8, 0x2a702, 0x2a704, 0x2a796, 0x2a798,
    0x2a8a6, 0x2a8bc, 0x2a8c6, 0x2a8cc, 0x2a8d6, 0x2a8dc,
    0x2a90e, 0x2a910, 0x2a932, 0x2a934, 0x2a9f2, 0x2a9f4,
    0x2aac2, 0x2aac4, 0x2aaca, 0x2aad4, 0x2aade, 0x2aae4,
    0x2aafa, 0x2ab00, 0x2ab06, 0x2ab08, 0x2abb2, 0x2abb8,
    0x2abda, 0x2abdc, 0x2af96, 0x2af98, 0x2afa2, 0x2afa4,
    0x2afb6, 0x2afb8, 0x3ec7a, 0x3ec7c, 0x3f802, 0x3f840,
    0x3f882, 0x3f8c0, 0x3fe7a, 0x3fe80, 0x407f6, 0x407f8,
    0x40b82, 0x40b84, 0x40dda, 0x40dec, 0x42806, 0x42810,
    0x42816, 0x4281c, 0x42832, 0x42840, 0x428e2, 0x428ec,
    0x428fe, 0x42900, 0x42b96, 0x42b9c, 0x43492, 0x434a0,
    0x435a6, 0x435b8, 0x43aae, 0x43ab4, 0x43bf2, 0x43c00,
    0x43d1a, 0x43d44, 0x43e0a, 0x43e18, 0x44006, 0x44008,
    0x440e2, 0x4411c, 0x441c2, 0x441c4, 0x441ce, 0x441d4,
    0x441fe, 0x44208, 0x442ce, 0x442dc, 0x442e6, 0x442ec,
    0x4430a, 0x4430c, 0x44402, 0x4440c, 0x4449e, 0x444b0,
    0x444b6, 0x444d4, 0x445ce, 0x445d0, 0x44602, 0x44608,
    0x446da, 0x446fc, 0x44702, 0x44704, 0x44726, 0x44734,
    0x4473e, 0x44740, 0x448be, 0x448c8, 0x448d2, 0x448e0,
    0x448fa, 0x448fc, 0x44906, 0x44908, 0x44b7e, 0x44b80,
    0x44b8e, 0x44bac, 0x44c02, 0x44c08, 0x44cee, 0x44cf4,
    0x44cfa, 0x44cfc, 0x44d02, 0x44d04, 0x44d36, 0x44d38,
    0x44d5e, 0x44d60, 0x44d9a, 0x44db4, 0x44dc2, 0x44dd4,
    0x44ee2, 0x44ee4, 0x44eee, 0x44f04, 0x44f0a, 0x44f0c,
    0x44f16, 0x44f18, 0x44f1e, 0x44f28, 0x44f3a, 0x44f44,
    0x44f4a, 0x44f4c, 0x44f86, 0x44f8c, 0x450e2, 0x45100,
    0x4510a, 0x45114, 0x4511a, 0x4511c, 0x4517a, 0x4517c,
    0x452c2, 0x452c4, 0x452ce, 0x452e4, 0x452ea, 0x452ec,
    0x452f6, 0x452f8, 0x452fe, 0x45304, 0x4530a, 0x45310,
    0x456be, 0x456c0, 0x456ca, 0x456d8, 0x456f2, 0x456f8,
    0x456fe, 0x45704, 0x45772, 0x45778, 0x458ce, 0x458ec,
    0x458f6, 0x458f8, 0x458fe, 0x45904, 0x45aae, 0x45ab0,
    0x45ab6, 0x45ab8, 0x45ac2, 0x45ae0, 0x45c76, 0x45c78,
    0x45c7e, 0x45c80, 0x45c8a, 0x45c98, 0x45c9e, 0x45cb0,
    0x460be, 0x460e0, 0x460e6, 0x460ec, 0x464c2, 0x464c4,
    0x464ee, 0x464fc, 0x4650e, 0x46510, 0x46752, 0x46760,
    0x4676a, 0x46770, 0x46782, 0x46784, 0x46806, 0x4682c,
    0x468ce, 0x468e4, 0x468ee, 0x468fc, 0x4691e, 0x46920,
    0x46946, 0x4695c, 0x46966, 0x46970, 0x46a2a, 0x46a5c,
    0x46a62, 0x46a68, 0x470c2, 0x470dc, 0x470e2, 0x470f8,
    0x470fe, 0x47100, 0x4724a, 0x472a0, 0x472aa, 0x472c4,
    0x472ca, 0x472d0, 0x472d6, 0x472dc, 0x474c6, 0x474dc,
    0x474ea, 0x474ec, 0x474f2, 0x474f8, 0x474fe, 0x47518,
    0x4751e, 0x47520, 0x47642, 0x47648, 0x47656, 0x47658,
    0x4765e, 0x47660, 0x47bce, 0x47bd4, 0x47c02, 0x47c08,
    0x47cda, 0x47cec, 0x47d02, 0x47d0c, 0x47d6a, 0x47d6c,
    0x4d102, 0x4d104, 0x4d11e, 0x4d158, 0x5847a, 0x584a8,
    0x584b6, 0x584c0, 0x5abc2, 0x5abd4, 0x5acc2, 0x5acdc,
    0x5bd3e, 0x5bd40, 0x5be3e, 0x5be4c, 0x5bf92, 0x5bf94,
    0x5bfc2, 0x5bfc8, 0x6f276, 0x6f27c, 0x73c02, 0x73cb8,
    0x73cc2, 0x73d1c, 0x74596, 0x745a8, 0x745b6, 0x745cc,
    0x745ee, 0x7460c, 0x74616, 0x74630, 0x746aa, 0x746b8,
    0x7490a, 0x74914, 0x76802, 0x768dc, 0x768ee, 0x769b4,
    0x769d6, 0x769d8, 0x76a12, 0x76a14, 0x76a6e, 0x76a80,
    0x76a86, 0x76ac0, 0x78002, 0x7801c, 0x78022, 0x78064,
    0x7806e, 0x78088, 0x7808e, 0x78094, 0x7809a, 0x780ac,
    0x7823e, 0x78240, 0x784c2, 0x784dc, 0x78aba, 0x78abc,
    0x78bb2, 0x78bc0, 0x793b2, 0x793c0, 0x797ba, 0x797c0,
    0x7a342, 0x7a35c, 0x7a512, 0x7a52c, 0x7cfee, 0x7d000,
    0x380082, 0x380200, 0x380402, 0x3807c0,
  };

  // Table generated by contrib/unicode/gen_libstdcxx_unicode_data.py,
  // from emoji-data.txt from the Unicode standard.
  inline constexpr char32_t __xpicto_edges[] = {
    0xa9, 0xaa, 0xae, 0xaf, 0x203c, 0x203d, 0x2049, 0x204a,
    0x2122, 0x2123, 0x2139, 0x213a, 0x2194, 0x219a, 0x21a9, 0x21ab,
    0x231a, 0x231c, 0x2328, 0x2329, 0x2388, 0x2389, 0x23cf, 0x23d0,
    0x23e9, 0x23f4, 0x23f8, 0x23fb, 0x24c2, 0x24c3, 0x25aa, 0x25ac,
    0x25b6, 0x25b7, 0x25c0, 0x25c1, 0x25fb, 0x25ff, 0x2600, 0x2606,
    0x2607, 0x2613, 0x2614, 0x2686, 0x2690, 0x2706, 0x2708, 0x2713,
    0x2714, 0x2715, 0x2716, 0x2717, 0x271d, 0x271e, 0x2721, 0x2722,
    0x2728, 0x2729, 0x2733, 0x2735, 0x2744, 0x2745, 0x2747, 0x2748,
    0x274c, 0x274d, 0x274e, 0x274f, 0x2753, 0x2756, 0x2757, 0x2758,
    0x2763, 0x2768, 0x2795, 0x2798, 0x27a1, 0x27a2, 0x27b0, 0x27b1,
    0x27bf, 0x27c0, 0x2934, 0x2936, 0x2b05, 0x2b08, 0x2b1b, 0x2b1d,
    0x2b50, 0x2b51, 0x2b55, 0x2b56, 0x3030, 0x3031, 0x303d, 0x303e,
    0x3297, 0x3298, 0x3299, 0x329a, 0x1f000, 0x1f100, 0x1f10d, 0x1f110,
    0x1f12f, 0x1f130, 0x1f16c, 0x1f172, 0x1f17e, 0x1f180, 0x1f18e, 0x1f18f,
    0x1f191, 0x1f19b, 0x1f1ad, 0x1f1e6, 0x1f201, 0x1f210, 0x1f21a, 0x1f21b,
    0x1f22f, 0x1f230, 0x1f232, 0x1f23b, 0x1f23c, 0x1f240, 0x1f249, 0x1f3fb,
    0x1f400, 0x1f53e, 0x1f546, 0x1f650, 0x1f680, 0x1f700, 0x1f774, 0x1f780,
    0x1f7d5, 0x1f800, 0x1f80c, 0x1f810, 0x1f848, 0x1f850, 0x1f85a, 0x1f860,
    0x1f888, 0x1f890, 0x1f8ae, 0x1f900, 0x1f90c, 0x1f93b, 0x1f93c, 0x1f946,
    0x1f947, 0x1fb00, 0x1fc00, 0x1fffe,
  };

#undef _GLIBCXX_GET_UNICODE_DATA
