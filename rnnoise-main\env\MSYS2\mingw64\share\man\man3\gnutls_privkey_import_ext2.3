.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_import_ext2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_import_ext2 \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_import_ext2(gnutls_privkey_t " pkey ", gnutls_pk_algorithm_t " pk ", void * " userdata ", gnutls_privkey_sign_func " sign_fn ", gnutls_privkey_decrypt_func " decrypt_fn ", gnutls_privkey_deinit_func " deinit_fn ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t pkey" 12
The private key
.IP "gnutls_pk_algorithm_t pk" 12
The public key algorithm
.IP "void * userdata" 12
private data to be provided to the callbacks
.IP "gnutls_privkey_sign_func sign_fn" 12
callback for signature operations
.IP "gnutls_privkey_decrypt_func decrypt_fn" 12
callback for decryption operations
.IP "gnutls_privkey_deinit_func deinit_fn" 12
a deinitialization function
.IP "unsigned int flags" 12
Flags for the import
.SH "DESCRIPTION"
This function will associate the given callbacks with the
\fBgnutls_privkey_t\fP type. At least one of the two callbacks
must be non\-null. If a deinitialization function is provided
then flags is assumed to contain \fBGNUTLS_PRIVKEY_IMPORT_AUTO_RELEASE\fP.

Note that the signing function is supposed to "raw" sign data, i.e.,
without any hashing or preprocessing. In case of RSA the DigestInfo
will be provided, and the signing function is expected to do the PKCS \fB1\fP
1.5 padding and the exponentiation.

See also \fBgnutls_privkey_import_ext3()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.1
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
