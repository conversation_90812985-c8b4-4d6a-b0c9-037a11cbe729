;ELC   
;;; Compiled
;;; in Emacs version 30.1
;;; with all optimizations.


#@281 *The name of the cmake executable.

This can be either absolute or looked up in $PATH.  You can also
set the path with these commands:
 (setenv "PATH" (concat (getenv "PATH") ";C:\\Program Files\\CMake 2.8\\bin"))
 (setenv "PATH" (concat (getenv "PATH") ":/usr/local/cmake/bin"))#@2 
(byte-code "\300\301!\210\300\302!\210\303\304\305\306\307DD\310\311\312\313\314&\207" [require rst rx custom-declare-variable cmake-mode-cmake-executable funcall function #[0 "\300\207" ["cmake"] 1 (#$ . 368)] (#$ . 84) :type file :group cmake] 8)
(defconst cmake-keywords-block-open '("BLOCK" "IF" "MACRO" "FOREACH" "ELSE" "ELSEIF" "WHILE" "FUNCTION"))
(defconst cmake-keywords-block-close '("ENDBLOCK" "ENDIF" "ENDFOREACH" "ENDMACRO" "ELSE" "ELSEIF" "ENDWHILE" "ENDFUNCTION"))
(defconst cmake-keywords (byte-code "\302	\303#\304!\207" [cmake-keywords-block-open cmake-keywords-block-close append nil delete-dups] 4))
(defconst cmake-regex-blank "^[ 	]*$")
(defconst cmake-regex-comment "#.*")
(defconst cmake-regex-paren-left "(")
(defconst cmake-regex-paren-right ")")
(defconst cmake-regex-closing-parens-line (concat "^[[:space:]]*\\(" cmake-regex-paren-right "+\\)[[:space:]]*$"))
(defconst cmake-regex-argument-quoted "\"\\(?:[^\"\\]\\|\\\\[^z-a]\\)*\"")
(defconst cmake-regex-argument-unquoted "\\(?:[^\n\"#()\\[:space:]]\\|\\\\.\\)\\(?:[^\n#()\\[:space:]]\\|\\\\.\\)*")
(defconst cmake-regex-token (byte-code "\303\304\305\306D\307\310\306	D\306\nD\257D!\207" [cmake-regex-comment cmake-regex-argument-unquoted cmake-regex-argument-quoted rx-to-string group or regexp 40 41] 9))
(defconst cmake-regex-indented (byte-code "\301\302\303\304\305\306\307D\310BBDDE!\207" [cmake-regex-token rx-to-string and bol * group or regexp ((any space 10))] 8))
(defconst cmake-regex-block-open (byte-code "\301\302\303\304\305\306\307\"\"B\310BBB!\207" [cmake-keywords-block-open rx-to-string and symbol-start or append mapcar downcase (symbol-end)] 9))
(defconst cmake-regex-block-close (byte-code "\301\302\303\304\305\306\307\"\"B\310BBB!\207" [cmake-keywords-block-close rx-to-string and symbol-start or append mapcar downcase (symbol-end)] 9))
(defconst cmake-regex-close (byte-code "\302\303\304\305\306D\307\306	D\257!\207" [cmake-regex-block-close cmake-regex-paren-left rx-to-string and bol (* space) regexp (* space)] 8))
(defconst cmake-regex-token-paren-left (concat "^" cmake-regex-paren-left "$"))
(defconst cmake-regex-token-paren-right (concat "^" cmake-regex-paren-right "$"))#@69 Determine whether the beginning of the current line is in a string.
(defalias 'cmake-line-starts-inside-string #[0 "\212\300 \210`eb\210\301\302`\"8)\207" [beginning-of-line 3 parse-partial-sexp] 5 (#$ . 2576)])#@73 Move to the beginning of the last line that has meaningful indentation.
(defalias 'cmake-find-last-indented-line #[0 "`\302\303y\210\304`\"\262o?\2057 \305!\204+ \306 \204+ \307	\"\205' \211G\310\225U?\2057 \303y\210\304`\"\262\202 \207" [cmake-regex-blank cmake-regex-indented nil -1 buffer-substring-no-properties looking-at cmake-line-starts-inside-string string-match 0] 5 (#$ . 2794)])#@42 Number of columns to indent cmake blocks#@36 Indent current line as CMake code.
(byte-code "\300\301\302\303\304DD\305\306\307\310\311&\207" [custom-declare-variable cmake-tab-width funcall function #[0 "\300\207" [2] 1 (#$ . 368)] (#$ . 3197) :type integer :group cmake] 8)
(defalias 'cmake-indent #[0 "\306 ?\205\262 o\203 \307\310!\207\311\212\312 \210`\313!\314\311\315 \210\316 \262\203Z \310\317\n\314#\203K \320\310!\262\321\"\203@ T\202' \321\f\"\203' S\202' \211\310V\204V 
Z\262\210\202\250 \313!\204\231 \317\n\314#\203\231 \320\310!\262\321\"\204\205 \321\"\203\212 \313\322P!\203\212 
\\\262\321\f\"\203` 
Z\262\202` b\210\313!\203\250 
Z\262*\266\307\310]!\262\207" [cmake-regex-closing-parens-line case-fold-search cmake-regex-token cmake-regex-token-paren-left cmake-regex-token-paren-right cmake-tab-width cmake-line-starts-inside-string cmake-indent-line-to 0 nil beginning-of-line looking-at t cmake-find-last-indented-line current-indentation re-search-forward match-string string-match "[ 	]*" cmake-regex-block-open cmake-regex-paren-left cmake-regex-close] 10 (#$ . 3243) nil])
(defalias 'cmake-point-in-indentation #[0 "\300\301\302 `{\"\207" [string-match "^[ \\t]*$" line-beginning-position] 4])#@188 Indent the current line to COLUMN.
If point is within the existing indentation it is moved to the end of
the indentation.  Otherwise it retains the same position on the line

(fn COLUMN)
(defalias 'cmake-indent-line-to #[257 "\300 \203	 \301!\207\212\301!)\207" [cmake-point-in-indentation indent-line-to] 3 (#$ . 4464)])#@52 Convert all CMake commands to lowercase in buffer.
(defalias 'cmake-unscreamify-buffer #[0 "\212eb\210\300\301\302\303#\205 \304\305\306!\305\307!\227\305\310!Q\303\"\210\202 )\207" [re-search-forward "^\\([ 	]*\\)\\_<\\(\\(?:\\w\\|\\s_\\)+\\)\\_>\\([ 	]*(\\)" nil t replace-match match-string 1 2 3] 5 (#$ . 4793) nil])
(defconst cmake--regex-defun-start "^[[:space:]]*\\(?:function\\|macro\\)[[:space:]]*(")
(defconst cmake--regex-defun-end "^[[:space:]]*end\\(?:function\\|macro\\)[[:space:]]*([^)]*)")#@120 Move backward to the beginning of a CMake function or macro.

Return t unless search stops due to beginning of buffer.
(defalias 'cmake-beginning-of-defun #[0 "\302 \204 \303 \210\304\305	\306\307#)??\207" [case-fold-search cmake--regex-defun-start region-active-p push-mark t re-search-backward nil move] 4 (#$ . 5307) nil])#@107 Move forward to the end of a CMake function or macro.

Return t unless search stops due to end of buffer.
(defalias 'cmake-end-of-defun #[0 "\302 \204 \303 \210\304\305	\306\307#\205 \306y\210\304)\207" [case-fold-search cmake--regex-defun-end region-active-p push-mark t re-search-forward nil move] 4 (#$ . 5641) nil])#@42 Highlighting expressions for CMake mode.
(defconst cmake-font-lock-keywords (byte-code "\301\302\303\304\305\306\307\"\"B\310BBB!\311B\312\313B\314\315BE\207" [cmake-keywords rx-to-string and symbol-start or append mapcar downcase (symbol-end) font-lock-keyword-face "\\_<\\(\\(?:[[:word:]]\\|\\s_\\)+\\)[[:blank:]]*(" (1 font-lock-function-name-face) "\\${\\([+./_[:alnum:]-]+\\)}" (1 font-lock-variable-name-face t)] 9) (#$ . 5969))#@19 

(fn SYNTAX END)
(defalias 'cmake--syntax-propertize-until-bracket-close #[514 "\300\224\300\225\301\302{\"\303 \304\305\"\216\306\307#\203, \310\225\262\311S\312\313\n!$\210\202/ \262)\210\311\314\224\315\316$\207" [2 format "]%s]" match-data make-closure #[0 "\301\300\302\"\207" [V0 set-match-data t] 3] search-forward move 0 put-text-property syntax-table string-to-syntax 1 syntax-multiline t] 12 (#$ . 6411)])#@18 

(fn START END)
(defconst cmake--syntax-propertize-function #[514 "b\210`W\205L \300\301\302#\205L \303\224\203' \304\303\224\303\225\305\306$\210\307\310\"\210\202 \311\224\203 \312\313!\211A\314\233\241\210\315!\266\304\303\224\303\225\305\316$\210\307\317\"\210\202 \207" [re-search-forward "\\(#\\)\\[\\(=*\\)\\[\\|\\(\\[\\)\\(=*\\)\\[" t 1 put-text-property syntax-table (14) cmake--syntax-propertize-until-bracket-close "!" 3 match-data ints 6 set-match-data (15) "|"] 7 (#$ . 6846)])#@30 Syntax table for CMake mode.
(defvar cmake-mode-syntax-table nil (#$ . 7354))
(byte-code "\204% \301 \302\303\304#\210\302\305\306#\210\302\307\310#\210\302\311\312#\210\302\313\314#\210\301\207" [cmake-mode-syntax-table make-syntax-table modify-syntax-entry 40 "()" 41 ")(" 35 "<" 10 ">" 36 "'"] 5)
(defvar cmake-mode-hook nil)
(defvar cmake-mode-hook nil)
(byte-code "\300\301N\204\f \302\300\301\303#\210\304\305!\204 \302\305\306\307#\210\300\207" [cmake-mode-hook variable-documentation put "Hook run after entering `cmake-mode'.\nNo problems result if this variable is not bound.\n`add-hook' automatically binds it.  (This is true for all hook variables.)" boundp cmake-mode-map definition-name cmake-mode] 4)
(defvar cmake-mode-map (make-sparse-keymap))#@232 Major mode for editing CMake source files.

In addition to any hooks its parent mode `prog-mode' might have run,
this mode runs the hook `cmake-mode-hook', as the final or penultimate
step during initialization.

\{cmake-mode-map}
(byte-code "\301\302N\204 \303\301\302\304\305!#\210\306\307!\204* \303\307\310\311#\210\312\307\306\307!\203& \313\202( \314 \"\210\307\302N\2048 \303\307\302\304\315!#\210\306\300!\204X \303\300\310\311#\210\312\300\306\300!\203P \313\202V \316\300\313\"\210\"\210\300\302N\204f \303\300\302\304\317!#\210\320\321!\203t \321\311\322\"\210\202z \303\311\323\322#\210\313\207" [cmake-mode-abbrev-table cmake-mode-map variable-documentation put purecopy "Keymap for `cmake-mode'." boundp cmake-mode-syntax-table definition-name cmake-mode defvar-1 nil make-syntax-table "Syntax table for `cmake-mode'." define-abbrev-table "Abbrev table for `cmake-mode'." fboundp derived-mode-set-parent prog-mode derived-mode-parent] 5)
(defalias 'cmake-mode #[0 "\306\300!\210\307\310 \210\311\312\310\313N\203 \314\311\313\310\313N#\210\315!\204' \316\317 \"\210\320\f!\211\2035 \211\321 =\203; \322\f\323 \"\210\210\324
\325\"\204R 
*=\204R \326
\325*C#\210\327!\210\330\f!\210
*\306\331!\210\332\306\333!\210\334\306\335!\210\336\306\337!\210\340\306\341!\210\342!\306\343!\210+#\344\345\346\347\307$)\210\350\351!\207" [delay-mode-hooks major-mode mode-name cmake-mode-map cmake-mode-syntax-table cmake-mode-abbrev-table make-local-variable t prog-mode cmake-mode "CMake" mode-class put keymap-parent set-keymap-parent current-local-map char-table-parent standard-syntax-table set-char-table-parent syntax-table abbrev-table-get :parents abbrev-table-put use-local-map set-syntax-table beginning-of-defun-function cmake-beginning-of-defun end-of-defun-function cmake-end-of-defun font-lock-defaults (cmake-font-lock-keywords) indent-line-function cmake-indent comment-start "#" syntax-propertize-function add-hook syntax-propertize-extend-region-functions syntax-propertize-multiline nil run-mode-hooks cmake-mode-hook local-abbrev-table cmake--syntax-propertize-function] 5 (#$ . 8129) nil])#@156 Runs the command cmake with the arguments specified.  The
optional argument topic will be appended to the argument list.

(fn TYPE &optional TOPIC BUFFER)
(defalias 'cmake-command-run #[769 "\211\206 \302\205 \303\304\260\305!\203 \305!\202 \306!\307\307\203/ \310!\2021 \260\311\312\"\210\313 r\314\315\"\216\316\317\320\"!\210\321 \210\322\323!\210\324\323!+\207" [cmake-mode-cmake-executable resize-mini-windows "*CMake" "-" "*" get-buffer generate-new-buffer " " shell-quote-argument nil shell-command internal--before-save-selected-window make-closure #[0 "\301\300!\207" [V0 internal--after-save-selected-window] 2] select-window display-buffer not-this-window cmake-mode read-only-mode 1 view-mode] 11 (#$ . 10271) "s"])#@83 `cmake-command-run' but rendered in `rst-mode'.

(fn TYPE &optional TOPIC BUFFER)
(defalias 'cmake-command-run-help #[769 "\211\206 \302\205 \303\304\260\305!\203 \305!\202 \306!\307\307\203/ \310!\2021 \260\311\312\"\210\313 r\314\315\"\216\316\317\320\"!\210\321 \210\322\323!\210\324\323!+\207" [cmake-mode-cmake-executable resize-mini-windows "*CMake" "-" "*" get-buffer generate-new-buffer " " shell-quote-argument nil shell-command internal--before-save-selected-window make-closure #[0 "\301\300!\207" [V0 internal--after-save-selected-window] 2] select-window display-buffer not-this-window rst-mode read-only-mode 1 view-mode] 11 (#$ . 11030) "s"])#@42 Prints out a list of the cmake commands.
(defalias 'cmake-help-list-commands #[0 "\300\301!\207" [cmake-command-run-help "--help-command-list"] 2 (#$ . 11719) nil])#@46 List of available topics for --help-command.
(defvar cmake-commands nil (#$ . 11889))#@23 Command read history.
(defvar cmake-help-command-history nil (#$ . 11980))#@45 List of available topics for --help-module.
(defvar cmake-modules nil (#$ . 12060))#@22 Module read history.
(defvar cmake-help-module-history nil (#$ . 12149))#@47 List of available topics for --help-variable.
(defvar cmake-variables nil (#$ . 12227))#@24 Variable read history.
(defvar cmake-help-variable-history nil (#$ . 12320))#@47 List of available topics for --help-property.
(defvar cmake-properties nil (#$ . 12402))#@24 Property read history.
(defvar cmake-help-property-history nil (#$ . 12496))#@29 Complete help read history.
(defvar cmake-help-complete-history nil (#$ . 12578))
(defvar cmake-string-to-list-symbol '(("command" cmake-commands cmake-help-command-history) ("module" cmake-modules cmake-help-module-history) ("variable" cmake-variables cmake-help-variable-history) ("property" cmake-properties cmake-help-property-history)))#@122 If the value of LISTVAR is nil, run cmake --help-LISTNAME-list
and store the result as a list in LISTVAR.

(fn LISTNAME)
(defalias 'cmake-get-list #[257 "\301\"A@\211J\204+ \302 \303\304\"\216\305\306\307Q\310\311#\210r\311q\210\312\313ed\"\314\315#L*\207\211J\207" [cmake-string-to-list-symbol assoc current-window-configuration make-closure #[0 "\301\300!\207" [V0 set-window-configuration] 2] cmake-command-run-help "--help-" "-list" nil "*CMake Temporary*" split-string buffer-substring-no-properties "\n" t] 8 (#$ . 12926)])
(require 'thingatpt)
(defalias 'cmake-symbol-at-point #[0 "\300 \211??\205 \301!\207" [symbol-at-point symbol-name] 3])#@13 

(fn TYPE)
(defalias 'cmake-help-type #[257 "\301 \302\"AA@\303\304\305\"\306!\307\310&\211\311\230\203# \312\313!\207\207" [cmake-string-to-list-symbol cmake-symbol-at-point assoc completing-read format "CMake %s: " cmake-get-list nil t "" error "No argument given"] 10 (#$ . 13588)])#@63 Prints out the help message for the command the cursor is on.
(defalias 'cmake-help-command #[0 "\300\301\302\303!\304#\207" [cmake-command-run-help "--help-command" cmake-help-type "command" "*CMake Help*"] 4 (#$ . 13889) nil])#@62 Prints out the help message for the module the cursor is on.
(defalias 'cmake-help-module #[0 "\300\301\302\303!\304#\207" [cmake-command-run-help "--help-module" cmake-help-type "module" "*CMake Help*"] 4 (#$ . 14123) nil])#@64 Prints out the help message for the variable the cursor is on.
(defalias 'cmake-help-variable #[0 "\300\301\302\303!\304#\207" [cmake-command-run-help "--help-variable" cmake-help-type "variable" "*CMake Help*"] 4 (#$ . 14353) nil])#@64 Prints out the help message for the property the cursor is on.
(defalias 'cmake-help-property #[0 "\300\301\302\303!\304#\207" [cmake-command-run-help "--help-property" cmake-help-type "property" "*CMake Help*"] 4 (#$ . 14591) nil])#@88 Queries for any of the four available help topics and prints out the
appropriate page.
(defalias 'cmake-help #[0 "\300 \301\302!\301\303!\301\304!\301\305!\306$\307\310\311\312\n\313&\211\314\230\203( \315\316!\207\211\235\2035 \317\320\321#\207\211\235\203A \317\322\321#\207\211\235\203M \317\323\321#\207\211\235\203Y \317\324\321#\207\315\325!\207" [cmake-symbol-at-point cmake-get-list "command" "variable" "module" "property" append completing-read "CMake command/module/variable/property: " nil t cmake-help-complete-history "" error "No argument given" cmake-command-run-help "--help-command" "*CMake Help*" "--help-variable" "--help-module" "--help-property" "Not a know help topic."] 13 (#$ . 14829) nil])
(byte-code "\300\301\302\"\210\300\301\303\"\210\304\305!\207" [add-to-list auto-mode-alist ("CMakeLists\\.txt\\'" . cmake-mode) ("\\.cmake\\'" . cmake-mode) provide cmake-mode] 3)
