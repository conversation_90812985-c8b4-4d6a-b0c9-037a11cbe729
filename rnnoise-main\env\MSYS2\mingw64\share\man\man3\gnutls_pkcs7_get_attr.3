.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs7_get_attr" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs7_get_attr \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs7.h>
.sp
.BI "int gnutls_pkcs7_get_attr(gnutls_pkcs7_attrs_t " list ", unsigned " idx ", char ** " oid ", gnutls_datum_t * " data ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs7_attrs_t list" 12
A list of existing attributes or \fBNULL\fP for the first one
.IP "unsigned idx" 12
the index of the attribute to get
.IP "char ** oid" 12
the OID of the attribute (read\-only)
.IP "gnutls_datum_t * data" 12
the raw data of the attribute
.IP "unsigned flags" 12
zero or \fBGNUTLS_PKCS7_ATTR_ENCODE_OCTET_STRING\fP
.SH "DESCRIPTION"
This function will get a PKCS \fB7\fP attribute from the provided list.
The OID is a constant string, but data will be allocated and must be
deinitialized by the caller.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value. \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP is returned
if there are no data in the current index.
.SH "SINCE"
3.4.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
