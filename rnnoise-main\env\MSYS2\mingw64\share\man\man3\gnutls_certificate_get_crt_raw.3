.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_get_crt_raw" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_get_crt_raw \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_certificate_get_crt_raw(gnutls_certificate_credentials_t " sc ", unsigned " idx1 ", unsigned " idx2 ", gnutls_datum_t * " cert ");"
.SH ARGUMENTS
.IP "gnutls_certificate_credentials_t sc" 12
is a \fBgnutls_certificate_credentials_t\fP type.
.IP "unsigned idx1" 12
the index of the certificate chain if multiple are present
.IP "unsigned idx2" 12
the index of the certificate in the chain. Zero gives the server's certificate.
.IP "gnutls_datum_t * cert" 12
Will hold the DER encoded certificate.
.SH "DESCRIPTION"
This function will return the DER encoded certificate of the
server or any other certificate on its certificate chain (based on  \fIidx2\fP ).
The returned data should be treated as constant and only accessible during the lifetime
of  \fIsc\fP . The  \fIidx1\fP matches the value \fBgnutls_certificate_set_x509_key()\fP and friends
functions.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value. In case the indexes are out of bounds \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
is returned.
.SH "SINCE"
3.2.5
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
