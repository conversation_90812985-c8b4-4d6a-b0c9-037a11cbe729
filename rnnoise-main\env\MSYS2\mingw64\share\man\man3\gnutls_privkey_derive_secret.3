.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_derive_secret" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_derive_secret \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_derive_secret(gnutls_privkey_t " privkey ", gnutls_pubkey_t " pubkey ", const gnutls_datum_t * " nonce ", gnutls_datum_t * " secret ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t privkey" 12
a private key of type \fBgnutls_privkey_t\fP
.IP "gnutls_pubkey_t pubkey" 12
a public key of type \fBgnutls_pubkey_t\fP
.IP "const gnutls_datum_t * nonce" 12
an optional nonce value
.IP "gnutls_datum_t * secret" 12
where shared secret will be stored
.IP "unsigned int flags" 12
must be zero
.SH "DESCRIPTION"
This function will calculate a shared secret from our  \fIprivkey\fP and
peer's  \fIpubkey\fP . The result will be stored in  \fIsecret\fP , whose data
member should be freed after use using \fBgnutls_free()\fP.  \fIprivkey\fP and
 \fIpubkey\fP must be backed by the X.509 keys.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.8.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
