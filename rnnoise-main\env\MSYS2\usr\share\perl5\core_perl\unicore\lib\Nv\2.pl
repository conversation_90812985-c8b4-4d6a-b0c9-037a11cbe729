# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 15.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V280
50
51
178
179
1634
1635
1778
1779
1986
1987
2408
2409
2536
2537
2664
2665
2792
2793
2920
2921
3048
3049
3176
3177
3194
3195
3197
3198
3304
3305
3432
3433
3560
3561
3666
3667
3794
3795
3874
3875
4162
4163
4242
4243
4970
4971
6114
6115
6130
6131
6162
6163
6472
6473
6610
6611
6786
6787
6802
6803
6994
6995
7090
7091
7234
7235
7250
7251
8322
8323
8545
8546
8561
8562
9313
9314
9333
9334
9353
9354
9462
9463
10103
10104
10113
10114
10123
10124
12322
12323
12691
12692
12833
12834
12929
12930
13443
13444
20108
20109
20841
20842
24333
24334
24336
24337
36014
36015
36019
36020
36144
36145
42530
42531
42727
42728
43218
43219
43266
43267
43474
43475
43506
43507
43602
43603
44018
44019
63864
63865
65298
65299
65800
65801
65883
65887
66274
66275
66514
66515
66722
66723
67673
67674
67706
67707
67752
67753
67866
67867
68033
68034
68161
68162
68441
68442
68473
68474
68522
68523
68914
68915
69217
69218
69406
69407
69574
69575
69715
69716
69736
69737
69874
69875
69944
69945
70098
70099
70114
70115
70386
70387
70738
70739
70866
70867
71250
71251
71362
71363
71474
71475
71906
71907
72018
72019
72786
72787
72795
72796
73042
73043
73122
73123
73554
73555
74752
74753
74774
74775
74783
74784
74787
74788
74797
74798
74805
74806
74826
74827
74832
74833
74838
74839
74841
74842
92770
92771
92866
92867
93010
93011
93826
93827
93845
93846
119490
119491
119522
119523
119649
119650
119667
119668
120784
120785
120794
120795
120804
120805
120814
120815
120824
120825
123202
123203
123634
123635
124146
124147
125128
125129
125266
125267
126066
126067
126116
126117
126130
126131
126210
126211
126255
126256
127235
127236
130034
130035
140176
140177
END
