set ::msgcat::header "Project-Id-Version: git-gui\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2009-12-14 14:04+0100\nLast-Translator: <PERSON><PERSON><PERSON> <djsz<PERSON><EMAIL>>\nLanguage-Team: Hungarian\nLanguage: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\n"
::msgcat::mcset hu "Couldn't get list of unmerged files:" "Nem siker\u00fclt let\u00f6lteni az unmerged f\u00e1jl list\u00e1t:"
::msgcat::mcset hu "Error parsing revisions:" "Hiba t\u00f6rt\u00e9nt \u00e9rtelmez\u00e9s k\u00f6zben:"
::msgcat::mcset hu "Error executing --argscmd command:" "Hiba t\u00f6rt\u00e9nt a v\u00e9grehajt\u00e1skor --argscmd parancs:"
::msgcat::mcset hu "No files selected: --merge specified but no files are unmerged." "Nincsen f\u00e1jl kiv\u00e1lasztva: --merge megadve, de egyetlen f\u00e1jl sem unmerged."
::msgcat::mcset hu "No files selected: --merge specified but no unmerged files are within file limit." "Nincsen f\u00e1jl kiv\u00e1lasztva: --merge megadva, de nincsenek unmerged f\u00e1jlok a f\u00e1jlon bel\u00fcl limit."
::msgcat::mcset hu "Error executing git log:" "Hiba t\u00f6rt\u00e9nt a git log v\u00e9grehajt\u00e1sa k\u00f6zben:"
::msgcat::mcset hu "Reading" "Olvas\u00e1s"
::msgcat::mcset hu "Reading commits..." "Commitok olvas\u00e1sa ..."
::msgcat::mcset hu "No commits selected" "Nincsen commit kiv\u00e1lasztva"
::msgcat::mcset hu "Command line" "Parancs sor"
::msgcat::mcset hu "Can't parse git log output:" "Nem lehet \u00e9rtelmezni a git log kimenet\u00e9t:"
::msgcat::mcset hu "No commit information available" "Nincsen el\u00e9rhet\u0151 commit inform\u00e1ci\u00f3"
::msgcat::mcset hu "OK" "OK"
::msgcat::mcset hu "Cancel" "Visszavon\u00e1s"
::msgcat::mcset hu "&Update" "Frissit\u00e9s"
::msgcat::mcset hu "&Reload" "\u00dajrat\u00f6lt\u00e9s"
::msgcat::mcset hu "Reread re&ferences" "Referenci\u00e1k \u00fajraolvas\u00e1sa"
::msgcat::mcset hu "&List references" "Referenci\u00e1k list\u00e1z\u00e1sa"
::msgcat::mcset hu "Start git &gui" "Git gui ind\u00edt\u00e1sa"
::msgcat::mcset hu "&Quit" "Kil\u00e9p\u00e9s"
::msgcat::mcset hu "&File" "F\u00e1jl"
::msgcat::mcset hu "&Preferences" "Be\u00e1ll\u00edt\u00e1sok"
::msgcat::mcset hu "&Edit" "Szerkeszt\u00e9s"
::msgcat::mcset hu "&New view..." "\u00daj n\u00e9zet ..."
::msgcat::mcset hu "&Edit view..." "N\u00e9zet szerkeszt\u00e9se ..."
::msgcat::mcset hu "&Delete view" "N\u00e9zet t\u00f6rl\u00e9se"
::msgcat::mcset hu "&All files" "Minden f\u00e1jl"
::msgcat::mcset hu "&View" "N\u00e9zet"
::msgcat::mcset hu "&About gitk" "Gitk n\u00e9vjegy"
::msgcat::mcset hu "&Key bindings" "Billenty\u0171kombin\u00e1ci\u00f3"
::msgcat::mcset hu "&Help" "Seg\u00edts\u00e9g"
::msgcat::mcset hu "SHA1 ID:" "SHA1 ID:"
::msgcat::mcset hu "Row" "Sor"
::msgcat::mcset hu "Find" "Keres\u00e9s"
::msgcat::mcset hu "commit" "commit"
::msgcat::mcset hu "containing:" "tartalmaz\u00e1s:"
::msgcat::mcset hu "touching paths:" "\u00e9rintend\u0151 \u00fatvonalak:"
::msgcat::mcset hu "adding/removing string:" "string hozz\u00e1ad\u00e1sa/t\u00f6rl\u00e9se:"
::msgcat::mcset hu "Exact" "Pontos"
::msgcat::mcset hu "IgnCase" "Kis/nagy bet\u0171 nem sz\u00e1m\u00edt"
::msgcat::mcset hu "Regexp" "Regexp"
::msgcat::mcset hu "All fields" "Minden mez\u0151"
::msgcat::mcset hu "Headline" "F\u0151c\u00edm"
::msgcat::mcset hu "Comments" "Megjegyz\u00e9sek"
::msgcat::mcset hu "Author" "Szerz\u0151"
::msgcat::mcset hu "Committer" "Commitol\u00f3"
::msgcat::mcset hu "Search" "Keres\u00e9s"
::msgcat::mcset hu "Diff" "Diff"
::msgcat::mcset hu "Old version" "R\u00e9gi verzi\u00f3"
::msgcat::mcset hu "New version" "\u00daj verzi\u00f3"
::msgcat::mcset hu "Lines of context" "Tartalmi sorok"
::msgcat::mcset hu "Ignore space change" "Space v\u00e1lt\u00e1s mell\u0151z\u00e9se"
::msgcat::mcset hu "Patch" "Patch"
::msgcat::mcset hu "Tree" "Tree"
::msgcat::mcset hu "Diff this -> selected" "Diff ezeket -> kiv\u00e1lasztott"
::msgcat::mcset hu "Diff selected -> this" "Diff kiv\u00e1lasztottakat -> ezt"
::msgcat::mcset hu "Make patch" "Patch k\u00e9sz\u00edt\u00e9se"
::msgcat::mcset hu "Create tag" "Tag k\u00e9sz\u00edt\u00e9se"
::msgcat::mcset hu "Write commit to file" "Commit f\u00e1ljba \u00edr\u00e1sa"
::msgcat::mcset hu "Create new branch" "\u00daj branch k\u00e9sz\u00edt\u00e9se"
::msgcat::mcset hu "Cherry-pick this commit" "Cherry-pick erre a commitra"
::msgcat::mcset hu "Reset HEAD branch to here" "HEAD branch \u00fajraind\u00edt\u00e1sa ide"
::msgcat::mcset hu "Mark this commit" "Ezen commit megjel\u00f6l\u00e9se"
::msgcat::mcset hu "Return to mark" "Visszat\u00e9r\u00e9s a megjel\u00f6l\u00e9shez"
::msgcat::mcset hu "Find descendant of this and mark" "Tal\u00e1ld meg ezen ut\u00f3dokat \u00e9s jel\u00f6ld meg"
::msgcat::mcset hu "Compare with marked commit" "\u00d6sszehasonl\u00edt\u00e1s a megjel\u00f6lt commit-tal"
::msgcat::mcset hu "Check out this branch" "Check out ezt a branchot"
::msgcat::mcset hu "Remove this branch" "T\u00f6r\u00f6ld ezt a branch-ot"
::msgcat::mcset hu "Highlight this too" "Emeld ki ezt is"
::msgcat::mcset hu "Highlight this only" "Csak ezt emeld ki"
::msgcat::mcset hu "External diff" "K\u00fcls\u0151 diff"
::msgcat::mcset hu "Blame parent commit" "Blame sz\u00fcl\u0151 kommitra"
::msgcat::mcset hu "Show origin of this line" "Mutasd meg ennek a sornak az eredet\u00e9t"
::msgcat::mcset hu "Run git gui blame on this line" "Futtasd a git gui blame-t ezen a soron"
::msgcat::mcset hu "Close" "Bez\u00e1r\u00e1s"
::msgcat::mcset hu "Gitk key bindings" "Gitk-billenty\u0171 hozz\u00e1rendel\u00e9s"
::msgcat::mcset hu "Gitk key bindings:" "Gitk-billenty\u0171 hozza\u00e1rendel\u00e9s:"
::msgcat::mcset hu "<%s-Q>\u0009\u0009Quit" "<%s-Q>\u0009\u0009Kil\u00e9p\u00e9s"
::msgcat::mcset hu "<Home>\u0009\u0009Move to first commit" "<Pos1>\u0009\u0009Els\u0151 commithoz"
::msgcat::mcset hu "<End>\u0009\u0009Move to last commit" "<Ende>\u0009\u0009Utols\u00f3 commithoz"
::msgcat::mcset hu "<Right>, x, l\u0009Go forward in history list" "<Rechts>, x, l\u0009El\u0151re a history list\u00e1ba"
::msgcat::mcset hu "<PageUp>\u0009Move up one page in commit list" "<BildHoch>\u0009Egy lappal feljebb a commit list\u00e1ba"
::msgcat::mcset hu "<PageDown>\u0009Move down one page in commit list" "<BildRunter>\u0009Egy lappal lejjebb a commit list\u00e1ba"
::msgcat::mcset hu "<%s-Home>\u0009Scroll to top of commit list" "<%s-Pos1>\u0009G\u00f6rget\u00e9s a commit lista tetej\u00e9hez"
::msgcat::mcset hu "<%s-End>\u0009Scroll to bottom of commit list" "<%s-Ende>\u0009G\u00f6rget\u00e9s a commit lista alj\u00e1hoz"
::msgcat::mcset hu "<%s-Up>\u0009Scroll commit list up one line" "<%s-Hoch>\u0009Egy sorral feljebb g\u00f6rget\u00e9s a commit list\u00e1ban"
::msgcat::mcset hu "<%s-Down>\u0009Scroll commit list down one line" "<%s-Runter>\u0009Egy sorral lejjebb g\u00f6rget\u00e9s a commit list\u00e1ban"
::msgcat::mcset hu "<%s-PageUp>\u0009Scroll commit list up one page" "<%s-BildHoch>\u0009Egy lappal feljebb g\u00f6rget\u00e9s a commit list\u00e1ban"
::msgcat::mcset hu "<%s-PageDown>\u0009Scroll commit list down one page" "<%s-BildRunter>\u0009Egy sorral lejjebb g\u00f6rget\u00e9s a commit list\u00e1ban"
::msgcat::mcset hu "<Shift-Up>\u0009Find backwards (upwards, later commits)" "<Umschalt-Hoch>\u0009Keres\u00e9s visszafele (felfele, utols\u00f3 commitok)"
::msgcat::mcset hu "<Shift-Down>\u0009Find forwards (downwards, earlier commits)" "<Umschalt-Runter>\u0009Keres\u00e9s el\u0151re (lefel\u00e9; kor\u00e1bbi commitok)"
::msgcat::mcset hu "<Delete>, b\u0009Scroll diff view up one page" "<Entf>, b\u0009\u0009Egy lappal feljebb g\u00f6rget\u00e9s a diff n\u00e9zetben"
::msgcat::mcset hu "<Backspace>\u0009Scroll diff view up one page" "<L\u00f6schtaste>\u0009Egy lappal feljebb g\u00f6rget\u00e9s a diff n\u00e9zetben"
::msgcat::mcset hu "<Space>\u0009\u0009Scroll diff view down one page" "<Leertaste>\u0009Egy lappal lejjebb g\u00f6rget\u00e9s a diff n\u00e9zetben"
::msgcat::mcset hu "u\u0009\u0009Scroll diff view up 18 lines" "u\u0009\u000918 sorral felfel\u00e9 g\u00f6rget\u00e9s diff n\u00e9zetben"
::msgcat::mcset hu "d\u0009\u0009Scroll diff view down 18 lines" "d\u0009\u000918 sorral lejjebb g\u00f6rget\u00e9s a diff n\u00e9zetben"
::msgcat::mcset hu "<%s-F>\u0009\u0009Find" "<%s-F>\u0009\u0009Keres\u00e9s"
::msgcat::mcset hu "<%s-G>\u0009\u0009Move to next find hit" "<%s-G>\u0009\u0009K\u00f6vetkez\u0151 tal\u00e1lathoz"
::msgcat::mcset hu "<Return>\u0009Move to next find hit" "<Eingabetaste>\u0009K\u00f6vetkez\u0151 tal\u00e1lathoz"
::msgcat::mcset hu "/\u0009\u0009Focus the search box" "/\u0009\u0009L\u00e9pj a keres\u00e9si mez\u0151re"
::msgcat::mcset hu "?\u0009\u0009Move to previous find hit" "?\u0009\u0009El\u0151z\u0151 tal\u00e1lathoz"
::msgcat::mcset hu "f\u0009\u0009Scroll diff view to next file" "f\u0009\u0009K\u00f6vetkez\u0151 f\u00e1jlra g\u00f6rget\u00e9s diff n\u00e9zetben"
::msgcat::mcset hu "<%s-S>\u0009\u0009Search for next hit in diff view" "<%s-S>\u0009\u0009K\u00f6vetkez\u0151 tal\u00e1latra keres\u00e9s diff n\u00e9zetben"
::msgcat::mcset hu "<%s-R>\u0009\u0009Search for previous hit in diff view" "<%s-R>\u0009\u0009El\u0151z\u0151 tal\u00e1latra keres\u00e9s diff n\u00e9zetben"
::msgcat::mcset hu "<%s-KP+>\u0009Increase font size" "<%s-Nummerblock-Plus>\u0009Bet\u0171m\u00e9ret n\u00f6vel\u00e9se"
::msgcat::mcset hu "<%s-plus>\u0009Increase font size" "<%s-Plus>\u0009Bet\u0171m\u00e9ret n\u00f6vel\u00e9se"
::msgcat::mcset hu "<%s-KP->\u0009Decrease font size" "<%s-Nummernblock-Minus> Bet\u0171m\u00e9ret cs\u00f6kkent\u00e9se"
::msgcat::mcset hu "<%s-minus>\u0009Decrease font size" "<%s-Minus>\u0009Bet\u0171m\u00e9ret cs\u00f6kkent\u00e9se"
::msgcat::mcset hu "<F5>\u0009\u0009Update" "<F5>\u0009\u0009Friss\u00edt\u00e9s"
::msgcat::mcset hu "Error creating temporary directory %s:" "Hiba t\u00f6rt\u00e9nt az ideiglenes k\u00f6nyvt\u00e1r l\u00e9trehoz\u00e1sa k\u00f6zben %s:"
::msgcat::mcset hu "Error getting \"%s\" from %s:" "Hiba t\u00f6rt\u00e9nt \"%s\" let\u00f6lt\u00e9se k\u00f6zben %s-r\u0151l:"
::msgcat::mcset hu "command failed:" "parancs hiba:"
::msgcat::mcset hu "No such commit" "Nincs ilyen commit"
::msgcat::mcset hu "git gui blame: command failed:" "git gui blame: parancs hiba:"
::msgcat::mcset hu "Couldn't read merge head: %s" "Nem siker\u00fclt a Merge head olvas\u00e1sa: %s"
::msgcat::mcset hu "Error reading index: %s" "Hiba t\u00f6rt\u00e9nt az index olvas\u00e1sa k\u00f6zben: %s"
::msgcat::mcset hu "Couldn't start git blame: %s" "Nem siker\u00fclt a git blame ind\u00edt\u00e1sa: %s"
::msgcat::mcset hu "Searching" "Keres\u00e9s"
::msgcat::mcset hu "Error running git blame: %s" "Hiba t\u00f6rt\u00e9nt a git blame futtat\u00e1sa k\u00f6zben: %s"
::msgcat::mcset hu "That line comes from commit %s,  which is not in this view" "A %s commitb\u00f3l sz\u00e1rmazik az a sor, amelyik nem tal\u00e1lhat\u00f3 ebben a n\u00e9zetben"
::msgcat::mcset hu "External diff viewer failed:" "K\u00fcls\u0151 diff n\u00e9zeget\u0151 hiba:"
::msgcat::mcset hu "Gitk view definition" "Gitk n\u00e9zet meghat\u00e1roz\u00e1sa"
::msgcat::mcset hu "Remember this view" "Maradj enn\u00e9l a n\u00e9zetn\u00e9l"
::msgcat::mcset hu "References (space separated list):" "Referenci\u00e1k (sz\u00f3k\u00f6zzel tagolt lista"
::msgcat::mcset hu "Branches & tags:" "Branch-ek & tagek:"
::msgcat::mcset hu "All refs" "Minden ref"
::msgcat::mcset hu "All (local) branches" "Minden (helyi) branch"
::msgcat::mcset hu "All tags" "Minden tag"
::msgcat::mcset hu "All remote-tracking branches" "Minden t\u00e1voli k\u00f6vet\u0151 branch"
::msgcat::mcset hu "Commit Info (regular expressions):" "Commit Inf\u00f3 (regul\u00e1ris kifejez\u00e9s):"
::msgcat::mcset hu "Author:" "Szerz\u0151:"
::msgcat::mcset hu "Committer:" "Commitol\u00f3:"
::msgcat::mcset hu "Commit Message:" "Commit \u00fczenet:"
::msgcat::mcset hu "Matches all Commit Info criteria" "Egyezik minen Commit Inf\u00f3 felt\u00e9tellel"
::msgcat::mcset hu "Changes to Files:" "F\u00e1jl v\u00e1ltoz\u00e1sok:"
::msgcat::mcset hu "Fixed String" "Fix String"
::msgcat::mcset hu "Regular Expression" "Regul\u00e1ris kifejez\u00e9s"
::msgcat::mcset hu "Search string:" "Keres\u00e9s sz\u00f6veg:"
::msgcat::mcset hu "Commit Dates (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):" "Commit D\u00e1tumok (\"2 weeks ago\", \"2009-03-17 15:27:38\", \"March 17, 2009 15:27:38\"):"
::msgcat::mcset hu "Since:" "Ett\u0151l:"
::msgcat::mcset hu "Until:" "Eddig:"
::msgcat::mcset hu "Limit and/or skip a number of revisions (positive integer):" "Limit\u00e1lva \u00e9s/vagy kihagyva egy adott sz\u00e1m\u00fa rev\u00edzi\u00f3t (pozit\u00edv eg\u00e9sz):"
::msgcat::mcset hu "Number to show:" "Mutatand\u00f3 sz\u00e1m:"
::msgcat::mcset hu "Number to skip:" "Kihagyand\u00f3 sz\u00e1m:"
::msgcat::mcset hu "Miscellaneous options:" "K\u00fcl\u00f6nf\u00e9le opci\u00f3k:"
::msgcat::mcset hu "Strictly sort by date" "Szigor\u00fa rendez\u00e1s d\u00e1tum alapj\u00e1n"
::msgcat::mcset hu "Mark branch sides" "Jel\u00f6lje meg az \u00e1gakat"
::msgcat::mcset hu "Limit to first parent" "Korl\u00e1toz\u00e1s az els\u0151 sz\u00fcl\u0151re"
::msgcat::mcset hu "Simple history" "Egyszer\u0171 history"
::msgcat::mcset hu "Additional arguments to git log:" "Tov\u00e1bbi argumentok a git log-hoz:"
::msgcat::mcset hu "Enter files and directories to include, one per line:" "F\u00e1jlok \u00e9s k\u00f6nyvt\u00e1rak bejegyz\u00e9se amiket tartalmaz, soronk\u00e9nt:"
::msgcat::mcset hu "Command to generate more commits to include:" "Parancs t\u00f6bb tartalmaz\u00f3 commit gener\u00e1l\u00e1s\u00e1ra:"
::msgcat::mcset hu "Gitk: edit view" "Gitk: szerkeszt\u00e9s n\u00e9zet"
::msgcat::mcset hu "-- criteria for selecting revisions" "-- krit\u00e9riumok a rev\u00edzi\u00f3k kiv\u00e1laszt\u00e1s\u00e1hoz"
::msgcat::mcset hu "View Name" "N\u00e9zet neve"
::msgcat::mcset hu "Apply (F5)" "Alkalmaz (F5)"
::msgcat::mcset hu "Error in commit selection arguments:" "Hiba t\u00f6rt\u00e9nt a commit argumentumok kiv\u00e1laszt\u00e1sa k\u00f6zben:"
::msgcat::mcset hu "None" "Keine"
::msgcat::mcset hu "Descendant" "Lesz\u00e1rmazott"
::msgcat::mcset hu "Not descendant" "Nem lesz\u00e1rmazott"
::msgcat::mcset hu "Ancestor" "El\u0151d"
::msgcat::mcset hu "Not ancestor" "Nem el\u0151d"
::msgcat::mcset hu "Local changes checked in to index but not committed" "Lok\u00e1lis v\u00e1ltoztat\u00e1sok, melyek be vannak t\u00e9ve az indexbe, de m\u00e9g nincsenek commitolva"
::msgcat::mcset hu "Local uncommitted changes, not checked in to index" "Lok\u00e1lis nem commitolt v\u00e1ltoz\u00e1sok, nincsenek bet\u00e9ve az indexbe"
::msgcat::mcset hu "many" "sok"
::msgcat::mcset hu "Tags:" "Tagek:"
::msgcat::mcset hu "Parent" "Eltern"
::msgcat::mcset hu "Child" "Gyerek"
::msgcat::mcset hu "Branch" "\u00c1g"
::msgcat::mcset hu "Follows" "K\u00f6vetkez\u0151k"
::msgcat::mcset hu "Precedes" "Megel\u0151z\u0151k"
::msgcat::mcset hu "Error getting diffs: %s" "Hiba t\u00f6rt\u00e9nt a diff-ek let\u00f6lt\u00e9se k\u00f6zben: %s"
::msgcat::mcset hu "Goto:" "Menj:"
::msgcat::mcset hu "Short SHA1 id %s is ambiguous" "R\u00f6vid SHA1 id %s f\u00e9lre\u00e9rthet\u0151"
::msgcat::mcset hu "Revision %s is not known" "A(z) %s rev\u00edzi\u00f3 nem ismert"
::msgcat::mcset hu "SHA1 id %s is not known" "SHA1 id %s nem ismert"
::msgcat::mcset hu "Revision %s is not in the current view" "A(z) %s rev\u00edzi\u00f3 nincs a jelenlegi n\u00e9zetben"
::msgcat::mcset hu "Date" "D\u00e1tum"
::msgcat::mcset hu "Children" "Gyerekek"
::msgcat::mcset hu "Reset %s branch to here" "\u00c1ll\u00edtsd vissza a %s branch-ot ide"
::msgcat::mcset hu "Detached head: can't reset" "Elk\u00fcl\u00f6n\u00edtett head: nem lehet vissza\u00e1ll\u00edtani"
::msgcat::mcset hu "Skipping merge commit " "Merge commit kihagy\u00e1sa "
::msgcat::mcset hu "Error getting patch ID for " "Hiba t\u00f6rt\u00e9nt a patch ID megszerz\u00e9se k\u00f6zben a k\u00f6vetkez\u0151n\u00e9l "
::msgcat::mcset hu " - stopping\n" " - abbahagy\u00e1s\n"
::msgcat::mcset hu "Commit " "Commit "
::msgcat::mcset hu " is the same patch as\n       " " Ugyanaz a patch mint\n       "
::msgcat::mcset hu " differs from\n       " " k\u00fcl\u00f6nb\u00f6zik innent\u0151l\n       "
::msgcat::mcset hu "Diff of commits:\n\n" "A commitok diffje:\n\n"
::msgcat::mcset hu " has %s children - stopping\n" " %s gyereke van. abbahagy\u00e1s\n"
::msgcat::mcset hu "Error writing commit to file: %s" "Hiba t\u00f6rt\u00e9nt a commit f\u00e1jlba \u00edr\u00e1sa k\u00f6zben: %s"
::msgcat::mcset hu "Error diffing commits: %s" "Hiba t\u00f6rt\u00e9nt a commitok diffel\u00e9se k\u00f6zben: %s"
::msgcat::mcset hu "Top" "Teteje"
::msgcat::mcset hu "From" "Innen"
::msgcat::mcset hu "To" "Ide"
::msgcat::mcset hu "Generate patch" "Patch gener\u00e1l\u00e1sa"
::msgcat::mcset hu "From:" "Innen:"
::msgcat::mcset hu "To:" "Ide:"
::msgcat::mcset hu "Reverse" "Visszafele"
::msgcat::mcset hu "Output file:" "Kimeneti f\u00e1jl:"
::msgcat::mcset hu "Generate" "Gener\u00e1l\u00e1s"
::msgcat::mcset hu "Error creating patch:" "Hiba t\u00f6rt\u00e9t a patch k\u00e9sz\u00edt\u00e9se k\u00f6zben:"
::msgcat::mcset hu "ID:" "ID:"
::msgcat::mcset hu "Tag name:" "Tag n\u00e9v:"
::msgcat::mcset hu "Create" "L\u00e9trehoz\u00e1s"
::msgcat::mcset hu "No tag name specified" "A tag neve nincsen megadva"
::msgcat::mcset hu "Tag \"%s\" already exists" "%s Tag m\u00e1r l\u00e9tezik"
::msgcat::mcset hu "Error creating tag:" "Hiba t\u00f6rt\u00e9nt a tag l\u00e9trehoz\u00e1sa k\u00f6zben:"
::msgcat::mcset hu "Command:" "Parancs:"
::msgcat::mcset hu "Write" "\u00cdr\u00e1s"
::msgcat::mcset hu "Error writing commit:" "Hiba t\u00f6rt\u00e9nt a commit \u00edr\u00e1sa k\u00f6zben:"
::msgcat::mcset hu "Name:" "N\u00e9v:"
::msgcat::mcset hu "Please specify a name for the new branch" "K\u00e9rem adja meg a nev\u00e9t az \u00faj branchhoz"
::msgcat::mcset hu "Branch '%s' already exists. Overwrite?" "%s branch m\u00e1r l\u00e9tezik. Fel\u00fcl\u00edrja?"
::msgcat::mcset hu "Commit %s is already included in branch %s -- really re-apply it?" "%s commit m\u00e1r benne van a %s branchban -- biztos hogy \u00fajra csin\u00e1lja ?eintragen?"
::msgcat::mcset hu "Cherry-picking" "Cherry-picking"
::msgcat::mcset hu "Cherry-pick failed because of local changes to file '%s'.\nPlease commit, reset or stash your changes and try again." "Cherry-pick hiba t\u00f6rt\u00e9nt lok\u00e1lis v\u00e1ltot\u00e1sok miatt a '%s' f\u00e1jlban.\nK\u00e9rem commitolja, ind\u00edtsa \u00fajra vagy rejtse el a v\u00e1ltoztat\u00e1sait \u00e9s pr\u00f3b\u00e1lja \u00fajra."
::msgcat::mcset hu "Cherry-pick failed because of merge conflict.\nDo you wish to run git citool to resolve it?" "Cherry-pick hiba t\u00f6rt\u00e9nt merge konfliktus miatt.\nK\u00edv\u00e1nja futtatni a git citool-t a probl\u00e9ma megold\u00e1s\u00e1hoz?"
::msgcat::mcset hu "No changes committed" "Nincsen v\u00e1ltoz\u00e1s commitolva"
::msgcat::mcset hu "Confirm reset" "\u00dajraind\u00edt\u00e1s meger\u0151s\u00edt\u00e9se"
::msgcat::mcset hu "Reset branch %s to %s?" "\u00dajraind\u00edtja a %s branchot %s-ig?"
::msgcat::mcset hu "Reset type:" "\u00dajraind\u00edt\u00e1s t\u00edpusa:"
::msgcat::mcset hu "Soft: Leave working tree and index untouched" "Soft: Hagyd a working tree-t \u00e9s az indexet \u00e9rintetlen\u00fcl"
::msgcat::mcset hu "Mixed: Leave working tree untouched, reset index" "Kevert: Hagyd a working tree-t \u00e9rintetlen\u00fcl, t\u00f6r\u00f6ld az indexet"
::msgcat::mcset hu "Hard: Reset working tree and index\n(discard ALL local changes)" "Hard: Ind\u00edtsd \u00fajra a working tree-t \u00e9s az indexet\n(MINDEN lok\u00e1lis v\u00e1ltoz\u00e1s eldob\u00e1sa)"
::msgcat::mcset hu "Resetting" "\u00dajraind\u00edt\u00e1s"
::msgcat::mcset hu "Checking out" "Kivesz"
::msgcat::mcset hu "Cannot delete the currently checked-out branch" "Nem lehet a jelenleg kivett branch-ot t\u00f6r\u00f6lni"
::msgcat::mcset hu "The commits on branch %s aren't on any other branch.\nReally delete branch %s?" "A %s branchon tal\u00e1lhat\u00f3 commit nem tal\u00e1lhat\u00f3 meg semelyik m\u00e1sik branchon.\nT\u00e9nyleg t\u00f6rli a %s branchot?"
::msgcat::mcset hu "Tags and heads: %s" "Tagek \u00e9s headek: %s"
::msgcat::mcset hu "Filter" "Sz\u0171r\u0151"
::msgcat::mcset hu "Error reading commit topology information; branch and preceding/following tag information will be incomplete." "Hiba t\u00f6rt\u00e9nt a commit topol\u00f3giai inform\u00e1ci\u00f3 olvas\u00e1sa k\u00f6zben; branch \u00e9sa megel\u0151z\u0151/k\u00f6vetkez\u0151 inform\u00e1ci\u00f3 nem lesz teljes."
::msgcat::mcset hu "Tag" "Tag"
::msgcat::mcset hu "Id" "Id"
::msgcat::mcset hu "Gitk font chooser" "Gitk-bet\u0171 kiv\u00e1laszt\u00f3"
::msgcat::mcset hu "B" "F"
::msgcat::mcset hu "I" "K"
::msgcat::mcset hu "Commit list display options" "Commit lista kijelz\u00e9si opci\u00f3k"
::msgcat::mcset hu "Maximum graph width (lines)" "Maxim\u00e1lis grafikon sz\u00e9less\u00e9g (sorok)"
::msgcat::mcset hu "Maximum graph width (% of pane)" "Maxim\u00e1lis grafikon sz\u00e9less\u00e9g (t\u00e1ble %-je)"
::msgcat::mcset hu "Show local changes" "Mutasd a lok\u00e1lis v\u00e1ltoztat\u00e1sokat"
::msgcat::mcset hu "Hide remote refs" "A t\u00e1voli refek elrejt\u00e9se"
::msgcat::mcset hu "Diff display options" "Diff kijelz\u0151 opci\u00f3k"
::msgcat::mcset hu "Tab spacing" "Tab sork\u00f6z"
::msgcat::mcset hu "Limit diffs to listed paths" "Korl\u00e1tozott diffek a kilist\u00e1zott \u00fatvonalakhoz"
::msgcat::mcset hu "Support per-file encodings" "F\u00e1jlonk\u00e9nti k\u00f3dol\u00e1s t\u00e1mgat\u00e1sa"
::msgcat::mcset hu "External diff tool" "K\u00fcls\u0151 diff alkalmaz\u00e1s"
::msgcat::mcset hu "Choose..." "V\u00e1laszd ..."
::msgcat::mcset hu "General options" "\u00c1ltal\u00e1nos opci\u00f3k"
::msgcat::mcset hu "Use themed widgets" "T\u00e9m\u00e1zott vez\u00e9rl\u0151k haszn\u00e1lata"
::msgcat::mcset hu "(change requires restart)" "(a v\u00e1ltoz\u00e1s \u00fajraind\u00edt\u00e1st ig\u00e9nyel)"
::msgcat::mcset hu "(currently unavailable)" "(jelenleg nem el\u00e9rhet\u0151)"
::msgcat::mcset hu "Colors: press to choose" "Sz\u00ednek: nyomja meg a kiv\u00e1laszt\u00e1shoz"
::msgcat::mcset hu "Interface" "Interf\u00e9sz"
::msgcat::mcset hu "interface" "interf\u00e9sz"
::msgcat::mcset hu "Background" "H\u00e1tt\u00e9r"
::msgcat::mcset hu "background" "h\u00e1tt\u00e9r"
::msgcat::mcset hu "Foreground" "El\u0151t\u00e9r"
::msgcat::mcset hu "foreground" "el\u0151t\u00e9r"
::msgcat::mcset hu "Diff: old lines" "Diff: r\u00e9gi sorok"
::msgcat::mcset hu "diff old lines" "diff r\u00e9gi sorok"
::msgcat::mcset hu "Diff: new lines" "Diff: \u00faj sorok"
::msgcat::mcset hu "diff new lines" "diff - \u00faj sorok"
::msgcat::mcset hu "Diff: hunk header" "Diff: nagy header\u00f6k"
::msgcat::mcset hu "diff hunk header" "diff - nagy header\u00f6k"
::msgcat::mcset hu "Marked line bg" "Megjel\u00f6lt sor h\u00e1tt\u00e9r"
::msgcat::mcset hu "marked line background" "megjel\u00f6lt sor h\u00e1tt\u00e9r"
::msgcat::mcset hu "Select bg" "V\u00e1lasszon h\u00e1tteret"
::msgcat::mcset hu "Fonts: press to choose" "Bet\u0171: nyomja meg a kiv\u00e1laszt\u00e1shoz"
::msgcat::mcset hu "Main font" "F\u0151 bet\u0171"
::msgcat::mcset hu "Diff display font" "Diff kijelz\u0151 bet\u0171"
::msgcat::mcset hu "User interface font" "Felhaszn\u00e1l\u00f3i interf\u00e9sz bet\u0171"
::msgcat::mcset hu "Gitk preferences" "Gitk be\u00e1ll\u00edt\u00e1sok"
::msgcat::mcset hu "Gitk: choose color for %s" "Gitk: v\u00e1lasszon sz\u00ednt a %s-ra"
::msgcat::mcset hu "Sorry, gitk cannot run with this version of Tcl/Tk.\n Gitk requires at least Tcl/Tk 8.4." "Sajn\u00e1ljuk, de a gitk nem futtathat\u00f3 ezzel a Tcl/Tk verzi\u00f3val.\nGitk futtat\u00e1s\u00e1hoz legal\u00e1bb Tcl/Tk 8.4 sz\u00fcks\u00e9ges."
::msgcat::mcset hu "Cannot find a git repository here." "Nem tal\u00e1lhat\u00fc git repository itt."
::msgcat::mcset hu "Ambiguous argument '%s': both revision and filename" "F\u00e9lre\u00e9rthet\u0151 argumentum '%s': rev\u00edzi\u00f3 \u00e9s f\u00e1jln\u00e9v is"
::msgcat::mcset hu "Bad arguments to gitk:" "Rossz gitk argumentumok:"
