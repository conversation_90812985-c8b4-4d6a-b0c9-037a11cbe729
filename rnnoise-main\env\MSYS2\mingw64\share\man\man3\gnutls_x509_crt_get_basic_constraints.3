.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_basic_constraints" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_basic_constraints \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_basic_constraints(gnutls_x509_crt_t " cert ", unsigned int * " critical ", unsigned int * " ca ", int * " pathlen ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "unsigned int * critical" 12
will be non\-zero if the extension is marked as critical
.IP "unsigned int * ca" 12
pointer to output integer indicating CA status, may be NULL,
value is 1 if the certificate CA flag is set, 0 otherwise.
.IP "int * pathlen" 12
pointer to output integer indicating path length (may be
NULL), non\-negative error codes indicate a present pathLenConstraint
field and the actual value, \-1 indicate that the field is absent.
.SH "DESCRIPTION"
This function will read the certificate's basic constraints, and
return the certificates CA status.  It reads the basicConstraints
X.509 extension (*********).
.SH "RETURNS"
If the certificate is a CA a positive value will be
returned, or (0) if the certificate does not have CA flag set.  A
negative error code may be returned in case of errors.  If the
certificate does not contain the basicConstraints extension
GNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE will be returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
