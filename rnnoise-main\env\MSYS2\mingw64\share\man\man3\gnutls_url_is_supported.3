.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_url_is_supported" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_url_is_supported \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "unsigned gnutls_url_is_supported(const char * " url ");"
.SH ARGUMENTS
.IP "const char * url" 12
A URI to be tested
.SH "DESCRIPTION"
Check whether the provided  \fIurl\fP is supported.  Depending on the system libraries
GnuTLS may support pkcs11, tpmkey or other URLs.
.SH "RETURNS"
return non\-zero if the given URL is supported, and zero if
it is not known.
.SH "SINCE"
3.1.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
