.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_subject" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_subject \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_subject(gnutls_x509_crt_t " cert ", gnutls_x509_dn_t * " dn ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "gnutls_x509_dn_t * dn" 12
output variable with pointer to uint8_t DN.
.SH "DESCRIPTION"
Return the Certificate's Subject DN as a \fBgnutls_x509_dn_t\fP data type,
that can be decoded using \fBgnutls_x509_dn_get_rdn_ava()\fP.

Note that  \fIdn\fP should be treated as constant. Because it points
into the  \fIcert\fP object, you should not use  \fIdn\fP after  \fIcert\fP is
deallocated.
.SH "RETURNS"
Returns 0 on success, or an error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
