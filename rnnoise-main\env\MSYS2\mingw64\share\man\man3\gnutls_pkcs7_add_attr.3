.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs7_add_attr" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs7_add_attr \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs7.h>
.sp
.BI "int gnutls_pkcs7_add_attr(gnutls_pkcs7_attrs_t * " list ", const char * " oid ", gnutls_datum_t * " data ", unsigned " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs7_attrs_t * list" 12
A list of existing attributes or pointer to \fBNULL\fP for the first one
.IP "const char * oid" 12
the OID of the attribute to be set
.IP "gnutls_datum_t * data" 12
the raw (DER\-encoded) data of the attribute to be set
.IP "unsigned flags" 12
zero or \fBGNUTLS_PKCS7_ATTR_ENCODE_OCTET_STRING\fP
.SH "DESCRIPTION"
This function will set a PKCS \fB7\fP attribute in the provided list.
If this function fails, the previous list would be deallocated.

Note that any attributes set with this function must either be
DER or BER encoded, unless a special flag is present.
.SH "RETURNS"
On success, the new list head, otherwise \fBNULL\fP.
.SH "SINCE"
3.4.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
