.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_inhibit_anypolicy" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_inhibit_anypolicy \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_get_inhibit_anypolicy(gnutls_x509_crt_t " cert ", unsigned int * " skipcerts ", unsigned int * " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.IP "unsigned int * skipcerts" 12
will hold the number of certificates after which anypolicy is no longer acceptable.
.IP "unsigned int * critical" 12
will be non\-zero if the extension is marked as critical
.SH "DESCRIPTION"
This function will return certificate's value of the SkipCerts, i.e.,
the Inhibit anyPolicy X.509 extension (*********).

The returned value is the number of additional certificates that
may appear in the path before the anyPolicy is no longer acceptable.
.SH "RETURNS"
zero on success, or a negative error code in case of
parsing error.  If the certificate does not contain the Inhibit anyPolicy
extension \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP will be
returned.
.SH "SINCE"
3.6.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
