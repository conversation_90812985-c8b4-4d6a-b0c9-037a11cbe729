.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_hkdf_expand" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_hkdf_expand \- API function
.SH SYNOPSIS
.B #include <gnutls/crypto.h>
.sp
.BI "int gnutls_hkdf_expand(gnutls_mac_algorithm_t " mac ", const gnutls_datum_t * " key ", const gnutls_datum_t * " info ", void * " output ", size_t " length ");"
.SH ARGUMENTS
.IP "gnutls_mac_algorithm_t mac" 12
the mac algorithm used internally
.IP "const gnutls_datum_t * key" 12
the pseudorandom key created with HKDF\-Extract
.IP "const gnutls_datum_t * info" 12
the optional informational data
.IP "void * output" 12
the output value of the expand operation
.IP "size_t length" 12
the desired length of the output key
.SH "DESCRIPTION"
This function will derive a variable length keying material from
the pseudorandom key using the HKDF\-Expand function as defined in
RFC 5869.
.SH "RETURNS"
Zero or a negative error code on error.
.SH "SINCE"
3.6.13
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
