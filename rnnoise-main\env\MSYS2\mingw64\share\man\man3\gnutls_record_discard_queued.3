.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_record_discard_queued" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_record_discard_queued \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "size_t gnutls_record_discard_queued(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
This function discards all queued to be sent packets in a DTLS session.
These are the packets queued after an interrupted \fBgnutls_record_send()\fP.

This function can only be used with transports where \fBsend()\fP is
an all\-or\-nothing operation (e.g., UDP). When partial writes are allowed
this function will cause session errors.
.SH "RETURNS"
The number of bytes discarded.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
