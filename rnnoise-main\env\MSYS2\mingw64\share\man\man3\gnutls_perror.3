.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_perror" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_perror \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "void gnutls_perror(int " error ");"
.SH ARGUMENTS
.IP "int error" 12
is a GnuTLS error code, a negative error code
.SH "DESCRIPTION"
This function is like \fBperror()\fP. The only difference is that it
accepts an error number returned by a gnutls function.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
