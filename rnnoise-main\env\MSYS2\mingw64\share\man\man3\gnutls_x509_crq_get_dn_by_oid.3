.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_dn_by_oid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_dn_by_oid \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_dn_by_oid(gnutls_x509_crq_t " crq ", const char * " oid ", unsigned " indx ", unsigned int " raw_flag ", void * " buf ", size_t * " buf_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a gnutls_x509_crq_t type
.IP "const char * oid" 12
holds an Object Identifier in a null terminated string
.IP "unsigned indx" 12
In case multiple same OIDs exist in the RDN, this specifies
which to get. Use (0) to get the first one.
.IP "unsigned int raw_flag" 12
If non\-zero returns the raw DER data of the DN part.
.IP "void * buf" 12
a pointer to a structure to hold the name (may be \fBNULL\fP)
.IP "size_t * buf_size" 12
initially holds the size of  \fIbuf\fP 
.SH "DESCRIPTION"
This function will extract the part of the name of the Certificate
request subject, specified by the given OID. The output will be
encoded as described in RFC2253. The output string will be ASCII
or UTF\-8 encoded, depending on the certificate data.

Some helper macros with popular OIDs can be found in gnutls/x509.h
If raw flag is (0), this function will only return known OIDs as
text. Other OIDs will be DER encoded, as described in RFC2253 \-\-
in hex format with a '\#' prefix.  You can check about known OIDs
using \fBgnutls_x509_dn_oid_known()\fP.
.SH "RETURNS"
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP if the provided buffer is
not long enough, and in that case the * \fIbuf_size\fP will be
updated with the required size.  On success 0 is returned.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
