/*
 * Copyright 2013 <PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "ocidl.idl";
import "d3d11.idl";

const UINT D3D11_DEBUG_FEATURE_FLUSH_PER_RENDER_OP = 0x1;
const UINT D3D11_DEBUG_FEATURE_FINISH_PER_RENDER_OP = 0x2;
const UINT D3D11_DEBUG_FEATURE_PRESENT_PER_RENDER_OP = 0x4;
const UINT D3D11_DEBUG_FEATURE_ALWAYS_DISCARD_OFFERED_RESOURCE = 0x8;
const UINT D3D11_DEBUG_FEATURE_NEVER_DISCARD_OFFERED_RESOURCE = 0x10;
const UINT D3D11_DEBUG_FEATURE_AVOID_BEHAVIOR_CHANGING_DEBUG_AIDS = 0x40;
const UINT D3D11_DEBUG_FEATURE_DISABLE_TILED_RESOURCE_MAPPING_TRACKING_AND_VALIDATION = 0x80;

cpp_quote("DEFINE_GUID(DXGI_DEBUG_D3D11, 0x4b99317b, 0xac39, 0x4aa6, 0xbb, 0xb, 0xba, 0xa0, 0x47, 0x84, 0x79, 0x8f);")

typedef enum D3D11_MESSAGE_CATEGORY {
    D3D11_MESSAGE_CATEGORY_APPLICATION_DEFINED,
    D3D11_MESSAGE_CATEGORY_MISCELLANEOUS,
    D3D11_MESSAGE_CATEGORY_INITIALIZATION,
    D3D11_MESSAGE_CATEGORY_CLEANUP,
    D3D11_MESSAGE_CATEGORY_COMPILATION,
    D3D11_MESSAGE_CATEGORY_STATE_CREATION,
    D3D11_MESSAGE_CATEGORY_STATE_SETTING,
    D3D11_MESSAGE_CATEGORY_STATE_GETTING,
    D3D11_MESSAGE_CATEGORY_RESOURCE_MANIPULATION,
    D3D11_MESSAGE_CATEGORY_EXECUTION,
    D3D11_MESSAGE_CATEGORY_SHADER
} D3D11_MESSAGE_CATEGORY;

typedef enum D3D11_MESSAGE_SEVERITY {
    D3D11_MESSAGE_SEVERITY_CORRUPTION,
    D3D11_MESSAGE_SEVERITY_ERROR,
    D3D11_MESSAGE_SEVERITY_WARNING,
    D3D11_MESSAGE_SEVERITY_INFO,
    D3D11_MESSAGE_SEVERITY_MESSAGE
} D3D11_MESSAGE_SEVERITY;

typedef enum D3D11_MESSAGE_ID {
    D3D11_MESSAGE_ID_UNKNOWN = 0,
    D3D11_MESSAGE_ID_DEVICE_IASETVERTEXBUFFERS_HAZARD,
    D3D11_MESSAGE_ID_DEVICE_IASETINDEXBUFFER_HAZARD,
    D3D11_MESSAGE_ID_DEVICE_VSSETSHADERRESOURCES_HAZARD,
    D3D11_MESSAGE_ID_DEVICE_VSSETCONSTANTBUFFERS_HAZARD,
    D3D11_MESSAGE_ID_DEVICE_GSSETSHADERRESOURCES_HAZARD,
    D3D11_MESSAGE_ID_DEVICE_GSSETCONSTANTBUFFERS_HAZARD,
    D3D11_MESSAGE_ID_DEVICE_PSSETSHADERRESOURCES_HAZARD,
    D3D11_MESSAGE_ID_DEVICE_PSSETCONSTANTBUFFERS_HAZARD,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETS_HAZARD,
    D3D11_MESSAGE_ID_DEVICE_SOSETTARGETS_HAZARD,
    D3D11_MESSAGE_ID_STRING_FROM_APPLICATION,
    D3D11_MESSAGE_ID_CORRUPTED_THIS,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER1,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER2,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER3,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER4,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER5,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER6,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER7,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER8,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER9,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER10,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER11,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER12,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER13,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER14,
    D3D11_MESSAGE_ID_CORRUPTED_PARAMETER15,
    D3D11_MESSAGE_ID_CORRUPTED_MULTITHREADING,
    D3D11_MESSAGE_ID_MESSAGE_REPORTING_OUTOFMEMORY,
    D3D11_MESSAGE_ID_IASETINPUTLAYOUT_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_IASETVERTEXBUFFERS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_IASETINDEXBUFFER_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_VSSETSHADER_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_VSSETSHADERRESOURCES_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_VSSETCONSTANTBUFFERS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_VSSETSAMPLERS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_GSSETSHADER_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_GSSETSHADERRESOURCES_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_GSSETCONSTANTBUFFERS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_GSSETSAMPLERS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_SOSETTARGETS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_PSSETSHADER_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_PSSETSHADERRESOURCES_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_PSSETCONSTANTBUFFERS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_PSSETSAMPLERS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_RSSETSTATE_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_OMSETBLENDSTATE_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_OMSETDEPTHSTENCILSTATE_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_OMSETRENDERTARGETS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_SETPREDICATION_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_GETPRIVATEDATA_MOREDATA,
    D3D11_MESSAGE_ID_SETPRIVATEDATA_INVALIDFREEDATA,
    D3D11_MESSAGE_ID_SETPRIVATEDATA_INVALIDIUNKNOWN,
    D3D11_MESSAGE_ID_SETPRIVATEDATA_INVALIDFLAGS,
    D3D11_MESSAGE_ID_SETPRIVATEDATA_CHANGINGPARAMS,
    D3D11_MESSAGE_ID_SETPRIVATEDATA_OUTOFMEMORY,
    D3D11_MESSAGE_ID_CREATEBUFFER_UNRECOGNIZEDFORMAT,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDSAMPLES,
    D3D11_MESSAGE_ID_CREATEBUFFER_UNRECOGNIZEDUSAGE,
    D3D11_MESSAGE_ID_CREATEBUFFER_UNRECOGNIZEDBINDFLAGS,
    D3D11_MESSAGE_ID_CREATEBUFFER_UNRECOGNIZEDCPUACCESSFLAGS,
    D3D11_MESSAGE_ID_CREATEBUFFER_UNRECOGNIZEDMISCFLAGS,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDCPUACCESSFLAGS,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDBINDFLAGS,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDINITIALDATA,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDDIMENSIONS,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDMIPLEVELS,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDMISCFLAGS,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDARG_RETURN,
    D3D11_MESSAGE_ID_CREATEBUFFER_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATEBUFFER_NULLDESC,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDCONSTANTBUFFERBINDINGS,
    D3D11_MESSAGE_ID_CREATEBUFFER_LARGEALLOCATION,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_UNRECOGNIZEDFORMAT,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_UNSUPPORTEDFORMAT,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDSAMPLES,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_UNRECOGNIZEDUSAGE,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_UNRECOGNIZEDBINDFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_UNRECOGNIZEDCPUACCESSFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_UNRECOGNIZEDMISCFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDCPUACCESSFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDBINDFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDINITIALDATA,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDDIMENSIONS,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDMIPLEVELS,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDMISCFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_INVALIDARG_RETURN,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_NULLDESC,
    D3D11_MESSAGE_ID_CREATETEXTURE1D_LARGEALLOCATION,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_UNRECOGNIZEDFORMAT,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_UNSUPPORTEDFORMAT,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDSAMPLES,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_UNRECOGNIZEDUSAGE,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_UNRECOGNIZEDBINDFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_UNRECOGNIZEDCPUACCESSFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_UNRECOGNIZEDMISCFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDCPUACCESSFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDBINDFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDINITIALDATA,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDDIMENSIONS,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDMIPLEVELS,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDMISCFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_INVALIDARG_RETURN,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_NULLDESC,
    D3D11_MESSAGE_ID_CREATETEXTURE2D_LARGEALLOCATION,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_UNRECOGNIZEDFORMAT,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_UNSUPPORTEDFORMAT,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDSAMPLES,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_UNRECOGNIZEDUSAGE,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_UNRECOGNIZEDBINDFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_UNRECOGNIZEDCPUACCESSFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_UNRECOGNIZEDMISCFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDCPUACCESSFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDBINDFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDINITIALDATA,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDDIMENSIONS,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDMIPLEVELS,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDMISCFLAGS,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_INVALIDARG_RETURN,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_NULLDESC,
    D3D11_MESSAGE_ID_CREATETEXTURE3D_LARGEALLOCATION,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_UNRECOGNIZEDFORMAT,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDDESC,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDFORMAT,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDDIMENSIONS,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDRESOURCE,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_TOOMANYOBJECTS,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDARG_RETURN,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_UNRECOGNIZEDFORMAT,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_UNSUPPORTEDFORMAT,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDDESC,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDFORMAT,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDDIMENSIONS,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDRESOURCE,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_TOOMANYOBJECTS,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDARG_RETURN,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_UNRECOGNIZEDFORMAT,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDDESC,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDFORMAT,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDDIMENSIONS,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDRESOURCE,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_TOOMANYOBJECTS,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDARG_RETURN,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_OUTOFMEMORY,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_TOOMANYELEMENTS,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDFORMAT,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INCOMPATIBLEFORMAT,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSLOT,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDINPUTSLOTCLASS,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_STEPRATESLOTCLASSMISMATCH,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSLOTCLASSCHANGE,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSTEPRATECHANGE,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDALIGNMENT,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_DUPLICATESEMANTIC,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_UNPARSEABLEINPUTSIGNATURE,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_NULLSEMANTIC,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_MISSINGELEMENT,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_NULLDESC,
    D3D11_MESSAGE_ID_CREATEVERTEXSHADER_OUTOFMEMORY,
    D3D11_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDSHADERBYTECODE,
    D3D11_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDSHADERTYPE,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADER_OUTOFMEMORY,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDSHADERBYTECODE,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDSHADERTYPE,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTOFMEMORY,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSHADERBYTECODE,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSHADERTYPE,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDNUMENTRIES,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTPUTSTREAMSTRIDEUNUSED,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDDECL,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_EXPECTEDDECL,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTPUTSLOT0EXPECTED,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDOUTPUTSLOT,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_ONLYONEELEMENTPERSLOT,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDCOMPONENTCOUNT,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTARTCOMPONENTANDCOMPONENTCOUNT,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDGAPDEFINITION,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_REPEATEDOUTPUT,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDOUTPUTSTREAMSTRIDE,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MISSINGSEMANTIC,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MASKMISMATCH,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_CANTHAVEONLYGAPS,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DECLTOOCOMPLEX,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MISSINGOUTPUTSIGNATURE,
    D3D11_MESSAGE_ID_CREATEPIXELSHADER_OUTOFMEMORY,
    D3D11_MESSAGE_ID_CREATEPIXELSHADER_INVALIDSHADERBYTECODE,
    D3D11_MESSAGE_ID_CREATEPIXELSHADER_INVALIDSHADERTYPE,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDFILLMODE,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDCULLMODE,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDDEPTHBIASCLAMP,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDSLOPESCALEDDEPTHBIAS,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_TOOMANYOBJECTS,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_NULLDESC,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDDEPTHWRITEMASK,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDDEPTHFUNC,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILFAILOP,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILZFAILOP,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILPASSOP,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILFUNC,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILFAILOP,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILZFAILOP,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILPASSOP,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILFUNC,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_TOOMANYOBJECTS,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_NULLDESC,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDSRCBLEND,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDDESTBLEND,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDBLENDOP,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDSRCBLENDALPHA,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDDESTBLENDALPHA,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDBLENDOPALPHA,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDRENDERTARGETWRITEMASK,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_TOOMANYOBJECTS,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_NULLDESC,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDFILTER,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDADDRESSU,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDADDRESSV,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDADDRESSW,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDMIPLODBIAS,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDMAXANISOTROPY,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDCOMPARISONFUNC,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDMINLOD,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_INVALIDMAXLOD,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_TOOMANYOBJECTS,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_NULLDESC,
    D3D11_MESSAGE_ID_CREATEQUERYORPREDICATE_INVALIDQUERY,
    D3D11_MESSAGE_ID_CREATEQUERYORPREDICATE_INVALIDMISCFLAGS,
    D3D11_MESSAGE_ID_CREATEQUERYORPREDICATE_UNEXPECTEDMISCFLAG,
    D3D11_MESSAGE_ID_CREATEQUERYORPREDICATE_NULLDESC,
    D3D11_MESSAGE_ID_DEVICE_IASETPRIMITIVETOPOLOGY_TOPOLOGY_UNRECOGNIZED,
    D3D11_MESSAGE_ID_DEVICE_IASETPRIMITIVETOPOLOGY_TOPOLOGY_UNDEFINED,
    D3D11_MESSAGE_ID_IASETVERTEXBUFFERS_INVALIDBUFFER,
    D3D11_MESSAGE_ID_DEVICE_IASETVERTEXBUFFERS_OFFSET_TOO_LARGE,
    D3D11_MESSAGE_ID_DEVICE_IASETVERTEXBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_IASETINDEXBUFFER_INVALIDBUFFER,
    D3D11_MESSAGE_ID_DEVICE_IASETINDEXBUFFER_FORMAT_INVALID,
    D3D11_MESSAGE_ID_DEVICE_IASETINDEXBUFFER_OFFSET_TOO_LARGE,
    D3D11_MESSAGE_ID_DEVICE_IASETINDEXBUFFER_OFFSET_UNALIGNED,
    D3D11_MESSAGE_ID_DEVICE_VSSETSHADERRESOURCES_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_VSSETCONSTANTBUFFERS_INVALIDBUFFER,
    D3D11_MESSAGE_ID_DEVICE_VSSETCONSTANTBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_VSSETSAMPLERS_SAMPLERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_GSSETSHADERRESOURCES_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_GSSETCONSTANTBUFFERS_INVALIDBUFFER,
    D3D11_MESSAGE_ID_DEVICE_GSSETCONSTANTBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_GSSETSAMPLERS_SAMPLERS_EMPTY,
    D3D11_MESSAGE_ID_SOSETTARGETS_INVALIDBUFFER,
    D3D11_MESSAGE_ID_DEVICE_SOSETTARGETS_OFFSET_UNALIGNED,
    D3D11_MESSAGE_ID_DEVICE_PSSETSHADERRESOURCES_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_PSSETCONSTANTBUFFERS_INVALIDBUFFER,
    D3D11_MESSAGE_ID_DEVICE_PSSETCONSTANTBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_PSSETSAMPLERS_SAMPLERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_RSSETVIEWPORTS_INVALIDVIEWPORT,
    D3D11_MESSAGE_ID_DEVICE_RSSETSCISSORRECTS_INVALIDSCISSOR,
    D3D11_MESSAGE_ID_CLEARRENDERTARGETVIEW_DENORMFLUSH,
    D3D11_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_DENORMFLUSH,
    D3D11_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_INVALID,
    D3D11_MESSAGE_ID_DEVICE_IAGETVERTEXBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_VSGETSHADERRESOURCES_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_VSGETCONSTANTBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_VSGETSAMPLERS_SAMPLERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_GSGETSHADERRESOURCES_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_GSGETCONSTANTBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_GSGETSAMPLERS_SAMPLERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_SOGETTARGETS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_PSGETSHADERRESOURCES_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_PSGETCONSTANTBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_PSGETSAMPLERS_SAMPLERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_RSGETVIEWPORTS_VIEWPORTS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_RSGETSCISSORRECTS_RECTS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_GENERATEMIPS_RESOURCE_INVALID,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_INVALIDDESTINATIONSUBRESOURCE,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_INVALIDSOURCESUBRESOURCE,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_INVALIDSOURCEBOX,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_INVALIDSOURCE,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_INVALIDDESTINATIONSTATE,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_INVALIDSOURCESTATE,
    D3D11_MESSAGE_ID_COPYRESOURCE_INVALIDSOURCE,
    D3D11_MESSAGE_ID_COPYRESOURCE_INVALIDDESTINATIONSTATE,
    D3D11_MESSAGE_ID_COPYRESOURCE_INVALIDSOURCESTATE,
    D3D11_MESSAGE_ID_UPDATESUBRESOURCE_INVALIDDESTINATIONSUBRESOURCE,
    D3D11_MESSAGE_ID_UPDATESUBRESOURCE_INVALIDDESTINATIONBOX,
    D3D11_MESSAGE_ID_UPDATESUBRESOURCE_INVALIDDESTINATIONSTATE,
    D3D11_MESSAGE_ID_DEVICE_RESOLVESUBRESOURCE_DESTINATION_INVALID,
    D3D11_MESSAGE_ID_DEVICE_RESOLVESUBRESOURCE_DESTINATION_SUBRESOURCE_INVALID,
    D3D11_MESSAGE_ID_DEVICE_RESOLVESUBRESOURCE_SOURCE_INVALID,
    D3D11_MESSAGE_ID_DEVICE_RESOLVESUBRESOURCE_SOURCE_SUBRESOURCE_INVALID,
    D3D11_MESSAGE_ID_DEVICE_RESOLVESUBRESOURCE_FORMAT_INVALID,
    D3D11_MESSAGE_ID_BUFFER_MAP_INVALIDMAPTYPE,
    D3D11_MESSAGE_ID_BUFFER_MAP_INVALIDFLAGS,
    D3D11_MESSAGE_ID_BUFFER_MAP_ALREADYMAPPED,
    D3D11_MESSAGE_ID_BUFFER_MAP_DEVICEREMOVED_RETURN,
    D3D11_MESSAGE_ID_BUFFER_UNMAP_NOTMAPPED,
    D3D11_MESSAGE_ID_TEXTURE1D_MAP_INVALIDMAPTYPE,
    D3D11_MESSAGE_ID_TEXTURE1D_MAP_INVALIDSUBRESOURCE,
    D3D11_MESSAGE_ID_TEXTURE1D_MAP_INVALIDFLAGS,
    D3D11_MESSAGE_ID_TEXTURE1D_MAP_ALREADYMAPPED,
    D3D11_MESSAGE_ID_TEXTURE1D_MAP_DEVICEREMOVED_RETURN,
    D3D11_MESSAGE_ID_TEXTURE1D_UNMAP_INVALIDSUBRESOURCE,
    D3D11_MESSAGE_ID_TEXTURE1D_UNMAP_NOTMAPPED,
    D3D11_MESSAGE_ID_TEXTURE2D_MAP_INVALIDMAPTYPE,
    D3D11_MESSAGE_ID_TEXTURE2D_MAP_INVALIDSUBRESOURCE,
    D3D11_MESSAGE_ID_TEXTURE2D_MAP_INVALIDFLAGS,
    D3D11_MESSAGE_ID_TEXTURE2D_MAP_ALREADYMAPPED,
    D3D11_MESSAGE_ID_TEXTURE2D_MAP_DEVICEREMOVED_RETURN,
    D3D11_MESSAGE_ID_TEXTURE2D_UNMAP_INVALIDSUBRESOURCE,
    D3D11_MESSAGE_ID_TEXTURE2D_UNMAP_NOTMAPPED,
    D3D11_MESSAGE_ID_TEXTURE3D_MAP_INVALIDMAPTYPE,
    D3D11_MESSAGE_ID_TEXTURE3D_MAP_INVALIDSUBRESOURCE,
    D3D11_MESSAGE_ID_TEXTURE3D_MAP_INVALIDFLAGS,
    D3D11_MESSAGE_ID_TEXTURE3D_MAP_ALREADYMAPPED,
    D3D11_MESSAGE_ID_TEXTURE3D_MAP_DEVICEREMOVED_RETURN,
    D3D11_MESSAGE_ID_TEXTURE3D_UNMAP_INVALIDSUBRESOURCE,
    D3D11_MESSAGE_ID_TEXTURE3D_UNMAP_NOTMAPPED,
    D3D11_MESSAGE_ID_CHECKFORMATSUPPORT_FORMAT_DEPRECATED,
    D3D11_MESSAGE_ID_CHECKMULTISAMPLEQUALITYLEVELS_FORMAT_DEPRECATED,
    D3D11_MESSAGE_ID_SETEXCEPTIONMODE_UNRECOGNIZEDFLAGS,
    D3D11_MESSAGE_ID_SETEXCEPTIONMODE_INVALIDARG_RETURN,
    D3D11_MESSAGE_ID_SETEXCEPTIONMODE_DEVICEREMOVED_RETURN,
    D3D11_MESSAGE_ID_REF_SIMULATING_INFINITELY_FAST_HARDWARE,
    D3D11_MESSAGE_ID_REF_THREADING_MODE,
    D3D11_MESSAGE_ID_REF_UMDRIVER_EXCEPTION,
    D3D11_MESSAGE_ID_REF_KMDRIVER_EXCEPTION,
    D3D11_MESSAGE_ID_REF_HARDWARE_EXCEPTION,
    D3D11_MESSAGE_ID_REF_ACCESSING_INDEXABLE_TEMP_OUT_OF_RANGE,
    D3D11_MESSAGE_ID_REF_PROBLEM_PARSING_SHADER,
    D3D11_MESSAGE_ID_REF_OUT_OF_MEMORY,
    D3D11_MESSAGE_ID_REF_INFO,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEXPOS_OVERFLOW,
    D3D11_MESSAGE_ID_DEVICE_DRAWINDEXED_INDEXPOS_OVERFLOW,
    D3D11_MESSAGE_ID_DEVICE_DRAWINSTANCED_VERTEXPOS_OVERFLOW,
    D3D11_MESSAGE_ID_DEVICE_DRAWINSTANCED_INSTANCEPOS_OVERFLOW,
    D3D11_MESSAGE_ID_DEVICE_DRAWINDEXEDINSTANCED_INSTANCEPOS_OVERFLOW,
    D3D11_MESSAGE_ID_DEVICE_DRAWINDEXEDINSTANCED_INDEXPOS_OVERFLOW,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEX_SHADER_NOT_SET,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_SEMANTICNAME_NOT_FOUND,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_REGISTERINDEX,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_COMPONENTTYPE,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_REGISTERMASK,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_SYSTEMVALUE,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_NEVERWRITTEN_ALWAYSREADS,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEX_BUFFER_NOT_SET,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INPUTLAYOUT_NOT_SET,
    D3D11_MESSAGE_ID_DEVICE_DRAW_CONSTANT_BUFFER_NOT_SET,
    D3D11_MESSAGE_ID_DEVICE_DRAW_CONSTANT_BUFFER_TOO_SMALL,
    D3D11_MESSAGE_ID_DEVICE_DRAW_SAMPLER_NOT_SET,
    D3D11_MESSAGE_ID_DEVICE_DRAW_SHADERRESOURCEVIEW_NOT_SET,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VIEW_DIMENSION_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEX_BUFFER_STRIDE_TOO_SMALL,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEX_BUFFER_TOO_SMALL,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INDEX_BUFFER_NOT_SET,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INDEX_BUFFER_FORMAT_INVALID,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INDEX_BUFFER_TOO_SMALL,
    D3D11_MESSAGE_ID_DEVICE_DRAW_GS_INPUT_PRIMITIVE_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_RETURN_TYPE_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_DRAW_POSITION_NOT_PRESENT,
    D3D11_MESSAGE_ID_DEVICE_DRAW_OUTPUT_STREAM_NOT_SET,
    D3D11_MESSAGE_ID_DEVICE_DRAW_BOUND_RESOURCE_MAPPED,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INVALID_PRIMITIVETOPOLOGY,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEX_OFFSET_UNALIGNED,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VERTEX_STRIDE_UNALIGNED,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INDEX_OFFSET_UNALIGNED,
    D3D11_MESSAGE_ID_DEVICE_DRAW_OUTPUT_STREAM_OFFSET_UNALIGNED,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_FORMAT_LD_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_FORMAT_SAMPLE_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_FORMAT_SAMPLE_C_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_MULTISAMPLE_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_DRAW_SO_TARGETS_BOUND_WITHOUT_SOURCE,
    D3D11_MESSAGE_ID_DEVICE_DRAW_SO_STRIDE_LARGER_THAN_BUFFER,
    D3D11_MESSAGE_ID_DEVICE_DRAW_OM_RENDER_TARGET_DOES_NOT_SUPPORT_BLENDING,
    D3D11_MESSAGE_ID_DEVICE_DRAW_OM_DUAL_SOURCE_BLENDING_CAN_ONLY_HAVE_RENDER_TARGET_0,
    D3D11_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_AT_FAULT,
    D3D11_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_POSSIBLY_AT_FAULT,
    D3D11_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_NOT_AT_FAULT,
    D3D11_MESSAGE_ID_DEVICE_OPEN_SHARED_RESOURCE_INVALIDARG_RETURN,
    D3D11_MESSAGE_ID_DEVICE_OPEN_SHARED_RESOURCE_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_DEVICE_OPEN_SHARED_RESOURCE_BADINTERFACE_RETURN,
    D3D11_MESSAGE_ID_DEVICE_DRAW_VIEWPORT_NOT_SET,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_TRAILING_DIGIT_IN_SEMANTIC,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_TRAILING_DIGIT_IN_SEMANTIC,
    D3D11_MESSAGE_ID_DEVICE_RSSETVIEWPORTS_DENORMFLUSH,
    D3D11_MESSAGE_ID_OMSETRENDERTARGETS_INVALIDVIEW,
    D3D11_MESSAGE_ID_DEVICE_SETTEXTFILTERSIZE_INVALIDDIMENSIONS,
    D3D11_MESSAGE_ID_DEVICE_DRAW_SAMPLER_MISMATCH,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_TYPE_MISMATCH,
    D3D11_MESSAGE_ID_BLENDSTATE_GETDESC_LEGACY,
    D3D11_MESSAGE_ID_SHADERRESOURCEVIEW_GETDESC_LEGACY,
    D3D11_MESSAGE_ID_CREATEQUERY_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATEPREDICATE_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATECOUNTER_OUTOFRANGE_COUNTER,
    D3D11_MESSAGE_ID_CREATECOUNTER_SIMULTANEOUS_ACTIVE_COUNTERS_EXHAUSTED,
    D3D11_MESSAGE_ID_CREATECOUNTER_UNSUPPORTED_WELLKNOWN_COUNTER,
    D3D11_MESSAGE_ID_CREATECOUNTER_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATECOUNTER_NONEXCLUSIVE_RETURN,
    D3D11_MESSAGE_ID_CREATECOUNTER_NULLDESC,
    D3D11_MESSAGE_ID_CHECKCOUNTER_OUTOFRANGE_COUNTER,
    D3D11_MESSAGE_ID_CHECKCOUNTER_UNSUPPORTED_WELLKNOWN_COUNTER,
    D3D11_MESSAGE_ID_SETPREDICATION_INVALID_PREDICATE_STATE,
    D3D11_MESSAGE_ID_QUERY_BEGIN_UNSUPPORTED,
    D3D11_MESSAGE_ID_PREDICATE_BEGIN_DURING_PREDICATION,
    D3D11_MESSAGE_ID_QUERY_BEGIN_DUPLICATE,
    D3D11_MESSAGE_ID_QUERY_BEGIN_ABANDONING_PREVIOUS_RESULTS,
    D3D11_MESSAGE_ID_PREDICATE_END_DURING_PREDICATION,
    D3D11_MESSAGE_ID_QUERY_END_ABANDONING_PREVIOUS_RESULTS,
    D3D11_MESSAGE_ID_QUERY_END_WITHOUT_BEGIN,
    D3D11_MESSAGE_ID_QUERY_GETDATA_INVALID_DATASIZE,
    D3D11_MESSAGE_ID_QUERY_GETDATA_INVALID_FLAGS,
    D3D11_MESSAGE_ID_QUERY_GETDATA_INVALID_CALL,
    D3D11_MESSAGE_ID_DEVICE_DRAW_PS_OUTPUT_TYPE_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_FORMAT_GATHER_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INVALID_USE_OF_CENTER_MULTISAMPLE_PATTERN,
    D3D11_MESSAGE_ID_DEVICE_IASETVERTEXBUFFERS_STRIDE_TOO_LARGE,
    D3D11_MESSAGE_ID_DEVICE_IASETVERTEXBUFFERS_INVALIDRANGE,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_EMPTY_LAYOUT,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RESOURCE_SAMPLE_COUNT_MISMATCH,
    D3D11_MESSAGE_ID_LIVE_OBJECT_SUMMARY,
    D3D11_MESSAGE_ID_LIVE_BUFFER,
    D3D11_MESSAGE_ID_LIVE_TEXTURE1D,
    D3D11_MESSAGE_ID_LIVE_TEXTURE2D,
    D3D11_MESSAGE_ID_LIVE_TEXTURE3D,
    D3D11_MESSAGE_ID_LIVE_SHADERRESOURCEVIEW,
    D3D11_MESSAGE_ID_LIVE_RENDERTARGETVIEW,
    D3D11_MESSAGE_ID_LIVE_DEPTHSTENCILVIEW,
    D3D11_MESSAGE_ID_LIVE_VERTEXSHADER,
    D3D11_MESSAGE_ID_LIVE_GEOMETRYSHADER,
    D3D11_MESSAGE_ID_LIVE_PIXELSHADER,
    D3D11_MESSAGE_ID_LIVE_INPUTLAYOUT,
    D3D11_MESSAGE_ID_LIVE_SAMPLER,
    D3D11_MESSAGE_ID_LIVE_BLENDSTATE,
    D3D11_MESSAGE_ID_LIVE_DEPTHSTENCILSTATE,
    D3D11_MESSAGE_ID_LIVE_RASTERIZERSTATE,
    D3D11_MESSAGE_ID_LIVE_QUERY,
    D3D11_MESSAGE_ID_LIVE_PREDICATE,
    D3D11_MESSAGE_ID_LIVE_COUNTER,
    D3D11_MESSAGE_ID_LIVE_DEVICE,
    D3D11_MESSAGE_ID_LIVE_SWAPCHAIN,
    D3D11_MESSAGE_ID_D3D10_MESSAGES_END,

    D3D11_MESSAGE_ID_D3D10L9_MESSAGES_START = 0x00100000,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_STENCIL_NO_TWO_SIDED,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_DepthBiasClamp_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_NO_COMPARISON_SUPPORT,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_EXCESSIVE_ANISOTROPY,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_BORDER_OUT_OF_RANGE,
    D3D11_MESSAGE_ID_VSSETSAMPLERS_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_VSSETSAMPLERS_TOO_MANY_SAMPLERS,
    D3D11_MESSAGE_ID_PSSETSAMPLERS_TOO_MANY_SAMPLERS,
    D3D11_MESSAGE_ID_CREATERESOURCE_NO_ARRAYS,
    D3D11_MESSAGE_ID_CREATERESOURCE_NO_VB_AND_IB_BIND,
    D3D11_MESSAGE_ID_CREATERESOURCE_NO_TEXTURE_1D,
    D3D11_MESSAGE_ID_CREATERESOURCE_DIMENSION_OUT_OF_RANGE,
    D3D11_MESSAGE_ID_CREATERESOURCE_NOT_BINDABLE_AS_SHADER_RESOURCE,
    D3D11_MESSAGE_ID_OMSETRENDERTARGETS_TOO_MANY_RENDER_TARGETS,
    D3D11_MESSAGE_ID_OMSETRENDERTARGETS_NO_DIFFERING_BIT_DEPTHS,
    D3D11_MESSAGE_ID_IASETVERTEXBUFFERS_BAD_BUFFER_INDEX,
    D3D11_MESSAGE_ID_DEVICE_RSSETVIEWPORTS_TOO_MANY_VIEWPORTS,
    D3D11_MESSAGE_ID_DEVICE_IASETPRIMITIVETOPOLOGY_ADJACENCY_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_RSSETSCISSORRECTS_TOO_MANY_SCISSORS,
    D3D11_MESSAGE_ID_COPYRESOURCE_ONLY_TEXTURE_2D_WITHIN_GPU_MEMORY,
    D3D11_MESSAGE_ID_COPYRESOURCE_NO_TEXTURE_3D_READBACK,
    D3D11_MESSAGE_ID_COPYRESOURCE_NO_TEXTURE_ONLY_READBACK,
    D3D11_MESSAGE_ID_CREATEINPUTLAYOUT_UNSUPPORTED_FORMAT,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_NO_ALPHA_TO_COVERAGE,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_DepthClipEnable_MUST_BE_TRUE,
    D3D11_MESSAGE_ID_DRAWINDEXED_STARTINDEXLOCATION_MUST_BE_POSITIVE,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_MUST_USE_LOWEST_LOD,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_MINLOD_MUST_NOT_BE_FRACTIONAL,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_MAXLOD_MUST_BE_FLT_MAX,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_FIRSTARRAYSLICE_MUST_BE_ZERO,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_CUBES_MUST_HAVE_6_SIDES,
    D3D11_MESSAGE_ID_CREATERESOURCE_NOT_BINDABLE_AS_RENDER_TARGET,
    D3D11_MESSAGE_ID_CREATERESOURCE_NO_DWORD_INDEX_BUFFER,
    D3D11_MESSAGE_ID_CREATERESOURCE_MSAA_PRECLUDES_SHADER_RESOURCE,
    D3D11_MESSAGE_ID_CREATERESOURCE_PRESENTATION_PRECLUDES_SHADER_RESOURCE,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_NO_INDEPENDENT_BLEND_ENABLE,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_NO_INDEPENDENT_WRITE_MASKS,
    D3D11_MESSAGE_ID_CREATERESOURCE_NO_STREAM_OUT,
    D3D11_MESSAGE_ID_CREATERESOURCE_ONLY_VB_IB_FOR_BUFFERS,
    D3D11_MESSAGE_ID_CREATERESOURCE_NO_AUTOGEN_FOR_VOLUMES,
    D3D11_MESSAGE_ID_CREATERESOURCE_DXGI_FORMAT_R8G8B8A8_CANNOT_BE_SHARED,
    D3D11_MESSAGE_ID_VSSHADERRESOURCES_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_GEOMETRY_SHADER_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_STREAM_OUT_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_TEXT_FILTER_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_NO_SEPARATE_ALPHA_BLEND,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_NO_MRT_BLEND,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_OPERATION_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_NO_MIRRORONCE,
    D3D11_MESSAGE_ID_DRAWINSTANCED_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_DRAWINDEXEDINSTANCED_NOT_SUPPORTED_BELOW_9_3,
    D3D11_MESSAGE_ID_DRAWINDEXED_POINTLIST_UNSUPPORTED,
    D3D11_MESSAGE_ID_SETBLENDSTATE_SAMPLE_MASK_CANNOT_BE_ZERO,
    D3D11_MESSAGE_ID_CREATERESOURCE_DIMENSION_EXCEEDS_FEATURE_LEVEL_DEFINITION,
    D3D11_MESSAGE_ID_CREATERESOURCE_ONLY_SINGLE_MIP_LEVEL_DEPTH_STENCIL_SUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_RSSETSCISSORRECTS_NEGATIVESCISSOR,
    D3D11_MESSAGE_ID_SLOT_ZERO_MUST_BE_D3D10_INPUT_PER_VERTEX_DATA,
    D3D11_MESSAGE_ID_CREATERESOURCE_NON_POW_2_MIPMAP,
    D3D11_MESSAGE_ID_CREATESAMPLERSTATE_BORDER_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_OMSETRENDERTARGETS_NO_SRGB_MRT,
    D3D11_MESSAGE_ID_COPYRESOURCE_NO_3D_MISMATCHED_UPDATES,
    D3D11_MESSAGE_ID_D3D10L9_MESSAGES_END,

    D3D11_MESSAGE_ID_D3D11_MESSAGES_START = 0x00200000,
    D3D11_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDFLAGS,
    D3D11_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDCLASSLINKAGE,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDCLASSLINKAGE,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDNUMSTREAMS,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTREAMTORASTERIZER,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDSTREAMS,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDCLASSLINKAGE,
    D3D11_MESSAGE_ID_CREATEPIXELSHADER_INVALIDCLASSLINKAGE,
    D3D11_MESSAGE_ID_CREATEDEFERREDCONTEXT_INVALID_COMMANDLISTFLAGS,
    D3D11_MESSAGE_ID_CREATEDEFERREDCONTEXT_SINGLETHREADED,
    D3D11_MESSAGE_ID_CREATEDEFERREDCONTEXT_INVALIDARG_RETURN,
    D3D11_MESSAGE_ID_CREATEDEFERREDCONTEXT_INVALID_CALL_RETURN,
    D3D11_MESSAGE_ID_CREATEDEFERREDCONTEXT_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_FINISHDISPLAYLIST_ONIMMEDIATECONTEXT,
    D3D11_MESSAGE_ID_FINISHDISPLAYLIST_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_FINISHDISPLAYLIST_INVALID_CALL_RETURN,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTREAM,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDENTRIES,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDSTRIDES,
    D3D11_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDNUMSTRIDES,
    D3D11_MESSAGE_ID_DEVICE_HSSETSHADERRESOURCES_HAZARD,
    D3D11_MESSAGE_ID_DEVICE_HSSETCONSTANTBUFFERS_HAZARD,
    D3D11_MESSAGE_ID_HSSETSHADERRESOURCES_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_HSSETCONSTANTBUFFERS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_CREATEHULLSHADER_INVALIDCALL,
    D3D11_MESSAGE_ID_CREATEHULLSHADER_OUTOFMEMORY,
    D3D11_MESSAGE_ID_CREATEHULLSHADER_INVALIDSHADERBYTECODE,
    D3D11_MESSAGE_ID_CREATEHULLSHADER_INVALIDSHADERTYPE,
    D3D11_MESSAGE_ID_CREATEHULLSHADER_INVALIDCLASSLINKAGE,
    D3D11_MESSAGE_ID_DEVICE_HSSETSHADERRESOURCES_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_HSSETCONSTANTBUFFERS_INVALIDBUFFER,
    D3D11_MESSAGE_ID_DEVICE_HSSETCONSTANTBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_HSSETSAMPLERS_SAMPLERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_HSGETSHADERRESOURCES_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_HSGETCONSTANTBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_HSGETSAMPLERS_SAMPLERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_DSSETSHADERRESOURCES_HAZARD,
    D3D11_MESSAGE_ID_DEVICE_DSSETCONSTANTBUFFERS_HAZARD,
    D3D11_MESSAGE_ID_DSSETSHADERRESOURCES_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_DSSETCONSTANTBUFFERS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDCALL,
    D3D11_MESSAGE_ID_CREATEDOMAINSHADER_OUTOFMEMORY,
    D3D11_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDSHADERBYTECODE,
    D3D11_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDSHADERTYPE,
    D3D11_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDCLASSLINKAGE,
    D3D11_MESSAGE_ID_DEVICE_DSSETSHADERRESOURCES_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_DSSETCONSTANTBUFFERS_INVALIDBUFFER,
    D3D11_MESSAGE_ID_DEVICE_DSSETCONSTANTBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_DSSETSAMPLERS_SAMPLERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_DSGETSHADERRESOURCES_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_DSGETCONSTANTBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_DSGETSAMPLERS_SAMPLERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_DRAW_HS_XOR_DS_MISMATCH,
    D3D11_MESSAGE_ID_DEFERRED_CONTEXT_REMOVAL_PROCESS_AT_FAULT,
    D3D11_MESSAGE_ID_DEVICE_DRAWINDIRECT_INVALID_ARG_BUFFER,
    D3D11_MESSAGE_ID_DEVICE_DRAWINDIRECT_OFFSET_UNALIGNED,
    D3D11_MESSAGE_ID_DEVICE_DRAWINDIRECT_OFFSET_OVERFLOW,
    D3D11_MESSAGE_ID_RESOURCE_MAP_INVALIDMAPTYPE,
    D3D11_MESSAGE_ID_RESOURCE_MAP_INVALIDSUBRESOURCE,
    D3D11_MESSAGE_ID_RESOURCE_MAP_INVALIDFLAGS,
    D3D11_MESSAGE_ID_RESOURCE_MAP_ALREADYMAPPED,
    D3D11_MESSAGE_ID_RESOURCE_MAP_DEVICEREMOVED_RETURN,
    D3D11_MESSAGE_ID_RESOURCE_MAP_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_RESOURCE_MAP_WITHOUT_INITIAL_DISCARD,
    D3D11_MESSAGE_ID_RESOURCE_UNMAP_INVALIDSUBRESOURCE,
    D3D11_MESSAGE_ID_RESOURCE_UNMAP_NOTMAPPED,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RASTERIZING_CONTROL_POINTS,
    D3D11_MESSAGE_ID_DEVICE_IASETPRIMITIVETOPOLOGY_TOPOLOGY_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_DRAW_HS_DS_SIGNATURE_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_DRAW_HULL_SHADER_INPUT_TOPOLOGY_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_DRAW_HS_DS_CONTROL_POINT_COUNT_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_DRAW_HS_DS_TESSELLATOR_DOMAIN_MISMATCH,
    D3D11_MESSAGE_ID_CREATE_CONTEXT,
    D3D11_MESSAGE_ID_LIVE_CONTEXT,
    D3D11_MESSAGE_ID_DESTROY_CONTEXT,
    D3D11_MESSAGE_ID_CREATE_BUFFER,
    D3D11_MESSAGE_ID_LIVE_BUFFER_WIN7,
    D3D11_MESSAGE_ID_DESTROY_BUFFER,
    D3D11_MESSAGE_ID_CREATE_TEXTURE1D,
    D3D11_MESSAGE_ID_LIVE_TEXTURE1D_WIN7,
    D3D11_MESSAGE_ID_DESTROY_TEXTURE1D,
    D3D11_MESSAGE_ID_CREATE_TEXTURE2D,
    D3D11_MESSAGE_ID_LIVE_TEXTURE2D_WIN7,
    D3D11_MESSAGE_ID_DESTROY_TEXTURE2D,
    D3D11_MESSAGE_ID_CREATE_TEXTURE3D,
    D3D11_MESSAGE_ID_LIVE_TEXTURE3D_WIN7,
    D3D11_MESSAGE_ID_DESTROY_TEXTURE3D,
    D3D11_MESSAGE_ID_CREATE_SHADERRESOURCEVIEW,
    D3D11_MESSAGE_ID_LIVE_SHADERRESOURCEVIEW_WIN7,
    D3D11_MESSAGE_ID_DESTROY_SHADERRESOURCEVIEW,
    D3D11_MESSAGE_ID_CREATE_RENDERTARGETVIEW,
    D3D11_MESSAGE_ID_LIVE_RENDERTARGETVIEW_WIN7,
    D3D11_MESSAGE_ID_DESTROY_RENDERTARGETVIEW,
    D3D11_MESSAGE_ID_CREATE_DEPTHSTENCILVIEW,
    D3D11_MESSAGE_ID_LIVE_DEPTHSTENCILVIEW_WIN7,
    D3D11_MESSAGE_ID_DESTROY_DEPTHSTENCILVIEW,
    D3D11_MESSAGE_ID_CREATE_VERTEXSHADER,
    D3D11_MESSAGE_ID_LIVE_VERTEXSHADER_WIN7,
    D3D11_MESSAGE_ID_DESTROY_VERTEXSHADER,
    D3D11_MESSAGE_ID_CREATE_HULLSHADER,
    D3D11_MESSAGE_ID_LIVE_HULLSHADER,
    D3D11_MESSAGE_ID_DESTROY_HULLSHADER,
    D3D11_MESSAGE_ID_CREATE_DOMAINSHADER,
    D3D11_MESSAGE_ID_LIVE_DOMAINSHADER,
    D3D11_MESSAGE_ID_DESTROY_DOMAINSHADER,
    D3D11_MESSAGE_ID_CREATE_GEOMETRYSHADER,
    D3D11_MESSAGE_ID_LIVE_GEOMETRYSHADER_WIN7,
    D3D11_MESSAGE_ID_DESTROY_GEOMETRYSHADER,
    D3D11_MESSAGE_ID_CREATE_PIXELSHADER,
    D3D11_MESSAGE_ID_LIVE_PIXELSHADER_WIN7,
    D3D11_MESSAGE_ID_DESTROY_PIXELSHADER,
    D3D11_MESSAGE_ID_CREATE_INPUTLAYOUT,
    D3D11_MESSAGE_ID_LIVE_INPUTLAYOUT_WIN7,
    D3D11_MESSAGE_ID_DESTROY_INPUTLAYOUT,
    D3D11_MESSAGE_ID_CREATE_SAMPLER,
    D3D11_MESSAGE_ID_LIVE_SAMPLER_WIN7,
    D3D11_MESSAGE_ID_DESTROY_SAMPLER,
    D3D11_MESSAGE_ID_CREATE_BLENDSTATE,
    D3D11_MESSAGE_ID_LIVE_BLENDSTATE_WIN7,
    D3D11_MESSAGE_ID_DESTROY_BLENDSTATE,
    D3D11_MESSAGE_ID_CREATE_DEPTHSTENCILSTATE,
    D3D11_MESSAGE_ID_LIVE_DEPTHSTENCILSTATE_WIN7,
    D3D11_MESSAGE_ID_DESTROY_DEPTHSTENCILSTATE,
    D3D11_MESSAGE_ID_CREATE_RASTERIZERSTATE,
    D3D11_MESSAGE_ID_LIVE_RASTERIZERSTATE_WIN7,
    D3D11_MESSAGE_ID_DESTROY_RASTERIZERSTATE,
    D3D11_MESSAGE_ID_CREATE_QUERY,
    D3D11_MESSAGE_ID_LIVE_QUERY_WIN7,
    D3D11_MESSAGE_ID_DESTROY_QUERY,
    D3D11_MESSAGE_ID_CREATE_PREDICATE,
    D3D11_MESSAGE_ID_LIVE_PREDICATE_WIN7,
    D3D11_MESSAGE_ID_DESTROY_PREDICATE,
    D3D11_MESSAGE_ID_CREATE_COUNTER,
    D3D11_MESSAGE_ID_DESTROY_COUNTER,
    D3D11_MESSAGE_ID_CREATE_COMMANDLIST,
    D3D11_MESSAGE_ID_LIVE_COMMANDLIST,
    D3D11_MESSAGE_ID_DESTROY_COMMANDLIST,
    D3D11_MESSAGE_ID_CREATE_CLASSINSTANCE,
    D3D11_MESSAGE_ID_LIVE_CLASSINSTANCE,
    D3D11_MESSAGE_ID_DESTROY_CLASSINSTANCE,
    D3D11_MESSAGE_ID_CREATE_CLASSLINKAGE,
    D3D11_MESSAGE_ID_LIVE_CLASSLINKAGE,
    D3D11_MESSAGE_ID_DESTROY_CLASSLINKAGE,
    D3D11_MESSAGE_ID_LIVE_DEVICE_WIN7,
    D3D11_MESSAGE_ID_LIVE_OBJECT_SUMMARY_WIN7,
    D3D11_MESSAGE_ID_CREATE_COMPUTESHADER,
    D3D11_MESSAGE_ID_LIVE_COMPUTESHADER,
    D3D11_MESSAGE_ID_DESTROY_COMPUTESHADER,
    D3D11_MESSAGE_ID_CREATE_UNORDEREDACCESSVIEW,
    D3D11_MESSAGE_ID_LIVE_UNORDEREDACCESSVIEW,
    D3D11_MESSAGE_ID_DESTROY_UNORDEREDACCESSVIEW,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INTERFACES_FEATURELEVEL,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INTERFACE_COUNT_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INVALID_INSTANCE,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INVALID_INSTANCE_INDEX,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INVALID_INSTANCE_TYPE,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INVALID_INSTANCE_DATA,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_UNBOUND_INSTANCE_DATA,
    D3D11_MESSAGE_ID_DEVICE_SETSHADER_INSTANCE_DATA_BINDINGS,
    D3D11_MESSAGE_ID_DEVICE_CREATESHADER_CLASSLINKAGE_FULL,
    D3D11_MESSAGE_ID_DEVICE_CHECKFEATURESUPPORT_UNRECOGNIZED_FEATURE,
    D3D11_MESSAGE_ID_DEVICE_CHECKFEATURESUPPORT_MISMATCHED_DATA_SIZE,
    D3D11_MESSAGE_ID_DEVICE_CHECKFEATURESUPPORT_INVALIDARG_RETURN,
    D3D11_MESSAGE_ID_DEVICE_CSSETSHADERRESOURCES_HAZARD,
    D3D11_MESSAGE_ID_DEVICE_CSSETCONSTANTBUFFERS_HAZARD,
    D3D11_MESSAGE_ID_CSSETSHADERRESOURCES_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_CSSETCONSTANTBUFFERS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDCALL,
    D3D11_MESSAGE_ID_CREATECOMPUTESHADER_OUTOFMEMORY,
    D3D11_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDSHADERBYTECODE,
    D3D11_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDSHADERTYPE,
    D3D11_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDCLASSLINKAGE,
    D3D11_MESSAGE_ID_DEVICE_CSSETSHADERRESOURCES_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_CSSETCONSTANTBUFFERS_INVALIDBUFFER,
    D3D11_MESSAGE_ID_DEVICE_CSSETCONSTANTBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_CSSETSAMPLERS_SAMPLERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_CSGETSHADERRESOURCES_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_CSGETCONSTANTBUFFERS_BUFFERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_CSGETSAMPLERS_SAMPLERS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_DOUBLEFLOATOPSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEHULLSHADER_DOUBLEFLOATOPSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_DOUBLEFLOATOPSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_DOUBLEFLOATOPSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DOUBLEFLOATOPSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_DOUBLEFLOATOPSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_DOUBLEFLOATOPSNOTSUPPORTED,
    D3D11_MESSAGE_ID_CREATEBUFFER_INVALIDSTRUCTURESTRIDE,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDFLAGS,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDRESOURCE,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDDESC,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDFORMAT,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDDIMENSIONS,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_UNRECOGNIZEDFORMAT,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETSANDUNORDEREDACCESSVIEWS_HAZARD,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETSANDUNORDEREDACCESSVIEWS_OVERLAPPING_OLD_SLOTS,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETSANDUNORDEREDACCESSVIEWS_NO_OP,
    D3D11_MESSAGE_ID_CSSETUNORDEREDACCESSVIEWS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_PSSETUNORDEREDACCESSVIEWS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDARG_RETURN,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_TOOMANYOBJECTS,
    D3D11_MESSAGE_ID_DEVICE_CSSETUNORDEREDACCESSVIEWS_HAZARD,
    D3D11_MESSAGE_ID_CLEARUNORDEREDACCESSVIEW_DENORMFLUSH,
    D3D11_MESSAGE_ID_DEVICE_CSSETUNORDEREDACCESSS_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_DEVICE_CSGETUNORDEREDACCESSS_VIEWS_EMPTY,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDFLAGS,
    D3D11_MESSAGE_ID_CREATESHADERRESESOURCEVIEW_TOOMANYOBJECTS,
    D3D11_MESSAGE_ID_DEVICE_DISPATCHINDIRECT_INVALID_ARG_BUFFER,
    D3D11_MESSAGE_ID_DEVICE_DISPATCHINDIRECT_OFFSET_UNALIGNED,
    D3D11_MESSAGE_ID_DEVICE_DISPATCHINDIRECT_OFFSET_OVERFLOW,
    D3D11_MESSAGE_ID_DEVICE_SETRESOURCEMINLOD_INVALIDCONTEXT,
    D3D11_MESSAGE_ID_DEVICE_SETRESOURCEMINLOD_INVALIDRESOURCE,
    D3D11_MESSAGE_ID_DEVICE_SETRESOURCEMINLOD_INVALIDMINLOD,
    D3D11_MESSAGE_ID_DEVICE_GETRESOURCEMINLOD_INVALIDCONTEXT,
    D3D11_MESSAGE_ID_DEVICE_GETRESOURCEMINLOD_INVALIDRESOURCE,
    D3D11_MESSAGE_ID_OMSETDEPTHSTENCIL_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_DEPTH_READONLY,
    D3D11_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_STENCIL_READONLY,
    D3D11_MESSAGE_ID_CHECKFEATURESUPPORT_FORMAT_DEPRECATED,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_RETURN_TYPE_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_NOT_SET,
    D3D11_MESSAGE_ID_DEVICE_DRAW_UNORDEREDACCESSVIEW_RENDERTARGETVIEW_OVERLAP,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_DIMENSION_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_APPEND_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMICS_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_STRUCTURE_STRIDE_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_BUFFER_TYPE_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_RAW_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_FORMAT_LD_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_FORMAT_STORE_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMIC_ADD_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMIC_BITWISE_OPS_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMIC_CMPSTORE_CMPEXCHANGE_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMIC_EXCHANGE_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMIC_SIGNED_MINMAX_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_ATOMIC_UNSIGNED_MINMAX_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_DISPATCH_BOUND_RESOURCE_MAPPED,
    D3D11_MESSAGE_ID_DEVICE_DISPATCH_THREADGROUPCOUNT_OVERFLOW,
    D3D11_MESSAGE_ID_DEVICE_DISPATCH_THREADGROUPCOUNT_ZERO,
    D3D11_MESSAGE_ID_DEVICE_SHADERRESOURCEVIEW_STRUCTURE_STRIDE_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_SHADERRESOURCEVIEW_BUFFER_TYPE_MISMATCH,
    D3D11_MESSAGE_ID_DEVICE_SHADERRESOURCEVIEW_RAW_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_DISPATCH_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_DISPATCHINDIRECT_UNSUPPORTED,
    D3D11_MESSAGE_ID_COPYSTRUCTURECOUNT_INVALIDOFFSET,
    D3D11_MESSAGE_ID_COPYSTRUCTURECOUNT_LARGEOFFSET,
    D3D11_MESSAGE_ID_COPYSTRUCTURECOUNT_INVALIDDESTINATIONSTATE,
    D3D11_MESSAGE_ID_COPYSTRUCTURECOUNT_INVALIDSOURCESTATE,
    D3D11_MESSAGE_ID_CHECKFORMATSUPPORT_FORMAT_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CSSETUNORDEREDACCESSVIEWS_INVALIDVIEW,
    D3D11_MESSAGE_ID_DEVICE_CSSETUNORDEREDACCESSVIEWS_INVALIDOFFSET,
    D3D11_MESSAGE_ID_DEVICE_CSSETUNORDEREDACCESSVIEWS_TOOMANYVIEWS,
    D3D11_MESSAGE_ID_CLEARUNORDEREDACCESSVIEWFLOAT_INVALIDFORMAT,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_COUNTER_UNSUPPORTED,
    D3D11_MESSAGE_ID_REF_WARNING,
    D3D11_MESSAGE_ID_DEVICE_DRAW_PIXEL_SHADER_WITHOUT_RTV_OR_DSV,
    D3D11_MESSAGE_ID_SHADER_ABORT,
    D3D11_MESSAGE_ID_SHADER_MESSAGE,
    D3D11_MESSAGE_ID_SHADER_ERROR,
    D3D11_MESSAGE_ID_OFFERRESOURCES_INVALIDRESOURCE,
    D3D11_MESSAGE_ID_HSSETSAMPLERS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_DSSETSAMPLERS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_CSSETSAMPLERS_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_HSSETSHADER_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_DSSETSHADER_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_CSSETSHADER_UNBINDDELETINGOBJECT,
    D3D11_MESSAGE_ID_ENQUEUESETEVENT_INVALIDARG_RETURN,
    D3D11_MESSAGE_ID_ENQUEUESETEVENT_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_ENQUEUESETEVENT_ACCESSDENIED_RETURN,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETSANDUNORDEREDACCESSVIEWS_NUMUAVS_INVALIDRANGE,
    D3D11_MESSAGE_ID_D3D11_MESSAGES_END,

    D3D11_MESSAGE_ID_D3D11_1_MESSAGES_START = 0x00300000,
    D3D11_MESSAGE_ID_CREATE_VIDEODECODER,
    D3D11_MESSAGE_ID_CREATE_VIDEOPROCESSORENUM,
    D3D11_MESSAGE_ID_CREATE_VIDEOPROCESSOR,
    D3D11_MESSAGE_ID_CREATE_DECODEROUTPUTVIEW,
    D3D11_MESSAGE_ID_CREATE_PROCESSORINPUTVIEW,
    D3D11_MESSAGE_ID_CREATE_PROCESSOROUTPUTVIEW,
    D3D11_MESSAGE_ID_CREATE_DEVICECONTEXTSTATE,
    D3D11_MESSAGE_ID_LIVE_VIDEODECODER,
    D3D11_MESSAGE_ID_LIVE_VIDEOPROCESSORENUM,
    D3D11_MESSAGE_ID_LIVE_VIDEOPROCESSOR,
    D3D11_MESSAGE_ID_LIVE_DECODEROUTPUTVIEW,
    D3D11_MESSAGE_ID_LIVE_PROCESSORINPUTVIEW,
    D3D11_MESSAGE_ID_LIVE_PROCESSOROUTPUTVIEW,
    D3D11_MESSAGE_ID_LIVE_DEVICECONTEXTSTATE,
    D3D11_MESSAGE_ID_DESTROY_VIDEODECODER,
    D3D11_MESSAGE_ID_DESTROY_VIDEOPROCESSORENUM,
    D3D11_MESSAGE_ID_DESTROY_VIDEOPROCESSOR,
    D3D11_MESSAGE_ID_DESTROY_DECODEROUTPUTVIEW,
    D3D11_MESSAGE_ID_DESTROY_PROCESSORINPUTVIEW,
    D3D11_MESSAGE_ID_DESTROY_PROCESSOROUTPUTVIEW,
    D3D11_MESSAGE_ID_DESTROY_DEVICECONTEXTSTATE,
    D3D11_MESSAGE_ID_CREATEDEVICECONTEXTSTATE_INVALIDFLAGS,
    D3D11_MESSAGE_ID_CREATEDEVICECONTEXTSTATE_INVALIDFEATURELEVEL,
    D3D11_MESSAGE_ID_CREATEDEVICECONTEXTSTATE_FEATURELEVELS_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_CREATEDEVICECONTEXTSTATE_INVALIDREFIID,
    D3D11_MESSAGE_ID_DEVICE_DISCARDVIEW_INVALIDVIEW,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION1_INVALIDCOPYFLAGS,
    D3D11_MESSAGE_ID_UPDATESUBRESOURCE1_INVALIDCOPYFLAGS,
    D3D11_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDFORCEDSAMPLECOUNT,
    D3D11_MESSAGE_ID_CREATEVIDEODECODER_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATEVIDEODECODER_NULLPARAM,
    D3D11_MESSAGE_ID_CREATEVIDEODECODER_INVALIDFORMAT,
    D3D11_MESSAGE_ID_CREATEVIDEODECODER_ZEROWIDTHHEIGHT,
    D3D11_MESSAGE_ID_CREATEVIDEODECODER_DRIVER_INVALIDBUFFERSIZE,
    D3D11_MESSAGE_ID_CREATEVIDEODECODER_DRIVER_INVALIDBUFFERUSAGE,
    D3D11_MESSAGE_ID_GETVIDEODECODERPROFILECOUNT_OUTOFMEMORY,
    D3D11_MESSAGE_ID_GETVIDEODECODERPROFILE_NULLPARAM,
    D3D11_MESSAGE_ID_GETVIDEODECODERPROFILE_INVALIDINDEX,
    D3D11_MESSAGE_ID_GETVIDEODECODERPROFILE_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CHECKVIDEODECODERFORMAT_NULLPARAM,
    D3D11_MESSAGE_ID_CHECKVIDEODECODERFORMAT_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_GETVIDEODECODERCONFIGCOUNT_NULLPARAM,
    D3D11_MESSAGE_ID_GETVIDEODECODERCONFIGCOUNT_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_GETVIDEODECODERCONFIG_NULLPARAM,
    D3D11_MESSAGE_ID_GETVIDEODECODERCONFIG_INVALIDINDEX,
    D3D11_MESSAGE_ID_GETVIDEODECODERCONFIG_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_GETDECODERCREATIONPARAMS_NULLPARAM,
    D3D11_MESSAGE_ID_GETDECODERDRIVERHANDLE_NULLPARAM,
    D3D11_MESSAGE_ID_GETDECODERBUFFER_NULLPARAM,
    D3D11_MESSAGE_ID_GETDECODERBUFFER_INVALIDBUFFER,
    D3D11_MESSAGE_ID_GETDECODERBUFFER_INVALIDTYPE,
    D3D11_MESSAGE_ID_GETDECODERBUFFER_LOCKED,
    D3D11_MESSAGE_ID_RELEASEDECODERBUFFER_NULLPARAM,
    D3D11_MESSAGE_ID_RELEASEDECODERBUFFER_INVALIDTYPE,
    D3D11_MESSAGE_ID_RELEASEDECODERBUFFER_NOTLOCKED,
    D3D11_MESSAGE_ID_DECODERBEGINFRAME_NULLPARAM,
    D3D11_MESSAGE_ID_DECODERBEGINFRAME_HAZARD,
    D3D11_MESSAGE_ID_DECODERENDFRAME_NULLPARAM,
    D3D11_MESSAGE_ID_SUBMITDECODERBUFFERS_NULLPARAM,
    D3D11_MESSAGE_ID_SUBMITDECODERBUFFERS_INVALIDTYPE,
    D3D11_MESSAGE_ID_DECODEREXTENSION_NULLPARAM,
    D3D11_MESSAGE_ID_DECODEREXTENSION_INVALIDRESOURCE,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_NULLPARAM,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_INVALIDFRAMEFORMAT,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_INVALIDUSAGE,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_INVALIDINPUTFRAMERATE,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_INVALIDOUTPUTFRAMERATE,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORENUMERATOR_INVALIDWIDTHHEIGHT,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORCONTENTDESC_NULLPARAM,
    D3D11_MESSAGE_ID_CHECKVIDEOPROCESSORFORMAT_NULLPARAM,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORCAPS_NULLPARAM,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORRATECONVERSIONCAPS_NULLPARAM,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORRATECONVERSIONCAPS_INVALIDINDEX,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORCUSTOMRATE_NULLPARAM,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORCUSTOMRATE_INVALIDINDEX,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORFILTERRANGE_NULLPARAM,
    D3D11_MESSAGE_ID_GETVIDEOPROCESSORFILTERRANGE_UNSUPPORTED,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOR_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOR_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTTARGETRECT_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTBACKGROUNDCOLOR_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTBACKGROUNDCOLOR_INVALIDALPHA,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTCOLORSPACE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTALPHAFILLMODE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTALPHAFILLMODE_UNSUPPORTED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTALPHAFILLMODE_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTALPHAFILLMODE_INVALIDFILLMODE,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTCONSTRICTION_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTSTEREOMODE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTSTEREOMODE_UNSUPPORTED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTEXTENSION_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTTARGETRECT_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTBACKGROUNDCOLOR_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTCOLORSPACE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTALPHAFILLMODE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTCONSTRICTION_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTCONSTRICTION_UNSUPPORTED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETOUTPUTCONSTRICTION_INVALIDSIZE,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTSTEREOMODE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETOUTPUTEXTENSION_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFRAMEFORMAT_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFRAMEFORMAT_INVALIDFORMAT,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFRAMEFORMAT_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMCOLORSPACE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMCOLORSPACE_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMOUTPUTRATE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMOUTPUTRATE_INVALIDRATE,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMOUTPUTRATE_INVALIDFLAG,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMOUTPUTRATE_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSOURCERECT_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSOURCERECT_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSOURCERECT_INVALIDRECT,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMDESTRECT_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMDESTRECT_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMDESTRECT_INVALIDRECT,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMALPHA_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMALPHA_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMALPHA_INVALIDALPHA,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPALETTE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPALETTE_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPALETTE_INVALIDCOUNT,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPALETTE_INVALIDALPHA,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPIXELASPECTRATIO_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPIXELASPECTRATIO_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPIXELASPECTRATIO_INVALIDRATIO,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMLUMAKEY_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMLUMAKEY_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMLUMAKEY_INVALIDRANGE,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMLUMAKEY_UNSUPPORTED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_UNSUPPORTED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_FLIPUNSUPPORTED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_MONOOFFSETUNSUPPORTED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_FORMATUNSUPPORTED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMSTEREOFORMAT_INVALIDFORMAT,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMAUTOPROCESSINGMODE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMAUTOPROCESSINGMODE_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFILTER_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFILTER_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFILTER_INVALIDFILTER,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFILTER_UNSUPPORTED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMFILTER_INVALIDLEVEL,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMEXTENSION_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMEXTENSION_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMFRAMEFORMAT_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMCOLORSPACE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMOUTPUTRATE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMSOURCERECT_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMDESTRECT_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMALPHA_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMPALETTE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMPIXELASPECTRATIO_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMLUMAKEY_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMSTEREOFORMAT_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMAUTOPROCESSINGMODE_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMFILTER_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMEXTENSION_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMEXTENSION_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDSTREAMCOUNT,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_TARGETRECT,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDOUTPUT,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDPASTFRAMES,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDFUTUREFRAMES,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDSOURCERECT,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDDESTRECT,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDINPUTRESOURCE,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDARRAYSIZE,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDARRAY,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_RIGHTEXPECTED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_RIGHTNOTEXPECTED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_STEREONOTENABLED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INVALIDRIGHTRESOURCE,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_NOSTEREOSTREAMS,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_INPUTHAZARD,
    D3D11_MESSAGE_ID_VIDEOPROCESSORBLT_OUTPUTHAZARD,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_NULLPARAM,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_INVALIDTYPE,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_INVALIDBIND,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_UNSUPPORTEDFORMAT,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_INVALIDMIP,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_UNSUPPORTEMIP,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_INVALIDARRAYSIZE,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_INVALIDARRAY,
    D3D11_MESSAGE_ID_CREATEVIDEODECODEROUTPUTVIEW_INVALIDDIMENSION,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_NULLPARAM,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDTYPE,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDBIND,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDMISC,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDUSAGE,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDFORMAT,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDFOURCC,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDMIP,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_UNSUPPORTEDMIP,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDARRAYSIZE,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDARRAY,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDDIMENSION,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_NULLPARAM,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDTYPE,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDBIND,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDFORMAT,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDMIP,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_UNSUPPORTEDMIP,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_UNSUPPORTEDARRAY,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDARRAY,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDDIMENSION,
    D3D11_MESSAGE_ID_DEVICE_DRAW_INVALID_USE_OF_FORCED_SAMPLE_COUNT,
    D3D11_MESSAGE_ID_CREATEBLENDSTATE_INVALIDLOGICOPS,
    D3D11_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDDARRAYWITHDECODER,
    D3D11_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDDARRAYWITHDECODER,
    D3D11_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDDARRAYWITHDECODER,
    D3D11_MESSAGE_ID_DEVICE_LOCKEDOUT_INTERFACE,
    D3D11_MESSAGE_ID_REF_WARNING_ATOMIC_INCONSISTENT,
    D3D11_MESSAGE_ID_REF_WARNING_READING_UNINITIALIZED_RESOURCE,
    D3D11_MESSAGE_ID_REF_WARNING_RAW_HAZARD,
    D3D11_MESSAGE_ID_REF_WARNING_WAR_HAZARD,
    D3D11_MESSAGE_ID_REF_WARNING_WAW_HAZARD,
    D3D11_MESSAGE_ID_CREATECRYPTOSESSION_NULLPARAM,
    D3D11_MESSAGE_ID_CREATECRYPTOSESSION_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_GETCRYPTOTYPE_NULLPARAM,
    D3D11_MESSAGE_ID_GETDECODERPROFILE_NULLPARAM,
    D3D11_MESSAGE_ID_GETCRYPTOSESSIONCERTIFICATESIZE_NULLPARAM,
    D3D11_MESSAGE_ID_GETCRYPTOSESSIONCERTIFICATE_NULLPARAM,
    D3D11_MESSAGE_ID_GETCRYPTOSESSIONCERTIFICATE_WRONGSIZE,
    D3D11_MESSAGE_ID_GETCRYPTOSESSIONHANDLE_WRONGSIZE,
    D3D11_MESSAGE_ID_NEGOTIATECRPYTOSESSIONKEYEXCHANGE_NULLPARAM,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_UNSUPPORTED,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_NULLPARAM,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_SRC_WRONGDEVICE,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_DST_WRONGDEVICE,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_FORMAT_MISMATCH,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_SIZE_MISMATCH,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_SRC_MULTISAMPLED,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_DST_NOT_STAGING,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_SRC_MAPPED,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_DST_MAPPED,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_SRC_OFFERED,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_DST_OFFERED,
    D3D11_MESSAGE_ID_ENCRYPTIONBLT_SRC_CONTENT_UNDEFINED,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_UNSUPPORTED,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_NULLPARAM,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_SRC_WRONGDEVICE,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_DST_WRONGDEVICE,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_FORMAT_MISMATCH,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_SIZE_MISMATCH,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_DST_MULTISAMPLED,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_SRC_NOT_STAGING,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_DST_NOT_RENDER_TARGET,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_SRC_MAPPED,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_DST_MAPPED,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_SRC_OFFERED,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_DST_OFFERED,
    D3D11_MESSAGE_ID_DECRYPTIONBLT_SRC_CONTENT_UNDEFINED,
    D3D11_MESSAGE_ID_STARTSESSIONKEYREFRESH_NULLPARAM,
    D3D11_MESSAGE_ID_STARTSESSIONKEYREFRESH_INVALIDSIZE,
    D3D11_MESSAGE_ID_FINISHSESSIONKEYREFRESH_NULLPARAM,
    D3D11_MESSAGE_ID_GETENCRYPTIONBLTKEY_NULLPARAM,
    D3D11_MESSAGE_ID_GETENCRYPTIONBLTKEY_INVALIDSIZE,
    D3D11_MESSAGE_ID_GETCONTENTPROTECTIONCAPS_NULLPARAM,
    D3D11_MESSAGE_ID_CHECKCRYPTOKEYEXCHANGE_NULLPARAM,
    D3D11_MESSAGE_ID_CHECKCRYPTOKEYEXCHANGE_INVALIDINDEX,
    D3D11_MESSAGE_ID_CREATEAUTHENTICATEDCHANNEL_NULLPARAM,
    D3D11_MESSAGE_ID_CREATEAUTHENTICATEDCHANNEL_UNSUPPORTED,
    D3D11_MESSAGE_ID_CREATEAUTHENTICATEDCHANNEL_INVALIDTYPE,
    D3D11_MESSAGE_ID_CREATEAUTHENTICATEDCHANNEL_OUTOFMEMORY_RETURN,
    D3D11_MESSAGE_ID_GETAUTHENTICATEDCHANNELCERTIFICATESIZE_INVALIDCHANNEL,
    D3D11_MESSAGE_ID_GETAUTHENTICATEDCHANNELCERTIFICATESIZE_NULLPARAM,
    D3D11_MESSAGE_ID_GETAUTHENTICATEDCHANNELCERTIFICATE_INVALIDCHANNEL,
    D3D11_MESSAGE_ID_GETAUTHENTICATEDCHANNELCERTIFICATE_NULLPARAM,
    D3D11_MESSAGE_ID_GETAUTHENTICATEDCHANNELCERTIFICATE_WRONGSIZE,
    D3D11_MESSAGE_ID_NEGOTIATEAUTHENTICATEDCHANNELKEYEXCHANGE_INVALIDCHANNEL,
    D3D11_MESSAGE_ID_NEGOTIATEAUTHENTICATEDCHANNELKEYEXCHANGE_NULLPARAM,
    D3D11_MESSAGE_ID_QUERYAUTHENTICATEDCHANNEL_NULLPARAM,
    D3D11_MESSAGE_ID_QUERYAUTHENTICATEDCHANNEL_WRONGCHANNEL,
    D3D11_MESSAGE_ID_QUERYAUTHENTICATEDCHANNEL_UNSUPPORTEDQUERY,
    D3D11_MESSAGE_ID_QUERYAUTHENTICATEDCHANNEL_WRONGSIZE,
    D3D11_MESSAGE_ID_QUERYAUTHENTICATEDCHANNEL_INVALIDPROCESSINDEX,
    D3D11_MESSAGE_ID_CONFIGUREAUTHENTICATEDCHANNEL_NULLPARAM,
    D3D11_MESSAGE_ID_CONFIGUREAUTHENTICATEDCHANNEL_WRONGCHANNEL,
    D3D11_MESSAGE_ID_CONFIGUREAUTHENTICATEDCHANNEL_UNSUPPORTEDCONFIGURE,
    D3D11_MESSAGE_ID_CONFIGUREAUTHENTICATEDCHANNEL_WRONGSIZE,
    D3D11_MESSAGE_ID_CONFIGUREAUTHENTICATEDCHANNEL_INVALIDPROCESSIDTYPE,
    D3D11_MESSAGE_ID_VSSETCONSTANTBUFFERS_INVALIDBUFFEROFFSETORCOUNT,
    D3D11_MESSAGE_ID_DSSETCONSTANTBUFFERS_INVALIDBUFFEROFFSETORCOUNT,
    D3D11_MESSAGE_ID_HSSETCONSTANTBUFFERS_INVALIDBUFFEROFFSETORCOUNT,
    D3D11_MESSAGE_ID_GSSETCONSTANTBUFFERS_INVALIDBUFFEROFFSETORCOUNT,
    D3D11_MESSAGE_ID_PSSETCONSTANTBUFFERS_INVALIDBUFFEROFFSETORCOUNT,
    D3D11_MESSAGE_ID_CSSETCONSTANTBUFFERS_INVALIDBUFFEROFFSETORCOUNT,
    D3D11_MESSAGE_ID_NEGOTIATECRPYTOSESSIONKEYEXCHANGE_INVALIDSIZE,
    D3D11_MESSAGE_ID_NEGOTIATEAUTHENTICATEDCHANNELKEYEXCHANGE_INVALIDSIZE,
    D3D11_MESSAGE_ID_OFFERRESOURCES_INVALIDPRIORITY,
    D3D11_MESSAGE_ID_GETCRYPTOSESSIONHANDLE_OUTOFMEMORY,
    D3D11_MESSAGE_ID_ACQUIREHANDLEFORCAPTURE_NULLPARAM,
    D3D11_MESSAGE_ID_ACQUIREHANDLEFORCAPTURE_INVALIDTYPE,
    D3D11_MESSAGE_ID_ACQUIREHANDLEFORCAPTURE_INVALIDBIND,
    D3D11_MESSAGE_ID_ACQUIREHANDLEFORCAPTURE_INVALIDARRAY,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMROTATION_NULLPARAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMROTATION_INVALIDSTREAM,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMROTATION_INVALID,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMROTATION_UNSUPPORTED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORGETSTREAMROTATION_NULLPARAM,
    D3D11_MESSAGE_ID_DEVICE_CLEARVIEW_INVALIDVIEW,
    D3D11_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_DOUBLEEXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_SHADEREXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEHULLSHADER_DOUBLEEXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEHULLSHADER_SHADEREXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_DOUBLEEXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_SHADEREXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_DOUBLEEXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_SHADEREXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DOUBLEEXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_SHADEREXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_DOUBLEEXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_SHADEREXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_DOUBLEEXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_SHADEREXTENSIONSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_SHADER_LINKAGE_MINPRECISION,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMALPHA_UNSUPPORTED,
    D3D11_MESSAGE_ID_VIDEOPROCESSORSETSTREAMPIXELASPECTRATIO_UNSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_UAVSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEHULLSHADER_UAVSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_UAVSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_UAVSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UAVSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_UAVSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_UAVSNOTSUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETSANDUNORDEREDACCESSVIEWS_INVALIDOFFSET,
    D3D11_MESSAGE_ID_DEVICE_OMSETRENDERTARGETSANDUNORDEREDACCESSVIEWS_TOOMANYVIEWS,
    D3D11_MESSAGE_ID_DEVICE_CLEARVIEW_NOTSUPPORTED,
    D3D11_MESSAGE_ID_SWAPDEVICECONTEXTSTATE_NOTSUPPORTED,
    D3D11_MESSAGE_ID_UPDATESUBRESOURCE_PREFERUPDATESUBRESOURCE1,
    D3D11_MESSAGE_ID_GETDC_INACCESSIBLE,
    D3D11_MESSAGE_ID_DEVICE_CLEARVIEW_INVALIDRECT,
    D3D11_MESSAGE_ID_DEVICE_DRAW_SAMPLE_MASK_IGNORED_ON_FL9,
    D3D11_MESSAGE_ID_DEVICE_OPEN_SHARED_RESOURCE1_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_DEVICE_OPEN_SHARED_RESOURCE_BY_NAME_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_ENQUEUESETEVENT_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_OFFERRELEASE_NOT_SUPPORTED,
    D3D11_MESSAGE_ID_OFFERRESOURCES_INACCESSIBLE,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSORINPUTVIEW_INVALIDMSAA,
    D3D11_MESSAGE_ID_CREATEVIDEOPROCESSOROUTPUTVIEW_INVALIDMSAA,
    D3D11_MESSAGE_ID_DEVICE_CLEARVIEW_INVALIDSOURCERECT,
    D3D11_MESSAGE_ID_DEVICE_CLEARVIEW_EMPTYRECT,
    D3D11_MESSAGE_ID_UPDATESUBRESOURCE_EMPTYDESTBOX,
    D3D11_MESSAGE_ID_COPYSUBRESOURCEREGION_EMPTYSOURCEBOX,
    D3D11_MESSAGE_ID_DEVICE_DRAW_OM_RENDER_TARGET_DOES_NOT_SUPPORT_LOGIC_OPS,
    D3D11_MESSAGE_ID_DEVICE_DRAW_DEPTHSTENCILVIEW_NOT_SET,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RENDERTARGETVIEW_NOT_SET,
    D3D11_MESSAGE_ID_DEVICE_DRAW_RENDERTARGETVIEW_NOT_SET_DUE_TO_FLIP_PRESENT,
    D3D11_MESSAGE_ID_DEVICE_UNORDEREDACCESSVIEW_NOT_SET_DUE_TO_FLIP_PRESENT,
    D3D11_MESSAGE_ID_D3D11_1_MESSAGES_END
} D3D11_MESSAGE_ID;

typedef enum D3D11_RLDO_FLAGS {
    D3D11_RLDO_SUMMARY = 1,
    D3D11_RLDO_DETAIL = 2
} D3D11_RLDO_FLAGS;

typedef struct D3D11_MESSAGE {
    D3D11_MESSAGE_CATEGORY Category;
    D3D11_MESSAGE_SEVERITY Severity;
    D3D11_MESSAGE_ID ID;
    const char *pDescription;
    SIZE_T DescriptionByteLength;
} D3D11_MESSAGE;

typedef struct D3D11_INFO_QUEUE_FILTER_DESC {
    UINT NumCategories;
    D3D11_MESSAGE_CATEGORY *pCategoryList;
    UINT NumSeverities;
    D3D11_MESSAGE_SEVERITY *pSeverityList;
    UINT NumIDs;
    D3D11_MESSAGE_ID *pIDList;
} D3D11_INFO_QUEUE_FILTER_DESC;

typedef struct D3D11_INFO_QUEUE_FILTER {
    D3D11_INFO_QUEUE_FILTER_DESC AllowList;
    D3D11_INFO_QUEUE_FILTER_DESC DenyList;
} D3D11_INFO_QUEUE_FILTER;

cpp_quote("#define D3D11_INFO_QUEUE_DEFAULT_MESSAGE_COUNT_LIMIT 1024")

[
    object,
    uuid(79cf2233-7536-4948-9d36-1e4692dc5760),
    local,
    pointer_default(unique)
]
interface ID3D11Debug : IUnknown {
    HRESULT SetFeatureMask(UINT Mask);
    UINT GetFeatureMask();
    HRESULT SetPresentPerRenderOpDelay(UINT Milliseconds);
    UINT GetPresentPerRenderOpDelay();
    HRESULT SetSwapChain(IDXGISwapChain *pSwapChain);
    HRESULT GetSwapChain(IDXGISwapChain **ppSwapChain);
    HRESULT ValidateContext(ID3D11DeviceContext *pContext);
    HRESULT ReportLiveDeviceObjects(D3D11_RLDO_FLAGS Flags);
    HRESULT ValidateContextForDispatch(ID3D11DeviceContext *pContext);
}

[
    object,
    uuid(1ef337e3-58e7-4f83-a692-db221f5ed47e),
    local,
    pointer_default(unique)
]
interface ID3D11SwitchToRef : IUnknown {
    BOOL SetUseRef(
        [in] BOOL useref
    );
    BOOL GetUseRef();
}

[
    object,
    uuid(6543dbb6-1b48-42f5-ab82-e97ec74326f6),
    local,
    pointer_default(unique)
]
interface ID3D11InfoQueue : IUnknown {
    HRESULT SetMessageCountLimit(UINT64 MessageCountLimit);
    void ClearStoredMessages();

cpp_quote("#ifdef WINE_NO_UNICODE_MACROS")
cpp_quote("#undef GetMessage")
cpp_quote("#endif")
    HRESULT GetMessage(UINT64 MessageIndex, D3D11_MESSAGE* pMessage, SIZE_T *pMessageByteLength);

    UINT64 GetNumMessagesAllowedByStorageFilter();
    UINT64 GetNumMessagesDeniedByStorageFilter();
    UINT64 GetNumStoredMessages();
    UINT64 GetNumStoredMessagesAllowedByRetrievalFilter();
    UINT64 GetNumMessagesDiscardedByMessageCountLimit();
    UINT64 GetMessageCountLimit();
    HRESULT AddStorageFilterEntries(D3D11_INFO_QUEUE_FILTER *pFilter);
    HRESULT GetStorageFilter(D3D11_INFO_QUEUE_FILTER *pFilter, SIZE_T *pFilterByteLength);
    void ClearStorageFilter();
    HRESULT PushEmptyStorageFilter();
    HRESULT PushCopyOfStorageFilter();
    HRESULT PushStorageFilter(D3D11_INFO_QUEUE_FILTER *pFilter);
    void PopStorageFilter();
    UINT GetStorageFilterStackSize();
    HRESULT AddRetrievalFilterEntries(D3D11_INFO_QUEUE_FILTER *pFilter);
    HRESULT GetRetrievalFilter(D3D11_INFO_QUEUE_FILTER *pFilter, SIZE_T *pFilterByteLength);
    void ClearRetrievalFilter();
    HRESULT PushEmptyRetrievalFilter();
    HRESULT PushCopyOfRetrievalFilter();
    HRESULT PushRetrievalFilter(D3D11_INFO_QUEUE_FILTER *pFilter);
    void PopRetrievalFilter();
    UINT GetRetrievalFilterStackSize();
    HRESULT AddMessage(D3D11_MESSAGE_CATEGORY Category, D3D11_MESSAGE_SEVERITY Severity,
            D3D11_MESSAGE_ID ID, LPCSTR pDescription);
    HRESULT AddApplicationMessage(D3D11_MESSAGE_SEVERITY Severity, LPCSTR pDescription);
    HRESULT SetBreakOnCategory(D3D11_MESSAGE_CATEGORY Category, BOOL bEnable);
    HRESULT SetBreakOnSeverity(D3D11_MESSAGE_SEVERITY Severity, BOOL bEnable);
    HRESULT SetBreakOnID(D3D11_MESSAGE_ID ID, BOOL bEnable);
    BOOL GetBreakOnCategory(D3D11_MESSAGE_CATEGORY Category);
    BOOL GetBreakOnSeverity(D3D11_MESSAGE_SEVERITY Severity);
    BOOL GetBreakOnID(D3D11_MESSAGE_ID ID);
    void SetMuteDebugOutput(BOOL bMute);
    BOOL GetMuteDebugOutput();
}
