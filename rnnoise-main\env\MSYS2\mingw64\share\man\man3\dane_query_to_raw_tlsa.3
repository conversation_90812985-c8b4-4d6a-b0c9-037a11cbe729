.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "dane_query_to_raw_tlsa" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
dane_query_to_raw_tlsa \- API function
.SH SYNOPSIS
.B #include <gnutls/dane.h>
.sp
.BI "int dane_query_to_raw_tlsa(dane_query_t " q ", unsigned int * " data_entries ", char *** " dane_data ", int ** " dane_data_len ", int * " secure ", int * " bogus ");"
.SH ARGUMENTS
.IP "dane_query_t q" 12
The query result structure
.IP "unsigned int * data_entries" 12
Pointer set to the number of entries in the query
.IP "char *** dane_data" 12
Pointer to contain an array of DNS rdata items, terminated with a NULL pointer;
caller must guarantee that the referenced data remains
valid until \fBdane_query_deinit()\fP is called.
.IP "int ** dane_data_len" 12
Pointer to contain the length n bytes of the dane_data items
.IP "int * secure" 12
Pointer set true if the result is validated securely, false if
validation failed or the domain queried has no security info
.IP "int * bogus" 12
Pointer set true if the result was not secure due to a security failure
.SH "DESCRIPTION"
This function will provide the DANE data from the query
response.

The pointers dane_data and dane_data_len are allocated with \fBgnutls_malloc()\fP
to contain the data from the query result structure (individual
 \fIdane_data\fP items simply point to the original data and are not allocated separately).
The returned  \fIdane_data\fP are only valid during the lifetime of  \fIq\fP .
.SH "RETURNS"
On success, \fBDANE_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
