/*
 * Copyright 2021 R<PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "unknwn.idl";
import "ocidl.idl";
import "mfidl.idl";

[
    odl,
    uuid(6d6cf803-1ec0-4c8d-b3ca-f18e27282074),
    oleautomation
]
interface IWMPVideoRenderConfig : IUnknown
{
    [propput] HRESULT presenterActivate([in] IMFActivate *activate);
}

[
    odl,
    uuid(959506c1-0314-4ec5-9e61-8528db5e5478),
    oleautomation
]
interface IWMPRenderConfig : IUnknown
{
    [propput] HRESULT inProcOnly([in] BOOL fInProc);

    [propget] HRESULT inProcOnly([out, retval] BOOL *pfInProc);
}
