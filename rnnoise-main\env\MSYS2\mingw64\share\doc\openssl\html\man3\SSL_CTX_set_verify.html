<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CTX_set_verify</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_get_ex_data_X509_STORE_CTX_idx, SSL_CTX_set_verify, SSL_set_verify, SSL_CTX_set_verify_depth, SSL_set_verify_depth, SSL_verify_cb, SSL_verify_client_post_handshake, SSL_set_post_handshake_auth, SSL_CTX_set_post_handshake_auth - set various SSL/TLS parameters for peer certificate verification</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

typedef int (*SSL_verify_cb)(int preverify_ok, X509_STORE_CTX *x509_ctx);

void SSL_CTX_set_verify(SSL_CTX *ctx, int mode, SSL_verify_cb verify_callback);
void SSL_set_verify(SSL *ssl, int mode, SSL_verify_cb verify_callback);
SSL_get_ex_data_X509_STORE_CTX_idx(void);

void SSL_CTX_set_verify_depth(SSL_CTX *ctx, int depth);
void SSL_set_verify_depth(SSL *ssl, int depth);

int SSL_verify_client_post_handshake(SSL *ssl);
void SSL_CTX_set_post_handshake_auth(SSL_CTX *ctx, int val);
void SSL_set_post_handshake_auth(SSL *ssl, int val);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_CTX_set_verify() sets the verification flags for <b>ctx</b> to be <b>mode</b> and specifies the <b>verify_callback</b> function to be used. If no callback function shall be specified, the NULL pointer can be used for <b>verify_callback</b>. <b>ctx</b> <b>MUST NOT</b> be NULL.</p>

<p>SSL_set_verify() sets the verification flags for <b>ssl</b> to be <b>mode</b> and specifies the <b>verify_callback</b> function to be used. If no callback function shall be specified, the NULL pointer can be used for <b>verify_callback</b>. In this case last <b>verify_callback</b> set specifically for this <b>ssl</b> remains. If no special <b>callback</b> was set before, the default callback for the underlying <b>ctx</b> is used, that was valid at the time <b>ssl</b> was created with <a href="../man3/SSL_new.html">SSL_new(3)</a>. Within the callback function, <b>SSL_get_ex_data_X509_STORE_CTX_idx</b> can be called to get the data index of the current SSL object that is doing the verification.</p>

<p>In client mode <b>verify_callback</b> may also call the <a href="../man3/SSL_set_retry_verify.html">SSL_set_retry_verify(3)</a> function on the <b>SSL</b> object set in the <i>x509_store_ctx</i> ex data (see <a href="../man3/SSL_get_ex_data_X509_STORE_CTX_idx.html">SSL_get_ex_data_X509_STORE_CTX_idx(3)</a>) and return 1. This would be typically done in case the certificate verification was not yet able to succeed. This makes the handshake suspend and return control to the calling application with <b>SSL_ERROR_WANT_RETRY_VERIFY</b>. The application can for instance fetch further certificates or cert status information needed for the verification. Calling <a href="../man3/SSL_connect.html">SSL_connect(3)</a> again resumes the connection attempt by retrying the server certificate verification step. This process may even be repeated if need be. Note that the handshake may still be aborted if a subsequent invocation of the callback (e.g., at a lower depth, or for a separate error condition) returns 0.</p>

<p>SSL_CTX_set_verify_depth() sets the maximum <b>depth</b> for the certificate chain verification that shall be allowed for <b>ctx</b>.</p>

<p>SSL_set_verify_depth() sets the maximum <b>depth</b> for the certificate chain verification that shall be allowed for <b>ssl</b>.</p>

<p>SSL_CTX_set_post_handshake_auth() and SSL_set_post_handshake_auth() enable the Post-Handshake Authentication extension to be added to the ClientHello such that post-handshake authentication can be requested by the server. If <b>val</b> is 0 then the extension is not sent, otherwise it is. By default the extension is not sent. A certificate callback will need to be set via SSL_CTX_set_client_cert_cb() if no certificate is provided at initialization.</p>

<p>SSL_verify_client_post_handshake() causes a CertificateRequest message to be sent by a server on the given <b>ssl</b> connection. The SSL_VERIFY_PEER flag must be set; the SSL_VERIFY_POST_HANDSHAKE flag is optional.</p>

<h1 id="NOTES">NOTES</h1>

<p>The verification of certificates can be controlled by a set of logically or&#39;ed <b>mode</b> flags:</p>

<dl>

<dt id="SSL_VERIFY_NONE">SSL_VERIFY_NONE</dt>
<dd>

<p><b>Server mode:</b> the server will not send a client certificate request to the client, so the client will not send a certificate.</p>

<p><b>Client mode:</b> if not using an anonymous cipher (by default disabled), the server will send a certificate which will be checked. The result of the certificate verification process can be checked after the TLS/SSL handshake using the <a href="../man3/SSL_get_verify_result.html">SSL_get_verify_result(3)</a> function. The handshake will be continued regardless of the verification result.</p>

</dd>
<dt id="SSL_VERIFY_PEER">SSL_VERIFY_PEER</dt>
<dd>

<p><b>Server mode:</b> the server sends a client certificate request to the client. The certificate returned (if any) is checked. If the verification process fails, the TLS/SSL handshake is immediately terminated with an alert message containing the reason for the verification failure. The behaviour can be controlled by the additional SSL_VERIFY_FAIL_IF_NO_PEER_CERT, SSL_VERIFY_CLIENT_ONCE and SSL_VERIFY_POST_HANDSHAKE flags.</p>

<p><b>Client mode:</b> the server certificate is verified. If the verification process fails, the TLS/SSL handshake is immediately terminated with an alert message containing the reason for the verification failure. If no server certificate is sent, because an anonymous cipher is used, SSL_VERIFY_PEER is ignored.</p>

</dd>
<dt id="SSL_VERIFY_FAIL_IF_NO_PEER_CERT">SSL_VERIFY_FAIL_IF_NO_PEER_CERT</dt>
<dd>

<p><b>Server mode:</b> if the client did not return a certificate, the TLS/SSL handshake is immediately terminated with a &quot;handshake failure&quot; alert. This flag must be used together with SSL_VERIFY_PEER.</p>

<p><b>Client mode:</b> ignored (see BUGS)</p>

</dd>
<dt id="SSL_VERIFY_CLIENT_ONCE">SSL_VERIFY_CLIENT_ONCE</dt>
<dd>

<p><b>Server mode:</b> only request a client certificate once during the connection. Do not ask for a client certificate again during renegotiation or post-authentication if a certificate was requested during the initial handshake. This flag must be used together with SSL_VERIFY_PEER.</p>

<p><b>Client mode:</b> ignored (see BUGS)</p>

</dd>
<dt id="SSL_VERIFY_POST_HANDSHAKE">SSL_VERIFY_POST_HANDSHAKE</dt>
<dd>

<p><b>Server mode:</b> the server will not send a client certificate request during the initial handshake, but will send the request via SSL_verify_client_post_handshake(). This allows the SSL_CTX or SSL to be configured for post-handshake peer verification before the handshake occurs. This flag must be used together with SSL_VERIFY_PEER. TLSv1.3 only; no effect on pre-TLSv1.3 connections.</p>

<p><b>Client mode:</b> ignored (see BUGS)</p>

</dd>
</dl>

<p>If the <b>mode</b> is SSL_VERIFY_NONE none of the other flags may be set.</p>

<p>If verification flags are not modified explicitly by <code>SSL_CTX_set_verify()</code> or <code>SSL_set_verify()</code>, the default value will be SSL_VERIFY_NONE.</p>

<p>The actual verification procedure is performed either using the built-in verification procedure or using another application provided verification function set with <a href="../man3/SSL_CTX_set_cert_verify_callback.html">SSL_CTX_set_cert_verify_callback(3)</a>. The following descriptions apply in the case of the built-in procedure. An application provided procedure also has access to the verify depth information and the verify_callback() function, but the way this information is used may be different.</p>

<p>SSL_CTX_set_verify_depth() and SSL_set_verify_depth() set a limit on the number of certificates between the end-entity and trust-anchor certificates. Neither the end-entity nor the trust-anchor certificates count against <b>depth</b>. If the certificate chain needed to reach a trusted issuer is longer than <b>depth+2</b>, X509_V_ERR_CERT_CHAIN_TOO_LONG will be issued. The depth count is &quot;level 0:peer certificate&quot;, &quot;level 1: CA certificate&quot;, &quot;level 2: higher level CA certificate&quot;, and so on. Setting the maximum depth to 2 allows the levels 0, 1, 2 and 3 (0 being the end-entity and 3 the trust-anchor). The default depth limit is 100, allowing for the peer certificate, at most 100 intermediate CA certificates and a final trust anchor certificate.</p>

<p>The <b>verify_callback</b> function is used to control the behaviour when the SSL_VERIFY_PEER flag is set. It must be supplied by the application and receives two arguments: <b>preverify_ok</b> indicates, whether the verification of the certificate in question was passed (preverify_ok=1) or not (preverify_ok=0). <b>x509_ctx</b> is a pointer to the complete context used for the certificate chain verification.</p>

<p>The certificate chain is checked starting with the deepest nesting level (the root CA certificate) and worked upward to the peer&#39;s certificate. At each level signatures and issuer attributes are checked. Whenever a verification error is found, the error number is stored in <b>x509_ctx</b> and <b>verify_callback</b> is called with <b>preverify_ok</b>=0. By applying X509_CTX_store_* functions <b>verify_callback</b> can locate the certificate in question and perform additional steps (see EXAMPLES). If no error is found for a certificate, <b>verify_callback</b> is called with <b>preverify_ok</b>=1 before advancing to the next level.</p>

<p>The return value of <b>verify_callback</b> controls the strategy of the further verification process. If <b>verify_callback</b> returns 0, the verification process is immediately stopped with &quot;verification failed&quot; state. If SSL_VERIFY_PEER is set, a verification failure alert is sent to the peer and the TLS/SSL handshake is terminated. If <b>verify_callback</b> returns 1, the verification process is continued. If <b>verify_callback</b> always returns 1, the TLS/SSL handshake will not be terminated with respect to verification failures and the connection will be established. The calling process can however retrieve the error code of the last verification error using <a href="../man3/SSL_get_verify_result.html">SSL_get_verify_result(3)</a> or by maintaining its own error storage managed by <b>verify_callback</b>.</p>

<p>If no <b>verify_callback</b> is specified, the default callback will be used. Its return value is identical to <b>preverify_ok</b>, so that any verification failure will lead to a termination of the TLS/SSL handshake with an alert message, if SSL_VERIFY_PEER is set.</p>

<p>After calling SSL_set_post_handshake_auth(), the client will need to add a certificate or certificate callback to its configuration before it can successfully authenticate. This must be called before SSL_connect().</p>

<p>SSL_verify_client_post_handshake() requires that verify flags have been previously set, and that a client sent the post-handshake authentication extension. When the client returns a certificate the verify callback will be invoked. A write operation must take place for the Certificate Request to be sent to the client, this can be done with SSL_do_handshake() or SSL_write_ex(). Only one certificate request may be outstanding at any time.</p>

<p>When post-handshake authentication occurs, a refreshed NewSessionTicket message is sent to the client.</p>

<p>Post-handshake authentication cannot be used with QUIC. SSL_set_post_handshake_auth() has no effect if called on a QUIC SSL object.</p>

<h1 id="BUGS">BUGS</h1>

<p>In client mode, it is not checked whether the SSL_VERIFY_PEER flag is set, but whether any flags other than SSL_VERIFY_NONE are set. This can lead to unexpected behaviour if SSL_VERIFY_PEER and other flags are not used as required.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The SSL*_set_verify*() functions do not provide diagnostic information.</p>

<p>The SSL_verify_client_post_handshake() function returns 1 if the request succeeded, and 0 if the request failed. The error stack can be examined to determine the failure reason.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>The following code sequence realizes an example <b>verify_callback</b> function that will always continue the TLS/SSL handshake regardless of verification failure, if wished. The callback realizes a verification depth limit with more informational output.</p>

<p>All verification errors are printed; information about the certificate chain is printed on request. The example is realized for a server that does allow but not require client certificates.</p>

<p>The example makes use of the ex_data technique to store application data into/retrieve application data from the SSL structure (see <a href="../man3/CRYPTO_get_ex_new_index.html">CRYPTO_get_ex_new_index(3)</a>, <a href="../man3/SSL_get_ex_data_X509_STORE_CTX_idx.html">SSL_get_ex_data_X509_STORE_CTX_idx(3)</a>).</p>

<pre><code>...
typedef struct {
  int verbose_mode;
  int verify_depth;
  int always_continue;
} mydata_t;
int mydata_index;

...
static int verify_callback(int preverify_ok, X509_STORE_CTX *ctx)
{
    char    buf[256];
    X509   *err_cert;
    int     err, depth;
    SSL    *ssl;
    mydata_t *mydata;

    err_cert = X509_STORE_CTX_get_current_cert(ctx);
    err = X509_STORE_CTX_get_error(ctx);
    depth = X509_STORE_CTX_get_error_depth(ctx);

    /*
     * Retrieve the pointer to the SSL of the connection currently treated
     * and the application specific data stored into the SSL object.
     */
    ssl = X509_STORE_CTX_get_ex_data(ctx, SSL_get_ex_data_X509_STORE_CTX_idx());
    mydata = SSL_get_ex_data(ssl, mydata_index);

    X509_NAME_oneline(X509_get_subject_name(err_cert), buf, 256);

    /*
     * Catch a too long certificate chain. The depth limit set using
     * SSL_CTX_set_verify_depth() is by purpose set to &quot;limit+1&quot; so
     * that whenever the &quot;depth&gt;verify_depth&quot; condition is met, we
     * have violated the limit and want to log this error condition.
     * We must do it here, because the CHAIN_TOO_LONG error would not
     * be found explicitly; only errors introduced by cutting off the
     * additional certificates would be logged.
     */
    if (depth &gt; mydata-&gt;verify_depth) {
        preverify_ok = 0;
        err = X509_V_ERR_CERT_CHAIN_TOO_LONG;
        X509_STORE_CTX_set_error(ctx, err);
    }
    if (!preverify_ok) {
        printf(&quot;verify error:num=%d:%s:depth=%d:%s\n&quot;, err,
               X509_verify_cert_error_string(err), depth, buf);
    } else if (mydata-&gt;verbose_mode) {
        printf(&quot;depth=%d:%s\n&quot;, depth, buf);
    }

    /*
     * At this point, err contains the last verification error. We can use
     * it for something special
     */
    if (!preverify_ok &amp;&amp; (err == X509_V_ERR_UNABLE_TO_GET_ISSUER_CERT)) {
        X509_NAME_oneline(X509_get_issuer_name(err_cert), buf, 256);
        printf(&quot;issuer= %s\n&quot;, buf);
    }

    if (mydata-&gt;always_continue)
        return 1;
    else
        return preverify_ok;
}
...

mydata_t mydata;

...
mydata_index = SSL_get_ex_new_index(0, &quot;mydata index&quot;, NULL, NULL, NULL);

...
SSL_CTX_set_verify(ctx, SSL_VERIFY_PEER | SSL_VERIFY_CLIENT_ONCE,
                   verify_callback);

/*
 * Let the verify_callback catch the verify_depth error so that we get
 * an appropriate error in the logfile.
 */
SSL_CTX_set_verify_depth(verify_depth + 1);

/*
 * Set up the SSL specific data into &quot;mydata&quot; and store it into th SSL
 * structure.
 */
mydata.verify_depth = verify_depth; ...
SSL_set_ex_data(ssl, mydata_index, &amp;mydata);

...
SSL_accept(ssl);       /* check of success left out for clarity */
if (peer = SSL_get_peer_certificate(ssl)) {
    if (SSL_get_verify_result(ssl) == X509_V_OK) {
        /* The client sent a certificate which verified OK */
    }
}</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_new.html">SSL_new(3)</a>, <a href="../man3/SSL_CTX_get_verify_mode.html">SSL_CTX_get_verify_mode(3)</a>, <a href="../man3/SSL_get_verify_result.html">SSL_get_verify_result(3)</a>, <a href="../man3/SSL_CTX_load_verify_locations.html">SSL_CTX_load_verify_locations(3)</a>, <a href="../man3/SSL_get_peer_certificate.html">SSL_get_peer_certificate(3)</a>, <a href="../man3/SSL_CTX_set_cert_verify_callback.html">SSL_CTX_set_cert_verify_callback(3)</a>, <a href="../man3/SSL_get_ex_data_X509_STORE_CTX_idx.html">SSL_get_ex_data_X509_STORE_CTX_idx(3)</a>, <a href="../man3/SSL_CTX_set_client_cert_cb.html">SSL_CTX_set_client_cert_cb(3)</a>, <a href="../man3/CRYPTO_get_ex_new_index.html">CRYPTO_get_ex_new_index(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The SSL_VERIFY_POST_HANDSHAKE option, and the SSL_verify_client_post_handshake() and SSL_set_post_handshake_auth() functions were added in OpenSSL 1.1.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


