<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>liblzma (XZ Utils): lzma/bcj.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">liblzma (XZ Utils)<span id="projectnumber">&#160;5.8.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('bcj_8h.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<div class="header">
  <div class="summary">
<a href="#nested-classes">Data Structures</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle"><div class="title">bcj.h File Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Branch/Call/Jump conversion filters.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Data Structures</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structlzma__options__bcj.html">lzma_options_bcj</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Options for BCJ filters.  <a href="structlzma__options__bcj.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="define-members" name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:aa9eac1f580ddde3309518cd153d596b1" id="r_aa9eac1f580ddde3309518cd153d596b1"><td class="memItemLeft" align="right" valign="top"><a id="aa9eac1f580ddde3309518cd153d596b1" name="aa9eac1f580ddde3309518cd153d596b1"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_X86</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x04)</td></tr>
<tr class="memdesc:aa9eac1f580ddde3309518cd153d596b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for x86 binaries. <br /></td></tr>
<tr class="separator:aa9eac1f580ddde3309518cd153d596b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7f667d4a5d319f227f23163cbea086f" id="r_ab7f667d4a5d319f227f23163cbea086f"><td class="memItemLeft" align="right" valign="top"><a id="ab7f667d4a5d319f227f23163cbea086f" name="ab7f667d4a5d319f227f23163cbea086f"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_POWERPC</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x05)</td></tr>
<tr class="memdesc:ab7f667d4a5d319f227f23163cbea086f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for Big endian PowerPC binaries. <br /></td></tr>
<tr class="separator:ab7f667d4a5d319f227f23163cbea086f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2fe36218a38f400e1ce40820758f7427" id="r_a2fe36218a38f400e1ce40820758f7427"><td class="memItemLeft" align="right" valign="top"><a id="a2fe36218a38f400e1ce40820758f7427" name="a2fe36218a38f400e1ce40820758f7427"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_IA64</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x06)</td></tr>
<tr class="memdesc:a2fe36218a38f400e1ce40820758f7427"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for IA-64 (Itanium) binaries. <br /></td></tr>
<tr class="separator:a2fe36218a38f400e1ce40820758f7427"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a495a58f63ebc7a8b756099efba492f8b" id="r_a495a58f63ebc7a8b756099efba492f8b"><td class="memItemLeft" align="right" valign="top"><a id="a495a58f63ebc7a8b756099efba492f8b" name="a495a58f63ebc7a8b756099efba492f8b"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_ARM</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x07)</td></tr>
<tr class="memdesc:a495a58f63ebc7a8b756099efba492f8b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for ARM binaries. <br /></td></tr>
<tr class="separator:a495a58f63ebc7a8b756099efba492f8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ec62e7e5e7df3d9af5b2ea3f857689a" id="r_a5ec62e7e5e7df3d9af5b2ea3f857689a"><td class="memItemLeft" align="right" valign="top"><a id="a5ec62e7e5e7df3d9af5b2ea3f857689a" name="a5ec62e7e5e7df3d9af5b2ea3f857689a"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_ARMTHUMB</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x08)</td></tr>
<tr class="memdesc:a5ec62e7e5e7df3d9af5b2ea3f857689a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for ARM-Thumb binaries. <br /></td></tr>
<tr class="separator:a5ec62e7e5e7df3d9af5b2ea3f857689a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a50941088e93ef659c6b000bbcaf58143" id="r_a50941088e93ef659c6b000bbcaf58143"><td class="memItemLeft" align="right" valign="top"><a id="a50941088e93ef659c6b000bbcaf58143" name="a50941088e93ef659c6b000bbcaf58143"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_SPARC</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x09)</td></tr>
<tr class="memdesc:a50941088e93ef659c6b000bbcaf58143"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for SPARC binaries. <br /></td></tr>
<tr class="separator:a50941088e93ef659c6b000bbcaf58143"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a01765158cd31cac21b272b180628fc4b" id="r_a01765158cd31cac21b272b180628fc4b"><td class="memItemLeft" align="right" valign="top"><a id="a01765158cd31cac21b272b180628fc4b" name="a01765158cd31cac21b272b180628fc4b"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_ARM64</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x0A)</td></tr>
<tr class="memdesc:a01765158cd31cac21b272b180628fc4b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for ARM64 binaries. <br /></td></tr>
<tr class="separator:a01765158cd31cac21b272b180628fc4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a932e9d66e945f5601b8fad7445a9b40c" id="r_a932e9d66e945f5601b8fad7445a9b40c"><td class="memItemLeft" align="right" valign="top"><a id="a932e9d66e945f5601b8fad7445a9b40c" name="a932e9d66e945f5601b8fad7445a9b40c"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>LZMA_FILTER_RISCV</b>&#160;&#160;&#160;<a class="el" href="vli_8h.html#a2d8bf5322898bfa11945848420585881">LZMA_VLI_C</a>(0x0B)</td></tr>
<tr class="memdesc:a932e9d66e945f5601b8fad7445a9b40c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter for RISC-V binaries. <br /></td></tr>
<tr class="separator:a932e9d66e945f5601b8fad7445a9b40c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="func-members" name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a864bc77639e1e70a2b50f3f03424e482" id="r_a864bc77639e1e70a2b50f3f03424e482"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a864bc77639e1e70a2b50f3f03424e482">lzma_bcj_arm64_encode</a> (uint32_t start_offset, uint8_t *buf, size_t size) lzma_nothrow</td></tr>
<tr class="memdesc:a864bc77639e1e70a2b50f3f03424e482"><td class="mdescLeft">&#160;</td><td class="mdescRight">Raw ARM64 BCJ encoder.  <br /></td></tr>
<tr class="separator:a864bc77639e1e70a2b50f3f03424e482"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1989d14c1abfb1cab1e6281b78b12250" id="r_a1989d14c1abfb1cab1e6281b78b12250"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a1989d14c1abfb1cab1e6281b78b12250">lzma_bcj_arm64_decode</a> (uint32_t start_offset, uint8_t *buf, size_t size) lzma_nothrow</td></tr>
<tr class="memdesc:a1989d14c1abfb1cab1e6281b78b12250"><td class="mdescLeft">&#160;</td><td class="mdescRight">Raw ARM64 BCJ decoder.  <br /></td></tr>
<tr class="separator:a1989d14c1abfb1cab1e6281b78b12250"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3c11efb1d448aaabd95595fb1c68ab01" id="r_a3c11efb1d448aaabd95595fb1c68ab01"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3c11efb1d448aaabd95595fb1c68ab01">lzma_bcj_riscv_encode</a> (uint32_t start_offset, uint8_t *buf, size_t size) lzma_nothrow</td></tr>
<tr class="memdesc:a3c11efb1d448aaabd95595fb1c68ab01"><td class="mdescLeft">&#160;</td><td class="mdescRight">Raw RISC-V BCJ encoder.  <br /></td></tr>
<tr class="separator:a3c11efb1d448aaabd95595fb1c68ab01"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7379d9357c8c7fc100a55d0c53a29021" id="r_a7379d9357c8c7fc100a55d0c53a29021"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a7379d9357c8c7fc100a55d0c53a29021">lzma_bcj_riscv_decode</a> (uint32_t start_offset, uint8_t *buf, size_t size) lzma_nothrow</td></tr>
<tr class="memdesc:a7379d9357c8c7fc100a55d0c53a29021"><td class="mdescLeft">&#160;</td><td class="mdescRight">Raw RISC-V BCJ decoder.  <br /></td></tr>
<tr class="separator:a7379d9357c8c7fc100a55d0c53a29021"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab17e110b27302635b7f97cf549b4b83d" id="r_ab17e110b27302635b7f97cf549b4b83d"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ab17e110b27302635b7f97cf549b4b83d">lzma_bcj_x86_encode</a> (uint32_t start_offset, uint8_t *buf, size_t size) lzma_nothrow</td></tr>
<tr class="memdesc:ab17e110b27302635b7f97cf549b4b83d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Raw x86 BCJ encoder.  <br /></td></tr>
<tr class="separator:ab17e110b27302635b7f97cf549b4b83d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a24814feed2515ef61d30f61c8ae19500" id="r_a24814feed2515ef61d30f61c8ae19500"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a24814feed2515ef61d30f61c8ae19500">lzma_bcj_x86_decode</a> (uint32_t start_offset, uint8_t *buf, size_t size) lzma_nothrow</td></tr>
<tr class="memdesc:a24814feed2515ef61d30f61c8ae19500"><td class="mdescLeft">&#160;</td><td class="mdescRight">Raw x86 BCJ decoder.  <br /></td></tr>
<tr class="separator:a24814feed2515ef61d30f61c8ae19500"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Branch/Call/Jump conversion filters. </p>
<dl class="section note"><dt>Note</dt><dd>Never include this file directly. Use &lt;<a class="el" href="lzma_8h.html" title="The public API of liblzma data compression library.">lzma.h</a>&gt; instead. </dd></dl>
</div><h2 class="groupheader">Function Documentation</h2>
<a id="a864bc77639e1e70a2b50f3f03424e482" name="a864bc77639e1e70a2b50f3f03424e482"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a864bc77639e1e70a2b50f3f03424e482">&#9670;&#160;</a></span>lzma_bcj_arm64_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t lzma_bcj_arm64_encode </td>
          <td>(</td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>start_offset</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>buf</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Raw ARM64 BCJ encoder. </p>
<p>This is for special use cases only.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">start_offset</td><td>The lowest 32 bits of the offset in the executable being filtered. For the ARM64 filter, this must be a multiple of four. For the very best results, this should also be in sync with 4096-byte page boundaries in the executable due to how ARM64's ADRP instruction works. </td></tr>
    <tr><td class="paramname">buf</td><td>Buffer to be filtered in place </td></tr>
    <tr><td class="paramname">size</td><td>Size of the buffer</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Number of bytes that were processed in <code>buf</code>. This is at most <code>size</code>. With the ARM64 filter, the return value is always a multiple of 4, and at most 3 bytes are left unfiltered.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>5.7.1alpha </dd></dl>

</div>
</div>
<a id="a1989d14c1abfb1cab1e6281b78b12250" name="a1989d14c1abfb1cab1e6281b78b12250"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1989d14c1abfb1cab1e6281b78b12250">&#9670;&#160;</a></span>lzma_bcj_arm64_decode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t lzma_bcj_arm64_decode </td>
          <td>(</td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>start_offset</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>buf</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Raw ARM64 BCJ decoder. </p>
<p>See <a class="el" href="#a864bc77639e1e70a2b50f3f03424e482" title="Raw ARM64 BCJ encoder.">lzma_bcj_arm64_encode()</a>.</p>
<dl class="section since"><dt>Since</dt><dd>5.7.1alpha </dd></dl>

</div>
</div>
<a id="a3c11efb1d448aaabd95595fb1c68ab01" name="a3c11efb1d448aaabd95595fb1c68ab01"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3c11efb1d448aaabd95595fb1c68ab01">&#9670;&#160;</a></span>lzma_bcj_riscv_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t lzma_bcj_riscv_encode </td>
          <td>(</td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>start_offset</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>buf</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Raw RISC-V BCJ encoder. </p>
<p>This is for special use cases only.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">start_offset</td><td>The lowest 32 bits of the offset in the executable being filtered. For the RISC-V filter, this must be a multiple of 2. </td></tr>
    <tr><td class="paramname">buf</td><td>Buffer to be filtered in place </td></tr>
    <tr><td class="paramname">size</td><td>Size of the buffer</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Number of bytes that were processed in <code>buf</code>. This is at most <code>size</code>. With the RISC-V filter, the return value is always a multiple of 2, and at most 7 bytes are left unfiltered.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>5.7.1alpha </dd></dl>

</div>
</div>
<a id="a7379d9357c8c7fc100a55d0c53a29021" name="a7379d9357c8c7fc100a55d0c53a29021"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7379d9357c8c7fc100a55d0c53a29021">&#9670;&#160;</a></span>lzma_bcj_riscv_decode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t lzma_bcj_riscv_decode </td>
          <td>(</td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>start_offset</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>buf</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Raw RISC-V BCJ decoder. </p>
<p>See <a class="el" href="#a3c11efb1d448aaabd95595fb1c68ab01" title="Raw RISC-V BCJ encoder.">lzma_bcj_riscv_encode()</a>.</p>
<dl class="section since"><dt>Since</dt><dd>5.7.1alpha </dd></dl>

</div>
</div>
<a id="ab17e110b27302635b7f97cf549b4b83d" name="ab17e110b27302635b7f97cf549b4b83d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab17e110b27302635b7f97cf549b4b83d">&#9670;&#160;</a></span>lzma_bcj_x86_encode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t lzma_bcj_x86_encode </td>
          <td>(</td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>start_offset</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>buf</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Raw x86 BCJ encoder. </p>
<p>This is for special use cases only.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">start_offset</td><td>The lowest 32 bits of the offset in the executable being filtered. For the x86 filter, all values are valid. </td></tr>
    <tr><td class="paramname">buf</td><td>Buffer to be filtered in place </td></tr>
    <tr><td class="paramname">size</td><td>Size of the buffer</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Number of bytes that were processed in <code>buf</code>. This is at most <code>size</code>. For the x86 filter, the return value is always a multiple of 1, and at most 4 bytes are left unfiltered.</dd></dl>
<dl class="section since"><dt>Since</dt><dd>5.7.1alpha </dd></dl>

</div>
</div>
<a id="a24814feed2515ef61d30f61c8ae19500" name="a24814feed2515ef61d30f61c8ae19500"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a24814feed2515ef61d30f61c8ae19500">&#9670;&#160;</a></span>lzma_bcj_x86_decode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">size_t lzma_bcj_x86_decode </td>
          <td>(</td>
          <td class="paramtype">uint32_t</td>          <td class="paramname"><span class="paramname"><em>start_offset</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>buf</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t</td>          <td class="paramname"><span class="paramname"><em>size</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel extern">extern</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Raw x86 BCJ decoder. </p>
<p>See <a class="el" href="#ab17e110b27302635b7f97cf549b4b83d" title="Raw x86 BCJ encoder.">lzma_bcj_x86_encode()</a>.</p>
<dl class="section since"><dt>Since</dt><dd>5.7.1alpha </dd></dl>

</div>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_b17a1d403082bd69a703ed987cf158fb.html">lzma</a></li><li class="navelem"><a class="el" href="bcj_8h.html">bcj.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
