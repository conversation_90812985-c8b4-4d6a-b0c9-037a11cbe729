<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ossl_store-file</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>ossl_store-file - The store &#39;file&#39; scheme loader</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p>#include &lt;openssl/store.h&gt;</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for the &#39;file&#39; scheme is built into <code>libcrypto</code>. Since files come in all kinds of formats and content types, the &#39;file&#39; scheme has its own layer of functionality called &quot;file handlers&quot;, which are used to try to decode diverse types of file contents.</p>

<p>In case a file is formatted as PEM, each called file handler receives the PEM name (everything following any &#39;<code>-----BEGIN </code>&#39;) as well as possible PEM headers, together with the decoded PEM body. Since PEM formatted files can contain more than one object, the file handlers are called upon for each such object.</p>

<p>If the file isn&#39;t determined to be formatted as PEM, the content is loaded in raw form in its entirety and passed to the available file handlers as is, with no PEM name or headers.</p>

<p>Each file handler is expected to handle PEM and non-PEM content as appropriate. Some may refuse non-PEM content for the sake of determinism (for example, there are keys out in the wild that are represented as an ASN.1 OCTET STRING. In raw form, it&#39;s not easily possible to distinguish those from any other data coming as an ASN.1 OCTET STRING, so such keys would naturally be accepted as PEM files only).</p>

<h1 id="NOTES">NOTES</h1>

<p>When needed, the &#39;file&#39; scheme loader will require a pass phrase by using the <b>UI_METHOD</b> that was passed via OSSL_STORE_open(). This pass phrase is expected to be UTF-8 encoded, anything else will give an undefined result. The files made accessible through this loader are expected to be standard compliant with regards to pass phrase encoding. Files that aren&#39;t should be re-generated with a correctly encoded pass phrase. See <a href="../man7/passphrase-encoding.html">passphrase-encoding(7)</a> for more information.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ossl_store.html">ossl_store(7)</a>, <a href="../man7/passphrase-encoding.html">passphrase-encoding(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


