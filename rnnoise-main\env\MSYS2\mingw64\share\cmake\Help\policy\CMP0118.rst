CMP0118
-------

.. versionadded:: 3.20

:prop_sf:`GENERATED` sources may be used across directories without manual marking.

In CMake 3.19 and below, the :prop_sf:`GENERATED` source file property,
like other source file properties, was scoped in every directory separately.
If a source file was generated in one directory, projects had to manually
set the ``GENERATED`` property in another directory in order to use the file.

Whether or not a source file is generated is an all-or-nothing global
property of the source: a source is either generated or it is not.
CMake 3.20 and above prefer to allow source files generated in one directory
to be used in other directories without manually marking them as ``GENERATED``.
Additionally, the ``GENERATED`` property may now be set only to boolean
values, and may not be turned off once turned on.  This policy provides
compatibility for projects that have not been updated for this behavior.

The ``OLD`` behavior of this policy is to allow generated files to be used
only in directories in which their ``GENERATED`` property has been turned on.
The ``NEW`` behavior of this policy is to allow generated files to be used
in other directories without explicitly turning on the ``GENERATED`` property
for those directories.

.. versionadded:: 3.30

  Policy :policy:`CMP0163` additionally makes the :prop_sf:`GENERATED` source
  file property visible to :command:`get_property` and
  :command:`get_source_file_property` calls in other directories.

.. |INTRODUCED_IN_CMAKE_VERSION| replace:: 3.20
.. |WARNS_OR_DOES_NOT_WARN| replace::
   warns about setting the ``GENERATED`` property to a non-boolean value
.. include:: STANDARD_ADVICE.txt

.. include:: DEPRECATED.txt
