.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_obj_list_import_url4" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_obj_list_import_url4 \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "int gnutls_pkcs11_obj_list_import_url4(gnutls_pkcs11_obj_t ** " p_list ", unsigned int * " n_list ", const char * " url ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_pkcs11_obj_t ** p_list" 12
An uninitialized object list (may be NULL)
.IP "unsigned int * n_list" 12
It will contain the size of the list.
.IP "const char * url" 12
A PKCS 11 url identifying a set of objects
.IP "unsigned int flags" 12
Or sequence of GNUTLS_PKCS11_OBJ_* flags
.SH "DESCRIPTION"
This function will enumerate all the objects specified by the PKCS\fB11\fP URL
provided. It will initialize and set values to the object pointer list ( \fIp_list\fP )
provided. To obtain a list of all available objects use a  \fIurl\fP of 'pkcs11:'.

All returned objects must be deinitialized using \fBgnutls_pkcs11_obj_deinit()\fP,
and  \fIp_list\fP must be deinitialized using \fBgnutls_free()\fP.

The supported in this function  \fIflags\fP are \fBGNUTLS_PKCS11_OBJ_FLAG_LOGIN\fP,
\fBGNUTLS_PKCS11_OBJ_FLAG_LOGIN_SO\fP, \fBGNUTLS_PKCS11_OBJ_FLAG_PRESENT_IN_TRUSTED_MODULE\fP,
\fBGNUTLS_PKCS11_OBJ_FLAG_CRT\fP, \fBGNUTLS_PKCS11_OBJ_FLAG_PUBKEY\fP, \fBGNUTLS_PKCS11_OBJ_FLAG_PRIVKEY\fP,
\fBGNUTLS_PKCS11_OBJ_FLAG_WITH_PRIVKEY\fP, \fBGNUTLS_PKCS11_OBJ_FLAG_MARK_CA\fP,
\fBGNUTLS_PKCS11_OBJ_FLAG_MARK_TRUSTED\fP, and since 3.5.1 the \fBGNUTLS_PKCS11_OBJ_FLAG_OVERWRITE_TRUSTMOD_EXT\fP.

On versions of GnuTLS prior to 3.4.0 the equivalent function was
\fBgnutls_pkcs11_obj_list_import_url2()\fP. That is also available on this version
as a macro which maps to this function.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
