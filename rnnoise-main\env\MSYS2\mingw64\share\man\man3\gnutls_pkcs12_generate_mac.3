.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs12_generate_mac" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs12_generate_mac \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs12.h>
.sp
.BI "int gnutls_pkcs12_generate_mac(gnutls_pkcs12_t " pkcs12 ", const char * " pass ");"
.SH ARGUMENTS
.IP "gnutls_pkcs12_t pkcs12" 12
A pkcs12 type
.IP "const char * pass" 12
The password for the MAC
.SH "DESCRIPTION"
This function will generate a MAC for the PKCS12 structure.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
