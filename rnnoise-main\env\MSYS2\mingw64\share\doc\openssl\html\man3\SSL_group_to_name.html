<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_group_to_name</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_group_to_name - get name of group</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

const char *SSL_group_to_name(SSL *ssl, int id);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_group_to_name() is used to retrieve the TLS group name associated with a given TLS group ID, as registered via built-in or external providers and as returned by a call to SSL_get1_groups() or SSL_get_shared_group().</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>If non-NULL, SSL_group_to_name() returns the TLS group name corresponding to the given <i>id</i> as a NUL-terminated string. If SSL_group_to_name() returns NULL, an error occurred; possibly no corresponding tlsname was registered during provider initialisation.</p>

<p>Note that the return value is valid only during the lifetime of the SSL object <i>ssl</i>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


