# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: rnn_gao_new
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/RNN/rnnoise-main/
# =============================================================================
# Object build statements for EXECUTABLE target rnn_gao_new


#############################################
# Order-only phony target for rnn_gao_new

build cmake_object_order_depends_target_rnn_gao_new: phony || cmake_object_order_depends_target_rnnLib

build CMakeFiles/rnn_gao_new.dir/main.c.obj: C_COMPILER__rnn_gao_new_unscanned_ D$:/RNN/rnnoise-main/main.c || cmake_object_order_depends_target_rnn_gao_new
  DEP_FILE = CMakeFiles\rnn_gao_new.dir\main.c.obj.d
  FLAGS = -std=gnu11
  INCLUDES = -ID:/RNN/rnnoise-main/include
  OBJECT_DIR = CMakeFiles\rnn_gao_new.dir
  OBJECT_FILE_DIR = CMakeFiles\rnn_gao_new.dir


# =============================================================================
# Link build statements for EXECUTABLE target rnn_gao_new


#############################################
# Link the executable bin\rnn_gao_new.exe

build bin/rnn_gao_new.exe: C_EXECUTABLE_LINKER__rnn_gao_new_ CMakeFiles/rnn_gao_new.dir/main.c.obj | src/librnnLib.a || src/librnnLib.a
  LINK_LIBRARIES = src/librnnLib.a  -lm  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = CMakeFiles\rnn_gao_new.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = bin\rnn_gao_new.exe
  TARGET_IMPLIB = librnn_gao_new.dll.a
  TARGET_PDB = rnn_gao_new.exe.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\RNN\rnnoise-main && D:\RNN\rnnoise-main\env\MSYS2\mingw64\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\RNN\rnnoise-main && D:\RNN\rnnoise-main\env\MSYS2\mingw64\bin\cmake.exe --regenerate-during-build -SD:\RNN\rnnoise-main -BD:\RNN\rnnoise-main"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/RNN/rnnoise-main/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target rnnLib


#############################################
# Order-only phony target for rnnLib

build cmake_object_order_depends_target_rnnLib: phony || .

build src/CMakeFiles/rnnLib.dir/celt_lpc.c.obj: C_COMPILER__rnnLib_unscanned_ D$:/RNN/rnnoise-main/src/celt_lpc.c || cmake_object_order_depends_target_rnnLib
  DEP_FILE = src\CMakeFiles\rnnLib.dir\celt_lpc.c.obj.d
  FLAGS = -std=gnu11
  INCLUDES = -ID:/RNN/rnnoise-main/include
  OBJECT_DIR = src\CMakeFiles\rnnLib.dir
  OBJECT_FILE_DIR = src\CMakeFiles\rnnLib.dir

build src/CMakeFiles/rnnLib.dir/kiss_fft.c.obj: C_COMPILER__rnnLib_unscanned_ D$:/RNN/rnnoise-main/src/kiss_fft.c || cmake_object_order_depends_target_rnnLib
  DEP_FILE = src\CMakeFiles\rnnLib.dir\kiss_fft.c.obj.d
  FLAGS = -std=gnu11
  INCLUDES = -ID:/RNN/rnnoise-main/include
  OBJECT_DIR = src\CMakeFiles\rnnLib.dir
  OBJECT_FILE_DIR = src\CMakeFiles\rnnLib.dir

build src/CMakeFiles/rnnLib.dir/rnn_data.c.obj: C_COMPILER__rnnLib_unscanned_ D$:/RNN/rnnoise-main/src/rnn_data.c || cmake_object_order_depends_target_rnnLib
  DEP_FILE = src\CMakeFiles\rnnLib.dir\rnn_data.c.obj.d
  FLAGS = -std=gnu11
  INCLUDES = -ID:/RNN/rnnoise-main/include
  OBJECT_DIR = src\CMakeFiles\rnnLib.dir
  OBJECT_FILE_DIR = src\CMakeFiles\rnnLib.dir

build src/CMakeFiles/rnnLib.dir/rnn.c.obj: C_COMPILER__rnnLib_unscanned_ D$:/RNN/rnnoise-main/src/rnn.c || cmake_object_order_depends_target_rnnLib
  DEP_FILE = src\CMakeFiles\rnnLib.dir\rnn.c.obj.d
  FLAGS = -std=gnu11
  INCLUDES = -ID:/RNN/rnnoise-main/include
  OBJECT_DIR = src\CMakeFiles\rnnLib.dir
  OBJECT_FILE_DIR = src\CMakeFiles\rnnLib.dir

build src/CMakeFiles/rnnLib.dir/denoise.c.obj: C_COMPILER__rnnLib_unscanned_ D$:/RNN/rnnoise-main/src/denoise.c || cmake_object_order_depends_target_rnnLib
  DEP_FILE = src\CMakeFiles\rnnLib.dir\denoise.c.obj.d
  FLAGS = -std=gnu11
  INCLUDES = -ID:/RNN/rnnoise-main/include
  OBJECT_DIR = src\CMakeFiles\rnnLib.dir
  OBJECT_FILE_DIR = src\CMakeFiles\rnnLib.dir

build src/CMakeFiles/rnnLib.dir/pitch.c.obj: C_COMPILER__rnnLib_unscanned_ D$:/RNN/rnnoise-main/src/pitch.c || cmake_object_order_depends_target_rnnLib
  DEP_FILE = src\CMakeFiles\rnnLib.dir\pitch.c.obj.d
  FLAGS = -std=gnu11
  INCLUDES = -ID:/RNN/rnnoise-main/include
  OBJECT_DIR = src\CMakeFiles\rnnLib.dir
  OBJECT_FILE_DIR = src\CMakeFiles\rnnLib.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target rnnLib


#############################################
# Link the static library src\librnnLib.a

build src/librnnLib.a: C_STATIC_LIBRARY_LINKER__rnnLib_ src/CMakeFiles/rnnLib.dir/celt_lpc.c.obj src/CMakeFiles/rnnLib.dir/kiss_fft.c.obj src/CMakeFiles/rnnLib.dir/rnn_data.c.obj src/CMakeFiles/rnnLib.dir/rnn.c.obj src/CMakeFiles/rnnLib.dir/denoise.c.obj src/CMakeFiles/rnnLib.dir/pitch.c.obj
  OBJECT_DIR = src\CMakeFiles\rnnLib.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = src\librnnLib.a
  TARGET_PDB = rnnLib.a.dbg


#############################################
# Utility command for edit_cache

build src/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\RNN\rnnoise-main\src && D:\RNN\rnnoise-main\env\MSYS2\mingw64\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build src/edit_cache: phony src/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\RNN\rnnoise-main\src && D:\RNN\rnnoise-main\env\MSYS2\mingw64\bin\cmake.exe --regenerate-during-build -SD:\RNN\rnnoise-main -BD:\RNN\rnnoise-main"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/rebuild_cache: phony src/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build librnnLib.a: phony src/librnnLib.a

build rnnLib: phony src/librnnLib.a

build rnn_gao_new: phony bin/rnn_gao_new.exe

build rnn_gao_new.exe: phony bin/rnn_gao_new.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/RNN/rnnoise-main

build all: phony bin/rnn_gao_new.exe src/all

# =============================================================================

#############################################
# Folder: D:/RNN/rnnoise-main/src

build src/all: phony src/librnnLib.a

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja D$:/RNN/rnnoise-main/cmake_install.cmake D$:/RNN/rnnoise-main/src/cmake_install.cmake: RERUN_CMAKE | CMakeCache.txt CMakeFiles/4.0.3/CMakeCCompiler.cmake CMakeFiles/4.0.3/CMakeRCCompiler.cmake CMakeFiles/4.0.3/CMakeSystem.cmake CMakeLists.txt env/MSYS2/mingw64/share/cmake/Modules/CMakeCInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeRCInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake env/MSYS2/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake env/MSYS2/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Compiler/GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/Linker/GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Linker/GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU-C-ABI.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-Initialize.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake src/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/4.0.3/CMakeCCompiler.cmake CMakeFiles/4.0.3/CMakeRCCompiler.cmake CMakeFiles/4.0.3/CMakeSystem.cmake CMakeLists.txt env/MSYS2/mingw64/share/cmake/Modules/CMakeCInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeRCInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake env/MSYS2/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake env/MSYS2/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Compiler/GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/Linker/GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Linker/GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU-C-ABI.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-Initialize.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake src/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
