# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: EnhancedRNNoise
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/RNN/rnnoise-main/
# =============================================================================
# Object build statements for EXECUTABLE target enhanced_inference_test


#############################################
# Order-only phony target for enhanced_inference_test

build cmake_object_order_depends_target_enhanced_inference_test: phony || .

build CMakeFiles/enhanced_inference_test.dir/src/enhanced_rnn_weights.c.obj: C_COMPILER__enhanced_inference_test_unscanned_ D$:/RNN/rnnoise-main/src/enhanced_rnn_weights.c || cmake_object_order_depends_target_enhanced_inference_test
  DEP_FILE = CMakeFiles\enhanced_inference_test.dir\src\enhanced_rnn_weights.c.obj.d
  FLAGS = -Wall -W -O3 -g -ffast-math -std=gnu99
  INCLUDES = -ID:/RNN/rnnoise-main/include
  OBJECT_DIR = CMakeFiles\enhanced_inference_test.dir
  OBJECT_FILE_DIR = CMakeFiles\enhanced_inference_test.dir\src

build CMakeFiles/enhanced_inference_test.dir/src/simple_model_test.c.obj: C_COMPILER__enhanced_inference_test_unscanned_ D$:/RNN/rnnoise-main/src/simple_model_test.c || cmake_object_order_depends_target_enhanced_inference_test
  DEP_FILE = CMakeFiles\enhanced_inference_test.dir\src\simple_model_test.c.obj.d
  FLAGS = -Wall -W -O3 -g -ffast-math -std=gnu99
  INCLUDES = -ID:/RNN/rnnoise-main/include
  OBJECT_DIR = CMakeFiles\enhanced_inference_test.dir
  OBJECT_FILE_DIR = CMakeFiles\enhanced_inference_test.dir\src


# =============================================================================
# Link build statements for EXECUTABLE target enhanced_inference_test


#############################################
# Link the executable bin\enhanced_inference_test.exe

build bin/enhanced_inference_test.exe: C_EXECUTABLE_LINKER__enhanced_inference_test_ CMakeFiles/enhanced_inference_test.dir/src/enhanced_rnn_weights.c.obj CMakeFiles/enhanced_inference_test.dir/src/simple_model_test.c.obj
  FLAGS = -Wall -W -O3 -g -ffast-math
  LINK_LIBRARIES = -lm  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = CMakeFiles\enhanced_inference_test.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = bin\enhanced_inference_test.exe
  TARGET_IMPLIB = libenhanced_inference_test.dll.a
  TARGET_PDB = enhanced_inference_test.exe.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\RNN\rnnoise-main && D:\RNN\rnnoise-main\env\MSYS2\mingw64\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\RNN\rnnoise-main && D:\RNN\rnnoise-main\env\MSYS2\mingw64\bin\cmake.exe --regenerate-during-build -SD:\RNN\rnnoise-main -BD:\RNN\rnnoise-main"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\RNN\rnnoise-main && D:\RNN\rnnoise-main\env\MSYS2\mingw64\bin\cmake.exe -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\RNN\rnnoise-main && D:\RNN\rnnoise-main\env\MSYS2\mingw64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\RNN\rnnoise-main && D:\RNN\rnnoise-main\env\MSYS2\mingw64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake"
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

build enhanced_inference_test: phony bin/enhanced_inference_test.exe

build enhanced_inference_test.exe: phony bin/enhanced_inference_test.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/RNN/rnnoise-main

build all: phony bin/enhanced_inference_test.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja D$:/RNN/rnnoise-main/cmake_install.cmake: RERUN_CMAKE | CMakeCache.txt CMakeFiles/4.0.3/CMakeCCompiler.cmake CMakeFiles/4.0.3/CMakeRCCompiler.cmake CMakeFiles/4.0.3/CMakeSystem.cmake CMakeLists.txt env/MSYS2/mingw64/share/cmake/Modules/CMakeCInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeRCInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake env/MSYS2/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake env/MSYS2/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Compiler/GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/Linker/GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Linker/GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU-C-ABI.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-Initialize.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/4.0.3/CMakeCCompiler.cmake CMakeFiles/4.0.3/CMakeRCCompiler.cmake CMakeFiles/4.0.3/CMakeSystem.cmake CMakeLists.txt env/MSYS2/mingw64/share/cmake/Modules/CMakeCInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeRCInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake env/MSYS2/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake env/MSYS2/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Compiler/GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake env/MSYS2/mingw64/share/cmake/Modules/Linker/GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Linker/GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU-C-ABI.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU-C.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-Initialize.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/Windows.cmake env/MSYS2/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
