XCODE_SCHEME_TEST_CONFIGURATION
-------------------------------

.. versionadded:: 4.0

Set the build configuration to test the target.

This property is initialized by the value of the variable
:variable:`CMAKE_XCODE_SCHEME_TEST_CONFIGURATION`
if it is set when a target is created.

Please refer to the :prop_tgt:`XCODE_GENERATE_SCHEME` target property
documentation to see all Xcode schema related properties.
