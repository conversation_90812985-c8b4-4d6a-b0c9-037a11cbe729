.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs11_token_check_mechanism" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs11_token_check_mechanism \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs11.h>
.sp
.BI "unsigned gnutls_pkcs11_token_check_mechanism(const char * " url ", unsigned long " mechanism ", void * " ptr ", unsigned " psize ", unsigned " flags ");"
.SH ARGUMENTS
.IP "const char * url" 12
should contain a PKCS 11 URL
.IP "unsigned long mechanism" 12
The PKCS \fB11\fP mechanism ID
.IP "void * ptr" 12
if set it should point to a CK_MECHANISM_INFO struct
.IP "unsigned psize" 12
the size of CK_MECHANISM_INFO struct (for safety)
.IP "unsigned flags" 12
must be zero
.SH "DESCRIPTION"
This function will return whether a mechanism is supported
by the given token. If the mechanism is supported and
 \fIptr\fP is set, it will be updated with the token information.
.SH "RETURNS"
Non\-zero if the mechanism is supported or zero otherwise.
.SH "SINCE"
3.6.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
