# help.de.txt - German GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
#
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


# Die Datei help.txt beschreibt das verwendete Format.
# Diese Datei muß UTF-8 kodiert sein.


.#pinentry.qualitybar.tooltip
# Dies ist lediglich eine kommentiertes Beispiel.  Es ist am sinnvolssten
# einen individuellen Text in /etc/gnupg/help.de.txt zu erstellen.
Die Qualität der Passphrase, die Sie oben eingegeben haben.  Bitte
fragen sie Ihren Systembeauftragten nach den Kriterien für die Messung
der Qualität.
.




.gpg.edit_ownertrust.value
Sie müssen selbst entscheiden, welchen Wert Sie hier eintragen; dieser Wert
wird niemals an eine dritte Seite weitergegeben.  Wir brauchen diesen Wert,
um das "Netz des Vertrauens" aufzubauen.  Dieses hat nichts mit dem
(implizit erzeugten) "Netz der Zertifikate" zu tun.
.

.gpg.edit_ownertrust.set_ultimate.okay
Um das Web-of-Trust aufzubauen muß GnuPG wissen, welchen Schlüsseln
ultimativ vertraut wird. Das sind üblicherweise die Schlüssel
auf deren geheimen Schlüssel Sie Zugruff haben.
Antworten Sie mit "yes" um diesen Schlüssel ultimativ zu vertrauen

.

.gpg.untrusted_key.override
Wenn Sie diesen nicht vertrauenswürdigen Schlüssel trotzdem benutzen wollen,
so antworten Sie mit "ja".
.

.gpg.pklist.user_id.enter
Geben Sie die User-ID dessen ein, dem Sie die Botschaft senden wollen.
.

.gpg.keygen.algo
Wählen Sie das zu verwendene Verfahren.

DSA (alias DSS) ist der "Digital Signature Algorithm" und kann nur für
Unterschriften genutzt werden.

Elgamal ist ein Verfahren nur für Verschlüsselung.

RSA kann sowohl für Unterschriften als auch für Verschlüsselung genutzt
werden.

Der erste Schlüssel (Hauptschlüssel) muß immer ein Schlüssel sein, mit dem
unterschrieben werden kann.
.

.gpg.keygen.algo.rsa_se
Normalerweise ist es nicht gut, denselben Schlüssel zum unterschreiben
und verschlüsseln zu nutzen.  Dieses Verfahren sollte in speziellen
Anwendungsgebiten benutzt werden.  Bitte lassen Sie sich zuerst von
einem Sicherheistexperten beraten.
.

.gpg.keygen.size
Wählen Sie die gewünschte Schlüssellänge
.

.gpg.keygen.size.huge.okay
Geben Sie "ja" oder "nein" ein
.

.gpg.keygen.size.large.okay
Geben Sie "ja" oder "nein" ein
.

.gpg.keygen.valid
Geben Sie den benötigten Wert so an, wie er im Prompt erscheint.
Es ist zwar möglich ein "ISO"-Datum (JJJJ-MM-DD) einzugeben, aber man
erhält dann ggfs. keine brauchbaren Fehlermeldungen - stattdessen versucht
der Rechner den Wert als Intervall (von-bis) zu deuten.
.

.gpg.keygen.valid.okay
Geben Sie "ja" oder "nein" ein
.

.gpg.keygen.name
Geben Sie den Namen des Schlüsselinhabers ein.
Beispiel: Heinrich Heine.
.

.gpg.keygen.email
Geben Sie eine Email-Adresse ein. Dies ist zwar nicht unbedingt notwendig,
aber sehr empfehlenswert.
Beispiel: <EMAIL>
.

.gpg.keygen.comment
Geben Sie - bei Bedarf - einen Kommentar ein.
.

.gpg.keygen.userid.cmd
N  um den Namen zu ändern.
K  um den Kommentar zu ändern.
E  um die Email-Adresse zu ändern.
F  um mit der Schlüsselerzeugung fortzusetzen.
B  um die Schlüsselerzeugung abbrechen.
.

.gpg.keygen.sub.okay
Geben Sie "ja" (oder nur "j") ein, um den Unterschlüssel zu erzeugen.
.

.gpg.sign_uid.okay
Geben Sie "ja" oder "nein" ein
.

.gpg.sign_uid.class
Wenn Sie die User-ID eines Schlüssels beglaubigen wollen, sollten Sie zunächst
sicherstellen, daß der Schlüssel demjenigen gehört, der in der User-ID genannt
ist. Für Dritte ist es hilfreich zu wissen, wie gut diese Zuordnung überprüft
wurde.

"0" zeigt, daß Sie keine bestimmte Aussage über die Sorgfalt der
    Schlüsselzuordnung machen.

"1" Sie glauben, daß der Schlüssel der benannten Person gehört,
    aber Sie konnten oder nahmen die Überpüfung überhaupt nicht vor.
    Dies ist hilfreich für eine "persona"-Überprüfung, wobei man den
    Schlüssel eines Pseudonym-Trägers beglaubigt

"2" Sie nahmen eine flüchtige Überprüfung vor. Das heißt Sie haben z.B.
    den Schlüsselfingerabdruck kontrolliert und die User-ID des Schlüssels
    anhand des Fotos geprüft.

"3" Sie haben eine ausführlich Kontrolle des Schlüssels vorgenommen.
    Das kann z.B. die Kontrolle des Schlüsselfingerabdrucks mit dem
    Schlüsselinhaber persönlich vorgenommen haben; daß Sie die User-ID des
    Schlüssel anhand einer schwer zu fälschenden Urkunde mit Foto (wie z.B.
    einem Paß) abgeglichen haben und schließlich per Email-Verkehr die
    Email-Adresse als zum Schlüsselbesitzer gehörig erkannt haben.

Beachten Sie, daß diese Beispiele für die Antworten 2 und 3 *nur* Beispiele
sind.  Schlußendlich ist es Ihre Sache, was Sie unter "flüchtig" oder
 "ausführlich" verstehen, wenn Sie Schlüssel Dritter beglaubigen.

Wenn Sie nicht wissen, wie Sie antworten sollen, wählen Sie "0".
.

.gpg.change_passwd.empty.okay
Geben Sie "ja" oder "nein" ein
.

.gpg.keyedit.save.okay
Geben Sie "ja" oder "nein" ein
.

.gpg.keyedit.cancel.okay
Geben Sie "ja" oder "nein" ein
.

.gpg.keyedit.sign_all.okay
Geben Sie "ja" (oder nur "j") ein, um alle User-IDs zu beglaubigen
.

.gpg.keyedit.remove.uid.okay
Geben Sie "ja" (oder nur "j") ein, um diese User-ID zu LÖSCHEN.
Alle Zertifikate werden dann auch weg sein!
.

.gpg.keyedit.remove.subkey.okay
Geben Sie "ja" (oder nur "j") ein, um diesen Unterschlüssel zu löschen
.

.gpg.keyedit.delsig.valid
Dies ist eine gültige Beglaubigung für den Schlüssel. Es ist normalerweise
unnötig sie zu löschen. Sie ist möglicherweise sogar notwendig, um einen
Trust-Weg zu diesem oder einem durch diesen Schlüssel beglaubigten Schlüssel
herzustellen.
.

.gpg.keyedit.delsig.unknown
Diese Beglaubigung kann nicht geprüft werden, da Sie den passenden Schlüssel
nicht besitzen. Sie sollten die Löschung der Beglaubigung verschieben, bis
sie wissen, welcher Schlüssel verwendet wurde. Denn vielleicht würde genau
diese Beglaubigung den "Trust"-Weg komplettieren.
.

.gpg.keyedit.delsig.invalid
Diese Beglaubigung ist ungültig. Es ist sinnvoll sie aus Ihrem
Schlüsselbund zu entfernen.
.

.gpg.keyedit.delsig.selfsig
Diese Beglaubigung bindet die User-ID an den Schlüssel. Normalerweise ist
es nicht gut, solche Beglaubigungen zu entfernen. Um ehrlich zu sein:
Es könnte dann sein, daß GnuPG diesen Schlüssel gar nicht mehr benutzen kann.
Sie sollten diese Eigenbeglaubigung also nur dann entfernen, wenn sie aus
irgendeinem Grund nicht gültig ist und eine zweite Beglaubigung verfügbar ist.
.

.gpg.keyedit.updpref.okay
Ändern der Voreinstellung aller User-IDs (oder nur der ausgewählten)
auf die aktuelle Liste der Voreinstellung. Die Zeitangaben aller betroffenen
Eigenbeglaubigungen werden um eine Sekunde vorgestellt.

.

.gpg.passphrase.enter
Bitte geben Sie die Passphrase ein. Dies ist ein geheimer Satz

.

.gpg.passphrase.repeat
Um sicher zu gehen, daß Sie sich bei der Eingabe der Passphrase nicht
vertippt haben, geben Sie diese bitte nochmal ein.  Nur wenn beide Eingaben
übereinstimmen, wird die Passphrase akzeptiert.
.

.gpg.detached_signature.filename
Geben Sie den Namen der Datei an, zu dem die abgetrennte Unterschrift gehört
.

.gpg.openfile.overwrite.okay
Geben Sie "ja" ein, wenn Sie die Datei überschreiben möchten
.

.gpg.openfile.askoutname
Geben Sie bitte einen neuen Dateinamen ein. Falls Sie nur die
Eingabetaste betätigen, wird der (in Klammern angezeigte) Standarddateiname
verwendet.
.

.gpg.ask_revocation_reason.code
Sie sollten einen Grund für die Zertifizierung angeben. Je nach
Zusammenhang können Sie aus dieser Liste auswählen:
  "Schlüssel wurde kompromitiert"
      Falls Sie Grund zu der Annahme haben, daß nicht berechtigte Personen
      Zugriff zu Ihrem geheimen Schlüssel hatten
  "Schlüssel ist überholt"
      Falls Sie diesen Schlüssel durch einem neuen ersetzt haben.
  "Schlüssel wird nicht mehr benutzt"
      Falls Sie diesen Schlüssel zurückgezogen haben.
  "User-ID ist nicht mehr gültig"
      Um bekanntzugeben, daß die User-ID nicht mehr benutzt werden soll.
      So weist man normalerweise auf eine ungültige Emailadresse hin.

.

.gpg.ask_revocation_reason.text
Wenn Sie möchten, können Sie hier einen Text eingeben, der darlegt, warum
Sie diesen Widerruf herausgeben.  Der Text sollte möglichst knapp sein.
Eine Leerzeile beendet die Eingabe.

.



# Local variables:
# mode: default-generic
# coding: utf-8
# End:
