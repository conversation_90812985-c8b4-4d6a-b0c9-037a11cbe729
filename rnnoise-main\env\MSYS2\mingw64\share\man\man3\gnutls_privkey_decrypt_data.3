.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_privkey_decrypt_data" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_privkey_decrypt_data \- API function
.SH SYNOPSIS
.B #include <gnutls/abstract.h>
.sp
.BI "int gnutls_privkey_decrypt_data(gnutls_privkey_t " key ", unsigned int " flags ", const gnutls_datum_t * " ciphertext ", gnutls_datum_t * " plaintext ");"
.SH ARGUMENTS
.IP "gnutls_privkey_t key" 12
Holds the key
.IP "unsigned int flags" 12
zero for now
.IP "const gnutls_datum_t * ciphertext" 12
holds the data to be decrypted
.IP "gnutls_datum_t * plaintext" 12
will contain the decrypted data, allocated with \fBgnutls_malloc()\fP
.SH "DESCRIPTION"
This function will decrypt the given data using the algorithm
supported by the private key.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.12.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
