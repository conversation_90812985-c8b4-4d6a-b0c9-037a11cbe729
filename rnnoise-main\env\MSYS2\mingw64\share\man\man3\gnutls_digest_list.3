.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_digest_list" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_digest_list \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "const gnutls_digest_algorithm_t * gnutls_digest_list( " void ");"
.SH ARGUMENTS
.IP " void" 12
.SH "DESCRIPTION"

Get a list of hash (digest) algorithms supported by GnuTLS.

This function is not thread safe.
.SH "RETURNS"
Return a (0)\-terminated list of \fBgnutls_digest_algorithm_t\fP
integers indicating the available digests.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
