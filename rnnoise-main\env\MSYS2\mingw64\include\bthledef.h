/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef __BTHLEDEF_H__
#define __BTHLEDEF_H__

#include <winapifamily.h>
#include <minwindef.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

#if NTDDI_VERSION >= NTDDI_WIN8

DEFINE_GUID(GUID_BLUETOOTHLE_DEVICE_INTERFACE, 0x781aee18, 0x7733, 0x4ce4, 0xad, 0xd0, 0x91, 0xf4, 0x1c, 0x67, 0xb5, 0x92);
DEFINE_GUID(GUID_BLUETOOTH_GATT_SERVICE_DEVICE_INTERFACE, 0x6e3bb679, 0x4372, 0x40c8, 0x9e, 0xaa, 0x45, 0x09, 0xdf, 0x26, 0x0c, 0xd8);
DEFINE_GUID(BTH_LE_ATT_BLUETOOTH_BASE_GUID, 0x00000000, 0x0000, 0x1000, 0x80, 0x00, 0x00, 0x80, 0x5f, 0x9b, 0x34, 0xfb);

#define BTH_LE_SERVICE_GAP (0x1800)
#define BTH_LE_SERVICE_GATT (0x1801)

#define BTH_LE_GATT_ATTRIBUTE_TYPE_PRIMARY_SERVICE (0x2800)
#define BTH_LE_GATT_ATTRIBUTE_TYPE_SECONDARY_SERVICE (0x2801)
#define BTH_LE_GATT_ATTRIBUTE_TYPE_INCLUDE (0x2802)
#define BTH_LE_GATT_ATTRIBUTE_TYPE_CHARACTERISTIC (0x2803)

#define BTH_LE_GATT_CHARACTERISTIC_DESCRIPTOR_EXTENDED_PROPERTIES (0x2900)
#define BTH_LE_GATT_CHARACTERISTIC_DESCRIPTOR_USER_DESCRIPTION (0x2901)
#define BTH_LE_GATT_CHARACTERISTIC_DESCRIPTOR_CLIENT_CONFIGURATION (0x2902)
#define BTH_LE_GATT_CHARACTERISTIC_DESCRIPTOR_SERVER_CONFIGURATION (0x2903)
#define BTH_LE_GATT_CHARACTERISTIC_DESCRIPTOR_FORMAT (0x2904)
#define BTH_LE_GATT_CHARACTERISTIC_DESCRIPTOR_AGGREGATE_FORMAT (0x2905)

#define BTH_LE_GATT_CHARACTERISTIC_TYPE_DEVICE_NAME (0x2a00)
#define BTH_LE_GATT_CHARACTERISTIC_TYPE_APPEARANCE (0x2a01)
#define BTH_LE_GATT_CHARACTERISTIC_TYPE_PERIPHERAL_PRIVACY_FLAG (0x2a02)
#define BTH_LE_GATT_CHARACTERISTIC_TYPE_RECONNECTION_ADDRESS (0x2a03)
#define BTH_LE_GATT_CHARACTERISTIC_TYPE_PERIPHERAL_PREFERED_CONNECTION_PARAMETER (0x2a04)
#define BTH_LE_GATT_CHARACTERISTIC_TYPE_SERVICE_CHANGED (0x2a05)

#define BTH_LE_GAP_APPEARANCE_CATEGORY_OFFSET 0x6
#define BTH_LE_GAP_APPEARANCE_CATEGORY_MASK 0x3ff
#define BTH_LE_GAP_APPEARANCE_GET_CATEGORY(a) ((a >> BTH_LE_GAP_APPEARANCE_CATEGORY_OFFSET) & BTH_LE_GAP_APPEARANCE_CATEGORY_MASK)
#define BTH_LE_GAP_APPEARANCE_SET_CATEGORY(a, c) a = ((a & ~BTH_LE_GAP_APPEARANCE_CATEGORY_MASK) | (c << BTH_LE_GAP_APPEARANCE_CATEGORY_OFFSET))
#define BTH_LE_GAP_APPEARANCE_SUB_CATEGORY_MASK 0x3f
#define BTH_LE_GAP_APPEARANCE_GET_SUB_CATEGORY(a) ((UCHAR)(a & BTH_LE_GAP_APPEARANCE_SUB_CATEGORY_MASK))
#define BTH_LE_GAP_APPEARANCE_SET_SUB_CATEGORY(a, s) a = ((a & ~BTH_LE_GAP_APPEARANCE_SUB_CATEGORY_MASK) | s)

#define BTH_LE_GAP_APPEARANCE_CATEGORY_UNCATEGORIZED 0x0000
#define BTH_LE_GAP_APPEARANCE_CATEGORY_PHONE 0x0001
#define BTH_LE_GAP_APPEARANCE_CATEGORY_COMPUTER 0x0002
#define BTH_LE_GAP_APPEARANCE_CATEGORY_WATCH 0x0003
#define BTH_LE_GAP_APPEARANCE_CATEGORY_CLOCK 0x0004
#define BTH_LE_GAP_APPEARANCE_CATEGORY_DISPLAY 0x0005
#define BTH_LE_GAP_APPEARANCE_CATEGORY_REMOTE_CONTROL 0x0006
#define BTH_LE_GAP_APPEARANCE_CATEGORY_EYE_GLASSES 0x0007
#define BTH_LE_GAP_APPEARANCE_CATEGORY_TAG 0x0008
#define BTH_LE_GAP_APPEARANCE_CATEGORY_KEYRING 0x0009
#define BTH_LE_GAP_APPEARANCE_CATEGORY_MEDIA_PLAYER 0x000a
#define BTH_LE_GAP_APPEARANCE_CATEGORY_BARCODE_SCANNER 0x000b
#define BTH_LE_GAP_APPEARANCE_CATEGORY_THERMOMETER 0x000c
#define BTH_LE_GAP_APPEARANCE_CATEGORY_HEART_RATE 0x000d
#define BTH_LE_GAP_APPEARANCE_CATEGORY_BLOOD_PRESSURE 0x000e
#define BTH_LE_GAP_APPEARANCE_CATEGORY_HID 0x000f
#define BTH_LE_GAP_APPEARANCE_CATEGORY_GLUCOSE_METER 0x0010
#define BTH_LE_GAP_APPEARANCE_CATEGORY_RUNNING_WALKING_SENSOR 0x0011
#define BTH_LE_GAP_APPEARANCE_CATEGORY_CYCLING 0x0012
#define BTH_LE_GAP_APPEARANCE_CATEGORY_CONTROL_DEVICE 0x0013
#define BTH_LE_GAP_APPEARANCE_CATEGORY_NETWORK_DEVICE 0x0014
#define BTH_LE_GAP_APPEARANCE_CATEGORY_SENSOR 0x0015
#define BTH_LE_GAP_APPEARANCE_CATEGORY_LIGHT_FIXTURES 0x0016
#define BTH_LE_GAP_APPEARANCE_CATEGORY_FAN 0x0017
#define BTH_LE_GAP_APPEARANCE_CATEGORY_HVAC 0x0018
#define BTH_LE_GAP_APPEARANCE_CATEGORY_AIR_CONDITIONING 0x0019
#define BTH_LE_GAP_APPEARANCE_CATEGORY_HUMIDIFIER 0x001a
#define BTH_LE_GAP_APPEARANCE_CATEGORY_HEATING 0x001b
#define BTH_LE_GAP_APPEARANCE_CATEGORY_ACCESS_CONTROL 0x001c
#define BTH_LE_GAP_APPEARANCE_CATEGORY_MOTORIZED_DEVICE 0x001d
#define BTH_LE_GAP_APPEARANCE_CATEGORY_POWER_DEVICE 0x001e
#define BTH_LE_GAP_APPEARANCE_CATEGORY_LIGHT_SOURCE 0x001f
#define BTH_LE_GAP_APPEARANCE_CATEGORY_WINDOW_COVERING 0x0020
#define BTH_LE_GAP_APPEARANCE_CATEGORY_AUDIO_SINK 0x0021
#define BTH_LE_GAP_APPEARANCE_CATEGORY_AUDIO_SOURCE 0x0022
#define BTH_LE_GAP_APPEARANCE_CATEGORY_MOTORIZED_VEHICLE 0x0023
#define BTH_LE_GAP_APPEARANCE_CATEGORY_DOMESTIC_APPLIANCE 0x0024
#define BTH_LE_GAP_APPEARANCE_CATEGORY_WEARABLE_AUDIO_DEVICE 0x0025
#define BTH_LE_GAP_APPEARANCE_CATEGORY_AIRCRAFT 0x0026
#define BTH_LE_GAP_APPEARANCE_CATEGORY_AV_EQUIPMENT 0x0027
#define BTH_LE_GAP_APPEARANCE_CATEGORY_DISPLAY_EQUIPMENT 0x0028
#define BTH_LE_GAP_APPEARANCE_CATEGORY_HEARING_AID 0x0029
#define BTH_LE_GAP_APPEARANCE_CATEGORY_GAMING 0x002a
#define BTH_LE_GAP_APPEARANCE_CATEGORY_SIGNAGE 0x002b
#define BTH_LE_GAP_APPEARANCE_CATEGORY_PLUSE_OXIMETER 0x0031
#define BTH_LE_GAP_APPEARANCE_CATEGORY_WEIGHT_SCALE 0x0032
#define BTH_LE_GAP_APPEARANCE_CATEGORY_PERSONAL_MOBILITY_DEVICE 0x0033
#define BTH_LE_GAP_APPEARANCE_CATEGORY_CONTINUOUS_GLUCOSE_MONITOR 0x0034
#define BTH_LE_GAP_APPEARANCE_CATEGORY_INSULIN_PUMP 0x0035
#define BTH_LE_GAP_APPEARANCE_CATEGORY_MEDICATION_DELIVERY 0x0036
#define BTH_LE_GAP_APPEARANCE_CATEGORY_OUTDOOR_SPORTS_ACTIVITY 0x0051
#define BTH_LE_GAP_APPEARANCE_SUBCATEGORY_GENERIC 0x00
#define BTH_LE_GAP_APPEARANCE_WATCH_SUBCATEGORY_SPORTS_WATCH 0x01
#define BTH_LE_GAP_APPEARANCE_THERMOMETER_SUBCATEGORY_EAR 0x01
#define BTH_LE_GAP_APPEARANCE_HEART_RATE_SUBCATEGORY_HEART_RATE_BELT 0x01
#define BTH_LE_GAP_APPEARANCE_BLOOD_PRESSURE_SUBCATEGORY_ARM 0x01
#define BTH_LE_GAP_APPEARANCE_BLOOD_PRESSURE_SUBCATEGORY_WRIST 0x02
#define BTH_LE_GAP_APPEARANCE_HID_SUBCATEGORY_KEYBOARD 0x01
#define BTH_LE_GAP_APPEARANCE_HID_SUBCATEGORY_MOUSE 0x02
#define BTH_LE_GAP_APPEARANCE_HID_SUBCATEGORY_JOYSTICK 0x03
#define BTH_LE_GAP_APPEARANCE_HID_SUBCATEGORY_GAMEPAD 0x04
#define BTH_LE_GAP_APPEARANCE_HID_SUBCATEGORY_DIGITIZER_TABLET 0x05
#define BTH_LE_GAP_APPEARANCE_HID_SUBCATEGORY_CARD_READER 0x06
#define BTH_LE_GAP_APPEARANCE_HID_SUBCATEGORY_DIGITAL_PEN 0x07
#define BTH_LE_GAP_APPEARANCE_HID_SUBCATEGORY_BARCODE_SCANNER 0x08
#define BTH_LE_GAP_APPEARANCE_RUNNING_WALKING_SENSOR_SUBCATEGORY_IN_SHOE 0x01
#define BTH_LE_GAP_APPEARANCE_RUNNING_WALKING_SENSOR_SUBCATEGORY_ON_SHOE 0x02
#define BTH_LE_GAP_APPEARANCE_RUNNING_WALKING_SENSOR_SUBCATEGORY_ON_HIP 0x03
#define BTH_LE_GAP_APPEARANCE_CYCLING_SUBCATEGORY_CYCLING_COMPUTER 0x01
#define BTH_LE_GAP_APPEARANCE_CYCLING_SUBCATEGORY_SPEED_SENSOR 0x02
#define BTH_LE_GAP_APPEARANCE_CYCLING_SUBCATEGORY_CADENCE_SENSOR 0x03
#define BTH_LE_GAP_APPEARANCE_CYCLING_SUBCATEGORY_POWER_SENSOR 0x04
#define BTH_LE_GAP_APPEARANCE_CYCLING_SUBCATEGORY_SPEED_AND_CADENCE_SENSOR 0x05
#define BTH_LE_GAP_APPEARANCE_AUDIO_SINK_SUBCATEGORY_STANDALONE_SPEAKER 0x01
#define BTH_LE_GAP_APPEARANCE_AUDIO_SINK_SUBCATEGORY_SOUNDBAR 0x02
#define BTH_LE_GAP_APPEARANCE_AUDIO_SINK_SUBCATEGORY_BOOKSHELF_SPEAKER 0x03
#define BTH_LE_GAP_APPEARANCE_AUDIO_SINK_SUBCATEGORY_STANDMOUNTED_SPEAKER 0x04
#define BTH_LE_GAP_APPEARANCE_AUDIO_SINK_SUBCATEGORY_SPEAKERPHONE 0x05
#define BTH_LE_GAP_APPEARANCE_AUDIO_SOURCE_SUBCATEGORY_MICROPHONE 0x01
#define BTH_LE_GAP_APPEARANCE_AUDIO_SOURCE_SUBCATEGORY_ALARM 0x02
#define BTH_LE_GAP_APPEARANCE_AUDIO_SOURCE_SUBCATEGORY_BELL 0x03
#define BTH_LE_GAP_APPEARANCE_AUDIO_SOURCE_SUBCATEGORY_HORN 0x04
#define BTH_LE_GAP_APPEARANCE_AUDIO_SOURCE_SUBCATEGORY_BROADCASTING_DEVICE 0x05
#define BTH_LE_GAP_APPEARANCE_AUDIO_SOURCE_SUBCATEGORY_SERVICE_DESK 0x06
#define BTH_LE_GAP_APPEARANCE_AUDIO_SOURCE_SUBCATEGORY_KIOSK 0x07
#define BTH_LE_GAP_APPEARANCE_AUDIO_SOURCE_SUBCATEGORY_BROADCASTING_ROOM 0x08
#define BTH_LE_GAP_APPEARANCE_AUDIO_SOURCE_SUBCATEGORY_AUDITORIUM 0x09
#define BTH_LE_GAP_APPEARANCE_WEARABLE_AUDIO_DEVICE_SUBCATEGORY_EARBUD 0x01
#define BTH_LE_GAP_APPEARANCE_WEARABLE_AUDIO_DEVICE_SUBCATEGORY_HEADSET 0x02
#define BTH_LE_GAP_APPEARANCE_WEARABLE_AUDIO_DEVICE_SUBCATEGORY_HEADPHONES 0x03
#define BTH_LE_GAP_APPEARANCE_WEARABLE_AUDIO_DEVICE_SUBCATEGORY_NECKBAND 0x04
#define BTH_LE_GAP_APPEARANCE_HEARING_AID_SUBCATEGORY_IN_EAR_HEARING_AID 0x01
#define BTH_LE_GAP_APPEARANCE_HEARING_AID_SUBCATEGORY_BEHIND_EAR_HEARING_AID 0x02
#define BTH_LE_GAP_APPEARANCE_HEARING_AID_SUBCATEGORY_COCHLEAR_IMPLANT 0x03
#define BTH_LE_GAP_APPEARANCE_PULSE_OXIMETER_SUBCATEGORY_FINGERTIP 0x01
#define BTH_LE_GAP_APPEARANCE_PULSE_OXIMETER_SUBCATEGORY_WRIST_WORN 0x02
#define BTH_LE_GAP_APPEARANCE_OUTDOOR_SPORTS_ACTIVITY_SUBCATEGORY_LOCATION_DISPLAY_DEVICE 0x01
#define BTH_LE_GAP_APPEARANCE_OUTDOOR_SPORTS_ACTIVITY_SUBCATEGORY_LOCATION_NAVIGATION_DISPLAY_DEVICE 0x02
#define BTH_LE_GAP_APPEARANCE_OUTDOOR_SPORTS_ACTIVITY_SUBCATEGORY_LOCATION_POD 0x03
#define BTH_LE_GAP_APPEARANCE_OUTDOOR_SPORTS_ACTIVITY_SUBCATEGORY_LOCATION_NAVIGATION_POD 0x04

#define BTH_LE_GATT_DEFAULT_MAX_INCLUDED_SERVICES_DEPTH (3)

#define BTH_LE_ATT_TRANSACTION_TIMEOUT 30

#define BTH_LE_ATT_MAX_VALUE_SIZE 512

#define BTH_LE_ATT_CID (0x0004)

#define BTHLEENUM_ATT_MTU_MIN (23)
#define BTHLEENUM_ATT_MTU_MAX (0xffff)
#define BTHLEENUM_ATT_MTU_DEFAULT (BTHLEENUM_ATT_MTU_MIN)
#define BTHLEENUM_ATT_MTU_INITIAL_NEGOTIATION (525)

#define BTH_LE_ERROR_INVALID_HANDLE (0x01)
#define BTH_LE_ERROR_READ_NOT_PERMITTED (0x02)
#define BTH_LE_ERROR_WRITE_NOT_PERMITTED (0x03)
#define BTH_LE_ERROR_INVALID_PDU (0x04)
#define BTH_LE_ERROR_INSUFFICIENT_AUTHENTICATION (0x05)
#define BTH_LE_ERROR_REQUEST_NOT_SUPPORTED (0x06)
#define BTH_LE_ERROR_INVALID_OFFSET (0x07)
#define BTH_LE_ERROR_INSUFFICIENT_AUTHORIZATION (0x08)
#define BTH_LE_ERROR_PREPARE_QUEUE_FULL (0x09)
#define BTH_LE_ERROR_ATTRIBUTE_NOT_FOUND (0x0a)
#define BTH_LE_ERROR_ATTRIBUTE_NOT_LONG (0x0b)
#define BTH_LE_ERROR_INSUFFICIENT_ENCRYPTION_KEY_SIZE (0x0c)
#define BTH_LE_ERROR_INVALID_ATTRIBUTE_VALUE_LENGTH (0x0d)
#define BTH_LE_ERROR_UNLIKELY (0x0e)
#define BTH_LE_ERROR_INSUFFICIENT_ENCRYPTION (0x0f)
#define BTH_LE_ERROR_UNSUPPORTED_GROUP_TYPE (0x10)
#define BTH_LE_ERROR_INSUFFICIENT_RESOURCES (0x11)
#define BTH_LE_ERROR_UNKNOWN (0x1000)

#define BLUETOOTH_GATT_FLAG_NONE 0x00000000
#define BLUETOOTH_GATT_FLAG_CONNECTION_ENCRYPTED 0x00000001
#define BLUETOOTH_GATT_FLAG_CONNECTION_AUTHENTICATED 0x00000002
#define BLUETOOTH_GATT_FLAG_FORCE_READ_FROM_DEVICE 0x00000004
#define BLUETOOTH_GATT_FLAG_FORCE_READ_FROM_CACHE 0x00000008
#define BLUETOOTH_GATT_FLAG_SIGNED_WRITE 0x00000010
#define BLUETOOTH_GATT_FLAG_WRITE_WITHOUT_RESPONSE 0x00000020
#define BLUETOOTH_GATT_FLAG_RETURN_ALL 0x00000040
#define BLUETOOTH_GATT_FLAG_VALID_MASK (BLUETOOTH_GATT_FLAG_NONE | BLUETOOTH_GATT_FLAG_CONNECTION_ENCRYPTED | BLUETOOTH_GATT_FLAG_CONNECTION_AUTHENTICATED | BLUETOOTH_GATT_FLAG_FORCE_READ_FROM_DEVICE | BLUETOOTH_GATT_FLAG_FORCE_READ_FROM_CACHE | BLUETOOTH_GATT_FLAG_SIGNED_WRITE | BLUETOOTH_GATT_FLAG_WRITE_WITHOUT_RESPONSE | BLUETOOTH_GATT_FLAG_RETURN_ALL)

#define IS_BLUETOOTH_GATT_FLAG_VALID(f) (0 == (f & ~BLUETOOTH_GATT_FLAG_VALID_MASK))

typedef HANDLE BLUETOOTH_GATT_EVENT_HANDLE;

typedef struct _BTH_LE_UUID {
  BOOLEAN IsShortUuid;
#ifdef __WIDL__
  [switch_type(BOOLEAN), switch_is((BOOLEAN)IsShortUuid)]
#endif
  union {
#ifdef __WIDL__
    [case(TRUE)]
#endif
    USHORT ShortUuid;
#ifdef __WIDL__
    [case(FALSE)]
#endif
    GUID LongUuid;
  } Value;
} BTH_LE_UUID, *PBTH_LE_UUID;

typedef struct _BTH_LE_GATT_SERVICE {
  BTH_LE_UUID ServiceUuid;
  USHORT AttributeHandle;
} BTH_LE_GATT_SERVICE, *PBTH_LE_GATT_SERVICE;

typedef enum _BTH_LE_GATT_DESCRIPTOR_TYPE {
  CharacteristicExtendedProperties,
  CharacteristicUserDescription,
  ClientCharacteristicConfiguration,
  ServerCharacteristicConfiguration,
  CharacteristicFormat,
  CharacteristicAggregateFormat,
  CustomDescriptor
} BTH_LE_GATT_DESCRIPTOR_TYPE, *PBTH_LE_GATT_DESCRIPTOR_TYPE;

typedef struct _BTH_LE_GATT_CHARACTERISTIC {
  USHORT ServiceHandle;
  BTH_LE_UUID CharacteristicUuid;
  USHORT AttributeHandle;
  USHORT CharacteristicValueHandle;
  BOOLEAN IsBroadcastable;
  BOOLEAN IsReadable;
  BOOLEAN IsWritable;
  BOOLEAN IsWritableWithoutResponse;
  BOOLEAN IsSignedWritable;
  BOOLEAN IsNotifiable;
  BOOLEAN IsIndicatable;
  BOOLEAN HasExtendedProperties;
} BTH_LE_GATT_CHARACTERISTIC, *PBTH_LE_GATT_CHARACTERISTIC;

typedef struct _BTH_LE_GATT_CHARACTERISTIC_VALUE {
  ULONG DataSize;
#ifdef __WIDL__
  [size_is(DataSize)] UCHAR Data[*];
#else
  UCHAR Data[1];
#endif
} BTH_LE_GATT_CHARACTERISTIC_VALUE, *PBTH_LE_GATT_CHARACTERISTIC_VALUE;

typedef struct _BTH_LE_GATT_DESCRIPTOR {
  USHORT ServiceHandle;
  USHORT CharacteristicHandle;
  BTH_LE_GATT_DESCRIPTOR_TYPE DescriptorType;
  BTH_LE_UUID DescriptorUuid;
  USHORT AttributeHandle;
} BTH_LE_GATT_DESCRIPTOR, *PBTH_LE_GATT_DESCRIPTOR;

typedef struct _BTH_LE_GATT_DESCRIPTOR_VALUE {
  BTH_LE_GATT_DESCRIPTOR_TYPE DescriptorType;
  BTH_LE_UUID DescriptorUuid;
#ifdef __WIDL__
  [switch_type(BTH_LE_GATT_DESCRIPTOR_TYPE), switch_is((BTH_LE_GATT_DESCRIPTOR_TYPE)DescriptorType)]
#endif
  __C89_NAMELESS union {
#ifdef __WIDL__
    [case(CharacteristicExtendedProperties)]
#endif
    struct {
      BOOLEAN IsReliableWriteEnabled;
      BOOLEAN IsAuxiliariesWritable;
    } CharacteristicExtendedProperties;
#ifdef __WIDL__
    [case(ClientCharacteristicConfiguration)]
#endif
    struct {
      BOOLEAN IsSubscribeToNotification;
      BOOLEAN IsSubscribeToIndication;
    } ClientCharacteristicConfiguration;
#ifdef __WIDL__
    [case(ServerCharacteristicConfiguration)]
#endif
    struct {
      BOOLEAN IsBroadcast;
    } ServerCharacteristicConfiguration;
#ifdef __WIDL__
    [case(CharacteristicFormat)]
#endif
    struct {
      UCHAR Format;
      UCHAR Exponent;
      BTH_LE_UUID Unit;
      UCHAR NameSpace;
      BTH_LE_UUID Description;
    } CharacteristicFormat;
#ifdef __WIDL__
    [default]
      ;
#endif
  };
  ULONG DataSize;
#ifdef __WIDL__
  [size_is(DataSize)] UCHAR Data[*];
#else
  UCHAR Data[1];
#endif
} BTH_LE_GATT_DESCRIPTOR_VALUE, *PBTH_LE_GATT_DESCRIPTOR_VALUE;

typedef enum _BTH_LE_GATT_EVENT_TYPE {
  CharacteristicValueChangedEvent
} BTH_LE_GATT_EVENT_TYPE;

typedef VOID (CALLBACK *PFNBLUETOOTH_GATT_EVENT_CALLBACK)(BTH_LE_GATT_EVENT_TYPE EventType, PVOID EventOutParameter, PVOID Context);

typedef struct _BLUETOOTH_GATT_VALUE_CHANGED_EVENT_REGISTRATION {
  USHORT NumCharacteristics;
  BTH_LE_GATT_CHARACTERISTIC Characteristics[1];
} BLUETOOTH_GATT_VALUE_CHANGED_EVENT_REGISTRATION, *PBLUETOOTH_GATT_VALUE_CHANGED_EVENT_REGISTRATION;

typedef struct _BLUETOOTH_GATT_VALUE_CHANGED_EVENT {
  USHORT ChangedAttributeHandle;
  size_t CharacteristicValueDataSize;
  PBTH_LE_GATT_CHARACTERISTIC_VALUE CharacteristicValue;
} BLUETOOTH_GATT_VALUE_CHANGED_EVENT, *PBLUETOOTH_GATT_VALUE_CHANGED_EVENT;

typedef ULONG64 BTH_LE_GATT_RELIABLE_WRITE_CONTEXT, *PBTH_LE_GATT_RELIABLE_WRITE_CONTEXT;

#ifdef __cplusplus
extern "C"{
#endif

FORCEINLINE BOOLEAN IsBthLEUuidMatch(BTH_LE_UUID uuid1, BTH_LE_UUID uuid2) {
  BTH_LE_UUID tempLongUuid = {0};
  tempLongUuid.IsShortUuid = FALSE;
  tempLongUuid.Value.LongUuid = BTH_LE_ATT_BLUETOOTH_BASE_GUID;
  if (uuid1.IsShortUuid && uuid2.IsShortUuid) {
    return (uuid1.Value.ShortUuid == uuid2.Value.ShortUuid);
  } else if (!uuid1.IsShortUuid && !uuid2.IsShortUuid) {
    return (0 == memcmp(&uuid1.Value.LongUuid, &uuid2.Value.LongUuid, sizeof(GUID)));
  } else if (uuid1.IsShortUuid) {
    tempLongUuid.Value.LongUuid.Data1 += uuid1.Value.ShortUuid;
    return (0 == memcmp(&tempLongUuid, &uuid2.Value.LongUuid, sizeof(GUID)));
  } else if (uuid2.IsShortUuid) {
    tempLongUuid.Value.LongUuid.Data1 += uuid2.Value.ShortUuid;
    return (0 == memcmp(&uuid1.Value.LongUuid, &tempLongUuid.Value.LongUuid, sizeof(GUID)));
  }
  return FALSE;
}

#ifdef __cplusplus
  }
#endif

#endif /* NTDDI_WIN8 */

#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */

#endif /* __BTHLEDEF_H__ */
