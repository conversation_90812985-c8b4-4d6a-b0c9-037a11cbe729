<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_aria_128_gcm</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_aria_128_cbc, EVP_aria_192_cbc, EVP_aria_256_cbc, EVP_aria_128_cfb, EVP_aria_192_cfb, EVP_aria_256_cfb, EVP_aria_128_cfb1, EVP_aria_192_cfb1, EVP_aria_256_cfb1, EVP_aria_128_cfb8, EVP_aria_192_cfb8, EVP_aria_256_cfb8, EVP_aria_128_cfb128, EVP_aria_192_cfb128, EVP_aria_256_cfb128, EVP_aria_128_ctr, EVP_aria_192_ctr, EVP_aria_256_ctr, EVP_aria_128_ecb, EVP_aria_192_ecb, EVP_aria_256_ecb, EVP_aria_128_ofb, EVP_aria_192_ofb, EVP_aria_256_ofb, EVP_aria_128_ccm, EVP_aria_192_ccm, EVP_aria_256_ccm, EVP_aria_128_gcm, EVP_aria_192_gcm, EVP_aria_256_gcm, - EVP ARIA cipher</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

const EVP_CIPHER *EVP_ciphername(void)</code></pre>

<p><i>EVP_ciphername</i> is used a placeholder for any of the described cipher functions, such as <i>EVP_aria_128_cbc</i>.</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The ARIA encryption algorithm for EVP.</p>

<dl>

<dt id="EVP_aria_128_cbc-EVP_aria_192_cbc-EVP_aria_256_cbc-EVP_aria_128_cfb-EVP_aria_192_cfb-EVP_aria_256_cfb-EVP_aria_128_cfb1-EVP_aria_192_cfb1-EVP_aria_256_cfb1-EVP_aria_128_cfb8-EVP_aria_192_cfb8-EVP_aria_256_cfb8-EVP_aria_128_cfb128-EVP_aria_192_cfb128-EVP_aria_256_cfb128-EVP_aria_128_ctr-EVP_aria_192_ctr-EVP_aria_256_ctr-EVP_aria_128_ecb-EVP_aria_192_ecb-EVP_aria_256_ecb-EVP_aria_128_ofb-EVP_aria_192_ofb-EVP_aria_256_ofb">EVP_aria_128_cbc(), EVP_aria_192_cbc(), EVP_aria_256_cbc(), EVP_aria_128_cfb(), EVP_aria_192_cfb(), EVP_aria_256_cfb(), EVP_aria_128_cfb1(), EVP_aria_192_cfb1(), EVP_aria_256_cfb1(), EVP_aria_128_cfb8(), EVP_aria_192_cfb8(), EVP_aria_256_cfb8(), EVP_aria_128_cfb128(), EVP_aria_192_cfb128(), EVP_aria_256_cfb128(), EVP_aria_128_ctr(), EVP_aria_192_ctr(), EVP_aria_256_ctr(), EVP_aria_128_ecb(), EVP_aria_192_ecb(), EVP_aria_256_ecb(), EVP_aria_128_ofb(), EVP_aria_192_ofb(), EVP_aria_256_ofb()</dt>
<dd>

<p>ARIA for 128, 192 and 256 bit keys in the following modes: CBC, CFB with 128-bit shift, CFB with 1-bit shift, CFB with 8-bit shift, CTR, ECB and OFB.</p>

</dd>
<dt id="EVP_aria_128_ccm-EVP_aria_192_ccm-EVP_aria_256_ccm-EVP_aria_128_gcm-EVP_aria_192_gcm-EVP_aria_256_gcm">EVP_aria_128_ccm(), EVP_aria_192_ccm(), EVP_aria_256_ccm(), EVP_aria_128_gcm(), EVP_aria_192_gcm(), EVP_aria_256_gcm(),</dt>
<dd>

<p>ARIA for 128, 192 and 256 bit keys in CBC-MAC Mode (CCM) and Galois Counter Mode (GCM). These ciphers require additional control operations to function correctly, see the <a href="../man3/EVP_EncryptInit.html">&quot;AEAD Interface&quot; in EVP_EncryptInit(3)</a> section for details.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>Developers should be aware of the negative performance implications of calling these functions multiple times and should consider using <a href="../man3/EVP_CIPHER_fetch.html">EVP_CIPHER_fetch(3)</a> with <a href="../man7/EVP_CIPHER-ARIA.html">EVP_CIPHER-ARIA(7)</a> instead. See <a href="../man7/crypto.html">&quot;Performance&quot; in crypto(7)</a> for further information.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>These functions return an <b>EVP_CIPHER</b> structure that contains the implementation of the symmetric cipher. See <a href="../man3/EVP_CIPHER_meth_new.html">EVP_CIPHER_meth_new(3)</a> for details of the <b>EVP_CIPHER</b> structure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/evp.html">evp(7)</a>, <a href="../man3/EVP_EncryptInit.html">EVP_EncryptInit(3)</a>, <a href="../man3/EVP_CIPHER_meth_new.html">EVP_CIPHER_meth_new(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


