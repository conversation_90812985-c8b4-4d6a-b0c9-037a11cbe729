var annotated_dup =
[
    [ "lzma_allocator", "structlzma__allocator.html", "structlzma__allocator" ],
    [ "lzma_block", "structlzma__block.html", "structlzma__block" ],
    [ "lzma_filter", "structlzma__filter.html", "structlzma__filter" ],
    [ "lzma_index_iter", "structlzma__index__iter.html", "structlzma__index__iter" ],
    [ "lzma_mt", "structlzma__mt.html", "structlzma__mt" ],
    [ "lzma_options_bcj", "structlzma__options__bcj.html", "structlzma__options__bcj" ],
    [ "lzma_options_delta", "structlzma__options__delta.html", "structlzma__options__delta" ],
    [ "lzma_options_lzma", "structlzma__options__lzma.html", "structlzma__options__lzma" ],
    [ "lzma_stream", "structlzma__stream.html", "structlzma__stream" ],
    [ "lzma_stream_flags", "structlzma__stream__flags.html", "structlzma__stream__flags" ]
];