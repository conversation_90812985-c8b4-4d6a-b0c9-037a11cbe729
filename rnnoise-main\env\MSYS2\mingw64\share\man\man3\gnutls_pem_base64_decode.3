.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pem_base64_decode" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pem_base64_decode \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_pem_base64_decode(const char * " header ", const gnutls_datum_t * " b64_data ", unsigned char * " result ", size_t * " result_size ");"
.SH ARGUMENTS
.IP "const char * header" 12
A null terminated string with the PEM header (eg. CERTIFICATE)
.IP "const gnutls_datum_t * b64_data" 12
contain the encoded data
.IP "unsigned char * result" 12
the place where decoded data will be copied
.IP "size_t * result_size" 12
holds the size of the result
.SH "DESCRIPTION"
This function will decode the given encoded data.  If the header
given is non \fBNULL\fP this function will search for "\-\-\-\-\-BEGIN header"
and decode only this part.  Otherwise it will decode the first PEM
packet found.
.SH "RETURNS"
On success \fBGNUTLS_E_SUCCESS\fP (0) is returned,
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP is returned if the buffer given is
not long enough, or 0 on success.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
