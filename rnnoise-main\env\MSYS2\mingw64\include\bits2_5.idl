/*
 * Background Intelligent Transfer Service (BITS) 2.5 interface
 *
 * Copyright 2015 <PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 *
 */

#ifndef DO_NO_IMPORTS
import "bits.idl";
import "bits1_5.idl";
import "bits2_0.idl";
#endif

[
    uuid(f1bd1079-9f01-4bdc-8036-f09b70095066),
    odl
]
interface IBackgroundCopyJobHttpOptions : IUnknown
{
    typedef enum
    {
        BG_CERT_STORE_LOCATION_CURRENT_USER,
        BG_CERT_STORE_LOCATION_LOCAL_MACHINE,
        BG_CERT_STORE_LOCATION_CURRENT_SERVICE,
        BG_CERT_STORE_LOCATION_SERVICES,
        BG_CERT_STORE_LOCATION_USERS,
        BG_CERT_STORE_LOCATION_CURRENT_USER_GROUP_POLICY,
        BG_CERT_STORE_LOCATION_LOCAL_MACHINE_GROUP_POLICY,
        BG_CERT_STORE_LOCATION_LOCAL_MACHINE_ENTERPRISE
    } BG_CERT_STORE_LOCATION;

    HRESULT SetClientCertificateByID(
        [in] BG_CERT_STORE_LOCATION StoreLocation,
        [in] LPCWSTR StoreName,
        [in, size_is(20), ref] BYTE *pCertHashBlob
    );

    HRESULT SetClientCertificateByName(
        [in] BG_CERT_STORE_LOCATION StoreLocation,
        [in] LPCWSTR StoreName,
        [in] LPCWSTR SubjectName
    );

    HRESULT RemoveClientCertificate();

    HRESULT GetClientCertificate(
        [out, ref] BG_CERT_STORE_LOCATION *pStoreLocation,
        [out, ref] LPWSTR *pStoreName,
        [out, size_is(, 20), ref] BYTE **ppCertHashBlob,
        [out, ref] LPWSTR *pSubjectName
    );

    HRESULT SetCustomHeaders(
        [in, unique] LPCWSTR RequestHeaders
    );

    HRESULT GetCustomHeaders(
        [out] LPWSTR *pRequestHeaders
    );

    HRESULT SetSecurityFlags(
        [in] ULONG Flags
    );

    HRESULT GetSecurityFlags(
        [out, ref] ULONG *pFlags
    );
}

[
    uuid(4974177c-3bb6-4c37-9ff0-6b7426f0aba9),
    version(1.0)
]
library BackgroundCopyManager2_5
{
    [
        uuid(03ca98d6-ff5d-49b8-abc6-03dd84127020)
    ]
    coclass BackgroundCopyManager2_5
    {
        [default] interface IBackgroundCopyManager;
    };

    interface IBackgroundCopyCallback;
    interface IBackgroundCopyJobHttpOptions;
}

cpp_quote("#include \"bits3_0.h\"")
