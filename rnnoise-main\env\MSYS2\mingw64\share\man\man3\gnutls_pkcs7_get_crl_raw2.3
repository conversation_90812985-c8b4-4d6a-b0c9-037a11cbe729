.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_pkcs7_get_crl_raw2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_pkcs7_get_crl_raw2 \- API function
.SH SYNOPSIS
.B #include <gnutls/pkcs7.h>
.sp
.BI "int gnutls_pkcs7_get_crl_raw2(gnutls_pkcs7_t " pkcs7 ", unsigned " indx ", gnutls_datum_t * " crl ");"
.SH ARGUMENTS
.IP "gnutls_pkcs7_t pkcs7" 12
The pkcs7 type
.IP "unsigned indx" 12
contains the index of the crl to extract
.IP "gnutls_datum_t * crl" 12
will contain the contents of the CRL in an allocated buffer
.SH "DESCRIPTION"
This function will return a DER encoded CRL of the PKCS7 or RFC2630 crl set.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.  After the last crl has been read
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP will be returned.
.SH "SINCE"
3.4.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
