<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_CIPHER-DES</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Algorithm-Names">Algorithm Names</a></li>
      <li><a href="#Parameters">Parameters</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_CIPHER-DES - The DES EVP_CIPHER implementations</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for DES symmetric encryption using the <b>EVP_CIPHER</b> API.</p>

<h2 id="Algorithm-Names">Algorithm Names</h2>

<p>The following algorithms are available in the FIPS provider as well as the default provider:</p>

<dl>

<dt id="DES-EDE3-ECB-or-DES-EDE3">&quot;DES-EDE3-ECB&quot; or &quot;DES-EDE3&quot;</dt>
<dd>

</dd>
<dt id="DES-EDE3-CBC-or-DES3">&quot;DES-EDE3-CBC&quot; or &quot;DES3&quot;</dt>
<dd>

</dd>
</dl>

<p>The following algorithms are available in the default provider, but not the FIPS provider:</p>

<dl>

<dt id="DES-EDE3-CFB8-and-DES-EDE3-CFB1">&quot;DES-EDE3-CFB8&quot; and &quot;DES-EDE3-CFB1&quot;</dt>
<dd>

</dd>
<dt id="DES-EDE-ECB-or-DES-EDE">&quot;DES-EDE-ECB&quot; or &quot;DES-EDE&quot;</dt>
<dd>

</dd>
<dt id="DES-EDE-CBC">&quot;DES-EDE-CBC&quot;</dt>
<dd>

</dd>
<dt id="DES-EDE-OFB">&quot;DES-EDE-OFB&quot;</dt>
<dd>

</dd>
<dt id="DES-EDE-CFB">&quot;DES-EDE-CFB&quot;</dt>
<dd>

</dd>
<dt id="DES3-WRAP">&quot;DES3-WRAP&quot;</dt>
<dd>

</dd>
</dl>

<p>The following algorithms are available in the legacy provider:</p>

<dl>

<dt id="DES-ECB">&quot;DES-ECB&quot;</dt>
<dd>

</dd>
<dt id="DES-CBC">&quot;DES-CBC&quot;</dt>
<dd>

</dd>
<dt id="DES-OFB">&quot;DES-OFB&quot;</dt>
<dd>

</dd>
<dt id="DES-CFB-DES-CFB1-and-DES-CFB8">&quot;DES-CFB&quot;, &quot;DES-CFB1&quot; and &quot;DES-CFB8&quot;</dt>
<dd>

</dd>
<dt id="DESX-CBC">&quot;DESX-CBC&quot;</dt>
<dd>

</dd>
</dl>

<h2 id="Parameters">Parameters</h2>

<p>This implementation supports the parameters described in <a href="../man3/EVP_EncryptInit.html">&quot;PARAMETERS&quot; in EVP_EncryptInit(3)</a> including &quot;encrypt-check&quot; and &quot;fips-indicator&quot;.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider-cipher.html">provider-cipher(7)</a>, <a href="../man7/OSSL_PROVIDER-FIPS.html">OSSL_PROVIDER-FIPS(7)</a>, <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a>, <a href="../man7/OSSL_PROVIDER-legacy.html">OSSL_PROVIDER-legacy(7)</a>,</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


