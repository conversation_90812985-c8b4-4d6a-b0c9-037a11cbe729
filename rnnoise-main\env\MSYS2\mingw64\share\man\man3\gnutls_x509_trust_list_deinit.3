.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_deinit" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_deinit \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "void gnutls_x509_trust_list_deinit(gnutls_x509_trust_list_t " list ", unsigned int " all ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The list to be deinitialized
.IP "unsigned int all" 12
if non\-zero it will deinitialize all the certificates and CRLs contained in the structure.
.SH "DESCRIPTION"
This function will deinitialize a trust list. Note that the
 \fIall\fP flag should be typically non\-zero unless you have specified
your certificates using \fBgnutls_x509_trust_list_add_cas()\fP and you
want to prevent them from being deinitialized by this function.
.SH "SINCE"
3.0.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
