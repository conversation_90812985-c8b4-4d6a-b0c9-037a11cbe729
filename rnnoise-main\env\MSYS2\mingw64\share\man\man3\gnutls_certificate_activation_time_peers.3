.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_certificate_activation_time_peers" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_certificate_activation_time_peers \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "time_t gnutls_certificate_activation_time_peers(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a gnutls session
.SH "DESCRIPTION"
This function will return the peer's certificate activation time.
.SH "RETURNS"
(time_t)\-1 on error.
.SH "DEPRECATED"
\fBgnutls_certificate_verify_peers2()\fP now verifies activation times.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
