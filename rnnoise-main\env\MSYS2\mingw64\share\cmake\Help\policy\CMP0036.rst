CMP0036
-------

.. |REMOVED_IN_CMAKE_VERSION| replace:: 4.0
.. include:: REMOVED_PROLOGUE.txt

The :command:`build_name` command should not be called.

This command was added in May 2001 to compute a name for the current
operating system and compiler combination.  The command has long been
documented as discouraged and replaced by the :variable:`CMAKE_SYSTEM`
and :variable:`CMAKE_<LANG>_COMPILER` variables.

.. |disallowed_version| replace:: 3.0
.. include:: REMOVED_COMMAND.txt
