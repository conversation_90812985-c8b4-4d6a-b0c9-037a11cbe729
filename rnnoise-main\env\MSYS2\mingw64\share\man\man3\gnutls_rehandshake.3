.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_rehandshake" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_rehandshake \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_rehandshake(gnutls_session_t " session ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.SH "DESCRIPTION"
This function can only be called in server side, and
instructs a TLS 1.2 or earlier client to renegotiate
parameters (perform a handshake), by sending a
hello request message.

If this function succeeds, the calling application
should call \fBgnutls_record_recv()\fP until \fBGNUTLS_E_REHANDSHAKE\fP
is returned to clear any pending data. If the \fBGNUTLS_E_REHANDSHAKE\fP
error code is not seen, then the handshake request was
not followed by the peer (the TLS protocol does not require
the client to do, and such compliance should be handled
by the application protocol).

Once the \fBGNUTLS_E_REHANDSHAKE\fP error code is seen, the
calling application should proceed to calling
\fBgnutls_handshake()\fP to negotiate the new
parameters.

If the client does not wish to renegotiate parameters he
may reply with an alert message, and in that case the return code seen
by subsequent \fBgnutls_record_recv()\fP will be
\fBGNUTLS_E_WARNING_ALERT_RECEIVED\fP with the specific alert being
\fBGNUTLS_A_NO_RENEGOTIATION\fP.  A client may also choose to ignore
this request.

Under TLS 1.3 this function is equivalent to \fBgnutls_session_key_update()\fP
with the \fBGNUTLS_KU_PEER\fP flag. In that case subsequent calls to
\fBgnutls_record_recv()\fP will not return \fBGNUTLS_E_REHANDSHAKE\fP, and
calls to \fBgnutls_handshake()\fP in server side are a no\-op.

This function always fails with \fBGNUTLS_E_INVALID_REQUEST\fP when
called in client side.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, otherwise a negative error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
