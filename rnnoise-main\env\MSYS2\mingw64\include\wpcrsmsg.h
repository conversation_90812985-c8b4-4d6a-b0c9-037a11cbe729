/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#define CRSWPP_ERROR_FIRST __MSABI_LONG(0x40042200)
#define CRSWPP_INVALID_POSTINFO_FILE __MSABI_LONG(0xC0042201)
#define CRSWPP_NO_MATCHING_MAPURL __MSABI_LONG(0xC0042202)
#define CRSWPP_SECURITY_PACKAGE __MSABI_LONG(0xC0042203)
#define CRSWPP_SECURITY_PACKAGE_NOT_FOUND __MSABI_LONG(0xC0042204)
#define CRSWPP_PROJECT_BINDING_INCOMPLETE __MSABI_LONG(0xC0042205)
#define CRSWPP_SERVER_BINDING_INCOMPLETE __MSABI_LONG(0xC0042206)
#define CRSWPP_SERVER_NOT_RESPONDING __MSABI_LONG(0xC0042207)
#define CRSWPP_FAILED_AUTH __MSABI_LONG(0xC0042208)
#define CRSWPP_POSTINFO_NEEDED __MSABI_LONG(0x40042209)
#define CRSWPP_BIND_FAILED __MSABI_LONG(0xC004220A)
