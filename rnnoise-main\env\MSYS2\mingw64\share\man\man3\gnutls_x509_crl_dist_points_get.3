.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crl_dist_points_get" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crl_dist_points_get \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_crl_dist_points_get(gnutls_x509_crl_dist_points_t " cdp ", unsigned int " seq ", unsigned int * " type ", gnutls_datum_t * " san ", unsigned int * " reasons ");"
.SH ARGUMENTS
.IP "gnutls_x509_crl_dist_points_t cdp" 12
The CRL distribution points
.IP "unsigned int seq" 12
specifies the sequence number of the distribution point (0 for the first one, 1 for the second etc.)
.IP "unsigned int * type" 12
The name type of the corresponding name (gnutls_x509_subject_alt_name_t)
.IP "gnutls_datum_t * san" 12
The distribution point names (to be treated as constant)
.IP "unsigned int * reasons" 12
Revocation reasons. An ORed sequence of flags from \fBgnutls_x509_crl_reason_flags_t\fP.
.SH "DESCRIPTION"
This function retrieves the individual CRL distribution points (*********),
contained in provided type. 
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
if the index is out of bounds, otherwise a negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
