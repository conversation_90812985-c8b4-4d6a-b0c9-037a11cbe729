.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_ocsp_resp_get_produced" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_ocsp_resp_get_produced \- API function
.SH SYNOPSIS
.B #include <gnutls/ocsp.h>
.sp
.BI "time_t gnutls_ocsp_resp_get_produced(gnutls_ocsp_resp_const_t " resp ");"
.SH ARGUMENTS
.IP "gnutls_ocsp_resp_const_t resp" 12
should contain a \fBgnutls_ocsp_resp_t\fP type
.SH "DESCRIPTION"
This function will return the time when the OCSP response was
signed.
.SH "RETURNS"
signing time, or (time_t)\-1 on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
