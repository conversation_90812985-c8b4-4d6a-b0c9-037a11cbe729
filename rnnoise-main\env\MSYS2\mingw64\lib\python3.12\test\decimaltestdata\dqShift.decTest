------------------------------------------------------------------------
-- dqShift.decTest -- shift decQuad coefficient left or right         --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- Sanity check
dqshi001 shift                                   0    0  ->  0
dqshi002 shift                                   0    2  ->  0
dqshi003 shift                                   1    2  ->  100
dqshi004 shift                                   1   33  ->  1000000000000000000000000000000000
dqshi005 shift                                   1   34  ->  0
dqshi006 shift                                   1   -1  ->  0
dqshi007 shift                                   0   -2  ->  0
dqshi008 shift  1234567890123456789012345678901234   -1  ->  123456789012345678901234567890123
dqshi009 shift  1234567890123456789012345678901234   -33 ->  1
dqshi010 shift  1234567890123456789012345678901234   -34 ->  0
dqshi011 shift  9934567890123456789012345678901234   -33 ->  9
dqshi012 shift  9934567890123456789012345678901234   -34 ->  0

-- rhs must be an integer
dqshi015 shift        1    1.5    -> NaN Invalid_operation
dqshi016 shift        1    1.0    -> NaN Invalid_operation
dqshi017 shift        1    0.1    -> NaN Invalid_operation
dqshi018 shift        1    0.0    -> NaN Invalid_operation
dqshi019 shift        1    1E+1   -> NaN Invalid_operation
dqshi020 shift        1    1E+99  -> NaN Invalid_operation
dqshi021 shift        1    Inf    -> NaN Invalid_operation
dqshi022 shift        1    -Inf   -> NaN Invalid_operation
-- and |rhs| <= precision
dqshi025 shift        1    -1000  -> NaN Invalid_operation
dqshi026 shift        1    -35    -> NaN Invalid_operation
dqshi027 shift        1     35    -> NaN Invalid_operation
dqshi028 shift        1     1000  -> NaN Invalid_operation

-- full shifting pattern
dqshi030 shift  1234567890123456789012345678901234         -34  -> 0
dqshi031 shift  1234567890123456789012345678901234         -33  -> 1
dqshi032 shift  1234567890123456789012345678901234         -32  -> 12
dqshi033 shift  1234567890123456789012345678901234         -31  -> 123
dqshi034 shift  1234567890123456789012345678901234         -30  -> 1234
dqshi035 shift  1234567890123456789012345678901234         -29  -> 12345
dqshi036 shift  1234567890123456789012345678901234         -28  -> 123456
dqshi037 shift  1234567890123456789012345678901234         -27  -> 1234567
dqshi038 shift  1234567890123456789012345678901234         -26  -> 12345678
dqshi039 shift  1234567890123456789012345678901234         -25  -> 123456789
dqshi040 shift  1234567890123456789012345678901234         -24  -> 1234567890
dqshi041 shift  1234567890123456789012345678901234         -23  -> 12345678901
dqshi042 shift  1234567890123456789012345678901234         -22  -> 123456789012
dqshi043 shift  1234567890123456789012345678901234         -21  -> 1234567890123
dqshi044 shift  1234567890123456789012345678901234         -20  -> 12345678901234
dqshi045 shift  1234567890123456789012345678901234         -19  -> 123456789012345
dqshi047 shift  1234567890123456789012345678901234         -18  -> 1234567890123456
dqshi048 shift  1234567890123456789012345678901234         -17  -> 12345678901234567
dqshi049 shift  1234567890123456789012345678901234         -16  -> 123456789012345678
dqshi050 shift  1234567890123456789012345678901234         -15  -> 1234567890123456789
dqshi051 shift  1234567890123456789012345678901234         -14  -> 12345678901234567890
dqshi052 shift  1234567890123456789012345678901234         -13  -> 123456789012345678901
dqshi053 shift  1234567890123456789012345678901234         -12  -> 1234567890123456789012
dqshi054 shift  1234567890123456789012345678901234         -11  -> 12345678901234567890123
dqshi055 shift  1234567890123456789012345678901234         -10  -> 123456789012345678901234
dqshi056 shift  1234567890123456789012345678901234         -9   -> 1234567890123456789012345
dqshi057 shift  1234567890123456789012345678901234         -8   -> 12345678901234567890123456
dqshi058 shift  1234567890123456789012345678901234         -7   -> 123456789012345678901234567
dqshi059 shift  1234567890123456789012345678901234         -6   -> 1234567890123456789012345678
dqshi060 shift  1234567890123456789012345678901234         -5   -> 12345678901234567890123456789
dqshi061 shift  1234567890123456789012345678901234         -4   -> 123456789012345678901234567890
dqshi062 shift  1234567890123456789012345678901234         -3   -> 1234567890123456789012345678901
dqshi063 shift  1234567890123456789012345678901234         -2   -> 12345678901234567890123456789012
dqshi064 shift  1234567890123456789012345678901234         -1   -> 123456789012345678901234567890123
dqshi065 shift  1234567890123456789012345678901234         -0   -> 1234567890123456789012345678901234

dqshi066 shift  1234567890123456789012345678901234         +0   -> 1234567890123456789012345678901234
dqshi067 shift  1234567890123456789012345678901234         +1   -> 2345678901234567890123456789012340
dqshi068 shift  1234567890123456789012345678901234         +2   -> 3456789012345678901234567890123400
dqshi069 shift  1234567890123456789012345678901234         +3   -> 4567890123456789012345678901234000
dqshi070 shift  1234567890123456789012345678901234         +4   -> 5678901234567890123456789012340000
dqshi071 shift  1234567890123456789012345678901234         +5   -> 6789012345678901234567890123400000
dqshi072 shift  1234567890123456789012345678901234         +6   -> 7890123456789012345678901234000000
dqshi073 shift  1234567890123456789012345678901234         +7   -> 8901234567890123456789012340000000
dqshi074 shift  1234567890123456789012345678901234         +8   -> 9012345678901234567890123400000000
dqshi075 shift  1234567890123456789012345678901234         +9   ->  123456789012345678901234000000000
dqshi076 shift  1234567890123456789012345678901234         +10  -> 1234567890123456789012340000000000
dqshi077 shift  1234567890123456789012345678901234         +11  -> 2345678901234567890123400000000000
dqshi078 shift  1234567890123456789012345678901234         +12  -> 3456789012345678901234000000000000
dqshi079 shift  1234567890123456789012345678901234         +13  -> 4567890123456789012340000000000000
dqshi080 shift  1234567890123456789012345678901234         +14  -> 5678901234567890123400000000000000
dqshi081 shift  1234567890123456789012345678901234         +15  -> 6789012345678901234000000000000000
dqshi082 shift  1234567890123456789012345678901234         +16  -> 7890123456789012340000000000000000
dqshi083 shift  1234567890123456789012345678901234         +17  -> 8901234567890123400000000000000000
dqshi084 shift  1234567890123456789012345678901234         +18  -> 9012345678901234000000000000000000
dqshi085 shift  1234567890123456789012345678901234         +19  ->  123456789012340000000000000000000
dqshi086 shift  1234567890123456789012345678901234         +20  -> 1234567890123400000000000000000000
dqshi087 shift  1234567890123456789012345678901234         +21  -> 2345678901234000000000000000000000
dqshi088 shift  1234567890123456789012345678901234         +22  -> 3456789012340000000000000000000000
dqshi089 shift  1234567890123456789012345678901234         +23  -> 4567890123400000000000000000000000
dqshi090 shift  1234567890123456789012345678901234         +24  -> 5678901234000000000000000000000000
dqshi091 shift  1234567890123456789012345678901234         +25  -> 6789012340000000000000000000000000
dqshi092 shift  1234567890123456789012345678901234         +26  -> 7890123400000000000000000000000000
dqshi093 shift  1234567890123456789012345678901234         +27  -> 8901234000000000000000000000000000
dqshi094 shift  1234567890123456789012345678901234         +28  -> 9012340000000000000000000000000000
dqshi095 shift  1234567890123456789012345678901234         +29  ->  123400000000000000000000000000000
dqshi096 shift  1234567890123456789012345678901234         +30  -> 1234000000000000000000000000000000
dqshi097 shift  1234567890123456789012345678901234         +31  -> 2340000000000000000000000000000000
dqshi098 shift  1234567890123456789012345678901234         +32  -> 3400000000000000000000000000000000
dqshi099 shift  1234567890123456789012345678901234         +33  -> 4000000000000000000000000000000000
dqshi100 shift  1234567890123456789012345678901234         +34  -> 0

-- zeros
dqshi270 shift  0E-10              +29   ->   0E-10
dqshi271 shift  0E-10              -29   ->   0E-10
dqshi272 shift  0.000              +29   ->   0.000
dqshi273 shift  0.000              -29   ->   0.000
dqshi274 shift  0E+10              +29   ->   0E+10
dqshi275 shift  0E+10              -29   ->   0E+10
dqshi276 shift -0E-10              +29   ->  -0E-10
dqshi277 shift -0E-10              -29   ->  -0E-10
dqshi278 shift -0.000              +29   ->  -0.000
dqshi279 shift -0.000              -29   ->  -0.000
dqshi280 shift -0E+10              +29   ->  -0E+10
dqshi281 shift -0E+10              -29   ->  -0E+10

-- Nmax, Nmin, Ntiny
dqshi141 shift  9.999999999999999999999999999999999E+6144     -1  -> 9.99999999999999999999999999999999E+6143
dqshi142 shift  9.999999999999999999999999999999999E+6144     -33 -> 9E+6111
dqshi143 shift  9.999999999999999999999999999999999E+6144      1  -> 9.999999999999999999999999999999990E+6144
dqshi144 shift  9.999999999999999999999999999999999E+6144      33 -> 9.000000000000000000000000000000000E+6144
dqshi145 shift  1E-6143                                       -1  -> 0E-6143
dqshi146 shift  1E-6143                                       -33 -> 0E-6143
dqshi147 shift  1E-6143                                        1  -> 1.0E-6142
dqshi148 shift  1E-6143                                        33 -> 1.000000000000000000000000000000000E-6110
dqshi151 shift  1.000000000000000000000000000000000E-6143     -1  -> 1.00000000000000000000000000000000E-6144
dqshi152 shift  1.000000000000000000000000000000000E-6143     -33 -> 1E-6176
dqshi153 shift  1.000000000000000000000000000000000E-6143      1  -> 0E-6176
dqshi154 shift  1.000000000000000000000000000000000E-6143      33 -> 0E-6176
dqshi155 shift  9.000000000000000000000000000000000E-6143     -1  -> 9.00000000000000000000000000000000E-6144
dqshi156 shift  9.000000000000000000000000000000000E-6143     -33 -> 9E-6176
dqshi157 shift  9.000000000000000000000000000000000E-6143      1  -> 0E-6176
dqshi158 shift  9.000000000000000000000000000000000E-6143      33 -> 0E-6176
dqshi160 shift  1E-6176                                       -1  -> 0E-6176
dqshi161 shift  1E-6176                                       -33 -> 0E-6176
dqshi162 shift  1E-6176                                        1  -> 1.0E-6175
dqshi163 shift  1E-6176                                        33 -> 1.000000000000000000000000000000000E-6143
--  negatives
dqshi171 shift -9.999999999999999999999999999999999E+6144     -1  -> -9.99999999999999999999999999999999E+6143
dqshi172 shift -9.999999999999999999999999999999999E+6144     -33 -> -9E+6111
dqshi173 shift -9.999999999999999999999999999999999E+6144      1  -> -9.999999999999999999999999999999990E+6144
dqshi174 shift -9.999999999999999999999999999999999E+6144      33 -> -9.000000000000000000000000000000000E+6144
dqshi175 shift -1E-6143                                       -1  -> -0E-6143
dqshi176 shift -1E-6143                                       -33 -> -0E-6143
dqshi177 shift -1E-6143                                        1  -> -1.0E-6142
dqshi178 shift -1E-6143                                        33 -> -1.000000000000000000000000000000000E-6110
dqshi181 shift -1.000000000000000000000000000000000E-6143     -1  -> -1.00000000000000000000000000000000E-6144
dqshi182 shift -1.000000000000000000000000000000000E-6143     -33 -> -1E-6176
dqshi183 shift -1.000000000000000000000000000000000E-6143      1  -> -0E-6176
dqshi184 shift -1.000000000000000000000000000000000E-6143      33 -> -0E-6176
dqshi185 shift -9.000000000000000000000000000000000E-6143     -1  -> -9.00000000000000000000000000000000E-6144
dqshi186 shift -9.000000000000000000000000000000000E-6143     -33 -> -9E-6176
dqshi187 shift -9.000000000000000000000000000000000E-6143      1  -> -0E-6176
dqshi188 shift -9.000000000000000000000000000000000E-6143      33 -> -0E-6176
dqshi190 shift -1E-6176                                       -1  -> -0E-6176
dqshi191 shift -1E-6176                                       -33 -> -0E-6176
dqshi192 shift -1E-6176                                        1  -> -1.0E-6175
dqshi193 shift -1E-6176                                        33 -> -1.000000000000000000000000000000000E-6143

-- more negatives (of sanities)
dqshi201 shift                                  -0    0  -> -0
dqshi202 shift                                  -0    2  -> -0
dqshi203 shift                                  -1    2  -> -100
dqshi204 shift                                  -1   33  -> -1000000000000000000000000000000000
dqshi205 shift                                  -1   34  -> -0
dqshi206 shift                                  -1   -1  -> -0
dqshi207 shift                                  -0   -2  -> -0
dqshi208 shift -1234567890123456789012345678901234   -1  -> -123456789012345678901234567890123
dqshi209 shift -1234567890123456789012345678901234   -33 -> -1
dqshi210 shift -1234567890123456789012345678901234   -34 -> -0
dqshi211 shift -9934567890123456789012345678901234   -33 -> -9
dqshi212 shift -9934567890123456789012345678901234   -34 -> -0


-- Specials; NaNs are handled as usual
dqshi781 shift -Inf  -8     -> -Infinity
dqshi782 shift -Inf  -1     -> -Infinity
dqshi783 shift -Inf  -0     -> -Infinity
dqshi784 shift -Inf   0     -> -Infinity
dqshi785 shift -Inf   1     -> -Infinity
dqshi786 shift -Inf   8     -> -Infinity
dqshi787 shift -1000 -Inf   -> NaN Invalid_operation
dqshi788 shift -Inf  -Inf   -> NaN Invalid_operation
dqshi789 shift -1    -Inf   -> NaN Invalid_operation
dqshi790 shift -0    -Inf   -> NaN Invalid_operation
dqshi791 shift  0    -Inf   -> NaN Invalid_operation
dqshi792 shift  1    -Inf   -> NaN Invalid_operation
dqshi793 shift  1000 -Inf   -> NaN Invalid_operation
dqshi794 shift  Inf  -Inf   -> NaN Invalid_operation

dqshi800 shift  Inf  -Inf   -> NaN Invalid_operation
dqshi801 shift  Inf  -8     -> Infinity
dqshi802 shift  Inf  -1     -> Infinity
dqshi803 shift  Inf  -0     -> Infinity
dqshi804 shift  Inf   0     -> Infinity
dqshi805 shift  Inf   1     -> Infinity
dqshi806 shift  Inf   8     -> Infinity
dqshi807 shift  Inf   Inf   -> NaN Invalid_operation
dqshi808 shift -1000  Inf   -> NaN Invalid_operation
dqshi809 shift -Inf   Inf   -> NaN Invalid_operation
dqshi810 shift -1     Inf   -> NaN Invalid_operation
dqshi811 shift -0     Inf   -> NaN Invalid_operation
dqshi812 shift  0     Inf   -> NaN Invalid_operation
dqshi813 shift  1     Inf   -> NaN Invalid_operation
dqshi814 shift  1000  Inf   -> NaN Invalid_operation
dqshi815 shift  Inf   Inf   -> NaN Invalid_operation

dqshi821 shift  NaN -Inf    ->  NaN
dqshi822 shift  NaN -1000   ->  NaN
dqshi823 shift  NaN -1      ->  NaN
dqshi824 shift  NaN -0      ->  NaN
dqshi825 shift  NaN  0      ->  NaN
dqshi826 shift  NaN  1      ->  NaN
dqshi827 shift  NaN  1000   ->  NaN
dqshi828 shift  NaN  Inf    ->  NaN
dqshi829 shift  NaN  NaN    ->  NaN
dqshi830 shift -Inf  NaN    ->  NaN
dqshi831 shift -1000 NaN    ->  NaN
dqshi832 shift -1    NaN    ->  NaN
dqshi833 shift -0    NaN    ->  NaN
dqshi834 shift  0    NaN    ->  NaN
dqshi835 shift  1    NaN    ->  NaN
dqshi836 shift  1000 NaN    ->  NaN
dqshi837 shift  Inf  NaN    ->  NaN

dqshi841 shift  sNaN -Inf   ->  NaN  Invalid_operation
dqshi842 shift  sNaN -1000  ->  NaN  Invalid_operation
dqshi843 shift  sNaN -1     ->  NaN  Invalid_operation
dqshi844 shift  sNaN -0     ->  NaN  Invalid_operation
dqshi845 shift  sNaN  0     ->  NaN  Invalid_operation
dqshi846 shift  sNaN  1     ->  NaN  Invalid_operation
dqshi847 shift  sNaN  1000  ->  NaN  Invalid_operation
dqshi848 shift  sNaN  NaN   ->  NaN  Invalid_operation
dqshi849 shift  sNaN sNaN   ->  NaN  Invalid_operation
dqshi850 shift  NaN  sNaN   ->  NaN  Invalid_operation
dqshi851 shift -Inf  sNaN   ->  NaN  Invalid_operation
dqshi852 shift -1000 sNaN   ->  NaN  Invalid_operation
dqshi853 shift -1    sNaN   ->  NaN  Invalid_operation
dqshi854 shift -0    sNaN   ->  NaN  Invalid_operation
dqshi855 shift  0    sNaN   ->  NaN  Invalid_operation
dqshi856 shift  1    sNaN   ->  NaN  Invalid_operation
dqshi857 shift  1000 sNaN   ->  NaN  Invalid_operation
dqshi858 shift  Inf  sNaN   ->  NaN  Invalid_operation
dqshi859 shift  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
dqshi861 shift  NaN1   -Inf    ->  NaN1
dqshi862 shift +NaN2   -1000   ->  NaN2
dqshi863 shift  NaN3    1000   ->  NaN3
dqshi864 shift  NaN4    Inf    ->  NaN4
dqshi865 shift  NaN5   +NaN6   ->  NaN5
dqshi866 shift -Inf     NaN7   ->  NaN7
dqshi867 shift -1000    NaN8   ->  NaN8
dqshi868 shift  1000    NaN9   ->  NaN9
dqshi869 shift  Inf    +NaN10  ->  NaN10
dqshi871 shift  sNaN11  -Inf   ->  NaN11  Invalid_operation
dqshi872 shift  sNaN12  -1000  ->  NaN12  Invalid_operation
dqshi873 shift  sNaN13   1000  ->  NaN13  Invalid_operation
dqshi874 shift  sNaN14   NaN17 ->  NaN14  Invalid_operation
dqshi875 shift  sNaN15  sNaN18 ->  NaN15  Invalid_operation
dqshi876 shift  NaN16   sNaN19 ->  NaN19  Invalid_operation
dqshi877 shift -Inf    +sNaN20 ->  NaN20  Invalid_operation
dqshi878 shift -1000    sNaN21 ->  NaN21  Invalid_operation
dqshi879 shift  1000    sNaN22 ->  NaN22  Invalid_operation
dqshi880 shift  Inf     sNaN23 ->  NaN23  Invalid_operation
dqshi881 shift +NaN25  +sNaN24 ->  NaN24  Invalid_operation
dqshi882 shift -NaN26    NaN28 -> -NaN26
dqshi883 shift -sNaN27  sNaN29 -> -NaN27  Invalid_operation
dqshi884 shift  1000    -NaN30 -> -NaN30
dqshi885 shift  1000   -sNaN31 -> -NaN31  Invalid_operation
