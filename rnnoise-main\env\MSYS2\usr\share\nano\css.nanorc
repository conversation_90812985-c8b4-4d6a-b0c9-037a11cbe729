## Syntax highlighting for CSS files.

## Original author:  <PERSON>

syntax css "\.css$"
comment "/*|*/"

# First make everything red:
color brightred "."
# Then everything between braces yellow:
color brightyellow start="\{" end="\}"
# Then everything after a colon white:
color brightwhite start=":" end="([;^{]|$)"

# Pseudo-classes:
color brightcyan ":(active|checked|focus|hover|link|visited|after|before)\>"

# Comments:
color brightblue start="/\*" end="\*/"

# Syntactic characters:
color green ";|:|\{|\}"
