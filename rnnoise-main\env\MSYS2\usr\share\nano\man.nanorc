## Syntax highlighting for man pages.

## Original author:  <PERSON>
## License:  GPL version 3 or newer

syntax man "\.[1-9]x?$"
magic "troff or preprocessor input"
comment ".\""

# Section headers, title line, and paragraphs.
color green "^\.(SH|SS|TH) .*"
color brightgreen "^\.((SH|SS|TH) |[HIT]P|TQ|LP$|P?P$)"
# Type faces, and synopses.
color brightred "^\.(B[IR]?|I[BR]?|RB|RI|SB|SM|SY|OP) .*"
color brightblue "^\.((B[IR]?|I[BR]?|RB|RI|SB|SM|SY|OP) |YS$)"
# Inline type faces.
color magenta "\\f[BIPR]"
# Some escapes:
color purple "\\([%&:e~]|\(e[mn])"
# Hyphenation control.
color yellow "^\.(hc|hla|hlm|hw|hy)"
# Relative margins, hyperlinks, and various other stuff.
color yellow "^\.(RS|RE|UR|UE|MT|ME|EX|EE|PD|DT)"
color yellow "^\.(ad|bp|br|ce|de|ds|el|ie|if|fi|ft|ig|in|na|ne|nf|nh|ps|so|sp|ti|tr)"

# Comments.
color cyan "(^\.)?\\".*"

# Trailing whitespace.
color ,green "[[:space:]]+$"
