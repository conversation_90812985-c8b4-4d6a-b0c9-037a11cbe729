<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_SIGNATURE-SLH-DSA</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#SLH-DSA-Signature-Parameters">SLH-DSA Signature Parameters</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_SIGNATURE-SLH-DSA, EVP_SIGNATURE-SLH-DSA-SHA2-128s, EVP_SIGNATURE-SLH-DSA-SHA2-128f, EVP_SIGNATURE-SLH-DSA-SHA2-192s, EVP_SIGNATURE-SLH-DSA-SHA2-192f, EVP_SIGNATURE-SLH-DSA-SHA2-256s, EVP_SIGNATURE-SLH-DSA-SHA2-256f, EVP_SIGNATURE-SLH-DSA-SHAKE-128s, EVP_SIGNATURE-SLH-DSA-SHAKE-128f, EVP_SIGNATURE-SLH-DSA-SHAKE-192s, EVP_SIGNATURE-SLH-DSA-SHAKE-192f, EVP_SIGNATURE-SLH-DSA-SHAKE-256s, EVP_SIGNATURE-SLH-DSA-SHAKE-256f - EVP_PKEY SLH-DSA support</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>SLH-DSA-SHA2-128s</b>, <b>EVP_PKEY-SLH-DSA-SHA2-128f</b>, <b>SLH-DSA-SHA2-192s</b>, <b>EVP_PKEY-SLH-DSA-SHA2-192f</b>, <b>SLH-DSA-SHA2-256s</b>, <b>EVP_PKEY-SLH-DSA-SHA2-256f</b>, <b>SLH-DSA-SHAKE-128s</b>, <b>EVP_PKEY-SLH-DSA-SHAKE-128f</b>, <b>SLH-DSA-SHAKE-192s</b>, <b>EVP_PKEY-SLH-DSA-SHAKE-192f</b>, <b>SLH-DSA-SHAKE-256s</b> and <b>EVP_PKEY-SLH-DSA-SHAKE-256f</b> EVP_PKEY implementations supports key generation, one-shot sign and verify using the SLH-DSA signature schemes described in FIPS 205.</p>

<p>The different algorithms names correspond to the parameter sets defined in FIPS 205 Section 11 Table 2. <code>s</code> types have smaller signature sizes, and the <code>f</code> variants are faster, (The signatures range from ~8K to ~50K depending on the type chosen). There are 3 different security categories also depending on the type.</p>

<p><a href="../man3/EVP_SIGNATURE_fetch.html">EVP_SIGNATURE_fetch(3)</a> can be used to explicitely fetch one of the 12 algorithms which can then be used with <a href="../man3/EVP_PKEY_sign_message_init.html">EVP_PKEY_sign_message_init(3)</a>, <a href="../man3/EVP_PKEY_sign.html">EVP_PKEY_sign(3)</a>, <a href="../man3/EVP_PKEY_verify_message_init.html">EVP_PKEY_verify_message_init(3)</a>, and <a href="../man3/EVP_PKEY_verify.html">EVP_PKEY_verify(3)</a> to perform one-shot message signing or verification.</p>

<p>The normal signing process (called Pure SLH-DSA Signature Generation) encodes the message internally as 0x00 || len(ctx) || ctx || message. where <b>ctx</b> is some optional value of size 0x00..0xFF. OpenSSL also allows the message to not be encoded which is required for testing. OpenSSL does not support Pre Hash SLH-DSA Signature Generation, but this may be done by the user by doing Pre hash encoding externally and then chosing the option to not encode the message.</p>

<h2 id="SLH-DSA-Signature-Parameters">SLH-DSA Signature Parameters</h2>

<p>The <code>context-string</code> parameter, described below, can be used for both signing and verification. It may be set by passing an OSSL_PARAM array to <a href="../man3/EVP_PKEY_sign_init_ex2.html">EVP_PKEY_sign_init_ex2(3)</a> or <a href="../man3/EVP_PKEY_verify_init_ex2.html">EVP_PKEY_verify_init_ex2(3)</a></p>

<dl>

<dt id="context-string-OSSL_SIGNATURE_PARAM_CONTEXT_STRING-octet-string">&quot;context-string&quot; (<b>OSSL_SIGNATURE_PARAM_CONTEXT_STRING</b>) &lt;octet string&gt;</dt>
<dd>

<p>A string of octets with length at most 255. By default it is the empty string.</p>

</dd>
</dl>

<p>The following parameters can be used when signing: They can be set by passing an OSSL_PARAM array to <a href="../man3/EVP_PKEY_sign_init_ex2.html">EVP_PKEY_sign_init_ex2(3)</a>.</p>

<dl>

<dt id="message-encoding-OSSL_SIGNATURE_PARAM_MESSAGE_ENCODING-integer">&quot;message-encoding&quot; (<b>OSSL_SIGNATURE_PARAM_MESSAGE_ENCODING</b>) &lt;integer&gt;</dt>
<dd>

<p>The default value of 1 uses &#39;Pure SLH-DSA Signature Generation&#39; as described above. Setting it to 0 does not encode the message, which is used for testing, but can also be used for &#39;Pre Hash SLH-DSA Signature Generation&#39;.</p>

</dd>
<dt id="test-entropy-OSSL_SIGNATURE_PARAM_TEST_ENTROPY-octet-string">&quot;test-entropy&quot; (<b>OSSL_SIGNATURE_PARAM_TEST_ENTROPY &lt;octet string</b></dt>
<dd>

<p>Used for testing to pass a optional random value.</p>

</dd>
<dt id="deterministic-OSSL_SIGNATURE_PARAM_DETERMINISTIC-integer">&quot;deterministic&quot; (<b>OSSL_SIGNATURE_PARAM_DETERMINISTIC</b>) &lt;integer&gt;</dt>
<dd>

<p>The default value of 0 generates a random value (using a DRBG) this is used when processing the message. Setting this to 1 causes the private key seed to be used instead. This value is ignored if &quot;test-entropy&quot; is set.</p>

</dd>
</dl>

<p>See <a href="../man7/EVP_PKEY-SLH-DSA.html">EVP_PKEY-SLH-DSA(7)</a> for information related to <b>SLH-DSA</b> keys.</p>

<h1 id="NOTES">NOTES</h1>

<p>For backwards compatibility reasons EVP_DigestSignInit_ex(), EVP_DigestSign(), EVP_DigestVerifyInit_ex() and EVP_DigestVerify() may also be used, but the digest passed in <i>mdname</i> must be NULL.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>To sign a message using an SLH-DSA EVP_PKEY structure:</p>

<pre><code>void do_sign(EVP_PKEY *key, unsigned char *msg, size_t msg_len)
{
    size_t sig_len;
    unsigned char *sig = NULL;
    const OSSL_PARAM params[] = {
        OSSL_PARAM_octet_string(&quot;context-string&quot;, (unsigned char *)&quot;A context string&quot;, 33),
        OSSL_PARAM_END
    };
    EVP_PKEY_CTX *sctx = EVP_PKEY_CTX_new_from_pkey(NULL, pkey, NULL);
    EVP_SIGNATURE *sig_alg = EVP_SIGNATURE_fetch(NULL, &quot;SLH-DSA-SHA2-128s&quot;, NULL);

    EVP_PKEY_sign_message_init(sctx, sig_alg, params);
    /* Calculate the required size for the signature by passing a NULL buffer. */
    EVP_PKEY_sign(sctx, NULL, &amp;sig_len, msg, msg_len);
    sig = OPENSSL_zalloc(sig_len);
    EVP_PKEY_sign(sctx, sig, &amp;sig_len, msg, msg_len);
    ...
    OPENSSL_free(sig);
    EVP_SIGNATURE(sig_alg);
    EVP_PKEY_CTX_free(sctx);
}</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/EVP_PKEY-SLH-DSA.html">EVP_PKEY-SLH-DSA(7)</a> <a href="../man7/provider-signature.html">provider-signature(7)</a>, <a href="../man3/EVP_PKEY_sign.html">EVP_PKEY_sign(3)</a>, <a href="../man3/EVP_PKEY_verify.html">EVP_PKEY_verify(3)</a>,</p>

<h1 id="HISTORY">HISTORY</h1>

<p>This functionality was added in OpenSSL 3.5.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2024-2025 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


