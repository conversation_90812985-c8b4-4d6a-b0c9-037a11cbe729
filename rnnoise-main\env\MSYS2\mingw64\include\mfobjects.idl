cpp_quote("/**")
cpp_quote(" * This file is part of the mingw-w64 runtime package.")
cpp_quote(" * No warranty is given; refer to the file <PERSON><PERSON><PERSON><PERSON>IMER within this package.")
cpp_quote(" */")
cpp_quote("")
cpp_quote("#include <winapifamily.h>")
cpp_quote("")

import "unknwn.idl";
import "propsys.idl";
import "mediaobj.idl";

#ifndef __QWORD_DEFINED
#define __QWORD_DEFINED
cpp_quote("")
typedef ULONGLONG QWORD;
#endif

cpp_quote("")
cpp_quote("#include <mmreg.h>")
#include <mmreg.h>

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
typedef enum _MF_ATTRIBUTE_TYPE {
  MF_ATTRIBUTE_UINT32 = VT_UI4,
  MF_ATTRIBUTE_UINT64 = VT_UI8,
  MF_ATTRIBUTE_DOUBLE = VT_R8,
  MF_ATTRIBUTE_GUID = VT_CLSID,
  MF_ATTRIBUTE_STRING = VT_LPWSTR,
  MF_ATTRIBUTE_BLOB = VT_VECTOR | VT_UI1,
  MF_ATTRIBUTE_IUNKNOWN = VT_UNKNOWN
} MF_ATTRIBUTE_TYPE;

cpp_quote("")
typedef enum _MF_ATTRIBUTES_MATCH_TYPE {
  MF_ATTRIBUTES_MATCH_OUR_ITEMS = 0,
  MF_ATTRIBUTES_MATCH_THEIR_ITEMS = 1,
  MF_ATTRIBUTES_MATCH_ALL_ITEMS = 2,
  MF_ATTRIBUTES_MATCH_INTERSECTION = 3,
  MF_ATTRIBUTES_MATCH_SMALLER = 4
} MF_ATTRIBUTES_MATCH_TYPE;

cpp_quote("")
[object, uuid (2cd2d921-c447-44a7-a13c-4adabfc247e3)]
interface IMFAttributes : IUnknown {
  HRESULT GetItem (REFGUID guidKey,[in, out, ptr] PROPVARIANT *pValue);
  HRESULT GetItemType (REFGUID guidKey,[out] MF_ATTRIBUTE_TYPE *pType);
  HRESULT CompareItem (REFGUID guidKey, REFPROPVARIANT Value,[out] WINBOOL *pbResult);
  HRESULT Compare (IMFAttributes *pTheirs, MF_ATTRIBUTES_MATCH_TYPE MatchType,[out] WINBOOL *pbResult);
  HRESULT GetUINT32 (REFGUID guidKey,[out] UINT32 *punValue);
  HRESULT GetUINT64 (REFGUID guidKey,[out] UINT64 *punValue);
  HRESULT GetDouble (REFGUID guidKey,[out] double *pfValue);
  HRESULT GetGUID (REFGUID guidKey,[out] GUID *pguidValue);
  HRESULT GetStringLength (REFGUID guidKey,[out] UINT32 *pcchLength);
  HRESULT GetString (REFGUID guidKey,[out, size_is (cchBufSize)] LPWSTR pwszValue, UINT32 cchBufSize,[in, out, ptr] UINT32 *pcchLength);
  HRESULT GetAllocatedString (REFGUID guidKey,[out, size_is (,*pcchLength + 1)] LPWSTR *ppwszValue,[out] UINT32 *pcchLength);
  HRESULT GetBlobSize (REFGUID guidKey,[out] UINT32 *pcbBlobSize);
  HRESULT GetBlob (REFGUID guidKey,[out, size_is (cbBufSize)] UINT8 *pBuf, UINT32 cbBufSize,[in, out, ptr] UINT32 *pcbBlobSize);
  HRESULT GetAllocatedBlob (REFGUID guidKey,[out, size_is (,*pcbSize)] UINT8 **ppBuf,[out] UINT32 *pcbSize);
  HRESULT GetUnknown (REFGUID guidKey, REFIID riid,[out, iid_is (riid)] LPVOID *ppv);
  HRESULT SetItem (REFGUID guidKey, REFPROPVARIANT Value);
  HRESULT DeleteItem (REFGUID guidKey);
  HRESULT DeleteAllItems ();
  HRESULT SetUINT32 (REFGUID guidKey, UINT32 unValue);
  HRESULT SetUINT64 (REFGUID guidKey, UINT64 unValue);
  HRESULT SetDouble (REFGUID guidKey, double fValue);
  HRESULT SetGUID (REFGUID guidKey, REFGUID guidValue);
  HRESULT SetString (REFGUID guidKey,[in, string] LPCWSTR wszValue);
  HRESULT SetBlob (REFGUID guidKey,[in, size_is (cbBufSize)] const UINT8 *pBuf, UINT32 cbBufSize);
  HRESULT SetUnknown (REFGUID guidKey,[in] IUnknown *pUnknown);
  HRESULT LockStore ();
  HRESULT UnlockStore ();
  HRESULT GetCount ([out] UINT32 *pcItems);
  HRESULT GetItemByIndex (UINT32 unIndex,[out] GUID *pguidKey,[in, out, ptr] PROPVARIANT *pValue);
  HRESULT CopyAllItems ([in] IMFAttributes *pDest);
};

cpp_quote("")
enum MF_ATTRIBUTE_SERIALIZE_OPTIONS {
  MF_ATTRIBUTE_SERIALIZE_UNKNOWN_BYREF = 0x00000001
};

cpp_quote("")
cpp_quote("STDAPI MFSerializeAttributesToStream(IMFAttributes *pAttr, DWORD dwOptions, IStream *pStm);")
cpp_quote("STDAPI MFDeserializeAttributesFromStream(IMFAttributes *pAttr, DWORD dwOptions, IStream *pStm);")

cpp_quote("")
[object, uuid (045fa593-8799-42b8-BC8D-8968c6453507), local,]
interface IMFMediaBuffer : IUnknown {
  HRESULT Lock ([out] BYTE **ppbBuffer,[out] DWORD *pcbMaxLength,[out] DWORD *pcbCurrentLength);
  HRESULT Unlock ();
  HRESULT GetCurrentLength ([out] DWORD *pcbCurrentLength);
  HRESULT SetCurrentLength ([in] DWORD cbCurrentLength);
  HRESULT GetMaxLength ([out] DWORD *pcbMaxLength);
}

cpp_quote("")
[object, uuid (c40a00f2-b93a-4d80-ae8c-5a1c634f58e4), local,]
interface IMFSample : IMFAttributes {
  HRESULT GetSampleFlags ([out] DWORD *pdwSampleFlags);
  HRESULT SetSampleFlags ([in] DWORD dwSampleFlags);
  HRESULT GetSampleTime ([out] LONGLONG *phnsSampleTime);
  HRESULT SetSampleTime ([in] LONGLONG hnsSampleTime);
  HRESULT GetSampleDuration ([out] LONGLONG *phnsSampleDuration);
  HRESULT SetSampleDuration ([in] LONGLONG hnsSampleDuration);
  HRESULT GetBufferCount ([out] DWORD *pdwBufferCount);
  HRESULT GetBufferByIndex ([in] DWORD dwIndex,[out] IMFMediaBuffer **ppBuffer);
  HRESULT ConvertToContiguousBuffer ([out] IMFMediaBuffer **ppBuffer);
  HRESULT AddBuffer ([in] IMFMediaBuffer *pBuffer);
  HRESULT RemoveBufferByIndex ([in] DWORD dwIndex);
  HRESULT RemoveAllBuffers (void);
  HRESULT GetTotalLength ([out] DWORD *pcbTotalLength);
  HRESULT CopyToBuffer ([in] IMFMediaBuffer *pBuffer);
};

cpp_quote("")
[object, uuid (7dc9d5f9-9ed9-44ec-9bbf-0600bb589fbb), local]
interface IMF2DBuffer : IUnknown {
  HRESULT Lock2D ([out] BYTE **ppbScanline0,[out] LONG *plPitch);
  HRESULT Unlock2D ();
  HRESULT GetScanline0AndPitch ([out] BYTE **pbScanline0,[out] LONG *plPitch);
  HRESULT IsContiguousFormat ([out] WINBOOL *pfIsContiguous);
  HRESULT GetContiguousLength ([out] DWORD *pcbLength);
  HRESULT ContiguousCopyTo ([out, size_is (cbDestBuffer)] BYTE *pbDestBuffer,[in] DWORD cbDestBuffer);
  HRESULT ContiguousCopyFrom ([in, size_is (cbSrcBuffer)] const BYTE *pbSrcBuffer,[in] DWORD cbSrcBuffer);
}

cpp_quote("")
typedef enum _MF2DBuffer_LockFlags {
  MF2DBuffer_LockFlags_LockTypeMask = 0x1 | 0x2 | 0x3,
  MF2DBuffer_LockFlags_Read = 0x1,
  MF2DBuffer_LockFlags_Write = 0x2,
  MF2DBuffer_LockFlags_ReadWrite = 0x3,
  MF2DBuffer_LockFlags_ForceDWORD = 0x7fffffff
} MF2DBuffer_LockFlags;

cpp_quote("")
[object, uuid (33ae5ea6-4316-436f-8ddd-d73d22f829ec), local]
interface IMF2DBuffer2 : IMF2DBuffer {
  HRESULT Lock2DSize ([in] MF2DBuffer_LockFlags lockFlags,[out] BYTE **ppbScanline0,[out] LONG *plPitch,[out] BYTE **ppbBufferStart,[out] DWORD *pcbBufferLength);
  HRESULT Copy2DTo ([in] IMF2DBuffer2 *pDestBuffer);
};

cpp_quote("")
[object, uuid (e7174cfa-1c9e-48b1-8866-626226bfc258), local]
interface IMFDXGIBuffer : IUnknown {
  HRESULT GetResource ([in] REFIID riid,[out] LPVOID *ppvObject);
  HRESULT GetSubresourceIndex ([out] UINT *puSubresource);
  HRESULT GetUnknown ([in] REFIID guid,[in] REFIID riid,[out] LPVOID *ppvObject);
  HRESULT SetUnknown ([in] REFIID guid,[in] IUnknown *pUnkData);
};

cpp_quote("")
[object, uuid (44ae0fa8-ea31-4109-8d2e-4cae4997c555), local]
interface IMFMediaType : IMFAttributes {
  HRESULT GetMajorType ([out] GUID *pguidMajorType);
  HRESULT IsCompressedFormat ([out] WINBOOL *pfCompressed);
  HRESULT IsEqual ([in] IMFMediaType *pIMediaType,[out] DWORD *pdwFlags);
  HRESULT GetRepresentation ([in] GUID guidRepresentation,[out] LPVOID *ppvRepresentation);
  HRESULT FreeRepresentation ([in] GUID guidRepresentation,[in] LPVOID pvRepresentation);
};

cpp_quote("")
cpp_quote("#define MF_MEDIATYPE_EQUAL_MAJOR_TYPES 0x00000001")
cpp_quote("#define MF_MEDIATYPE_EQUAL_FORMAT_TYPES 0x00000002")
cpp_quote("#define MF_MEDIATYPE_EQUAL_FORMAT_DATA 0x00000004")
cpp_quote("#define MF_MEDIATYPE_EQUAL_FORMAT_USER_DATA 0x00000008")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, uuid (26a0adc3-ce26-4672-9304-69552edd3faf), local]
interface IMFAudioMediaType : IMFMediaType {
  const WAVEFORMATEX *GetAudioFormat ();
};
cpp_quote("")
cpp_quote("#ifndef _WINGDI_")
#ifndef RGBQUAD
typedef DWORD RGBQUAD;
#endif

cpp_quote("")
typedef struct {
  DWORD biSize;
  LONG biWidth;
  LONG biHeight;
  WORD biPlanes;
  WORD biBitCount;
  DWORD biCompression;
  DWORD biSizeImage;
  LONG biXPelsPerMeter;
  LONG biYPelsPerMeter;
  DWORD biClrUsed;
  DWORD biClrImportant;
} BITMAPINFOHEADER;

cpp_quote("")
typedef struct {
  BITMAPINFOHEADER bmiHeader;
  RGBQUAD bmiColors[1];
} BITMAPINFO;
cpp_quote("#endif")

cpp_quote("")
typedef struct {
  GUID guidMajorType;
  GUID guidSubtype;
} MFT_REGISTER_TYPE_INFO;
cpp_quote("#endif")

cpp_quote("#ifndef _MFVIDEOFORMAT_")
cpp_quote("#define _MFVIDEOFORMAT_")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
typedef enum _MFVideoInterlaceMode {
  MFVideoInterlace_Unknown = 0,
  MFVideoInterlace_Progressive = 2,
  MFVideoInterlace_FieldInterleavedUpperFirst = 3,
  MFVideoInterlace_FieldInterleavedLowerFirst = 4,
  MFVideoInterlace_FieldSingleUpper = 5,
  MFVideoInterlace_FieldSingleLower = 6,
  MFVideoInterlace_MixedInterlaceOrProgressive= 7,
  MFVideoInterlace_Last,
  MFVideoInterlace_ForceDWORD = 0x7fffffff
} MFVideoInterlaceMode;
cpp_quote("")
cpp_quote("#define MFVideoInterlace_FieldSingleUpperFirst MFVideoInterlace_FieldSingleUpper")
cpp_quote("#define MFVideoInterlace_FieldSingleLowerFirst MFVideoInterlace_FieldSingleLower")

cpp_quote("")
typedef enum _MFVideoTransferFunction {
  MFVideoTransFunc_Unknown = 0,
  MFVideoTransFunc_10 = 1,
  MFVideoTransFunc_18 = 2,
  MFVideoTransFunc_20 = 3,
  MFVideoTransFunc_22 = 4,
  MFVideoTransFunc_709 = 5,
  MFVideoTransFunc_240M = 6,
  MFVideoTransFunc_sRGB = 7,
  MFVideoTransFunc_28 = 8,
  MFVideoTransFunc_Log_100 = 9,
  MFVideoTransFunc_Log_316 = 10,
  MFVideoTransFunc_709_sym = 11,
  MFVideoTransFunc_2020_const = 12,
  MFVideoTransFunc_2020 = 13,
  MFVideoTransFunc_26 = 14,
  MFVideoTransFunc_2084 = 15,
  MFVideoTransFunc_HLG = 16,
  MFVideoTransFunc_10_rel = 17,
  MFVideoTransFunc_Last,
  MFVideoTransFunc_ForceDWORD = 0x7fffffff
} MFVideoTransferFunction;

cpp_quote("")
typedef enum _MFVideoPrimaries {
  MFVideoPrimaries_Unknown = 0,
  MFVideoPrimaries_reserved = 1,
  MFVideoPrimaries_BT709 = 2,
  MFVideoPrimaries_BT470_2_SysM = 3,
  MFVideoPrimaries_BT470_2_SysBG = 4,
  MFVideoPrimaries_SMPTE170M = 5,
  MFVideoPrimaries_SMPTE240M = 6,
  MFVideoPrimaries_EBU3213 = 7,
  MFVideoPrimaries_SMPTE_C = 8,
  MFVideoPrimaries_BT2020 = 9,
  MFVideoPrimaries_XYZ = 10,
  MFVideoPrimaries_DCI_P3 = 11,
  MFVideoPrimaries_ACES = 12,
  MFVideoPrimaries_Last,
  MFVideoPrimaries_ForceDWORD = 0x7fffffff
} MFVideoPrimaries;

cpp_quote("")
typedef enum _MFVideoLighting {
  MFVideoLighting_Unknown = 0,
  MFVideoLighting_bright=1,
  MFVideoLighting_office=2,
  MFVideoLighting_dim=3,
  MFVideoLighting_dark=4,
  MFVideoLighting_Last,
  MFVideoLighting_ForceDWORD = 0x7fffffff
} MFVideoLighting;

cpp_quote("")
typedef enum _MFVideoTransferMatrix {
  MFVideoTransferMatrix_Unknown = 0,
  MFVideoTransferMatrix_BT709 = 1,
  MFVideoTransferMatrix_BT601 = 2,
  MFVideoTransferMatrix_SMPTE240M = 3,
  MFVideoTransferMatrix_BT2020_10 = 4,
  MFVideoTransferMatrix_BT2020_12 = 5,
  MFVideoTransferMatrix_Last,
  MFVideoTransferMatrix_ForceDWORD = 0x7fffffff
} MFVideoTransferMatrix;

cpp_quote("")
typedef enum _MFVideoChromaSubsampling {
  MFVideoChromaSubsampling_Unknown = 0,
  MFVideoChromaSubsampling_ProgressiveChroma = 0x8,
  MFVideoChromaSubsampling_Horizontally_Cosited = 0x4,
  MFVideoChromaSubsampling_Vertically_Cosited = 0x2,
  MFVideoChromaSubsampling_Vertically_AlignedChromaPlanes = 0x1,
  MFVideoChromaSubsampling_MPEG2 = MFVideoChromaSubsampling_Horizontally_Cosited |
  MFVideoChromaSubsampling_Vertically_AlignedChromaPlanes,
  MFVideoChromaSubsampling_MPEG1 = MFVideoChromaSubsampling_Vertically_AlignedChromaPlanes,
  MFVideoChromaSubsampling_DV_PAL = MFVideoChromaSubsampling_Horizontally_Cosited |
  MFVideoChromaSubsampling_Vertically_Cosited,
  MFVideoChromaSubsampling_Cosited = MFVideoChromaSubsampling_Horizontally_Cosited |
  MFVideoChromaSubsampling_Vertically_Cosited |
  MFVideoChromaSubsampling_Vertically_AlignedChromaPlanes,
  MFVideoChromaSubsampling_Last = MFVideoChromaSubsampling_Cosited+1,
  MFVideoChromaSubsampling_ForceDWORD = 0x7fffffff
} MFVideoChromaSubsampling;

cpp_quote("")
typedef enum _MFNominalRange {
  MFNominalRange_Unknown = 0,
  MFNominalRange_Normal = 1,
  MFNominalRange_Wide = 2,
  MFNominalRange_0_255 = 1,
  MFNominalRange_16_235 = 2,
  MFNominalRange_48_208 = 3,
  MFNominalRange_64_127 = 4,
  MFNominalRange_Last,
  MFNominalRange_ForceDWORD = 0x7fffffff
} MFNominalRange;
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

typedef enum _MFVideoFlags {
  MFVideoFlag_PAD_TO_Mask = 0x0001 | 0x0002,
  MFVideoFlag_PAD_TO_None = 0 *0x0001,
  MFVideoFlag_PAD_TO_4x3 = 1 *0x0001,
  MFVideoFlag_PAD_TO_16x9 = 2 *0x0001,
  MFVideoFlag_SrcContentHintMask = 0x0004 | 0x0008 | 0x0010,
  MFVideoFlag_SrcContentHintNone = 0 *0x0004,
  MFVideoFlag_SrcContentHint16x9 = 1 *0x0004,
  MFVideoFlag_SrcContentHint235_1 = 2 *0x0004,
  MFVideoFlag_AnalogProtected = 0x0020,
  MFVideoFlag_DigitallyProtected = 0x0040,
  MFVideoFlag_ProgressiveContent = 0x0080,
  MFVideoFlag_FieldRepeatCountMask = 0x0100 | 0x0200 | 0x0400,
  MFVideoFlag_FieldRepeatCountShift = 8,
  MFVideoFlag_ProgressiveSeqReset = 0x0800,
  MFVideoFlag_PanScanEnabled = 0x20000,
  MFVideoFlag_LowerFieldFirst = 0x40000,
  MFVideoFlag_BottomUpLinearRep = 0x80000,
  MFVideoFlags_DXVASurface = 0x100000,
  MFVideoFlags_RenderTargetSurface = 0x400000,
  MFVideoFlags_ForceQWORD = 0x7fffffff
} MFVideoFlags;
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
typedef struct _MFRatio {
  DWORD Numerator;
  DWORD Denominator;
} MFRatio;

cpp_quote("")
typedef struct _MFOffset {
  WORD fract;
  short value;
} MFOffset;
typedef struct _MFVideoArea {
  MFOffset OffsetX;
  MFOffset OffsetY;
  SIZE Area;
} MFVideoArea;
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
typedef struct _MFVideoInfo {
  DWORD dwWidth;
  DWORD dwHeight;
  MFRatio PixelAspectRatio;
  MFVideoChromaSubsampling SourceChromaSubsampling;
  MFVideoInterlaceMode InterlaceMode;
  MFVideoTransferFunction TransferFunction;
  MFVideoPrimaries ColorPrimaries;
  MFVideoTransferMatrix TransferMatrix;
  MFVideoLighting SourceLighting;
  MFRatio FramesPerSecond;
  MFNominalRange NominalRange;
  MFVideoArea GeometricAperture;
  MFVideoArea MinimumDisplayAperture;
  MFVideoArea PanScanAperture;
  unsigned __int64 VideoFlags;
} MFVideoInfo;
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
typedef struct __MFAYUVSample {
  BYTE bCrValue;
  BYTE bCbValue;
  BYTE bYValue;
  BYTE bSampleAlpha8;
} MFAYUVSample;

cpp_quote("")
typedef struct _MFARGB {
  BYTE rgbBlue;
  BYTE rgbGreen;
  BYTE rgbRed;
  BYTE rgbAlpha;
} MFARGB;

cpp_quote("")
typedef union _MFPaletteEntry {
  MFARGB ARGB;
  MFAYUVSample AYCbCr;
} MFPaletteEntry;
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
typedef struct _MFVideoSurfaceInfo {
  DWORD Format;
  DWORD PaletteEntries;
  [size_is (PaletteEntries)] MFPaletteEntry Palette[];
} MFVideoSurfaceInfo;

cpp_quote("")
typedef struct _MFVideoCompressedInfo {
  LONGLONG AvgBitrate;
  LONGLONG AvgBitErrorRate;
  DWORD MaxKeyFrameSpacing;
} MFVideoCompressedInfo;

cpp_quote("")
typedef struct _MFVIDEOFORMAT {
  DWORD dwSize;
  MFVideoInfo videoInfo;
  GUID guidFormat;
  MFVideoCompressedInfo compressedInfo;
  MFVideoSurfaceInfo surfaceInfo;
} MFVIDEOFORMAT;

cpp_quote("")
typedef enum _MFStandardVideoFormat {
  MFStdVideoFormat_reserved = 0,
  MFStdVideoFormat_NTSC,
  MFStdVideoFormat_PAL,
  MFStdVideoFormat_DVD_NTSC,
  MFStdVideoFormat_DVD_PAL,
  MFStdVideoFormat_DV_PAL,
  MFStdVideoFormat_DV_NTSC,
  MFStdVideoFormat_ATSC_SD480i,
  MFStdVideoFormat_ATSC_HD1080i,
  MFStdVideoFormat_ATSC_HD720p,
} MFStandardVideoFormat;
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, uuid (b99f381f-a8f9-47a2-a5af-ca3a225a3890), local]
interface IMFVideoMediaType : IMFMediaType {
  const MFVIDEOFORMAT *GetVideoFormat ();
  HRESULT GetVideoRepresentation ([in] GUID guidRepresentation,[out] LPVOID *ppvRepresentation,[in] LONG lStride);
};
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
[object, uuid (ac6b7889-0740-4d51-8619-905994a55cc6)]
interface IMFAsyncResult : IUnknown {
  HRESULT GetState ([out] IUnknown **ppunkState);
  HRESULT GetStatus ();
  HRESULT SetStatus ([in] HRESULT hrStatus);
  HRESULT GetObject ([out] IUnknown **ppObject);
  [local] IUnknown *GetStateNoAddRef ();
};

cpp_quote("")
[object, uuid (a27003cf-2354-4f2a-8d6a-ab7cff15437e),]
interface IMFAsyncCallback : IUnknown {
  HRESULT GetParameters ([out] DWORD *pdwFlags,[out] DWORD *pdwQueue);
  HRESULT Invoke ([in] IMFAsyncResult *pAsyncResult);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, uuid (c7a4dca1-f5f0-47b6-b92b-bf0106d25791), local]
interface IMFAsyncCallbackLogging : IMFAsyncCallback {
  void *GetObjectPointer ();
  DWORD GetObjectTag ();
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
cpp_quote("#define MFASYNC_FAST_IO_PROCESSING_CALLBACK 0x00000001")
cpp_quote("#define MFASYNC_SIGNAL_CALLBACK 0x00000002")
cpp_quote("#define MFASYNC_BLOCKING_CALLBACK 0x00000004")
cpp_quote("#define MFASYNC_REPLY_CALLBACK 0x00000008")
cpp_quote("#define MFASYNC_LOCALIZE_REMOTE_CALLBACK 0x00000010")
cpp_quote("")
cpp_quote("#define MFASYNC_CALLBACK_QUEUE_UNDEFINED 0x00000000")
cpp_quote("#define MFASYNC_CALLBACK_QUEUE_STANDARD 0x00000001")
cpp_quote("#define MFASYNC_CALLBACK_QUEUE_RT 0x00000002")
cpp_quote("#define MFASYNC_CALLBACK_QUEUE_IO 0x00000003")
cpp_quote("#define MFASYNC_CALLBACK_QUEUE_TIMER 0x00000004")
cpp_quote("#define MFASYNC_CALLBACK_QUEUE_MULTITHREADED 0x00000005")
cpp_quote("#define MFASYNC_CALLBACK_QUEUE_LONG_FUNCTION 0x00000007")
cpp_quote("#define MFASYNC_CALLBACK_QUEUE_PRIVATE_MASK 0xFFFF0000")
cpp_quote("#define MFASYNC_CALLBACK_QUEUE_ALL 0xFFFFFFFF")

cpp_quote("")
enum {
  MEUnknown = 0,
  MEError = 1,
  MEExtendedType = 2,
  MENonFatalError = 3,
  MEGenericV1Anchor = MENonFatalError,
  MESessionUnknown = 100,
  MESessionTopologySet = 101,
  MESessionTopologiesCleared = 102,
  MESessionStarted = 103,
  MESessionPaused = 104,
  MESessionStopped = 105,
  MESessionClosed = 106,
  MESessionEnded = 107,
  MESessionRateChanged = 108,
  MESessionScrubSampleComplete = 109,
  MESessionCapabilitiesChanged = 110,
  MESessionTopologyStatus = 111,
  MESessionNotifyPresentationTime = 112,
  MENewPresentation = 113,
  MELicenseAcquisitionStart = 114,
  MELicenseAcquisitionCompleted = 115,
  MEIndividualizationStart = 116,
  MEIndividualizationCompleted = 117,
  MEEnablerProgress = 118,
  MEEnablerCompleted = 119,
  MEPolicyError = 120,
  MEPolicyReport = 121,
  MEBufferingStarted = 122,
  MEBufferingStopped = 123,
  MEConnectStart = 124,
  MEConnectEnd = 125,
  MEReconnectStart = 126,
  MEReconnectEnd = 127,
  MERendererEvent = 128,
  MESessionStreamSinkFormatChanged = 129,
  MESessionV1Anchor = MESessionStreamSinkFormatChanged,
  MESourceUnknown = 200,
  MESourceStarted = 201,
  MEStreamStarted = 202,
  MESourceSeeked = 203,
  MEStreamSeeked = 204,
  MENewStream = 205,
  MEUpdatedStream = 206,
  MESourceStopped = 207,
  MEStreamStopped = 208,
  MESourcePaused = 209,
  MEStreamPaused = 210,
  MEEndOfPresentation = 211,
  MEEndOfStream = 212,
  MEMediaSample = 213,
  MEStreamTick = 214,
  MEStreamThinMode = 215,
  MEStreamFormatChanged = 216,
  MESourceRateChanged = 217,
  MEEndOfPresentationSegment = 218,
  MESourceCharacteristicsChanged = 219,
  MESourceRateChangeRequested = 220,
  MESourceMetadataChanged = 221,
  MESequencerSourceTopologyUpdated = 222,
  MESourceV1Anchor = MESequencerSourceTopologyUpdated,
  MESinkUnknown = 300,
  MEStreamSinkStarted = 301,
  MEStreamSinkStopped = 302,
  MEStreamSinkPaused = 303,
  MEStreamSinkRateChanged = 304,
  MEStreamSinkRequestSample = 305,
  MEStreamSinkMarker = 306,
  MEStreamSinkPrerolled = 307,
  MEStreamSinkScrubSampleComplete = 308,
  MEStreamSinkFormatChanged = 309,
  MEStreamSinkDeviceChanged = 310,
  MEQualityNotify = 311,
  MESinkInvalidated = 312,
  MEAudioSessionNameChanged = 313,
  MEAudioSessionVolumeChanged = 314,
  MEAudioSessionDeviceRemoved = 315,
  MEAudioSessionServerShutdown = 316,
  MEAudioSessionGroupingParamChanged = 317,
  MEAudioSessionIconChanged = 318,
  MEAudioSessionFormatChanged = 319,
  MEAudioSessionDisconnected = 320,
  MEAudioSessionExclusiveModeOverride = 321,
  MESinkV1Anchor = MEAudioSessionExclusiveModeOverride,
  MECaptureAudioSessionVolumeChanged = 322,
  MECaptureAudioSessionDeviceRemoved = 323,
  MECaptureAudioSessionFormatChanged = 324,
  MECaptureAudioSessionDisconnected = 325,
  MECaptureAudioSessionExclusiveModeOverride = 326,
  MECaptureAudioSessionServerShutdown = 327,
  MESinkV2Anchor = MECaptureAudioSessionServerShutdown,
  METrustUnknown = 400,
  MEPolicyChanged = 401,
  MEContentProtectionMessage = 402,
  MEPolicySet = 403,
  METrustV1Anchor = MEPolicySet,
  MEWMDRMLicenseBackupCompleted = 500,
  MEWMDRMLicenseBackupProgress = 501,
  MEWMDRMLicenseRestoreCompleted = 502,
  MEWMDRMLicenseRestoreProgress = 503,
  MEWMDRMLicenseAcquisitionCompleted = 506,
  MEWMDRMIndividualizationCompleted = 508,
  MEWMDRMIndividualizationProgress = 513,
  MEWMDRMProximityCompleted = 514,
  MEWMDRMLicenseStoreCleaned = 515,
  MEWMDRMRevocationDownloadCompleted = 516,
  MEWMDRMV1Anchor = MEWMDRMRevocationDownloadCompleted,
  METransformUnknown = 600,
  METransformNeedInput,
  METransformHaveOutput,
  METransformDrainComplete,
  METransformMarker,
  METransformInputStreamStateChanged,
  MEByteStreamCharacteristicsChanged = 700,
  MEVideoCaptureDeviceRemoved = 800,
  MEVideoCaptureDevicePreempted = 801,
  MEStreamSinkFormatInvalidated = 802,
  MEEncodingParameters = 803,
  MEContentProtectionMetadata = 900,
  MEDeviceThermalStateChanged = 950,
  MEReservedMax = 10000
};

cpp_quote("")
typedef DWORD MediaEventType;

cpp_quote("")
[object, uuid (DF598932-F10C-4e39-BBA2-C308F101DAA3)]
interface IMFMediaEvent : IMFAttributes {
  HRESULT GetType ([out] MediaEventType *pmet);
  HRESULT GetExtendedType ([out] GUID *pguidExtendedType);
  HRESULT GetStatus ([out] HRESULT *phrStatus);
  HRESULT GetValue ([out] PROPVARIANT *pvValue);
}

cpp_quote("")
cpp_quote("#define MF_EVENT_FLAG_NO_WAIT 0x00000001")

cpp_quote("")
interface IMFRemoteAsyncCallback;

cpp_quote("")
[object, uuid (2cd0bd52-BCD5-4b89-B62C-EADC0C031E7D)]
interface IMFMediaEventGenerator : IUnknown {
  HRESULT GetEvent ([in] DWORD dwFlags,[out] IMFMediaEvent **ppEvent);
  [local] HRESULT BeginGetEvent ([in] IMFAsyncCallback *pCallback,[in] IUnknown *punkState);
  [call_as (BeginGetEvent)] HRESULT RemoteBeginGetEvent ([in] IMFRemoteAsyncCallback *pCallback);
  [local] HRESULT EndGetEvent ([in] IMFAsyncResult *pResult,[out] IMFMediaEvent **ppEvent);
  [call_as (EndGetEvent)] HRESULT RemoteEndGetEvent ([in] IUnknown *pResult,[out] DWORD *pcbEvent,[out, size_is (,*pcbEvent)] BYTE **ppbEvent);
  HRESULT QueueEvent ([in] MediaEventType met,[in] REFGUID guidExtendedType,[in] HRESULT hrStatus,[in, unique] const PROPVARIANT *pvValue);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, uuid (a27003d0-2354-4f2a-8d6a-ab7cff15437e),]
interface IMFRemoteAsyncCallback : IUnknown {
  HRESULT Invoke ([in] HRESULT hr,[in] IUnknown *pRemoteResult);
};
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")

cpp_quote("")
typedef enum _MFBYTESTREAM_SEEK_ORIGIN {
  msoBegin,
  msoCurrent
} MFBYTESTREAM_SEEK_ORIGIN;

cpp_quote("")
[object, uuid (ad4c1b00-4bf7-422f-9175-756693d9130d),]
interface IMFByteStream : IUnknown {
  HRESULT GetCapabilities ([out] DWORD *pdwCapabilities);
  HRESULT GetLength ([out] QWORD *pqwLength);
  HRESULT SetLength ([in] QWORD qwLength);
  HRESULT GetCurrentPosition ([out] QWORD *pqwPosition);
  HRESULT SetCurrentPosition ([in] QWORD qwPosition);
  HRESULT IsEndOfStream ([out] WINBOOL *pfEndOfStream);
  HRESULT Read ([out, size_is (cb)] BYTE *pb,[in] ULONG cb,[out] ULONG *pcbRead);
  [local] HRESULT BeginRead ([out] BYTE *pb,[in] ULONG cb,[in] IMFAsyncCallback *pCallback,[in] IUnknown *punkState);
  [call_as (BeginRead)] HRESULT RemoteBeginRead ([in] ULONG cb,[in] IMFRemoteAsyncCallback *pCallback);
  [local] HRESULT EndRead ([in] IMFAsyncResult *pResult,[out] ULONG *pcbRead);
  [call_as (EndRead)] HRESULT RemoteEndRead ([in] IUnknown *punkResult,[out, size_is (cb)] BYTE *pb,[in] ULONG cb,[out] ULONG *pcbRead);
  HRESULT Write ([in, size_is (cb)] const BYTE *pb,[in] ULONG cb,[out] ULONG *pcbWritten);
  [local] HRESULT BeginWrite ([in] const BYTE *pb,[in] ULONG cb,[in] IMFAsyncCallback *pCallback,[in] IUnknown *punkState);
  [call_as (BeginWrite)] HRESULT RemoteBeginWrite ([in, size_is (cb)] const BYTE *pb,[in] ULONG cb,[in] IMFRemoteAsyncCallback *pCallback);
  [local] HRESULT EndWrite ([in] IMFAsyncResult *pResult,[out] ULONG *pcbWritten);
  [call_as (EndWrite)] HRESULT RemoteEndWrite ([in] IUnknown *punkResult,[out] ULONG *pcbWritten);
  HRESULT Seek ([in] MFBYTESTREAM_SEEK_ORIGIN SeekOrigin,[in] LONGLONG llSeekOffset,[in] DWORD dwSeekFlags,[out] QWORD *pqwCurrentPosition);
  HRESULT Flush ();
  HRESULT Close ();
};

cpp_quote("")
cpp_quote("#define MFBYTESTREAM_IS_READABLE 0x00000001")
cpp_quote("#define MFBYTESTREAM_IS_WRITABLE 0x00000002")
cpp_quote("#define MFBYTESTREAM_IS_SEEKABLE 0x00000004")
cpp_quote("#define MFBYTESTREAM_IS_REMOTE 0x00000008")
cpp_quote("#define MFBYTESTREAM_IS_DIRECTORY 0x00000080")
cpp_quote("#define MFBYTESTREAM_HAS_SLOW_SEEK 0x00000100")
cpp_quote("#define MFBYTESTREAM_IS_PARTIALLY_DOWNLOADED 0x00000200")
cpp_quote("#if WINVER >= _WIN32_WINNT_WIN7")
cpp_quote("#define MFBYTESTREAM_SHARE_WRITE 0x00000400")
cpp_quote("#endif")
cpp_quote("#if WINVER >= _WIN32_WINNT_WIN8")
cpp_quote("#define MFBYTESTREAM_DOES_NOT_USE_NETWORK 0x00000800")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#define MFBYTESTREAM_SEEK_FLAG_CANCEL_PENDING_IO 0x00000001")
cpp_quote("")
cpp_quote("EXTERN_GUID( MF_BYTESTREAM_ORIGIN_NAME, 0xfc358288, 0x3cb6, 0x460c, 0xa4, 0x24, 0xb6, 0x68, 0x12, 0x60, 0x37, 0x5a);")
cpp_quote("EXTERN_GUID( MF_BYTESTREAM_CONTENT_TYPE, 0xfc358289, 0x3cb6, 0x460c, 0xa4, 0x24, 0xb6, 0x68, 0x12, 0x60, 0x37, 0x5a);")
cpp_quote("EXTERN_GUID( MF_BYTESTREAM_DURATION, 0xfc35828a, 0x3cb6, 0x460c, 0xa4, 0x24, 0xb6, 0x68, 0x12, 0x60, 0x37, 0x5a);")
cpp_quote("EXTERN_GUID( MF_BYTESTREAM_LAST_MODIFIED_TIME, 0xfc35828b, 0x3cb6, 0x460c, 0xa4, 0x24, 0xb6, 0x68, 0x12, 0x60, 0x37, 0x5a);")
cpp_quote("#if (WINVER >= _WIN32_WINNT_WIN7)")
cpp_quote("EXTERN_GUID( MF_BYTESTREAM_IFO_FILE_URI, 0xfc35828c, 0x3cb6, 0x460c, 0xa4, 0x24, 0xb6, 0x68, 0x12, 0x60, 0x37, 0x5a);")
cpp_quote("EXTERN_GUID( MF_BYTESTREAM_DLNA_PROFILE_ID, 0xfc35828d, 0x3cb6, 0x460c, 0xa4, 0x24, 0xb6, 0x68, 0x12, 0x60, 0x37, 0x5a);")
cpp_quote("EXTERN_GUID( MF_BYTESTREAM_EFFECTIVE_URL, 0x9afa0209, 0x89d1, 0x42af, 0x84, 0x56, 0x1d, 0xe6, 0xb5, 0x62, 0xd6, 0x91);")
cpp_quote("EXTERN_GUID( MF_BYTESTREAM_TRANSCODED, 0xb6c5c282, 0x4dc9, 0x4db9, 0xab, 0x48, 0xcf, 0x3b, 0x6d, 0x8b, 0xc5, 0xe0 );")
cpp_quote("#endif")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("EXTERN_GUID(CLSID_MFByteStreamProxyClassFactory, 0x770e8e77, 0x4916, 0x441c, 0xa9, 0xa7, 0xb3, 0x42, 0xd0, 0xee, 0xbc, 0x71 );")

cpp_quote("")
[object, uuid (a6b43f84-5c0a-42e8-a44d-b1857a76992f)]
interface IMFByteStreamProxyClassFactory : IUnknown {
  HRESULT CreateByteStreamProxy ([in] IMFByteStream *pByteStream,[in, unique] IMFAttributes *pAttributes,[in] REFIID riid,[out, iid_is (riid)] LPVOID *ppvObject);
};

cpp_quote("")
typedef enum {
  MF_ACCESSMODE_READ = 1,
  MF_ACCESSMODE_WRITE = 2,
  MF_ACCESSMODE_READWRITE = 3
} MF_FILE_ACCESSMODE;

cpp_quote("")
typedef enum {
  MF_OPENMODE_FAIL_IF_NOT_EXIST = 0,
  MF_OPENMODE_FAIL_IF_EXIST = 1,
  MF_OPENMODE_RESET_IF_EXIST = 2,
  MF_OPENMODE_APPEND_IF_EXIST = 3,
  MF_OPENMODE_DELETE_IF_EXIST = 4
} MF_FILE_OPENMODE;

cpp_quote("")
typedef enum {
  MF_FILEFLAGS_NONE = 0x00000000,
  MF_FILEFLAGS_NOBUFFERING = 0x00000001,
  MF_FILEFLAGS_ALLOW_WRITE_SHARING = 0x00000002
} MF_FILE_FLAGS;
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
[object, uuid (8feed468-6f7e-440d-869a-49bdd283ad0d),]
interface IMFSampleOutputStream : IUnknown {
  HRESULT BeginWriteSample ([in] IMFSample *pSample,[in] IMFAsyncCallback *pCallback,[in] IUnknown *punkState);
  HRESULT EndWriteSample ([in] IMFAsyncResult *pResult);
  HRESULT Close ();
};

cpp_quote("")
[object, uuid (5bc8a76b-869a-46a3-9b03-FA218A66AEBE)]
interface IMFCollection : IUnknown {
  HRESULT GetElementCount ([out] DWORD *pcElements);
  HRESULT GetElement ([in] DWORD dwElementIndex,[out] IUnknown **ppUnkElement);
  HRESULT AddElement ([in] IUnknown *pUnkElement);
  HRESULT RemoveElement ([in] DWORD dwElementIndex,[out] IUnknown **ppUnkElement);
  HRESULT InsertElementAt ([in] DWORD dwIndex,[in]IUnknown *pUnknown);
  HRESULT RemoveAllElements ();
}

cpp_quote("")
[object, uuid (36f846fc-2256-48b6-b58e-e2b638316581), local]
interface IMFMediaEventQueue : IUnknown {
  HRESULT GetEvent ([in] DWORD dwFlags,[out] IMFMediaEvent **ppEvent);
  HRESULT BeginGetEvent ([in] IMFAsyncCallback *pCallback,[in] IUnknown *punkState);
  HRESULT EndGetEvent ([in] IMFAsyncResult *pResult,[out] IMFMediaEvent **ppEvent);
  HRESULT QueueEvent ([in] IMFMediaEvent *pEvent);
  HRESULT QueueEventParamVar ([in] MediaEventType met,[in] REFGUID guidExtendedType,[in] HRESULT hrStatus,[in, unique] const PROPVARIANT *pvValue);
  HRESULT QueueEventParamUnk ([in] MediaEventType met,[in] REFGUID guidExtendedType,[in] HRESULT hrStatus,[in, unique] IUnknown *pUnk);
  HRESULT Shutdown ();
}

cpp_quote("")
[object, uuid (7fee9e9a-4a89-47a6-899c-B6A53A70FB67), helpstring ("IMFActivate Interface"), pointer_default (unique)]
interface IMFActivate : IMFAttributes {
  HRESULT ActivateObject ([in] REFIID riid,[out, iid_is (riid), retval] void **ppv);
  HRESULT ShutdownObject ();
  HRESULT DetachObject ();
};
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINVER >= _WIN32_WINNT_WIN7")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
typedef enum _MF_Plugin_Type {
  MF_Plugin_Type_MFT = 0,
  MF_Plugin_Type_MediaSource = 1,
  MF_Plugin_Type_MFT_MatchOutputType = 2,
  MF_Plugin_Type_Other = (DWORD)-1,
} MF_Plugin_Type;

cpp_quote("")
[object, local, uuid (5c6c44bf-1db6-435b-9249-e8cd10fdec96), helpstring ("IMFPluginControl Interface"), pointer_default (unique)]
interface IMFPluginControl : IUnknown {
  HRESULT GetPreferredClsid (DWORD pluginType, LPCWSTR selector, CLSID *clsid);
  HRESULT GetPreferredClsidByIndex (DWORD pluginType, DWORD index, LPWSTR *selector, CLSID *clsid);
  HRESULT SetPreferredClsid (DWORD pluginType, LPCWSTR selector, const CLSID *clsid);
  HRESULT IsDisabled (DWORD pluginType, REFCLSID clsid);
  HRESULT GetDisabledByIndex (DWORD pluginType, DWORD index, CLSID *clsid);
  HRESULT SetDisabled (DWORD pluginType, REFCLSID clsid, WINBOOL disabled);
}

cpp_quote("")
typedef enum MF_PLUGIN_CONTROL_POLICY {
  MF_PLUGIN_CONTROL_POLICY_USE_ALL_PLUGINS = 0,
  MF_PLUGIN_CONTROL_POLICY_USE_APPROVED_PLUGINS = 1,
  MF_PLUGIN_CONTROL_POLICY_USE_WEB_PLUGINS = 2,
  MF_PLUGIN_CONTROL_POLICY_USE_WEB_PLUGINS_EDGEMODE = 3
} MF_PLUGIN_CONTROL_POLICY;

cpp_quote("")
[object, local, uuid (C6982083-3ddc-45cb-AF5E-0f7a8ce4de77), helpstring ("IMFPluginControl2 Interface"), pointer_default (unique)]
interface IMFPluginControl2 : IMFPluginControl {
  HRESULT SetPolicy ([in] MF_PLUGIN_CONTROL_POLICY policy);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
[object, local, uuid (eb533d5d-2db6-40f8-97a9-494692014f07), helpstring ("IMFDXGIDeviceManager Interface"), pointer_default (unique)]
interface IMFDXGIDeviceManager : IUnknown {
  HRESULT CloseDeviceHandle (HANDLE hDevice);
  HRESULT GetVideoService (HANDLE hDevice, REFIID riid, void **ppService);
  HRESULT LockDevice (HANDLE hDevice, REFIID riid, void **ppUnkDevice, WINBOOL fBlock);
  HRESULT OpenDeviceHandle (HANDLE *phDevice);
  HRESULT ResetDevice (IUnknown *pUnkDevice, UINT resetToken);
  HRESULT TestDevice (HANDLE hDevice);
  HRESULT UnlockDevice (HANDLE hDevice, WINBOOL fSaveState);
}

cpp_quote("")
typedef enum _MF_STREAM_STATE {
  MF_STREAM_STATE_STOPPED = 0,
  MF_STREAM_STATE_PAUSED,
  MF_STREAM_STATE_RUNNING
} MF_STREAM_STATE;

cpp_quote("#endif")
cpp_quote("")
cpp_quote("#endif")

cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("#if NTDDI_VERSION >= NTDDI_WIN10_RS2")

cpp_quote("")
[object, local, uuid(ce8bd576-e440-43b3-be34-1e53f565f7e8), helpstring("IMFMuxStreamAttributesManager Interface"), pointer_default(unique)]
interface IMFMuxStreamAttributesManager : IUnknown {
  HRESULT GetStreamCount (DWORD *pdwMuxStreamCount);
  HRESULT GetAttributes (DWORD dwMuxStreamIndex, IMFAttributes **ppStreamAttributes);
}

cpp_quote("")
[object, local, uuid(505a2c72-42f7-4690-aeab-8f513d0ffdb8), helpstring("IMFMuxStreamMediaTypeManager Interface"), pointer_default(unique)]
interface IMFMuxStreamMediaTypeManager : IUnknown {
  HRESULT GetStreamCount (DWORD *pdwMuxStreamCount);
  HRESULT GetMediaType (DWORD dwMuxStreamIndex, IMFMediaType **ppMediaType);
  HRESULT GetStreamConfigurationCount (DWORD *pdwCount);
  HRESULT AddStreamConfiguration (ULONGLONG ullStreamMask);
  HRESULT RemoveStreamConfiguration (ULONGLONG ullStreamMask);
  HRESULT GetStreamConfiguration (DWORD ulIndex, ULONGLONG *pullStreamMask);
}

cpp_quote("")
[object, local, uuid(74abbc19-b1cc-4e41-bb8b-9d9b86a8f6ca), helpstring("IMFMuxStreamSampleManager Interface"), pointer_default(unique)]
interface IMFMuxStreamSampleManager : IUnknown {
  HRESULT GetStreamCount (DWORD *pdwMuxStreamCount);
  HRESULT GetSample (DWORD dwMuxStreamIndex, IMFSample **ppSample);
  ULONGLONG GetStreamConfiguration ();
}

cpp_quote("#endif")
cpp_quote("#endif")
