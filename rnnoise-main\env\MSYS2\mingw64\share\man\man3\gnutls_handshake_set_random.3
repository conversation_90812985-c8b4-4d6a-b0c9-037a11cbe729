.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_handshake_set_random" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_handshake_set_random \- API function
.SH SYNOPSIS
.B #include <gnutls/gnutls.h>
.sp
.BI "int gnutls_handshake_set_random(gnutls_session_t " session ", const gnutls_datum_t * " random ");"
.SH ARGUMENTS
.IP "gnutls_session_t session" 12
is a \fBgnutls_session_t\fP type.
.IP "const gnutls_datum_t * random" 12
a random value of 32\-bytes
.SH "DESCRIPTION"
This function will explicitly set the server or client hello 
random value in the subsequent TLS handshake. The random value 
should be a 32\-byte value.

Note that this function should not normally be used as gnutls
will select automatically a random value for the handshake.

This function should not be used when resuming a session.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP on success, or an error code.

Since 3.1.9
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
